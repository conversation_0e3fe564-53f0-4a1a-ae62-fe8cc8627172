<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" style="background-color: #1a1a1a;">
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" fill="#ffffff" font-size="24" font-weight="bold">
    Wheel Generation Benchmark Flow
  </text>

  <!-- Context Section -->
  <g id="context-section">
    <!-- Context Background -->
    <rect id="cell-context-bg" x="50" y="60" width="300" height="180" rx="10"
          fill="#2d3748" stroke="#4a5568" stroke-width="2"/>
    <text x="200" y="85" text-anchor="middle" fill="#ffffff" font-size="16" font-weight="bold">
      Original Context
    </text>

    <!-- Trust Level -->
    <rect id="cell-trust-level" x="70" y="100" width="120" height="40" rx="5"
          fill="#4299e1" stroke="#2b6cb0" stroke-width="1"/>
    <text id="cell-trust-label" x="130" y="125" text-anchor="middle" fill="#ffffff" font-size="12">
      Trust: 75
    </text>

    <!-- Mood State -->
    <rect id="cell-mood-state" x="210" y="100" width="120" height="40" rx="5"
          fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
    <text id="cell-mood-label" x="270" y="125" text-anchor="middle" fill="#ffffff" font-size="12">
      Mood: +0.5/+0.2
    </text>

    <!-- Environment -->
    <rect id="cell-environment" x="70" y="160" width="120" height="40" rx="5"
          fill="#ed8936" stroke="#c05621" stroke-width="1"/>
    <text id="cell-env-label" x="130" y="185" text-anchor="middle" fill="#ffffff" font-size="12">
      Stress: 20
    </text>

    <!-- Time Pressure -->
    <rect id="cell-time-pressure" x="210" y="160" width="120" height="40" rx="5"
          fill="#9f7aea" stroke="#6b46c1" stroke-width="1"/>
    <text id="cell-time-label" x="270" y="185" text-anchor="middle" fill="#ffffff" font-size="12">
      Time: 30
    </text>
  </g>

  <!-- Agent Processing Section -->
  <g id="agent-section">
    <!-- Agent Background -->
    <rect id="cell-agent-bg" x="450" y="60" width="500" height="400" rx="10"
          fill="#2d3748" stroke="#4a5568" stroke-width="2"/>
    <text x="700" y="85" text-anchor="middle" fill="#ffffff" font-size="16" font-weight="bold">
      Agent Processing Pipeline
    </text>

    <!-- LLM Configuration -->
    <rect id="cell-llm-config" x="470" y="100" width="200" height="50" rx="5"
          fill="#805ad5" stroke="#553c9a" stroke-width="1"/>
    <text id="cell-llm-label" x="570" y="130" text-anchor="middle" fill="#ffffff" font-size="12">
      LLM: GPT-4o-mini
    </text>

    <!-- Agent Role -->
    <rect id="cell-agent-role" x="720" y="100" width="200" height="50" rx="5"
          fill="#38b2ac" stroke="#2c7a7b" stroke-width="1"/>
    <text id="cell-role-label" x="820" y="130" text-anchor="middle" fill="#ffffff" font-size="12">
      Role: Mentor
    </text>

    <!-- Tool Calls Section -->
    <rect id="cell-tools-bg" x="470" y="170" width="460" height="120" rx="5"
          fill="#1a202c" stroke="#2d3748" stroke-width="1"/>
    <text x="700" y="190" text-anchor="middle" fill="#ffffff" font-size="14" font-weight="bold">
      Tool Interactions
    </text>

    <!-- Tool 1: Get User Profile -->
    <circle id="cell-tool-profile" cx="520" cy="230" r="25"
            fill="#4299e1" stroke="#2b6cb0" stroke-width="2"/>
    <text x="520" y="235" text-anchor="middle" fill="#ffffff" font-size="10">
      Profile
    </text>

    <!-- Tool 2: Generate Wheel -->
    <circle id="cell-tool-wheel" cx="600" cy="230" r="25"
            fill="#48bb78" stroke="#2f855a" stroke-width="2"/>
    <text x="600" y="235" text-anchor="middle" fill="#ffffff" font-size="10">
      Wheel
    </text>

    <!-- Tool 3: Validate -->
    <circle id="cell-tool-validate" cx="680" cy="230" r="25"
            fill="#ed8936" stroke="#c05621" stroke-width="2"/>
    <text x="680" y="235" text-anchor="middle" fill="#ffffff" font-size="10">
      Validate
    </text>

    <!-- Tool 4: Format Output -->
    <circle id="cell-tool-format" cx="760" cy="230" r="25"
            fill="#9f7aea" stroke="#6b46c1" stroke-width="2"/>
    <text x="760" y="235" text-anchor="middle" fill="#ffffff" font-size="10">
      Format
    </text>

    <!-- Tool 5: Save Results -->
    <circle id="cell-tool-save" cx="840" cy="230" r="25"
            fill="#f56565" stroke="#c53030" stroke-width="2"/>
    <text x="840" y="235" text-anchor="middle" fill="#ffffff" font-size="10">
      Save
    </text>

    <!-- Communication Flow -->
    <rect id="cell-comm-bg" x="470" y="310" width="460" height="120" rx="5"
          fill="#1a202c" stroke="#2d3748" stroke-width="1"/>
    <text x="700" y="330" text-anchor="middle" fill="#ffffff" font-size="14" font-weight="bold">
      Agent Communications
    </text>

    <!-- Input Processing -->
    <rect id="cell-input-tokens" x="490" y="350" width="100" height="30" rx="3"
          fill="#4a5568" stroke="#718096" stroke-width="1"/>
    <text id="cell-input-label" x="540" y="370" text-anchor="middle" fill="#ffffff" font-size="10">
      Input: 1.2k
    </text>

    <!-- Processing Time -->
    <rect id="cell-processing-time" x="610" y="350" width="100" height="30" rx="3"
          fill="#4a5568" stroke="#718096" stroke-width="1"/>
    <text id="cell-time-proc-label" x="660" y="370" text-anchor="middle" fill="#ffffff" font-size="10">
      Time: 2.3s
    </text>

    <!-- Output Generation -->
    <rect id="cell-output-tokens" x="730" y="350" width="100" height="30" rx="3"
          fill="#4a5568" stroke="#718096" stroke-width="1"/>
    <text id="cell-output-label" x="780" y="370" text-anchor="middle" fill="#ffffff" font-size="10">
      Output: 800
    </text>

    <!-- Cost Tracking -->
    <rect id="cell-cost" x="590" y="390" width="120" height="30" rx="3"
          fill="#2d3748" stroke="#4a5568" stroke-width="1"/>
    <text id="cell-cost-label" x="650" y="410" text-anchor="middle" fill="#ffffff" font-size="10">
      Cost: $0.003
    </text>
  </g>

  <!-- Output Section -->
  <g id="output-section">
    <!-- Output Background -->
    <rect id="cell-output-bg" x="1050" y="60" width="300" height="400" rx="10"
          fill="#2d3748" stroke="#4a5568" stroke-width="2"/>
    <text x="1200" y="85" text-anchor="middle" fill="#ffffff" font-size="16" font-weight="bold">
      Final Output
    </text>

    <!-- Success Rate -->
    <rect id="cell-success-rate" x="1070" y="100" width="260" height="40" rx="5"
          fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
    <text id="cell-success-label" x="1200" y="125" text-anchor="middle" fill="#ffffff" font-size="12">
      Success Rate: 95%
    </text>

    <!-- Semantic Score -->
    <rect id="cell-semantic-score" x="1070" y="160" width="260" height="40" rx="5"
          fill="#4299e1" stroke="#2b6cb0" stroke-width="1"/>
    <text id="cell-semantic-label" x="1200" y="185" text-anchor="middle" fill="#ffffff" font-size="12">
      Semantic Score: 8.5/10
    </text>

    <!-- User Text Output -->
    <rect id="cell-user-text" x="1070" y="220" width="260" height="80" rx="5"
          fill="#805ad5" stroke="#553c9a" stroke-width="1"/>
    <text x="1200" y="240" text-anchor="middle" fill="#ffffff" font-size="12" font-weight="bold">
      User Text
    </text>
    <text id="cell-user-text-content" x="1200" y="260" text-anchor="middle" fill="#ffffff" font-size="10">
      "Your personalized wheel"
    </text>
    <text x="1200" y="280" text-anchor="middle" fill="#ffffff" font-size="10">
      "focuses on growth areas..."
    </text>

    <!-- Wheel Items -->
    <rect id="cell-wheel-items" x="1070" y="320" width="260" height="120" rx="5"
          fill="#38b2ac" stroke="#2c7a7b" stroke-width="1"/>
    <text x="1200" y="340" text-anchor="middle" fill="#ffffff" font-size="12" font-weight="bold">
      Wheel Items
    </text>
    <text id="cell-wheel-count" x="1200" y="360" text-anchor="middle" fill="#ffffff" font-size="10">
      8 Categories Generated
    </text>
    <text x="1200" y="380" text-anchor="middle" fill="#ffffff" font-size="10">
      • Career Growth
    </text>
    <text x="1200" y="400" text-anchor="middle" fill="#ffffff" font-size="10">
      • Health & Wellness
    </text>
    <text x="1200" y="420" text-anchor="middle" fill="#ffffff" font-size="10">
      • Relationships...
    </text>
  </g>

  <!-- Flow Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7"
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4a5568"/>
    </marker>
    <marker id="arrowhead-success" markerWidth="10" markerHeight="7"
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#48bb78"/>
    </marker>
  </defs>

  <!-- Context to Agent Flow -->
  <path id="cell-flow-context-agent" d="M 350 150 Q 400 150 450 150"
        stroke="#4a5568" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <text x="400" y="140" text-anchor="middle" fill="#a0aec0" font-size="10">
    Context Input
  </text>

  <!-- Agent to Output Flow -->
  <path id="cell-flow-agent-output" d="M 950 260 Q 1000 260 1050 260"
        stroke="#48bb78" stroke-width="3" fill="none" marker-end="url(#arrowhead-success)"/>
  <text x="1000" y="250" text-anchor="middle" fill="#a0aec0" font-size="10">
    Generated Output
  </text>

  <!-- Tool Flow Connections -->
  <path id="cell-flow-tool1-tool2" d="M 545 230 L 575 230"
        stroke="#4299e1" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path id="cell-flow-tool2-tool3" d="M 625 230 L 655 230"
        stroke="#48bb78" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path id="cell-flow-tool3-tool4" d="M 705 230 L 735 230"
        stroke="#ed8936" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path id="cell-flow-tool4-tool5" d="M 785 230 L 815 230"
        stroke="#9f7aea" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>

  <!-- Performance Indicators -->
  <g id="performance-indicators">
    <!-- Quality Indicator -->
    <circle id="cell-quality-indicator" cx="1200" cy="500" r="30"
            fill="#48bb78" stroke="#2f855a" stroke-width="3"/>
    <text x="1200" y="485" text-anchor="middle" fill="#ffffff" font-size="10" font-weight="bold">
      Quality
    </text>
    <text id="cell-quality-score" x="1200" y="505" text-anchor="middle" fill="#ffffff" font-size="12">
      Excellent
    </text>
    <text x="1200" y="520" text-anchor="middle" fill="#ffffff" font-size="8">
      95% Success
    </text>

    <!-- Efficiency Indicator -->
    <circle id="cell-efficiency-indicator" cx="1100" cy="500" r="30"
            fill="#4299e1" stroke="#2b6cb0" stroke-width="3"/>
    <text x="1100" y="485" text-anchor="middle" fill="#ffffff" font-size="10" font-weight="bold">
      Speed
    </text>
    <text id="cell-efficiency-score" x="1100" y="505" text-anchor="middle" fill="#ffffff" font-size="12">
      Fast
    </text>
    <text x="1100" y="520" text-anchor="middle" fill="#ffffff" font-size="8">
      2.3s avg
    </text>

    <!-- Cost Indicator -->
    <circle id="cell-cost-indicator" cx="1300" cy="500" r="30"
            fill="#805ad5" stroke="#553c9a" stroke-width="3"/>
    <text x="1300" y="485" text-anchor="middle" fill="#ffffff" font-size="10" font-weight="bold">
      Cost
    </text>
    <text id="cell-cost-score" x="1300" y="505" text-anchor="middle" fill="#ffffff" font-size="12">
      Low
    </text>
    <text x="1300" y="520" text-anchor="middle" fill="#ffffff" font-size="8">
      $0.003
    </text>
  </g>

  <!-- Evaluation Criteria Adaptation -->
  <g id="evaluation-section">
    <rect id="cell-eval-bg" x="50" y="500" width="900" height="150" rx="10"
          fill="#1a202c" stroke="#2d3748" stroke-width="2"/>
    <text x="500" y="525" text-anchor="middle" fill="#ffffff" font-size="16" font-weight="bold">
      Contextual Evaluation Adaptation
    </text>

    <!-- Trust-Based Criteria -->
    <rect id="cell-trust-criteria" x="70" y="540" width="200" height="80" rx="5"
          fill="#4299e1" stroke="#2b6cb0" stroke-width="1"/>
    <text x="170" y="560" text-anchor="middle" fill="#ffffff" font-size="12" font-weight="bold">
      Trust Level: High (75)
    </text>
    <text x="170" y="580" text-anchor="middle" fill="#ffffff" font-size="10">
      • Collaborative tone
    </text>
    <text x="170" y="595" text-anchor="middle" fill="#ffffff" font-size="10">
      • Empowering content
    </text>
    <text x="170" y="610" text-anchor="middle" fill="#ffffff" font-size="10">
      • Creative challenges
    </text>

    <!-- Mood-Based Criteria -->
    <rect id="cell-mood-criteria" x="290" y="540" width="200" height="80" rx="5"
          fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
    <text x="390" y="560" text-anchor="middle" fill="#ffffff" font-size="12" font-weight="bold">
      Mood: Positive (+0.5)
    </text>
    <text x="390" y="580" text-anchor="middle" fill="#ffffff" font-size="10">
      • Enthusiastic approach
    </text>
    <text x="390" y="595" text-anchor="middle" fill="#ffffff" font-size="10">
      • Energetic language
    </text>
    <text x="390" y="610" text-anchor="middle" fill="#ffffff" font-size="10">
      • Positive reinforcement
    </text>

    <!-- Environment-Based Criteria -->
    <rect id="cell-env-criteria" x="510" y="540" width="200" height="80" rx="5"
          fill="#ed8936" stroke="#c05621" stroke-width="1"/>
    <text x="610" y="560" text-anchor="middle" fill="#ffffff" font-size="12" font-weight="bold">
      Environment: Low Stress
    </text>
    <text x="610" y="580" text-anchor="middle" fill="#ffffff" font-size="10">
      • Detailed explanations
    </text>
    <text x="610" y="595" text-anchor="middle" fill="#ffffff" font-size="10">
      • Comprehensive options
    </text>
    <text x="610" y="610" text-anchor="middle" fill="#ffffff" font-size="10">
      • Thorough guidance
    </text>

    <!-- Final Evaluation -->
    <rect id="cell-final-eval" x="730" y="540" width="200" height="80" rx="5"
          fill="#805ad5" stroke="#553c9a" stroke-width="1"/>
    <text x="830" y="560" text-anchor="middle" fill="#ffffff" font-size="12" font-weight="bold">
      Adapted Evaluation
    </text>
    <text id="cell-eval-result" x="830" y="580" text-anchor="middle" fill="#ffffff" font-size="10">
      Criteria: Collaborative
    </text>
    <text x="830" y="595" text-anchor="middle" fill="#ffffff" font-size="10">
      Score: 8.5/10
    </text>
    <text x="830" y="610" text-anchor="middle" fill="#ffffff" font-size="10">
      Quality: Excellent
    </text>
  </g>

  <!-- Legend -->
  <g id="legend">
    <rect x="50" y="700" width="1300" height="120" rx="5"
          fill="#1a202c" stroke="#2d3748" stroke-width="1"/>
    <text x="700" y="720" text-anchor="middle" fill="#ffffff" font-size="14" font-weight="bold">
      Flow Legend
    </text>

    <!-- Color Legend -->
    <circle cx="100" cy="750" r="8" fill="#4299e1"/>
    <text x="120" y="755" fill="#ffffff" font-size="10">Trust/Profile Data</text>

    <circle cx="250" cy="750" r="8" fill="#48bb78"/>
    <text x="270" y="755" fill="#ffffff" font-size="10">Success/Generation</text>

    <circle cx="400" cy="750" r="8" fill="#ed8936"/>
    <text x="420" y="755" fill="#ffffff" font-size="10">Processing/Validation</text>

    <circle cx="570" cy="750" r="8" fill="#9f7aea"/>
    <text x="590" y="755" fill="#ffffff" font-size="10">Output/Format</text>

    <circle cx="720" cy="750" r="8" fill="#f56565"/>
    <text x="740" y="755" fill="#ffffff" font-size="10">Storage/Completion</text>

    <circle cx="100" cy="780" r="8" fill="#805ad5"/>
    <text x="120" y="785" fill="#ffffff" font-size="10">Evaluation/Quality</text>

    <circle cx="250" cy="780" r="8" fill="#38b2ac"/>
    <text x="270" y="785" fill="#ffffff" font-size="10">Final Output</text>

    <text x="500" y="785" fill="#a0aec0" font-size="10">
      Flow Direction: Context → Agent Processing → Tool Calls → Output Generation → Quality Assessment
    </text>
  </g>
</svg>
