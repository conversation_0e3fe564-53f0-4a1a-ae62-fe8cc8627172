<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600" style="background-color: #1a1a1a;">
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" fill="#ffffff" font-size="20" font-weight="bold">
    Generic Agent Benchmark Flow
  </text>
  
  <!-- Input Context -->
  <g id="input-section">
    <rect id="cell-input-bg" x="50" y="60" width="200" height="120" rx="10" 
          fill="#2d3748" stroke="#4a5568" stroke-width="2"/>
    <text x="150" y="85" text-anchor="middle" fill="#ffffff" font-size="14" font-weight="bold">
      Input Context
    </text>
    
    <!-- Scenario Input -->
    <rect id="cell-scenario-input" x="70" y="100" width="160" height="30" rx="5" 
          fill="#4299e1" stroke="#2b6cb0" stroke-width="1"/>
    <text id="cell-scenario-label" x="150" y="120" text-anchor="middle" fill="#ffffff" font-size="11">
      Scenario Data
    </text>
    
    <!-- Context Variables -->
    <rect id="cell-context-vars" x="70" y="140" width="160" height="30" rx="5" 
          fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
    <text id="cell-context-label" x="150" y="160" text-anchor="middle" fill="#ffffff" font-size="11">
      Context Variables
    </text>
  </g>
  
  <!-- Agent Processing -->
  <g id="agent-section">
    <rect id="cell-agent-bg" x="350" y="60" width="500" height="300" rx="10" 
          fill="#2d3748" stroke="#4a5568" stroke-width="2"/>
    <text x="600" y="85" text-anchor="middle" fill="#ffffff" font-size="14" font-weight="bold">
      Agent Processing Pipeline
    </text>
    
    <!-- LLM Configuration -->
    <rect id="cell-llm-config" x="370" y="100" width="150" height="40" rx="5" 
          fill="#805ad5" stroke="#553c9a" stroke-width="1"/>
    <text id="cell-llm-label" x="445" y="125" text-anchor="middle" fill="#ffffff" font-size="11">
      LLM Model
    </text>
    
    <!-- Agent Role -->
    <rect id="cell-agent-role" x="540" y="100" width="150" height="40" rx="5" 
          fill="#38b2ac" stroke="#2c7a7b" stroke-width="1"/>
    <text id="cell-role-label" x="615" y="125" text-anchor="middle" fill="#ffffff" font-size="11">
      Agent Role
    </text>
    
    <!-- Prompt Processing -->
    <rect id="cell-prompt-processing" x="710" y="100" width="120" height="40" rx="5" 
          fill="#ed8936" stroke="#c05621" stroke-width="1"/>
    <text id="cell-prompt-label" x="770" y="125" text-anchor="middle" fill="#ffffff" font-size="11">
      Prompt
    </text>
    
    <!-- Tool Execution -->
    <rect id="cell-tools-bg" x="370" y="160" width="460" height="80" rx="5" 
          fill="#1a202c" stroke="#2d3748" stroke-width="1"/>
    <text x="600" y="180" text-anchor="middle" fill="#ffffff" font-size="12" font-weight="bold">
      Tool Execution
    </text>
    
    <!-- Tool Calls -->
    <circle id="cell-tool-1" cx="420" cy="210" r="20" 
            fill="#4299e1" stroke="#2b6cb0" stroke-width="2"/>
    <text x="420" y="215" text-anchor="middle" fill="#ffffff" font-size="9">
      T1
    </text>
    
    <circle id="cell-tool-2" cx="480" cy="210" r="20" 
            fill="#48bb78" stroke="#2f855a" stroke-width="2"/>
    <text x="480" y="215" text-anchor="middle" fill="#ffffff" font-size="9">
      T2
    </text>
    
    <circle id="cell-tool-3" cx="540" cy="210" r="20" 
            fill="#ed8936" stroke="#c05621" stroke-width="2"/>
    <text x="540" y="215" text-anchor="middle" fill="#ffffff" font-size="9">
      T3
    </text>
    
    <circle id="cell-tool-4" cx="600" cy="210" r="20" 
            fill="#9f7aea" stroke="#6b46c1" stroke-width="2"/>
    <text x="600" y="215" text-anchor="middle" fill="#ffffff" font-size="9">
      T4
    </text>
    
    <circle id="cell-tool-5" cx="660" cy="210" r="20" 
            fill="#f56565" stroke="#c53030" stroke-width="2"/>
    <text x="660" y="215" text-anchor="middle" fill="#ffffff" font-size="9">
      T5
    </text>
    
    <!-- Performance Metrics -->
    <rect id="cell-perf-bg" x="370" y="260" width="460" height="80" rx="5" 
          fill="#1a202c" stroke="#2d3748" stroke-width="1"/>
    <text x="600" y="280" text-anchor="middle" fill="#ffffff" font-size="12" font-weight="bold">
      Performance Metrics
    </text>
    
    <!-- Input Tokens -->
    <rect id="cell-input-tokens" x="390" y="300" width="80" height="25" rx="3" 
          fill="#4a5568" stroke="#718096" stroke-width="1"/>
    <text id="cell-input-label" x="430" y="317" text-anchor="middle" fill="#ffffff" font-size="9">
      Input
    </text>
    
    <!-- Processing Time -->
    <rect id="cell-processing-time" x="490" y="300" width="80" height="25" rx="3" 
          fill="#4a5568" stroke="#718096" stroke-width="1"/>
    <text id="cell-time-label" x="530" y="317" text-anchor="middle" fill="#ffffff" font-size="9">
      Time
    </text>
    
    <!-- Output Tokens -->
    <rect id="cell-output-tokens" x="590" y="300" width="80" height="25" rx="3" 
          fill="#4a5568" stroke="#718096" stroke-width="1"/>
    <text id="cell-output-label" x="630" y="317" text-anchor="middle" fill="#ffffff" font-size="9">
      Output
    </text>
    
    <!-- Cost -->
    <rect id="cell-cost" x="690" y="300" width="80" height="25" rx="3" 
          fill="#4a5568" stroke="#718096" stroke-width="1"/>
    <text id="cell-cost-label" x="730" y="317" text-anchor="middle" fill="#ffffff" font-size="9">
      Cost
    </text>
  </g>
  
  <!-- Output Results -->
  <g id="output-section">
    <rect id="cell-output-bg" x="950" y="60" width="200" height="300" rx="10" 
          fill="#2d3748" stroke="#4a5568" stroke-width="2"/>
    <text x="1050" y="85" text-anchor="middle" fill="#ffffff" font-size="14" font-weight="bold">
      Results
    </text>
    
    <!-- Success Rate -->
    <rect id="cell-success-rate" x="970" y="100" width="160" height="40" rx="5" 
          fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
    <text id="cell-success-label" x="1050" y="125" text-anchor="middle" fill="#ffffff" font-size="11">
      Success Rate
    </text>
    
    <!-- Semantic Score -->
    <rect id="cell-semantic-score" x="970" y="160" width="160" height="40" rx="5" 
          fill="#4299e1" stroke="#2b6cb0" stroke-width="1"/>
    <text id="cell-semantic-label" x="1050" y="185" text-anchor="middle" fill="#ffffff" font-size="11">
      Semantic Score
    </text>
    
    <!-- Communication Quality -->
    <rect id="cell-comm-quality" x="970" y="220" width="160" height="40" rx="5" 
          fill="#805ad5" stroke="#553c9a" stroke-width="1"/>
    <text id="cell-quality-label" x="1050" y="245" text-anchor="middle" fill="#ffffff" font-size="11">
      Quality
    </text>
    
    <!-- Output Data -->
    <rect id="cell-output-data" x="970" y="280" width="160" height="60" rx="5" 
          fill="#38b2ac" stroke="#2c7a7b" stroke-width="1"/>
    <text x="1050" y="300" text-anchor="middle" fill="#ffffff" font-size="11" font-weight="bold">
      Output Data
    </text>
    <text id="cell-output-preview" x="1050" y="320" text-anchor="middle" fill="#ffffff" font-size="9">
      Generated content...
    </text>
  </g>
  
  <!-- Flow Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4a5568"/>
    </marker>
    <marker id="arrowhead-success" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#48bb78"/>
    </marker>
  </defs>
  
  <!-- Input to Agent Flow -->
  <path id="cell-flow-input-agent" d="M 250 120 Q 300 120 350 120" 
        stroke="#4a5568" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <text x="300" y="110" text-anchor="middle" fill="#a0aec0" font-size="10">
    Process
  </text>
  
  <!-- Agent to Output Flow -->
  <path id="cell-flow-agent-output" d="M 850 220 Q 900 220 950 220" 
        stroke="#48bb78" stroke-width="3" fill="none" marker-end="url(#arrowhead-success)"/>
  <text x="900" y="210" text-anchor="middle" fill="#a0aec0" font-size="10">
    Generate
  </text>
  
  <!-- Tool Flow Connections -->
  <path id="cell-flow-tool1-tool2" d="M 440 210 L 460 210" 
        stroke="#4299e1" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path id="cell-flow-tool2-tool3" d="M 500 210 L 520 210" 
        stroke="#48bb78" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path id="cell-flow-tool3-tool4" d="M 560 210 L 580 210" 
        stroke="#ed8936" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <path id="cell-flow-tool4-tool5" d="M 620 210 L 640 210" 
        stroke="#9f7aea" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Evaluation Section -->
  <g id="evaluation-section">
    <rect id="cell-eval-bg" x="50" y="400" width="1100" height="120" rx="10" 
          fill="#1a202c" stroke="#2d3748" stroke-width="2"/>
    <text x="600" y="425" text-anchor="middle" fill="#ffffff" font-size="14" font-weight="bold">
      Contextual Evaluation & Quality Assessment
    </text>
    
    <!-- Context Analysis -->
    <rect id="cell-context-analysis" x="70" y="440" width="200" height="60" rx="5" 
          fill="#4299e1" stroke="#2b6cb0" stroke-width="1"/>
    <text x="170" y="460" text-anchor="middle" fill="#ffffff" font-size="11" font-weight="bold">
      Context Analysis
    </text>
    <text id="cell-context-result" x="170" y="480" text-anchor="middle" fill="#ffffff" font-size="9">
      Trust: High, Mood: Positive
    </text>
    <text x="170" y="495" text-anchor="middle" fill="#ffffff" font-size="9">
      Environment: Low Stress
    </text>
    
    <!-- Criteria Adaptation -->
    <rect id="cell-criteria-adaptation" x="290" y="440" width="200" height="60" rx="5" 
          fill="#48bb78" stroke="#2f855a" stroke-width="1"/>
    <text x="390" y="460" text-anchor="middle" fill="#ffffff" font-size="11" font-weight="bold">
      Criteria Adaptation
    </text>
    <text id="cell-adaptation-result" x="390" y="480" text-anchor="middle" fill="#ffffff" font-size="9">
      Collaborative Approach
    </text>
    <text x="390" y="495" text-anchor="middle" fill="#ffffff" font-size="9">
      Empowering Content
    </text>
    
    <!-- Performance Assessment -->
    <rect id="cell-performance-assessment" x="510" y="440" width="200" height="60" rx="5" 
          fill="#ed8936" stroke="#c05621" stroke-width="1"/>
    <text x="610" y="460" text-anchor="middle" fill="#ffffff" font-size="11" font-weight="bold">
      Performance Assessment
    </text>
    <text id="cell-perf-result" x="610" y="480" text-anchor="middle" fill="#ffffff" font-size="9">
      Speed: Fast, Cost: Low
    </text>
    <text x="610" y="495" text-anchor="middle" fill="#ffffff" font-size="9">
      Efficiency: High
    </text>
    
    <!-- Quality Score -->
    <rect id="cell-quality-score" x="730" y="440" width="200" height="60" rx="5" 
          fill="#805ad5" stroke="#553c9a" stroke-width="1"/>
    <text x="830" y="460" text-anchor="middle" fill="#ffffff" font-size="11" font-weight="bold">
      Quality Score
    </text>
    <text id="cell-final-score" x="830" y="480" text-anchor="middle" fill="#ffffff" font-size="9">
      Semantic: 8.5/10
    </text>
    <text x="830" y="495" text-anchor="middle" fill="#ffffff" font-size="9">
      Overall: Excellent
    </text>
    
    <!-- Final Rating -->
    <circle id="cell-final-rating" cx="1000" cy="470" r="25" 
            fill="#48bb78" stroke="#2f855a" stroke-width="3"/>
    <text x="1000" y="465" text-anchor="middle" fill="#ffffff" font-size="9" font-weight="bold">
      Rating
    </text>
    <text id="cell-rating-value" x="1000" y="480" text-anchor="middle" fill="#ffffff" font-size="11">
      A+
    </text>
  </g>
  
  <!-- Legend -->
  <g id="legend">
    <rect x="50" y="540" width="1100" height="50" rx="5" 
          fill="#1a202c" stroke="#2d3748" stroke-width="1"/>
    <text x="600" y="560" text-anchor="middle" fill="#ffffff" font-size="12" font-weight="bold">
      Flow Legend
    </text>
    
    <!-- Color Legend -->
    <circle cx="100" cy="575" r="6" fill="#4299e1"/>
    <text x="115" y="580" fill="#ffffff" font-size="9">Input/Context</text>
    
    <circle cx="220" cy="575" r="6" fill="#48bb78"/>
    <text x="235" y="580" fill="#ffffff" font-size="9">Success/Quality</text>
    
    <circle cx="340" cy="575" r="6" fill="#ed8936"/>
    <text x="355" y="580" fill="#ffffff" font-size="9">Processing</text>
    
    <circle cx="450" cy="575" r="6" fill="#9f7aea"/>
    <text x="465" y="580" fill="#ffffff" font-size="9">Tools/Actions</text>
    
    <circle cx="570" cy="575" r="6" fill="#805ad5"/>
    <text x="585" y="580" fill="#ffffff" font-size="9">Evaluation</text>
    
    <circle cx="680" cy="575" r="6" fill="#38b2ac"/>
    <text x="695" y="580" fill="#ffffff" font-size="9">Output</text>
    
    <text x="850" y="580" fill="#a0aec0" font-size="9">
      Flow: Input → Agent Processing → Tool Execution → Quality Assessment → Output
    </text>
  </g>
</svg>
