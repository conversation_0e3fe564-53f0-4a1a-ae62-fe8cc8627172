{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "success_rate"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "semantic_score"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}, {"id": "max", "value": 10}]}]}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date,\n  agent_role,\n  workflow_type,\n  llm_model,\n  llm_config_name,\n  llm_temperature,\n  prompt_version,\n  ROUND(success_rate::numeric, 3) as success_rate,\n  ROUND(semantic_score::numeric, 2) as semantic_score,\n  ROUND(estimated_cost::numeric, 6) as cost,\n  trust_level,\n  mood_valence,\n  stress_level,\n  performance_category\nFROM grafana_llm_performance\nWHERE $__timeFilter(execution_date)\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$workflow_type' = 'All' OR workflow_type = '$workflow_type')\nORDER BY execution_date DESC\nLIMIT 50", "refId": "A"}], "title": "Detailed Benchmark Results with LLM Configuration & Context", "type": "table"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date as time,\n  AVG(success_rate * 100) as \"Success Rate %\",\n  CONCAT(llm_model, ' (', llm_config_name, ')') as series\nFROM grafana_llm_performance\nWHERE $__timeFilter(execution_date)\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$workflow_type' = 'All' OR workflow_type = '$workflow_type')\nGROUP BY execution_date, llm_model, llm_config_name\nORDER BY execution_date", "refId": "A"}], "title": "Success Rate by LLM Configuration", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_success_rate"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_cost"}, "properties": [{"id": "unit", "value": "currencyUSD"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 3, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  llm_config_name,\n  llm_model,\n  llm_temperature,\n  ROUND(AVG(success_rate)::numeric, 3) as avg_success_rate,\n  ROUND(AVG(semantic_score)::numeric, 2) as avg_semantic_score,\n  ROUND(AVG(estimated_cost)::numeric, 6) as avg_cost,\n  ROUND(AVG(cost_per_success)::numeric, 6) as avg_cost_per_success,\n  COUNT(*) as total_runs\nFROM grafana_llm_performance\nWHERE $__timeFilter(execution_date)\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$workflow_type' = 'All' OR workflow_type = '$workflow_type')\nGROUP BY llm_config_name, llm_model, llm_temperature\nORDER BY avg_success_rate DESC", "refId": "A"}], "title": "LLM Configuration Performance Analysis", "type": "table"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "full_prompt"}, "properties": [{"id": "custom.width", "value": 500}, {"id": "custom.cellOptions", "value": {"type": "auto", "wrapText": true}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "raw_conversation_data"}, "properties": [{"id": "custom.width", "value": 600}, {"id": "custom.cellOptions", "value": {"type": "json-view"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "success_rate"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}]}, "gridPos": {"h": 16, "w": 24, "x": 0, "y": 20}, "id": 4, "options": {"cellHeight": "lg", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  run_id,\n  execution_date,\n  agent_role,\n  workflow_type,\n  llm_config_name,\n  prompt_version,\n  ROUND(success_rate::numeric, 3) as success_rate,\n  ROUND(semantic_score::numeric, 2) as semantic_score,\n  scenario_name,\n  prompt_length,\n  total_input_tokens,\n  total_output_tokens,\n  LEFT(full_prompt, 200) || '...' as prompt_preview,\n  full_prompt,\n  raw_conversation_data\nFROM grafana_detailed_benchmark_analysis\nWHERE $__timeFilter(execution_date)\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$workflow_type' = 'All' OR workflow_type = '$workflow_type')\nORDER BY execution_date DESC\nLIMIT 5", "refId": "A"}], "title": "Detailed Analysis - Prompt Content & Conversation Data", "type": "table"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": ["benchmark", "advanced", "analytics"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT llm_model as value, llm_model as text FROM grafana_llm_performance ORDER BY text", "hide": 0, "includeAll": false, "label": "LLM Model", "multi": false, "name": "llm_model", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT llm_model as value, llm_model as text FROM grafana_llm_performance ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT agent_role as value, agent_role as text FROM grafana_llm_performance ORDER BY text", "hide": 0, "includeAll": false, "label": "Agent Role", "multi": false, "name": "agent_role", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT agent_role as value, agent_role as text FROM grafana_llm_performance ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT workflow_type as value, workflow_type as text FROM grafana_llm_performance ORDER BY text", "hide": 0, "includeAll": false, "label": "Workflow Type", "multi": false, "name": "workflow_type", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT workflow_type as value, workflow_type as text FROM grafana_llm_performance ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Advanced Benchmark Analytics", "uid": "advanced-benchmark-analytics", "version": 2, "weekStart": ""}