{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "currencyUSD"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date_hour as time,\n  SUM(estimated_cost) as \"Total Cost\",\n  llm_model\nFROM grafana_cost_analytics\nWHERE $__timeFilter(execution_date)\nGROUP BY execution_date_hour, llm_model\nORDER BY execution_date_hour", "refId": "A", "select": [[{"params": ["estimated_cost"], "type": "column"}]], "table": "grafana_cost_analytics", "timeColumn": "execution_date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Hourly Cost by LLM Model", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date_hour as time,\n  SUM(total_tokens) as \"Total Tokens\",\n  llm_model\nFROM grafana_cost_analytics\nWHERE $__timeFilter(execution_date)\nGROUP BY execution_date_hour, llm_model\nORDER BY execution_date_hour", "refId": "A", "select": [[{"params": ["total_tokens"], "type": "column"}]], "table": "grafana_cost_analytics", "timeColumn": "execution_date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Token Usage by LLM Model", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_cost_per_token"}, "properties": [{"id": "unit", "value": "currencyUSD"}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "total_cost"}, "properties": [{"id": "unit", "value": "currencyUSD"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "id": 3, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  llm_model,\n  ROUND(SUM(estimated_cost)::numeric, 4) as total_cost,\n  SUM(total_tokens) as total_tokens,\n  ROUND(AVG(cost_per_token)::numeric, 8) as avg_cost_per_token,\n  ROUND(AVG(tokens_per_second)::numeric, 2) as avg_tokens_per_second,\n  COUNT(*) as total_runs\nFROM grafana_cost_analytics\nWHERE $__timeFilter(execution_date)\nGROUP BY llm_model\nORDER BY total_cost DESC", "refId": "A", "select": [[{"params": ["llm_model"], "type": "column"}]], "table": "grafana_cost_analytics", "timeColumn": "execution_date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Cost Efficiency by LLM Model", "type": "table"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": ["benchmark", "cost", "analytics"], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Cost Analytics Dashboard", "uid": "cost-analytics-dashboard", "version": 1, "weekStart": ""}