{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date as time,\n  mean_duration as \"Total Duration (ms)\",\n  scenario_name\nFROM main_benchmarkrun br\nJOIN main_benchmarkscenario bs ON br.scenario_id = bs.id\nWHERE $__timeFilter(execution_date)\nORDER BY execution_date", "refId": "A", "select": [[{"params": ["mean_duration"], "type": "column"}]], "table": "main_benchmarkrun", "timeColumn": "execution_date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Benchmark Total Duration Over Time", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  bs.name as scenario,\n  AVG(br.mean_duration) as avg_duration\nFROM main_benchmarkrun br\nJOIN main_benchmarkscenario bs ON br.scenario_id = bs.id\nWHERE $__timeFilter(execution_date)\nGROUP BY bs.name\nORDER BY avg_duration DESC", "refId": "A", "select": [[{"params": ["mean_duration"], "type": "column"}]], "table": "main_benchmarkrun", "timeColumn": "execution_date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Average Duration by <PERSON><PERSON><PERSON>", "type": "piechart"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "id": 3, "options": {"showHeader": true}, "pluginVersion": "8.5.0", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "WITH stage_data AS (\n  SELECT \n    br.id,\n    br.execution_date,\n    bs.name as scenario_name,\n    ga.role as agent_role,\n    br.mean_duration as total_duration_ms,\n    br.stage_performance_details,\n    jsonb_object_keys(br.stage_performance_details) as stage_name\n  FROM main_benchmarkrun br\n  JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id\n  JOIN main_genericagent ga ON br.agent_definition_id = ga.id\n  WHERE $__timeFilter(execution_date)\n    AND br.stage_performance_details IS NOT NULL\n    AND br.stage_performance_details != '{}'\n    AND ('$scenario' = 'All' OR bs.name IN ($scenario))\n    AND ('$agent_role' = 'All' OR ga.role IN ($agent_role))\n)\nSELECT \n  scenario_name as \"<PERSON><PERSON><PERSON>\",\n  agent_role as \"Agent Role\",\n  stage_name as \"Stage\",\n  ROUND((stage_performance_details->stage_name->>'mean_ms')::numeric, 2) as \"Mean Duration (ms)\",\n  ROUND((stage_performance_details->stage_name->>'median_ms')::numeric, 2) as \"Median Duration (ms)\",\n  ROUND((stage_performance_details->stage_name->>'min_ms')::numeric, 2) as \"Min Duration (ms)\",\n  ROUND((stage_performance_details->stage_name->>'max_ms')::numeric, 2) as \"Max Duration (ms)\",\n  ROUND((stage_performance_details->stage_name->>'std_dev_ms')::numeric, 2) as \"Std Dev (ms)\",\n  (stage_performance_details->stage_name->>'count')::integer as \"Count\",\n  ROUND(((stage_performance_details->stage_name->>'mean_ms')::numeric / total_duration_ms * 100), 1) as \"% of Total\"\nFROM stage_data\nORDER BY scenario_name, agent_role, (stage_performance_details->stage_name->>'mean_ms')::numeric DESC", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Stage Performance Breakdown", "type": "table"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "WITH stage_data AS (\n  SELECT \n    br.execution_date,\n    bs.name as scenario_name,\n    ga.role as agent_role,\n    br.stage_performance_details,\n    jsonb_object_keys(br.stage_performance_details) as stage_name\n  FROM main_benchmarkrun br\n  JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id\n  JOIN main_genericagent ga ON br.agent_definition_id = ga.id\n  WHERE $__timeFilter(execution_date)\n    AND br.stage_performance_details IS NOT NULL\n    AND br.stage_performance_details != '{}'\n    AND ('$scenario' = 'All' OR bs.name IN ($scenario))\n    AND ('$agent_role' = 'All' OR ga.role IN ($agent_role))\n)\nSELECT \n  execution_date as time,\n  (stage_performance_details->stage_name->>'mean_ms')::numeric as value,\n  stage_name as metric\nFROM stage_data\nORDER BY execution_date, stage_name", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Stage Duration Over Time (Stacked)", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Cost ($)"}, "properties": [{"id": "unit", "value": "currencyUSD"}, {"id": "custom.axisPlacement", "value": "right"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date as time,\n  COALESCE(total_input_tokens, 0) + COALESCE(total_output_tokens, 0) as \"Total Tokens\",\n  COALESCE(estimated_cost, 0) as \"Cost ($)\",\n  bs.name as scenario\nFROM main_benchmarkrun br\nJOIN main_benchmarkscenario bs ON br.scenario_id = bs.id\nJOIN main_genericagent ga ON br.agent_definition_id = ga.id\nWHERE $__timeFilter(execution_date)\n  AND ('$scenario' = 'All' OR bs.name IN ($scenario))\n  AND ('$agent_role' = 'All' OR ga.role IN ($agent_role))\nORDER BY execution_date", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Token Usage and Cost Over Time", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": ["benchmark", "timing", "performance", "stages"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT DISTINCT bs.name FROM main_benchmarkscenario bs ORDER BY bs.name", "hide": 0, "includeAll": true, "label": "<PERSON><PERSON><PERSON>", "multi": true, "name": "scenario", "options": [], "query": "SELECT DISTINCT bs.name FROM main_benchmarkscenario bs ORDER BY bs.name", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT DISTINCT ga.role FROM main_genericagent ga ORDER BY ga.role", "hide": 0, "includeAll": true, "label": "Agent Role", "multi": true, "name": "agent_role", "options": [], "query": "SELECT DISTINCT ga.role FROM main_genericagent ga ORDER BY ga.role", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Benchmark Timing Analysis", "uid": "benchmark-timing-analysis", "version": 1, "weekStart": ""}