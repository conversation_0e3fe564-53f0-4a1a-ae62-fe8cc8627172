{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "full_prompt_content"}, "properties": [{"id": "custom.width", "value": 600}, {"id": "custom.cellOptions", "value": {"type": "auto", "wrapText": true}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "prompt_effectiveness_score"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}, {"id": "max", "value": 10}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "success_rate"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}]}, "gridPos": {"h": 16, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "lg", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date,\n  agent_role,\n  workflow_type,\n  llm_config_name,\n  prompt_version,\n  ROUND(prompt_effectiveness_score::numeric, 2) as prompt_effectiveness_score,\n  ROUND(success_rate::numeric, 3) as success_rate,\n  ROUND(semantic_score::numeric, 2) as semantic_score,\n  prompt_length,\n  prompt_style,\n  full_prompt_content\nFROM grafana_prompt_content\nWHERE $__timeFilter(execution_date)\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\nORDER BY execution_date DESC\nLIMIT 20", "refId": "A"}], "title": "Prompt Content Analysis - Full Prompt Text with Performance Metrics", "type": "table"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": ["benchmark", "prompt-analysis", "content"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT DISTINCT agent_role FROM grafana_prompt_content ORDER BY agent_role", "hide": 0, "includeAll": false, "label": "Agent Role", "multi": false, "name": "agent_role", "options": [], "query": "SELECT DISTINCT agent_role FROM grafana_prompt_content ORDER BY agent_role", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT DISTINCT llm_model FROM grafana_prompt_content ORDER BY llm_model", "hide": 0, "includeAll": false, "label": "LLM Model", "multi": false, "name": "llm_model", "options": [], "query": "SELECT DISTINCT llm_model FROM grafana_prompt_content ORDER BY llm_model", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Prompt Content Analysis", "uid": "prompt-content-analysis", "version": 1, "weekStart": ""}