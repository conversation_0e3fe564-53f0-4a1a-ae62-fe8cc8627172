{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_success_rate"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_semantic_score"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}, {"id": "max", "value": 10}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  llm_config_name,\n  llm_model,\n  trust_phase,\n  mood_quadrant,\n  stress_category,\n  ROUND(AVG(success_rate)::numeric, 3) as avg_success_rate,\n  ROUND(AVG(semantic_score)::numeric, 2) as avg_semantic_score,\n  ROUND(AVG(estimated_cost)::numeric, 6) as avg_cost,\n  COUNT(*) as runs,\n  context_performance_category\nFROM grafana_contextual_evaluation\nWHERE $__timeFilter(execution_date)\nGROUP BY llm_config_name, llm_model, trust_phase, mood_quadrant, stress_category, context_performance_category\nORDER BY avg_success_rate DESC", "refId": "A"}], "title": "LLM Configuration Performance by Context - Shows when each config performs well/poorly", "type": "table"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date as time,\n  AVG(success_rate * 100) as \"Success Rate %\",\n  CONCAT(trust_phase, ' - ', mood_quadrant) as series\nFROM grafana_contextual_evaluation\nWHERE $__timeFilter(execution_date)\nGROUP BY execution_date, trust_phase, mood_quadrant\nORDER BY execution_date", "refId": "A"}], "title": "Performance Trends by Context Combination", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_success_rate"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 3, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  CASE \n    WHEN avg_success_rate >= 0.8 THEN 'High Performance Contexts'\n    WHEN avg_success_rate >= 0.6 THEN 'Good Performance Contexts'\n    WHEN avg_success_rate >= 0.4 THEN 'Fair Performance Contexts'\n    ELSE 'Poor Performance Contexts'\n  END as performance_tier,\n  CONCAT(trust_phase, ' + ', mood_quadrant, ' + ', stress_category) as context_combination,\n  ROUND(avg_success_rate::numeric, 3) as avg_success_rate,\n  total_runs\nFROM (\n  SELECT\n    trust_phase,\n    mood_quadrant,\n    stress_category,\n    AVG(success_rate) as avg_success_rate,\n    COUNT(*) as total_runs\n  FROM grafana_contextual_evaluation\n  WHERE $__timeFilter(execution_date)\n  GROUP BY trust_phase, mood_quadrant, stress_category\n  HAVING COUNT(*) >= 2\n) subq\nORDER BY avg_success_rate DESC", "refId": "A"}], "title": "Context Combinations Ranked by Performance - Actionable Insights", "type": "table"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": ["benchmark", "contextual", "evaluation"], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Contextual Evaluation Insights", "uid": "contextual-evaluation-insights", "version": 1, "weekStart": ""}