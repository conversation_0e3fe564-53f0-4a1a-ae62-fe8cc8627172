{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "description": "Main chronological event stream showing all benchmark events in sequence with intelligent filtering", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 20, "w": 16, "x": 0, "y": 0}, "id": 1, "options": {"showTime": true, "showLabels": true, "showCommonLabels": false, "wrapLogMessage": true, "prettifyLogMessage": true, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  event_timestamp as time,\n  \n  -- Structured log message with context\n  CONCAT(\n    '🔄 [', UPPER(event_type), '] ',\n    event_name,\n    CASE \n      WHEN event_type = 'stage' THEN \n        CONCAT(' → ', (event_details->>'status'), \n               CASE WHEN (event_details->>'mean_duration_ms')::float > 0 \n                    THEN CONCAT(' (', ROUND((event_details->>'mean_duration_ms')::numeric, 0), 'ms avg)')\n                    ELSE '' END,\n               CASE WHEN (event_details->>'count')::int > 1\n                    THEN CONCAT(' [', (event_details->>'count'), ' runs]')\n                    ELSE '' END)\n      WHEN event_type = 'tool_call' THEN \n        CONCAT(' → ', (event_details->>'call_count'), ' calls | ', \n               UPPER((event_details->>'effectiveness')), ' effectiveness',\n               CASE WHEN (event_details->>'effectiveness') = 'high' THEN ' ✅'\n                    WHEN (event_details->>'effectiveness') = 'low' THEN ' ⚠️'\n                    ELSE '' END)\n      WHEN event_type = 'evaluation' THEN \n        CONCAT(' → Score: ', ROUND((event_details->>'semantic_score')::numeric, 1), '/10 | ', \n               UPPER((event_details->>'quality_category')),\n               CASE WHEN (event_details->>'semantic_score')::float >= 8 THEN ' 🎯'\n                    WHEN (event_details->>'semantic_score')::float < 4 THEN ' 🔴'\n                    ELSE ' 🟡' END)\n      ELSE ''\n    END,\n    ' | Run: ', run_id, ' | ', scenario_name\n  ) as message,\n  \n  -- Dynamic log level based on performance and context\n  CASE \n    WHEN event_type = 'stage' AND (event_details->>'error') IS NOT NULL THEN 'error'\n    WHEN event_type = 'stage' AND (event_details->>'status') = 'FAILED' THEN 'error'\n    WHEN event_type = 'stage' AND (event_details->>'mean_duration_ms')::float > 30000 THEN 'warn'\n    WHEN event_type = 'tool_call' AND (event_details->>'effectiveness') = 'low' THEN 'warn'\n    WHEN event_type = 'tool_call' AND (event_details->>'call_count')::int > 10 THEN 'warn'\n    WHEN event_type = 'evaluation' AND (event_details->>'semantic_score')::float < 4 THEN 'error'\n    WHEN event_type = 'evaluation' AND (event_details->>'semantic_score')::float < 6 THEN 'warn'\n    WHEN event_type = 'evaluation' AND (event_details->>'semantic_score')::float >= 8 THEN 'info'\n    WHEN event_type = 'stage' AND (event_details->>'status') = 'COMPLETED' THEN 'info'\n    ELSE 'debug'\n  END as level,\n  \n  -- Rich labels for filtering and analysis\n  run_id::text,\n  scenario_name,\n  agent_role,\n  llm_model,\n  llm_config_name,\n  event_type,\n  ROUND(success_rate::numeric, 2)::text as success_rate,\n  ROUND(semantic_score::numeric, 1)::text as semantic_score,\n  trust_level::text,\n  CASE \n    WHEN mood_valence > 0.3 THEN 'positive'\n    WHEN mood_valence < -0.3 THEN 'negative'\n    ELSE 'neutral'\n  END as mood_category,\n  CASE \n    WHEN stress_level < 30 THEN 'low_stress'\n    WHEN stress_level < 70 THEN 'medium_stress'\n    ELSE 'high_stress'\n  END as stress_category,\n  \n  -- Performance indicators as labels\n  CASE \n    WHEN success_rate >= 0.8 THEN 'high_performance'\n    WHEN success_rate >= 0.5 THEN 'medium_performance'\n    ELSE 'low_performance'\n  END as performance_category,\n  \n  -- Full event context for drill-down\n  event_details::text as event_details_json\n  \nFROM grafana_chronological_events\nWHERE $__timeFilter(event_timestamp)\n  AND ('$run_id' = 'All' OR run_id::text = '$run_id')\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\n  AND ('$event_type' = 'All' OR event_type = '$event_type')\n  AND ('$performance_filter' = 'All' OR \n       CASE \n         WHEN '$performance_filter' = 'high' AND success_rate >= 0.8 THEN true\n         WHEN '$performance_filter' = 'medium' AND success_rate >= 0.5 AND success_rate < 0.8 THEN true\n         WHEN '$performance_filter' = 'low' AND success_rate < 0.5 THEN true\n         ELSE false\n       END)\nORDER BY event_timestamp DESC, run_id\nLIMIT 500", "refId": "A"}], "title": "📊 Chronological Event Stream", "type": "logs"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "description": "Event type distribution and performance correlation", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 8, "x": 16, "y": 0}, "id": 2, "options": {"legend": {"displayMode": "visible", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  event_type,\n  COUNT(*) as event_count\nFROM grafana_chronological_events\nWHERE $__timeFilter(event_timestamp)\n  AND ('$run_id' = 'All' OR run_id::text = '$run_id')\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\nGROUP BY event_type\nORDER BY event_count DESC", "refId": "A"}], "title": "📈 Event Type Distribution", "type": "piechart"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": ["benchmark", "chronological", "logs", "enhanced"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT run_id::text as value, CONCAT('Run ', run_id) as text FROM grafana_chronological_events ORDER BY text", "hide": 0, "includeAll": false, "label": "Benchmark Run", "multi": false, "name": "run_id", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT run_id::text as value, CONCAT('Run ', run_id) as text FROM grafana_chronological_events ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT agent_role as value, agent_role as text FROM grafana_chronological_events ORDER BY text", "hide": 0, "includeAll": false, "label": "Agent Role", "multi": false, "name": "agent_role", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT agent_role as value, agent_role as text FROM grafana_chronological_events ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT llm_model as value, llm_model as text FROM grafana_chronological_events ORDER BY text", "hide": 0, "includeAll": false, "label": "LLM Model", "multi": false, "name": "llm_model", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT llm_model as value, llm_model as text FROM grafana_chronological_events ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT event_type as value, event_type as text FROM grafana_chronological_events ORDER BY text", "hide": 0, "includeAll": false, "label": "Event Type", "multi": false, "name": "event_type", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT event_type as value, event_type as text FROM grafana_chronological_events ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "hide": 0, "includeAll": false, "label": "Performance Filter", "multi": false, "name": "performance_filter", "options": [{"selected": true, "text": "All", "value": "All"}, {"selected": false, "text": "High Performance (≥80%)", "value": "high"}, {"selected": false, "text": "Medium Performance (50-80%)", "value": "medium"}, {"selected": false, "text": "Low Performance (<50%)", "value": "low"}], "query": "All,High Performance (≥80%),Medium Performance (50-80%),Low Performance (<50%)", "queryValue": "All,high,medium,low", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "🔍 Enhanced Chronological Analysis Dashboard", "uid": "enhanced-chronological-analysis", "version": 1, "weekStart": ""}