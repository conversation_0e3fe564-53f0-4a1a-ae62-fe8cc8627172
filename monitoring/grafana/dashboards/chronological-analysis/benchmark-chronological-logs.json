{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "description": "Chronological decomposition of benchmark run events showing stages, tool calls, and evaluations in sequence", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 16, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"showTime": true, "showLabels": true, "showCommonLabels": false, "wrapLogMessage": true, "prettifyLogMessage": true, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  event_timestamp as time,\n  CONCAT(\n    '[', UPPER(event_type), '] ',\n    event_name,\n    CASE \n      WHEN event_type = 'stage' THEN \n        CONCAT(' (', (event_details->>'status'), \n               CASE WHEN (event_details->>'mean_duration_ms')::float > 0 \n                    THEN CONCAT(', ', ROUND((event_details->>'mean_duration_ms')::numeric, 0), 'ms')\n                    ELSE '' END, ')')\n      WHEN event_type = 'tool_call' THEN \n        CONCAT(' (', (event_details->>'call_count'), ' calls, ', \n               (event_details->>'effectiveness'), ' effectiveness)')\n      WHEN event_type = 'evaluation' THEN \n        CONCAT(' (score: ', ROUND((event_details->>'semantic_score')::numeric, 1), \n               ', ', (event_details->>'quality_category'), ')')\n      ELSE ''\n    END\n  ) as message,\n  \n  -- Log level based on event type and performance\n  CASE \n    WHEN event_type = 'stage' AND (event_details->>'error') IS NOT NULL THEN 'error'\n    WHEN event_type = 'stage' AND (event_details->>'status') = 'FAILED' THEN 'error'\n    WHEN event_type = 'stage' AND (event_details->>'status') = 'COMPLETED' THEN 'info'\n    WHEN event_type = 'tool_call' AND (event_details->>'effectiveness') = 'low' THEN 'warn'\n    WHEN event_type = 'tool_call' AND (event_details->>'effectiveness') = 'high' THEN 'info'\n    WHEN event_type = 'evaluation' AND (event_details->>'semantic_score')::float < 4 THEN 'error'\n    WHEN event_type = 'evaluation' AND (event_details->>'semantic_score')::float >= 8 THEN 'info'\n    ELSE 'debug'\n  END as level,\n  \n  -- Labels for filtering and grouping\n  run_id::text as run_id,\n  scenario_name,\n  agent_role,\n  llm_model,\n  llm_config_name,\n  event_type,\n  \n  -- Performance context as labels\n  ROUND(success_rate::numeric, 2)::text as success_rate,\n  ROUND(semantic_score::numeric, 1)::text as semantic_score,\n  \n  -- Context variables as labels\n  trust_level::text as trust_level,\n  ROUND(mood_valence::numeric, 2)::text as mood_valence,\n  stress_level::text as stress_level,\n  \n  -- Full event details as JSON for drill-down\n  event_details::text as event_details_json\n  \nFROM grafana_chronological_events\nWHERE $__timeFilter(event_timestamp)\n  AND ('$run_id' = 'All' OR run_id::text = '$run_id')\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\n  AND ('$event_type' = 'All' OR event_type = '$event_type')\nORDER BY event_timestamp DESC, run_id\nLIMIT 1000", "refId": "A"}], "title": "Benchmark Run Chronological Events", "type": "logs"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "description": "Summary of benchmark runs with key performance metrics", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "success_rate"}, "properties": [{"id": "custom.cellOptions", "value": {"type": "gauge", "mode": "basic"}}, {"id": "max", "value": 1}, {"id": "min", "value": 0}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "semantic_score"}, "properties": [{"id": "custom.cellOptions", "value": {"type": "gauge", "mode": "basic"}}, {"id": "max", "value": 10}, {"id": "min", "value": 0}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT DISTINCT\n  run_id,\n  run_start_time as execution_date,\n  scenario_name,\n  agent_role,\n  llm_model,\n  llm_config_name,\n  ROUND(success_rate::numeric, 3) as success_rate,\n  ROUND(semantic_score::numeric, 2) as semantic_score,\n  ROUND(run_duration_ms::numeric, 0) as duration_ms,\n  total_input_tokens,\n  total_output_tokens,\n  ROUND(estimated_cost::numeric, 4) as cost,\n  trust_level,\n  ROUND(mood_valence::numeric, 2) as mood_valence,\n  stress_level\nFROM grafana_chronological_events\nWHERE $__timeFilter(event_timestamp)\n  AND ('$run_id' = 'All' OR run_id::text = '$run_id')\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\nORDER BY run_start_time DESC\nLIMIT 20", "refId": "A"}], "title": "Benchmark Run Summary", "type": "table"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": ["benchmark", "chronological", "logs", "events"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT run_id::text as value, CONCAT('Run ', run_id) as text FROM grafana_chronological_events ORDER BY text", "hide": 0, "includeAll": false, "label": "Benchmark Run", "multi": false, "name": "run_id", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT run_id::text as value, CONCAT('Run ', run_id) as text FROM grafana_chronological_events ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT agent_role as value, agent_role as text FROM grafana_chronological_events ORDER BY text", "hide": 0, "includeAll": false, "label": "Agent Role", "multi": false, "name": "agent_role", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT agent_role as value, agent_role as text FROM grafana_chronological_events ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT llm_model as value, llm_model as text FROM grafana_chronological_events ORDER BY text", "hide": 0, "includeAll": false, "label": "LLM Model", "multi": false, "name": "llm_model", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT llm_model as value, llm_model as text FROM grafana_chronological_events ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT event_type as value, event_type as text FROM grafana_chronological_events ORDER BY text", "hide": 0, "includeAll": false, "label": "Event Type", "multi": false, "name": "event_type", "options": [], "query": "SELECT 'All' as value, 'All' as text UNION SELECT DISTINCT event_type as value, event_type as text FROM grafana_chronological_events ORDER BY text", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Benchmark Chronological Analysis - Logs View", "uid": "benchmark-chronological-logs", "version": 1, "weekStart": ""}