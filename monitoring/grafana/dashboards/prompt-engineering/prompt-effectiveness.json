{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "prompt_effectiveness_score"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}, {"id": "max", "value": 10}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "success_rate"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}]}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date,\n  agent_role,\n  workflow_type,\n  llm_config_name,\n  prompt_version,\n  previous_prompt_version,\n  ROUND(prompt_effectiveness_score::numeric, 2) as prompt_effectiveness_score,\n  ROUND(success_rate::numeric, 3) as success_rate,\n  ROUND(semantic_score::numeric, 2) as semantic_score,\n  prompt_length,\n  CASE \n    WHEN previous_success_rate IS NOT NULL \n    THEN ROUND(((success_rate - previous_success_rate) * 100)::numeric, 1)\n    ELSE NULL\n  END as success_rate_change_pct,\n  CASE \n    WHEN previous_semantic_score IS NOT NULL \n    THEN ROUND((semantic_score - previous_semantic_score)::numeric, 2)\n    ELSE NULL\n  END as semantic_score_change\nFROM grafana_prompt_analytics\nWHERE $__timeFilter(execution_date)\nORDER BY execution_date DESC\nLIMIT 50", "refId": "A"}], "title": "Prompt Performance Analysis with Version Comparison - Shows prompt evolution impact", "type": "table"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date as time,\n  prompt_effectiveness_score as \"Effectiveness Score\",\n  CONCAT(agent_role, ' v', prompt_version) as series\nFROM grafana_prompt_analytics\nWHERE $__timeFilter(execution_date)\n  AND prompt_effectiveness_score IS NOT NULL\nORDER BY execution_date", "refId": "A"}], "title": "Prompt Effectiveness Trends by Version", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_effectiveness"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}, {"id": "max", "value": 10}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 3, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  CASE \n    WHEN prompt_length < 500 THEN 'Short (<500 chars)'\n    WHEN prompt_length < 1500 THEN 'Medium (500-1500 chars)'\n    WHEN prompt_length < 3000 THEN 'Long (1500-3000 chars)'\n    ELSE 'Very Long (>3000 chars)'\n  END as prompt_length_category,\n  ROUND(AVG(prompt_effectiveness_score)::numeric, 2) as avg_effectiveness,\n  ROUND(AVG(success_rate)::numeric, 3) as avg_success_rate,\n  ROUND(AVG(semantic_score)::numeric, 2) as avg_semantic_score,\n  ROUND(AVG(estimated_cost)::numeric, 6) as avg_cost,\n  COUNT(*) as total_runs,\n  'Optimize prompt length for better performance' as recommendation\nFROM grafana_prompt_analytics\nWHERE $__timeFilter(execution_date)\n  AND prompt_length IS NOT NULL\nGROUP BY prompt_length_category\nORDER BY avg_effectiveness DESC", "refId": "A"}], "title": "Prompt Length Analysis - Optimization Insights", "type": "table"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": ["benchmark", "prompt", "engineering"], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Prompt Effectiveness Dashboard", "uid": "prompt-effectiveness-dashboard", "version": 1, "weekStart": ""}