{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "conversation_data"}, "properties": [{"id": "custom.width", "value": 800}, {"id": "custom.cellOptions", "value": {"type": "json-view"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "scenario_input"}, "properties": [{"id": "custom.width", "value": 400}, {"id": "custom.cellOptions", "value": {"type": "json-view"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "success_rate"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "semantic_score"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}, {"id": "max", "value": 10}]}]}, "gridPos": {"h": 16, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "lg", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  run_id,\n  execution_date,\n  agent_role,\n  workflow_type,\n  llm_config_name,\n  scenario_name,\n  ROUND(success_rate::numeric, 3) as success_rate,\n  ROUND(semantic_score::numeric, 2) as semantic_score,\n  communication_quality,\n  total_input_tokens,\n  total_output_tokens,\n  scenario_input,\n  conversation_data\nFROM grafana_conversation_analysis\nWHERE $__timeFilter(execution_date)\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\n  AND conversation_data IS NOT NULL\n  AND conversation_data != '{}'\nORDER BY execution_date DESC\nLIMIT 10", "refId": "A"}], "title": "Conversation Flow Analysis - Raw Agent Communications", "type": "table"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "evaluation_details"}, "properties": [{"id": "custom.width", "value": 600}, {"id": "custom.cellOptions", "value": {"type": "json-view"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "stage_performance_details"}, "properties": [{"id": "custom.width", "value": 400}, {"id": "custom.cellOptions", "value": {"type": "json-view"}}]}]}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 16}, "id": 2, "options": {"cellHeight": "lg", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.5.2", "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  run_id,\n  execution_date,\n  agent_role,\n  scenario_name,\n  llm_config_name,\n  ROUND(semantic_score::numeric, 2) as semantic_score,\n  communication_quality,\n  evaluation_details,\n  stage_performance_details,\n  tool_breakdown\nFROM grafana_conversation_analysis\nWHERE $__timeFilter(execution_date)\n  AND ('$agent_role' = 'All' OR agent_role = '$agent_role')\n  AND ('$llm_model' = 'All' OR llm_model = '$llm_model')\n  AND (evaluation_details IS NOT NULL OR stage_performance_details IS NOT NULL)\nORDER BY execution_date DESC\nLIMIT 10", "refId": "A"}], "title": "Semantic Evaluation & Stage Performance Details", "type": "table"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": ["benchmark", "conversation", "agent-communication"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT DISTINCT agent_role FROM grafana_conversation_analysis ORDER BY agent_role", "hide": 0, "includeAll": false, "label": "Agent Role", "multi": false, "name": "agent_role", "options": [], "query": "SELECT DISTINCT agent_role FROM grafana_conversation_analysis ORDER BY agent_role", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "All"}, "datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "definition": "SELECT DISTINCT llm_model FROM grafana_conversation_analysis ORDER BY llm_model", "hide": 0, "includeAll": false, "label": "LLM Model", "multi": false, "name": "llm_model", "options": [], "query": "SELECT DISTINCT llm_model FROM grafana_conversation_analysis ORDER BY llm_model", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Conversation Flow Analysis", "uid": "conversation-flow-analysis", "version": 1, "weekStart": ""}