{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "postgres-benchmarks"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \n  event_type,\n  COUNT(*) as count\nFROM grafana_chronological_events \nWHERE $__timeFilter(event_timestamp)\nGROUP BY event_type\nORDER BY count DESC", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Event Type Distribution", "type": "piechart"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "postgres-benchmarks"}, "editorMode": "code", "format": "time_series", "rawQuery": true, "rawSql": "SELECT \n  event_timestamp as time,\n  COUNT(*) as events\nFROM grafana_chronological_events \nWHERE $__timeFilter(event_timestamp)\nGROUP BY event_timestamp\nORDER BY event_timestamp", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Events Over Time", "type": "timeseries"}], "refresh": "", "schemaVersion": 39, "tags": ["benchmark", "chronological", "events"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Benchmark Event Timeline", "uid": "benchmark-event-timeline", "version": 0, "weekStart": ""}