{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date as time,\n  AVG(success_rate * 100) as \"Success Rate %\",\n  llm_model\nFROM grafana_llm_performance\nWHERE $__timeFilter(execution_date)\nGROUP BY execution_date, llm_model\nORDER BY execution_date", "refId": "A", "select": [[{"params": ["success_rate"], "type": "column"}]], "table": "grafana_llm_performance", "timeColumn": "execution_date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Success Rate by LLM Model Over Time", "type": "timeseries"}, {"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "postgres", "uid": "postgres-benchmarks"}, "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  execution_date as time,\n  AVG(mean_duration_ms) as \"Mean Duration (ms)\",\n  llm_model\nFROM grafana_llm_performance\nWHERE $__timeFilter(execution_date)\nGROUP BY execution_date, llm_model\nORDER BY execution_date", "refId": "A", "select": [[{"params": ["mean_duration_ms"], "type": "column"}]], "table": "grafana_llm_performance", "timeColumn": "execution_date", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Response Time by LLM Model", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": ["benchmark", "llm", "performance"], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "LLM Performance Overview", "uid": "llm-performance-overview", "version": 1, "weekStart": ""}