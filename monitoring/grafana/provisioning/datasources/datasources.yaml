apiVersion: 1

datasources:
  - name: PostgreSQL-Benchmarks
    type: grafana-postgresql-datasource
    uid: postgres-benchmarks
    url: db:5432
    database: mydb
    user: postgres
    password: postgres
    isDefault: true
    editable: false
    jsonData:
      sslmode: 'disable' # Use 'require' or 'verify-full' for production
      maxOpenConns: 100
      maxIdleConns: 10
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: false
    secureJsonData:
      password: postgres
    version: 1
    readOnly: true
