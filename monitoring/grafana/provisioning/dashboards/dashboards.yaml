apiVersion: 1

providers:
  - name: 'Benchmark Analytics'
    orgId: 1
    folder: 'Benchmark Analytics'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/benchmark-analytics

  - name: 'LLM Performance'
    orgId: 1
    folder: 'LLM Performance'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/llm-performance

  - name: 'Prompt Engineering'
    orgId: 1
    folder: 'Prompt Engineering'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/prompt-engineering
  - name: 'prompt-analysis'
    orgId: 1
    folder: 'Prompt Analysis'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/prompt-analysis
  - name: 'conversation-analysis'
    orgId: 1
    folder: 'Conversation Analysis'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/conversation-analysis

  - name: 'Contextual Evaluation'
    orgId: 1
    folder: 'Contextual Evaluation'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/contextual-evaluation

  - name: 'Chronological Analysis'
    orgId: 1
    folder: 'Chronological Analysis'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/chronological-analysis

  - name: 'Interactive Tree'
    orgId: 1
    folder: 'Interactive Tree'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/interactive-tree
