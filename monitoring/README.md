# Monitoring & Analytics Infrastructure

This directory contains the complete monitoring and analytics infrastructure for the benchmark system, featuring Grafana dashboards for comprehensive visualization of LLM performance, prompt effectiveness, and contextual evaluation insights.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Benchmark     │    │    PostgreSQL    │    │     <PERSON>ana     │
│     Runs        │───▶│  Analytics Views │───▶│   Dashboards    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   Prometheus     │
                       │ (System Metrics) │
                       └──────────────────┘
```

## 📊 Dashboard Categories

### 1. LLM Performance (`/grafana/dashboards/llm-performance/`)
- **LLM Performance Overview**: Real-time success rates and response times
- Model comparison and trend analysis
- Performance categorization and alerting

### 2. Prompt Engineering (`/grafana/dashboards/prompt-engineering/`)
- **Prompt Effectiveness Dashboard**: Version comparison and A/B testing
- Prompt iteration tracking and optimization insights
- Agent role specialization analysis

### 3. Contextual Evaluation (`/grafana/dashboards/contextual-evaluation/`)
- **Contextual Insights Dashboard**: Trust phase and mood analysis
- Context variable impact assessment
- Adaptive evaluation performance metrics

### 4. Cost Analytics (`/grafana/dashboards/benchmark-analytics/`)
- **Cost Analytics Dashboard**: Resource optimization and budget tracking
- **Advanced Analytics**: Multi-dimensional filtering and analysis
- Token efficiency and cost-per-success metrics

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
# Run the automated setup script
./scripts/setup_grafana.sh
```

### Option 2: Manual Setup
```bash
# Start services
cd backend
docker-compose up -d grafana prometheus

# Apply database migrations
docker-compose run --rm web python manage.py migrate

# Access Grafana
open http://localhost:3000
```

## 🔧 Configuration

### Data Sources
- **PostgreSQL-Benchmarks**: Main benchmark database connection
- **Prometheus**: System metrics (optional)

### Provisioning
All dashboards and data sources are automatically provisioned through:
- `/grafana/provisioning/datasources/`: Data source configurations
- `/grafana/provisioning/dashboards/`: Dashboard provider settings

### Environment Variables
```bash
# Grafana Configuration
GF_SECURITY_ADMIN_PASSWORD=admin
GF_SECURITY_ADMIN_USER=admin
GF_PATHS_PROVISIONING=/etc/grafana/provisioning

# Database Connection
DATABASE_URL=************************************/mydb
```

## 📈 Key Metrics & Insights

### LLM Strengths & Weaknesses
- **Success Rate Trends**: Identify consistent vs. inconsistent models
- **Response Time Analysis**: Performance optimization opportunities
- **Cost Efficiency**: ROI analysis by model and configuration
- **Context Sensitivity**: Performance across different user states

### Prompt Optimization
- **Effectiveness Scoring**: Combined success rate and semantic quality
- **Version Comparison**: A/B testing results and improvements
- **Agent Specialization**: Role-specific performance patterns
- **Iteration Tracking**: Prompt evolution and optimization paths

### Contextual Insights
- **Trust Phase Analysis**: Foundation/Expansion/Integration performance
- **Mood Impact**: Emotional state correlation with effectiveness
- **Environmental Factors**: Stress and time pressure effects
- **Adaptive Evaluation**: Context-aware quality assessment

## 🔍 Advanced Analytics

### Custom Queries
The system provides optimized database views for complex analytics:

```sql
-- LLM Performance Analysis
SELECT * FROM grafana_llm_performance 
WHERE execution_date >= NOW() - INTERVAL '24 hours';

-- Contextual Evaluation Insights
SELECT * FROM grafana_contextual_evaluation 
WHERE trust_phase = 'Integration';

-- Cost Optimization Analysis
SELECT * FROM grafana_cost_analytics 
WHERE cost_per_token < 0.001;

-- Prompt Effectiveness Tracking
SELECT * FROM grafana_prompt_analytics 
WHERE prompt_effectiveness_score > 8.0;
```

### Template Variables
Advanced dashboards support filtering by:
- LLM Model
- Agent Role
- Workflow Type
- Time Range
- Performance Category

## 🛠️ Customization

### Adding New Dashboards
1. Create JSON dashboard definition in appropriate folder
2. Update dashboard provider configuration
3. Restart Grafana service for provisioning

### Custom Metrics
Add new calculated fields to database views:
```sql
-- Example: Custom effectiveness metric
CASE 
    WHEN semantic_score IS NOT NULL AND success_rate IS NOT NULL
    THEN (semantic_score * 0.6 + success_rate * 10 * 0.4)
    ELSE NULL
END as custom_effectiveness_score
```

### Alerting
Configure alerts for:
- Performance degradation
- Cost threshold breaches
- Quality score drops
- Context-specific anomalies

## 🔒 Security & Best Practices

### Database Access
- Read-only PostgreSQL user for Grafana
- Restricted network access between containers
- Environment variable management for credentials

### Dashboard Security
- Authentication required for Grafana access
- Role-based access control (RBAC) ready
- Audit logging for dashboard changes

### Performance Optimization
- Indexed time columns for efficient queries
- Materialized views for complex calculations
- Query result caching for frequently accessed data

## 📚 Documentation

- **Integration Guide**: `/docs/backend/GRAFANA_INTEGRATION.md`
- **Contextual Evaluation**: `/docs/backend/CONTEXTUAL_EVALUATION_SYSTEM.md`
- **Database Schema**: `/docs/backend/BENCHMARK_SYSTEM.md`

## 🐛 Troubleshooting

### Common Issues

1. **Grafana Not Starting**
   ```bash
   docker-compose logs grafana
   # Check for port conflicts or permission issues
   ```

2. **Data Source Connection Failed**
   ```bash
   # Verify PostgreSQL is running
   docker-compose ps db
   
   # Test database connectivity
   docker-compose exec db pg_isready -U postgres
   ```

3. **Empty Dashboards**
   ```bash
   # Check for benchmark data
   docker-compose exec db psql -U postgres -d mydb -c "SELECT COUNT(*) FROM main_benchmarkrun;"
   
   # Verify view creation
   docker-compose exec db psql -U postgres -d mydb -c "\dv grafana_*"
   ```

### Performance Issues
- Monitor query execution times in Grafana query inspector
- Check database indexing on frequently queried columns
- Consider data retention policies for large datasets

## 🚀 Future Enhancements

### Planned Features
- **Machine Learning Integration**: Predictive analytics and forecasting
- **Real-time Streaming**: Live benchmark result updates
- **Advanced Alerting**: Context-aware performance notifications
- **Export Capabilities**: Automated report generation

### Integration Opportunities
- **CI/CD Pipeline**: Automated performance reporting
- **Slack/Teams**: Alert notifications
- **A/B Testing Framework**: Statistical significance testing
- **Cost Optimization**: Automated recommendations

## 📞 Support

For issues or questions:
1. Check troubleshooting section above
2. Review service logs: `docker-compose logs [service]`
3. Verify configuration files and environment variables
4. Consult Grafana documentation for advanced features

---

**Happy Analyzing!** 📊✨

Use these dashboards to gain deep insights into your LLM performance, optimize prompts, and understand contextual evaluation patterns.
