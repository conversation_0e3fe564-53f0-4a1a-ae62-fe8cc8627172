[flake8]
max-line-length = 100
exclude = .git,__pycache__,build,dist,migrations
ignore = 
    # Temporarily ignored
    # line too long
    E501,
    # trailing whitespace
    W291,
    # no newline at end of file
    W292,
    # blank line contains whitespace
    W293,
    # expected 2 blank lines
    E302,
    # expected 2 blank lines after class/function
    E305

# Only check files changed in the current branch compared to main
per-file-ignores =
    */models.py:E501
    */__init__.py:F403,F401,F405