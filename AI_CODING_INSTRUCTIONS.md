# Game of Life - AI Coding Assistant Global Instructions

## Mandatory Preparation & Context Awareness

### Project Initialization
ALWAYS begin every new conversation by:
1. Reading PLANNING.md - Contains project architecture, goals & constraints
2. Reading README.md - Contains practical setup info
3. Checking TASK.md - Contains current tasks to be worked on

**Why?** This ensures you understand context before making suggestions that might not fit with project architecture or standards.

### Project Mental Model
The Game of Life uses:
- WebSockets (not REST) for frontend-backend communication via ApiContract.md spec
- Django + Channels backend with multi-agent LangGraph system
- React (v19+) + TypeScript frontend with modular component structure

## Workflow & Task Management

### Task Handling
Always check TASK.md before starting. Do not hallucinate tasks.

If asked to work on a new feature:
1. Check if task exists in TASK.md
2. If it doesn't exist, suggest adding it first
3. When complete, mark it as done in TASK.md

**Important:**
- Add any discovered sub-tasks to a "Discovered During Work" section

### Conversation Management
Start fresh conversations often - don't rely on long history

**Why?** Long conversation threads:
- Degrade response quality
- Can lead to context confusion
- Are harder for the human developer to track

When you are not sure about something, check the documentation first :
- in '/docs/global' for general purpose
- in /backend/docs

### Task Focus
Focus on one task per message - avoid multi-part requests

**Example:**
Instead of: "Update WebSocketManager and also fix the unit tests and then add documentation"

Suggest breaking this into:
1. "First, let's update WebSocketManager"
2. "Now, let's fix the related unit tests"
3. "Finally, let's update the documentation"

## Code Structure & Modularity

### File Size Limit
**ENFORCE 500-LINE LIMIT FOR ALL FILES**

If a file approaches this limit, proactively suggest refactoring by:
- Extracting components/functions into separate files
- Creating utility modules for repeated logic
- Splitting classes/modules by responsibility

### Implementation Patterns
Before implementing a feature:
1. Locate similar implementations in the codebase
2. Analyze patterns, naming conventions, and structure
3. Apply these patterns to new code

### React Best Practices (Frontend)
- Use functional components with hooks (not classes)
- Create stateful logic with custom hooks in hooks/
- Use Context for shared state
- Follow single-responsibility principle
- Use TypeScript interfaces/types for all props
- Avoid any - use proper types or generics

### Django Best Practices (Backend)
- Place code in appropriate app (main, activity, user)
- Use Django ORM correctly (with proper relationships)
- Use @database_sync_to_async for ORM in async code
- Keep views/services/models separated by responsibility
- Use Celery (`main/tasks/`) for background tasks, likely with Redis as the broker/backend (check `config/settings/` and `config/celery.py`)

### Multi-Agent LangGraph Patterns
- Extend AgentNodeBase for new agent nodes
- Follow patterns in agents/nodes/ directory
- Use proper tools registration system
- Maintain clear input/output schemas
- Utilize established memory management patterns (check base classes/context)

### WebSocket Communication
- STRICTLY follow ApiContract.md specification
- All message handling through WebSocketManager.ts in frontend
- All messages processed through consumers.py in backend, using the Channels layer (likely backed by Redis - see `channels-redis` in requirements)
- Implement proper reconnection, error handling, and timeout logic

## Testing Requirements

### Backend Testing (Python/Django/LangGraph)
- Use pytest
- Create tests in app's tests/ directory
- Mock external dependencies (DB, LLMs, etc.)
- Create at least 3 tests per feature:
  1. Happy path - Normal expected use
  2. Edge case - Boundary conditions
  3. Failure case - Error handling

### Frontend Testing (React/TypeScript)
- Use Jest/RTL for components and hooks
- Mock WebSockets using MockWebSocket.ts
- Test both success and error states

### Test Maintenance
When changing code behavior:
1. Identify affected tests in tests/
2. Update test expectations to match new behavior
3. Add new tests for any new functionality

## Style & Coding Conventions

### Python (Backend)
- Follow PEP8 standards
- Use type hints for all functions and methods
- Write Google-style docstrings for functions, classes, methods
- Use meaningful variable/parameter names
- Add "# Reason:" comments for complex logic
- Use Django's ORM properly (select_related, prefetch_related, etc.)

### TypeScript/React (Frontend)
- Define interfaces for props, state, and data
- Avoid 'any' type - use proper types or generics
- Use JSDoc/TSDoc comments for functions/components
- Use React hooks correctly (follow rules of hooks)
- Add "// Reason:" comments for complex logic

## Documentation Requirements

### Code Update Documentation
When updating existing code:
- Don't get rid of existing comments: update them if necessary

### Feature Documentation
When adding new features:
- Update README.md for setup changes
- Add JSDoc/Google-style docstrings
- Update ApiContract.md for message format changes
- Document props/params in code

### Architectural Consistency
If the implementation significantly differs from the plan in PLANNING.md, ensure relevant documentation is updated

### Complex Logic Explanation
For non-obvious algorithms or patterns:
- Add explanatory comments using "# Reason:" or "// Reason:"
- Document why a particular approach was chosen
- Reference patterns or design principles being followed

## AI Behavior Rules

### Context Awareness
Never assume missing context - ask questions when uncertain about:
- Project-specific patterns
- Implementation details
- Requirements

### Systematic Approach
Before providing code or solutions:
1. Identify potentially relevant files
2. If necessary, request to read these files
3. Think step-by-step about integration with existing code
4. Explain thought process briefly before presenting code

### Strict Implementation Guidelines
- Never hallucinate code features, libraries, or APIs
- Only use documented libraries, APIs, and patterns
- Verify paths and imports before suggesting
- Do not delete existing code without explicit instruction

### Pedagogical Approach
- Explain the approach in plain language
- Compare with alternatives where appropriate
- Use analogies to clarify difficult concepts
- Reference relevant documentation or patterns

## Security Considerations

### Input Validation
Apply proper validation for:
- User inputs in frontend forms
- WebSocket message data
- LLM outputs before using them
- Agent tool inputs/outputs

### Sensitive Data Handling
- Never log sensitive information
- Don't include API keys/credentials in code
- Properly sanitize user inputs
- Follow secure patterns for authentication

### WebSocket Security
- Validate message formats
- Handle reconnection securely
- Implement proper error handling
- Ensure authentication is maintained

### Environment Management
- Do not suggest code that embeds secrets
- Always use environment variables for credentials
- Let the human developer manage credentials