@startuml
title Goali Governance with LangGraph Supervisor and Human-in-the-Loop

' Basic styling
skinparam backgroundColor white
skinparam roundCorner 10
skinparam ArrowColor black
skinparam shadowing false

' Color definitions
!define SUPERVISOR_COLOR #90caf9
!define AGENT_COLOR #a5d6a7
!define USER_COLOR #fff59d
!define DECISION_COLOR #ffcc80
!define TOOL_COLOR #ce93d8
!define STATE_COLOR #80cbc4
!define CONTEXT_COLOR #e3f2fd

' State containers
rectangle "Shared State" as SharedState {
  rectangle "Conversation History" as ConvHistory #STATE_COLOR
  rectangle "Governance Context" as GovContext #STATE_COLOR
  rectangle "Task Status" as TaskStatus #STATE_COLOR
  rectangle "Agent Results" as AgentResults #STATE_COLOR
  
  hexagon "Checkpointing Mechanism" as Checkpoint 
}

' User interface and input
rectangle "User Interface\n(CLI/Streamlit)" as UserInterface #USER_COLOR

' Main supervisor node
rectangle "Goali LangGraph Supervisor" as Supervisor #SUPERVISOR_COLOR {
  diamond "Context Determination" as ContextDetermination #DECISION_COLOR
  diamond "Information Completeness" as InfoCheck #DECISION_COLOR
  diamond "Agent Selection Strategy" as AgentSelection #DECISION_COLOR
  diamond "Response Synthesis" as ResponseSynthesis #DECISION_COLOR
}

' Human-in-the-loop node
rectangle "Human-in-the-Loop Information Gathering" as HumanInLoop #USER_COLOR {
  rectangle "Context-Specific Questions Generator" as QuestionsGen
  rectangle "User Input Processor" as UserInputProc
}

' Specialized Agent nodes
rectangle "Specialized Agents" as SpecializedAgents {
  rectangle "Value Soul Agent\n(Primary in Evaluation)" as ValueSoul #AGENT_COLOR
  rectangle "Business Agent\n(Primary in Planning/Execution)" as Business #AGENT_COLOR
  rectangle "Team Spirit Agent\n(Primary in Reflection)" as TeamSpirit #AGENT_COLOR
}

' Tools for agents
rectangle "Agent Tools" as AgentTools {
  ellipse "Constitution Access Tool" as ConstitutionTool #TOOL_COLOR
  ellipse "Task Management Tool" as TaskTool #TOOL_COLOR
  ellipse "Timeline Tool" as TimelineTool #TOOL_COLOR
  ellipse "Communication Analysis Tool" as CommTool #TOOL_COLOR
}

' Execution context nodes
rectangle "Execution Contexts" as ExecutionContexts {
  rectangle "Planning Context" as PlanningCtx #CONTEXT_COLOR
  rectangle "Execution Context" as ExecutionCtx #CONTEXT_COLOR
  rectangle "Evaluation Context" as EvaluationCtx #CONTEXT_COLOR
  rectangle "Reflection Context" as ReflectionCtx #CONTEXT_COLOR
  rectangle "Resolution Context" as ResolutionCtx #CONTEXT_COLOR
}

' Sequential Execution Manager (for multi-agent workflows)
rectangle "Sequential Execution Manager" as SeqManager #SUPERVISOR_COLOR

' Connections
UserInterface --> Supervisor : 1. User Request
Supervisor --> ContextDetermination : 2. Determine Context

ContextDetermination --> GovContext : 3. Update Context
ContextDetermination --> ExecutionContexts : 4. Select Context

ExecutionContexts --> InfoCheck : 5. Context-Specific Requirements Check

InfoCheck --> HumanInLoop : 6a. Insufficient Information
HumanInLoop --> QuestionsGen : 6b. Generate Questions
QuestionsGen --> UserInterface : 6c. Ask User
UserInterface --> UserInputProc : 6d. User Provides Info
UserInputProc --> Supervisor : 6e. Update with User Input

InfoCheck --> AgentSelection : 7a. Complete Information

AgentSelection --> ValueSoul : 8a. Single Agent (Based on Context)
AgentSelection --> Business : 8b. Single Agent (Based on Context)
AgentSelection --> TeamSpirit : 8c. Single Agent (Based on Context)

AgentSelection --> SeqManager : 9a. Multi-Agent Workflow
SeqManager --> ValueSoul : 9b. First Agent
ValueSoul --> Business : 9c. Next Agent
Business --> TeamSpirit : 9d. Next Agent
TeamSpirit --> SeqManager : 9e. Return to Manager

SeqManager --> AgentResults : 9f. Store Combined Results

ValueSoul --> ConstitutionTool : 10a. Use Tools
Business --> TaskTool : 10b. Use Tools
Business --> TimelineTool : 10c. Use Tools
TeamSpirit --> CommTool : 10d. Use Tools

ValueSoul --> AgentResults : 11a. Store Results
Business --> AgentResults : 11b. Store Results
TeamSpirit --> AgentResults : 11c. Store Results

AgentResults --> ResponseSynthesis : 12. Access Results
ResponseSynthesis --> ConvHistory : 13. Update History
ResponseSynthesis --> UserInterface : 14. Return Response

Checkpoint --> SharedState : 15. Periodic State Persistence

' Context-specific connections with standard arrows
PlanningCtx --> Business : Primary Agent
ExecutionCtx --> Business : Primary Agent
EvaluationCtx --> ValueSoul : Primary Agent
ReflectionCtx --> TeamSpirit : Primary Agent
ResolutionCtx --> SeqManager : Multiple Agents

note right of SharedState
  LangGraph maintains state
  between interactions and
  checkpoints for persistence
end note

note bottom of AgentTools
  Tools replace MCP server
  for file access and specialized
  operations
end note

@enduml