# UX Design Best Practices for Mobile Apps

## Research & Discovery
- **User Interviews**: 5-7 participants minimum for qualitative insights
- **Competitive Analysis**: Document features, flows, strengths/weaknesses of 5+ competitors
- **Personas**: Include behavioral attributes, goals, pain points (not just demographics)
- **User Journeys**: Map emotional states alongside functional steps

## Information Architecture
- **Navigation**: Limit primary navigation to 5 or fewer options
- **Screen Hierarchy**: Main tasks accessible within 3 taps
- **Naming Conventions**: Test terminology with actual users
- **Content Strategy**: Define consistent voice, tone, and messaging standards

## Wireframing Standards
- **Fidelity Progression**: Lo-fi sketches → mid-fi wireframes → hi-fi mockups
- **Annotation**: Include interaction notes, data fields, and conditional states
- **Edge Cases**: Document empty states, errors, and edge cases
- **User Flows**: Connect wireframes into complete user journeys

## UI Design
- **Consistency**: Maintain coherent visual language and component usage
- **Accessibility**: Follow WCAG 2.1 AA standards for contrast, touch targets
- **Typography**: Maximum 2-3 font families with clear hierarchy
- **Color Psychology**: Document rationale for color choices based on brand values

## Usability Testing
- **Test Protocol**: Script with clear tasks and minimal guidance
- **Participant Selection**: Match defined personas, 5 users minimum per round
- **Documentation**: Record sessions, note success rates, time on task
- **Iterative Approach**: Test → Refine → Retest for critical flows

## Mobile-Specific Considerations
- **Touch Targets**: Minimum 44×44px for all interactive elements
- **Thumb Zones**: Place primary actions within easy reach
- **Performance**: Optimize for <3 second load times on 4G connections
- **Progressive Disclosure**: Reveal information gradually to reduce cognitive load

## Professional Deliverables
- **Style Guide**: Colors, typography, components with usage guidelines
- **Component Library**: Reusable UI elements with states and behaviors
- **Prototype**: Interactive representation of key user flows
- **Design Specs**: Detailed documentation for implementation

## Product-Led UX Principles
- **First-Time User Experience**: Design for immediate value delivery
- **Onboarding**: Progressive, contextual introduction to features
- **Engagement Loops**: Hook → Action → Reward → Investment pattern
- **Retention Mechanisms**: Provide increasing value over time
