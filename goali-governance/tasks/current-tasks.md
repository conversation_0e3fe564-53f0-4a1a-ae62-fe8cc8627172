# Current Tasks - <PERSON><PERSON> MVP

## Priority: Governance Setup (Today - 2025-04-05)

- [ ] **Technical:** Set up governance folder structure in VS Code (Verify completion)
- [ ] **Technical:** Implement Claude custom instructions (Verify completion)
- [ ] **Technical:** Test initial decision evaluation using `decision-request.md` template
- [ ] **Strategic:** Define clear governance workflow for decision-making (Document process)
- [ ] **Strategic:** Review partnership equity allocation proposal using governance framework
- [ ] **Strategic:** Prioritize next week's development tasks using governance framework

## Next Up: UX/Creative Domain Initiation (Post-Governance Setup)

### Phase 1: Foundational Definition
- [ ] **UX:** Define Initial Target Audience Persona(s) for MVP
- [ ] **UX:** Articulate Core UX Value Proposition based on `PLANNING.md`
- [ ] **UX:** Establish Initial Brand Attributes (5-7 keywords) aligned with Values Constitution
- [ ] **UX:** Create a Minimal Visual Moodboard for MVP brand feel
- [ ] **UX:** Draft Initial UX Principles (3-5) derived from Values Constitution
- [ ] **STRATEGY/UX:** Develop initial marketing messaging centered on "Decision Relief" & "knowing-doing gap" (Ref: `strategy/cognitive-dissonance-opportunity.md`)
- [ ] **UX/AI:** Refine onboarding flow concept to include value-action gap questions (Leverage Psychological Agent) (Ref: `strategy/cognitive-dissonance-opportunity.md`)
- [ ] **AI/ANALYTICS:** *Note: Configure AI analytics (Engagement/Strategy Agents) to track Dissonance Reduction Score (DRS) and identify dissonance clusters.* (Cross-functional task - requires backend input)

### Phase 2: Core Flow & Interaction Sketching
- [ ] **UX:** Map Key MVP User Journeys (Onboarding, Wheel Interaction, Chat)
- [ ] **UX:** Sketch Low-Fidelity Wireframes for key MVP journeys
- [ ] **UX:** (Optional) Prototype Key Interactions (e.g., Onboarding or Wheel flow)

### Phase 3: Initial Visual Language & Testing Plan
- [ ] **UX:** Develop Basic MVP Style Guide (Colors, Typography, Buttons)
- [ ] **UX:** Apply Visuals to 1-2 Key Screens (Mockups)
- [ ] **UX:** Design "Permission Slip" UI pattern concept (Ref: `strategy/cognitive-dissonance-opportunity.md`)
- [ ] **UX/FE:** Implement basic mood-responsive UI colors (Proof of Concept - requires backend mood data) (Ref: `strategy/cognitive-dissonance-opportunity.md`)
- [ ] **UX/GROWTH:** Plan & Scope Reddit Web Wheel experiment targeting dissonance themes (Ref: `strategy/cognitive-dissonance-opportunity.md`)
- [ ] **UX:** Plan User Testing Approach for initial concepts (Ref: `daily-planning.md`)

## Broader Creative Domain Scope (Philipp - Ongoing/Future)
*Note: This outlines the wider responsibilities beyond the initial MVP phases listed above.*
- **Product Vision & UX Strategy:** Championing the user, defining the "why," developing the UX roadmap, integrating psychology ethically.
- **User Research & Market Analysis:** Deep user understanding (interviews, testing), competitive analysis, market trend monitoring, synthesizing insights.
- **Branding & Marketing Strategy:** Full brand identity development, storytelling, marketing channel identification, content strategy, PR/communications.
- **Design & Creative Execution:** Overseeing design quality/consistency, information architecture, onboarding/trust design, content creation.
- **Collaboration & Communication:** Bridging technical/creative, fostering feedback culture, maintaining UX/brand documentation, developing pitch materials.

## Backlog / Future Considerations
- (Add tasks as they arise)
