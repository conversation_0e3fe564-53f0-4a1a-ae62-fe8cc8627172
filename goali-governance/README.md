# Goali Governance System: Practical Guide

This directory contains the core documents and operational files for the Goali project's governance system, designed to align development with our values and business objectives.

Welcome! This guide explains how to use our integrated Goali Governance System effectively in your daily workflow. It helps us make balanced decisions honoring our values while ensuring business success.

* **Goal:** Streamline decision-making, reduce friction, and leverage AI assistance for alignment and efficiency.
* **Audience:** Primarily for <PERSON> (Technical Captain) and <PERSON> (Creative Captain).

## 1. Getting Started: AI Setup

**Crucial First Step:** Configure your AI Assistant (Cline/Gemini) to act as our specialized Governance Assistant.

1.  **Locate Instructions:** Open the file `/goali-governance/custom_instructions.md` in VS Code.
2.  **Copy Content:** Select and copy the *entire* content of this file.
3.  **Paste into AI Settings:** Go to your AI Assistant's settings (e.g., Cline's Custom Instructions panel) and paste the copied text into the appropriate field.
4.  **Save:** Ensure the settings are saved.

This configuration teaches the AI about our specific values, business standards, roles, and protocols, enabling it to provide tailored governance support.

## 2. Core Principles (Why This System?)

This system helps us by:

*   **Integrating Values & Business:** Providing a single assessment framework (`templates/assessment-template.md`) considering both our ethical principles (`constitution/values-constitution.md`) and business needs (`constitution/business-standards.md`).
*   **Leveraging AI:** Using the configured AI assistant for analysis, task management, and ensuring alignment, saving us time and cognitive load.
*   **Respecting Domains:** Supporting clear decision authority (Technical vs. Creative) while facilitating cross-domain alignment.
*   **Streamlining Workflow:** Integrating directly into VS Code for efficiency.

## 3. System Components Overview

The key components are:

1. **Constitution Files:**
   - `constitution/values-constitution.md`: Our ethical framework and benchmark specifications
   - `constitution/business-standards.md`: Criteria for business optimization

2. **Operational Files:**
   - `status/daily-planning.md`: Plans and reflects on each day's focus
   - `tasks/current-tasks.md`: Tracks active development priorities
   - `status/project-status.md`: High-level project health dashboard

3. **Templates:**
   - `templates/decision-request.md`: For submitting proposals
   - `templates/assessment-template.md`: For structured evaluations
   - `templates/daily-plan-template.md`: For consistent planning

4. **Decision Records:**
   - `decisions/decision-log.md`: A log summarizing key decisions made.
   - `decisions/[decision-name].md`: Individual files documenting specific decisions using the request template.

5. **AI Governance Assistant (Cline/Gemini):**
   - **Your Partner:** Configured via `/goali-governance/custom_instructions.md` to understand our specific context.
   - **Capabilities:** Provides integrated assessments, assists with task management, helps with daily planning, and ensures alignment with our values and business standards.

## 4. How to Use: Practical Workflows & Best Practices

**Best Practice:** Start each new conversation or significant task request by stating your name (e.g., "Guillaume here. Please review...") This helps the AI apply domain-specific context correctly.

### Daily Planning Workflow

1.  **Morning Kick-off:**
    *   Update `/status/daily-planning.md` with your planned focus and tasks.
    *   **Ask the AI:** "*(Your Name) here.* Please review today's plan in `/status/daily-planning.md` and provide an integrated governance assessment."
    *   The AI will analyze alignment with values (e.g., Benevolence, Pragmatism) and business standards (e.g., Resource Efficiency, Market Viability).
    *   Use the AI's feedback to refine your plan for the day.

2.  **Throughout the Day:**
    *   Refer to the plan. Update task status in `/tasks/current-tasks.md` as you complete items.
    *   If priorities shift or blockers arise, **ask the AI:** "*(Your Name) here.* Based on [new situation/blocker], how should I adjust today's priorities listed in `/status/daily-planning.md` and `/tasks/current-tasks.md`?"

3.  **Evening Wrap-up:**
    *   Add your reflections to the "End of Day Reflection" in `/status/daily-planning.md`.
    *   **Ask the AI:** "*(Your Name) here.* Please review my reflections in `/status/daily-planning.md` and help summarize today's progress and identify priorities for tomorrow."
    *   Use this to prepare the next day's plan.

### Decision Evaluation Workflow

1.  **Prepare the Request:**
    *   Create a new file (e.g., `/decisions/feature-xyz-design.md`) using the `templates/decision-request.md` template.
    *   **Be Specific:** Clearly describe the decision, the options considered, expected impacts (user, technical, business), and your initial thoughts on value/business alignment. The more detail you provide, the better the AI's assessment.

2.  **Request AI Evaluation:**
    *   **Ask the AI:** "*(Your Name) here.* Please evaluate the decision documented in `/decisions/feature-xyz-design.md` using our governance framework."
    *   The AI will perform the dual assessment (Values + Business), referencing our constitution files and providing scores/rationale, likely following the `templates/assessment-template.md` structure.

3.  **Act on Assessment:**
    *   Review the AI's integrated assessment. Pay attention to low scores or identified tensions between values and business goals.
    *   **Apply Domain Authority:** As the relevant Domain Captain (Guillaume for Technical, Philipp for Creative), use the assessment to inform your final decision.
    *   **Consult Partner:** For significant decisions or those crossing domain boundaries, share the decision file *and* the AI's assessment with your partner for discussion.
    *   **Document Outcome:** Update the decision file with the final decision and rationale. Add a summary line to `/decisions/decision-log.md`.

### Task Management Workflow

1.  **Prioritization:**
    *   Keep `/tasks/current-tasks.md` updated with active tasks.
    *   **Ask the AI:** "*(Your Name) here.* Please review `/tasks/current-tasks.md` and suggest prioritization based on MVP goals, values alignment, business impact, and dependencies."

2.  **Status Updates & Reprioritization:**
    *   Update task statuses directly in the file.
    *   If things change, **ask the AI:** "*(Your Name) here.* Given [update/blocker], please help reassess priorities in `/tasks/current-tasks.md`."

3.  **Project Health Check:**
    *   Periodically update `/status/project-status.md`.
    *   **Ask the AI:** "*(Your Name) here.* Please analyze `/status/project-status.md` and highlight any potential risks or areas needing attention based on our governance framework."

## 5. Best Practices for AI Interaction

*   **Be Explicit:** Clearly state what you need and reference specific file paths (`/goali-governance/...`).
*   **Provide Context:** If discussing code related to a decision, mention the decision file and the relevant code files.
*   **Iterate:** Use the AI's feedback to refine your plans or decision requests. Ask follow-up questions.
*   **Verify:** While the AI assists, the final judgment rests with you as Domain Captains. Use the AI as a powerful advisor, not an infallible oracle.
*   **Keep it Focused:** Start new conversations for distinct tasks (e.g., one for daily planning, another for a specific decision evaluation).

## 6. Getting Comfortable: Initial Steps

To get familiar with the system:

1.  **Configure AI:** Perform the setup in Section 1.
2.  **Run Daily Plan:** Try the "Morning Kick-off" workflow with today's actual plan. See how the AI responds.
3.  **Evaluate a Test Decision:** Use the `templates/decision-request.md` to create a hypothetical decision (e.g., "Implement basic user analytics"). Ask the AI to evaluate it. Review the assessment structure.
4.  **Prioritize Tasks:** Ask the AI to prioritize the current tasks in `/tasks/current-tasks.md`. Does the suggested order make sense?

This hands-on usage is the best way to understand the flow and the AI's capabilities within our framework.

## 7. Why This Matters (Practical Benefits)

This governance system offers immediate benefits for our collaboration:

* **For Technical Decisions:** Get rapid assessment of business viability while ensuring values alignment
* **For Creative Direction:** Evaluate UX choices against both user impact and implementation feasibility
* **For Partnership Dynamics:** Reduce potential friction by providing neutral, criteria-based assessment
* **For Daily Workflow:** Maintain focus on highest-impact activities through structured planning
* **For Long-term Vision:** Ensure incremental decisions remain aligned with our foundational goals

This system builds on our previous discussions about AI governance while simplifying implementation for our current MVP phase. As we progress through later phases outlined in the values constitution, we can evolve this approach toward our ultimate dual structure vision.

I look forward to using this system together to build Goali efficiently while honoring our shared vision and values.
