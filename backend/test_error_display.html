<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Error Display</title>
    <link rel="stylesheet" href="/static/admin/css/benchmark_management.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Error Display Test</h1>
        <p>This page tests the error display functionality for the benchmark preview & test modal.</p>

        <div class="test-section">
            <h3>Test Error Display</h3>
            <button class="test-button" onclick="showTestError()">Show Test Error</button>
            <button class="test-button" onclick="clearError()">Clear Error</button>
            
            <div id="test-results" style="display: none; margin-top: 20px;">
                <!-- Error will be displayed here -->
            </div>
        </div>

        <div class="test-section">
            <h3>Test Progress Bar Error</h3>
            <button class="test-button" onclick="showProgressError()">Show Progress Error</button>
            <button class="test-button" onclick="resetProgress()">Reset Progress</button>
            
            <div id="test-status" style="display: none; margin-top: 20px;">
                <div class="status-indicator">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span id="test-status-text">Preparing test...</span>
                </div>
                <div class="progress-bar" style="width: 100%; height: 20px; background: #e9ecef; border-radius: 4px; overflow: hidden; margin-top: 10px;">
                    <div class="progress-fill" id="test-progress-fill" style="height: 100%; background: #007bff; width: 25%; transition: all 0.3s ease;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTestError() {
            const resultsDiv = document.getElementById('test-results');
            const errorMessage = `Failed to instantiate LLMService with config eval-mistral-small-latest: Mistral API key not provided

Details:
{
  "error_type": "ValueError",
  "eval_config_name": "eval-mistral-small-latest",
  "eval_model": "mistral-small-latest",
  "traceback": "Traceback (most recent call last):\\n  File \\"/usr/src/app/apps/main/services/benchmark_manager.py\\", line 961, in run_benchmark\\n    self.llm_service = RealLLMClient(llm_config=selected_evaluation_config)\\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File \\"/usr/src/app/apps/main/llm/service.py\\", line 81, in __init__\\n    self.client = LLMClient(llm_config=llm_config)\\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File \\"/usr/src/app/apps/main/llm/client.py\\", line 34, in __init__\\n    raise ValueError(\\"Mistral API key not provided\\")\\nValueError: Mistral API key not provided"
}`;

            resultsDiv.innerHTML = `
                <div class="test-error-display">
                    <h6><i class="fas fa-exclamation-triangle"></i> Test Error</h6>
                    <div class="error-message">
                        <pre>${errorMessage}</pre>
                    </div>
                    <div class="error-actions">
                        <button type="button" class="btn btn-secondary" onclick="clearError()">
                            <i class="fas fa-times"></i> Dismiss
                        </button>
                    </div>
                </div>
            `;
            resultsDiv.style.display = 'block';
        }

        function clearError() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.style.display = 'none';
            resultsDiv.innerHTML = '';
        }

        function showProgressError() {
            const statusDiv = document.getElementById('test-status');
            const statusText = document.getElementById('test-status-text');
            const progressFill = document.getElementById('test-progress-fill');
            
            statusDiv.style.display = 'block';
            statusText.textContent = 'Error: Failed to instantiate LLMService with config eval-mistral-small-latest: Mistral API key not provided';
            progressFill.style.width = '100%';
            progressFill.style.backgroundColor = '#dc3545';
            
            // Change icon to error
            const icon = statusDiv.querySelector('i');
            icon.className = 'fas fa-exclamation-triangle';
            icon.style.color = '#dc3545';
        }

        function resetProgress() {
            const statusDiv = document.getElementById('test-status');
            const statusText = document.getElementById('test-status-text');
            const progressFill = document.getElementById('test-progress-fill');
            
            statusDiv.style.display = 'none';
            statusText.textContent = 'Preparing test...';
            progressFill.style.width = '25%';
            progressFill.style.backgroundColor = '#007bff';
            
            // Reset icon
            const icon = statusDiv.querySelector('i');
            icon.className = 'fas fa-spinner fa-spin';
            icon.style.color = '';
        }
    </script>
</body>
</html>
