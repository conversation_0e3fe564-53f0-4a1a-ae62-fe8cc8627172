import os
from pathlib import Path
import sys
from .base import *
import environ
import django
import json
import logging
logger = logging.getLogger(__name__)

ADMIN_MEDIA_PREFIX = '/static/admin/'

ALLOWED_HOSTS = ['*']  # allow all in dev for convenience

# Print current directory and BASE_DIR for verification
#print(f"Current directory: {os.getcwd()}")
#print(f"BASE_DIR: {BASE_DIR}")

# Check if .env.dev exists
env_path = os.path.join(BASE_DIR, '.env.dev')

# BASE_DIR is defined in base.py as backend directory
# Read .env.dev file located at BASE_DIR (backend/.env.dev)
environ.Env.read_env(os.path.join(BASE_DIR, '.env.dev'))

# Try to read the .env.dev file
try:
    env = environ.Env(DEBUG=(bool, True))
    environ.Env.read_env(env_path, overwrite=True)
    #print("✅ Successfully read .env.dev file")
    
    # Print all environment variables loaded from .env.dev
    # This gets env vars that were set by .env.dev, not all env vars
    with open(env_path, 'r') as f:
        env_vars = {}
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    #print("\nEnvironment variables loaded from .env.dev:")
    #print("-" * 50)
    for key, value in env_vars.items():
        # Get the actual value from env to confirm it was loaded properly
        actual_value = os.environ.get(key)
        loaded_correctly = "✅" if actual_value == value else "❌"
        
        # Mask sensitive values like passwords and keys
        if any(sensitive in key.lower() for sensitive in ['password', 'secret', 'key', 'token']):
            display_value = '*' * 8
            display_actual = '*' * 8 if actual_value else 'Not set'
        else:
            display_value = value
            display_actual = actual_value or 'Not set'
            
        #print(f"{loaded_correctly} {key}: File value: {display_value} | Loaded value: {display_actual}")
    
    # Test retrieving a specific variable using django-environ
    test_var = list(env_vars.keys())[0] if env_vars else 'DEBUG'
    try:
        test_value = env(test_var)
        #print(f"\n✅ Successfully retrieved '{test_var}' using env(): {test_value if 'secret' not in test_var.lower() else '*****'}")
    except Exception as e:
        #print(f"\n❌ Failed to retrieve '{test_var}' using env(): {str(e)}")
        pass
        
except Exception as e:
    #print(f"❌ Error reading .env.dev file: {str(e)}")
    sys.exit(1)

# False if not in os.environ because of casting above
DEBUG = env('DEBUG')

# Add DevelopmentStaticFilesMiddleware for serving static files during development without collectstatic
MIDDLEWARE.insert(0, 'config.middleware.DevelopmentStaticFilesMiddleware')

# INSTALLED_APPS += []

# Override template settings to include our custom admin templates at the project root level
TEMPLATES[0]['DIRS'] = [BASE_DIR / 'templates']

# Override STATIC_ROOT to None in development to ensure live static file serving
# from STATICFILES_DIRS without interference from collectstatic.
STATIC_ROOT = None

# Celery Configuration
CELERY_BROKER_URL = env('CELERY_BROKER_URL', default='redis://localhost:6379/0') # Default to local Redis if not in .env



DATABASES = {
    'default': env.db(),  # Reads DATABASE_URL
}

DATABASE_URL = env('DATABASE_URL')
#logger.warning(f"DATABASE_URL: {DATABASE_URL}")
#DATABASE_URL=xxx
#postgres://dev_user:dev_password@localhost:5432/dev_goali?options='-c client_encoding=UTF8'
#logger.warning(f"DATABASES config: {json.dumps(DATABASES, indent=2)}")
