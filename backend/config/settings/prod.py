import os
from pathlib import Path
from .base import *
import environ


env = environ.Env(
    DEBUG=(bool, False)
)

# Set the project base directory
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent
# Take environment variables from .env file
environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

DEBUG = False



environ.Env.read_env(env_file=BASE_DIR / '.env')

DATABASES = {
    'default': env.db(),  # Reads DATABASE_URL
}


CELERY_BROKER_URL = env('CELERY_BROKER_URL')

# could also enable extra logging, etc.
