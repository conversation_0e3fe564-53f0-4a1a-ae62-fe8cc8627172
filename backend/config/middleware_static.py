import os
import posixpath
from pathlib import Path
from urllib.parse import unquote

from django.conf import settings
from django.contrib.staticfiles import utils
from django.core.exceptions import ImproperlyConfigured
from django.http import Http404, HttpResponseNotModified
from django.views.static import serve


class DevelopmentStaticFilesMiddleware:
    """
    Middleware to serve static files in development.
    
    This middleware handles static file requests directly, bypassing the need for
    collectstatic in development environments. It checks both STATICFILES_DIRS and
    STATIC_ROOT to find files.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Initialize the static paths to check
        self.static_paths = []
        
        # Add STATIC_ROOT if it exists
        if hasattr(settings, 'STATIC_ROOT') and settings.STATIC_ROOT:
            static_root = Path(settings.STATIC_ROOT)
            if static_root.exists():
                self.static_paths.append(static_root)
        
        # Add all STATICFILES_DIRS
        if hasattr(settings, 'STATICFILES_DIRS'):
            for staticfiles_dir in settings.STATICFILES_DIRS:
                if isinstance(staticfiles_dir, (str, Path)):
                    path = Path(staticfiles_dir)
                    if path.exists():
                        self.static_paths.append(path)
                elif isinstance(staticfiles_dir, (list, tuple)) and len(staticfiles_dir) == 2:
                    prefix, path = staticfiles_dir
                    if Path(path).exists():
                        self.static_paths.append((prefix, Path(path)))
        
        # Add default Django admin static files path
        import django
        django_path = Path(django.__file__).parent
        admin_static_path = django_path / 'contrib' / 'admin' / 'static'
        if admin_static_path.exists():
            self.static_paths.append(admin_static_path)
            
    def __call__(self, request):
        # Only process if DEBUG is True and it's a static file request
        if not settings.DEBUG or not request.path.startswith(settings.STATIC_URL):
            return self.get_response(request)
        
        # Get the relative path from the static URL
        path = request.path[len(settings.STATIC_URL):]
        path = posixpath.normpath(unquote(path)).lstrip('/')
        
        # If the path is empty or ends with /, return 404
        if not path or path.endswith('/'):
            raise Http404("Directory indexes are not allowed here.")
            
        # Try to find the file in all static paths
        for static_path in self.static_paths:
            if isinstance(static_path, tuple):
                # Handle prefixed static directories
                prefix, root = static_path
                if path.startswith(prefix):
                    relative_path = path[len(prefix):]
                    file_path = Path(root) / relative_path
                    if file_path.exists() and file_path.is_file():
                        return serve(request, relative_path, document_root=root)
            else:
                # Standard static path
                file_path = Path(static_path) / path
                if file_path.exists() and file_path.is_file():
                    return serve(request, path, document_root=static_path)
                
        # If we get here, the file wasn't found
        return self.get_response(request)