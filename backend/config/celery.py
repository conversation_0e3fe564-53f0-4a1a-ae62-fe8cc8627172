# backend/config/celery.py

import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')

# Create the Celery app
app = Celery('game_of_life')

# Configure Celery using Django settings
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django app configs
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)

# Configure Celery to use Redis as both broker and backend
app.conf.update(
    broker_url=settings.CELERY_BROKER_URL,
    result_backend=settings.CELERY_RESULT_BACKEND,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone=settings.TIME_ZONE,
    task_track_started=True,
    task_time_limit=900,  # 15 minutes
    worker_concurrency=2,
    worker_prefetch_multiplier=1,
)

# Import the Celery result handlers to register the signal handlers
import apps.main.celery_results