import os
import django
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolType<PERSON>outer, URLRouter
from channels.auth import AuthMiddlewareStack

# Set up Django - Use current settings if already set, otherwise default to dev
# This allows the settings to be overridden via command line
if 'DJANGO_SETTINGS_MODULE' not in os.environ:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

# Import routing configurations after Django setup
from apps.main.routing import websocket_urlpatterns as main_websocket_urlpatterns
from apps.admin_tools.routing import websocket_urlpatterns as admin_tools_websocket_urlpatterns

# Combine all WebSocket URL patterns
all_websocket_urlpatterns = main_websocket_urlpatterns + admin_tools_websocket_urlpatterns

# Create the ASGI application
application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack( # Ensures user is available in scope
        URLRouter(
            all_websocket_urlpatterns
        )
    ),
})
