{% extends "admin/base.html" %} {# Extend the CORRECT base template #}
{% load i18n %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    {# Use the correct admin namespace #}
    <a href="{% url 'game_of_life_admin:index' %}">{% trans 'Home' %}</a>
    {# Check if we are on a page with model-specific options (opts) #}
    {% if opts %}
        {# We are on a model page (changelist, add, change) #}
        {% with current_app_label=opts.app_label %}
            &rsaquo;
            {# Use the correct admin namespace and rely on 'silent' for apps without app_list #}
            {% url 'game_of_life_admin:app_list' app_label=current_app_label as app_list_url silent %}
            {% if app_list_url %}
                <a href="{{ app_list_url }}">{{ opts.app_config.verbose_name|default:current_app_label|capfirst }}</a>
            {% else %}
                {# Fallback if app_list URL fails (e.g., for admin_tools) #}
                {{ opts.app_config.verbose_name|default:current_app_label|capfirst }}
            {% endif %}
            {% if title %}
                &rsaquo; {{ title }}
            {% endif %}
        {% endwith %}
    {% else %}
        {# Not a model page - could be index, custom view, etc. #}
        {# Only add the title if it's not the main index page's default title #}
        {% if title and title != index_title %}
             &rsaquo; {{ title }}
        {% endif %}
    {% endif %}
</div>
{% endblock %}
