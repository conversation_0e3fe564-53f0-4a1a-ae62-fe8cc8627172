{% extends "admin/change_form.html" %}
{% load static %}

{% block extrahead %}
{{ block.super }}
<style>
  .activity-detail-card {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .activity-header {
    background-color: #417690;
    color: white;
    padding: 10px 15px;
    border-radius: 4px 4px 0 0;
    margin: -15px -15px 15px -15px;
  }
  
  .activity-tailored-header {
    background-color: #c45c25;
  }
  
  .user-profile-info {
    background-color: #eef7fa;
    border: 1px solid #d1e3e9;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
  }
  
  .domains-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
  }
  
  .domain-badge {
    display: inline-block;
    background-color: #79aec8;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
  }
  
  .domain-badge.primary {
    background-color: #417690;
    font-weight: bold;
  }
  
  .tag-container {
    margin-top: 15px;
  }
  
  .tag {
    display: inline-block;
    background-color: #f1f1f1;
    color: #333;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 5px;
  }
  
  .challenge-ratings {
    background-color: #fff5e6;
    border: 1px solid #ffebcc;
    border-radius: 4px;
    padding: 10px;
    margin-top: 15px;
  }
  
  .chart-container {
    width: 100%;
    height: 200px;
    margin-top: 15px;
    background-color: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .influences-container {
    margin-top: 20px;
  }
  
  .influence-item {
    padding: 8px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 10px;
  }
  
  .influence-strength {
    display: inline-block;
    min-width: 30px;
    text-align: center;
    padding: 2px 5px;
    border-radius: 3px;
    color: white;
    font-weight: bold;
    font-size: 12px;
  }
  
  .influence-strong {
    background-color: #d35400;
  }
  
  .influence-medium {
    background-color: #e67e22;
  }
  
  .influence-light {
    background-color: #f39c12;
  }
  
  .base-link {
    margin-top: 15px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #417690;
  }
</style>
{% endblock %}

{% block content %}
<div class="activity-detail-card">
  <div class="activity-header activity-tailored-header">
    <h2>Tailored Activity Overview</h2>
  </div>
  
  <div class="user-profile-info">
    <h4>User: {{ original.user_profile.profile_name }}</h4>
    {% if original.user_profile.demographics %}
      <p>
        {{ original.user_profile.demographics.full_name }}, 
        {{ original.user_profile.demographics.age }} years old,
        {{ original.user_profile.demographics.occupation }}
      </p>
    {% endif %}
  </div>
  
  <h3>{{ original.name }}</h3>
  <p><strong>Created:</strong> {{ original.created_on }}</p>
  <p><strong>Duration:</strong> {{ original.duration_range }}</p>
  <p><strong>Version:</strong> {{ original.version }}</p>
  <p><strong>Tailorization Level:</strong> {{ original.tailorization_level }}%</p>
  
  <div class="base-link">
    <strong>Based on Generic Activity:</strong> 
    <a href="{% url 'admin:activity_genericactivity_change' original.generic_activity.id %}">
      {{ original.generic_activity.name }}
    </a>
  </div>
  
  <div class="domains-container">
    <h4>Domains:</h4>
    {% for domain_rel in original.domain_relationships.all %}
      <span class="domain-badge {% if domain_rel.strength == 100 %}primary{% endif %}">
        {{ domain_rel.domain.name }} 
        {% if domain_rel.strength < 100 %}({{ domain_rel.get_strength_display }}){% endif %}
      </span>
    {% endfor %}
  </div>
  
  <div class="tag-container">
    <h4>Tags:</h4>
    {% for tag_item in original.tags.all %}
      <span class="tag">{{ tag_item.tag.name }}</span>
    {% endfor %}
  </div>
  
  <div class="challenge-ratings">
    <h4>Challenge Ratings</h4>
    <p><strong>Base Challenge Rating:</strong> {{ original.base_challenge_rating }}/100</p>
    
    <h5>Personality Dimension Challenge Levels:</h5>
    <ul>
      {% if original.challengingness.openness %}
        <li><strong>Openness:</strong> {{ original.challengingness.openness }}/100</li>
      {% endif %}
      {% if original.challengingness.conscientiousness %}
        <li><strong>Conscientiousness:</strong> {{ original.challengingness.conscientiousness }}/100</li>
      {% endif %}
      {% if original.challengingness.extraversion %}
        <li><strong>Extraversion:</strong> {{ original.challengingness.extraversion }}/100</li>
      {% endif %}
      {% if original.challengingness.agreeableness %}
        <li><strong>Agreeableness:</strong> {{ original.challengingness.agreeableness }}/100</li>
      {% endif %}
      {% if original.challengingness.neuroticism %}
        <li><strong>Neuroticism:</strong> {{ original.challengingness.neuroticism }}/100</li>
      {% endif %}
      {% if original.challengingness.honestyhumility %}
        <li><strong>Honesty-Humility:</strong> {{ original.challengingness.honestyhumility }}/100</li>
      {% endif %}
      {% if original.challengingness.emotionality %}
        <li><strong>Emotionality:</strong> {{ original.challengingness.emotionality }}/100</li>
      {% endif %}
    </ul>
  </div>
  
  {% if original.influences.exists %}
  <div class="influences-container">
    <h4>Influences:</h4>
    {% for influence in original.influences.all %}
      <div class="influence-item">
        {% if influence.influence_strength >= 75 %}
          <span class="influence-strength influence-strong">{{ influence.influence_strength }}</span>
        {% elif influence.influence_strength >= 50 %}
          <span class="influence-strength influence-medium">{{ influence.influence_strength }}</span>
        {% else %}
          <span class="influence-strength influence-light">{{ influence.influence_strength }}</span>
        {% endif %}
        
        <strong>{{ influence.content_type.model|capfirst }}:</strong>
        {% if influence.content_type.model == 'usergoal' %}
          {{ influence.influencing_entity.title }}
        {% elif influence.content_type.model == 'belief' %}
          {{ influence.influencing_entity.content|truncatechars:50 }}
        {% elif influence.content_type.model == 'inspiration' %}
          {{ influence.influencing_entity.source }}
        {% elif influence.content_type.model == 'preference' %}
          {{ influence.influencing_entity.pref_name }}
        {% elif influence.content_type.model == 'currentmood' %}
          {{ influence.influencing_entity.description|truncatechars:50 }}
        {% else %}
          {{ influence.influencing_entity }}
        {% endif %}
        
        {% if influence.note %}
          <p><small>{{ influence.note }}</small></p>
        {% endif %}
      </div>
    {% endfor %}
  </div>
  {% endif %}
</div>

{{ block.super }}
{% endblock %}