{% extends "admin/change_form.html" %}
{% load static %}

{% block extrahead %}
{{ block.super }}
<style>
  .activity-detail-card {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .activity-header {
    background-color: #417690;
    color: white;
    padding: 10px 15px;
    border-radius: 4px 4px 0 0;
    margin: -15px -15px 15px -15px;
  }
  
  .domains-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
  }
  
  .domain-badge {
    display: inline-block;
    background-color: #79aec8;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
  }
  
  .domain-badge.primary {
    background-color: #417690;
    font-weight: bold;
  }
  
  .requirement-list {
    list-style-type: none;
    padding-left: 0;
  }
  
  .requirement-list li {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
  }
  
  .tag-container {
    margin-top: 15px;
  }
  
  .tag {
    display: inline-block;
    background-color: #f1f1f1;
    color: #333;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 5px;
  }
  
  .metrics-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
  }
  
  .metric {
    flex: 1;
    min-width: 120px;
    background-color: #e8f4f8;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
  }
  
  .metric-value {
    font-size: 20px;
    font-weight: bold;
    color: #417690;
  }
  
  .metric-label {
    font-size: 12px;
    color: #666;
  }
  
  .related-activities {
    margin-top: 20px;
  }
  
  .related-activity {
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 10px;
  }
  
  @media (max-width: 767px) {
    .metrics-container {
      flex-direction: column;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="activity-detail-card">
  <div class="activity-header">
    <h2>Activity Overview</h2>
  </div>
  
  <h3>{{ original.name }}</h3>
  <p><strong>Created:</strong> {{ original.created_on }}</p>
  <p><strong>Duration:</strong> {{ original.duration_range }}</p>
  
  <div class="domains-container">
    <h4>Domains:</h4>
    {% for domain_rel in original.domain_relationships.all %}
      <span class="domain-badge {% if domain_rel.strength == 100 %}primary{% endif %}">
        {{ domain_rel.domain.name }} 
        {% if domain_rel.strength < 100 %}({{ domain_rel.get_strength_display }}){% endif %}
      </span>
    {% endfor %}
  </div>
  
  <div class="tag-container">
    <h4>Tags:</h4>
    {% for tag_item in original.tags.all %}
      <span class="tag">{{ tag_item.tag.name }}</span>
    {% endfor %}
  </div>
  
  <div class="metrics-container">
    <div class="metric">
      <div class="metric-value">{{ original.resource_requirements.count }}</div>
      <div class="metric-label">Resources Required</div>
    </div>
    <div class="metric">
      <div class="metric-value">{{ original.env_requirements.count }}</div>
      <div class="metric-label">Environmental Requirements</div>
    </div>
    <div class="metric">
      <div class="metric-value">{{ original.user_requirements.count }}</div>
      <div class="metric-label">User Requirements</div>
    </div>
    <div class="metric">
      <div class="metric-value">{{ original.tailored_activities.count }}</div>
      <div class="metric-label">Tailored Versions</div>
    </div>
  </div>
  
  {% if original.tailored_activities.exists %}
  <div class="related-activities">
    <h4>Recent Tailored Versions:</h4>
    {% for tailored in original.tailored_activities.all|slice:":5" %}
      <div class="related-activity">
        <strong>{{ tailored.name }}</strong> (v{{ tailored.version }})<br>
        <small>User: {{ tailored.user_profile.profile_name }} | Challenge Rating: {{ tailored.base_challenge_rating }}</small>
      </div>
    {% endfor %}
    {% if original.tailored_activities.count > 5 %}
      <p><em>... and {{ original.tailored_activities.count|add:"-5" }} more tailored versions</em></p>
    {% endif %}
  </div>
  {% endif %}
</div>

{{ block.super }}
{% endblock %}