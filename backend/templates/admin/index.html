{% extends "admin/base_site.html" %} {# Extend the custom base site, not the original index #}
{% load i18n %}

{% block content %}
{# The base_site template usually provides #content-main, so we might not need it here #}
{# Check base_site structure if layout breaks #}
<div id="content-main">
    {# Optional: Include welcome message or other elements from block.super if needed #}
    {# For example: {% block pretitle %}{% endblock %} <h1>{% block content_title %}{% endblock %}</h1> #}

    {# PAGE 1 Section #}
    <div class="module">
        <h2>Page 1: User-Specific Entities & Global Instances/Logs</h2>
        <table>
            <caption>User-Specific</caption>
            <tbody>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_userprofile_changelist' %}">User Profiles</a></th>
                    <td><a href="{% url 'admin:user_userprofile_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:main_customagent_changelist' %}">Custom Agents</a></th>
                    <td><a href="{% url 'admin:main_customagent_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_userenvironment_changelist' %}">User Environments</a></th>
                    <td><a href="{% url 'admin:user_userenvironment_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_userattributeproficiency_changelist' %}">User Attribute Proficiencies</a></th>
                    <td><a href="{% url 'admin:user_userattributeproficiency_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_belief_changelist' %}">Beliefs</a></th>
                    <td><a href="{% url 'admin:user_belief_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_activitytailored_changelist' %}">Tailored Activities</a></th>
                    <td><a href="{% url 'admin:activity_activitytailored_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                 <tr>
                    <th scope="row"><a href="{% url 'admin:main_agentgoal_changelist' %}">Agent Goals</a></th>
                    <td><a href="{% url 'admin:main_agentgoal_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
            </tbody>
        </table>
        <table>
            <caption>Global Instances/Logs</caption>
            <tbody>
                <tr>
                    <th scope="row"><a href="{% url 'admin:main_agentrun_changelist' %}">Agent Runs</a></th>
                    <td></td> {# No 'Add' link #}
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:main_agentmetric_changelist' %}">Agent Metrics</a></th>
                    <td></td> {# No 'Add' link #}
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:main_agentrecommendation_changelist' %}">Agent Recommendations</a></th>
                    <td></td> {# No 'Add' link #}
                </tr>
            </tbody>
        </table>
    </div>

    {# PAGE 2 Section #}
    <div class="module">
        <h2>Page 2: Generic Definitions & Global Relationships/Requirements</h2>
        <table>
            <caption>Generic Definitions</caption>
            <tbody>
                <tr>
                    <th scope="row"><a href="{% url 'admin:main_genericagent_changelist' %}">Generic Agents</a></th>
                    <td><a href="{% url 'admin:main_genericagent_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:main_agenttool_changelist' %}">Agent Tools</a></th>
                    <td><a href="{% url 'admin:main_agenttool_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:main_benchmarkmetric_changelist' %}">Benchmark Metrics</a></th>
                    <td><a href="{% url 'admin:main_benchmarkmetric_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_generictrait_changelist' %}">Generic Traits</a></th>
                    <td><a href="{% url 'admin:user_generictrait_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_genericenvironment_changelist' %}">Generic Environments</a></th>
                    <td><a href="{% url 'admin:user_genericenvironment_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_skillattribute_changelist' %}">Skill Attributes</a></th>
                    <td><a href="{% url 'admin:user_skillattribute_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_skilldefinition_changelist' %}">Skill Definitions</a></th>
                    <td><a href="{% url 'admin:user_skilldefinition_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_genericbelief_changelist' %}">Generic Beliefs</a></th>
                    <td><a href="{% url 'admin:user_genericbelief_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_tag_changelist' %}">Tags</a></th>
                    <td><a href="{% url 'admin:activity_tag_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_genericdomain_changelist' %}">Generic Domains</a></th>
                    <td><a href="{% url 'admin:activity_genericdomain_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_genericactivity_changelist' %}">Generic Activities</a></th>
                    <td><a href="{% url 'admin:activity_genericactivity_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
            </tbody>
        </table>
         <table>
            <caption>Global Relationships/Requirements</caption>
            <tbody>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_genericenvironmentdomainrelationship_changelist' %}">Generic Environment Domain Relationships</a></th>
                    <td><a href="{% url 'admin:user_genericenvironmentdomainrelationship_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_attributetraitinfluence_changelist' %}">Attribute Trait Influences</a></th>
                    <td><a href="{% url 'admin:user_attributetraitinfluence_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_skillattributecomposition_changelist' %}">Skill Attribute Compositions</a></th>
                    <td><a href="{% url 'admin:user_skillattributecomposition_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_skilldomainapplication_changelist' %}">Skill Domain Applications</a></th>
                    <td><a href="{% url 'admin:user_skilldomainapplication_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:user_genericbeliefdomainrelationship_changelist' %}">Generic Belief Domain Relationships</a></th>
                    <td><a href="{% url 'admin:user_genericbeliefdomainrelationship_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_genericactivityuserrequirement_changelist' %}">Generic Activity User Requirements</a></th>
                    <td><a href="{% url 'admin:activity_genericactivityuserrequirement_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_genericactivityuserrequirementsummary_changelist' %}">Generic Activity User Requirement Summaries</a></th>
                    <td><a href="{% url 'admin:activity_genericactivityuserrequirementsummary_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_activityinfluencedby_changelist' %}">Activity Influenced Bys</a></th>
                    <td><a href="{% url 'admin:activity_activityinfluencedby_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_entitydomainrelationship_changelist' %}">Entity Domain Relationships</a></th>
                    <td><a href="{% url 'admin:activity_entitydomainrelationship_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_activityuserrequirement_changelist' %}">Activity User Requirements</a></th>
                    <td><a href="{% url 'admin:activity_activityuserrequirement_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_activityenvrequirement_changelist' %}">Activity Env Requirements</a></th>
                    <td><a href="{% url 'admin:activity_activityenvrequirement_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
                <tr>
                    <th scope="row"><a href="{% url 'admin:activity_activitytailoredresourcerequirement_changelist' %}">Activity Tailored Resource Requirements</a></th>
                    <td><a href="{% url 'admin:activity_activitytailoredresourcerequirement_add' %}" class="addlink">{% trans 'Add' %}</a></td>
                </tr>
            </tbody>
        </table>
    </div>

    {# Keep existing custom modules #}
    {% if coverage_dashboard_url %}
    <div class="module" style="margin-top: 20px;">
        <table>
            <caption>
                <a href="{{ coverage_dashboard_url }}" class="section">
                    {% trans 'Test Coverage Dashboard' %}
                </a>
            </caption>
            <tbody>
                <tr>
                    <th scope="row">
                        <a href="{{ coverage_dashboard_url }}">{% trans 'View Coverage Dashboard' %}</a>
                    </th>
                    <td></td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
    {% endif %}

    {# Use the new context variables passed from the overridden index view #}
    {% if websocket_tester_url or benchmark_dashboard_url %}
    <div class="module" style="margin-top: 20px;">
        <table>
            <caption>
                {# Link the caption to one of the tools, e.g., the first one available #}
                <a href="{% if websocket_tester_url %}{{ websocket_tester_url }}{% else %}{{ benchmark_dashboard_url }}{% endif %}" class="section">
                    {% trans 'Admin Tools' %}
                </a>
            </caption>
            <tbody>
                {% if websocket_tester_url %}
                <tr>
                    <th scope="row">
                        <a href="{{ websocket_tester_url }}">{% trans 'WebSocket Tester' %}</a>
                    </th>
                    <td>{% trans 'Send raw messages to the WebSocket consumer.' %}</td>
                    <td></td> {# Placeholder for add/change links if needed later #}
                </tr>
                {% endif %}
                {% if benchmark_dashboard_url %}
                <tr>
                    <th scope="row">
                        <a href="{{ benchmark_dashboard_url }}">{% trans 'Benchmark Dashboard' %}</a>
                    </th>
                    <td>{% trans 'Run agent benchmarks and view results.' %}</td>
                    <td></td>
                {% endif %}
                </tr>
            </tbody>
        </table>
    </div>
    {% endif %}

</div> {# End content-main #}
{% endblock %}
