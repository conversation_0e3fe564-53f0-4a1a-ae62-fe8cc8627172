{% extends "admin/change_form.html" %}
{% load static %}

{% block extrahead %}
    {{ block.super }}
    {# Add Plotly.js library #}
    <script src='https://cdn.plot.ly/plotly-latest.min.js'></script>
    <style>
        /* Optional: Add some styling for the chart containers */
        .plotly-chart-container {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .plotly-chart-container h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
    </style>
{% endblock %}

{% block after_field_sets %}
    {{ block.super }}

    {# Add containers for Plotly charts #}
    <div class="plotly-chart-container">
        <h3>Benchmark Performance Overview</h3>
        <div id="duration-distribution-chart"></div>
        {# Add more chart divs as needed, e.g., for scores, LLM calls, etc. #}
        <div id="semantic-score-chart" style="margin-top: 20px;"></div>
        <div id="tool-usage-chart" style="margin-top: 20px;"></div> {# New container for tool usage #}
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log("Attempting to render Plotly charts for BenchmarkRun");

            // Function to safely parse JSON from readonly fields
            function getJsonData(fieldName) {
                try {
                    // Find the field's display element (usually a div.readonly)
                    // This selector might need adjustment based on the exact admin HTML structure
                    const fieldElement = document.querySelector(`.field-${fieldName} .readonly`);
                    if (fieldElement) {
                        // Attempt to parse the text content as JSON
                        return JSON.parse(fieldElement.textContent || fieldElement.innerText);
                    }
                    console.warn(`Readonly field element not found for: ${fieldName}`);
                    return null;
                } catch (e) {
                    console.error(`Error parsing JSON for field ${fieldName}:`, e);
                    return null;
                }
            }

            // --- Chart 1: Duration Distribution (Example using raw_results) ---
            const rawResultsData = getJsonData('raw_results');
            const durationChartDiv = document.getElementById('duration-distribution-chart');

            if (rawResultsData && Array.isArray(rawResultsData.individual_runs) && durationChartDiv) {
                const durations = rawResultsData.individual_runs.map(run => run.duration_seconds).filter(d => d !== null && d !== undefined);

                if (durations.length > 0) {
                    const trace = {
                        x: durations,
                        type: 'histogram',
                        marker: {
                           color: 'rgb(100, 200, 102)',
                        },
                    };
                    const layout = {
                        title: 'Distribution of Run Durations (seconds)',
                        xaxis: { title: 'Duration (s)' },
                        yaxis: { title: 'Frequency' },
                        bargap: 0.05
                    };
                    Plotly.newPlot(durationChartDiv, [trace], layout);
                    console.log("Duration distribution chart rendered.");
                } else {
                    durationChartDiv.innerHTML = '<p>No duration data available for histogram.</p>';
                    console.log("No valid duration data found for histogram.");
                }
            } else if (durationChartDiv) {
                 durationChartDiv.innerHTML = '<p>Could not load duration data for chart.</p>';
                 console.log("Raw results data or duration chart div not found/invalid.");
            }


            // --- Chart 2: Semantic Score (Example using semantic_score field) ---
            const semanticScoreChartDiv = document.getElementById('semantic-score-chart');
            const semanticScoreField = document.querySelector('.field-semantic_score .readonly'); // Direct field access

            if (semanticScoreField && semanticScoreChartDiv) {
                 const score = parseFloat(semanticScoreField.textContent || semanticScoreField.innerText);
                 if (!isNaN(score)) {
                     const data = [{
                         type: 'indicator',
                         mode: "gauge+number",
                         value: score,
                         title: { text: "Semantic Score", font: { size: 20 } },
                         gauge: {
                             axis: { range: [0, 1], tickwidth: 1, tickcolor: "darkblue" }, // Assuming score is 0-1
                             bar: { color: "darkblue" },
                             bgcolor: "white",
                             borderwidth: 2,
                             bordercolor: "gray",
                             steps: [
                                 { range: [0, 0.5], color: "rgba(255, 0, 0, 0.6)" }, // Red for low scores
                                 { range: [0.5, 0.8], color: "rgba(255, 255, 0, 0.6)" }, // Yellow for medium
                                 { range: [0.8, 1], color: "rgba(0, 255, 0, 0.6)" } // Green for high
                             ],
                             threshold: { // Optional: Add a target threshold line
                                 line: { color: "red", width: 4 },
                                 thickness: 0.75,
                                 value: 0.9 // Example threshold
                             }
                         }
                     }];

                     const layout = {
                         width: 400, height: 300, margin: { t: 25, r: 25, l: 25, b: 25 },
                         paper_bgcolor: "rgba(0,0,0,0)", // Transparent background
                         font: { color: "darkblue", family: "Arial" }
                     };

                     Plotly.newPlot(semanticScoreChartDiv, data, layout);
                     console.log("Semantic score gauge rendered.");
                 } else {
                     semanticScoreChartDiv.innerHTML = '<p>Invalid semantic score value.</p>';
                     console.log("Invalid semantic score value found.");
                 }
            } else if (semanticScoreChartDiv) {
                semanticScoreChartDiv.innerHTML = '<p>Could not load semantic score data for chart.</p>';
                console.log("Semantic score field or chart div not found.");
            }

            // Add more chart rendering logic here for other metrics...

            // --- Chart 3: Tool Usage Breakdown (Example using tool_breakdown) ---
            const toolBreakdownData = getJsonData('tool_breakdown');
            const toolUsageChartDiv = document.getElementById('tool-usage-chart');

            if (toolBreakdownData && typeof toolBreakdownData === 'object' && Object.keys(toolBreakdownData).length > 0 && toolUsageChartDiv) {
                const toolNames = Object.keys(toolBreakdownData);
                const toolCounts = Object.values(toolBreakdownData);

                const trace = {
                    x: toolNames,
                    y: toolCounts,
                    type: 'bar',
                    marker: {
                        color: 'rgb(91, 155, 213)', // A different color
                    },
                    text: toolCounts.map(String), // Show count on bar
                    textposition: 'auto',
                };
                const layout = {
                    title: 'Tool Usage Frequency',
                    xaxis: { title: 'Tool Code', tickangle: -45 }, // Angle labels if needed
                    yaxis: { title: 'Number of Calls' },
                    bargap: 0.1
                };
                Plotly.newPlot(toolUsageChartDiv, [trace], layout);
                console.log("Tool usage chart rendered.");

            } else if (toolUsageChartDiv) {
                toolUsageChartDiv.innerHTML = '<p>No tool usage data available for chart.</p>';
                console.log("Tool breakdown data or chart div not found/invalid/empty.");
            }


        });
    </script>
{% endblock %}
