<!-- backend/templates/admin_tools/benchmark_dashboard.html -->
{% extends "admin/base_site.html" %} {# Extend our custom base site template #}
{% load static %} <!-- Load static files tag -->

{% block title %}Agent Benchmarking Dashboard | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrastyle %}
    {{ block.super }}
    {# Re-add Plotly.js library #}
    <script src='https://cdn.plot.ly/plotly-latest.min.js'></script>
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group select,
        .form-group input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box; /* Include padding and border in element's total width and height */
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none; /* Remove underline from links styled as buttons */
            display: inline-block; /* Ensure proper spacing and alignment */
            text-align: center;
        }

        .btn-primary {
            background: #447e9b; /* Django admin primary color */
            color: white;
        }
        .btn-primary:hover {
            background: #3a6a83;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }

        .hidden {
            display: none;
        }

        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #447e9b; /* Use admin primary color */
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite; /* Faster spin */
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .benchmark-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .benchmark-table th,
        .benchmark-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #ddd;
            text-align: left;
            vertical-align: top;
        }

        .benchmark-table th {
            background-color: #f8f8f8; /* Lighter grey for header */
            font-weight: bold;
        }

        .benchmark-table tr:hover {
            background-color: #f5f5f5;
        }
        /* Add style for error rows */
        .benchmark-table tr.error-row {
            background-color: #f8d7da !important; /* Use important to override hover */
        }
        .benchmark-table tr.error-row:hover {
            background-color: #f1c6cb !important; /* Darker red on hover */
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); /* Slightly smaller min width */
            gap: 15px;
            margin-top: 20px;
        }

        .metric {
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
        }

        .metric-label {
            display: block;
            font-size: 13px; /* Slightly smaller label */
            color: #666;
            margin-bottom: 5px;
        }

        .metric-value {
            display: block;
            font-size: 18px; /* Slightly smaller value */
            font-weight: bold;
            color: #333;
        }

        #result-charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
            min-height: 250px; /* Ensure charts have some minimum height */
        }

        #result-charts canvas {
            max-width: 100%; /* Ensure charts are responsive */
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
        .alert-success {
            color: #3c763d;
            background-color: #dff0d8;
            border-color: #d6e9c6;
        }
        .alert-info {
            color: #31708f;
            background-color: #d9edf7;
            border-color: #bce8f1;
        }

        .text-center { text-align: center; }
        .mt-3 { margin-top: 1rem; } /* Adjust spacing as needed */

        @media (max-width: 768px) {
            #result-charts {
                grid-template-columns: 1fr;
            }
            .metrics-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
        .filter-container {
            display: flex;
            gap: 15px; /* Space between filters */
            margin-bottom: 15px;
        }
        .filter-container .form-group {
            flex: 1; /* Allow filters to grow */
            margin-bottom: 0; /* Remove bottom margin within the container */
        }
    </style>
{% endblock %}

{% block breadcrumbs %}
{# Custom breadcrumbs for this specific view #}
{% load i18n %}
<div class="breadcrumbs">
    <a href="{% url 'game_of_life_admin:index' %}">{% trans 'Home' %}</a> {# Use correct namespace #}
    {# Since admin_tools doesn't have a standard app_list view, just show text #}
    &rsaquo; {% trans 'Admin Tools' %}
    &rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div id="content-main" class="dashboard-container">
    <h1>{{ title }}</h1>

    {% if error %}
        <p class="errornote">{{ error }}</p>
    {% endif %}

    <div class="card">
        <h2>Run New Benchmark</h2>
        <form id="benchmark-form">
            {% csrf_token %} <!-- Add CSRF token -->

            <div class="filter-container">
                <div class="form-group">
                    <label for="tag-filter">Filter by Tag:</label>
                    <select id="tag-filter" name="tag_filter">
                        <option value="">-- All Tags --</option>
                        {% for tag in tags %}
                        <option value="{{ tag.name }}">{{ tag.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                {# Add more filters here if needed (e.g., Agent Role) #}
            </div>

            <div class="form-group">
                <label for="scenario">Select Scenario:</label>
                <select id="scenario" name="scenario" required>
                    <option value="">-- Select Scenario --</option>
                    {% for scenario in scenarios %}
                    {# Store tags in a data attribute, joining them with a comma #}
                    <option value="{{ scenario.id }}" data-tags="{% for tag in scenario.tags.all %}{{ tag.name }}{% if not forloop.last %},{% endif %}{% endfor %}">
                        {{ scenario.name }} ({{ scenario.agent_role }})
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="form-group">
                <label for="runs">Number of Runs:</label>
                <input type="number" id="runs" name="runs" value="1" min="1" max="3">
            </div>

            <div class="form-group">
                <label for="semantic-eval">Include Semantic Evaluation:</label>
                <input type="checkbox" id="semantic-eval" name="semantic_evaluation">
            </div>

            {# --- Replace separate LLM Config fields with single LLMConfig select --- #}
            <div class="form-group">
                <label for="llm-config-select">Select LLM Configuration:</label>
                <select id="llm-config-select" name="llm_config_id">
                    <option value="">-- Use Default LLM Config --</option>
                    {% for config in llm_configs %}
                    <option value="{{ config.id }}">{{ config.name }} ({{ config.model_name }})</option>
                    {% endfor %}
                </select>
            </div>
            {# --- End LLM Config Select --- #}

            {# --- Add Evaluation LLM Config Select --- #}
            <div class="form-group">
                <label for="evaluation-llm-config-select">Select Evaluation LLM Configuration:</label>
                <select id="evaluation-llm-config-select" name="evaluation_llm_config_id">
                    <option value="">-- Use Default Evaluation Config --</option>
                    {% for config in evaluation_llm_configs %}
                    <option value="{{ config.id }}">{{ config.name }} ({{ config.model_name }})</option>
                    {% endfor %}
                </select>
            </div>
            {# --- End Evaluation LLM Config Select --- #}

            <button type="submit" class="btn btn-primary">Run Benchmark</button>
        </form>

        <div id="benchmark-status" class="hidden" style="margin-top: 20px; text-align: center;">
            <div class="loader"></div>
            <p>Running benchmark...</p>
        </div>

        <div id="benchmark-result" class="hidden" style="margin-top: 20px;">
            <h3>Benchmark Results</h3>
            <div id="result-content"></div>
            {# Add containers for Plotly charts #}
            <div id="result-charts" style="margin-top: 20px;">
                 <div id="duration-distribution-chart" class="chart-container"></div>
                 <div id="semantic-score-chart" class="chart-container"></div>
                 <div id="tool-usage-chart" class="chart-container"></div>
            </div>
            <div id="raw-results-container" style="margin-top: 20px;">
                <h4>Raw Results</h4>
                <pre id="raw-results-pre" style="background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;"></pre>
            </div>
        </div>
    </div>

    {# --- Run All Benchmarks Card --- #}
    <div class="card">
        <h2>Run All Benchmarks</h2>
        <p>Run all available benchmark scenarios with global settings.</p>
        <form id="run-all-benchmarks-form">
            {% csrf_token %}
            <div class="form-group">
                <label for="all-runs">Number of Runs (per scenario):</label>
                <input type="number" id="all-runs" name="runs" value="3" min="1" max="10">
            </div>
            <div class="form-group">
                <label for="all-semantic-eval">Include Semantic Evaluation:</label>
                <input type="checkbox" id="all-semantic-eval" name="semantic_evaluation" checked> {# Default to checked #}
            </div>
            <button type="submit" class="btn btn-primary">Run All Benchmarks</button>
        </form>
        <div id="run-all-status" class="hidden" style="margin-top: 20px; text-align: center;">
            <div class="loader"></div>
            <p>Starting all benchmarks... This may take a while.</p>
            <p id="run-all-progress"></p> {# To show progress #}
        </div>
        <div id="run-all-result" class="hidden alert" style="margin-top: 20px;"></div> {# Combined status/result area #}
    </div>
    {# --- End Run All Benchmarks Card --- #}


    {# --- Import/Export Card --- #}
    <div class="card">
        <h2>Manage Scenarios</h2>
        <div style="display: flex; justify-content: space-between; align-items: start; gap: 20px;">
            {# Export Section #}
            <div style="flex: 1;">
                <h4>Export Scenarios</h4>
                <p>Download all active benchmark scenarios as a JSON file.</p>
                {# Use the correct namespace 'game_of_life_admin' #}
                <a href="{% url 'game_of_life_admin:export_scenarios' %}" class="btn btn-secondary">Export Scenarios</a>
            </div>

            {# Import Section #}
            <div style="flex: 1;">
                <h4>Import Scenarios</h4>
                <p>Upload a JSON file to create or update benchmark scenarios. Matches scenarios by name.</p>
                {# Use the correct namespace 'game_of_life_admin' #}
                <form action="{% url 'game_of_life_admin:import_scenarios' %}" method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="scenario-file">Scenario JSON File:</label>
                        <input type="file" id="scenario-file" name="scenario_file" accept=".json" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Import Scenarios</button>
                </form>
            </div>
        </div>
    </div>
    {# --- End Import/Export Card --- #}


    <div class="card">
        <h2>Recent Benchmark Runs</h2>
        {% if recent_runs %}
        <table class="benchmark-table">
            <thead>
                <tr>
                    <th>Scenario</th>
                    <th>Agent</th>
                    <th>Date</th>
                    <th>Duration (ms)</th>
                    <th>Success Rate / Status</th> {# Updated Header #}
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="recent-runs-tbody"> {# Add ID for easier JS targeting #}
                {% for run in recent_runs %}
                {# Add data-run-id to the row #}
                <tr data-run-id="{{ run.id }}">
                    <td>{{ run.scenario.name }}</td>
                    <td>{{ run.agent_definition.role }}</td>
                    <td>{{ run.execution_date|date:"Y-m-d H:i" }}</td>
                    <td>{{ run.mean_duration|floatformat:2 }}</td>
                    {# Add data-status-cell-id to the status cell #}
                    <td data-status-cell-id="{{ run.id }}">
                        {# Default display - JS will update #}
                        {% if run.success_rate is not None %}
                            {{ run.success_rate|floatformat:"2g" }}%
                        {% else %}
                            N/A
                        {% endif %}
                    </td>
                    <td>
                        <a href="#" class="view-details btn btn-secondary" data-run-id="{{ run.id }}">View Details</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p>No benchmark runs yet.</p>
        {% endif %}

        <div class="text-center mt-3">
            {# Use the correct 'game_of_life_admin' namespace #}
            <a href="{% url 'game_of_life_admin:benchmark_history' %}" class="btn btn-secondary">
                View Full Benchmark History
            </a>
            <a href="{% url 'game_of_life_admin:benchmark_management' %}" class="btn btn-primary">
                Manage Benchmarks
            </a>
        </div>
    </div>

    <div class="card" id="error-events-panel" style="margin-top: 20px;">
        <h2>Error Events</h2>
        <div id="error-events-container" style="max-height: 200px; overflow-y: auto; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 10px; color: #721c24; font-family: monospace; font-size: 13px;"></div>
    </div>
</div>

{# Embed recent runs data as JSON for JavaScript #}
<script id="recent-runs-data" type="application/json">
    {{ recent_runs_json|safe }}
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const benchmarkForm = document.getElementById('benchmark-form');
    const benchmarkStatus = document.getElementById('benchmark-status');
    const benchmarkResult = document.getElementById('benchmark-result');
    const resultContent = document.getElementById('result-content');
    const rawResultsPre = document.getElementById('raw-results-pre');
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value; // Get CSRF token
    const scenarioSelect = document.getElementById('scenario');
    const tagFilterSelect = document.getElementById('tag-filter');
    // Store all scenario options *after* the initial placeholder
    const allScenarioOptions = Array.from(scenarioSelect.options).slice(1);

    // --- Error Events UI ---
    const errorEventsContainer = document.getElementById('error-events-container');

    function addErrorEvent(event) {
        const timestamp = event.content.timestamp || new Date().toISOString();
        const level = event.content.level || 'error';
        const source = event.content.source || 'unknown';
        const message = event.content.message || '';
        const details = event.content.details || {};

        const eventDiv = document.createElement('div');
        eventDiv.style.borderBottom = '1px solid #f5c6cb';
        eventDiv.style.padding = '5px 0';

        const header = document.createElement('div');
        header.style.fontWeight = 'bold';
        header.textContent = `[${timestamp}] [${level.toUpperCase()}] [${source}]`;

        const messageDiv = document.createElement('div');
        messageDiv.textContent = message;

        const detailsPre = document.createElement('pre');
        detailsPre.style.whiteSpace = 'pre-wrap';
        detailsPre.style.margin = '5px 0 0 0';
        detailsPre.textContent = JSON.stringify(details, null, 2);

        eventDiv.appendChild(header);
        eventDiv.appendChild(messageDiv);
        if (Object.keys(details).length > 0) {
            eventDiv.appendChild(detailsPre);
        }

        errorEventsContainer.prepend(eventDiv);
    }

    // --- WebSocket for real-time error events ---
    const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const wsUrl = `${protocol}://${window.location.host}/ws/benchmark-dashboard/`;
    const socket = new WebSocket(wsUrl);

    socket.onopen = function() {
        console.log('Connected to benchmark dashboard WebSocket.');
    };

    socket.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            if (data.type === 'debug_info' || data.type === 'error' || data.type === 'tool_argument_error') {
                addErrorEvent(data);
            }
        } catch (e) {
            console.error('Error parsing WebSocket message:', e);
        }
    };

    socket.onclose = function(event) {
        console.warn('Benchmark dashboard WebSocket closed:', event);
    };

    socket.onerror = function(error) {
        console.error('Benchmark dashboard WebSocket error:', error);
    };
    // --- End Error Events UI ---

    // --- Tag Filtering Logic ---
    tagFilterSelect.addEventListener('change', function() {
        const selectedTag = this.value;
        // Clear current options (except placeholder)
        // Keep the first option (placeholder) and remove the rest
        while (scenarioSelect.options.length > 1) {
            scenarioSelect.remove(1);
        }

        allScenarioOptions.forEach(option => {
            const scenarioTags = option.getAttribute('data-tags').split(',');
            // Check if the option has the selected tag, or if no tag is selected (show all)
            // Also handle the case where data-tags might be empty string if no tags exist
            if (!selectedTag || (scenarioTags.length > 0 && scenarioTags[0] !== '' && scenarioTags.includes(selectedTag))) {
                scenarioSelect.appendChild(option.cloneNode(true));
            }
        });
        // Reset selected scenario to the placeholder
        scenarioSelect.value = '';
    });
    // --- End Tag Filtering Logic ---


    // --- Form Submission Logic ---
        benchmarkForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            benchmarkStatus.classList.remove('hidden');
            benchmarkResult.classList.add('hidden');
            resultContent.innerHTML = ''; // Clear previous results

            const scenarioId = document.getElementById('scenario').value;
            const runs = document.getElementById('runs').value;
            const semanticEval = document.getElementById('semantic-eval').checked;
            // Get selected LLMConfig ID
            const llmConfigId = document.getElementById('llm-config-select').value;
            // Get selected Evaluation LLMConfig ID
            const evaluationLlmConfigId = document.getElementById('evaluation-llm-config-select').value;

            if (!scenarioId) {
                resultContent.innerHTML = `<div class="alert alert-danger">Please select a scenario.</div>`;
                benchmarkResult.classList.remove('hidden');
                benchmarkStatus.classList.add('hidden');
                return;
            }

                try {
                    // Use the correct API endpoint URL name ('game_of_life_admin' namespace)
                    const response = await fetch("{% url 'game_of_life_admin:benchmark_runs_api' %}", {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrfToken, // Include CSRF token
                        },
                        body: JSON.stringify({
                            scenario_id: scenarioId,
                            params: {
                                runs: parseInt(runs),
                                semantic_evaluation: semanticEval,
                                // Add LLMConfig ID to params if provided
                                ...(llmConfigId && { llm_config_id: llmConfigId }),
                                // Add Evaluation LLMConfig ID to params if provided
                                ...(evaluationLlmConfigId && { evaluation_llm_config_id: evaluationLlmConfigId })
                            }
                        })
                    });

                const data = await response.json();

                if (!response.ok) {
                    // Display detailed error from backend if available
                    let errorMsg = data.error || `HTTP error ${response.status}`;
                    const traceback = data.traceback ? `<pre>${data.traceback}</pre>` : '';
                    // If errorMsg is JSON string, try to parse and pretty print
                    try {
                        const parsedError = JSON.parse(errorMsg);
                        errorMsg = `<pre>${JSON.stringify(parsedError, null, 2)}</pre>`;
                    } catch (e) {
                        // Not JSON, keep as is
                    }
                    throw new Error(`${errorMsg}${traceback}`);
                }

                // Fetch the full results of the completed run
                // Use the correct detail API endpoint URL name ('game_of_life_admin' namespace)
                const resultResponse = await fetch(`{% url 'game_of_life_admin:benchmark_runs_detail_api' run_id=0 %}`.replace('0', data.run_id));
                if (!resultResponse.ok) {
                     const resultErrorData = await resultResponse.json();
                     throw new Error(resultErrorData.error || `Failed to fetch results (HTTP ${resultResponse.status})`);
                }
                const resultData = await resultResponse.json();

                displayBenchmarkResults(resultData); // This will now also update the table row
                resultContent.insertAdjacentHTML('afterbegin', `<div class="alert alert-success">Benchmark completed successfully (Run ID: ${data.run_id}).</div>`);


            } catch (error) {
                console.error('Benchmark error:', error);
                // Display error message, potentially including traceback if provided
                resultContent.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
                benchmarkResult.classList.remove('hidden');
            } finally {
                benchmarkStatus.classList.add('hidden');
            }
        });
        // --- End Form Submission Logic ---


    // --- View Details Logic ---
    document.querySelectorAll('.view-details').forEach(link => {
        link.addEventListener('click', async function(e) {
            e.preventDefault();
            const runId = this.getAttribute('data-run-id');
            benchmarkResult.classList.add('hidden'); // Hide previous results while loading
            resultContent.innerHTML = '<div class="loader"></div><p>Loading details...</p>'; // Show loader
            benchmarkResult.classList.remove('hidden');


            try {
            // Use the correct detail API endpoint URL name ('game_of_life_admin' namespace)
            const response = await fetch(`{% url 'game_of_life_admin:benchmark_runs_detail_api' run_id=0 %}`.replace('0', runId));
             if (!response.ok) {
                 const errorData = await response.json();
                 throw new Error(errorData.error || `Failed to fetch details (HTTP ${response.status})`);
             }
            const data = await response.json();
            // console.log('Fetched Run Details:', data); // Debug log removed

                displayBenchmarkResults(data); // This will now also update the table row
                benchmarkResult.scrollIntoView({ behavior: 'smooth' });

            } catch (error) {
                console.error('Error fetching details:', error);
                resultContent.innerHTML = `<div class="alert alert-danger">Error fetching details: ${error.message}</div>`;
                benchmarkResult.classList.remove('hidden');
            }
        });
    });
    // --- End View Details Logic ---


    // --- Display Results Function ---
    function displayBenchmarkResults(data) {
        benchmarkResult.classList.remove('hidden');

        // Format basic info
        resultContent.innerHTML = `
            <h4>${data.scenario} (${data.agent_role})</h4>
            <p>Execution Date: ${new Date(data.execution_date).toLocaleString()}</p>
            <p>Parameters: <pre>${JSON.stringify(data.parameters || {}, null, 2)}</pre></p>
            <div class="metrics-grid">
                <div class="metric">
                    <span class="metric-label">Mean Duration</span>
                    <span class="metric-value">${data.mean_duration.toFixed(2)}ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Median Duration</span>
                    <span class="metric-value">${data.median_duration.toFixed(2)}ms</span>
                </div>
                 <div class="metric">
                    <span class="metric-label">Min Duration</span>
                    <span class="metric-value">${data.min_duration.toFixed(2)}ms</span>
                </div>
                 <div class="metric">
                    <span class="metric-label">Max Duration</span>
                    <span class="metric-value">${data.max_duration.toFixed(2)}ms</span>
                </div>
                 <div class="metric">
                    <span class="metric-label">Std Dev</span>
                    <span class="metric-value">${data.std_dev.toFixed(2)}ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Success Rate</span>
                    <span class="metric-value">${(data.success_rate * 100).toFixed(1)}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">LLM Calls</span>
                    <span class="metric-value">${data.llm_calls}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Tool Calls</span>
                    <span class="metric-value">${data.tool_calls}</span>
                </div>
                ${data.semantic_score !== null ? `
                <div class="metric">
                    <span class="metric-label">Semantic Score</span>
                    <span class="metric-value">${(data.semantic_score * 100).toFixed(1)}%</span>
                </div>` : ''}
            </div>
        `;

        // Display Raw Results
        rawResultsPre.textContent = JSON.stringify(data.raw_results || {}, null, 2);

        // --- Render Plotly Charts ---
        renderPlotlyCharts(data);

        // --- Update the corresponding row in the Recent Runs table ---
        try { // Add try-catch for robustness
            const runId = data.id; // Assuming the fetched data has the run ID
            const rawResults = data.raw_results;
            // More robust check for errors array
            const hasErrors = rawResults && Array.isArray(rawResults.errors) && rawResults.errors.length > 0;

            if (runId) { // Proceed only if runId is valid
                const tableBody = document.getElementById('recent-runs-tbody');
                if (!tableBody) {
                    console.error("Could not find recent runs table body (#recent-runs-tbody).");
                    return; // Exit if table body not found
                }

                const row = tableBody.querySelector(`tr[data-run-id="${runId}"]`);
                const statusCell = tableBody.querySelector(`td[data-status-cell-id="${runId}"]`);

                if (row && statusCell) {
                    if (hasErrors) {
                        console.log(`Updating status for run ${runId} to Error.`);
                        row.classList.add('error-row');
                        row.title = 'Run completed with errors. Check details.';
                        statusCell.innerHTML = '<strong style="color: #a94442;">Error</strong>';
                    } else {
                        // Optional: Explicitly remove error state if the run was re-run and now succeeds
                        // This might not be necessary if the page reloads, but good practice for dynamic updates
                        // row.classList.remove('error-row');
                        // row.title = '';
                        // // Restore original success rate display if needed (might require fetching original value)
                        // // statusCell.innerHTML = ... // Restore original content if needed
                        console.log(`Run ${runId} has no errors, status cell not updated to Error.`);
                    }
                } else {
                    if (!row) console.warn(`Could not find table row for run ID: ${runId} to update status.`);
                    if (!statusCell) console.warn(`Could not find status cell for run ID: ${runId} to update status.`);
                }
            } else {
                 console.warn("Run ID missing in displayBenchmarkResults data.");
            }
        } catch (e) {
             console.error("Error updating recent runs table status within displayBenchmarkResults:", e);
        }
        // --- End Update Recent Runs Table Row ---


        console.log("Benchmark results and charts displayed on dashboard.");
    }
    // --- End Display Results Function ---


    // --- Render Charts Function ---
    function renderPlotlyCharts(data) {
        // Clear previous charts if any
        document.getElementById('duration-distribution-chart').innerHTML = '';
        document.getElementById('semantic-score-chart').innerHTML = '';
        document.getElementById('tool-usage-chart').innerHTML = '';

        // console.log("Rendering charts with data:", data); // Debug log removed

        const plotlyLayoutBase = {
            paper_bgcolor: 'rgba(0,0,0,0)', // Transparent background
            plot_bgcolor: '#f9f9f9', // Match card background slightly
            font: { color: '#333' }, // Dark text for light background
            margin: { l: 50, r: 30, t: 50, b: 60 }, // Adjust margins
        };
        const plotlyConfig = { responsive: true };

        // --- Chart 1: Min/Mean/Max Duration ---
        const durationChartDiv = document.getElementById('duration-distribution-chart');
        const minDuration = data.min_duration;
        const meanDuration = data.mean_duration;
        const maxDuration = data.max_duration;

        if (minDuration !== null && meanDuration !== null && maxDuration !== null) {
             const trace = {
                 x: ['Min', 'Mean', 'Max'],
                 y: [minDuration, meanDuration, maxDuration],
                 type: 'bar',
                 text: [minDuration.toFixed(2), meanDuration.toFixed(2), maxDuration.toFixed(2)], // Show values on bars
                 textposition: 'auto',
                 marker: {
                    color: ['#6c757d', '#447e9b', '#6c757d'] // Different color for mean
                 }
             };
             const layout = {
                 ...plotlyLayoutBase,
                 title: 'Duration Overview (ms)',
                 yaxis: { title: 'Duration (ms)' }
             };
             Plotly.newPlot(durationChartDiv, [trace], layout, plotlyConfig);
        } else {
             durationChartDiv.innerHTML = '<p>Duration data (min/mean/max) not available.</p>';
        }


        // --- Chart 2: Semantic Score ---
        const semanticScoreChartDiv = document.getElementById('semantic-score-chart');
        const score = data.semantic_score;
        // console.log("Semantic score for gauge:", score); // Debug log removed
        if (score !== null && score !== undefined && !isNaN(score)) {
            const chartData = [{
                type: 'indicator', mode: "gauge+number", value: score,
                title: { text: "Semantic Score", font: { size: 16 } },
                gauge: {
                    axis: { range: [0, 1], tickwidth: 1, tickcolor: "darkblue" },
                    bar: { color: "darkblue" }, bgcolor: "white", borderwidth: 1, bordercolor: "#ccc",
                    steps: [
                        { range: [0, 0.5], color: "rgba(255, 0, 0, 0.6)" },
                        { range: [0.5, 0.8], color: "rgba(255, 255, 0, 0.6)" },
                        { range: [0.8, 1], color: "rgba(0, 255, 0, 0.6)" }
                    ],
                    threshold: { line: { color: "red", width: 3 }, thickness: 0.75, value: 0.9 }
                }
            }];
            const layout = { ...plotlyLayoutBase, width: 300, height: 250, margin: { t: 40, r: 25, l: 25, b: 25 } };
            Plotly.newPlot(semanticScoreChartDiv, chartData, layout, plotlyConfig);
        } else {
            semanticScoreChartDiv.innerHTML = '<p>No semantic score data.</p>';
        }

        // --- Chart 3: Tool Usage Breakdown ---
        const toolBreakdownData = data.tool_breakdown;
        // console.log("Tool breakdown data for bar chart:", toolBreakdownData); // Debug log removed
        const toolUsageChartDiv = document.getElementById('tool-usage-chart');
        if (toolBreakdownData && typeof toolBreakdownData === 'object' && Object.keys(toolBreakdownData).length > 0) {
            const toolNames = Object.keys(toolBreakdownData);
            const toolCounts = Object.values(toolBreakdownData);
            // console.log("Tool names:", toolNames, "Counts:", toolCounts); // Debug log removed
            const trace = {
                x: toolNames, y: toolCounts, type: 'bar',
                marker: { color: 'rgb(91, 155, 213)' },
                text: toolCounts.map(String), textposition: 'auto',
            };
            const layout = { ...plotlyLayoutBase, title: 'Tool Usage Frequency', xaxis: { title: 'Tool Code', tickangle: -45 }, yaxis: { title: 'Calls' }, bargap: 0.1 };
            Plotly.newPlot(toolUsageChartDiv, [trace], layout, plotlyConfig);
        } else {
            toolUsageChartDiv.innerHTML = '<p>No tool usage data.</p>';
        }
    }
    // --- End Render Charts Function ---


    // --- Run All Benchmarks Logic ---
    const runAllForm = document.getElementById('run-all-benchmarks-form');
    const runAllStatus = document.getElementById('run-all-status');
    const runAllResult = document.getElementById('run-all-result');
    const runAllProgress = document.getElementById('run-all-progress');

    runAllForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        runAllStatus.classList.remove('hidden');
        runAllResult.classList.add('hidden'); // Hide previous result message
        runAllResult.className = 'hidden alert'; // Reset alert classes
        runAllProgress.textContent = ''; // Clear progress text

        const runs = document.getElementById('all-runs').value;
        const semanticEval = document.getElementById('all-semantic-eval').checked;

        try {
            // Define the URL for the new endpoint (needs to be created in urls.py and views.py)
            // Using a placeholder name for now, adjust as needed.
            const runAllUrl = "{% url 'game_of_life_admin:run_all_benchmarks' %}"; // Assumes this URL name exists

            const response = await fetch(runAllUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                },
                body: JSON.stringify({
                    runs: parseInt(runs),
                    semantic_evaluation: semanticEval
                })
            });

            const data = await response.json();

            if (!response.ok) {
                const errorMsg = data.error || `HTTP error ${response.status}`;
                throw new Error(errorMsg);
            }

            // Display success message
            runAllResult.textContent = `Successfully started running all benchmarks (Task ID: ${data.task_id}). Check Celery logs or the history page for progress.`;
            runAllResult.classList.remove('hidden');
            runAllResult.classList.add('alert-success'); // Add success class

        } catch (error) {
            console.error('Run All Benchmarks error:', error);
            runAllResult.textContent = `Error starting benchmarks: ${error.message}`;
            runAllResult.classList.remove('hidden');
            runAllResult.classList.add('alert-danger'); // Add error class
        } finally {
            // Keep the status message visible until completion or error
            // runAllStatus.classList.add('hidden'); // Maybe hide loader but keep message? Or rely on result message.
            // For now, just hide the loader itself after initiating
             const loader = runAllStatus.querySelector('.loader');
             if (loader) loader.style.display = 'none';
             runAllProgress.textContent = 'Request sent. Monitor progress elsewhere.'; // Update progress text
        }
    });
    // --- End Run All Benchmarks Logiccc ---

    // --- Update Recent Runs Table Status (Initial Load) ---
    try {
        const recentRunsDataElement = document.getElementById('recent-runs-data');
        if (recentRunsDataElement) {
            // Ensure textContent is not null or empty before parsing
            const jsonDataText = recentRunsDataElement.textContent;
            if (jsonDataText && jsonDataText.trim() !== '') {
                const recentRunsData = JSON.parse(jsonDataText);
                if (Array.isArray(recentRunsData)) {
                    recentRunsData.forEach(runData => {
                        // Check if runData has the necessary properties
                        if (runData && typeof runData === 'object' && runData.id !== undefined && runData.has_errors !== undefined) {
                            if (runData.has_errors) {
                                // Use more specific selectors if needed, e.g., within #recent-runs-tbody
                                const row = document.querySelector(`#recent-runs-tbody tr[data-run-id="${runData.id}"]`);
                                const statusCell = document.querySelector(`#recent-runs-tbody td[data-status-cell-id="${runData.id}"]`);
                                if (row) {
                                    row.classList.add('error-row');
                                    row.title = 'Run completed with errors. Check details.';
                                } else {
                                    console.warn(`Could not find table row for run ID: ${runData.id}`);
                                }
                                if (statusCell) {
                                    statusCell.innerHTML = '<strong style="color: #a94442;">Error</strong>';
                                } else {
                                     console.warn(`Could not find status cell for run ID: ${runData.id}`);
                                }
                            }
                        } else {
                             console.warn("Skipping invalid run data object:", runData);
                        }
                    });
                } else {
                    console.error("Parsed recent runs data is not an array:", recentRunsData);
                }
            } else {
                 console.warn("Recent runs data script tag is empty or contains only whitespace.");
            }
        } else {
            console.warn("Could not find recent runs data element with ID 'recent-runs-data'.");
        }
    } catch (e) {
        console.error("Error processing recent runs data for status update:", e);
    }
    // --- End Update Recent Runs Table Status ---

});
</script>
{% endblock %}
