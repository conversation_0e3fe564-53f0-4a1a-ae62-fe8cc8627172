<!-- backend/templates/admin_tools/benchmark_history.html -->
{% extends "admin/base_site.html" %} {# Extend the correct base site template #}
{% load static %}

{% block title %}Benchmark History | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrastyle %}
    {{ block.super }}
    <!-- Include Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@next/dist/chartjs-adapter-date-fns.bundle.min.js"></script> <!-- Date adapter -->

    <style>
        .history-container {
            max-width: 1400px; /* Wider container for history */
            margin: 0 auto;
            padding: 20px;
        }

        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .filters {
            margin-bottom: 20px;
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #eee;
        }

        .filter-form {
            display: flex;
            flex-wrap: wrap; /* Allow wrapping on smaller screens */
            gap: 20px; /* Increased gap */
            align-items: flex-end; /* Align items to bottom */
        }

        .form-group {
            margin-bottom: 10px; /* Add some bottom margin back */
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group select,
        .form-group input[type="date"] { /* Style date inputs */
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-width: 180px; /* Adjust width */
        }

        .benchmark-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .benchmark-table th,
        .benchmark-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #ddd;
            text-align: left;
            vertical-align: top; /* Align content to top */
        }

        .benchmark-table th {
            background-color: #f8f8f8;
            font-weight: bold;
        }

        .benchmark-table tr:hover {
            background-color: #f5f5f5;
        }

        #history-chart-container {
            margin-bottom: 30px;
            /* height: 350px; Remove fixed height */
            min-height: 350px; /* Set a minimum height for the main chart */
            position: relative; /* Needed for Chart.js responsiveness */
            height: 400px; /* Explicit height for testing graph flatness */
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1050; /* Higher z-index for admin */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.5); /* Darker background */
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto; /* More top margin */
            padding: 25px;
            border: 1px solid #888;
            width: 85%; /* Wider modal */
            max-width: 900px;
            border-radius: 8px;
            position: relative;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .close {
            color: #aaa;
            position: absolute; /* Position close button */
            top: 10px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .metric {
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
        }

        .metric-label {
            display: block;
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
        }

        .metric-value {
            display: block;
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .tool-chart-container {
            /* height: 300px; Remove fixed height */
            min-height: 200px; /* Ensure a minimum height */
            margin-top: 20px;
            position: relative; /* Needed for Chart.js */
        }

        .modal-section h3 {
            margin-top: 25px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        .modal-section pre {
             background: #f5f5f5;
             padding: 10px;
             border: 1px solid #ddd;
             max-height: 250px;
             overflow-y: auto;
             white-space: pre-wrap; /* Wrap long lines */
             word-wrap: break-word;
        }

        .btn {
            padding: 8px 15px; /* Slightly larger buttons */
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px; /* Slightly larger font */
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-right: 5px; /* Add some space between buttons if needed */
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
        .errornote { /* Style Django error messages */
            color: #a94442;
            background-color: #f2dede;
            border: 1px solid #ebccd1;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .loader {
            border: 4px solid #f3f3f3; /* Light grey */
            border-top: 4px solid #3498db; /* Blue */
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 10px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Comparison Styles */
        .comparison-controls {
            margin-bottom: 15px;
            text-align: right; /* Align button to the right */
        }
        #compare-selected-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        #comparison-modal .modal-content {
            max-width: 1200px; /* Wider modal for comparison */
            width: 95%;
        }
        .comparison-modal-body {
            display: flex;
            gap: 20px;
            overflow-x: auto; /* Allow horizontal scroll if needed */
        }
        .comparison-column {
            flex: 1;
            min-width: 400px; /* Ensure columns have minimum width */
            border: 1px solid #eee;
            padding: 15px;
            border-radius: 4px;
            background-color: #fdfdfd;
        }
        .comparison-column h4 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        .comparison-metric {
            margin-bottom: 12px;
            font-size: 0.95em;
        }
        .comparison-metric strong {
            display: inline-block;
            min-width: 150px; /* Align labels */
            color: #555;
        }
        .comparison-metric pre {
            background: #f0f0f0;
            padding: 8px;
            border: 1px solid #e0e0e0;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 0.9em;
            margin-top: 5px;
        }
    </style>
{% endblock %}

{# Remove the overridden breadcrumbs block entirely to inherit from base_site.html #}
{# {% block breadcrumbs %} ... {% endblock %} #}

{% block content %}
<div id="content-main" class="history-container">
    <h1>{{ title }} xx</h1>

     {% if error %}
        <p class="errornote">{{ error }}</p>
    {% endif %}

    <div class="filters">
        {# Use the correct admin namespace for the form action #}
        <form id="filter-form" class="filter-form"> {# Removed action, added ID #}
            <div class="form-group">
                <label for="agent-role">Agent Role:</label>
                <select id="agent-role" name="agent_role"> {# Removed onchange #}
                    <option value="">-- All Agents --</option>
                    {% for role in agent_roles %}
                    <option value="{{ role }}" {% if selected_role == role %}selected{% endif %}>
                        {{ role|title }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="scenario">Scenario:</label> {# New Scenario Filter #}
                <select id="scenario" name="scenario_id">
                    <option value="">-- All Scenarios (Aggregated View) --</option>
                    {% for scenario in available_scenarios %}
                    <option value="{{ scenario.id }}" {% if selected_scenario_id == scenario.id|stringformat:"s" %}selected{% endif %}>
                        {{ scenario.name }} (v{{ scenario.version }}) ({{ scenario.run_count }} runs) {# Display run count #}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="tag">Scenario Tag:</label>
                <select id="tag" name="tag">
                    <option value="">-- All Tags --</option>
                    {% for tag in available_tags %}
                    <option value="{{ tag.id }}" {% if selected_tag_id == tag.id|stringformat:"s" %}selected{% endif %}>
                        {{ tag.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="start-date">Start Date:</label>
                <input type="date" id="start-date" name="start_date" value="{{ selected_start_date|default:'' }}">
            </div>
            <div class="form-group">
                <label for="end-date">End Date:</label>
                <input type="date" id="end-date" name="end_date" value="{{ selected_end_date|default:'' }}">
            </div>
            <div class="form-group">
                 <button type="submit" class="btn btn-secondary">Apply Filters</button>
            </div>
        </form>
    </div>

    <div class="card">
        {# Add placeholders for dynamic content update #}
        <div id="history-chart-container">
            <canvas id="history-chart"></canvas>
            <div id="chart-loading-message" style="display: none; text-align: center; padding: 20px;">Loading chart data...</div>
            <div id="chart-no-data-message" style="display: none; text-align: center; padding: 20px;">No data available for the selected filters.</div>
        </div>

        <div class="comparison-controls">
            <button id="compare-selected-btn" class="btn btn-secondary" disabled>Compare Selected (0)</button>
        </div>

        <div id="table-container">
            {# Initial table structure will be replaced by JS #}
            <table class="benchmark-table">
                <thead>
                <tr>
                    <th><input type="checkbox" id="select-all-checkbox" title="Select/Deselect All Visible"></th> {# Checkbox Header #}
                    <th>Scenario</th>
                    <th>Agent</th>
                    <th>LLM Model</th>
                    <th>LLM Temp</th> {# New Column #}
                    <th>Date</th>
                    <th>Mean Duration (ms)</th>
                    <th>Success Rate</th>
                    <th>Semantic Score</th>
                    <th>Tokens In</th> {# New Column #}
                    <th>Tokens Out</th> {# New Column #}
                    <th>Est. Cost</th> {# New Column #}
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                    {# Rows will be populated by JS #}
                </tbody>
            </table>
            <p id="table-no-data-message" style="display: none; text-align: center; padding: 20px;">No benchmark runs match the selected filters.</p>
        </div>
        <div id="table-loading-message" style="display: none; text-align: center; padding: 20px;">Loading table data...</div>
    </div>

    <!-- Modal for viewing details -->
    <div id="details-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Benchmark Run Details</h2>
            <div id="modal-body">
                <!-- Content will be loaded here by JS -->
                <div class="loader"></div> Loading...
            </div>
        </div>
    </div>

    <!-- Modal for viewing comparison -->
    <div id="comparison-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Compare Benchmark Runs</h2>
            <div id="comparison-modal-body" class="comparison-modal-body">
                <!-- Comparison columns will be loaded here by JS -->
                <div class="loader"></div> Loading comparison...
            </div>
        </div>
    </div>
</div>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // --- Modal Element Declarations (Moved to top) ---
    const detailsModal = document.getElementById('details-modal');
    const detailsModalBody = document.getElementById('modal-body');
    const detailsCloseButton = detailsModal ? detailsModal.querySelector('.close') : null; // Added null check
    const comparisonModal = document.getElementById('comparison-modal');
    const comparisonModalBody = document.getElementById('comparison-modal-body');
    const comparisonCloseButton = comparisonModal ? comparisonModal.querySelector('.close') : null; // Added null check
    let modalToolChartInstance = null; // To manage modal chart - Moved here too

    // --- Other Element Declarations ---
    const historyChartEl = document.getElementById('history-chart');
    const chartLoadingMessage = document.getElementById('chart-loading-message');
    const chartNoDataMessage = document.getElementById('chart-no-data-message');
    const tableContainer = document.getElementById('table-container');
    const tableLoadingMessage = document.getElementById('table-loading-message');
    const tableNoDataMessage = document.getElementById('table-no-data-message');
    const filterForm = document.getElementById('filter-form');
    const compareButton = document.getElementById('compare-selected-btn');
    const selectAllCheckbox = document.getElementById('select-all-checkbox');

    let historyChartInstance = null; // To manage chart instance
    let selectedRunIds = new Set(); // To track selected runs for comparison

    // --- Chart Drawing Function ---
    // Updated to handle both aggregated and dimensional views
    // Added llmModels and agentVersions for scenario view tooltips
    function drawChart(isScenarioView, labelsISO, dimensionData, meanDurations, medianDurations, successRates, semanticScores, llmModels = [], agentVersions = []) {
        const chartLabels = labelsISO.map(isoString => new Date(isoString));
        const hasChartData = chartLabels.length > 0;

        // Show/hide messages based on data
        chartNoDataMessage.style.display = hasChartData ? 'none' : 'block';
        historyChartEl.style.display = hasChartData ? 'block' : 'none'; // Hide canvas if no data

        if (historyChartInstance) {
            historyChartInstance.destroy(); // Destroy previous instance
            historyChartInstance = null;
        }

        if (!hasChartData) {
            return; // Don't draw if no data
        }

        let chartConfigData;
        let chartConfigOptions;

        // Define distinct colors for dimensions
        const dimensionColors = [
            'rgb(255, 99, 132)', 'rgb(54, 162, 235)', 'rgb(255, 206, 86)',
            'rgb(75, 192, 192)', 'rgb(153, 102, 255)', 'rgb(255, 159, 64)',
            'rgb(199, 199, 199)', 'rgb(83, 102, 255)', 'rgb(40, 159, 64)',
            'rgb(210, 99, 132)'
        ];
        let colorIndex = 0;

        if (isScenarioView) {
            // --- Scenario View: Plot Dimensions AND Duration ---
            const datasets = [];
            // Add Dimension datasets
            for (const dimName in dimensionData) {
                const color = dimensionColors[colorIndex % dimensionColors.length];
                datasets.push({
                    label: dimName, // Dimension Name (e.g., Clarity)
                    data: dimensionData[dimName], // Array of scores for this dimension
                    borderColor: color,
                    backgroundColor: color.replace('rgb', 'rgba').replace(')', ', 0.1)'),
                    yAxisID: 'yDimensions', // Use the left Y axis for dimensions
                    tension: 0.1,
                    spanGaps: true // Connect lines across null data points
                });
                colorIndex++;
            }
            // Add Mean Duration dataset
            const durationColor = 'rgb(255, 99, 71)'; // Example: Tomato color for duration
            datasets.push({
                label: 'Mean Duration (ms)',
                data: meanDurations, // Use the meanDurations array passed in
                borderColor: durationColor,
                backgroundColor: durationColor.replace('rgb', 'rgba').replace(')', ', 0.1)'),
                yAxisID: 'yDuration', // Use the new right Y axis for duration
                tension: 0.1,
                spanGaps: true // Or false, depending on preference for missing duration
            });


            chartConfigData = {
                labels: chartLabels, // X-axis labels (dates)
                datasets: datasets
            };

            chartConfigOptions = { // Options for Scenario View
                responsive: true,
                maintainAspectRatio: false,
                interaction: { mode: 'index', intersect: false }, // Show tooltip for all datasets at the same x-index
                stacked: false,
                plugins: {
                    title: { display: true, text: 'Scenario Trend: Dimensions & Duration' }, // Updated title
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            // Custom label callback to format each line in the tooltip
                            label: function(tooltipItem) {
                                let label = tooltipItem.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (tooltipItem.parsed.y !== null) {
                                    // Format dimension scores (0-1) differently from duration (ms)
                                    if (tooltipItem.dataset.yAxisID === 'yDimensions') {
                                        label += tooltipItem.parsed.y.toFixed(2); // 2 decimal places for scores
                                    } else if (tooltipItem.dataset.yAxisID === 'yDuration') {
                                        label += tooltipItem.parsed.y.toFixed(0) + ' ms'; // 0 decimal places for ms
                                    } else {
                                        label += tooltipItem.formattedValue; // Fallback
                                    }
                                } else {
                                    label += 'N/A';
                                }
                                return label;
                            },
                            // Custom afterBody callback to add LLM model and Agent Version
                            afterBody: function(tooltipItems) {
                                // Get the index of the data point from the first item
                                const dataIndex = tooltipItems[0].dataIndex;
                                const model = llmModels[dataIndex] || 'N/A';
                                const version = agentVersions[dataIndex] || 'N/A';
                                // Return an array of strings, each will be a new line
                                return [
                                    `LLM: ${model}`,
                                    `Version: ${version}`
                                ];
                            }
                        } // End callbacks
                    } // End tooltip
                }, // End plugins
                scales: {
                    x: { // X-axis (Time)
                        type: 'time',
                        time: { unit: 'day', tooltipFormat: 'PPpp', displayFormats: { day: 'MMM d, yyyy' } },
                        title: { display: true, text: 'Execution Date' }
                    },
                    yDimensions: { // Y-axis for Semantic Scores (Left)
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: { display: true, text: 'Semantic Score (0-1)' },
                        min: 0,
                        max: 1, // Scores are 0-1
                        beginAtZero: true
                    },
                    yDuration: { // Y-axis for Duration (Right)
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: { display: true, text: 'Mean Duration (ms)' },
                        beginAtZero: true,
                        // Prevent grid lines from overlapping with the dimension grid lines
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                } // End scales
            }; // End Scenario View Options

        } else {
            // --- Aggregated View: Plot Original Metrics ---
            // Tooltip customization could be added here too if needed,
            // using the llmModels and agentVersions arrays passed from the backend.
            chartConfigData = {
                labels: chartLabels,
                datasets: [
                    {
                        label: 'Mean Duration (ms)',
                        data: meanDurations,
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        yAxisID: 'yDuration',
                        tension: 0.1
                    },
                    {
                        label: 'Median Duration (ms)',
                        data: medianDurations,
                        borderColor: 'rgb(153, 102, 255)',
                        backgroundColor: 'rgba(153, 102, 255, 0.1)',
                        yAxisID: 'yDuration',
                        tension: 0.1
                    },
                    {
                        label: 'Success Rate (%)',
                        data: successRates,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        yAxisID: 'ySuccess',
                        tension: 0.1
                    },
                    {
                        label: 'Semantic Score (0-1)', // Overall score
                        data: semanticScores,
                        borderColor: 'rgb(255, 159, 64)',
                        backgroundColor: 'rgba(255, 159, 64, 0.1)',
                        yAxisID: 'ySemantic',
                        tension: 0.1,
                        spanGaps: true // Connect lines across null data points
                    }
                ]
            };

            chartConfigOptions = { // Options for Aggregated View
                responsive: true,
                maintainAspectRatio: false,
                interaction: { mode: 'index', intersect: false },
                stacked: false,
                plugins: {
                    title: { display: true, text: 'Aggregated Benchmark History Trend' },
                    tooltip: { mode: 'index', intersect: false }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: { unit: 'day', tooltipFormat: 'PPpp', displayFormats: { day: 'MMM d, yyyy' } },
                        title: { display: true, text: 'Execution Date' }
                    },
                    yDuration: {
                        type: 'linear', display: true, position: 'left',
                        title: { display: true, text: 'Duration (ms)' },
                        beginAtZero: true
                    },
                    ySuccess: {
                        type: 'linear', display: true, position: 'right',
                        title: { display: true, text: 'Success Rate (%)' },
                        min: 0, max: 100,
                        grid: { drawOnChartArea: false }
                    },
                    ySemantic: {
                        type: 'linear', display: true, position: 'right',
                        title: { display: true, text: 'Overall Semantic Score (0-1)' },
                        min: 0, max: 1,
                        grid: { drawOnChartArea: false }
                    }
                }
            }; // End Aggregated View Options
        } // End else (Aggregated View)

        // --- Draw the chart using the determined config ---
        const ctx = historyChartEl.getContext('2d');
        historyChartInstance = new Chart(ctx, {
            type: 'line',
            data: chartConfigData,
            options: chartConfigOptions
        });

    } // End drawChart function


    // --- Update Table Function ---
    function updateTable(runs) {
        console.log("updateTable called with runs:", runs); // DEBUG: Log runs data received
        const table = tableContainer.querySelector('table');
        let tbody = table ? table.querySelector('tbody') : null;

        // Ensure table and tbody exist, and add checkbox header if missing
        if (!table) {
            tableContainer.innerHTML = `
                <table class="benchmark-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="select-all-checkbox" title="Select/Deselect All Visible"></th>
                            <th>Scenario</th><th>Agent</th><th>LLM Model</th><th>LLM Temp</th><th>Date</th>
                            <th>Mean Duration (ms)</th><th>Success Rate</th><th>Semantic Score</th>
                            <th>Tokens In</th><th>Tokens Out</th><th>Est. Cost</th><th>Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <p id="table-no-data-message" style="display: none; text-align: center; padding: 20px;">No benchmark runs match the selected filters.</p>`;
            tbody = tableContainer.querySelector('tbody');
        } else if (!tbody) {
            tbody = document.createElement('tbody');
            table.appendChild(tbody);
        }

        // Clear existing rows
        tbody.innerHTML = '';
        const currentNoDataMsg = tableContainer.querySelector('#table-no-data-message');
        if (currentNoDataMsg) currentNoDataMsg.style.display = 'none'; // Hide no data message initially

        if (runs && runs.length > 0) {
            table.style.display = ''; // Show table
            runs.forEach(run => {
                const row = tbody.insertRow();
                const semanticScoreDisplay = run.semantic_score !== null ? parseFloat(run.semantic_score).toFixed(2) : 'N/A';
                const meanDurationDisplay = run.mean_duration !== null ? parseFloat(run.mean_duration).toFixed(2) : 'N/A';
                // Ensure success_rate is treated as a percentage (0-1 scale from backend)
                const successRateDisplay = run.success_rate !== null ? (parseFloat(run.success_rate) * 100).toFixed(1) + '%' : 'N/A';
                const executionDate = new Date(run.execution_date).toLocaleString(); // Format date
                const llmTempDisplay = run.llm_temperature !== null ? parseFloat(run.llm_temperature).toFixed(1) : 'N/A';
                const tokensInDisplay = run.total_input_tokens ?? 'N/A';
                const tokensOutDisplay = run.total_output_tokens ?? 'N/A';
                const costDisplay = run.estimated_cost !== null ? '$' + parseFloat(run.estimated_cost).toFixed(4) : 'N/A';


                row.innerHTML = `
                    <td><input type="checkbox" class="run-checkbox" data-run-id="${run.id}"></td>
                    <td>${run.scenario_name || 'N/A'}</td>
                    <td>${run.agent_role ? run.agent_role.charAt(0).toUpperCase() + run.agent_role.slice(1) : 'N/A'}</td>
                    <td>${run.agent_llm_model_name || (run.llm_config && run.llm_config.model_name) || 'N/A'}</td>
                    <td>${llmTempDisplay}</td> {# New Column Data #}
                    <td>${executionDate}</td>
                    <td>${meanDurationDisplay}</td>
                    <td>${successRateDisplay}</td>
                    <td>${semanticScoreDisplay}</td>
                    <td>${tokensInDisplay}</td> {# New Column Data #}
                    <td>${tokensOutDisplay}</td> {# New Column Data #}
                    <td>${costDisplay}</td> {# New Column Data #}
                    <td>
                        <a href="#" class="view-details btn btn-secondary" data-run-id="${run.id}">View Details</a>
                    </td>
                `;
            }); // End of forEach loop

            // Ensure listeners are attached *after* rows are added
            attachModalListeners();
            attachCheckboxListeners();
            updateCompareButtonState(); // Update button state after table redraw

        } else {
            // Show no data message
            table.style.display = 'none'; // Hide table
            const noDataMsg = tableContainer.querySelector('#table-no-data-message');
            if (noDataMsg) noDataMsg.style.display = 'block';
        }
    }


    // --- Fetch Data and Update UI ---
    async function fetchDataAndUpdateUI() {
        const formData = new FormData(filterForm);
        const params = new URLSearchParams(formData);
        // Corrected API URL to fetch filtered run data AND chart data
        const apiUrl = `{% url 'game_of_life_admin:benchmark_runs_api' %}?${params.toString()}&include_chart_data=true`;

        // Show loading indicators
        chartLoadingMessage.style.display = 'block';
        tableLoadingMessage.style.display = 'block';
        historyChartEl.style.display = 'none'; // Hide chart canvas
        tableContainer.style.display = 'none'; // Hide table container
        const currentNoDataMsg = tableContainer.querySelector('#table-no-data-message');
        if (currentNoDataMsg) currentNoDataMsg.style.display = 'none'; // Hide no data message
        chartNoDataMessage.style.display = 'none'; // Hide chart no data message


        try {
            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            // Determine if it's scenario view based on API response or filters
            // The backend view doesn't explicitly return is_scenario_view in the API response yet.
            // We can infer it based on whether dimension_data exists and has keys.
            // Or, more reliably, check the scenario filter value directly.
            const scenarioSelect = document.getElementById('scenario');
            const isScenarioView = scenarioSelect && scenarioSelect.value !== '';

            // Update UI with fetched data
            // Pass the correct arguments based on the view type
            drawChart(
                isScenarioView,
                data.chart_data?.labels || [],
                data.chart_data?.dimension_data || {},
                data.chart_data?.mean_durations || [],
                data.chart_data?.median_durations || [],
                data.chart_data?.success_rates || [],
                data.chart_data?.semantic_scores || [],
                data.chart_data?.llm_models || [],      // Pass LLM models
                data.chart_data?.agent_versions || [] // Pass agent versions
            );

            updateTable(data.runs); // Update table with runs data

        } catch (error) {
            console.error('Error fetching benchmark data:', error);
            // Show error messages
            chartNoDataMessage.textContent = 'Error loading chart data.';
            chartNoDataMessage.style.display = 'block';
            tableContainer.innerHTML = '<p class="errornote">Error loading table data.</p>';
            tableContainer.style.display = 'block';
        } finally {
            // Hide loading indicators
            chartLoadingMessage.style.display = 'none';
            tableLoadingMessage.style.display = 'none';
        }
    }

    // --- Initial Chart Draw (using data from Django context) ---
    // Ensure llm_models_json and agent_versions_json are added to the context in views.py
    const isScenarioViewInitial = "{{ is_scenario_view|yesno:'true,false' }}";
    drawChart(
        isScenarioViewInitial,
        JSON.parse('{{ chart_labels_json|escapejs|default:"[]" }}'),
        JSON.parse('{{ dimension_data_json|escapejs|default:"{}" }}'),
        JSON.parse('{{ mean_durations_json|escapejs|default:"[]" }}'), // Pass mean durations for scenario view too
        JSON.parse('{{ median_durations_json|escapejs|default:"[]" }}'),
        JSON.parse('{{ success_rates_json|escapejs|default:"[]" }}'),
        JSON.parse('{{ semantic_scores_json|escapejs|default:"[]" }}'),
        JSON.parse('{{ llm_models_json|escapejs|default:"[]" }}'),      // Pass LLM models
        JSON.parse('{{ agent_versions_json|escapejs|default:"[]" }}') // Pass agent versions
    );

    // Update table with initial data
    const initialRuns = JSON.parse('{{ benchmark_runs_json|escapejs|default:"[]" }}');
    console.log("Initial Runs parsed:", initialRuns); // DEBUG: Log parsed initial runs
    try {
        updateTable(initialRuns);
        console.log("updateTable called successfully with initialRuns."); // DEBUG: Confirm call
    } catch (e) {
        console.error("Error calling updateTable with initialRuns:", e); // DEBUG: Catch errors
    }


    // --- Event Listeners ---

    // Filter form submission
    filterForm.addEventListener('submit', function(event) {
        event.preventDefault(); // Prevent default form submission
        fetchDataAndUpdateUI();
    });


    // --- Modal Functionality (Needs to be re-attached after table update) ---
    // Variable declarations moved to the top of the DOMContentLoaded scope

    function attachModalListeners() {
        // --- Details Modal ---
        // Use the variables declared at the top scope
        detailsCloseButton.onclick = function() {
            detailsModal.style.display = 'none';
            if (modalToolChartInstance) {
                modalToolChartInstance.destroy(); // Clean up chart
                modalToolChartInstance = null;
            }
        };

        // Close modal when clicking outside (handle both modals)
        window.onclick = function(event) {
            if (event.target == detailsModal) {
                detailsModal.style.display = 'none';
                 if (modalToolChartInstance) {
                    modalToolChartInstance.destroy();
                    modalToolChartInstance = null;
                }
            } else if (event.target == comparisonModal) {
                comparisonModal.style.display = 'none';
            }
        };

        // --- Comparison Modal ---
        comparisonCloseButton.onclick = function() {
            comparisonModal.style.display = 'none';
        };

        // --- Common for both ---
        // Use event delegation for dynamically added elements
        tableContainer.removeEventListener('click', handleTableClick); // Remove previous listener if any
        tableContainer.addEventListener('click', handleTableClick);
    }

    // --- Event Handler for Table Clicks (Delegation) ---
    function handleTableClick(event) {
        // Check if the clicked element is a "View Details" link
        if (event.target.matches('.view-details')) {
            handleViewDetailsClick.call(event.target, event); // Call the original handler with correct 'this' context
        }
        // Add other delegated listeners here if needed (e.g., for checkboxes)
        else if (event.target.matches('.run-checkbox')) {
             handleCheckboxChange.call(event.target, event);
        }
    }

    // --- Checkbox and Comparison Logic ---
    function updateCompareButtonState() {
        const count = selectedRunIds.size;
        compareButton.textContent = `Compare Selected (${count})`;
        compareButton.disabled = count !== 2; // Enable only when exactly 2 are selected
    }

    function attachCheckboxListeners() {
        const checkboxes = tableContainer.querySelectorAll('.run-checkbox');
        checkboxes.forEach(checkbox => {
            // Remove existing listener first
            checkbox.removeEventListener('change', handleCheckboxChange);
            // Add listener
            checkbox.addEventListener('change', handleCheckboxChange);
            // Sync checkbox state with the selectedRunIds set
            checkbox.checked = selectedRunIds.has(checkbox.getAttribute('data-run-id'));
        });

        // Select All Checkbox Listener
        if (selectAllCheckbox) {
            selectAllCheckbox.removeEventListener('change', handleSelectAllChange); // Remove first
            selectAllCheckbox.addEventListener('change', handleSelectAllChange);
            // Determine initial state of select-all
            const allVisibleChecked = checkboxes.length > 0 && Array.from(checkboxes).every(cb => cb.checked);
            selectAllCheckbox.checked = allVisibleChecked;
            selectAllCheckbox.indeterminate = !allVisibleChecked && selectedRunIds.size > 0 && selectedRunIds.size < checkboxes.length;
        }
    }

    function handleCheckboxChange(event) {
        const runId = event.target.getAttribute('data-run-id');
        if (event.target.checked) {
            selectedRunIds.add(runId);
        } else {
            selectedRunIds.delete(runId);
        }
        updateCompareButtonState();
        // Update select-all checkbox state
        const checkboxes = tableContainer.querySelectorAll('.run-checkbox');
        const allVisibleChecked = checkboxes.length > 0 && Array.from(checkboxes).every(cb => cb.checked);
        selectAllCheckbox.checked = allVisibleChecked;
        selectAllCheckbox.indeterminate = !allVisibleChecked && selectedRunIds.size > 0;
    }

    function handleSelectAllChange(event) {
        const isChecked = event.target.checked;
        const checkboxes = tableContainer.querySelectorAll('.run-checkbox');
        checkboxes.forEach(checkbox => {
            const runId = checkbox.getAttribute('data-run-id');
            checkbox.checked = isChecked;
            if (isChecked) {
                selectedRunIds.add(runId);
            } else {
                selectedRunIds.delete(runId);
            }
        });
        updateCompareButtonState();
        selectAllCheckbox.indeterminate = false; // Reset indeterminate state
    }

    // Compare Button Listener
    compareButton.addEventListener('click', async function() {
        if (selectedRunIds.size !== 2) return; // Should be disabled, but double-check

        comparisonModalBody.innerHTML = '<div class="loader"></div> Loading comparison...';
        comparisonModal.style.display = 'block';

        const ids = Array.from(selectedRunIds).join(',');
        const apiUrl = `{% url 'game_of_life_admin:benchmark_runs_api' %}?run_ids=${ids}`;

        try {
            const response = await fetch(apiUrl);
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Failed to fetch comparison data (HTTP ${response.status})`);
            }
            const data = await response.json();

            if (!data.runs || data.runs.length !== 2) {
                throw new Error("API did not return exactly two runs for comparison.");
            }

            // Sort runs by date ascending for consistent column order
            data.runs.sort((a, b) => new Date(a.execution_date) - new Date(b.execution_date));

            // Build comparison HTML
            comparisonModalBody.innerHTML = ''; // Clear loader
            data.runs.forEach(run => {
                const column = document.createElement('div');
                column.classList.add('comparison-column');

                // Helper to format values
                const formatVal = (val, decimals = 2, suffix = '') => val !== null && val !== undefined ? parseFloat(val).toFixed(decimals) + suffix : 'N/A';
                const formatPercent = (val) => val !== null && val !== undefined ? (parseFloat(val) * 100).toFixed(1) + '%' : 'N/A';
                const formatDate = (iso) => iso ? new Date(iso).toLocaleString() : 'N/A';

                column.innerHTML = `
                    <h4>Run: ${run.id.substring(0, 8)}...</h4>
                    <div class="comparison-metric"><strong>Scenario:</strong> ${run.scenario || 'N/A'}</div>
                    <div class="comparison-metric"><strong>Scenario:</strong> ${run.scenario || 'N/A'}</div>
                    <div class="comparison-metric"><strong>Agent Role:</strong> ${run.agent_role || 'N/A'}</div>
                    <div class="comparison-metric"><strong>Agent Version:</strong> ${run.agent_version || 'N/A'}</div>
                    <div class="comparison-metric"><strong>LLM Model:</strong> ${run.llm_model || 'N/A'}</div>
                    <div class="comparison-metric"><strong>LLM Temp:</strong> ${formatVal(run.llm_temperature, 1)}</div> {# Added Temp #}
                    <div class="comparison-metric"><strong>Date:</strong> ${formatDate(run.execution_date)}</div>
                    <hr>
                    <div class="comparison-metric"><strong>Mean Duration:</strong> ${formatVal(run.mean_duration, 2, 'ms')}</div>
                    <div class="comparison-metric"><strong>Median Duration:</strong> ${formatVal(run.median_duration, 2, 'ms')}</div>
                    <div class="comparison-metric"><strong>Min Duration:</strong> ${formatVal(run.min_duration, 2, 'ms')}</div>
                    <div class="comparison-metric"><strong>Max Duration:</strong> ${formatVal(run.max_duration, 2, 'ms')}</div>
                    <div class="comparison-metric"><strong>Std Dev:</strong> ${formatVal(run.std_dev)}</div>
                    <div class="comparison-metric"><strong>Success Rate:</strong> ${formatPercent(run.success_rate)}</div>
                    <hr>
                    <div class="comparison-metric"><strong>LLM Calls:</strong> ${run.llm_calls ?? 'N/A'}</div>
                    <div class="comparison-metric"><strong>Tool Calls:</strong> ${run.tool_calls ?? 'N/A'}</div>
                    <div class="comparison-metric"><strong>Tokens In:</strong> ${run.total_input_tokens ?? 'N/A'}</div> {# Added Tokens In #}
                    <div class="comparison-metric"><strong>Tokens Out:</strong> ${run.total_output_tokens ?? 'N/A'}</div> {# Added Tokens Out #}
                    <div class="comparison-metric"><strong>Est. Cost:</strong> ${formatVal(run.estimated_cost, 4, '$')}</div> {# Added Cost #}
                    <div class="comparison-metric"><strong>Tool Breakdown:</strong> <pre>${JSON.stringify(run.tool_breakdown || {}, null, 2)}</pre></div>
                    <hr>
                    <div class="comparison-metric"><strong>Semantic Score:</strong> ${formatVal(run.semantic_score, 2)}</div> {# Changed format to 0-1 #}
                    <div class="comparison-metric"><strong>Semantic Details:</strong> <pre>${JSON.stringify(run.semantic_evaluation_details || {}, null, 2)}</pre></div>
                    <div class="comparison-metric"><strong>Multi-Model Eval:</strong> <pre>${JSON.stringify(run.semantic_evaluations || {}, null, 2)}</pre></div>
                    <hr>
                    <div class="comparison-metric"><strong>Parameters:</strong> <pre>${JSON.stringify(run.parameters || {}, null, 2)}</pre></div>
                    <div class="comparison-metric"><strong>Stage Perf:</strong> <pre>${JSON.stringify(run.stage_performance_details || {}, null, 2)}</pre></div>
                    ${run.comparison_results ? `
                        <hr>
                        <div class="comparison-metric"><strong>Stat Comp. vs:</strong> ${run.comparison_results.compared_to_run_id ? run.comparison_results.compared_to_run_id.substring(0,8)+'...' : 'N/A'}</div>
                        <div class="comparison-metric"><strong>P-Value:</strong> ${formatVal(run.comparison_results.performance_p_value, 4)}</div>
                        <div class="comparison-metric"><strong>Significant?:</strong> ${run.comparison_results.is_performance_significant !== null ? (run.comparison_results.is_performance_significant ? 'Yes' : 'No') : 'N/A'}</div>
                    ` : ''}
                `;
                comparisonModalBody.appendChild(column);
            });

        } catch (error) {
            console.error('Error fetching comparison data:', error);
            comparisonModalBody.innerHTML = `<div class="alert alert-danger" style="width: 100%;">Error loading comparison: ${error.message}</div>`;
        }
    });


    // --- Details Modal Logic (Existing) ---
    async function handleViewDetailsClick(e) {
        e.preventDefault(); // Prevent default link behavior immediately
        console.log("View Details clicked for run ID:", this.getAttribute('data-run-id')); // Add logging

        const runId = this.getAttribute('data-run-id');
        detailsModalBody.innerHTML = '<div class="loader"></div> Loading...'; // Show loader
        detailsModal.style.display = 'block'; // Show modal immediately
        console.log("Modal display style set to:", detailsModal.style.display); // Log display style

        // Force display if needed (though 'block' should work)
        detailsModal.style.setProperty('display', 'block', 'important');


        try {
            // Use the correct admin namespace for the API URL
            const response = await fetch(`{% url 'game_of_life_admin:benchmark_runs_detail_api' run_id=0 %}`.replace('0', runId));
             if (!response.ok) {
                 const errorData = await response.json();
                 throw new Error(errorData.error || `Failed to fetch details (HTTP ${response.status})`);
             }
            const data = await response.json();

            // Display details in modal
            // Ensure all fields are accessed safely with defaults
            const meanDuration = data.mean_duration !== null ? data.mean_duration.toFixed(2) : 'N/A';
            const medianDuration = data.median_duration !== null ? data.median_duration.toFixed(2) : 'N/A';
            const minDuration = data.min_duration !== null ? data.min_duration.toFixed(2) : 'N/A';
            const maxDuration = data.max_duration !== null ? data.max_duration.toFixed(2) : 'N/A';
            const stdDev = data.std_dev !== null ? data.std_dev.toFixed(2) : 'N/A';
            const successRate = data.success_rate !== null ? (data.success_rate * 100).toFixed(1) + '%' : 'N/A';
            const semanticScore = data.semantic_score !== null ? parseFloat(data.semantic_score).toFixed(2) : 'N/A'; // Keep 0-1 scale for consistency

            detailsModalBody.innerHTML = `
                <h3>${data.scenario || 'N/A'} (${data.agent_role || 'N/A'})</h3>
                <p><strong>Run ID:</strong> ${runId}</p>
                <p><strong>Run ID:</strong> ${runId}</p>
                <p><strong>Execution Date:</strong> ${data.execution_date ? new Date(data.execution_date).toLocaleString() : 'N/A'}</p>
                <p><strong>Agent Version:</strong> ${data.agent_version || 'N/A'}</p>
                {# Removed LLM Model from here as it's in Operational Metrics #}

                <div class="modal-section">
                    <h3>Performance Metrics</h3>
                    <div class="metrics-grid">
                        <div class="metric"><span class="metric-label">Mean Duration</span><span class="metric-value">${meanDuration}ms</span></div>
                        <div class="metric"><span class="metric-label">Median Duration</span><span class="metric-value">${medianDuration}ms</span></div>
                        <div class="metric"><span class="metric-label">Min Duration</span><span class="metric-value">${minDuration}ms</span></div>
                        <div class="metric"><span class="metric-label">Max Duration</span><span class="metric-value">${maxDuration}ms</span></div>
                        <div class="metric"><span class="metric-label">Std Dev</span><span class="metric-value">${stdDev}ms</span></div>
                        <div class="metric"><span class="metric-label">Success Rate</span><span class="metric-value">${successRate}</span></div>
                    </div>
                </div>

                <div class="modal-section">
                    <h3>Operational Metrics</h3>
                     <div class="metrics-grid">
                        <div class="metric"><span class="metric-label">LLM Model</span><span class="metric-value">${data.llm_model || 'N/A'}</span></div> {# Moved LLM Model here #}
                        <div class="metric"><span class="metric-label">LLM Temp</span><span class="metric-value">${data.llm_temperature !== null ? parseFloat(data.llm_temperature).toFixed(1) : 'N/A'}</span></div> {# Added Temp #}
                        <div class="metric"><span class="metric-label">LLM Calls</span><span class="metric-value">${data.llm_calls ?? 'N/A'}</span></div>
                        <div class="metric"><span class="metric-label">Tool Calls</span><span class="metric-value">${data.tool_calls ?? 'N/A'}</span></div>
                        <div class="metric"><span class="metric-label">Memory Ops</span><span class="metric-value">${data.memory_operations || 0}</span></div>
                        <div class="metric"><span class="metric-label">Last Resp. Length</span><span class="metric-value">${data.last_response_length ?? 'N/A'}</span></div>
                        <div class="metric"><span class="metric-label">Tokens In</span><span class="metric-value">${data.total_input_tokens ?? 'N/A'}</span></div> {# Added Tokens In #}
                        <div class="metric"><span class="metric-label">Tokens Out</span><span class="metric-value">${data.total_output_tokens ?? 'N/A'}</span></div> {# Added Tokens Out #}
                        <div class="metric"><span class="metric-label">Est. Cost</span><span class="metric-value">${data.estimated_cost !== null ? '$' + parseFloat(data.estimated_cost).toFixed(4) : 'N/A'}</span></div> {# Added Cost #}
                     </div>
                </div>

                ${data.semantic_score !== null ? `
                <div class="modal-section">
                    <h3>Semantic Evaluation</h3>
                     <div class="metrics-grid">
                        <div class="metric"><span class="metric-label">Semantic Score</span><span class="metric-value">${semanticScore}</span></div> {# Display 0-1 score #}
                     </div>
                     <div id="semantic-evaluation-details">
                        <!-- Semantic details will be rendered here by JS -->
                     </div>
                </div>` : ''}

                <div class="modal-section">
                    <h3>Tool Usage</h3>
                    <div class="tool-chart-container">
                        <canvas id="modal-tool-chart"></canvas>
                    </div>
                </div>

                 <div class="modal-section">
                    <h3>Parameters</h3>
                    <pre>${JSON.stringify(data.parameters || {}, null, 2)}</pre>
                </div>

                <div class="modal-section">
                    <h3>Raw Results</h3>
                     <pre>${JSON.stringify(data.raw_results || {}, null, 2)}</pre>
                </div>

                ${data.stage_performance_details ? `
                <div class="modal-section">
                    <h3>Stage Performance Details</h3>
                    <pre>${JSON.stringify(data.stage_performance_details, null, 2)}</pre>
                </div>
                ` : ''}

                ${data.comparison_results ? `
                <div class="modal-section">
                    <h3>Statistical Comparison (vs Run ${data.comparison_results.compared_to_run_id})</h3>
                    <div class="metrics-grid">
                         <div class="metric"><span class="metric-label">Perf. p-value</span><span class="metric-value">${data.comparison_results.performance_p_value !== null ? data.comparison_results.performance_p_value.toFixed(4) : 'N/A'}</span></div>
                         <div class="metric"><span class="metric-label">Significant?</span><span class="metric-value">${data.comparison_results.is_performance_significant !== null ? (data.comparison_results.is_performance_significant ? 'Yes' : 'No') : 'N/A'}</span></div>
                    </div>
                </div>
                ` : ''}
            `;

            // Create tool chart in modal
            const modalToolCtx = document.getElementById('modal-tool-chart')?.getContext('2d');
            if (modalToolChartInstance) { // Destroy previous instance if exists
                modalToolChartInstance.destroy();
                modalToolChartInstance = null;
            }

            const toolBreakdown = data.tool_breakdown || {};
            if (modalToolCtx && Object.keys(toolBreakdown).length > 0) {
                const tools = Object.keys(toolBreakdown);
                const counts = tools.map(tool => toolBreakdown[tool]);
                const bgColors = tools.map((_, i) => `hsl(${i * (360 / tools.length)}, 70%, 70%)`);

                modalToolChartInstance = new Chart(modalToolCtx, {
                    type: 'pie', // Pie chart might be better for modal
                    data: {
                        labels: tools,
                        datasets: [{
                            label: 'Tool Usage',
                            data: counts,
                            backgroundColor: bgColors,
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'right' },
                            title: { display: false } // Title is in H3
                        }
                    }
                });
            } else if (modalToolCtx) {
                 modalToolCtx.font = "14px Arial";
                 modalToolCtx.fillStyle = "#666";
                 modalToolCtx.textAlign = "center";
                 modalToolCtx.fillText("No tool usage data available", modalToolCtx.canvas.width / 2, modalToolCtx.canvas.height / 2);
            }

            // --- Render Semantic Evaluation Details ---
            const semanticDetailsContainer = detailsModalBody.querySelector('#semantic-evaluation-details');
            if (semanticDetailsContainer) {
                semanticDetailsContainer.innerHTML = ''; // Clear previous content

                // Display Primary Evaluator Details (Legacy/Overall)
                if (data.semantic_evaluation_details && Object.keys(data.semantic_evaluation_details).length > 0) {
                    const primaryDetailsDiv = document.createElement('div');
                    primaryDetailsDiv.innerHTML = `
                        <h4>Primary Evaluator Details (Overall)</h4>
                        <pre>${JSON.stringify(data.semantic_evaluation_details, null, 2)}</pre>
                    `;
                    semanticDetailsContainer.appendChild(primaryDetailsDiv);
                }

                // Display Multi-Model Dimensional Details
                if (data.semantic_evaluations && typeof data.semantic_evaluations === 'object' && Object.keys(data.semantic_evaluations).length > 0) {
                    const multiModelDiv = document.createElement('div');
                    multiModelDiv.innerHTML = '<h4>Multi-Model Dimensional Evaluation</h4>';

                    for (const modelName in data.semantic_evaluations) {
                        const evaluation = data.semantic_evaluations[modelName];
                        const modelSection = document.createElement('div');
                        modelSection.style.marginBottom = '15px';
                        modelSection.style.paddingLeft = '10px';
                        modelSection.style.borderLeft = '2px solid #eee';

                        let dimensionsHtml = '<h5>Dimensions:</h5><ul>';
                        if (evaluation && evaluation.dimensions && typeof evaluation.dimensions === 'object') {
                            for (const dimName in evaluation.dimensions) {
                                const dimData = evaluation.dimensions[dimName] || {}; // Default to empty object if dimData is null/undefined
                                const score = dimData.score !== null && dimData.score !== undefined ? parseFloat(dimData.score).toFixed(2) : 'N/A';
                                // Escape HTML in reasoning to prevent potential XSS
                                const reasoning = dimData.reasoning ? document.createTextNode(dimData.reasoning).textContent : 'No reasoning provided.';
                                dimensionsHtml += `<li><strong>${dimName}</strong> (Score: ${score}): ${reasoning}</li>`;
                            }
                        } else {
                            dimensionsHtml += '<li>No dimensional data available.</li>';
                        }
                        dimensionsHtml += '</ul>';

                        const overallScore = evaluation && evaluation.overall_score !== null && evaluation.overall_score !== undefined ? parseFloat(evaluation.overall_score).toFixed(2) : 'N/A';
                        // Escape HTML in reasoning
                        const overallReasoning = evaluation && evaluation.overall_reasoning ? document.createTextNode(evaluation.overall_reasoning).textContent : 'No overall reasoning provided.';
                        const errorStatus = evaluation && evaluation.error ? '<span style="color: red;"> (Error during evaluation)</span>' : '';

                        modelSection.innerHTML = `
                            <h5 style="margin-bottom: 5px;">Evaluator: ${modelName}${errorStatus}</h5>
                            <p><strong>Overall Score:</strong> ${overallScore}</p>
                            <p><strong>Overall Reasoning:</strong> ${overallReasoning}</p>
                            ${dimensionsHtml}
                        `;
                        multiModelDiv.appendChild(modelSection);
                    }
                    semanticDetailsContainer.appendChild(multiModelDiv);
                } else if (data.semantic_score !== null) {
                    // Fallback if only legacy score exists but multi-eval doesn't
                     semanticDetailsContainer.innerHTML += '<p>Multi-model dimensional data not available for this run.</p>';
                }
            }
            // --- End Semantic Evaluation Rendering ---


        } catch (error) {
            console.error('Error fetching details:', error);
            detailsModalBody.innerHTML = `<div class="alert alert-danger">Error fetching benchmark details: ${error.message}</div>`;
        }
    } // <-- Correct closing brace for handleViewDetailsClick function

    // Initial attachment of modal and checkbox listeners
    // attachModalListeners(); // Called within updateTable now
    // attachCheckboxListeners(); // Called within updateTable now
    updateCompareButtonState(); // Initial button state is fine here

    // Pre-populate modal if URL has #run-<id>
    if (window.location.hash && window.location.hash.startsWith('#run-')) {
        const runId = window.location.hash.substring(5); // Get ID after #run-
        const viewButton = document.querySelector(`.view-details[data-run-id="${runId}"]`);
        if (viewButton) {
            viewButton.click(); // Simulate click to open modal
        }
    }
});
</script>
{% endblock %}
