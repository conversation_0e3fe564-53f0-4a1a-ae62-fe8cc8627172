# ACTIVE_FILE - 29-05-2025
{% extends "admin/base_site.html" %}
{% load static %}

{% block extrahead %}
{{ block.super }}
<script>
    window.BENCHMARK_SCENARIOS_API_URL = "{% url 'game_of_life_admin:benchmark_scenarios_api' %}";
    window.TEMPLATES_API_URL = "{% url 'game_of_life_admin:evaluation_templates_api' %}";
</script>
<!-- Enhanced evaluation template modal modules -->
<script src="{% static 'admin/js/template_modal_utils.js' %}"></script>
<script src="{% static 'admin/js/help_system.js' %}"></script>
<script src="{% static 'admin/js/contextual_criteria_builder.js' %}"></script>
<script src="{% static 'admin/js/variable_ranges_builder.js' %}"></script>
<script src="{% static 'admin/js/context_preview.js' %}"></script>
<script src="{% static 'admin/js/evaluation_template_modal.js' %}"></script>
<!-- Scenario editing modal modules -->
<script src="{% static 'admin/js/scenario_modal_utils.js' %}"></script>
<script src="{% static 'admin/js/scenario_editing_modal.js' %}"></script>
<!-- User profile management modules -->
<script src="{% static 'admin/js/user_profile_modal_utils.js' %}"></script>
<script src="{% static 'admin/js/user_profile_modal.js' %}"></script>
<!-- Main benchmark management script -->
<script src="{% static 'admin/js/benchmark_management.js' %}"></script>
{% endblock %}

{% block extrastyle %}
<link rel="stylesheet" href="{% static 'admin/css/benchmark_management.css' %}">
{% endblock %}

{% block content %}
<div class="content-main">
    <h1>Benchmark Management
        <button id="concept-help-btn" class="btn btn-info btn-sm" style="margin-left: 10px;" title="Learn about core concepts">
            <i class="fas fa-question-circle"></i> Core Concepts
        </button>
    </h1>

    {% if error %}
    <div class="alert alert-danger">{{ error }}</div>
    {% endif %}

    <ul class="nav nav-tabs" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link active" id="scenarios-tab" data-toggle="tab" href="#scenarios" role="tab" aria-controls="scenarios" aria-selected="true">Generic Situations</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="workflow-types-tab" data-toggle="tab" href="#workflow-types" role="tab" aria-controls="workflow-types" aria-selected="false">Workflow Types</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="templates-tab" data-toggle="tab" href="#templates" role="tab" aria-controls="templates" aria-selected="false">Context-Linked Assessment Framework</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="user-profiles-tab" data-toggle="tab" href="#user-profiles" role="tab" aria-controls="user-profiles" aria-selected="false">Comprehensive Context Variables</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="benchmark-runs-tab" data-toggle="tab" href="#benchmark-runs" role="tab" aria-controls="benchmark-runs" aria-selected="false">Benchmark Runs</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="validation-tab" data-toggle="tab" href="#validation" role="tab" aria-controls="validation" aria-selected="false">Validation</a>
        </li>
    </ul>

    <div class="tab-content" id="myTabContent">
        <!-- Generic Situations Tab -->
        <div class="tab-pane active" id="scenarios" role="tabpanel" aria-labelledby="scenarios-tab">
            <div class="card">
                <h2>Generic Situations</h2>
                <div class="alert alert-info">
                    <p><strong>Generic Situations</strong> define reusable testing contexts for benchmarks without being tied to specific user or environmental conditions.</p>
                    <p>These templates focus on core interaction patterns and can be adapted to different contexts using Comprehensive Context Variables.</p>
                </div>

                <div class="filter-section">
                    <h3>Filters</h3>
                    <div class="filter-row">
                        <div class="filter-item">
                            <label for="agent-role-filter">Agent Role:</label>
                            <select id="agent-role-filter" class="form-control">
                                <option value="">All Roles</option>
                                {% for role in agent_roles %}
                                <option value="{{ role }}">{{ role }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="workflow-type-filter">Workflow Type:</label>
                            <select id="workflow-type-filter" class="form-control">
                                <option value="">All Types</option>
                                {% for workflow_type in workflow_types %}
                                <option value="{{ workflow_type }}">{{ workflow_type }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="tag-filter">Tag:</label>
                            <select id="tag-filter" class="form-control">
                                <option value="">All Tags</option>
                                {% for tag in tags %}
                                <option value="{{ tag.id }}">{{ tag.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="active-filter">Status:</label>
                            <select id="active-filter" class="form-control">
                                <option value="">All</option>
                                <option value="true">Active</option>
                                <option value="false">Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button id="apply-filters-btn" class="btn btn-primary">Apply Filters</button>
                        <button id="reset-filters-btn" class="btn btn-secondary">Reset Filters</button>
                        <button id="create-scenario-btn" class="btn btn-success">Create New Generic Situation</button>
                        <button id="import-scenario-btn" class="btn btn-info">Import from JSON</button>
                    </div>
                </div>

                <div id="scenarios-table-container">
                    <table id="scenarios-table" class="table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all-scenarios-checkbox"> Select</th>
                                <th>Name</th>
                                <th>Agent Role</th>
                                <th>Workflow Type</th>
                                <th>Tags</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Generic situations will be loaded here via JavaScript -->
                        </tbody>
                    </table>
                    <div id="scenarios-loading" class="hidden">Loading generic situations...</div>
                    <div id="scenarios-empty" class="hidden">No generic situations found.</div>

                    <div id="batch-operations" class="hidden">
                        <h3>Batch Operations</h3>
                        <p><span id="selected-scenarios-count">0</span> generic situations selected</p>
                        <div class="action-buttons">
                            <button id="batch-validate-btn" class="btn btn-primary">Validate Selected</button>
                            <button id="batch-activate-btn" class="btn btn-success">Activate Selected</button>
                            <button id="batch-deactivate-btn" class="btn btn-warning">Deactivate Selected</button>
                            <button id="batch-add-tag-btn" class="btn btn-secondary">Add Tag to Selected</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Workflow Types Tab -->
        <div class="tab-pane" id="workflow-types" role="tabpanel" aria-labelledby="workflow-types-tab">
            <div class="card">
                <h2>Workflow Types</h2>

                <div class="alert alert-info">
                    <p>Workflow types are derived from benchmark scenarios. Each scenario has a workflow type specified in its metadata.</p>
                    <p>This view shows statistics for each workflow type in the system.</p>
                </div>

                <div id="workflow-types-table-container">
                    <table id="workflow-types-table" class="table">
                        <thead>
                            <tr>
                                <th>Workflow Type</th>
                                <th>Total Scenarios</th>
                                <th>Active Scenarios</th>
                                <th>Recent Runs</th>
                                <th>Avg. Success Rate</th>
                                <th>Avg. Semantic Score</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if workflow_stats %}
                                {% for stat in workflow_stats %}
                                <tr>
                                    <td>{{ stat.name }}</td>
                                    <td>{{ stat.scenario_count }}</td>
                                    <td>{{ stat.active_count }}</td>
                                    <td id="recent-runs-{{ stat.name }}">Loading...</td>
                                    <td id="success-rate-{{ stat.name }}">Loading...</td>
                                    <td id="semantic-score-{{ stat.name }}">Loading...</td>
                                    <td>
                                        <button class="btn btn-primary view-scenarios-btn" data-workflow-type="{{ stat.name }}">View Scenarios</button>
                                        <button class="btn btn-secondary validate-workflow-btn" data-workflow-type="{{ stat.name }}">Validate</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7">No workflow types found.</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                    <div id="workflow-types-loading" class="hidden">Loading workflow types...</div>
                </div>

                <div id="workflow-type-scenarios-container" class="hidden">
                    <h3>Scenarios for <span id="selected-workflow-type"></span></h3>
                    <button id="back-to-workflow-types-btn" class="btn btn-secondary">Back to Workflow Types</button>
                    <table id="workflow-type-scenarios-table" class="table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Agent Role</th>
                                <th>Tags</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Scenarios will be loaded here via JavaScript -->
                        </tbody>
                    </table>
                    <div id="workflow-type-scenarios-loading" class="hidden">Loading scenarios...</div>
                    <div id="workflow-type-scenarios-empty" class="hidden">No scenarios found for this workflow type.</div>
                </div>
            </div>
        </div>

        <!-- Comprehensive Context Variables Tab -->
        <div class="tab-pane" id="user-profiles" role="tabpanel" aria-labelledby="user-profiles-tab">
            <div class="card">
                <h2>Comprehensive Context Variables</h2>

                <div class="alert alert-info">
                    <p><strong>Comprehensive Context Variables</strong> encompass all factors that influence evaluation criteria, including user profile AND environmental conditions.</p>
                    <p>These variables are the primary interface for defining testing contexts, with user profiles serving as convenient presets that auto-populate variable ranges.</p>
                    <p><strong>Variable Categories:</strong></p>
                    <ul>
                        <li><strong>User Profile Variables:</strong> Trust level (0-100), personality traits, preferences, demographics</li>
                        <li><strong>Environmental Variables:</strong> Stress level (0-100), time pressure (0-100), mood state (valence/arousal -1.0 to 1.0)</li>
                        <li><strong>Interaction Variables:</strong> Session history, current goals, relationship phase</li>
                        <li><strong>Context Presets:</strong> Pre-configured variable combinations for common testing scenarios</li>
                    </ul>
                </div>

                <div class="filter-section">
                    <h3>Context Variable Management</h3>
                    <div class="filter-row">
                        <div class="filter-item">
                            <label for="profile-trust-phase-filter">Trust Phase:</label>
                            <select id="profile-trust-phase-filter" class="form-control">
                                <option value="">All Phases</option>
                                <option value="Foundation">Foundation (0-39)</option>
                                <option value="Expansion">Expansion (40-69)</option>
                                <option value="Integration">Integration (70-100)</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="profile-archetype-filter">Context Preset:</label>
                            <select id="profile-archetype-filter" class="form-control">
                                <option value="">All Context Presets</option>
                                <option value="new_user">New User Context</option>
                                <option value="experienced_user">Experienced User Context</option>
                                <option value="cautious_user">Cautious User Context</option>
                                <option value="adventurous_user">Adventurous User Context</option>
                                <option value="stressed_user">High Stress Context</option>
                                <option value="confident_user">High Confidence Context</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="profile-status-filter">Status:</label>
                            <select id="profile-status-filter" class="form-control">
                                <option value="">All</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button id="apply-profile-filters-btn" class="btn btn-primary">Apply Filters</button>
                        <button id="reset-profile-filters-btn" class="btn btn-secondary">Reset Filters</button>
                        <button id="create-profile-btn" class="btn btn-success">Create New Context Preset</button>
                        <button id="import-profile-btn" class="btn btn-info">Import from JSON</button>
                        <button id="export-profiles-btn" class="btn btn-secondary">Export All Context Presets</button>
                    </div>
                </div>

                <div id="user-profiles-table-container">
                    <table id="user-profiles-table" class="table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all-profiles-checkbox"> Select</th>
                                <th>Profile Name</th>
                                <th>Trust Phase</th>
                                <th>Trust Level</th>
                                <th>Archetype</th>
                                <th>Demographics</th>
                                <th>Personality</th>
                                <th>Status</th>
                                <th>Usage Count</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Profiles will be loaded here via JavaScript -->
                        </tbody>
                    </table>
                    <div id="user-profiles-loading" class="hidden">Loading user profiles...</div>
                    <div id="user-profiles-empty" class="hidden">No user profiles found. Create your first profile to get started.</div>

                    <div id="profile-batch-operations" class="hidden">
                        <h3>Batch Operations</h3>
                        <p><span id="selected-profiles-count">0</span> profiles selected</p>
                        <div class="action-buttons">
                            <button id="batch-activate-profiles-btn" class="btn btn-success">Activate Selected</button>
                            <button id="batch-deactivate-profiles-btn" class="btn btn-warning">Deactivate Selected</button>
                            <button id="batch-delete-profiles-btn" class="btn btn-danger">Delete Selected</button>
                            <button id="batch-export-profiles-btn" class="btn btn-secondary">Export Selected</button>
                            <button id="batch-duplicate-profiles-btn" class="btn btn-info">Duplicate Selected</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Context-Linked Assessment Framework Tab -->
        <div class="tab-pane" id="templates" role="tabpanel" aria-labelledby="templates-tab">
            <div class="card">
                <h2>Context-Linked Assessment Framework</h2>
                <div class="alert alert-info">
                    <p><strong>Context-Linked Assessment Framework</strong> defines evaluation criteria that are ALWAYS linked with Generic Situations and Context Variable ranges.</p>
                    <p>The structure follows: <code>Generic Situation + Context Variable Ranges → Adapted Evaluation Criteria</code></p>
                    <p>This ensures that evaluation criteria are contextually appropriate and consistently applied across different testing scenarios.</p>
                </div>

                <div class="filter-section">
                    <h3>Filters</h3>
                    <div class="filter-row">
                        <div class="filter-item">
                            <label for="template-workflow-type-filter">Workflow Type:</label>
                            <select id="template-workflow-type-filter" class="form-control">
                                <option value="">All Types</option>
                                {% for workflow_type in workflow_types %}
                                <option value="{{ workflow_type }}">{{ workflow_type }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="template-status-filter">Status:</label>
                            <select id="template-status-filter" class="form-control">
                                <option value="">All</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="template-category-filter">Category:</label>
                            <select id="template-category-filter" class="form-control">
                                <option value="">All Categories</option>
                                <option value="semantic">Semantic Evaluation</option>
                                <option value="quality">Quality Assessment</option>
                                <option value="phase">Phase-based</option>
                                <option value="custom">Custom</option>
                            </select>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button id="apply-template-filters-btn" class="btn btn-primary">Apply Filters</button>
                        <button id="reset-template-filters-btn" class="btn btn-secondary">Reset Filters</button>
                        <button id="create-template-btn" class="btn btn-success">Create New Template</button>
                        <button id="import-template-btn" class="btn btn-info">Import from JSON</button>
                    </div>
                </div>

                <div id="templates-table-container">
                    <table id="templates-table" class="table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all-templates-checkbox"> Select</th>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Workflow Type</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Templates will be loaded here via JavaScript -->
                        </tbody>
                    </table>
                    <div id="templates-loading" class="hidden">Loading templates...</div>
                    <div id="templates-empty" class="hidden">No templates found.</div>

                    <div id="template-batch-operations" class="hidden">
                        <h3>Batch Operations</h3>
                        <p><span id="selected-templates-count">0</span> templates selected</p>
                        <div class="action-buttons">
                            <button id="batch-activate-templates-btn" class="btn btn-success">Activate Selected</button>
                            <button id="batch-deactivate-templates-btn" class="btn btn-warning">Deactivate Selected</button>
                            <button id="batch-delete-templates-btn" class="btn btn-danger">Delete Selected</button>
                            <button id="batch-export-templates-btn" class="btn btn-secondary">Export Selected</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Benchmark Runs Tab -->
        <div class="tab-pane" id="benchmark-runs" role="tabpanel" aria-labelledby="benchmark-runs-tab">
            <div class="card">
                <h2>Recent Benchmark Runs</h2>

                <div class="filter-section">
                    <h3>Filters</h3>
                    <div class="filter-row">
                        <div class="filter-item">
                            <label for="runs-scenario-filter">Scenario:</label>
                            <select id="runs-scenario-filter" class="form-control">
                                <option value="">All Scenarios</option>
                                <!-- Will be populated via JavaScript -->
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="runs-agent-role-filter">Agent Role:</label>
                            <select id="runs-agent-role-filter" class="form-control">
                                <option value="">All Roles</option>
                                {% for role in agent_roles %}
                                <option value="{{ role }}">{{ role }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="runs-success-filter">Success:</label>
                            <select id="runs-success-filter" class="form-control">
                                <option value="">All</option>
                                <option value="true">Successful (≥50%)</option>
                                <option value="false">Failed (<50%)</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="runs-start-date">Start Date:</label>
                            <input type="date" id="runs-start-date" class="form-control">
                        </div>
                        <div class="filter-item">
                            <label for="runs-end-date">End Date:</label>
                            <input type="date" id="runs-end-date" class="form-control">
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button id="apply-runs-filters-btn" class="btn btn-primary">Apply Filters</button>
                        <button id="reset-runs-filters-btn" class="btn btn-secondary">Reset Filters</button>
                        <a href="{% url 'game_of_life_admin:benchmark_dashboard' %}" class="btn btn-info">Go to Dashboard</a>
                        <a href="{% url 'game_of_life_admin:benchmark_history' %}" class="btn btn-secondary">View Full History</a>
                    </div>
                </div>

                <div id="benchmark-runs-table-container">
                    <table id="benchmark-runs-table" class="table">
                        <thead>
                            <tr>
                                <th>Scenario</th>
                                <th>Agent Role</th>
                                <th>Date</th>
                                <th>Duration (ms)</th>
                                <th>Success Rate</th>
                                <th>Semantic Score</th>
                                <th>Trust Level</th>
                                <th>Valence</th>
                                <th>Arousal</th>
                                <th>Stress Level</th>
                                <th>Time Pressure</th>
                                <th>Token Usage</th>
                                <th>Cost</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Runs will be loaded here via JavaScript -->
                        </tbody>
                    </table>
                    <div id="benchmark-runs-loading" class="hidden">Loading benchmark runs...</div>
                    <div id="benchmark-runs-empty" class="hidden">No benchmark runs found.</div>
                </div>
            </div>
        </div>

        <!-- Validation Tab -->
        <div class="tab-pane" id="validation" role="tabpanel" aria-labelledby="validation-tab">
            <div class="card">
                <h2>Benchmark Validation</h2>

                <div class="filter-section">
                    <h3>Select Scenarios to Validate</h3>
                    <div id="validation-scenarios-container">
                        <div id="validation-scenarios-loading">Loading scenarios...</div>
                        <div id="validation-scenarios-list" class="hidden">
                            <!-- Scenarios checkboxes will be loaded here via JavaScript -->
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button id="validate-selected-btn" class="btn btn-primary">Validate Selected</button>
                        <button id="validate-all-btn" class="btn btn-secondary">Validate All</button>
                    </div>
                </div>

                <div id="validation-results-container" class="hidden">
                    <h3>Validation Results</h3>
                    <div id="validation-results">
                        <!-- Validation results will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scenario Modal -->
<div id="scenario-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2 id="scenario-modal-title">Create New Generic Situation</h2>
        <form id="scenario-form">
            <div class="form-group">
                <label for="scenario-name">Name:</label>
                <input type="text" id="scenario-name" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="scenario-description">Description:</label>
                <textarea id="scenario-description" class="form-control" rows="3"></textarea>
            </div>
            <div class="form-group">
                <label for="scenario-agent-role">Agent Role:</label>
                <select id="scenario-agent-role" class="form-control" required>
                    <option value="">Select Agent Role</option>
                    {% for role in agent_roles %}
                    <option value="{{ role }}">{{ role }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="scenario-tags">Tags (comma-separated):</label>
                <input type="text" id="scenario-tags" class="form-control">
            </div>
            <div class="form-group">
                <label for="scenario-is-active">Status:</label>
                <select id="scenario-is-active" class="form-control">
                    <option value="true">Active</option>
                    <option value="false">Inactive</option>
                </select>
            </div>
            <div class="form-group">
                <label for="scenario-input-data">Input Data (JSON):</label>
                <p class="help-text">This field should contain the input data for the generic situation, including user messages and context. The structure depends on the workflow type.</p>
                <textarea id="scenario-input-data" class="json-editor" required></textarea>
            </div>
            <div class="form-group">
                <label for="scenario-workflow-type">Workflow Type:</label>
                <select id="scenario-workflow-type" class="form-control" required>
                    <option value="">Select Workflow Type</option>
                    {% for workflow_type in workflow_types %}
                    <option value="{{ workflow_type }}">{{ workflow_type }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="scenario-metadata">Metadata (JSON):</label>
                <p class="help-text">This field contains configuration for the benchmark, including workflow type, evaluation criteria, and mock tool responses. The workflow_type value should match the selection above.</p>
                <textarea id="scenario-metadata" class="json-editor" required></textarea>
            </div>

            <!-- Context Variables Form Group (hidden by default, shown in context tab) -->
            <div class="form-group" id="scenario-context-variables" style="display: none;">
                <label>Context Variables:</label>
                <p class="help-text">Contextual variables for evaluation adaptation (managed in Context Variables tab).</p>
            </div>

            <!-- Preview Container (hidden by default, shown in preview tab) -->
            <div class="form-group" id="scenario-preview-container" style="display: none;">
                <label>Generic Situation Preview:</label>
                <div id="scenario-preview-content" class="preview-content">
                    <p class="text-muted">Switch to Preview & Test tab to see generic situation validation and testing options.</p>
                </div>
            </div>

            <input type="hidden" id="scenario-id">
            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                <button type="button" id="scenario-generate-sample-btn" class="btn btn-info">Generate Sample</button>
            </div>
        </form>
    </div>
</div>

<!-- Template Modal -->
<div id="template-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2 id="template-modal-title">Create New Context-Linked Assessment Framework</h2>
        <form id="template-form">
            <div class="form-group">
                <label for="template-name">Name:</label>
                <input type="text" id="template-name" class="form-control" required>
                <p class="help-text">A descriptive name for this evaluation template</p>
            </div>
            <div class="form-group">
                <label for="template-description">Description:</label>
                <textarea id="template-description" class="form-control" rows="3"></textarea>
                <p class="help-text">Detailed description of what this template evaluates</p>
            </div>
            <div class="form-group">
                <label for="template-workflow-type-select">Workflow Type:</label>
                <select id="template-workflow-type-select" class="form-control">
                    <option value="">All Workflow Types</option>
                    {% for workflow_type in workflow_types %}
                    <option value="{{ workflow_type }}">{{ workflow_type }}</option>
                    {% endfor %}
                </select>
                <p class="help-text">Specific workflow type this template is designed for (optional)</p>
            </div>
            <div class="form-group">
                <label for="template-category-select">Category:</label>
                <select id="template-category-select" class="form-control" required>
                    <option value="">Select Category</option>
                    <option value="semantic">Semantic Evaluation</option>
                    <option value="quality">Quality Assessment</option>
                    <option value="phase">Phase-based</option>
                    <option value="contextual">Contextual Evaluation</option>
                    <option value="custom">Custom</option>
                </select>
                <p class="help-text">Category that best describes this evaluation template</p>
            </div>
            <div class="form-group">
                <label for="template-is-active">Status:</label>
                <select id="template-is-active" class="form-control">
                    <option value="true">Active</option>
                    <option value="false">Inactive</option>
                </select>
                <p class="help-text">Whether this template is available for use</p>
            </div>
            <div class="form-group">
                <label for="template-criteria">Base Evaluation Criteria (JSON):</label>
                <p class="help-text">JSON structure defining the base evaluation criteria. Can be simple criteria object or phase-based structure.</p>
                <textarea id="template-criteria" class="json-editor" required></textarea>
            </div>
            <div class="form-group">
                <label for="template-contextual-criteria">Contextual Criteria (JSON):</label>
                <p class="help-text">Optional: Criteria that vary based on context variables like trust_level, mood (valence/arousal), environment (stress_level/time_pressure).</p>
                <textarea id="template-contextual-criteria" class="json-editor" placeholder='{"trust_level": {"0-39": {"Tone": ["Simple", "Clear"]}, "40-69": {"Tone": ["Encouraging", "Supportive"]}, "70-100": {"Tone": ["Collaborative", "Empowering"]}}}'></textarea>
            </div>
            <div class="form-group">
                <label for="template-variable-ranges">Variable Ranges (JSON):</label>
                <p class="help-text">Optional: Defines the supported ranges for contextual variables this template can handle.</p>
                <textarea id="template-variable-ranges" class="json-editor" placeholder='{"trust_level": {"min": 0, "max": 100}, "mood": {"valence": {"min": -1.0, "max": 1.0}, "arousal": {"min": -1.0, "max": 1.0}}, "environment": {"stress_level": {"min": 0, "max": 100}, "time_pressure": {"min": 0, "max": 100}}}'></textarea>
            </div>
            <input type="hidden" id="template-id">
            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                <button type="button" id="template-generate-sample-btn" class="btn btn-info">Generate Sample</button>
            </div>
        </form>
    </div>
</div>

<!-- Import Scenario Modal -->
<div id="import-scenario-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>Import Generic Situation from JSON</h2>
        <form id="import-scenario-form">
            <div class="form-group">
                <label for="scenario-file">Select JSON File:</label>
                <input type="file" id="scenario-file" accept=".json" required>
                <p class="help-text">Select a JSON file containing a generic situation definition.</p>
                <div style="margin-top: 10px;">
                    <button type="button" id="generate-template-btn" class="btn btn-secondary">Generate Template</button>
                    <select id="template-workflow-type" style="margin-left: 10px; padding: 5px;">
                        <option value="">Select Workflow Type</option>
                        {% for workflow_type in workflow_types %}
                        <option value="{{ workflow_type }}">{{ workflow_type }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="import-preview">Preview:</label>
                <pre id="import-preview" style="max-height: 300px; overflow-y: auto; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">No file selected</pre>
            </div>
            <div class="form-group">
                <div id="import-scenario-error-message" class="alert alert-danger hidden"></div>
            </div>
            <div class="form-group">
                <details>
                    <summary>Schema Information</summary>
                    <div class="schema-info" style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 10px;">
                        <h4>Required Fields</h4>
                        <ul>
                            <li><strong>name</strong>: String - Name of the scenario</li>
                            <li><strong>agent_role</strong>: String - Role of the agent being benchmarked</li>
                            <li><strong>input_data</strong>: Object - Input data for the benchmark scenario</li>
                            <li><strong>metadata</strong>: Object - Configuration for the benchmark</li>
                            <li><strong>metadata.workflow_type</strong>: String - Type of workflow being benchmarked</li>
                        </ul>

                        <h4>Optional Fields</h4>
                        <ul>
                            <li><strong>description</strong>: String - Description of the scenario</li>
                            <li><strong>is_active</strong>: Boolean - Whether the scenario is active (default: true)</li>
                            <li><strong>version</strong>: String - Version of the scenario (default: "1.0.0")</li>
                        </ul>

                        <h4>Metadata Fields</h4>
                        <ul>
                            <li><strong>user_profile_context</strong>: Object - User profile for the benchmark</li>
                            <li><strong>expected_quality_criteria</strong>: Object - Expected quality criteria</li>
                            <li><strong>evaluation_criteria_by_phase</strong>: Object - Phase-aware evaluation criteria</li>
                            <li><strong>evaluator_models</strong>: Array - List of evaluator models</li>
                            <li><strong>mock_tool_responses</strong>: Object - Mock responses for tools</li>
                            <li><strong>tool_expectations</strong>: Array - Expectations for tool calls</li>
                            <li><strong>situation</strong>: Object - Situation for the benchmark</li>
                            <li><strong>evaluation_criteria</strong>: Object - Evaluation criteria</li>
                            <li><strong>warmup_runs</strong>: Number - Number of warmup runs (default: 1)</li>
                            <li><strong>benchmark_runs</strong>: Number - Number of benchmark runs (default: 3)</li>
                        </ul>

                        <h4>Example Structure</h4>
<pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 0.9em;">
{
  "name": "Example Scenario",
  "description": "A test scenario for wheel generation",
  "agent_role": "mentor",
  "input_data": {
    "user_message": "I want to try something new today",
    "context_packet": {
      "workflow_type": "wheel_generation",
      "trust_level": 50
    }
  },
  "metadata": {
    "workflow_type": "wheel_generation",
    "user_profile_context": {
      "trust_level": 50,
      "preferences": {
        "activity_types": ["outdoor", "creative"]
      }
    },
    "expected_quality_criteria": {
      "Content": ["Should be relevant and helpful"],
      "Tone": ["Should be supportive and encouraging"]
    },
    "mock_tool_responses": {
      "get_user_profile": {
        "response": "{ \"id\": \"123\", \"name\": \"Test User\" }"
      }
    },
    "warmup_runs": 1,
    "benchmark_runs": 3
  },
  "is_active": true
}
</pre>
                    </div>
                </details>
            </div>
            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">Import</button>
                <button type="button" class="btn btn-secondary close-modal">Cancel</button>
            </div>
        </form>
    </div>
</div>

<!-- User Profile Modal -->
<div id="user-profile-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2 id="user-profile-modal-title">Create New Context Preset</h2>
        <form id="user-profile-form">
            <div class="form-group">
                <label for="profile-name">Profile Name:</label>
                <input type="text" id="profile-name" class="form-control" required>
                <p class="help-text">A descriptive name for this user profile (e.g., "New User - Foundation Phase", "Experienced Creative User")</p>
            </div>
            <div class="form-group">
                <label for="profile-description">Description:</label>
                <textarea id="profile-description" class="form-control" rows="3"></textarea>
                <p class="help-text">Detailed description of this user profile and when to use it</p>
            </div>
            <div class="form-group">
                <label for="profile-archetype">Profile Archetype:</label>
                <select id="profile-archetype" class="form-control" required>
                    <option value="">Select Archetype</option>
                    <option value="new_user">New User - Just starting, low trust, cautious</option>
                    <option value="experienced_user">Experienced User - Familiar with system, moderate trust</option>
                    <option value="cautious_user">Cautious User - Risk-averse, prefers safe options</option>
                    <option value="adventurous_user">Adventurous User - Open to new experiences, high trust</option>
                    <option value="stressed_user">Stressed User - Under pressure, needs quick solutions</option>
                    <option value="confident_user">Confident User - Self-assured, collaborative approach</option>
                    <option value="custom">Custom - Define your own archetype</option>
                </select>
                <p class="help-text">Choose a predefined archetype or create a custom profile</p>
            </div>
            <div class="form-group">
                <label for="profile-trust-level">Trust Level:</label>
                <div class="trust-level-container">
                    <input type="range" id="profile-trust-level" min="0" max="100" value="50" class="form-range">
                    <div class="trust-level-display">
                        <span id="profile-trust-level-value">50</span>
                        <span id="profile-trust-phase" class="trust-phase">Expansion</span>
                    </div>
                </div>
                <div class="trust-level-guide">
                    <small>
                        <strong>Foundation (0-39):</strong> Building basic trust, needs simple and clear guidance<br>
                        <strong>Expansion (40-69):</strong> Growing confidence, open to moderate challenges<br>
                        <strong>Integration (70-100):</strong> High trust, ready for collaboration and complex tasks
                    </small>
                </div>
            </div>
            <div class="form-group">
                <label for="profile-is-active">Status:</label>
                <select id="profile-is-active" class="form-control">
                    <option value="true">Active</option>
                    <option value="false">Inactive</option>
                </select>
                <p class="help-text">Whether this profile is available for use in scenarios</p>
            </div>
            <div class="form-group">
                <label>Demographics:</label>
                <div class="demographics-grid">
                    <div class="demo-item">
                        <label for="profile-age">Age:</label>
                        <input type="number" id="profile-age" class="form-control" min="13" max="120" value="30">
                    </div>
                    <div class="demo-item">
                        <label for="profile-gender">Gender:</label>
                        <select id="profile-gender" class="form-control">
                            <option value="">Not specified</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                            <option value="non-binary">Non-binary</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="demo-item">
                        <label for="profile-location">Location:</label>
                        <input type="text" id="profile-location" class="form-control" placeholder="e.g., New York, USA">
                    </div>
                    <div class="demo-item">
                        <label for="profile-occupation">Occupation:</label>
                        <input type="text" id="profile-occupation" class="form-control" placeholder="e.g., Software Engineer">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>HEXACO Personality Traits:</label>
                <p class="help-text">Configure the six major personality dimensions (0.0 = low, 1.0 = high)</p>
                <div class="hexaco-grid">
                    <div class="hexaco-trait">
                        <label for="profile-honesty-humility">Honesty-Humility: <span id="honesty-humility-value">0.5</span></label>
                        <input type="range" id="profile-honesty-humility" min="0" max="1" step="0.1" value="0.5" class="form-range">
                        <small>Sincerity, fairness, greed avoidance, modesty</small>
                    </div>
                    <div class="hexaco-trait">
                        <label for="profile-emotionality">Emotionality: <span id="emotionality-value">0.5</span></label>
                        <input type="range" id="profile-emotionality" min="0" max="1" step="0.1" value="0.5" class="form-range">
                        <small>Fearfulness, anxiety, dependence, sentimentality</small>
                    </div>
                    <div class="hexaco-trait">
                        <label for="profile-extraversion">Extraversion: <span id="extraversion-value">0.5</span></label>
                        <input type="range" id="profile-extraversion" min="0" max="1" step="0.1" value="0.5" class="form-range">
                        <small>Social self-esteem, social boldness, sociability, liveliness</small>
                    </div>
                    <div class="hexaco-trait">
                        <label for="profile-agreeableness">Agreeableness: <span id="agreeableness-value">0.5</span></label>
                        <input type="range" id="profile-agreeableness" min="0" max="1" step="0.1" value="0.5" class="form-range">
                        <small>Forgiveness, gentleness, flexibility, patience</small>
                    </div>
                    <div class="hexaco-trait">
                        <label for="profile-conscientiousness">Conscientiousness: <span id="conscientiousness-value">0.5</span></label>
                        <input type="range" id="profile-conscientiousness" min="0" max="1" step="0.1" value="0.5" class="form-range">
                        <small>Organization, diligence, perfectionism, prudence</small>
                    </div>
                    <div class="hexaco-trait">
                        <label for="profile-openness">Openness: <span id="openness-value">0.5</span></label>
                        <input type="range" id="profile-openness" min="0" max="1" step="0.1" value="0.5" class="form-range">
                        <small>Aesthetic appreciation, inquisitiveness, creativity, unconventionality</small>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="profile-preferences">Preferences (JSON):</label>
                <p class="help-text">User preferences for activities, communication styles, interests, etc.</p>
                <textarea id="profile-preferences" class="json-editor" rows="8" placeholder='{"activity_types": ["outdoor", "creative"], "communication_style": "supportive", "interests": ["technology", "fitness"]}'></textarea>
            </div>
            <div class="form-group">
                <label for="profile-mock-responses">Mock Tool Responses (JSON):</label>
                <p class="help-text">Define how tools should respond when this profile is used in benchmarks</p>
                <textarea id="profile-mock-responses" class="json-editor" rows="6" placeholder='{"get_user_profile": {"response": {"id": "user-123", "name": "Test User"}}, "get_user_activities": {"response": {"activities": []}}}'></textarea>
            </div>
            <input type="hidden" id="profile-id">
            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">Save Profile</button>
                <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                <button type="button" id="profile-load-template-btn" class="btn btn-info">Load Template</button>
                <button type="button" id="profile-preview-btn" class="btn btn-outline-primary">Preview Profile</button>
            </div>
        </form>
    </div>
</div>

<!-- Import Template Modal -->
<div id="import-template-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>Import Evaluation Template from JSON</h2>
        <form id="import-template-form">
            <div class="form-group">
                <label for="template-file">Select JSON File:</label>
                <input type="file" id="template-file" accept=".json" required>
                <p class="help-text">Select a JSON file containing an evaluation template definition.</p>
                <div style="margin-top: 10px;">
                    <button type="button" id="generate-template-sample-btn" class="btn btn-secondary">Generate Sample Template</button>
                    <select id="sample-template-category" style="margin-left: 10px; padding: 5px;">
                        <option value="semantic">Semantic Evaluation</option>
                        <option value="quality">Quality Assessment</option>
                        <option value="phase">Phase-based</option>
                        <option value="custom">Custom</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="template-import-preview">Preview:</label>
                <pre id="template-import-preview" style="max-height: 300px; overflow-y: auto; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">No file selected</pre>
            </div>
            <div class="form-group">
                <div id="import-template-error-message" class="alert alert-danger hidden"></div>
            </div>
            <div class="form-group">
                <details>
                    <summary>Template Schema Information</summary>
                    <div class="schema-info" style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 10px;">
                        <h4>Required Fields</h4>
                        <ul>
                            <li><strong>name</strong>: String - Name of the template</li>
                            <li><strong>category</strong>: String - Category (semantic, quality, phase, custom)</li>
                            <li><strong>criteria</strong>: Object - Evaluation criteria structure</li>
                        </ul>

                        <h4>Optional Fields</h4>
                        <ul>
                            <li><strong>description</strong>: String - Description of the template</li>
                            <li><strong>workflow_type</strong>: String - Specific workflow type (optional)</li>
                            <li><strong>is_active</strong>: Boolean - Whether template is active (default: true)</li>
                            <li><strong>version</strong>: String - Version of the template (default: "1.0.0")</li>
                        </ul>

                        <h4>Example Structures</h4>
                        <h5>Simple Criteria Template:</h5>
<pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 0.9em;">
{
  "name": "Basic Quality Assessment",
  "description": "Simple quality evaluation template",
  "category": "quality",
  "criteria": {
    "Content": ["Relevance", "Accuracy", "Completeness"],
    "Tone": ["Appropriate", "Supportive", "Professional"],
    "Structure": ["Clear", "Organized", "Logical"]
  },
  "is_active": true
}
</pre>
                        <h5>Phase-based Template:</h5>
<pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 0.9em;">
{
  "name": "Phase-based Evaluation",
  "description": "Evaluation template with phase-specific criteria",
  "category": "phase",
  "criteria": {
    "foundation": {
      "Understanding": ["Grasps user needs", "Identifies context"],
      "Approach": ["Systematic", "Thoughtful"]
    },
    "expansion": {
      "Creativity": ["Novel ideas", "Diverse options"],
      "Feasibility": ["Practical", "Achievable"]
    },
    "integration": {
      "Synthesis": ["Coherent solution", "Well-integrated"],
      "Presentation": ["Clear", "Compelling"]
    }
  },
  "is_active": true
}
</pre>
                    </div>
                </details>
            </div>
            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">Import</button>
                <button type="button" class="btn btn-secondary close-modal">Cancel</button>
            </div>
        </form>
    </div>
</div>

<!-- Core Concepts Help Modal -->
<div id="concept-help-modal" class="modal">
    <div class="modal-content" style="max-width: 800px;">
        <span class="close">&times;</span>
        <h2>Core Concepts Guide</h2>

        <div class="concept-section">
            <h3>🎯 Generic Situations</h3>
            <p><strong>Definition:</strong> Reusable test case templates that define core interaction patterns without being tied to specific user or environmental conditions.</p>
            <p><strong>Examples:</strong> "Life Wheel Creation", "Goal Setting Session", "Progress Review Meeting"</p>
            <p><strong>Key Principle:</strong> Context-agnostic, focused on the core interaction pattern that can be adapted to different contexts.</p>
        </div>

        <div class="concept-section">
            <h3>🔄 Comprehensive Context Variables</h3>
            <p><strong>Definition:</strong> All factors that influence evaluation criteria, encompassing user profile AND environmental conditions.</p>
            <p><strong>Components:</strong></p>
            <ul>
                <li><strong>User Profile Variables:</strong> Trust level (0-100), personality traits, preferences, demographics</li>
                <li><strong>Environmental Variables:</strong> Stress level (0-100), time pressure (0-100), mood state (valence/arousal -1.0 to 1.0)</li>
                <li><strong>Interaction Variables:</strong> Session history, current goals, relationship phase</li>
            </ul>
            <p><strong>Key Principle:</strong> Context variables are the primary interface; user profiles serve as convenient presets that auto-populate variable ranges.</p>
        </div>

        <div class="concept-section">
            <h3>🔗 Context-Linked Assessment Framework</h3>
            <p><strong>Definition:</strong> Evaluation criteria that are ALWAYS linked with Generic Situations and Context Variable ranges.</p>
            <p><strong>Structure:</strong> <code>Generic Situation + Context Variable Ranges → Adapted Evaluation Criteria</code></p>
            <p><strong>Examples:</strong></p>
            <ul>
                <li>"Life Wheel Creation" + "Low Trust (0-39)" → Simple, clear, reassuring criteria</li>
                <li>"Goal Setting" + "High Stress (70-100)" → Concise, essential-only criteria</li>
            </ul>
            <p><strong>Key Principle:</strong> Evaluation criteria must always be contextually appropriate and consistently linked to the specific situation and context.</p>
        </div>

        <div class="concept-section">
            <h3>📊 Relationship Overview</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0;">
                <p><strong>Data Flow:</strong></p>
                <p>1. Select a <strong>Generic Situation</strong> (reusable template)</p>
                <p>2. Define <strong>Comprehensive Context Variables</strong> (or use presets)</p>
                <p>3. Apply <strong>Context-Linked Assessment Framework</strong> (adapted criteria)</p>
                <p>4. Execute benchmark with contextually appropriate evaluation</p>
            </div>
        </div>

        <div class="action-buttons">
            <button type="button" class="btn btn-secondary close-modal">Got it!</button>
        </div>
    </div>
</div>

{% endblock %}

{% block extrajs %}
{# JavaScript code has been moved to backend/static/admin/js/benchmark_management.js #}
{% endblock %}
