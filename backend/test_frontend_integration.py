#!/usr/bin/env python3
"""
Test script to verify that the frontend integration with Celery task works.
This simulates what happens when the frontend calls the template test endpoint.
"""

import os
import sys
import django
import asyncio
import json
from unittest.mock import patch, MagicMock

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from apps.admin_tools.views import BenchmarkRunView


async def test_frontend_integration():
    """Test that the frontend integration works with Celery tasks."""
    print("🧪 Testing Frontend Integration with Celery...")
    
    # Create mock request data (what the frontend sends)
    request_data = {
        'scenario_id': 1,
        'evaluation_template_data': {
            'name': 'Test Template',
            'description': 'Test template for frontend integration',
            'criteria': {
                'accuracy': ['Response is factually correct'],
                'helpfulness': ['Response addresses the user query']
            },
            'contextual_criteria': {
                'trust_level': {
                    '0-39': {'accuracy': ['Extra strict accuracy for low trust']},
                    '70-100': {'accuracy': ['Standard accuracy for high trust']}
                }
            }
        },
        'params': {
            'runs': 1,
            'semantic_evaluation': True,
            'context_variables': {
                'trust_level': 50,
                'mood': {'valence': 0.0, 'arousal': 0.0},
                'environment': {'stress_level': 30, 'time_pressure': 30}
            },
            'multi_range_contextual_evaluation': True,
            'selected_combinations': [
                {
                    'trust_level': 19.5,
                    'mood': {'valence': -0.75, 'arousal': 0.0},
                    'environment': {'stress_level': 30, 'time_pressure': 30},
                    'range_info': {'trust_level': '0-39'}
                },
                {
                    'trust_level': 85.0,
                    'mood': {'valence': 0.75, 'arousal': 0.0},
                    'environment': {'stress_level': 30, 'time_pressure': 30},
                    'range_info': {'trust_level': '70-100'}
                }
            ],
            'selected_combination_indices': [0, 2]
        }
    }
    
    try:
        # Mock the scenario and Celery task
        mock_scenario = MagicMock()
        mock_scenario.id = 1
        mock_scenario.name = "Test Scenario"
        mock_scenario.agent_role = "mentor"
        
        mock_task = MagicMock()
        mock_task.id = "test-task-12345"
        
        with patch('apps.admin_tools.views.get_object_or_404', return_value=mock_scenario), \
             patch('apps.main.tasks.benchmark_tasks.run_template_test.delay', return_value=mock_task):
            
            # Create view instance and call the template test handler
            view = BenchmarkRunView()
            response = await view._handle_template_test(request_data)
            
            # Check the response
            print(f"   📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                response_data = json.loads(response.content)
                print(f"   ✅ Template test endpoint working!")
                print(f"   📊 Response data: {response_data}")
                
                # Verify the response structure
                assert response_data['success'] is True, "Response should indicate success"
                assert 'task_id' in response_data, "Response should contain task_id"
                assert response_data['task_id'] == "test-task-12345", "Task ID should match"
                
                print(f"   ✅ Task ID returned: {response_data['task_id']}")
                print(f"   ✅ Message: {response_data['message']}")
                
                return True
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
                print(f"   📊 Response content: {response.content}")
                return False
                
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_task_status_endpoint():
    """Test the task status endpoint."""
    print("🧪 Testing Task Status Endpoint...")
    
    try:
        from apps.admin_tools.views import BenchmarkTaskStatusView
        from celery.result import AsyncResult
        
        # Mock a completed task
        mock_result = MagicMock()
        mock_result.ready.return_value = True
        mock_result.failed.return_value = False
        mock_result.successful.return_value = True
        mock_result.result = {
            'runs': [
                {
                    'id': 'test-run-1',
                    'success': True,
                    'semantic_score': 0.9,
                    'execution_time': 1.5,
                    'token_usage': '100',
                    'cost': 0.01
                }
            ]
        }
        
        with patch('celery.result.AsyncResult', return_value=mock_result):
            view = BenchmarkTaskStatusView()
            response = await view.get(None, "test-task-12345")
            
            print(f"   📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                response_data = json.loads(response.content)
                print(f"   ✅ Task status endpoint working!")
                print(f"   📊 Response data: {response_data}")
                
                # Verify the response structure
                assert response_data['status'] == 'completed', "Status should be completed"
                assert 'runs' in response_data, "Response should contain runs"
                assert len(response_data['runs']) == 1, "Should have one run"
                
                print(f"   ✅ Task status: {response_data['status']}")
                print(f"   ✅ Number of runs: {len(response_data['runs'])}")
                
                return True
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests."""
    print("🚀 Testing Frontend Integration with Celery")
    print("=" * 50)
    
    try:
        # Test 1: Template test endpoint
        test1_success = await test_frontend_integration()
        print()
        
        # Test 2: Task status endpoint
        test2_success = await test_task_status_endpoint()
        print()
        
        if test1_success and test2_success:
            print("🎉 ALL FRONTEND INTEGRATION TESTS PASSED!")
            print("✅ Template test now uses Celery tasks")
            print("✅ Task status endpoint works correctly")
            print("✅ Frontend can track task progress")
            print("\n📋 Next Steps:")
            print("1. Test in the actual web interface")
            print("2. Select 2 combinations and run a template test")
            print("3. Verify that only 2 combinations are tested (not all)")
        else:
            print("❌ SOME TESTS FAILED!")
            
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
