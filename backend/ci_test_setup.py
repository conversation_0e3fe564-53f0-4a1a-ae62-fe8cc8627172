#!/usr/bin/env python
"""
CI-specific test setup script.
This script is designed to run in GitHub Actions CI environment.
"""
import os
import sys
import logging
import subprocess
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
)
logger = logging.getLogger("ci_test_setup")

def check_dependencies():
    """Check if required dependencies are installed"""
    logger.info("Checking dependencies...")
    
    try:
        import django
        logger.info(f"Django {django.__version__} is available")
    except ImportError:
        logger.error("Django is not installed!")
        return False
    
    try:
        import psycopg2
        logger.info("psycopg2 is available")
    except ImportError:
        logger.error("psycopg2 is not installed!")
        return False
    
    try:
        import redis
        logger.info("redis is available")
    except ImportError:
        logger.error("redis is not installed!")
        return False
    
    return True

def wait_for_services():
    """Wait for database and Redis services to be ready"""
    logger.info("Waiting for services to be ready...")
    
    # Wait for PostgreSQL
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            result = subprocess.run(
                ['pg_isready', '-h', 'localhost', '-p', '5432', '-U', 'test_user'],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                logger.info("PostgreSQL is ready")
                break
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            logger.warning(f"pg_isready check failed: {e}")
        
        if attempt == max_attempts - 1:
            logger.error("PostgreSQL is not ready after 30 attempts")
            return False
        
        logger.info(f"Waiting for PostgreSQL... (attempt {attempt + 1}/{max_attempts})")
        time.sleep(1)
    
    # Wait for Redis
    for attempt in range(max_attempts):
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            logger.info("Redis is ready")
            break
        except Exception as e:
            if attempt == max_attempts - 1:
                logger.error(f"Redis is not ready after 30 attempts: {e}")
                return False
            logger.info(f"Waiting for Redis... (attempt {attempt + 1}/{max_attempts})")
            time.sleep(1)
    
    return True

def setup_environment():
    """Set up environment variables for CI"""
    logger.info("Setting up environment variables...")

    # Default environment variables (only set if not already set)
    env_defaults = {
        'DJANGO_SETTINGS_MODULE': 'config.settings.test',
        'TESTING': 'true',
        'CI': 'true',
        'PYTHONPATH': os.path.join(os.environ.get('GITHUB_WORKSPACE', '.'), 'backend'),
        'PYTHONDONTWRITEBYTECODE': '1',
        'PYTHONUNBUFFERED': '1',
        'DATABASE_URL': 'postgres://test_user:test_password@localhost:5432/test_goali',
        'CELERY_BROKER_URL': 'redis://localhost:6379/0',
        'DJANGO_ALLOW_ASYNC_UNSAFE': 'true',
        'DJANGO_SKIP_CHECKS': '1',
        'PYTEST_DJANGO_AUTODISCOVER': '0',
    }

    for key, default_value in env_defaults.items():
        if key not in os.environ:
            os.environ[key] = default_value
            logger.info(f"Set {key}={default_value}")
        else:
            logger.info(f"Using existing {key}={os.environ[key]}")

def test_database_connection():
    """Test database connection"""
    logger.info("Testing database connection...")
    
    try:
        import psycopg2
        conn = psycopg2.connect(
            dbname='test_goali',
            user='test_user',
            password='test_password',
            host='localhost',
            port='5432'
        )
        conn.close()
        logger.info("Database connection successful")
        return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False

def initialize_django():
    """Initialize Django"""
    logger.info("Initializing Django...")
    
    try:
        import django
        django.setup()
        logger.info("Django setup successful")
        return True
    except Exception as e:
        logger.error(f"Django setup failed: {e}")
        return False

def run_migrations():
    """Run Django migrations"""
    logger.info("Running Django migrations...")

    try:
        from django.core.management import call_command
        call_command('migrate', '--noinput', verbosity=1)
        logger.info("Migrations completed successfully")
        return True
    except Exception as e:
        logger.error(f"Migrations failed: {e}")
        return False

def seed_test_data():
    """Seed minimal test data for CI"""
    logger.info("Seeding test data...")

    try:
        from django.core.management import call_command

        # Run basic seeders
        seeders = [
            'seed_db_10_hexacos',
            'seed_db_20_limitations',
            'seed_db_30_domains',
            'seed_db_40_envs',
        ]

        for seeder in seeders:
            try:
                call_command(seeder, verbosity=0)
                logger.info(f"Successfully ran {seeder}")
            except Exception as e:
                logger.warning(f"Failed to run {seeder}: {e}")

        logger.info("Test data seeding completed")
        return True
    except Exception as e:
        logger.error(f"Test data seeding failed: {e}")
        return False

def main():
    """Main CI setup function"""
    logger.info("=== Starting CI Test Setup ===")

    # Set up environment first (only set defaults for missing vars)
    setup_environment()

    # Check dependencies (but don't fail if missing - CI should have installed them)
    logger.info("Checking dependencies...")
    try:
        import django
        logger.info(f"Django {django.__version__} is available")
    except ImportError:
        logger.warning("Django not found - this may be expected if running before pip install")
        # Don't fail here - let the CI workflow handle dependency installation

    # Skip service waiting - CI workflow should handle this
    logger.info("Skipping service wait - CI workflow handles this")

    # Initialize Django (this is the main purpose of this script)
    if not initialize_django():
        logger.error("Django initialization failed")
        return False

    # Run migrations
    if not run_migrations():
        logger.error("Migrations failed")
        return False

    # Seed test data
    if not seed_test_data():
        logger.error("Test data seeding failed")
        return False

    logger.info("=== CI Test Setup Complete ===")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
