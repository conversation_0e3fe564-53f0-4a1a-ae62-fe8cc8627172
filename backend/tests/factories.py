import factory
import factory.fuzzy
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone

from apps.main.models import (
    BenchmarkScenario, GenericAgent, BenchmarkTag, LLMConfig, BenchmarkRun,
    HistoryEvent, AgentMemory, AgentGoal, EvaluationCriteriaTemplate
)
from apps.user.models import UserProfile, CurrentMood # Assuming UserProfile and CurrentMood are in apps.user
from apps.activity.models import ActivityTailored

User = get_user_model()

class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User
        django_get_or_create = ('username',) # Prevent creating duplicate users with the same username

    username = factory.Sequence(lambda n: f'user_{n}')
    password = factory.PostGenerationMethodCall('set_password', 'password')
    is_staff = False
    is_superuser = False

class StaffUserFactory(UserFactory):
    is_staff = True

class SuperuserFactory(StaffUserFactory):
    is_superuser = True

class UserProfileFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = UserProfile
        django_get_or_create = ('user',)

    user = factory.SubFactory(UserFactory)
    profile_name = factory.Sequence(lambda n: f'Profile {n}')  # Add required profile_name field

class LLMConfigFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = LLMConfig
        django_get_or_create = ('name',)

    name = factory.Sequence(lambda n: f'llm_config_{n}')
    model_name = factory.fuzzy.FuzzyChoice(['gpt-4o', 'mistral-small-latest', 'claude-3-sonnet-20240229'])
    temperature = factory.fuzzy.FuzzyDecimal(0.1, 1.0)
    input_token_price = factory.fuzzy.FuzzyDecimal(0.0001, 0.001)
    output_token_price = factory.fuzzy.FuzzyDecimal(0.0001, 0.001)
    is_default = False
    is_evaluation = False

class GenericAgentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = GenericAgent
        django_get_or_create = ('role',)

    role = factory.Sequence(lambda n: f'agent_role_{n}')
    description = factory.LazyAttribute(lambda o: f'Description for {o.role}')
    system_instructions = factory.LazyAttribute(lambda o: f'System instructions for {o.role}')
    llm_config = factory.SubFactory(LLMConfigFactory)
    langgraph_node_class = factory.LazyAttribute(lambda o: f'apps.main.agents.{o.role}_agent.{o.role.capitalize()}Agent')
    input_schema = {}
    output_schema = {}
    state_schema = {}
    memory_schema = {}
    read_models = []
    write_models = []
    recommend_models = []
    version = '1.0.0'
    is_active = True
    processing_timeout = 30

class BenchmarkTagFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = BenchmarkTag
        django_get_or_create = ('name',)

    name = factory.Sequence(lambda n: f'tag_{n}')
    description = factory.LazyAttribute(lambda o: f'Description for {o.name}')

class BenchmarkScenarioFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = BenchmarkScenario
        django_get_or_create = ('name',)

    name = factory.Sequence(lambda n: f'scenario_{n}')
    description = factory.LazyAttribute(lambda o: f'Description for {o.name}')
    agent_role = 'mentor'
    input_data = factory.Dict({})
    metadata = factory.Dict({})
    version = 1
    is_active = True
    is_latest = True

    @factory.PostGeneration
    def tags(self, create, extracted, **kwargs):
        if not create:
            # Simple build, do nothing.
            return

        if extracted:
            # A list of tags were passed in, add them to the scenario
            for tag in extracted:
                self.tags.add(tag)

class BenchmarkRunFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = BenchmarkRun

    scenario = factory.SubFactory(BenchmarkScenarioFactory)
    agent_definition = factory.SubFactory(GenericAgentFactory)
    agent_version = "1.0.0"
    llm_config = factory.SubFactory(LLMConfigFactory)
    execution_date = factory.LazyFunction(timezone.now)
    success_rate = factory.fuzzy.FuzzyFloat(0.0, 1.0)
    semantic_score = factory.fuzzy.FuzzyFloat(0.0, 10.0)
    # Fixed field names to match actual BenchmarkRun model
    mean_duration = factory.fuzzy.FuzzyFloat(100.0, 5000.0)
    median_duration = factory.LazyAttribute(lambda o: o.mean_duration * 0.9)
    min_duration = factory.LazyAttribute(lambda o: o.mean_duration * 0.5)
    max_duration = factory.LazyAttribute(lambda o: o.mean_duration * 1.5)
    std_dev = factory.LazyAttribute(lambda o: o.mean_duration * 0.1)
    total_input_tokens = factory.fuzzy.FuzzyInteger(10, 1000)
    total_output_tokens = factory.fuzzy.FuzzyInteger(10, 1000)
    estimated_cost = factory.LazyAttribute(lambda o: (o.total_input_tokens * o.llm_config.input_token_price) + (o.total_output_tokens * o.llm_config.output_token_price))
    raw_results = factory.Dict({})
    runs_count = 1
    tool_calls = factory.fuzzy.FuzzyInteger(0, 10)
    tool_breakdown = factory.Dict({})
    llm_calls = factory.fuzzy.FuzzyInteger(0, 20)
    memory_operations = factory.fuzzy.FuzzyInteger(0, 5)
    parameters = factory.Dict({})
    semantic_evaluation_details = factory.Dict({})
    semantic_evaluations = factory.Dict({})
    stage_performance_details = factory.Dict({})

# Add factories for other models as needed
class HistoryEventFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = HistoryEvent
    user_profile = factory.SubFactory(UserProfileFactory)
    event_type = factory.fuzzy.FuzzyChoice(['message', 'tool_call', 'workflow_initiated', 'user_message', 'assistant_message', 'chat_message']) # Added chat/user/assistant message types
    timestamp = factory.LazyFunction(timezone.now)
    details = factory.Dict({})
    # content_type and object_id are often set dynamically, might not need factory defaults
    # content_type = factory.LazyAttribute(lambda o: ContentType.objects.get_for_model(UserProfile)) # Example, adjust as needed
    # object_id = factory.SelfAttribute('user_profile.id') # Example, adjust as needed


class CurrentMoodFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = CurrentMood
    user_profile = factory.SubFactory(UserProfileFactory)
    description = factory.Sequence(lambda n: f'Mood description {n}')
    height = factory.fuzzy.FuzzyInteger(0, 100)
    user_awareness = factory.fuzzy.FuzzyInteger(0, 100)
    inferred_from_text = factory.fuzzy.FuzzyChoice([True, False])
    processed_at = factory.LazyFunction(timezone.now)  # Add required processed_at field
    effective_start = factory.LazyFunction(timezone.now)
    effective_end = factory.LazyFunction(timezone.now)
    duration_estimate = "1 day"  # Add default duration_estimate

# class AgentMemoryFactory(factory.django.DjangoModelFactory):
#     class Meta:
#         model = AgentMemory
#     user_profile = factory.SubFactory(UserProfileFactory)
#     agent_definition = factory.SubFactory(GenericAgentFactory)
#     memory_key = factory.Sequence(lambda n: f'memory_key_{n}')
#     value = factory.Dict({})
#     timestamp = factory.LazyFunction(timezone.now)

# class AgentGoalFactory(factory.django.DjangoModelFactory):
#     class Meta:
#         model = AgentGoal
#     user_profile = factory.SubFactory(UserProfileFactory)
#     description = factory.Sequence(lambda n: f'Goal description {n}')
#     is_completed = False
#     created_at = factory.LazyFunction(timezone.now)
#     completed_at = None

# class EvaluationCriteriaTemplateFactory(factory.django.DjangoModelFactory):
#     class Meta:
#         model = EvaluationCriteriaTemplate
#         django_get_or_create = ('name',)
#     name = factory.Sequence(lambda n: f'eval_template_{n}')
#     description = factory.LazyAttribute(lambda o: f'Description for {o.name}')
#     criteria = factory.Dict({})

# class ActivityTailoredFactory(factory.django.DjangoModelFactory):
#     class Meta:
#         model = ActivityTailored
#     user_profile = factory.SubFactory(UserProfileFactory)
#     original_activity = factory.Sequence(lambda n: f'Original Activity {n}')
#     tailored_content = factory.Dict({})
#     created_at = factory.LazyFunction(timezone.now)
#     # Add other ActivityTailored fields as needed
