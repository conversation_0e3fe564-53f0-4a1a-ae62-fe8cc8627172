#!/usr/bin/env python3
"""
Export an entire codebase to a structured JSON file for LLM processing.
This script preserves file paths, content, and maintains the hierarchical structure.
"""

import os
import sys
import json
import argparse
from datetime import datetime
from pathlib import Path
import hashlib

def calculate_file_hash(content):
    """Calculate a SHA-256 hash of file content."""
    return hashlib.sha256(content.encode('utf-8')).hexdigest()

def create_structured_export(root_dir, output_file, extensions=None, exclude_dirs=None, exclude_patterns=None):
    """
    Export all files from root_dir with given extensions into a structured JSON file.
    
    Args:
        root_dir (str): Root directory to start the search
        output_file (str): Output JSON file path
        extensions (list): File extensions to include (e.g., ['.py'])
        exclude_dirs (list): Directory names to exclude (e.g., ['__pycache__', '.git'])
        exclude_patterns (list): File/directory patterns to exclude (e.g., ['*.pyc'])
    """
    if extensions is None:
        extensions = ['.py']
    
    if exclude_dirs is None:
        exclude_dirs = ['__pycache__', '.git', 'venv', '.venv', 'node_modules', '.pytest_cache']
    
    if exclude_patterns is None:
        exclude_patterns = ['*.pyc', '*.pyo', '*.pyd', '*.so', '*.dll', '*.class']
    
    # Convert extensions to lowercase for case-insensitive matching
    extensions = [ext.lower() for ext in extensions]
    
    # Statistics
    stats = {
        'total_files': 0,
        'included_files': 0,
        'skipped_files': 0,
        'errors': 0,
        'total_lines': 0,
        'total_size_bytes': 0
    }
    
    # Results list
    results = []
    
    # Get absolute path of root_dir
    root_dir_abs = os.path.abspath(root_dir)
    
    print(f"Starting export from {root_dir_abs}...")
    
    for dirpath, dirnames, filenames in os.walk(root_dir_abs):
        # Skip excluded directories
        dirnames[:] = [d for d in dirnames if d not in exclude_dirs]
        
        # Calculate relative path from root_dir
        rel_dirpath = os.path.relpath(dirpath, root_dir_abs)
        
        for filename in filenames:
            stats['total_files'] += 1
            
            # Check if file should be excluded based on patterns
            skip = False
            for pattern in exclude_patterns:
                if pattern.startswith('*'):
                    if filename.endswith(pattern[1:]):
                        skip = True
                        break
            
            if skip:
                stats['skipped_files'] += 1
                continue
            
            # Check extension
            _, ext = os.path.splitext(filename)
            if extensions and ext.lower() not in extensions:
                stats['skipped_files'] += 1
                continue
            
            # Get relative path
            if rel_dirpath == '.':
                rel_path = filename
            else:
                rel_path = os.path.join(rel_dirpath, filename)
            
            # Normalize path separators to forward slashes for consistency
            rel_path = rel_path.replace('\\', '/')
            
            file_path = os.path.join(dirpath, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Calculate file stats
                file_size = os.path.getsize(file_path)
                line_count = content.count('\n') + 1
                
                # Update stats
                stats['included_files'] += 1
                stats['total_lines'] += line_count
                stats['total_size_bytes'] += file_size
                
                # Add to results
                results.append({
                    'path': rel_path,
                    'content': content,
                    'size_bytes': file_size,
                    'line_count': line_count,
                    'hash': calculate_file_hash(content)
                })
                
                # Progress indicator every 10 files
                if stats['included_files'] % 10 == 0:
                    print(f"Processed {stats['included_files']} files...", end='\r')
                
            except Exception as e:
                stats['errors'] += 1
                print(f"Error processing {rel_path}: {str(e)}")
                results.append({
                    'path': rel_path,
                    'error': str(e)
                })
    
    # Sort by path for better organization
    results.sort(key=lambda x: x['path'])
    
    # Create the final structure
    output = {
        'metadata': {
            'exported_at': datetime.now().isoformat(),
            'root_directory': root_dir_abs,
            'file_count': stats['included_files'],
            'total_lines': stats['total_lines'],
            'total_size_bytes': stats['total_size_bytes'],
            'extensions_included': extensions,
            'stats': stats
        },
        'files': results
    }
    
    # Write to JSON file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output, f, indent=2)
    
    print(f"\nExport complete!")
    print(f"Included {stats['included_files']} files ({stats['total_lines']} lines, {stats['total_size_bytes'] / 1024:.2f} KB)")
    print(f"Skipped {stats['skipped_files']} files")
    print(f"Encountered {stats['errors']} errors")
    print(f"Output written to {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Export a codebase to a structured JSON file.')
    parser.add_argument('root_dir', help='Root directory of the codebase')
    parser.add_argument('--output', '-o', default='codebase_export.json', 
                        help='Output file path (default: codebase_export.json)')
    parser.add_argument('--extensions', '-e', nargs='+', default=['.py'],
                        help='File extensions to include (default: .py)')
    parser.add_argument('--exclude-dirs', '-d', nargs='+', 
                        default=['__pycache__', '.git', 'venv', '.venv', 'node_modules', '.pytest_cache'],
                        help='Directories to exclude')
    parser.add_argument('--exclude-patterns', '-p', nargs='+',
                        default=['*.pyc', '*.pyo', '*.pyd'],
                        help='File patterns to exclude')
    
    args = parser.parse_args()
    
    create_structured_export(
        args.root_dir, 
        args.output, 
        extensions=args.extensions,
        exclude_dirs=args.exclude_dirs,
        exclude_patterns=args.exclude_patterns
    )

if __name__ == '__main__':
    main()