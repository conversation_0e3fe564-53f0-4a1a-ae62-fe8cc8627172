#!/bin/bash
set -e

# Navigate to the backend directory if not already there
if [[ ! "$PWD" == */backend ]]; then
  cd backend
fi

# Run migrations in proper dependency order to ensure tables are created properly
echo "Applying migrations in dependency order..."
python manage.py makemigrations activity
python manage.py migrate activity
python manage.py makemigrations user
python manage.py migrate user
python manage.py makemigrations main
python manage.py migrate main
python manage.py makemigrations
python manage.py migrate

# Register tools (based on your entrypoint.sh)
echo "Registering tools..."
python manage.py cmd_register_tools --module apps.main.agents.tools.tools || echo "Warning: Basic tool registration failed, continuing"
python manage.py cmd_register_tools --module apps.main.agents.tools.extra_tools || echo "Warning: Extra tool registration failed, continuing"

# Seed minimal test data
echo "Seeding minimal test data..."
python manage.py seed_db_10_hexacos || echo "Warning: Seeding hexacos failed, continuing"
python manage.py seed_db_20_limitations || echo "Warning: Seeding limitations failed, continuing"
python manage.py seed_db_30_domains || echo "Warning: Seeding domains failed, continuing"
python manage.py seed_db_40_envs || echo "Warning: Seeding environments failed, continuing"
python manage.py seed_db_50_skill_system || echo "Warning: Seeding skill system failed, continuing"
python manage.py seed_db_60_beliefs || echo "Warning: Seeding beliefs failed, continuing"
python manage.py seed_db_70_activities || echo "Warning: Seeding activities failed, continuing"
python manage.py seed_db_80_agents || echo "Warning: Seeding agents failed, continuing"
python manage.py cmd_tool_connect --reset || echo "Warning: Tool connect failed, continuing"

# Create template directories for coverage dashboard if they don't exist
echo "Setting up coverage dashboard directories..."
mkdir -p templates/coverage_dashboard

# Create an empty coverage.json if it doesn't exist 
# (will be updated by the tests)
if [ ! -f "coverage.json" ]; then
  echo '{"agents":{},"modules":{},"test_types":{"unit":0,"integration":0,"workflow":0,"unknown":0}}' > coverage.json
  
  # Add timestamp to the coverage data
  python -c "
import json
import datetime
try:
    with open('coverage.json', 'r') as f:
        data = json.load(f)
    data['last_updated'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    with open('coverage.json', 'w') as f:
        json.dump(data, f, indent=2)
except Exception as e:
    print(f'Error updating coverage.json: {e}')
  "
fi

# Create static directory for coverage badge if it doesn't exist
mkdir -p static/coverage_dashboard

# Generate a placeholder coverage badge
python -c "
svg_content = '''<svg xmlns='http://www.w3.org/2000/svg' width='108' height='20'>
  <linearGradient id='a' x2='0' y2='100%'>
    <stop offset='0' stop-color='#bbb' stop-opacity='.1'/>
    <stop offset='1' stop-opacity='.1'/>
  </linearGradient>
  <rect rx='3' width='108' height='20' fill='#555'/>
  <rect rx='3' x='58' width='50' height='20' fill='#999'/>
  <path fill='#999' d='M58 0h4v20h-4z'/>
  <rect rx='3' width='108' height='20' fill='url(#a)'/>
  <g fill='#fff' text-anchor='middle' font-family='DejaVu Sans,Verdana,Geneva,sans-serif' font-size='11'>
    <text x='29' y='15' fill='#010101' fill-opacity='.3'>coverage</text>
    <text x='29' y='14'>coverage</text>
    <text x='83' y='15' fill='#010101' fill-opacity='.3'>pending</text>
    <text x='83' y='14'>pending</text>
  </g>
</svg>'''

with open('static/coverage_dashboard/badge.svg', 'w') as f:
    f.write(svg_content)
"

echo "GitHub setup completed successfully!"