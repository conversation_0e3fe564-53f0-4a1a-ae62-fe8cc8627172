from django.db import models
import uuid

class CoverageReport(models.Model):
    """
    Stores historical test coverage data for a specific test run.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    run_number = models.IntegerField(
        help_text="Identifier for the test run (e.g., from CI/CD pipeline).",
        db_index=True,
        null=True, # Allow null if run number isn't available
        blank=True
    )
    timestamp = models.DateTimeField(
        auto_now_add=True,
        help_text="Timestamp when the coverage report was generated and saved."
    )
    coverage_data = models.JSONField(
        help_text="The raw coverage data, typically from coverage.json."
    )
    total_coverage = models.FloatField(
        help_text="Overall line coverage percentage for this run.",
        null=True, # Calculated from coverage_data, might fail
        blank=True
    )
    branch_coverage = models.FloatField(
        help_text="Overall branch coverage percentage for this run.",
        null=True, # Branch coverage might not always be enabled/available
        blank=True
    )
    commit_hash = models.Char<PERSON>ield(
        max_length=40,
        help_text="Git commit hash associated with this test run.",
        null=True,
        blank=True,
        db_index=True
    )

    class Meta:
        ordering = ['-timestamp'] # Show newest reports first
        verbose_name = "Coverage Report"
        verbose_name_plural = "Coverage Reports"

    def __str__(self):
        return f"Coverage Report - Run {self.run_number or 'N/A'} - {self.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
