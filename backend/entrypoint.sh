#!/bin/bash
set -e

# Check if running in test environment
if [ "$TESTING" != "true" ]; then
  echo "Running standard environment setup..."

  # Ensure migrations for all apps are created/updated
  echo "Generating/Updating migration files for ALL apps..."
  python manage.py makemigrations --noinput
  # Apply activity app migrations first (dependency for user)
  echo "Applying activity app database migrations..."
  python manage.py migrate activity --noinput
  # Apply user app migrations next (dependency for main)
  echo "Applying user app database migrations..."
  python manage.py migrate user --noinput
  # Run remaining migrations to apply schema changes
  echo "Applying remaining database migrations..."
  python manage.py migrate --noinput

    # Note: All tables are now created using migrations

  # Fix static files issues and collect static files (only in non-debug/production environments)
  # In debug mode, static files are served directly from STATICFILES_DIRS via middleware
  if [ "${DJANGO_DEBUG}" != "1" ]; then
    echo "Fixing static files issues and collecting static files..."
    python manage.py fix_static_files
  else
    echo "Skipping fix_static_files in debug mode for live static file updates."
  fi

  # Run idempotent seeding commands
  echo "Running idempotent seeding commands..."
  python manage.py run_seeders

  # Register tools idempotently (should run every time to catch updates)
  echo "Registering/Synchronizing tools..."
  python manage.py cmd_register_tools

  # Reset tool connections (should run every time)
  echo "Resetting tool connections..."
  python manage.py cmd_tool_connect --reset

  # Create/Update benchmark scenarios (should run every time to catch updates)
  echo "Creating/Updating benchmark scenarios..."
  python manage.py create_benchmark_scenarios_v3 --file testing/benchmark_data/scenarios/mentor_discussion_25.json
  python manage.py create_benchmark_scenarios_v3 --file testing/benchmark_data/scenarios/mentor_discussion_65.json
  python manage.py create_benchmark_scenarios_v3 --file testing/benchmark_data/scenarios/mentor_discussion_85.json
  python manage.py create_benchmark_scenarios_v3 --file testing/benchmark_data/scenarios/mentor_postactfb_40.json
  python manage.py create_benchmark_scenarios_v3 --file testing/benchmark_data/scenarios/mentor_postactfb_65.json
  python manage.py create_benchmark_scenarios_v3 --file testing/benchmark_data/scenarios/mentor_wheelgen_35.json
  python manage.py create_benchmark_scenarios_v3 --file testing/benchmark_data/scenarios/mentor_wheelgen_65.json
  python manage.py create_benchmark_scenarios_v3 --file testing/benchmark_data/scenarios/mentor_wheelgen_85.json
  python manage.py create_benchmark_scenarios_v3 --file testing/benchmark_data/scenarios/mentor_crisis_support.json
  python manage.py create_benchmark_scenarios_v3 --file testing/benchmark_data/scenarios/mentor_peak_performance.json

  echo "Running schema seed command..."
  python manage.py seed_benchmark_schemas --templates-dir=evaluation_templates

  # Load contextual evaluation templates from JSON files
  echo "Loading contextual evaluation templates..."
  python manage.py load_contextual_templates --templates-dir=testing/benchmark_data/contextual_templates --force

  # Other commands that might need to run every time (if any)
  # Example: python manage.py some_other_command
else
  echo "Skipping standard setup for TESTING environment."
fi

# Start the application or run tests (this part runs regardless of TESTING flag)
if [ "$TESTING" = "true" ]; then
  echo "Running tests with pytest..."
  exec pytest "$@"
elif [ "${WAIT_FOR_DEBUGGER}" = "true" ]; then
    echo "Starting Django with debugger (waiting mode)..."
    exec python debug_django.py --wait-for-debugger runserver 0.0.0.0:8000
else
    echo "Starting Django with debugger (non-waiting mode)..."
    exec uvicorn config.asgi:application --host 0.0.0.0 --port 8000 --workers 1 --reload
fi
