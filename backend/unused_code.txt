apps\activity\admin.py:26: unused variable 'model_admin' (100% confidence)
apps\activity\admin.py:58: unused variable 'model_admin' (100% confidence)
apps\admin_tools\consumers.py:35: unused variable 'bytes_data' (100% confidence)
apps\admin_tools\consumers.py:80: unused variable 'bytes_data' (100% confidence)
apps\main\admin.py:69: unused variable 'using_transactions' (100% confidence)
apps\main\admin.py:759: unused variable 'model_admin' (100% confidence)
apps\main\admin.py:973: unused import 'default_admin' (90% confidence)
apps\main\agents\benchmarking.py:440: unused variable 'configure_agent' (100% confidence)
apps\main\agents\tools\extra_tools.py:659: unused import 'ExtractHour' (90% confidence)
apps\main\agents\tools\extra_tools.py:659: unused import 'ExtractWeekDay' (90% confidence)
apps\main\agents\tools\extra_tools.py:659: unused import 'TruncDate' (90% confidence)
apps\main\agents\tools\get_user_profile_tool.py:8: unused import 'PersonalResource' (90% confidence)
apps\main\agents\tools\get_user_profile_tool.py:8: unused import 'UserDemographics' (90% confidence)
apps\main\agents\tools\get_user_profile_tool.py:9: unused import 'Domain' (90% confidence)
apps\main\agents\tools\get_user_profile_tool.py:9: unused import 'EnvironmentRequirement' (90% confidence)
apps\main\agents\tools\get_user_profile_tool.py:9: unused import 'ResourceRequirement' (90% confidence)
apps\main\agents\tools\get_user_profile_tool.py:9: unused import 'UserRequirement' (90% confidence)
apps\main\consumers.py:998: redundant if-condition (100% confidence)
apps\main\services\benchmark_validation.py:8: unused import 'glob' (90% confidence)
apps\main\services\conversation_dispatcher.py:170: unreachable code after 'return' (100% confidence)
apps\main\services\statistical_analysis.py:24: unused import 'StdDev' (90% confidence)
apps\main\tasks\wheel_generation_tasks.py:106: unreachable code after 'return' (100% confidence)
apps\main\testing\definition_extractors.py:415: unused variable 'prog_name' (100% confidence)
apps\main\testing\definition_extractors.py:415: unused variable 'subcommand' (100% confidence)
apps\main\utils\db_connection_utility.py:6: unused import 'cast' (90% confidence)
apps\main\utils\db_connection_utility.py:10: unused import 'QuerySet' (90% confidence)
apps\user\admin.py:6: unused import 'FilteredSelectMultiple' (90% confidence)
apps\user\admin.py:530: unused import 'Sum' (90% confidence)
apps\user\admin.py:662: unused variable 'model_admin' (100% confidence)
apps\user\services\skill_service.py:2: unused import 'Case' (90% confidence)
apps\user\services\skill_service.py:2: unused import 'Sum' (90% confidence)
apps\user\services\skill_service.py:2: unused import 'Value' (90% confidence)
apps\user\services\skill_service.py:2: unused import 'When' (90% confidence)
apps\user\services\skill_service.py:332: unused variable 'usage_context' (100% confidence)
apps\user\signals.py:1: unused import 'post_delete' (90% confidence)
apps\utils\activity_compatibility.py:2: unused import 'Case' (90% confidence)
apps\utils\activity_compatibility.py:2: unused import 'Value' (90% confidence)
apps\utils\activity_compatibility.py:2: unused import 'When' (90% confidence)
apps\utils\activity_skill_integration.py:1: unused import 'Sum' (90% confidence)
config\middleware.py:2: unused import 'DevelopmentStaticFilesMiddleware' (90% confidence)
config\middleware_static.py:7: unused import 'utils' (90% confidence)
config\middleware_static.py:8: unused import 'ImproperlyConfigured' (90% confidence)
config\middleware_static.py:9: unused import 'HttpResponseNotModified' (90% confidence)
conftest.py:10: unused import 'cast' (90% confidence)
conftest.py:56: unused variable 'django_db_setup' (100% confidence)
conftest.py:300: unused variable 'signum' (100% confidence)
conftest.py:641: unused variable 'signum' (100% confidence)
scripts\run_benchmarks.py:38: unused import 'test_agent_runs_with_varying_complexity' (90% confidence)
scripts\run_benchmarks.py:38: unused import 'test_individual_agent_performance' (90% confidence)
scripts\run_benchmarks.py:43: unused import 'test_workflow_compare_scenarios' (90% confidence)
scripts\run_benchmarks.py:43: unused import 'test_workflow_standard_benchmark' (90% confidence)
scripts\run_benchmarks.py:49: unused import 'BenchmarkMetrics' (90% confidence)
testing\error_reporter_plugin.py:67: unused variable 'exitstatus' (100% confidence)
