#!/usr/bin/env python
"""
Test script to verify Grafana views were created successfully.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings.dev')
django.setup()

from django.db import connection

def test_grafana_views():
    """Test that all Grafana views were created successfully."""
    print("🔍 Testing Grafana Analytics Views")
    print("=" * 50)
    
    with connection.cursor() as cursor:
        # Check if views exist
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.views 
            WHERE table_schema = 'public' 
            AND table_name LIKE 'grafana_%' 
            ORDER BY table_name
        """)
        views = cursor.fetchall()
        
        expected_views = [
            'grafana_cost_analytics',
            'grafana_contextual_evaluation',
            'grafana_llm_performance',
            'grafana_prompt_analytics'
        ]
        
        print(f"📊 Found {len(views)} Grafana views:")
        for view in views:
            print(f"  ✅ {view[0]}")
        
        print()
        
        # Check if all expected views exist
        found_views = [view[0] for view in views]
        missing_views = [v for v in expected_views if v not in found_views]
        
        if missing_views:
            print(f"❌ Missing views: {missing_views}")
            return False
        else:
            print("✅ All expected views found!")
        
        # Test each view can be queried
        print("\n🧪 Testing view queries...")
        for view_name in found_views:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {view_name}")
                count = cursor.fetchone()[0]
                print(f"  ✅ {view_name}: {count} rows")
            except Exception as e:
                print(f"  ❌ {view_name}: Error - {e}")
                return False
        
        print("\n🎉 All Grafana views are working correctly!")
        return True

if __name__ == "__main__":
    success = test_grafana_views()
    sys.exit(0 if success else 1)
