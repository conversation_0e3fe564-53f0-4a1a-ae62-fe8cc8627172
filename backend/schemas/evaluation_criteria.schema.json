{"_comment": "ACTIVE_FILE - 29-05-2025", "$schema": "http://json-schema.org/draft-07/schema#", "title": "EvaluationCriteriaSchema", "description": "Schema for defining evaluation criteria in benchmark scenarios", "$version": "2.0.0", "type": "object", "oneOf": [{"properties": {"criteria": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "weight": {"type": "number", "minimum": 0, "maximum": 1}}, "required": ["name", "weight"]}}}, "required": ["criteria"]}, {"properties": {"evaluation_criteria_by_phase": {"type": "object", "properties": {"foundation": {"type": "object", "description": "Criteria for foundation phase (trust level 0-39)", "properties": {"criteria": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "weight": {"type": "number", "minimum": 0, "maximum": 1}}, "required": ["name", "weight"]}}}, "required": ["criteria"]}, "expansion": {"type": "object", "description": "Criteria for expansion phase (trust level 40-69)", "properties": {"criteria": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "weight": {"type": "number", "minimum": 0, "maximum": 1}}, "required": ["name", "weight"]}}}, "required": ["criteria"]}, "integration": {"type": "object", "description": "Criteria for integration phase (trust level 70-100)", "properties": {"criteria": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "weight": {"type": "number", "minimum": 0, "maximum": 1}}, "required": ["name", "weight"]}}}, "required": ["criteria"]}}, "required": ["foundation", "expansion", "integration"]}}, "required": ["evaluation_criteria_by_phase"]}]}