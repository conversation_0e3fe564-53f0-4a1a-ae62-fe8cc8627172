{"$schema": "http://json-schema.org/draft-07/schema#", "title": "SituationSchema", "description": "Schema for defining benchmark situations and workflows with enhanced context", "version": "2.0.0", "type": "object", "required": ["workflow_type", "text"], "properties": {"workflow_type": {"type": "string", "enum": ["wheel_generation", "activity_feedback", "discussion", "onboarding", "goal_setting", "test_workflow"], "description": "The type of workflow this situation should trigger"}, "text": {"type": "string", "description": "The user input text that initiates this situation"}, "user_id": {"type": "string", "description": "ID of the user in this situation"}, "trust_level": {"type": "number", "minimum": 0, "maximum": 100, "description": "Trust level in this specific situation"}, "activity_id": {"type": "string", "description": "ID of the activity being referenced (for activity_feedback)"}, "feedback_rating": {"type": "number", "minimum": 1, "maximum": 5, "description": "Optional user rating of activity (for activity_feedback)"}, "time_availability": {"type": "number", "description": "User's available time in minutes (for wheel_generation)"}, "environment": {"type": "object", "description": "Environmental context for this situation", "properties": {"location": {"type": "string"}, "noise_level": {"type": "string", "enum": ["low", "medium", "high"]}, "available_resources": {"type": "array", "items": {"type": "string"}}, "social_context": {"type": "string", "enum": ["alone", "family", "friends", "public", "work"]}}}, "conversation_stage": {"type": "string", "enum": ["initial_conversation", "clarify_or_suggest", "process_feedback", "goal_definition", "activity_recommendation"], "description": "The current stage in the conversation flow"}, "psychological_state": {"type": "object", "properties": {"mood": {"type": "string"}, "energy_level": {"type": "string", "enum": ["low", "medium", "high"]}, "stress_level": {"type": "string", "enum": ["low", "medium", "high"]}, "motivation": {"type": "string", "enum": ["low", "medium", "high"]}}}, "expected_next_step": {"type": "string", "description": "Expected action from the agent (for testing workflow transitions)"}, "user_state": {"type": "object", "description": "Enhanced user state information", "properties": {"trust_level": {"type": "number", "minimum": 0, "maximum": 100, "description": "Numeric trust level (0-100)"}, "mood": {"type": "string", "description": "Current emotional state of the user"}, "environment": {"type": "string", "description": "Current physical environment of the user"}}}, "device_capabilities": {"type": "object", "description": "Information about the user's device capabilities", "properties": {"screen_size": {"type": "string", "description": "Size of the user's screen (e.g., 'small', 'medium', 'large')"}, "input_method": {"type": "string", "enum": ["touch", "keyboard", "voice", "mixed"], "description": "Primary input method used by the user"}, "accessibility": {"type": "object", "description": "Accessibility settings and requirements", "properties": {"vision_impaired": {"type": "boolean"}, "hearing_impaired": {"type": "boolean"}, "motor_impaired": {"type": "boolean"}, "preferred_modality": {"type": "string", "enum": ["visual", "auditory", "text"], "description": "Preferred information modality"}}}}}, "time_context": {"type": "object", "description": "Temporal context information", "properties": {"available_time": {"type": "number", "description": "Amount of time available in minutes"}, "time_of_day": {"type": "string", "enum": ["morning", "afternoon", "evening", "night"], "description": "Current time of day"}, "time_zone": {"type": "string", "description": "User's time zone (e.g., 'UTC', 'America/New_York')"}}}, "user_preferences": {"type": "object", "description": "User preferences for interaction and learning", "properties": {"learning_style": {"type": "string", "enum": ["visual", "auditory", "reading", "kinesthetic"], "description": "User's preferred learning style"}, "communication_preferences": {"type": "object", "description": "Communication preferences", "properties": {"verbosity": {"type": "string", "enum": ["concise", "moderate", "detailed"], "description": "Preferred level of detail in communications"}, "formality": {"type": "string", "enum": ["casual", "neutral", "formal"], "description": "Preferred tone of communication"}, "feedback_frequency": {"type": "string", "enum": ["minimal", "moderate", "frequent"], "description": "Preferred frequency of feedback"}}}}}}}