{"_comment": "ACTIVE_FILE - 29-05-2025", "$schema": "http://json-schema.org/draft-07/schema#", "title": "WorkflowBenchmarkSchema", "description": "Schema for defining workflow benchmark scenarios", "type": "object", "required": ["workflow_type"], "properties": {"workflow_type": {"type": "string", "description": "Type of workflow to benchmark", "enum": ["wheel_generation", "activity_feedback", "discussion", "onboarding", "goal_setting", "test_workflow"]}, "mock_tool_responses": {"type": "object", "description": "Mock responses for tools used in this benchmark", "additionalProperties": {"oneOf": [{"type": "object", "properties": {"response": {"type": "string", "description": "Response template (use {variable} placeholders)"}, "assertions": {"type": "array", "description": "Assertions to validate tool calls", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["param_check", "call_count", "sequence"], "description": "Type of assertion"}, "param": {"type": "string", "description": "Parameter name to check (for param_check)"}, "should_exist": {"type": "boolean", "description": "Whether the parameter should exist (for param_check)"}, "expected": {"type": ["string", "number", "boolean", "object", "array", "null"], "description": "Expected value (for param_check or call_count)"}, "sequence": {"type": "array", "description": "Expected sequence of tool calls (for sequence)", "items": {"type": "string"}}}, "required": ["type"]}}, "delay": {"type": "number", "description": "Delay in seconds before returning response", "minimum": 0}}, "required": ["response"]}, {"type": "array", "description": "Conditional responses based on tool input or call count", "items": {"type": "object", "properties": {"condition": {"type": "string", "description": "Python expression condition using tool_input and call_count variables"}, "response": {"type": "string", "description": "Response template (use {variable} placeholders)"}, "assertions": {"type": "array", "description": "Assertions to validate tool calls", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["param_check", "call_count", "sequence"], "description": "Type of assertion"}, "param": {"type": "string", "description": "Parameter name to check (for param_check)"}, "should_exist": {"type": "boolean", "description": "Whether the parameter should exist (for param_check)"}, "expected": {"type": ["string", "number", "boolean", "object", "array", "null"], "description": "Expected value (for param_check or call_count)"}, "sequence": {"type": "array", "description": "Expected sequence of tool calls (for sequence)", "items": {"type": "string"}}}, "required": ["type"]}}, "delay": {"type": "number", "description": "Delay in seconds before returning response", "minimum": 0}}, "required": ["condition", "response"]}}]}}, "mock_tool_validation": {"type": "object", "description": "Configuration for tool validation", "properties": {"validate_schemas": {"type": "boolean", "description": "Whether to validate tool inputs and outputs against schemas", "default": true}, "validate_params": {"type": "boolean", "description": "Whether to validate tool parameters", "default": true}, "strict_mode": {"type": "boolean", "description": "Whether to fail on validation errors", "default": false}}}, "warmup_runs": {"type": "integer", "description": "Number of warmup runs to perform before benchmarking", "minimum": 0, "default": 1}, "benchmark_runs": {"type": "integer", "description": "Number of benchmark runs to perform", "minimum": 1, "default": 3}, "expected_quality_criteria": {"type": "object", "description": "Criteria for evaluating the quality of the workflow output", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "evaluator_models": {"type": "array", "description": "LLM models to use for semantic evaluation", "items": {"type": "string"}}, "expected_output": {"type": "object", "description": "Expected output from the workflow for validation", "additionalProperties": true}, "expected_stages": {"type": "array", "description": "Expected stages in the workflow execution", "items": {"type": "string"}}, "expected_tool_calls": {"type": "object", "description": "Expected tool calls during workflow execution", "additionalProperties": {"type": "integer", "minimum": 0}}, "timeout_seconds": {"type": "number", "description": "Maximum time in seconds for the workflow to complete", "minimum": 0}}}