{"$schema": "http://json-schema.org/draft-07/schema#", "title": "UserProfileSchema", "description": "Schema for defining user profiles in benchmark scenarios", "type": "object", "required": ["trust_phase"], "properties": {"profile_name": {"type": "string", "description": "Human-readable name for this profile"}, "trust_phase": {"type": "string", "enum": ["Foundation", "Expansion", "Integration"], "description": "Current trust phase of the user"}, "trust_level": {"type": "number", "minimum": 0, "maximum": 100, "description": "Numeric trust level (0-100)"}, "communication_preferences": {"type": "object", "properties": {"tone": {"type": "string", "enum": ["warm", "analytical", "casual", "formal", "encouraging", "direct"], "description": "Preferred communication tone"}, "detail_level": {"type": "string", "enum": ["low", "medium", "high"], "description": "Preferred level of detail in responses"}}}, "personality_traits": {"type": "object", "description": "HEXACO personality model traits (0.0-1.0)", "properties": {"honesty_humility": {"type": "number", "minimum": 0, "maximum": 1}, "emotionality": {"type": "number", "minimum": 0, "maximum": 1}, "extraversion": {"type": "number", "minimum": 0, "maximum": 1}, "agreeableness": {"type": "number", "minimum": 0, "maximum": 1}, "conscientiousness": {"type": "number", "minimum": 0, "maximum": 1}, "openness": {"type": "number", "minimum": 0, "maximum": 1}, "neuroticism": {"type": "number", "minimum": 0, "maximum": 1}}}, "activity_history": {"type": "object", "properties": {"completed_count": {"type": "integer", "minimum": 0}, "abandonment_rate": {"type": "number", "minimum": 0, "maximum": 1}, "average_rating": {"type": "number", "minimum": 0, "maximum": 5}, "domain_distribution": {"type": "object", "additionalProperties": {"type": "integer", "minimum": 0}}, "challenge_progression": {"type": "array", "items": {"type": "object", "properties": {"period": {"type": "string"}, "avg_challenge": {"type": "number", "minimum": 0, "maximum": 100}}, "required": ["period", "avg_challenge"]}}}}, "skill_assessments": {"type": "array", "items": {"type": "object", "properties": {"skill": {"type": "string"}, "start_level": {"type": "number", "minimum": 0}, "current_level": {"type": "number", "minimum": 0}}, "required": ["skill"]}}, "user_history": {"type": "object", "description": "Extended history for Integration phase users", "properties": {"total_activities": {"type": "integer", "minimum": 0}, "recent_activities": {"type": "integer", "minimum": 0}, "completion_rate": {"type": "number", "minimum": 0, "maximum": 1}, "average_feedback": {"type": "number", "minimum": 0, "maximum": 5}}}}}