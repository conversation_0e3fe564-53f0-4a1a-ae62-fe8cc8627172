{"$schema": "http://json-schema.org/draft-07/schema#", "title": "ToolExpectationSchema", "description": "Schema for defining expected tool usage patterns in benchmark scenarios", "type": "object", "properties": {"required_tools": {"type": "array", "description": "Tools that must be called during the benchmark", "items": {"type": "string", "description": "Tool code (e.g., 'get_user_profile')"}, "minItems": 0}, "restricted_tools": {"type": "array", "description": "Tools that should not be called during this benchmark", "items": {"type": "string"}, "minItems": 0}, "tool_expectations": {"type": "object", "description": "Detailed expectations for specific tools", "additionalProperties": {"type": "object", "properties": {"min_calls": {"type": "integer", "minimum": 0, "description": "Minimum number of times this tool should be called"}, "max_calls": {"type": "integer", "minimum": 0, "description": "Maximum number of times this tool should be called"}, "required_params": {"type": "object", "description": "Parameters that must be included in the tool call", "additionalProperties": {"type": ["string", "number", "boolean", "object", "array", "null"]}}, "conditional_params": {"type": "array", "description": "Rules for parameters that must appear together or exclusively", "items": {"type": "object", "properties": {"condition": {"type": "string", "description": "Condition expression (e.g., 'if param1 then param2')"}, "error_message": {"type": "string", "description": "Error message if condition is not met"}}, "required": ["condition"]}}}}}, "sequence_rules": {"type": "array", "description": "Rules for the sequence of tool calls", "items": {"type": "object", "properties": {"tool_sequence": {"type": "array", "description": "Expected sequence of tool calls (in order)", "items": {"type": "string"}, "minItems": 2}, "exact_match": {"type": "boolean", "description": "Whether the sequence must match exactly (true) or just maintain relative order (false)", "default": false}, "allow_other_tools_between": {"type": "boolean", "description": "Whether other tools can be called between these tools", "default": true}}, "required": ["tool_sequence"]}}, "mock_responses": {"type": "object", "description": "Mock responses for tools used in this benchmark", "additionalProperties": {"oneOf": [{"type": "string", "description": "Simple response template (use {variable} placeholders)"}, {"type": "array", "description": "Conditional responses based on tool input or call count", "items": {"type": "object", "properties": {"condition": {"type": "string", "description": "Python expression condition using tool_input and call_count variables"}, "response": {"type": "string", "description": "Response template (use {variable} placeholders)"}}, "required": ["condition", "response"]}}]}}, "error_simulations": {"type": "array", "description": "Configure simulated errors to test agent error handling", "items": {"type": "object", "properties": {"tool_code": {"type": "string", "description": "Tool that should return an error"}, "trigger_condition": {"type": "string", "description": "When to trigger the error (e.g., 'call_count == 2')"}, "error_message": {"type": "string", "description": "Error message to return"}, "error_type": {"type": "string", "enum": ["validation", "not_found", "permission", "timeout", "server"], "description": "Type of error to simulate"}}, "required": ["tool_code", "error_message"]}}}}