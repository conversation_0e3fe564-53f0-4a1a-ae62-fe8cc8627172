{"$schema": "http://json-schema.org/draft-07/schema#", "title": "SituationSchema", "description": "Schema for defining benchmark situations and workflows", "type": "object", "required": ["workflow_type", "text"], "properties": {"workflow_type": {"type": "string", "enum": ["wheel_generation", "activity_feedback", "discussion", "onboarding", "goal_setting", "test_workflow"], "description": "The type of workflow this situation should trigger"}, "text": {"type": "string", "description": "The user input text that initiates this situation"}, "user_id": {"type": "string", "description": "ID of the user in this situation"}, "trust_level": {"type": "number", "minimum": 0, "maximum": 100, "description": "Trust level in this specific situation"}, "activity_id": {"type": "string", "description": "ID of the activity being referenced (for activity_feedback)"}, "feedback_rating": {"type": "number", "minimum": 1, "maximum": 5, "description": "Optional user rating of activity (for activity_feedback)"}, "time_availability": {"type": "number", "description": "User's available time in minutes (for wheel_generation)"}, "environment": {"type": "object", "description": "Environmental context for this situation", "properties": {"location": {"type": "string"}, "noise_level": {"type": "string", "enum": ["low", "medium", "high"]}, "available_resources": {"type": "array", "items": {"type": "string"}}, "social_context": {"type": "string", "enum": ["alone", "family", "friends", "public", "work"]}}}, "conversation_stage": {"type": "string", "enum": ["initial_conversation", "clarify_or_suggest", "process_feedback", "goal_definition", "activity_recommendation"], "description": "The current stage in the conversation flow"}, "psychological_state": {"type": "object", "properties": {"mood": {"type": "string"}, "energy_level": {"type": "string", "enum": ["low", "medium", "high"]}, "stress_level": {"type": "string", "enum": ["low", "medium", "high"]}, "motivation": {"type": "string", "enum": ["low", "medium", "high"]}}}, "expected_next_step": {"type": "string", "description": "Expected action from the agent (for testing workflow transitions)"}}}