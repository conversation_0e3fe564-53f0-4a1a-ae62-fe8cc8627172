#!/usr/bin/env python
"""
Quick test script to verify the benchmark runs API fixes.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from apps.main.models import BenchmarkRun
from apps.admin_tools.views import BenchmarkRunView
from django.test import RequestFactory
from django.contrib.auth.models import User
import json
import asyncio

def test_api_response():
    """Test the benchmark runs API response format."""
    print("Testing benchmark runs API...")
    
    # Check if we have any benchmark runs
    runs_count = BenchmarkRun.objects.count()
    print(f"Total benchmark runs in database: {runs_count}")
    
    if runs_count == 0:
        print("No benchmark runs found. Cannot test API response.")
        return
    
    # Get a sample run
    sample_run = BenchmarkRun.objects.first()
    print(f"Sample run ID: {sample_run.id}")
    print(f"Sample run token usage display: {sample_run.token_usage_display}")
    print(f"Sample run estimated cost: {sample_run.estimated_cost}")
    
    # Create a mock request
    factory = RequestFactory()
    request = factory.get('/admin/benchmarks/api/run/')
    
    # Create a mock user (staff required)
    user = User.objects.filter(is_staff=True).first()
    if not user:
        print("No staff user found. Creating one...")
        user = User.objects.create_user(
            username='teststaff',
            password='testpass',
            is_staff=True,
            is_superuser=True
        )
    
    request.user = user
    
    # Call the API view
    try:
        view = BenchmarkRunView()
        response = asyncio.run(view.get(request))
        print(f"API response status: {response.status_code}")
        
        if response.status_code == 200:
            data = json.loads(response.content)
            print(f"Response keys: {list(data.keys())}")
            
            if 'runs' in data and data['runs']:
                first_run = data['runs'][0]
                print(f"First run keys: {list(first_run.keys())}")
                
                # Check our specific fixes
                print("\n=== CHECKING FIXES ===")
                print(f"Token usage: {first_run.get('token_usage', 'MISSING')}")
                print(f"Cost: {first_run.get('cost', 'MISSING')}")
                print(f"Trust level: {first_run.get('trust_level', 'MISSING')}")
                print(f"Valence: {first_run.get('valence', 'MISSING')}")
                print(f"Arousal: {first_run.get('arousal', 'MISSING')}")
                print(f"Stress level: {first_run.get('stress_level', 'MISSING')}")
                print(f"Time pressure: {first_run.get('time_pressure', 'MISSING')}")
                
                # Check if we have the raw data
                print(f"Total input tokens: {first_run.get('total_input_tokens', 'MISSING')}")
                print(f"Total output tokens: {first_run.get('total_output_tokens', 'MISSING')}")
                print(f"Estimated cost (raw): {first_run.get('estimated_cost', 'MISSING')}")
                
                print("\n=== SUCCESS ===")
                print("API is returning the expected fields!")
            else:
                print("No runs in API response")
        else:
            print(f"API returned error: {response.content}")
            
    except Exception as e:
        print(f"Error calling API: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_api_response()
