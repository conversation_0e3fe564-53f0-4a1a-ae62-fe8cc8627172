#!/usr/bin/env python
"""
Django Debugpy Connection Diagnostic Script

This script performs comprehensive diagnostics on the VS Code debugging setup for Django,
especially when running in Docker containers. It checks for common issues that might
cause the "connect ECONNREFUSED 127.0.0.1:5678" error.

Usage:
    python debug_diagnostic.py [--docker-compose-file PATH] [--debug-port PORT]

Options:
    --docker-compose-file    Path to the docker-compose.yml file (default: ./backend/docker-compose.yml)
    --debug-port             Debugpy port to check (default: 5678)
"""

import os
import sys
import socket
import subprocess
import json
import argparse
import platform
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any


class bcolors:
    """Terminal colors for output formatting."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


def print_header(message: str) -> None:
    """Print a formatted header message."""
    print(f"\n{bcolors.HEADER}{bcolors.BOLD}{'=' * 80}{bcolors.ENDC}")
    print(f"{bcolors.HEADER}{bcolors.BOLD} {message} {bcolors.ENDC}")
    print(f"{bcolors.HEADER}{bcolors.BOLD}{'=' * 80}{bcolors.ENDC}\n")


def print_success(message: str) -> None:
    """Print a success message."""
    print(f"{bcolors.OKGREEN}✓ {message}{bcolors.ENDC}")


def print_error(message: str) -> None:
    """Print an error message."""
    print(f"{bcolors.FAIL}✗ {message}{bcolors.ENDC}")


def print_warning(message: str) -> None:
    """Print a warning message."""
    print(f"{bcolors.WARNING}! {message}{bcolors.ENDC}")


def print_info(message: str) -> None:
    """Print an info message."""
    print(f"{bcolors.OKBLUE}i {message}{bcolors.ENDC}")


def run_command(command: List[str]) -> Tuple[int, str, str]:
    """
    Run a shell command and return the exit code, stdout, and stderr.
    
    Args:
        command: The command to run as a list of strings
        
    Returns:
        Tuple of (exit_code, stdout, stderr)
    """
    try:
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        stdout, stderr = process.communicate()
        return process.returncode, stdout, stderr
    except Exception as e:
        return 1, "", str(e)


def check_docker_running() -> bool:
    """
    Check if Docker is running on the system.
    
    Returns:
        True if Docker is running, False otherwise
    """
    print_header("Checking Docker Status")
    
    if platform.system() == "Windows":
        # Windows-specific check
        cmd = ["docker", "info"]
    else:
        # Linux/Mac check
        cmd = ["docker", "info"]
    
    exit_code, stdout, stderr = run_command(cmd)
    
    if exit_code == 0:
        print_success("Docker is running")
        return True
    else:
        print_error(f"Docker is not running or not accessible: {stderr}")
        print_info("Make sure Docker Desktop (Windows/Mac) or Docker service (Linux) is running")
        return False


def check_docker_compose_file(docker_compose_file: str) -> bool:
    """
    Check if the docker-compose.yml file exists and is valid.
    
    Args:
        docker_compose_file: Path to the docker-compose.yml file
        
    Returns:
        True if the file exists and is valid, False otherwise
    """
    print_header(f"Checking Docker Compose File: {docker_compose_file}")
    
    # Check if file exists
    if not os.path.isfile(docker_compose_file):
        print_error(f"Docker Compose file not found at: {docker_compose_file}")
        return False
    
    # Check if file is valid by running docker-compose config
    cmd = ["docker-compose", "-f", docker_compose_file, "config", "--quiet"]
    exit_code, _, stderr = run_command(cmd)
    
    if exit_code == 0:
        print_success("Docker Compose file is valid")
        return True
    else:
        print_error(f"Docker Compose file is invalid: {stderr}")
        return False


def check_env_debug_file(base_path: str) -> bool:
    """
    Check if the .env.dev file exists and contains the WAIT_FOR_DEBUGGER variable.
    
    Args:
        base_path: The base path to look for the .env.dev file
        
    Returns:
        True if the file exists and is valid, False otherwise
    """
    print_header("Checking .env.dev File")
    
    env_debug_path = os.path.join(base_path, ".env.dev")
    
    # Check if file exists
    if not os.path.isfile(env_debug_path):
        print_error(f".env.dev file not found at: {env_debug_path}")
        print_info("Create this file with WAIT_FOR_DEBUGGER=true")
        return False
    
    # Read file and check for WAIT_FOR_DEBUGGER
    with open(env_debug_path, "r") as f:
        content = f.read()
    
    if "WAIT_FOR_DEBUGGER" in content:
        print_success(".env.dev file contains WAIT_FOR_DEBUGGER variable")
        
        if "WAIT_FOR_DEBUGGER=true" in content:
            print_info("WAIT_FOR_DEBUGGER is set to true - Django will wait for debugger to attach")
        else:
            print_warning("WAIT_FOR_DEBUGGER is not set to true - Django will not wait for debugger to attach")
            print_info("Set WAIT_FOR_DEBUGGER=true if you want Django to wait for the debugger")
        
        return True
    else:
        print_error(".env.dev file does not contain WAIT_FOR_DEBUGGER variable")
        print_info("Add WAIT_FOR_DEBUGGER=true to .env.dev")
        return False


def check_launch_json() -> bool:
    """
    Check if launch.json exists and contains the necessary debug configurations.
    
    Returns:
        True if the file exists and is valid, False otherwise
    """
    print_header("Checking VS Code launch.json Configuration")
    
    vscode_dir = os.path.join(os.getcwd(), ".vscode")
    launch_json_path = os.path.join(vscode_dir, "launch.json")
    
    # Check if .vscode directory exists
    if not os.path.isdir(vscode_dir):
        print_error(".vscode directory not found")
        return False
    
    # Check if launch.json exists
    if not os.path.isfile(launch_json_path):
        print_error("launch.json file not found")
        return False
    
    # Read and parse launch.json
    try:
        with open(launch_json_path, "r") as f:
            launch_config = json.load(f)
        
        if "configurations" not in launch_config:
            print_error("No configurations found in launch.json")
            return False
        
        django_configs = [
            config for config in launch_config["configurations"] 
            if config.get("name", "").startswith("Django") and config.get("type") == "python"
        ]
        
        if not django_configs:
            print_error("No Django Python configurations found in launch.json")
            return False
        
        # Check for attach configurations
        attach_configs = [
            config for config in django_configs 
            if config.get("request") == "attach" and "connect" in config
        ]
        
        if not attach_configs:
            print_error("No attach configurations found for Django in launch.json")
            return False
        
        # Check port settings in all attach configurations
        valid_port_configs = []
        for config in attach_configs:
            connect = config.get("connect", {})
            host = connect.get("host", "")
            port = connect.get("port", 0)
            
            if host and port:
                valid_port_configs.append((config["name"], host, port))
        
        if not valid_port_configs:
            print_error("No valid host:port configurations found in Django attach configurations")
            return False
        
        print_success(f"Found {len(valid_port_configs)} valid Django debug configurations")
        for name, host, port in valid_port_configs:
            print_info(f"Configuration: {name} ({host}:{port})")
        
        return True
            
    except json.JSONDecodeError:
        print_error("launch.json is not valid JSON")
        return False
    except Exception as e:
        print_error(f"Error reading launch.json: {str(e)}")
        return False


def check_django_container_status(docker_compose_file: str) -> Tuple[bool, Optional[str]]:
    """
    Check if the Django container is running.
    
    Args:
        docker_compose_file: Path to the docker-compose.yml file
        
    Returns:
        Tuple of (is_running, container_id)
    """
    print_header("Checking Django Container Status")
    
    # Get the container name for the web service
    cmd = ["docker-compose", "-f", docker_compose_file, "ps", "--services"]
    exit_code, stdout, stderr = run_command(cmd)
    
    if exit_code != 0:
        print_error(f"Failed to get service names: {stderr}")
        return False, None
    
    services = stdout.strip().split('\n')
    web_service = next((s for s in services if s in ["web", "django", "app"]), None)
    
    if not web_service:
        print_error("No web/django service found in docker-compose.yml")
        return False, None
    
    print_info(f"Found web service: {web_service}")
    
    # Check if the container is running
    cmd = ["docker-compose", "-f", docker_compose_file, "ps", web_service]
    exit_code, stdout, stderr = run_command(cmd)
    
    if exit_code != 0:
        print_error(f"Failed to check container status: {stderr}")
        return False, None
    
    # Parse the output to find the container ID and status
    lines = stdout.strip().split('\n')
    if len(lines) < 2:
        print_error("No running container found for the web service")
        return False, None
    
    # Get container name from docker-compose ps output
    words = lines[-1].split()
    container_name = None
    for word in words:
        if web_service in word:
            container_name = word
            break
    
    if not container_name:
        print_error("Could not determine container name")
        return False, None
    
    # Get container ID and status
    cmd = ["docker", "inspect", "--format", "{{.State.Status}}", container_name]
    exit_code, stdout, stderr = run_command(cmd)
    
    if exit_code != 0:
        print_error(f"Failed to inspect container: {stderr}")
        return False, None
    
    status = stdout.strip()
    
    if status == "running":
        print_success(f"Container {container_name} is running")
        return True, container_name
    else:
        print_error(f"Container {container_name} is not running (status: {status})")
        return False, None


def check_debugpy_in_container(container_id: str) -> bool:
    """
    Check if debugpy is installed and running in the container.
    
    Args:
        container_id: The ID or name of the container
        
    Returns:
        True if debugpy is running, False otherwise
    """
    print_header("Checking Debugpy in Container")
    
    # Check if debugpy is installed
    cmd = ["docker", "exec", container_id, "pip", "list"]
    exit_code, stdout, stderr = run_command(cmd)
    
    if exit_code != 0:
        print_error(f"Failed to check installed packages: {stderr}")
        return False
    
    if "debugpy" not in stdout:
        print_error("debugpy is not installed in the container")
        print_info("Add debugpy to your requirements.txt or install it in your Dockerfile")
        return False
    
    print_success("debugpy is installed in the container")
    
    # Check if debugpy process is running
    cmd = ["docker", "exec", container_id, "ps", "aux"]
    exit_code, stdout, stderr = run_command(cmd)
    
    if exit_code != 0:
        print_error(f"Failed to check running processes: {stderr}")
        return False
    
    debugpy_processes = [line for line in stdout.split('\n') if 'debugpy' in line]
    
    if not debugpy_processes:
        print_error("No debugpy processes found running in the container")
        if "debug_django.py" in stdout:
            print_info("debug_django.py is running but might not have initialized debugpy correctly")
        return False
    
    print_success("debugpy process is running in the container")
    for process in debugpy_processes:
        print_info(f"  Process: {process.strip()}")
    
    return True


def check_port_mapping(docker_compose_file: str, debug_port: int) -> bool:
    """
    Check if the debug port is correctly mapped in docker-compose.yml.
    
    Args:
        docker_compose_file: Path to the docker-compose.yml file
        debug_port: The debug port to check
        
    Returns:
        True if the port is correctly mapped, False otherwise
    """
    print_header(f"Checking Port Mapping for Port {debug_port}")
    
    # Check port mapping in docker-compose.yml
    try:
        import yaml
        with open(docker_compose_file, 'r') as f:
            try:
                compose_config = yaml.safe_load(f)
            except yaml.YAMLError:
                print_error("Could not parse docker-compose.yml as valid YAML")
                return False
        
        web_service = None
        for service_name, service_config in compose_config.get('services', {}).items():
            if service_name in ["web", "django", "app"]:
                web_service = service_name
                break
        
        if not web_service:
            print_error("No web/django service found in docker-compose.yml")
            return False
        
        ports = compose_config['services'][web_service].get('ports', [])
        
        # Convert ports to standardized format
        parsed_ports = []
        for port in ports:
            if isinstance(port, str):
                parts = port.split(':')
                if len(parts) == 2:
                    parsed_ports.append((int(parts[0]), int(parts[1])))
            elif isinstance(port, dict):
                parsed_ports.append((int(port.get('published')), int(port.get('target'))))
        
        debug_port_mappings = [p for p in parsed_ports if p[0] == debug_port or p[1] == debug_port]
        
        if not debug_port_mappings:
            print_error(f"Debug port {debug_port} is not mapped in docker-compose.yml")
            print_info(f"Add '{debug_port}:{debug_port}' to the ports section of the web service")
            return False
        
        for host_port, container_port in debug_port_mappings:
            print_success(f"Debug port mapping found: {host_port}:{container_port}")
        
        return True
        
    except Exception as e:
        print_error(f"Error checking port mapping: {str(e)}")
        return False


def check_port_availability(debug_port: int) -> bool:
    """
    Check if the debug port is available on localhost.
    
    Args:
        debug_port: The debug port to check
        
    Returns:
        True if the port is in use (which means the server is likely running),
        False if the port is not in use
    """
    print_header(f"Checking Port {debug_port} Availability")
    
    # Try to connect to the port
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(1)
    result = sock.connect_ex(('127.0.0.1', debug_port))
    sock.close()
    
    if result == 0:
        print_success(f"Port {debug_port} is in use (debugpy should be listening)")
        return True
    else:
        print_error(f"Port {debug_port} is not in use (debugpy is not listening)")
        print_info("This could be why you're getting the ECONNREFUSED error")
        return False


def check_network_status(container_id: str, debug_port: int) -> bool:
    """
    Check network status inside the container.
    
    Args:
        container_id: The ID or name of the container
        debug_port: The debug port to check
        
    Returns:
        True if the network is configured correctly, False otherwise
    """
    print_header("Checking Network Status")
    
    # Check if the container is actually listening on the debug port
    cmd = ["docker", "exec", container_id, "netstat", "-tulnp"]
    exit_code, stdout, stderr = run_command(cmd)
    
    if exit_code != 0:
        # Try with 'ss' command if netstat is not available
        cmd = ["docker", "exec", container_id, "ss", "-tulnp"]
        exit_code, stdout, stderr = run_command(cmd)
        
        if exit_code != 0:
            print_error("Could not check listening ports inside container")
            print_info("The container may not have netstat or ss installed")
            return False
    
    listening_on_port = False
    for line in stdout.split('\n'):
        if f":{debug_port}" in line and ("LISTEN" in line or "tcp" in line):
            listening_on_port = True
            print_success(f"Container is listening on port {debug_port}")
            print_info(f"Details: {line.strip()}")
    
    if not listening_on_port:
        print_error(f"Container is not listening on port {debug_port}")
        print_info("Make sure debugpy is configured to listen on 0.0.0.0 (not 127.0.0.1)")
        return False
    
    # Check if the port is accessible from outside the container
    cmd = ["docker", "exec", container_id, "curl", "-s", "-o", "/dev/null", "-w", "%{http_code}", f"http://localhost:{debug_port}"]
    exit_code, stdout, stderr = run_command(cmd)
    
    # debugpy doesn't respond properly to HTTP requests, so a connection error or any response is fine
    print_info(f"Attempted to connect to debugpy port: {stdout} (connection errors are expected)")
    
    return True


def check_entrypoint_script(base_path: str) -> bool:
    """
    Check if the entrypoint.sh script has appropriate permissions and settings.
    
    Args:
        base_path: The base path where entrypoint.sh should be located
        
    Returns:
        True if the script is properly configured, False otherwise
    """
    print_header("Checking Entrypoint Script")
    
    entrypoint_path = os.path.join(base_path, "entrypoint.sh")
    
    # Check if file exists
    if not os.path.isfile(entrypoint_path):
        print_error(f"Entrypoint script not found at: {entrypoint_path}")
        return False
    
    # Read file and check content
    with open(entrypoint_path, "r") as f:
        content = f.read()
    
    # Check for debug configuration
    if "debug_django.py" in content:
        print_success("Entrypoint script references debug_django.py")
    else:
        print_error("Entrypoint script does not reference debug_django.py")
        print_info("Make sure the entrypoint is configured to run debug_django.py")
        return False
    
    # Check if script checks WAIT_FOR_DEBUGGER
    if "WAIT_FOR_DEBUGGER" in content:
        print_success("Entrypoint script checks WAIT_FOR_DEBUGGER environment variable")
    else:
        print_warning("Entrypoint script does not check WAIT_FOR_DEBUGGER environment variable")
        print_info("Add a check for WAIT_FOR_DEBUGGER in your entrypoint script")
    
    # Check file permissions
    if platform.system() != "Windows":  # Skip on Windows
        cmd = ["ls", "-l", entrypoint_path]
        exit_code, stdout, stderr = run_command(cmd)
        
        if exit_code == 0:
            if "x" in stdout:
                print_success("Entrypoint script has execute permissions")
            else:
                print_error("Entrypoint script lacks execute permissions")
                print_info("Run 'chmod +x entrypoint.sh' to make it executable")
        else:
            print_warning(f"Could not check file permissions: {stderr}")
    
    return True


def check_debug_django_script(base_path: str) -> bool:
    """
    Check if debug_django.py script exists and is properly configured.
    
    Args:
        base_path: The base path where debug_django.py should be located
        
    Returns:
        True if the script is properly configured, False otherwise
    """
    print_header("Checking debug_django.py Script")
    
    debug_django_path = os.path.join(base_path, "debug_django.py")
    
    # Check if file exists
    if not os.path.isfile(debug_django_path):
        print_error(f"debug_django.py script not found at: {debug_django_path}")
        return False
    
    # Read file and check content
    with open(debug_django_path, "r") as f:
        content = f.read()
    
    # Check for debugpy import
    if "import debugpy" in content:
        print_success("debug_django.py imports debugpy")
    else:
        print_error("debug_django.py does not import debugpy")
        print_info("Add 'import debugpy' to debug_django.py")
        return False
    
    # Check for debugpy.listen call
    if "debugpy.listen" in content:
        print_success("debug_django.py calls debugpy.listen")
        
        # Check if listening on all interfaces
        if "0.0.0.0" in content and "debugpy.listen" in content:
            print_success("debug_django.py configures debugpy to listen on all interfaces (0.0.0.0)")
        else:
            print_warning("debug_django.py might not be configured to listen on all interfaces")
            print_info("Ensure debugpy.listen is called with ('0.0.0.0', PORT)")
    else:
        print_error("debug_django.py does not call debugpy.listen")
        print_info("Add 'debugpy.listen(('0.0.0.0', DEBUG_PORT))' to debug_django.py")
        return False
    
    # Check for wait_for_client handling
    if "debugpy.wait_for_client" in content:
        print_success("debug_django.py calls debugpy.wait_for_client when needed")
    else:
        print_warning("debug_django.py does not call debugpy.wait_for_client")
        print_info("Add 'debugpy.wait_for_client()' for when WAIT_FOR_DEBUGGER is true")
    
    return True


def check_task_json() -> bool:
    """
    Check if tasks.json exists and contains the necessary task configurations.
    
    Returns:
        True if the file exists and is valid, False otherwise
    """
    print_header("Checking VS Code tasks.json Configuration")
    
    vscode_dir = os.path.join(os.getcwd(), ".vscode")
    tasks_json_path = os.path.join(vscode_dir, "tasks.json")
    
    # Check if .vscode directory exists
    if not os.path.isdir(vscode_dir):
        print_error(".vscode directory not found")
        return False
    
    # Check if tasks.json exists
    if not os.path.isfile(tasks_json_path):
        print_error("tasks.json file not found")
        return False
    
    # Read and parse tasks.json
    try:
        with open(tasks_json_path, "r") as f:
            tasks_config = json.load(f)
        
        if "tasks" not in tasks_config:
            print_error("No tasks found in tasks.json")
            return False
        
        # Look for django debug tasks
        debug_tasks = [
            task for task in tasks_config["tasks"] 
            if "start-django" in task.get("label", "") or "django" in task.get("label", "").lower()
        ]
        
        if not debug_tasks:
            print_error("No Django debug tasks found in tasks.json")
            return False
        
        print_success(f"Found {len(debug_tasks)} Django debug tasks")
        for task in debug_tasks:
            print_info(f"Task: {task.get('label')}")
        
        return True
            
    except json.JSONDecodeError:
        print_error("tasks.json is not valid JSON")
        return False
    except Exception as e:
        print_error(f"Error reading tasks.json: {str(e)}")
        return False


def run_diagnostics(docker_compose_file: str, debug_port: int) -> None:
    """
    Run all diagnostics.
    
    Args:
        docker_compose_file: Path to the docker-compose.yml file
        debug_port: The debug port to check
    """
    base_path = os.path.dirname(docker_compose_file)
    
    # System checks
    docker_running = check_docker_running()
    if not docker_running:
        print_error("Docker must be running for further diagnostics")
        return
    
    # Configuration file checks
    check_docker_compose_file(docker_compose_file)
    check_env_debug_file(base_path)
    check_launch_json()
    check_task_json()
    check_entrypoint_script(base_path)
    check_debug_django_script(base_path)
    
    # Container and port checks
    is_running, container_id = check_django_container_status(docker_compose_file)
    if is_running and container_id:
        check_debugpy_in_container(container_id)
        check_port_mapping(docker_compose_file, debug_port)
        check_port_availability(debug_port)
        check_network_status(container_id, debug_port)
    else:
        print_error("Container must be running for further diagnostics")
    
    # Print summary
    print_header("Diagnostic Summary")
    print_info("If you're still having issues connecting the debugger:")
    print_info("1. Ensure debugpy is correctly installed and configured")
    print_info("2. Verify Docker container is running and has port mapping")
    print_info("3. Check that debug_django.py is correctly setting up debugpy")
    print_info("4. Try restarting the VS Code window")
    print_info("5. Try creating a new launch configuration")
    print_info("6. Confirm VS Code is using the right Docker context")
    print_info("7. Check if any firewalls are blocking the connection")


def main() -> None:
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Diagnose issues with Django debugpy connection")
    parser.add_argument("--docker-compose-file", type=str, 
                        default="./backend/docker-compose.yml",
                        help="Path to the docker-compose.yml file")
    parser.add_argument("--debug-port", type=int, default=5678,
                        help="Debugpy port to check")
    
    args = parser.parse_args()
    
    print(f"""
{bcolors.BOLD}Django Debugpy Connection Diagnostic v1.0{bcolors.ENDC}
This script will diagnose issues with connecting VS Code debugger to Django in Docker
    """)
    
    run_diagnostics(args.docker_compose_file, args.debug_port)


if __name__ == "__main__":
    main()