#!/usr/bin/env python
"""
Test script to verify CI setup works correctly.
This can be run locally to test the CI environment setup.
"""
import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
)
logger = logging.getLogger("test_ci_setup")

def test_ci_setup():
    """Test the CI setup script"""
    logger.info("Testing CI setup script...")
    
    # Set CI environment
    os.environ['CI'] = 'true'
    os.environ['TESTING'] = 'true'
    
    try:
        # Run the CI setup script
        result = subprocess.run([
            sys.executable, 'ci_test_setup.py'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info("CI setup script completed successfully")
            logger.info("STDOUT:")
            logger.info(result.stdout)
            return True
        else:
            logger.error("CI setup script failed")
            logger.error("STDOUT:")
            logger.error(result.stdout)
            logger.error("STDERR:")
            logger.error(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("CI setup script timed out")
        return False
    except Exception as e:
        logger.error(f"Error running CI setup script: {e}")
        return False

def test_django_import():
    """Test Django import and basic functionality"""
    logger.info("Testing Django import...")
    
    try:
        import django
        logger.info(f"Django {django.__version__} imported successfully")
        
        # Set up Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')
        django.setup()
        logger.info("Django setup successful")
        
        # Test database connection
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result[0] == 1:
                logger.info("Database connection test successful")
                return True
            else:
                logger.error("Database connection test failed")
                return False
                
    except Exception as e:
        logger.error(f"Django test failed: {e}")
        return False

def test_pytest_run():
    """Test running a simple pytest command"""
    logger.info("Testing pytest run...")
    
    try:
        # Run a simple pytest command to test one file
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            '--version'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logger.info("Pytest is working")
            logger.info(result.stdout)
            return True
        else:
            logger.error("Pytest test failed")
            logger.error(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"Pytest test error: {e}")
        return False

def main():
    """Main test function"""
    logger.info("=== Testing CI Setup ===")
    
    tests = [
        ("CI Setup Script", test_ci_setup),
        ("Django Import", test_django_import),
        ("Pytest Run", test_pytest_run),
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"Running test: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Report results
    logger.info("=== Test Results ===")
    all_passed = True
    for test_name, passed in results.items():
        status = "PASS" if passed else "FAIL"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        logger.info("All tests passed!")
        return True
    else:
        logger.error("Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
