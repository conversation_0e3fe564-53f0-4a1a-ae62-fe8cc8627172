# Configuration for coverage.py (used by pytest-cov)

[run]
# Enable concurrency measurement (important for async/Celery)
concurrency = greenlet
branch = True
# Specify the source code directories to measure (relative to where pytest is run, i.e., backend/)
source = .
# Omit files matching these patterns
omit =
    # Standard Django/Python exclusions
    manage.py
    config/*
    */migrations/*
    */tests/*
    */testing/*
    */**/__init__.py
    */urls.py
    */wsgi.py
    */asgi.py
    # Third-party library directories (if any within apps/)
    # Example: */vendor/*
    # Project-specific exclusions (if needed)
    # Example: apps/utils/legacy_script.py

[report]
# Regexes for lines to exclude from consideration
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover

    # Don't complain about missing debug-only code:
    if settings.DEBUG:
    if DEBUG:

    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError

    # Don't complain if non-runnable code isn't run:
    if __name__ == .__main__.:

    # Don't complain about abstract methods, they aren't run:
    @(abc\.)?abstractmethod
    @(abc\.)?abstractproperty

    # Don't complain about typing imports
    if TYPE_CHECKING:
    from typing import TYPE_CHECKING

# Fail if coverage drops below this threshold (adjust as needed)
# fail_under = 80

[html]
# Directory to output HTML report
directory = coverage_html

[lcov]
# Output file for LCOV report
output = coverage.lcov
