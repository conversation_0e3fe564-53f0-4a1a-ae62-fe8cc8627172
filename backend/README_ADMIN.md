# Game of Life Admin Interface

This document provides an overview of the Game of Life admin interface capabilities, which have been customized to make it easier to manage GenericActivities and related models.

## Admin Dashboard

The admin dashboard provides an overview of your system with key statistics:

- **Activity Stats**: Number of generic activities, tailored activities, and domains
- **User Stats**: Number of user profiles, generic traits, and user goals
- **Wheel Stats**: Number of wheels and wheel items
- **Agent Stats**: Number of generic and custom agents
- **History Stats**: Number of history events and user feedback

The dashboard also shows the latest activities, tailored activities, and history events for quick access.

## Admin Features

### Managing GenericActivities

The GenericActivity admin page offers several features:

1. **Viewing Activities**:
   - View all activities with their domains, tags, and requirements
   - Sort by various fields like name, creation date, etc.
   - Filter by difficulty level, primary domain, and creation date

2. **Activity Details**:
   - Each activity has a detailed view showing all related information
   - Includes domain relationships, tags, and requirements
   - Shows related tailored activities

3. **Bulk Actions**:
   - **Duplicate Activities**: Create copies of selected activities with all related data
   - **Create/Update Requirement Summaries**: Generate summaries of requirements for selected activities

### Managing ActivityTailored Instances

The ActivityTailored admin page allows you to:

1. **View Tailored Activities**:
   - See all tailored activities with their user profiles and challenge ratings
   - Filter by user profile, creation date, and challenge rating

2. **Tailored Activity Details**:
   - Each tailored activity shows detailed information including:
     - User profile information
     - Challenge ratings across personality dimensions
     - Influences from goals, beliefs, and preferences
     - Link to the base generic activity

### User Profile Management

The UserProfile admin provides:

1. **Comprehensive User Information**:
   - Demographics data
   - Trait inclinations
   - Goals and aspirations
   - Beliefs and limitations

2. **Related Data Access**:
   - View tailored activities for each user
   - Manage user-specific traits and resources

## Using Activity Filters

### Difficulty Level Filter

Filter activities by their difficulty:

- **Easy (0-25)**: Activities with low difficulty
- **Medium (26-50)**: Activities with moderate difficulty
- **Challenging (51-75)**: Activities with high difficulty
- **Difficult (76-100)**: Activities with very high difficulty
- **Unknown/Not Set**: Activities without a difficulty rating

### Primary Domain Filter

Filter activities by their primary domain (domains with a "Primary" relationship strength).

## Duplicating Activities

To duplicate one or more activities:

1. Select the activities you want to duplicate using the checkboxes
2. Choose "Duplicate selected activities" from the Actions dropdown
3. Click "Go"

This will create new copies of the selected activities, including:
- All basic fields (name, description, etc.)
- Domain relationships with the correct strength
- Tags
- Resource requirements
- Environment requirements
- User requirements

The new copies will have "Copy of" prepended to their names and will have today's date as the creation date.

## Requirement Summaries

To generate or update requirement summaries:

1. Select the activities you want to summarize using the checkboxes
2. Choose "Create/update requirement summaries" from the Actions dropdown
3. Click "Go"

This will:
- Create a new summary if one doesn't exist
- Update an existing summary with the latest requirements
- Calculate the overall difficulty rating based on requirements

## Activity Details Page

The activity details page shows a comprehensive overview of the activity, including:

- Basic information (name, creation date, duration)
- Domains (with primary domain highlighted)
- Tags
- Metrics (number of resources, environments, and user requirements)
- Recent tailored versions

## Tailored Activity Details Page

The tailored activity details page shows:

- User profile information
- Activity details
- Challenge ratings across personality dimensions
- Influences from goals, beliefs, and preferences
- Link to the base generic activity

## Setup Instructions

The admin customizations are implemented in several files:

- `backend/config/admin.py`: The custom admin site configuration
- `backend/apps/activity/admin.py`: Activity-related admin classes
- `backend/apps/user/admin.py`: User-related admin classes
- `backend/apps/main/admin.py`: Agent and wheel-related admin classes
- Custom templates in `backend/templates/admin/`

No additional setup is required beyond ensuring that the templates directory is correctly configured in your Django settings.