# backend/testing/error_reporter_plugin.py
import os
from datetime import datetime
import pytest

class ErrorReporterPlugin:
    """
    A pytest plugin that creates a concise error report without assertion details.
    """
    
    def __init__(self, output_file="error_report.txt"):
        self.output_file = output_file
        self.errors = []
        self.test_count = 0
        self.error_count = 0
        self.start_time = None
        print(f"ErrorReporterPlugin initialized with output file: {self.output_file}")  # Debug print
        
    def pytest_sessionstart(self, session):
        self.start_time = datetime.now()
        # Create directory if it doesn't exist
        output_dir = os.path.dirname(self.output_file)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        # Clear any existing file
        with open(self.output_file, 'w') as f:
            f.write(f"Test Error Report - Started at {self.start_time}\n")
            f.write("="*80 + "\n\n")
        print(f"ErrorReporterPlugin started session, writing to: {self.output_file}")  # Debug print
    
    def pytest_runtest_logreport(self, report):
        if report.when == 'call':
            self.test_count += 1
            
        # Capture test errors (not assertion failures)
        if report.when in ('setup', 'call', 'teardown') and report.outcome == 'failed':
            # Check if this is an assertion error (which we want to skip)
            if not (hasattr(report, 'longrepr') and isinstance(report.longrepr, tuple) and 
                   'AssertionError' in str(report.longrepr[0])):
                # This is a non-assertion error
                self.error_count += 1
                
                # Format error info
                phase = f"[{report.when.upper()}]"
                nodeid = report.nodeid
                error_msg = str(report.longrepr) if hasattr(report, 'longrepr') else "Unknown error"
                
                # Clean up error message (remove assertion details)
                cleaned_error = []
                capture_error = False
                for line in error_msg.split('\n'):
                    if 'E   ' in line and not capture_error:
                        capture_error = True
                        cleaned_error.append(line)
                    elif capture_error and line.strip() and not line.startswith('E       '):
                        capture_error = False
                    elif capture_error:
                        cleaned_error.append(line)
                
                # Store error info
                self.errors.append({
                    'phase': phase,
                    'nodeid': nodeid,
                    'error': '\n'.join(cleaned_error) if cleaned_error else error_msg
                })
    
    def pytest_sessionfinish(self, session, exitstatus):
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        with open(self.output_file, 'a') as f:
            if self.errors:
                f.write(f"Found {self.error_count} errors in {self.test_count} tests:\n\n")
                
                for i, error in enumerate(self.errors, 1):
                    f.write(f"ERROR {i}: {error['nodeid']} {error['phase']}\n")
                    f.write("-" * 80 + "\n")
                    f.write(error['error'] + "\n\n")
            else:
                f.write("No errors detected in test run.\n\n")
            
            f.write("=" * 80 + "\n")
            f.write(f"Test run completed at {end_time}\n")
            f.write(f"Total duration: {duration}\n")
            f.write(f"Total tests: {self.test_count}\n")
            f.write(f"Total errors: {self.error_count}\n")


# Hook for pytest to discover and load the plugin
def pytest_configure(config):
    # For GitHub Actions, use a path relative to the workspace
    if os.environ.get('CI') == 'true':
        output_file = os.path.join(
            os.environ.get('GITHUB_WORKSPACE', '.'),
            'backend/test-results/error_report.txt'
        )
    else:
        # Use the environment variable or default for local/Docker execution
        output_file = os.environ.get(
            'ERROR_REPORT_FILE', 
            '/usr/src/app/test-results/error_report.txt'
        )
    
    plugin = ErrorReporterPlugin(output_file=output_file)
    config.pluginmanager.register(plugin, 'error_reporter')