# backend/testing/coverage_plugin.py
"""
Pytest plugin to collect test metadata (type, component, workflow, etc.)
and save it to a JSON file for correlation with coverage data.
"""

import pytest
import json
import os
from pathlib import Path
import logging

# Configure logging for the plugin
logger = logging.getLogger(__name__)
# Set default logging level if not configured elsewhere
# logging.basicConfig(level=logging.INFO) # Uncomment if needed for debugging

# Dictionary to store metadata per test node ID during the session
test_metadata_store = {}

def get_component_from_path(nodeid: str) -> str:
    """
    Infer component name from the test node ID (file path).

    Tries to generate a component string like 'app_name.module.submodule'
    based on the test file's location relative to 'backend/apps/'.

    Args:
        nodeid: The unique node ID of the test item (e.g.,
                'backend/apps/main/tests/test_agents/test_mentor.py::test_greeting').

    Returns:
        A string representing the inferred component, or 'unknown'.
    """
    try:
        # Ensure we are working with relative paths from the project root perspective
        # Assuming pytest runs from the root or backend directory
        if nodeid.startswith("backend/"):
            path_part_str = nodeid.split("::", 1)[0]
        else:
             # Try to construct path assuming it's relative to current execution
             path_part_str = str(Path(nodeid.split("::", 1)[0]))

        path_part = Path(path_part_str)

        # Find the 'apps' directory part
        try:
            apps_index = path_part.parts.index('apps')
            # Get path relative to 'apps' directory
            rel_path = Path(*path_part.parts[apps_index + 1:])
        except ValueError:
             # If 'apps' not in path, maybe it's a root test or structure changed
             logger.warning(f"Could not find 'apps' directory in path for nodeid: {nodeid}. Cannot infer component.")
             return "unknown"


        # Remove '/tests/' part and '.py' extension
        parts = list(rel_path.parts)
        component_parts = []
        for part in parts:
            if part == "tests":
                continue # Skip the 'tests' directory itself
            # Remove 'test_' prefix from modules/files if present
            cleaned_part = part.replace("test_", "")
            # Remove .py extension from the last part (filename)
            if part == parts[-1]:
                 cleaned_part = cleaned_part.replace(".py", "")
            component_parts.append(cleaned_part)

        # Join parts to form component string
        if component_parts:
            return ".".join(component_parts)
        else:
            logger.warning(f"Could not determine component parts for nodeid: {nodeid}")
            return "unknown"

    except (ValueError, IndexError, Exception) as e:
        # Catch potential errors during path manipulation
        logger.error(f"Error inferring component from nodeid '{nodeid}': {e}", exc_info=True)
        return "unknown" # Fallback

# Hook called after setup, call, and teardown of each test item
# Using hookwrapper=True allows us to access the report object reliably
@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_makereport(item: pytest.Item, call: pytest.CallInfo):
    """
    Collect metadata after each test execution ('call' phase).
    """
    outcome = yield
    report = outcome.get_result()

    # We only process the report for the 'call' phase
    if report.when == 'call':
        nodeid = item.nodeid
        metadata = {}

        # --- Collect Metadata ---
        # 1. Test Type (from marker or default to 'unit')
        test_type_marker = item.get_closest_marker("test_type")
        metadata['test_type'] = test_type_marker.args[0] if test_type_marker else 'unit'

        # 2. Component (from marker first, then infer from path)
        component_marker = item.get_closest_marker("component")
        if component_marker:
            metadata['component'] = component_marker.args[0]
        else:
            inferred_component = get_component_from_path(nodeid)
            metadata['component'] = inferred_component
            # Log if component was inferred vs explicitly marked (optional)
            # if inferred_component != 'unknown':
            #     logger.debug(f"Inferred component '{inferred_component}' for {nodeid}")

        # 3. Workflow (from marker)
        workflow_marker = item.get_closest_marker("workflow")
        if workflow_marker:
            metadata['workflow'] = workflow_marker.args[0]

        # 4. Agent (from marker)
        agent_marker = item.get_closest_marker("agent")
        if agent_marker:
            metadata['agent'] = agent_marker.args[0]

        # 5. Tool (from marker)
        tool_marker = item.get_closest_marker("tool")
        if tool_marker:
            metadata['tool'] = tool_marker.args[0]

        # --- Store Metadata ---
        # Store collected metadata using the unique test node ID
        # Use update to merge, allowing specific markers to override defaults/inferred values
        if nodeid not in test_metadata_store:
            test_metadata_store[nodeid] = {}
        test_metadata_store[nodeid].update(metadata)
        # logger.debug(f"Stored metadata for {nodeid}: {test_metadata_store[nodeid]}")


# Hook called after the entire test session finishes
def pytest_sessionfinish(session: pytest.Session):
    """
    Save the collected metadata to a JSON file at the end of the test session.
    """
    # Determine output path (relative to execution directory)
    if os.environ.get('CI') == 'true':
        # In GitHub Actions
        output_dir = Path(os.environ.get('GITHUB_WORKSPACE', '.')) / 'backend'
    else:
        # Local or Docker environment
        if Path("backend").exists():
            output_dir = Path("backend")
        else:
            output_dir = Path(".")  # Assume current dir if backend/ not found

    output_file = output_dir / "coverage_metadata.json"

    # Ensure the output directory exists (though backend/ should exist)
    try:
        output_dir.mkdir(parents=True, exist_ok=True)
    except OSError as e:
         logger.error(f"Error creating directory {output_dir}: {e}")
         # Optionally, decide if you want to proceed or raise error

    # Write the collected metadata to the JSON file
    logger.info(f"Attempting to save coverage metadata to {output_file.resolve()}")
    try:
        with open(output_file, 'w') as f:
            json.dump(test_metadata_store, f, indent=2, sort_keys=True)
        # Use pytest's terminal writer for visible output
        reporter = session.config.pluginmanager.get_plugin('terminalreporter')
        if reporter:
             reporter.write_line(f"\nCoverage metadata saved to {output_file}", green=True)
        else:
             print(f"\nCoverage metadata saved to {output_file}") # Fallback print

    except IOError as e:
        logger.error(f"Error writing coverage metadata file '{output_file}': {e}", exc_info=True)
        if reporter:
            reporter.write_line(f"\nError writing coverage metadata file: {e}", red=True)
        else:
            print(f"\nError writing coverage metadata file: {e}") # Fallback print
    except Exception as e:
        logger.error(f"An unexpected error occurred during metadata saving: {e}", exc_info=True)
        if reporter:
            reporter.write_line(f"\nUnexpected error saving metadata: {e}", red=True)
        else:
            print(f"\nUnexpected error saving metadata: {e}") # Fallback print


    # Clear the store for subsequent runs if pytest is run multiple times in one process
    # This is important for tools like pytest-watch or repeated programmatic runs
    test_metadata_store.clear()

# Hook to register custom markers
def pytest_configure(config: pytest.Config):
    """
    Register custom markers used by this plugin.
    """
    config.addinivalue_line(
        "markers",
        "test_type(type): Specify the type of test (e.g., unit, integration, workflow, llm, component)."
    )
    config.addinivalue_line(
        "markers",
        "component(name): Specify the architectural component tested (e.g., main.agents.mentor, frontend.services.websocket, user.models)."
    )
    config.addinivalue_line(
        "markers",
        "workflow(name): Specify the LangGraph workflow tested (e.g., discussion, wheel_generation, onboarding)."
    )
    config.addinivalue_line(
        "markers",
        "agent(name): Specify the primary agent tested (e.g., MentorAgent, OrchestratorAgent, EthicalAgent)."
    )
    config.addinivalue_line(
        "markers",
        "tool(name): Specify the agent tool tested (e.g., get_user_profile, evaluate_message_sentiment, update_goal)."
    )
    logger.info("Coverage metadata plugin configured and markers registered.")
