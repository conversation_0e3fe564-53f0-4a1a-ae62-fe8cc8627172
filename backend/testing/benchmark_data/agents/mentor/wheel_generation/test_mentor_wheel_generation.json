{"name": "test_mentor_wheel_generation", "description": "Test scenario for mentor in wheel generation workflow", "agent_role": "mentor", "input_data": {"user_message": "I want to try something new today", "context_packet": {"workflow_type": "wheel_generation", "trust_level": 65, "text": "I want to try something new today"}}, "metadata": {"workflow_type": "wheel_generation", "template_reference": "mentor_wheel_generation_criteria"}, "tags": ["mentor", "wheel_generation", "test"], "situation": {"workflow_type": "wheel_generation", "text": "The user is looking for new activities to try.", "context": "The user has been doing the same activities for a while and wants to try something new."}}