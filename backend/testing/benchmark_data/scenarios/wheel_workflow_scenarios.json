[{"name": "wheel_generation_basic", "description": "Basic wheel generation workflow benchmark", "agent_role": "workflow", "input_data": {"user_profile_id": "test-user-123", "context_packet": {"task_type": "wheel_generation", "user_message": "I want to improve my productivity", "user_context": {"recent_activities": [], "preferences": {"activity_types": ["productivity", "learning", "creativity"]}}}}, "metadata": {"workflow_type": "wheel_generation", "expected_quality_criteria": {"Completeness": ["Does the wheel contain a balanced set of activities?", "Are the activities appropriate for the user's request?"], "Personalization": ["Do the activities reflect the user's stated preferences?", "Is the difficulty level appropriate?"], "Clarity": ["Are the activity descriptions clear and actionable?", "Would the user understand how to perform each activity?"]}, "mock_tool_responses": {"get_user_profile": {"condition": "True", "response": "{\"id\": \"test-user-123\", \"name\": \"Test User\", \"traits\": {\"openness\": 0.8, \"conscientiousness\": 0.7, \"extraversion\": 0.5, \"agreeableness\": 0.6, \"neuroticism\": 0.3}, \"preferences\": {\"activity_types\": [\"productivity\", \"learning\", \"creativity\"]}, \"trust_phase\": \"established\"}"}, "get_user_activities": {"condition": "True", "response": "{\"activities\": []}"}, "store_wheel": {"condition": "True", "response": "{\"success\": true, \"wheel_id\": \"test-wheel-123\"}"}}, "expected_stages": {"initial_conversation": {"max_duration_ms": 2000, "required": true}, "profile_analysis": {"max_duration_ms": 3000, "required": true}, "wheel_generation": {"max_duration_ms": 5000, "required": true}, "activity_selection": {"max_duration_ms": 4000, "required": true}, "final_response": {"max_duration_ms": 2000, "required": true}}}, "is_active": true, "tags": ["workflow", "wheel_generation", "core"]}, {"name": "wheel_generation_complex", "description": "Complex wheel generation workflow with specific user requirements", "agent_role": "workflow", "input_data": {"user_profile_id": "test-user-456", "context_packet": {"task_type": "wheel_generation", "user_message": "I need activities that help me balance work and personal life while improving my health", "user_context": {"recent_activities": [{"id": "act-123", "name": "Morning Meditation", "category": "wellness", "completed": true, "feedback": "positive"}], "preferences": {"activity_types": ["wellness", "productivity", "relaxation"], "time_available": "limited"}}}}, "metadata": {"workflow_type": "wheel_generation", "expected_quality_criteria": {"Completeness": ["Does the wheel contain a balanced set of activities?", "Are the activities appropriate for the user's request?"], "Personalization": ["Do the activities reflect the user's stated preferences?", "Is the difficulty level appropriate?", "Do the activities consider the user's time constraints?"], "Clarity": ["Are the activity descriptions clear and actionable?", "Would the user understand how to perform each activity?"], "Work-Life Balance": ["Do the activities help balance work and personal life?", "Is there a good mix of professional and personal development activities?"], "Health Focus": ["Do the activities support the user's health improvement goals?", "Is there a balance of physical and mental health activities?"]}, "mock_tool_responses": {"get_user_profile": {"condition": "True", "response": "{\"id\": \"test-user-456\", \"name\": \"Complex User\", \"traits\": {\"openness\": 0.6, \"conscientiousness\": 0.8, \"extraversion\": 0.4, \"agreeableness\": 0.7, \"neuroticism\": 0.5}, \"preferences\": {\"activity_types\": [\"wellness\", \"productivity\", \"relaxation\"], \"time_available\": \"limited\"}, \"trust_phase\": \"established\"}"}, "get_user_activities": {"condition": "True", "response": "{\"activities\": [{\"id\": \"act-123\", \"name\": \"Morning Meditation\", \"category\": \"wellness\", \"completed\": true, \"feedback\": \"positive\"}]}"}, "store_wheel": {"condition": "True", "response": "{\"success\": true, \"wheel_id\": \"test-wheel-456\"}"}}, "expected_stages": {"initial_conversation": {"max_duration_ms": 2000, "required": true}, "profile_analysis": {"max_duration_ms": 3000, "required": true}, "wheel_generation": {"max_duration_ms": 5000, "required": true}, "activity_selection": {"max_duration_ms": 4000, "required": true}, "final_response": {"max_duration_ms": 2000, "required": true}}}, "is_active": true, "tags": ["workflow", "wheel_generation", "complex"]}]