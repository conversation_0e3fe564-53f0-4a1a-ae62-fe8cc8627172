[{"name": "Mentor - Handle Constructive Feedback - Expansion", "description": "Tests mentor responding constructively to negative/constructive feedback for an Expansion phase user, demonstrating adaptive challenge calibration.", "agent_role": "mentor", "input_data": {"user_message": "That activity was way too easy and didn't really push me. I need something that actually challenges me.", "context_packet": {"workflow_type": "activity_feedback", "trust_level": 65}}, "metadata": {"workflow_type": "activity_feedback", "user_profile_context": {"trust_phase": "Expansion", "communication_preferences": {"tone": "collaborative", "detail_level": "high"}, "personality_traits": {"extraversion": 0.6, "neuroticism": 0.3, "openness": 0.8, "conscientiousness": 0.9}, "skills": {"programming": 4.2, "problem_solving": 4.5, "algorithm_design": 3.9}, "experience_level": "intermediate", "preferred_domains": ["technology", "problem-solving", "learning"]}, "activity_context": {"name": "Basic Python Algorithmic Challenge", "domain": "programming", "challenge_level": 32, "skills_required": ["basic_python", "list_manipulation", "conditional_logic"]}, "expected_quality_criteria": {"Receptiveness": ["Does it receive feedback non-defensively?", "Does it acknowledge the mismatch in challenge level?"], "Adaptiveness": ["Does it adapt to the user's expressed need for greater challenge?", "Does it demonstrate willingness to update the challenge calibration?"], "Collaborative Tone": ["Is the tone collaborative rather than apologetic or defensive?", "Does it frame the feedback as valuable input for better calibration?"], "Specific Follow-up": ["Does it ask specific, targeted questions to better understand the user's skill level?", "Does it inquire about specific aspects of the challenge that were too easy?"], "Growth-Oriented Response": ["Does it frame the situation positively as a step in finding the right challenge level?", "Does it validate the user's desire for growth through appropriate challenges?"]}, "mock_tool_responses": {"get_activity_details": [{"condition": "tool_input.get('activity_id') == 'act_expansion_456'", "response": "{'name': 'Basic Python Algorithmic Challenge', 'domain': 'programming', 'challenge_level': 32, 'skills_required': ['basic_python', 'list_manipulation', 'conditional_logic'], 'description': 'A programming challenge involving sorting with specific rules.', 'recommended_challenge_range': {'min': 25, 'max': 45}}"}], "get_user_profile": {"profile_name": "Advanced Programmer", "trust_phase": "Expansion", "trust_level": 65, "communication_preferences": {"tone": "collaborative", "detail_level": "high"}, "skills": {"programming": 4.2, "problem_solving": 4.5, "algorithm_design": 3.9}}, "get_skill_level_activities": {"programming": [{"name": "Advanced Algorithm Implementation", "challenge_level": 75, "domain": "programming"}, {"name": "Design Pattern Application Challenge", "challenge_level": 68, "domain": "programming"}, {"name": "Optimization Problem", "challenge_level": 70, "domain": "programming"}]}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}]