[{"name": "Mentor - <PERSON>le Positive Feedback - Foundation", "description": "Tests mentor responding to positive user feedback after completing an activity, demonstrating appropriate reinforcement for a Foundation phase user.", "agent_role": "mentor", "input_data": {"user_message": "Wow, I actually did it! That felt good.", "context_packet": {"workflow_type": "activity_feedback", "trust_level": 40}}, "metadata": {"workflow_type": "activity_feedback", "user_profile_context": {"trust_phase": "Foundation", "communication_preferences": {"tone": "warm", "detail_level": "low"}, "personality_traits": {"extraversion": 0.4, "neuroticism": 0.6, "openness": 0.5, "conscientiousness": 0.5}, "activity_history": {"completed_count": 3, "abandonment_rate": 0.4, "average_rating": 3.5}}, "activity_context": {"name": "10-Minute Mindful Breathing", "domain": "mindfulness", "challenge_level": 15, "time_required": 10}, "expected_quality_criteria": {"Tone": ["Is the tone warm and genuinely positive?", "Does it avoid excessive enthusiasm that might feel inauthentic?"], "Validation": ["Does it validate the user's accomplishment appropriately?", "Does it acknowledge the significance of 'actually doing it' for someone in Foundation phase?"], "Growth Framing": ["Does it frame the success as a building block for trust and future activities?", "Does it avoid pushing immediately toward harder challenges?"], "Next Steps": ["Does it offer gentle next steps that build on this success?", "Are suggestions appropriate for Foundation phase (not too challenging)?"], "Philosophical Connection": ["Does it connect this success to broader wellbeing concepts?", "Is the philosophical framing kept simple and accessible?"]}, "mock_tool_responses": {"get_activity_details": [{"condition": "tool_input.get('activity_id') == 'act_foundation_123'", "response": "{\"name\": \"10-Minute Mindful Breathing\", \"domain\": \"mindfulness\", \"challenge_level\": 15, \"description\": \"A simple mindfulness exercise focused on breath awareness\", \"completion_count\": 28, \"average_rating\": 4.7}"}], "get_user_profile": {"profile_name": "Foundation User with Progress", "trust_phase": "Foundation", "trust_level": 42, "communication_preferences": {"tone": "warm", "detail_level": "low"}}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}]