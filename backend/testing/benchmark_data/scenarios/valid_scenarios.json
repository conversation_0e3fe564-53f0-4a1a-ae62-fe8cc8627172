[{"name": "test_mentor_wheel_generation", "description": "Test scenario for mentor in wheel generation workflow", "agent_role": "mentor", "input_data": {"user_message": "I want to try something new today", "context_packet": {"workflow_type": "wheel_generation", "trust_level": 65}}, "metadata": {"workflow_type": "wheel_generation", "expected_quality_criteria": {"Tone": ["supportive", "friendly", "encouraging"], "Content": ["relevant", "helpful", "personalized"]}}, "tags": ["mentor", "wheel_generation", "test"], "situation": {"text": "The user is looking for new activities to try.", "context": "The user has been doing the same activities for a while and wants to try something new."}}, {"name": "test_mentor_discussion", "description": "Test scenario for mentor in discussion workflow", "agent_role": "mentor", "input_data": {"user_message": "I'm feeling stuck with my current activities", "context_packet": {"workflow_type": "discussion", "trust_level": 50}}, "metadata": {"workflow_type": "discussion", "expected_quality_criteria": {"Tone": ["empathetic", "understanding", "patient"], "Content": ["insightful", "thought-provoking", "relevant"]}}, "tags": ["mentor", "discussion", "test"], "situation": {"text": "The user is feeling stuck with their current activities.", "context": "The user has been doing the same activities for a while and is feeling bored and unmotivated."}}, {"name": "test_workflow_wheel_generation", "description": "Test scenario for wheel generation workflow", "agent_role": "workflow", "input_data": {"user_message": "I need some new activities", "context_packet": {"workflow_type": "wheel_generation", "trust_level": 70}}, "metadata": {"workflow_type": "wheel_generation", "expected_quality_criteria": {"Efficiency": ["timely", "streamlined", "responsive"], "Quality": ["accurate", "comprehensive", "personalized"]}}, "tags": ["workflow", "wheel_generation", "test"], "situation": {"text": "The user needs new activities for their wheel.", "context": "The user has completed their previous activities and is ready for new challenges."}}]