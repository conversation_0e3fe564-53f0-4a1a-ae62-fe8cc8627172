[{"name": "Mentor - Present Activity - Integration", "description": "Tests mentor presenting a complex activity to an Integration phase user who prefers high detail, focusing on philosophical framing and personalization. Uses contextual evaluation that adapts to high trust, positive mood, and moderate stress environment.", "agent_role": "mentor", "input_data": {"user_message": "Okay, I'm ready to see what you suggest.", "context_packet": {"workflow_type": "discussion", "trust_level": 85}}, "metadata": {"workflow_type": "discussion", "evaluation_template_id": 2, "context": {"trust_level": 85, "mood": {"valence": 0.6, "arousal": 0.3}, "environment": {"stress_level": 40, "time_pressure": 25}}, "user_profile_context": {"trust_phase": "Integration", "communication_preferences": {"tone": "professional", "detail_level": "high"}, "personality_traits": {"extraversion": 0.5, "neuroticism": 0.3, "openness": 0.9, "conscientiousness": 0.8, "agreeableness": 0.7}, "professional_background": "Software Developer (10+ years)", "growing_interests": ["education", "mentoring", "knowledge sharing"], "current_goals": ["transfer technical knowledge", "develop teaching skills", "build educational portfolio"]}, "expected_quality_criteria": {"Philosophical Integration": ["Does it connect the activity to deeper philosophical principles?", "Does it frame the activity in terms of Integration phase values (synthesis, wisdom sharing)?", "Does it help the user see connections between their various skills and interests?", "Does it demonstrate collaborative and empowering language appropriate for high trust level?"], "Personalization": ["Is the activity clearly tailored to the user's specific background and goals?", "Does it reference specific aspects of the user's interests and skills?", "Does it connect to the user's stated goal of combining technical skills with teaching?", "Does it offer ambitious goals and creative challenges suitable for Integration phase?"], "Comprehensive Detail": ["Does it provide appropriately high level of detail for the user's preference?", "Does it cover purpose, process, benefits, and potential challenges?", "Is information organized in a clear, logical structure?", "Does it provide comprehensive options given the moderate stress environment?"], "Multi-dimensional Value": ["Does it explain the activity's value across multiple dimensions?", "Does it highlight both skill development and personal growth aspects?", "Does it connect the activity to broader impact (e.g., contribution to learning community)?", "Does it match the positive but calm energy level of the user's mood state?"], "Balanced Agency": ["Does it present the activity as a suggestion rather than a directive?", "Does it invite the user's input on how to shape or modify the activity?", "Does it respect the user's autonomy while providing clear guidance?", "Does it encourage independent exploration and advanced techniques appropriate for high trust?", "Does it provide long-term planning and detailed development given low time pressure?"]}, "mock_tool_responses": {"get_user_profile": {"profile_name": "Integration Phase Professional", "trust_phase": "Integration", "trust_level": 88, "communication_preferences": {"tone": "professional", "detail_level": "high"}, "professional_background": "Software Developer (10+ years)", "growing_interests": ["education", "mentoring", "knowledge sharing"], "current_goals": ["transfer technical knowledge", "develop teaching skills", "build educational portfolio"], "last_activity_feedback": "I enjoyed creating the technical blog post, but I'd like something more interactive next time."}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}]