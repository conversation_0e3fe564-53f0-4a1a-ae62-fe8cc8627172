{"name": "Contextual Discussion Evaluation", "description": "Comprehensive contextual evaluation template for discussion workflows that adapts criteria based on user trust level, emotional state, and environmental context", "workflow_type": "discussion", "category": "contextual", "criteria": {"Engagement": ["Active listening", "Thoughtful responses", "Question quality", "Conversation flow"], "Empathy": ["Understanding", "Validation", "Emotional support", "Compassion"], "Guidance": ["Helpful suggestions", "Appropriate direction", "Skill building", "Growth facilitation"], "Communication": ["Clarity", "Tone appropriateness", "Language choice", "Pacing"]}, "contextual_criteria": {"trust_level": {"0-39": {"Engagement": ["Gentle probing", "Safe space creation", "Non-judgmental", "Careful listening", "Respectful boundaries"], "Empathy": ["Extra validation", "Reassurance", "Patience", "Emotional safety", "Unconditional support"], "Guidance": ["Basic suggestions", "Safe recommendations", "Low-pressure advice", "Simple steps"], "Communication": ["Simple language", "Clear explanations", "Slow pacing", "Reassuring tone"]}, "40-69": {"Engagement": ["Balanced questioning", "Encouraging participation", "Collaborative exploration", "Active involvement"], "Empathy": ["Supportive validation", "Understanding responses", "Emotional attunement", "Caring presence"], "Guidance": ["Collaborative problem-solving", "Skill development", "Growth encouragement", "Moderate challenges"], "Communication": ["Encouraging language", "Motivating tone", "Balanced detail", "Supportive pacing"]}, "70-100": {"Engagement": ["Deep exploration", "Challenge assumptions", "Advanced questioning", "Independent thinking"], "Empathy": ["Sophisticated understanding", "Nuanced validation", "Complex emotional support"], "Guidance": ["Advanced techniques", "Independent thinking", "Complex problem-solving", "Leadership development"], "Communication": ["Collaborative language", "Empowering tone", "Rich detail", "Dynamic pacing"]}}, "mood": {"valence": {"-1.0--0.3": {"Empathy": ["Extra compassion", "Emotional support", "Gentle approach", "Mood sensitivity", "Comfort provision"], "Guidance": ["Mood-sensitive suggestions", "Comfort-focused", "Emotional healing", "Self-care emphasis"], "Communication": ["Soft tone", "Gentle language", "Patient pacing", "Emotional warmth"], "Engagement": ["Careful attention", "Emotional validation", "Supportive presence", "Gentle encouragement"]}, "-0.3-0.3": {"Empathy": ["Balanced support", "Steady presence", "Reliable understanding", "Consistent validation"], "Guidance": ["Balanced suggestions", "Stable recommendations", "Consistent support", "Moderate guidance"], "Communication": ["Neutral-positive tone", "Clear language", "Steady pacing", "Reliable presence"], "Engagement": ["Consistent attention", "Balanced participation", "Steady involvement"]}, "0.3-1.0": {"Empathy": ["Enthusiastic support", "Positive validation", "Energy matching", "Celebratory understanding"], "Guidance": ["Growth-oriented", "Achievement-focused", "Momentum building", "Success amplification"], "Communication": ["Enthusiastic tone", "Positive language", "Energetic pacing", "Celebratory style"], "Engagement": ["Enthusiastic participation", "Energy matching", "Dynamic involvement", "Positive reinforcement"]}}, "arousal": {"-1.0--0.3": {"Communication": ["Calm tone", "Soothing language", "Slow pacing", "Peaceful presence"], "Guidance": ["Calming suggestions", "Relaxation focus", "Stress reduction", "Peace building"], "Engagement": ["Gentle attention", "Calm presence", "Soothing interaction", "Peaceful dialogue"]}, "-0.3-0.3": {"Communication": ["Balanced tone", "Moderate language", "Steady pacing", "Consistent presence"], "Guidance": ["Balanced suggestions", "Moderate energy", "Sustainable approaches", "Steady progress"], "Engagement": ["Consistent attention", "Balanced energy", "Moderate involvement"]}, "0.3-1.0": {"Communication": ["Dynamic tone", "Energetic language", "Quick pacing", "Stimulating presence"], "Guidance": ["Energizing suggestions", "Dynamic approaches", "Stimulating challenges", "Active engagement"], "Engagement": ["High energy attention", "Dynamic participation", "Stimulating interaction", "Active dialogue"]}}}, "environment": {"stress_level": {"0-30": {"Guidance": ["Comprehensive exploration", "Detailed analysis", "Thorough discussion", "In-depth guidance"], "Communication": ["Detailed explanations", "Rich language", "<PERSON><PERSON> pacing", "Complete coverage"], "Engagement": ["Deep exploration", "Comprehensive attention", "<PERSON><PERSON> involvement", "Complete presence"]}, "31-70": {"Guidance": ["Focused suggestions", "Balanced guidance", "Moderate detail", "Structured support"], "Communication": ["Clear explanations", "Focused language", "Efficient pacing", "Organized delivery"], "Engagement": ["Focused attention", "Balanced involvement", "Structured participation", "Organized dialogue"]}, "71-100": {"Guidance": ["Essential suggestions", "Quick guidance", "Stress-relief focus", "Immediate support"], "Communication": ["Concise explanations", "Simple language", "Quick pacing", "Essential delivery"], "Engagement": ["Focused attention", "Stress-aware involvement", "Efficient participation", "Supportive presence"]}}, "time_pressure": {"0-30": {"Guidance": ["Long-term planning", "Detailed development", "Comprehensive growth", "Patient guidance"], "Communication": ["Thorough explanations", "Patient language", "Relaxed pacing", "Unhurried delivery"], "Engagement": ["Patient attention", "Unhurried involvement", "Thorough exploration", "Relaxed dialogue"]}, "31-70": {"Guidance": ["Moderate planning", "Balanced development", "Focused growth", "Efficient guidance"], "Communication": ["Balanced explanations", "Efficient language", "Moderate pacing", "Focused delivery"], "Engagement": ["Efficient attention", "Balanced involvement", "Focused exploration", "Productive dialogue"]}, "71-100": {"Guidance": ["Immediate planning", "Quick development", "Urgent support", "Time-sensitive guidance"], "Communication": ["Quick explanations", "Urgent language", "Fast pacing", "Immediate delivery"], "Engagement": ["Urgent attention", "Quick involvement", "Immediate exploration", "Time-efficient dialogue"]}}}}, "variable_ranges": {"trust_level": {"min": 0, "max": 100, "description": "User's trust level in the system and mentor (Foundation: 0-39, Expansion: 40-69, Integration: 70-100)"}, "mood": {"valence": {"min": -1.0, "max": 1.0, "description": "Emotional valence from negative to positive"}, "arousal": {"min": -1.0, "max": 1.0, "description": "Emotional arousal from calm to excited"}}, "environment": {"stress_level": {"min": 0, "max": 100, "description": "Current environmental stress level affecting conversation capacity"}, "time_pressure": {"min": 0, "max": 100, "description": "Time pressure affecting conversation depth and pacing"}}}, "is_active": true}