# Contextual Evaluation Templates

> **📖 For comprehensive documentation, see:**
> - **[Main Documentation](../../../../docs/backend/BENCHMARKING_SYSTEM.md)** - Complete technical reference
> - **[Usage Guide](../../../../docs/user-guide/BENCHMARKING_USAGE_GUIDE.md)** - Step-by-step instructions
> - **[Contextual Evaluation System](../../../../docs/backend/quality/CONTEXTUAL_EVALUATION_SYSTEM.md)** - Detailed contextual evaluation documentation

This directory contains rich contextual evaluation templates that demonstrate the full power of the Contextual Evaluation System. These templates adapt evaluation criteria based on user context variables including trust level, mood, and environmental factors.

## Template Overview

### Core Templates

#### 1. `wheel_generation_contextual.json`
**Purpose**: Comprehensive contextual evaluation for wheel generation workflows
**Key Features**:
- Adapts to trust levels from Foundation (0-39) to Integration (70-100)
- Responds to mood valence (-1.0 to 1.0) and arousal (-1.0 to 1.0)
- Adjusts for stress levels (0-100) and time pressure (0-100)
- Includes criteria for Content, Tone, Structure, and Approach

**Example Adaptations**:
- **Low Trust (0-39)**: Simple, clear, reassuring tone with safe options
- **High Trust (70-100)**: Collaborative, empowering tone with ambitious goals
- **Negative Mood**: Gentle, understanding approach with mood-lifting activities
- **High Stress**: Concise, essential-only guidance with stress-relief focus

#### 2. `discussion_contextual.json`
**Purpose**: Contextual evaluation for discussion and conversation workflows
**Key Features**:
- Focuses on Engagement, Empathy, Guidance, and Communication
- Adapts conversation depth based on environmental stress
- Modifies empathy approach based on emotional state
- Adjusts guidance complexity based on trust level

**Example Adaptations**:
- **Foundation Trust**: Gentle probing with extra validation and patience
- **Integration Trust**: Deep exploration with advanced questioning
- **High Stress**: Essential suggestions with quick guidance
- **Low Arousal**: Calming tone with soothing language

#### 3. `therapeutic_support_contextual.json`
**Purpose**: Advanced template for therapeutic and mental health support
**Key Features**:
- Clinical appropriateness that adapts to user psychological state
- Therapeutic alliance building based on trust development
- Crisis-sensitive adaptations for severe distress
- Professional boundary awareness

**Example Adaptations**:
- **Crisis Mood (-1.0 to -0.5)**: Crisis intervention with safety prioritization
- **High Trust**: Advanced interventions with complex case handling
- **High Stress**: Crisis-aware interventions with immediate techniques

#### 4. `coaching_contextual.json`
**Purpose**: Professional coaching evaluation with performance focus
**Key Features**:
- Goal alignment that adapts to client readiness
- Performance optimization based on energy levels
- Accountability support matching trust development
- Motivation enhancement responding to mood states

**Example Adaptations**:
- **High Energy**: Dynamic approaches with accelerated progress
- **Low Trust**: Basic goal setting with gentle accountability
- **Time Pressure**: Immediate planning with urgent objectives

#### 5. `comprehensive_contextual.json`
**Purpose**: Master template demonstrating full contextual capability
**Key Features**:
- Showcases all supported variable ranges
- Demonstrates fine-grained adaptations (20-point trust ranges)
- Includes extreme condition handling
- Comprehensive criteria coverage

**Example Adaptations**:
- **Ultra-low Trust (0-20)**: Ultra-safe adaptation with maximum reassurance
- **Peak Performance (80-100)**: Masterful adaptation with transformational challenges
- **Crisis Stress (86-100)**: Emergency adaptation with immediate responses

## Usage in Scenarios

### Integration with Benchmark Scenarios

Scenarios now include contextual evaluation metadata:

```json
{
  "metadata": {
    "evaluation_template_id": 1,
    "context": {
      "trust_level": 65,
      "mood": {
        "valence": 0.7,
        "arousal": 0.6
      },
      "environment": {
        "stress_level": 20,
        "time_pressure": 30
      }
    }
  }
}
```

### Example Scenario Combinations

1. **Crisis Support** (`mentor_crisis_support.json`)
   - Trust: 15 (Foundation)
   - Mood: Valence -0.8, Arousal -0.6 (Distressed, Low Energy)
   - Environment: Stress 90, Time Pressure 85 (Crisis)
   - **Result**: Ultra-gentle, stabilizing, immediate support

2. **Peak Performance** (`mentor_peak_performance.json`)
   - Trust: 95 (Integration)
   - Mood: Valence 0.9, Arousal 0.8 (Euphoric, High Energy)
   - Environment: Stress 10, Time Pressure 15 (Optimal)
   - **Result**: Ambitious, comprehensive, visionary guidance

## Loading Templates

Templates are automatically loaded during system startup via `entrypoint.sh`:

```bash
python manage.py load_contextual_templates --templates-dir=testing/benchmark_data/contextual_templates --force
```

### Manual Loading

```bash
# Load all templates from directory
python manage.py load_contextual_templates

# Load from specific directory
python manage.py load_contextual_templates --templates-dir=path/to/templates

# Force update existing templates
python manage.py load_contextual_templates --force
```

## Template Structure

### Required Fields
- `name`: Unique template identifier
- `description`: Template purpose and scope
- `criteria`: Base evaluation criteria (always applied)
- `contextual_criteria`: Context-dependent adaptations
- `variable_ranges`: Supported variable ranges and descriptions

### Optional Fields
- `workflow_type`: Target workflow (e.g., "wheel_generation", "discussion")
- `category`: Template category (default: "contextual")
- `is_active`: Template activation status (default: true)

### Contextual Criteria Structure

```json
{
  "contextual_criteria": {
    "trust_level": {
      "0-39": { "Tone": ["Simple", "Clear"] },
      "40-69": { "Tone": ["Encouraging", "Supportive"] },
      "70-100": { "Tone": ["Collaborative", "Empowering"] }
    },
    "mood": {
      "valence": {
        "-1.0-0.0": { "Approach": ["Gentle", "Patient"] },
        "0.0-1.0": { "Approach": ["Energetic", "Dynamic"] }
      }
    },
    "environment": {
      "stress_level": {
        "0-30": { "Detail": ["Comprehensive", "Thorough"] },
        "71-100": { "Detail": ["Essential", "Concise"] }
      }
    }
  }
}
```

## Best Practices

### Template Design
1. **Start with base criteria** that apply in all contexts
2. **Add contextual layers** that enhance or modify base criteria
3. **Use consistent range definitions** across templates
4. **Provide clear descriptions** for all variables

### Range Definition
- **Trust Level**: 0-39 (Foundation), 40-69 (Expansion), 70-100 (Integration)
- **Mood Valence**: -1.0 (severe distress) to 1.0 (peak joy)
- **Mood Arousal**: -1.0 (shutdown) to 1.0 (hyperactivation)
- **Stress Level**: 0-30 (optimal), 31-70 (moderate), 71-100 (crisis)
- **Time Pressure**: 0-30 (unlimited), 31-70 (moderate), 71-100 (urgent)

### Criteria Adaptation
- **Additive**: Contextual criteria extend base criteria
- **Dimensional**: Organize criteria by evaluation dimensions
- **Specific**: Use concrete, measurable criteria
- **Graduated**: Ensure smooth transitions between ranges

## Testing and Validation

### Scenario Coverage
- Test extreme conditions (crisis, peak performance)
- Validate range boundaries (e.g., trust level 39 vs 40)
- Verify contextual adaptations work as expected
- Check criteria merging logic

### Quality Assurance
- Ensure criteria are measurable and specific
- Verify range definitions don't overlap inappropriately
- Test with real user scenarios
- Validate against psychological research

## Future Enhancements

### Planned Features
- **Machine learning optimization** of criteria adaptations
- **Real-time context detection** from user interactions
- **Dynamic range adjustment** based on usage patterns
- **Multi-template composition** for complex scenarios

### Extension Points
- Additional contextual variables (personality, culture, goals)
- Industry-specific templates (healthcare, education, corporate)
- Temporal adaptations (time of day, season, lifecycle stage)
- Social context awareness (group dynamics, relationship stage)
