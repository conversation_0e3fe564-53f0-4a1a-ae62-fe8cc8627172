{"name": "Contextual Coaching Evaluation", "description": "Professional coaching evaluation template that adapts to client readiness, motivation levels, and performance context", "workflow_type": "coaching", "category": "contextual", "criteria": {"Goal_Alignment": ["Objective clarity", "Priority setting", "Vision alignment", "Outcome focus"], "Skill_Development": ["Competency building", "Learning facilitation", "Practice guidance", "Mastery support"], "Motivation_Enhancement": ["Engagement building", "Momentum creation", "Confidence boosting", "Drive amplification"], "Performance_Optimization": ["Efficiency improvement", "Quality enhancement", "Results achievement", "Excellence pursuit"], "Accountability_Support": ["Progress tracking", "Commitment reinforcement", "Responsibility ownership", "Follow-through assistance"]}, "contextual_criteria": {"trust_level": {"0-39": {"Goal_Alignment": ["Basic goal setting", "Simple objectives", "Clear expectations", "Safe targets"], "Skill_Development": ["Fundamental skills", "Basic competencies", "Safe learning", "Gradual building"], "Motivation_Enhancement": ["Gentle encouragement", "Safety building", "Confidence foundation", "Basic engagement"], "Performance_Optimization": ["Small improvements", "Safe progress", "Basic efficiency", "Comfort zone work"], "Accountability_Support": ["Gentle accountability", "Supportive tracking", "Basic commitment", "Safe responsibility"]}, "40-69": {"Goal_Alignment": ["Balanced objectives", "Moderate challenges", "Growth targets", "Skill-building goals"], "Skill_Development": ["Intermediate skills", "Balanced competencies", "Progressive learning", "Confidence building"], "Motivation_Enhancement": ["Encouraging support", "Momentum building", "Confidence growth", "Balanced engagement"], "Performance_Optimization": ["Moderate improvements", "Steady progress", "Balanced efficiency", "Growth zone work"], "Accountability_Support": ["Collaborative accountability", "Supportive tracking", "Shared commitment", "Growth responsibility"]}, "70-100": {"Goal_Alignment": ["Ambitious objectives", "<PERSON><PERSON><PERSON> challenges", "Visionary targets", "Leadership goals"], "Skill_Development": ["Advanced skills", "Expert competencies", "Mastery learning", "Excellence building"], "Motivation_Enhancement": ["Inspiring support", "Peak performance", "Excellence drive", "Mastery engagement"], "Performance_Optimization": ["Breakthrough improvements", "Excellence pursuit", "Peak efficiency", "Mastery zone work"], "Accountability_Support": ["High accountability", "Excellence tracking", "Mastery commitment", "Leadership responsibility"]}}, "mood": {"valence": {"-1.0--0.3": {"Motivation_Enhancement": ["Mood lifting", "Emotional support", "Gentle encouragement", "Comfort provision"], "Goal_Alignment": ["Mood-sensitive goals", "Emotional healing", "Comfort objectives", "Supportive targets"], "Performance_Optimization": ["Emotional recovery", "Mood improvement", "Gentle progress", "Supportive advancement"], "Accountability_Support": ["Emotional accountability", "Supportive tracking", "Gentle commitment", "Healing responsibility"]}, "-0.3-0.3": {"Motivation_Enhancement": ["Steady encouragement", "Balanced support", "Consistent engagement", "Reliable motivation"], "Goal_Alignment": ["Balanced objectives", "Steady targets", "Consistent goals", "Reliable outcomes"], "Performance_Optimization": ["Steady progress", "Consistent improvement", "Balanced advancement", "Reliable growth"], "Accountability_Support": ["Steady accountability", "Consistent tracking", "Balanced commitment", "Reliable responsibility"]}, "0.3-1.0": {"Motivation_Enhancement": ["Energy amplification", "Enthusiasm building", "Momentum acceleration", "Success celebration"], "Goal_Alignment": ["Ambitious objectives", "High-energy targets", "Success-focused goals", "Achievement outcomes"], "Performance_Optimization": ["Accelerated progress", "Peak performance", "Excellence pursuit", "Success amplification"], "Accountability_Support": ["High-energy accountability", "Success tracking", "Achievement commitment", "Excellence responsibility"]}}, "arousal": {"-1.0--0.3": {"Performance_Optimization": ["Calming approaches", "Stress reduction", "Peaceful progress", "Relaxed advancement"], "Motivation_Enhancement": ["Gentle motivation", "Calm encouragement", "Peaceful engagement", "Relaxed support"], "Accountability_Support": ["Calm accountability", "Peaceful tracking", "Relaxed commitment", "Gentle responsibility"]}, "-0.3-0.3": {"Performance_Optimization": ["Balanced approaches", "Moderate energy", "Steady progress", "Consistent advancement"], "Motivation_Enhancement": ["Balanced motivation", "Steady encouragement", "Consistent engagement", "Moderate support"], "Accountability_Support": ["Balanced accountability", "Steady tracking", "Consistent commitment", "Moderate responsibility"]}, "0.3-1.0": {"Performance_Optimization": ["Dynamic approaches", "High energy", "Rapid progress", "Accelerated advancement"], "Motivation_Enhancement": ["Dynamic motivation", "Energetic encouragement", "High engagement", "Stimulating support"], "Accountability_Support": ["Dynamic accountability", "Energetic tracking", "High commitment", "Stimulating responsibility"]}}}, "environment": {"stress_level": {"0-30": {"Goal_Alignment": ["Comprehensive planning", "Detailed objectives", "<PERSON><PERSON> goal setting", "In-depth alignment"], "Skill_Development": ["Deep skill building", "Comprehensive learning", "Thorough competency development", "In-depth mastery"], "Performance_Optimization": ["Comprehensive optimization", "Detailed improvement", "Thorough enhancement", "In-depth excellence"], "Accountability_Support": ["Comprehensive accountability", "Detailed tracking", "Thorough commitment", "In-depth responsibility"]}, "31-70": {"Goal_Alignment": ["Focused planning", "Clear objectives", "Structured goal setting", "Organized alignment"], "Skill_Development": ["Focused skill building", "Structured learning", "Organized competency development", "Balanced mastery"], "Performance_Optimization": ["Focused optimization", "Structured improvement", "Organized enhancement", "Balanced excellence"], "Accountability_Support": ["Focused accountability", "Structured tracking", "Organized commitment", "Balanced responsibility"]}, "71-100": {"Goal_Alignment": ["Essential planning", "Priority objectives", "Stress-aware goal setting", "Crisis alignment"], "Skill_Development": ["Essential skill building", "Priority learning", "Stress-aware competency development", "Crisis mastery"], "Performance_Optimization": ["Essential optimization", "Priority improvement", "Stress-aware enhancement", "Crisis excellence"], "Accountability_Support": ["Essential accountability", "Priority tracking", "Stress-aware commitment", "Crisis responsibility"]}}, "time_pressure": {"0-30": {"Goal_Alignment": ["Long-term planning", "Strategic objectives", "Vision-based goal setting", "Future alignment"], "Skill_Development": ["Long-term skill building", "Strategic learning", "Vision-based competency development", "Future mastery"], "Performance_Optimization": ["Long-term optimization", "Strategic improvement", "Vision-based enhancement", "Future excellence"], "Accountability_Support": ["Long-term accountability", "Strategic tracking", "Vision-based commitment", "Future responsibility"]}, "31-70": {"Goal_Alignment": ["Medium-term planning", "Tactical objectives", "Balanced goal setting", "Present-future alignment"], "Skill_Development": ["Medium-term skill building", "Tactical learning", "Balanced competency development", "Present-future mastery"], "Performance_Optimization": ["Medium-term optimization", "Tactical improvement", "Balanced enhancement", "Present-future excellence"], "Accountability_Support": ["Medium-term accountability", "Tactical tracking", "Balanced commitment", "Present-future responsibility"]}, "71-100": {"Goal_Alignment": ["Immediate planning", "Urgent objectives", "Crisis goal setting", "Present alignment"], "Skill_Development": ["Immediate skill building", "Urgent learning", "Crisis competency development", "Present mastery"], "Performance_Optimization": ["Immediate optimization", "Urgent improvement", "Crisis enhancement", "Present excellence"], "Accountability_Support": ["Immediate accountability", "Urgent tracking", "Crisis commitment", "Present responsibility"]}}}}, "variable_ranges": {"trust_level": {"min": 0, "max": 100, "description": "Client's trust in coach and coaching process (Foundation: 0-39, Development: 40-69, <PERSON>y: 70-100)"}, "mood": {"valence": {"min": -1.0, "max": 1.0, "description": "Client's emotional state from negative to positive"}, "arousal": {"min": -1.0, "max": 1.0, "description": "Client's energy level from low to high activation"}}, "environment": {"stress_level": {"min": 0, "max": 100, "description": "Environmental stress affecting coaching capacity and intervention choice"}, "time_pressure": {"min": 0, "max": 100, "description": "Time constraints affecting coaching depth and goal complexity"}}}, "is_active": true}