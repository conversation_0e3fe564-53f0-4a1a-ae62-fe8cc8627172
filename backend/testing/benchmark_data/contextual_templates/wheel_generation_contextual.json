{"name": "Contextual Wheel Generation Evaluation", "description": "Comprehensive contextual evaluation template for wheel generation workflows that adapts criteria based on user trust level, mood, and environmental factors", "workflow_type": "wheel_generation", "category": "contextual", "criteria": {"Content": ["Relevance", "Personalization", "Feasibility", "Innovation"], "Tone": ["Appropriate", "Supportive", "Engaging"], "Structure": ["Clear", "Organized", "Actionable"], "Approach": ["User-centered", "Growth-oriented", "Adaptive"]}, "contextual_criteria": {"trust_level": {"0-39": {"Tone": ["Simple", "Clear", "Reassuring", "Non-threatening", "Gentle"], "Approach": ["Step-by-step", "Basic", "Gentle introduction", "Safety-focused", "Predictable"], "Content": ["Safe options", "Low-risk activities", "Familiar contexts", "Comfort zone", "Basic challenges"], "Structure": ["Highly structured", "Clear boundaries", "Simple format", "Minimal complexity"]}, "40-69": {"Tone": ["Encouraging", "Supportive", "Motivating", "Collaborative", "Warm"], "Approach": ["Guided exploration", "Moderate challenge", "Building confidence", "Skill development", "Progressive"], "Content": ["Balanced options", "Some stretch goals", "Growth-oriented", "Skill-building", "Moderate innovation"], "Structure": ["Well-organized", "Clear progression", "Balanced detail", "Moderate complexity"]}, "70-100": {"Tone": ["Collaborative", "Empowering", "Challenging", "Inspiring", "Dynamic"], "Approach": ["Advanced options", "Independent exploration", "Push boundaries", "Creative freedom", "Self-directed"], "Content": ["Ambitious goals", "Creative challenges", "Leadership opportunities", "Innovation-focused", "Complex projects"], "Structure": ["Flexible format", "Advanced organization", "Rich detail", "Complex interconnections"]}}, "mood": {"valence": {"-1.0--0.3": {"Tone": ["Gentle", "Understanding", "Patient", "Compassionate", "Nurturing"], "Content": ["Mood-lifting activities", "Comfort-focused", "Low pressure", "Emotional support", "Self-care"], "Approach": ["Extra sensitivity", "Emotional validation", "Gradual engagement", "Stress reduction"]}, "-0.3-0.3": {"Tone": ["Balanced", "Steady", "Reliable", "Calm", "Neutral-positive"], "Content": ["Stable activities", "Routine-friendly", "Moderate engagement", "Balanced challenge"], "Approach": ["Consistent support", "Steady progress", "Reliable structure"]}, "0.3-1.0": {"Tone": ["Enthusiastic", "Energetic", "Positive", "Celebratory", "Vibrant"], "Content": ["High-energy options", "Social activities", "Achievement-focused", "Dynamic challenges"], "Approach": ["Energy matching", "Momentum building", "Celebration of progress"]}}, "arousal": {"-1.0--0.3": {"Content": ["Calming activities", "Relaxation-focused", "Peaceful options", "Meditative practices", "Quiet pursuits"], "Approach": ["Soothing guidance", "Gentle pacing", "Stress relief", "Mindfulness integration"]}, "-0.3-0.3": {"Content": ["Moderate-energy activities", "Balanced engagement", "Steady-paced options", "Comfortable intensity"], "Approach": ["Balanced energy", "Sustainable pacing", "Moderate stimulation"]}, "0.3-1.0": {"Content": ["Stimulating activities", "Dynamic options", "Exciting challenges", "High-energy pursuits", "Active engagement"], "Approach": ["Energy channeling", "Dynamic interaction", "Stimulating guidance"]}}}, "environment": {"stress_level": {"0-30": {"Approach": ["Detailed explanations", "Comprehensive options", "Thorough planning", "In-depth exploration"], "Content": ["Complex activities", "Multi-step processes", "Learning opportunities", "Detailed projects"], "Structure": ["Rich detail", "Comprehensive coverage", "Multiple options", "Thorough analysis"]}, "31-70": {"Approach": ["Balanced detail", "Focused suggestions", "Moderate complexity", "Streamlined guidance"], "Content": ["Manageable challenges", "Clear outcomes", "Structured activities", "Focused goals"], "Structure": ["Organized presentation", "Clear priorities", "Balanced information", "Efficient format"]}, "71-100": {"Approach": ["Concise", "Essential-only", "Quick decisions", "Simplified guidance"], "Content": ["Simple activities", "Immediate gratification", "Stress-relief focused", "Quick wins"], "Structure": ["Minimal complexity", "Clear priorities", "Simple format", "Essential information only"]}}, "time_pressure": {"0-30": {"Content": ["Long-term projects", "Detailed activities", "Skill development", "Comprehensive learning"], "Approach": ["Thorough exploration", "Patient guidance", "Long-term planning", "Deep engagement"]}, "31-70": {"Content": ["Medium-duration activities", "Balanced time commitment", "Moderate planning", "Flexible timing"], "Approach": ["Balanced pacing", "Moderate urgency", "Flexible scheduling", "Adaptive timing"]}, "71-100": {"Content": ["Quick activities", "Immediate options", "Time-efficient choices", "Rapid results"], "Approach": ["Urgent guidance", "Quick decisions", "Immediate action", "Time-sensitive support"]}}}}, "variable_ranges": {"trust_level": {"min": 0, "max": 100, "description": "User's trust level in the system and recommendations (Foundation: 0-39, Expansion: 40-69, Integration: 70-100)"}, "mood": {"valence": {"min": -1.0, "max": 1.0, "description": "Emotional valence from negative to positive"}, "arousal": {"min": -1.0, "max": 1.0, "description": "Emotional arousal from calm to excited"}}, "environment": {"stress_level": {"min": 0, "max": 100, "description": "Current environmental stress level (Low: 0-30, Moderate: 31-70, High: 71-100)"}, "time_pressure": {"min": 0, "max": 100, "description": "Time pressure in current environment (Relaxed: 0-30, Moderate: 31-70, Urgent: 71-100)"}}}, "is_active": true}