{"name": "test_workflow_wheel_generation", "description": "Test scenario for wheel generation workflow", "agent_role": "workflow", "input_data": {"user_message": "I need some new activities", "context_packet": {"workflow_type": "wheel_generation", "trust_level": 70, "text": "I need some new activities"}}, "metadata": {"workflow_type": "wheel_generation", "template_reference": "workflow_wheel_generation_criteria"}, "tags": ["workflow", "wheel_generation", "test"], "situation": {"workflow_type": "wheel_generation", "text": "The user needs new activities for their wheel.", "context": "The user has completed their previous activities and is ready for new challenges."}}