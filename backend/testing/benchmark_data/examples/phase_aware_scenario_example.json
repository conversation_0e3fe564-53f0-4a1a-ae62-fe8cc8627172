{"name": "phase_aware_mentor_discussion", "description": "Example phase-aware scenario for mentor in discussion workflow", "agent_role": "mentor", "input_data": {"user_message": "I'm feeling stuck with my current activities", "context_packet": {"workflow_type": "discussion", "trust_level": 50, "text": "I'm feeling stuck with my current activities"}}, "metadata": {"workflow_type": "discussion", "evaluation_criteria_by_phase": {"foundation": {"Clarity": ["Is the response clear and easy to understand?", "Does it avoid unnecessary jargon or complexity?", "Is the structure logical and easy to follow?"], "Supportiveness": ["Does the response acknowledge the user's feelings?", "Does it provide basic emotional support?", "Is the tone warm and encouraging?"], "Guidance": ["Does it provide simple, actionable advice?", "Are the suggestions concrete and specific?", "Does it focus on immediate, practical steps?"]}, "expansion": {"Depth": ["Does the response explore underlying issues?", "Does it connect feelings to potential causes?", "Does it provide more nuanced insights?"], "Growth": ["Does it challenge the user appropriately?", "Does it encourage reflection and self-discovery?", "Does it suggest growth-oriented activities?"], "Personalization": ["Does it reference the user's specific situation?", "Does it tailor advice to the user's needs?", "Does it build on previous interactions?"]}, "integration": {"Synthesis": ["Does it integrate multiple perspectives?", "Does it connect the current situation to broader patterns?", "Does it help the user see the bigger picture?"], "Philosophical Depth": ["Does it explore deeper meaning and purpose?", "Does it connect to values and life philosophy?", "Does it encourage higher-order thinking?"], "Transformative Guidance": ["Does it suggest transformative approaches?", "Does it encourage fundamental shifts in perspective?", "Does it support deep personal growth?"]}}, "evaluator_models": ["openai/gpt-4o"], "tool_expectations": {"get_user_profile": {"min_calls": 1, "max_calls": 2}, "get_user_activities": {"min_calls": 1, "max_calls": 2}}}, "is_active": true, "version": 1, "is_latest": true, "situation": {"workflow_type": "discussion", "text": "I'm feeling stuck with my current activities", "context": "The user has been doing the same activities for a while and is feeling bored and unmotivated."}}