# Developer Guide

Last Updated: 2025-04-24

This guide provides comprehensive information for developers working on the backend of the Goali project.

## Getting Started

The backend is built using Django (Python). Key setup and operational details are managed via Docker.

**Prerequisites:**
- Docker and Docker Compose installed.
- Python environment (matching the version in `Dockerfile`, currently 3.12).

**Running the Development Environment:**
The primary way to run the backend services (web server, database, Redis, Celery) is through Docker Compose:
```bash
# Build and start all services in detached mode
docker-compose up -d --build

# Stop services
docker-compose down
```
Refer to the `docker-compose.yml` file for service details, ports, and environment variables.

**Generating Model Graphs:**
To visualize the database schema (models defined in `apps/*/models.py`):
```bash
# Ensure you have graphviz installed locally
# Run inside the backend container or a local env with dependencies
python manage.py graph_models user activity main -o models.png
```

## Development Environment

The development environment is containerized using Docker and managed by `docker-compose.yml`.

**Key Components:**
- **`Dockerfile`:** Defines the base Python image (3.12-slim), installs system dependencies (like `gcc`, `libpq-dev`, `netcat-openbsd`), Python packages from `requirements.txt`, and development tools (`debugpy`, `django-debug-toolbar`, `ipython`). It sets up the working directory and copies the project code.
- **`docker-compose.yml`:** Defines the services needed for development and testing:
    - `web`: Runs the Django development server, exposing port 8000 (app) and 5678 (debugpy). Mounts the local code for live reloading. Depends on `db` and `redis`. Uses `.env` and `.env.dev`.
    - `db`: PostgreSQL 15 database service. Data is persisted in the `postgres_data` volume.
    - `redis`: Redis service for Celery broker and Channels layer backend.
    - `celery`: Runs the Celery worker for background tasks, exposing port 5680 for debugging. Depends on `redis`, `db`, and `test-db`.
    - `web-test`: Service specifically configured for running backend tests using `pytest`. Uses `.env.test` and `config.settings.test`.
    - `debug-tests`: Extends `web-test` to allow debugging tests with `debugpy` on port 5681.
    - `test-db`: Separate PostgreSQL database instance for testing, using the `postgres_test_data` volume.
- **`.env` / `.env.dev` / `.env.test`:** Files containing environment variables for different configurations (base, development, testing).
- **`entrypoint.sh`:** Script executed when the `web` container starts, handling tasks like waiting for the database and running initial setup commands.

**Debugging:**
- Debugging is enabled using `debugpy`.
- Attach your debugger (e.g., VS Code) to the relevant service port:
    - Django Web Server: Port 5678
    - Celery Worker: Port 5680
    - Pytest Runner: Port 5681 (when using `debug-tests` service)
- VS Code launch configurations are provided in `.vscode/launch.json`.

## Database Schema

**Schema Management:**
- **Important:** This project **now uses standard Django migrations**.
- The database schema is defined in the `models.py` files within each Django app (`apps/main/`, `apps/user/`, `apps/activity/`).
- Schema creation and updates are handled by **idempotent seeding commands** located in `apps/main/management/commands/seed_db_*.py`.
- These seeders are run via the `run_seeders` management command, which tracks applied commands in the `AppliedSeedingCommand` model to prevent re-execution.
- Running `python manage.py makemigrations` or `python manage.py migrate` will likely not work as expected and may cause issues. Any schema changes require updating the relevant `models.py` file AND creating/updating a corresponding seeder command.

**Key Models:**
*   **`apps/main/models.py`**:
    *   `LLMConfig`: Stores configurations for different LLMs.
    *   `AgentTool`: Defines tools available to agents (schemas, function paths).
    *   `GenericAgent`: Blueprint for agent roles (instructions, schemas, tools, permissions).
    *   `AgentRun`: Records a specific execution of an agent.
    *   `AgentMetric`: Records benchmark metric measurements for agent runs.
    *   `AgentMemory`: Persistent memory for agents per user.
    *   `AgentRecommendation`: Recommendations made by agents for data updates.
    *   `BenchmarkScenario`, `BenchmarkRun`, `BenchmarkTag`: Models for the agent benchmarking system.
    *   `EvaluationCriteriaTemplate`: Templates for evaluating benchmark results.
    *   `Wheel`, `WheelItem`: Models for the activity selection game mechanic.
    *   `AppliedSeedingCommand`: Tracks executed seeding commands.
*   **`apps/user/models.py`**:
    *   `UserProfile`: Core user model linking to Django's auth user.
    *   `Demographics`: Basic user demographic information.
    *   `UserGoal`, `Intention`, `Aspiration`: User goals and their types.
    *   `Inspiration`, `GoalInspiration`: Sources of motivation for goals.
    *   `TrustLevel`: User's trust level across domains.
    *   `Preference`: User likes, dislikes, habits.
    *   `Belief`, `GenericBelief`, `BeliefEvidence`, `BeliefInfluence`: Models for user beliefs and their structure.
    *   `CurrentMood`: Tracks the user's immediate emotional state.
    *   `GenericTrait`, `UserTraitInclination`: HEXACO personality traits and user's inclination.
    *   `GenericEnvironment`, `UserEnvironment`: Environment templates and user-specific instances.
    *   `UserEnvironmentPhysicalProperties`, `UserEnvironmentSocialContext`, `UserEnvironmentActivitySupport`, `UserEnvironmentPsychologicalQualities`: Detailed properties of user environments.
    *   `Inventory`, `GenericResource`, `UserResource`: Resources available to the user.
    *   `GenericSkill`, `Skill`: Skill templates and user's proficiency.
    *   `GenericUserLimitation`, `UserLimitation`: User constraints or challenges.
    *   `SkillAttribute`, `SkillDefinition`, `SkillAttributeComposition`, `SkillDomainApplication`, `UserAttributeProficiency`: Models for the skill composition system.
*   **`apps/activity/models.py`**:
    *   `Tag`, `TaggedItem`: Generic tagging system for any model.
    *   `GenericDomain`, `EntityDomainRelationship`: Hierarchical domains and their relationship to entities.
    *   `BaseActivity` (Abstract): Common fields for activities.
    *   `GenericActivity`: Template activities in the catalog.
    *   `ActivityTailored`: Activities personalized for a specific user.
    *   `ActivityTailoredQueryIndexes`: Materialized indexes for faster activity recommendations.
    *   `ActivityInfluencedBy`: Tracks how goals, beliefs, etc., influence a tailored activity.
    *   `GenericActivityResourceRequirement`, `GenericActivityUserRequirement`, `GenericActivityEnvRequirement`: Requirements for generic activities.
    *   `ActivityUserRequirement`, `ActivityEnvRequirement`, `ActivityTailoredResourceRequirement`: Requirements specific to tailored activities.

Refer to the docstrings within each `models.py` file for detailed field descriptions and examples. Use the `graph_models` command mentioned in "Getting Started" to visualize relationships.

## Agent System

The backend features a multi-agent AI system built using LangGraph integrated within Django.

**Core Concepts:**
- **Agents (`apps/main/agents/`)**: Defined by `GenericAgent` models and implemented as classes (e.g., `MentorAgent`, `StrategyAgent`). The base class is `LangGraphAgent`.
- **Workflows (`apps/main/graphs/`)**: LangGraph graphs define the flow of interaction between agents (e.g., `discussion_graph.py`).
- **Tools (`apps/main/agents/tools/`)**: Agents use tools (`AgentTool` model) to interact with the system (e.g., query the database, get user profile). Tools have defined input/output schemas.
- **Dispatcher (`apps/main/services/conversation_dispatcher.py`)**: Receives messages (typically via WebSockets), classifies user intent, and initiates the appropriate LangGraph workflow as an asynchronous Celery task.
- **Asynchronous Execution**: Agent workflows run in the background using Celery (`apps/main/tasks/`). The initial response confirms workflow start; the final result is sent back via the `EventService` upon task completion.
- **State & Memory**: Agents maintain state within a LangGraph run (`AgentRun` model) and can persist information across runs using `AgentMemory`.

**Flow:**
1.  User message received (e.g., via WebSocket).
2.  `UserSessionConsumer` passes message to `ConversationDispatcher`.
3.  Dispatcher classifies intent and selects a workflow graph.
4.  Dispatcher launches the graph execution as a Celery task (`agent_tasks.py`).
5.  An initial confirmation is sent back to the user.
6.  The LangGraph workflow executes, involving multiple agent nodes and tool calls.
7.  Agents interact with services (`apps/main/services/`) and potentially the database (via tools or async ORM calls).
8.  Upon completion, the final result/response is emitted via `EventService.emit_event`.
9.  `UserSessionConsumer` receives the event and sends the final response to the user.

For more detailed agent descriptions and workflow diagrams, refer to the documentation in `docs/backend/agents/` and `docs/backend/agents/flows/`.

## Common Development Tasks

**Running Tests:**
- Use the `web-test` or `debug-tests` services in `docker-compose.yml`.
- Execute tests via `docker-compose exec`:
  ```bash
  # Run all tests in the 'main' app
  docker-compose exec -T web-test pytest apps/main/

  # Run a specific test file
  docker-compose exec -T web-test pytest apps/main/tests/test_agents/test_mentor_agent.py

  # Run tests with debugger attached (using debug-tests service)
  # Set PYTEST_ARGS for specific tests if needed, e.g., export PYTEST_ARGS="apps/main/tests/test_agents/test_mentor_agent.py"
  docker-compose up debug-tests
  # Attach debugger to port 5681
  ```
- Refer to `backend/README_TESTING.md` and `docs/testing/TESTING_GUIDE.md` for more details on the testing strategy.

**Adding a New Agent Tool:**
1.  Define the tool function in `apps/main/agents/tools/`.
2.  Create input/output JSON schemas for the tool.
3.  Add a record to the `AgentTool` model (usually via a seeder command). Ensure `function_path` is correct.
4.  Update the relevant `GenericAgent` model(s) via seeders to grant access to the new tool.
5.  Register the tool function in `apps/main/agents/tools/tools_util.py`'s registration mechanism.

**Modifying the Database Schema:**
1.  Update the relevant model definition in `apps/*/models.py`.
2.  Update or create a corresponding **idempotent seeder command** in `apps/main/management/commands/seed_db_*.py` to reflect the changes. Remember, no Django migrations!
3.  Run the seeders (potentially after resetting the DB if necessary for testing): `docker-compose exec web python manage.py run_seeders`

**Working with Async Code:**
- Use `async`/`await` for I/O-bound operations.
- Access the database from async code using `channels.db.database_sync_to_async`.
- Be mindful of Django's limitations with async ORM calls (see Troubleshooting).
- Emit events using `EventService.emit_event` (async) or `EventService.emit_event_sync` (sync, e.g., from Celery tasks).

## Troubleshooting

**`django.core.exceptions.AppRegistryNotReady` Errors:**
- **Cause:** This often happens during test discovery or execution when Django models or other app-dependent code are imported at the module level (top-level imports) before `pytest-django` has fully initialized the Django app registry. The issue can be deep in the import chain.
- **Solution:** Strictly enforce deferred imports for all Django-related components (models, services using models, utils using models, etc.). Import them *inside* the function or method where they are needed, not at the top of the file. Use string literals for type hints if necessary (e.g., `user: 'UserProfile'`).
- **Debugging:** Trace the import chain from the failing test or `conftest.py` file to find the module performing the premature top-level Django import. Check services, utils, testing helpers, and the code under test itself. `PLANNING.md` contains a detailed investigation log for this issue related to `get_user_profile_tool.py`.

**Benchmark Test Fixture Issues:**
- **Cause:** The `BenchmarkScenario` model has a `unique_together` constraint on `('name', 'version')` fields. When multiple tests create scenarios with the same name and version (typically version=1), this causes database integrity errors.
- **Solution:** Use the utility functions in `apps/main/tests/utils.py` to create test scenarios with unique names:
  ```python
  # Import the utility function
  from apps.main.tests.utils import create_test_scenario

  # Create a scenario with a unique name
  scenario = create_test_scenario(
      name="My Test Scenario",  # Optional - will generate unique name if not provided
      agent_role="mentor",
      input_data={"query": "Hello"},
      metadata={"expected_quality_criteria": {...}}
  )
  ```
- For async tests, use the async version:
  ```python
  # Import the async utility function
  from apps.main.tests.utils import create_test_scenario_async

  # Create a scenario with a unique name
  scenario = await create_test_scenario_async(
      name="My Test Scenario",  # Optional - will generate unique name if not provided
      agent_role="mentor",
      input_data={"query": "Hello"},
      metadata={"expected_quality_criteria": {...}}
  )
  ```
- **Debugging:** If you see `duplicate key value violates unique constraint "main_benchmarkscenario_name_version_f48b9c05_uniq"` errors, check if your test is creating scenarios with non-unique names.

**Async Database Access Issues:**
- Ensure all ORM calls within an `async` function are wrapped with `@database_sync_to_async`.
- Avoid calling `async_to_sync` *inside* a function decorated with `@database_sync_to_async`. Perform sync DB operations first if needed, then subsequent async calls.
- Load related data eagerly (`select_related`, `prefetch_related`) within the sync function passed to `database_sync_to_async` where possible to minimize separate async DB calls.

**WebSocket Issues:**
- Use the Admin WebSocket Tester tool (`/admin/websocket_tester/`) for manual testing.
- Check `UserSessionConsumer` logic in `apps/main/consumers.py`.
- Ensure messages adhere to the format defined in `docs/api/ApiContract.md`.
- Verify `EventService` is used correctly to send messages back to the client, especially from Celery tasks.

**Debugging Containerized Services:**
- Ensure the correct ports are exposed in `docker-compose.yml`.
- Use `docker-compose logs <service_name>` to view container logs.
- Attach a debugger using the appropriate port and VS Code launch configurations.

