FROM python:3.12-slim

# Prevent Python from writing .pyc files & enable unbuffered output
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    # Development settings
    DJANGO_DEBUG=1 \
    # Pip configuration
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

WORKDIR /usr/src/app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libpq-dev \
    netcat-openbsd \
    dos2unix \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt && \
    # Install development and debugging tools
    pip install debugpy django-debug-toolbar ipython

# Copy the entire project code to the working directory
COPY . .

# Create frontend/dist directory structure for static files
# This directory is referenced in STATICFILES_DIRS but doesn't exist by default
WORKDIR /usr/src/app
RUN mkdir -p frontend/dist/css frontend/dist/js && \
    echo "/* Placeholder CSS file */" > frontend/dist/css/placeholder.css && \
    echo "// Placeholder JavaScript file" > frontend/dist/js/placeholder.js && \
    echo "# Frontend Dist Directory\n\nThis directory was automatically created during Docker build.\n\nIt exists to prevent Django from raising errors when looking for static files in the frontend/dist directory." > frontend/dist/README.md

# Fix line endings and make entrypoint executable
RUN dos2unix entrypoint.sh && \
    chmod +x entrypoint.sh


# Set the default command to run the entrypoint script
CMD ["/usr/src/app/entrypoint.sh"]