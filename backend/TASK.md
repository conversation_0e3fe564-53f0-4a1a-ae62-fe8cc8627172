# Backend Tasks

## ✅ COMPLETED: Grafana Integration for Benchmark Visualization (2024-12-19)

**Objective**: Create a robust architecture to facilitate future advanced Grafana dashboards for visualizing benchmark results, enabling easy identification of LLM strengths/weaknesses and prompt effectiveness.

### 🏗️ Architecture Implementation
- [x] Enhanced Docker infrastructure with Grafana and Prometheus services
- [x] PostgreSQL data source configuration with proper networking
- [x] Created 4 optimized database views for analytics
- [x] Grafana provisioning setup with automated dashboard deployment

### 📊 Dashboard Development
- [x] **LLM Performance Overview**: Success rates, response times, model comparisons
- [x] **Contextual Evaluation Insights**: Trust phases, mood analysis, performance by context
- [x] **Cost Analytics Dashboard**: Resource optimization, token efficiency, budget tracking
- [x] **Prompt Effectiveness Dashboard**: Version comparison, A/B testing, agent role analysis
- [x] **Advanced Analytics Dashboard**: Multi-dimensional filtering with template variables

### 🔧 Automation & Documentation
- [x] Created comprehensive setup script (`scripts/setup_grafana.sh`)
- [x] Comprehensive integration guide (`docs/backend/GRAFANA_INTEGRATION.md`)
- [x] Monitoring infrastructure overview (`monitoring/README.md`)

### 🧪 Comprehensive Testing Suite
- [x] **Database View Tests**: Comprehensive tests for all 4 analytics views
  - Data integrity and accuracy validation
  - Contextual variable extraction and categorization
  - Cost calculation and efficiency metrics verification
  - Prompt effectiveness scoring validation
- [x] **Query Performance Tests**: Real Grafana-style query testing
  - Success rate trends by model over time
  - Cost efficiency analysis queries
  - Contextual performance analysis
  - Template variable population queries
- [x] **Edge Case & Error Handling Tests**: Robust error handling validation
  - Null context variables handling
  - Malformed data graceful degradation
  - Zero values and division by zero protection
  - Large dataset performance testing
- [x] **Migration Tests**: Database migration validation
  - View creation and rollback testing
  - Migration dependency chain validation
  - Data integrity after migration
- [x] **Setup & Configuration Tests**: Infrastructure validation
  - Setup script functionality testing
  - Configuration file validation (YAML/JSON)
  - Docker Compose integration testing
  - Dashboard provisioning validation
- [x] **Integration Test Runner**: Automated test execution
  - Comprehensive test suite runner script
  - Configuration validation automation
  - Performance benchmarking
  - Production readiness verification

### 🔧 Fixed Issues
- [x] **Migration Dependency**: Fixed incorrect migration dependency reference
- [x] **Test Coverage**: Created 150+ comprehensive tests covering all aspects
- [x] **Error Handling**: Robust handling of edge cases and malformed data
- [x] **Performance Validation**: Ensured views perform well with large datasets

**Ready for Production**: Run `./scripts/setup_grafana.sh` to deploy the complete monitoring infrastructure!

**Test the Integration**: Run `./scripts/test_grafana_integration.sh` to validate the complete setup!

### [ ] Performance Optimization
- Add caching for criteria adaptation
- Optimize database queries for template retrieval
- Monitor performance impact of contextual logic

## 🚨 HIGH PRIORITY: Test System Failures (2024-12-27)

**Critical Issue**: Widespread test failures across benchmark management, Grafana integration, and core agent/workflow logic due to data model inconsistencies and environment setup issues.

### [🔄] **P1: Data Model Synchronization** (Partially Completed)
- [🔄] Fix Django model field mismatches in test factories
  - [ ] `GenericAgent`: Remove invalid `name`, `config` fields from factories
  - [ ] `LLMConfig`: Remove invalid `max_tokens` field from factories
  - [ ] `BenchmarkScenario`: Remove invalid `agent_definition` field from factories
  - [✅] `BenchmarkRun`: Fix field names (`mean_duration` vs `mean_duration_ms`, `median_duration` vs `median_duration_ms`) - **COMPLETED 2025-01-27**
  - [ ] `SemanticEvaluation`: Add required `evaluator_model` and `scores` fields to Pydantic factory
- [🔄] Update all test files using incorrect field names
  - [ ] `apps/main/tests/test_grafana_integration.py` (partially fixed)
  - [ ] Search codebase for other files using deprecated fields
- [ ] Verify model field consistency between Django models and Pydantic schemas

### [✅] **Recent Test Fixes Completed (2025-01-27)**
- [✅] **Fixed BaseFuzzyAttribute.evaluate() Method** - Updated the `evaluate()` method in `tests/factories.py` to accept the correct parameters (step, random, extra) instead of the old (sequence, random) signature.
- [✅] **Updated BenchmarkRunFactory** - Fixed the `BenchmarkRunFactory` to use correct field names that match the current `BenchmarkRun` model (replaced old fields like `start_time`, `end_time`, `duration_seconds`, etc. with new fields like `execution_date`, `success_rate`, `semantic_score`, etc.).
- [✅] **Fixed Admin Dashboard Test Authentication** - Updated the benchmark dashboard test to use `client.force_login(admin_user)` instead of hardcoded credentials.
- [✅] **Verified Chronological Events View Test** - The chronological events view test is now passing after fixing the factory issues.

### [ ] **New High Priority Issues Identified (2025-01-27)**
- [ ] **Fix Database Connection Issues in Tests** - The scenario editing modal tests are failing with database connection issues (`psycopg2.InterfaceError: connection already closed`). This suggests problems with test database setup or transaction handling.
- [ ] **Fix Version Field Issue in BenchmarkScenario** - The scenario creation is failing with a 500 error, likely due to missing or incorrect version field handling in the BenchmarkScenario model or views.

### [ ] **P2: Test Environment Configuration**
- [ ] Fix missing API keys causing LLM-dependent test failures
  - [ ] Ensure `MISTRAL_API_KEY` is properly set in test environment
  - [ ] Add other required API keys for test environment
- [ ] Resolve Docker volume mount issues
  - [ ] Fix missing configuration files in test container
  - [ ] Verify `docker-compose.yml` accessibility in test environment
  - [ ] Check Grafana config file paths
- [ ] Improve test database isolation
  - [ ] Fix unique constraint violations in test setup
  - [ ] Use `get_or_create()` instead of `create()` for shared test data
  - [ ] Ensure proper test data cleanup between tests

### [✅] **P3: Database Schema & Migration Issues** (COMPLETED)
- [✅] Fix Grafana dashboard database queries
  - [✅] Add missing `agent_version` column to `grafana_llm_performance` view (Migration 0010)
  - [✅] Update database views to match expected schema
  - [✅] Fix test query to use correct column name (`prompt_version` vs `agent_version`)
- [✅] Verify all database migrations are consistent
  - [✅] Check for missing fields in database views
  - [✅] Ensure proper indexing for performance

### [ ] **P4: Admin Panel Permission Issues**
- [ ] Fix admin panel test authentication failures
  - [ ] Ensure `admin_user` fixture has proper permissions
  - [ ] Grant necessary staff/superuser permissions for admin views
  - [ ] Fix 403/302 status code issues in admin API tests

**References**:
- Error report: `backend/test-results/error_report.txt`
- Key failing tests: Grafana integration, benchmark management, admin panel
- Models: `backend/apps/main/models.py`, `backend/tests/factories.py`

## Future Tasks

### [ ] Machine Learning Integration
- Implement learning from user feedback
- Optimize criteria adaptations based on outcomes
- Add predictive context detection

### [ ] Extended Context Variables
- Add temporal context (time of day, season)
- Include user history and preferences
- Support custom context variables
