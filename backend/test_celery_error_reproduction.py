#!/usr/bin/env python3
"""
Test to reproduce the exact Celery error that's still happening.
This test should fail to demonstrate the issue, then pass after the fix.
"""

import os
import asyncio
import django
from unittest.mock import patch, MagicMock

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')
django.setup()

from apps.main.agents.tools.tools_util import get_tool_registry_info
from django.core.exceptions import SynchronousOnlyOperation


def test_celery_error_reproduction():
    """
    Test that reproduces the exact error from Celery logs.
    This simulates the exact scenario where the function is called
    from an async context and should fail.
    """
    print("Testing Celery error reproduction...")
    
    async def simulate_celery_worker():
        """Simulate the exact Celery worker environment."""
        
        # Mock the AgentTool.objects.filter to return a queryset that fails on iteration
        with patch('apps.main.models.AgentTool.objects') as mock_objects:
            # Create a mock queryset
            mock_queryset = MagicMock()
            mock_objects.filter.return_value = mock_queryset
            
            # Make the queryset raise SynchronousOnlyOperation when iterated
            def raise_sync_error():
                raise SynchronousOnlyOperation("You cannot call this from an async context - use a thread or sync_to_async.")
            
            # The error happens on iteration, not on filter()
            mock_queryset.__iter__ = raise_sync_error
            
            # This should catch the error and return mock data
            try:
                result = get_tool_registry_info()
                
                # Check if we got mock data (should be a dict with tools)
                if isinstance(result, dict) and len(result) > 0:
                    print(f"✅ Successfully handled SynchronousOnlyOperation, got {len(result)} mock tools")
                    # Verify it's actually mock data by checking for known mock tools
                    if 'analyze_psychological_state' in result:
                        print("✅ Confirmed: returned mock tool registry")
                        return True
                    else:
                        print(f"❌ Got unexpected tools: {list(result.keys())}")
                        return False
                else:
                    print(f"❌ Expected non-empty dict but got: {result}")
                    return False
                    
            except SynchronousOnlyOperation as e:
                print(f"❌ SynchronousOnlyOperation was not caught by the function: {e}")
                return False
            except Exception as e:
                print(f"❌ Got unexpected error: {e}")
                import traceback
                traceback.print_exc()
                return False
    
    # Run in async context to simulate Celery worker
    return asyncio.run(simulate_celery_worker())


def test_multiple_calls():
    """
    Test multiple calls to simulate what happens in the actual benchmark.
    The logs show the function is called multiple times.
    """
    print("Testing multiple calls in async context...")
    
    async def simulate_multiple_calls():
        """Simulate multiple calls like in the benchmark."""
        
        with patch('apps.main.models.AgentTool.objects') as mock_objects:
            mock_queryset = MagicMock()
            mock_objects.filter.return_value = mock_queryset
            
            def raise_sync_error():
                raise SynchronousOnlyOperation("You cannot call this from an async context - use a thread or sync_to_async.")
            
            mock_queryset.__iter__ = raise_sync_error
            
            # Call the function multiple times like in the benchmark
            results = []
            for i in range(3):
                try:
                    result = get_tool_registry_info()
                    results.append(result)
                    print(f"Call {i+1}: Got {len(result)} tools")
                except Exception as e:
                    print(f"Call {i+1}: Failed with {e}")
                    return False
            
            # All calls should succeed and return mock data
            if all(isinstance(r, dict) and len(r) > 0 for r in results):
                print("✅ All multiple calls succeeded")
                return True
            else:
                print("❌ Some calls failed or returned empty data")
                return False
    
    return asyncio.run(simulate_multiple_calls())


def test_actual_benchmark_scenario():
    """
    Test the actual scenario from the benchmark system.
    This simulates what happens when the benchmark system calls get_tool_registry_info.
    """
    print("Testing actual benchmark scenario...")
    
    async def simulate_benchmark():
        """Simulate the benchmark calling the function."""
        
        # Don't mock anything - test the actual function in async context
        # This should demonstrate if our fix is working
        try:
            result = get_tool_registry_info()
            
            if isinstance(result, dict):
                print(f"✅ Benchmark scenario: Got {len(result)} tools without error")
                return True
            else:
                print(f"❌ Benchmark scenario: Expected dict but got {type(result)}")
                return False
                
        except SynchronousOnlyOperation as e:
            print(f"❌ Benchmark scenario: SynchronousOnlyOperation not handled: {e}")
            return False
        except Exception as e:
            print(f"❌ Benchmark scenario: Unexpected error: {e}")
            return False
    
    return asyncio.run(simulate_benchmark())


def main():
    """Run all tests to reproduce the Celery error."""
    print("Reproducing Celery async context errors...\n")
    
    # Test 1: Basic error reproduction
    print("=== Test 1: Basic Celery error reproduction ===")
    test1_passed = test_celery_error_reproduction()
    print()
    
    # Test 2: Multiple calls
    print("=== Test 2: Multiple calls simulation ===")
    test2_passed = test_multiple_calls()
    print()
    
    # Test 3: Actual benchmark scenario
    print("=== Test 3: Actual benchmark scenario ===")
    test3_passed = test_actual_benchmark_scenario()
    print()
    
    if test1_passed and test2_passed and test3_passed:
        print("🎉 All tests passed! The fix is working correctly.")
        return True
    else:
        print("❌ Some tests failed. The fix needs improvement.")
        print("This demonstrates the issue that needs to be fixed.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
