#!/usr/bin/env python
"""
Test script to verify that benchmark errors are properly displayed in the UI.
This script simulates the error conditions and tests the WebSocket error emission.
"""

import os
import sys
import django
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock
from asgiref.sync import sync_to_async

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from apps.main.services.benchmark_manager import AgentBenchmarker
from apps.main.services.event_service import EventService
from apps.main.models import BenchmarkScenario, LLMConfig


async def test_missing_default_llm_config_error():
    """Test that missing default LLMConfig error is properly emitted via WebSocket."""
    print("🧪 Testing missing default LLMConfig error emission...")

    # Temporarily hide the default LLMConfig to simulate the error
    default_config = await sync_to_async(LLMConfig.objects.filter(is_default=True, is_evaluation=False).first)()
    if default_config:
        print(f"   Found default config: {default_config.name}")
        # Temporarily set it to non-default
        default_config.is_default = False
        await sync_to_async(default_config.save)()
        print("   Temporarily disabled default config")

    try:
        # Create a mock event service to capture emitted events
        mock_event_service = AsyncMock()

        # Get a test scenario
        scenario = await sync_to_async(BenchmarkScenario.objects.filter(is_active=True).first)()
        if not scenario:
            print("   ❌ No active benchmark scenarios found")
            return False

        print(f"   Using scenario: {scenario.name}")

        # Create benchmark manager with mock event service
        manager = AgentBenchmarker()
        manager.event_service = mock_event_service

        # Try to run benchmark - this should trigger the missing default config error
        try:
            await manager.run_benchmark(
                scenario_id=scenario.id,
                params={
                    'runs': 1,
                    'warmup_runs': 0,
                    'semantic_evaluation': False
                },
                user_profile_id=1  # Mock user ID
            )
            print("   ❌ Expected ValueError but benchmark ran successfully")
            return False
        except ValueError as e:
            if "no default configuration is available" in str(e).lower():
                print(f"   ✅ Got expected ValueError: {e}")

                # Check if error was emitted via WebSocket
                if mock_event_service.emit_debug_info.called:
                    call_args = mock_event_service.emit_debug_info.call_args
                    print(f"   ✅ WebSocket error emission called with:")
                    print(f"      Level: {call_args.kwargs.get('level')}")
                    print(f"      Message: {call_args.kwargs.get('message')}")
                    print(f"      Source: {call_args.kwargs.get('source')}")
                    details = call_args.kwargs.get('details', {})
                    print(f"      Details: {json.dumps(details, indent=8)}")
                    return True
                else:
                    print("   ❌ WebSocket error emission was not called")
                    return False
            else:
                print(f"   ❌ Got unexpected ValueError: {e}")
                return False
        except Exception as e:
            print(f"   ❌ Got unexpected exception: {e}")
            return False

    finally:
        # Restore the default config
        if default_config:
            default_config.is_default = True
            await sync_to_async(default_config.save)()
            print("   Restored default config")


async def test_api_error_emission():
    """Test that API errors are properly emitted via WebSocket."""
    print("\n🧪 Testing API error emission...")

    # This would normally be tested by making an actual API call to the BenchmarkRunView
    # For now, we'll just verify the EventService can emit debug info
    try:
        await EventService.emit_debug_info(
            level='error',
            message='Test API error: Missing Mistral API key',
            source='TestScript',
            details={
                'error_type': 'ValueError',
                'error_message': 'Mistral API key not provided',
                'endpoint': 'POST /admin/benchmarks/api/run/',
                'test': True
            },
            user_profile_id=1
        )
        print("   ✅ EventService.emit_debug_info() executed successfully")
        return True
    except Exception as e:
        print(f"   ❌ EventService.emit_debug_info() failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting benchmark error display tests...\n")

    # Check if default configs exist
    default_agent_config = await sync_to_async(LLMConfig.objects.filter(is_default=True, is_evaluation=False).first)()
    default_eval_config = await sync_to_async(LLMConfig.objects.filter(is_default=True, is_evaluation=True).first)()

    print(f"📊 Current LLMConfig status:")
    print(f"   Default agent config: {default_agent_config.name if default_agent_config else 'None'}")
    print(f"   Default eval config: {default_eval_config.name if default_eval_config else 'None'}")
    print()

    # Run tests
    test1_result = await test_missing_default_llm_config_error()
    test2_result = await test_api_error_emission()

    # Summary
    print(f"\n📋 Test Results:")
    print(f"   Missing default LLMConfig error emission: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   API error emission: {'✅ PASS' if test2_result else '❌ FAIL'}")

    if test1_result and test2_result:
        print(f"\n🎉 All tests passed! Error display should work correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Check the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
