# Generated by Django 5.2 on 2025-04-17 19:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('activity', '0001_initial'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='activitytailored',
            name='user_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tailored_activities', to='user.userprofile'),
        ),
        migrations.AddField(
            model_name='activityinfluencedby',
            name='activity_tailored',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='influences', to='activity.activitytailored'),
        ),
        migrations.AddField(
            model_name='activityenvrequirement',
            name='activity_tailored',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='env_requirements', to='activity.activitytailored'),
        ),
        migrations.AddField(
            model_name='activitytailoredqueryindexes',
            name='activity_tailored',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='query_indexes', to='activity.activitytailored'),
        ),
        migrations.AddField(
            model_name='activitytailoredresourcerequirement',
            name='activity_tailored',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resource_requirements', to='activity.activitytailored'),
        ),
        migrations.AddField(
            model_name='activitytailoredresourcerequirement',
            name='personal_resource',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tailored_resource_requirements', to='user.userresource'),
        ),
        migrations.AddField(
            model_name='activityuserrequirement',
            name='activity_tailored',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_requirements', to='activity.activitytailored'),
        ),
        migrations.AddField(
            model_name='activityuserrequirement',
            name='content_type',
            field=models.ForeignKey(help_text='The type of user resource required (Skill, Trait, UserLimitation, etc.).', on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='entitydomainrelationship',
            name='content_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddIndex(
            model_name='genericactivity',
            index=models.Index(fields=['code'], name='activity_ge_code_10facf_idx'),
        ),
        migrations.AddField(
            model_name='activitytailored',
            name='generic_activity',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tailored_activities', to='activity.genericactivity'),
        ),
        migrations.AddField(
            model_name='genericactivityenvrequirement',
            name='env',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_env_requirements', to='user.genericenvironment'),
        ),
        migrations.AddField(
            model_name='genericactivityenvrequirement',
            name='generic_activity',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='env_requirements', to='activity.genericactivity'),
        ),
        migrations.AddField(
            model_name='genericactivityresourcerequirement',
            name='generic_activity',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resource_requirements', to='activity.genericactivity'),
        ),
        migrations.AddField(
            model_name='genericactivityresourcerequirement',
            name='resource_base',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_requirements', to='user.genericresource'),
        ),
        migrations.AddField(
            model_name='genericactivityuserrequirement',
            name='content_type',
            field=models.ForeignKey(help_text='The type of entity required (trait, skill, limitation, etc.).', on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='genericactivityuserrequirement',
            name='generic_activity',
            field=models.ForeignKey(help_text='The generic activity that has this requirement.', on_delete=django.db.models.deletion.CASCADE, related_name='user_requirements', to='activity.genericactivity'),
        ),
        migrations.AddField(
            model_name='genericactivityuserrequirementsummary',
            name='generic_activity',
            field=models.OneToOneField(help_text='The generic activity this summary is for.', on_delete=django.db.models.deletion.CASCADE, related_name='user_requirements_summary', to='activity.genericactivity'),
        ),
        migrations.AddField(
            model_name='entitydomainrelationship',
            name='domain',
            field=models.ForeignKey(help_text='Domain associated with the entity', on_delete=django.db.models.deletion.CASCADE, to='activity.genericdomain'),
        ),
        migrations.AddField(
            model_name='taggeditem',
            name='content_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='taggeditem',
            name='tag',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tagged_items', to='activity.tag'),
        ),
        migrations.AddIndex(
            model_name='activityinfluencedby',
            index=models.Index(fields=['content_type', 'object_id'], name='activity_ac_content_bcd478_idx'),
        ),
        migrations.AddIndex(
            model_name='activityinfluencedby',
            index=models.Index(fields=['activity_tailored'], name='activity_ac_activit_573012_idx'),
        ),
        migrations.AddConstraint(
            model_name='activityinfluencedby',
            constraint=models.UniqueConstraint(fields=('activity_tailored', 'content_type', 'object_id'), name='unique_activity_influence'),
        ),
        migrations.AddIndex(
            model_name='activitytailoredqueryindexes',
            index=models.Index(fields=['user_profile_id', 'challenge_score'], name='activity_ac_user_pr_62900b_idx'),
        ),
        migrations.AddIndex(
            model_name='activitytailoredqueryindexes',
            index=models.Index(fields=['user_profile_id', 'domain_primary'], name='activity_ac_user_pr_d1e591_idx'),
        ),
        migrations.AddIndex(
            model_name='activitytailoredqueryindexes',
            index=models.Index(fields=['user_profile_id', 'challenge_score', 'domain_primary'], name='wheel_selection_idx'),
        ),
        migrations.AddIndex(
            model_name='activityuserrequirement',
            index=models.Index(fields=['content_type', 'object_id'], name='activity_ac_content_8f2df8_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='activityuserrequirement',
            unique_together={('activity_tailored', 'content_type', 'object_id')},
        ),
        migrations.AddConstraint(
            model_name='activitytailored',
            constraint=models.UniqueConstraint(fields=('user_profile', 'generic_activity', 'version'), name='unique_activity_version'),
        ),
        migrations.AlterUniqueTogether(
            name='genericactivityuserrequirement',
            unique_together={('generic_activity', 'content_type', 'object_id')},
        ),
        migrations.AddIndex(
            model_name='entitydomainrelationship',
            index=models.Index(fields=['content_type', 'object_id'], name='activity_en_content_fe13e2_idx'),
        ),
        migrations.AddIndex(
            model_name='entitydomainrelationship',
            index=models.Index(fields=['domain'], name='activity_en_domain__b502eb_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='entitydomainrelationship',
            unique_together={('content_type', 'object_id', 'domain')},
        ),
        migrations.AddIndex(
            model_name='taggeditem',
            index=models.Index(fields=['content_type', 'object_id'], name='activity_ta_content_ebb53f_idx'),
        ),
        migrations.AddIndex(
            model_name='taggeditem',
            index=models.Index(fields=['tag'], name='activity_ta_tag_id_458d40_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='taggeditem',
            unique_together={('tag', 'content_type', 'object_id')},
        ),
    ]
