from django.db.models.signals import post_save
from django.dispatch import receiver
from activity.models import ActivityTailored, ActivityTailoredQueryIndexes

@receiver(post_save, sender=ActivityTailored)
def update_activity_indexes(sender, instance, created, **kwargs):
    """Update materialized query indexes when an activity is created or updated."""
    from django.db import transaction
    from celery import shared_task
    
    @shared_task
    def update_indexes_task(activity_id):
        try:
            activity = ActivityTailored.objects.get(id=activity_id)
            
            # Get or create the index object
            index, created = ActivityTailoredQueryIndexes.objects.get_or_create(
                activity_tailored=activity
            )
            
            # Update the index values
            index.update_from_activity()
            
        except ActivityTailored.DoesNotExist:
            # Activity was deleted before the task executed
            pass
    
    # Schedule the update task asynchronously to avoid blocking the save operation
    transaction.on_commit(lambda: update_indexes_task.delay(instance.id))