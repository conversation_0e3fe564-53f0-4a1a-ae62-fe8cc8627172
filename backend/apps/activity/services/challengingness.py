from django.db import models

class ChallengingessComputation:
    """
    Service class for calculating activity challengingness using the formula
    from docs/global/challengingness.md with performance optimizations.
    """
    
    def __init__(self, user_profile):
        self.user_profile = user_profile
        self._trait_cache = {}
        self._skill_cache = {}
        self._limitation_cache = None
        self._mood_cache = None
    
    def preload_user_data(self):
        """Preload all user data needed for calculations to minimize DB queries."""
        # Fetch all user traits in a single query
        traits = self.user_profile.trait_inclinations.select_related('generic_trait').all()
        self._trait_cache = {t.generic_trait.id: t for t in traits}
        
        # Fetch all user skills in a single query
        skills = self.user_profile.skills.select_related('generic_skill').all()
        self._skill_cache = {s.generic_skill.id: s for s in skills}
        
        # Cache current limitations
        import datetime
        today = datetime.date.today()
        self._limitation_cache = list(self.user_profile.limitations.filter(
            models.Q(valid_until__gte=today) | models.Q(is_unlimited=True)
        ).select_related('generic_limitation'))
        
        # Cache current mood
        try:
            self._mood_cache = self.user_profile.current_mood
        except AttributeError:
            self._mood_cache = None

    
    def calculate_contextual_adjustment(user_profile, activity, environment):
        """Calculate adjustments based on environmental and contextual factors."""
        # Environment support for activity domains
        env_adjustment = 0
        activity_domains = activity.domain_relationships.all()
        
        for rel in activity_domains:
            domain = rel.domain
            importance = rel.strength / 100  # Normalize to 0-1
            
            # Get environment support for this domain
            env_support = environment.get_domain_support_rating(domain_id=domain.id)
            
            # Convert from -100 to +100 scale to a 0.5 to 1.5 multiplier
            # -100 (discouraging) would make activity 50% harder
            # +100 (highly supportive) would make activity 50% easier
            support_multiplier = 1 + (env_support / 200)
            
            env_adjustment += importance * (1 - support_multiplier)  # Convert to difficulty adjustment
        
        # Current mood factor
        mood_adjustment = 0
        try:
            mood = user_profile.current_mood
            # Normalized mood height (0-1)
            mood_factor = mood.height / 100
            
            # Linear adjustment: low mood increases difficulty by up to 20%
            mood_adjustment = 0.2 * (0.5 - mood_factor)  # Centered around 0.5
        except:
            pass
        
        return env_adjustment + mood_adjustment
    
    def calculate_time_factors(user_profile, skill):
        """Adjust skill level based on recency of practice and skill decay."""
        if not skill.last_practiced or not skill.generic_skill.decay_rate:
            return 1.0  # No adjustment
            
        import datetime
        days_since_practice = (datetime.date.today() - skill.last_practiced).days
        
        # Convert practice frequency to a numeric value (days)
        frequency_factors = {
            'daily': 1,
            'weekly': 7,
            'monthly': 30,
            'quarterly': 90,
            'yearly': 365,
            'rarely': 180,
            'never': 365
        }
        practice_interval = frequency_factors.get(skill.practice_frequency, 180)
        
        # Calculate decay based on time since practice
        # skill.generic_skill.decay_rate is normalized to 0-100
        decay_rate = skill.generic_skill.decay_rate / 100
        intervals_passed = days_since_practice / practice_interval
        
        # Exponential decay formula: level * (1 - decay_rate)^intervals
        decay_factor = (1 - decay_rate) ** intervals_passed
        
        # Floor of 0.5 (skill can't decay below 50% of original level)
        return max(0.5, decay_factor)
    
    def calculate_skill_synergy_bonus(user_profile, skill, domain):
        """
        Calculate bonus from having complementary skills.
        
        Args:
            user_profile: The UserProfile instance
            skill: The primary Skill instance
            domain: The GenericDomain for calculation
            
        Returns:
            float: Synergy bonus (0-20)
        """
        # Get domain relationship for this skill
        domain_rel = skill.generic_skill.domain_relationships_details.filter(domain=domain).first()
        if not domain_rel or not domain_rel.complementary_skills.exists():
            return 0
        
        # Get complementary skills
        complementary_skill_ids = domain_rel.complementary_skills.values_list('id', flat=True)
        
        # Check which complementary skills the user has
        user_complementary_skills = user_profile.skills.filter(
            generic_skill_id__in=complementary_skill_ids
        )
        
        if not user_complementary_skills.exists():
            return 0
        
        # Calculate synergy bonus (capped at 20%)
        # More complementary skills = higher bonus, higher skill levels = higher bonus
        complementary_skill_count = user_complementary_skills.count()
        avg_skill_level = user_complementary_skills.aggregate(avg=models.Avg('level'))['avg'] or 0
        
        # Formula: each complementary skill can add up to 5% bonus
        # depending on its level (a level 100 skill adds 5%, a level 50 skill adds 2.5%)
        synergy_bonus = complementary_skill_count * (avg_skill_level / 100) * 5
        
        return min(20, synergy_bonus)  # Cap at 20% bonus
    

    def calculate_time_factors(user_profile, skill):
        """
        Adjust skill level based on recency of practice and skill decay.
        
        Args:
            user_profile: The UserProfile instance
            skill: The Skill instance
            
        Returns:
            float: Time decay multiplier (0.5-1.0)
        """
        if not skill.last_practiced or not skill.generic_skill.decay_rate:
            return 1.0  # No adjustment
            
        import datetime
        days_since_practice = (datetime.date.today() - skill.last_practiced).days
        
        # Convert practice frequency to a numeric value (days)
        frequency_factors = {
            'daily': 1,
            'weekly': 7,
            'monthly': 30,
            'quarterly': 90,
            'yearly': 365,
            'rarely': 180,
            'never': 365
        }
        practice_interval = frequency_factors.get(skill.practice_frequency, 180)
        
        # Calculate decay based on time since practice
        # skill.generic_skill.decay_rate is normalized to 0-100
        decay_rate = skill.generic_skill.decay_rate / 100
        intervals_passed = days_since_practice / practice_interval
        
        # Exponential decay formula: level * (1 - decay_rate)^intervals
        decay_factor = (1 - decay_rate) ** intervals_passed
        
        # Floor of 0.5 (skill can't decay below 50% of original level)
        return max(0.5, decay_factor)


    def calculate_trait_gaps(activity, user_profile):
        """
        Calculate gaps between activity trait requirements and user's personality traits.
        
        Args:
            activity: The GenericActivity or ActivityTailored instance
            user_profile: The UserProfile instance
            
        Returns:
            float: Weighted trait gap score (0-100)
        """
        # Get activity's trait requirements
        trait_requirements = []
        
        # For GenericActivity, get trait requirements from GenericActivityUserRequirement
        if hasattr(activity, 'code'):
            reqs = activity.user_requirements.filter(
                content_type__model='generictrait'
            ).select_related('content_type')
            
            for req in reqs:
                trait_id = req.object_id
                level_required = req.level_required
                optional = req.optional
                
                trait_requirements.append({
                    'trait_id': trait_id,
                    'level_required': level_required,
                    'importance': 100 if not optional else 50
                })
        
        # For ActivityTailored, get trait requirements
        else:
            # Get from challengingness JSON
            trait_fields = [
                ('openness', 'OPEN'),
                ('conscientiousness', 'CONS'),
                ('extraversion', 'EXTR'),
                ('agreeableness', 'AGRE'),
                ('neuroticism', 'EMO'),  # Emotionality in HEXACO
                ('honestyhumility', 'HONHUM')
            ]
            
            from apps.user.models import GenericTrait
            for field, trait_type in trait_fields:
                if field in activity.challengingness:
                    level = activity.challengingness[field]
                    if level > 0:
                        # Find trait by type
                        trait = GenericTrait.objects.filter(trait_type=trait_type).first()
                        if trait:
                            trait_requirements.append({
                                'trait_id': trait.id,
                                'level_required': level,
                                'importance': 70  # Default importance
                            })
        
        # Calculate gap for each required trait
        trait_gaps = []
        for req in trait_requirements:
            trait_id = req['trait_id']
            level_required = req['level_required']
            importance = req['importance']
            
            # Get user's trait level
            user_trait = user_profile.trait_inclinations.filter(generic_trait_id=trait_id).first()
            user_level = user_trait.strength if user_trait else 0
            
            # Calculate gap
            gap = max(0, level_required - user_level)
            
            trait_gaps.append({
                'trait_id': trait_id,
                'importance': importance,
                'gap': gap
            })
        
        # Calculate weighted average gap
        if not trait_gaps:
            return 0
        
        total_importance = sum(g['importance'] for g in trait_gaps)
        weighted_gap = sum((g['gap'] * (g['importance'] / total_importance)) for g in trait_gaps)
        
        return weighted_gap


    def calculate_contextual_adjustment(user_profile, activity, environment):
        """
        Calculate adjustments based on environmental and contextual factors.
        
        Args:
            user_profile: The UserProfile instance
            activity: The GenericActivity or ActivityTailored instance
            environment: The UserEnvironment instance
            
        Returns:
            float: Context adjustment factor (-0.3 to +0.5)
        """
        # Begin with no adjustment
        total_adjustment = 0
        
        # FACTOR 1: Environment support for activity domains
        env_adjustment = 0
        if hasattr(activity, 'domain_relationships'):
            activity_domains = activity.domain_relationships.all()
            
            if activity_domains.exists():
                # Calculate weighted average of environment support
                total_importance = 0
                weighted_support = 0
                
                for rel in activity_domains:
                    domain = rel.domain
                    importance = rel.strength
                    total_importance += importance
                    
                    # Get environment support for this domain (-100 to +100)
                    env_support = environment.get_domain_support_rating(domain_id=domain.id)
                    weighted_support += env_support * importance
                
                if total_importance > 0:
                    avg_env_support = weighted_support / total_importance
                    
                    # Convert from -100 to +100 scale to a -0.2 to +0.2 adjustment
                    # Negative support makes activity harder, positive support makes it easier
                    env_adjustment = -1 * (avg_env_support / 500)  # Divided by 500 to get range of -0.2 to +0.2
        
        # FACTOR 2: Current mood effect
        mood_adjustment = 0
        try:
            mood = user_profile.current_mood
            # Normalized mood height (0-1)
            mood_factor = mood.height / 100
            
            # Linear adjustment: low mood increases difficulty by up to 20%
            # Centered at 0.5 (neutral mood has no effect)
            mood_adjustment = 0.2 * (0.5 - mood_factor)
        except:
            pass
        
        # FACTOR 3: Time of day alignment
        time_adjustment = 0
        try:
            import datetime
            current_hour = datetime.datetime.now().hour
            
            # Check if user has preferred time preferences
            preferred_times = user_profile.preferences.filter(
                pref_name__icontains='time'
            ).first()
            
            if preferred_times:
                # Simple logic: morning (6-12), afternoon (12-18), evening (18-23), night (0-6)
                if 'morning' in preferred_times.pref_name.lower() and 6 <= current_hour < 12:
                    time_adjustment = -0.1  # 10% easier
                elif 'afternoon' in preferred_times.pref_name.lower() and 12 <= current_hour < 18:
                    time_adjustment = -0.1
                elif 'evening' in preferred_times.pref_name.lower() and 18 <= current_hour < 23:
                    time_adjustment = -0.1
                elif 'night' in preferred_times.pref_name.lower() and (current_hour >= 23 or current_hour < 6):
                    time_adjustment = -0.1
        except:
            pass
        
        # FACTOR 4: User limitations
        limitation_adjustment = 0
        
        # Get active limitations
        import datetime
        today = datetime.date.today()
        active_limitations = user_profile.limitations.filter(
            models.Q(valid_until__gte=today) | models.Q(is_unlimited=True)
        ).select_related('generic_limitation')
        
        if active_limitations.exists():
            # Check for activity's limitation-related requirements
            if hasattr(activity, 'user_requirements'):
                limitation_reqs = activity.user_requirements.filter(
                    impact_type='limitation'
                )
                
                # If activity has specific limitation adaptations, adjust accordingly
                if limitation_reqs.exists():
                    limitation_adjustment = -0.1  # Slightly easier due to adaptations
                else:
                    # Calculate adjustment based on limitation severity and count
                    total_severity = sum(l.severity for l in active_limitations)
                    count = active_limitations.count()
                    
                    # More limitations and higher severity = harder
                    # Maximum effect: 30% harder
                    limitation_adjustment = min(0.3, (total_severity / 100) * (count * 0.1))
        
        # Combine all adjustments (negative = easier, positive = harder)
        total_adjustment = env_adjustment + mood_adjustment + time_adjustment + limitation_adjustment
        
        # Ensure adjustment stays within reasonable bounds
        return max(-0.3, min(0.5, total_adjustment))
    
    def calculate_comprehensive_challengingness(activity, user_profile, environment):
        """Calculate comprehensive challengingness score for an activity."""
        # Step 1: Calculate domain-specific skill gaps
        domain_gaps = []
        activity_domains = activity.domain_relationships.all()
        
        for domain_rel in activity_domains:
            domain = domain_rel.domain
            domain_importance = domain_rel.strength
            
            # Skip domains with minimal importance
            if domain_importance < 10:
                continue
                
            # Find user's effective skill level for this domain
            effective_skill_level = 0
            for skill in user_profile.skills.all():
                # Get domain-specific adjusted skill level
                skill_domain_level = calculate_adjusted_skill_level(
                    user_profile, skill, domain
                )
                
                # Apply time-based factors
                time_factor = calculate_time_factors(user_profile, skill)
                skill_domain_level *= time_factor
                
                # Take the highest skill level for this domain
                effective_skill_level = max(effective_skill_level, skill_domain_level)
            
            # Calculate gap
            gap = max(0, 100 - effective_skill_level)
            
            domain_gaps.append({
                'domain': domain,
                'importance': domain_importance,
                'gap': gap
            })
        
        # Step 2: Calculate trait gaps
        trait_gap = calculate_trait_gaps(activity, user_profile)
        
        # Step 3: Apply contextual factors
        context_adjustment = calculate_contextual_adjustment(
            user_profile, activity, environment
        )
        
        # Step 4: Calculate weighted skill gap
        if not domain_gaps:
            skill_gap = 0
        else:
            total_importance = sum(g['importance'] for g in domain_gaps)
            skill_gap = sum(g['gap'] * (g['importance']/total_importance) for g in domain_gaps)

        # 5. Calculate belief factor
        belief_factor = self.calculate_belief_factor(activity, user_profile)
        
        # Step 5: Calculate comprehensive score
        # Formula: overall challenge = (skill_gap * 0.6) + (trait_gap * 0.4) * (1 + context_adjustment)
        comprehensive_score = ((skill_gap * 0.6) + (trait_gap * 0.4)) * (1 + context_adjustment)* belief_factor
        
        # Normalize to 0-100 scale
        return min(100, max(0, comprehensive_score))
    
from apps.activity.services.challengingness import ChallengingessComputation
from apps.utils.activity_skill_integration import ActivitySkillIntegration
import logging

logger = logging.getLogger(__name__)

class SkillBasedChallengingness:
    """
    Extension to the ChallengingessComputation system that incorporates
    the dynamic skill composition model into activity challengingness calculations.
    
    This class provides methods for calculating skill-based challenge components
    and integrating them with the existing trait-based approach.
    """
    
    def __init__(self, user_profile):
        self.user_profile = user_profile
        self.base_computation = ChallengingessComputation(user_profile)
        self._skill_cache = {}
        
    def calculate_skill_gap_challenge(self, activity, domain=None):
        """
        Calculate the challenge component based on skills gap.
        
        Args:
            activity: The activity to evaluate
            domain: Optional specific domain context
            
        Returns:
            float: Skill gap challenge component (0-100)
        """
        # Get activity-skill match analysis
        match_analysis = ActivitySkillIntegration.analyze_activity_skill_match(
            activity, self.user_profile
        )
        
        # Calculate gap as inverse of match percentage
        skill_gap = 100 - match_analysis['overall_match']
        
        # Normalize to ensure reasonable values
        # We don't want a perfect 100 gap unless there's truly zero match
        normalized_gap = min(90, skill_gap)
        
        return normalized_gap
    
    def calculate_integrated_challengingness(self, activity, environment=None):
        """
        Calculate an integrated challengingness score that combines:
        1. Trait-based gap challenge (from original system)
        2. Skill-based gap challenge (from new system)
        3. Limitation factor (from original system)
        4. Mood modifier (from original system)
        
        Args:
            activity: The activity to evaluate
            environment: Optional user environment context
            
        Returns:
            dict: Integrated challengingness calculation with components
        """
        # Calculate skill gap component
        skill_gap = self.calculate_skill_gap_challenge(activity)
        
        # Get trait gap from original computation method
        try:
            trait_gap = self.base_computation.calculate_trait_gaps(activity, self.user_profile)
        except Exception as e:
            logger.warning(f"Error calculating trait gap: {str(e)}")
            trait_gap = 0
        
        # Get contextual adjustments from original system
        try:
            ctx_adjustment = self.base_computation.calculate_contextual_adjustment(
                self.user_profile, activity, environment
            ) if environment else 0
        except Exception as e:
            logger.warning(f"Error calculating contextual adjustment: {str(e)}")
            ctx_adjustment = 0
        
        # Calculate limitation factor
        try:
            limitation_factor = self._calculate_limitation_factor()
        except Exception as e:
            logger.warning(f"Error calculating limitation factor: {str(e)}")
            limitation_factor = 1.0
        
        # Calculate mood modifier
        try:
            mood_modifier = self._calculate_mood_modifier()
        except Exception as e:
            logger.warning(f"Error calculating mood modifier: {str(e)}")
            mood_modifier = 1.0
        
        # Integrated calculation with weighted components
        # We weight skill gap more heavily (0.7) than trait gap (0.3)
        base_challenge = (skill_gap * 0.7) + (trait_gap * 0.3)
        
        # Apply contextual adjustment
        adjusted_challenge = base_challenge * (1 + ctx_adjustment)
        
        # Apply limitation factor and mood modifier
        final_challenge = adjusted_challenge * limitation_factor * mood_modifier
        
        # Ensure result is within valid range
        final_challenge = max(0, min(100, final_challenge))
        
        return {
            'challengingness': final_challenge,
            'components': {
                'skill_gap': skill_gap,
                'trait_gap': trait_gap,
                'contextual_adjustment': ctx_adjustment,
                'limitation_factor': limitation_factor,
                'mood_modifier': mood_modifier
            }
        }
    
    def _calculate_limitation_factor(self):
        """Calculate limitation factor using the base computation method."""
        if not hasattr(self.base_computation, '_limitation_cache'):
            # Fall back to default if limitation cache not available
            import datetime
            today = datetime.date.today()
            limitations = list(self.user_profile.limitations.filter(
                models.Q(valid_until__gte=today) | models.Q(is_unlimited=True)
            ).select_related('generic_limitation'))
        else:
            limitations = self.base_computation._limitation_cache
        
        if not limitations:
            return 1.0  # No limitations = neutral factor
        
        # Calculate average severity
        avg_severity = sum(l.severity for l in limitations) / len(limitations)
        
        # Convert to factor (0-100 severity to 1.0-1.3 factor)
        return 1.0 + (avg_severity / 100) * 0.3
    
    def _calculate_mood_modifier(self):
        """Calculate mood modifier using the base computation method."""
        if not hasattr(self.base_computation, '_mood_cache'):
            # Fall back to getting current mood
            try:
                mood = self.user_profile.current_mood
            except AttributeError:
                return 1.0  # No mood = neutral modifier
        else:
            mood = self.base_computation._mood_cache
        
        if not mood:
            return 1.0
        
        # Apply same formula as original
        baseline = 0.5  # Neutral mood baseline
        sensitivity = 0.5  # Mood sensitivity parameter
        
        return 1.0 + sensitivity * (baseline - (mood.height / 100))
    


    def calculate_belief_factor(self, activity, user_profile=None):
        """
        Calculate how the user's beliefs affect the perceived challenge of an activity.
        
        Args:
            activity: The activity to evaluate
            user_profile: Optional user profile (uses self.user_profile if None)
            
        Returns:
            float: Belief adjustment factor (0.7-1.3)
        """
        if user_profile is None:
            user_profile = self.user_profile
        
        # Default neutral factor
        adjustment_factor = 1.0
        
        # List to collect relevant belief impacts
        belief_impacts = []
        
        # 1. First approach: Find GenericBelief requirements for this activity
        if hasattr(activity, 'user_requirements'):
            belief_reqs = activity.user_requirements.filter(
                content_type__model='genericbelief'
            ).select_related('content_type')
            
            for req in belief_reqs:
                generic_belief_id = req.object_id
                
                # Find if user has beliefs related to this generic belief
                user_beliefs = user_profile.beliefs.filter(
                    generic_belief_id=generic_belief_id
                )
                
                if user_beliefs.exists():
                    for belief in user_beliefs:
                        # Calculate impact based on belief attributes
                        # Higher stability, emotionality and user_confidence create stronger effects
                        
                        # Determine if belief aligns with or conflicts with activity
                        req_entity = req.required_entity
                        
                        # Check if activity requires positive or negative relationship to this belief
                        alignment_score = 0
                        if req.notes and "challenge" in req.notes.lower():
                            # Activity is meant to challenge this belief
                            alignment_score = -1  # Makes activity harder
                        elif req.notes and "reinforce" in req.notes.lower():
                            # Activity is meant to reinforce this belief
                            alignment_score = 1   # Makes activity easier
                        
                        # Calculate impact
                        # stability:     0-100 (higher = more resistant to change)
                        # emotionality: -100 to 100 (abs higher = more emotional weight)
                        # user_confidence: 0-100 (higher = stronger conviction)
                        
                        # Calculate the strength of belief impact
                        belief_strength = (
                            (belief.stability / 100) * 
                            (abs(belief.emotionality) / 100) * 
                            (belief.user_confidence / 100)
                        )
                        
                        # If alignment_score is negative, this belief makes the activity harder
                        # If alignment_score is positive, this belief makes the activity easier
                        impact = alignment_score * belief_strength
                        
                        belief_impacts.append(impact)
        
        # 2. Second approach: Consider domain relationships
        if hasattr(activity, 'domain_relationships'):
            # Get domains associated with this activity
            activity_domains = activity.domain_relationships.values_list('domain_id', flat=True)
            
            # For each user belief with a generic_belief, check domain relationships
            for belief in user_profile.beliefs.filter(generic_belief__isnull=False).select_related('generic_belief'):
                generic_belief = belief.generic_belief
                
                # Get domains this belief affects
                domain_rels = generic_belief.domain_relationships_details.filter(
                    domain_id__in=activity_domains
                )
                
                for rel in domain_rels:
                    # Calculate impact based on belief-domain relationship
                    # rel.impact ranges from -3 to +3
                    # Convert to a -0.3 to +0.3 impact factor
                    domain_impact = rel.impact / 10
                    
                    # Adjust based on belief strength 
                    belief_strength = (
                        (belief.stability / 100) * 
                        (abs(belief.emotionality) / 100) * 
                        (belief.user_confidence / 100)
                    )
                    
                    # Combine domain impact with belief strength
                    impact = domain_impact * belief_strength
                    
                    belief_impacts.append(impact)
        
        # 3. Calculate overall adjustment factor
        if belief_impacts:
            # Calculate weighted average (stronger impacts count more)
            total_impact = sum(belief_impacts)
            
            # Convert to adjustment factor: 
            # Negative impact = activity feels harder (>1.0 multiplier)
            # Positive impact = activity feels easier (<1.0 multiplier)
            adjustment_factor = 1.0 - total_impact
            
            # Constrain to reasonable range
            adjustment_factor = max(0.7, min(1.3, adjustment_factor))
        
        return adjustment_factor

    '''
    Calculating a User's Skill Level
    # Get a user's level in creative writing with domain context
from apps.user.services.skill_service import SkillService
from apps.user.models import SkillDefinition
from apps.activity.models import GenericDomain

# Get required objects
user_profile = request.user.profiles.first()
creative_writing = SkillDefinition.objects.get(code='creative_writing')
writing_domain = GenericDomain.objects.get(code='creative_writing')

# Calculate skill level
result = SkillService.get_user_skill_level(
    user_profile, creative_writing, writing_domain
)

level = result['level']  # Overall skill level (0-100)
details = result['details']  # Breakdown by attribute
    '''


    '''
    # Analyze a user's skill match for an activity
from apps.utils.activity_skill_integration import ActivitySkillIntegration

# Get required objects
activity = GenericActivity.objects.get(id=activity_id)
user_profile = request.user.profiles.first()

# Analyze skill match
match_analysis = ActivitySkillIntegration.analyze_activity_skill_match(
    activity, user_profile
)

overall_match = match_analysis['overall_match']  # 0-100 percentage
recommendations = match_analysis['recommendations']  # Improvement suggestions
    
    '''

    '''
    # Record attribute usage after activity completion
from apps.user.services.skill_service import SkillService

# Record usage of specific attributes
attribute_ids = [1, 3, 5]  # IDs of attributes used
context = {'activity_id': 'act_123', 'duration': 30}  # Optional context

result = SkillService.record_attribute_usage(
    user_profile, attribute_ids, context
)
    '''