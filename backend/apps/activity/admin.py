from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from config.admin import admin_site # Import the custom admin site
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.admin import GenericTabularInline

from .models import (
    Tag, TaggedItem, 
    GenericDomain, EntityDomainRelationship, # Renamed models
    GenericActivity, ActivityTailored,
    GenericActivityResourceRequirement, GenericActivityEnvRequirement,
    GenericActivityUserRequirement, GenericActivityUserRequirementSummary,
    ActivityInfluencedBy, ActivityUserRequirement, ActivityEnvRequirement,
    ActivityTailoredResourceRequirement
)

# =====================
# Custom Filters
# =====================

class DifficultyLevelFilter(admin.SimpleListFilter):
    title = _('Difficulty Level')
    parameter_name = 'difficulty'
    
    def lookups(self, request, model_admin):
        return (
            ('easy', _('Easy (0-25)')),
            ('medium', _('Medium (26-50)')),
            ('challenging', _('Challenging (51-75)')),
            ('difficult', _('Difficult (76-100)')),
            ('unknown', _('Unknown/Not Set')),
        )
    
    def queryset(self, request, queryset):
        if self.value() == 'easy':
            return queryset.filter(user_requirements_summary__difficulty_rating__lte=25)
        if self.value() == 'medium':
            return queryset.filter(
                user_requirements_summary__difficulty_rating__gt=25,
                user_requirements_summary__difficulty_rating__lte=50
            )
        if self.value() == 'challenging':
            return queryset.filter(
                user_requirements_summary__difficulty_rating__gt=50,
                user_requirements_summary__difficulty_rating__lte=75
            )
        if self.value() == 'difficult':
            return queryset.filter(user_requirements_summary__difficulty_rating__gt=75)
        if self.value() == 'unknown':
            return queryset.filter(user_requirements_summary__isnull=True)


class DomainFilter(admin.SimpleListFilter):
    title = _('Primary Domain')
    parameter_name = 'primary_domain'
    
    def lookups(self, request, model_admin):
        from .models import EntityDomainRelationship # Use renamed model
        # Get unique domains where relationship strength is PRIMARY (100)
        domains = EntityDomainRelationship.objects.filter( # Use renamed model
            strength=EntityDomainRelationship.RelationshipStrength.PRIMARY # Use renamed model
        ).values_list('domain__name', 'domain__id').distinct()
        return [(str(id), name) for name, id in domains]
    
    def queryset(self, request, queryset):
        if self.value():
            from .models import EntityDomainRelationship, GenericDomain # Use renamed models
            from django.contrib.contenttypes.models import ContentType
            
            # Get content type for GenericActivity
            ct = ContentType.objects.get_for_model(queryset.model)
            
            # Find relationships for this domain with PRIMARY strength
            domain_id = self.value()
            activity_ids = EntityDomainRelationship.objects.filter( # Use renamed model
                content_type=ct,
                domain_id=domain_id,
                strength=EntityDomainRelationship.RelationshipStrength.PRIMARY # Use renamed model
            ).values_list('object_id', flat=True)
            
            return queryset.filter(id__in=activity_ids)
        return queryset

# =====================
# Inline Admin Classes
# =====================

class TaggedItemInline(GenericTabularInline):
    model = TaggedItem
    extra = 1
    autocomplete_fields = ['tag']

class EntityDomainRelationshipInline(GenericTabularInline): # Renamed Inline
    model = EntityDomainRelationship # Use renamed model
    extra = 1
    verbose_name = "Domain Relationship"
    verbose_name_plural = "Domain Relationships"
    raw_id_fields = ['domain']

class GenericActivityResourceRequirementInline(admin.TabularInline):
    model = GenericActivityResourceRequirement
    extra = 1
    verbose_name = "Resource Requirement"
    verbose_name_plural = "Resource Requirements"

class GenericActivityEnvRequirementInline(admin.TabularInline):
    model = GenericActivityEnvRequirement
    extra = 1
    verbose_name = "Environment Requirement"
    verbose_name_plural = "Environment Requirements"

class GenericActivityUserRequirementInline(admin.TabularInline):
    model = GenericActivityUserRequirement
    extra = 1
    verbose_name = "User Requirement"
    verbose_name_plural = "User Requirements"

class ActivityTailoredInline(admin.TabularInline):
    model = ActivityTailored
    extra = 0
    verbose_name = "Tailored Activity"
    verbose_name_plural = "Tailored Activities"
    fields = ('name', 'user_profile', 'base_challenge_rating', 'version')
    readonly_fields = ('name', 'user_profile', 'base_challenge_rating', 'version')
    max_num = 5
    can_delete = False

class ActivityInfluencedByInline(admin.TabularInline):
    model = ActivityInfluencedBy
    extra = 1
    verbose_name = "Influence"
    verbose_name_plural = "Influences"

class ActivityUserRequirementInline(admin.TabularInline):
    model = ActivityUserRequirement
    extra = 1
    verbose_name = "User Requirement"
    verbose_name_plural = "User Requirements"

# =====================
# Main Admin Classes
# =====================

# @admin.register(Tag) # Use admin_site.register instead
class TagAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'description')
    search_fields = ('name', 'slug', 'description')
    prepopulated_fields = {'slug': ('name',)}

# Removed old ActivityDomainAdmin registration

# @admin.register(GenericDomain) # Use admin_site.register instead
class GenericDomainAdmin(admin.ModelAdmin): # Rename admin class
    list_display = ('name', 'code', 'primary_category')
    search_fields = ('name', 'code', 'description') # Ensure search_fields are present for autocomplete
    fieldsets = (
        (None, {'fields': ('code', 'name', 'description', 'primary_category')}),
    )

# @admin.register(GenericActivity) # Use admin_site.register instead
class GenericActivityAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_on', 'duration_range', 'get_primary_domain_display', 'get_tags_display',
                   'get_tailored_versions_count', 'get_user_requirements_count', 'get_difficulty_level')
    list_filter = ('created_on', DomainFilter, DifficultyLevelFilter)
    search_fields = ('name', 'description', 'instructions', 'code')
    date_hierarchy = 'created_on'
    # Removed filter_horizontal = ('domain',) as the direct M2M field was removed
    
    inlines = [
        TaggedItemInline,
        EntityDomainRelationshipInline, # Use renamed inline
        GenericActivityResourceRequirementInline,
        GenericActivityEnvRequirementInline,
        GenericActivityUserRequirementInline,
        ActivityTailoredInline,
    ]
    
    fieldsets = (
        (None, {
            'fields': ('code', 'name', 'description', 'created_on')
        }),
        ('Activity Details', {
            'fields': ('duration_range', 'instructions'),
            'classes': ('collapse',),
        }),
        ('Requirements', {
            'fields': ('social_requirements',), # Removed 'domain' field
            'classes': ('collapse',),
        }),
    )
    
    actions = ['duplicate_activity', 'create_requirement_summary']
    
    def get_primary_domain_display(self, obj):
        primary_domain = obj.get_primary_domain()
        if primary_domain:
            return primary_domain.name
        return '-'
    get_primary_domain_display.short_description = 'Primary Domain'
    
    def get_tags_display(self, obj):
        tagged_items = TaggedItem.objects.filter(
            content_type__app_label='activity',
            content_type__model='genericactivity',
            object_id=str(obj.id)
        )
        return ", ".join([item.tag.name for item in tagged_items])
    get_tags_display.short_description = 'Tags'
    
    def get_tailored_versions_count(self, obj):
        # Check the correct related_name in the ActivityTailored model
        # The error indicates we should use 'activitytailored_tailored_activities'
        return ActivityTailored.objects.filter(generic_activity=obj).count()
    get_tailored_versions_count.short_description = 'Tailored Versions'
    
    def get_user_requirements_count(self, obj):
        return obj.user_requirements.count()
    get_user_requirements_count.short_description = 'Requirements'
    
    def get_difficulty_level(self, obj):
        try:
            difficulty = obj.user_requirements_summary.difficulty_rating
            if difficulty <= 25:
                return format_html('<span style="color: green; font-weight: bold;">Easy</span>')
            elif difficulty <= 50:
                return format_html('<span style="color: blue; font-weight: bold;">Medium</span>')
            elif difficulty <= 75:
                return format_html('<span style="color: orange; font-weight: bold;">Challenging</span>')
            else:
                return format_html('<span style="color: red; font-weight: bold;">Difficult</span>')
        except (AttributeError, GenericActivityUserRequirementSummary.DoesNotExist):
            return format_html('<span style="color: gray;">Unknown</span>')
    get_difficulty_level.short_description = 'Difficulty'
    
    def duplicate_activity(self, request, queryset):
        from django.utils.text import slugify
        from django.contrib.contenttypes.models import ContentType
        import datetime
        
        activity_ct = ContentType.objects.get_for_model(GenericActivity)
        count = 0
        
        for activity in queryset:
            # Create a new activity with a copy of the original data
            new_activity = GenericActivity.objects.create(
                name=f"Copy of {activity.name}",
                code=f"{activity.code}_copy_{count}",
                description=activity.description,
                created_on=datetime.date.today(),
                duration_range=activity.duration_range,
                instructions=activity.instructions,
                social_requirements=activity.social_requirements
            )
            
            # Copy domain relationships (now through EntityDomainRelationship)
            for rel in activity.domain_relationships.all(): # Use the GenericRelation name
                EntityDomainRelationship.objects.create( # Use renamed model
                    content_type=activity_ct,
                    object_id=new_activity.id,
                    domain=rel.domain,
                    strength=rel.strength
                )
            
            # Copy tags
            for tag_item in activity.tags.all():
                TaggedItem.objects.create(
                    tag=tag_item.tag,
                    content_type=activity_ct,
                    object_id=new_activity.id
                )
            
            # Copy resource requirements
            for req in activity.resource_requirements.all():
                GenericActivityResourceRequirement.objects.create(
                    generic_activity=new_activity,
                    resource_base=req.resource_base,
                    quantity_required=req.quantity_required,
                    optional=req.optional
                )
            
            # Copy environment requirements
            for env_req in activity.env_requirements.all():
                GenericActivityEnvRequirement.objects.create(
                    generic_activity=new_activity,
                    env=env_req.env,
                    optional=env_req.optional
                )
            
            # Copy user requirements
            for user_req in activity.user_requirements.all():
                GenericActivityUserRequirement.objects.create(
                    generic_activity=new_activity,
                    content_type=user_req.content_type,
                    object_id=user_req.object_id,
                    level_required=user_req.level_required,
                    optional=user_req.optional,
                    impact_type=user_req.impact_type,
                    notes=user_req.notes
                )
                
            count += 1
            
        self.message_user(request, f"Successfully duplicated {count} activities.")
    duplicate_activity.short_description = "Duplicate selected activities"
    
    def create_requirement_summary(self, request, queryset):
        count = 0
        for activity in queryset:
            try:
                # Try to get existing summary
                summary = GenericActivityUserRequirementSummary.objects.get(generic_activity=activity)
                summary.update_summary()
            except GenericActivityUserRequirementSummary.DoesNotExist:
                # Create new summary
                summary = GenericActivityUserRequirementSummary(
                    generic_activity=activity,
                    difficulty_rating=0,  # Will be updated by update_summary
                    requirements_summary={},
                    limitations_summary={}
                )
                summary.update_summary()
            count += 1
            
        self.message_user(request, f"Created/updated requirement summaries for {count} activities.")
    create_requirement_summary.short_description = "Create/update requirement summaries"

# @admin.register(ActivityTailored) # Use admin_site.register instead
class ActivityTailoredAdmin(admin.ModelAdmin):
    list_display = ('name', 'user_profile', 'generic_activity', 'base_challenge_rating', 'version', 'created_on')
    list_filter = ('created_on', 'base_challenge_rating', 'user_profile')
    search_fields = ('name', 'description', 'instructions')
    raw_id_fields = ('user_profile', 'generic_activity')
    readonly_fields = ('version',)
    
    inlines = [
        ActivityInfluencedByInline,
        ActivityUserRequirementInline,
    ]
    
    fieldsets = (
        (None, {
            'fields': ('user_profile', 'generic_activity', 'name', 'description', 'created_on')
        }),
        ('Activity Details', {
            'fields': ('duration_range', 'instructions', 'base_challenge_rating', 'version'),
            'classes': ('collapse',),
        }),
        ('Advanced Configuration', {
            'fields': ('social_requirements', 'challengingness', 'tailorization_level'),
            'classes': ('collapse',),
        }),
    )

# @admin.register(GenericActivityUserRequirement) # Use admin_site.register instead
class GenericActivityUserRequirementAdmin(admin.ModelAdmin):
    list_display = ('get_activity_name', 'entity_type_name', 'get_entity_name', 'impact_type', 'level_required', 'optional')
    list_filter = ('impact_type', 'optional', 'content_type')
    search_fields = ('generic_activity__name', 'notes')
    
    def get_activity_name(self, obj):
        return obj.generic_activity.name
    get_activity_name.short_description = 'Activity'
    
    def get_entity_name(self, obj):
        if hasattr(obj.required_entity, 'name'):
            return obj.required_entity.name
        return str(obj.required_entity)[:30]
    get_entity_name.short_description = 'Entity'

# @admin.register(GenericActivityUserRequirementSummary) # Use admin_site.register instead
class GenericActivityUserRequirementSummaryAdmin(admin.ModelAdmin):
    list_display = ('generic_activity', 'difficulty_rating', 'last_updated')
    list_filter = ('difficulty_rating', 'last_updated')
    search_fields = ('generic_activity__name',)
    readonly_fields = ('last_updated',)
    
    actions = ['update_summaries']
    
    def update_summaries(self, request, queryset):
        for summary in queryset:
            summary.update_summary()
        self.message_user(request, f"Updated {queryset.count()} requirement summaries.")
    update_summaries.short_description = "Update selected requirement summaries"

# @admin.register(ActivityInfluencedBy) # Use admin_site.register instead
class ActivityInfluencedByAdmin(admin.ModelAdmin):
    list_display = ('activity_tailored', 'get_influencing_type', 'get_influencing_name', 'influence_strength')
    list_filter = ('influence_strength', 'content_type')
    search_fields = ('activity_tailored__name', 'note')
    
    def get_influencing_type(self, obj):
        return obj.content_type.model.capitalize()
    get_influencing_type.short_description = 'Influenced By'
    
    def get_influencing_name(self, obj):
        if hasattr(obj.influencing_entity, 'name'):
            return obj.influencing_entity.name
        elif hasattr(obj.influencing_entity, 'title'):
            return obj.influencing_entity.title
        elif hasattr(obj.influencing_entity, 'content'):
            return obj.influencing_entity.content[:30]
        return str(obj.influencing_entity)[:30]
    get_influencing_name.short_description = 'Entity'

# Register all models with the custom admin site
admin_site.register(Tag, TagAdmin)
admin_site.register(GenericDomain, GenericDomainAdmin)
admin_site.register(GenericActivity, GenericActivityAdmin)
admin_site.register(ActivityTailored, ActivityTailoredAdmin)
admin_site.register(GenericActivityUserRequirement, GenericActivityUserRequirementAdmin)
admin_site.register(GenericActivityUserRequirementSummary, GenericActivityUserRequirementSummaryAdmin)
admin_site.register(ActivityInfluencedBy, ActivityInfluencedByAdmin)

# Register the remaining models with simple admin interfaces using the custom site
admin_site.register(EntityDomainRelationship) # Register renamed model
admin_site.register(ActivityUserRequirement)
admin_site.register(ActivityEnvRequirement)
admin_site.register(ActivityTailoredResourceRequirement)

# Note: Models used only in inlines (like GenericActivityResourceRequirement)
# don't need separate registration unless standalone access is required.
