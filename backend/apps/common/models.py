from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Permission(models.Model):
    """
    Represents a specific permission within the system.
    e.g., 'view_dashboard', 'edit_profile', 'manage_users'
    """
    name = models.CharField(max_length=100, unique=True, help_text="Unique name for the permission (e.g., 'view_reports').")
    description = models.TextField(blank=True, help_text="Optional description of what the permission allows.")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Permission"
        verbose_name_plural = "Permissions"
        ordering = ['name']


class Role(models.Model):
    """
    Represents a role that groups multiple permissions.
    e.g., 'Administrator', 'Editor', 'Viewer'
    """
    name = models.CharField(max_length=100, unique=True, help_text="Unique name for the role (e.g., 'Administrator').")
    description = models.TextField(blank=True, help_text="Optional description of the role's purpose.")
    permissions = models.ManyToManyField(
        Permission,
        blank=True,
        related_name='roles',
        help_text="Permissions granted to this role."
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Role"
        verbose_name_plural = "Roles"
        ordering = ['name']


class UserRole(models.Model):
    """
    Links a User to a Role, granting them the permissions associated with that role.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_roles')
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name='role_users')
    assigned_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} - {self.role.name}"

    class Meta:
        verbose_name = "User Role Assignment"
        verbose_name_plural = "User Role Assignments"
        unique_together = ('user', 'role') # Ensure a user doesn't have the same role assigned multiple times
        ordering = ['user__username', 'role__name']
