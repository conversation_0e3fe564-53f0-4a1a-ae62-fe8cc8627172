# backend/apps/common/fields.py

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator


class ValidatedRangeField(models.IntegerField):
    """
    Custom field for consistent 0-100 scale values with validation.
    
    This field ensures all values are between 0 and 100, providing consistent
    validation and standardized help text across models.
    """
    
    def __init__(self, verbose_name=None, name=None, **kwargs):
        # Set default validators
        kwargs.setdefault('validators', [
            MinValueValidator(0), 
            MaxValueValidator(100)
        ])
        
        # Set default help text if not provided
        if 'help_text' not in kwargs:
            kwargs['help_text'] = 'Value from 0 to 100'
            
        super().__init__(verbose_name, name, **kwargs)


class ExtendedRangeField(models.IntegerField):
    """
    Custom field for consistent -100 to 100 scale values with validation.
    
    This field ensures all values are between -100 and 100, providing consistent
    validation and standardized help text across models. Useful for fields that
    represent values that can be both positive and negative.
    """
    
    def __init__(self, verbose_name=None, name=None, **kwargs):
        # Set default validators
        kwargs.setdefault('validators', [
            MinValueValidator(-100), 
            <PERSON><PERSON><PERSON>ueValidator(100)
        ])
        
        # Set default help text if not provided
        if 'help_text' not in kwargs:
            kwargs['help_text'] = 'Value from -100 to 100'
            
        super().__init__(verbose_name, name, **kwargs)


class ScaleField(models.IntegerField):
    """
    Flexible range field with customizable min and max values.
    
    This field allows you to specify minimum and maximum values while providing
    consistent validation and help text based on those boundaries.
    """
    
    def __init__(self, min_value=0, max_value=100, verbose_name=None, name=None, **kwargs):
        # Set validators based on the provided min and max values
        kwargs.setdefault('validators', [
            MinValueValidator(min_value), 
            MaxValueValidator(max_value)
        ])
        
        # Set default help text if not provided
        if 'help_text' not in kwargs:
            kwargs['help_text'] = f'Value from {min_value} to {max_value}'
            
        self.min_value = min_value
        self.max_value = max_value
        
        super().__init__(verbose_name, name, **kwargs)
        
    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        # Include min_value and max_value in deconstruction for migrations
        kwargs['min_value'] = self.min_value
        kwargs['max_value'] = self.max_value
        return name, path, args, kwargs