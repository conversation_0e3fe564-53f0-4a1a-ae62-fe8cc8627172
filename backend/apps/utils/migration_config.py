"""
Configuration file for migration order and dependencies.
This centralizes the app dependency information to ensure consistent ordering
across reset_db.py, entrypoint.sh, and test setup.
"""

# Order in which apps should be migrated
MIGRATION_ORDER = [
    'activity',  # Base app with no dependencies
    'user',      # Depends on activity
    'main',      # Depends on activity and user
    # Add other apps in dependency order
]

# Dependencies for initial migrations (used when generating new migrations)
APP_DEPENDENCIES = {
    'activity': [],  # No dependencies
    'user': ['activity'],  # Depends on activity
    'main': ['activity', 'user'],  # Depends on both
    # Add other apps and their dependencies
}