import os
import shutil
import subprocess
import time
import argparse

import environ

def delete_migration_files():
    # Only target the 'backend' directory where your apps reside
    print("Deleting migration files...")
    current_dir = os.getcwd()
    if "backend" not in current_dir.split(os.sep):
        project_dir = os.path.join(current_dir, "backend")
    else:
        project_dir = current_dir
    
    for root, dirs, files in os.walk(project_dir):
        # Only process folders named "migrations"
        if os.path.basename(root) == "migrations":
            for file in files:
                # Keep __init__.py and potentially the initial migration file if needed later
                if file.endswith(".py") and file != "__init__.py":
                    file_path = os.path.join(root, file)
                    print(f"Deleting migration file: {file_path}")
                    os.remove(file_path)
                else:
                    print(f"skipping file: {file}")
            # Remove __pycache__ directories inside migrations
            pycache_dir = os.path.join(root, "__pycache__")
            if os.path.exists(pycache_dir):
                print(f"Deleting __pycache__ directory: {pycache_dir}")
                shutil.rmtree(pycache_dir)
        else:
            print(f"skipping folder: {root}")

def run_command(command, cwd=None):
    result = subprocess.run(
        command,
        check=True,        # Raises an exception if the command fails
        cwd=cwd,           # Sets the working directory for the command
        capture_output=True, # Captures stdout and stderr
        text=True          # Returns strings instead of bytes
    )
    print("STDOUT:\n", result.stdout)
    print("STDERR:\n", result.stderr)
    return result

def reset_postgres_db(container_name, db_user, db_name):
    """
    Resets the PostgreSQL database inside a Docker container.
    It terminates active connections, drops the specified database,
    and then creates a new one.
    """
    # SQL to terminate active connections
    terminate_sql = (
        f"SELECT pg_terminate_backend(pg_stat_activity.pid) "
        f"FROM pg_stat_activity "
        f"WHERE pg_stat_activity.datname = '{db_name}' "
        f"AND pid <> pg_backend_pid();"
    )
    drop_sql = f"DROP DATABASE IF EXISTS {db_name};"
    create_sql = f"CREATE DATABASE {db_name};"

    # Check if container exists and is running
    try:
        run_command(["docker", "inspect", "--format={{.State.Running}}", container_name])
    except subprocess.CalledProcessError as e:
        print(f"Error: Container '{container_name}' does not exist or cannot be accessed. Details: {e}")
        return False

    # Uncomment this section in production to ensure clean disconnection
    print("Terminating active connections...")
    try:
        run_command([
            "docker", "exec", container_name, 
            "psql", "-U", db_user, "-d", "postgres", 
            "-c", terminate_sql
        ])
        time.sleep(2)  # Reduced from 10 seconds, but you may need to adjust this
    except subprocess.CalledProcessError as e:
        print(f"Warning: Could not terminate existing connections. Ensure the database container is running and accessible. Details: {e}")

    print("Dropping the database...")
    try:
        run_command([
            "docker", "exec", container_name, 
            "psql", "-U", db_user, "-d", "postgres", 
            "-c", drop_sql
        ])
    except subprocess.CalledProcessError as e:
        print(f"Error dropping the database. Ensure the database container is running and accessible. Details: {e}")
        return False

    print("Creating a new database...")
    try:
        run_command([
            "docker", "exec", container_name, 
            "psql", "-U", db_user, "-d", "postgres", 
            "-c", create_sql
        ])
    except subprocess.CalledProcessError as e:
        print(f"Error creating the database. Details: {e}")
        return False
        
    print(f"Database '{db_name}' has been reset successfully.")
    return True


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Reset the database and optionally delete migration files.")
    parser.add_argument(
        "--delete-migrations",
        action="store_true",
        help="Delete migration files (excluding __init__.py) before resetting the database."
    )
    args = parser.parse_args()

    if args.delete_migrations:
        # Step 1: Delete migration files if the option is provided
        delete_migration_files()

    # Step 2: Reset the PostgreSQL database inside Docker (default behavior)
    # Adjust these variables to match your environment.
    container_name = "backend-db-1"  # Docker container name or ID
    # Read from environment variables, with hardcoded defaults
    db_user = os.environ.get("POSTGRES_USER", "postgres")
    db_name = os.environ.get("POSTGRES_DB", "mydb")

    reset_postgres_db(container_name, db_user, db_name)

    print("Database reset process completed.")
