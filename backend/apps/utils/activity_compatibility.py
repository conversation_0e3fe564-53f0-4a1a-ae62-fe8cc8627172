from django.db import models
from django.db.models import F, Q, Case, When, Value, IntegerField

class ActivityEnvironmentCompatibility:
    """
    Helper methods for determining compatibility between activities and environments.
    This can be used as a mixin or as standalone utility functions.
    """
    
    @staticmethod
    def get_compatible_environments_for_activity(activity, user_profile=None, min_compatibility=30):
        """
        Find environments that support the domains required by the given activity.
        
        Args:
            activity: A GenericActivity or ActivityTailored instance
            user_profile: Optional UserProfile to limit to user's environments
            min_compatibility: Minimum domain support strength required (default: 30 - MODERATE)
            
        Returns:
            QuerySet of GenericEnvironment or UserEnvironment objects
        """
        from apps.user.models import GenericEnvironment, UserEnvironment
        from apps.activity.models import EntityDomainRelationship # Renamed model
        
        # Get the domains for this activity with their strengths
        activity_domains = EntityDomainRelationship.objects.filter( # Use renamed model
            content_type__model='genericactivity' if hasattr(activity, 'code') else 'activitytailored',
            object_id=activity.id
        ).select_related('domain')
        
        # If no domains are found, return all environments
        if not activity_domains.exists():
            qs = GenericEnvironment.objects.filter(is_active=True)
            if user_profile:
                user_envs = UserEnvironment.objects.filter(user_profile=user_profile, is_current=True)
                return user_envs
            return qs
        
        # Start with all active environments
        qs = GenericEnvironment.objects.filter(is_active=True)
        
        # Filter to environments that support the primary domain (if any)
        primary_domains = activity_domains.filter(strength=EntityDomainRelationship.RelationshipStrength.PRIMARY) # Use renamed model
        if primary_domains.exists():
            primary_domain_ids = primary_domains.values_list('domain_id', flat=True)
            qs = qs.filter(
                domain_relationships_details__domain_id__in=primary_domain_ids,
                domain_relationships_details__strength__gte=min_compatibility
            )
        
        # If a user profile is provided, get their environments that match the generic ones
        if user_profile:
            user_envs = UserEnvironment.objects.filter(
                user_profile=user_profile,
                is_current=True,
                generic_environment__in=qs
            )
            return user_envs
        
        return qs
    
    @staticmethod
    def calculate_environment_activity_match_score(activity, environment):
        """
        Calculate how well an environment supports a specific activity.
        
        Args:
            activity: A GenericActivity or ActivityTailored instance
            environment: A GenericEnvironment or UserEnvironment instance
            
        Returns:
            int: Match score from 0 to 100
        """
        from apps.activity.models import EntityDomainRelationship # Renamed model
        
        # Get the generic environment if a user environment is provided
        if hasattr(environment, 'generic_environment') and environment.generic_environment:
            gen_env = environment.generic_environment
        else:
            gen_env = environment
        
        # Get activity domains with their strengths
        activity_domains = EntityDomainRelationship.objects.filter( # Use renamed model
            content_type__model='genericactivity' if hasattr(activity, 'code') else 'activitytailored',
            object_id=activity.id
        ).select_related('domain')
        
        if not activity_domains.exists():
            return 50  # Neutral score if no domains
        
        # Get environment domain support
        env_domain_support = gen_env.domain_relationships_details.all()
        env_support_map = {rel.domain_id: rel.strength for rel in env_domain_support}
        
        # Calculate match score
        total_weight = 0
        weighted_score = 0
        
        for rel in activity_domains:
            # Determine domain importance weight based on relationship strength
            if rel.strength == EntityDomainRelationship.RelationshipStrength.PRIMARY: # Use renamed model
                weight = 4  # Primary domains are most important
            elif rel.strength == EntityDomainRelationship.RelationshipStrength.SIGNIFICANT: # Use renamed model
                weight = 2
            else:
                weight = 1
                
            # Get environment's support for this domain
            env_support = env_support_map.get(rel.domain_id, 0)
            
            # Apply user-specific overrides if available
            if hasattr(environment, 'activity_support') and hasattr(environment.activity_support, 'domain_specific_support'):
                domain_code = rel.domain.code
                if domain_code in environment.activity_support.domain_specific_support:
                    env_support = environment.activity_support.domain_specific_support[domain_code]
            
            total_weight += weight
            weighted_score += weight * (env_support / 100)  # Normalize to 0-1 scale
        
        # Calculate final score (0-100)
        if total_weight > 0:
            match_score = int((weighted_score / total_weight) * 100)
            return match_score
        else:
            return 50  # Default neutral score
    
    @staticmethod
    def rank_activities_by_environment_compatibility(activities, environment):
        """
        Rank a list of activities based on how well they match the given environment.
        
        Args:
            activities: QuerySet or list of GenericActivity or ActivityTailored instances
            environment: A GenericEnvironment or UserEnvironment instance
            
        Returns:
            list: Activities sorted by compatibility score (highest first)
        """
        scored_activities = []
        
        for activity in activities:
            score = ActivityEnvironmentCompatibility.calculate_environment_activity_match_score(
                activity, environment
            )
            scored_activities.append((activity, score))
        
        # Sort by score (descending)
        return sorted(scored_activities, key=lambda x: x[1], reverse=True)
    
    @staticmethod
    def get_environment_compatibility_distribution(activity, user_profile):
        """
        Calculate compatibility scores for all of a user's environments for a specific activity.
        
        Args:
            activity: A GenericActivity or ActivityTailored instance
            user_profile: UserProfile instance
            
        Returns:
            dict: Environment ID to compatibility score mapping
        """
        from apps.user.models import UserEnvironment
        
        environments = UserEnvironment.objects.filter(
            user_profile=user_profile,
            is_current=True
        ).select_related('generic_environment', 'activity_support')
        
        compatibility_map = {}
        
        for env in environments:
            score = ActivityEnvironmentCompatibility.calculate_environment_activity_match_score(
                activity, env
            )
            compatibility_map[env.id] = score
        
        return compatibility_map
    
class ActivityEnvironmentCompatibility:
    """
    Helper methods for determining compatibility between activities and users.
    This can be used as a mixin or as standalone utility functions.
    
    Calculate the gap between activity skill requirements and user skill levels.
    
    This is separate from trait gap and represents the technical/learned skill gap
    rather than personality trait differences.
    """
    def calculate_skill_gap(activity, user):
        from apps.user.models import Skill
        skill_gaps = []
        skill_weights = []
        
        for skill_req in activity.skill_requirements.all():
            # Get user's skill level for this requirement
            user_skill = Skill.objects.filter(
                user_profile=user,
                generic_skill=skill_req.generic_skill
            ).first()
            
            # Calculate gap (if user has the skill)
            if user_skill:
                gap = max(0, skill_req.level_required - user_skill.level)
            else:
                # User doesn't have the skill at all
                gap = skill_req.level_required
                
            skill_gaps.append(gap)
            skill_weights.append(skill_req.importance)
        
        # Calculate weighted average of skill gaps
        if skill_gaps:
            return sum(g * w for g, w in zip(skill_gaps, skill_weights)) / sum(skill_weights)
        return 0
