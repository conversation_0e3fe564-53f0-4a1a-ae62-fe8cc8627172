from django.db.models import Q, F, Sum, Avg
from apps.user.models import (
    SkillDefinition, SkillDomainApplication, UserAttributeProficiency, 
    SkillAttribute
)
from apps.user.services.skill_service import SkillService
import logging

logger = logging.getLogger(__name__)

class ActivitySkillIntegration:
    """
    Utility class for integrating the dynamic skill composition system with
    activity models and challenge calculations.
    
    This class provides methods for calculating skill requirements for activities,
    analyzing user competencies relative to activities, and incorporating skill
    data into activity tailoring.
    """
    
    @staticmethod
    def get_relevant_skills_for_activity(activity, min_relevance=30):
        """
        Identify skills relevant to an activity based on its domains.
        
        Args:
            activity: GenericActivity or ActivityTailored instance
            min_relevance: Minimum relevance threshold (1-100)
            
        Returns:
            list: Relevant skills with their domain applications and relevance scores
        """
        # Get activity domains
        domains = []
        if hasattr(activity, 'domain_relationships'):
            for rel in activity.domain_relationships.all():
                domains.append({
                    'domain': rel.domain,
                    'strength': rel.strength
                })
        
        if not domains:
            return []
            
        # Get skills applied to these domains
        relevant_skills = []
        
        for domain_info in domains:
            domain = domain_info['domain']
            domain_strength = domain_info['strength']
            
            # Find skill applications for this domain
            applications = SkillDomainApplication.objects.filter(
                domain=domain,
                relevance__gte=min_relevance
            ).select_related('skill')
            
            for app in applications:
                # Calculate weighted relevance based on domain strength
                weighted_relevance = (app.relevance * domain_strength) / 100
                
                # Only include if it meets the threshold after weighting
                if weighted_relevance >= min_relevance:
                    relevant_skills.append({
                        'skill': app.skill,
                        'domain': domain,
                        'domain_strength': domain_strength,
                        'base_relevance': app.relevance,
                        'weighted_relevance': weighted_relevance,
                        'domain_application': app
                    })
        
        # Sort by weighted relevance (most relevant first)
        return sorted(relevant_skills, key=lambda x: x['weighted_relevance'], reverse=True)
    
    @staticmethod
    def calculate_activity_skill_requirements(activity, challenge_level=None):
        """
        Calculate the skill requirements for an activity based on its domains
        and optional challenge level.
        
        Args:
            activity: GenericActivity or ActivityTailored instance
            challenge_level: Optional overall challenge level (0-100)
            
        Returns:
            dict: Skill requirements with required levels
        """
        # Get relevant skills
        relevant_skills = ActivitySkillIntegration.get_relevant_skills_for_activity(activity)
        
        if not relevant_skills:
            return {
                'skill_requirements': [],
                'attributes_involved': []
            }
        
        # Determine challenge scaling
        base_challenge = 50  # Default medium challenge
        if challenge_level is not None:
            base_challenge = challenge_level
        elif hasattr(activity, 'user_requirements_summary') and activity.user_requirements_summary:
            base_challenge = activity.user_requirements_summary.difficulty_rating
        
        # Calculate required levels
        skill_requirements = []
        all_attributes = set()
        
        for skill_info in relevant_skills:
            skill = skill_info['skill']
            weighted_relevance = skill_info['weighted_relevance']
            
            # Scale requirement based on challenge level and relevance
            required_level = (base_challenge * weighted_relevance) / 100
            
            # Get skill attributes for reference
            attributes = skill.attribute_compositions.select_related('attribute').all()
            skill_attributes = [
                {'id': comp.attribute.id, 'name': comp.attribute.name, 'weight': comp.weight}
                for comp in attributes
            ]
            
            # Add to requirements list
            skill_requirements.append({
                'skill_id': skill.id,
                'skill_name': skill.name,
                'domain_id': skill_info['domain'].id,
                'domain_name': skill_info['domain'].name,
                'relevance': weighted_relevance,
                'required_level': required_level,
                'attributes': skill_attributes
            })
            
            # Track all involved attributes
            for attr in skill_attributes:
                all_attributes.add((attr['id'], attr['name']))
        
        # Format attribute list
        attributes_involved = [{'id': attr_id, 'name': attr_name} for attr_id, attr_name in all_attributes]
        
        return {
            'skill_requirements': sorted(skill_requirements, key=lambda x: x['required_level'], reverse=True),
            'attributes_involved': sorted(attributes_involved, key=lambda x: x['name'])
        }
    
    @staticmethod
    def analyze_activity_skill_match(activity, user_profile):
        """
        Analyze how well a user's skills match an activity's requirements.
        
        Args:
            activity: GenericActivity or ActivityTailored instance
            user_profile: UserProfile instance
            
        Returns:
            dict: Analysis of skill match with gap information
        """
        # Get activity requirements
        requirements = ActivitySkillIntegration.calculate_activity_skill_requirements(activity)
        
        if not requirements['skill_requirements']:
            return {
                'overall_match': 100,
                'skill_matches': [],
                'attribute_matches': [],
                'recommendations': []
            }
        
        # Analyze skill matches
        skill_matches = []
        total_relevance = 0
        weighted_match = 0
        
        for req in requirements['skill_requirements']:
            skill_id = req['skill_id']
            domain_id = req['domain_id']
            required_level = req['required_level']
            relevance = req['relevance']
            
            # Get user's skill level
            try:
                skill = SkillDefinition.objects.get(id=skill_id)
                from apps.activity.models import GenericDomain # Renamed import
                domain = GenericDomain.objects.get(id=domain_id) # Use renamed model
                
                result = SkillService.get_user_skill_level(user_profile, skill, domain)
                user_level = result['level']
                
                # Calculate match percentage
                if required_level <= 0:
                    match_percentage = 100  # Avoid division by zero
                else:
                    match_percentage = min(100, (user_level / required_level) * 100)
                
                # Calculate weighted contribution to overall match
                weighted_contribution = match_percentage * relevance
                
                # Add to skill matches
                skill_matches.append({
                    'skill_id': skill_id,
                    'skill_name': req['skill_name'],
                    'domain_id': domain_id,
                    'domain_name': req['domain_name'],
                    'required_level': required_level,
                    'user_level': user_level,
                    'match_percentage': match_percentage,
                    'relevance': relevance,
                    'weighted_contribution': weighted_contribution,
                    'skill_details': result
                })
                
                # Update totals
                total_relevance += relevance
                weighted_match += weighted_contribution
                
            except (SkillDefinition.DoesNotExist, GenericDomain.DoesNotExist): # Use renamed model
                logger.warning(f"Could not analyze skill match for skill {skill_id}, domain {domain_id}")
                continue
        
        # Calculate overall match percentage
        if total_relevance > 0:
            overall_match = weighted_match / total_relevance
        else:
            overall_match = 100  # Default if no relevance data
        
        # Analyze attribute matches
        attribute_matches = ActivitySkillIntegration._analyze_attribute_matches(
            requirements['attributes_involved'],
            user_profile
        )
        
        # Generate recommendations
        recommendations = ActivitySkillIntegration._generate_recommendations(
            skill_matches, attribute_matches
        )
        
        return {
            'overall_match': overall_match,
            'skill_matches': sorted(skill_matches, key=lambda x: x['relevance'], reverse=True),
            'attribute_matches': attribute_matches,
            'recommendations': recommendations
        }
    
    @staticmethod
    def _analyze_attribute_matches(attributes_involved, user_profile):
        """
        Analyze user's proficiency in attributes involved in an activity.
        
        Args:
            attributes_involved: List of attributes from calculate_activity_skill_requirements
            user_profile: UserProfile instance
            
        Returns:
            list: Analysis of attribute matches
        """
        attribute_matches = []
        
        for attr_info in attributes_involved:
            attr_id = attr_info['id']
            
            # Get user's proficiency for this attribute
            proficiency = user_profile.attribute_proficiencies.filter(
                attribute_id=attr_id
            ).first()
            
            if proficiency:
                # Calculate effective level
                effective_level = SkillService.calculate_effective_attribute_level(proficiency)
                
                attribute_matches.append({
                    'attribute_id': attr_id,
                    'attribute_name': attr_info['name'],
                    'base_level': proficiency.level,
                    'effective_level': effective_level,
                    'last_practiced': proficiency.last_practiced,
                    'practice_frequency': proficiency.practice_frequency
                })
            else:
                # User has no recorded proficiency in this attribute
                attribute_matches.append({
                    'attribute_id': attr_id,
                    'attribute_name': attr_info['name'],
                    'base_level': 0,
                    'effective_level': 0,
                    'last_practiced': None,
                    'practice_frequency': None
                })
        
        # Sort by effective level (ascending - weakest first)
        return sorted(attribute_matches, key=lambda x: x['effective_level'])
    
    @staticmethod
    def _generate_recommendations(skill_matches, attribute_matches):
        """
        Generate recommendations based on skill and attribute matches.
        
        Args:
            skill_matches: Skill match analysis from analyze_activity_skill_match
            attribute_matches: Attribute match analysis from _analyze_attribute_matches
            
        Returns:
            list: Recommendations for improving match
        """
        recommendations = []
        
        # Focus on skills with lowest match percentages and high relevance
        weak_skills = [s for s in skill_matches if s['match_percentage'] < 70]
        weak_skills.sort(key=lambda x: x['match_percentage'])
        
        # Get the weakest attributes
        weak_attributes = [a for a in attribute_matches if a['effective_level'] < 30]
        
        # Generate skill-based recommendations
        for skill in weak_skills[:2]:  # Focus on top 2 weak skills
            recommendations.append({
                'type': 'skill_development',
                'skill_id': skill['skill_id'],
                'skill_name': skill['skill_name'],
                'domain_id': skill['domain_id'],
                'domain_name': skill['domain_name'],
                'description': f"Develop your {skill['skill_name']} skills relevant to {skill['domain_name']} activities"
            })
        
        # Generate attribute-based recommendations
        for attr in weak_attributes[:3]:  # Focus on top 3 weak attributes
            recommendations.append({
                'type': 'attribute_development',
                'attribute_id': attr['attribute_id'],
                'attribute_name': attr['attribute_name'],
                'description': f"Work on developing your {attr['attribute_name']}"
            })
        
        return recommendations
    
    @staticmethod
    def record_activity_attribute_usage(activity, user_profile):
        """
        Record attribute usage when a user completes an activity.
        
        Args:
            activity: The completed activity
            user_profile: The user who completed the activity
            
        Returns:
            dict: Results of recording usage
        """
        requirements = ActivitySkillIntegration.calculate_activity_skill_requirements(activity)
        
        if not requirements['attributes_involved']:
            return {'success': False, 'message': 'No attributes involved in this activity'}
        
        # Get attribute IDs
        attribute_ids = [attr['id'] for attr in requirements['attributes_involved']]
        
        # Record usage
        return SkillService.record_attribute_usage(user_profile, attribute_ids, {
            'activity_id': str(activity.id),
            'activity_type': activity.__class__.__name__,
            'domains': [rel.domain.code for rel in activity.domain_relationships.all()]
        })
