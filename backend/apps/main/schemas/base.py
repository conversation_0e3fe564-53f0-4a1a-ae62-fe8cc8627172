import logging
from pydantic import BaseModel, ConfigDict

logger = logging.getLogger(__name__)

class GoaliBaseModel(BaseModel):
    model_config = ConfigDict(
        extra='forbid',  # Prevent unexpected fields
        validate_assignment=True,  # Validate on attribute assignment
        json_schema_serialization_defaults=True
    )
    
    def __init__(self, **data):
        try:
            super().__init__(**data)
            logger.debug(f"Successfully initialized {self.__class__.__name__}", 
                extra={"model": self.__class__.__name__, "data": data})
        except Exception as e:
            logger.error(f"Failed to initialize {self.__class__.__name__}", 
                extra={"model": self.__class__.__name__, 
                      "data": data, 
                      "error": str(e)}, 
                exc_info=True)
            raise
