"""
Utilities for converting between Pydantic models and dictionaries.

This module provides functions for converting between Pydantic models and dictionaries,
which is useful for integrating Pydantic models with existing code that expects dictionaries.
"""

import logging
from typing import Dict, Any, Type, TypeVar, Optional, Union, List
from pydantic import BaseModel

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BaseModel)

def model_to_dict(model: BaseModel) -> Dict[str, Any]:
    """
    Convert a Pydantic model to a dictionary.
    
    Args:
        model: The Pydantic model to convert
        
    Returns:
        A dictionary representation of the model
    """
    return model.model_dump()


def dict_to_model(data: Dict[str, Any], model_class: Type[T], strict: bool = False) -> T:
    """
    Convert a dictionary to a Pydantic model.
    
    Args:
        data: The dictionary to convert
        model_class: The Pydantic model class to convert to
        strict: Whether to enforce strict validation
        
    Returns:
        An instance of the specified model class
        
    Raises:
        ValidationError: If the data does not match the model schema
    """
    try:
        return model_class.model_validate(data, strict=strict)
    except Exception as e:
        logger.error(f"Failed to convert dictionary to {model_class.__name__}",
                    extra={"data": data, "error": str(e)},
                    exc_info=True)
        raise


def convert_model_list(models: List[BaseModel]) -> List[Dict[str, Any]]:
    """
    Convert a list of Pydantic models to a list of dictionaries.
    
    Args:
        models: The list of Pydantic models to convert
        
    Returns:
        A list of dictionary representations of the models
    """
    return [model_to_dict(model) for model in models]


def convert_dict_list(data_list: List[Dict[str, Any]], model_class: Type[T], strict: bool = False) -> List[T]:
    """
    Convert a list of dictionaries to a list of Pydantic models.
    
    Args:
        data_list: The list of dictionaries to convert
        model_class: The Pydantic model class to convert to
        strict: Whether to enforce strict validation
        
    Returns:
        A list of instances of the specified model class
        
    Raises:
        ValidationError: If any of the dictionaries do not match the model schema
    """
    return [dict_to_model(data, model_class, strict=strict) for data in data_list]


def merge_model_with_dict(model: BaseModel, data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge a Pydantic model with a dictionary.
    
    Args:
        model: The Pydantic model to merge
        data: The dictionary to merge with
        
    Returns:
        A dictionary containing the merged data
    """
    model_dict = model_to_dict(model)
    merged = {**model_dict, **data}
    return merged


def update_model_from_dict(model: BaseModel, data: Dict[str, Any]) -> BaseModel:
    """
    Update a Pydantic model from a dictionary.
    
    Args:
        model: The Pydantic model to update
        data: The dictionary containing the updates
        
    Returns:
        An updated instance of the model
    """
    model_dict = model_to_dict(model)
    merged = {**model_dict, **data}
    return type(model).model_validate(merged)


def safe_dict_to_model(data: Dict[str, Any], model_class: Type[T], default: Optional[T] = None) -> Optional[T]:
    """
    Safely convert a dictionary to a Pydantic model, returning a default value on failure.
    
    Args:
        data: The dictionary to convert
        model_class: The Pydantic model class to convert to
        default: The default value to return on failure
        
    Returns:
        An instance of the specified model class, or the default value on failure
    """
    try:
        return dict_to_model(data, model_class)
    except Exception as e:
        logger.warning(f"Failed to convert dictionary to {model_class.__name__}, using default",
                      extra={"data": data, "error": str(e)})
        return default


def convert_django_model_to_pydantic(django_model, pydantic_model_class: Type[T]) -> T:
    """
    Convert a Django model instance to a Pydantic model.
    
    Args:
        django_model: The Django model instance to convert
        pydantic_model_class: The Pydantic model class to convert to
        
    Returns:
        An instance of the specified Pydantic model class
    """
    # Convert Django model to dictionary
    data = {}
    for field in django_model._meta.fields:
        field_name = field.name
        field_value = getattr(django_model, field_name)
        data[field_name] = field_value
    
    # Convert dictionary to Pydantic model
    return dict_to_model(data, pydantic_model_class)
