import logging
from typing import Dict, Any, Optional, List, Callable, Tuple
from .base import GoaliBaseModel, logger

class SchemaMigration(GoaliBaseModel):
    """Base class for schema migrations.
    
    This class provides a framework for migrating data between schema versions.
    """
    SOURCE_VERSION: str
    TARGET_VERSION: str
    SCHEMA_TYPE: str
    
    def migrate(self, old_data: dict) -> dict:
        """Migrate data from the source version to the target version.
        
        Args:
            old_data: The data to migrate.
            
        Returns:
            dict: The migrated data.
        """
        logger.info("Starting schema migration",
            extra={
                "from_version": old_data.get("version", self.SOURCE_VERSION),
                "to_version": self.TARGET_VERSION,
                "schema_type": self.SCHEMA_TYPE
            })
        
        # Implement migration logic in subclasses
        result = self._migrate_data(old_data.copy())
        
        # Update version
        result["version"] = self.TARGET_VERSION
        
        logger.info("Schema migration completed",
            extra={
                "from_version": old_data.get("version", self.SOURCE_VERSION),
                "to_version": self.TARGET_VERSION,
                "schema_type": self.SCHEMA_TYPE
            })
        
        return result
    
    def _migrate_data(self, data: dict) -> dict:
        """Implement the actual migration logic.
        
        This method should be overridden by subclasses.
        
        Args:
            data: The data to migrate.
            
        Returns:
            dict: The migrated data.
        """
        raise NotImplementedError("Subclasses must implement _migrate_data")


class MigrationRegistry:
    """Registry for schema migrations.
    
    This class maintains a registry of migration classes for different schema types
    and versions, and provides methods for finding and applying migrations.
    """
    
    def __init__(self):
        self.migrations: Dict[str, Dict[Tuple[str, str], type]] = {}
    
    def register(self, migration_class: type) -> None:
        """Register a migration class.
        
        Args:
            migration_class: A subclass of SchemaMigration.
            
        Raises:
            TypeError: If the class is not a subclass of SchemaMigration.
            ValueError: If the class does not define required class variables.
        """
        if not issubclass(migration_class, SchemaMigration):
            raise TypeError(f"Migration class must be a subclass of SchemaMigration")
        
        # Check for required class variables
        for attr in ["SOURCE_VERSION", "TARGET_VERSION", "SCHEMA_TYPE"]:
            if not hasattr(migration_class, attr):
                raise ValueError(f"Migration class must define {attr}")
        
        schema_type = migration_class.SCHEMA_TYPE
        source_version = migration_class.SOURCE_VERSION
        target_version = migration_class.TARGET_VERSION
        
        if schema_type not in self.migrations:
            self.migrations[schema_type] = {}
        
        self.migrations[schema_type][(source_version, target_version)] = migration_class
        logger.info(f"Registered migration from {source_version} to {target_version} for {schema_type}")
    
    def get_migration(self, schema_type: str, source_version: str, 
                     target_version: str) -> Optional[type]:
        """Get a migration class for a specific schema type and version transition.
        
        Args:
            schema_type: The type of schema.
            source_version: The source version.
            target_version: The target version.
            
        Returns:
            Optional[type]: The migration class, or None if no migration is available.
        """
        if schema_type not in self.migrations:
            return None
        
        return self.migrations[schema_type].get((source_version, target_version))
    
    def find_migration_path(self, schema_type: str, source_version: str, 
                           target_version: str) -> Optional[List[Tuple[str, str]]]:
        """Find a path of migrations from one version to another.
        
        Uses a breadth-first search to find the shortest path.
        
        Args:
            schema_type: The type of schema.
            source_version: The source version.
            target_version: The target version.
            
        Returns:
            Optional[List[Tuple[str, str]]]: A list of version pairs forming a path from source to target,
                or None if no path exists.
        """
        if schema_type not in self.migrations:
            return None
        
        # If versions are the same, no migration needed
        if source_version == target_version:
            return []
        
        # Check for direct migration
        if (source_version, target_version) in self.migrations[schema_type]:
            return [(source_version, target_version)]
        
        # Initialize BFS
        queue = [(source_version, [])]
        visited = {source_version}
        
        while queue:
            current_version, path = queue.pop(0)
            
            # Check all possible next steps
            for (src, dst) in self.migrations[schema_type].keys():
                if src == current_version and dst not in visited:
                    new_path = path + [(src, dst)]
                    if dst == target_version:
                        return new_path
                    
                    visited.add(dst)
                    queue.append((dst, new_path))
        
        return None
    
    def migrate(self, schema_type: str, data: Dict[str, Any], 
               source_version: str, target_version: str) -> Dict[str, Any]:
        """Migrate data from one version to another.
        
        Args:
            schema_type: The type of schema.
            data: The data to migrate.
            source_version: The source version.
            target_version: The target version.
            
        Returns:
            Dict[str, Any]: The migrated data.
            
        Raises:
            ValueError: If no migration path is available.
        """
        # If versions are the same, no migration needed
        if source_version == target_version:
            return data.copy()
        
        # Find migration path
        path = self.find_migration_path(schema_type, source_version, target_version)
        if not path:
            raise ValueError(
                f"No migration path available from {source_version} to {target_version} for {schema_type}"
            )
        
        # Apply migrations in sequence
        current_data = data.copy()
        for src_version, dst_version in path:
            migration_class = self.migrations[schema_type][(src_version, dst_version)]
            migration = migration_class()
            current_data = migration.migrate(current_data)
        
        return current_data


# Global migration registry instance
migration_registry = MigrationRegistry()


def register_migration(migration_class: type) -> type:
    """Decorator for registering a migration class.
    
    This decorator registers a migration class with the global migration registry
    and returns the class unchanged.
    
    Args:
        migration_class: A subclass of SchemaMigration.
        
    Returns:
        type: The migration class.
        
    Example:
        @register_migration
        class UserProfileV1ToV2(SchemaMigration):
            SOURCE_VERSION = "1.0.0"
            TARGET_VERSION = "2.0.0"
            SCHEMA_TYPE = "user_profile"
            
            def _migrate_data(self, data: dict) -> dict:
                # Migration logic here
                return data
    """
    migration_registry.register(migration_class)
    return migration_class
