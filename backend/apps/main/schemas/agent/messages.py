from typing import Dict, Any, Optional, List, Union
from ..base import GoaliBaseModel, logger
from ..version import VersionedModel


class AgentMessage(VersionedModel):
    """Base class for agent messages.
    
    This class provides common functionality for all agent message types.
    """
    message_type: str
    agent_id: str
    content: Dict[str, Any] = {}
    
    @classmethod
    def validate_message(cls, data: dict) -> 'AgentMessage':
        """Validate a message against the schema.
        
        Args:
            data: The message data to validate.
            
        Returns:
            AgentMessage: The validated message instance.
            
        Raises:
            Exception: If validation fails.
        """
        try:
            instance = cls(**data)
            logger.info("Message validation successful",
                extra={"message_type": instance.message_type,
                      "agent_id": instance.agent_id})
            return instance
        except Exception as e:
            logger.error("Message validation failed",
                extra={"data": data, "error": str(e)},
                exc_info=True)
            raise


class UserMessage(AgentMessage):
    """Message from a user to an agent."""
    message_type: str = "user_message"
    query: str
    context: Optional[Dict[str, Any]] = None


class AgentResponse(AgentMessage):
    """Response from an agent to a user."""
    message_type: str = "agent_response"
    response_text: str
    confidence: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


class PsychologicalAssessment(VersionedModel):
    """Psychological assessment model.
    
    This model represents a psychological assessment of a user, including
    personality traits, communication preferences, and trust levels.
    """
    version: str = "1.0.0"
    
    # HEXACO personality traits (0-100 scale)
    honesty_humility: Optional[int] = None
    emotionality: Optional[int] = None
    extraversion: Optional[int] = None
    agreeableness: Optional[int] = None
    conscientiousness: Optional[int] = None
    openness: Optional[int] = None
    
    # Communication preferences
    communication_style: Optional[str] = None
    learning_style: Optional[str] = None
    feedback_preference: Optional[str] = None
    
    # Trust levels
    trust_level: Optional[int] = None
    trust_phase: Optional[str] = None
    
    # Assessment confidence (0-100 scale)
    confidence: Optional[int] = None
    
    # Assessment notes
    notes: Optional[str] = None
    
    def get_trust_phase(self) -> str:
        """Get the trust phase based on the trust level.
        
        Returns:
            str: The trust phase ('foundation', 'expansion', or 'integration').
        """
        if self.trust_level is None:
            return "foundation"
        
        if self.trust_level < 40:
            return "foundation"
        elif self.trust_level < 70:
            return "expansion"
        else:
            return "integration"
    
    def update_trust_phase(self) -> None:
        """Update the trust phase based on the current trust level."""
        self.trust_phase = self.get_trust_phase()


class AgentAssessmentMessage(AgentMessage):
    """Message containing a psychological assessment from an agent."""
    message_type: str = "agent_assessment"
    assessment: PsychologicalAssessment
    reasoning: Optional[str] = None
