# backend/apps/main/schemas/agent/outputs.py
"""
Pydantic models defining the expected output structures for various agents.
These models are used for validation, especially in testing scenarios,
to ensure agents produce consistent and correctly typed outputs.
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class BaseAgentOutput(BaseModel):
    """Base model for agent outputs, allowing extra fields."""
    class Config:
        extra = 'allow'


class EngagementAgentOutput(BaseAgentOutput):
    """
    Expected output structure for the Engagement Agent.
    Error 14/15 ('NoneType' is not iterable) suggests engagement_analysis
    is expected to be iterable. We'll define it as a dictionary.
    """
    engagement_analysis: Dict[str, Any] = Field(
        default_factory=dict,
        description="Analysis results from the engagement agent."
    )
    # Add other known fields if necessary


class WheelActivity(BaseModel):
    """
    Model for an activity in the wheel.
    """
    id: str = Field(..., description="Unique identifier for the activity")
    name: str = Field(..., description="Name of the activity")
    description: str = Field(..., description="Description of the activity")
    domain: str = Field(..., description="Domain of the activity (e.g., creative, reflective)")
    duration: int = Field(..., description="Duration of the activity in minutes")
    challenge_level: int = Field(..., description="Challenge level of the activity (0-100)")
    resources_required: List[str] = Field(default_factory=list, description="List of resources required for the activity")
    estimated_completion_time: int = Field(..., description="Estimated time to complete the activity in minutes")
    resource_intensity: Optional[str] = Field(None, description="Intensity of resource usage (low, medium, high)")
    adaptability: Optional[Dict[str, Any]] = Field(None, description="Adaptability options for the activity")
    instructions: Optional[str] = Field(None, description="Instructions for the activity")


class WheelItem(BaseModel):
    """
    Model for an item in the wheel.
    """
    id: str = Field(..., description="Unique identifier for the wheel item")
    activity_id: str = Field(..., description="ID of the associated activity")
    percentage: int = Field(..., description="Percentage of the wheel occupied by this item")
    position: Optional[int] = Field(None, description="Position of the item in the wheel")
    color: Optional[str] = Field(None, description="Color of the wheel item")


class WheelMetadata(BaseModel):
    """
    Model for wheel metadata.
    """
    name: str = Field(..., description="Name of the wheel")
    trust_phase: Optional[str] = Field(None, description="Trust phase for the wheel")
    description: Optional[str] = Field(None, description="Description of the wheel")
    timestamp: Optional[str] = Field(None, description="Timestamp when the wheel was created")
    user_id: Optional[str] = Field(None, description="ID of the user the wheel is for")


class Wheel(BaseModel):
    """
    Model for the complete wheel structure.
    """
    metadata: WheelMetadata = Field(..., description="Metadata for the wheel")
    items: List[WheelItem] = Field(default_factory=list, description="List of wheel items")
    activities: List[WheelActivity] = Field(default_factory=list, description="List of activities in the wheel")
    value_propositions: Dict[str, Any] = Field(default_factory=dict, description="Value propositions for the activities")
    timestamp: Optional[str] = Field(None, description="Timestamp when the wheel was created")
    user_id: Optional[str] = Field(None, description="ID of the user the wheel is for")


class WheelActivityAgentOutput(BaseAgentOutput):
    """
    Expected output structure for the Wheel Activity Agent.
    The agent implementation uses 'wheel' as the key for wheel data,
    which contains metadata, items, activities, and value propositions.
    """
    wheel: Wheel = Field(
        ...,
        description="Wheel data structure containing metadata, items, activities, and value propositions."
    )
    next_agent: Optional[str] = Field(
        default="ethical",
        description="The next agent to route to, defaults to 'ethical'."
    )


class OrchestratorAgentOutput(BaseAgentOutput):
    """
    Expected output structure for the Orchestrator Agent.
    Error 53 ('dict' object has no attribute 'lower') implies 'next_agent'
    was incorrectly a dictionary instead of a string.
    """
    next_agent: Optional[str] = Field(
        default=None,
        description="The name of the next agent to route to."
    )
    # Add other known fields if necessary

# Add other agent output models here as needed, e.g.:
# class MentorAgentOutput(BaseAgentOutput): ...
# class EthicalAgentOutput(BaseAgentOutput): ...
# class ResourceAgentOutput(BaseAgentOutput): ...
