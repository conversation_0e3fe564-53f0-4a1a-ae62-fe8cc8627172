"""
Schema registry updates for enhanced context schema.

This module registers the new schema versions and migration functions
for the enhanced context schema.
"""

import logging
import os
import json
from typing import Dict, Any, Optional

from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_version_manager import SchemaVersionManager
from apps.main.schemas.migrations.situation_v1_to_v2 import migrate_situation_v1_to_v2

logger = logging.getLogger(__name__)

def register_enhanced_context_schemas(
    registry: Optional[SchemaRegistry] = None,
    version_manager: Optional[SchemaVersionManager] = None
) -> None:
    """
    Register enhanced context schemas with the schema registry and version manager.
    
    Args:
        registry: Optional schema registry to use. If None, creates a new SchemaRegistry.
        version_manager: Optional schema version manager to use. If None, creates a new SchemaVersionManager.
    """
    # Initialize registry and version manager if not provided
    if registry is None:
        registry = SchemaRegistry()
    
    if version_manager is None:
        version_manager = SchemaVersionManager(registry)
    
    # Get project root directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..'))
    
    # Load situation schema v2.0.0
    situation_schema_v2_path = os.path.join(
        project_root, 'schemas', 'situation', 'versions', 'v2.0.0.schema.json'
    )
    
    try:
        with open(situation_schema_v2_path, 'r') as f:
            situation_schema_v2 = json.load(f)
        
        # Register situation schema v2.0.0
        registry.register_schema("situation", situation_schema_v2, version="2.0.0")
        version_manager.register_schema_version("situation", "2.0.0", situation_schema_v2)
        
        # Register migration function
        version_manager.register_migration(
            "situation", "1.0.0", "2.0.0", migrate_situation_v1_to_v2
        )
        
        logger.info("Registered enhanced context schemas and migrations")
    except Exception as e:
        logger.error(f"Error registering enhanced context schemas: {e}", exc_info=True)
