"""
Schema validation service using Pydantic models.
"""

import logging
import json
import os
from typing import Dict, Any, List, Optional, Type

from pydantic import ValidationError

from .base import VersionedModel
from apps.user.models import UserProfile
from .scenarios import (
    BenchmarkScenario, BenchmarkScenarioMetadata,
    EvaluationCriteria, PhaseAwareCriteria, ToolExpectation
)
from .runs import (
    BenchmarkRun, TokenUsage, StagePerformance, SemanticEvaluation,
    EvaluationScore
)

logger = logging.getLogger(__name__)


class SchemaValidationResult:
    """Result of schema validation."""

    def __init__(self, is_valid: bool, errors: List[str] = None, model_instance: Any = None):
        self.is_valid = is_valid
        self.errors = errors or []
        self.model_instance = model_instance

    def __bool__(self) -> bool:
        return self.is_valid

    def to_dict(self) -> Dict[str, Any]:
        """Convert the validation result to a dictionary."""
        return {
            'valid': self.is_valid,
            'errors': self.errors
        }


class PydanticSchemaValidator:
    """Schema validation service using Pydantic models."""

    # Map of schema types to model classes
    MODEL_REGISTRY: Dict[str, Type[VersionedModel]] = {
        'benchmark_scenario': BenchmarkScenario,
        'benchmark_metadata': BenchmarkScenarioMetadata,
        'user_profile': UserProfile,
        'evaluation_criteria': EvaluationCriteria,
        'phase_aware_criteria': PhaseAwareCriteria,
        'tool_expectation': ToolExpectation,
        'benchmark_run': BenchmarkRun,
        'token_usage': TokenUsage,
        'stage_performance': StagePerformance,
        'semantic_evaluation': SemanticEvaluation,
        'evaluation_score': EvaluationScore,
    }

    def __init__(self):
        """Initialize the schema validator."""
        logger.info("Initializing PydanticSchemaValidator")

    def validate(self, schema_type: str, data: Dict[str, Any]) -> SchemaValidationResult:
        """
        Validate data against a schema.

        Args:
            schema_type: Type of schema to validate against
            data: Data to validate

        Returns:
            SchemaValidationResult with validation results
        """
        if schema_type not in self.MODEL_REGISTRY:
            return SchemaValidationResult(False, [f"Unknown schema type: {schema_type}"])

        model_class = self.MODEL_REGISTRY[schema_type]

        try:
            # Validate using Pydantic
            instance = model_class(**data)
            return SchemaValidationResult(True, [], instance)
        except ValidationError as e:
            # Extract error messages
            errors = []
            for error in e.errors():
                location = '.'.join(str(loc) for loc in error['loc'])
                message = error['msg']
                errors.append(f"{location}: {message}")

            return SchemaValidationResult(False, errors)
        except Exception as e:
            logger.error(f"Unexpected error validating {schema_type}", exc_info=True)
            return SchemaValidationResult(False, [f"Validation error: {str(e)}"])

    def validate_file(self, file_path: str, schema_type: Optional[str] = None) -> SchemaValidationResult:
        """
        Validate a JSON file against a schema.

        Args:
            file_path: Path to the JSON file
            schema_type: Type of schema to validate against. If None, tries to infer from the file.

        Returns:
            SchemaValidationResult with validation results
        """
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)

            # Try to infer schema type if not provided
            if schema_type is None:
                schema_type = self._infer_schema_type(file_path, data)
                if schema_type is None:
                    return SchemaValidationResult(False, ["Could not infer schema type"])

            return self.validate(schema_type, data)
        except json.JSONDecodeError as e:
            return SchemaValidationResult(False, [f"Invalid JSON: {str(e)}"])
        except Exception as e:
            logger.error(f"Error validating file {file_path}", exc_info=True)
            return SchemaValidationResult(False, [f"Validation error: {str(e)}"])

    def _infer_schema_type(self, file_path: str, data: Dict[str, Any]) -> Optional[str]:
        """
        Infer the schema type from the file path and data.

        Args:
            file_path: Path to the file
            data: Data from the file

        Returns:
            Inferred schema type, or None if it couldn't be inferred
        """
        # Check if the schema_type is explicitly specified in the data
        if 'schema_type' in data:
            schema_type = data['schema_type']
            if schema_type in self.MODEL_REGISTRY:
                return schema_type

        # Try to infer from file path
        file_name = os.path.basename(file_path)
        if 'scenario' in file_name.lower():
            return 'benchmark_scenario'
        elif 'run' in file_name.lower():
            return 'benchmark_run'
        elif 'user_profile' in file_name.lower():
            return 'user_profile'
        elif 'evaluation' in file_name.lower() and 'criteria' in file_name.lower():
            return 'evaluation_criteria'

        # Try to infer from data structure
        if 'agent_role' in data and 'input_data' in data and 'metadata' in data:
            return 'benchmark_scenario'
        elif 'scenario_id' in data and 'execution_date' in data:
            return 'benchmark_run'
        elif 'workflow_type' in data and 'user_profile_context' in data:
            return 'benchmark_metadata'
        elif 'trust_phase' in data:
            return 'user_profile'
        elif 'criteria' in data:
            return 'evaluation_criteria'
        elif 'input_tokens' in data and 'output_tokens' in data:
            return 'token_usage'
        elif 'stage_name' in data and 'status' in data:
            return 'stage_performance'
        elif 'evaluator_model' in data and 'scores' in data:
            return 'semantic_evaluation'
        elif 'dimension' in data and 'score' in data:
            return 'evaluation_score'

        return None

    def validate_benchmark_scenario(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a complete benchmark scenario including all components.

        Args:
            data: Complete benchmark scenario data

        Returns:
            Dict with validation results for each component
        """
        result = {
            'valid': True,
            'errors': [],
            'components': {}
        }

        # Validate the main scenario
        scenario_result = self.validate('benchmark_scenario', data)
        result['components']['scenario'] = scenario_result.to_dict()

        if not scenario_result.is_valid:
            result['valid'] = False
            result['errors'].extend([f"Scenario: {error}" for error in scenario_result.errors])
            return result

        # Validate metadata
        if 'metadata' in data:
            metadata_result = self.validate('benchmark_metadata', data['metadata'])
            result['components']['metadata'] = metadata_result.to_dict()

            if not metadata_result.is_valid:
                result['valid'] = False
                result['errors'].extend([f"Metadata: {error}" for error in metadata_result.errors])

        # Validate user profile if present
        if 'metadata' in data and 'user_profile_context' in data['metadata']:
            user_profile_result = self.validate('user_profile', data['metadata']['user_profile_context'])
            result['components']['user_profile'] = user_profile_result.to_dict()

            if not user_profile_result.is_valid:
                result['valid'] = False
                result['errors'].extend([f"User profile: {error}" for error in user_profile_result.errors])

        return result
