"""
Utilities for benchmark naming conventions.
"""

import os
import re
import logging
from typing import Dict, Any, List, Optional, Set
from enum import Enum

logger = logging.getLogger(__name__)


class SchemaAgentRole(str, Enum):
    """Standard agent roles in the system (schema version)."""
    MENTOR = "mentor"
    ORCHESTRATOR = "orchestrator"
    STRATEGY = "strategy"
    EVALUATOR = "evaluator"
    TEST = "test"


class WorkflowType(str, Enum):
    """Standard workflow types in the system."""
    WHEEL_GENERATION = "wheel_generation"
    ACTIVITY_FEEDBACK = "activity_feedback"
    DISCUSSION = "discussion"
    ONBOARDING = "onboarding"
    GOAL_SETTING = "goal_setting"
    TEST = "test_workflow"


class ScenarioType(str, Enum):
    """Standard scenario types in the system."""
    BASIC = "basic"
    COMPLEX = "complex"
    ERROR_HANDLING = "error_handling"
    EDGE_CASE = "edge_case"
    PERFORMANCE = "performance"
    INTEGRATION = "integration"
    REGRESSION = "regression"
    TEST = "test"


class ScenarioVariant(str, Enum):
    """Standard scenario variants in the system."""
    TRUST_LOW = "trust_low"
    TRUST_MEDIUM = "trust_medium"
    TRUST_HIGH = "trust_high"
    PERSONALITY_OPEN = "personality_open"
    PERSONALITY_CLOSED = "personality_closed"
    ENVIRONMENT_NOISY = "environment_noisy"
    ENVIRONMENT_QUIET = "environment_quiet"
    MOOD_POSITIVE = "mood_positive"
    MOOD_NEGATIVE = "mood_negative"
    STRESS_HIGH = "stress_high"
    STRESS_LOW = "stress_low"
    TIME_LIMITED = "time_limited"
    TIME_UNLIMITED = "time_unlimited"


class BenchmarkNamingConvention:
    """Utility for parsing and generating benchmark scenario names."""
    
    # Regular expression pattern for parsing benchmark names
    PATTERN = r'^(?P<agent_role>[a-z_]+)_(?P<workflow_type>[a-z_]+)_(?P<scenario_type>[a-z_]+)(_(?P<variant>[a-z_]+))?_(?P<id>\d+)$'
    
    # Directory structure patterns
    DIR_PATTERN = r'^(?P<base_dir>agents|workflows)/(?P<agent_role>[a-z_]+)?/?(?P<workflow_type>[a-z_]+)?$'
    
    @classmethod
    def parse_name(cls, name: str) -> Dict[str, str]:
        """
        Parse a benchmark name into its components.
        
        Args:
            name: Benchmark name to parse
            
        Returns:
            Dictionary with parsed components
            
        Raises:
            ValueError: If the name doesn't follow the convention
        """
        # Remove file extension if present
        if name.endswith('.json'):
            name = name[:-5]
            
        match = re.match(cls.PATTERN, name)
        if not match:
            raise ValueError(f"Invalid benchmark name: {name}")
        
        components = match.groupdict()
        
        # Validate components
        cls._validate_component(components['agent_role'], SchemaAgentRole, 'agent_role')
        cls._validate_component(components['workflow_type'], WorkflowType, 'workflow_type')
        cls._validate_component(components['scenario_type'], ScenarioType, 'scenario_type')
        
        if components['variant']:
            cls._validate_component(components['variant'], ScenarioVariant, 'variant')
        
        return components
    
    @classmethod
    def _validate_component(cls, value: str, enum_class: type, component_name: str) -> None:
        """
        Validate that a component value is valid.
        
        Args:
            value: Component value to validate
            enum_class: Enum class to validate against
            component_name: Name of the component for error messages
            
        Raises:
            ValueError: If the value is not valid
        """
        try:
            enum_class(value)
        except ValueError:
            valid_values = [e.value for e in enum_class]
            logger.warning(
                f"Invalid {component_name}: {value}. Valid values: {valid_values}. "
                f"Using it anyway, but consider using a standard value."
            )
    
    @classmethod
    def generate_name(cls, agent_role: str, workflow_type: str, 
                     scenario_type: str, id: int, variant: Optional[str] = None) -> str:
        """
        Generate a benchmark name from its components.
        
        Args:
            agent_role: Role of the agent being benchmarked
            workflow_type: Type of workflow being benchmarked
            scenario_type: Type of scenario
            id: Numeric identifier
            variant: Optional variant
            
        Returns:
            Generated benchmark name
        """
        # Validate components
        cls._validate_component(agent_role, SchemaAgentRole, 'agent_role')
        cls._validate_component(workflow_type, WorkflowType, 'workflow_type')
        cls._validate_component(scenario_type, ScenarioType, 'scenario_type')
        
        if variant:
            cls._validate_component(variant, ScenarioVariant, 'variant')
            return f"{agent_role}_{workflow_type}_{scenario_type}_{variant}_{id:03d}"
        else:
            return f"{agent_role}_{workflow_type}_{scenario_type}_{id:03d}"
    
    @classmethod
    def validate_name(cls, name: str) -> bool:
        """
        Validate that a name follows the naming convention.
        
        Args:
            name: Name to validate
            
        Returns:
            True if the name is valid, False otherwise
        """
        try:
            cls.parse_name(name)
            return True
        except ValueError:
            return False
