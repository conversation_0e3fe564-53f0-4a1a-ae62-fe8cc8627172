"""
Benchmark schema system using Pydantic models.

This package provides a comprehensive schema validation system for benchmark scenarios,
using Pydantic models for robust validation and clear error messages.
"""

from .scenarios import (
    BenchmarkScenario, BenchmarkScenarioMetadata,
    EvaluationCriteria, PhaseAwareCriteria, ToolExpectation
)
from .naming import (
    BenchmarkNamingConvention, SchemaAgentRole, WorkflowType,
    ScenarioType, ScenarioVariant
)
from .runs import (
    BenchmarkRun, TokenUsage, StagePerformance, SemanticEvaluation,
    EvaluationScore, StageStatus
)
from .validator import PydanticSchemaValidator, SchemaValidationResult

__all__ = [
    # Scenario models
    'BenchmarkScenario', 'BenchmarkScenarioMetadata',
    'EvaluationCriteria', 'PhaseAwareCriteria', 'ToolExpectation',

    # Naming conventions
    'BenchmarkNamingConvention', 'SchemaAgentRole', 'WorkflowType',
    'ScenarioType', 'ScenarioVariant',

    # Run models
    'BenchmarkRun', 'TokenUsage', 'StagePerformance', 'SemanticEvaluation',
    'EvaluationScore', 'StageStatus',

    # Validation
    'PydanticSchemaValidator', 'SchemaValidationResult'
]
