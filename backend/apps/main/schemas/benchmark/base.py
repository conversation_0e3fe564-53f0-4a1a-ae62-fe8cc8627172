"""
Base classes for benchmark schema models.
"""

import logging
import re
import semver
from typing import Dict, Any, Optional, ClassVar, Set, Type, TypeVar
from pydantic import BaseModel, ConfigDict, Field, field_validator

logger = logging.getLogger(__name__)

T = TypeVar('T', bound='VersionedModel')


class BenchmarkBaseModel(BaseModel):
    """Base model for all benchmark schema models."""
    model_config = ConfigDict(
        extra='forbid',  # Prevent unexpected fields
        validate_assignment=True,  # Validate on attribute assignment
        json_schema_serialization_defaults=True,
        populate_by_name=True,  # Allow field aliases
    )

    def __init__(self, **data):
        try:
            super().__init__(**data)
            logger.debug(f"Successfully initialized {self.__class__.__name__}",
                extra={"model": self.__class__.__name__, "data": data})
        except Exception as e:
            logger.error(f"Failed to initialize {self.__class__.__name__}",
                extra={"model": self.__class__.__name__,
                      "data": data,
                      "error": str(e)},
                exc_info=True)
            raise


class VersionedModel(BenchmarkBaseModel):
    """Base model for versioned models."""
    schema_type: ClassVar[str] = "base"
    version: str = "1.0.0"

    @field_validator('version')
    @classmethod
    def validate_version_format(cls, v: str) -> str:
        """Validate that the version is in semver format."""
        try:
            semver.VersionInfo.parse(v)
            return v
        except ValueError:
            raise ValueError(f"Invalid semantic version format: {v}")

    @classmethod
    def get_current_version(cls) -> str:
        """Get the current version of the model."""
        return cls.version

    @classmethod
    def is_compatible_version(cls, version: str) -> bool:
        """Check if the given version is compatible with the current version."""
        try:
            current = semver.VersionInfo.parse(cls.version)
            other = semver.VersionInfo.parse(version)

            # Major version must match for compatibility
            return current.major == other.major
        except ValueError:
            return False

    @classmethod
    def from_dict(cls: Type[T], data: Dict[str, Any]) -> T:
        """Create an instance from a dictionary, handling version compatibility."""
        if 'version' in data:
            version = data['version']
            if not cls.is_compatible_version(version):
                logger.warning(f"Version {version} is not compatible with current version {cls.version}")
                # TODO: Implement version migration

        return cls(**data)


class NamedEntity(BenchmarkBaseModel):
    """Base model for named entities."""
    name: str = Field(..., description="Name of the entity")
    description: Optional[str] = Field(None, description="Description of the entity")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate that the name is not empty and has a reasonable length."""
        if not v:
            raise ValueError("Name cannot be empty")
        if len(v) > 100:
            raise ValueError("Name is too long (max 100 characters)")
        return v


class BenchmarkNamingConvention:
    """Utility for parsing and generating benchmark scenario names."""

    # Regular expression pattern for parsing benchmark names
    PATTERN = r'^(?P<agent_role>[a-z_]+)_(?P<workflow_type>[a-z_]+)_(?P<scenario_type>[a-z_]+)(_(?P<variant>[a-z_]+))?_(?P<id>\d+)$'

    # Directory structure patterns
    DIR_PATTERN = r'^(?P<base_dir>agents|workflows)/(?P<agent_role>[a-z_]+)?/?(?P<workflow_type>[a-z_]+)?$'

    @classmethod
    def parse_name(cls, name: str) -> Dict[str, str]:
        """
        Parse a benchmark name into its components.

        Args:
            name: Benchmark name to parse

        Returns:
            Dictionary with parsed components

        Raises:
            ValueError: If the name doesn't follow the convention
        """
        # Remove file extension if present
        if name.endswith('.json'):
            name = name[:-5]

        match = re.match(cls.PATTERN, name)
        if not match:
            raise ValueError(f"Invalid benchmark name: {name}")

        return match.groupdict()

    @classmethod
    def generate_name(cls, agent_role: str, workflow_type: str,
                     scenario_type: str, id: int, variant: Optional[str] = None) -> str:
        """
        Generate a benchmark name from its components.

        Args:
            agent_role: Role of the agent being benchmarked
            workflow_type: Type of workflow being benchmarked
            scenario_type: Type of scenario
            id: Numeric identifier
            variant: Optional variant

        Returns:
            Generated benchmark name
        """
        if variant:
            return f"{agent_role}_{workflow_type}_{scenario_type}_{variant}_{id:03d}"
        else:
            return f"{agent_role}_{workflow_type}_{scenario_type}_{id:03d}"

    @classmethod
    def validate_name(cls, name: str) -> bool:
        """
        Validate that a name follows the naming convention.

        Args:
            name: Name to validate

        Returns:
            True if the name is valid, False otherwise
        """
        try:
            cls.parse_name(name)
            return True
        except ValueError:
            return False

    @classmethod
    def get_directory_for_scenario(cls, agent_role: str, workflow_type: str) -> str:
        """
        Get the directory path for a scenario based on its components.

        Args:
            agent_role: Role of the agent being benchmarked
            workflow_type: Type of workflow being benchmarked

        Returns:
            Directory path for the scenario
        """
        # Agent-specific scenarios go in agents/{agent_role}/{workflow_type}
        # Workflow-specific scenarios go in workflows/{workflow_type}
        return f"agents/{agent_role}/{workflow_type}" if agent_role else f"workflows/{workflow_type}"

    @classmethod
    def suggest_name_from_metadata(cls, metadata: Dict[str, Any],
                                  agent_role: str, existing_ids: Set[int] = None) -> str:
        """
        Suggest a name based on metadata.

        Args:
            metadata: Scenario metadata
            agent_role: Role of the agent being benchmarked
            existing_ids: Set of existing IDs to avoid duplicates

        Returns:
            Suggested name
        """
        workflow_type = metadata.get('workflow_type', 'test_workflow')

        # Determine scenario type based on metadata
        scenario_type = cls._determine_scenario_type(metadata)

        # Determine variant based on metadata
        variant = cls._determine_variant(metadata)

        # Determine ID
        id = 1
        if existing_ids:
            id = max(existing_ids) + 1 if existing_ids else 1

        return cls.generate_name(agent_role, workflow_type, scenario_type, id, variant)

    @classmethod
    def _determine_scenario_type(cls, metadata: Dict[str, Any]) -> str:
        """
        Determine the scenario type based on metadata.

        Args:
            metadata: Scenario metadata

        Returns:
            Determined scenario type
        """
        # Check for error simulation
        if 'error_simulations' in metadata and metadata['error_simulations']:
            return "error_handling"

        # Check for complex criteria
        if 'expected_quality_criteria' in metadata and metadata['expected_quality_criteria']:
            criteria = metadata['expected_quality_criteria']
            if isinstance(criteria, dict) and len(criteria) > 3:
                return "complex"

        # Check for performance expectations
        if 'performance_expectations' in metadata:
            return "performance"

        # Default to basic
        return "basic"

    @classmethod
    def _determine_variant(cls, metadata: Dict[str, Any]) -> Optional[str]:
        """
        Determine the scenario variant based on metadata.

        Args:
            metadata: Scenario metadata

        Returns:
            Determined variant, or None if no variant applies
        """
        # Check for user profile
        if 'user_profile_context' in metadata and metadata['user_profile_context']:
            profile = metadata['user_profile_context']

            # Check trust level
            if 'trust_level' in profile:
                trust_level = profile['trust_level']
                if trust_level < 40:
                    return "trust_low"
                elif trust_level < 70:
                    return "trust_medium"
                else:
                    return "trust_high"

        return None
