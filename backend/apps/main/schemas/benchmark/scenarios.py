from typing import Dict, Any, Optional, List
from pydantic import field_validator, Field
from .base import VersionedModel, BenchmarkBaseModel, logger


class EvaluationCriterion(BenchmarkBaseModel):
    """A single evaluation criterion for benchmark scenarios."""
    dimension: str
    description: str
    weight: float = 1.0

    @field_validator('weight')
    def validate_weight(cls, v):
        """Validate that weight is between 0 and 1."""
        if v < 0 or v > 1:
            raise ValueError("Weight must be between 0 and 1")
        return v


class EvaluationCriteria(BenchmarkBaseModel):
    """Collection of evaluation criteria for benchmark scenarios."""
    criteria: List[EvaluationCriterion]

    def get_dimensions(self) -> List[str]:
        """Get the list of unique dimensions in the criteria."""
        return list(set(c.dimension for c in self.criteria))

    def get_criteria_by_dimension(self, dimension: str) -> List[EvaluationCriterion]:
        """Get all criteria for a specific dimension."""
        return [c for c in self.criteria if c.dimension == dimension]

    def get_weight_for_dimension(self, dimension: str) -> float:
        """Get the total weight for a specific dimension."""
        criteria = self.get_criteria_by_dimension(dimension)
        if not criteria:
            return 0.0
        return sum(c.weight for c in criteria)


class PhaseAwareCriteria(BenchmarkBaseModel):
    """Phase-aware evaluation criteria for benchmark scenarios.

    This model supports different evaluation criteria for different trust phases:
    - foundation (0-39): Basic support with focus on clarity and simplicity
    - expansion (40-69): Growth-oriented with increasing complexity
    - integration (70-100): Advanced synthesis with philosophical depth
    """
    foundation: Optional[Dict[str, List[str]]] = None
    expansion: Optional[Dict[str, List[str]]] = None
    integration: Optional[Dict[str, List[str]]] = None

    def get_criteria_for_phase(self, phase: str) -> Optional[Dict[str, List[str]]]:
        """Get the evaluation criteria for a specific trust phase."""
        if phase == "foundation" and self.foundation:
            return self.foundation
        elif phase == "expansion" and self.expansion:
            return self.expansion
        elif phase == "integration" and self.integration:
            return self.integration
        return None

    def get_criteria_for_trust_level(self, trust_level: int) -> Optional[Dict[str, List[str]]]:
        """Get the evaluation criteria for a specific trust level."""
        if trust_level < 40:
            return self.get_criteria_for_phase("foundation")
        elif trust_level < 70:
            return self.get_criteria_for_phase("expansion")
        else:
            return self.get_criteria_for_phase("integration")

    def get_phase_for_trust_level(self, trust_level: int) -> str:
        """Get the trust phase for a specific trust level."""
        if trust_level < 40:
            return "foundation"
        elif trust_level < 70:
            return "expansion"
        else:
            return "integration"


class ToolExpectation(BenchmarkBaseModel):
    """Expectation for a tool call in a benchmark scenario."""
    tool_name: str
    expected_calls: Optional[int] = None
    parameters: Optional[Dict[str, Any]] = None  # Added parameters field
    mock_responses: Optional[List[Dict[str, Any]]] = None
    conditional_responses: Optional[List[Dict[str, Any]]] = None
    error_simulation: Optional[Dict[str, Any]] = None

    model_config = {
        "extra": "allow",  # Allow extra fields for backward compatibility
    }


class BenchmarkScenarioMetadata(BenchmarkBaseModel):
    """Metadata for benchmark scenarios."""
    user_profile_context: Optional[Dict[str, Any]] = None
    expected_quality_criteria: Optional[Dict[str, List[str]]] = None
    evaluation_criteria_by_phase: Optional[PhaseAwareCriteria] = None
    evaluator_models: Optional[List[str]] = None
    mock_tool_responses: Optional[Dict[str, Any]] = None
    mock_responses: Optional[Dict[str, Any]] = None  # Added for backward compatibility with test_validate_benchmark_scenario_with_separate_mock_responses
    tool_expectations: Optional[List[ToolExpectation]] = None
    workflow_type: Optional[str] = None
    situation: Optional[Dict[str, Any]] = None
    evaluation_criteria: Optional[Dict[str, Any]] = None
    warmup_runs: Optional[int] = 1

    # Contextual evaluation fields
    evaluation_template_id: Optional[int] = Field(
        None,
        description="ID of the EvaluationCriteriaTemplate to use for contextual evaluation"
    )
    context: Optional[Dict[str, Any]] = Field(
        None,
        description="Contextual variables for evaluation adaptation (trust_level, mood, environment)"
    )
    benchmark_runs: Optional[int] = 3

    model_config = {
        "extra": "allow",  # Allow extra fields for backward compatibility
    }

    @field_validator('mock_responses')
    def validate_mock_responses(cls, v, info):
        """
        Validate mock_responses and sync with mock_tool_responses if needed.
        This is for backward compatibility with older test code.
        """
        if v is not None:
            # Get the current values
            values = info.data

            # If mock_tool_responses is not set, use mock_responses
            if 'mock_tool_responses' not in values or values['mock_tool_responses'] is None:
                # We can't modify values directly in Pydantic v2, but we can log this
                logger.debug("mock_responses field is used, should be migrated to mock_tool_responses",
                            extra={"mock_responses": v})
        return v

    @field_validator('mock_tool_responses')
    def validate_mock_tool_responses(cls, v):
        """
        Validate mock_tool_responses to ensure it has the correct structure.
        """
        if v is not None:
            # Log the structure for debugging
            logger.debug("Validating mock_tool_responses",
                        extra={"mock_tool_responses": v})

            # Ensure each tool response has the correct structure
            for tool_name, response in v.items():
                if isinstance(response, dict):
                    # Check if it's a structured response with 'response' field
                    if 'response' not in response and not any(k.startswith('condition') for k in response.keys()):
                        logger.warning(f"Tool response for {tool_name} is missing 'response' field",
                                    extra={"tool_name": tool_name, "response": response})
                elif not isinstance(response, str) and not isinstance(response, list):
                    logger.warning(f"Tool response for {tool_name} has invalid type: {type(response)}",
                                extra={"tool_name": tool_name, "response_type": str(type(response))})
        return v


class BenchmarkScenario(VersionedModel):
    """Schema for benchmark scenarios."""
    version: str = "1.0.0"
    name: str
    description: Optional[str] = None
    agent_role: str
    input_data: Dict[str, Any]
    metadata: BenchmarkScenarioMetadata
    is_active: bool = True

    @classmethod
    def validate_scenario(cls, data: dict) -> 'BenchmarkScenario':
        """Validate a scenario against the schema.

        Args:
            data: The scenario data to validate.

        Returns:
            BenchmarkScenario: The validated scenario instance.

        Raises:
            Exception: If validation fails.
        """
        try:
            instance = cls(**data)
            logger.info("Scenario validation successful",
                extra={"scenario_name": instance.name,
                      "agent_role": instance.agent_role})
            return instance
        except Exception as e:
            logger.error("Scenario validation failed",
                extra={"data": data, "error": str(e)},
                exc_info=True)
            raise
