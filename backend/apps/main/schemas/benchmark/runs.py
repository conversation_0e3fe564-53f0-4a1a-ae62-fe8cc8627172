from typing import Dict, Any, Optional, Union, List, ClassVar
from uuid import UUID
from datetime import datetime
from enum import Enum
from pydantic import Field, model_validator
from .base import VersionedModel, logger


class TokenUsage(VersionedModel):
    """Token usage information for benchmark runs."""
    schema_type: ClassVar[str] = "token_usage"
    version: str = "1.0.0"

    input_tokens: int = Field(0, ge=0, description="Number of input tokens used")
    output_tokens: int = Field(0, ge=0, description="Number of output tokens used")
    total_tokens: int = Field(0, ge=0, description="Total number of tokens used")
    input_token_price: Optional[float] = Field(None, ge=0, description="Price per input token")
    output_token_price: Optional[float] = Field(None, ge=0, description="Price per output token")
    estimated_cost: Optional[float] = Field(None, ge=0, description="Estimated cost of the run")

    @model_validator(mode='after')
    def calculate_total_tokens(self) -> 'TokenUsage':
        """Calculate total tokens if not provided."""
        if self.total_tokens == 0 and (self.input_tokens > 0 or self.output_tokens > 0):
            self.total_tokens = self.input_tokens + self.output_tokens
        return self

    @model_validator(mode='after')
    def calculate_estimated_cost(self) -> 'TokenUsage':
        """Calculate estimated cost if not provided."""
        if self.estimated_cost is None and self.input_token_price is not None and self.output_token_price is not None:
            self.estimated_cost = (
                self.input_tokens * self.input_token_price +
                self.output_tokens * self.output_token_price
            )
        return self


class StageStatus(str, Enum):
    """Status of a workflow stage."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class StagePerformance(VersionedModel):
    """Performance metrics for workflow stages."""
    schema_type: ClassVar[str] = "stage_performance"
    version: str = "1.0.0"

    stage_name: str = Field(..., description="Name of the workflow stage")
    status: StageStatus = Field(StageStatus.NOT_STARTED, description="Status of the stage")
    start_time: Optional[datetime] = Field(None, description="Start time of the stage")
    end_time: Optional[datetime] = Field(None, description="End time of the stage")
    duration_seconds: Optional[float] = Field(None, ge=0, description="Duration of the stage in seconds")
    token_usage: Optional[TokenUsage] = Field(None, description="Token usage for this stage")
    tool_calls: Optional[List[Dict[str, Any]]] = Field(None, description="Tool calls made during this stage")
    error: Optional[str] = Field(None, description="Error message if the stage failed")

    # Legacy fields
    mean_duration_ms: Optional[float] = None
    median_duration_ms: Optional[float] = None
    min_duration_ms: Optional[float] = None
    max_duration_ms: Optional[float] = None
    std_dev_ms: Optional[float] = None
    count: Optional[int] = None

    @model_validator(mode='after')
    def calculate_duration(self) -> 'StagePerformance':
        """Calculate duration if not provided."""
        if self.duration_seconds is None and self.start_time is not None and self.end_time is not None:
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()
        return self


class EvaluationScore(VersionedModel):
    """Evaluation score for a benchmark run."""
    schema_type: ClassVar[str] = "evaluation_score"
    version: str = "1.0.0"

    dimension: str = Field(..., description="Dimension being evaluated")
    score: float = Field(..., ge=0, le=10, description="Score (0-10)")
    weight: float = Field(1.0, ge=0, le=1, description="Weight of this dimension")
    explanation: Optional[str] = Field(None, description="Explanation for the score")


class SemanticEvaluation(VersionedModel):
    """Semantic evaluation results for benchmark runs."""
    schema_type: ClassVar[str] = "semantic_evaluation"
    version: str = "1.0.0"

    evaluator_model: str = Field(..., description="LLM model used for evaluation")
    scores: List[EvaluationScore] = Field(..., description="Evaluation scores")
    overall_score: float = Field(..., ge=0, le=10, description="Overall weighted score")
    evaluation_prompt: Optional[str] = Field(None, description="Prompt used for evaluation")
    evaluation_response: Optional[str] = Field(None, description="Raw response from the evaluator")
    token_usage: Optional[TokenUsage] = Field(None, description="Token usage for the evaluation")
    overall_reasoning: Optional[str] = Field(None, description="Overall reasoning for the evaluation")

    # Legacy fields
    dimensions: Optional[Dict[str, Dict[str, Union[float, str]]]] = None

    @model_validator(mode='after')
    def calculate_overall_score(self) -> 'SemanticEvaluation':
        """Calculate overall score if not provided."""
        if self.overall_score == 0 and self.scores:
            total_weight = sum(score.weight for score in self.scores)
            if total_weight > 0:
                self.overall_score = sum(score.score * score.weight for score in self.scores) / total_weight
        return self


class BenchmarkRun(VersionedModel):
    """Schema for benchmark run results."""
    schema_type: ClassVar[str] = "benchmark_run"
    version: str = "1.0.0"

    id: Optional[UUID] = Field(None, description="Unique ID for this run")
    scenario_id: UUID = Field(..., description="ID of the benchmark scenario")
    agent_id: Optional[UUID] = Field(None, description="ID of the agent being benchmarked")
    agent_version: Optional[str] = Field(None, description="Version of the agent being benchmarked")
    execution_date: datetime = Field(default_factory=datetime.now, description="Date and time of execution")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Additional parameters for the run")

    # LLM Configuration & Cost
    agent_llm_model_name: Optional[str] = Field(None, description="LLM model used by the agent")
    evaluator_llm_model: Optional[str] = Field(None, description="LLM model used for evaluation")
    llm_temperature: Optional[float] = Field(None, ge=0, le=2, description="Temperature setting for the LLM")
    llm_input_token_price: Optional[float] = Field(None, ge=0, description="Price per input token")
    llm_output_token_price: Optional[float] = Field(None, ge=0, description="Price per output token")
    total_input_tokens: int = Field(0, ge=0, description="Total number of input tokens used")
    total_output_tokens: int = Field(0, ge=0, description="Total number of output tokens used")
    estimated_cost: float = Field(0.0, ge=0, description="Estimated cost of the run")

    # Performance Metrics
    runs_count: int = Field(0, ge=0, description="Number of runs performed")
    mean_duration_ms: float = Field(0.0, ge=0, description="Mean duration in milliseconds")
    median_duration_ms: float = Field(0.0, ge=0, description="Median duration in milliseconds")
    min_duration_ms: float = Field(0.0, ge=0, description="Minimum duration in milliseconds")
    max_duration_ms: float = Field(0.0, ge=0, description="Maximum duration in milliseconds")
    std_dev_ms: float = Field(0.0, ge=0, description="Standard deviation in milliseconds")
    success_rate: float = Field(0.0, ge=0, le=1, description="Success rate (0-1)")

    # Operational Metrics
    tool_calls: int = Field(0, ge=0, description="Number of tool calls made")
    tool_breakdown: Optional[Dict[str, int]] = Field(None, description="Breakdown of tool calls by tool")
    last_response_length: Optional[int] = Field(None, ge=0, description="Length of the last response")

    # Semantic Quality Metrics
    semantic_score: Optional[float] = Field(None, ge=0, le=10, description="Overall semantic score")
    semantic_evaluation_details: Optional[str] = Field(None, description="Details of the semantic evaluation")
    semantic_evaluations: Optional[Dict[str, Any]] = Field(None, description="Semantic evaluations by dimension")

    # Statistical Comparison Metrics
    compared_to_run_id: Optional[UUID] = Field(None, description="ID of the run this run is compared to")
    performance_p_value: Optional[float] = Field(None, ge=0, le=1, description="P-value for performance comparison")
    is_performance_significant: bool = Field(False, description="Whether the performance difference is statistically significant")

    # Stage Performance
    stages: Optional[List[StagePerformance]] = Field(None, description="Performance metrics for each stage")
    stage_performance_details: Optional[Dict[str, Any]] = Field(None, description="Legacy stage performance details")

    # Enhanced Metrics
    token_usage: Optional[TokenUsage] = Field(None, description="Token usage information")
    semantic_evaluation: Optional[SemanticEvaluation] = Field(None, description="Semantic evaluation results")

    # Raw Results
    output: Optional[Dict[str, Any]] = Field(None, description="Output of the run")
    error: Optional[str] = Field(None, description="Error message if the run failed")
    raw_results: Optional[Dict[str, Any]] = Field(None, description="Raw results of the run")

    @model_validator(mode='after')
    def ensure_token_usage(self) -> 'BenchmarkRun':
        """Ensure token_usage is populated."""
        if self.token_usage is None:
            self.token_usage = TokenUsage(
                input_tokens=self.total_input_tokens,
                output_tokens=self.total_output_tokens,
                input_token_price=self.llm_input_token_price,
                output_token_price=self.llm_output_token_price,
                estimated_cost=self.estimated_cost
            )
        return self

    def record_completion(self):
        """Record the completion of a benchmark run."""
        logger.info("Benchmark run completed",
            extra={
                "run_id": self.id,
                "scenario_id": self.scenario_id,
                "duration": self.mean_duration_ms,
                "token_usage": {
                    "input_tokens": self.total_input_tokens,
                    "output_tokens": self.total_output_tokens,
                    "total_tokens": self.total_input_tokens + self.total_output_tokens,
                    "estimated_cost": self.estimated_cost
                }
            })

    def calculate_cost(self):
        """Calculate the estimated cost based on token usage and prices."""
        if self.llm_input_token_price is not None and self.llm_output_token_price is not None:
            input_cost = self.total_input_tokens * self.llm_input_token_price
            output_cost = self.total_output_tokens * self.llm_output_token_price
            self.estimated_cost = input_cost + output_cost

            # Update token_usage if it exists
            if self.token_usage:
                self.token_usage.estimated_cost = self.estimated_cost

    def get_token_usage(self) -> TokenUsage:
        """Get the token usage information."""
        if self.token_usage:
            return self.token_usage

        return TokenUsage(
            input_tokens=self.total_input_tokens,
            output_tokens=self.total_output_tokens,
            input_token_price=self.llm_input_token_price,
            output_token_price=self.llm_output_token_price,
            estimated_cost=self.estimated_cost
        )
