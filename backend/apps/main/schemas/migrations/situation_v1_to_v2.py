"""
Migration utility for situation schema from v1.0.0 to v2.0.0.

This module provides functions to migrate situation data from version 1.0.0 to 2.0.0,
adding the new context fields required for enhanced evaluation features.
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

def migrate_situation_v1_to_v2(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migrate situation data from v1.0.0 to v2.0.0.
    
    This migration adds the new context fields required for enhanced evaluation features:
    - user_state
    - device_capabilities
    - time_context
    - user_preferences
    
    Args:
        data: Situation data in v1.0.0 format
        
    Returns:
        Situation data in v2.0.0 format
    """
    # Create a copy of the original data
    result = data.copy()
    
    # Add version field if not present
    result["version"] = "2.0.0"
    
    # Initialize new fields with default values
    
    # 1. Add user_state object
    result["user_state"] = {
        "trust_level": data.get("trust_level", 50),  # Use existing trust_level if available
        "mood": "",
        "environment": ""
    }
    
    # Extract mood from psychological_state if available
    if "psychological_state" in data and isinstance(data["psychological_state"], dict):
        if "mood" in data["psychological_state"]:
            result["user_state"]["mood"] = data["psychological_state"]["mood"]
    
    # Extract environment information if available
    if "environment" in data and isinstance(data["environment"], dict):
        if "location" in data["environment"]:
            result["user_state"]["environment"] = data["environment"]["location"]
    
    # 2. Add device_capabilities object
    result["device_capabilities"] = {
        "screen_size": "medium",  # Default value
        "input_method": "mixed",  # Default value
        "accessibility": {
            "vision_impaired": False,
            "hearing_impaired": False,
            "motor_impaired": False,
            "preferred_modality": "visual"
        }
    }
    
    # 3. Add time_context object
    result["time_context"] = {
        "available_time": data.get("time_availability", 0),  # Use existing time_availability if available
        "time_of_day": "afternoon",  # Default value
        "time_zone": "UTC"  # Default value
    }
    
    # 4. Add user_preferences object
    result["user_preferences"] = {
        "learning_style": "visual",  # Default value
        "communication_preferences": {
            "verbosity": "moderate",  # Default value
            "formality": "neutral",  # Default value
            "feedback_frequency": "moderate"  # Default value
        }
    }
    
    logger.info(f"Migrated situation data from v1.0.0 to v2.0.0")
    return result
