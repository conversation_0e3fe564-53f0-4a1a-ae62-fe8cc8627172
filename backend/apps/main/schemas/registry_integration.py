import logging
from typing import Dict, Any, Optional, List, Tuple
from ..services.schema_registry import SchemaRegistry
from ..services.schema_version_manager import SchemaVersionManager
from .base import logger
from .version import VersionedModel
from .agent.messages import AgentMessage, UserMessage, AgentResponse, PsychologicalAssessment, AgentAssessmentMessage
from .benchmark.scenarios import BenchmarkScenario, EvaluationCriteria, PhaseAwareCriteria, ToolExpectation
from .benchmark.runs import BenchmarkRun, TokenUsage, StagePerformance, SemanticEvaluation

def register_pydantic_schemas(registry: Optional[SchemaRegistry] = None,
                             version_manager: Optional[SchemaVersionManager] = None) -> Tuple[SchemaRegistry, SchemaVersionManager]:
    """Register Pydantic schemas with the schema registry and version manager.
    
    Args:
        registry: Optional schema registry to use. If None, creates a new SchemaRegistry.
        version_manager: Optional schema version manager to use. If None, creates a new SchemaVersionManager.
        
    Returns:
        <PERSON><PERSON>[SchemaReg<PERSON>ry, SchemaVersionManager]: The schema registry and version manager.
    """
    # Create registry and version manager if not provided
    if registry is None:
        registry = SchemaRegistry()
    
    if version_manager is None:
        version_manager = SchemaVersionManager(registry=registry)
    
    # Register agent message schemas
    _register_model_schema(registry, version_manager, AgentMessage, "agent_message")
    _register_model_schema(registry, version_manager, UserMessage, "user_message")
    _register_model_schema(registry, version_manager, AgentResponse, "agent_response")
    _register_model_schema(registry, version_manager, PsychologicalAssessment, "psychological_assessment")
    _register_model_schema(registry, version_manager, AgentAssessmentMessage, "agent_assessment_message")
    
    # Register benchmark schemas
    _register_model_schema(registry, version_manager, BenchmarkScenario, "benchmark_scenario")
    _register_model_schema(registry, version_manager, EvaluationCriteria, "evaluation_criteria")
    _register_model_schema(registry, version_manager, PhaseAwareCriteria, "phase_aware_criteria")
    _register_model_schema(registry, version_manager, ToolExpectation, "tool_expectation")
    _register_model_schema(registry, version_manager, BenchmarkRun, "benchmark_run")
    _register_model_schema(registry, version_manager, TokenUsage, "token_usage")
    _register_model_schema(registry, version_manager, StagePerformance, "stage_performance")
    _register_model_schema(registry, version_manager, SemanticEvaluation, "semantic_evaluation")
    
    logger.info("Registered Pydantic schemas with schema registry and version manager")
    
    return registry, version_manager


def _register_model_schema(registry: SchemaRegistry, 
                          version_manager: SchemaVersionManager,
                          model_class: type, 
                          schema_type: str) -> None:
    """Register a Pydantic model schema with the schema registry and version manager.
    
    Args:
        registry: The schema registry to register with.
        version_manager: The schema version manager to register with.
        model_class: The Pydantic model class to register.
        schema_type: The type identifier for the schema.
    """
    # Get the schema from the model
    schema = model_class.model_json_schema()
    
    # Get the version from the model if it's a versioned model
    version = "1.0.0"
    if issubclass(model_class, VersionedModel) and hasattr(model_class, "version"):
        version = model_class.version
    
    # Register with the registry
    registry.register_schema(schema_type, schema, version=version)
    
    # Register with the version manager
    version_manager.register_schema_version(schema_type, version, schema)
    
    logger.debug(f"Registered schema '{schema_type}' version '{version}'")


def validate_model_instance(registry: SchemaRegistry, 
                           instance: VersionedModel, 
                           schema_type: Optional[str] = None) -> Tuple[bool, List[str]]:
    """Validate a Pydantic model instance against its schema.
    
    Args:
        registry: The schema registry to validate with.
        instance: The model instance to validate.
        schema_type: Optional type identifier for the schema. If None, uses the class name.
        
    Returns:
        Tuple[bool, List[str]]: A tuple containing a boolean indicating if the instance is valid,
            and a list of error messages if the instance is invalid.
    """
    # Get the schema type from the class name if not provided
    if schema_type is None:
        schema_type = instance.__class__.__name__.lower()
    
    # Get the version from the instance if it's a versioned model
    version = None
    if isinstance(instance, VersionedModel) and hasattr(instance, "version"):
        version = instance.version
    
    # Convert the instance to a dict
    data = instance.model_dump()
    
    # Validate with the registry
    return registry.validate(schema_type, data, version=version)
