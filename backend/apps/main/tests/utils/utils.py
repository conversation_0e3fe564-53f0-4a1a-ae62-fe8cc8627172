"""
Utility functions for testing the benchmark system.
"""
import uuid
from typing import Optional, Dict, Any, List


def generate_unique_scenario_name(base_name: str = "Test Scenario") -> str:
    """
    Generate a unique name for a benchmark scenario to avoid unique constraint violations.

    Args:
        base_name: The base name to use for the scenario

    Returns:
        A unique name with a UUID suffix
    """
    unique_suffix = str(uuid.uuid4())[:8]
    return f"{base_name} {unique_suffix}"
