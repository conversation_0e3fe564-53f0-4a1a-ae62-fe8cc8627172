"""
Test utilities for creating test scenarios, agents, and other test objects.
This file is a direct copy of the functions from the parent utils.py file.
"""
import uuid
import json
import logging
from typing import Optional, Dict, Any, List, Tuple
from django.db import models
from django.utils import timezone
from asgiref.sync import sync_to_async
from apps.main.models import BenchmarkScenario, BenchmarkTag, LLMConfig, GenericAgent, BenchmarkRun
from apps.main.models import AgentRole as ModelAgentRole

logger = logging.getLogger(__name__)


def generate_unique_scenario_name(base_name: str = "Test Scenario") -> str:
    """
    Generate a unique name for a benchmark scenario to avoid unique constraint violations.

    Args:
        base_name: The base name to use for the scenario

    Returns:
        A unique name with a UUID suffix
    """
    unique_suffix = str(uuid.uuid4())[:8]
    return f"{base_name} {unique_suffix}"


def generate_unique_agent_role(base_role: str = "test_role") -> str:
    """
    Generate a unique agent role name to avoid unique constraint violations.

    Args:
        base_role: The base role name to use

    Returns:
        A unique role name with a UUID suffix
    """
    unique_suffix = str(uuid.uuid4())[:8]
    return f"{base_role}_{unique_suffix}"


def generate_unique_llm_config_name(base_name: str = "Test LLM Config") -> str:
    """
    Generate a unique name for an LLM config to avoid unique constraint violations.

    Args:
        base_name: The base name to use for the LLM config

    Returns:
        A unique name with a UUID suffix
    """
    unique_suffix = str(uuid.uuid4())[:8]
    return f"{base_name} {unique_suffix}"


def create_test_scenario(
    name: Optional[str] = None,
    agent_role: str = ModelAgentRole.MENTOR.value,
    description: str = "A scenario for testing.",
    input_data: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None,
    is_active: bool = True,
    version: int = 1,
    tags: Optional[list] = None,
    is_latest: bool = False,
    add_random_suffix: bool = True
) -> BenchmarkScenario:
    """
    Create a test benchmark scenario with unique name to avoid constraint violations.

    Args:
        name: Base name for the scenario
        agent_role: Role of the agent for this scenario
        description: Description of the scenario
        input_data: Input data for the scenario
        metadata: Metadata for the scenario
        is_active: Whether the scenario is active
        version: Version of the scenario
        tags: Tags for the scenario
        is_latest: Whether this is the latest version of the scenario
        add_random_suffix: Whether to add a random suffix to the name (default: True)

    Returns:
        The created BenchmarkScenario instance
    """
    # Generate a name based on the provided name or default
    base_name = name if name is not None else "Test Scenario"

    # Add a random suffix if requested
    if add_random_suffix:
        unique_name = generate_unique_scenario_name(base_name)
    else:
        unique_name = base_name

    if input_data is None:
        input_data = {"user_query": "Hello"}

    if metadata is None:
        metadata = {
            "expected_quality_criteria": {
                "Clarity": ["Is the response easy to understand?"],
                "Friendliness": ["Is the tone welcoming?"]
            }
        }

    scenario = BenchmarkScenario.objects.create(
        name=unique_name,
        description=description,
        agent_role=agent_role,
        input_data=input_data,
        metadata=metadata,
        is_active=is_active,
        version=version,
        is_latest=is_latest
    )

    # Add tags if provided
    if tags:
        for tag_name in tags:
            tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name)
            scenario.tags.add(tag)

    return scenario


async def create_test_scenario_async(
    name: Optional[str] = None,
    agent_role: str = ModelAgentRole.MENTOR.value,
    description: str = "A scenario for testing.",
    input_data: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None,
    is_active: bool = True,
    version: int = 1,
    tags: Optional[list] = None,
    is_latest: bool = False,
    add_random_suffix: bool = True
) -> BenchmarkScenario:
    """
    Async version of create_test_scenario.

    Args:
        name: Base name for the scenario
        agent_role: Role of the agent for this scenario
        description: Description of the scenario
        input_data: Input data for the scenario
        metadata: Metadata for the scenario
        is_active: Whether the scenario is active
        version: Version of the scenario
        tags: Tags for the scenario
        is_latest: Whether this is the latest version of the scenario
        add_random_suffix: Whether to add a random suffix to the name (default: True)

    Returns:
        The created BenchmarkScenario instance
    """
    # Generate a name based on the provided name or default
    base_name = name if name is not None else "Test Scenario"

    # Add a random suffix if requested
    if add_random_suffix:
        unique_name = generate_unique_scenario_name(base_name)
    else:
        unique_name = base_name

    if input_data is None:
        input_data = {"user_query": "Hello"}

    if metadata is None:
        metadata = {
            "expected_quality_criteria": {
                "Clarity": ["Is the response easy to understand?"],
                "Friendliness": ["Is the tone welcoming?"]
            }
        }

    scenario = await sync_to_async(BenchmarkScenario.objects.create, thread_sensitive=True)(
        name=unique_name,
        description=description,
        agent_role=agent_role,
        input_data=input_data,
        metadata=metadata,
        is_active=is_active,
        version=version,
        is_latest=is_latest
    )

    # Add tags if provided
    if tags:
        for tag_name in tags:
            tag, _ = await sync_to_async(BenchmarkTag.objects.get_or_create, thread_sensitive=True)(name=tag_name)
            await sync_to_async(scenario.tags.add, thread_sensitive=True)(tag)

    return scenario


def create_test_llm_config(
    name: Optional[str] = None,
    model_name: str = "test-model",
    temperature: float = 0.7,
    input_token_price: float = 0.0001,
    output_token_price: float = 0.0002,
    is_default: bool = False,
    is_evaluation: bool = False
) -> LLMConfig:
    """
    Create a test LLM config with unique name to avoid constraint violations.

    Args:
        name: Base name for the config (will always be made unique)
        model_name: The model name for the config
        temperature: The temperature setting
        input_token_price: Price per input token
        output_token_price: Price per output token
        is_default: Whether this is the default config
        is_evaluation: Whether this config is for evaluation purposes

    Returns:
        The created LLMConfig instance
    """
    # Always generate a unique name based on the provided name or default
    base_name = name if name is not None else "Test LLM Config"
    unique_name = generate_unique_llm_config_name(base_name)

    return LLMConfig.objects.create(
        name=unique_name,
        model_name=model_name,
        temperature=temperature,
        input_token_price=input_token_price,
        output_token_price=output_token_price,
        is_default=is_default,
        is_evaluation=is_evaluation
    )


async def create_test_llm_config_async(
    name: Optional[str] = None,
    model_name: str = "test-model",
    temperature: float = 0.7,
    input_token_price: float = 0.0001,
    output_token_price: float = 0.0002,
    is_default: bool = False,
    is_evaluation: bool = False
) -> LLMConfig:
    """
    Async version of create_test_llm_config.

    Args:
        name: Base name for the config (will always be made unique)
        model_name: The model name for the config
        temperature: The temperature setting
        input_token_price: Price per input token
        output_token_price: Price per output token
        is_default: Whether this is the default config
        is_evaluation: Whether this config is for evaluation purposes

    Returns:
        The created LLMConfig instance
    """
    # Always generate a unique name based on the provided name or default
    base_name = name if name is not None else "Test LLM Config"
    unique_name = generate_unique_llm_config_name(base_name)

    return await sync_to_async(LLMConfig.objects.create, thread_sensitive=True)(
        name=unique_name,
        model_name=model_name,
        temperature=temperature,
        input_token_price=input_token_price,
        output_token_price=output_token_price,
        is_default=is_default,
        is_evaluation=is_evaluation
    )


def create_test_agent(
    role: Optional[str] = None,
    description: str = "Test agent",
    system_instructions: str = "Test instructions",
    input_schema: Optional[Dict[str, Any]] = None,
    output_schema: Optional[Dict[str, Any]] = None,
    langgraph_node_class: str = "test.TestAgent",
    version: str = "1.0.0",
    llm_config: Optional[LLMConfig] = None,
    is_active: bool = True
) -> GenericAgent:
    """
    Create a test agent with unique role to avoid constraint violations.

    Args:
        role: Base role for the agent (will always be made unique)
        description: Description of the agent
        system_instructions: System instructions for the agent
        input_schema: Input schema for the agent
        output_schema: Output schema for the agent
        langgraph_node_class: LangGraph node class for the agent
        version: Version of the agent
        llm_config: LLM configuration for the agent
        is_active: Whether the agent is active

    Returns:
        The created GenericAgent instance
    """
    # Always generate a unique role based on the provided role or default
    base_role = role if role is not None else "test_role"
    unique_role = generate_unique_agent_role(base_role)

    # Create a default LLM config if none is provided
    if llm_config is None:
        llm_config = create_test_llm_config()

    if input_schema is None:
        input_schema = {}

    if output_schema is None:
        output_schema = {}

    return GenericAgent.objects.create(
        role=unique_role,
        description=description,
        system_instructions=system_instructions,
        input_schema=input_schema,
        output_schema=output_schema,
        langgraph_node_class=langgraph_node_class,
        version=version,
        llm_config=llm_config,
        is_active=is_active
    )


async def create_test_agent_async(
    role: Optional[str] = None,
    description: str = "Test agent",
    system_instructions: str = "Test instructions",
    input_schema: Optional[Dict[str, Any]] = None,
    output_schema: Optional[Dict[str, Any]] = None,
    langgraph_node_class: str = "test.TestAgent",
    version: str = "1.0.0",
    llm_config: Optional[LLMConfig] = None,
    is_active: bool = True
) -> GenericAgent:
    """
    Async version of create_test_agent.

    Args:
        role: Base role for the agent (will always be made unique)
        description: Description of the agent
        system_instructions: System instructions for the agent
        input_schema: Input schema for the agent
        output_schema: Output schema for the agent
        langgraph_node_class: LangGraph node class for the agent
        version: Version of the agent
        llm_config: LLM configuration for the agent
        is_active: Whether the agent is active

    Returns:
        The created GenericAgent instance
    """
    # Always generate a unique role based on the provided role or default
    base_role = role if role is not None else "test_role"
    unique_role = generate_unique_agent_role(base_role)

    # Create a default LLM config if none is provided
    if llm_config is None:
        llm_config = await create_test_llm_config_async()

    if input_schema is None:
        input_schema = {}

    if output_schema is None:
        output_schema = {}

    return await sync_to_async(GenericAgent.objects.create, thread_sensitive=True)(
        role=unique_role,
        description=description,
        system_instructions=system_instructions,
        input_schema=input_schema,
        output_schema=output_schema,
        langgraph_node_class=langgraph_node_class,
        version=version,
        llm_config=llm_config,
        is_active=is_active
    )


def create_test_benchmark_run(
    scenario: Optional[BenchmarkScenario] = None,
    agent: Optional[GenericAgent] = None,
    status: str = "completed",
    result: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None,
    created_by: Optional[models.Model] = None
) -> BenchmarkRun:
    """
    Create a test benchmark run.
    """
    # Create a default scenario if none is provided
    if scenario is None:
        scenario = create_test_scenario()

    # Create a default agent if none is provided
    if agent is None:
        agent = create_test_agent(role=scenario.agent_role)

    if result is None:
        result = {
            "response": "This is a test response.",
            "metrics": {
                "tokens": 10,
                "time_ms": 100
            }
        }

    if metadata is None:
        metadata = {
            "test_run": True,
            "environment": "test"
        }

    return BenchmarkRun.objects.create(
        scenario=scenario,
        agent=agent,
        status=status,
        result=result,
        metadata=metadata,
        created_by=created_by
    )


async def create_test_benchmark_run_async(
    scenario: Optional[BenchmarkScenario] = None,
    agent: Optional[GenericAgent] = None,
    status: str = "completed",
    result: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None,
    created_by: Optional[models.Model] = None
) -> BenchmarkRun:
    """
    Async version of create_test_benchmark_run.
    """
    # Create a default scenario if none is provided
    if scenario is None:
        scenario = await create_test_scenario_async()

    # Create a default agent if none is provided
    if agent is None:
        agent = await create_test_agent_async(role=scenario.agent_role)

    if result is None:
        result = {
            "response": "This is a test response.",
            "metrics": {
                "tokens": 10,
                "time_ms": 100
            }
        }

    if metadata is None:
        metadata = {
            "test_run": True,
            "environment": "test"
        }

    return await sync_to_async(BenchmarkRun.objects.create, thread_sensitive=True)(
        scenario=scenario,
        agent=agent,
        status=status,
        result=result,
        metadata=metadata,
        created_by=created_by
    )


class MainTestBenchmarkRun:
    """
    A class to help with testing benchmark runs.
    """
    def __init__(self, benchmark_run: BenchmarkRun):
        self.benchmark_run = benchmark_run


def ensure_mentor_agent_exists() -> Tuple[GenericAgent, bool]:
    """
    Ensures that a mentor agent exists in the database with all required fields.
    This is useful for tests that require a mentor agent to be present.

    Returns:
        Tuple[GenericAgent, bool]: The mentor agent and a boolean indicating if it was created
    """
    logger.info("Ensuring mentor agent exists in the database...")

    # Define default schemas that satisfy NOT NULL constraints
    default_schema = {"type": "object", "properties": {}}
    default_memory_schema = {
        "type": "object",
        "properties": {
            "communication_preferences": {"type": "object"},
            "conversation_context": {"type": "object"},
            "effective_approaches": {"type": "object"}
        }
    }

    # Create or get the mentor agent with all required fields
    mentor_agent, created = GenericAgent.objects.get_or_create(
        role=ModelAgentRole.MENTOR.value,
        defaults={
            'description': 'Test Mentor Agent for Integration Tests',
            'is_active': True,
            'input_schema': default_schema,
            'output_schema': default_schema,
            'state_schema': default_schema,
            'memory_schema': default_memory_schema,
            'system_instructions': 'You are a helpful mentor agent for testing purposes.',
            'version': '1.0.0-test',
            'langgraph_node_class': 'apps.main.agents.mentor_agent.MentorAgent',
            'processing_timeout': 30,
            'read_models': [
                'user.UserProfile',
                'main.HistoryEvent',
                'main.UserFeedback',
                'activity.ActivityTailored',
                'main.Wheel',
                'main.WheelItem',
                'user.TrustLevel'
            ],
            'write_models': [
                'main.HistoryEvent',
                'main.UserFeedback',
                'user.CurrentMood'
            ],
            'recommend_models': [
                'user.TrustLevel'
            ]
        }
    )

    if created:
        logger.info("Created new mentor agent for testing")
    else:
        logger.info("Using existing mentor agent for testing")

        # Update the agent with required fields if they're missing
        updated = False

        # Check for missing fields and update if necessary
        if not mentor_agent.langgraph_node_class:
            mentor_agent.langgraph_node_class = 'apps.main.agents.mentor_agent.MentorAgent'
            updated = True

        if not mentor_agent.memory_schema:
            mentor_agent.memory_schema = default_memory_schema
            updated = True

        if not mentor_agent.state_schema:
            mentor_agent.state_schema = default_schema
            updated = True

        if not mentor_agent.read_models:
            mentor_agent.read_models = [
                'user.UserProfile',
                'main.HistoryEvent',
                'main.UserFeedback',
                'activity.ActivityTailored',
                'main.Wheel',
                'main.WheelItem',
                'user.TrustLevel'
            ]
            updated = True

        if not mentor_agent.write_models:
            mentor_agent.write_models = [
                'main.HistoryEvent',
                'main.UserFeedback',
                'user.CurrentMood'
            ]
            updated = True

        if not mentor_agent.recommend_models:
            mentor_agent.recommend_models = [
                'user.TrustLevel'
            ]
            updated = True

        if updated:
            mentor_agent.save()
            logger.info("Updated existing mentor agent with missing fields")

    return mentor_agent, created


async def ensure_mentor_agent_exists_async() -> Tuple[GenericAgent, bool]:
    """
    Async version of ensure_mentor_agent_exists.

    Returns:
        Tuple[GenericAgent, bool]: The mentor agent and a boolean indicating if it was created
    """
    return await sync_to_async(ensure_mentor_agent_exists, thread_sensitive=True)()
