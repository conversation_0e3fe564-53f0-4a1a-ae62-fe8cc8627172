"""
Test utilities for phase-aware benchmark scenarios.

This module provides utilities for creating and testing phase-aware benchmark scenarios.
"""

import uuid
from typing import Dict, Any, List, Optional, Union

from django.db import transaction
from channels.db import database_sync_to_async

from apps.main.models import BenchmarkScenario, GenericAgent


async def create_phase_aware_test_scenario_async(
    name: Optional[str] = None,
    description: str = "Test phase-aware scenario",
    agent_role: str = "mentor",
    input_data: Optional[Dict[str, Any]] = None,
    trust_level: Optional[int] = None,
    foundation_criteria: Optional[Dict[str, List[str]]] = None,
    expansion_criteria: Optional[Dict[str, List[str]]] = None,
    integration_criteria: Optional[Dict[str, List[str]]] = None,
    evaluator_models: Optional[List[str]] = None,
    is_active: bool = True
) -> BenchmarkScenario:
    """
    Create a test benchmark scenario with phase-aware criteria.

    Args:
        name: Optional name for the scenario (will be made unique if not provided)
        description: Description of the scenario
        agent_role: Role of the agent being benchmarked
        input_data: Input data for the scenario
        trust_level: Optional trust level to include in the input data
        foundation_criteria: Criteria for foundation phase (trust level 0-39)
        expansion_criteria: Criteria for expansion phase (trust level 40-69)
        integration_criteria: Criteria for integration phase (trust level 70-100)
        evaluator_models: List of evaluator models to use
        is_active: Whether the scenario is active

    Returns:
        The created BenchmarkScenario instance
    """
    # Import here to avoid circular imports
    from apps.main.tests.utils.utils import generate_unique_scenario_name

    # Generate a unique name if not provided
    if name is None:
        name = generate_unique_scenario_name()

    # Create default input data if not provided
    if input_data is None:
        input_data = {
            "user_message": "Test message",
            "context_packet": {}
        }

    # Add trust level to input data if provided
    if trust_level is not None and "context_packet" in input_data:
        input_data["context_packet"]["trust_level"] = trust_level

    # Create default criteria if not provided
    if foundation_criteria is None:
        foundation_criteria = {
            "Clarity": ["Is it simple?", "Is it direct?"],
            "Helpfulness": ["Does it provide basic guidance?"]
        }

    if expansion_criteria is None:
        expansion_criteria = {
            "Growth": ["Does it challenge?", "Does it support?"],
            "Depth": ["Does it provide deeper insights?"]
        }

    if integration_criteria is None:
        integration_criteria = {
            "Synthesis": ["Does it integrate?", "Does it elevate?"],
            "Philosophy": ["Does it provide philosophical depth?"]
        }

    # Create metadata with phase-aware criteria
    metadata = {
        "evaluation_criteria_by_phase": {
            "foundation": foundation_criteria,
            "expansion": expansion_criteria,
            "integration": integration_criteria
        }
    }

    # Add evaluator models if provided
    if evaluator_models:
        metadata["evaluator_models"] = evaluator_models

    # Create the scenario
    @database_sync_to_async
    def create_scenario():
        with transaction.atomic():
            return BenchmarkScenario.objects.create(
                name=name,
                description=description,
                agent_role=agent_role,
                input_data=input_data,
                metadata=metadata,
                is_active=is_active,
                version=1,
                is_latest=True
            )

    return await create_scenario()


def get_criteria_for_trust_level(
    scenario: BenchmarkScenario,
    trust_level: int
) -> Dict[str, List[str]]:
    """
    Get the appropriate criteria for a given trust level from a phase-aware scenario.

    Args:
        scenario: The benchmark scenario
        trust_level: Integer trust level between 0 and 100

    Returns:
        Dictionary with criteria for the appropriate phase
    """
    # Import the utility function
    from apps.main.services.evaluation_criteria_migration import map_trust_level_to_phase

    # Check if scenario has phase-aware criteria
    if 'evaluation_criteria_by_phase' not in scenario.metadata:
        return scenario.metadata.get('expected_quality_criteria', {})

    # Map trust level to phase
    phase = map_trust_level_to_phase(trust_level)

    # Get criteria for the phase
    phase_criteria = scenario.metadata['evaluation_criteria_by_phase'].get(phase, {})

    # If phase has no criteria, fall back to foundation phase
    if not phase_criteria:
        phase_criteria = scenario.metadata['evaluation_criteria_by_phase'].get('foundation', {})

    return phase_criteria
