"""
Configuration file for pytest with unified fixtures for all tests including agent-specific testing.
This provides fixtures for agent testing with real LLM capabilities and extracted definitions.
"""

import os
import pytest
import uuid
import logging
import asyncio
from unittest.mock import MagicMock, AsyncMock, patch
from typing import Dict, Any, List, Optional, Union, Tuple
from django.utils import timezone
from asgiref.sync import sync_to_async
from apps.main.testing.agent_test_runner import Agent<PERSON>estRunner
from apps.main.testing.workflow_test_runner import WorkflowTestRunner
from apps.user.models import UserProfile # Added UserProfile import
from apps.main.models import BenchmarkScenario, GenericAgent, LLMConfig
from apps.main.models import AgentRole as ModelAgentRole

# Flag to enable/disable real LLM calls in tests
USE_REAL_LLM = os.environ.get('USE_REAL_LLM', 'false').lower() == 'true'

logger = logging.getLogger(__name__)

# Import extractors and utils from their new locations
from apps.main.testing.definition_extractors import AgentDefinitionsExtractor, ToolDefinitionsExtractor
from apps.main.testing.mock_utils import create_scenario_fixture
# Import specific mock services needed directly
from apps.main.testing.mock_database_service import MockDatabaseService
# Remove redundant grouped import from old mocks.py
# from apps.main.testing.mocks import (
#     AgentDefinitionsExtractor,
#     ToolDefinitionsExtractor,
#     MockDatabaseService
# )
from apps.main.llm.response import LLMResponse, ResponseType, ToolCall
# Create singleton extractors for test session to avoid repeated extraction
_agent_extractor = None
_tool_extractor = None

def get_agent_extractor():
    """Get the singleton agent extractor, creating it if needed"""
    global _agent_extractor
    if _agent_extractor is None:
        _agent_extractor = AgentDefinitionsExtractor()
        _agent_extractor.extract_definitions()
        _agent_extractor.extract_tool_mappings()
    return _agent_extractor

def get_tool_extractor():
    """Get the singleton tool extractor, creating it if needed"""
    global _tool_extractor
    if _tool_extractor is None:
        _tool_extractor = ToolDefinitionsExtractor()
        _tool_extractor.extract_definitions()
    return _tool_extractor

# Setup function for LLM mocking
def _setup_llm_mock(llm_mock, llm_responses):
    """Configure the LLM mock with appropriate responses"""
    # Set up default chat completion behavior
    async def mock_chat_completion(messages, **kwargs):
        # Extract user message
        user_message = ""
        for msg in messages:
            if isinstance(msg, dict) and msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        # Check for pattern-specific responses
        if llm_responses:
            for pattern, response in llm_responses.items():
                if pattern != "default" and pattern in user_message:
                    return response

            # Use default response if available
            if "default" in llm_responses:
                return llm_responses["default"]

        # Generic responses based on common agent tasks
        if "context extraction" in user_message.lower() or "extract context" in user_message.lower():
            return {
                "extracted_context": {
                    "mood": "focused",
                    "environment": "home",
                    "time_availability": "30 minutes",
                    "focus": "productivity",
                    "satisfaction": "moderate"
                },
                "extraction_confidence": 0.8
            }
        elif "workflow classification" in user_message.lower() or "classify message" in user_message.lower():
            return {
                "workflow_type": "wheel_generation",
                "confidence": 0.75,
                "reason": "User is asking for activity suggestions"
            }
        elif "strategy" in user_message.lower() or "formulate strategy" in user_message.lower():
            return {
                "domain_distribution": {
                    "creative": 0.3,
                    "physical": 0.2,
                    "intellectual": 0.2,
                    "social": 0.15,
                    "reflective": 0.15
                },
                "challenge_parameters": {
                    "overall_challenge": 65,
                    "traits": {
                        "OPEN": 70,
                        "CONS": 65,
                        "EXTR": 55
                    }
                }
            }

        # Generic fallback
        return {
            "content": "This is a mock LLM response for: " + user_message[:50] + "..."
        }

    # Apply the mock implementation
    llm_mock.chat_completion = AsyncMock(side_effect=mock_chat_completion)

@pytest.fixture
def test_scenario():
    """Create a test scenario with pre-configured mock dependencies.

    This fixture provides a complete test environment with standard mocks
    that can be customized for specific test cases.
    """
    def _create_scenario(scenario_name="test_scenario", **config_options):
        """Create a specific test scenario with custom configurations.

        Args:
            scenario_name: Name of the test scenario
            **config_options: Configuration options for mock dependencies
                - llm: Configuration for LLM service mock
                - database: Configuration for database service mock
                - tools: Configuration for tool registry mock

        Returns:
            Dict with scenario configuration and mock instances
        """
        return create_scenario_fixture(scenario_name, **config_options)

    return _create_scenario

# Agent-Specific Fixtures

@pytest.fixture
def standard_db_mock():
    """Provide a standard mock for database operations that uses extracted definitions."""
    # MockDatabaseService is already imported at the top level
    # from apps.main.testing.mocks import MockDatabaseService # Removed redundant import

    # Create a database service with extracted definitions and tools
    db_service = MockDatabaseService(
        use_extracted_definitions=True,
        use_extracted_tools=True
    )

    # Ensure definitions are loaded
    agent_extractor = get_agent_extractor()
    tool_extractor = get_tool_extractor()

    # Force load of agent definitions
    if not db_service.agent_definitions:
        db_service.agent_definitions = agent_extractor.agent_definitions

    # Force load of tool mappings if needed
    if not db_service.agent_tool_mappings:
        db_service.agent_tool_mappings = agent_extractor.agent_tool_mappings
        db_service.common_tools = agent_extractor.common_tools

    # Force load of tool definitions if needed
    if not db_service.tool_definitions:
        db_service.tool_definitions = tool_extractor.tool_definitions
        db_service.tool_responses = tool_extractor.tool_mock_responses

    return db_service

@pytest.fixture
def mock_db_with_extracted_definitions():
    """
    Provide a database service mock with extracted definitions and tools.
    This is a direct reference to create a fresh instance each time.
    """
    return standard_db_mock()

@pytest.fixture
def mock_tool_responses():
    """Provide common mock responses for tool calls."""
    return {
        'get_user_profile': {
            'profile_name': 'Test User',
            'demographics': {'age': 30},
            'traits': {
                'openness': {'strength': 75},
                'conscientiousness': {'strength': 65},
                'extraversion': {'strength': 45}
            },
            'trust_level': {'value': 55, 'phase': 'Foundation'}
        },
        'get_activity_details': {
            'id': 'mock-activity-id',
            'name': 'Creative Writing Exercise',
            'description': 'A short creative writing session to develop your skills',
            'instructions': 'Find a quiet space and write for 20 minutes about a memory from your childhood.',
            'challenge_rating': 60,
            'challenge_breakdown': {'openness': 70, 'conscientiousness': 55},
            'duration_range': {'min': 15, 'max': 30},
            'domains': [{'code': 'creative', 'strength': 80}],
            'value_proposition': 'Enhances creativity and self-expression'
        },
        'get_communication_guidelines': {
            'tone': 'supportive',
            'detail_level': 'moderate',
            'metaphor_types': ['nature', 'journey'],
            'pacing': 'moderate',
            'formatting': {'use_emojis': True, 'use_bullet_points': True}
        },
    }

@pytest.fixture
def workflow_runner():
    """Factory fixture for creating workflow test runners."""
    def _create_runner(create_workflow_fn, user_profile_id="test-user-id"):
        return WorkflowTestRunner(create_workflow_fn, user_profile_id=user_profile_id)
    return _create_runner

@pytest.fixture
def mock_user_profile() -> UserProfile: # Added return type hint
    """Fixture to create a valid mock UserProfile object for tool tests."""
    # Create a MagicMock simulating a UserProfile instance
    mock_profile = MagicMock(spec=UserProfile)
    mock_profile.id = uuid.uuid4() # Assign a UUID
    mock_profile.profile_name = "Test User"
    mock_profile.trust_phase = "Foundation"
    mock_profile.trust_level = 55
    # Mock communication preferences as a dictionary or another MagicMock if needed
    mock_profile.communication_preferences = {
        "tone": "supportive",
        "detail_level": "medium"
    }
    # Mock personality traits as a dictionary or another MagicMock
    mock_profile.personality_traits = {
        "honesty_humility": 0.5,
        "emotionality": 0.5,
        "extraversion": 0.5,
        "agreeableness": 0.5,
        "conscientiousness": 0.5,
        "openness": 0.5,
        "neuroticism": 0.5
    }
    # Mock related managers needed by the test setup
    mock_profile.trait_inclinations = MagicMock()
    mock_profile.trait_inclinations.all.return_value = []
    mock_profile.user_goals = MagicMock()
    mock_profile.user_goals.all.return_value = []
    mock_profile.beliefs = MagicMock()
    mock_profile.beliefs.all.return_value = []

    # Mock other fields if accessed directly by the tool or tests
    # Example:
    # mock_profile.activity_history = { ... }
    # mock_profile.skill_assessments = [ ... ]
    # mock_profile.user_history = { ... }

    return mock_profile

# Keep the old dictionary-based fixture for reference or other tests if needed,
# but rename it to avoid conflict.
@pytest.fixture
def llm_config(db):
    """Fixture to create a LLMConfig instance for testing."""
    from apps.main.models import LLMConfig
    config, created = LLMConfig.objects.get_or_create(
        name='test-llm-config',
        defaults={
            'model_name': 'test-model',
            'temperature': 0.7,
            'input_token_price': 0.0001,
            'output_token_price': 0.0002,
            'is_default': False,
            'is_evaluation': False
        }
    )
    return config

@pytest.fixture
def mock_user_profile_dict():
    """Fixture returning a dictionary representing user profile data."""
    return {
        "profile_name": "Test User Dict",
        "trust_phase": "Foundation",
        "trust_level": 55,
        "communication_preferences": {
            "tone": "supportive",
            "detail_level": "medium"
        },
        "personality_traits": {
            "honesty_humility": 0.5,
            "emotionality": 0.5,
            "extraversion": 0.5,
            "agreeableness": 0.5,
            "conscientiousness": 0.5,
            "openness": 0.5,
            "neuroticism": 0.5
        },
        "activity_history": {
            "completed_count": 10,
            "abandonment_rate": 0.1,
            "average_rating": 4.0,
            "domain_distribution": {
                "creative": 5,
                "physical": 3,
                "intellectual": 2
            },
            "challenge_progression": [
                {"period": "2024-Q1", "avg_challenge": 60},
                {"period": "2024-Q2", "avg_challenge": 65}
            ]
        },
        "skill_assessments": [
            {"skill": "writing", "start_level": 1, "current_level": 3},
            {"skill": "public speaking", "start_level": 2, "current_level": 4}
        ],
        "user_history": {
            "total_activities": 20,
            "recent_activities": 5,
            "completion_rate": 0.8,
            "average_feedback": 4.5
        }
    }

# Benchmark Scenario Fixtures for Testing

@pytest.fixture
def generic_agent_mentor(db):
    """Fixture for a Mentor GenericAgent."""
    agent, _ = GenericAgent.objects.get_or_create(
        role=ModelAgentRole.MENTOR.value,
        defaults={
            'langgraph_node_class': 'path.to.Mentor',
            'input_schema': {},
            'output_schema': {},
            'description': 'Test Mentor Agent',
            'system_instructions': 'You are a test mentor agent',
            'version': '1.0.0-test',
            'is_active': True
        }
    )
    return agent

@pytest.fixture
def generic_agent_other(db):
    """Fixture for another GenericAgent."""
    agent, _ = GenericAgent.objects.get_or_create(
        role="OtherRole",
        defaults={
            'langgraph_node_class': 'path.to.Other',
            'input_schema': {},
            'output_schema': {},
            'description': 'Test Other Agent',
            'system_instructions': 'You are a test other agent',
            'version': '1.0.0-test',
            'is_active': True
        }
    )
    return agent

@pytest.fixture
def benchmark_scenario_1(db, generic_agent_mentor):
    """Fixture for first active scenario with fixed ID 1."""
    # Import the utility function
    from apps.main.tests.utils import create_test_scenario

    # Delete any existing scenario with ID 1
    BenchmarkScenario.objects.filter(id=1).delete()

    # Create a new scenario with a fixed name (no random suffix)
    scenario = create_test_scenario(
        name="Scenario One",
        agent_role=ModelAgentRole.MENTOR.value,
        is_active=True,
        is_latest=True,
        input_data={"user_query": "Hello"},
        add_random_suffix=False
    )

    # Use raw SQL to set the ID to 1
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute(
            "UPDATE main_benchmarkscenario SET id = 1 WHERE id = %s",
            [scenario.id]
        )

    # Get the updated scenario
    scenario = BenchmarkScenario.objects.get(id=1)

    return scenario

@pytest.fixture
def benchmark_scenario_2(db, generic_agent_mentor):
    """Fixture for second active scenario with fixed ID 2."""
    # Import the utility function
    from apps.main.tests.utils import create_test_scenario

    # Delete any existing scenario with ID 2
    BenchmarkScenario.objects.filter(id=2).delete()

    # Create a new scenario with a fixed name (no random suffix)
    scenario = create_test_scenario(
        name="Scenario Two (Mentor)",
        agent_role=ModelAgentRole.MENTOR.value,
        is_active=True,
        is_latest=True,
        input_data={"user_query": "Help me"},
        add_random_suffix=False
    )

    # Use raw SQL to set the ID to 2
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute(
            "UPDATE main_benchmarkscenario SET id = 2 WHERE id = %s",
            [scenario.id]
        )

    # Get the updated scenario
    scenario = BenchmarkScenario.objects.get(id=2)

    return scenario

@pytest.fixture
def benchmark_scenario_3(db, generic_agent_other):
    """Fixture for third active scenario with fixed ID 3."""
    # Import the utility function
    from apps.main.tests.utils import create_test_scenario

    # Delete any existing scenario with ID 3
    BenchmarkScenario.objects.filter(id=3).delete()

    # Create a new scenario with a fixed name (no random suffix)
    scenario = create_test_scenario(
        name="Scenario Three (Other)",
        agent_role="OtherRole",
        is_active=True,
        is_latest=True,
        input_data={"user_query": "Other request"},
        add_random_suffix=False
    )

    # Use raw SQL to set the ID to 3
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute(
            "UPDATE main_benchmarkscenario SET id = 3 WHERE id = %s",
            [scenario.id]
        )

    # Get the updated scenario
    scenario = BenchmarkScenario.objects.get(id=3)

    return scenario

@pytest.fixture
def benchmark_scenario_inactive(db, generic_agent_mentor):
    """Fixture for an inactive scenario with fixed ID 4."""
    # Import the utility function
    from apps.main.tests.utils import create_test_scenario

    # Delete any existing scenario with ID 4
    BenchmarkScenario.objects.filter(id=4).delete()

    # Create a new scenario with a fixed name (no random suffix)
    scenario = create_test_scenario(
        name="Inactive Scenario",
        agent_role=ModelAgentRole.MENTOR.value,
        is_active=False,
        is_latest=True,
        input_data={"user_query": "Inactive"},
        add_random_suffix=False
    )

    # Use raw SQL to set the ID to 4
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute(
            "UPDATE main_benchmarkscenario SET id = 4 WHERE id = %s",
            [scenario.id]
        )

    # Get the updated scenario
    scenario = BenchmarkScenario.objects.get(id=4)

    return scenario

@pytest.fixture
def async_benchmark_scenario_1(db, generic_agent_mentor):
    """Async fixture for first active scenario with fixed ID 1."""
    @sync_to_async
    def _get_or_create_scenario():
        # Import the utility function
        from apps.main.tests.utils import create_test_scenario

        # Delete any existing scenario with ID 1
        BenchmarkScenario.objects.filter(id=1).delete()

        # Create a new scenario with a fixed name (no random suffix)
        scenario = create_test_scenario(
            name="Scenario One",
            agent_role=ModelAgentRole.MENTOR.value,
            is_active=True,
            is_latest=True,
            input_data={"user_query": "Hello"},
            add_random_suffix=False
        )

        # Use raw SQL to set the ID to 1
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute(
                "UPDATE main_benchmarkscenario SET id = 1 WHERE id = %s",
                [scenario.id]
            )

        # Get the updated scenario
        scenario = BenchmarkScenario.objects.get(id=1)

        return scenario

    return _get_or_create_scenario
