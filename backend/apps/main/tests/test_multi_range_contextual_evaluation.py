"""
Tests for multi-range contextual evaluation functionality.
"""
import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from apps.main.services.benchmark_manager import AgentBenchmarker


@pytest.mark.asyncio
class TestMultiRangeContextualEvaluation:
    """Test multi-range contextual evaluation functionality."""

    @pytest.fixture
    def benchmark_manager(self):
        """Create a BenchmarkManager instance for testing."""
        return AgentBenchmarker()

    @pytest.fixture
    def sample_contextual_template(self):
        """Sample contextual evaluation template with multiple ranges."""
        return {
            'id': '6',
            'name': 'Test Contextual Template',
            'category': 'contextual',
            'criteria': {
                'Goal_Alignment': ['Basic goal setting', 'Clear objectives'],
                'Skill_Development': ['Competency building', 'Learning facilitation']
            },
            'contextual_criteria': {
                'trust_level': {
                    '0-39': {
                        'Goal_Alignment': ['Basic goal setting', 'Simple objectives'],
                        'Skill_Development': ['Fundamental skills', 'Basic competencies']
                    },
                    '40-69': {
                        'Goal_Alignment': ['Balanced objectives', 'Moderate challenges'],
                        'Skill_Development': ['Intermediate skills', 'Balanced competencies']
                    },
                    '70-100': {
                        'Goal_Alignment': ['Ambitious objectives', 'Stretch challenges'],
                        'Skill_Development': ['Advanced skills', 'Expert competencies']
                    }
                },
                'mood': {
                    'valence': {
                        '-1.0--0.3': {
                            'Goal_Alignment': ['Mood-sensitive goals', 'Emotional healing'],
                            'Skill_Development': ['Gentle skill building', 'Supportive learning']
                        },
                        '-0.3-0.3': {
                            'Goal_Alignment': ['Balanced objectives', 'Steady targets'],
                            'Skill_Development': ['Steady skill building', 'Consistent learning']
                        },
                        '0.3-1.0': {
                            'Goal_Alignment': ['Ambitious objectives', 'High-energy targets'],
                            'Skill_Development': ['Accelerated skill building', 'Dynamic learning']
                        }
                    }
                }
            }
        }

    def test_generate_context_combinations(self, benchmark_manager, sample_contextual_template):
        """Test generation of context combinations from contextual criteria."""
        combinations = benchmark_manager._generate_context_combinations(sample_contextual_template)

        # Should generate Cartesian product of trust_level and mood.valence ranges
        # 3 trust ranges × 3 valence ranges = 9 combinations
        assert len(combinations) == 9

        # All combinations should have both trust_level and mood
        for combo in combinations:
            assert 'trust_level' in combo
            assert 'mood' in combo
            assert 'valence' in combo['mood']

        # Check that all trust values are present (midpoints of ranges)
        trust_values = set(c['trust_level'] for c in combinations)
        expected_trust_values = {19.5, 54.5, 85.0}  # midpoints of 0-39, 40-69, 70-100
        assert trust_values == expected_trust_values

        # Check that all valence values are present (midpoints of ranges)
        valence_values = set(c['mood']['valence'] for c in combinations)
        expected_valence_values = {-0.65, 0.0, 0.65}  # midpoints of -1.0--0.3, -0.3-0.3, 0.3-1.0
        assert valence_values == expected_valence_values

        # Verify each combination has range_info
        for combo in combinations:
            assert 'range_info' in combo
            assert 'trust_level' in combo['range_info']
            assert 'mood_valence' in combo['range_info']

    def test_range_midpoint_calculation(self, benchmark_manager):
        """Test the range midpoint calculation for various range formats."""
        # Test positive ranges
        template = {'contextual_criteria': {'trust_level': {'0-39': {}, '40-69': {}, '70-100': {}}}}
        combinations = benchmark_manager._generate_context_combinations(template)
        trust_values = [c['trust_level'] for c in combinations]

        assert 19.5 in trust_values
        assert 54.5 in trust_values
        assert 85.0 in trust_values

        # Test negative ranges
        template = {'contextual_criteria': {'mood': {'valence': {'-1.0--0.3': {}, '-0.3-0.3': {}, '0.3-1.0': {}}}}}
        combinations = benchmark_manager._generate_context_combinations(template)
        valence_values = [c['mood']['valence'] for c in combinations]

        assert -0.65 in valence_values
        assert 0.0 in valence_values
        assert 0.65 in valence_values

    @pytest.mark.asyncio
    async def test_multi_range_evaluation_flow(self, benchmark_manager, sample_contextual_template):
        """Test the complete multi-range evaluation flow."""
        # Mock dependencies
        with patch.object(benchmark_manager, '_adapt_criteria_for_context') as mock_adapt, \
             patch.object(benchmark_manager, '_evaluate_semantic_quality_with_llm') as mock_evaluate:

            # Setup mocks
            mock_adapt.return_value = {'Goal_Alignment': ['test criteria']}
            mock_evaluate.return_value = {
                'dimensions': {'Goal_Alignment': {'score': 80, 'reasoning': 'Good alignment'}},
                'overall_score': 80,
                'overall_reasoning': 'Test evaluation',
                'error': False
            }

            # Mock scenario
            mock_scenario = MagicMock()
            mock_scenario.description = "Test scenario"

            # Test that context combinations are generated correctly
            combinations = benchmark_manager._generate_context_combinations(sample_contextual_template)
            assert len(combinations) == 9  # 3 trust × 3 valence = 9 combinations

            # Verify that each combination has range_info with both variables
            for combo in combinations:
                assert 'range_info' in combo
                assert len(combo['range_info']) == 2  # Each combo has trust_level and mood_valence
                assert 'trust_level' in combo['range_info']
                assert 'mood_valence' in combo['range_info']

    def test_empty_contextual_criteria(self, benchmark_manager):
        """Test behavior with empty contextual criteria."""
        template = {'criteria': {'Goal_Alignment': ['test']}}
        combinations = benchmark_manager._generate_context_combinations(template)
        assert combinations == []

    def test_invalid_range_format(self, benchmark_manager):
        """Test handling of invalid range formats."""
        template = {
            'contextual_criteria': {
                'trust_level': {
                    'invalid-range': {},
                    '0-39': {},
                    'another-invalid': {}
                }
            }
        }
        combinations = benchmark_manager._generate_context_combinations(template)

        # Should generate combinations for all ranges, with invalid ones getting value 0
        assert len(combinations) == 3  # All ranges are processed

        # Find the valid range
        valid_combo = next(c for c in combinations if c['trust_level'] == 19.5)
        assert valid_combo['range_info']['trust_level'] == '0-39'

        # Invalid ranges should get value 0
        invalid_combos = [c for c in combinations if c['trust_level'] == 0]
        assert len(invalid_combos) == 2
