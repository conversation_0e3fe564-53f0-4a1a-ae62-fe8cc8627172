"""
Fixed utility functions for workflow benchmarking tests.

This module contains fixed utility functions for creating test data for workflow benchmarking tests,
with proper schema validation support.
"""

import uuid
from asgiref.sync import sync_to_async
from django.db.models import Q

from apps.main.models import BenchmarkScenario
from apps.main.tests.utils import generate_unique_scenario_name


async def create_test_workflow_scenario_fixed(workflow_type="test_workflow", name=None):
    """Create a test workflow benchmark scenario with proper schema validation support."""
    # Generate a unique name to avoid conflicts
    unique_name = name or generate_unique_scenario_name(f"test_{workflow_type}_workflow")

    # Check if scenario with this name already exists
    exists = await sync_to_async(
        lambda: BenchmarkScenario.objects.filter(Q(name=unique_name)).exists(),
        thread_sensitive=True
    )()

    if exists:
        # If it exists, return the existing scenario
        return await sync_to_async(
            lambda: BenchmarkScenario.objects.get(name=unique_name),
            thread_sensitive=True
        )()

    # Create a new scenario with all required fields for schema validation
    return await sync_to_async(
        BenchmarkScenario.objects.create,
        thread_sensitive=True
    )(
        name=unique_name,
        description=f"Test {workflow_type} workflow benchmark scenario",
        agent_role="workflow",
        input_data={
            "user_profile_id": "test-user-123",
            "context_packet": {
                "workflow_type": workflow_type,  # Required by situation schema
                "text": "This is a test situation for workflow benchmarking",  # Required by situation schema
                "task_type": workflow_type,
                "user_message": "Test message for workflow benchmarking",
                "context": "Testing workflow benchmarking system"
            }
        },
        metadata={
            "workflow_type": workflow_type,
            "situation": {
                "workflow_type": workflow_type,
                "text": "This is a test situation for workflow benchmarking",
                "context": "Testing workflow benchmarking system"
            },
            "evaluation_criteria": {
                "criteria": [
                    {
                        "dimension": "Completeness",
                        "description": "Is the workflow output complete?",
                        "weight": 0.5
                    },
                    {
                        "dimension": "Accuracy",
                        "description": "Is the workflow output accurate?",
                        "weight": 0.5
                    }
                ]
            },
            "mock_tool_responses": {
                "get_user_profile": {
                    "response": '{"id": "test-user-123", "name": "Test User", "preferences": {"language": "en", "theme": "light"}}'
                },
                "get_user_activities": {
                    "response": '{"activities": [{"id": "act1", "name": "Activity 1", "description": "Test activity 1"}]}'
                }
            },
            # Also include mock_responses for backward compatibility in the correct format
            "mock_responses": {
                "get_user_profile": '{"id": "test-user-123", "name": "Test User", "preferences": {"language": "en", "theme": "light"}}',
                "get_user_activities": '{"activities": [{"id": "act1", "name": "Activity 1", "description": "Test activity 1"}]}'
            },
            "evaluator_models": ["test-model"],
            "expected_quality_criteria": {
                "Clarity": ["Is the response clear?", "Is the language simple?"],
                "Helpfulness": ["Is the response helpful?", "Does it address the user's needs?"]
            },
            "warmup_runs": 1,
            "benchmark_runs": 2,
            "expected_stages": ["init", "process", "complete"],
            "expected_tool_calls": {
                "get_user_profile": 1,
                "get_user_activities": 1
            }
        },
        is_active=True
    )
