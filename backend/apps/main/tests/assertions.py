# backend/apps/main/tests/assertions.py
import json
from typing import Dict, Any, List, Optional, Union

class AgentAssertions:
    """Custom assertions for agent testing"""
    
    @staticmethod
    def assert_contains_context_packet(output_data: Dict[str, Any]) -> None:
        """Assert that output data contains a valid context packet"""
        assert "context_packet" in output_data, "Output missing context_packet"
        assert isinstance(output_data["context_packet"], dict), "context_packet is not a dictionary"
        
        # Assert essential fields are present
        required_fields = ["session_timestamp", "user_id"]
        for field in required_fields:
            assert field in output_data["context_packet"], f"context_packet missing required field: {field}"
    
    @staticmethod
    def assert_has_next_agent(output_data: Dict[str, Any], expected_agent: Optional[str] = None) -> None:
        """Assert that output data has a next agent specification"""
        assert "forwardTo" in output_data, "Output missing forwardTo field"
        
        if expected_agent:
            assert output_data["forwardTo"] == expected_agent, \
                f"Expected forwardTo to be '{expected_agent}', got '{output_data['forwardTo']}'"
    
    @staticmethod
    def assert_activity_properties(activity: Dict[str, Any], expected_properties: Dict[str, Any]) -> None:
        """Assert that an activity has expected properties"""
        for prop, expected in expected_properties.items():
            assert prop in activity, f"Activity missing expected property: {prop}"
            
            if isinstance(expected, dict) and isinstance(activity[prop], dict):
                # Recursive check for nested dictionaries
                for sub_prop, sub_expected in expected.items():
                    assert sub_prop in activity[prop], f"Activity.{prop} missing: {sub_prop}"
                    assert activity[prop][sub_prop] == sub_expected, \
                        f"Activity.{prop}.{sub_prop} expected {sub_expected}, got {activity[prop][sub_prop]}"
            else:
                # Direct comparison for non-dict values
                assert activity[prop] == expected, \
                    f"Activity.{prop} expected {expected}, got {activity[prop]}"
    
    @staticmethod
    def assert_contains_mood(context: Dict[str, Any], expected_terms: List[str] = None) -> None:
        """Assert that context includes mood with expected terms"""
        assert "reported_mood" in context, "Context missing reported_mood"
        
        if expected_terms:
            mood_text = context["reported_mood"].lower()
            for term in expected_terms:
                assert term.lower() in mood_text, f"Expected mood to contain '{term}'"