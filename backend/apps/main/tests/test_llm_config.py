import pytest
from decimal import Decimal
from apps.main.models import LLMConfig


@pytest.mark.django_db
class TestLLMConfig:
    """Tests for the LLMConfig model."""

    def test_create_llm_config(self):
        """Test creating a LLMConfig instance with is_evaluation field."""
        config = LLMConfig.objects.create(
            name="test-llm-config-1",
            model_name="test-model",
            temperature=0.7,
            input_token_price=Decimal("0.0001"),
            output_token_price=Decimal("0.0002"),
            is_default=False,
            is_evaluation=True
        )

        # Verify the config was created
        assert config.pk is not None
        assert config.name == "test-llm-config-1"
        assert config.model_name == "test-model"
        assert config.temperature == 0.7
        assert config.input_token_price == Decimal("0.0001")
        assert config.output_token_price == Decimal("0.0002")
        assert config.is_default is False
        assert config.is_evaluation is True

    def test_filter_by_is_evaluation(self):
        """Test filtering LLMConfig by is_evaluation field."""
        # Create configs with different is_evaluation values
        LLMConfig.objects.create(
            name="test-llm-config-eval",
            model_name="test-model-eval",
            temperature=0.7,
            input_token_price=Decimal("0.0001"),
            output_token_price=Decimal("0.0002"),
            is_default=False,
            is_evaluation=True
        )

        LLMConfig.objects.create(
            name="test-llm-config-non-eval",
            model_name="test-model-non-eval",
            temperature=0.7,
            input_token_price=Decimal("0.0001"),
            output_token_price=Decimal("0.0002"),
            is_default=False,
            is_evaluation=False
        )

        # Filter by is_evaluation=True
        eval_configs = LLMConfig.objects.filter(is_evaluation=True)
        assert eval_configs.count() >= 1
        assert "test-llm-config-eval" in [config.name for config in eval_configs]

        # Filter by is_evaluation=False
        non_eval_configs = LLMConfig.objects.filter(is_evaluation=False)
        assert non_eval_configs.count() >= 1
        assert "test-llm-config-non-eval" in [config.name for config in non_eval_configs]
