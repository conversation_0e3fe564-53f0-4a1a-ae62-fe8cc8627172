"""
Tests for contextual evaluation templates functionality.
"""
import pytest
from django.test import TestCase
from apps.main.models import EvaluationCriteriaTemplate


class TestContextualEvaluationTemplates(TestCase):
    """Test contextual evaluation templates functionality."""

    def setUp(self):
        """Set up test data."""
        # Use get_or_create to avoid unique constraint violations
        self.template, created = EvaluationCriteriaTemplate.objects.get_or_create(
            name="Test Contextual Template",
            defaults={
                "description": "Test template for contextual evaluation",
                "workflow_type": "wheel_generation",
                "category": "contextual",
                "criteria": {
                    "Content": ["Relevance", "Personalization"],
                    "Tone": ["Appropriate", "Supportive"]
                },
                "contextual_criteria": {
                "trust_level": {
                    "0-39": {
                        "Tone": ["Simple", "Clear", "Reassuring"],
                        "Content": ["Safe options", "Low-risk activities"]
                    },
                    "40-69": {
                        "Tone": ["Encouraging", "Supportive"],
                        "Content": ["Balanced options", "Some stretch goals"]
                    },
                    "70-100": {
                        "Tone": ["Collaborative", "Empowering"],
                        "Content": ["Ambitious goals", "Creative challenges"]
                    }
                },
                "mood": {
                    "valence": {
                        "-1.0-0.0": {
                            "Tone": ["Gentle", "Understanding", "Patient"]
                        },
                        "0.0-1.0": {
                            "Tone": ["Enthusiastic", "Energetic", "Positive"]
                        }
                    }
                },
                "environment": {
                    "stress_level": {
                        "0-30": {
                            "Approach": ["Detailed", "Comprehensive"]
                        },
                        "71-100": {
                            "Approach": ["Concise", "Essential-only"]
                        }
                    }
                }
                },
                "variable_ranges": {
                "trust_level": {"min": 0, "max": 100},
                "mood": {
                    "valence": {"min": -1.0, "max": 1.0},
                    "arousal": {"min": -1.0, "max": 1.0}
                },
                "environment": {
                    "stress_level": {"min": 0, "max": 100}
                },
                "is_active": True
            }
        }
        )

    def test_basic_criteria_retrieval(self):
        """Test that basic criteria are returned when no context is provided."""
        criteria = self.template.get_criteria_for_context({})

        expected = {
            "Content": ["Relevance", "Personalization"],
            "Tone": ["Appropriate", "Supportive"]
        }

        self.assertEqual(criteria, expected)

    def test_trust_level_low_adaptation(self):
        """Test criteria adaptation for low trust level."""
        context = {"trust_level": 25}
        criteria = self.template.get_criteria_for_context(context)

        # Should include base criteria plus trust level adaptations
        self.assertIn("Content", criteria)
        self.assertIn("Tone", criteria)

        # Check that trust level adaptations are applied
        self.assertIn("Simple", criteria["Tone"])
        self.assertIn("Clear", criteria["Tone"])
        self.assertIn("Safe options", criteria["Content"])

    def test_trust_level_medium_adaptation(self):
        """Test criteria adaptation for medium trust level."""
        context = {"trust_level": 55}
        criteria = self.template.get_criteria_for_context(context)

        # Check that medium trust level adaptations are applied
        self.assertIn("Encouraging", criteria["Tone"])
        self.assertIn("Balanced options", criteria["Content"])

    def test_trust_level_high_adaptation(self):
        """Test criteria adaptation for high trust level."""
        context = {"trust_level": 85}
        criteria = self.template.get_criteria_for_context(context)

        # Check that high trust level adaptations are applied
        self.assertIn("Collaborative", criteria["Tone"])
        self.assertIn("Ambitious goals", criteria["Content"])

    def test_mood_valence_negative_adaptation(self):
        """Test criteria adaptation for negative mood valence."""
        context = {"mood": {"valence": -0.5}}
        criteria = self.template.get_criteria_for_context(context)

        # Check that negative valence adaptations are applied
        self.assertIn("Gentle", criteria["Tone"])
        self.assertIn("Understanding", criteria["Tone"])

    def test_mood_valence_positive_adaptation(self):
        """Test criteria adaptation for positive mood valence."""
        context = {"mood": {"valence": 0.7}}
        criteria = self.template.get_criteria_for_context(context)

        # Check that positive valence adaptations are applied
        self.assertIn("Enthusiastic", criteria["Tone"])
        self.assertIn("Energetic", criteria["Tone"])

    def test_environment_stress_low_adaptation(self):
        """Test criteria adaptation for low stress environment."""
        context = {"environment": {"stress_level": 15}}
        criteria = self.template.get_criteria_for_context(context)

        # Check that low stress adaptations are applied
        self.assertIn("Approach", criteria)
        self.assertIn("Detailed", criteria["Approach"])

    def test_environment_stress_high_adaptation(self):
        """Test criteria adaptation for high stress environment."""
        context = {"environment": {"stress_level": 85}}
        criteria = self.template.get_criteria_for_context(context)

        # Check that high stress adaptations are applied
        self.assertIn("Approach", criteria)
        self.assertIn("Concise", criteria["Approach"])

    def test_multiple_context_variables(self):
        """Test criteria adaptation with multiple context variables."""
        context = {
            "trust_level": 75,
            "mood": {"valence": 0.5},
            "environment": {"stress_level": 20}
        }
        criteria = self.template.get_criteria_for_context(context)

        # Should include adaptations from all context variables
        self.assertIn("Collaborative", criteria["Tone"])  # High trust
        self.assertIn("Enthusiastic", criteria["Tone"])   # Positive mood
        self.assertIn("Detailed", criteria["Approach"])   # Low stress

    def test_range_parsing_negative_values(self):
        """Test that negative range parsing works correctly."""
        # Test negative range parsing
        self.assertTrue(self.template._value_in_range(-0.5, "-1.0-0.0"))
        self.assertFalse(self.template._value_in_range(0.5, "-1.0-0.0"))

    def test_range_parsing_positive_values(self):
        """Test that positive range parsing works correctly."""
        self.assertTrue(self.template._value_in_range(0.5, "0.0-1.0"))
        self.assertFalse(self.template._value_in_range(-0.5, "0.0-1.0"))

    def test_range_parsing_integer_values(self):
        """Test that integer range parsing works correctly."""
        self.assertTrue(self.template._value_in_range(25, "0-39"))
        self.assertTrue(self.template._value_in_range(55, "40-69"))
        self.assertTrue(self.template._value_in_range(85, "70-100"))
        self.assertFalse(self.template._value_in_range(45, "0-39"))

    def test_invalid_range_format(self):
        """Test that invalid range formats return False."""
        self.assertFalse(self.template._value_in_range(50, "invalid"))
        self.assertFalse(self.template._value_in_range(50, "50"))
        self.assertFalse(self.template._value_in_range(50, ""))

    def test_criteria_merging(self):
        """Test that criteria merging works correctly."""
        base = {"Tone": ["Base1", "Base2"], "Content": ["BaseContent"]}
        adaptations = {"Tone": ["Adapted1"], "NewDimension": ["New1", "New2"]}

        merged = self.template._merge_criteria(base, adaptations)

        # Should extend existing dimensions
        self.assertEqual(len(merged["Tone"]), 3)
        self.assertIn("Base1", merged["Tone"])
        self.assertIn("Adapted1", merged["Tone"])

        # Should add new dimensions
        self.assertIn("NewDimension", merged)
        self.assertEqual(merged["NewDimension"], ["New1", "New2"])

        # Should preserve unmodified dimensions
        self.assertEqual(merged["Content"], ["BaseContent"])
