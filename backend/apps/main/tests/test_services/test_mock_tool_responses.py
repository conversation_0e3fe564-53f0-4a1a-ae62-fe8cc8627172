"""
Tests for mock tool responses validation.

This module contains tests for validating mock tool responses against schemas.
"""

import pytest

from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_validator_service import SchemaValidationService


@pytest.fixture
def schema_registry():
    """Fixture to provide a SchemaRegistry instance."""
    return SchemaRegistry()


@pytest.fixture
def schema_validator(schema_registry):
    """Fixture to provide a SchemaValidationService instance."""
    return SchemaValidationService(schema_registry=schema_registry)


def test_validate_mock_tool_responses(schema_validator):
    """Test validation of mock tool responses."""
    # Create a valid mock tool responses object
    mock_responses = {
        "mock_responses": {
            "get_user_profile": "{'id': 'test-user-123', 'name': 'Test User'}",
            "get_user_activities": "{'activities': [{'id': 'act1', 'name': 'Activity 1'}]}"
        }
    }
    
    # Validate the mock responses
    is_valid, errors = schema_validator.validate_tool_expectation(mock_responses)
    
    # Verify that validation succeeds
    assert is_valid, f"Validation failed: {errors}"
    assert not errors, f"Unexpected errors: {errors}"


def test_validate_invalid_mock_tool_responses(schema_validator):
    """Test validation of invalid mock tool responses."""
    # Create an invalid mock tool responses object (missing response)
    mock_responses = {
        "mock_responses": {
            "get_user_profile": {
                # This is not a string, which is invalid
                "response": {
                    "id": "test-user-123",
                    "name": "Test User"
                }
            }
        }
    }
    
    # Validate the mock responses
    is_valid, errors = schema_validator.validate_tool_expectation(mock_responses)
    
    # Verify that validation fails
    assert not is_valid
    assert errors, "Expected validation errors but got none"
