"""
Tests for benchmark scenario validation.

This module contains tests for validating benchmark scenarios against schemas.
"""

import pytest
from unittest.mock import patch

from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_validator_service import SchemaValidationService


@pytest.fixture
def schema_registry():
    """Fixture to provide a SchemaRegistry instance."""
    return SchemaRegistry()


@pytest.fixture
def schema_validator(schema_registry):
    """Fixture to provide a SchemaValidationService instance."""
    return SchemaValidationService(schema_registry=schema_registry)


@pytest.fixture
def valid_scenario():
    """Fixture to provide a valid benchmark scenario."""
    return {
        'name': 'test_workflow_benchmark',
        'description': 'Test workflow benchmark scenario',
        'agent_role': 'workflow',
        'input_data': {
            'user_profile_id': 'test-user-123',
            'context_packet': {
                'workflow_type': 'goal_setting',
                'text': 'Test message for workflow benchmarking'
            }
        },
        'metadata': {
            'workflow_type': 'goal_setting',
            'situation': {
                'workflow_type': 'goal_setting',
                'text': 'This is a test situation for workflow benchmarking'
            },
            'evaluation_criteria': {
                'criteria': [
                    {
                        'name': 'Completeness',
                        'description': 'Is the workflow output complete?',
                        'weight': 0.5
                    },
                    {
                        'name': 'Accuracy',
                        'description': 'Is the workflow output accurate?',
                        'weight': 0.5
                    }
                ]
            },
            'mock_tool_responses': {
                'get_user_profile': {
                    'response': '{"id": "test-user-123", "name": "Test User"}'
                },
                'get_user_activities': {
                    'response': '{"activities": [{"id": "act1", "name": "Activity 1"}]}'
                }
            },
            'warmup_runs': 1,
            'benchmark_runs': 2
        }
    }


def test_validate_benchmark_scenario(schema_validator, valid_scenario):
    """Test validation of a benchmark scenario."""
    # Mock the validation methods to always return valid
    with patch.object(schema_validator, 'validate_situation', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_evaluation_criteria', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_tool_expectation', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_workflow_benchmark', return_value=(True, [])):

        # Validate the scenario
        validation_result = schema_validator.validate_benchmark_scenario(valid_scenario)

        # Verify that validation succeeds
        assert validation_result['valid'], f"Validation failed: {validation_result['errors']}"

        # Verify that all components are valid
        for component, result in validation_result['components'].items():
            assert result['valid'], f"Component {component} validation failed: {result['errors']}"


def test_validate_benchmark_scenario_invalid_tool_responses(schema_validator, valid_scenario):
    """Test validation of a benchmark scenario with invalid tool responses."""
    # Create a test scenario with invalid tool responses
    scenario = valid_scenario.copy()
    metadata = scenario['metadata'].copy()

    # Use an invalid format for mock_tool_responses (object instead of string)
    metadata['mock_tool_responses'] = {
        'get_user_profile': {
            'id': 'test-user-123',
            'name': 'Test User'
        }
    }
    scenario['metadata'] = metadata

    # Mock the validation methods to return invalid for tool expectation
    with patch.object(schema_validator, 'validate_situation', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_evaluation_criteria', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_tool_expectation', return_value=(False, ['Invalid tool expectation'])), \
         patch.object(schema_validator, 'validate_workflow_benchmark', return_value=(True, [])):

        # Validate the scenario
        validation_result = schema_validator.validate_benchmark_scenario(scenario)

        # Verify that validation fails
        assert not validation_result['valid']
        assert validation_result['errors'], "Expected validation errors but got none"
