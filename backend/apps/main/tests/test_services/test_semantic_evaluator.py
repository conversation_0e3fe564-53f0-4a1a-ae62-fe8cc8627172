"""
Tests for the SemanticEvaluator service.
"""

import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from apps.main.services.semantic_evaluator import SemanticEvaluator
from apps.main.llm.response import LLMResponse, ResponseType


@pytest.fixture
def semantic_evaluator():
    """Fixture to provide a SemanticEvaluator instance."""
    return SemanticEvaluator()


@pytest.fixture
def mock_llm_client():
    """Fixture to provide a mock LLM client."""
    mock_client = AsyncMock()

    # Configure the mock to return a valid response
    mock_response = LLMResponse(
        response_type=ResponseType.TEXT,
        content=json.dumps({
            "dimensions": {
                "Clarity": {
                    "score": 0.85,
                    "reasoning": "The response is clear and well-structured."
                },
                "Helpfulness": {
                    "score": 0.75,
                    "reasoning": "The response provides useful information but could be more detailed."
                }
            },
            "overall_score": 0.8,
            "overall_reasoning": "Overall, the response is good but has room for improvement."
        }),
        input_tokens=100,
        output_tokens=50
    )

    mock_client.chat_completion = AsyncMock(return_value=mock_response)
    return mock_client


@pytest.mark.asyncio
async def test_evaluate_response_success(semantic_evaluator, mock_llm_client):
    """Test successful evaluation of a response."""
    # Patch the _get_llm_client method to return our mock
    with patch.object(semantic_evaluator, '_get_llm_client', return_value=mock_llm_client):
        # Define test inputs
        scenario_context = "Test scenario context"
        agent_response = "Test agent response"
        criteria = {
            "Clarity": ["Is the response clear?", "Is it well-structured?"],
            "Helpfulness": ["Does the response provide useful information?"]
        }
        evaluator_models = ["test-model"]

        # Call the method
        result = await semantic_evaluator.evaluate_response(
            scenario_context=scenario_context,
            agent_response=agent_response,
            criteria=criteria,
            evaluator_models=evaluator_models
        )

        # Verify the result
        assert "test-model" in result
        assert "_meta" in result
        assert result["_meta"]["primary_model"] == "test-model"

        model_result = result["test-model"]
        assert model_result["overall_score"] == 0.8
        assert "overall_reasoning" in model_result
        assert "dimensions" in model_result
        assert "Clarity" in model_result["dimensions"]
        assert "Helpfulness" in model_result["dimensions"]
        assert model_result["dimensions"]["Clarity"]["score"] == 0.85

        # Verify the LLM client was called correctly
        mock_llm_client.chat_completion.assert_called_once()
        args, kwargs = mock_llm_client.chat_completion.call_args
        assert "messages" in kwargs
        assert len(kwargs["messages"]) == 2
        assert kwargs["messages"][0]["role"] == "system"
        assert kwargs["messages"][1]["role"] == "user"
        assert scenario_context in kwargs["messages"][1]["content"]
        assert agent_response in kwargs["messages"][1]["content"]


@pytest.mark.asyncio
async def test_evaluate_response_multiple_models(semantic_evaluator, mock_llm_client):
    """Test evaluation with multiple models."""
    # Create a second mock client with different results
    mock_client2 = AsyncMock()
    mock_response2 = LLMResponse(
        response_type=ResponseType.TEXT,
        content=json.dumps({
            "dimensions": {
                "Clarity": {
                    "score": 0.9,
                    "reasoning": "Very clear response."
                },
                "Helpfulness": {
                    "score": 0.8,
                    "reasoning": "Quite helpful."
                }
            },
            "overall_score": 0.85,
            "overall_reasoning": "Better than average response."
        }),
        input_tokens=100,
        output_tokens=50
    )
    mock_client2.chat_completion = AsyncMock(return_value=mock_response2)

    # Patch the _get_llm_client method to return our mocks based on model name
    async def mock_get_llm_client(model_name):
        if model_name == "test-model-1":
            return mock_llm_client
        else:
            return mock_client2

    with patch.object(semantic_evaluator, '_get_llm_client', side_effect=mock_get_llm_client):
        # Define test inputs
        scenario_context = "Test scenario context"
        agent_response = "Test agent response"
        criteria = {
            "Clarity": ["Is the response clear?"],
            "Helpfulness": ["Is it helpful?"]
        }
        evaluator_models = ["test-model-1", "test-model-2"]

        # Call the method
        result = await semantic_evaluator.evaluate_response(
            scenario_context=scenario_context,
            agent_response=agent_response,
            criteria=criteria,
            evaluator_models=evaluator_models
        )

        # Verify the result contains both models
        assert "test-model-1" in result
        assert "test-model-2" in result
        assert "_meta" in result
        assert result["_meta"]["primary_model"] == "test-model-1"

        # Verify normalized scores are calculated
        assert "normalized_scores" in result["_meta"]
        assert "overall" in result["_meta"]["normalized_scores"]
        assert result["_meta"]["normalized_scores"]["overall"] == (0.8 + 0.85) / 2


@pytest.mark.asyncio
async def test_evaluate_response_error_handling(semantic_evaluator):
    """Test error handling during evaluation."""
    # Create a mock client that raises an exception
    mock_error_client = AsyncMock()
    mock_error_client.chat_completion = AsyncMock(side_effect=Exception("Test error"))

    with patch.object(semantic_evaluator, '_get_llm_client', return_value=mock_error_client):
        # Define test inputs
        scenario_context = "Test scenario context"
        agent_response = "Test agent response"
        criteria = {"Clarity": ["Is it clear?"]}
        evaluator_models = ["test-model"]

        # Call the method
        result = await semantic_evaluator.evaluate_response(
            scenario_context=scenario_context,
            agent_response=agent_response,
            criteria=criteria,
            evaluator_models=evaluator_models
        )

        # Verify the result contains error information
        assert "test-model" in result
        assert "_meta" in result
        assert "errors" in result["_meta"]
        assert len(result["_meta"]["errors"]) == 1

        model_result = result["test-model"]
        assert model_result["error"] is True
        assert "error_message" in model_result
        assert model_result["overall_score"] == 0.0


@pytest.mark.asyncio
async def test_parse_evaluation_response(semantic_evaluator):
    """Test parsing of evaluation responses."""
    # Test with valid JSON
    valid_json = '{"dimensions": {"Clarity": {"score": 0.9, "reasoning": "Very clear"}}, "overall_score": 0.9, "overall_reasoning": "Good"}'
    result = semantic_evaluator._parse_evaluation_response(valid_json)
    assert result["overall_score"] == 0.9
    assert "dimensions" in result
    assert "Clarity" in result["dimensions"]

    # Test with JSON in code block
    code_block_json = '```json\n{"dimensions": {"Clarity": {"score": 0.8, "reasoning": "Clear"}}, "overall_score": 0.8, "overall_reasoning": "Good"}\n```'
    result = semantic_evaluator._parse_evaluation_response(code_block_json)
    assert result["overall_score"] == 0.8

    # Test with missing overall_score
    missing_score = '{"dimensions": {"Clarity": {"score": 0.7, "reasoning": "OK"}}}'
    result = semantic_evaluator._parse_evaluation_response(missing_score)
    assert "overall_score" in result
    assert result["overall_score"] == 0.7  # Should calculate from dimension

    # Test with invalid JSON
    invalid_json = 'This is not JSON'
    result = semantic_evaluator._parse_evaluation_response(invalid_json)
    assert result["error"] is True
    assert "error_message" in result
    assert result["overall_score"] == 0.0


def test_extract_response_text(semantic_evaluator):
    """Test the _extract_response_text method."""
    # Test with direct string
    assert semantic_evaluator._extract_response_text("Direct text") == "Direct text"

    # Test with common response fields
    assert semantic_evaluator._extract_response_text({"response": "Response text"}) == "Response text"
    assert semantic_evaluator._extract_response_text({"message": "Message text"}) == "Message text"

    # Test with nested fields
    assert semantic_evaluator._extract_response_text({"response": {"text": "Nested text"}}) == "Nested text"

    # Test with fallback to string representation
    complex_data = {"complex": ["data", "structure"]}
    assert semantic_evaluator._extract_response_text(complex_data) == str(complex_data)


def test_get_scenario_context(semantic_evaluator):
    """Test the _get_scenario_context method."""
    # Create a mock scenario
    mock_scenario = MagicMock()
    mock_scenario.name = "Test Scenario"
    mock_scenario.description = "Test Description"
    mock_scenario.metadata = {
        "workflow_type": "test_workflow",
        "user_profile_context": {
            "name": "Test User",
            "preferences": {"theme": "dark"}
        }
    }
    mock_scenario.input_data = {
        "query": "Test query",
        "complex_data": {"nested": "value"}
    }

    # Get the context
    context = semantic_evaluator._get_scenario_context(mock_scenario)

    # Verify the context contains expected information
    assert "Test Scenario" in context
    assert "Test Description" in context
    assert "test_workflow" in context
    assert "Test User" in context
    assert "Test query" in context
    assert "<complex data>" in context  # Complex data should be summarized
