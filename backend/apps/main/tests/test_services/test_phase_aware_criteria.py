"""
Tests for phase-aware criteria implementation.

This module tests the phase-aware criteria functionality in the benchmark system.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from apps.main.services.benchmark_manager import AgentBenchmarker
from apps.main.services.semantic_evaluator import SemanticEvaluator
from apps.main.services.evaluation_criteria_migration import map_trust_level_to_phase
from apps.main.tests.utils.phase_aware_test_utils import create_phase_aware_test_scenario_async, get_criteria_for_trust_level


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_map_trust_level_to_phase():
    """Test mapping trust levels to phases."""
    # Test foundation phase
    assert map_trust_level_to_phase(0) == "foundation"
    assert map_trust_level_to_phase(20) == "foundation"
    assert map_trust_level_to_phase(39) == "foundation"

    # Test expansion phase
    assert map_trust_level_to_phase(40) == "expansion"
    assert map_trust_level_to_phase(55) == "expansion"
    assert map_trust_level_to_phase(69) == "expansion"

    # Test integration phase
    assert map_trust_level_to_phase(70) == "integration"
    assert map_trust_level_to_phase(85) == "integration"
    assert map_trust_level_to_phase(100) == "integration"


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_create_phase_aware_test_scenario():
    """Test creating a phase-aware test scenario."""
    # Create a phase-aware test scenario
    scenario = await create_phase_aware_test_scenario_async(
        name="test_phase_aware_scenario",
        trust_level=50
    )

    # Verify the scenario was created
    assert scenario is not None
    assert scenario.name == "test_phase_aware_scenario"
    assert "evaluation_criteria_by_phase" in scenario.metadata
    assert "foundation" in scenario.metadata["evaluation_criteria_by_phase"]
    assert "expansion" in scenario.metadata["evaluation_criteria_by_phase"]
    assert "integration" in scenario.metadata["evaluation_criteria_by_phase"]
    assert scenario.input_data["context_packet"]["trust_level"] == 50


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_get_criteria_for_trust_level():
    """Test getting criteria for a specific trust level."""
    # Create a phase-aware test scenario
    scenario = await create_phase_aware_test_scenario_async(
        foundation_criteria={"Foundation": ["Test foundation"]},
        expansion_criteria={"Expansion": ["Test expansion"]},
        integration_criteria={"Integration": ["Test integration"]}
    )

    # Get criteria for different trust levels
    foundation_criteria = get_criteria_for_trust_level(scenario, 20)
    expansion_criteria = get_criteria_for_trust_level(scenario, 50)
    integration_criteria = get_criteria_for_trust_level(scenario, 80)

    # Verify the correct criteria are returned
    assert "Foundation" in foundation_criteria
    assert "Expansion" in expansion_criteria
    assert "Integration" in integration_criteria


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_semantic_evaluator_with_phase_aware_criteria():
    """Test semantic evaluator with phase-aware criteria."""
    # Create a semantic evaluator
    evaluator = SemanticEvaluator()

    # Mock the _evaluate_with_model method
    evaluator._evaluate_with_model = AsyncMock(return_value={
        "dimensions": {"Test": {"score": 0.8, "reasoning": "Good"}},
        "overall_score": 0.8,
        "overall_reasoning": "Good overall"
    })

    # Create phase-aware criteria
    phase_aware_criteria = {
        "evaluation_criteria_by_phase": {
            "foundation": {"Foundation": ["Test foundation"]},
            "expansion": {"Expansion": ["Test expansion"]},
            "integration": {"Integration": ["Test integration"]}
        }
    }

    # Test with different trust levels
    foundation_result = await evaluator.evaluate_response(
        scenario_context="Test context",
        agent_response="Test response",
        criteria=phase_aware_criteria,
        evaluator_models=["test-model"],
        trust_level=20
    )

    expansion_result = await evaluator.evaluate_response(
        scenario_context="Test context",
        agent_response="Test response",
        criteria=phase_aware_criteria,
        evaluator_models=["test-model"],
        trust_level=50
    )

    integration_result = await evaluator.evaluate_response(
        scenario_context="Test context",
        agent_response="Test response",
        criteria=phase_aware_criteria,
        evaluator_models=["test-model"],
        trust_level=80
    )

    # Verify the results include phase information
    assert foundation_result["_meta"]["phase"] == "foundation"
    assert expansion_result["_meta"]["phase"] == "expansion"
    assert integration_result["_meta"]["phase"] == "integration"

    # Verify the trust level is included in the results
    assert foundation_result["_meta"]["trust_level"] == 20
    assert expansion_result["_meta"]["trust_level"] == 50
    assert integration_result["_meta"]["trust_level"] == 80


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_benchmark_manager_with_phase_aware_criteria():
    """Test benchmark manager with phase-aware criteria."""
    # Create a phase-aware test scenario
    scenario = await create_phase_aware_test_scenario_async(
        trust_level=50,
        foundation_criteria={"Foundation": ["Test foundation"]},
        expansion_criteria={"Expansion": ["Test expansion"]},
        integration_criteria={"Integration": ["Test integration"]}
    )

    # Create a benchmark manager
    benchmark_manager = AgentBenchmarker()

    # Create a mock LLM service
    mock_llm_service = MagicMock()
    mock_response = MagicMock()
    mock_response.content = '{"dimensions": {"Test": {"score": 0.8, "reasoning": "Good"}}, "overall_score": 0.8, "overall_reasoning": "Good overall"}'
    mock_llm_service.chat_completion = AsyncMock(return_value=mock_response)
    benchmark_manager.llm_service = mock_llm_service

    # Get the criteria for the expansion phase (trust level 50)
    expansion_criteria = get_criteria_for_trust_level(scenario, 50)

    # Test the _evaluate_semantic_quality_with_llm method directly
    result = await benchmark_manager._evaluate_semantic_quality_with_llm(
        agent_response="This is a test response",
        quality_criteria=expansion_criteria,
        scenario_context="Test scenario context",
        model_name="mistral-small-latest"
    )

    # Verify the LLM service was called
    mock_llm_service.chat_completion.assert_called_once()

    # Verify the result structure
    assert "dimensions" in result
    assert "overall_score" in result
    assert "overall_reasoning" in result

    # Verify the expansion phase criteria was used
    assert expansion_criteria == {"Expansion": ["Test expansion"]}


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_phase_transition_edge_cases():
    """Test phase transition edge cases."""
    # Create a semantic evaluator
    evaluator = SemanticEvaluator()

    # Mock the _evaluate_with_model method
    evaluator._evaluate_with_model = AsyncMock(return_value={
        "dimensions": {"Test": {"score": 0.8, "reasoning": "Good"}},
        "overall_score": 0.8,
        "overall_reasoning": "Good overall"
    })

    # Create phase-aware criteria
    phase_aware_criteria = {
        "evaluation_criteria_by_phase": {
            "foundation": {"Foundation": ["Test foundation"]},
            "expansion": {"Expansion": ["Test expansion"]},
            "integration": {"Integration": ["Test integration"]}
        }
    }

    # Test edge cases
    foundation_edge = await evaluator.evaluate_response(
        scenario_context="Test context",
        agent_response="Test response",
        criteria=phase_aware_criteria,
        evaluator_models=["test-model"],
        trust_level=39  # Edge of foundation phase
    )

    expansion_edge_lower = await evaluator.evaluate_response(
        scenario_context="Test context",
        agent_response="Test response",
        criteria=phase_aware_criteria,
        evaluator_models=["test-model"],
        trust_level=40  # Start of expansion phase
    )

    expansion_edge_upper = await evaluator.evaluate_response(
        scenario_context="Test context",
        agent_response="Test response",
        criteria=phase_aware_criteria,
        evaluator_models=["test-model"],
        trust_level=69  # Edge of expansion phase
    )

    integration_edge = await evaluator.evaluate_response(
        scenario_context="Test context",
        agent_response="Test response",
        criteria=phase_aware_criteria,
        evaluator_models=["test-model"],
        trust_level=70  # Start of integration phase
    )

    # Verify the phase transitions
    assert foundation_edge["_meta"]["phase"] == "foundation"
    assert expansion_edge_lower["_meta"]["phase"] == "expansion"
    assert expansion_edge_upper["_meta"]["phase"] == "expansion"
    assert integration_edge["_meta"]["phase"] == "integration"
