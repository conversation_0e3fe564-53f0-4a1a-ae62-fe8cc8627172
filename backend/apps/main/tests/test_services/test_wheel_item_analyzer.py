# backend/apps/main/tests/test_services/test_wheel_item_analyzer.py

import pytest
import statistics
from unittest.mock import patch
import asyncio

from apps.main.services.wheel_item_analyzer import WheelItemAnalyzer

# Sample wheel items for testing
SAMPLE_WHEEL_ITEMS = [
    {
        "id": "act-1",
        "name": "Creative Writing",
        "description": "Express yourself through writing",
        "domain": "creative",
        "duration": 20,
        "challenge_level": 60,
        "resources_required": ["pen", "paper"],
        "estimated_completion_time": 25,
        "resource_intensity": "low"
    },
    {
        "id": "act-2",
        "name": "Mindful Meditation",
        "description": "Focus on your breath",
        "domain": "reflective",
        "duration": 15,
        "challenge_level": 50,
        "resources_required": [],
        "estimated_completion_time": 20,
        "resource_intensity": "low"
    },
    {
        "id": "act-3",
        "name": "Build a Small Project",
        "description": "Create something with your hands",
        "domain": "physical",
        "duration": 45,
        "challenge_level": 80,
        "resources_required": ["wood", "nails", "hammer", "saw"],
        "estimated_completion_time": 60,
        "resource_intensity": "high"
    }
]

# Sample wheel structure for testing
SAMPLE_WHEEL = {
    "metadata": {
        "name": "Test Wheel",
        "trust_phase": "Foundation"
    },
    "items": [
        {
            "id": "item-1",
            "activity_id": "act-1",
            "percentage": 40,
            "position": 0,
            "color": "#66BB6A"
        },
        {
            "id": "item-2",
            "activity_id": "act-2",
            "percentage": 30,
            "position": 1,
            "color": "#42A5F5"
        },
        {
            "id": "item-3",
            "activity_id": "act-3",
            "percentage": 30,
            "position": 2,
            "color": "#FFA726"
        }
    ],
    "activities": SAMPLE_WHEEL_ITEMS,
    "value_propositions": {
        "act-1": {
            "growth_value": "Enhances creativity",
            "connection_to_goals": "Supports creative expression",
            "challenge_description": "Gentle creative challenge"
        },
        "act-2": {
            "growth_value": "Develops mindfulness",
            "connection_to_goals": "Supports stress reduction",
            "challenge_description": "Moderate focus challenge"
        },
        "act-3": {
            "growth_value": "Builds practical skills",
            "connection_to_goals": "Supports physical development",
            "challenge_description": "Significant physical challenge"
        }
    }
}


@pytest.fixture
def wheel_analyzer():
    """Fixture to create a WheelItemAnalyzer instance."""
    return WheelItemAnalyzer()


class TestWheelItemAnalyzer:
    """Tests for the WheelItemAnalyzer class."""

    def test_init(self, wheel_analyzer):
        """Test that the analyzer initializes correctly."""
        assert isinstance(wheel_analyzer, WheelItemAnalyzer)
        assert hasattr(wheel_analyzer, 'logger')

    def test_measure_challenge_level_basic(self, wheel_analyzer):
        """Test basic challenge level measurement with sample data."""
        result = wheel_analyzer.measure_challenge_level(SAMPLE_WHEEL_ITEMS)

        # Check that all expected keys are present
        expected_keys = [
            "overall_challenge", "time_challenge", "resource_challenge",
            "dependency_challenge", "implementation_challenge", "challenge_variance"
        ]
        for key in expected_keys:
            assert key in result

        # Check that values are in the expected range (0-100)
        for key in expected_keys:
            if key != "challenge_variance":  # Variance can be any non-negative value
                assert 0 <= result[key] <= 100

        # Check that challenge_variance is non-negative
        assert result["challenge_variance"] >= 0

        # Check that overall_challenge is a weighted average of component challenges
        # Implementation challenge should have the highest weight (0.5)
        assert abs(result["overall_challenge"] - 63.33) < 5  # Allow some tolerance for rounding

    def test_measure_challenge_level_empty(self, wheel_analyzer):
        """Test challenge level measurement with empty data."""
        result = wheel_analyzer.measure_challenge_level([])

        # Check that all expected keys are present with default values
        assert result["overall_challenge"] == 0.0
        assert result["time_challenge"] == 0.0
        assert result["resource_challenge"] == 0.0
        assert result["dependency_challenge"] == 0.0
        assert result["implementation_challenge"] == 0.0
        assert result["challenge_variance"] == 0.0

    def test_measure_challenge_level_error_handling(self, wheel_analyzer):
        """Test error handling in challenge level measurement."""
        # Create invalid data that will cause an error
        invalid_data = [{"challenge_level": "not_a_number"}]

        # Mock the _calculate_time_challenge method to raise an exception
        with patch.object(wheel_analyzer, '_calculate_time_challenge', side_effect=ValueError("Test error")):
            result = wheel_analyzer.measure_challenge_level(invalid_data)

            # Check that default values are returned with an error message
            assert result["overall_challenge"] == 50.0
            assert result["time_challenge"] == 50.0
            assert result["resource_challenge"] == 50.0
            assert result["dependency_challenge"] == 50.0
            assert result["implementation_challenge"] == 50.0
            assert result["challenge_variance"] == 0.0
            assert "error" in result
            assert "Test error" in result["error"]

    def test_calculate_distribution_metrics_basic(self, wheel_analyzer):
        """Test basic distribution metrics calculation with sample data."""
        result = wheel_analyzer.calculate_distribution_metrics(SAMPLE_WHEEL_ITEMS)

        # Check that all expected keys are present
        expected_keys = [
            "challenge_mean", "challenge_median", "challenge_min", "challenge_max",
            "challenge_std_dev", "domain_distribution", "challenge_histogram"
        ]
        for key in expected_keys:
            assert key in result

        # Check that statistical values match expected calculations
        challenge_values = [item["challenge_level"] for item in SAMPLE_WHEEL_ITEMS]
        assert result["challenge_mean"] == round(statistics.mean(challenge_values), 2)
        assert result["challenge_median"] == statistics.median(challenge_values)
        assert result["challenge_min"] == min(challenge_values)
        assert result["challenge_max"] == max(challenge_values)
        assert result["challenge_std_dev"] == round(statistics.stdev(challenge_values), 2)

        # Check domain distribution
        assert "creative" in result["domain_distribution"]
        assert "reflective" in result["domain_distribution"]
        assert "physical" in result["domain_distribution"]

        # Check histogram
        assert "51-60" in result["challenge_histogram"]
        assert result["challenge_histogram"]["51-60"] == 1  # One item with challenge level 60
        assert result["challenge_histogram"]["41-50"] == 1  # One item with challenge level 50
        assert result["challenge_histogram"]["71-80"] == 1  # One item with challenge level 80

    def test_calculate_distribution_metrics_empty(self, wheel_analyzer):
        """Test distribution metrics calculation with empty data."""
        result = wheel_analyzer.calculate_distribution_metrics([])

        # Check that all expected keys are present with default values
        assert result["challenge_mean"] == 0.0
        assert result["challenge_median"] == 0.0
        assert result["challenge_min"] == 0.0
        assert result["challenge_max"] == 0.0
        assert result["challenge_std_dev"] == 0.0
        assert result["domain_distribution"] == {}
        assert result["challenge_histogram"] == {}

    def test_calculate_distribution_metrics_error_handling(self, wheel_analyzer):
        """Test error handling in distribution metrics calculation."""
        # Create invalid data that will cause an error
        invalid_data = [{"challenge_level": "not_a_number"}]

        # Mock the statistics.mean function to raise an exception
        with patch('statistics.mean', side_effect=ValueError("Test error")):
            result = wheel_analyzer.calculate_distribution_metrics(invalid_data)

            # Check that default values are returned with an error message
            assert result["challenge_mean"] == 50.0
            assert result["challenge_median"] == 50.0
            assert result["challenge_min"] == 0.0
            assert result["challenge_max"] == 100.0
            assert result["challenge_std_dev"] == 0.0
            assert result["domain_distribution"] == {}
            assert result["challenge_histogram"] == {}
            assert "error" in result
            assert "Test error" in result["error"]

    def test_time_challenge_calculation(self, wheel_analyzer):
        """Test time challenge calculation."""
        # Create items with different time requirements
        items = [
            {"duration": 10, "estimated_completion_time": 15},  # Low time requirement
            {"duration": 30, "estimated_completion_time": 25},  # Medium time requirement
            {"duration": 60, "estimated_completion_time": 75}   # High time requirement
        ]

        result = wheel_analyzer._calculate_time_challenge(items)

        # Check that the result is in the expected range
        assert 0 <= result <= 100

        # Higher time requirements should result in higher challenge
        assert result > 50  # Should be above medium challenge due to the high time item

    def test_resource_challenge_calculation(self, wheel_analyzer):
        """Test resource challenge calculation."""
        # Create items with different resource requirements
        items = [
            {"resources_required": [], "resource_intensity": "low"},  # No resources
            {"resources_required": ["pen", "paper"], "resource_intensity": "medium"},  # Few resources
            {"resources_required": ["wood", "nails", "hammer", "saw", "drill"], "resource_intensity": "high"}  # Many resources
        ]

        result = wheel_analyzer._calculate_resource_challenge(items)

        # Check that the result is in the expected range
        assert 0 <= result <= 100

        # More resources and higher intensity should result in higher challenge
        assert result > 50  # Should be above medium challenge due to the high resource item

    def test_dependency_challenge_calculation(self, wheel_analyzer):
        """Test dependency challenge calculation."""
        # Create items with different dependency requirements
        items = [
            {"dependencies": []},  # No dependencies
            {"prerequisites": ["item1"]},  # One prerequisite
            {"dependencies": ["item2", "item3"], "prerequisites": ["item4"]}  # Multiple dependencies
        ]

        result = wheel_analyzer._calculate_dependency_challenge(items)

        # Check that the result is in the expected range
        assert 0 <= result <= 100

        # More dependencies should result in higher challenge
        # 2 out of 3 items have dependencies, so challenge should be around 30 + (2/3) * 70 = 76.67
        assert 70 <= result <= 80

    def test_implementation_challenge_calculation(self, wheel_analyzer):
        """Test implementation challenge calculation."""
        # Create items with different challenge levels
        items = [
            {"challenge_level": 30},  # Low challenge
            {"challenge_level": 50},  # Medium challenge
            {"challenge_level": 80}   # High challenge
        ]

        result = wheel_analyzer._calculate_implementation_challenge(items)

        # Check that the result is the average of the challenge levels
        assert result == (30 + 50 + 80) / 3

    def test_overall_challenge_calculation(self, wheel_analyzer):
        """Test overall challenge calculation."""
        # Test with different component challenges
        time_challenge = 60.0
        resource_challenge = 70.0
        dependency_challenge = 40.0
        implementation_challenge = 80.0

        result = wheel_analyzer._calculate_overall_challenge(
            time_challenge, resource_challenge, dependency_challenge, implementation_challenge
        )

        # Check that the result is a weighted average
        # Weights: time=0.2, resource=0.2, dependency=0.1, implementation=0.5
        expected = (0.2 * 60.0) + (0.2 * 70.0) + (0.1 * 40.0) + (0.5 * 80.0)
        assert result == expected

    def test_domain_distribution_calculation(self, wheel_analyzer):
        """Test domain distribution calculation."""
        result = wheel_analyzer._calculate_domain_distribution(SAMPLE_WHEEL_ITEMS)

        # Check that all domains are present
        assert "creative" in result
        assert "reflective" in result
        assert "physical" in result

        # Check that each domain has the expected statistics
        for domain in ["creative", "reflective", "physical"]:
            assert "count" in result[domain]
            assert "mean_challenge" in result[domain]
            assert "min_challenge" in result[domain]
            assert "max_challenge" in result[domain]
            assert "std_dev" in result[domain]

        # Check specific values for creative domain
        assert result["creative"]["count"] == 1
        assert result["creative"]["mean_challenge"] == 60.0
        assert result["creative"]["min_challenge"] == 60.0
        assert result["creative"]["max_challenge"] == 60.0
        assert result["creative"]["std_dev"] == 0.0  # Only one item, so std_dev is 0

    def test_challenge_histogram_creation(self, wheel_analyzer):
        """Test challenge histogram creation."""
        challenge_values = [10, 15, 25, 35, 45, 55, 65, 75, 85, 95]

        result = wheel_analyzer._create_challenge_histogram(challenge_values)

        # Check that all buckets are present
        expected_buckets = ['0-10', '11-20', '21-30', '31-40', '41-50',
                           '51-60', '61-70', '71-80', '81-90', '91-100']
        for bucket in expected_buckets:
            assert bucket in result

        # Check that each bucket has the correct count
        assert result['0-10'] == 1  # 10
        assert result['11-20'] == 1  # 15
        assert result['21-30'] == 1  # 25
        assert result['31-40'] == 1  # 35
        assert result['41-50'] == 1  # 45
        assert result['51-60'] == 1  # 55
        assert result['61-70'] == 1  # 65
        assert result['71-80'] == 1  # 75
        assert result['81-90'] == 1  # 85
        assert result['91-100'] == 1  # 95

    @pytest.mark.asyncio
    async def test_measure_challenge_level_async(self, wheel_analyzer):
        """Test async wrapper for measure_challenge_level."""
        # Mock the synchronous method to verify it's called
        with patch.object(wheel_analyzer, 'measure_challenge_level', return_value={"overall_challenge": 70.0}):
            result = await wheel_analyzer.measure_challenge_level_async(SAMPLE_WHEEL_ITEMS)

            # Check that the result matches the mocked return value
            assert result == {"overall_challenge": 70.0}

            # Verify that the synchronous method was called with the correct arguments
            wheel_analyzer.measure_challenge_level.assert_called_once_with(SAMPLE_WHEEL_ITEMS)

    @pytest.mark.asyncio
    async def test_calculate_distribution_metrics_async(self, wheel_analyzer):
        """Test async wrapper for calculate_distribution_metrics."""
        # Mock the synchronous method to verify it's called
        with patch.object(wheel_analyzer, 'calculate_distribution_metrics', return_value={"challenge_mean": 60.0}):
            result = await wheel_analyzer.calculate_distribution_metrics_async(SAMPLE_WHEEL_ITEMS)

            # Check that the result matches the mocked return value
            assert result == {"challenge_mean": 60.0}

            # Verify that the synchronous method was called with the correct arguments
            wheel_analyzer.calculate_distribution_metrics.assert_called_once_with(SAMPLE_WHEEL_ITEMS)

    def test_with_wheel_structure(self, wheel_analyzer):
        """Test analyzer with a complete wheel structure."""
        # Extract activities from the wheel structure
        activities = SAMPLE_WHEEL["activities"]

        # Measure challenge level
        challenge_result = wheel_analyzer.measure_challenge_level(activities)

        # Calculate distribution metrics
        distribution_result = wheel_analyzer.calculate_distribution_metrics(activities)

        # Check that both methods return valid results
        assert "overall_challenge" in challenge_result
        assert "challenge_mean" in distribution_result

        # Check that the results are consistent
        assert abs(challenge_result["overall_challenge"] - distribution_result["challenge_mean"]) < 15  # Allow some difference due to weighting

    @pytest.mark.django_db(transaction=True)
    def test_database_connection_utility_integration(self, wheel_analyzer):
        """Test integration with DatabaseConnectionUtility."""
        # Instead of mocking the decorator, we'll check that the methods are decorated
        # by inspecting the function's __wrapped__ attribute which is added by functools.wraps

        # Get the measure_challenge_level method
        measure_method = wheel_analyzer.__class__.measure_challenge_level

        # Get the calculate_distribution_metrics method
        calculate_method = wheel_analyzer.__class__.calculate_distribution_metrics

        # Check that both methods have the DatabaseConnectionUtility.with_retry decorator
        # by verifying they have the __wrapped__ attribute which is added by the decorator
        assert hasattr(measure_method, '__wrapped__'), "measure_challenge_level should be decorated with DatabaseConnectionUtility.with_retry"
        assert hasattr(calculate_method, '__wrapped__'), "calculate_distribution_metrics should be decorated with DatabaseConnectionUtility.with_retry"

        # Call the methods to ensure they work with the decorator
        result1 = wheel_analyzer.measure_challenge_level(SAMPLE_WHEEL_ITEMS)
        result2 = wheel_analyzer.calculate_distribution_metrics(SAMPLE_WHEEL_ITEMS)

        # Verify we got valid results
        assert "overall_challenge" in result1
        assert "challenge_mean" in result2
