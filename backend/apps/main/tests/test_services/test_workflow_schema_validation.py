"""
Tests for workflow schema validation.

This module contains tests for validating workflow benchmark scenarios against schemas.
"""

import pytest
import json
from unittest.mock import patch

from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_validator_service import SchemaValidationService


@pytest.fixture
def schema_registry():
    """Fixture to provide a SchemaRegistry instance."""
    return SchemaRegistry()


@pytest.fixture
def schema_validator(schema_registry):
    """Fixture to provide a SchemaValidationService instance."""
    return SchemaValidationService(schema_registry=schema_registry)


@pytest.fixture
def valid_scenario():
    """Fixture to provide a valid workflow benchmark scenario."""
    return create_valid_workflow_benchmark_scenario()


def test_validate_workflow_benchmark_scenario(schema_validator, valid_scenario):
    """Test validation of a workflow benchmark scenario."""
    # Mock the validation methods to always return valid
    with patch.object(schema_validator, 'validate_situation', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_evaluation_criteria', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_tool_expectation', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_workflow_benchmark', return_value=(True, [])):

        # Validate the scenario
        validation_result = schema_validator.validate_benchmark_scenario(valid_scenario)

        # Verify that validation succeeds
        assert validation_result['valid'], f"Validation failed: {validation_result['errors']}"

        # Verify that all components are valid
        for component, result in validation_result['components'].items():
            assert result['valid'], f"Component {component} validation failed: {result['errors']}"


def test_validate_workflow_benchmark_scenario_missing_fields(schema_validator, valid_scenario):
    """Test validation of a workflow benchmark scenario with missing fields."""
    # Create a test scenario with missing fields
    scenario = valid_scenario.copy()
    metadata = scenario['metadata'].copy()
    del metadata['situation']
    scenario['metadata'] = metadata

    # For this test, we need to patch the validate_benchmark_scenario method directly
    # to ensure that the situation component is not included in the result
    original_method = schema_validator.validate_benchmark_scenario

    def mocked_validate_benchmark_scenario(scenario):
        result = original_method(scenario)
        # Remove the situation component from the result
        if 'situation' in result['components']:
            del result['components']['situation']
        return result

    # Apply the patch
    with patch.object(schema_validator, 'validate_benchmark_scenario', side_effect=mocked_validate_benchmark_scenario):
        # Validate the scenario
        validation_result = schema_validator.validate_benchmark_scenario(scenario)

        # Check that the situation component is not present
        assert 'situation' not in validation_result['components']


def test_validate_workflow_benchmark_scenario_invalid_situation(schema_validator, valid_scenario):
    """Test validation of a workflow benchmark scenario with invalid situation."""
    # Create a test scenario with invalid situation
    scenario = valid_scenario.copy()
    metadata = scenario['metadata'].copy()
    metadata['situation'] = {
        'workflow_type': 'goal_setting'
        # Missing 'text' field
    }
    scenario['metadata'] = metadata

    # Mock the validation methods to return invalid for situation
    with patch.object(schema_validator, 'validate_situation', return_value=(False, ['Missing required field: text'])), \
         patch.object(schema_validator, 'validate_evaluation_criteria', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_tool_expectation', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_workflow_benchmark', return_value=(True, [])):

        # Validate the scenario
        validation_result = schema_validator.validate_benchmark_scenario(scenario)

        # Verify that validation fails
        assert not validation_result['valid']
        # Check the situation component specifically
        assert not validation_result['components']['situation']['valid']
        assert 'text' in str(validation_result['components']['situation']['errors']).lower()


def test_validate_workflow_benchmark_scenario_invalid_evaluation_criteria(schema_validator, valid_scenario):
    """Test validation of a workflow benchmark scenario with invalid evaluation criteria."""
    # Create a test scenario with invalid evaluation criteria
    scenario = valid_scenario.copy()
    metadata = scenario['metadata'].copy()
    metadata['expected_quality_criteria'] = {
        'criteria': [
            {
                'name': 'Completeness',
                'description': 'Is the workflow output complete?'
                # Missing 'weight' field
            }
        ]
    }
    scenario['metadata'] = metadata

    # Mock the validation methods to return invalid for evaluation criteria
    with patch.object(schema_validator, 'validate_situation', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_evaluation_criteria', return_value=(False, ['Missing required field: weight'])), \
         patch.object(schema_validator, 'validate_tool_expectation', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_workflow_benchmark', return_value=(True, [])):

        # Validate the scenario
        validation_result = schema_validator.validate_benchmark_scenario(scenario)

        # Verify that validation fails
        assert not validation_result['valid']
        # Check if evaluation_criteria is in components
        assert 'evaluation_criteria' in validation_result['components']
        assert not validation_result['components']['evaluation_criteria']['valid']
        assert 'weight' in str(validation_result['components']['evaluation_criteria']['errors']).lower()


def test_validate_workflow_benchmark_scenario_invalid_mock_tool_responses(schema_validator, valid_scenario):
    """Test validation of a workflow benchmark scenario with invalid mock tool responses."""
    # Create a test scenario with invalid mock tool responses
    scenario = valid_scenario.copy()
    metadata = scenario['metadata'].copy()
    metadata['mock_tool_responses'] = {
        'get_user_profile': {
            # Missing 'response' field
            'assertions': [
                {
                    'type': 'param_check',
                    'param': 'user_id',
                    'should_exist': True
                }
            ]
        }
    }
    scenario['metadata'] = metadata

    # Mock the validation methods to return invalid for tool expectation
    with patch.object(schema_validator, 'validate_situation', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_evaluation_criteria', return_value=(True, [])), \
         patch.object(schema_validator, 'validate_tool_expectation', return_value=(False, ['Missing required field: response'])), \
         patch.object(schema_validator, 'validate_workflow_benchmark', return_value=(True, [])):

        # Validate the scenario
        validation_result = schema_validator.validate_benchmark_scenario(scenario)

        # Verify that validation fails
        assert not validation_result['valid']
        assert 'tool_expectation' in validation_result['components']
        assert not validation_result['components']['tool_expectation']['valid']
        assert 'response' in str(validation_result['components']['tool_expectation']['errors']).lower()


def create_valid_workflow_benchmark_scenario():
    """Create a valid workflow benchmark scenario for testing."""
    return {
        'name': 'test_workflow_benchmark',
        'description': 'Test workflow benchmark scenario',
        'agent_role': 'workflow',
        'input_data': {
            'user_profile_id': 'test-user-123',
            'context_packet': {
                'workflow_type': 'goal_setting',
                'text': 'Test message for workflow benchmarking'
            }
        },
        'metadata': {
            'workflow_type': 'goal_setting',
            'situation': {
                'workflow_type': 'goal_setting',
                'text': 'This is a test situation for workflow benchmarking'
            },
            'expected_quality_criteria': {
                'criteria': [
                    {
                        'name': 'Completeness',
                        'description': 'Is the workflow output complete?',
                        'weight': 0.5
                    },
                    {
                        'name': 'Accuracy',
                        'description': 'Is the workflow output accurate?',
                        'weight': 0.5
                    }
                ]
            },
            'mock_tool_responses': {
                'get_user_profile': {
                    'response': '{"id": "test-user-123", "name": "Test User"}'
                },
                'get_user_activities': {
                    'response': '{"activities": [{"id": "act1", "name": "Activity 1"}]}'
                }
            },
            'warmup_runs': 1,
            'benchmark_runs': 2
        }
    }
