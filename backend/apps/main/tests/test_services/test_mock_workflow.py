"""
Tests for the mock workflow implementation.

This module contains tests for the MockWorkflow and TestWorkflow classes.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from apps.main.testing.mock_workflow import MockWorkflow, TestWorkflow
from apps.main.testing.mock_tool_registry import MockToolRegistry
from apps.main.tests.workflow_utils import create_test_workflow_scenario_async


@pytest.fixture
def mock_tool_registry():
    """Fixture to provide a MockToolRegistry instance."""
    return MockToolRegistry()


@pytest.fixture
async def workflow_scenario(db):
    """Fixture to provide a workflow benchmark scenario."""
    return await create_test_workflow_scenario_async(
        workflow_type="test_workflow",
        input_data={
            "user_profile_id": "test-user-123",
            "context_packet": {
                "task_type": "test_workflow",
                "user_message": "Test message"
            }
        },
        metadata={
            "workflow_type": "test_workflow",
            "expected_quality_criteria": {
                "Clarity": ["Is the response clear?"]
            },
            "mock_tool_responses": {
                "get_user_profile": '{"id": "test-user-123", "name": "Test User"}'
            }
        }
    )


@pytest.mark.asyncio
async def test_mock_workflow_initialization():
    """Test that the MockWorkflow initializes correctly."""
    # Create a mock workflow with default parameters
    workflow = MockWorkflow()

    # Verify default values
    assert workflow.workflow_type == "test_workflow"
    assert workflow.stages == ["init", "process", "complete"]
    assert "get_user_profile" in workflow.tool_calls
    assert workflow.input_tokens == 100
    assert workflow.output_tokens == 50
    assert workflow.success_rate == 1.0
    assert workflow.error_stages == []
    assert "response" in workflow.output_data

    # Create a mock workflow with custom parameters
    custom_workflow = MockWorkflow(
        workflow_type="custom_workflow",
        stages=["start", "middle", "end"],
        stage_durations={"start": 0.1, "middle": 0.2, "end": 0.1},
        tool_calls={"custom_tool": 3},
        input_tokens=200,
        output_tokens=100,
        success_rate=0.8,
        error_stages=["middle"],
        output_data={"custom_response": "Custom output"},
        delay_seconds=0.2
    )

    # Verify custom values
    assert custom_workflow.workflow_type == "custom_workflow"
    assert custom_workflow.stages == ["start", "middle", "end"]
    assert custom_workflow.stage_durations == {"start": 0.1, "middle": 0.2, "end": 0.1}
    assert custom_workflow.tool_calls == {"custom_tool": 3}
    assert custom_workflow.input_tokens == 200
    assert custom_workflow.output_tokens == 100
    assert custom_workflow.success_rate == 0.8
    assert custom_workflow.error_stages == ["middle"]
    assert custom_workflow.output_data == {"custom_response": "Custom output"}
    assert custom_workflow.delay_seconds == 0.2


@pytest.mark.asyncio
async def test_mock_workflow_run_benchmark(workflow_scenario, mock_tool_registry):
    """Test that the MockWorkflow.run_benchmark method works correctly."""
    # Create a mock workflow
    workflow = MockWorkflow()

    # Create a mock progress callback
    progress_callback = AsyncMock()

    # Run the benchmark
    result = await workflow.run_benchmark(
        workflow_scenario,
        benchmark_params={"runs": 2, "warmup_runs": 1},
        progress_callback=progress_callback
    )

    # Verify the result
    assert result is not None
    assert result.scenario == workflow_scenario
    # Check that the parameters contain the expected values
    assert result.parameters.get("runs") == 2
    assert result.parameters.get("warmup_runs") == 1
    assert result.mean_duration > 0
    assert result.median_duration > 0
    assert result.min_duration > 0
    assert result.max_duration > 0
    # Success rate should be a fraction between 0 and 1
    assert 0 <= result.success_rate <= 1.0
    assert result.tool_calls > 0
    assert result.total_input_tokens == 100
    assert result.total_output_tokens == 50

    # Verify that the progress callback was called
    assert progress_callback.await_count > 0


@pytest.mark.asyncio
async def test_mock_workflow_simulate_error(workflow_scenario, mock_tool_registry):
    """Test that the MockWorkflow can simulate errors in specific stages."""
    # Create a mock workflow with an error in the "process" stage
    workflow = MockWorkflow(error_stages=["process"])

    # Run the benchmark and expect an error
    with pytest.raises(ValueError, match="Simulated error in stage process"):
        await workflow._simulate_workflow_run(mock_tool_registry)


@pytest.mark.asyncio
async def test_test_workflow_run_benchmark(workflow_scenario, mock_tool_registry):
    """Test that the TestWorkflow.run_benchmark method works correctly."""
    # Create a test workflow
    workflow = TestWorkflow()

    # Create a mock progress callback
    progress_callback = AsyncMock()

    # Run the benchmark
    result = await workflow.run_benchmark(
        workflow_scenario,
        benchmark_params={"runs": 2, "warmup_runs": 1},
        progress_callback=progress_callback
    )

    # Verify the result
    assert result is not None
    assert result.scenario == workflow_scenario
    # Check that the parameters contain the expected values
    assert result.parameters.get("runs") == 2
    assert result.parameters.get("warmup_runs") == 1
    # The durations are stored in milliseconds in the database
    assert result.mean_duration == 1000.0
    assert result.median_duration == 1000.0
    assert result.min_duration == 900.0
    assert result.max_duration == 1100.0
    # The std_dev is stored in milliseconds in the database
    assert result.std_dev == 100.0
    assert result.success_rate == 1.0
    assert "get_user_profile" in result.tool_breakdown
    assert result.total_input_tokens == 100
    assert result.total_output_tokens == 50
    # The response is in the last_output field of raw_results
    assert "last_output" in result.raw_results
    assert "response" in result.raw_results["last_output"]
    assert result.raw_results["last_output"]["response"] == "This is a mock response from the test workflow."

    # Verify that the progress callback was called
    assert progress_callback.await_count > 0


@pytest.mark.asyncio
async def test_mock_workflow_with_custom_tool_calls(workflow_scenario):
    """Test that the MockWorkflow can use custom tool calls."""
    # Create a mock tool registry with custom responses
    mock_tools = MockToolRegistry()
    mock_tools.register_mock_response(
        "get_user_profile",
        '{"id": "test-user-123", "name": "Custom User"}'
    )

    # Create a mock workflow with custom tool calls
    workflow = MockWorkflow(
        tool_calls={"get_user_profile": 3, "custom_tool": 2}
    )

    # Run a workflow run
    duration, success = await workflow._simulate_workflow_run(mock_tools)

    # Verify the result
    assert duration > 0
    assert success is True

    # Verify that the tool was called the expected number of times
    assert mock_tools.get_call_count("get_user_profile") == 3

    # Verify that the token tracker recorded tokens
    assert workflow.token_tracker.input_tokens > 0
    assert workflow.token_tracker.output_tokens > 0
