"""
Tests for situation validation.

This module contains tests for validating situations against schemas.
"""

import pytest

from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_validator_service import SchemaValidationService


@pytest.fixture
def schema_registry():
    """Fixture to provide a SchemaRegistry instance."""
    return SchemaRegistry()


@pytest.fixture
def schema_validator(schema_registry):
    """Fixture to provide a SchemaValidationService instance."""
    return SchemaValidationService(schema_registry=schema_registry)


def test_validate_situation(schema_validator):
    """Test validation of a situation."""
    # Create a valid situation
    situation = {
        "workflow_type": "goal_setting",
        "text": "This is a test situation for workflow benchmarking"
    }
    
    # Validate the situation
    is_valid, errors = schema_validator.validate_situation(situation)
    
    # Verify that validation succeeds
    assert is_valid, f"Validation failed: {errors}"
    assert not errors, f"Unexpected errors: {errors}"


def test_validate_invalid_situation(schema_validator):
    """Test validation of an invalid situation."""
    # Create an invalid situation (missing required text)
    situation = {
        "workflow_type": "goal_setting"
        # Missing text field
    }
    
    # Validate the situation
    is_valid, errors = schema_validator.validate_situation(situation)
    
    # Verify that validation fails
    assert not is_valid
    assert errors, "Expected validation errors but got none"
    assert any("text" in str(error).lower() for error in errors), "Expected error about missing text"
