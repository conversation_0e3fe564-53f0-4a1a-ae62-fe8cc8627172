"""
Tests for schema validator service.

This module contains tests for the SchemaValidationService class.
"""

import pytest
from unittest.mock import patch

from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_validator_service import SchemaValidationService


@pytest.fixture
def schema_registry():
    """Fixture to provide a SchemaRegistry instance."""
    return SchemaRegistry()


@pytest.fixture
def schema_validator(schema_registry):
    """Fixture to provide a SchemaValidationService instance."""
    return SchemaValidationService(schema_registry=schema_registry)


def test_validate_benchmark_scenario_with_mocked_validation(schema_validator):
    """Test validation of a benchmark scenario with mocked validation methods."""
    # Create a valid benchmark scenario
    scenario = {
        'name': 'test_workflow_benchmark',
        'description': 'Test workflow benchmark scenario',
        'agent_role': 'workflow',
        'input_data': {
            'user_profile_id': 'test-user-123',
            'context_packet': {
                'workflow_type': 'goal_setting',
                'text': 'Test message for workflow benchmarking'
            }
        },
        'metadata': {
            'workflow_type': 'goal_setting',
            'situation': {
                'workflow_type': 'goal_setting',
                'text': 'This is a test situation for workflow benchmarking'
            },
            'expected_quality_criteria': {
                'criteria': [
                    {
                        'name': 'Completeness',
                        'description': 'Is the workflow output complete?',
                        'weight': 0.5
                    }
                ]
            },
            'mock_tool_responses': {
                'get_user_profile': {
                    'response': '{"id": "test-user-123", "name": "Test User"}'
                }
            },
            'warmup_runs': 1,
            'benchmark_runs': 2
        }
    }

    # Mock the validation methods to always return valid
    with patch.object(schema_validator, 'validate_situation', return_value=(True, [])) as mock_validate_situation, \
         patch.object(schema_validator, 'validate_evaluation_criteria', return_value=(True, [])) as mock_validate_evaluation_criteria, \
         patch.object(schema_validator, 'validate_tool_expectation', return_value=(True, [])) as mock_validate_tool_expectation, \
         patch.object(schema_validator, 'validate_workflow_benchmark', return_value=(True, [])) as mock_validate_workflow_benchmark:

        # Validate the scenario
        validation_result = schema_validator.validate_benchmark_scenario(scenario)

        # Verify that validation succeeds
        assert validation_result['valid'], f"Validation failed: {validation_result['errors']}"

        # Verify that all components are valid
        for component, result in validation_result['components'].items():
            assert result['valid'], f"Component {component} validation failed: {result['errors']}"

        # Verify that the validation methods were called with the correct arguments
        mock_validate_situation.assert_called_once()
        mock_validate_evaluation_criteria.assert_called_once()
        mock_validate_tool_expectation.assert_called_once()
        mock_validate_workflow_benchmark.assert_called_once()


def test_validate_benchmark_scenario_with_invalid_component(schema_validator):
    """Test validation of a benchmark scenario with an invalid component."""
    # Create a benchmark scenario with an invalid component
    scenario = {
        'name': 'test_workflow_benchmark',
        'description': 'Test workflow benchmark scenario',
        'agent_role': 'workflow',
        'input_data': {
            'user_profile_id': 'test-user-123',
            'context_packet': {
                'workflow_type': 'goal_setting',
                'text': 'Test message for workflow benchmarking'
            }
        },
        'metadata': {
            'workflow_type': 'goal_setting',
            'situation': {
                'workflow_type': 'goal_setting',
                'text': 'This is a test situation for workflow benchmarking'
            },
            'expected_quality_criteria': {
                'criteria': [
                    {
                        'name': 'Completeness',
                        'description': 'Is the workflow output complete?',
                        'weight': 0.5
                    }
                ]
            },
            'mock_tool_responses': {
                'get_user_profile': {
                    'response': '{"id": "test-user-123", "name": "Test User"}'
                }
            },
            'warmup_runs': 1,
            'benchmark_runs': 2
        }
    }

    # Mock the validation methods to return invalid for tool expectation
    with patch.object(schema_validator, 'validate_situation', return_value=(True, [])) as mock_validate_situation, \
         patch.object(schema_validator, 'validate_evaluation_criteria', return_value=(True, [])) as mock_validate_evaluation_criteria, \
         patch.object(schema_validator, 'validate_tool_expectation', return_value=(False, ['Invalid tool expectation'])) as mock_validate_tool_expectation, \
         patch.object(schema_validator, 'validate_workflow_benchmark', return_value=(True, [])) as mock_validate_workflow_benchmark:

        # Validate the scenario
        validation_result = schema_validator.validate_benchmark_scenario(scenario)

        # Verify that validation fails
        assert not validation_result['valid']
        assert 'Tool expectation: Invalid tool expectation' in validation_result['errors']

        # Verify that the tool_expectation component is invalid
        assert not validation_result['components']['tool_expectation']['valid']
        assert validation_result['components']['tool_expectation']['errors'] == ['Invalid tool expectation']

        # Verify that the validation methods were called with the correct arguments
        mock_validate_situation.assert_called_once()
        mock_validate_evaluation_criteria.assert_called_once()
        mock_validate_tool_expectation.assert_called_once()
        mock_validate_workflow_benchmark.assert_called_once()
