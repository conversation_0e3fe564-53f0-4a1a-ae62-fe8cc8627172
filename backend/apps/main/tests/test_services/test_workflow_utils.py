"""
Tests for the workflow benchmark utilities.

This module contains tests for the utility functions in workflow_utils.py.
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch

from apps.main.tests.workflow_utils import (
    create_test_workflow_scenario,
    create_test_workflow_scenario_async,
    create_mock_tool_config,
    create_semantic_evaluation_test_data,
    create_workflow_benchmark_result_data
)


@pytest.mark.django_db
def test_create_test_workflow_scenario():
    """Test that create_test_workflow_scenario creates a valid workflow scenario."""
    # Create a workflow scenario with default parameters
    scenario = create_test_workflow_scenario()
    
    # Verify the scenario
    assert scenario is not None
    assert scenario.agent_role == "workflow"
    assert "user_profile_id" in scenario.input_data
    assert "context_packet" in scenario.input_data
    assert "workflow_type" in scenario.metadata
    assert scenario.metadata["workflow_type"] == "test_workflow"
    assert "expected_quality_criteria" in scenario.metadata
    assert "mock_tool_responses" in scenario.metadata
    assert "get_user_profile" in scenario.metadata["mock_tool_responses"]
    
    # Create a workflow scenario with custom parameters
    custom_scenario = create_test_workflow_scenario(
        workflow_type="custom_workflow",
        input_data={"custom_key": "custom_value"},
        metadata={"custom_metadata": "custom_value"}
    )
    
    # Verify the custom scenario
    assert custom_scenario is not None
    assert custom_scenario.agent_role == "workflow"
    assert "custom_key" in custom_scenario.input_data
    assert "custom_metadata" in custom_scenario.metadata


@pytest.mark.django_db
@pytest.mark.asyncio
async def test_create_test_workflow_scenario_async():
    """Test that create_test_workflow_scenario_async creates a valid workflow scenario."""
    # Create a workflow scenario with default parameters
    scenario = await create_test_workflow_scenario_async()
    
    # Verify the scenario
    assert scenario is not None
    assert scenario.agent_role == "workflow"
    assert "user_profile_id" in scenario.input_data
    assert "context_packet" in scenario.input_data
    assert "workflow_type" in scenario.metadata
    assert scenario.metadata["workflow_type"] == "test_workflow"
    assert "expected_quality_criteria" in scenario.metadata
    assert "mock_tool_responses" in scenario.metadata
    assert "get_user_profile" in scenario.metadata["mock_tool_responses"]
    
    # Create a workflow scenario with custom parameters
    custom_scenario = await create_test_workflow_scenario_async(
        workflow_type="custom_workflow",
        input_data={"custom_key": "custom_value"},
        metadata={"custom_metadata": "custom_value"}
    )
    
    # Verify the custom scenario
    assert custom_scenario is not None
    assert custom_scenario.agent_role == "workflow"
    assert "custom_key" in custom_scenario.input_data
    assert "custom_metadata" in custom_scenario.metadata


def test_create_mock_tool_config():
    """Test that create_mock_tool_config creates a valid mock tool configuration."""
    # Create a mock tool config with default parameters
    config = create_mock_tool_config()
    
    # Verify the config
    assert config is not None
    assert "mock_tool_responses" in config
    assert "mock_tool_errors" in config
    assert "mock_tool_conditional_responses" in config
    
    # Create a mock tool config with custom parameters
    tool_responses = {
        "get_user_profile": {"id": "test-user", "name": "Test User"},
        "search_database": "Database result"
    }
    tool_errors = {
        "error_tool": "This is an error"
    }
    conditional_responses = {
        "conditional_tool": [
            {
                "condition": {"param1": "value1"},
                "response": {"result": "Response 1"}
            },
            {
                "condition": {"param1": "value2"},
                "response": {"result": "Response 2"}
            }
        ]
    }
    
    custom_config = create_mock_tool_config(
        tool_responses=tool_responses,
        tool_errors=tool_errors,
        conditional_responses=conditional_responses
    )
    
    # Verify the custom config
    assert custom_config is not None
    assert "get_user_profile" in custom_config["mock_tool_responses"]
    assert "search_database" in custom_config["mock_tool_responses"]
    assert "error_tool" in custom_config["mock_tool_errors"]
    assert "conditional_tool" in custom_config["mock_tool_conditional_responses"]
    
    # Verify that the tool responses are properly formatted
    get_user_profile_response = custom_config["mock_tool_responses"]["get_user_profile"]
    assert isinstance(get_user_profile_response, str)
    assert json.loads(get_user_profile_response)["id"] == "test-user"
    
    # Verify that the tool errors are properly formatted
    error_tool_error = custom_config["mock_tool_errors"]["error_tool"]
    assert error_tool_error["error_type"] == "ValueError"
    assert error_tool_error["error_message"] == "This is an error"
    
    # Verify that the conditional responses are properly formatted
    conditional_tool_responses = custom_config["mock_tool_conditional_responses"]["conditional_tool"]
    assert len(conditional_tool_responses) == 2
    assert conditional_tool_responses[0]["condition"]["param1"] == "value1"
    assert conditional_tool_responses[1]["response"]["result"] == "Response 2"


def test_create_semantic_evaluation_test_data():
    """Test that create_semantic_evaluation_test_data creates valid semantic evaluation test data."""
    # Create semantic evaluation test data with default parameters
    data = create_semantic_evaluation_test_data()
    
    # Verify the data
    assert data is not None
    assert "test-model" in data
    assert data["test-model"]["overall_score"] == 0.85
    assert "dimensions" in data["test-model"]
    assert "Clarity" in data["test-model"]["dimensions"]
    assert "Helpfulness" in data["test-model"]["dimensions"]
    assert "_meta" in data
    assert data["_meta"]["primary_model"] == "test-model"
    
    # Create semantic evaluation test data with custom parameters
    custom_dimensions = {
        "CustomDimension1": {
            "score": 0.7,
            "reasoning": "Custom reasoning 1"
        },
        "CustomDimension2": {
            "score": 0.6,
            "reasoning": "Custom reasoning 2"
        }
    }
    
    custom_data = create_semantic_evaluation_test_data(
        overall_score=0.65,
        dimensions=custom_dimensions,
        evaluator_model="custom-model",
        errors=["Error 1", "Error 2"]
    )
    
    # Verify the custom data
    assert custom_data is not None
    assert "custom-model" in custom_data
    assert custom_data["custom-model"]["overall_score"] == 0.65
    assert "dimensions" in custom_data["custom-model"]
    assert "CustomDimension1" in custom_data["custom-model"]["dimensions"]
    assert "CustomDimension2" in custom_data["custom-model"]["dimensions"]
    assert "_meta" in custom_data
    assert custom_data["_meta"]["primary_model"] == "custom-model"
    assert len(custom_data["_meta"]["errors"]) == 2


def test_create_workflow_benchmark_result_data():
    """Test that create_workflow_benchmark_result_data creates valid workflow benchmark result data."""
    # Create workflow benchmark result data with default parameters
    data = create_workflow_benchmark_result_data()
    
    # Verify the data
    assert data is not None
    assert data["workflow_type"] == "test_workflow"
    assert data["scenario_name"] == "Test Workflow Scenario"
    assert data["mean_duration"] == 1.0
    assert data["median_duration"] == 1.0
    assert data["min_duration"] == 0.9
    assert data["max_duration"] == 1.1
    assert data["std_dev"] == 0.1
    assert data["success_rate"] == 1.0
    assert "get_user_profile" in data["tool_call_counts"]
    assert data["total_input_tokens"] == 100
    assert data["total_output_tokens"] == 50
    assert "response" in data["last_output_data"]
    assert "init" in data["stage_durations"]
    assert "process" in data["stage_durations"]
    assert "complete" in data["stage_durations"]
    assert len(data["errors"]) == 0
    
    # Create workflow benchmark result data with custom parameters
    custom_tool_call_counts = {"custom_tool": 5}
    custom_last_output_data = {"custom_key": "custom_value"}
    custom_stage_durations = {"stage1": 0.5, "stage2": 1.5}
    
    custom_data = create_workflow_benchmark_result_data(
        workflow_type="custom_workflow",
        scenario_name="Custom Scenario",
        mean_duration=2.0,
        median_duration=1.9,
        min_duration=1.8,
        max_duration=2.2,
        std_dev=0.2,
        success_rate=0.9,
        tool_call_counts=custom_tool_call_counts,
        total_input_tokens=200,
        total_output_tokens=100,
        last_output_data=custom_last_output_data,
        stage_durations=custom_stage_durations,
        errors=["Error 1", "Error 2"]
    )
    
    # Verify the custom data
    assert custom_data is not None
    assert custom_data["workflow_type"] == "custom_workflow"
    assert custom_data["scenario_name"] == "Custom Scenario"
    assert custom_data["mean_duration"] == 2.0
    assert custom_data["median_duration"] == 1.9
    assert custom_data["min_duration"] == 1.8
    assert custom_data["max_duration"] == 2.2
    assert custom_data["std_dev"] == 0.2
    assert custom_data["success_rate"] == 0.9
    assert "custom_tool" in custom_data["tool_call_counts"]
    assert custom_data["total_input_tokens"] == 200
    assert custom_data["total_output_tokens"] == 100
    assert "custom_key" in custom_data["last_output_data"]
    assert "stage1" in custom_data["stage_durations"]
    assert "stage2" in custom_data["stage_durations"]
    assert len(custom_data["errors"]) == 2
