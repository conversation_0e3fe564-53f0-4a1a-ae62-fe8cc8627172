"""
Tests for schema validation edge cases.

This module contains tests for schema validation with invalid schemas and data.
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch

from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_validator_service import SchemaValidationService
from apps.main.services.schema_version_manager import SchemaVersionManager


@pytest.fixture
def schema_registry():
    """Fixture to provide a SchemaRegistry instance."""
    return SchemaRegistry()


@pytest.fixture
def schema_validator(schema_registry):
    """Fixture to provide a SchemaValidationService instance."""
    return SchemaValidationService(registry=schema_registry)


@pytest.fixture
def schema_version_manager(schema_registry):
    """Fixture to provide a SchemaVersionManager instance."""
    return SchemaVersionManager(registry=schema_registry)


def test_validate_with_invalid_schema(schema_registry):
    """Test that validation with an invalid schema fails gracefully."""
    # Register an invalid schema
    invalid_schema = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "InvalidSchema",
        "type": "not_a_valid_type"  # Invalid type
    }

    # Register the schema
    schema_registry.register_schema("invalid_schema", invalid_schema)

    # Validate data against the schema
    is_valid, errors = schema_registry.validate("invalid_schema", {"test": "data"})

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0


def test_validate_with_missing_schema(schema_registry):
    """Test that validation with a missing schema fails gracefully."""
    # Validate data against a non-existent schema
    is_valid, errors = schema_registry.validate("non_existent_schema", {"test": "data"})

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0
    assert "not registered" in errors[0]


def test_validate_with_invalid_data(schema_registry):
    """Test that validation with invalid data fails gracefully."""
    # Register a valid schema
    valid_schema = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "TestSchema",
        "type": "object",
        "required": ["required_field"],
        "properties": {
            "required_field": {"type": "string"},
            "optional_field": {"type": "number"}
        }
    }

    # Register the schema
    schema_registry.register_schema("test_schema", valid_schema)

    # Validate invalid data against the schema (missing required field)
    is_valid, errors = schema_registry.validate("test_schema", {"optional_field": 123})

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0
    assert "required_field" in errors[0]

    # Validate invalid data against the schema (wrong type)
    is_valid, errors = schema_registry.validate("test_schema", {"required_field": 123})

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0
    assert "type" in errors[0]


def test_validate_with_null_data(schema_registry):
    """Test that validation with null data fails gracefully."""
    # Register a valid schema
    valid_schema = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "TestSchema",
        "type": "object",
        "properties": {
            "field": {"type": "string"}
        }
    }

    # Register the schema
    schema_registry.register_schema("test_schema", valid_schema)

    # Validate null data against the schema
    is_valid, errors = schema_registry.validate("test_schema", None)

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0


def test_validate_with_empty_data(schema_registry):
    """Test that validation with empty data fails gracefully if required fields are missing."""
    # Register a valid schema with required fields
    schema_with_required = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "TestSchema",
        "type": "object",
        "required": ["required_field"],
        "properties": {
            "required_field": {"type": "string"}
        }
    }

    # Register the schema
    schema_registry.register_schema("schema_with_required", schema_with_required)

    # Validate empty data against the schema
    is_valid, errors = schema_registry.validate("schema_with_required", {})

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0
    assert "required_field" in errors[0]

    # Register a valid schema without required fields
    schema_without_required = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "TestSchema",
        "type": "object",
        "properties": {
            "optional_field": {"type": "string"}
        }
    }

    # Register the schema
    schema_registry.register_schema("schema_without_required", schema_without_required)

    # Validate empty data against the schema
    is_valid, errors = schema_registry.validate("schema_without_required", {})

    # Verify that validation succeeds
    assert is_valid is True
    assert len(errors) == 0


def test_validate_with_additional_properties(schema_registry):
    """Test that validation with additional properties fails if additionalProperties is false."""
    # Register a schema that disallows additional properties
    schema_no_additional = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "TestSchema",
        "type": "object",
        "additionalProperties": False,
        "properties": {
            "allowed_field": {"type": "string"}
        }
    }

    # Register the schema
    schema_registry.register_schema("schema_no_additional", schema_no_additional)

    # Validate data with additional properties against the schema
    is_valid, errors = schema_registry.validate("schema_no_additional", {
        "allowed_field": "value",
        "additional_field": "value"
    })

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0
    assert "additional_field" in errors[0]

    # Register a schema that allows additional properties
    schema_with_additional = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "TestSchema",
        "type": "object",
        "additionalProperties": True,
        "properties": {
            "allowed_field": {"type": "string"}
        }
    }

    # Register the schema
    schema_registry.register_schema("schema_with_additional", schema_with_additional)

    # Validate data with additional properties against the schema
    is_valid, errors = schema_registry.validate("schema_with_additional", {
        "allowed_field": "value",
        "additional_field": "value"
    })

    # Verify that validation succeeds
    assert is_valid is True
    assert len(errors) == 0


def test_validate_with_pattern_properties(schema_registry):
    """Test that validation with pattern properties works correctly."""
    # Register a schema with pattern properties
    schema_with_pattern = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "TestSchema",
        "type": "object",
        "patternProperties": {
            "^prefix_": {"type": "string"}
        },
        "additionalProperties": False
    }

    # Register the schema
    schema_registry.register_schema("schema_with_pattern", schema_with_pattern)

    # Validate data with matching pattern properties
    is_valid, errors = schema_registry.validate("schema_with_pattern", {
        "prefix_field1": "value1",
        "prefix_field2": "value2"
    })

    # Verify that validation succeeds
    assert is_valid is True
    assert len(errors) == 0

    # Validate data with non-matching properties
    is_valid, errors = schema_registry.validate("schema_with_pattern", {
        "prefix_field": "value",
        "non_prefix_field": "value"
    })

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0
    assert "non_prefix_field" in errors[0]


def test_validate_with_nested_objects(schema_registry):
    """Test that validation with nested objects works correctly."""
    # Register a schema with nested objects
    schema_with_nested = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "TestSchema",
        "type": "object",
        "properties": {
            "nested_object": {
                "type": "object",
                "required": ["required_nested_field"],
                "properties": {
                    "required_nested_field": {"type": "string"},
                    "optional_nested_field": {"type": "number"}
                }
            }
        }
    }

    # Register the schema
    schema_registry.register_schema("schema_with_nested", schema_with_nested)

    # Validate data with valid nested object
    is_valid, errors = schema_registry.validate("schema_with_nested", {
        "nested_object": {
            "required_nested_field": "value",
            "optional_nested_field": 123
        }
    })

    # Verify that validation succeeds
    assert is_valid is True
    assert len(errors) == 0

    # Validate data with invalid nested object (missing required field)
    is_valid, errors = schema_registry.validate("schema_with_nested", {
        "nested_object": {
            "optional_nested_field": 123
        }
    })

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0
    assert "required_nested_field" in errors[0]

    # Validate data with invalid nested object (wrong type)
    is_valid, errors = schema_registry.validate("schema_with_nested", {
        "nested_object": {
            "required_nested_field": "value",
            "optional_nested_field": "not_a_number"
        }
    })

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0
    assert "optional_nested_field" in errors[0]


def test_validate_with_array_items(schema_registry):
    """Test that validation with array items works correctly."""
    # Register a schema with array items
    schema_with_array = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "TestSchema",
        "type": "object",
        "properties": {
            "array_field": {
                "type": "array",
                "items": {
                    "type": "object",
                    "required": ["item_field"],
                    "properties": {
                        "item_field": {"type": "string"}
                    }
                }
            }
        }
    }

    # Register the schema
    schema_registry.register_schema("schema_with_array", schema_with_array)

    # Validate data with valid array items
    is_valid, errors = schema_registry.validate("schema_with_array", {
        "array_field": [
            {"item_field": "value1"},
            {"item_field": "value2"}
        ]
    })

    # Verify that validation succeeds
    assert is_valid is True
    assert len(errors) == 0

    # Validate data with invalid array items (missing required field)
    is_valid, errors = schema_registry.validate("schema_with_array", {
        "array_field": [
            {"item_field": "value1"},
            {"wrong_field": "value2"}
        ]
    })

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0
    assert "item_field" in errors[0]

    # Validate data with invalid array items (wrong type)
    is_valid, errors = schema_registry.validate("schema_with_array", {
        "array_field": [
            {"item_field": "value1"},
            "not_an_object"
        ]
    })

    # Verify that validation fails
    assert is_valid is False
    assert len(errors) > 0


@pytest.mark.asyncio
async def test_schema_version_manager_with_invalid_version(schema_version_manager):
    """Test that SchemaVersionManager handles invalid versions gracefully."""
    # Try to register a schema with an invalid version
    with pytest.raises(ValueError):
        schema_version_manager.register_schema_version(
            "test_schema",
            "not_a_valid_version",
            {"type": "object"}
        )


@pytest.mark.asyncio
async def test_schema_version_manager_with_missing_migration(schema_version_manager):
    """Test that SchemaVersionManager handles missing migrations gracefully."""
    # Register two schema versions
    schema_version_manager.register_schema_version(
        "test_schema",
        "1.0.0",
        {"type": "object", "properties": {"field1": {"type": "string"}}}
    )

    schema_version_manager.register_schema_version(
        "test_schema",
        "2.0.0",
        {"type": "object", "properties": {"field2": {"type": "string"}}}
    )

    # Try to migrate data without a registered migration
    with pytest.raises(ValueError):
        await schema_version_manager.migrate_data(
            "test_schema",
            {"field1": "value1"},
            "1.0.0",
            "2.0.0"
        )


@pytest.mark.asyncio
async def test_schema_version_manager_with_circular_migration_path(schema_version_manager):
    """Test that SchemaVersionManager handles circular migration paths gracefully."""
    # Register three schema versions
    schema_version_manager.register_schema_version(
        "test_schema",
        "1.0.0",
        {"type": "object", "properties": {"field1": {"type": "string"}}}
    )

    schema_version_manager.register_schema_version(
        "test_schema",
        "2.0.0",
        {"type": "object", "properties": {"field2": {"type": "string"}}}
    )

    schema_version_manager.register_schema_version(
        "test_schema",
        "3.0.0",
        {"type": "object", "properties": {"field3": {"type": "string"}}}
    )

    # Register migrations that create a circular path
    def migrate_1_to_2(data):
        return {"field2": data.get("field1", "")}

    def migrate_2_to_3(data):
        return {"field3": data.get("field2", "")}

    def migrate_3_to_1(data):
        return {"field1": data.get("field3", "")}

    schema_version_manager.register_migration(
        "test_schema", "1.0.0", "2.0.0", migrate_1_to_2
    )

    schema_version_manager.register_migration(
        "test_schema", "2.0.0", "3.0.0", migrate_2_to_3
    )

    schema_version_manager.register_migration(
        "test_schema", "3.0.0", "1.0.0", migrate_3_to_1
    )

    # Migrate data through the path 1.0.0 -> 2.0.0 -> 3.0.0
    # The current implementation doesn't detect circular paths, but it should
    # still correctly apply the migrations in sequence
    result = schema_version_manager.migrate_data(
        "test_schema",
        {"field1": "value1"},
        "1.0.0",
        "3.0.0"
    )

    # Verify that the migrations were applied correctly
    assert "field3" in result
    assert result["field3"] == "value1"  # Value should be passed through the chain
