"""
Tests for workflow benchmark validation.

This module contains tests for validating workflow benchmark scenarios against schemas.
"""

import pytest

from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_validator_service import SchemaValidationService


@pytest.fixture
def schema_registry():
    """Fixture to provide a SchemaRegistry instance."""
    return SchemaRegistry()


@pytest.fixture
def schema_validator(schema_registry):
    """Fixture to provide a SchemaValidationService instance."""
    return SchemaValidationService(schema_registry=schema_registry)


def test_validate_workflow_benchmark_directly(schema_validator):
    """Test direct validation of a workflow benchmark."""
    # Create a valid workflow benchmark
    workflow_benchmark = {
        "workflow_type": "goal_setting",
        "mock_tool_responses": {
            "get_user_profile": {
                "response": '{"id": "test-user-123", "name": "Test User"}'
            },
            "get_user_activities": {
                "response": '{"activities": [{"id": "act1", "name": "Activity 1"}]}'
            }
        },
        "warmup_runs": 1,
        "benchmark_runs": 2
    }
    
    # Validate the workflow benchmark directly
    is_valid, errors = schema_validator.validate_workflow_benchmark(workflow_benchmark)
    
    # Verify that validation succeeds
    assert is_valid, f"Validation failed: {errors}"
    assert not errors, f"Unexpected errors: {errors}"


def test_validate_tool_expectation_directly(schema_validator):
    """Test direct validation of tool expectations."""
    # Create valid tool expectations
    tool_expectation = {
        "mock_responses": {
            "get_user_profile": '{"id": "test-user-123", "name": "Test User"}',
            "get_user_activities": '{"activities": [{"id": "act1", "name": "Activity 1"}]}'
        }
    }
    
    # Validate the tool expectation directly
    is_valid, errors = schema_validator.validate_tool_expectation(tool_expectation)
    
    # Verify that validation succeeds
    assert is_valid, f"Validation failed: {errors}"
    assert not errors, f"Unexpected errors: {errors}"
