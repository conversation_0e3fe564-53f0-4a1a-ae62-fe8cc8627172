"""
Tests for token tracking with various LLM responses.

This module contains tests for the TokenTracker class in the AsyncWorkflowManager.
"""

import pytest
import uuid
from unittest.mock import AsyncMock, patch

from apps.main.services.async_workflow_manager import TokenTracker


@pytest.fixture
def token_tracker():
    """Fixture to provide a TokenTracker instance."""
    # Create a mock run_id
    run_id = uuid.uuid4()
    # Mock the record_usage method to avoid database operations
    with patch.object(TokenTracker, 'record_usage', AsyncMock()):
        return TokenTracker(run_id=run_id)


def test_token_tracker_initialization(token_tracker):
    """Test that the TokenTracker initializes correctly."""
    # Verify initial values
    assert token_tracker.input_tokens == 0
    assert token_tracker.output_tokens == 0
    assert hasattr(token_tracker, 'run_id')


@pytest.mark.asyncio
async def test_record_usage(token_tracker):
    """Test that record_usage works correctly."""
    # Record token usage
    await token_tracker.record_usage(100, 50)

    # Verify the counts
    assert token_tracker.input_tokens == 100
    assert token_tracker.output_tokens == 50

    # Record more token usage
    await token_tracker.record_usage(50, 25)

    # Verify the updated counts
    assert token_tracker.input_tokens == 150
    assert token_tracker.output_tokens == 75


@pytest.mark.asyncio
async def test_calculate_cost(token_tracker):
    """Test that calculate_cost works correctly."""
    # Record token usage
    await token_tracker.record_usage(100, 50)

    # Calculate cost
    cost = await token_tracker.calculate_cost(0.001, 0.002)

    # Verify the cost
    expected_cost = (100 * 0.001) + (50 * 0.002)
    assert cost == pytest.approx(expected_cost)


@pytest.mark.asyncio
async def test_multiple_record_usage_calls(token_tracker):
    """Test multiple record_usage calls."""
    # Record token usage multiple times
    await token_tracker.record_usage(100, 50)
    await token_tracker.record_usage(50, 25)
    await token_tracker.record_usage(25, 10)

    # Verify the counts
    assert token_tracker.input_tokens == 175
    assert token_tracker.output_tokens == 85


@pytest.mark.asyncio
async def test_calculate_cost_with_zero_tokens(token_tracker):
    """Test that calculate_cost works correctly with zero tokens."""
    # Calculate cost with zero tokens
    cost = await token_tracker.calculate_cost(0.001, 0.002)

    # Verify the cost
    assert cost == 0.0


@pytest.mark.asyncio
async def test_run_id_is_required():
    """Test that run_id is required for TokenTracker initialization."""
    # Verify that TokenTracker requires run_id
    with pytest.raises(TypeError, match="missing 1 required positional argument: 'run_id'"):
        TokenTracker()


@pytest.mark.asyncio
async def test_token_tracker_with_uuid_string():
    """Test that TokenTracker works with a UUID string."""
    # Create a token tracker with a UUID object
    run_id = uuid.uuid4()

    # Mock the record_usage method to avoid database operations
    with patch.object(TokenTracker, 'record_usage', AsyncMock()):
        tracker = TokenTracker(run_id=run_id)

    # Verify that the run_id is a UUID
    assert isinstance(tracker.run_id, uuid.UUID)


@pytest.mark.asyncio
async def test_token_tracker_with_invalid_run_id_type():
    """Test that TokenTracker works with different run_id types."""
    # Mock the record_usage method to avoid database operations
    with patch.object(TokenTracker, 'record_usage', AsyncMock()):
        # Create a token tracker with an integer run_id
        # This should work as the implementation is flexible
        tracker = TokenTracker(run_id=12345)

        # Verify that the run_id is stored
        assert tracker.run_id == 12345
