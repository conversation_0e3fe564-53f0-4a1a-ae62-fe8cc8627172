"""
Tests for tool mocking with complex conditional responses.

This module contains tests for the MockToolRegistry class with conditional responses.
"""

import pytest
import json
from unittest.mock import AsyncMock  # Used in other tests

from apps.main.testing.mock_tool_registry import MockToolRegistry
from apps.main.tests.workflow_utils import create_mock_tool_config


@pytest.fixture
def mock_tool_registry():
    """Fixture to provide a MockToolRegistry instance."""
    return MockToolRegistry()


def test_register_mock_response(mock_tool_registry):
    """Test that register_mock_response works correctly."""
    # Register a simple response
    mock_tool_registry.register_mock_response("test_tool", "Test response")

    # Verify that the response was registered
    assert "test_tool" in mock_tool_registry.mock_responses
    assert mock_tool_registry.mock_responses["test_tool"] == "Test response"

    # Register a JSON response
    json_response = {"key": "value"}
    mock_tool_registry.register_mock_response("json_tool", json_response)

    # Verify that the JSON response was registered as a string
    assert "json_tool" in mock_tool_registry.mock_responses
    assert isinstance(mock_tool_registry.mock_responses["json_tool"], str)
    assert json.loads(mock_tool_registry.mock_responses["json_tool"]) == json_response


def test_register_mock_error(mock_tool_registry):
    """Test that register_mock_error works correctly."""
    # Register a simple error
    mock_tool_registry.register_mock_error("test_tool", "Test error")

    # Verify that the error was registered
    assert "test_tool" in mock_tool_registry.mock_errors
    assert mock_tool_registry.mock_errors["test_tool"]["error_message"] == "Test error"
    assert mock_tool_registry.mock_errors["test_tool"]["error_type"] == "ValueError"

    # Register an error with a custom type
    mock_tool_registry.register_mock_error("custom_error_tool", "Custom error", error_type="TypeError")

    # Verify that the custom error was registered
    assert "custom_error_tool" in mock_tool_registry.mock_errors
    assert mock_tool_registry.mock_errors["custom_error_tool"]["error_message"] == "Custom error"
    assert mock_tool_registry.mock_errors["custom_error_tool"]["error_type"] == "TypeError"


def test_register_conditional_response(mock_tool_registry):
    """Test that register_conditional_response works correctly."""
    # Register a simple conditional response
    condition = {"param1": "value1"}
    response = {"result": "Response 1"}
    mock_tool_registry.register_conditional_response("test_tool", condition, response)

    # Verify that the conditional response was registered
    assert "test_tool" in mock_tool_registry.conditional_responses
    assert len(mock_tool_registry.conditional_responses["test_tool"]) == 1
    assert mock_tool_registry.conditional_responses["test_tool"][0]["condition"] == condition
    assert isinstance(mock_tool_registry.conditional_responses["test_tool"][0]["response"], str)
    assert json.loads(mock_tool_registry.conditional_responses["test_tool"][0]["response"]) == response

    # Register another conditional response for the same tool
    condition2 = {"param1": "value2"}
    response2 = {"result": "Response 2"}
    mock_tool_registry.register_conditional_response("test_tool", condition2, response2)

    # Verify that both conditional responses are registered
    assert len(mock_tool_registry.conditional_responses["test_tool"]) == 2
    assert mock_tool_registry.conditional_responses["test_tool"][1]["condition"] == condition2
    assert json.loads(mock_tool_registry.conditional_responses["test_tool"][1]["response"]) == response2


@pytest.mark.asyncio
async def test_execute_tool_with_simple_response(mock_tool_registry):
    """Test that execute_tool works correctly with a simple response."""
    # Register a simple response
    mock_tool_registry.register_mock_response("test_tool", "Test response")

    # Execute the tool
    result = await mock_tool_registry.execute_tool("test_tool", {"param1": "value1"})

    # Verify the result
    assert result == "Test response"
    assert mock_tool_registry.get_call_count("test_tool") == 1
    assert mock_tool_registry.get_call_args("test_tool") == [{"param1": "value1"}]


@pytest.mark.asyncio
async def test_execute_tool_with_json_response(mock_tool_registry):
    """Test that execute_tool works correctly with a JSON response."""
    # Register a JSON response
    json_response = {"key": "value"}
    mock_tool_registry.register_mock_response("json_tool", json_response)

    # Execute the tool
    result = await mock_tool_registry.execute_tool("json_tool", {"param1": "value1"})

    # Verify the result - should be parsed JSON
    assert result == json_response
    assert mock_tool_registry.get_call_count("json_tool") == 1


@pytest.mark.asyncio
async def test_execute_tool_with_error(mock_tool_registry):
    """Test that execute_tool works correctly with an error."""
    # Register an error
    mock_tool_registry.register_mock_error("error_tool", "Test error")

    # Execute the tool and expect an error
    with pytest.raises(ValueError, match="Test error"):
        await mock_tool_registry.execute_tool("error_tool", {"param1": "value1"})

    # Verify that the call was recorded
    assert mock_tool_registry.get_call_count("error_tool") == 1


@pytest.mark.asyncio
async def test_execute_tool_with_conditional_response(mock_tool_registry):
    """Test that execute_tool works correctly with conditional responses."""
    # Register conditional responses
    mock_tool_registry.register_conditional_response(
        "conditional_tool",
        {"param1": "value1"},
        {"result": "Response 1"}
    )

    mock_tool_registry.register_conditional_response(
        "conditional_tool",
        {"param1": "value2"},
        {"result": "Response 2"}
    )

    # Register a default response
    mock_tool_registry.register_mock_response(
        "conditional_tool",
        {"result": "Default response"}
    )

    # Execute the tool with matching condition 1
    result1 = await mock_tool_registry.execute_tool("conditional_tool", {"param1": "value1"})

    # Verify the result
    assert result1["result"] == "Response 1"

    # Execute the tool with matching condition 2
    result2 = await mock_tool_registry.execute_tool("conditional_tool", {"param1": "value2"})

    # Verify the result
    assert result2["result"] == "Response 2"

    # Execute the tool with non-matching condition
    result3 = await mock_tool_registry.execute_tool("conditional_tool", {"param1": "value3"})

    # Verify that the default response was used
    assert result3["result"] == "Default response"

    # Verify that all calls were recorded
    assert mock_tool_registry.get_call_count("conditional_tool") == 3


@pytest.mark.asyncio
async def test_execute_tool_with_nested_conditional_response(mock_tool_registry):
    """Test that execute_tool works correctly with nested conditional responses."""
    # Register conditional responses with nested conditions
    mock_tool_registry.register_conditional_response(
        "nested_tool",
        {"user": {"id": "user1", "role": "admin"}},
        {"result": "Admin response"}
    )

    mock_tool_registry.register_conditional_response(
        "nested_tool",
        {"user": {"id": "user2", "role": "user"}},
        {"result": "User response"}
    )

    # Execute the tool with matching nested condition 1
    result1 = await mock_tool_registry.execute_tool("nested_tool", {"user": {"id": "user1", "role": "admin"}})

    # Verify the result
    assert result1["result"] == "Admin response"

    # Execute the tool with matching nested condition 2
    result2 = await mock_tool_registry.execute_tool("nested_tool", {"user": {"id": "user2", "role": "user"}})

    # Verify the result
    assert result2["result"] == "User response"

    # Execute the tool with partially matching nested condition
    result3 = await mock_tool_registry.execute_tool("nested_tool", {"user": {"id": "user1", "role": "user"}})

    # Verify that no match was found (should return an error response)
    assert "No mock response found for tool nested_tool" in result3["error"]
    assert result3["status"] == "error"


@pytest.mark.asyncio
async def test_execute_tool_with_partial_condition_match(mock_tool_registry):
    """Test that execute_tool works correctly with partial condition matches."""
    # Register conditional responses with multiple fields
    mock_tool_registry.register_conditional_response(
        "partial_tool",
        {"param1": "value1", "param2": "value2"},
        {"result": "Response 1"}
    )

    # Execute the tool with exact match
    result1 = await mock_tool_registry.execute_tool("partial_tool", {"param1": "value1", "param2": "value2"})

    # Verify the result
    assert result1["result"] == "Response 1"

    # Execute the tool with partial match (extra fields)
    result2 = await mock_tool_registry.execute_tool("partial_tool", {"param1": "value1", "param2": "value2", "param3": "value3"})

    # Verify the result (should still match)
    assert result2["result"] == "Response 1"

    # Execute the tool with partial match (missing fields)
    result3 = await mock_tool_registry.execute_tool("partial_tool", {"param1": "value1"})

    # Verify that no match was found (should return an error response)
    assert "No mock response found for tool partial_tool" in result3["error"]
    assert result3["status"] == "error"


@pytest.mark.asyncio
async def test_execute_tool_with_template_response(mock_tool_registry):
    """Test that execute_tool works correctly with template responses."""
    # Register a template response
    mock_tool_registry.register_mock_response(
        "template_tool",
        '{"result": "Response for {param1}"}'
    )

    # Execute the tool
    result = await mock_tool_registry.execute_tool("template_tool", {"param1": "test"})

    # Verify the result
    assert result["result"] == "Response for test"

    # Register a conditional template response
    mock_tool_registry.register_conditional_response(
        "conditional_template_tool",
        {"param1": "value1"},
        '{"result": "Response for {param1} with {param2}"}'
    )

    # Execute the tool
    result = await mock_tool_registry.execute_tool("conditional_template_tool", {"param1": "value1", "param2": "test"})

    # Verify the result
    assert result["result"] == "Response for value1 with test"


@pytest.mark.asyncio
async def test_execute_tool_with_complex_conditional_logic(mock_tool_registry):
    """Test that execute_tool works correctly with complex conditional logic."""
    # Register conditional responses with complex conditions
    mock_tool_registry.register_conditional_response(
        "complex_tool",
        {"operation": "add", "values": [1, 2]},
        {"result": 3}
    )

    mock_tool_registry.register_conditional_response(
        "complex_tool",
        {"operation": "multiply", "values": [2, 3]},
        {"result": 6}
    )

    # Execute the tool with matching condition 1
    result1 = await mock_tool_registry.execute_tool("complex_tool", {"operation": "add", "values": [1, 2]})

    # Verify the result
    assert result1["result"] == 3

    # Execute the tool with matching condition 2
    result2 = await mock_tool_registry.execute_tool("complex_tool", {"operation": "multiply", "values": [2, 3]})

    # Verify the result
    assert result2["result"] == 6

    # Execute the tool with non-matching condition
    result3 = await mock_tool_registry.execute_tool("complex_tool", {"operation": "subtract", "values": [5, 2]})

    # Verify that no match was found (should return an error response)
    assert "No mock response found for tool complex_tool" in result3["error"]
    assert result3["status"] == "error"


@pytest.mark.asyncio
async def test_execute_tool_with_conditional_error(mock_tool_registry):
    """Test that execute_tool works correctly with conditional errors."""
    # Register a conditional response
    mock_tool_registry.register_conditional_response(
        "conditional_error_tool",
        {"param1": "success"},
        {"result": "Success response"}
    )

    # Register a conditional error
    mock_tool_registry.register_conditional_response(
        "conditional_error_tool",
        {"param1": "error"},
        {"error": True, "error_type": "ValueError", "error_message": "Conditional error"}
    )

    # Execute the tool with success condition
    result = await mock_tool_registry.execute_tool("conditional_error_tool", {"param1": "success"})

    # Verify the result
    assert result["result"] == "Success response"

    # Execute the tool with error condition
    with pytest.raises(ValueError, match="Conditional error"):
        await mock_tool_registry.execute_tool("conditional_error_tool", {"param1": "error"})


@pytest.mark.asyncio
async def test_create_mock_tool_config_integration(mock_tool_registry):
    """Test that create_mock_tool_config integrates correctly with MockToolRegistry."""
    # Create a mock tool config
    tool_responses = {
        "response_tool": {"response": {"result": "Response"}},
        "template_tool": {"response": '{"result": "Response for {param1}"}'}
    }

    tool_errors = {
        "error_tool": "Test error"
    }

    conditional_responses = {
        "conditional_tool": [
            {
                "condition": {"param1": "value1"},
                "response": {"response": {"result": "Response 1"}}
            },
            {
                "condition": {"param1": "value2"},
                "response": {"response": {"result": "Response 2"}}
            }
        ]
    }

    config = create_mock_tool_config(
        tool_responses=tool_responses,
        tool_errors=tool_errors,
        conditional_responses=conditional_responses
    )

    # Initialize a MockToolRegistry with the config
    registry = MockToolRegistry(config=config)

    # Test response tool
    result1 = await registry.execute_tool("response_tool", {})
    assert isinstance(result1, dict), f"Expected dict, got {type(result1)}: {result1}"

    # Handle different response formats
    if "response" in result1:
        if isinstance(result1["response"], dict):
            # Nested dict
            response_data = result1["response"]
            assert "result" in response_data, f"Expected 'result' key in response: {response_data}"
            assert response_data["result"] == "Response"
        elif isinstance(result1["response"], str):
            # JSON string
            try:
                response_data = json.loads(result1["response"])
                assert "result" in response_data, f"Expected 'result' key in parsed response: {response_data}"
                assert response_data["result"] == "Response"
            except json.JSONDecodeError:
                assert False, f"Expected JSON string in response, got: {result1['response']}"
        else:
            assert False, f"Unexpected response type: {type(result1['response'])}"
    else:
        # Direct result
        assert "result" in result1, f"Expected 'result' key in {result1}"
        assert result1["result"] == "Response"

    # Test template tool
    result2 = await registry.execute_tool("template_tool", {"param1": "test"})
    assert isinstance(result2, dict), f"Expected dict, got {type(result2)}: {result2}"

    # Handle different response formats:
    # 1. Direct result: {"result": "value"}
    # 2. Nested dict: {"response": {"result": "value"}}
    # 3. JSON string: {"response": '{"result": "value"}'}
    if "response" in result2:
        if isinstance(result2["response"], dict):
            # Case 2: Nested dict
            response_data = result2["response"]
            assert "result" in response_data, f"Expected 'result' key in response: {response_data}"
            assert response_data["result"] == "Response for test"
        elif isinstance(result2["response"], str):
            # Case 3: JSON string
            try:
                response_data = json.loads(result2["response"])
                assert "result" in response_data, f"Expected 'result' key in parsed response: {response_data}"
                assert response_data["result"] == "Response for test"
            except json.JSONDecodeError:
                assert False, f"Expected JSON string in response, got: {result2['response']}"
        else:
            assert False, f"Unexpected response type: {type(result2['response'])}"
    else:
        # Case 1: Direct result
        assert "result" in result2, f"Expected 'result' key in {result2}"
        assert result2["result"] == "Response for test"

    # Test error tool
    with pytest.raises(ValueError, match="Test error"):
        await registry.execute_tool("error_tool", {})

    # Test conditional tool with condition 1
    result3 = await registry.execute_tool("conditional_tool", {"param1": "value1"})
    assert isinstance(result3, dict), f"Expected dict, got {type(result3)}: {result3}"

    # Handle different response formats
    if "response" in result3:
        if isinstance(result3["response"], dict):
            # Nested dict
            response_data = result3["response"]
            assert "result" in response_data, f"Expected 'result' key in response: {response_data}"
            assert response_data["result"] == "Response 1"
        elif isinstance(result3["response"], str):
            # JSON string
            try:
                response_data = json.loads(result3["response"])
                assert "result" in response_data, f"Expected 'result' key in parsed response: {response_data}"
                assert response_data["result"] == "Response 1"
            except json.JSONDecodeError:
                assert False, f"Expected JSON string in response, got: {result3['response']}"
        else:
            assert False, f"Unexpected response type: {type(result3['response'])}"
    else:
        # Direct result
        assert "result" in result3, f"Expected 'result' key in {result3}"
        assert result3["result"] == "Response 1"

    # Test conditional tool with condition 2
    result4 = await registry.execute_tool("conditional_tool", {"param1": "value2"})
    assert isinstance(result4, dict), f"Expected dict, got {type(result4)}: {result4}"

    # Handle different response formats
    if "response" in result4:
        if isinstance(result4["response"], dict):
            # Nested dict
            response_data = result4["response"]
            assert "result" in response_data, f"Expected 'result' key in response: {response_data}"
            assert response_data["result"] == "Response 2"
        elif isinstance(result4["response"], str):
            # JSON string
            try:
                response_data = json.loads(result4["response"])
                assert "result" in response_data, f"Expected 'result' key in parsed response: {response_data}"
                assert response_data["result"] == "Response 2"
            except json.JSONDecodeError:
                assert False, f"Expected JSON string in response, got: {result4['response']}"
        else:
            assert False, f"Unexpected response type: {type(result4['response'])}"
    else:
        # Direct result
        assert "result" in result4, f"Expected 'result' key in {result4}"
        assert result4["result"] == "Response 2"
