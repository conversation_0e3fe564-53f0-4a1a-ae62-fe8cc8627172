"""
Tests for enhanced token tracking with stage and model information.

This module contains tests for the enhanced TokenTracker class that supports
tracking tokens by stage and model.
"""

import pytest
import uuid
from unittest.mock import AsyncMock, patch

from apps.main.services.async_workflow_manager import TokenTracker


@pytest.fixture
def token_tracker():
    """Fixture to provide a TokenTracker instance."""
    # Create a mock run_id
    run_id = uuid.uuid4()
    # Mock the record_usage method to avoid database operations
    with patch.object(TokenTracker, 'record_usage', AsyncMock()):
        return TokenTracker(run_id=run_id)


def test_token_tracker_initialization(token_tracker):
    """Test that the TokenTracker initializes correctly."""
    # Verify initial values
    assert token_tracker.input_tokens == 0
    assert token_tracker.output_tokens == 0
    assert hasattr(token_tracker, 'run_id')
    # Verify that stage_tokens and model_tokens are initialized as empty dictionaries
    assert hasattr(token_tracker, 'stage_tokens')
    assert hasattr(token_tracker, 'model_tokens')


@pytest.mark.asyncio
async def test_record_usage_with_stage_and_model(token_tracker):
    """Test recording token usage with stage and model information."""
    # Mock the record_usage method to avoid database operations
    token_tracker.record_usage = AsyncMock()
    
    # Record token usage with stage and model
    await token_tracker.record_usage(100, 50, stage="process_input", model="openai/gpt-4")
    
    # Verify the method was called with the correct arguments
    token_tracker.record_usage.assert_awaited_once_with(100, 50, stage="process_input", model="openai/gpt-4")


@pytest.mark.asyncio
async def test_record_usage_updates_stage_tokens():
    """Test that record_usage updates stage_tokens correctly."""
    # Create a token tracker with a real implementation of record_usage
    run_id = uuid.uuid4()
    token_tracker = TokenTracker(run_id=run_id)
    
    # Mock the database update to avoid actual database operations
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function
        mock_sync_to_async.return_value = AsyncMock()
        
        # Record token usage with stage
        await token_tracker.record_usage(100, 50, stage="process_input")
        
        # Verify that stage_tokens was updated correctly
        assert token_tracker.stage_tokens["process_input"]["input"] == 100
        assert token_tracker.stage_tokens["process_input"]["output"] == 50
        
        # Record more token usage for the same stage
        await token_tracker.record_usage(200, 100, stage="process_input")
        
        # Verify that stage_tokens was updated correctly
        assert token_tracker.stage_tokens["process_input"]["input"] == 300
        assert token_tracker.stage_tokens["process_input"]["output"] == 150
        
        # Record token usage for a different stage
        await token_tracker.record_usage(150, 75, stage="generate_response")
        
        # Verify that stage_tokens was updated correctly
        assert token_tracker.stage_tokens["generate_response"]["input"] == 150
        assert token_tracker.stage_tokens["generate_response"]["output"] == 75


@pytest.mark.asyncio
async def test_record_usage_updates_model_tokens():
    """Test that record_usage updates model_tokens correctly."""
    # Create a token tracker with a real implementation of record_usage
    run_id = uuid.uuid4()
    token_tracker = TokenTracker(run_id=run_id)
    
    # Mock the database update to avoid actual database operations
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function
        mock_sync_to_async.return_value = AsyncMock()
        
        # Record token usage with model
        await token_tracker.record_usage(100, 50, model="openai/gpt-4")
        
        # Verify that model_tokens was updated correctly
        assert token_tracker.model_tokens["openai/gpt-4"]["input"] == 100
        assert token_tracker.model_tokens["openai/gpt-4"]["output"] == 50
        
        # Record more token usage for the same model
        await token_tracker.record_usage(200, 100, model="openai/gpt-4")
        
        # Verify that model_tokens was updated correctly
        assert token_tracker.model_tokens["openai/gpt-4"]["input"] == 300
        assert token_tracker.model_tokens["openai/gpt-4"]["output"] == 150
        
        # Record token usage for a different model
        await token_tracker.record_usage(150, 75, model="openai/gpt-3.5-turbo")
        
        # Verify that model_tokens was updated correctly
        assert token_tracker.model_tokens["openai/gpt-3.5-turbo"]["input"] == 150
        assert token_tracker.model_tokens["openai/gpt-3.5-turbo"]["output"] == 75


@pytest.mark.asyncio
async def test_calculate_cost_by_model():
    """Test calculating cost by model."""
    # Create a token tracker with a real implementation of record_usage
    run_id = uuid.uuid4()
    token_tracker = TokenTracker(run_id=run_id)
    
    # Mock the database update to avoid actual database operations
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function
        mock_sync_to_async.return_value = AsyncMock()
        
        # Record token usage for different models
        await token_tracker.record_usage(100, 50, model="openai/gpt-4")
        await token_tracker.record_usage(200, 100, model="openai/gpt-3.5-turbo")
        
        # Calculate cost by model
        model_costs = await token_tracker.calculate_cost_by_model({
            "openai/gpt-4": {"input": 0.001, "output": 0.002},
            "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.001}
        })
        
        # Verify the costs
        expected_gpt4_cost = (100 * 0.001) + (50 * 0.002)
        expected_gpt35_cost = (200 * 0.0005) + (100 * 0.001)
        assert model_costs["openai/gpt-4"] == pytest.approx(expected_gpt4_cost)
        assert model_costs["openai/gpt-3.5-turbo"] == pytest.approx(expected_gpt35_cost)


@pytest.mark.asyncio
async def test_calculate_cost_by_stage():
    """Test calculating cost by stage."""
    # Create a token tracker with a real implementation of record_usage
    run_id = uuid.uuid4()
    token_tracker = TokenTracker(run_id=run_id)
    
    # Mock the database update to avoid actual database operations
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function
        mock_sync_to_async.return_value = AsyncMock()
        
        # Record token usage for different stages
        await token_tracker.record_usage(100, 50, stage="process_input")
        await token_tracker.record_usage(200, 100, stage="generate_response")
        
        # Calculate cost by stage
        stage_costs = await token_tracker.calculate_cost_by_stage(0.001, 0.002)
        
        # Verify the costs
        expected_process_input_cost = (100 * 0.001) + (50 * 0.002)
        expected_generate_response_cost = (200 * 0.001) + (100 * 0.002)
        assert stage_costs["process_input"] == pytest.approx(expected_process_input_cost)
        assert stage_costs["generate_response"] == pytest.approx(expected_generate_response_cost)


@pytest.mark.asyncio
async def test_get_token_usage_report():
    """Test generating a token usage report."""
    # Create a token tracker with a real implementation of record_usage
    run_id = uuid.uuid4()
    token_tracker = TokenTracker(run_id=run_id)
    
    # Mock the database update to avoid actual database operations
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function
        mock_sync_to_async.return_value = AsyncMock()
        
        # Record token usage for different stages and models
        await token_tracker.record_usage(100, 50, stage="process_input", model="openai/gpt-4")
        await token_tracker.record_usage(200, 100, stage="generate_response", model="openai/gpt-3.5-turbo")
        
        # Generate token usage report
        report = token_tracker.get_token_usage_report()
        
        # Verify the report
        assert report["total"]["input"] == 300
        assert report["total"]["output"] == 150
        assert report["total"]["total"] == 450
        
        assert report["by_stage"]["process_input"]["input"] == 100
        assert report["by_stage"]["process_input"]["output"] == 50
        assert report["by_stage"]["process_input"]["total"] == 150
        
        assert report["by_stage"]["generate_response"]["input"] == 200
        assert report["by_stage"]["generate_response"]["output"] == 100
        assert report["by_stage"]["generate_response"]["total"] == 300
        
        assert report["by_model"]["openai/gpt-4"]["input"] == 100
        assert report["by_model"]["openai/gpt-4"]["output"] == 50
        assert report["by_model"]["openai/gpt-4"]["total"] == 150
        
        assert report["by_model"]["openai/gpt-3.5-turbo"]["input"] == 200
        assert report["by_model"]["openai/gpt-3.5-turbo"]["output"] == 100
        assert report["by_model"]["openai/gpt-3.5-turbo"]["total"] == 300


@pytest.mark.asyncio
async def test_multiple_record_usage_calls_with_different_stages_and_models():
    """Test multiple record_usage calls with different stages and models."""
    # Create a token tracker with a real implementation of record_usage
    run_id = uuid.uuid4()
    token_tracker = TokenTracker(run_id=run_id)
    
    # Mock the database update to avoid actual database operations
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function
        mock_sync_to_async.return_value = AsyncMock()
        
        # Record token usage for different stages and models
        await token_tracker.record_usage(100, 50, stage="process_input", model="openai/gpt-4")
        await token_tracker.record_usage(200, 100, stage="generate_response", model="openai/gpt-3.5-turbo")
        await token_tracker.record_usage(150, 75, stage="process_input", model="openai/gpt-4")
        await token_tracker.record_usage(250, 125, stage="generate_response", model="openai/gpt-3.5-turbo")
        
        # Verify the total token counts
        assert token_tracker.input_tokens == 700
        assert token_tracker.output_tokens == 350
        
        # Verify the stage token counts
        assert token_tracker.stage_tokens["process_input"]["input"] == 250
        assert token_tracker.stage_tokens["process_input"]["output"] == 125
        assert token_tracker.stage_tokens["generate_response"]["input"] == 450
        assert token_tracker.stage_tokens["generate_response"]["output"] == 225
        
        # Verify the model token counts
        assert token_tracker.model_tokens["openai/gpt-4"]["input"] == 250
        assert token_tracker.model_tokens["openai/gpt-4"]["output"] == 125
        assert token_tracker.model_tokens["openai/gpt-3.5-turbo"]["input"] == 450
        assert token_tracker.model_tokens["openai/gpt-3.5-turbo"]["output"] == 225
