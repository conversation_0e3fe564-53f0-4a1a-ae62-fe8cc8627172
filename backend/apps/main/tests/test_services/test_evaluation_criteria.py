"""
Tests for evaluation criteria validation.

This module contains tests for validating evaluation criteria against schemas.
"""

import pytest

from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_validator_service import SchemaValidationService


@pytest.fixture
def schema_registry():
    """Fixture to provide a SchemaRegistry instance."""
    return SchemaRegistry()


@pytest.fixture
def schema_validator(schema_registry):
    """Fixture to provide a SchemaValidationService instance."""
    return SchemaValidationService(schema_registry=schema_registry)


def test_validate_evaluation_criteria(schema_validator):
    """Test validation of evaluation criteria."""
    # Create valid evaluation criteria
    evaluation_criteria = {
        "criteria": [
            {
                "name": "Completeness",
                "description": "Is the workflow output complete?",
                "weight": 0.5
            },
            {
                "name": "Accuracy",
                "description": "Is the workflow output accurate?",
                "weight": 0.5
            }
        ]
    }

    # Validate the evaluation criteria
    is_valid, errors = schema_validator.validate_evaluation_criteria(evaluation_criteria)

    # Verify that validation succeeds
    assert is_valid, f"Validation failed: {errors}"
    assert not errors, f"Unexpected errors: {errors}"


def test_validate_invalid_evaluation_criteria(schema_validator):
    """Test validation of invalid evaluation criteria."""
    # Create invalid evaluation criteria (missing weight)
    evaluation_criteria = {
        "criteria": [
            {
                "name": "Completeness",
                "description": "Is the workflow output complete?"
                # Missing weight field
            }
        ]
    }

    # Validate the evaluation criteria
    is_valid, errors = schema_validator.validate_evaluation_criteria(evaluation_criteria)

    # Verify that validation fails
    assert not is_valid
    assert errors, "Expected validation errors but got none"
    # The error message doesn't specifically mention 'weight', but it does indicate the schema validation failed
    assert len(errors) > 0, "Expected at least one validation error"
