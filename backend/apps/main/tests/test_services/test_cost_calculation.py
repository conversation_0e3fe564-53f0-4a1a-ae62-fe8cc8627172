"""
Tests for cost calculation functionality in workflow benchmarking.

This module contains tests for the cost calculation functionality in the
workflow benchmarking system, including token usage tracking and cost reporting.
"""

import pytest
import uuid
from unittest.mock import AsyncMock, patch, MagicMock

from apps.main.services.async_workflow_manager import TokenTracker, BenchmarkResult, WorkflowBenchmarker
from apps.main.models import BenchmarkScenario, BenchmarkRun


@pytest.fixture
def token_tracker():
    """Fixture to provide a TokenTracker instance."""
    # Create a mock run_id
    run_id = uuid.uuid4()
    # Create a real TokenTracker instance
    return TokenTracker(run_id=run_id)


@pytest.fixture
def benchmark_result():
    """Fixture to provide a BenchmarkResult instance."""
    return BenchmarkResult(
        workflow_type="test_workflow",
        scenario_name="test_scenario",
        total_input_tokens=1000,
        total_output_tokens=500
    )


@pytest.fixture
def workflow_manager():
    """Fixture to provide an AsyncWorkflowManager instance."""
    # Create a workflow manager instance
    manager = WorkflowBenchmarker()
    # Create a token tracker for the manager
    manager.token_tracker = TokenTracker(run_id=uuid.uuid4())
    return manager


@pytest.mark.asyncio
async def test_calculate_cost(token_tracker):
    """Test calculating cost based on token usage."""
    # Record token usage
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function
        mock_sync_to_async.return_value = AsyncMock()

        # Record token usage
        await token_tracker.record_usage(1000, 500)

        # Calculate cost
        cost = await token_tracker.calculate_cost(0.001, 0.002)

        # Verify the cost
        expected_cost = (1000 * 0.001) + (500 * 0.002)
        assert cost == pytest.approx(expected_cost)


@pytest.mark.asyncio
async def test_calculate_cost_by_model(token_tracker):
    """Test calculating cost by model."""
    # Record token usage
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function
        mock_sync_to_async.return_value = AsyncMock()

        # Record token usage for different models
        await token_tracker.record_usage(1000, 500, model="openai/gpt-4")
        await token_tracker.record_usage(2000, 1000, model="openai/gpt-3.5-turbo")

        # Calculate cost by model
        model_costs = await token_tracker.calculate_cost_by_model({
            "openai/gpt-4": {"input": 0.001, "output": 0.002},
            "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.001}
        })

        # Verify the costs
        expected_gpt4_cost = (1000 * 0.001) + (500 * 0.002)
        expected_gpt35_cost = (2000 * 0.0005) + (1000 * 0.001)
        assert model_costs["openai/gpt-4"] == pytest.approx(expected_gpt4_cost)
        assert model_costs["openai/gpt-3.5-turbo"] == pytest.approx(expected_gpt35_cost)


@pytest.mark.asyncio
async def test_calculate_cost_by_stage(token_tracker):
    """Test calculating cost by stage."""
    # Record token usage
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function
        mock_sync_to_async.return_value = AsyncMock()

        # Record token usage for different stages
        await token_tracker.record_usage(1000, 500, stage="process_input")
        await token_tracker.record_usage(2000, 1000, stage="generate_response")

        # Calculate cost by stage
        stage_costs = await token_tracker.calculate_cost_by_stage(0.001, 0.002)

        # Verify the costs
        expected_process_input_cost = (1000 * 0.001) + (500 * 0.002)
        expected_generate_response_cost = (2000 * 0.001) + (1000 * 0.002)
        assert stage_costs["process_input"] == pytest.approx(expected_process_input_cost)
        assert stage_costs["generate_response"] == pytest.approx(expected_generate_response_cost)


@pytest.mark.asyncio
async def test_store_results_with_cost_calculation(workflow_manager, benchmark_result):
    """Test storing results with cost calculation."""
    # Mock the scenario
    scenario = MagicMock(spec=BenchmarkScenario)
    scenario.name = "test_scenario"

    # Mock the create_benchmark_run_sync method
    workflow_manager._create_benchmark_run_sync = MagicMock(return_value=MagicMock(spec=BenchmarkRun))

    # Mock the _get_or_create_agent_def_async method
    mock_agent = MagicMock()
    workflow_manager._get_or_create_agent_def_async = AsyncMock(return_value=mock_agent)

    # Mock sync_to_async
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function that returns the mock benchmark run
        mock_sync_to_async.return_value = AsyncMock(return_value=MagicMock(spec=BenchmarkRun))

        # Record token usage
        await workflow_manager.token_tracker.record_usage(1000, 500, stage="process_input", model="openai/gpt-4")
        await workflow_manager.token_tracker.record_usage(2000, 1000, stage="generate_response", model="openai/gpt-3.5-turbo")

        # Define model prices
        model_prices = {
            "default": {"input": 0.001, "output": 0.002},
            "openai/gpt-4": {"input": 0.001, "output": 0.002},
            "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.001}
        }

        # Store results with cost calculation
        await workflow_manager._store_results(
            scenario=scenario,
            result=benchmark_result,
            params={"model_prices": model_prices},
            user_profile_id="test_user"
        )

        # Verify that the cost report was created
        assert "total_cost" in benchmark_result.cost_report
        assert "by_model" in benchmark_result.cost_report
        assert "by_stage" in benchmark_result.cost_report

        # Verify the token usage report was created
        assert "total" in benchmark_result.token_usage_report
        assert "by_stage" in benchmark_result.token_usage_report
        assert "by_model" in benchmark_result.token_usage_report

        # Verify the token usage by stage and model
        assert "process_input" in benchmark_result.token_usage_by_stage
        assert "generate_response" in benchmark_result.token_usage_by_stage
        assert "openai/gpt-4" in benchmark_result.token_usage_by_model
        assert "openai/gpt-3.5-turbo" in benchmark_result.token_usage_by_model


@pytest.mark.asyncio
async def test_store_results_without_token_tracker(benchmark_result):
    """Test storing results without a token tracker."""
    # Create a workflow manager without a token tracker
    workflow_manager = WorkflowBenchmarker()

    # Mock the scenario
    scenario = MagicMock(spec=BenchmarkScenario)
    scenario.name = "test_scenario"

    # Mock the create_benchmark_run_sync method
    workflow_manager._create_benchmark_run_sync = MagicMock(return_value=MagicMock(spec=BenchmarkRun))

    # Mock the _get_or_create_agent_def_async method
    mock_agent = MagicMock()
    workflow_manager._get_or_create_agent_def_async = AsyncMock(return_value=mock_agent)

    # Mock sync_to_async
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function that returns the mock benchmark run
        mock_sync_to_async.return_value = AsyncMock(return_value=MagicMock(spec=BenchmarkRun))

        # Define model prices
        model_prices = {
            "default": {"input": 0.001, "output": 0.002}
        }

        # Store results with cost calculation
        await workflow_manager._store_results(
            scenario=scenario,
            result=benchmark_result,
            params={"model_prices": model_prices},
            user_profile_id="test_user"
        )

        # Verify that a simple cost report was created
        assert "total_cost" in benchmark_result.cost_report

        # Verify that the token usage report was not created
        assert not benchmark_result.token_usage_report


@pytest.mark.asyncio
async def test_store_results_without_model_prices(workflow_manager, benchmark_result):
    """Test storing results without model prices."""
    # Mock the scenario
    scenario = MagicMock(spec=BenchmarkScenario)
    scenario.name = "test_scenario"

    # Mock the create_benchmark_run_sync method
    workflow_manager._create_benchmark_run_sync = MagicMock(return_value=MagicMock(spec=BenchmarkRun))

    # Mock the _get_or_create_agent_def_async method
    mock_agent = MagicMock()
    workflow_manager._get_or_create_agent_def_async = AsyncMock(return_value=mock_agent)

    # Mock sync_to_async
    with patch('apps.main.services.async_workflow_manager.sync_to_async') as mock_sync_to_async:
        # Configure the mock to return a mock function that returns the mock benchmark run
        mock_sync_to_async.return_value = AsyncMock(return_value=MagicMock(spec=BenchmarkRun))

        # Record token usage
        await workflow_manager.token_tracker.record_usage(1000, 500, stage="process_input", model="openai/gpt-4")

        # Store results without model prices
        await workflow_manager._store_results(
            scenario=scenario,
            result=benchmark_result,
            params={},
            user_profile_id="test_user"
        )

        # Verify that no cost report was created
        assert not benchmark_result.cost_report

        # Verify that the token usage report was created
        assert "total" in benchmark_result.token_usage_report
        assert "by_stage" in benchmark_result.token_usage_report
        assert "by_model" in benchmark_result.token_usage_report
