"""
Tests for the Statistical Analysis Service.
"""

import pytest
import math
from decimal import Decimal
from datetime import datetime, timedelta
from django.utils import timezone
from unittest.mock import patch, MagicMock

from apps.main.services.statistical_analysis import StatisticalAnalysisService
from apps.main.models import <PERSON><PERSON>markRun, BenchmarkScenario, GenericAgent
from apps.main.tests.utils import create_test_scenario, create_test_agent


@pytest.mark.asyncio
async def test_calculate_welchs_t_test():
    """Test the calculate_welchs_t_test method."""
    # Arrange
    service = StatisticalAnalysisService()

    # Act
    t_stat, p_value = service.calculate_welchs_t_test(
        mean1=100.0,
        std1=10.0,
        n1=10,
        mean2=90.0,
        std2=10.0,
        n2=10
    )

    # Assert
    assert isinstance(t_stat, float)
    assert isinstance(p_value, float)
    assert t_stat > 0  # t-statistic should be positive when mean1 > mean2
    assert 0 <= p_value <= 1  # p-value should be between 0 and 1


@pytest.mark.asyncio
async def test_calculate_cohens_d():
    """Test the calculate_cohens_d method."""
    # Arrange
    service = StatisticalAnalysisService()

    # Act
    d = service.calculate_cohens_d(
        mean1=100.0,
        std1=10.0,
        n1=10,
        mean2=90.0,
        std2=10.0,
        n2=10
    )

    # Assert
    assert isinstance(d, float)
    assert d > 0  # Effect size should be positive
    assert d == 1.0  # For these values, Cohen's d should be 1.0


@pytest.mark.asyncio
async def test_interpret_cohens_d():
    """Test the interpret_cohens_d method."""
    # Arrange
    service = StatisticalAnalysisService()

    # Act & Assert
    assert service.interpret_cohens_d(0.1) == "negligible"
    assert service.interpret_cohens_d(0.3) == "small"
    assert service.interpret_cohens_d(0.6) == "medium"
    assert service.interpret_cohens_d(1.0) == "large"


@pytest.mark.asyncio
async def test_calculate_confidence_interval():
    """Test the calculate_confidence_interval method."""
    # Arrange
    service = StatisticalAnalysisService()

    # Act
    lower, upper = service.calculate_confidence_interval(
        mean=100.0,
        std=10.0,
        n=10,
        confidence_level=0.95
    )

    # Assert
    assert isinstance(lower, float)
    assert isinstance(upper, float)
    assert lower < 100.0  # Lower bound should be less than the mean
    assert upper > 100.0  # Upper bound should be greater than the mean
    assert upper - lower > 0  # Interval should have positive width


@pytest.mark.asyncio
async def test_bootstrap_confidence_interval():
    """Test the bootstrap_confidence_interval method."""
    # Arrange
    service = StatisticalAnalysisService()
    data = [95, 98, 102, 105, 107, 93, 97, 101, 104, 99]

    # Act
    lower, upper = service.bootstrap_confidence_interval(
        data=data,
        confidence_level=0.95,
        n_resamples=1000
    )

    # Assert
    assert isinstance(lower, float)
    assert isinstance(upper, float)
    assert lower < 100.0  # Lower bound should be less than the mean
    assert upper > 100.0  # Upper bound should be greater than the mean
    assert upper - lower > 0  # Interval should have positive width


@pytest.mark.django_db
@pytest.mark.asyncio
async def test_get_benchmark_runs():
    """Test the get_benchmark_runs method."""
    # Arrange
    service = StatisticalAnalysisService()

    # Create test data
    scenario = create_test_scenario(
        name="Test Scenario for get_benchmark_runs",
        description="Test scenario for statistical analysis",
        agent_role="test",
        input_data={"key": "value"},
        metadata={}
    )

    agent_def = create_test_agent(
        role="test1",
        description="Test agent",
        system_instructions="Test instructions",
        input_schema={},
        output_schema={},
        langgraph_node_class="test.TestAgent"
    )

    # Create benchmark runs
    for i in range(5):
        BenchmarkRun.objects.create(
            scenario=scenario,
            agent_definition=agent_def,
            agent_version="1.0.0",
            runs_count=10,
            mean_duration=100.0 + i * 10,
            median_duration=100.0 + i * 10,
            min_duration=90.0 + i * 10,
            max_duration=110.0 + i * 10,
            std_dev=10.0,
            success_rate=0.9,
            execution_date=timezone.now() - timedelta(days=i)
        )

    # Act
    runs = await service.get_benchmark_runs(scenario_id=scenario.id)

    # Assert
    assert len(runs) == 5
    assert runs[0].scenario.id == scenario.id
    assert runs[0].agent_definition.id == agent_def.id


@pytest.mark.django_db
@pytest.mark.asyncio
async def test_compare_benchmark_runs():
    """Test the compare_benchmark_runs method."""
    # Arrange
    service = StatisticalAnalysisService()

    # Create test data
    scenario = create_test_scenario(
        name="Test Scenario for compare_benchmark_runs",
        description="Test scenario for statistical analysis",
        agent_role="test",
        input_data={"key": "value"},
        metadata={}
    )

    agent_def = create_test_agent(
        role="test2",
        description="Test agent",
        system_instructions="Test instructions",
        input_schema={},
        output_schema={},
        langgraph_node_class="test.TestAgent"
    )

    # Create benchmark runs
    run1 = BenchmarkRun.objects.create(
        scenario=scenario,
        agent_definition=agent_def,
        agent_version="1.0.0",
        runs_count=10,
        mean_duration=100.0,
        median_duration=100.0,
        min_duration=90.0,
        max_duration=110.0,
        std_dev=10.0,
        success_rate=0.9,
        execution_date=timezone.now() - timedelta(days=1)
    )

    run2 = BenchmarkRun.objects.create(
        scenario=scenario,
        agent_definition=agent_def,
        agent_version="1.0.0",
        runs_count=10,
        mean_duration=90.0,
        median_duration=90.0,
        min_duration=80.0,
        max_duration=100.0,
        std_dev=10.0,
        success_rate=0.9,
        execution_date=timezone.now() - timedelta(days=2)
    )

    # Act
    comparison = await service.compare_benchmark_runs(run1.id, run2.id)

    # Assert
    assert comparison is not None
    assert comparison['run1']['id'] == str(run1.id)
    assert comparison['run2']['id'] == str(run2.id)
    assert 'statistical_comparison' in comparison
    assert 't_statistic' in comparison['statistical_comparison']
    assert 'p_value' in comparison['statistical_comparison']
    assert 'is_significant' in comparison['statistical_comparison']
    assert 'cohens_d' in comparison['statistical_comparison']
    assert 'effect_size' in comparison['statistical_comparison']


@pytest.mark.django_db
@pytest.mark.asyncio
async def test_analyze_benchmark_trend():
    """Test the analyze_benchmark_trend method."""
    # Arrange
    service = StatisticalAnalysisService()

    # Create test data
    scenario = create_test_scenario(
        name="Test Scenario for analyze_benchmark_trend",
        description="Test scenario for statistical analysis",
        agent_role="test",
        input_data={"key": "value"},
        metadata={}
    )

    agent_def = create_test_agent(
        role="test3",
        description="Test agent",
        system_instructions="Test instructions",
        input_schema={},
        output_schema={},
        langgraph_node_class="test.TestAgent"
    )

    # Create benchmark runs with increasing mean_duration
    for i in range(10):
        BenchmarkRun.objects.create(
            scenario=scenario,
            agent_definition=agent_def,
            agent_version="1.0.0",
            runs_count=10,
            mean_duration=100.0 + i * 5,  # Increasing trend
            median_duration=100.0 + i * 5,
            min_duration=90.0 + i * 5,
            max_duration=110.0 + i * 5,
            std_dev=10.0,
            success_rate=0.9 - i * 0.01,  # Decreasing trend
            semantic_score=0.8 + i * 0.01,  # Increasing trend
            execution_date=timezone.now() - timedelta(days=10-i)
        )

    # Act
    trend = await service.analyze_benchmark_trend(scenario.id, time_window=30, min_runs=5)

    # Assert
    assert trend is not None
    assert trend['scenario_id'] == scenario.id
    assert trend['runs_analyzed'] == 10
    assert trend['duration_metrics']['trend'] == "increasing"
    assert trend['success_rate_metrics']['trend'] == "decreasing"
    assert 'semantic_score_metrics' in trend
    assert trend['semantic_score_metrics']['trend'] == "increasing"


@pytest.mark.django_db
@pytest.mark.asyncio
async def test_get_benchmark_statistics():
    """Test the get_benchmark_statistics method."""
    # Arrange
    service = StatisticalAnalysisService()

    # Create test data
    scenario = create_test_scenario(
        name="Test Scenario for get_benchmark_statistics",
        description="Test scenario for statistical analysis",
        agent_role="test",
        input_data={"key": "value"},
        metadata={}
    )

    agent_def = create_test_agent(
        role="test4",
        description="Test agent",
        system_instructions="Test instructions",
        input_schema={},
        output_schema={},
        langgraph_node_class="test.TestAgent"
    )

    # Create benchmark runs
    for i in range(5):
        BenchmarkRun.objects.create(
            scenario=scenario,
            agent_definition=agent_def,
            agent_version="1.0.0",
            runs_count=10,
            mean_duration=100.0 + i * 10,
            median_duration=100.0 + i * 10,
            min_duration=90.0 + i * 10,
            max_duration=110.0 + i * 10,
            std_dev=10.0,
            success_rate=0.9,
            semantic_score=0.8,
            execution_date=timezone.now() - timedelta(days=i)
        )

    # Act
    stats = await service.get_benchmark_statistics(scenario_id=scenario.id)

    # Assert
    assert stats is not None
    assert stats['count'] == 5
    assert 'duration_statistics' in stats
    assert 'success_rate_statistics' in stats
    assert 'semantic_score_statistics' in stats
    assert stats['duration_statistics']['average_mean'] is not None
    assert stats['success_rate_statistics']['average'] is not None
    assert stats['semantic_score_statistics']['average'] is not None


@pytest.mark.django_db
@pytest.mark.asyncio
async def test_update_benchmark_run_comparison():
    """Test the update_benchmark_run_comparison method."""
    # Arrange
    service = StatisticalAnalysisService()

    # Create test data
    scenario = create_test_scenario(
        name="Test Scenario for update_benchmark_run_comparison",
        description="Test scenario for statistical analysis",
        agent_role="test",
        input_data={"key": "value"},
        metadata={}
    )

    agent_def = create_test_agent(
        role="test5",
        description="Test agent",
        system_instructions="Test instructions",
        input_schema={},
        output_schema={},
        langgraph_node_class="test.TestAgent"
    )

    # Create benchmark runs
    run1 = BenchmarkRun.objects.create(
        scenario=scenario,
        agent_definition=agent_def,
        agent_version="1.0.0",
        runs_count=10,
        mean_duration=100.0,
        median_duration=100.0,
        min_duration=90.0,
        max_duration=110.0,
        std_dev=10.0,
        success_rate=0.9,
        execution_date=timezone.now() - timedelta(days=1),
        raw_results={}
    )

    run2 = BenchmarkRun.objects.create(
        scenario=scenario,
        agent_definition=agent_def,
        agent_version="1.0.0",
        runs_count=10,
        mean_duration=90.0,
        median_duration=90.0,
        min_duration=80.0,
        max_duration=100.0,
        std_dev=10.0,
        success_rate=0.9,
        execution_date=timezone.now() - timedelta(days=2),
        raw_results={}
    )

    # Act
    result = await service.update_benchmark_run_comparison(run1.id, run2.id)

    # Assert
    assert result is not None
    assert result['status'] == 'success'
    assert 'comparison' in result
    assert result['comparison']['compared_to_run_id'] == str(run2.id)

    # Verify the database was updated
    updated_run = await service._get_benchmark_runs_sync(limit=None)
    updated_run = next((r for r in updated_run if str(r.id) == str(run1.id)), None)
    assert updated_run is not None
    assert updated_run.compared_to_run_id == run2.id
    assert updated_run.performance_p_value is not None
    assert updated_run.is_performance_significant is not None
    assert 'statistical_comparison' in updated_run.raw_results
    assert 'cohens_d' in updated_run.raw_results['statistical_comparison']
    assert 'effect_size' in updated_run.raw_results['statistical_comparison']
