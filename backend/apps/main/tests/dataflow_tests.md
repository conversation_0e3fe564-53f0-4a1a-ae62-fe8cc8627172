# Data Flow Architecture Tests

This suite of tests ensures the reliability of the Game of Life AI system's data flow architecture. These tests validate how data moves between components, how user interactions are processed, and how responses are delivered.

## Overview

The Game of Life uses a multi-agent architecture with real-time communication:

1. **Frontend WebSocket Client** sends messages and receives responses
2. **UserSessionConsumer** manages WebSocket connections and message routing
3. **ConversationDispatcher** analyzes user input and routes to appropriate workflows
4. **Agent Workflow System** processes requests through specialized agent workflows
5. **WorkflowResultHandler** processes completed workflow results and delivers to clients

## Test Coverage

The test suite covers the following critical components:

### ConversationDispatcher Tests

Tests in `test_conversation_dispatcher.py` verify that:

- User messages are correctly analyzed and classified into workflow types
- Context extraction works correctly for different message patterns
- Workflow classification rules apply properly for ambiguous messages
- Explicit metadata routing directs messages to the appropriate workflow
- Workflow IDs are generated consistently and included in results
- Error handling provides fallback responses

### WorkflowResultHandler Tests

Tests in `test_workflow_result_handler.py` ensure that:

- Results are properly formatted based on workflow type
- Messages are sent to the correct WebSocket channel/group
- Wheel data is transformed to follow the API contract
- Activity details are sent with the correct structure
- Error handling provides appropriate error notifications
- Fallback behavior handles missing WebSocket session names

### WebSocket Consumer Tests

Tests in `test_websocket_consumer.py` validate that:

- WebSocket connections are established successfully
- Chat messages are properly processed and dispatched
- Spin results trigger the appropriate workflow
- Data validation catches malformed requests
- Channel layer messages are transformed into proper WebSocket responses
- WebSocket-specific error handling works as expected

### Integration Tests

Tests in `test_data_flow_integration.py` provide end-to-end coverage of:

- Complete message flow from WebSocket through dispatcher to result handler
- Wheel generation flow with wheel data retrieval and display
- Spin result to activity details flow
- Error recovery and notification flow

### Celery Task Result Tests

Tests in `test_celery_results.py` verify:

- Successful task completion signal handling
- Task failure signal handling
- Workflow result processing
- Agent node result processing
- Onboarding workflow result handling

## Running the Tests

### Prerequisites

1. A working Python environment with Django, Channels, and pytest installed
2. The Game of Life backend code
3. The Redis service for Channels layer (optional for some tests)

### Environment Setup

```bash
# Navigate to project backend directory
cd backend

# Install test dependencies if needed
pip install pytest pytest-django pytest-asyncio

# Set up test environment variables
export DJANGO_SETTINGS_MODULE=config.settings.test
export TESTING=true
```

### Running All Tests

```bash
# Run all data flow tests
python -m pytest apps/main/tests/test_conversation_dispatcher.py apps/main/tests/test_workflow_result_handler.py apps/main/tests/test_websocket_consumer.py apps/main/tests/test_data_flow_integration.py apps/main/tests/test_celery_results.py -v
```

### Running Specific Test Groups

```bash
# Run just the conversation dispatcher tests
python -m pytest apps/main/tests/test_conversation_dispatcher.py -v

# Run just the workflow result handler tests
python -m pytest apps/main/tests/test_workflow_result_handler.py -v

# Run just the WebSocket consumer tests
python -m pytest apps/main/tests/test_websocket_consumer.py -v

# Run just the end-to-end integration tests
python -m pytest apps/main/tests/test_data_flow_integration.py -v

# Run just the Celery result handling tests
python -m pytest apps/main/tests/test_celery_results.py -v
```

## Troubleshooting

### Common Issues

1. **WebSocket Test Failures**: Ensure Channels is properly configured with an in-memory channel layer for testing.

2. **Async Test Errors**: Verify that pytest-asyncio is installed and the event loop policy is correctly set.

3. **Django Model Errors**: If you encounter model-related errors, check that your test environment is using the correct settings and database configuration.

4. **Celery Task Test Failures**: These tests mock Celery functionality and don't require a running Celery worker.

## Future Test Improvements

Potential enhancements to the test suite include:

1. **Performance Tests**: Add benchmarks to ensure data flow components meet latency requirements.

2. **Stress Tests**: Test the system under high message volume.

3. **Fault Injection**: Introduce network failures and service outages to test resilience.

4. **Security Tests**: Validate authorization and authentication for WebSocket connections.

## API Contract Validation

These tests ensure compliance with the defined API contract between frontend and backend:

1. All client → server messages follow the specified format
2. All server → client messages adhere to the documented structure
3. WebSocket event types are consistently used
4. Error responses have the expected format and content