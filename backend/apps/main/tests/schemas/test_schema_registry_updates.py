import pytest
import os
import json
from unittest.mock import patch, MagicMock

from apps.main.schemas.registry_updates import register_enhanced_context_schemas
from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_version_manager import SchemaVersionManager
from apps.main.schemas.migrations.situation_v1_to_v2 import migrate_situation_v1_to_v2


@pytest.mark.test_type("unit")
@pytest.mark.component("main.schemas.registry_updates")
def test_register_enhanced_context_schemas():
    """Test that enhanced context schemas are registered correctly."""
    # Create mock registry and version manager
    mock_registry = MagicMock(spec=SchemaRegistry)
    mock_version_manager = MagicMock(spec=SchemaVersionManager)
    
    # Mock the open function to return a valid schema
    mock_schema = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "SituationSchema",
        "version": "2.0.0",
        "type": "object",
        "properties": {
            "user_state": {
                "type": "object",
                "properties": {
                    "trust_level": {"type": "number"}
                }
            }
        }
    }
    
    with patch('builtins.open', MagicMock()) as mock_open:
        # Configure mock_open to return our mock schema
        mock_open.return_value.__enter__.return_value.read.return_value = json.dumps(mock_schema)
        
        # Call the function under test
        register_enhanced_context_schemas(mock_registry, mock_version_manager)
        
        # Verify the registry and version manager were called correctly
        mock_registry.register_schema.assert_called_once_with(
            "situation", mock_schema, version="2.0.0"
        )
        mock_version_manager.register_schema_version.assert_called_once_with(
            "situation", "2.0.0", mock_schema
        )
        mock_version_manager.register_migration.assert_called_once_with(
            "situation", "1.0.0", "2.0.0", migrate_situation_v1_to_v2
        )


@pytest.mark.test_type("unit")
@pytest.mark.component("main.schemas.migrations.situation_v1_to_v2")
def test_migrate_situation_v1_to_v2():
    """Test migration from situation schema v1.0.0 to v2.0.0."""
    # Create a sample v1.0.0 situation data
    v1_data = {
        "workflow_type": "wheel_generation",
        "text": "I need an activity suggestion",
        "trust_level": 75,
        "time_availability": 30,
        "environment": {
            "location": "home",
            "noise_level": "low"
        },
        "psychological_state": {
            "mood": "happy",
            "energy_level": "high"
        }
    }
    
    # Migrate to v2.0.0
    v2_data = migrate_situation_v1_to_v2(v1_data)
    
    # Verify the migration
    assert v2_data["version"] == "2.0.0"
    assert v2_data["workflow_type"] == v1_data["workflow_type"]
    assert v2_data["text"] == v1_data["text"]
    assert v2_data["trust_level"] == v1_data["trust_level"]
    
    # Verify new fields
    assert "user_state" in v2_data
    assert v2_data["user_state"]["trust_level"] == v1_data["trust_level"]
    assert v2_data["user_state"]["mood"] == v1_data["psychological_state"]["mood"]
    assert v2_data["user_state"]["environment"] == v1_data["environment"]["location"]
    
    assert "device_capabilities" in v2_data
    assert "time_context" in v2_data
    assert v2_data["time_context"]["available_time"] == v1_data["time_availability"]
    assert "user_preferences" in v2_data


@pytest.mark.test_type("unit")
@pytest.mark.component("main.schemas.migrations.situation_v1_to_v2")
def test_migrate_situation_v1_to_v2_minimal():
    """Test migration with minimal v1.0.0 data."""
    # Create minimal v1.0.0 situation data
    v1_data = {
        "workflow_type": "discussion",
        "text": "Hello"
    }
    
    # Migrate to v2.0.0
    v2_data = migrate_situation_v1_to_v2(v1_data)
    
    # Verify the migration
    assert v2_data["version"] == "2.0.0"
    assert v2_data["workflow_type"] == v1_data["workflow_type"]
    assert v2_data["text"] == v1_data["text"]
    
    # Verify new fields with default values
    assert "user_state" in v2_data
    assert v2_data["user_state"]["trust_level"] == 50  # Default value
    assert v2_data["user_state"]["mood"] == ""
    assert v2_data["user_state"]["environment"] == ""
    
    assert "device_capabilities" in v2_data
    assert v2_data["device_capabilities"]["screen_size"] == "medium"
    
    assert "time_context" in v2_data
    assert v2_data["time_context"]["available_time"] == 0
    
    assert "user_preferences" in v2_data
    assert v2_data["user_preferences"]["learning_style"] == "visual"
