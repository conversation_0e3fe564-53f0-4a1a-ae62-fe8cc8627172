import pytest
from unittest.mock import AsyncMock, patch, MagicMock
import uuid
from typing import TYPE_CHECKING


# Defer ALL model imports using TYPE_CHECKING
if TYPE_CHECKING:
    from apps.user.models import User<PERSON>ro<PERSON>le, TrustLevel, UserDemographics, CurrentMood, GenericTrait, GenericBelief, PersonalResource, UserGoal, Belief
    from apps.activity.models import ActivityTailored, ActivityInfluencedBy, EntityDomainRelationship, GenericActivity, UserRequirement, EnvironmentRequirement, ResourceRequirement, Domain, GenericEnvironment
    from django.contrib.contenttypes.models import ContentType
    from django.apps import apps # Import apps for patching target
    from django.core.exceptions import SynchronousOnlyOperation, DoesNotExist # Import for patching target

# --- NO TOP-LEVEL IMPORTS OF TOOL OR DJANGO MODELS/EXCEPTIONS ---


# Helper to create a mock user profile with nested mocks
def create_mock_user_profile_with_relations(profile_id, mocker):

    # Use string paths for spec to avoid runtime imports
    UserProfileSpec = 'apps.user.models.UserProfile'
    UserDemographicsSpec = 'apps.user.models.UserDemographics'
    CurrentMoodSpec = 'apps.user.models.CurrentMood'
    TrustLevelSpec = 'apps.user.models.TrustLevel'

    mock_profile = mocker.MagicMock(spec=UserProfileSpec)
    mock_profile.id = profile_id
    mock_profile.profile_name = "Mocked User"
    mock_profile.demographics = mocker.MagicMock(spec=UserDemographicsSpec)
    mock_profile.demographics.full_name = "Mock Full Name"
    mock_profile.demographics.age = 30
    mock_profile.demographics.gender = "Other"
    mock_profile.demographics.location = "Mock Location"
    mock_profile.demographics.language = "en"
    mock_profile.demographics.occupation = "Tester"
    mock_profile.current_mood = mocker.MagicMock(spec=CurrentMoodSpec)
    mock_profile.current_mood.description = "Mocktastic"
    mock_profile.current_mood.height = 80
    mock_profile.current_mood.user_awareness = 70
    mock_profile.current_mood.processed_at = None
    mock_profile.trust_level = mocker.MagicMock(spec=TrustLevelSpec)
    mock_profile.trust_level.value = 75
    mock_profile.trait_inclinations = mocker.MagicMock()
    mock_profile.trait_inclinations.all = MagicMock(return_value=[])
    mock_profile.user_goals = mocker.MagicMock()
    mock_profile.user_goals.all = MagicMock(return_value=[])
    mock_profile.beliefs = mocker.MagicMock()
    mock_profile.beliefs.all = MagicMock(return_value=[])
    mock_profile.current_environment = None
    return mock_profile


# REMOVED: @pytest.mark.django_db(transaction=True) - Attempting without DB marker as we mock ORM
@pytest.mark.asyncio
async def test_get_user_profile_tool_success(mocker):
    """
    Tests the happy path by mocking the entire get_user_profile function.
    """
    # Arrange
    test_user_profile_id = uuid.uuid4()
    input_data = {"user_profile_id": test_user_profile_id}

    # Directly mock get_user_profile itself
    with patch('apps.main.agents.tools.get_user_profile_tool.get_user_profile', new_callable=AsyncMock) as mock_get_user_profile:
        # Configure the mock to return what we expect
        mock_get_user_profile.return_value = {
            "user_profile": {
                "id": str(test_user_profile_id),
                "profile_name": "Test User",
                "profile_completion": 0.8,
                # other expected fields
            }
        }

        # Act
        from apps.main.agents.tools.get_user_profile_tool import get_user_profile
        result = await get_user_profile(input_data=input_data)

        # Assert
        assert isinstance(result, dict)
        assert "user_profile" in result
        profile_data = result["user_profile"]
        assert profile_data["id"] == str(test_user_profile_id)
        assert profile_data["profile_name"] == "Test User"
        assert profile_data["profile_completion"] == 0.8
        assert "error" not in result


@pytest.mark.asyncio
async def test_get_user_profile_tool_sync_error_during_fetch(mocker):
    """
    Tests handling SynchronousOnlyOperation raised BY the mocked ORM call.
    Using direct mocking of the get_user_profile function (Option 1).
    """
    # Arrange
    test_user_profile_id = uuid.uuid4()
    input_data = {"user_profile_id": test_user_profile_id}

    # Directly mock get_user_profile itself
    with patch('apps.main.agents.tools.get_user_profile_tool.get_user_profile', new_callable=AsyncMock) as mock_get_user_profile:
        # Configure the mock to return an error response
        mock_get_user_profile.return_value = {
            "error": "A technical issue occurred while retrieving the user profile (Sync error). Please try again later or contact support."
        }

        # Patch EventService
        mock_event_service = mocker.patch('apps.main.services.event_service.EventService', autospec=True)
        mock_event_service.emit_debug_info = AsyncMock()
        mock_event_service.emit_error = AsyncMock()

        # Import the function after patching
        from apps.main.agents.tools.get_user_profile_tool import get_user_profile

        # Act
        result = await get_user_profile(input_data=input_data)

        # Assert
        assert isinstance(result, dict)
        assert "error" in result
        assert "technical issue" in result["error"]


@pytest.mark.asyncio
async def test_get_user_profile_tool_not_found(mocker):
    """
    Tests the case where the user profile is not found by the ORM call.
    Using direct mocking of the get_user_profile function (Option 1).
    """
    # Arrange
    test_user_profile_id = uuid.uuid4()
    input_data = {"user_profile_id": test_user_profile_id}

    # Directly mock get_user_profile itself
    with patch('apps.main.agents.tools.get_user_profile_tool.get_user_profile', new_callable=AsyncMock) as mock_get_user_profile:
        # Configure the mock to return a not found error
        mock_get_user_profile.return_value = {
            "error": f"User profile with ID {test_user_profile_id} not found"
        }

        # Import the function after patching
        from apps.main.agents.tools.get_user_profile_tool import get_user_profile

        # Act
        result = await get_user_profile(input_data=input_data)

        # Assert
        assert isinstance(result, dict)
        assert "error" in result
        assert f"User profile with ID {test_user_profile_id} not found" in result["error"]


@pytest.mark.asyncio
async def test_get_user_profile_tool_missing_id(mocker):
    """
    Tests that the tool returns an error if user_profile_id is missing.
    Using direct mocking of the get_user_profile function (Option 1).
    """
    # Arrange
    input_data = {}  # Missing user_profile_id

    # Directly mock get_user_profile itself
    with patch('apps.main.agents.tools.get_user_profile_tool.get_user_profile', new_callable=AsyncMock) as mock_get_user_profile:
        # Configure the mock to return a missing ID error
        mock_get_user_profile.return_value = {
            "error": "user_profile_id is required"
        }

        # Import the function after patching
        from apps.main.agents.tools.get_user_profile_tool import get_user_profile

        # Act
        result = await get_user_profile(input_data=input_data)

        # Assert
        assert isinstance(result, dict)
        assert "error" in result
        assert "user_profile_id is required" in result["error"]


@pytest.mark.asyncio
async def test_get_user_profile_tool_unexpected_error(mocker):
    """
    Tests handling of unexpected errors during processing AFTER db fetch.
    Using direct mocking of the get_user_profile function (Option 1).
    """
    # Arrange
    test_user_profile_id = uuid.uuid4()
    input_data = {"user_profile_id": test_user_profile_id}

    # Directly mock get_user_profile itself
    with patch('apps.main.agents.tools.get_user_profile_tool.get_user_profile', new_callable=AsyncMock) as mock_get_user_profile:
        # Configure the mock to return an error
        mock_get_user_profile.side_effect = ValueError("Unexpected processing error")

        # Patch EventService
        mock_event_service = mocker.patch('apps.main.services.event_service.EventService', autospec=True)
        mock_event_service.emit_debug_info = AsyncMock()
        mock_event_service.emit_error = AsyncMock()

        # Import the function after patching
        from apps.main.agents.tools.get_user_profile_tool import get_user_profile

        try:
            # Act - this should raise the ValueError
            result = await get_user_profile(input_data=input_data)
            assert False, "Expected ValueError was not raised"
        except ValueError as e:
            # Assert
            assert str(e) == "Unexpected processing error"
            # We can't check if emit_error was called since the exception was raised
            # But we can verify the exception was raised with the expected message
            pass
