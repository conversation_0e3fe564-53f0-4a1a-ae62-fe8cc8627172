# backend/apps/main/tests/test_agents/agent_test_env.py
"""
Isolated environment setup for agent testing without Django dependencies.
This provides comprehensive mocking of Django components.
"""

import sys
import os
from unittest.mock import MagicMock
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Removed global tracking for sys.modules backups

# --- Define Mock Model Base Class ---
class _MockModelBase: # Renamed to avoid potential name clashes
    objects = MagicMock()
    DoesNotExist = type('DoesNotExist', (Exception,), {})

    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def save(self, *args, **kwargs): pass
    def delete(self, *args, **kwargs): pass
    def __str__(self): return f"MockModel-{id(self)}"

    # Make this a proper type for isinstance() checks
    def __class_getitem__(cls, item):
        return cls

# --- Define Mock Model Classes (Singletons at module level) ---
class _MockGenericAgent(_MockModelBase):
    objects = MagicMock()
    objects.get = MagicMock(return_value=MagicMock(
        role='test_role', system_instructions='Test instructions',
        recommend_models=[], read_models=[], write_models=[]
    ))
    def get_available_tools_dict(self): return []

class _MockAgentRun(_MockModelBase):
    objects = MagicMock()
    objects.create = MagicMock(return_value=MagicMock(id='mock-run-id'))
    objects.get = MagicMock(return_value=MagicMock(id='mock-run-id', status='running'))

class _MockAgentMemory(_MockModelBase):
    objects = MagicMock()
    # Set initial side_effect for DoesNotExist
    objects.get = MagicMock(side_effect=_MockModelBase.DoesNotExist)
    objects.create = MagicMock(return_value=MagicMock(content={}))
    objects.update_or_create = MagicMock(return_value=(MagicMock(content={}), True))

_MockAgentRole = MagicMock(MENTOR='mentor', STRATEGY='strategy', DISPATCHER='dispatcher')

class _MockUserProfile(_MockModelBase):
    objects = MagicMock()
    objects.get = MagicMock(return_value=MagicMock(id='test-user-id', profile_name='Test User'))

# --- setup_django_mocks remains largely the same ---
def setup_django_mocks():
    """Set up comprehensive Django mocks for testing"""
    # Create mock for django settings
    django_settings = MagicMock()
    django_settings.INSTALLED_APPS = [
        'django.contrib.contenttypes',
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'apps.main',
        'apps.user',
        'apps.activity',
    ]
    
    # Create mock for django.conf.settings
    django_conf = MagicMock()
    django_conf.settings = django_settings
    # sys.modules['django.conf'] = django_conf # Replaced by monkeypatch in fixture
    
    # Create the apps registry mock
    django_apps = MagicMock()
    django_apps.apps = MagicMock()
    django_apps.apps.get_containing_app_config.return_value = MagicMock()
    django_apps.apps.is_installed.return_value = True
    django_apps.apps.get_models.return_value = []
    # sys.modules['django.apps'] = django_apps # Replaced by monkeypatch in fixture
    
    # Mock django.db
    django_db = MagicMock()
    django_db.DEFAULT_DB_ALIAS = 'default' # Add this line
    django_db.models = MagicMock()
    
    # Create common model field types
    field_types = [
        'CharField', 'TextField', 'IntegerField', 'BooleanField',
        'DateTimeField', 'DateField', 'AutoField', 'UUIDField', 
        'JSONField', 'ForeignKey', 'ManyToManyField', 'OneToOneField',
        'GenericForeignKey', 'GenericRelation'
    ]
    
    for field_type in field_types:
        setattr(django_db.models, field_type, lambda **kwargs: MagicMock())
    
    # Create model base class that works with isinstance()
    class MockModel:
        objects = MagicMock()
        DoesNotExist = type('DoesNotExist', (Exception,), {})

        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

        def save(self, *args, **kwargs):
            pass

        def delete(self, *args, **kwargs):
            pass

        def __str__(self):
            return f"MockModel-{id(self)}"

    # Use MockModel instead of _MockModelBase to avoid isinstance() issues
    # MockModel is a proper class that can be used with isinstance()
    django_db.models.Model = MockModel
    django_db.models.QuerySet = MagicMock()
    django_db.models.Q = MagicMock()
    django_db.models.F = MagicMock()
    
    # Mock transaction management
    django_db.transaction = MagicMock()
    
    # Create proper context manager for transaction.atomic
    class MockAtomic:
        def __enter__(self):
            return None
            
        def __exit__(self, exc_type, exc_val, exc_tb):
            return False
    
    django_db.transaction.atomic = lambda: MockAtomic()
    
    # sys.modules['django.db'] = django_db # Replaced by monkeypatch in fixture
    # sys.modules['django.db.models'] = django_db.models # Replaced by monkeypatch in fixture
    # sys.modules['django.db.transaction'] = django_db.transaction # Replaced by monkeypatch in fixture
    
    # Mock django.utils
    django_utils = MagicMock()
    django_utils.timezone = MagicMock()
    django_utils.timezone.now = MagicMock(return_value='2023-01-01T00:00:00Z')
    # sys.modules['django.utils'] = django_utils # Replaced by monkeypatch in fixture
    # sys.modules['django.utils.timezone'] = django_utils.timezone # Replaced by monkeypatch in fixture
    
    # Mock Django exceptions
    django_core = MagicMock()
    django_core.exceptions = MagicMock()
    django_core.exceptions.ImproperlyConfigured = type('ImproperlyConfigured', (Exception,), {})
    django_core.exceptions.ValidationError = type('ValidationError', (Exception,), {})
    django_core.exceptions.ObjectDoesNotExist = type('ObjectDoesNotExist', (Exception,), {})
    # sys.modules['django.core'] = django_core # Commenting out again - causes collection issues
    # sys.modules['django.core.exceptions'] = django_core.exceptions # Commenting out again
    
    # Mock ContentType
    django_contenttypes = MagicMock()
    django_contenttypes.models = MagicMock()
    
    class MockContentType(MockModel):
        objects = MagicMock()
        objects.get_for_model = MagicMock(return_value=MagicMock(id=1))
        objects.get = MagicMock(return_value=MagicMock(id=1))
    
    django_contenttypes.models.ContentType = MockContentType
    mock_django_contrib = MagicMock() # Need a reference for nested modules
    # sys.modules['django.contrib'] = mock_django_contrib # Replaced by monkeypatch in fixture
    # sys.modules['django.contrib.contenttypes'] = django_contenttypes # Replaced by monkeypatch in fixture
    # sys.modules['django.contrib.contenttypes.models'] = django_contenttypes.models # Replaced by monkeypatch in fixture
    # sys.modules['django.contrib.contenttypes.fields'] = MagicMock() # Replaced by monkeypatch in fixture
    
    # Mock settings configuration
    def mock_settings_configure(**options):
        for key, value in options.items():
            setattr(django_settings, key, value)
    
    django_conf.settings.configure = mock_settings_configure
    django_conf.settings.configure = mock_settings_configure
    
    logger.info("Django mocking setup complete")
    
    # Return a dictionary of modules to be patched
    return {
        'django.conf': django_conf,
        'django.apps': django_apps,
        'django.db': django_db,
        'django.db.models': django_db.models,
        'django.db.transaction': django_db.transaction,
        'django.utils': django_utils,
        'django.utils.timezone': django_utils.timezone,
        'django.contrib': mock_django_contrib, # Use the reference
        'django.contrib.contenttypes': django_contenttypes,
        'django.contrib.contenttypes.models': django_contenttypes.models,
        'django.contrib.contenttypes.fields': MagicMock(), # Create mock directly here
        'django.contrib.auth': MagicMock(), # Create mock directly here
        'django.contrib.auth.models': MagicMock() # Create mock directly here
    # Note: app models (apps.main.models etc.) are handled in create_app_models now
    }

# Removed _backup_original_module function

def setup_agent_test_env():
    """Set up the isolated test environment for agent tests."""
    # Configure environment variable to indicate testing
    os.environ['TESTING'] = 'true'

    # Set up Django core mocks and get the dictionary of mocks to apply
    # Note: This function no longer directly modifies sys.modules
    core_mocks_to_apply = setup_django_mocks()

    # create_app_models is now called by the fixture directly to get app model mocks

    logger.info("Agent test environment core setup prepared")
    return core_mocks_to_apply # Return only the dictionary for core mocks

# Removed cleanup_agent_test_env function (monkeypatch handles cleanup)


def get_test_seed_path():
    """Get the path to test seed data."""
    return os.path.join(os.path.dirname(__file__), 'test_data')

def create_app_models():
    """Create mock app modules and assign singleton mock model classes."""
    # Create mock modules, don't assign to sys.modules here
    main_models = MagicMock()
    user_models = MagicMock()
    activity_models = MagicMock()

    # Assign singleton mock classes (defined at module level) to attributes
    main_models.GenericAgent = _MockGenericAgent
    main_models.AgentRun = _MockAgentRun
    main_models.AgentMemory = _MockAgentMemory
    main_models.AgentRole = _MockAgentRole

    user_models.UserProfile = _MockUserProfile

    # activity_models.Activity = _MockActivity # Assign if defined

    # Return the dictionary of mock app model modules for the fixture to patch
    return {
        'apps.main.models': main_models,
        'apps.user.models': user_models,
        'apps.activity.models': activity_models,
    }

def reset_mock_models():
    """Reset mocks on the singleton model classes."""
    models_to_reset = [
        _MockGenericAgent, _MockAgentRun, _MockAgentMemory, _MockUserProfile # Add others like _MockActivity if defined
    ]
    logger.debug(f"Resetting mocks for {len(models_to_reset)} model classes.")
    for model_class in models_to_reset:
        if hasattr(model_class, 'objects') and hasattr(model_class.objects, 'reset_mock'):
            model_class.objects.reset_mock()
            # Reset specific mock methods if needed, e.g., get, create
            if hasattr(model_class.objects.get, 'reset_mock'):
                 model_class.objects.get.reset_mock()
                 # Re-apply side_effect if needed, e.g., for AgentMemory.get
                 if model_class is _MockAgentMemory:
                     model_class.objects.get.side_effect = _MockModelBase.DoesNotExist
                 elif model_class is _MockGenericAgent:
                     model_class.objects.get.return_value=MagicMock(
                         role='test_role', system_instructions='Test instructions',
                         recommend_models=[], read_models=[], write_models=[]
                     )
                 elif model_class is _MockAgentRun:
                      model_class.objects.get.return_value=MagicMock(id='mock-run-id', status='running')
                 elif model_class is _MockUserProfile:
                      model_class.objects.get.return_value=MagicMock(id='test-user-id', profile_name='Test User')


            if hasattr(model_class.objects.create, 'reset_mock'):
                 model_class.objects.create.reset_mock()
                 # Re-apply return_value if needed
                 if model_class is _MockAgentRun:
                     model_class.objects.create.return_value=MagicMock(id='mock-run-id')
                 elif model_class is _MockAgentMemory:
                     model_class.objects.create.return_value=MagicMock(content={})

            if hasattr(model_class.objects.update_or_create, 'reset_mock'):
                 model_class.objects.update_or_create.reset_mock()
                 # Re-apply return_value if needed
                 if model_class is _MockAgentMemory:
                     model_class.objects.update_or_create.return_value=(MagicMock(content={}), True)
        else:
             logger.warning(f"Could not reset mock for model class: {model_class.__name__}")


# Removed automatic initialization: setup/cleanup should be managed by the fixture
