# backend/apps/main/tests/test_agents/test_basic_setup.py
"""
Basic test to verify the agent test environment is correctly set up.
"""

import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from apps.main.agents.base_agent import LangGraphAgent

# Force agent test environment to be imported first
from apps.main.tests.test_agents.test_env import setup_agent_test_env

setup_agent_test_env()

# Create a simple agent class for testing
class SimpleTestAgent(LangGraphAgent):
    def __init__(self, user_profile_id, db_service=None, llm_client=None, llm_config=None):
        # Remove db_service from super call as it's not expected by LangGraphAgent base class
        super().__init__(user_profile_id=user_profile_id, agent_role="simple_test_agent", llm_config=llm_config)
        self.user_profile_id = user_profile_id
        self.db_service = db_service or MagicMock() # Keep db_service as instance attribute if needed by test logic
        self.llm_client = llm_client or AsyncMock()

    async def run(self, input_data, state):
        # Simple mock implementation
        return {"result": "success", "input_processed": True}, state

@pytest.mark.asyncio
async def test_basic_agent_setup():
    """Test that we can create and run a basic agent without Django dependencies."""
    # Create agent
    llm_config = MagicMock()
    agent = SimpleTestAgent("test-user-id", llm_config=llm_config)

    # Run the agent
    input_data = {"test": "data"}
    state = {"initial": "state"}
    
    output, updated_state = await agent.run(input_data, state)
    
    # Verify output
    assert "result" in output
    assert output["result"] == "success"
    assert output["input_processed"] is True
    
    # State should be unchanged in this simple test
    assert updated_state == state

@pytest.mark.test_type("setup")
@pytest.mark.component("testing.environment")
@pytest.mark.asyncio
async def test_with_agent_runner(agent_runner):
    """Test that the agent_runner fixture works correctly."""
    # Create test runner for the SimpleTestAgent
    runner = agent_runner(SimpleTestAgent)
    
    # Set up state
    state = MagicMock()
    state.context_packet = {"user_id": "test-user-id", "text": "test message"}
    
    # Define mock responses
    mock_llm_responses = {
        "test message": {"content": "This is a mock LLM response"}
    }
    
    # Run the test with mocked dependencies
    try:
        # Set up agent and dependencies
        agent, llm_service, db_service, tool_registry = await runner.setup(
            user_profile_id="test-user-id",
            mock_llm_responses=mock_llm_responses
        )
        
        # Verify the agent was created
        assert agent is not None
        assert agent.user_profile_id == "test-user-id"
        
        # Verify the LLM service was configured
        assert llm_service is not None
        
        # Verify the database service was configured
        assert db_service is not None
        
        # Verify the tool registry was configured
        assert tool_registry is not None
        
        # Create a simple method for testing
        async def simple_method():
            return {"result": "success"}
        
        # Patch the run method for testing
        with patch.object(agent, 'run', AsyncMock(return_value=({"result": "success"}, {}))):
            # Call the agent's run method
            output, _ = await agent.run({}, {})
            
            # Verify the output
            assert "result" in output
            assert output["result"] == "success"
            
    finally:
        # Clean up
        runner.cleanup()

@pytest.mark.test_type("setup")
@pytest.mark.component("testing.environment")
@pytest.mark.asyncio
async def test_with_extracted_definitions(agent_runner_with_extracted_definitions):
    """Test that the agent_runner_with_extracted_definitions fixture works correctly."""
    # Create test runner with extracted definitions
    runner = agent_runner_with_extracted_definitions(SimpleTestAgent)
    
    # Set up state
    state = MagicMock()
    state.context_packet = {"user_id": "test-user-id", "text": "test message"}
    
    try:
        # Set up agent with extracted definitions
        agent, llm_service, db_service, tool_registry = await runner.setup(
            user_profile_id="test-user-id"
        )
        
        # Verify the agent was created
        assert agent is not None
        assert agent.user_profile_id == "test-user-id"
        
        # Verify the database service has extracted definitions loaded
        assert db_service is not None
        assert hasattr(db_service, 'agent_definitions')
        assert len(db_service.agent_definitions) > 0
        
        # Verify the tool registry was configured
        assert tool_registry is not None
        
    finally:
        # Clean up
        runner.cleanup()
