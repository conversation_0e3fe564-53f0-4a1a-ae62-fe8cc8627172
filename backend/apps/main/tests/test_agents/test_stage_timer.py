import pytest
import time
import asyncio
from collections import defaultdict

# Assuming StageTimer is in this path based on previous steps
from apps.main.agents.benchmarking import StageTimer

@pytest.fixture
def timer():
    """Provides a fresh StageTimer instance for each test."""
    return StageTimer()

def test_stage_timer_initialization(timer):
    """Test that the timer initializes with empty dictionaries."""
    assert timer._start_times == {}
    assert timer._timings == defaultdict(list)
    assert timer.get_timings() == {}

async def wait_briefly(duration=0.01):
    """Helper async function to simulate work."""
    await asyncio.sleep(duration)

@pytest.mark.asyncio
async def test_stage_timer_start_stop_single_stage(timer):
    """Test starting and stopping a single stage."""
    stage_name = "test_stage_1"
    timer.start(stage_name)
    await wait_briefly(0.02) # Simulate work for ~20ms
    timer.stop(stage_name)

    timings = timer.get_timings()
    assert stage_name in timings
    assert isinstance(timings[stage_name], list)
    assert len(timings[stage_name]) == 1
    assert timings[stage_name][0] > 0.019 and timings[stage_name][0] < 0.03 # Check duration is roughly correct

    # Check internal state
    assert stage_name not in timer._start_times # Start time should be removed after stop

@pytest.mark.asyncio
async def test_stage_timer_multiple_stages(timer):
    """Test timing multiple stages sequentially."""
    stage1 = "stage_a"
    stage2 = "stage_b"

    timer.start(stage1)
    await wait_briefly(0.01)
    timer.stop(stage1)

    timer.start(stage2)
    await wait_briefly(0.015)
    timer.stop(stage2)

    timings = timer.get_timings()
    assert stage1 in timings
    assert stage2 in timings
    assert len(timings[stage1]) == 1
    assert len(timings[stage2]) == 1
    assert timings[stage1][0] > 0.009 and timings[stage1][0] < 0.02
    assert timings[stage2][0] > 0.014 and timings[stage2][0] < 0.025

@pytest.mark.asyncio
async def test_stage_timer_overlapping_stages(timer):
    """Test timing overlapping stages (though typically used sequentially)."""
    outer_stage = "outer"
    inner_stage = "inner"

    timer.start(outer_stage)
    await wait_briefly(0.01)
    timer.start(inner_stage)
    await wait_briefly(0.015)
    timer.stop(inner_stage)
    await wait_briefly(0.01)
    timer.stop(outer_stage)

    timings = timer.get_timings()
    assert outer_stage in timings
    assert inner_stage in timings
    assert len(timings[outer_stage]) == 1
    assert len(timings[inner_stage]) == 1
    # Inner should be ~15ms, Outer should be ~35ms
    assert timings[inner_stage][0] > 0.014 and timings[inner_stage][0] < 0.025
    assert timings[outer_stage][0] > 0.034 and timings[outer_stage][0] < 0.045

@pytest.mark.asyncio
async def test_stage_timer_multiple_calls_same_stage(timer):
    """Test starting and stopping the same stage multiple times."""
    stage_name = "repeated_stage"

    timer.start(stage_name)
    await wait_briefly(0.01)
    timer.stop(stage_name)

    timer.start(stage_name)
    await wait_briefly(0.015)
    timer.stop(stage_name)

    timings = timer.get_timings()
    assert stage_name in timings
    assert isinstance(timings[stage_name], list)
    assert len(timings[stage_name]) == 2
    assert timings[stage_name][0] > 0.009 and timings[stage_name][0] < 0.02
    assert timings[stage_name][1] > 0.014 and timings[stage_name][1] < 0.025

def test_stage_timer_stop_without_start(timer, caplog):
    """Test stopping a timer that wasn't started."""
    stage_name = "not_started"
    timer.stop(stage_name) # Should log a warning but not raise error

    assert stage_name not in timer.get_timings()
    assert f"Attempted to stop timer for stage '{stage_name}' which was not started" in caplog.text

def test_stage_timer_stop_twice(timer, caplog):
    """Test stopping a timer twice."""
    stage_name = "stopped_twice"
    timer.start(stage_name)
    time.sleep(0.01)
    timer.stop(stage_name)
    timer.stop(stage_name) # Second stop should log warning

    timings = timer.get_timings()
    assert stage_name in timings
    assert len(timings[stage_name]) == 1 # Should only record the first stop
    assert f"Attempted to stop timer for stage '{stage_name}' which was not started or already stopped." in caplog.text

def test_stage_timer_reset(timer):
    """Test the reset functionality."""
    stage1 = "stage1"
    stage2 = "stage2"

    timer.start(stage1)
    time.sleep(0.01)
    timer.start(stage2) # Leave one running
    timer.stop(stage1)

    assert stage1 in timer.get_timings()
    assert stage2 in timer._start_times

    timer.reset()

    assert timer.get_timings() == {}
    assert timer._start_times == {}

def test_get_timings_returns_copy(timer):
    """Test that get_timings returns a copy, not the internal dict."""
    stage = "copy_test"
    timer.start(stage)
    time.sleep(0.01)
    timer.stop(stage)

    timings1 = timer.get_timings()
    assert stage in timings1
    timings1["new_stage"] = [1.0] # Modify the returned dict

    timings2 = timer.get_timings()
    assert stage in timings2
    assert "new_stage" not in timings2 # Internal dict should be unchanged
