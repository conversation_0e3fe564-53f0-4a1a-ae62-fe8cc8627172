# backend/apps/main/tests/test_agents/base.py
import os
import pytest
from typing import Type, Dict, Any, Tuple, List
from unittest.mock import AsyncMock, patch

from apps.main.testing import AgentTestCase, TestMode
# Updated imports for mock services and extractors
from apps.main.testing.mock_database_service import MockDatabaseService
from apps.main.testing.mock_llm_service import MockLLMService
from apps.main.testing.definition_extractors import AgentDefinitionsExtractor, ToolDefinitionsExtractor

class BaseAgentTest:
    """Base class for all agent tests with standardized setup."""

    # Create singleton extractors for test session
    _agent_extractor = None
    _tool_extractor = None
    
    @classmethod
    def get_agent_extractor(cls):
        """Get the singleton agent extractor, creating it if needed"""
        if cls._agent_extractor is None:
            cls._agent_extractor = AgentDefinitionsExtractor()
            cls._agent_extractor.extract_definitions()
            cls._agent_extractor.extract_tool_mappings()
        return cls._agent_extractor
    
    @classmethod
    def get_tool_extractor(cls):
        """Get the singleton tool extractor, creating it if needed"""
        if cls._tool_extractor is None:
            cls._tool_extractor = ToolDefinitionsExtractor()
            cls._tool_extractor.extract_definitions()
        return cls._tool_extractor
    
    @pytest.fixture
    def configured_agent(self):
        """
        Configure an agent for testing with real LLM and extracted definitions.
        
        Returns an async function that configures an agent with only tool responses mocked.
        """
        async def _configure(agent_class, tool_responses=None):
            """
            Configure an agent for testing.
            
            Args:
                agent_class: The agent class to instantiate
                tool_responses: Optional dict of custom tool responses
                
            Returns:
                tuple: (agent, tool_mock, patches)
            """
            # Get or create extractors
            agent_extractor = self.get_agent_extractor()
            tool_extractor = self.get_tool_extractor()

            # Import necessary classes (MockDatabaseService already imported at top level)
            # from apps.main.testing.mocks import MockDatabaseService # Removed redundant import
            from apps.main.llm.client import LLMClient

            # Create database service with extracted definitions
            db_service = MockDatabaseService(
                use_extracted_definitions=True,
                use_extracted_tools=True
            )
            
            # Force load definitions and mappings if needed
            if not db_service.agent_definitions:
                db_service.agent_definitions = agent_extractor.agent_definitions
            if not db_service.agent_tool_mappings:
                db_service.agent_tool_mappings = agent_extractor.agent_tool_mappings
                db_service.common_tools = agent_extractor.common_tools
            if not db_service.tool_definitions:
                db_service.tool_definitions = tool_extractor.tool_definitions
                db_service.tool_responses = tool_extractor.tool_mock_responses
            
            # Create the real LLM client
            api_key = os.environ.get("MISTRAL_API_KEY")
            if not api_key:
                raise ValueError("MISTRAL_API_KEY environment variable not found. Required for real LLM testing.")
            
            llm_client = LLMClient(api_key=api_key)
            
            # Create agent instance
            user_profile_id = "test-user-id"
            agent = agent_class(
                user_profile_id=user_profile_id,
                db_service=db_service,
                llm_client=llm_client
            )
            
            # Configure tool mock
            tool_mock = AsyncMock()
            
            async def tool_side_effect(input_data, **kwargs):
                input_str = str(input_data)
                
                # Generic defaults for different common tasks
                if "extract context" in input_str.lower():
                    return {
                        "user_context": {
                            "mood": "focused",
                            "environment": "home",
                            "time_availability": "30 minutes"
                        }
                    }
                elif "activity" in input_str.lower():
                    return {
                        "user_response": "Here's a great activity suggestion!",
                        "activity_details": {
                            "name": "Test Activity",
                            "duration": 30
                        }
                    }
                
                # Most generic fallback
                return {
                    "content": "This is a mock LLM response.",
                    "user_response": "This is a mock user response." 
                }
            
            tool_mock.side_effect = tool_side_effect
            
            # Apply patches
            patches = [
                patch.object(agent, '_call_tool', tool_mock)
            ]
            
            # Start patches
            for p in patches:
                p.start()
            
            return agent, tool_mock, patches
            
        # Return the async function directly
        return _configure
    
    async def _setup_llm_client(self, use_real_llm, llm_responses):
        """Set up a real or mock LLM client."""
        if use_real_llm:
            # Try to load a real LLM client
            api_key = os.environ.get("OPENAI_API_KEY") or os.environ.get("ANTHROPIC_API_KEY")
            
            if not api_key:
                raise EnvironmentError(
                    "Real LLM requested but no API key found. "
                    "Please set OPENAI_API_KEY or ANTHROPIC_API_KEY environment variable."
                )
            
            try:
                # Import your actual LLM client
                from apps.main.llm.client import LLMClient
                return LLMClient(api_key=api_key)
            except Exception as e:
                raise RuntimeError(f"Failed to initialize real LLM client: {str(e)}")
        else:
            # Create a mock LLM client
            llm_client = AsyncMock()
            
            # Configure the mock responses
            async def mock_chat_completion(messages, **kwargs):
                # Extract user message
                user_message = next((m.get("content", "") for m in messages if m.get("role") == "user"), "")
                
                # Find matching response pattern
                if llm_responses:
                    for pattern, response in llm_responses.items():
                        if pattern in user_message:
                            return response
                
                # Default response
                return {"content": "Mock LLM response"}
            
            llm_client.chat_completion = AsyncMock(side_effect=mock_chat_completion)
            return llm_client
    
    def _configure_llm(self, use_real_llm, llm_responses):
        """Configure the LLM client for testing."""
        # Implementation here
