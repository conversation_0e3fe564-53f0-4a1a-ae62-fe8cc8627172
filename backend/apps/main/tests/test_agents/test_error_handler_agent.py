import pytest
import os
from pydantic import BaseModel

# Set environment variable to indicate we're testing
os.environ['TESTING'] = 'true'

# Import the MockRunFailedError directly - this should be safe as it doesn't depend on Django models
from apps.main.testing.mock_database_service import MockRunFailedError

# Mark all tests in this module as requiring the Django database
pytestmark = pytest.mark.django_db

class State(BaseModel):
    """Test state for error handler agent."""
    workflow_id: str = "test-workflow-id"
    user_profile_id: str = "test-user-123"
    error: str = "Test error message"
    last_agent: str = "activity"
    current_stage: str = "activity_selection"
    context_packet: dict = {}
    output_data: dict = {}
    next_agent: str = None
    error_context: dict = None
    completed: bool = False
    retry_count: int = 0

@pytest.mark.asyncio
@pytest.mark.test_type("integration")
@pytest.mark.component("main.agents.error_handler")
@pytest.mark.agent("ErrorHandlerAgent")
async def test_error_handler_recoverable_error(agent_runner):
    """
    Test that the error handler agent correctly processes a recoverable error.

    This test validates that the error handler agent:
    1. Analyzes a transient/recoverable error
    2. Creates a recovery plan to retry with appropriate agent
    3. Sets up the correct state for recovery
    4. Routes to the appropriate agent for retry
    """
    # Create test runner for the ErrorHandlerAgent using the agent name
    # This avoids importing the ErrorHandlerAgent class directly
    runner = agent_runner("ErrorHandlerAgent")

    # Mock LLM responses
    mock_llm_responses = {
        "recoverable": {
            "error_analysis": {
                "error_type": "transient",
                "severity": "low",
                "can_recover": True,
                "recovery_path": "activity",
                "failed_agent": "activity",
                "failed_stage": "activity_selection"
            },
            "recovery_plan": {
                "type": "agent_retry",
                "target_agent": "activity",
                "instructions": "Retry processing from activity agent",
                "state_modifications": {
                    "skip_validation": True,
                    "retry_count": 1
                }
            }
        }
    }

    # Set up state with a recoverable error
    state = State()
    state.error = "Temporary resource unavailable: database connection timeout"
    state.error_context = {
        "agent": "activity",
        "timestamp": "2023-10-15T14:30:00Z",
        "exception": "DatabaseConnectionError: Connection timed out"
    }

    # Run the test
    state_updates = await runner.run_test(
        state=state,
        mock_llm_responses=mock_llm_responses
    )

    output_data = state_updates['output_data']

    # Verify error was handled correctly
    assert output_data["error_handled"] == True, "Error should be marked as handled"
    assert "next_agent" in output_data, "Missing next_agent in output"
    assert output_data["next_agent"] == "activity", "Should route back to activity agent"

    # Verify recovery plan
    assert "recovery_plan" in output_data, "Missing recovery plan in output"
    recovery_plan = output_data["recovery_plan"]
    assert recovery_plan["type"] == "agent_retry", "Incorrect recovery plan type"
    assert recovery_plan["target_agent"] == "activity", "Incorrect target agent"

    # Verify state updates
    assert "error" in state_updates, "Error field should be in state updates"
    assert state_updates["error"] is None, "Error should be cleared in state updates"

    # Verify user message is set
    assert "user_message" in output_data, "Missing user message in output"
    assert "technical issue" in output_data["user_message"].lower(), "User message should mention technical issue"
    assert "try again" in output_data["user_message"].lower(), "User message should indicate retry"

@pytest.mark.asyncio
@pytest.mark.test_type("integration")
@pytest.mark.component("main.agents.error_handler")
@pytest.mark.agent("ErrorHandlerAgent")
async def test_error_handler_unrecoverable_error(agent_runner):
    """
    Test that the error handler agent correctly processes an unrecoverable error.

    This test validates that the error handler agent:
    1. Analyzes a persistent/serious error correctly
    2. Creates a graceful degradation response
    3. Properly ends the workflow
    4. Formats an appropriate message for the user
    """
    # Create test runner for the ErrorHandlerAgent using the agent name
    # This avoids importing the ErrorHandlerAgent class directly
    runner = agent_runner("ErrorHandlerAgent")

    # Mock LLM responses
    # Note: The mock LLM matches patterns against the *prompt content*.
    # The "craft a friendly message" key matches the prompt generated by _create_fallback_response.
    # The "unrecoverable" key with "error_analysis" is kept in case _analyze_error uses LLM in the future.
    mock_llm_responses = {
        "unrecoverable": { # Pattern for potential future _analyze_error LLM call
            "error_analysis": {
                "error_type": "persistent",
                "severity": "high",
                "can_recover": False,
                "recovery_path": None,
                "failed_agent": "psychological",
                "failed_stage": "psychological_assessment"
            }
            # The actual fallback response is now triggered by the pattern below
        },
        "craft a friendly message": "I'm sorry, I encountered a problem while processing your request. Let's try something different." # Pattern for _create_fallback_response LLM call
    }

    # Set up state with an unrecoverable error
    state = State()
    state.error = "Critical error: Failed to load psychological profile data"
    state.last_agent = "psychological"
    state.current_stage = "psychological_assessment"
    state.error_context = {
        "agent": "psychological",
        "timestamp": "2023-10-15T14:30:00Z",
        "exception": "DataIntegrityError: Invalid profile structure"
    }

    # Run the test
    state_updates = await runner.run_test(
        state=state,
        mock_llm_responses=mock_llm_responses
    )

    output_data = state_updates['output_data']

    # Verify error handling for unrecoverable error
    assert "error_handled" in output_data, "Missing error_handled in output"
    assert output_data["error_handled"] == False, "Unrecoverable error should be marked as not handled"
    assert "next_agent" in output_data, "Missing next_agent in output"
    assert output_data["next_agent"] == "end", "Should route to end for unrecoverable error"

    # Verify fallback response
    assert "user_response" in output_data, "Missing user_response in output"
    assert len(output_data["user_response"]) > 10, "User response too short"
    assert "sorry" in output_data["user_response"].lower(), "User response should include apology"

    # Verify state updates
    assert "completed" in state_updates, "Missing completed flag in state updates"
    assert state_updates["completed"] == True, "Workflow should be marked as completed with error"

@pytest.mark.asyncio
@pytest.mark.test_type("integration") # Still integration as it uses agent runner
@pytest.mark.component("main.agents.error_handler")
@pytest.mark.agent("ErrorHandlerAgent")
@pytest.mark.llm  # Indicates this test interacts with a real LLM
async def test_error_handler_with_real_llm(agent_runner):
    """
    Test the error handler agent with a real LLM to validate its decision-making.

    This test validates that the error handler agent:
    1. Can use a real LLM to analyze error messages and make recovery decisions
    2. Produces realistic recovery plans or degradation responses
    3. Makes appropriate routing decisions based on error characteristics

    This test uses a real LLM and only mocks the tool responses.
    """
    # Skip the test if no API key is available
    api_key = os.environ.get("MISTRAL_API_KEY") or os.environ.get("OPENAI_API_KEY")
    if not api_key:
        pytest.skip("No API key available for real LLM testing")

    # Create test runner for the ErrorHandlerAgent with real LLM
    runner = agent_runner("ErrorHandlerAgent", use_real_llm=True)

    # Set up state with a potentially recoverable network error
    state = State()
    state.error = "Network error: Failed to fetch activity data, connection reset by peer"
    state.last_agent = "activity"
    state.current_stage = "activity_selection"
    state.error_context = {
        "agent": "activity",
        "timestamp": "2023-10-15T14:30:00Z",
        "exception": "ConnectionError: Connection reset by peer"
    }

    try:
        # Run the test with real LLM
        state_updates = await runner.run_test(
            state=state
        )
        output_data = state_updates['output_data']

        # Basic validation of the output structure
        assert isinstance(output_data, dict), "Output data should be a dictionary"
        assert "error_handled" in output_data, "Missing error_handled flag in output"
        assert "next_agent" in output_data, "Missing next_agent in output"

        # Since we're using a real LLM, we can't be certain of the exact decision,
        # but we can validate the structure and reasonableness of the response

        if output_data["error_handled"]:
            # If recoverable error detected
            assert "recovery_plan" in output_data, "Missing recovery plan for handled error"
            assert "type" in output_data["recovery_plan"], "Recovery plan missing type"
            assert "target_agent" in output_data["recovery_plan"], "Recovery plan missing target agent"
            assert output_data["next_agent"] != "end", "Recoverable error shouldn't route to end"

            # Verify user message explains retry
            assert "user_message" in output_data, "Missing user message for recoverable error"
            assert len(output_data["user_message"]) > 10, "User message too short"
        else:
            # If unrecoverable error detected
            assert "user_response" in output_data, "Missing user response for unrecoverable error"
            assert len(output_data["user_response"]) > 10, "User response too short"
            assert output_data["next_agent"] == "end", "Unrecoverable error should route to end"

            # Verify state updates
            assert "completed" in state_updates, "Missing completed flag in state updates"
            assert state_updates["completed"] == True, "Workflow should be marked as completed"

    except Exception as e:
        pytest.fail(f"Test failed with real LLM: {str(e)}")
    finally:
        # Clean up
        runner.cleanup()

@pytest.mark.asyncio
@pytest.mark.test_type("integration") # Tests agent's internal error handling path
@pytest.mark.component("main.agents.error_handler")
@pytest.mark.agent("ErrorHandlerAgent")
async def test_error_handler_meta_error(agent_runner):
    """
    Test that the error handler agent correctly handles errors that occur during its own execution.

    This test validates that the error handler agent:
    1. Gracefully handles errors in its own execution
    2. Provides a simple fallback response
    3. Properly ends the workflow
    4. Records the meta-error in the workflow state
    """
    # Create test runner for the ErrorHandlerAgent using the agent name
    # This avoids importing the ErrorHandlerAgent class directly
    runner = agent_runner("ErrorHandlerAgent")

    # Set up state with an error first (needed for setup)
    state = State()
    state.error = "Original error in activity agent"
    state.last_agent = "activity"
    state.current_stage = "activity_selection"

    # Explicitly set up the runner
    # We need the agent instance to mock its method
    agent, _, _, _ = await runner.setup(user_profile_id=state.user_profile_id)

    # Mock the agent's internal _analyze_error method to raise an exception
    # This simulates an error occurring within the ErrorHandlerAgent's own logic
    async def mock_analyze_error(*args, **kwargs):
        raise Exception("Internal analysis failed")

    # Patch the method on the specific agent instance
    # Make sure the method exists before patching
    if hasattr(agent, '_analyze_error'):
        agent._analyze_error = mock_analyze_error
    else:
        # Try alternative method names that might be used for error analysis
        for method_name in ['analyze_error', 'process_error', '_process_error']:
            if hasattr(agent, method_name):
                setattr(agent, method_name, mock_analyze_error)
                break
        else:
            # If no suitable method is found, add a new method and make sure it's called
            agent._analyze_error = mock_analyze_error
            # Override the process method to ensure our mock is called
            original_process = agent.process

            async def process_with_error(*args, **kwargs):
                # Call our mock function to trigger the error
                await mock_analyze_error()
                # This should never be reached due to the exception
                return await original_process(*args, **kwargs)

            agent.process = process_with_error

    # Run the test and expect MockRunFailedError
    try:
        await runner.run_test(state=state)
        pytest.fail("Expected MockRunFailedError but no exception was raised.") # Fail if no exception
    except MockRunFailedError as e:
        # This is the expected path. Now verify the details.

        # Verify the exception message contains the meta-error details
        error_str = str(e)
        assert "mock run" in error_str.lower(), "Exception message should indicate mock run failure"
        assert "failed" in error_str.lower(), "Exception message should indicate failure status"
        assert "internal analysis failed" in error_str.lower(), "Exception should contain the mocked internal error"

        # Verify the agent's intended final state stored by MockDatabaseService
        try:
            # Get the run_id from the keys of the runs stored by the mock service
            # Assuming the latest run_id corresponds to this test execution
            assert runner.db_service.runs, "No runs recorded in mock db service"
            # Retrieve the most recently added run_id
            run_id = list(runner.db_service.runs.keys())[-1]
            assert run_id, "Could not retrieve run_id from mock db service runs dictionary"

            # Fetch the run data using the retrieved run_id
            run_data = runner.db_service.runs.get(run_id)
            assert run_data is not None, f"Run data for run_id {run_id} not found in mock db service."

            final_output_data = run_data.get('output_data', {})
            assert 'meta_error' in final_output_data, \
                "Agent's final output_data should contain a meta_error field"

            # Explicitly get the error message from the output data
            meta_error_message = final_output_data.get('meta_error', '')
            assert meta_error_message is not None, "Meta error message should not be None in output_data"

            # Check for the specific internal error message
            assert "Internal analysis failed" in meta_error_message, \
                 f"Substring 'Internal analysis failed' not found in '{meta_error_message}'"

            # Verify the agent intended to route to end in meta-error case
            assert "next_agent" in final_output_data, "Missing next_agent in output_data"
            assert final_output_data["next_agent"] == "end", "Meta-error should route to 'end'"

            # Verify error_handled is False for meta-errors
            assert "error_handled" in final_output_data, "Missing error_handled in output_data"
            assert final_output_data["error_handled"] is False, "Meta-error should have error_handled=False"

            # Verify user_response exists and is appropriate
            assert "user_response" in final_output_data, "Missing user_response in output_data"
            assert "sorry" in final_output_data["user_response"].lower(), "User response should include an apology"
            assert "internal problem" in final_output_data["user_response"].lower(), "User response should mention internal problem"

        except AttributeError:
             pytest.fail("Could not access runner.db_service.runs to verify final agent state.")
        except KeyError:
             pytest.fail(f"Run ID {run_id} not found in runner.db_service.runs.")
        except AssertionError as final_state_ae:
              pytest.fail(f"Assertion failed when checking agent's final stored state: {final_state_ae}")

@pytest.mark.asyncio
@pytest.mark.test_type("integration")
@pytest.mark.component("main.agents.error_handler")
@pytest.mark.agent("ErrorHandlerAgent")
async def test_error_handler_retry_limit(agent_runner):
    """
    Test that the error handler agent correctly handles retry limits.

    This test validates that the error handler agent:
    1. Detects when a retry count exceeds a safe threshold
    2. Switches to graceful degradation instead of infinite retries
    3. Properly ends the workflow with an appropriate message
    """
    # Create test runner for the ErrorHandlerAgent using the agent name
    # This avoids importing the ErrorHandlerAgent class directly
    runner = agent_runner("ErrorHandlerAgent")

    # Mock LLM responses for retry limit case
    mock_llm_responses = {
        "retry_limit": {
            "error_analysis": {
                "error_type": "persistent",
                "severity": "medium",
                "can_recover": False,  # Critical: LLM should detect we've hit retry limit
                "recovery_path": None,
                "failed_agent": "activity",
                "failed_stage": "activity_selection"
            },
            "fallback_response": "I've tried several times but I'm still having difficulty. Let's take a different approach."
        }
    }

    # Set up state with multiple retries already attempted
    state = State()
    state.error = "Temporary resource unavailable: database connection timeout"
    state.last_agent = "activity"
    state.current_stage = "activity_selection"
    state.retry_count = 3  # Already tried 3 times

    # Run the test
    state_updates = await runner.run_test(
        state=state,
        mock_llm_responses=mock_llm_responses
    )
    output_data = state_updates['output_data']

    # Verify retry limit handling
    assert "error_handled" in output_data, "Missing error_handled flag in output"
    assert output_data["error_handled"] == False, "Error after retry limit should be marked as not handled"
    assert "next_agent" in output_data, "Missing next_agent in output"
    assert output_data["next_agent"] == "end", "Should route to end after retry limit"

    # Verify fallback response
    assert "user_response" in output_data, "Missing user_response in output"
    assert len(output_data["user_response"]) > 10, "User response too short"
    assert "tried" in output_data["user_response"].lower() or "attempt" in output_data["user_response"].lower(), \
        "User response should indicate multiple attempts were made"

    # Verify state updates
    assert "completed" in state_updates, "Missing completed flag in state updates"
    assert state_updates["completed"] == True, "Workflow should be marked as completed after retry limit"
