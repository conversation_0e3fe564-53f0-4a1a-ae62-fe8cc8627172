# backend/apps/main/tests/test_agents/pytest.ini
[pytest]
# Disable only Django plugin, keep asyncio support
addopts = -p no:django

# Mark asyncio tests for proper handling
markers =
    asyncio: mark a test as an asyncio coroutine

# Use pytest-asyncio for handling async tests - this is critical
asyncio_mode = strict

# Configure test output
console_output_style = progress
log_cli = true
log_cli_level = INFO

# Set the Python path for imports
pythonpath = ../../../

# These tests don't use database
python_classes = Test*
python_functions = test_*