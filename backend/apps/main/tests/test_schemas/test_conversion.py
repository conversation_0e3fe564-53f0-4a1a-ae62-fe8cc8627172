"""
Tests for conversion utilities.

This module contains tests for the conversion utilities that convert between
Pydantic models and dictionaries.
"""

import pytest
from pydantic import ValidationError

from apps.main.schemas.conversion import (
    model_to_dict,
    dict_to_model,
    convert_model_list,
    convert_dict_list,
    merge_model_with_dict,
    update_model_from_dict,
    safe_dict_to_model
)
from apps.main.schemas.benchmark.scenarios import (
    BenchmarkScenario,
    BenchmarkScenarioMetadata,
    EvaluationCriterion
)
from apps.main.tests.factories import (
    BenchmarkScenarioFactory,
    BenchmarkScenarioMetadataFactory,
    EvaluationCriterionFactory
)


def test_model_to_dict():
    """Test converting a Pydantic model to a dictionary."""
    # Create a test model
    model = EvaluationCriterionFactory.build(
        dimension="Clarity",
        description="Is the response clear?",
        weight=0.8
    )
    
    # Convert to dictionary
    result = model_to_dict(model)
    
    # Verify the result
    assert isinstance(result, dict)
    assert result["dimension"] == "Clarity"
    assert result["description"] == "Is the response clear?"
    assert result["weight"] == 0.8


def test_dict_to_model():
    """Test converting a dictionary to a Pydantic model."""
    # Create a test dictionary
    data = {
        "dimension": "Clarity",
        "description": "Is the response clear?",
        "weight": 0.8
    }
    
    # Convert to model
    result = dict_to_model(data, EvaluationCriterion)
    
    # Verify the result
    assert isinstance(result, EvaluationCriterion)
    assert result.dimension == "Clarity"
    assert result.description == "Is the response clear?"
    assert result.weight == 0.8


def test_dict_to_model_validation_error():
    """Test that dict_to_model raises ValidationError for invalid data."""
    # Create an invalid dictionary (missing required field)
    data = {
        "description": "Is the response clear?",
        "weight": 0.8
    }
    
    # Attempt to convert to model
    with pytest.raises(ValidationError):
        dict_to_model(data, EvaluationCriterion)


def test_convert_model_list():
    """Test converting a list of Pydantic models to a list of dictionaries."""
    # Create test models
    models = [
        EvaluationCriterionFactory.build(dimension="Clarity", weight=0.8),
        EvaluationCriterionFactory.build(dimension="Helpfulness", weight=0.7)
    ]
    
    # Convert to list of dictionaries
    result = convert_model_list(models)
    
    # Verify the result
    assert isinstance(result, list)
    assert len(result) == 2
    assert result[0]["dimension"] == "Clarity"
    assert result[1]["dimension"] == "Helpfulness"


def test_convert_dict_list():
    """Test converting a list of dictionaries to a list of Pydantic models."""
    # Create test dictionaries
    data_list = [
        {"dimension": "Clarity", "description": "Is it clear?", "weight": 0.8},
        {"dimension": "Helpfulness", "description": "Is it helpful?", "weight": 0.7}
    ]
    
    # Convert to list of models
    result = convert_dict_list(data_list, EvaluationCriterion)
    
    # Verify the result
    assert isinstance(result, list)
    assert len(result) == 2
    assert isinstance(result[0], EvaluationCriterion)
    assert result[0].dimension == "Clarity"
    assert result[1].dimension == "Helpfulness"


def test_merge_model_with_dict():
    """Test merging a Pydantic model with a dictionary."""
    # Create a test model
    model = EvaluationCriterionFactory.build(
        dimension="Clarity",
        description="Is the response clear?",
        weight=0.8
    )
    
    # Create a dictionary with updates
    updates = {
        "weight": 0.9,
        "extra_field": "This will be added"
    }
    
    # Merge model with dictionary
    result = merge_model_with_dict(model, updates)
    
    # Verify the result
    assert isinstance(result, dict)
    assert result["dimension"] == "Clarity"
    assert result["description"] == "Is the response clear?"
    assert result["weight"] == 0.9  # Updated
    assert result["extra_field"] == "This will be added"  # Added


def test_update_model_from_dict():
    """Test updating a Pydantic model from a dictionary."""
    # Create a test model
    model = EvaluationCriterionFactory.build(
        dimension="Clarity",
        description="Is the response clear?",
        weight=0.8
    )
    
    # Create a dictionary with updates
    updates = {
        "weight": 0.9
    }
    
    # Update model from dictionary
    result = update_model_from_dict(model, updates)
    
    # Verify the result
    assert isinstance(result, EvaluationCriterion)
    assert result.dimension == "Clarity"
    assert result.description == "Is the response clear?"
    assert result.weight == 0.9  # Updated


def test_safe_dict_to_model():
    """Test safely converting a dictionary to a Pydantic model."""
    # Create a valid dictionary
    valid_data = {
        "dimension": "Clarity",
        "description": "Is the response clear?",
        "weight": 0.8
    }
    
    # Create an invalid dictionary
    invalid_data = {
        "description": "Is the response clear?",
        "weight": 0.8
    }
    
    # Create a default model
    default_model = EvaluationCriterionFactory.build(
        dimension="Default",
        description="Default description",
        weight=0.5
    )
    
    # Test with valid data
    valid_result = safe_dict_to_model(valid_data, EvaluationCriterion)
    assert isinstance(valid_result, EvaluationCriterion)
    assert valid_result.dimension == "Clarity"
    
    # Test with invalid data and no default
    invalid_result_no_default = safe_dict_to_model(invalid_data, EvaluationCriterion)
    assert invalid_result_no_default is None
    
    # Test with invalid data and default
    invalid_result_with_default = safe_dict_to_model(invalid_data, EvaluationCriterion, default=default_model)
    assert isinstance(invalid_result_with_default, EvaluationCriterion)
    assert invalid_result_with_default.dimension == "Default"


def test_mock_tool_responses_handling():
    """Test handling of mock_tool_responses in BenchmarkScenarioMetadata."""
    # Create metadata with mock_tool_responses
    metadata = BenchmarkScenarioMetadataFactory.build(
        workflow_type="test_workflow",
        mock_tool_responses={
            "get_user_profile": {
                "response": '{"id": "test-user-123", "name": "Test User"}'
            },
            "get_user_activities": {
                "response": '{"activities": [{"id": "act1", "name": "Activity 1"}]}'
            }
        }
    )
    
    # Convert to dictionary and back to model
    metadata_dict = model_to_dict(metadata)
    metadata_model = dict_to_model(metadata_dict, BenchmarkScenarioMetadata)
    
    # Verify the mock_tool_responses field
    assert "get_user_profile" in metadata_model.mock_tool_responses
    assert "response" in metadata_model.mock_tool_responses["get_user_profile"]


def test_mock_responses_backward_compatibility():
    """Test backward compatibility with mock_responses field."""
    # Create metadata with mock_responses instead of mock_tool_responses
    metadata_dict = {
        "workflow_type": "test_workflow",
        "mock_responses": {
            "get_user_profile": '{"id": "test-user-123", "name": "Test User"}',
            "get_user_activities": '{"activities": [{"id": "act1", "name": "Activity 1"}]}'
        }
    }
    
    # Convert to model
    metadata_model = dict_to_model(metadata_dict, BenchmarkScenarioMetadata)
    
    # Verify the mock_responses field is preserved
    assert metadata_model.mock_responses is not None
    assert "get_user_profile" in metadata_model.mock_responses
