import pytest
import pytest_asyncio
import uuid
import logging
from unittest.mock import AsyncMock, patch, MagicMock
from typing import Dict, Any

# Ensure the correct import path for ConversationDispatcher
from apps.main.services.conversation_dispatcher import ConversationDispatcher

# Ensure async support for tests
pytestmark = pytest.mark.asyncio

# Import the specific task function to patch its .delay method
from apps.main.tasks.agent_tasks import execute_graph_workflow

@pytest_asyncio.fixture
async def dispatcher_instance():
    """Provides a ConversationDispatcher instance for testing workflow launch."""
    user_profile_id = str(uuid.uuid4())
    return ConversationDispatcher(user_profile_id, user_ws_session_name="test-session-launch")

@pytest.mark.parametrize("workflow_type_to_launch", [
    "wheel_generation",
    "activity_feedback",
    "pre_spin_feedback",
    "user_onboarding",
    "post_spin",
    "discussion"
])
@pytest.mark.test_type("unit")
@pytest.mark.component("main.services.conversation_dispatcher")
@pytest.mark.asyncio
async def test_launch_workflow_calls_celery_task(dispatcher_instance: ConversationDispatcher, workflow_type_to_launch: str):
    """
    Tests that _launch_workflow correctly calls the Celery task's delay method
    with the expected arguments.
    """
    dispatcher = dispatcher_instance
    test_workflow_id = str(uuid.uuid4())
    test_context_packet = {
        'user_id': dispatcher.user_profile_id,
        'user_profile_id': dispatcher.user_profile_id,
        'session_timestamp': '2025-04-03T00:00:00Z',
        'reported_mood': 'test_mood',
        'user_ws_session_name': dispatcher.user_ws_session_name
        # Add other relevant fields if needed for specific workflows
    }

    # Expected input for the Celery task
    expected_initial_input = {
        "task_type": workflow_type_to_launch,
        "context_packet": test_context_packet,
        "workflow_type": workflow_type_to_launch,
        "user_ws_session_name": dispatcher.user_ws_session_name
    }

    # Patch the .delay method of the task function where it's defined
    # The dispatcher imports it from 'apps.main.tasks.agent_tasks'
    with patch('apps.main.tasks.agent_tasks.execute_graph_workflow.delay') as mock_delay:

        # Call the method under test
        dispatcher._launch_workflow(
            workflow_type_to_launch,
            test_context_packet,
            test_workflow_id
        )

        # Assertions
        mock_delay.assert_called_once_with(
            workflow_id=test_workflow_id,
            user_profile_id=dispatcher.user_profile_id,
            initial_input=expected_initial_input,
            workflow_type=workflow_type_to_launch # Assert workflow_type is passed
        )

@pytest.mark.test_type("unit")
@pytest.mark.component("main.services.conversation_dispatcher")
@pytest.mark.asyncio
async def test_launch_workflow_logs_error_on_failure(dispatcher_instance: ConversationDispatcher, caplog):
    """
    Tests that _launch_workflow logs an error if the Celery task call fails,
    but does not raise the exception itself.
    """
    # dispatcher = dispatcher_instance # Moved inside 'with' block
    test_workflow_id = str(uuid.uuid4())
    # test_context_packet = {'user_id': dispatcher.user_profile_id} # Moved inside 'with' block
    workflow_type_to_launch = "wheel_generation"
    simulated_error = Exception("Celery connection failed")

    # Patch the .delay method where it's defined to raise an error
    with patch('apps.main.tasks.agent_tasks.execute_graph_workflow.delay', side_effect=simulated_error) as mock_delay, \
            caplog.at_level(logging.ERROR): # Capture ERROR level logs

        dispatcher = dispatcher_instance
        test_context_packet = {'user_id': dispatcher.user_profile_id}

        # Call the method under test - it should catch the exception
        dispatcher._launch_workflow(
            workflow_type_to_launch,
            test_context_packet,
            test_workflow_id
        )

        # Assertions
        mock_delay.assert_called_once() # Ensure delay was attempted
        # Check that an error was logged
        assert len(caplog.records) == 1
        assert "Error launching workflow task" in caplog.text
        assert str(simulated_error) in caplog.text
        # The method itself should not raise the error
