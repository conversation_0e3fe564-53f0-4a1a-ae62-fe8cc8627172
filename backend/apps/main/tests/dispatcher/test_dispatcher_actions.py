import pytest
import uuid
from unittest.mock import AsyncMock, patch, MagicMock
from typing import Dict, Any, Optional

# Ensure the correct import path for ConversationDispatcher
from apps.main.services.conversation_dispatcher import ConversationDispatcher

# Ensure async support for tests
pytestmark = pytest.mark.asyncio

import pytest_asyncio

@pytest_asyncio.fixture
async def dispatcher_instance():
    """Provides a ConversationDispatcher instance for testing action checks."""
    user_profile_id = str(uuid.uuid4())
    return ConversationDispatcher(user_profile_id, user_ws_session_name="test-session-actions")

@pytest.mark.parametrize("workflow_type, context, expected_action", [
    # Scenario 1: Missing time availability for wheel generation
    (
        "wheel_generation",
        {"mood": "bored", "environment": "home", "time_availability": ""}, # Missing time
        {
            'type': 'gather_information',
            'missing_field': 'time_availability',
            'prompt': "Before I suggest activities, could you let me know how much time you have available right now?"
        }
    ),
    # Scenario 2: Time availability present for wheel generation
    (
        "wheel_generation",
        {"mood": "bored", "environment": "home", "time_availability": "30 minutes"}, # Time present
        None # No action required
    ),
    # Scenario 3: Missing activity_id for activity feedback
    (
        "activity_feedback",
        {"mood": "reflective", "satisfaction": "medium"}, # Missing activity_id
        {
            'type': 'gather_information',
            'missing_field': 'activity_id',
            'prompt': "I'd love to hear your feedback. Which activity are you providing feedback on?"
        }
    ),
    # Scenario 4: activity_id present for activity feedback
    (
        "activity_feedback",
        {"mood": "reflective", "satisfaction": "medium", "activity_id": "act-123"}, # ID present
        None # No action required
    ),
    # Scenario 5: Other workflow types (should not require action based on current rules)
    (
        "discussion",
        {"mood": "confused"},
        None
    ),
    (
        "user_onboarding",
        {},
        None
    ),
    (
        "pre_spin_feedback",
        {"mood": "anxious"},
        None
    ),
    (
         "post_spin",
         {"activity_id": "act-456"},
         None
    )
])
@pytest.mark.test_type("unit")
@pytest.mark.component("main.services.conversation_dispatcher")
@pytest.mark.asyncio
async def test_check_if_action_required(dispatcher_instance: ConversationDispatcher, workflow_type: str, context: Dict[str, Any], expected_action: Optional[Dict[str, Any]]):
    """
    Tests the _check_if_action_required method for various scenarios.
    """
    dispatcher = dispatcher_instance

    # Call the method directly with the test case inputs
    action = dispatcher._check_if_action_required(workflow_type, context)

    # Assertions
    assert action == expected_action
