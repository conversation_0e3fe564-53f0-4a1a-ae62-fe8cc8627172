"""
Tests for contextual evaluation integration with benchmark execution.

This module tests the end-to-end integration of contextual evaluation templates
with the benchmark execution system, ensuring that criteria are properly adapted
based on contextual variables during benchmark runs.
"""

import pytest
from asgiref.sync import sync_to_async

from apps.main.models import EvaluationCriteriaTemplate
from apps.main.services.benchmark_manager import AgentBenchmarker


@pytest.mark.django_db
@pytest.mark.asyncio
class TestContextualEvaluationIntegration:
    """Test contextual evaluation integration with benchmark execution."""

    @pytest.fixture
    async def contextual_template(self):
        """Create a contextual evaluation template for testing."""
        template, _ = await sync_to_async(EvaluationCriteriaTemplate.objects.get_or_create)(
            name="Test Contextual Template",
            defaults={
                "description": "Test template for contextual evaluation",
                "workflow_type": "wheel_generation",
                "category": "contextual",
                "criteria": {
                    "Content": ["Relevance", "Personalization"],
                    "Tone": ["Appropriate", "Supportive"]
                },
                "contextual_criteria": {
                "trust_level": {
                    "0-39": {
                        "Tone": ["Simple", "Clear", "Reassuring"],
                        "Content": ["Safe options", "Low-risk activities"]
                    },
                    "40-69": {
                        "Tone": ["Encouraging", "Supportive"],
                        "Content": ["Balanced options", "Some stretch goals"]
                    },
                    "70-100": {
                        "Tone": ["Collaborative", "Empowering"],
                        "Content": ["Ambitious goals", "Creative challenges"]
                    }
                },
                "mood": {
                    "valence": {
                        "-1.0-0.0": {
                            "Tone": ["Gentle", "Understanding", "Patient"]
                        },
                        "0.0-1.0": {
                            "Tone": ["Enthusiastic", "Energetic", "Positive"]
                        }
                    }
                },
                "environment": {
                    "stress_level": {
                        "0-30": {
                            "Approach": ["Detailed", "Comprehensive"]
                        },
                        "71-100": {
                            "Approach": ["Concise", "Essential-only"]
                        }
                    }
                }
                },
                "variable_ranges": {
                "trust_level": {"min": 0, "max": 100},
                "mood": {
                    "valence": {"min": -1.0, "max": 1.0},
                    "arousal": {"min": -1.0, "max": 1.0}
                },
                "environment": {
                    "stress_level": {"min": 0, "max": 100},
                    "time_pressure": {"min": 0, "max": 100}
                }
                },
                "is_active": True
            }
        )
        return template

    async def test_criteria_adaptation_high_trust(self, contextual_template):
        """Test criteria adaptation for high trust level."""
        manager = AgentBenchmarker()

        template_dict = {
            'criteria': {"Content": ["Relevance"], "Tone": ["Appropriate"]},
            'contextual_criteria': contextual_template.contextual_criteria
        }

        context = {
            "trust_level": 85,
            "mood": {"valence": 0.5},
            "environment": {"stress_level": 20}
        }

        adapted_criteria = manager._adapt_criteria_for_context(template_dict, context)

        # Should include high trust adaptations
        assert "Collaborative" in adapted_criteria["Tone"]
        assert "Empowering" in adapted_criteria["Tone"]
        assert "Ambitious goals" in adapted_criteria["Content"]
        assert "Creative challenges" in adapted_criteria["Content"]

        # Should include positive mood adaptations
        assert "Enthusiastic" in adapted_criteria["Tone"]
        assert "Energetic" in adapted_criteria["Tone"]
        assert "Positive" in adapted_criteria["Tone"]

        # Should include low stress adaptations
        assert "Detailed" in adapted_criteria["Approach"]
        assert "Comprehensive" in adapted_criteria["Approach"]

    async def test_criteria_adaptation_low_trust(self, contextual_template):
        """Test criteria adaptation for low trust level."""
        manager = AgentBenchmarker()

        template_dict = {
            'criteria': {"Content": ["Relevance"], "Tone": ["Appropriate"]},
            'contextual_criteria': contextual_template.contextual_criteria
        }

        context = {
            "trust_level": 25,
            "mood": {"valence": -0.5},
            "environment": {"stress_level": 80}
        }

        adapted_criteria = manager._adapt_criteria_for_context(template_dict, context)

        # Should include low trust adaptations
        assert "Simple" in adapted_criteria["Tone"]
        assert "Clear" in adapted_criteria["Tone"]
        assert "Reassuring" in adapted_criteria["Tone"]
        assert "Safe options" in adapted_criteria["Content"]
        assert "Low-risk activities" in adapted_criteria["Content"]

        # Should include negative mood adaptations
        assert "Gentle" in adapted_criteria["Tone"]
        assert "Understanding" in adapted_criteria["Tone"]
        assert "Patient" in adapted_criteria["Tone"]

        # Should include high stress adaptations
        assert "Concise" in adapted_criteria["Approach"]
        assert "Essential-only" in adapted_criteria["Approach"]

    async def test_range_parsing(self):
        """Test range parsing for different value types."""
        manager = AgentBenchmarker()

        # Test integer ranges
        assert manager._value_in_range(25, "0-39") == True
        assert manager._value_in_range(50, "40-69") == True
        assert manager._value_in_range(85, "70-100") == True
        assert manager._value_in_range(35, "40-69") == False

        # Test float ranges
        assert manager._value_in_range(0.5, "0.0-1.0") == True
        assert manager._value_in_range(-0.5, "-1.0-0.0") == True
        assert manager._value_in_range(1.5, "0.0-1.0") == False

        # Test edge cases
        assert manager._value_in_range(0, "0-39") == True
        assert manager._value_in_range(39, "0-39") == True
        assert manager._value_in_range(40, "0-39") == False

    async def test_criteria_merging(self):
        """Test criteria merging logic."""
        manager = AgentBenchmarker()

        base_criteria = {
            "Content": ["Relevance", "Accuracy"],
            "Tone": ["Appropriate"]
        }

        adaptations = {
            "Tone": ["Supportive", "Encouraging"],
            "Approach": ["Detailed"]
        }

        merged = manager._merge_criteria(base_criteria, adaptations)

        # Should extend existing dimensions
        assert "Relevance" in merged["Content"]
        assert "Accuracy" in merged["Content"]
        assert "Appropriate" in merged["Tone"]
        assert "Supportive" in merged["Tone"]
        assert "Encouraging" in merged["Tone"]

        # Should add new dimensions
        assert "Detailed" in merged["Approach"]
