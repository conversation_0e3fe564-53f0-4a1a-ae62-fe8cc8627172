"""
Tests for the schema version manager.
"""

import unittest
import json
import os
import tempfile
from apps.main.services.schema_version_manager import SchemaVersionManager
from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_migration_utility import SchemaMigrationUtility


class TestSchemaVersionManager(unittest.TestCase):
    """Test cases for the SchemaVersionManager class."""

    def setUp(self):
        """Set up test fixtures."""
        self.schema_registry = SchemaRegistry()
        self.version_manager = SchemaVersionManager(self.schema_registry)
        self.migration_utility = SchemaMigrationUtility()

        # Create test schemas
        self.schema_v1 = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "TestSchema v1.0.0",
            "$version": "1.0.0",
            "type": "object",
            "required": ["name"],
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer", "minimum": 0}
            }
        }

        self.schema_v2 = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "TestSchema v2.0.0",
            "$version": "2.0.0",
            "type": "object",
            "required": ["name", "email"],
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer", "minimum": 0},
                "email": {"type": "string", "format": "email"}
            }
        }

        # Create test data
        self.data_v1 = {
            "name": "John Doe",
            "age": 30
        }

        self.data_v2 = {
            "name": "John Doe",
            "age": 30,
            "email": "<EMAIL>"
        }

        # Define migration functions
        def migrate_v1_to_v2(data):
            """Migrate data from v1 to v2 by adding a default email."""
            result = data.copy()
            if "email" not in result:
                result["email"] = f"{result['name'].lower().replace(' ', '.')}@example.com"
            return result

        def migrate_v2_to_v1(data):
            """Migrate data from v2 to v1 by removing email."""
            result = data.copy()
            if "email" in result:
                del result["email"]
            return result

        # Register migrations
        self.migration_utility.register_migration(
            "test_schema", "1.0.0", "2.0.0", migrate_v1_to_v2
        )
        self.migration_utility.register_migration(
            "test_schema", "2.0.0", "1.0.0", migrate_v2_to_v1
        )

    def test_register_schema_version(self):
        """Test registering schema versions."""
        # Register schemas
        self.version_manager.register_schema_version("test_schema", "1.0.0", self.schema_v1)
        self.version_manager.register_schema_version("test_schema", "2.0.0", self.schema_v2)

        # Check that schemas are registered
        self.assertIn("test_schema", self.version_manager.versioned_schemas)
        self.assertIn("1.0.0", self.version_manager.versioned_schemas["test_schema"])
        self.assertIn("2.0.0", self.version_manager.versioned_schemas["test_schema"])

        # Check that the latest version is set as default
        self.assertEqual("2.0.0", self.version_manager.default_versions["test_schema"])

        # Check that the latest version is registered with the schema registry
        self.assertIn("test_schema", self.schema_registry.schemas)
        self.assertEqual(
            self.schema_v2,
            self.schema_registry.schemas["test_schema"]
        )

    def test_get_schema_version(self):
        """Test getting schema versions."""
        # Register schemas
        self.version_manager.register_schema_version("test_schema", "1.0.0", self.schema_v1)
        self.version_manager.register_schema_version("test_schema", "2.0.0", self.schema_v2)

        # Get specific version
        schema_v1 = self.version_manager.get_schema_version("test_schema", "1.0.0")
        self.assertEqual(self.schema_v1, schema_v1)

        # Get latest version
        schema_latest = self.version_manager.get_schema_version("test_schema")
        self.assertEqual(self.schema_v2, schema_latest)

        # Get non-existent version
        schema_none = self.version_manager.get_schema_version("test_schema", "3.0.0")
        self.assertIsNone(schema_none)

        # Get non-existent schema type
        schema_none = self.version_manager.get_schema_version("non_existent")
        self.assertIsNone(schema_none)

    def test_list_schema_versions(self):
        """Test listing schema versions."""
        # Register schemas
        self.version_manager.register_schema_version("test_schema", "1.0.0", self.schema_v1)
        self.version_manager.register_schema_version("test_schema", "2.0.0", self.schema_v2)
        self.version_manager.register_schema_version("test_schema", "1.5.0", self.schema_v1)

        # List versions
        versions = self.version_manager.list_schema_versions("test_schema")
        self.assertEqual(["1.0.0", "1.5.0", "2.0.0"], versions)

        # List versions for non-existent schema type
        versions = self.version_manager.list_schema_versions("non_existent")
        self.assertEqual([], versions)

    def test_register_migration(self):
        """Test registering migrations."""
        # Define a simple migration function
        def migrate_v1_to_v2(data):
            return data

        # Register migration
        self.version_manager.register_migration(
            "test_schema", "1.0.0", "2.0.0", migrate_v1_to_v2
        )

        # Check that migration is registered
        self.assertIn("test_schema", self.version_manager.migrations)
        self.assertIn(("1.0.0", "2.0.0"), self.version_manager.migrations["test_schema"])
        self.assertEqual(
            migrate_v1_to_v2,
            self.version_manager.migrations["test_schema"][("1.0.0", "2.0.0")]
        )

    def test_migrate_data_direct(self):
        """Test migrating data with a direct migration path."""
        # Register schemas
        self.version_manager.register_schema_version("test_schema", "1.0.0", self.schema_v1)
        self.version_manager.register_schema_version("test_schema", "2.0.0", self.schema_v2)

        # Register migration
        def migrate_v1_to_v2(data):
            result = data.copy()
            if "email" not in result:
                result["email"] = f"{result['name'].lower().replace(' ', '.')}@example.com"
            return result

        self.version_manager.register_migration(
            "test_schema", "1.0.0", "2.0.0", migrate_v1_to_v2
        )

        # Migrate data
        migrated_data = self.version_manager.migrate_data(
            "test_schema", self.data_v1, "1.0.0", "2.0.0"
        )

        # Check that data is migrated correctly
        self.assertIn("email", migrated_data)
        self.assertEqual("<EMAIL>", migrated_data["email"])

    def test_migrate_data_indirect(self):
        """Test migrating data with an indirect migration path."""
        # Register schemas
        self.version_manager.register_schema_version("test_schema", "1.0.0", self.schema_v1)
        self.version_manager.register_schema_version("test_schema", "1.5.0", self.schema_v1)
        self.version_manager.register_schema_version("test_schema", "2.0.0", self.schema_v2)

        # Register migrations
        def migrate_v1_to_v15(data):
            return data  # No changes needed

        def migrate_v15_to_v2(data):
            result = data.copy()
            if "email" not in result:
                result["email"] = f"{result['name'].lower().replace(' ', '.')}@example.com"
            return result

        self.version_manager.register_migration(
            "test_schema", "1.0.0", "1.5.0", migrate_v1_to_v15
        )
        self.version_manager.register_migration(
            "test_schema", "1.5.0", "2.0.0", migrate_v15_to_v2
        )

        # Migrate data
        migrated_data = self.version_manager.migrate_data(
            "test_schema", self.data_v1, "1.0.0", "2.0.0"
        )

        # Check that data is migrated correctly
        self.assertIn("email", migrated_data)
        self.assertEqual("<EMAIL>", migrated_data["email"])

    def test_migrate_data_to_latest(self):
        """Test migrating data to the latest version."""
        # Register schemas
        self.version_manager.register_schema_version("test_schema", "1.0.0", self.schema_v1)
        self.version_manager.register_schema_version("test_schema", "2.0.0", self.schema_v2)

        # Register migration
        def migrate_v1_to_v2(data):
            result = data.copy()
            if "email" not in result:
                result["email"] = f"{result['name'].lower().replace(' ', '.')}@example.com"
            return result

        self.version_manager.register_migration(
            "test_schema", "1.0.0", "2.0.0", migrate_v1_to_v2
        )

        # Migrate data to latest version
        migrated_data = self.version_manager.migrate_data(
            "test_schema", self.data_v1, "1.0.0"
        )

        # Check that data is migrated correctly
        self.assertIn("email", migrated_data)
        self.assertEqual("<EMAIL>", migrated_data["email"])

    def test_validate_with_version(self):
        """Test validating data against a specific schema version."""
        # Register schemas
        self.version_manager.register_schema_version("test_schema", "1.0.0", self.schema_v1)
        self.version_manager.register_schema_version("test_schema", "2.0.0", self.schema_v2)

        # Validate valid data against v1
        is_valid, errors = self.version_manager.validate_with_version(
            "test_schema", self.data_v1, "1.0.0"
        )
        self.assertTrue(is_valid)
        self.assertEqual([], errors)

        # Validate invalid data against v2 (missing required email)
        is_valid, errors = self.version_manager.validate_with_version(
            "test_schema", self.data_v1, "2.0.0"
        )
        self.assertFalse(is_valid)
        self.assertTrue(any("email" in error for error in errors))

        # Validate valid data against v2
        is_valid, errors = self.version_manager.validate_with_version(
            "test_schema", self.data_v2, "2.0.0"
        )
        self.assertTrue(is_valid)
        self.assertEqual([], errors)

    def test_is_compatible(self):
        """Test checking compatibility between schema versions."""
        # Register schemas
        self.version_manager.register_schema_version("test_schema", "1.0.0", self.schema_v1)
        self.version_manager.register_schema_version("test_schema", "2.0.0", self.schema_v2)

        # Register migrations
        def migrate_v1_to_v2(data):
            result = data.copy()
            if "email" not in result:
                result["email"] = f"{result['name'].lower().replace(' ', '.')}@example.com"
            return result

        self.version_manager.register_migration(
            "test_schema", "1.0.0", "2.0.0", migrate_v1_to_v2
        )

        # Check compatibility of v1 data with v2 schema
        is_compatible, errors = self.version_manager.is_compatible(
            "test_schema", self.data_v1, "1.0.0", "2.0.0"
        )
        self.assertTrue(is_compatible)
        self.assertEqual([], errors)

        # Check compatibility of invalid data
        invalid_data = {"age": 30}  # Missing required name
        is_compatible, errors = self.version_manager.is_compatible(
            "test_schema", invalid_data, "1.0.0", "2.0.0"
        )
        self.assertFalse(is_compatible)
        self.assertTrue(any("name" in error for error in errors))

    def test_extract_version_from_schema(self):
        """Test extracting version information from a schema."""
        # Test with $version field
        version = self.version_manager.extract_version_from_schema(self.schema_v1)
        self.assertEqual("1.0.0", version)

        # Test with version field
        schema = self.schema_v1.copy()
        del schema["$version"]
        schema["version"] = "1.0.0"
        version = self.version_manager.extract_version_from_schema(schema)
        self.assertEqual("1.0.0", version)

        # Test with metadata.version field
        schema = self.schema_v1.copy()
        del schema["$version"]
        schema["metadata"] = {"version": "1.0.0"}
        version = self.version_manager.extract_version_from_schema(schema)
        self.assertEqual("1.0.0", version)

        # Test with version in title
        schema = self.schema_v1.copy()
        del schema["$version"]
        schema["title"] = "TestSchema v1.0.0"
        version = self.version_manager.extract_version_from_schema(schema)
        self.assertEqual("1.0.0", version)

        # Test with version in description
        schema = self.schema_v1.copy()
        del schema["$version"]
        schema["description"] = "Test schema v1.0.0"
        version = self.version_manager.extract_version_from_schema(schema)
        self.assertEqual("1.0.0", version)

        # Test with no version information
        schema = self.schema_v1.copy()
        del schema["$version"]
        del schema["title"]
        version = self.version_manager.extract_version_from_schema(schema)
        self.assertIsNone(version)


class TestSchemaMigrationUtility(unittest.TestCase):
    """Test cases for the SchemaMigrationUtility class."""

    def setUp(self):
        """Set up test fixtures."""
        self.migration_utility = SchemaMigrationUtility()

    def test_add_field(self):
        """Test adding a field to data."""
        data = {"name": "John"}
        result = self.migration_utility.add_field(data, "age", 30)
        self.assertEqual(30, result["age"])
        self.assertEqual("John", result["name"])

        # Test adding a nested field
        data = {"user": {"name": "John"}}
        result = self.migration_utility.add_field(data, "user.age", 30)
        self.assertEqual(30, result["user"]["age"])
        self.assertEqual("John", result["user"]["name"])

        # Test adding a field to a non-existent parent
        data = {"name": "John"}
        result = self.migration_utility.add_field(data, "user.age", 30)
        self.assertEqual(30, result["user"]["age"])
        self.assertEqual("John", result["name"])

    def test_remove_field(self):
        """Test removing a field from data."""
        data = {"name": "John", "age": 30}
        result = self.migration_utility.remove_field(data, "age")
        self.assertNotIn("age", result)
        self.assertEqual("John", result["name"])

        # Test removing a nested field
        data = {"user": {"name": "John", "age": 30}}
        result = self.migration_utility.remove_field(data, "user.age")
        self.assertNotIn("age", result["user"])
        self.assertEqual("John", result["user"]["name"])

        # Test removing a non-existent field
        data = {"name": "John"}
        result = self.migration_utility.remove_field(data, "age")
        self.assertEqual(data, result)

    def test_rename_field(self):
        """Test renaming a field in data."""
        data = {"name": "John"}
        result = self.migration_utility.rename_field(data, "name", "full_name")
        self.assertNotIn("name", result)
        self.assertEqual("John", result["full_name"])

        # Test renaming a nested field
        data = {"user": {"name": "John"}}
        result = self.migration_utility.rename_field(data, "user.name", "user.full_name")
        self.assertNotIn("name", result["user"])
        self.assertEqual("John", result["user"]["full_name"])

        # Test renaming a field to a nested path
        data = {"name": "John"}
        result = self.migration_utility.rename_field(data, "name", "user.name")
        self.assertNotIn("name", result)
        self.assertEqual("John", result["user"]["name"])

    def test_transform_field(self):
        """Test transforming a field in data."""
        data = {"name": "john"}
        result = self.migration_utility.transform_field(data, "name", str.upper)
        self.assertEqual("JOHN", result["name"])

        # Test transforming a nested field
        data = {"user": {"name": "john"}}
        result = self.migration_utility.transform_field(data, "user.name", str.upper)
        self.assertEqual("JOHN", result["user"]["name"])

        # Test transforming a non-existent field
        data = {"name": "John"}
        result = self.migration_utility.transform_field(data, "age", lambda x: x + 1)
        self.assertEqual(data, result)

    def test_merge_objects(self):
        """Test merging objects in data."""
        data = {"user": {"name": "John"}}
        merge_data = {"age": 30, "email": "<EMAIL>"}
        result = self.migration_utility.merge_objects(data, "user", merge_data)
        self.assertEqual("John", result["user"]["name"])
        self.assertEqual(30, result["user"]["age"])
        self.assertEqual("<EMAIL>", result["user"]["email"])

        # Test merging into a non-existent path
        data = {"name": "John"}
        merge_data = {"age": 30}
        result = self.migration_utility.merge_objects(data, "user", merge_data)
        self.assertEqual("John", result["name"])
        self.assertEqual(30, result["user"]["age"])

    def test_update_enum_values(self):
        """Test updating enum values in data."""
        data = {"status": "active"}
        value_map = {"active": "ACTIVE", "inactive": "INACTIVE"}
        result = self.migration_utility.update_enum_values(data, "status", value_map)
        self.assertEqual("ACTIVE", result["status"])

        # Test updating enum values in an array
        data = {"statuses": ["active", "inactive"]}
        value_map = {"active": "ACTIVE", "inactive": "INACTIVE"}
        result = self.migration_utility.update_enum_values(data, "statuses", value_map)
        self.assertEqual(["ACTIVE", "INACTIVE"], result["statuses"])

    def test_update_array_items(self):
        """Test updating items in an array field."""
        data = {"users": [{"name": "John"}, {"name": "Jane"}]}

        def transform_item(item):
            item["name"] = item["name"].upper()
            return item

        result = self.migration_utility.update_array_items(data, "users", transform_item)
        self.assertEqual("JOHN", result["users"][0]["name"])
        self.assertEqual("JANE", result["users"][1]["name"])

    def test_create_migration_chain(self):
        """Test creating a chain of migrations."""
        def add_age(data):
            result = data.copy()
            result["age"] = 30
            return result

        def add_email(data):
            result = data.copy()
            result["email"] = "<EMAIL>"
            return result

        chain = self.migration_utility.create_migration_chain([add_age, add_email])
        result = chain({"name": "John"})
        self.assertEqual("John", result["name"])
        self.assertEqual(30, result["age"])
        self.assertEqual("<EMAIL>", result["email"])


if __name__ == "__main__":
    unittest.main()
