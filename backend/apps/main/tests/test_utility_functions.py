"""
Tests for the utility functions in apps.main.tests.utils.
"""
import pytest
from django.db import transaction
from apps.main.tests.utils import (
    generate_unique_scenario_name,
    create_test_llm_config,
    create_test_llm_config_async,
    create_test_agent,
    create_test_agent_async
)


def test_generate_unique_scenario_name():
    """Test that generate_unique_scenario_name generates unique names."""
    name1 = generate_unique_scenario_name("Test")
    name2 = generate_unique_scenario_name("Test")
    assert name1 != name2
    assert name1.startswith("Test")
    assert name2.startswith("Test")


@pytest.mark.django_db
def test_create_test_llm_config():
    """Test that create_test_llm_config creates a valid LLM config."""
    config = create_test_llm_config(
        model_name="test-model",
        temperature=0.7,
        input_token_price=0.0001,
        output_token_price=0.0002,
        is_default=False,
        is_evaluation=True
    )
    assert config.model_name == "test-model"
    assert config.temperature == 0.7
    assert float(config.input_token_price) == 0.0001
    assert float(config.output_token_price) == 0.0002
    assert config.is_default is False
    assert config.is_evaluation is True


@pytest.mark.django_db
def test_create_test_agent():
    """Test that create_test_agent creates a valid agent."""
    agent = create_test_agent(
        description="Test agent description",
        system_instructions="Test system instructions",
        input_schema={"type": "object", "properties": {}},
        output_schema={"type": "object", "properties": {}},
        langgraph_node_class="test.TestAgent",
        version="1.0.0",
        is_active=True
    )
    assert agent.description == "Test agent description"
    assert agent.system_instructions == "Test system instructions"
    assert agent.input_schema == {"type": "object", "properties": {}}
    assert agent.output_schema == {"type": "object", "properties": {}}
    assert agent.langgraph_node_class == "test.TestAgent"
    assert agent.version == "1.0.0"
    assert agent.is_active is True


@pytest.mark.django_db
@pytest.mark.asyncio
async def test_create_test_llm_config_async():
    """Test that create_test_llm_config_async creates a valid LLM config."""
    config = await create_test_llm_config_async(
        model_name="test-model-async",
        temperature=0.5,
        input_token_price=0.0002,
        output_token_price=0.0003,
        is_default=True,
        is_evaluation=False
    )
    assert config.model_name == "test-model-async"
    assert config.temperature == 0.5
    assert float(config.input_token_price) == 0.0002
    assert float(config.output_token_price) == 0.0003
    assert config.is_default is True
    assert config.is_evaluation is False


@pytest.mark.django_db
@pytest.mark.asyncio
async def test_create_test_agent_async():
    """Test that create_test_agent_async creates a valid agent."""
    agent = await create_test_agent_async(
        description="Test agent description async",
        system_instructions="Test system instructions async",
        input_schema={"type": "object", "properties": {"test": {"type": "string"}}},
        output_schema={"type": "object", "properties": {"result": {"type": "string"}}},
        langgraph_node_class="test.AsyncTestAgent",
        version="1.1.0",
        is_active=True
    )
    assert agent.description == "Test agent description async"
    assert agent.system_instructions == "Test system instructions async"
    assert agent.input_schema == {"type": "object", "properties": {"test": {"type": "string"}}}
    assert agent.output_schema == {"type": "object", "properties": {"result": {"type": "string"}}}
    assert agent.langgraph_node_class == "test.AsyncTestAgent"
    assert agent.version == "1.1.0"
    assert agent.is_active is True
