# backend/apps/main/tests/__init__.py

# This file ensures proper initialization of mocks when tests are run from VS Code

import os
import sys
import importlib.util

# Only set up mocks if we're running tests
if any(arg.startswith('test') for arg in sys.argv) or os.environ.get('TESTING') == 'true':
    # Try to import the mock module
    try:
        # Check if it's already imported (might be the case when running from script)
        # Note: sys.path modification was removed as it's handled by pyproject.toml's pythonpath
        if 'apps.main.tests.mock_django' not in sys.modules:
            # Print a message indicating mocks have been initialized
            print("Test mocks initialized for VS Code Test Explorer")
    except ImportError as e:
        print(f"Warning: Failed to initialize test mocks: {e}")
        # Don't fail here, just print a warning

# Additional test utilities can be added below this point
