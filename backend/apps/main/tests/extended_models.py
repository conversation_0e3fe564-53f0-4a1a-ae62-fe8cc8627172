"""
Extended test models for the main app.

This module contains models that extend the existing test models to add additional functionality.
"""
from django.db import models

from apps.main.tests.models import TestBenchmarkRun
from apps.main.tests.test_agents.conftest import TestLLMConfig


class ExtendedTestBenchmarkRunManager(models.Manager):
    """
    Custom manager for ExtendedTestBenchmarkRun that handles TestLLMConfig instances.
    """
    def create(self, **kwargs):
        """
        Override the create method to handle TestLLMConfig instances.

        If the llm_config is a TestLLMConfig instance, we need to create a real LLMConfig instance
        and use that instead.
        """
        from apps.main.models import LLMConfig

        # Check if llm_config is a TestLLMConfig instance
        if 'llm_config' in kwargs and isinstance(kwargs['llm_config'], TestLLMConfig):
            test_llm_config = kwargs['llm_config']
            # Create a real LLMConfig instance with the same attributes
            real_llm_config, _ = LLMConfig.objects.get_or_create(
                name=test_llm_config.name,
                defaults={
                    'model_name': test_llm_config.model_name,
                    'temperature': test_llm_config.temperature,
                    'input_token_price': test_llm_config.input_token_price,
                    'output_token_price': test_llm_config.output_token_price,
                }
            )
            # Use the real LLMConfig instance
            kwargs['llm_config'] = real_llm_config

        # Call the parent create method
        return super().create(**kwargs)


class ExtendedTestBenchmarkRun(TestBenchmarkRun):
    """
    An extended version of TestBenchmarkRun that accepts TestLLMConfig instances.

    This model is used for tests that need to use TestLLMConfig instances instead of LLMConfig instances.
    """
    objects = ExtendedTestBenchmarkRunManager()

    class Meta:
        proxy = True
