"""
Example of schema migration using the schema versioning system.
"""

import unittest
from apps.main.services.schema_version_manager import SchemaVersionManager
from apps.main.services.schema_registry import SchemaRegistry
from apps.main.services.schema_migration_utility import SchemaMigrationUtility


class TestSchemaMigrationExample(unittest.TestCase):
    """Example of schema migration using the schema versioning system."""

    def setUp(self):
        """Set up test fixtures."""
        self.schema_registry = SchemaRegistry()
        self.version_manager = SchemaVersionManager(self.schema_registry)
        self.migration_utility = SchemaMigrationUtility()

        # Define schema versions
        self.user_profile_v1 = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "UserProfileSchema v1.0.0",
            "$version": "1.0.0",
            "type": "object",
            "required": ["name", "trust_phase"],
            "properties": {
                "name": {"type": "string"},
                "trust_phase": {
                    "type": "string",
                    "enum": ["Foundation", "Expansion", "Integration"]
                },
                "trust_level": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 100
                }
            }
        }

        self.user_profile_v2 = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "UserProfileSchema v2.0.0",
            "$version": "2.0.0",
            "type": "object",
            "required": ["name", "trust_phase", "email"],
            "properties": {
                "name": {"type": "string"},
                "email": {"type": "string", "format": "email"},
                "trust_phase": {
                    "type": "string",
                    "enum": ["Foundation", "Expansion", "Integration"]
                },
                "trust_level": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 100
                }
            }
        }

        # Register schema versions
        self.version_manager.register_schema_version(
            "user_profile", "1.0.0", self.user_profile_v1
        )
        self.version_manager.register_schema_version(
            "user_profile", "2.0.0", self.user_profile_v2
        )

        # Define migration functions
        def migrate_v1_to_v2(data):
            """Migrate user profile from v1 to v2 by adding a default email."""
            result = data.copy()
            if "email" not in result:
                # Generate a default email based on the user's name
                result["email"] = f"{result['name'].lower().replace(' ', '.')}@example.com"
            return result

        def migrate_v2_to_v1(data):
            """Migrate user profile from v2 to v1 by removing email."""
            result = data.copy()
            if "email" in result:
                del result["email"]
            return result

        # Register migrations
        self.version_manager.register_migration(
            "user_profile", "1.0.0", "2.0.0", migrate_v1_to_v2
        )
        self.version_manager.register_migration(
            "user_profile", "2.0.0", "1.0.0", migrate_v2_to_v1
        )

    def test_migration_example(self):
        """Test the schema migration example."""
        # Create a user profile using v1 schema
        user_profile_v1 = {
            "name": "John Doe",
            "trust_phase": "Foundation",
            "trust_level": 50
        }

        # Validate against v1 schema
        is_valid, errors = self.version_manager.validate_with_version(
            "user_profile", user_profile_v1, "1.0.0"
        )
        self.assertTrue(is_valid)
        self.assertEqual([], errors)

        # Validate against v2 schema (should fail due to missing email)
        is_valid, errors = self.version_manager.validate_with_version(
            "user_profile", user_profile_v1, "2.0.0"
        )
        self.assertFalse(is_valid)
        self.assertTrue(any("email" in error for error in errors))

        # Migrate from v1 to v2
        user_profile_v2 = self.version_manager.migrate_data(
            "user_profile", user_profile_v1, "1.0.0", "2.0.0"
        )

        # Validate migrated data against v2 schema
        is_valid, errors = self.version_manager.validate_with_version(
            "user_profile", user_profile_v2, "2.0.0"
        )
        self.assertTrue(is_valid)
        self.assertEqual([], errors)

        # Check that email was added correctly
        self.assertEqual("<EMAIL>", user_profile_v2["email"])

        # Migrate back from v2 to v1
        user_profile_v1_migrated = self.version_manager.migrate_data(
            "user_profile", user_profile_v2, "2.0.0", "1.0.0"
        )

        # Validate migrated data against v1 schema
        is_valid, errors = self.version_manager.validate_with_version(
            "user_profile", user_profile_v1_migrated, "1.0.0"
        )
        self.assertTrue(is_valid)
        self.assertEqual([], errors)

        # Check that email was removed
        self.assertNotIn("email", user_profile_v1_migrated)

    def test_migration_utility_example(self):
        """Test the schema migration utility example."""
        # Create a user profile
        user_profile = {
            "name": "John Doe",
            "trust_phase": "Foundation",
            "trust_level": 50,
            "preferences": {
                "theme": "light",
                "notifications": True
            }
        }

        # Add a field
        result = self.migration_utility.add_field(
            user_profile, "email", "<EMAIL>"
        )
        self.assertEqual("<EMAIL>", result["email"])

        # Add a nested field
        result = self.migration_utility.add_field(
            result, "preferences.language", "en"
        )
        self.assertEqual("en", result["preferences"]["language"])

        # Rename a field
        result = self.migration_utility.rename_field(
            result, "preferences.theme", "preferences.display_theme"
        )
        self.assertNotIn("theme", result["preferences"])
        self.assertEqual("light", result["preferences"]["display_theme"])

        # Transform a field
        result = self.migration_utility.transform_field(
            result, "name", str.upper
        )
        self.assertEqual("JOHN DOE", result["name"])

        # Remove a field
        result = self.migration_utility.remove_field(
            result, "preferences.notifications"
        )
        self.assertNotIn("notifications", result["preferences"])

        # Create a migration chain
        def add_email(data):
            return self.migration_utility.add_field(
                data, "email", f"{data['name'].lower().replace(' ', '.')}@example.com"
            )

        def add_language(data):
            return self.migration_utility.add_field(
                data, "preferences.language", "en"
            )

        def uppercase_name(data):
            return self.migration_utility.transform_field(
                data, "name", str.upper
            )

        chain = self.migration_utility.create_migration_chain([
            add_email,
            add_language,
            uppercase_name
        ])

        # Apply the migration chain
        original = {
            "name": "Jane Smith",
            "trust_phase": "Expansion",
            "trust_level": 75,
            "preferences": {
                "theme": "dark"
            }
        }
        result = chain(original)

        # Check the result
        self.assertEqual("JANE SMITH", result["name"])
        self.assertEqual("<EMAIL>", result["email"])
        self.assertEqual("en", result["preferences"]["language"])
        self.assertEqual("dark", result["preferences"]["theme"])


if __name__ == "__main__":
    unittest.main()
