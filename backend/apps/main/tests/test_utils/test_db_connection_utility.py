import pytest
import async<PERSON>
from unittest.mock import patch, MagicMock
from django.db import connection, connections, OperationalError, DatabaseError
from django.db.models import Model

from apps.main.utils.db_connection_utility import DatabaseConnectionUtility


@pytest.mark.django_db(transaction=True)
def test_execute_with_retry_success():
    """Test that execute_with_retry successfully executes a query."""
    # Execute a simple query
    result = DatabaseConnectionUtility.execute_with_retry("SELECT 1")

    # The result should be None for a successful execution
    assert result is None


@pytest.mark.django_db(transaction=True)
def test_execute_with_retry_retries_on_error():
    """Test that execute_with_retry retries on database errors."""
    # Mock the cursor's execute method to raise an error on first call, then succeed
    original_cursor = connection.cursor

    mock_cursor = MagicMock()
    mock_cursor.__enter__ = MagicMock()
    mock_cursor.__exit__ = MagicMock()
    mock_execute = MagicMock(side_effect=[OperationalError("Test error"), None])
    mock_cursor.__enter__.return_value.execute = mock_execute

    # Patch the connection.cursor method
    with patch.object(connection, 'cursor', return_value=mock_cursor):
        # Execute a query that will fail on first attempt
        result = DatabaseConnectionUtility.execute_with_retry(
            "SELECT 1",
            max_retries=2,
            retry_delay=0.01  # Use a small delay for faster tests
        )

    # The result should be None for a successful execution
    assert result is None
    # The execute method should have been called twice
    assert mock_execute.call_count == 2


@pytest.mark.django_db(transaction=True)
def test_execute_with_retry_max_retries_exceeded():
    """Test that execute_with_retry raises an error after max retries."""
    # Create a function that will be used to mock the cursor's execute method
    # It will always raise an OperationalError
    def mock_execute(*args, **kwargs):
        raise OperationalError("Test error")

    # Create a mock cursor that will use our mock_execute function
    mock_cursor = MagicMock()
    mock_cursor_instance = MagicMock()
    mock_cursor_instance.execute = MagicMock(side_effect=mock_execute)
    mock_cursor.__enter__.return_value = mock_cursor_instance

    # Patch the connection.cursor method to return our mock cursor
    with patch.object(connection, 'cursor', return_value=mock_cursor):
        # Execute a query that will always fail
        # We expect it to raise an OperationalError after max_retries attempts
        with pytest.raises(OperationalError):
            DatabaseConnectionUtility.execute_with_retry(
                "SELECT 1",
                max_retries=2,
                retry_delay=0.01  # Use a small delay for faster tests
            )

    # The execute method should have been called twice (max_retries)
    assert mock_cursor_instance.execute.call_count == 2


@pytest.mark.django_db(transaction=True)
def test_safe_connection():
    """Test that safe_connection properly manages connections."""
    # Use the safe_connection context manager
    with DatabaseConnectionUtility.safe_connection() as conn:
        # The connection should be open
        assert conn.connection is not None
        assert not conn.connection.closed

    # After exiting the context, the connection should be closed
    # but still in the pool (Django will reopen it when needed)
    # This is hard to test directly, so we'll just verify the context manager works


@pytest.mark.django_db(transaction=True)
def test_safe_cursor():
    """Test that safe_cursor properly manages cursors."""
    # Use the safe_cursor context manager
    with DatabaseConnectionUtility.safe_cursor() as cursor:
        # Execute a simple query
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        assert result[0] == 1

    # After exiting the context, the cursor should be closed
    # This is handled by Django's cursor context manager


@pytest.mark.django_db(transaction=True)
def test_with_retry_decorator():
    """Test that the with_retry decorator adds retry logic to functions."""
    # Create a function that raises an error on first call, then succeeds
    def test_function():
        if test_function.call_count == 0:
            test_function.call_count += 1
            raise OperationalError("Test error")
        test_function.call_count += 1
        return "success"

    test_function.call_count = 0

    # Decorate the function with with_retry
    decorated_function = DatabaseConnectionUtility.with_retry(
        max_retries=2,
        retry_delay=0.01
    )(test_function)

    # Call the decorated function
    result = decorated_function()

    # The result should be "success"
    assert result == "success"
    # The function should have been called twice
    assert test_function.call_count == 2


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_async_with_retry_decorator():
    """Test that the async_with_retry decorator adds retry logic to async functions."""
    # Create an async function that raises an error on first call, then succeeds
    async def mock_async_function():
        if mock_async_function.call_count == 0:
            mock_async_function.call_count += 1
            raise OperationalError("Test error")
        mock_async_function.call_count += 1
        return "success"

    mock_async_function.call_count = 0

    # Decorate the function with async_with_retry
    decorated_function = DatabaseConnectionUtility.async_with_retry(
        max_retries=2,
        retry_delay=0.01
    )(mock_async_function)

    # Call the decorated function
    result = await decorated_function()

    # The result should be "success"
    assert result == "success"
    # The function should have been called twice
    assert mock_async_function.call_count == 2


@pytest.mark.django_db(transaction=True)
def test_safe_transaction_decorator():
    """Test that the safe_transaction decorator properly manages transactions."""
    # Create a function that executes a query within a transaction
    @DatabaseConnectionUtility.safe_transaction()
    def test_function():
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            return cursor.fetchone()[0]

    # Call the decorated function
    result = test_function()

    # The result should be 1
    assert result == 1


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_async_safe_transaction_decorator():
    """Test that the async_safe_transaction decorator properly manages transactions."""
    # Create an async function that executes a query within a transaction
    @DatabaseConnectionUtility.async_safe_transaction()
    async def test_async_function():
        # This function is run in a sync context by the decorator,
        # so we can use synchronous database operations
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            return cursor.fetchone()[0]

    # Call the decorated function
    result = await test_async_function()

    # The result should be 1
    assert result == 1


@pytest.mark.django_db(transaction=True)
def test_get_or_create_with_retry(db):
    """Test that get_or_create_with_retry successfully creates or retrieves objects."""
    from django.contrib.auth.models import User

    # Create a user
    user, created = DatabaseConnectionUtility.get_or_create_with_retry(
        User,
        defaults={"email": "<EMAIL>"},
        username="testuser"
    )

    # The user should be created
    assert created is True
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"

    # Retrieve the same user
    user2, created2 = DatabaseConnectionUtility.get_or_create_with_retry(
        User,
        defaults={"email": "<EMAIL>"},  # This should be ignored
        username="testuser"
    )

    # The user should be retrieved, not created
    assert created2 is False
    assert user2.username == "testuser"
    assert user2.email == "<EMAIL>"  # Original email, not the default


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_async_get_or_create_with_retry(db):
    """Test that async_get_or_create_with_retry successfully creates or retrieves objects."""
    from django.contrib.auth.models import User

    # Create a user
    user, created = await DatabaseConnectionUtility.async_get_or_create_with_retry(
        User,
        defaults={"email": "<EMAIL>"},
        username="testuser_async"
    )

    # The user should be created
    assert created is True
    assert user.username == "testuser_async"
    assert user.email == "<EMAIL>"

    # Retrieve the same user
    user2, created2 = await DatabaseConnectionUtility.async_get_or_create_with_retry(
        User,
        defaults={"email": "<EMAIL>"},  # This should be ignored
        username="testuser_async"
    )

    # The user should be retrieved, not created
    assert created2 is False
    assert user2.username == "testuser_async"
    assert user2.email == "<EMAIL>"  # Original email, not the default


@pytest.mark.django_db(transaction=True)
def test_add_connection_monitoring(db_connection_monitor):
    """Test that add_connection_monitoring properly monitors connections."""
    # Create a function that uses a database connection
    @DatabaseConnectionUtility.add_connection_monitoring(db_connection_monitor)
    def test_function():
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            return cursor.fetchone()[0]

    # Call the decorated function
    result = test_function()

    # The result should be 1
    assert result == 1
    # The connection monitoring should have been applied
    # This is hard to test directly, but we can verify the function works


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_async_add_connection_monitoring(db_connection_monitor):
    """Test that async_add_connection_monitoring properly monitors connections."""
    # Create an async function that uses a database connection
    @DatabaseConnectionUtility.async_add_connection_monitoring(db_connection_monitor)
    async def test_async_function():
        # Use sync_to_async to run database operations in a sync context
        from asgiref.sync import sync_to_async

        @sync_to_async
        def db_operation():
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                return cursor.fetchone()[0]

        return await db_operation()

    # Call the decorated function
    result = await test_async_function()

    # The result should be 1
    assert result == 1
    # The connection monitoring should have been applied
    # This is hard to test directly, but we can verify the function works


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_limit_concurrent_db_operations():
    """Test that limit_concurrent_db_operations properly limits concurrency."""
    # Create a counter to track concurrent operations
    concurrent_operations = 0
    max_concurrent_operations = 0

    # Create an async function that simulates a database operation
    @DatabaseConnectionUtility.limit_concurrent_db_operations(max_concurrent=2)
    async def simulated_db_operation(delay):
        nonlocal concurrent_operations, max_concurrent_operations

        # Increment the counter
        concurrent_operations += 1
        max_concurrent_operations = max(max_concurrent_operations, concurrent_operations)

        # Simulate a database operation
        await asyncio.sleep(delay)

        # Decrement the counter
        concurrent_operations -= 1

        return delay

    # Create a list of tasks
    tasks = [
        simulated_db_operation(0.05),
        simulated_db_operation(0.1),
        simulated_db_operation(0.15),
        simulated_db_operation(0.2),
        simulated_db_operation(0.25)
    ]

    # Run all tasks concurrently
    results = await asyncio.gather(*tasks)

    # The results should match the delays
    assert results == [0.05, 0.1, 0.15, 0.2, 0.25]

    # The maximum number of concurrent operations should be limited to 2
    assert max_concurrent_operations == 2
