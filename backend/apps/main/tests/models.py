"""
Test models for the main app.

This module contains models that are used for testing purposes only.
These models match the actual database schema, not the model definition in models.py.
"""
from django.db import models
from django.utils import timezone

from apps.main.models import BenchmarkScenario, GenericAgent, LLMConfig


class TestBenchmarkRun(models.Model):
    """
    A simplified version of BenchmarkRun for tests that doesn't include evaluator_llm_model
    if it's not in the database schema yet.
    
    This model matches the actual database schema, not the model definition in models.py.
    """
    scenario = models.ForeignKey(BenchmarkScenario, on_delete=models.CASCADE, related_name="test_runs")
    agent_definition = models.ForeignKey(GenericAgent, on_delete=models.CASCADE, related_name="test_benchmark_runs")
    agent_version = models.CharField(max_length=40)
    execution_date = models.DateTimeField(default=timezone.now)
    parameters = models.JSONField(default=dict)
    
    # LLM Configuration used for the AGENT during this run
    llm_config = models.ForeignKey(
        LLMConfig,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="test_benchmark_runs"
    )
    
    # Performance metrics
    runs_count = models.IntegerField(default=1)
    mean_duration = models.FloatField(default=0.0)
    median_duration = models.FloatField(default=0.0)
    min_duration = models.FloatField(default=0.0)
    max_duration = models.FloatField(default=0.0)
    std_dev = models.FloatField(default=0.0)
    success_rate = models.FloatField(default=1.0)
    
    # Operational metrics
    llm_calls = models.IntegerField(default=0)
    tool_calls = models.IntegerField(default=0)
    tool_breakdown = models.JSONField(default=dict)
    memory_operations = models.IntegerField(default=0)
    
    # Semantic evaluation
    semantic_score = models.FloatField(null=True, blank=True)
    semantic_evaluation_details = models.JSONField(default=dict)
    semantic_evaluations = models.JSONField(default=dict)
    
    # Response metrics
    last_response_length = models.IntegerField(null=True, blank=True)
    
    # Raw results
    raw_results = models.JSONField(default=dict)
    
    # Token Usage & Cost
    total_input_tokens = models.IntegerField(null=True, blank=True)
    total_output_tokens = models.IntegerField(null=True, blank=True)
    estimated_cost = models.FloatField(null=True, blank=True)
    
    # Statistical comparison fields
    compared_to_run = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='test_comparisons'
    )
    performance_p_value = models.FloatField(null=True, blank=True)
    is_performance_significant = models.BooleanField(null=True, blank=True)
    
    # Stage performance
    stage_performance_details = models.JSONField(default=dict)
    
    # Add property to match the real BenchmarkRun model
    @property
    def agent_llm_model_name(self):
        """Return the LLM model name from the llm_config."""
        if self.llm_config:
            return self.llm_config.model_name
        return None
    
    # Add llm_temperature property
    @property
    def llm_temperature(self):
        """Return the LLM temperature from the llm_config."""
        if self.llm_config:
            return self.llm_config.temperature
        return None
    
    class Meta:
        app_label = 'main'
        db_table = 'main_benchmarkrun'
