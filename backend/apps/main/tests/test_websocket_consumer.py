# backend/apps/main/tests/test_websocket_consumer.py
import pytest
import uuid
from unittest.mock import patch, MagicMock, AsyncMock
from channels.testing import WebsocketCommunicator
from apps.main.consumers import UserSessionConsumer

# Test the complete consumer using Channels' testing utilities
@pytest.mark.test_type("integration")
@pytest.mark.component("main.consumers")
@pytest.mark.django_db(transaction=True) # Allow DB access for disconnect cleanup
@pytest.mark.asyncio
async def test_websocket_consumer_connection():
    """Test WebSocket consumer connection and initial welcome message."""
    # Create communicator
    communicator = WebsocketCommunicator(UserSessionConsumer.as_asgi(), "/ws/game/")

    # Connect
    connected, subprotocol = await communicator.connect()

    # Test connection established
    assert connected

    # Test welcome message received
    response = await communicator.receive_json_from()
    assert response['type'] == 'system_message'
    assert 'Connected to the Game of Life server' in response['content']

    # Disconnect
    await communicator.disconnect()

@pytest.mark.test_type("integration")
@pytest.mark.component("main.consumers")
@pytest.mark.django_db(transaction=True) # Allow DB access for disconnect cleanup
@pytest.mark.asyncio
async def test_chat_message_processing():
    """Test chat message processing flow."""
    # Mock the ConversationDispatcher for this test
    with patch('apps.main.consumers.ConversationDispatcher') as MockDispatcher:
        # Configure the mock dispatcher
        mock_instance = AsyncMock()
        MockDispatcher.return_value = mock_instance

        # Configure process_message return value
        mock_instance.process_message.return_value = {
            'workflow_id': str(uuid.uuid4()),
            'workflow_type': 'wheel_generation',
            'confidence': 0.9,
            'status': 'initiated',
            'session_timestamp': '2023-10-15T14:30:00Z'
        }

        # Create communicator
        communicator = WebsocketCommunicator(UserSessionConsumer.as_asgi(), "/ws/game/")

        # Connect
        connected, subprotocol = await communicator.connect()
        assert connected

        # Skip welcome message
        await communicator.receive_json_from()

        # Send chat message
        await communicator.send_json_to({
            'type': 'chat_message',
            'content': {
                'message': 'Hello, I need an activity suggestion',
                'user_profile_id': 'test-user-123'
            }
        })

        # Verify user message echo
        response = await communicator.receive_json_from()
        assert response['type'] == 'chat_message'
        assert response['content'] == 'Hello, I need an activity suggestion'
        assert response['is_user'] == True

        # Verify processing status notification
        response = await communicator.receive_json_from()
        assert response['type'] == 'processing_status'
        assert response['status'] == 'processing'

        # Verify workflow status notification
        response = await communicator.receive_json_from()
        assert response['type'] == 'workflow_status'
        assert response['status'] == 'initiated'

        # Verify the dispatcher was called with correct arguments
        mock_instance.process_message.assert_called_once()
        args = mock_instance.process_message.call_args[0][0]
        assert args['text'] == 'Hello, I need an activity suggestion'

        # Disconnect
    await communicator.disconnect()

@pytest.mark.test_type("integration")
@pytest.mark.component("main.consumers")
@pytest.mark.django_db(transaction=True) # Allow DB access for disconnect cleanup
@pytest.mark.asyncio
async def test_spin_result_processing():
    """Test spin result processing flow."""
    # Mock the ConversationDispatcher for this test
    with patch('apps.main.consumers.ConversationDispatcher') as MockDispatcher:
        # Configure the mock dispatcher
        mock_instance = AsyncMock()
        MockDispatcher.return_value = mock_instance

        # Configure process_message return value
        mock_instance.process_message.return_value = {
            'workflow_id': str(uuid.uuid4()),
            'workflow_type': 'post_spin',
            'confidence': 1.0,
            'status': 'initiated',
            'session_timestamp': '2023-10-15T14:30:00Z'
        }

        # Create communicator
        communicator = WebsocketCommunicator(UserSessionConsumer.as_asgi(), "/ws/game/")

        # Connect
        connected, subprotocol = await communicator.connect()
        assert connected

        # Skip welcome message
        await communicator.receive_json_from()

        # Send spin result message
        await communicator.send_json_to({
            'type': 'spin_result',
            'content': {
                'activity_tailored_id': 'act-123',
                'name': 'Creative Writing Exercise',
                'description': 'A short creative writing activity',
                'user_profile_id': 'test-user-123'  # Include user_profile_id as it's required
            }
        })

        # Verify processing status notification
        # According to API contract, this should be 'processing_status' not 'error'
        response = await communicator.receive_json_from()
        assert response['type'] == 'processing_status'
        assert response['status'] == 'processing'

        # Verify initial chat message response
        response = await communicator.receive_json_from()
        assert response['type'] == 'chat_message'
        assert 'Great choice' in response['content']
        assert response['is_user'] == False

        # Verify description follow-up message
        response = await communicator.receive_json_from()
        assert response['type'] == 'chat_message'
        assert "Here's what to do" in response['content']
        assert response['is_user'] == False

        # Verify workflow status notification
        response = await communicator.receive_json_from()
        assert response['type'] == 'workflow_status'
        assert response['status'] == 'initiated'

        # Verify the dispatcher was called with the expected metadata
        mock_instance.process_message.assert_called_once()
        call_args = mock_instance.process_message.call_args[0][0]
        assert 'metadata' in call_args
        assert call_args['metadata']['type'] == 'spin_result'
        assert call_args['metadata']['activity_id'] == 'act-123'

        # Disconnect
        await communicator.disconnect()

@pytest.mark.test_type("integration")
@pytest.mark.component("main.consumers")
@pytest.mark.django_db # Add this marker to allow database access
@pytest.mark.asyncio
async def test_workflow_status_request():
    """Test workflow status request processing."""
    # Mock database operation
    with patch('apps.main.consumers.database_sync_to_async') as mock_sync_to_async:
        # Configure mock to return status
        mock_sync_to_async.return_value = AsyncMock(return_value='completed')

        # Create communicator
        communicator = WebsocketCommunicator(UserSessionConsumer.as_asgi(), "/ws/game/")

        # Connect
        connected, subprotocol = await communicator.connect()
        assert connected

        # Skip welcome message
        await communicator.receive_json_from()

        # Send workflow status request
        workflow_id = str(uuid.uuid4())
        await communicator.send_json_to({
            'type': 'workflow_status_request',
            'content': {
                'workflow_id': workflow_id
            }
        })

        # Verify status response
        response = await communicator.receive_json_from()
        assert response['type'] == 'workflow_status'
        assert response['workflow_id'] == workflow_id
        assert response['status'] == 'unknown'  # Default for new connections

        # Disconnect
    await communicator.disconnect()

@pytest.mark.test_type("integration")
@pytest.mark.component("main.consumers")
@pytest.mark.django_db(transaction=True) # Allow DB access for disconnect cleanup
@pytest.mark.asyncio
async def test_message_validation():
    """Test message validation in the consumer."""
    # Create communicator
    communicator = WebsocketCommunicator(UserSessionConsumer.as_asgi(), "/ws/game/")

    # Connect
    connected, subprotocol = await communicator.connect()
    assert connected

    # Skip welcome message
    await communicator.receive_json_from()

    # Send invalid chat message (missing required fields)
    await communicator.send_json_to({
        'type': 'chat_message',
        'content': {}  # Missing 'message' field
    })

    # Verify error response
    response = await communicator.receive_json_from()
    assert response['type'] == 'error'
    assert 'Invalid chat message format' in response['content']

    # Send invalid spin result (missing required fields)
    await communicator.send_json_to({
        'type': 'spin_result',
        'content': {
            'activity_tailored_id': 'act-123'
            # Missing 'name' field
        }
    })

    # Verify error response
    response = await communicator.receive_json_from()
    assert response['type'] == 'error'  # This is correct according to API contract
    assert 'Invalid spin result format' in response['content']

    # Disconnect
    await communicator.disconnect()

from channels.layers import get_channel_layer # Import needed for direct layer access if patching fails

@pytest.mark.test_type("integration")
@pytest.mark.component("main.consumers")
@pytest.mark.django_db(transaction=True) # Allow DB access for disconnect cleanup
@pytest.mark.asyncio
async def test_channel_layer_handlers():
    """Test the consumer's channel layer message handlers."""

    consumer_instance_ref = {} # Dictionary to hold the instance reference
    original_connect = UserSessionConsumer.connect

    async def patched_connect(consumer_self):
        # Call original connect logic first
        # This ensures self.channel_name and self.channel_layer are set
        await original_connect(consumer_self)
        # Store the instance for the test to access
        consumer_instance_ref['instance'] = consumer_self

    # Patch the connect method to capture the consumer instance
    with patch('apps.main.consumers.UserSessionConsumer.connect', new=patched_connect):
        # Create communicator
        communicator = WebsocketCommunicator(UserSessionConsumer.as_asgi(), "/ws/game/")

        # Connect - this will now use the patched connect
        connected, subprotocol = await communicator.connect()
        assert connected

        # Skip welcome message
        await communicator.receive_json_from()

    # Retrieve the captured consumer instance and its attributes
    assert 'instance' in consumer_instance_ref, "Patched connect did not capture consumer instance"
    consumer_instance = consumer_instance_ref['instance']
    channel_name = consumer_instance.channel_name
    channel_layer = consumer_instance.channel_layer # Consumer instance has channel_layer set

    assert channel_name is not None, "Channel name not set on captured consumer instance"
    assert channel_layer is not None, "Channel layer not set on captured consumer instance"

    # Test chat_message handler
    await channel_layer.send(channel_name, {
        'type': 'chat_message',
        'content': 'This is a test message from the agent',
        'is_user': False
    })

    # Verify message received
    response = await communicator.receive_json_from()
    assert response['type'] == 'chat_message'
    assert response['content'] == 'This is a test message from the agent'
    assert response['is_user'] == False

    # Test wheel_data handler
    wheel_data = {
        'name': 'Test Wheel',
        'items': [
            {
                'id': 'item-1',
                'name': 'Test Activity',
                'description': 'Test description',
                'percentage': 20.0,
                'color': '#66BB6A',
                'domain': 'creative',
                'base_challenge_rating': 50,
                'activity_tailored_id': 'act-123'
            }
        ]
    }

    await channel_layer.send(channel_name, {
        'type': 'wheel_data',
        'wheel': wheel_data
    })

    # Verify wheel data received
    response = await communicator.receive_json_from()
    assert response['type'] == 'wheel_data'
    assert 'wheel' in response
    assert response['wheel']['name'] == 'Test Wheel'
    assert len(response['wheel']['items']) == 1

    # Test processing_status handler
    await channel_layer.send(channel_name, {
        'type': 'processing_status',
        'status': 'completed'
    })

    # Verify status received
    response = await communicator.receive_json_from()
    assert response['type'] == 'processing_status'
    assert response['status'] == 'completed'

    # Test error handler
    await channel_layer.send(channel_name, {
        'type': 'error',
        'content': 'This is a test error message'
    })

    # Verify error received
    response = await communicator.receive_json_from()
    assert response['type'] == 'error'
    assert response['content'] == 'This is a test error message'

    # Test workflow_status handler
    workflow_id = str(uuid.uuid4())
    await channel_layer.send(channel_name, {
        'type': 'workflow_status',
        'workflow_id': workflow_id,
        'status': 'completed'
    })

    # Verify workflow status received
    response = await communicator.receive_json_from()
    assert response['type'] == 'workflow_status'
    assert response['workflow_id'] == workflow_id
    assert response['status'] == 'completed'

    # Test debug_info handler
    # The handler expects the data under the 'content' key
    debug_content_to_send = {
        'source': 'test_agent',
        'level': 'debug',
        'message': 'Test debug message',
        'details': {'step': 1, 'info': 'details'}
    }
    await channel_layer.send(channel_name, {
        'type': 'debug_info',
        'content': debug_content_to_send
    })

    # Verify debug info received (this assumes the user is staff or staff check is bypassed)
    # The consumer wraps the received content inside another 'content' key in the final payload
    response = await communicator.receive_json_from()
    assert response['type'] == 'debug_info'
    assert 'content' in response
    received_content = response['content']
    assert received_content['source'] == debug_content_to_send['source']
    assert received_content['level'] == debug_content_to_send['level']
    assert received_content['message'] == debug_content_to_send['message']
    assert received_content['details'] == debug_content_to_send['details']
    assert 'timestamp' in received_content # Timestamp is added by the handler

    # Disconnect
    await communicator.disconnect()


@pytest.mark.test_type("integration")
@pytest.mark.component("main.consumers")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_debug_info_sent_to_staff_user():
    """Verify debug_info messages are sent alongside regular messages to staff users."""
    consumer_instance_ref = {}
    original_connect = UserSessionConsumer.connect

    async def patched_connect_staff(consumer_self):
        # Mock a staff user
        mock_user = AsyncMock()
        mock_user.is_staff = True
        consumer_self.scope['user'] = mock_user
        await original_connect(consumer_self)
        consumer_instance_ref['instance'] = consumer_self

    with patch('apps.main.consumers.UserSessionConsumer.connect', new=patched_connect_staff):
        communicator = WebsocketCommunicator(UserSessionConsumer.as_asgi(), "/ws/game/")
        connected, _ = await communicator.connect()
        assert connected
        await communicator.receive_json_from() # Skip welcome

    consumer_instance = consumer_instance_ref['instance']
    channel_name = consumer_instance.channel_name
    channel_layer = consumer_instance.channel_layer

    # Simulate sending a regular message that might trigger debug info
    # We patch the _send_to_client_and_admin method to check its calls
    with patch.object(consumer_instance, '_send_to_client_and_admin', wraps=consumer_instance._send_to_client_and_admin) as mock_send_helper:
        chat_content = 'Agent message triggering debug'

        # Create a properly formatted debug_payload according to the API contract
        debug_payload = {
            'source': 'agent_logic',
            'level': 'debug',
            'message': 'Debug information for staff',
            'details': {'info': 'some debug data'}
        }

        # First, send a chat message
        await channel_layer.send(channel_name, {
            'type': 'chat_message',
            'content': chat_content,
            'is_user': False
        })

        # 1. Verify the chat message was received
        response = await communicator.receive_json_from(timeout=2)
        assert response['type'] == 'chat_message'
        assert response['content'] == chat_content

        # Now, send a debug_info message directly
        # This is the correct way to send debug info according to the API contract
        await channel_layer.send(channel_name, {
            'type': 'debug_info',
            'content': debug_payload
        })

        # 2. Verify the debug_info message was received
        # Increase timeout to avoid flakiness
        response = await communicator.receive_json_from(timeout=2)
        assert response['type'] == 'debug_info'
        assert 'content' in response

        # The debug_info handler adds a timestamp, so we can't directly compare the entire payload
        received_content = response['content']
        assert received_content['source'] == debug_payload['source']
        assert received_content['level'] == debug_payload['level']
        assert received_content['message'] == debug_payload['message']
        assert received_content['details'] == debug_payload['details']
        assert 'timestamp' in received_content  # Timestamp is added by the handler

        # 3. Verify the helper was called at least once
        # The debug_info handler doesn't use _send_to_client_and_admin but sends directly
        # So we only expect one call for the chat message
        assert mock_send_helper.call_count >= 1

    await communicator.disconnect()


@pytest.mark.test_type("integration")
@pytest.mark.component("main.consumers")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_debug_info_not_sent_to_non_staff_user():
    """Verify debug_info messages are NOT sent to non-staff users."""
    consumer_instance_ref = {}
    original_connect = UserSessionConsumer.connect

    async def patched_connect_non_staff(consumer_self):
        # Mock a non-staff user
        mock_user = AsyncMock()
        mock_user.is_staff = False
        consumer_self.scope['user'] = mock_user
        await original_connect(consumer_self)
        consumer_instance_ref['instance'] = consumer_self

    with patch('apps.main.consumers.UserSessionConsumer.connect', new=patched_connect_non_staff):
        communicator = WebsocketCommunicator(UserSessionConsumer.as_asgi(), "/ws/game/")
        connected, _ = await communicator.connect()
        assert connected
        await communicator.receive_json_from() # Skip welcome

    consumer_instance = consumer_instance_ref['instance']
    channel_name = consumer_instance.channel_name
    channel_layer = consumer_instance.channel_layer

    with patch.object(consumer_instance, '_send_to_client_and_admin', wraps=consumer_instance._send_to_client_and_admin) as mock_send_helper:
        chat_content = 'Agent message potentially triggering debug'

        # Create a properly formatted debug_payload according to the API contract
        debug_payload = {
            'source': 'agent_logic',
            'level': 'debug',
            'message': 'Debug information for staff only',
            'details': {'info': 'sensitive debug data'}
        }

        # First, send a chat message
        await channel_layer.send(channel_name, {
            'type': 'chat_message',
            'content': chat_content,
            'is_user': False
        })

        # 1. Verify the chat message was received
        response = await communicator.receive_json_from(timeout=2)
        assert response['type'] == 'chat_message'
        assert response['content'] == chat_content

        # Now, send a debug_info message directly
        await channel_layer.send(channel_name, {
            'type': 'debug_info',
            'content': debug_payload
        })

        # 2. Verify NO debug_info message was received by non-staff user
        # The debug_info handler should check user.is_staff and not send to non-staff
        await communicator.receive_nothing(timeout=0.5)

        # 3. Verify the helper was called only for the chat message
        assert mock_send_helper.call_count == 1

    await communicator.disconnect()
