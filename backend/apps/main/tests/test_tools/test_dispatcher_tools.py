import pytest
from unittest.mock import patch, MagicMock, AsyncMock # Use AsyncMock for async methods
import json
import asyncio # Import asyncio for use in test side effects

# Import the function to test
from apps.main.agents.tools.dispatcher_tools import extract_message_context

# Mock LLM response data
MOCK_LLM_JSON_RESPONSE = json.dumps({
    "mood": "happy",
    "environment": "home office",
    "time_availability": "about 1 hour",
    "focus": "planning my day",
    "satisfaction": "satisfied",
    "extraction_confidence": 0.9,
    "extracted_entities": ["planning", "day"]
})

MOCK_LLM_TEXT_RESPONSE = """
Mood: curious
Environment: coffee shop
Time Availability: 30 minutes
Focus: reading news
Satisfaction: neutral
Confidence: 80
"""

MOCK_HISTORICAL_CONTEXT = {
    "mood_patterns": {"happy": 2, "focused": 1},
    "environment_patterns": {"home": 3},
    "time_patterns": {"1 hour": 1},
    "data_points": 5
}

@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
# Patch the object where it's looked up (imported inside the function from llm.service)
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock) # Mock async function
async def test_extract_message_context_happy_path_json_no_history(mock_get_history, MockLLMClient):
    """
    Tests extract_message_context with a comprehensive level, mocked JSON LLM response,
    and no historical context requested.
    """
    # --- Arrange ---
    # Configure the mock LLM client instance and its async method
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    mock_llm_response.content = MOCK_LLM_JSON_RESPONSE
    # Configure the async method mock
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)

    # Configure mock history (to return empty dict as it shouldn't be called)
    mock_get_history.return_value = {}

    message = "I'm feeling great and ready to plan my day from my home office. Got about an hour."
    user_profile_id = "user-123"

    # --- Act ---
    result = await extract_message_context(
        message=message,
        user_profile_id=user_profile_id,
        include_historical_context=False, # History not requested
        extraction_level="comprehensive"
    )

    # --- Assert ---
    assert "extracted_context" in result
    context = result["extracted_context"]
    assert context["mood"] == "happy"
    assert context["environment"] == "home office"
    assert context["time_availability"] == "about 1 hour"
    assert context["focus"] == "planning my day"
    assert context["satisfaction"] == "satisfied"
    assert context["extraction_confidence"] == 0.9
    assert "historical_context" not in result # History was not requested
    assert "error" not in result # No error expected

    # Verify LLM was called correctly
    mock_llm_instance.chat_completion.assert_called_once()
    # Example: Check system prompt contains comprehensive instruction
    call_args, call_kwargs = mock_llm_instance.chat_completion.call_args
    messages = call_kwargs.get('messages', [])
    assert len(messages) > 0
    system_prompt = messages[0].get("content", "")
    # Check for the specific instruction text for comprehensive
    assert "perform deep analysis" in system_prompt.lower()
    assert message in messages[1].get("content", "")

    # Verify history was not called because include_historical_context=False
    mock_get_history.assert_not_called()


@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_happy_path_text_parsing(mock_get_history, MockLLMClient):
    """
    Tests extract_message_context when the LLM returns plain text,
    requiring parsing via _extract_field_from_text.
    """
    # --- Arrange ---
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    mock_llm_response.content = MOCK_LLM_TEXT_RESPONSE # Use the plain text mock
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)

    mock_get_history.return_value = {}

    message = "Just grabbing coffee and reading the news. Got maybe 30 mins."
    user_profile_id = "user-456"

    # --- Act ---
    result = await extract_message_context(
        message=message,
        user_profile_id=user_profile_id,
        include_historical_context=False,
        extraction_level="standard" # Use standard level for this test
    )

    # --- Assert ---
    assert "extracted_context" in result
    context = result["extracted_context"]
    # Check values parsed from MOCK_LLM_TEXT_RESPONSE
    assert context["mood"] == "curious"
    assert context["environment"] == "coffee shop"
    assert context["time_availability"] == "30 minutes"
    assert context["focus"] == "reading news"
    assert context["satisfaction"] == "neutral"
    # Confidence defaults to 0.7 when not parsed from text
    assert context["extraction_confidence"] == 0.7
    assert "historical_context" not in result
    assert "error" not in result

    # Verify LLM was called
    mock_llm_instance.chat_completion.assert_called_once()
    # Verify history was not called
    mock_get_history.assert_not_called()


@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_with_history(mock_get_history, MockLLMClient):
    """
    Tests extract_message_context when historical context is requested.
    """
    # --- Arrange ---
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    # Use JSON response for simplicity, focus is on history inclusion
    mock_llm_response.content = MOCK_LLM_JSON_RESPONSE
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)

    # Configure mock history to return specific data
    mock_get_history.return_value = MOCK_HISTORICAL_CONTEXT

    message = "Feeling good today."
    user_profile_id = "user-789"

    # --- Act ---
    result = await extract_message_context(
        message=message,
        user_profile_id=user_profile_id,
        include_historical_context=True, # Request history
        extraction_level="comprehensive"
    )

    # --- Assert ---
    assert "extracted_context" in result
    assert "historical_context" in result # Check history key exists
    assert result["historical_context"] == MOCK_HISTORICAL_CONTEXT # Check history content
    assert "error" not in result

    # Verify LLM was called
    mock_llm_instance.chat_completion.assert_called_once()
    # Verify history was called correctly
    mock_get_history.assert_called_once_with(user_profile_id)


@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_basic_extraction_level(mock_get_history, MockLLMClient):
    """
    Tests extract_message_context with extraction_level='basic' to ensure
    the system prompt reflects the change.
    """
    # --- Arrange ---
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    # Use JSON response, focus is on the prompt sent to LLM
    mock_llm_response.content = MOCK_LLM_JSON_RESPONSE
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)

    mock_get_history.return_value = {}

    message = "Just basic info needed."
    user_profile_id = "user-basic"

    # --- Act ---
    await extract_message_context(
        message=message,
        user_profile_id=user_profile_id,
        include_historical_context=False,
        extraction_level="basic" # Set level to basic
    )

    # --- Assert ---
    # Verify LLM was called
    mock_llm_instance.chat_completion.assert_called_once()
    # Check that the system prompt contains the 'basic' instruction
    call_args, call_kwargs = mock_llm_instance.chat_completion.call_args
    messages = call_kwargs.get('messages', [])
    assert len(messages) > 0
    system_prompt = messages[0].get("content", "")
    # Check for the specific instruction text for basic
    assert "extract only the most obvious" in system_prompt.lower()
    assert "perform deep analysis" not in system_prompt.lower() # Ensure other levels aren't mentioned

    # Verify history was not called
    mock_get_history.assert_not_called()


@pytest.mark.test_type("unit") # Testing error path
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_llm_error(mock_get_history, MockLLMClient):
    """
    Tests extract_message_context error handling when the LLM call fails.
    """
    # --- Arrange ---
    # Configure the mock LLM client to raise an exception
    mock_llm_instance = MockLLMClient.return_value
    error_message = "LLM API unavailable"
    mock_llm_instance.chat_completion = AsyncMock(side_effect=Exception(error_message))

    mock_get_history.return_value = {} # History shouldn't be called if LLM fails first

    message = "This message will cause an error."
    user_profile_id = "user-error-llm"

    # --- Act ---
    result = await extract_message_context(
        message=message,
        user_profile_id=user_profile_id,
        include_historical_context=False,
        extraction_level="comprehensive"
    )

    # --- Assert ---
    assert "extracted_context" in result
    context = result["extracted_context"]
    # Check that default/empty values are returned
    assert context["mood"] == ""
    assert context["environment"] == ""
    assert context["time_availability"] == ""
    assert context["focus"] == ""
    assert context["satisfaction"] == ""
    assert context["extraction_confidence"] == 0.3 # Default confidence on error
    assert context["extracted_entities"] == []
    assert "historical_context" not in result
    assert "error" in result # Check that the error field is present
    assert error_message in result["error"] # Check that the original error message is included

    # Verify LLM was called (which raised the error)
    mock_llm_instance.chat_completion.assert_called_once()
    # Verify history was not called
    mock_get_history.assert_not_called()


@pytest.mark.test_type("unit") # Testing error path
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_history_error(mock_get_history, MockLLMClient):
    """
    Tests extract_message_context error handling when _get_historical_context fails,
    but the LLM call succeeds. The extracted context should still be returned.
    """
    # --- Arrange ---
    # Configure the mock LLM client instance for a successful call
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    mock_llm_response.content = MOCK_LLM_JSON_RESPONSE
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)

    # Configure mock history to raise an exception
    error_message = "Database connection failed"
    mock_get_history.side_effect = Exception(error_message)

    message = "Trying to get history but it will fail."
    user_profile_id = "user-error-history"

    # --- Act ---
    result = await extract_message_context(
        message=message,
        user_profile_id=user_profile_id,
        include_historical_context=True, # Request history (which will fail)
        extraction_level="comprehensive"
    )

    # --- Assert ---
    # Check that context extracted from LLM is still present
    assert "extracted_context" in result
    context = result["extracted_context"]
    assert context["mood"] == "happy"
    assert context["environment"] == "home office"
    assert context["extraction_confidence"] == 0.9

    # Check that historical_context is NOT present due to the error
    assert "historical_context" not in result

    # Check that the main 'error' field is NOT present in the root result
    # The function should log the history error internally but not fail the overall tool execution
    assert "error" not in result

    # Verify LLM was called successfully
    mock_llm_instance.chat_completion.assert_called_once()
    # Verify history was called (which raised the error)
    mock_get_history.assert_called_once_with(user_profile_id)

@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_empty_message(mock_get_history, MockLLMClient):
    """Tests how extract_message_context handles empty or very short messages."""
    # Configure mocks
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    mock_llm_response.content = json.dumps({"mood": "neutral", "extraction_confidence": 0.4})
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)
    
    # Test with empty message
    result = await extract_message_context(
        message="",
        user_profile_id="user-empty",
        include_historical_context=False
    )
    
    # Verify minimal context is extracted with low confidence
    assert result["extracted_context"]["extraction_confidence"] <= 0.5
    assert "mood" in result["extracted_context"]
    
    # Test with very short message
    result = await extract_message_context(
        message="Hi",
        user_profile_id="user-short",
        include_historical_context=False
    )
    
    # Verify system message indicates brevity challenge
    call_args, call_kwargs = mock_llm_instance.chat_completion.call_args
    messages = call_kwargs.get('messages', [])
    system_message = next((m["content"] for m in messages if m["role"] == "system"), "")
    assert "brevity" in system_message.lower() or "limited information" in system_message.lower()

@pytest.mark.test_type("unit") # Testing error/fallback path
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")

@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_malformed_responses(mock_get_history, MockLLMClient):
    """Tests handling of malformed or incomplete LLM responses."""
    # Configure mocks
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    
    # Test with invalid JSON
    mock_llm_response.content = "This is not valid JSON at all"
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)
    
    result = await extract_message_context(
        message="Test message for invalid JSON",
        user_profile_id="user-invalid-json"
    )
    
    # Verify fallback parsing works and assigns the default confidence
    assert "extracted_context" in result
    assert result["extracted_context"]["extraction_confidence"] == 0.7 # Default confidence for text parsing
    
    # Test with partial/malformed JSON
    mock_llm_response.content = '{"mood": "happy", "environment": '  # Truncated JSON
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)
    
    result = await extract_message_context(
        message="Test message for malformed JSON",
        user_profile_id="user-malformed-json"
    )
    
    # Verify tool doesn't crash and provides defaults for missing fields
    assert "extracted_context" in result
    assert "mood" in result["extracted_context"]
    assert result["extracted_context"]["environment"] == ""  # Default value for missing field


@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")

@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_multilingual(mock_get_history, MockLLMClient):
    """Tests extraction from non-English messages."""
    # Configure mocks
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    mock_llm_response.content = json.dumps({
        "mood": "happy",
        "environment": "home",
        "time_availability": "1 hour",
        "focus": "relaxing",
        "extraction_confidence": 0.85
    })
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)
    
    # French message
    french_message = "Je suis content aujourd'hui et je veux me détendre à la maison pendant une heure."
    result = await extract_message_context(
        message=french_message,
        user_profile_id="user-french"
    )
    
    # Verify LLM was called with the non-English message
    mock_llm_instance.chat_completion.assert_called_once()
    call_args, call_kwargs = mock_llm_instance.chat_completion.call_args
    messages = call_kwargs.get('messages', [])
    assert french_message in messages[1].get("content", "")
    
    # Verify context was extracted normally
    assert result["extracted_context"]["mood"] == "happy"
    assert result["extracted_context"]["environment"] == "home"
    assert result["extracted_context"]["time_availability"] == "1 hour"


@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")

@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_historical_context_weighting(mock_get_history, MockLLMClient):
    """
    Tests that historical context is properly incorporated into extraction.
    The historical context should be included in the prompt to the LLM.
    """
    # Configure mocks
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    mock_llm_response.content = json.dumps({
        "mood": "focused",  # Matches historical pattern
        "environment": "home",  # Matches historical pattern
        "time_availability": "2 hours", 
        "focus": "working",
        "extraction_confidence": 0.9
    })
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)
    
    # Configure detailed historical context
    historical_context = {
        "mood_patterns": {"focused": 5, "stressed": 2, "relaxed": 1},
        "environment_patterns": {"home": 7, "office": 1},
        "time_patterns": {"1-2 hours": 4, "30 minutes": 3},
        "typical_activities": ["coding", "reading", "exercise"],
        "data_points": 8
    }
    mock_get_history.return_value = historical_context
    
    # Ambiguous message that could use historical context
    message = "Back to work on the project."
    result = await extract_message_context(
        message=message,
        user_profile_id="user-history-test",
        include_historical_context=True
    )
    
    # Verify LLM was called with the historical context in the prompt
    mock_llm_instance.chat_completion.assert_called_once()
    call_args, call_kwargs = mock_llm_instance.chat_completion.call_args
    messages = call_kwargs.get('messages', [])
    system_message = messages[0].get("content", "")
    
    # System message should mention historical patterns
    assert "historical context" in system_message.lower()
    # Check that specific values from history are included
    assert "focused" in system_message
    assert "home" in system_message
    
    # Verify result includes both extracted context and historical context
    assert "extracted_context" in result
    assert "historical_context" in result
    assert result["historical_context"] == historical_context
    assert result["extracted_context"]["mood"] == "focused"
    assert result["extracted_context"]["environment"] == "home"


@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_advanced_analysis(mock_get_history, MockLLMClient):
    """Tests advanced extraction features like entity extraction and sentiment analysis."""
    # Configure mocks
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    mock_llm_response.content = json.dumps({
        "mood": "frustrated",
        "environment": "office",
        "time_availability": "tight schedule",
        "focus": "meeting preparation",
        "satisfaction": "low",
        "extraction_confidence": 0.85,
        "extracted_entities": ["quarterly review", "presentation", "deadline"],
        "sentiment_analysis": {
            "primary_emotion": "stress",
            "intensity": 7,
            "secondary_emotions": ["worry", "determination"]
        }
    })
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)
    
    message = "I need to prepare for the quarterly review presentation. The deadline is tomorrow and I'm not ready yet."
    result = await extract_message_context(
        message=message,
        user_profile_id="user-advanced",
        extraction_level="comprehensive"
    )
    
    # Verify additional extraction fields are present
    assert "extracted_entities" in result["extracted_context"]
    assert "quarterly review" in result["extracted_context"]["extracted_entities"]
    assert "deadline" in result["extracted_context"]["extracted_entities"]
    
    # If sentiment_analysis is implemented as part of the context
    if "sentiment_analysis" in result["extracted_context"]:
        assert result["extracted_context"]["sentiment_analysis"]["primary_emotion"] == "stress"
        assert result["extracted_context"]["sentiment_analysis"]["intensity"] == 7


@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("extract_message_context")
@pytest.mark.workflow("dispatcher")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
@patch('apps.main.agents.tools.dispatcher_tools._get_historical_context', new_callable=AsyncMock)
async def test_extract_message_context_performance_metrics(mock_get_history, MockLLMClient):
    """Tests that performance metrics are properly tracked."""
    # Configure mocks
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    mock_llm_response.content = MOCK_LLM_JSON_RESPONSE
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)
    
    # Define an async side effect to properly await sleep
    async def side_effect_with_delay(**kwargs):
        await asyncio.sleep(0.1) # Properly await the sleep
        return mock_llm_response

    # Add timing and tracking info to mock using the async side effect
    mock_llm_instance.chat_completion.side_effect = side_effect_with_delay
    
    message = "Test message for performance metrics"
    
    # Configure simple history to track its timing too
    mock_get_history.return_value = {"data_points": 3}
    
    # If the function supports performance tracking
    # Call the function with performance tracking enabled
    result = await extract_message_context(
        message=message,
        user_profile_id="user-perf",
        include_historical_context=True,
        track_performance=True # Re-enabled: Function now accepts this argument
    )
    
    # Verify performance metrics are present in the result
    assert "performance_metrics" in result
    metrics = result["performance_metrics"]
    assert "total_duration_ms" in metrics
    assert "llm_call_duration_ms" in metrics
    assert "history_fetch_duration_ms" in metrics # History was fetched
    assert metrics["total_duration_ms"] > 0
    assert metrics["llm_call_duration_ms"] > 0
    assert metrics["history_fetch_duration_ms"] >= 0 # Can be 0 if history fetch is very fast or fails quickly
    # Check that total is roughly the sum of parts (allowing for overhead)
    assert metrics["total_duration_ms"] >= metrics["llm_call_duration_ms"] + metrics["history_fetch_duration_ms"]



@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.dispatcher")
@pytest.mark.tool("classify_message_intent")
@pytest.mark.workflow("dispatcher")
@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch('apps.main.llm.service.RealLLMClient', autospec=True)
async def test_classify_message_intent(MockLLMClient):
    """
    Tests the classify_message_intent tool, which categorizes user messages
    into workflow types (assuming this tool exists).
    """
    # Configure mocks
    mock_llm_instance = MockLLMClient.return_value
    mock_llm_response = MagicMock()
    mock_llm_response.is_text = True
    mock_llm_response.content = json.dumps({
        "workflow_type": "wheel_generation",
        "confidence": 0.85,
        "reason": "User is explicitly asking for activity suggestions"
    })
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)
    
    from apps.main.agents.tools.dispatcher_tools import classify_message_intent

    # Clear message asking for activities
    # Provide dummy context and profile_completion as required by the function signature
    dummy_context = {"mood": "neutral", "environment": "home"}
    dummy_profile_completion = 0.9

    result = await classify_message_intent(
        message="I'd like some activity suggestions for today",
        context=dummy_context,
        profile_completion=dummy_profile_completion
    )

    # Check the result structure based on the function's actual return value
    # The function returns a dict with a top-level "classification" key
    assert "classification" in result
    classification = result["classification"]
    assert "workflow_type" in classification
    assert classification["workflow_type"] == "wheel_generation"
    assert "confidence" in classification
    assert classification["confidence"] >= 0.8

    # Test with ambiguous message
    mock_llm_response.content = json.dumps({
        "workflow_type": "discussion",
        "confidence": 0.6,
        "reason": "User message is ambiguous, could be general chat",
        "secondary_intents": ["wheel_generation"] # Example secondary intent
    })
    mock_llm_instance.chat_completion = AsyncMock(return_value=mock_llm_response)

    # Provide dummy context and profile_completion for the second call as well
    ambiguous_context = {"mood": "happy"}
    ambiguous_profile_completion = 0.95

    result_ambiguous = await classify_message_intent(
        message="I'm feeling good today",
        context=ambiguous_context,
        profile_completion=ambiguous_profile_completion
    )

    # Check the result structure for the ambiguous case
    assert "classification" in result_ambiguous
    classification_ambiguous = result_ambiguous["classification"]
    assert "workflow_type" in classification_ambiguous
    assert classification_ambiguous["workflow_type"] == "discussion"
    assert "confidence" in classification_ambiguous
    assert classification_ambiguous["confidence"] < 0.7  # Lower confidence for ambiguous messages
    # Check for secondary intents if the mock provides them
    assert "secondary_intents" in result_ambiguous
    assert "wheel_generation" in result_ambiguous["secondary_intents"]
