import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.db import IntegrityError
from django.utils import timezone

# Import the tool function we are testing
from apps.main.agents.tools.update_current_mood_tool import update_current_mood

# Import models needed for setup and assertions
from apps.main.models import HistoryEvent
from apps.user.models import UserProfile, CurrentMood

# Import factories
from tests.factories import UserProfileFactory, CurrentMoodFactory, UserFactory

# Get the User model
User = get_user_model()

# --- Test Data ---
# Use a placeholder ID for input data structure, will use real IDs in tests
TEST_USER_PROFILE_ID = 999
INPUT_DATA_CREATE = {
    'user_profile_id': TEST_USER_PROFILE_ID,
    'description': 'Feeling creative',
    'height': 75,
    'user_awareness': 80,
    'inferred_from_text': False
}
INPUT_DATA_UPDATE = {
    'user_profile_id': TEST_USER_PROFILE_ID,
    'description': 'Feeling focused now',
    'height': 60,
    'user_awareness': 70,
    'inferred_from_text': True
}

# Mark all tests in this module to use the database and run asynchronously
pytestmark = [pytest.mark.django_db, pytest.mark.asyncio]

# --- Integration Test (Verifies DB interaction) ---

@pytest.mark.test_type("integration")
@pytest.mark.component("main.agents.tools.update_current_mood")
@pytest.mark.tool("update_current_mood")
@patch('datetime.datetime')
async def test_integration_mood_creation_and_history(mock_datetime_now, db):
    """
    Integration test: Verifies actual database creation of UserProfile,
    CurrentMood, and HistoryEvent when the tool is called.
    """
    # Configure mock to return real datetime objects
    import datetime
    fixed_time = datetime.datetime(2024, 1, 1, 12, 0, 0, tzinfo=datetime.timezone.utc)
    mock_datetime_now.now.return_value = fixed_time

    # Arrange: Create User and UserProfile directly using async ORM
    # Use a unique username and email to avoid duplicate key errors
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    test_user = await User.objects.acreate(
        username=f'integration_test_user_{unique_id}',
        email=f'integration_{unique_id}@example.com',
        password='password' # Django handles hashing automatically
    )
    user_profile = await UserProfile.objects.acreate(
        user=test_user,
        profile_name=f'Integration Test Profile {unique_id}'
    )
    input_data = {**INPUT_DATA_CREATE, 'user_profile_id': user_profile.id} # Use real ID

    # Pre-assertion: Ensure no mood exists yet
    assert not await CurrentMood.objects.filter(user_profile_id=user_profile.id).aexists()
    assert not await HistoryEvent.objects.filter(user_profile_id=user_profile.id).aexists()

    # Act: Call the tool function
    result = await update_current_mood(input_data)

    # Assert: Check the tool response
    assert 'mood' in result
    assert result['mood']['description'] == input_data['description']
    assert int(result['mood']['height']) == input_data['height']
    assert 'id' in result['mood']
    mood_id = result['mood']['id']

    # Assert: Check database state - Mood record creation
    assert await CurrentMood.objects.filter(user_profile_id=user_profile.id).acount() == 1
    created_mood = await CurrentMood.objects.aget(user_profile_id=user_profile.id)
    assert created_mood.id == mood_id
    assert created_mood.description == input_data['description']
    assert created_mood.height == input_data['height']
    assert created_mood.user_awareness == input_data['user_awareness']
    assert created_mood.inferred_from_text == input_data['inferred_from_text']

    # Assert: Check database state - HistoryEvent creation
    assert await HistoryEvent.objects.filter(user_profile_id=user_profile.id).acount() == 1
    history_event = await HistoryEvent.objects.filter(user_profile_id=user_profile.id).alatest('timestamp')
    assert history_event.event_type == 'mood_explicitly_updated'
    # Need to fetch ContentType within the async context or ensure it's loaded
    current_mood_ct = await ContentType.objects.aget(app_label='user', model='currentmood')
    assert history_event.content_type_id == current_mood_ct.id
    assert str(history_event.object_id) == str(created_mood.id)
    assert history_event.details['description'] == input_data['description']
    assert history_event.details['height'] == input_data['height']


# --- Unit Tests (Using Factories and limited Mocks) ---

@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.update_current_mood")
@pytest.mark.tool("update_current_mood")
# Only patch HistoryEvent and ContentType creation/lookup, use factories for UserProfile and CurrentMood
@patch('django.utils.timezone.datetime.now')
@patch('apps.main.models.HistoryEvent.objects.create') # Sync create
@patch('django.contrib.contenttypes.models.ContentType.objects.get_for_model') # Sync get_for_model
async def test_mood_creation_logic_with_factories(mock_get_content_type_for_model, mock_history_create, mock_datetime_now):
    """
    Unit test: Verifies the tool's logic for creating a new mood,
    using factories for UserProfile and CurrentMood.
    """
    # Configure mock to return real datetime objects
    import datetime
    fixed_time = datetime.datetime(2024, 1, 1, 12, 0, 0, tzinfo=datetime.timezone.utc)
    mock_datetime_now.return_value = fixed_time

    # Arrange: Create a fresh UserProfile for a clean test
    # This avoids using adelete() which is causing issues with GenericRelation
    # Use a unique user profile to avoid unique constraint violations
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    user = UserFactory.create(username=f'test_user_{unique_id}', email=f'test_{unique_id}@example.com')
    user_profile = UserProfileFactory.create(user=user, profile_name=f'Test Profile {unique_id}')

    # Use the new user profile ID
    input_data = {**INPUT_DATA_CREATE, 'user_profile_id': user_profile.id} # Use real ID

    # Create a proper mock with both id and pk attributes
    mock_content_type = MagicMock(spec=ContentType)
    mock_content_type.id = 5 # Example ContentType ID
    mock_content_type.pk = 5 # Set pk to match id
    mock_get_content_type_for_model.return_value = mock_content_type # Mock sync get_for_model

    # Act: Call the tool function
    result = await update_current_mood(input_data)

    # Assert: Check the tool response
    assert 'mood' in result
    assert result['mood']['description'] == input_data['description']
    assert int(result['mood']['height']) == input_data['height']
    assert 'id' in result['mood']
    mood_id = result['mood']['id']

    # Assert: Check database state - Mood record creation
    # Since we are not mocking CurrentMood.objects.create, check the actual DB
    assert await CurrentMood.objects.filter(user_profile_id=user_profile.id).acount() == 1
    created_mood = await CurrentMood.objects.aget(user_profile_id=user_profile.id)
    assert str(created_mood.id) == str(mood_id)  # Convert both to string for comparison
    assert created_mood.description == input_data['description']
    assert created_mood.height == input_data['height']
    assert created_mood.user_awareness == input_data['user_awareness']
    assert created_mood.inferred_from_text == input_data['inferred_from_text']

    mock_get_content_type_for_model.assert_called_once_with(CurrentMood) # Check sync get_for_model
    mock_history_create.assert_called_once() # Check sync create
    # Check args passed to history create
    _, hist_call_kwargs = mock_history_create.call_args
    assert hist_call_kwargs.get('user_profile') == user_profile
    assert hist_call_kwargs.get('event_type') == 'mood_explicitly_updated' # Based on INPUT_DATA_CREATE
    assert hist_call_kwargs.get('content_type') == mock_content_type
    # Convert both to integers for comparison
    assert int(hist_call_kwargs.get('object_id')) == int(created_mood.id)
    assert hist_call_kwargs.get('details')['description'] == INPUT_DATA_CREATE['description']

    # Check result structure
    assert 'mood' in result
    # Convert both to integers for comparison
    assert int(result['mood']['id']) == int(created_mood.id)
    assert result['mood']['description'] == INPUT_DATA_CREATE['description']
    assert int(result['mood']['height']) == INPUT_DATA_CREATE['height']
    assert int(result['mood']['user_awareness']) == INPUT_DATA_CREATE['user_awareness']
    # Don't check for 'previous_mood' as it might be present but empty in some cases


@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.update_current_mood")
@pytest.mark.tool("update_current_mood")
# Only patch HistoryEvent and ContentType creation/lookup, use factories for UserProfile and CurrentMood
@patch('django.utils.timezone.datetime.now')
@patch('apps.main.models.HistoryEvent.objects.create') # Sync create
@patch('django.contrib.contenttypes.models.ContentType.objects.get_for_model') # Sync get_for_model
async def test_mood_update_logic_with_factories(mock_get_content_type_for_model, mock_history_create, mock_datetime_now):
    """
    Unit test: Verifies the tool's logic for updating an existing mood,
    using factories for UserProfile and CurrentMood.
    """
    # Configure mock to return real datetime objects
    import datetime
    fixed_time = datetime.datetime(2024, 1, 1, 12, 0, 0, tzinfo=datetime.timezone.utc)
    mock_datetime_now.return_value = fixed_time

    # Arrange: Create a fresh UserProfile for a clean test
    # This avoids using adelete() which is causing issues with GenericRelation
    # Use a unique user profile to avoid unique constraint violations
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    user = UserFactory.create(username=f'test_user_{unique_id}', email=f'test_{unique_id}@example.com')
    user_profile = UserProfileFactory.create(user=user, profile_name=f'Test Profile {unique_id}')

    # Create a new mood with all required fields
    existing_mood = CurrentMoodFactory.create( # Use sync create
        user_profile=user_profile,
        description="Old description",
        height=50,
        user_awareness=55,
        inferred_from_text=False,
        processed_at=timezone.now(),
        effective_start=timezone.now(),
        effective_end=timezone.now(),
        duration_estimate="1 day"
    )

    input_data = {**INPUT_DATA_UPDATE, 'user_profile_id': user_profile.id} # Use real ID

    # Create a proper mock with a pk attribute
    mock_content_type = MagicMock(spec=ContentType)
    mock_content_type.pk = 1  # Set a valid integer pk
    mock_get_content_type_for_model.return_value = mock_content_type # Mock sync get_for_model

    # Act: Call the tool function
    result = await update_current_mood(input_data)

    # Assert: Check the tool response
    assert 'mood' in result
    assert result['mood']['description'] == input_data['description'] # Should reflect the update
    assert result['mood']['height'] == input_data['height']
    assert result['mood']['user_awareness'] == input_data['user_awareness']
    assert 'id' in result['mood']
    # Convert both to integers for comparison
    assert int(result['mood']['id']) == int(existing_mood.id)

    # Assert: Check database state - Mood record update
    # Since we are not mocking CurrentMood.objects.get/create, check the actual DB
    assert await CurrentMood.objects.filter(user_profile_id=user_profile.id).acount() == 1 # Still only one mood
    updated_mood = await CurrentMood.objects.aget(user_profile_id=user_profile.id)
    assert updated_mood.id == existing_mood.id # Same ID
    assert updated_mood.description == input_data['description'] # Updated field
    assert updated_mood.height == input_data['height'] # Updated field
    assert updated_mood.user_awareness == input_data['user_awareness'] # Updated field
    assert updated_mood.inferred_from_text == input_data['inferred_from_text'] # Updated field

    mock_history_create.assert_called_once() # History should still be created on update
    _, hist_call_kwargs = mock_history_create.call_args
    assert hist_call_kwargs.get('user_profile') == user_profile
    assert hist_call_kwargs.get('event_type') == 'mood_inferred' # Based on INPUT_DATA_UPDATE
    assert hist_call_kwargs.get('content_type') == mock_content_type
    # Convert both to integers for comparison
    assert int(hist_call_kwargs.get('object_id')) == int(updated_mood.id)
    assert hist_call_kwargs.get('details')['description'] == INPUT_DATA_UPDATE['description']

    assert 'previous_mood' in result['mood'] # Should exist on update
    assert result['mood']['previous_mood']['description'] == "Old description" # Check previous mood data
    assert result['mood']['previous_mood']['height'] == 50
    assert result['mood']['previous_mood']['user_awareness'] == 55


# Patch models where they are looked up within the 'update_current_mood' function
# Note: Patching synchronous methods used within the async tool function
@pytest.mark.test_type("unit") # Testing error path
@pytest.mark.component("main.agents.tools.update_current_mood")
@pytest.mark.tool("update_current_mood")
@patch('apps.user.models.UserProfile.objects.aget') # Patch async get to simulate DoesNotExist
@patch('apps.main.models.HistoryEvent.objects.create') # Sync create (should not be called)
@patch('django.contrib.contenttypes.models.ContentType.objects.get_for_model') # Sync get_for_model
async def test_error_handling_invalid_user_with_factories(mock_get_content_type_for_model, mock_history_create, mock_profile_get):
    """
    Unit test: Verifies error handling when the user_profile_id is invalid,
    using factories for other potential interactions.
    """
    # Arrange
    mock_profile_get.side_effect = UserProfile.DoesNotExist("User profile not found") # Mock sync get

    # Create a proper mock with both id and pk attributes
    mock_content_type = MagicMock(spec=ContentType)
    mock_content_type.id = 5 # Example ContentType ID
    mock_content_type.pk = 5 # Set pk to match id
    mock_get_content_type_for_model.return_value = mock_content_type # Mock sync get_for_model

    # Act
    result = await update_current_mood(INPUT_DATA_CREATE)

    # Assert
    mock_profile_get.assert_called_once_with(id=TEST_USER_PROFILE_ID) # Check sync get with test ID
    mock_history_create.assert_not_called() # History should not be created

    assert 'error' in result
    # Assert the specific error message format returned by the tool
    assert f"User profile with ID {TEST_USER_PROFILE_ID} not found" in result['error']
    assert 'mood' not in result # No mood data should be present on error


# Patch models where they are looked up within the 'update_current_mood' function
# Note: Patching both synchronous and asynchronous methods used within the async tool function
@pytest.mark.test_type("unit") # Testing error path
@pytest.mark.component("main.agents.tools.update_current_mood")
@pytest.mark.tool("update_current_mood")
@patch('apps.user.models.UserProfile.objects.aget') # Patch async get to simulate success
@patch('apps.user.models.CurrentMood.objects.aget') # Patch async get to simulate DoesNotExist
@patch('apps.user.models.CurrentMood.objects.acreate') # Patch async create to simulate IntegrityError
@patch('apps.main.models.HistoryEvent.objects.create') # Sync create (should not be called)
@patch('django.contrib.contenttypes.models.ContentType.objects.get_for_model') # Sync get_for_model
async def test_error_handling_db_error_on_create_with_factories(mock_get_content_type_for_model, mock_history_create, mock_mood_acreate, mock_mood_get, mock_profile_get):
    """
    Unit test: Verifies error handling when the database operation fails
    (simulating error during the 'create' phase after DoesNotExist),
    using factories for UserProfile.
    """
    # Arrange: Create UserProfile using factory
    user_profile = UserProfileFactory.create() # Use sync create
    mock_profile_get.return_value = user_profile # Mock async get to return the real instance

    mock_mood_get.side_effect = CurrentMood.DoesNotExist # Simulate mood not found

    # Create a proper mock with both id and pk attributes
    mock_content_type = MagicMock(spec=ContentType)
    mock_content_type.id = 5 # Example ContentType ID
    mock_content_type.pk = 5 # Set pk to match id
    mock_get_content_type_for_model.return_value = mock_content_type # Mock sync get_for_model

    db_error_message = "Database connection lost"
    mock_mood_acreate.side_effect = IntegrityError(db_error_message) # Simulate DB error on async create

    input_data = {**INPUT_DATA_CREATE, 'user_profile_id': user_profile.id} # Use real ID

    # Act
    result = await update_current_mood(input_data)

    # Assert
    mock_profile_get.assert_called_once_with(id=user_profile.id) # Check sync get with real ID
    mock_mood_get.assert_called_once_with(user_profile_id=user_profile.id) # Check sync get with real profile ID
    mock_mood_acreate.assert_called_once() # DB async create call was attempted
    mock_history_create.assert_not_called() # History should not be created if mood create fails

    assert 'error' in result
    assert db_error_message in result['error']
    assert 'mood' not in result
