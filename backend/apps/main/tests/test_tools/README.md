# Agent Tool Implementation Testing (`test_tools/`)

This directory (`backend/apps/main/tests/test_tools/`) houses integration tests specifically focused on verifying the **correct implementation and database interactions** of individual agent tools. Agent tools are the Python functions decorated with `@register_tool` (typically found in `apps.main.agents.tools.*`) that agents call to perform actions, often involving direct Django ORM operations.

These tests complement the agent logic tests found in `test_agents/`, which primarily focus on the agent's decision-making process and *whether* it calls the correct tool, often mocking the tool's execution or response. Here, we ensure the tools themselves function as expected when they *are* called.

## Testing Philosophy & Approach

*   **Integration Testing:** We primarily use integration tests that interact with a **real PostgreSQL test database**, configured via `backend/config/settings/test.py`. This is crucial because tools often perform critical database reads and writes using the Django ORM. Migrations must be applied successfully to this test database.
*   **Database Setup:** The `@pytest.mark.django_db` marker ensures `pytest-django` manages the test database lifecycle based on the PostgreSQL settings. *(Note: Ignore the SQLite override attempt in the root `backend/conftest.py`; the `django_db` marker takes precedence here).*
*   **Isolation:** Each test function runs within its own database transaction, ensuring that tests do not interfere with each other. Data created for one test is rolled back before the next one runs.
*   **Realism:** Tests execute the actual tool code against a database populated with realistic data structures, providing high confidence in the tool's behavior.
*   **Readability & Maintainability:** While `factory-boy` was previously used, the current approach often involves creating necessary model instances directly within test functions or using simpler fixtures for setup. Fixtures from parent `conftest.py` files are available, but these tests generally **do not** use the `MockDatabaseService` common in agent-level tests (`test_agents/`).

## Key Components & Structure

1.  **`conftest.py` (Local Fixtures):**
    *   Currently, this file (`backend/apps/main/tests/test_tools/conftest.py`) is minimal and does not define specific fixtures for tool tests.
    *   Tool tests rely on fixtures inherited from parent `conftest.py` files (like `backend/apps/main/tests/conftest.py`) if needed, but primarily use `@pytest.mark.django_db` for database setup.

2.  **`test_tool_*.py` (Test Files):**
    *   Each file contains tests for a *single* agent tool (e.g., `test_tool_update_current_mood.py`).
    *   Tests are `async def` functions (since tools are async) marked with:
        *   `@pytest.mark.django_db`: Enables database access within an isolated transaction for the test.
        *   `@pytest.mark.asyncio`: Allows running `async` test functions and `await`ing async code (like the tool function and async ORM calls).
    *   Tests may request shared fixtures (e.g., from `backend/apps/main/tests/conftest.py`) if applicable, but often create necessary data directly.
    *   **Arrange-Act-Assert Pattern:**
        *   **Arrange:** Use **async ORM methods** (`Model.objects.acreate()`, etc.) to set up required database records directly within the test function. Prepare the `input_data` dictionary for the tool. Perform pre-assertions using async ORM calls to verify the initial database state.
        *   **Act:** `await` the execution of the actual tool function with the `input_data`.
        *   **Assert:**
            *   Verify the structure and content of the dictionary returned by the tool.
            *   **Crucially:** Query the database using **async ORM methods** (`Model.objects.aget()`, `.afilter().aexists()`, `.acount()`, etc.) to confirm the expected records were created, updated, or deleted correctly.

## Relationship to Seeding Commands

*   **Generic Seeds (e.g., `seed_db_10_hexacos`, `seed_db_30_domains`):**
    *   **Recommended Usage:** Run these commands **once per test session** using a `session-scoped` fixture defined in a higher-level `conftest.py` (e.g., `backend/apps/main/tests/conftest.py` or `backend/conftest.py`). This efficiently provides stable, read-only baseline data (like `GenericTrait`, `GenericDomain`) that factories and tests can rely on.
    *   **Factory Interaction:** Factories like `UserTraitInclinationFactory` can link to this session-seeded data (e.g., by using `GenericTraitFactory` with `django_get_or_create = ('code',)` to find existing generic traits).
    *   See example session fixture structure in the planning discussion or `pytest-django` documentation.

*   **User-Specific Seeds (e.g., `seed_db_phiphi.py`):**
    *   **Role:** Serves as a **blueprint/inspiration** for the default values defined within the `factory-boy` factories in `factories.py`. It defines what a "standard" populated user looks like.
    *   **Usage:** Keep it updated to reflect current models for blueprint accuracy and potential manual development setup.
    *   **DO NOT CALL DIRECTLY IN TESTS:** Calling this command within tests (especially session-scoped) creates shared state, leading to unreliable and interfering tests. Use factories to generate isolated instances *based on* this blueprint instead.

## Running and Debugging

*   **Discovery:** `pytest` should automatically discover tests in this directory if run from the `backend/` directory or if configured in `pytest.ini`.
*   **Execution:**
    ```bash
    # Run all tests in this directory (from backend/)
    pytest apps/main/tests/test_tools/

    # Run a specific test file
    pytest apps/main/tests/test_tools/test_tool_update_current_mood.py

    # Run a specific test function
    pytest apps/main/tests/test_tools/test_tool_update_current_mood.py::test_mood_creation
    ```
*   **Verbose Output / Logging:** Use `pytest` flags for more output:
    *   `-s`: Shows `print` statements (like the ones added to the example test).
    *   `-v`: Increases verbosity.
    *   `--log-cli-level=INFO`: If using standard Python logging, shows INFO level messages.
    ```bash
    pytest -s -v apps/main/tests/test_tools/test_tool_update_current_mood.py
    ```
*   **Database Requirement:** Ensure a PostgreSQL server is running and accessible using the credentials defined in `backend/config/settings/test.py` (`mydb`, `postgres`/`postgres` on `localhost:5432` by default). The test runner needs privileges to create the `test_mydb` database.
*   **IDE Integration:** Tests should be discoverable and runnable via IDE integrations like the VS Code Python testing panel (ensure it's configured for `pytest`). This allows setting breakpoints for debugging.

By following this structure, we ensure that our agent tools are robust, interact correctly with the PostgreSQL test database using the async ORM, and can be maintained effectively alongside the rest of the codebase.
