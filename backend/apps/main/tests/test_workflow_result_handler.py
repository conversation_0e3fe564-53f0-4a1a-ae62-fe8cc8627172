# backend/apps/main/tests/test_workflow_result_handler.py
import pytest
import json
from unittest.mock import patch, MagicMock, AsyncMock, call # Import AsyncMock
from apps.main.services.workflow_result_handler import WorkflowResultHandler
# Removed unused async_to_sync import from here if it existed implicitly via patch target

@pytest.mark.parametrize("workflow_type, expected_messages", [
    (
        "wheel_generation", 
        [
            {"type": "processing_status", "status": "completed"},
            {"type": "chat_message", "content": "I've generated some activities for you.", "is_user": False},
            {"type": "wheel_data"}
        ]
    ),
    (
        "activity_feedback", 
        [
            {"type": "processing_status", "status": "completed"},
            {"type": "chat_message", "content": "Thanks for your feedback!", "is_user": False}
        ]
    ),
    (
        "user_onboarding", 
        [
            {"type": "processing_status", "status": "completed"},
            {"type": "chat_message", "content": "Welcome to the Game of Life!", "is_user": False}
        ]
    ),
    (
        "post_spin", 
        [
            {"type": "processing_status", "status": "completed"},
            {"type": "chat_message", "content": "Here are the details for your selected activity.", "is_user": False},
            {"type": "activity_details"}
        ]
    )
])
@pytest.mark.test_type("unit")
@pytest.mark.component("main.services.workflow_result_handler")
def test_format_result_messages(workflow_type, expected_messages):
    """Test formatting of result messages based on workflow type."""
    # Create the handler
    handler = WorkflowResultHandler()
    
    # Create a sample result with necessary fields
    result = {
        "output_data": {
            "user_response": "This is a test response",
            "wheel": {
                "name": "Test Wheel",
                "items": [
                    {
                        "id": "item-1",
                        "name": "Test Activity",
                        "description": "Test Description",
                        "percentage": 20.0,
                        "color": "#66BB6A",
                        "domain": "creative",
                        "base_challenge_rating": 50,
                        "activity_tailored_id": "act-123"
                    }
                ]
            },
            "activity_details": {
                "id": "act-123",
                "name": "Test Activity",
                "description": "Test Description",
                "instructions": "Test Instructions"
            }
        }
    }
    
    # Call the method
    formatted_messages = handler._format_result_messages(result, workflow_type)
    
    # Verify the message types match expected types
    message_types = [msg["type"] for msg in formatted_messages]
    expected_types = [msg["type"] for msg in expected_messages]
    
    # Check that all expected message types are present
    for expected_type in expected_types:
        assert expected_type in message_types, f"Expected message type '{expected_type}' not found"
    
    # For chat messages, verify the content exists
    chat_messages = [msg for msg in formatted_messages if msg["type"] == "chat_message"]
    if chat_messages:
        for msg in chat_messages:
            assert "content" in msg
            assert "is_user" in msg
            assert isinstance(msg["content"], str)
    
    # For wheel data, verify wheel structure
    wheel_data_messages = [msg for msg in formatted_messages if msg["type"] == "wheel_data"]
    if wheel_data_messages and "wheel" in result.get("output_data", {}):
        assert "wheel" in wheel_data_messages[0]
        assert "name" in wheel_data_messages[0]["wheel"]
        assert "items" in wheel_data_messages[0]["wheel"]
    
    # For activity details, verify details structure
    activity_messages = [msg for msg in formatted_messages if msg["type"] == "activity_details"]
    if activity_messages and "activity_details" in result.get("output_data", {}):
            assert "details" in activity_messages[0]

@pytest.mark.test_type("unit")
@pytest.mark.component("main.services.workflow_result_handler")
@pytest.mark.asyncio # Mark test as async
async def test_process_result(): # Make test function async
    """Test the complete process_result method with channel layer integration."""
    # Mock the channel layer using AsyncMock for group_send
    mock_channel_layer = MagicMock()
    mock_channel_layer.group_send = AsyncMock() # Use AsyncMock

    # Create the handler with the mocked channel layer
    handler = WorkflowResultHandler()
    handler.channel_layer = mock_channel_layer

    # No need to patch async_to_sync when calling async method directly

    # Create test data
    workflow_id = "test-workflow-123"
    task_id = "test-task-456"
    workflow_type = "wheel_generation"

    result = {
        "user_ws_session_name": "test-session-789",
        "user_profile_id": "test-user-123",
        "output_data": {
            "user_response": "Here are some activities for you.",
            "wheel": {
                "name": "Activity Wheel",
                "items": [
                    {
                        "id": "item-1",
                        "name": "Creative Writing",
                        "percentage": 25.0,
                        "color": "#66BB6A",
                        "activity_tailored_id": "act-123"
                    }
                ]
            }
        }
    }

    # Call the async method directly
    await handler.process_result(workflow_id, result, task_id, workflow_type)

    # Verify channel_layer.group_send was awaited the correct number of times
    # Expected: processing_status, chat_message, wheel_data, workflow_status = 4 calls
    assert mock_channel_layer.group_send.await_count == 4

    # Verify all calls used the correct WebSocket session
    for call_args in mock_channel_layer.group_send.await_args_list:
        assert call_args[0][0] == "test-session-789"

    # Verify the workflow status update was sent
    workflow_status_call = None
    for call_args in mock_channel_layer.group_send.await_args_list:
        if call_args[0][1].get("type") == "workflow_status":
            workflow_status_call = call_args
            break

    assert workflow_status_call is not None
    assert workflow_status_call[0][1]["workflow_id"] == workflow_id
    assert workflow_status_call[0][1]["status"] == "completed"

@pytest.mark.test_type("unit")
@pytest.mark.component("main.services.workflow_result_handler")
@pytest.mark.asyncio # Mark test as async
async def test_handle_error(): # Make test function async
    """Test error handling in the workflow result handler."""
    # Mock the channel layer using AsyncMock for group_send
    mock_channel_layer = MagicMock()
    mock_channel_layer.group_send = AsyncMock() # Use AsyncMock

    # Create the handler with the mocked channel layer
    handler = WorkflowResultHandler()
    handler.channel_layer = mock_channel_layer

    # No need to patch async_to_sync when calling async method directly

    # Call the error handler directly (it uses async_to_sync internally, but the mock handles the await)
    workflow_id = "test-workflow-123"
    error = "Test error message"
    task_id = "test-task-456"
    user_ws_session = "test-session-789"

    # Call the async handle_error method directly using await
    await handler.handle_error(workflow_id, error, task_id, workflow_type="wheel_generation", user_ws_session=user_ws_session)

    # Verify error messages were sent via group_send using await
    # Expected: error, processing_status, workflow_status = 3 calls
    assert mock_channel_layer.group_send.await_count == 3

    # Verify all calls used the correct WebSocket session
    for call_args in mock_channel_layer.group_send.await_args_list:
        assert call_args[0][0] == user_ws_session

    # Verify the error message was sent
    error_call = None
    for call_args in mock_channel_layer.group_send.await_args_list:
        if call_args[0][1].get("type") == "error":
            error_call = call_args
            break

    assert error_call is not None
    assert "content" in error_call[0][1]

    # Verify the workflow status update was sent
    workflow_status_call = None
    for call_args in mock_channel_layer.group_send.await_args_list:
        if call_args[0][1].get("type") == "workflow_status":
            workflow_status_call = call_args
            break

    assert workflow_status_call is not None
    assert workflow_status_call[0][1]["workflow_id"] == workflow_id
    assert workflow_status_call[0][1]["status"] == "failed"


@pytest.mark.test_type("unit")
@pytest.mark.component("main.services.workflow_result_handler")
@pytest.mark.asyncio # Mark test as async
async def test_fallback_session_name(): # Make test function async
    """Test that the handler falls back to 'game' session if no WebSocket session is provided."""
    # Mock the channel layer using AsyncMock
    mock_channel_layer = MagicMock()
    mock_channel_layer.group_send = AsyncMock() # Use AsyncMock

    # Create the handler with the mocked channel layer
    handler = WorkflowResultHandler()
    handler.channel_layer = mock_channel_layer

    # No need to patch async_to_sync

    # Create test data with no WebSocket session
    workflow_id = "test-workflow-123"
    task_id = "test-task-456"
    workflow_type = "wheel_generation"

    result = {
        # No user_ws_session_name provided
        "output_data": {
            "user_response": "Here are some activities for you."
        }
    }

    # Call the async method directly
    await handler.process_result(workflow_id, result, task_id, workflow_type)

    # Verify channel_layer.group_send was awaited with the default 'game' session
    assert mock_channel_layer.group_send.await_count >= 1 # At least processing_status and workflow_status
    assert mock_channel_layer.group_send.await_args_list[0][0][0] == "game"
