"""
Tests for the chronological events view used by the Logs plugin dashboard.
"""

import pytest
from django.test import TestCase
from django.db import connection
from datetime import datetime, timedelta
from apps.main.models import BenchmarkRun, BenchmarkScenario, GenericAgent, LLMConfig
from tests.factories import (
    BenchmarkRunFactory,
    BenchmarkScenarioFactory,
    GenericAgentFactory,
    LLMConfigFactory
)


class ChronologicalEventsViewTest(TestCase):
    """Test the grafana_chronological_events view."""

    def setUp(self):
        """Set up test data."""
        # Create test LLM config
        self.llm_config = LLMConfigFactory(
            name="test-gpt-4",
            model_name="gpt-4",
            temperature=0.7
        )

        # Create test agent
        self.agent = GenericAgentFactory(
            role="wheel_generation"
        )

        # Create test scenario
        self.scenario = BenchmarkScenarioFactory(
            name="test_scenario",
            agent_role="wheel_generation",
            description="Test scenario for chronological events",
            input_data={"test": "data"},
            metadata={"test": "metadata"}
        )

    def test_view_exists(self):
        """Test that the chronological events view exists."""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.views
                WHERE table_name = 'grafana_chronological_events'
            """)
            result = cursor.fetchone()
            self.assertEqual(result[0], 1, "grafana_chronological_events view should exist")

    def test_stage_events_extraction(self):
        """Test extraction of stage events from benchmark runs."""
        # Create benchmark run with stage performance details
        stage_performance_details = {
            "initialization": {
                "mean_duration_ms": 150.0,
                "median_duration_ms": 145.0,
                "min_duration_ms": 120.0,
                "max_duration_ms": 180.0,
                "std_dev_ms": 15.0,
                "count": 5
            },
            "processing": {
                "mean_duration_ms": 300.0,
                "median_duration_ms": 295.0,
                "min_duration_ms": 250.0,
                "max_duration_ms": 350.0,
                "std_dev_ms": 25.0,
                "count": 5
            }
        }

        benchmark_run = BenchmarkRunFactory(
            scenario=self.scenario,
            agent_definition=self.agent,
            llm_config=self.llm_config,
            stage_performance_details=stage_performance_details,
            success_rate=0.85,
            semantic_score=7.5,
            parameters={
                "context_variables": {
                    "trust_level": 75,
                    "mood": {"valence": 0.5, "arousal": 0.3},
                    "environment": {"stress_level": 25}
                }
            }
        )

        # Query the view for stage events
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    event_name,
                    event_type,
                    event_details,
                    success_rate,
                    semantic_score,
                    trust_level
                FROM grafana_chronological_events
                WHERE run_id = %s AND event_type = 'stage'
                ORDER BY event_timestamp
            """, [benchmark_run.id])

            results = cursor.fetchall()

            # Should have 2 stage events
            self.assertEqual(len(results), 2)

            # Check first stage event
            event_name, event_type, event_details, success_rate, semantic_score, trust_level = results[0]
            self.assertEqual(event_name, "initialization")
            self.assertEqual(event_type, "stage")
            self.assertEqual(float(success_rate), 0.85)
            self.assertEqual(float(semantic_score), 7.5)
            self.assertEqual(trust_level, 75)

            # Check event details JSON structure
            import json
            details = json.loads(event_details)
            self.assertEqual(details["stage_name"], "initialization")
            self.assertEqual(details["status"], "COMPLETED")
            self.assertEqual(details["mean_duration_ms"], 150.0)
            self.assertEqual(details["count"], 5)

    def test_tool_events_extraction(self):
        """Test extraction of tool call events from benchmark runs."""
        # Create benchmark run with tool breakdown
        tool_breakdown = {
            "memory_search": 5,
            "web_search": 3,
            "calculation": 1
        }

        benchmark_run = BenchmarkRunFactory(
            scenario=self.scenario,
            agent_definition=self.agent,
            llm_config=self.llm_config,
            tool_breakdown=tool_breakdown,
            success_rate=0.9,  # High success rate for "high" effectiveness
            semantic_score=8.2
        )

        # Query the view for tool events
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    event_name,
                    event_type,
                    event_details
                FROM grafana_chronological_events
                WHERE run_id = %s AND event_type = 'tool_call'
                ORDER BY event_name
            """, [benchmark_run.id])

            results = cursor.fetchall()

            # Should have 3 tool events (one for each tool with count > 0)
            self.assertEqual(len(results), 3)

            # Check tool events
            tool_names = [result[0] for result in results]
            self.assertIn("memory_search", tool_names)
            self.assertIn("web_search", tool_names)
            self.assertIn("calculation", tool_names)

            # Check effectiveness calculation for high-performing run
            import json
            for event_name, event_type, event_details in results:
                details = json.loads(event_details)
                if event_name == "memory_search":
                    self.assertEqual(details["call_count"], "5")
                    self.assertEqual(details["effectiveness"], "high")  # success_rate > 0.7

    def test_evaluation_events_extraction(self):
        """Test extraction of semantic evaluation events."""
        benchmark_run = BenchmarkRunFactory(
            scenario=self.scenario,
            agent_definition=self.agent,
            llm_config=self.llm_config,
            semantic_score=6.5,
            semantic_evaluation_details="Good performance with minor issues"
        )

        # Query the view for evaluation events
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    event_name,
                    event_type,
                    event_details
                FROM grafana_chronological_events
                WHERE run_id = %s AND event_type = 'evaluation'
            """, [benchmark_run.id])

            results = cursor.fetchall()

            # Should have 1 evaluation event
            self.assertEqual(len(results), 1)

            event_name, event_type, event_details = results[0]
            self.assertEqual(event_name, "semantic_evaluation")
            self.assertEqual(event_type, "evaluation")

            # Check event details
            import json
            details = json.loads(event_details)
            self.assertEqual(details["semantic_score"], 6.5)
            self.assertEqual(details["quality_category"], "good")  # score >= 6
            self.assertIn("Good performance", details["evaluation_details"])

    def test_context_variables_extraction(self):
        """Test extraction and categorization of context variables."""
        benchmark_run = BenchmarkRunFactory(
            scenario=self.scenario,
            agent_definition=self.agent,
            llm_config=self.llm_config,
            parameters={
                "context_variables": {
                    "trust_level": 85,
                    "mood": {"valence": -0.4, "arousal": 0.6},
                    "environment": {"stress_level": 75}
                }
            },
            stage_performance_details={
                "test_stage": {
                    "mean_duration_ms": 1000.0,
                    "count": 1
                }
            }
        )

        # Query the view for context variables
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    trust_level,
                    mood_valence,
                    stress_level
                FROM grafana_chronological_events
                WHERE run_id = %s
                LIMIT 1
            """, [benchmark_run.id])

            result = cursor.fetchone()
            trust_level, mood_valence, stress_level = result

            self.assertEqual(trust_level, 85)
            self.assertEqual(float(mood_valence), -0.4)
            self.assertEqual(stress_level, 75)

    def test_context_variables_extraction_complex_format(self):
        """Test that context variables are properly extracted from complex object parameters."""
        # Create a benchmark run with complex context variables (new format)
        benchmark_run = BenchmarkRunFactory(
            scenario=self.scenario,
            agent_definition=self.agent,
            llm_config=self.llm_config,
            parameters={
                "context_variables": {
                    "trust_level": {
                        "label": "Foundation (0-39)",
                        "range": "0-39",
                        "value": 20
                    },
                    "arousal": {
                        "label": "Calm (-1.0 to 0.0)",
                        "range": "-1.0-0.0",
                        "value": -0.8
                    },
                    "valence": {
                        "label": "Negative (-1.0 to 0.0)",
                        "range": "-1.0-0.0",
                        "value": -0.8
                    },
                    "stress_level": {
                        "label": "Medium (31-70)",
                        "range": "31-70",
                        "value": 45
                    }
                }
            },
            stage_performance_details={
                "test_stage": {
                    "mean_duration_ms": 1000.0,
                    "count": 1
                }
            }
        )

        # Query the view for context variables
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    trust_level,
                    mood_valence,
                    stress_level
                FROM grafana_chronological_events
                WHERE run_id = %s
                LIMIT 1
            """, [benchmark_run.id])

            result = cursor.fetchone()
            trust_level, mood_valence, stress_level = result

            self.assertEqual(trust_level, 20)
            self.assertEqual(float(mood_valence), -0.8)
            self.assertEqual(stress_level, 45)

    def test_view_performance(self):
        """Test that the view performs reasonably with multiple runs."""
        # Create multiple benchmark runs
        runs = []
        for i in range(10):
            run = BenchmarkRunFactory(
                scenario=self.scenario,
                agent_definition=self.agent,
                llm_config=self.llm_config,
                stage_performance_details={
                    f"stage_{i}": {
                        "mean_duration_ms": (i + 1) * 100.0,
                        "count": 1
                    }
                },
                tool_breakdown={"test_tool": i + 1}
            )
            runs.append(run)

        # Query the view and measure basic performance
        import time
        start_time = time.time()

        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT COUNT(*)
                FROM grafana_chronological_events
                WHERE run_id IN %s
            """, [tuple(run.id for run in runs)])

            count = cursor.fetchone()[0]

        end_time = time.time()
        query_time = end_time - start_time

        # Should have events for all runs (stages + tools + evaluations)
        self.assertGreater(count, 20)  # At least 2 events per run

        # Query should complete reasonably quickly
        self.assertLess(query_time, 1.0, "View query should complete within 1 second")

    def test_chronological_ordering(self):
        """Test that events are properly ordered chronologically."""
        base_time = datetime.now()

        # Create benchmark run with multiple stages
        stage_performance_details = {
            "first_stage": {
                "mean_duration_ms": 100.0,
                "count": 1
            },
            "second_stage": {
                "mean_duration_ms": 200.0,
                "count": 1
            },
            "third_stage": {
                "mean_duration_ms": 150.0,
                "count": 1
            }
        }

        benchmark_run = BenchmarkRunFactory(
            scenario=self.scenario,
            agent_definition=self.agent,
            llm_config=self.llm_config,
            stage_performance_details=stage_performance_details,
            execution_date=base_time
        )

        # Query events in chronological order
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    event_name,
                    event_timestamp
                FROM grafana_chronological_events
                WHERE run_id = %s AND event_type = 'stage'
                ORDER BY event_timestamp
            """, [benchmark_run.id])

            results = cursor.fetchall()

            # Should have 3 stage events
            self.assertEqual(len(results), 3)

            # All events should have the same base timestamp since they're from stage_performance_details
            # but with slight offsets for ordering
            stage_names = [result[0] for result in results]
            self.assertIn("first_stage", stage_names)
            self.assertIn("second_stage", stage_names)
            self.assertIn("third_stage", stage_names)

            # Timestamps should be in ascending order (with ROW_NUMBER offset)
            timestamps = [result[1] for result in results]
            self.assertEqual(timestamps, sorted(timestamps))
