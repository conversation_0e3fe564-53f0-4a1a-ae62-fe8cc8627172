"""
Utility functions for testing the benchmark system.
"""
import uuid
import logging
from typing import Optional, Dict, Any, Tuple
from django.utils import timezone
from asgiref.sync import sync_to_async
from apps.main.models import BenchmarkScenario, BenchmarkTag, LLMConfig, GenericAgent
from apps.main.models import AgentRole as ModelAgentRole

logger = logging.getLogger(__name__)

# Import the generate_unique_scenario_name function from the utils directory
from apps.main.tests.utils.utils import generate_unique_scenario_name


def create_test_scenario(
    name: Optional[str] = None,
    agent_role: str = ModelAgentRole.MENTOR.value,
    description: str = "A scenario for testing.",
    input_data: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None,
    is_active: bool = True,
    version: int = 1,
    tags: Optional[list] = None,
    is_latest: bool = False,
    add_random_suffix: bool = True
) -> BenchmarkScenario:
    """
    Create a test benchmark scenario with unique name to avoid constraint violations.

    Args:
        name: Base name for the scenario
        agent_role: The agent role for the scenario
        description: Description of the scenario
        input_data: Input data for the scenario
        metadata: Metadata for the scenario
        is_active: Whether the scenario is active
        version: Version number for the scenario
        tags: Optional list of tag names to add to the scenario
        is_latest: Whether this is the latest version of the scenario
        add_random_suffix: Whether to add a random suffix to the name (default: True)

    Returns:
        The created BenchmarkScenario instance
    """
    # Generate a name based on the provided name or default
    base_name = name if name is not None else "Test Scenario"
    # Only add a random suffix if requested
    if add_random_suffix:
        unique_name = generate_unique_scenario_name(base_name)
    else:
        unique_name = base_name

    if input_data is None:
        input_data = {"user_query": "Hello"}

    if metadata is None:
        metadata = {
            "expected_quality_criteria": {
                "Clarity": ["Is the response easy to understand?"],
                "Friendliness": ["Is the tone welcoming?"]
            }
        }

    scenario = BenchmarkScenario.objects.create(
        name=unique_name,
        description=description,
        agent_role=agent_role,
        input_data=input_data,
        metadata=metadata,
        is_active=is_active,
        version=version,
        is_latest=is_latest
    )

    # Add tags if provided
    if tags:
        for tag_name in tags:
            tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name)
            scenario.tags.add(tag)

    return scenario


async def create_test_scenario_async(
    name: Optional[str] = None,
    agent_role: str = ModelAgentRole.MENTOR.value,
    description: str = "A scenario for testing.",
    input_data: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None,
    is_active: bool = True,
    version: int = 1,
    tags: Optional[list] = None,
    is_latest: bool = False,
    add_random_suffix: bool = True
) -> BenchmarkScenario:
    """
    Async version of create_test_scenario.

    Args:
        name: Base name for the scenario
        agent_role: The agent role for the scenario
        description: Description of the scenario
        input_data: Input data for the scenario
        metadata: Metadata for the scenario
        is_active: Whether the scenario is active
        version: Version number for the scenario
        tags: Optional list of tag names to add to the scenario
        is_latest: Whether this is the latest version of the scenario
        add_random_suffix: Whether to add a random suffix to the name (default: True)

    Returns:
        The created BenchmarkScenario instance
    """
    # Generate a name based on the provided name or default
    base_name = name if name is not None else "Test Scenario"
    # Only add a random suffix if requested
    if add_random_suffix:
        unique_name = generate_unique_scenario_name(base_name)
    else:
        unique_name = base_name

    if input_data is None:
        input_data = {"user_query": "Hello"}

    if metadata is None:
        metadata = {
            "expected_quality_criteria": {
                "Clarity": ["Is the response easy to understand?"],
                "Friendliness": ["Is the tone welcoming?"]
            }
        }

    scenario = await sync_to_async(BenchmarkScenario.objects.create, thread_sensitive=True)(
        name=unique_name,
        description=description,
        agent_role=agent_role,
        input_data=input_data,
        metadata=metadata,
        is_active=is_active,
        version=version,
        is_latest=is_latest
    )

    # Add tags if provided
    if tags:
        for tag_name in tags:
            tag, _ = await sync_to_async(BenchmarkTag.objects.get_or_create, thread_sensitive=True)(name=tag_name)
            await sync_to_async(scenario.tags.add, thread_sensitive=True)(tag)

    return scenario


def generate_unique_agent_role(base_role: str = "test-agent") -> str:
    """
    Generate a unique role for an agent to avoid unique constraint violations.
    The role field has a max length of 20 characters.

    Args:
        base_role: The base role to use for the agent

    Returns:
        A unique role with a UUID suffix
    """
    # Ensure the base_role is short enough
    if len(base_role) > 10:
        base_role = base_role[:10]

    # Use a shorter unique suffix (4 chars)
    unique_suffix = str(uuid.uuid4())[:4]

    # Combine them ensuring total length <= 20
    role = f"{base_role}-{unique_suffix}"
    if len(role) > 20:
        role = role[:20]

    return role


def generate_unique_llm_config_name(base_name: str = "test-config") -> str:
    """
    Generate a unique name for an LLM config to avoid unique constraint violations.

    Args:
        base_name: The base name to use for the config

    Returns:
        A unique name with a UUID suffix
    """
    unique_suffix = str(uuid.uuid4())[:8]
    return f"{base_name}-{unique_suffix}"


def create_test_agent(
    role: Optional[str] = None,
    description: str = "Test agent",
    system_instructions: str = "Test instructions",
    input_schema: Optional[Dict[str, Any]] = None,
    output_schema: Optional[Dict[str, Any]] = None,
    langgraph_node_class: str = "test.TestAgent",
    version: str = "1.0.0",
    is_active: bool = True
) -> GenericAgent:
    """
    Create a test agent with a unique role to avoid constraint violations.

    Args:
        role: Base role for the agent (will always be made unique)
        description: Description of the agent
        system_instructions: System instructions for the agent
        input_schema: Input schema for the agent
        output_schema: Output schema for the agent
        langgraph_node_class: LangGraph node class for the agent
        version: Version of the agent
        is_active: Whether the agent is active

    Returns:
        The created GenericAgent instance
    """
    # Always generate a unique role based on the provided role or default
    base_role = role if role is not None else "test-agent"
    unique_role = generate_unique_agent_role(base_role)

    if input_schema is None:
        input_schema = {}

    if output_schema is None:
        output_schema = {}

    agent = GenericAgent.objects.create(
        role=unique_role,
        description=description,
        system_instructions=system_instructions,
        input_schema=input_schema,
        output_schema=output_schema,
        langgraph_node_class=langgraph_node_class,
        version=version,
        is_active=is_active
    )

    return agent


def create_test_llm_config(
    name: Optional[str] = None,
    model_name: str = "test-model",
    temperature: float = 0.7,
    input_token_price: float = 0.0001,
    output_token_price: float = 0.0002,
    is_default: bool = False,
    is_evaluation: bool = False
) -> LLMConfig:
    """
    Create a test LLM config with a unique name to avoid constraint violations.

    Args:
        name: Base name for the config (will always be made unique)
        model_name: The model name for the config
        temperature: The temperature setting
        input_token_price: Price per input token
        output_token_price: Price per output token
        is_default: Whether this is the default config

    Returns:
        The created LLMConfig instance
    """
    # Always generate a unique name based on the provided name or default
    base_name = name if name is not None else "test-config"
    unique_name = generate_unique_llm_config_name(base_name)

    config = LLMConfig.objects.create(
        name=unique_name,
        model_name=model_name,
        temperature=temperature,
        input_token_price=input_token_price,
        output_token_price=output_token_price,
        is_default=is_default,
        is_evaluation=is_evaluation
    )

    return config


async def create_test_agent_async(
    role: Optional[str] = None,
    description: str = "Test agent",
    system_instructions: str = "Test instructions",
    input_schema: Optional[Dict[str, Any]] = None,
    output_schema: Optional[Dict[str, Any]] = None,
    langgraph_node_class: str = "test.TestAgent",
    version: str = "1.0.0",
    is_active: bool = True
) -> GenericAgent:
    """
    Async version of create_test_agent.

    Args:
        role: Base role for the agent (will always be made unique)
        description: Description of the agent
        system_instructions: System instructions for the agent
        input_schema: Input schema for the agent
        output_schema: Output schema for the agent
        langgraph_node_class: LangGraph node class for the agent
        version: Version of the agent
        is_active: Whether the agent is active

    Returns:
        The created GenericAgent instance
    """
    # Always generate a unique role based on the provided role or default
    base_role = role if role is not None else "test-agent"
    unique_role = generate_unique_agent_role(base_role)

    if input_schema is None:
        input_schema = {}

    if output_schema is None:
        output_schema = {}

    agent = await sync_to_async(GenericAgent.objects.create, thread_sensitive=True)(
        role=unique_role,
        description=description,
        system_instructions=system_instructions,
        input_schema=input_schema,
        output_schema=output_schema,
        langgraph_node_class=langgraph_node_class,
        version=version,
        is_active=is_active
    )

    return agent


async def create_test_llm_config_async(
    name: Optional[str] = None,
    model_name: str = "test-model",
    temperature: float = 0.7,
    input_token_price: float = 0.0001,
    output_token_price: float = 0.0002,
    is_default: bool = False,
    is_evaluation: bool = False
) -> LLMConfig:
    """
    Async version of create_test_llm_config.

    Args:
        name: Base name for the config (will always be made unique)
        model_name: The model name for the config
        temperature: The temperature setting
        input_token_price: Price per input token
        output_token_price: Price per output token
        is_default: Whether this is the default config

    Returns:
        The created LLMConfig instance
    """
    # Always generate a unique name based on the provided name or default
    base_name = name if name is not None else "test-config"
    unique_name = generate_unique_llm_config_name(base_name)

    config = await sync_to_async(LLMConfig.objects.create, thread_sensitive=True)(
        name=unique_name,
        model_name=model_name,
        temperature=temperature,
        input_token_price=input_token_price,
        output_token_price=output_token_price,
        is_default=is_default,
        is_evaluation=is_evaluation
    )

    return config


# Import the TestBenchmarkRun model from the models module
from apps.main.tests.models import TestBenchmarkRun

# Use TestBenchmarkRun as MainTestBenchmarkRun for backward compatibility
MainTestBenchmarkRun = TestBenchmarkRun


def create_test_benchmark_run(
    scenario: BenchmarkScenario,
    agent_definition: GenericAgent,
    llm_config: Optional[LLMConfig] = None,
    agent_version: str = "1.0",
    parameters: Optional[Dict[str, Any]] = None,
    runs_count: int = 3,
    mean_duration: float = 150.0,
    median_duration: float = 145.0,
    min_duration: float = 100.0,
    max_duration: float = 200.0,
    std_dev: float = 25.0,
    success_rate: float = 1.0,
    llm_calls: int = 5,
    tool_calls: int = 2,
    tool_breakdown: Optional[Dict[str, int]] = None,
    memory_operations: int = 0,
    semantic_score: Optional[float] = 0.85,
    semantic_evaluation_details: Optional[Dict[str, Any]] = None,
    semantic_evaluations: Optional[Dict[str, Any]] = None,
    execution_date: Optional[timezone.datetime] = None
) -> MainTestBenchmarkRun:
    """
    Create a test benchmark run using the MainTestBenchmarkRun model that matches the actual database schema.

    Args:
        scenario: The benchmark scenario
        agent_definition: The agent definition
        llm_config: The LLM configuration
        agent_version: The agent version
        parameters: Benchmark parameters
        runs_count: Number of runs
        mean_duration: Mean duration in milliseconds
        median_duration: Median duration in milliseconds
        min_duration: Minimum duration in milliseconds
        max_duration: Maximum duration in milliseconds
        std_dev: Standard deviation of duration in milliseconds
        success_rate: Success rate (0.0 to 1.0)
        llm_calls: Number of LLM calls
        tool_calls: Number of tool calls
        tool_breakdown: Breakdown of tool calls by tool
        memory_operations: Number of memory operations
        semantic_score: Semantic quality score
        semantic_evaluation_details: Semantic evaluation details
        semantic_evaluations: Semantic evaluations by evaluator
        execution_date: Execution date

    Returns:
        The created TestBenchmarkRun instance
    """
    if parameters is None:
        parameters = {"runs": runs_count}

    if tool_breakdown is None:
        tool_breakdown = {"tool1": tool_calls}

    if semantic_evaluation_details is None:
        semantic_evaluation_details = {"reasoning": "Looks good"}

    if semantic_evaluations is None:
        semantic_evaluations = {}

    if execution_date is None:
        execution_date = timezone.now()

    return MainTestBenchmarkRun.objects.create(
        scenario=scenario,
        agent_definition=agent_definition,
        llm_config=llm_config,
        agent_version=agent_version,
        parameters=parameters,
        runs_count=runs_count,
        mean_duration=mean_duration,
        median_duration=median_duration,
        min_duration=min_duration,
        max_duration=max_duration,
        std_dev=std_dev,
        success_rate=success_rate,
        llm_calls=llm_calls,
        tool_calls=tool_calls,
        tool_breakdown=tool_breakdown,
        memory_operations=memory_operations,
        semantic_score=semantic_score,
        semantic_evaluation_details=semantic_evaluation_details,
        semantic_evaluations=semantic_evaluations,
        execution_date=execution_date
    )


async def create_test_benchmark_run_async(
    scenario: BenchmarkScenario,
    agent_definition: GenericAgent,
    llm_config: Optional[LLMConfig] = None,
    agent_version: str = "1.0",
    parameters: Optional[Dict[str, Any]] = None,
    runs_count: int = 3,
    mean_duration: float = 150.0,
    median_duration: float = 145.0,
    min_duration: float = 100.0,
    max_duration: float = 200.0,
    std_dev: float = 25.0,
    success_rate: float = 1.0,
    llm_calls: int = 5,
    tool_calls: int = 2,
    tool_breakdown: Optional[Dict[str, int]] = None,
    memory_operations: int = 0,
    semantic_score: Optional[float] = 0.85,
    semantic_evaluation_details: Optional[Dict[str, Any]] = None,
    semantic_evaluations: Optional[Dict[str, Any]] = None,
    execution_date: Optional[timezone.datetime] = None
) -> MainTestBenchmarkRun:
    """
    Async version of create_test_benchmark_run.

    Args:
        scenario: The benchmark scenario
        agent_definition: The agent definition
        llm_config: The LLM configuration
        agent_version: The agent version
        parameters: Benchmark parameters
        runs_count: Number of runs
        mean_duration: Mean duration in milliseconds
        median_duration: Median duration in milliseconds
        min_duration: Minimum duration in milliseconds
        max_duration: Maximum duration in milliseconds
        std_dev: Standard deviation of duration in milliseconds
        success_rate: Success rate (0.0 to 1.0)
        llm_calls: Number of LLM calls
        tool_calls: Number of tool calls
        tool_breakdown: Breakdown of tool calls by tool
        memory_operations: Number of memory operations
        semantic_score: Semantic quality score
        semantic_evaluation_details: Semantic evaluation details
        semantic_evaluations: Semantic evaluations by evaluator
        execution_date: Execution date

    Returns:
        The created TestBenchmarkRun instance
    """
    if parameters is None:
        parameters = {"runs": runs_count}

    if tool_breakdown is None:
        tool_breakdown = {"tool1": tool_calls}

    if semantic_evaluation_details is None:
        semantic_evaluation_details = {"reasoning": "Looks good"}

    if semantic_evaluations is None:
        semantic_evaluations = {}

    if execution_date is None:
        execution_date = timezone.now()

    return await sync_to_async(MainTestBenchmarkRun.objects.create, thread_sensitive=True)(
        scenario=scenario,
        agent_definition=agent_definition,
        llm_config=llm_config,
        agent_version=agent_version,
        parameters=parameters,
        runs_count=runs_count,
        mean_duration=mean_duration,
        median_duration=median_duration,
        min_duration=min_duration,
        max_duration=max_duration,
        std_dev=std_dev,
        success_rate=success_rate,
        llm_calls=llm_calls,
        tool_calls=tool_calls,
        tool_breakdown=tool_breakdown,
        memory_operations=memory_operations,
        semantic_score=semantic_score,
        semantic_evaluation_details=semantic_evaluation_details,
        semantic_evaluations=semantic_evaluations,
        execution_date=execution_date
    )


def ensure_mentor_agent_exists() -> Tuple[GenericAgent, bool]:
    """
    Ensures that a mentor agent exists in the database with all required fields.
    This is useful for tests that require a mentor agent to be present.

    Returns:
        Tuple[GenericAgent, bool]: The mentor agent and a boolean indicating if it was created
    """
    logger.info("Ensuring mentor agent exists in the database...")

    # Define default schemas that satisfy NOT NULL constraints
    default_schema = {"type": "object", "properties": {}}
    default_memory_schema = {
        "type": "object",
        "properties": {
            "communication_preferences": {"type": "object"},
            "conversation_context": {"type": "object"},
            "effective_approaches": {"type": "object"}
        }
    }

    # Create or get the mentor agent with all required fields
    mentor_agent, created = GenericAgent.objects.get_or_create(
        role=ModelAgentRole.MENTOR.value,
        defaults={
            'description': 'Test Mentor Agent for Integration Tests',
            'is_active': True,
            'input_schema': default_schema,
            'output_schema': default_schema,
            'state_schema': default_schema,
            'memory_schema': default_memory_schema,
            'system_instructions': 'You are a helpful mentor agent for testing purposes.',
            'version': '1.0.0-test',
            'langgraph_node_class': 'apps.main.agents.mentor_agent.MentorAgent',
            'processing_timeout': 30,
            'read_models': [
                'user.UserProfile',
                'main.HistoryEvent',
                'main.UserFeedback',
                'activity.ActivityTailored',
                'main.Wheel',
                'main.WheelItem',
                'user.TrustLevel'
            ],
            'write_models': [
                'main.HistoryEvent',
                'main.UserFeedback',
                'user.CurrentMood'
            ],
            'recommend_models': [
                'user.TrustLevel'
            ]
        }
    )

    if created:
        logger.info("Created new mentor agent for testing")
    else:
        logger.info("Using existing mentor agent for testing")

        # Update the agent with required fields if they're missing
        updated = False

        # Check for missing fields and update if necessary
        if not mentor_agent.langgraph_node_class:
            mentor_agent.langgraph_node_class = 'apps.main.agents.mentor_agent.MentorAgent'
            updated = True

        if not mentor_agent.memory_schema:
            mentor_agent.memory_schema = default_memory_schema
            updated = True

        if not mentor_agent.state_schema:
            mentor_agent.state_schema = default_schema
            updated = True

        if not mentor_agent.read_models:
            mentor_agent.read_models = [
                'user.UserProfile',
                'main.HistoryEvent',
                'main.UserFeedback',
                'activity.ActivityTailored',
                'main.Wheel',
                'main.WheelItem',
                'user.TrustLevel'
            ]
            updated = True

        if not mentor_agent.write_models:
            mentor_agent.write_models = [
                'main.HistoryEvent',
                'main.UserFeedback',
                'user.CurrentMood'
            ]
            updated = True

        if not mentor_agent.recommend_models:
            mentor_agent.recommend_models = [
                'user.TrustLevel'
            ]
            updated = True

        if updated:
            mentor_agent.save()
            logger.info("Updated existing mentor agent with missing fields")

    return mentor_agent, created


async def ensure_mentor_agent_exists_async() -> Tuple[GenericAgent, bool]:
    """
    Async version of ensure_mentor_agent_exists.

    Returns:
        Tuple[GenericAgent, bool]: The mentor agent and a boolean indicating if it was created
    """
    return await sync_to_async(ensure_mentor_agent_exists, thread_sensitive=True)()