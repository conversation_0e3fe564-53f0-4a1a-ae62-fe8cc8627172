"""
Test factories for creating test instances of models.

This package provides factory classes for creating test instances of models
with random or specified data. The factories support creating both valid and invalid
instances for testing validation logic.
"""

from .pydantic_factories import (
    # Factory classes
    BaseFactory,
    GoaliBaseModelFactory,
    VersionedModelFactory,
    BenchmarkScenarioFactory,
    BenchmarkScenarioMetadataFactory,
    EvaluationCriterionFactory,
    EvaluationCriteriaFactory,
    PhaseAwareCriteriaFactory,
    ToolExpectationFactory,
    BenchmarkRunFactory,
    TokenUsageFactory,
    StagePerformanceFactory,
    SemanticEvaluationFactory,
    
    # Helper functions
    create_benchmark_scenario,
    create_benchmark_run,
    create_evaluation_criteria,
    create_phase_aware_criteria,
    create_tool_expectation,
    create_token_usage,
    create_stage_performance,
    create_semantic_evaluation,
    create_invalid_model_data
)

__all__ = [
    # Factory classes
    'BaseFactory',
    'GoaliBaseModelFactory',
    'VersionedModelFactory',
    'BenchmarkScenarioFactory',
    'BenchmarkScenarioMetadataFactory',
    'EvaluationCriterionFactory',
    'EvaluationCriteriaFactory',
    'PhaseAwareCriteriaFactory',
    'ToolExpectationFactory',
    'BenchmarkRunFactory',
    'TokenUsageFactory',
    'StagePerformanceFactory',
    'SemanticEvaluationFactory',
    
    # Helper functions
    'create_benchmark_scenario',
    'create_benchmark_run',
    'create_evaluation_criteria',
    'create_phase_aware_criteria',
    'create_tool_expectation',
    'create_token_usage',
    'create_stage_performance',
    'create_semantic_evaluation',
    'create_invalid_model_data'
]
