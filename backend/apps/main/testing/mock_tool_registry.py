"""
Mock implementation of the Tool Registry service for testing.
"""
import asyncio
import logging
import pprint
import json
from typing import Dict, Any, List, Optional, Union, Callable

# Import the custom exception
from apps.main.agents.exceptions import SimulatedToolException

from .interfaces import ToolRegistryInterface
from .definition_extractors import ToolDefinitionsExtractor

logger = logging.getLogger(__name__) # Added logger instance

class MockToolRegistry(ToolRegistryInterface):
    """Mock implementation of tool registry with enhanced mocking capabilities.

    This class provides methods for registering mock responses, errors, and conditional
    responses for tools. It tracks tool calls and provides methods for retrieving
    call information for testing and verification.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None, tool_extractor: Optional[ToolDefinitionsExtractor] = None):
        """Initialize mock with optional configuration.

        Args:
            config: Optional configuration with scenario-specific tool responses
            tool_extractor: Optional tool extractor to use for definitions
        """
        self.config = config or {}
        self.calls = []
        self.call_counts: Dict[str, int] = {} # Track call counts per tool
        self.call_args: Dict[str, List[Dict[str, Any]]] = {} # Track arguments for each call
        self.tool_schemas = self.config.get("tool_schemas", {})

        # Storage for registered mock responses, errors, and conditional responses
        self.mock_responses: Dict[str, Any] = {}
        self.mock_errors: Dict[str, Dict[str, str]] = {}
        self.conditional_responses: Dict[str, List[Dict[str, Any]]] = {}

        # Use tool extractor if provided, otherwise create a new one
        self.tool_extractor = tool_extractor or ToolDefinitionsExtractor()
        # Ensure definitions are loaded if not already
        if not self.tool_extractor.tool_definitions:
            self.tool_extractor.extract_definitions() # Removed use_cache=True argument

        # Initialize from config if provided
        if config:
            if "mock_tool_responses" in config:
                for tool_code, response in config["mock_tool_responses"].items():
                    self.register_mock_response(tool_code, response)

            if "mock_tool_errors" in config:
                for tool_code, error_config in config["mock_tool_errors"].items():
                    if isinstance(error_config, dict):
                        self.register_mock_error(
                            tool_code,
                            error_config.get("error_message", "Mock error"),
                            error_config.get("error_type", "ValueError")
                        )
                    else:
                        self.register_mock_error(tool_code, str(error_config))

            if "mock_tool_conditional_responses" in config:
                for tool_code, conditions in config["mock_tool_conditional_responses"].items():
                    for condition_config in conditions:
                        self.register_conditional_response(
                            tool_code,
                            condition_config.get("condition", {}),
                            condition_config.get("response", {})
                        )

    def reset(self):
        """Reset call counts, recorded calls, and call arguments."""
        self.calls = []
        self.call_counts = {}
        self.call_args = {}

    def register_mock_response(self, tool_code: str, response: Any) -> None:
        """Register a mock response for a tool.

        Args:
            tool_code: The tool code to register the response for
            response: The response to return when the tool is called.
                     Can be a string, dict, or any serializable object.
        """
        logger.debug(f"Registering mock response for tool '{tool_code}'")
        # Convert dict/list to JSON string for consistency with existing tests
        if isinstance(response, (dict, list)):
            self.mock_responses[tool_code] = json.dumps(response)
        else:
            self.mock_responses[tool_code] = response

    def register_mock_error(self, tool_code: str, error_message: str, error_type: str = "ValueError") -> None:
        """Register a mock error for a tool.

        Args:
            tool_code: The tool code to register the error for
            error_message: The error message to include in the exception
            error_type: The type of exception to raise (default: ValueError)
        """
        logger.debug(f"Registering mock error for tool '{tool_code}': {error_type}: {error_message}")
        self.mock_errors[tool_code] = {
            "error_message": error_message,
            "error_type": error_type
        }

    def register_conditional_response(self, tool_code: str, condition: Dict[str, Any], response: Any) -> None:
        """Register a conditional response for a tool.

        Args:
            tool_code: The tool code to register the conditional response for
            condition: Dictionary of conditions that must be matched in the tool input
            response: The response to return when the condition is met
        """
        logger.debug(f"Registering conditional response for tool '{tool_code}' with condition: {condition}")

        # Initialize the list if it doesn't exist
        if tool_code not in self.conditional_responses:
            self.conditional_responses[tool_code] = []

        # Convert response to JSON string if it's a dict or list
        if isinstance(response, (dict, list)):
            response_str = json.dumps(response)
        else:
            response_str = str(response)

        # Add the condition-response pair
        self.conditional_responses[tool_code].append({
            "condition": condition,
            "response": response_str
        })

    def get_call_count(self, tool_code: str) -> int:
        """Get the number of times a tool has been called.

        Args:
            tool_code: The tool code to get the call count for

        Returns:
            The number of times the tool has been called
        """
        return self.call_counts.get(tool_code, 0)

    def get_call_args(self, tool_code: str) -> List[Dict[str, Any]]:
        """Get the arguments passed to a tool for all calls.

        Args:
            tool_code: The tool code to get the call arguments for

        Returns:
            List of argument dictionaries for each call to the tool
        """
        return self.call_args.get(tool_code, [])

    def _evaluate_template(self, template: str, tool_input: Dict[str, Any], call_count: int) -> str:
        """Evaluate a template string with the given tool input and call count.

        Args:
            template: Template string with placeholders
            tool_input: Tool input parameters
            call_count: Current call count for the tool

        Returns:
            Evaluated template string
        """
        try:
            # Create a copy of tool_input with all values converted to strings
            # to avoid formatting errors with non-string values
            safe_input = {}
            for key, value in tool_input.items():
                if isinstance(value, (dict, list)):
                    safe_input[key] = json.dumps(value)
                else:
                    safe_input[key] = str(value) if value is not None else ""

            # Add call_count to the safe_input dictionary
            safe_input['call_count'] = call_count

            # Replace placeholders in the template using string formatting
            # This handles both {key} and {key.subkey} patterns
            result = template
            for key, value in safe_input.items():
                placeholder = "{" + key + "}"
                if placeholder in result:
                    result = result.replace(placeholder, str(value))

            return result
        except KeyError as e:
            logger.warning(f"Missing key in template: {e}")
            return template
        except Exception as e:
            logger.error(f"Error evaluating template: {e}", exc_info=True)
            return template

    def _check_condition_match(self, condition: Union[Dict[str, Any], bool, str], tool_input: Dict[str, Any]) -> bool:
        """Check if a condition matches the tool input.

        Args:
            condition: Condition to check (can be a dictionary, boolean, or string)
            tool_input: Tool input parameters

        Returns:
            True if the condition matches, False otherwise
        """
        # Handle boolean conditions directly
        if isinstance(condition, bool):
            return condition

        # Handle string conditions (e.g., "True" or Python expressions)
        if isinstance(condition, str):
            if condition == "True":
                return True
            elif condition == "False":
                return False
            else:
                # For more complex string conditions like "get(tool_input, 'user_id') == 'test-user-123'"
                # We would need to evaluate them, but for now we'll just return False
                # This is a simplified implementation
                try:
                    # Simple check for user_id equality in the condition string
                    if "user_id" in condition and "==" in condition:
                        # Extract the expected user_id value from the condition
                        import re
                        match = re.search(r"'user_id'\s*\)\s*==\s*'([^']+)'", condition)
                        if match and 'user_id' in tool_input:
                            expected_user_id = match.group(1)
                            return tool_input['user_id'] == expected_user_id
                except Exception as e:
                    logger.error(f"Error evaluating string condition: {e}", exc_info=True)
                    return False

            # Default to False for unhandled string conditions
            return False

        # Handle dictionary conditions
        if isinstance(condition, dict):
            # Check if all key-value pairs in condition exist in tool_input
            for key, value in condition.items():
                # Handle nested dictionaries
                if isinstance(value, dict) and key in tool_input and isinstance(tool_input[key], dict):
                    # Recursively check nested dictionaries
                    if not self._check_nested_condition(value, tool_input[key]):
                        return False
                # Handle simple key-value pairs
                elif key not in tool_input or tool_input[key] != value:
                    return False
            return True

        # Default to False for unhandled condition types
        return False

    def _check_nested_condition(self, condition: Dict[str, Any], input_dict: Dict[str, Any]) -> bool:
        """Check if a nested condition matches the input dictionary.

        Args:
            condition: Nested condition dictionary to check
            input_dict: Input dictionary to check against

        Returns:
            True if the condition matches, False otherwise
        """
        for key, value in condition.items():
            if isinstance(value, dict) and key in input_dict and isinstance(input_dict[key], dict):
                # Recursively check nested dictionaries
                if not self._check_nested_condition(value, input_dict[key]):
                    return False
            elif key not in input_dict or input_dict[key] != value:
                return False
        return True

    async def call_tool(self,
                      tool_code: str,
                      tool_input: Dict[str, Any],
                      context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Mock tool execution with realistic responses based on schemas.

        Args:
            tool_code: The tool code to call
            tool_input: Input parameters for the tool
            context: Optional context information

        Returns:
            Mock tool result based on tool schema or config
        """
        # Log the tool call details
        logger.debug(f"MockToolRegistry calling tool '{tool_code}' with input:\n{pprint.pformat(tool_input)}") # Added logging

        # Increment and get call count for this tool
        current_call_count = self.call_counts.get(tool_code, 0) + 1
        self.call_counts[tool_code] = current_call_count
        logger.debug(f"Tool '{tool_code}' call count: {current_call_count}")

        # Record the call arguments
        if tool_code not in self.call_args:
            self.call_args[tool_code] = []
        self.call_args[tool_code].append(tool_input)

        # Record the call for test verification
        call_info = {
            "tool_code": tool_code,
            "tool_input": tool_input,
            "context": context,
            "call_count": current_call_count # Record the count for this call
        }
        self.calls.append(call_info)

        # Check for registered errors first
        if tool_code in self.mock_errors:
            error_config = self.mock_errors[tool_code]
            error_message = error_config["error_message"]
            error_type = error_config["error_type"]

            logger.warning(f"MockToolRegistry raising registered error for tool '{tool_code}': {error_type}: {error_message}")

            # Raise the appropriate exception type
            if error_type == "ValueError":
                raise ValueError(error_message)
            elif error_type == "TypeError":
                raise TypeError(error_message)
            elif error_type == "KeyError":
                raise KeyError(error_message)
            elif error_type == "SimulatedToolException":
                raise SimulatedToolException(error_message)
            else:
                # Default to ValueError
                raise ValueError(error_message)

        # Check for conditional responses
        if tool_code in self.conditional_responses:
            for condition_response in self.conditional_responses[tool_code]:
                condition = condition_response["condition"]
                response_template = condition_response["response"]

                if self._check_condition_match(condition, tool_input):
                    # Evaluate the template with the tool input and call count
                    response_str = self._evaluate_template(response_template, tool_input, current_call_count)

                    # Try to parse as JSON
                    try:
                        response_json = json.loads(response_str)

                        # Check for error directives
                        if isinstance(response_json, dict):
                            # Check for $raise_exception directive
                            if "$raise_exception" in response_json:
                                error_message = response_json["$raise_exception"]
                                logger.warning(f"MockToolRegistry raising exception from conditional response for tool '{tool_code}': {error_message}")
                                raise SimulatedToolException(error_message)

                            # Check for error directive with type
                            if "error" in response_json and response_json.get("error") is True:
                                error_type = response_json.get("error_type", "ValueError")
                                error_message = response_json.get("error_message", "Mock error")
                                logger.warning(f"MockToolRegistry raising {error_type} from conditional response for tool '{tool_code}': {error_message}")

                                if error_type == "ValueError":
                                    raise ValueError(error_message)
                                elif error_type == "TypeError":
                                    raise TypeError(error_message)
                                elif error_type == "KeyError":
                                    raise KeyError(error_message)
                                elif error_type == "SimulatedToolException":
                                    raise SimulatedToolException(error_message)
                                else:
                                    # Default to ValueError
                                    raise ValueError(error_message)

                        logger.debug(f"MockToolRegistry returning conditional response for '{tool_code}': {pprint.pformat(response_json)}")
                        return response_json
                    except json.JSONDecodeError:
                        # Not valid JSON, return as string
                        logger.debug(f"MockToolRegistry returning conditional response string for '{tool_code}': {response_str}")
                        return response_str

        # Check for registered mock responses
        if tool_code in self.mock_responses:
            response = self.mock_responses[tool_code]

            # If response is a string, try to evaluate it as a template
            if isinstance(response, str):
                response_str = self._evaluate_template(response, tool_input, current_call_count)

                # Try to parse as JSON
                try:
                    response_json = json.loads(response_str)

                    # Check for error directives
                    if isinstance(response_json, dict):
                        # Check for $raise_exception directive
                        if "$raise_exception" in response_json:
                            error_message = response_json["$raise_exception"]
                            logger.warning(f"MockToolRegistry raising exception from mock response for tool '{tool_code}': {error_message}")
                            raise SimulatedToolException(error_message)

                        # Check for error directive with type
                        if "error" in response_json and response_json.get("error") is True:
                            error_type = response_json.get("error_type", "ValueError")
                            error_message = response_json.get("error_message", "Mock error")
                            logger.warning(f"MockToolRegistry raising {error_type} from mock response for tool '{tool_code}': {error_message}")

                            if error_type == "ValueError":
                                raise ValueError(error_message)
                            elif error_type == "TypeError":
                                raise TypeError(error_message)
                            elif error_type == "KeyError":
                                raise KeyError(error_message)
                            elif error_type == "SimulatedToolException":
                                raise SimulatedToolException(error_message)
                            else:
                                # Default to ValueError
                                raise ValueError(error_message)

                    logger.debug(f"MockToolRegistry returning mock response for '{tool_code}': {pprint.pformat(response_json)}")
                    return response_json
                except json.JSONDecodeError:
                    # Not valid JSON, return as string
                    logger.debug(f"MockToolRegistry returning mock response string for '{tool_code}': {response_str}")
                    return response_str

            # If response is a dict or other object, return it directly
            logger.debug(f"MockToolRegistry returning mock response for '{tool_code}': {pprint.pformat(response)}")
            return response

        # Check for predefined responses in config first
        if "tool_responses" in self.config and tool_code in self.config["tool_responses"]:
            response_config = self.config["tool_responses"][tool_code]

            # --- START ADDITION: Check for configured Exception ---
            # Check if the configured response is an Exception instance to be raised
            if isinstance(response_config, Exception):
                logger.warning(f"MockToolRegistry raising configured exception for tool '{tool_code}': {type(response_config).__name__}: {response_config}")
                raise response_config
            # --- END ADDITION ---

            # If response is a function, call it with the input and call count
            if callable(response_config):
                try:
                    # Pass tool_input and call_count, but check if the function accepts context
                    if asyncio.iscoroutinefunction(response_config):
                        # Check the function signature to see if it accepts context
                        import inspect
                        sig = inspect.signature(response_config)
                        if 'context' in sig.parameters:
                            result = await response_config(tool_input=tool_input, context=context, call_count=current_call_count)
                        else:
                            result = await response_config(tool_input=tool_input, call_count=current_call_count)
                    else:
                        # Check the function signature to see if it accepts context
                        import inspect
                        sig = inspect.signature(response_config)
                        if 'context' in sig.parameters:
                            result = response_config(tool_input=tool_input, context=context, call_count=current_call_count)
                        else:
                            result = response_config(tool_input=tool_input, call_count=current_call_count)
                    logger.debug(f"MockToolRegistry returning result for '{tool_code}' (call {current_call_count}) from callable: {pprint.pformat(result)}")
                    return result
                except SimulatedToolException:
                    # Re-raise the specific simulated exception
                    raise
                except Exception as e:
                    # Log and re-raise other unexpected errors from the response function
                    logger.error(f"Error executing configured response function for tool '{tool_code}': {e}", exc_info=True)
                    raise RuntimeError(f"Error in mock response function for tool '{tool_code}': {e}") from e

            # If it's a static response (e.g., dict)
            logger.debug(f"MockToolRegistry returning static result for '{tool_code}': {pprint.pformat(response_config)}") # Log result
            return response_config

        # Use extracted tool responses if available from the extractor
        if tool_code in self.tool_extractor.tool_mock_responses:
            result = self.tool_extractor.get_mock_response(tool_code, tool_input) # Use the method to get response
            logger.debug(f"MockToolRegistry returning result for '{tool_code}' from extractor: {pprint.pformat(result)}") # Log result
            return result

        # Default empty response for unknown tools
        logger.debug(f"MockToolRegistry returning default empty result for unknown tool '{tool_code}'.") # Log result
        return {"error": f"No mock response found for tool {tool_code}", "status": "error"}

    def get_tool_schema(self, tool_code: str) -> Dict[str, Any]:
        """Get mock tool schema.

        Args:
            tool_code: The tool code

        Returns:
            Mock tool schema from extractor or config or default
        """
        # Try extractor first
        extracted_schema = self.tool_extractor.get_tool_definition(tool_code)
        if extracted_schema:
            # Return the full definition which includes the schema parts
            return extracted_schema

        # Try config next
        if tool_code in self.tool_schemas:
            return self.tool_schemas[tool_code]

        # Default fallback schema
        return {
            "code": tool_code,
            "name": f"{tool_code.replace('_', ' ').title()}",
            "description": f"Default mock schema for {tool_code}",
            "input_schema": {},
            "output_schema": {},
            "function_path": "unknown"
        }

    def list_available_tools(self, category: Optional[str] = None) -> List[str]:
        """List mock available tools from config or extractor.

        Args:
            category: Optional category to filter by (Not implemented in mock)

        Returns:
            List of mock tool codes
        """
        # Prefer tools defined in config if provided
        if "available_tools" in self.config:
             available_tools = self.config["available_tools"]
        else:
             # Otherwise, use the codes from the extracted definitions
             available_tools = list(self.tool_extractor.tool_definitions.keys())

        # Basic filtering if category is provided (can be enhanced)
        if category:
            # Simple filtering based on category name in tool code (example)
            return [t for t in available_tools if category.lower() in t.lower()]

        # Return default list if nothing else is found
        return available_tools if available_tools else ["get_data", "update_data", "analyze_data"] # Fallback

    async def execute_tool(self, tool_code: str, tool_input: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Execute a tool with the given input and context.

        This is an alias for call_tool to maintain compatibility with the interface expected by the workflow.

        Args:
            tool_code: The tool code to call
            tool_input: Input parameters for the tool
            context: Optional context information

        Returns:
            Tool execution result
        """
        logger.debug(f"MockToolRegistry.execute_tool called for '{tool_code}'")
        return await self.call_tool(tool_code, tool_input, context)
