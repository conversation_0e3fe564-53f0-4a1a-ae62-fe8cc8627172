"""
Utility functions for setting up mock testing scenarios.
"""
from typing import Dict, Any

# Import the specific mock service classes from their new locations
from .mock_llm_service import MockLLMService
from .mock_database_service import MockDatabaseService
from .mock_tool_registry import Mock<PERSON>oolRegistry

def create_scenario_fixture(scenario_name: str, **config) -> Dict[str, Any]:
    """Create a complete scenario fixture with all mock dependencies.

    Args:
        scenario_name: Name of the test scenario
        **config: Configuration options for the mocks. Expected keys:
                  'llm': Config for MockLLMService
                  'database': Config for MockDatabaseService
                  'tools': Config for MockToolRegistry
                  'use_extracted_definitions': bool for MockDatabaseService
                  'definitions_cache_path': str for MockDatabaseService
                  'tool_mapping_cache_path': str for MockDatabaseService
                  'use_extracted_tools': bool for MockDatabaseService
                  'tools_doc_path': str for MockDatabaseService

    Returns:
        Dict with configured mock instances:
            'scenario_name': str
            'llm_service': MockLLMService instance
            'db_service': MockDatabaseService instance
            'tool_registry': MockToolRegistry instance
    """
    # Extract individual component configs
    llm_config = config.get("llm", {})
    db_config = config.get("database", {})
    tools_config = config.get("tools", {}) # Config for MockToolRegistry

    # Extract settings relevant to MockDatabaseService initialization
    use_extracted_defs = config.get("use_extracted_definitions", True)
    definitions_cache_path = config.get("definitions_cache_path", None)
    tool_mapping_cache_path = config.get("tool_mapping_cache_path", None)
    use_extracted_tools = config.get("use_extracted_tools", True)
    tools_doc_path = config.get("tools_doc_path", None)

    # Create mock instances using the specific configs and settings
    db_service = MockDatabaseService(
        config=db_config, # Pass the 'database' sub-config
        use_extracted_definitions=use_extracted_defs,
        definitions_cache_path=definitions_cache_path,
        tool_mapping_cache_path=tool_mapping_cache_path,
        use_extracted_tools=use_extracted_tools,
        tools_doc_path=tools_doc_path
    )

    # MockToolRegistry might need the ToolDefinitionsExtractor from db_service if created there
    tool_registry = MockToolRegistry(
        config=tools_config, # Pass the 'tools' sub-config
        tool_extractor=db_service.tool_definitions_extractor # Reuse extractor if created
    )

    return {
        "scenario_name": scenario_name,
        "llm_service": MockLLMService(llm_config), # Pass the 'llm' sub-config
        "db_service": db_service,
        "tool_registry": tool_registry
    }
