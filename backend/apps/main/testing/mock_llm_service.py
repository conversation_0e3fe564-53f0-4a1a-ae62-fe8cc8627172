"""
Mock implementation of the LLM service for testing.
"""
import json
import logging
import re
from typing import Dict, Any, List, Optional

from .interfaces import LLMServiceInterface
from apps.main.llm.response import LLMResponse, ResponseType, ToolCall

logger = logging.getLogger(__name__)

class MockLLMService(LLMServiceInterface):
    """Mock implementation of LLM service with standardized responses."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize mock with optional configuration."""
        self.config = config or {}
        self.calls = []
        self.call_count = 0

    async def chat_completion(self,
                             messages: List[Dict[str, str]],
                             temperature: float = 0.7,
                             max_tokens: int = 2000,
                             tools: Optional[List[Dict[str, Any]]] = None) -> LLMResponse:
        """Mock implementation for chat completion."""
        # Record call for verification
        self.calls.append({
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "tools": tools
        })
        self.call_count += 1

        # Extract user message for pattern matching
        user_message = next((m.get("content", "") for m in messages if m.get("role") == "user"), "")

        # --- Debug Logging Start ---
        logger.debug(f"MockLLMService received user_message content:\n---\n{user_message}\n---")
        pattern_matched = False
        # --- Debug Logging End ---

        # Check for pattern-based responses
        if "response_patterns" in self.config:
            for pattern, response in self.config["response_patterns"].items():
                # --- Debug Logging Start ---
                # Perform case-insensitive matching
                match_result = pattern.lower() in user_message.lower()
                logger.debug(f"Checking pattern '{pattern}' (case-insensitive) in user_message: {match_result}")
                # --- Debug Logging End ---
                if match_result: # Use the result of the case-insensitive check
                    # --- Debug Logging Start ---
                    logger.debug(f"Pattern '{pattern}' matched!")
                    pattern_matched = True
                    # --- Debug Logging End ---
                    # Convert to LLMResponse if it's a dict
                    if isinstance(response, dict):
                        if "tool_calls" in response:
                            # Return tool call response
                            tool_calls = [
                                ToolCall(
                                    tool_name=tc.get("name", "unknown_tool"),
                                    tool_input=tc.get("arguments", {}), # Use "arguments" to match common usage/test config
                                    id=f"mock-id-{i}"
                                )
                                for i, tc in enumerate(response["tool_calls"])
                            ]
                            return LLMResponse(
                                response_type=ResponseType.TOOL_CALL,
                                tool_calls=tool_calls,
                                content=response.get("content")
                            )
                        else:
                            # Regular text response
                            return LLMResponse(
                                response_type=ResponseType.TEXT,
                                content=json.dumps(response) if not isinstance(response.get("content"), str) else response.get("content", json.dumps(response))
                            )
                    elif isinstance(response, LLMResponse):
                        # Already an LLMResponse
                        return response
                    else:
                        # String or other type
                        return LLMResponse(
                            response_type=ResponseType.TEXT,
                            content=str(response)
                        )

        # --- Debug Logging Start ---
        if not pattern_matched:
            logger.debug("No patterns matched. Returning default response.")
        # --- Debug Logging End ---

        # Default response - Ensure it includes a key expected by BenchmarkManager for semantic eval
        default_response = self.config.get("default_response", {"user_response": "Default mock agent response for benchmark."})
        if isinstance(default_response, dict):
            if "tool_calls" in default_response:
                # Tool call response
                tool_calls = [
                    ToolCall(
                        tool_name=tc.get("name", "unknown_tool"),
                        tool_input=tc.get("input", {}), # Keep original key for default response handling if needed
                        id=f"mock-id-{i}"
                    )
                    for i, tc in enumerate(default_response["tool_calls"])
                ]
                return LLMResponse(
                    response_type=ResponseType.TOOL_CALL,
                    tool_calls=tool_calls,
                    content=default_response.get("content")
                )
            elif "user_response" in default_response: # Check for the key we added
                 # Text response using the expected key
                 return LLMResponse(
                     response_type=ResponseType.TEXT,
                     content=json.dumps(default_response) # Serialize the whole dict including user_response
                 )
            else:
                 # Fallback text response if neither tool_calls nor user_response is present
                 # This preserves the old behavior for other potential default structures
                 return LLMResponse(
                     response_type=ResponseType.TEXT,
                     content=json.dumps(default_response) if not isinstance(default_response.get("content"), str) else default_response.get("content", json.dumps(default_response))
                 )
        elif isinstance(default_response, LLMResponse):
            return default_response
        else:
            return LLMResponse(
                response_type=ResponseType.TEXT,
                content=str(default_response)
            )

    async def parse_json_response(self, response: Any) -> Dict[str, Any]:
        """Parse a JSON response from the LLM."""
        try:
            if isinstance(response, LLMResponse) and response.content:
                # Try to extract JSON from LLMResponse content
                try:
                    return json.loads(response.content)
                except json.JSONDecodeError:
                    # Try to extract from markdown code blocks
                    json_match = re.search(r"```(?:json)?\s*([\s\S]*?)\s*```", response.content)
                    if json_match:
                        return json.loads(json_match.group(1))
                    raise  # Re-raise if not found in code blocks
            elif isinstance(response, dict):
                return response
            elif isinstance(response, str):
                return json.loads(response)
            else:
                return {"error": "Could not parse response", "raw_response": str(response)}
        except json.JSONDecodeError:
            return {"error": "Invalid JSON in response", "raw_content": str(response) if hasattr(response, "content") else str(response)}
