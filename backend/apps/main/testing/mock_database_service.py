"""
Mock implementation of the Database service for testing.
"""
import json
import logging
import uuid
from unittest.mock import <PERSON>M<PERSON>
from typing import Dict, Any, List, Optional

from .interfaces import DatabaseServiceInterface
from .definition_extractors import AgentDefinitionsExtractor, ToolDefinitionsExtractor

logger = logging.getLogger(__name__)

class MockRunFailedError(Exception):
    """Custom exception raised when a mock run completes with status 'failed'."""
    pass

class MockDatabaseService(DatabaseServiceInterface):
    """Mock database service for testing that uses extracted agent definitions and tool mappings."""

    def __init__(self, config: Optional[Dict[str, Any]] = None, use_extracted_definitions: bool = True,
                 definitions_cache_path: Optional[str] = None, tool_mapping_cache_path: Optional[str] = None,
                 use_extracted_tools: bool = True, tools_doc_path: Optional[str] = None):
        """Initialize with optional configuration.

        Args:
            config: Optional configuration with scenario-specific data
            use_extracted_definitions: Whether to use extracted agent definitions
            definitions_cache_path: Optional path to cache extracted definitions
            tool_mapping_cache_path: Optional path to cache extracted tool mappings
            use_extracted_tools: Whether to use extracted tool definitions
            tools_doc_path: Optional path to tools documentation for extraction
        """
        self.config = config or {}
        self.memory_store = {}
        self.runs = {}
        self.calls = {"get_memory": [], "set_memory": [], "start_run": [], "complete_run": []}

        # Set up agent definitions and tool mappings
        self.definitions_extractor = None
        self.agent_definitions = {}
        self.agent_tool_mappings = {}
        self.common_tools = []

        if use_extracted_definitions:
            self.definitions_extractor = AgentDefinitionsExtractor(
                definitions_cache_path,
                tool_mapping_cache_path
            )
            # Extract definitions and mappings, using cache if available
            self.agent_definitions = self.definitions_extractor.extract_definitions(use_cache=True)
            self.agent_tool_mappings, self.common_tools = self.definitions_extractor.extract_tool_mappings(use_cache=True)


        self.tool_definitions_extractor = None
        self.tool_definitions = {}
        self.tool_responses = {}

        if use_extracted_tools:
            self.tool_definitions_extractor = ToolDefinitionsExtractor() # Removed tools_doc_path argument
            # Extract tool definitions and mock responses, using cache if available
            self.tool_definitions = self.tool_definitions_extractor.extract_definitions() # Removed use_cache=True as it's not needed
            self.tool_responses = self.tool_definitions_extractor.tool_mock_responses


    def load_agent_definition(self, agent_role: str) -> Dict[str, Any]:
        """Load mock agent definition.

        Args:
            agent_role: The role of the agent

        Returns:
            Mock agent definition (as MagicMock)
        """
        agent_def_mock = MagicMock()
        # Normalize role for lookup
        role_lower = str(agent_role).lower()

        logger.debug(f"MockDatabaseService.load_agent_definition called for role: {agent_role}")

        # First try to get from extracted definitions
        definition = self.agent_definitions.get(role_lower, {})

        if definition:
            logger.debug(f"Found extracted definition for role: {agent_role}")
            try:
                # Set attributes from extracted definition with safe defaults
                agent_def_mock.role = agent_role # Keep original case if needed elsewhere

                # Set system instructions
                system_instructions = definition.get('system_instructions')
                if not isinstance(system_instructions, str):
                    system_instructions = f"You are a helpful {agent_role} agent"
                agent_def_mock.system_instructions = system_instructions

                # Set schema fields with proper type checking
                for schema_field in ['input_schema', 'output_schema', 'state_schema', 'memory_schema']:
                    schema_value = definition.get(schema_field)
                    if isinstance(schema_value, dict):
                        setattr(agent_def_mock, schema_field, schema_value)
                    elif isinstance(schema_value, str) and schema_value.startswith('{'):
                        # Try to parse JSON string
                        try:
                            setattr(agent_def_mock, schema_field, json.loads(schema_value))
                        except json.JSONDecodeError:
                            setattr(agent_def_mock, schema_field, {})
                    else:
                        setattr(agent_def_mock, schema_field, {})

                # Set list fields with proper type checking
                for list_field in ['recommend_models', 'read_models', 'write_models']:
                    list_value = definition.get(list_field)
                    if isinstance(list_value, list):
                        setattr(agent_def_mock, list_field, list_value)
                    elif isinstance(list_value, str) and list_value.startswith('['):
                        # Try to parse JSON string
                        try:
                            setattr(agent_def_mock, list_field, json.loads(list_value))
                        except json.JSONDecodeError:
                            setattr(agent_def_mock, list_field, [])
                    else:
                        setattr(agent_def_mock, list_field, [])

                # Set langgraph_node_class if available
                langgraph_node_class = definition.get('langgraph_node_class')
                if isinstance(langgraph_node_class, str):
                    agent_def_mock.langgraph_node_class = langgraph_node_class
                else:
                    # Provide a default class path based on role
                    default_class_paths = {
                        'mentor': 'apps.main.agents.mentor_agent.MentorAgent',
                        'orchestrator': 'apps.main.agents.orchestrator_agent.OrchestratorAgent',
                        'resource': 'apps.main.agents.resource_agent.ResourceAgent',
                        'engagement': 'apps.main.agents.engagement_agent.EngagementAndPatternAgent',
                        'psychological': 'apps.main.agents.psy_agent.PsychologicalMonitoringAgent',
                        'strategy': 'apps.main.agents.strategy_agent.StrategyAgent',
                        'wheel_activity': 'apps.main.agents.wheel_activity_agent.WheelAndActivityAgent',
                        'ethical': 'apps.main.agents.ethical_agent.EthicalAgent',
                        'dispatcher': 'apps.main.agents.dispatcher_agent.DispatcherAgent',
                        'error_handler': 'apps.main.agents.error_handler_agent.ErrorHandlerAgent'
                    }
                    agent_def_mock.langgraph_node_class = default_class_paths.get(
                        role_lower, f'apps.main.agents.{role_lower}_agent.{role_lower.capitalize()}Agent'
                    )

                # Create a method to get available tools as dictionaries
                # This part is complex as it relies on tool definitions being loaded
                # We'll use the load_tools method logic here directly
                # Note: This is a synchronous method that returns an empty list
                # The actual async load_tools will be called directly in the agent
                def get_available_tools_dict():
                    # Return an empty list since this is a sync method and can't call async load_tools
                    return []

                agent_def_mock.get_available_tools_dict = get_available_tools_dict

                # Add llm_config attribute if missing
                if not hasattr(agent_def_mock, 'llm_config') or agent_def_mock.llm_config is None:
                    # Create a mock LLMConfig
                    llm_config_mock = MagicMock()
                    llm_config_mock.model_name = "mistral-small-latest"
                    llm_config_mock.temperature = 0.7
                    llm_config_mock.is_default = True
                    llm_config_mock.is_evaluation = False
                    agent_def_mock.llm_config = llm_config_mock

                # Add version attribute if missing
                if not hasattr(agent_def_mock, 'version') or agent_def_mock.version is None:
                    agent_def_mock.version = "1.0.0"

                # Add is_active attribute if missing
                if not hasattr(agent_def_mock, 'is_active') or agent_def_mock.is_active is None:
                    agent_def_mock.is_active = True

                logger.debug(f"Successfully created agent definition mock for role: {agent_role}")
                return agent_def_mock

            except Exception as e:
                logger.warning(f"Error setting up agent definition for {agent_role}: {str(e)}")
                # Fall through to other fallbacks

        # Fallback to configured agent definitions if available
        agent_definitions_config = self.config.get("agent_definitions", {})
        if agent_role in agent_definitions_config:
            logger.debug(f"Using configured agent definition for role: {agent_role}")
            # Return as MagicMock for consistency
            return MagicMock(**agent_definitions_config[agent_role])

        # Final fallback to bare minimum definition
        logger.debug(f"Using fallback agent definition for role: {agent_role}")
        agent_def_mock.role = agent_role
        agent_def_mock.system_instructions = f"You are a helpful {agent_role} agent"
        agent_def_mock.recommend_models = []
        agent_def_mock.read_models = []
        agent_def_mock.write_models = []
        agent_def_mock.input_schema = {}
        agent_def_mock.output_schema = {}
        agent_def_mock.state_schema = {}
        agent_def_mock.memory_schema = {}
        agent_def_mock.version = "1.0.0"
        agent_def_mock.is_active = True

        # Provide a default class path based on role
        default_class_paths = {
            'mentor': 'apps.main.agents.mentor_agent.MentorAgent',
            'orchestrator': 'apps.main.agents.orchestrator_agent.OrchestratorAgent',
            'resource': 'apps.main.agents.resource_agent.ResourceAgent',
            'engagement': 'apps.main.agents.engagement_agent.EngagementAndPatternAgent',
            'psychological': 'apps.main.agents.psy_agent.PsychologicalMonitoringAgent',
            'strategy': 'apps.main.agents.strategy_agent.StrategyAgent',
            'wheel_activity': 'apps.main.agents.wheel_activity_agent.WheelAndActivityAgent',
            'ethical': 'apps.main.agents.ethical_agent.EthicalAgent',
            'dispatcher': 'apps.main.agents.dispatcher_agent.DispatcherAgent',
            'error_handler': 'apps.main.agents.error_handler_agent.ErrorHandlerAgent'
        }
        agent_def_mock.langgraph_node_class = default_class_paths.get(
            role_lower, f'apps.main.agents.{role_lower}_agent.{role_lower.capitalize()}Agent'
        )

        # Create a mock LLMConfig
        llm_config_mock = MagicMock()
        llm_config_mock.model_name = "mistral-small-latest"
        llm_config_mock.temperature = 0.7
        llm_config_mock.is_default = True
        llm_config_mock.is_evaluation = False
        agent_def_mock.llm_config = llm_config_mock

        agent_def_mock.get_available_tools_dict = lambda: [] # Empty tools list

        return agent_def_mock

    async def load_tools(self, agent_definition: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Load mock tools for an agent using extracted tool mappings and definitions.

        Converts tool definitions to the format expected by LLM:
        {
            "type": "function",
            "function": {
                "name": tool_code,
                "description": tool_description,
                "parameters": input_schema
            }
        }

        Args:
            agent_definition: The agent definition (MagicMock expected)

        Returns:
            List of tools in LLM-compatible format
        """
        agent_role = agent_definition.role if hasattr(agent_definition, 'role') else 'unknown'
        agent_role_str = str(agent_role).lower()

        # Check if we have specific tools configured in the config
        if "tools" in self.config and agent_role_str in self.config.get("tools", {}):
            return self.config["tools"][agent_role_str]

        # Use extracted tool mappings to get tool codes
        role_tool_codes = set() # Use set to avoid duplicates

        # First, add the common tools for all agents
        role_tool_codes.update(self.common_tools)

        # Then, add role-specific tools
        role_tool_codes.update(self.agent_tool_mappings.get(agent_role_str, []))

        # Convert tool codes to LLM-compatible format
        loaded_tool_definitions = []
        for tool_code in role_tool_codes:
            # Get the full definition if available
            if tool_code in self.tool_definitions:
                tool_def = self.tool_definitions[tool_code]

                # Convert to LLM-compatible format
                llm_tool = {
                    "type": "function",
                    "function": {
                        "name": tool_code,
                        "description": tool_def.get('description', f'Tool for {tool_code}'),
                        "parameters": tool_def.get('input_schema', {"type": "object", "properties": {}})
                    }
                }

                loaded_tool_definitions.append(llm_tool)
            else:
                # Fallback to basic definition if not found in extracted data
                logger.warning(f"Tool definition for '{tool_code}' not found, using basic mock.")
                loaded_tool_definitions.append({
                    "type": "function",
                    "function": {
                        "name": tool_code,
                        "description": f'Mock tool for {tool_code}',
                        "parameters": {"type": "object", "properties": {}}
                    }
                })

        # If no tools found after checking mappings and definitions, provide a minimal fallback
        if not loaded_tool_definitions:
            logger.warning(f"No tools found for agent role '{agent_role_str}', providing fallback.")
            return [
                {
                    "type": "function",
                    "function": {
                        "name": "mock_fallback_tool",
                        "description": "A fallback tool when none are defined.",
                        "parameters": {"type": "object", "properties": {}}
                    }
                }
            ]

        return loaded_tool_definitions

    async def start_run(self, # Changed to async
                 agent_definition: Dict[str, Any], # Expects MagicMock
                 user_profile_id: str,
                 input_data: Dict[str, Any],
                 state: Dict[str, Any]) -> Dict[str, Any]: # Returns MagicMock
        """Record mock run start.

        Args:
            agent_definition: The agent definition (MagicMock)
            user_profile_id: The user profile ID
            input_data: Input data for the run
            state: Initial state for the run

        Returns:
            Mock run information (MagicMock)
        """
        run_id = str(uuid.uuid4())
        agent_role = agent_definition.role if hasattr(agent_definition, 'role') else 'unknown'

        run_info = {
            'id': run_id,
            'agent_role': agent_role,
            'user_profile_id': user_profile_id,
            'input_data': input_data,
            'initial_state': state,
            'status': 'running'
        }

        self.runs[run_id] = run_info
        self.calls["start_run"].append(run_info)

        # Return a MagicMock simulating the Run object
        return MagicMock(id=run_id)

    async def complete_run(self, # Changed to async
                     run_id: str,
                     output_data: Dict[str, Any],
                     state: Dict[str, Any], # Keep state for logging/inspection if needed
                    memory_updates: Optional[Dict[str, Any]] = None,
                    status: str = 'completed',
                    error_message: Optional[str] = None) -> None: # Added error_message argument
        """Record mock run completion. If status is 'failed', raise MockRunFailedError.

        Args:
            run_id: The run ID (string)
            output_data: Output data from the run
            state: Final state
            memory_updates: Optional memory updates
            status: Run status
            error_message: Optional error message if status is 'failed'

        Raises:
            MockRunFailedError: If status is 'failed'.
        """
        # Log the call regardless of status
        call_data = {
            "run_id": run_id,
            "output_data": output_data,
            "state": state,
            "memory_updates": memory_updates,
            "status": status,
            "error_message": error_message # Store error message
        }
        self.calls["complete_run"].append(call_data)

        if run_id in self.runs:
            # Update status first
            self.runs[run_id]['status'] = status

            if status == 'failed':
                # Extract error message with enhanced handling for error_handler agent
                agent_role = self.runs[run_id].get('agent_role', 'unknown')

                # Special handling for error_handler agent
                if agent_role.lower() == 'error_handler':
                    # Check for meta_error in output_data first (error handler agent specific)
                    if isinstance(output_data, dict) and 'meta_error' in output_data:
                        effective_error_message = output_data['meta_error']
                    # Then check for error_message parameter
                    elif error_message:
                        effective_error_message = error_message
                    # Then check for error in output_data
                    elif isinstance(output_data, dict) and 'error' in output_data:
                        effective_error_message = output_data['error']
                    # Check for original_error in output_data
                    elif isinstance(output_data, dict) and 'original_error' in output_data:
                        effective_error_message = output_data['original_error']
                    # Fallback
                    else:
                        effective_error_message = "No error message provided for error_handler agent"
                else:
                    # Standard error extraction for other agents
                    if error_message:
                        effective_error_message = error_message
                    elif isinstance(output_data, dict) and 'error' in output_data:
                        effective_error_message = output_data['error']
                    else:
                        effective_error_message = "No error message provided"

                # Store the final state and output data even on failure for inspection
                self.runs[run_id].update({
                    'output_data': output_data, # Keep original output_data
                    'final_state': state,
                    'memory_updates': memory_updates, # May be None
                    'error_message': effective_error_message, # Store the error message
                    'agent_role': agent_role # Store agent role for context
                })

                logger.error(f"Mock run {run_id} for agent '{agent_role}' completed with status 'failed'. Raising MockRunFailedError.")
                raise MockRunFailedError(f"Mock run {run_id} failed. Error: {effective_error_message}")
            else:
                # Proceed with normal completion logic for non-failed statuses
                self.runs[run_id].update({
                    'output_data': output_data,
                    'final_state': state,
                    'memory_updates': memory_updates,
                })

                # Apply memory updates if provided
            if memory_updates:
                agent_role = self.runs[run_id].get('agent_role', 'unknown')
                user_id = self.runs[run_id].get('user_profile_id', 'unknown')

                for key, value in memory_updates.items():
                    # Use the set_memory method to ensure consistency and recording
                    self.set_memory(agent_role, user_id, key, value)
        else:
             logger.warning(f"Attempted to complete non-existent run_id: {run_id}")
             if status == 'failed':
                 # Check for meta_error in output_data first (error handler agent specific)
                 if isinstance(output_data, dict) and 'meta_error' in output_data:
                     effective_error_message = output_data['meta_error']
                 # Then check for error_message parameter
                 elif error_message:
                     effective_error_message = error_message
                 # Then check for error in output_data
                 elif isinstance(output_data, dict) and 'error' in output_data:
                     effective_error_message = output_data['error']
                 # Fallback
                 else:
                     effective_error_message = "No error message provided"

                 # Still raise if status is failed, even if run_id wasn't tracked
                 raise MockRunFailedError(f"Attempted to complete non-existent run_id {run_id} with status 'failed'. Error: {effective_error_message}")

        # Ensure the method returns None as per the interface
        return None

    async def get_memory(self, agent_role: str, user_profile_id: str, memory_key: str) -> Any: # Changed to async
        """Get mock memory value.

        Args:
            agent_role: The role of the agent
            user_profile_id: The user profile ID
            memory_key: The memory key to retrieve

        Returns:
            Memory value if found, otherwise None
        """
        memory_id = f"{agent_role}:{user_profile_id}:{memory_key}"
        self.calls["get_memory"].append({
            "agent_role": agent_role,
            "user_profile_id": user_profile_id,
            "memory_key": memory_key
        })

        # Check for predefined memory values in config first
        predefined = self.config.get("memory_data", {}).get(memory_key)
        if predefined is not None:
            logger.debug(f"Returning predefined memory for key '{memory_key}'")
            return predefined

        # Then check the dynamic memory store
        value = self.memory_store.get(memory_id)
        logger.debug(f"Memory get '{memory_id}': {'Found' if value is not None else 'Not found'}")
        return value


    async def set_memory(self, # Changed to async
                  agent_role: str,
                  user_profile_id: str,
                  memory_key: str,
                  content: Any,
                  confidence: float = 1.0) -> None: # Returns MagicMock in original, but interface says None
        """Set mock memory value.

        Args:
            agent_role: The role of the agent
            user_profile_id: The user profile ID
            memory_key: The memory key to set
            content: The memory content
            confidence: Confidence level for the memory (ignored in mock)
        """
        memory_id = f"{agent_role}:{user_profile_id}:{memory_key}"
        self.memory_store[memory_id] = content
        self.calls["set_memory"].append({
            "agent_role": agent_role,
            "user_profile_id": user_profile_id,
            "memory_key": memory_key,
            "content": content,
            "confidence": confidence
        })
        logger.debug(f"Memory set '{memory_id}'")

        # Interface expects None, but original mock returned MagicMock.
        # Returning None to match interface. If tests break, adjust.
        # return MagicMock(
        #     agent_role=agent_role,
        #     user_profile_id=user_profile_id,
        #     memory_key=memory_key,
        #     content=content,
        #     confidence=confidence
        # )
        return None


    def get_agent_definition_dict(self, agent_role: str) -> Dict[str, Any]:
        """
        Get the raw definition dictionary for an agent.
        Useful for testing without creating a MagicMock.

        Args:
            agent_role: The agent role

        Returns:
            dict: The agent definition dictionary
        """
        # Normalize role for lookup
        role_lower = str(agent_role).lower()
        def_dict = self.agent_definitions.get(role_lower, {})
        # Ensure the role key exists in the returned dict
        if 'role' not in def_dict:
            def_dict['role'] = agent_role
        return def_dict

    def preload_memory(self, memory_data: Dict[str, Any]) -> None:
        """
        Preload memory with test data. Keys should be in 'agent_role:user_id:memory_key' format
        or just 'memory_key' (will use default 'unknown:unknown').

        Args:
            memory_data: Dictionary of memory data to preload
        """
        for key, value in memory_data.items():
            if ':' in key and len(key.split(':')) == 3:
                # Key already has agent_role:user_id:memory_key format
                self.memory_store[key] = value
                logger.debug(f"Preloaded memory '{key}'")
            else:
                # Assume default user and agent
                default_key = f"unknown:unknown:{key}"
                self.memory_store[default_key] = value
                logger.debug(f"Preloaded memory '{default_key}' (from key '{key}')")
