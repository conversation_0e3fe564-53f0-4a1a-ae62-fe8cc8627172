# backend/apps/main/testing/workflow_test_runner.py

from typing import Dict, Any, Optional, Callable, Type, List, Union
from pydantic import BaseModel
from unittest.mock import patch, AsyncMock
import importlib
import inspect
import sys
import asyncio # Added for iscoroutinefunction check

class WorkflowTestRunner:
    """Test runner for LangGraph workflows."""
    
    def __init__(self, create_workflow_fn: Callable, user_profile_id: str = "test-user-id",
                 agent_classes: Optional[List[Type]] = None):
        """Initialize the workflow test runner.
        
        Args:
            create_workflow_fn: Function that creates the workflow graph
            user_profile_id: The user profile ID to use
            agent_classes: Optional explicit list of agent classes to patch
        """
        self.create_workflow_fn = create_workflow_fn
        self.user_profile_id = user_profile_id
        self.patches = []
        self.original_process_methods = {}
        self.agent_mocks = {}
        self.agent_classes = agent_classes
        
    async def setup(self, agent_mocks: Optional[Dict[str, Dict[str, Any]]] = None):
        """Set up patches for agent process methods.
        
        Args:
            agent_mocks: Dict mapping agent roles to mock output data
            
        Returns:
            Tuple containing the workflow and compiled app
        """
        self.agent_mocks = agent_mocks or {}
        
        # Clean up any existing patches
        self.cleanup()
        
        # Get the source file of the create_workflow_fn to determine what agent classes to import
        workflow_module = inspect.getmodule(self.create_workflow_fn)
        
        # Extract or use provided agent classes
        agent_classes = self.agent_classes or self._extract_agent_classes(workflow_module)
        
        # Patch agent process methods before creating the workflow
        for agent_class in agent_classes:
            agent_role = getattr(agent_class, 'agent_role', agent_class.__name__.lower())
            
            # If we have a mock for this role, patch its process method
            if agent_role in self.agent_mocks:
                mock_output = self.agent_mocks[agent_role]
                
                # Save original method for cleanup
                if hasattr(agent_class, 'process'):
                    self.original_process_methods[agent_role] = agent_class.process
                
                # Create patched process method that returns our mock output
                async def create_mock_process(role, output):
                    async def mock_process(self, state):
                        # Create a copy of the output to avoid modifying the original
                        result_output = output.copy() if isinstance(output, dict) else output
                        
                        # Update state tracking attributes
                        if hasattr(state, "last_agent"):
                            state.last_agent = role
                            
                        # Return only the mock output dictionary, as expected by BaseAgent.__call__
                        return result_output 
                    return mock_process
                
                # Create a bound method with closure over the role and output
                mock_process_method = await create_mock_process(agent_role, mock_output)
                
                # Patch the process method
                p = patch.object(agent_class, 'process', mock_process_method)
                p.start()
                self.patches.append(p)
        
        # Now create the workflow with patched agents
        # Check if the creation function is async
        if inspect.iscoroutinefunction(self.create_workflow_fn):
            workflow = await self.create_workflow_fn(self.user_profile_id)
        else:
            workflow = self.create_workflow_fn(self.user_profile_id) # Call sync function directly
            
        compiled_app = workflow.compile()
        
        return workflow, compiled_app
    
    async def run_test(self, 
                       state: Union[BaseModel, Dict[str, Any]],
                       agent_mocks: Optional[Dict[str, Dict[str, Any]]] = None) -> Any:
        """Run a test with the workflow.
        
        Args:
            state: The initial state model or dictionary
            agent_mocks: Dict mapping agent roles to mock output data
            
        Returns:
            Any: The workflow result (could be a dict or AddableValuesDict)
        """
        try:
            # Set up patches
            workflow, compiled_app = await self.setup(agent_mocks=agent_mocks)
            
            # Run the workflow
            # If state is a BaseModel, we need to use it directly
            # Otherwise, we use the dict format expected by LangGraph
            if isinstance(state, dict):
                result = await compiled_app.ainvoke(state)
            else:
                # LangGraph expects a specific input structure for ainvoke
                # Try both approaches - standard and with the state wrapped in {'input': state}
                try:
                    result = await compiled_app.ainvoke(state)
                except Exception:
                    # If that fails, try wrapping in the input/config format
                    result = await compiled_app.ainvoke({"input": state})
            
            return result
        finally:
            # Clean up patches
            self.cleanup()
    
    def cleanup(self):
        """Clean up by stopping all patches."""
        for p in self.patches:
            p.stop()
        self.patches = []
        
        # Restore original process methods if needed
        for agent_role, original_method in self.original_process_methods.items():
            # This would be needed if patches weren't properly stopped
            pass
    
    def _extract_agent_classes(self, module):
        """Extract agent classes imported in the module."""
        # Implementation remains the same as before
        agent_classes = []
        
        # Iterate through module attributes
        for name, obj in inspect.getmembers(module):
            # Look for classes that might be agents
            if inspect.isclass(obj) and hasattr(obj, 'process') and callable(getattr(obj, 'process')):
                # Check if it's a LangGraphAgent
                if any(base.__name__ == 'LangGraphAgent' for base in obj.__mro__):
                    agent_classes.append(obj)
                    continue
                
                # Check method signatures as a fallback
                try:
                    sig = inspect.signature(obj.process)
                    if 'state' in sig.parameters:
                        agent_classes.append(obj)
                except (ValueError, TypeError):
                    pass
        
        # If we didn't find any, try to look through imported modules
        if not agent_classes:
            for name, obj in inspect.getmembers(module):
                if inspect.ismodule(obj) and obj.__name__.startswith('apps.main.agents'):
                    # This is one of our agent modules, look for agent classes
                    for subname, subobj in inspect.getmembers(obj):
                        if (inspect.isclass(subobj) and hasattr(subobj, 'process') and 
                            callable(getattr(subobj, 'process'))):
                            agent_classes.append(subobj)
        
        return agent_classes
