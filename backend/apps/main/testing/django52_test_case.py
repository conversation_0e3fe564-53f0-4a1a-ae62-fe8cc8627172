"""
Custom TestCase class that is compatible with Django 5.2's flush command.

This module provides a Django52TestCase class that overrides the _fixture_teardown
method to use the new flush command parameters introduced in Django 5.2.
"""

from django.test import TestCase
from django.core.management import call_command


class Django52TestCase(TestCase):
    """
    A custom TestCase class that is compatible with Django 5.2's flush command.

    This class overrides the _fixture_teardown method to use the new flush command
    parameters introduced in Django 5.2 (reset_sequences, allow_cascade, inhibit_post_migrate)
    instead of the old parameters (database, interactive, verbosity).
    """

    @classmethod
    def _fixture_teardown(cls):
        """
        Override the _fixture_teardown method to use the new flush command parameters.
        """
        # Use the new flush command parameters for Django 5.2.1
        # Valid options are: allow_cascade, inhibit_post_migrate, reset_sequences
        call_command(
            'flush',
            reset_sequences=True,
            allow_cascade=True,
            inhibit_post_migrate=False,
        )
