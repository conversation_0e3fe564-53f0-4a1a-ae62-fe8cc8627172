"""
Test-specific implementation of the ResourceAgent for testing.
"""
import logging
from typing import Dict, Any, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from apps.main.models import LLMConfig
from pydantic import BaseModel

from apps.main.agents.base_agent import LangGraphAgent

logger = logging.getLogger(__name__)

class TestResourceAgent(LangGraphAgent):
    """Test-specific implementation of the ResourceAgent for testing."""

    def __init__(self,
                 user_profile_id: str,
                 db_service=None,
                 llm_client=None,
                 llm_config: Optional['LLMConfig'] = None):
        # Pass user_profile_id and agent_role to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="resource"
        )
        # Store other parameters separately
        self.db_service = db_service
        self.llm_client = llm_client
        self.llm_config = llm_config
        self.agent_definition = None
        self.available_tools = []
        self.run_id = None # Initialize run_id

    async def process(self, state: BaseModel) -> Dict[str, Any]:
        """Analyze user's resources, environment and constraints."""
        try:
            # Extract context packet from state
            context_packet = getattr(state, "context_packet", {})
            if not context_packet:
                raise ValueError("Missing context packet in state")

            # Extract user_profile_id from context
            user_profile_id = context_packet.get("user_id")
            if not user_profile_id:
                raise ValueError("Missing user_id in context packet")

            # Store user_profile_id for tool calls
            self.user_profile_id = user_profile_id

            # Extract run_id if present
            self.run_id = context_packet.get("run_id", "mock-run-id")

            # Analyze environment, time, and resources
            environment_data = await self._analyze_environment(context_packet)
            time_data = await self._analyze_time_availability(context_packet)
            resources_data = await self._analyze_resources(context_packet)

            # Construct resource context
            resource_context = {
                "environment": environment_data,
                "time": time_data,
                "resources": resources_data,
                "workflow_type": context_packet.get("workflow_type", "unknown")
            }

            # Prepare output data
            output_data = {
                "resource_context": resource_context,
                "next_agent": "engagement"  # Route to engagement agent next
            }

            return {"output_data": output_data}

        except Exception as e:
            logger.error(f"Exception caught in TestResourceAgent process: {str(e)}", exc_info=True)
            error_message = f"Error in resource agent: {str(e)}"
            error_output_data = {
                "error": error_message,
                "resource_context": {},  # Include empty resource_context
                "next_agent": "error_handler"  # Route to error handler on error
            }
            return {"error": error_message, "output_data": error_output_data}

    async def _analyze_environment(self, context_packet):
        """Analyze the user's environment"""
        # Extract environment information from context
        reported_environment = context_packet.get("reported_environment", "")
        if reported_environment is None:
            reported_environment = ""

        logger.debug(f"Analyzing environment from context: '{reported_environment}'")

        # Use tool to get environment data
        try:
            environment_data = await self._call_tool(
                "get_environment_context",
                {
                    "user_profile_id": self.user_profile_id,
                    "reported_environment": reported_environment
                }
            )

            # Ensure environment_data is a dictionary
            if not isinstance(environment_data, dict):
                logger.warning(f"Tool returned non-dict environment data: {environment_data}")
                environment_data = {}

            # Ensure reported environment is included in the result
            result = {
                "reported": reported_environment,
                "analyzed_type": environment_data.get("environment_type", "unknown"),
                "domain_support": environment_data.get("domain_support", {}),
                "limitations": environment_data.get("limitations", []),
                "opportunities": environment_data.get("opportunities", [])
            }

            # If the tool returned a 'reported' field, use that instead
            if "reported" in environment_data:
                result["reported"] = environment_data["reported"]

            logger.debug(f"Environment analysis result: {result}")
            return result

        except Exception as e:
            logger.error(f"Error analyzing environment: {str(e)}", exc_info=True)
            # Return minimal data if tool fails, but ensure reported environment is included
            return {
                "reported": reported_environment,
                "analyzed_type": "unknown",
                "domain_support": {},
                "confidence": 0.5
            }

    async def _analyze_time_availability(self, context_packet):
        """Analyze the user's time availability"""
        # Extract time information from context
        reported_time = context_packet.get("reported_time_availability", "")

        # Use tool to parse time availability
        try:
            time_data = await self._call_tool(
                "parse_time_availability",
                {
                    "user_profile_id": self.user_profile_id,
                    "reported_time": reported_time
                }
            )

            # Add reported time to the result
            time_data["reported"] = reported_time
            return time_data

        except Exception as e:
            # Return minimal data if tool fails
            return {
                "reported": reported_time,
                "duration_minutes": 30,  # Default to 30 minutes
                "flexibility": "medium",
                "confidence": 0.5
            }

    async def _analyze_resources(self, context_packet):
        """Analyze the user's available resources"""
        try:
            # Call tool to get available resources
            resources_data = await self._call_tool(
                "get_available_resources",
                {
                    "user_profile_id": self.user_profile_id,
                    "context": context_packet
                }
            )

            # Ensure we have the expected structure
            if not isinstance(resources_data, dict):
                resources_data = {}

            # Rename fields for consistency
            return {
                "available_inventory": resources_data.get("inventory", []),
                "limitations": resources_data.get("limitations", []),
                "capabilities": resources_data.get("capabilities", {}),
                "confidence": resources_data.get("confidence", 0.7)
            }

        except Exception as e:
            # Return minimal data if tool fails
            return {
                "available_inventory": ["basic resources"],
                "limitations": ["unknown limitations"],
                "capabilities": {"physical": 50, "cognitive": 50, "emotional": 50},
                "confidence": 0.5
            }
