"""
Standardized mock implementations for agent dependencies.

This module provides consistent mock implementations that conform to the interfaces
defined in interfaces.py, with configuration options for test scenarios.

It re-exports the main mock classes and utility functions from their respective modules.
"""
import logging

# Re-export the main mock classes and utilities
from .definition_extractors import AgentDefinitionsExtractor, ToolDefinitionsExtractor
from .mock_llm_service import MockLLMService
from .mock_database_service import MockDatabaseService
from .mock_tool_registry import MockToolRegistry
from .mock_utils import create_scenario_fixture

# Setup logger for the testing package
logger = logging.getLogger(__name__)

# You can add package-level initialization or configuration here if needed
# For example, setting up default logging for the mocks package

# Make specific classes available for easier import if desired
# (This is redundant with the direct imports above but can be explicit)
__all__ = [
    'AgentDefinitionsExtractor',
    'ToolDefinitionsExtractor',
    'MockLLMService',
    'MockDatabaseService',
    'MockToolRegistry',
    'create_scenario_fixture',
]
