"""

Interface definitions for agent dependencies to support dependency injection in testing.



This module defines the abstract interfaces that all dependencies should implement,

ensuring consistent behavior between real implementations and test mocks.

"""

from abc import ABC, abstractmethod

from typing import Dict, Any, List, Optional





class LLMServiceInterface(ABC):

    """Interface for LLM service operations."""



    @abstractmethod

    async def chat_completion(self,

                             messages: List[Dict[str, str]],

                             temperature: float = 0.7,

                             max_tokens: int = 2000,

                             tools: Optional[List[Dict[str, Any]]] = None) -> Any:

        """Send a request to the chat completion API.



        Args:

            messages: List of message objects with role and content

            temperature: Temperature parameter for generation

            max_tokens: Maximum tokens to generate

            tools: Optional tools for function calling



        Returns:

            The generated response content

        """

        pass



    @abstractmethod

    async def parse_json_response(self, response: Any) -> Dict[str, Any]:

        """Parse a JSON response from the LLM.



        Args:

            response: Raw response from the LLM



        Returns:

            Parsed JSON object

        """

        pass





class DatabaseServiceInterface(ABC):

    """Interface for database service operations."""



    @abstractmethod

    async def load_agent_definition(self, agent_role: str) -> Dict[str, Any]:

        """Load agent definition from database.



        Args:

            agent_role: The role of the agent



        Returns:

            Agent definition object

        """

        pass



    @abstractmethod

    async def load_tools(self, agent_definition: Dict[str, Any]) -> List[Dict[str, Any]]:

        """Load tools for an agent definition.



        Args:

            agent_definition: The agent definition



        Returns:

            List of tool definitions

        """

        pass



    @abstractmethod

    async def start_run(self,

                 agent_definition: Dict[str, Any],

                 user_profile_id: str,

                 input_data: Dict[str, Any],

                 state: Dict[str, Any]) -> Dict[str, Any]:

        """Start an agent run and record it.



        Args:

            agent_definition: The agent definition

            user_profile_id: The user profile ID

            input_data: Input data for the run

            state: Initial state for the run



        Returns:

            Run information including ID

        """

        pass



    @abstractmethod

    async def complete_run(self,

                    run_id: str,

                    output_data: Dict[str, Any],

                    state: Dict[str, Any],

                    memory_updates: Optional[Dict[str, Any]] = None,

                    status: str = 'completed') -> None:

        """Complete an agent run and record results.



        Args:

            run_id: The run ID

            output_data: Output data from the run

            state: Final state from the run

            memory_updates: Optional memory updates

            status: Run status (completed, failed, etc.)

        """

        pass



    @abstractmethod

    async def get_memory(self,

                  agent_role: str,

                  user_profile_id: str,

                  memory_key: str) -> Any:

        """Get agent memory value.



        Args:

            agent_role: The role of the agent

            user_profile_id: The user profile ID

            memory_key: The memory key to retrieve



        Returns:

            Memory value or None if not found

        """

        pass



    @abstractmethod

    async def set_memory(self,

                  agent_role: str,

                  user_profile_id: str,

                  memory_key: str,

                  content: Any,

                  confidence: float = 1.0) -> None:

        """Set agent memory value.



        Args:

            agent_role: The role of the agent

            user_profile_id: The user profile ID

            memory_key: The memory key to set

            content: The memory content

            confidence: Confidence level for the memory

        """

        pass





class ToolRegistryInterface(ABC):

    """Interface for tool registry operations."""



    @abstractmethod

    async def call_tool(self,

                       tool_code: str,

                       tool_input: Dict[str, Any],

                       context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:

        """Call a tool with the given input.



        Args:

            tool_code: The tool code to call

            tool_input: Input parameters for the tool

            context: Optional context information



        Returns:

            Tool execution result

        """

        pass



    @abstractmethod

    def get_tool_schema(self, tool_code: str) -> Dict[str, Any]:

        """Get the schema for a tool.



        Args:

            tool_code: The tool code



        Returns:

            Tool schema definition

        """

        pass



    @abstractmethod

    def list_available_tools(self, category: Optional[str] = None) -> List[str]:

        """List available tools, optionally filtered by category.



        Args:

            category: Optional category to filter by



        Returns:

            List of tool codes

        """

        pass

