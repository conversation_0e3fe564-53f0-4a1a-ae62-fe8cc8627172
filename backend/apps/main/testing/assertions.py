# backend/apps/main/testing/assertions.py

from typing import Dict, Any, List, Optional

def assert_tool_called(test_runner, tool_code, min_times=1):
    """Assert that a specific tool was called at least the specified number of times."""
    if not test_runner.tool_registry or not hasattr(test_runner.tool_registry, 'calls'):
        raise AssertionError("Tool registry not available or doesn't track calls")
        
    calls = [call for call in test_runner.tool_registry.calls 
             if call.get("tool_code") == tool_code]
    
    assert len(calls) >= min_times, f"Tool '{tool_code}' was called {len(calls)} times, expected at least {min_times}"
    
    return calls

def assert_memory_accessed(test_runner, memory_key, access_type="any"):
    """Assert that a specific memory key was accessed."""
    if not test_runner.db_service or not hasattr(test_runner.db_service, 'calls'):
        raise AssertionError("Database service not available or doesn't track calls")
    
    get_calls = [call for call in test_runner.db_service.calls.get("get_memory", []) 
                if call.get("memory_key") == memory_key]
                
    set_calls = [call for call in test_runner.db_service.calls.get("set_memory", []) 
                if call.get("memory_key") == memory_key]
    
    if access_type == "get":
        assert len(get_calls) > 0, f"Memory key '{memory_key}' was never retrieved"
        return get_calls
    elif access_type == "set":
        assert len(set_calls) > 0, f"Memory key '{memory_key}' was never set"
        return set_calls
    else:  # "any"
        assert len(get_calls) + len(set_calls) > 0, f"Memory key '{memory_key}' was never accessed"
        return get_calls + set_calls

def assert_llm_prompt_contains(test_runner, text):
    """Assert that an LLM prompt contained the specified text."""
    if not test_runner.llm_service or not hasattr(test_runner.llm_service, 'calls'):
        raise AssertionError("LLM service not available or doesn't track calls")
    
    matching_calls = []
    for call in test_runner.llm_service.calls:
        for message in call.get("messages", []):
            if message.get("role") == "user" and text in message.get("content", ""):
                matching_calls.append(call)
                break
    
    assert len(matching_calls) > 0, f"No LLM prompt contained the text: '{text}'"
    return matching_calls