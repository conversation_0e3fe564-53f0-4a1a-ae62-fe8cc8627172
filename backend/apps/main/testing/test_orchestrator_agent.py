"""
Test-specific implementation of the OrchestratorAgent for testing.
"""
import logging
from typing import Dict, Any, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from apps.main.models import LLMConfig
from pydantic import BaseModel

from apps.main.agents.base_agent import LangGraphAgent

logger = logging.getLogger(__name__)

class TestOrchestratorAgent(LangGraphAgent):
    """Test-specific implementation of the OrchestratorAgent for testing."""

    def __init__(self,
                 user_profile_id: str,
                 db_service=None,
                 llm_client=None,
                 llm_config: Optional['LLMConfig'] = None):
        # Pass user_profile_id and agent_role to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="orchestrator"
        )
        # Store other parameters separately
        self.db_service = db_service
        self.llm_client = llm_client
        self.llm_config = llm_config
        self.agent_definition = None
        self.available_tools = []
        self.run_id = None # Initialize run_id

    async def process(self, state: BaseModel) -> Dict[str, Any]:
        """Orchestrate the workflow by determining the next agent."""
        try:
            # Extract context packet from state
            context_packet = getattr(state, "context_packet", {})
            if not context_packet:
                raise ValueError("Missing context packet in state")

            # Extract user_profile_id from context
            user_profile_id = context_packet.get("user_id")
            if not user_profile_id:
                raise ValueError("Missing user_id in context packet")

            # Store user_profile_id for tool calls
            self.user_profile_id = user_profile_id

            # Extract run_id if present
            self.run_id = context_packet.get("run_id", "mock-run-id")

            # Get current stage from state
            current_stage = self._get_current_stage_from_state(state)

            # Check for error in state
            self.error = getattr(state, "error", None)
            self.error_context = getattr(state, "error_context", None)

            # Extract data from state
            resource_context = getattr(state, "resource_context", None)
            engagement_analysis = getattr(state, "engagement_analysis", None)
            psychological_assessment = getattr(state, "psychological_assessment", None)
            strategy_framework = getattr(state, "strategy_framework", None)
            wheel = getattr(state, "wheel", None)
            ethical_validation = getattr(state, "ethical_validation", None)

            # Check for error in state
            if self.error:
                logger.debug(f"Orchestrator: Error detected: {self.error}. Routing to error_handler.")
                output_data = {
                    "next_agent": "error_handler",
                    "orchestration_status": "error_handling",
                    "error": self.error,
                    "error_context": self.error_context
                }
            # Determine next steps based on current stage
            elif current_stage == "orchestration_initial":
                # Initial orchestration - route to resource agent
                logger.debug("Orchestrator: Initial stage, routing to resource agent.")
                output_data = {
                    "next_agent": "resource",
                    "orchestration_status": "initial_routing_complete"
                }
            elif current_stage == "resource_assessment" and getattr(state, "last_agent", None) == "resource":
                # After resource agent - route to engagement agent
                logger.debug("Orchestrator: Resource assessment complete, routing to engagement agent.")
                output_data = {
                    "next_agent": "engagement",
                    "orchestration_status": "resource_to_engagement_routing_complete"
                }
            elif current_stage == "ethical_validation" and ethical_validation:
                # Final integration after ethical validation
                logger.debug("Orchestrator: Ethical validation complete, routing to mentor for final response.")
                output_data = {
                    "next_agent": "mentor",
                    "orchestration_status": "final_integration_complete",
                    "user_response": "I've prepared some activities for you based on your preferences and context."
                }
            else:
                # Determine appropriate next agent based on available data
                logger.debug("Orchestrator: Determining next agent based on available data.")
                next_agent = self._determine_next_agent(
                    resource_context,
                    engagement_analysis,
                    psychological_assessment,
                    strategy_framework,
                    wheel,
                    ethical_validation
                )
                logger.debug(f"Orchestrator: Routing to {next_agent}.")
                output_data = {
                    "next_agent": next_agent,
                    "orchestration_status": f"routing_to_{next_agent}"
                }

            return {"output_data": output_data}

        except Exception as e:
            logger.error(f"Exception caught in TestOrchestratorAgent process: {str(e)}", exc_info=True)
            error_message = f"Error in orchestrator agent: {str(e)}"
            error_output_data = {
                "error": error_message,
                "next_agent": "error_handler"  # Route to error handler on error
            }
            return {"error": error_message, "output_data": error_output_data}

    def _get_current_stage_from_state(self, state):
        """Extract the current stage from the state object."""
        # Try to get current_stage attribute
        current_stage = getattr(state, "current_stage", None)

        # If not found, try to get it from context_packet
        if not current_stage:
            context_packet = getattr(state, "context_packet", {})
            if isinstance(context_packet, dict):
                current_stage = context_packet.get("current_stage")

        # Default to initial orchestration if not found
        if not current_stage:
            current_stage = "orchestration_initial"

        return current_stage

    def _determine_next_agent(self, resource_context, engagement_analysis,
                             psychological_assessment, strategy_framework,
                             wheel, ethical_validation):
        """Determine the next agent in the sequence based on current data."""
        logger.debug(f"Determining next agent based on available data:")
        logger.debug(f"  resource_context: {bool(resource_context)}")
        logger.debug(f"  engagement_analysis: {bool(engagement_analysis)}")
        logger.debug(f"  psychological_assessment: {bool(psychological_assessment)}")
        logger.debug(f"  strategy_framework: {bool(strategy_framework)}")
        logger.debug(f"  wheel: {bool(wheel)}")
        logger.debug(f"  ethical_validation: {bool(ethical_validation)}")

        # Check if there's an error in the state
        if hasattr(self, 'error') and self.error:
            logger.debug(f"Error detected: {self.error}. Routing to error_handler agent.")
            return "error_handler"

        # Check for empty dictionaries as well as None
        if not resource_context or (isinstance(resource_context, dict) and not resource_context):
            logger.debug("Routing to resource agent (resource_context is empty or None)")
            return "resource"
        elif not engagement_analysis or (isinstance(engagement_analysis, dict) and not engagement_analysis):
            logger.debug("Routing to engagement agent (engagement_analysis is empty or None)")
            return "engagement"
        elif not psychological_assessment or (isinstance(psychological_assessment, dict) and not psychological_assessment):
            logger.debug("Routing to psychological agent (psychological_assessment is empty or None)")
            return "psychological"
        elif not strategy_framework or (isinstance(strategy_framework, dict) and not strategy_framework):
            logger.debug("Routing to strategy agent (strategy_framework is empty or None)")
            return "strategy"
        elif not wheel or (isinstance(wheel, dict) and not wheel):
            logger.debug("Routing to activity agent (wheel is empty or None)")
            return "activity"
        elif not ethical_validation or (isinstance(ethical_validation, dict) and not ethical_validation):
            logger.debug("Routing to ethical agent (ethical_validation is empty or None)")
            return "ethical"
        else:
            logger.debug("Routing to mentor agent (all data is present)")
            return "mentor"
