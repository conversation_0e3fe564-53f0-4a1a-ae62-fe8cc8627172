"""
Mock agent implementations for testing.

This module provides mock implementations of agents that don't import Django models
at module level, avoiding AppRegistryNotReady errors in tests.
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class MockErrorHandlerAgent:
    """
    Mock implementation of ErrorHandlerAgent for testing.

    This class mimics the interface of the real ErrorHandlerAgent but doesn't
    import Django models at module level, avoiding AppRegistryNotReady errors.
    """

    def __init__(self, user_profile_id: str, db_service=None, llm_client=None, llm_config=None):
        self.user_profile_id = user_profile_id
        self.agent_role = "error_handler"
        self.db_service = db_service
        self.llm_client = llm_client
        self.llm_config = llm_config
        self.agent_definition = None
        self.available_tools = []
        self.run_id = None
        self._stages = {}

    def start_stage(self, stage_name: str) -> None:
        """Start timing a stage."""
        self._stages[stage_name] = {'start': True}

    def stop_stage(self, stage_name: str) -> None:
        """Stop timing a stage."""
        if stage_name in self._stages:
            self._stages[stage_name]['stop'] = True

    async def process(self, state) -> Dict[str, Any]:
        """Handle errors and attempt recovery."""
        # Extract relevant data from state
        error = getattr(state, "error", "Unknown error")
        last_agent = getattr(state, "last_agent", "unknown")
        current_stage = getattr(state, "current_stage", "unknown")
        retry_count = getattr(state, "retry_count", 0)

        # Analyze the error
        error_analysis = await self._analyze_error(error, last_agent, current_stage, state)

        # Determine if recovery is possible
        can_recover = error_analysis.get("can_recover", False)
        recovery_path = error_analysis.get("recovery_path", None)

        if can_recover and recovery_path:
            # Create recovery plan
            recovery_plan = await self._create_recovery_plan(error_analysis, state)

            # Output recovery data and routing
            output_data = {
                "next_agent": recovery_path,
                "recovery_plan": recovery_plan,
                "error_handled": True,
                "user_message": "I'm working to address a technical issue. Let me try again."
            }

            return {
                "error": None,  # Clear the error
                "output_data": output_data
            }
        else:
            # Create graceful degradation response
            fallback_response = await self._create_fallback_response(error_analysis)

            # Output degradation response and end workflow
            output_data = {
                "next_agent": "end",
                "error_handled": False,
                "user_response": fallback_response,
                "recovery_plan": None,
                "original_error": error
            }

            return {
                "completed": True,
                "error": error,
                "output_data": output_data
            }

    async def _analyze_error(self, error, last_agent, current_stage, state):
        """Analyze the error to determine if recovery is possible."""
        # Check retry limit first (3 is the default max retries)
        retry_count = getattr(state, "retry_count", 0)
        if retry_count >= 3:
            return {
                "error_type": "persistent_retry_limit",
                "severity": "high",
                "can_recover": False,
                "recovery_path": None,
                "failed_agent": last_agent,
                "failed_stage": current_stage
            }

        # Check if error contains recoverable patterns
        recoverable_patterns = [
            "timeout", "temporary", "retry", "resource unavailable",
            "connection", "network", "throttled", "rate limit"
        ]
        can_recover = any(pattern in error.lower() for pattern in recoverable_patterns)

        # Map stages to agent nodes
        stage_to_agent = {
            "mentor_context_gathering": "mentor",
            "orchestration_initial": "orchestrator",
            "resource_assessment": "resource",
            "engagement_analysis": "engagement",
            "psychological_assessment": "psychological",
            "strategy_formulation": "strategy",
            "activity_selection": "activity",
            "ethical_validation": "ethical",
            "orchestration_final": "orchestrator",
            "mentor_presentation": "mentor"
        }

        # Determine recovery path
        recovery_path = None
        if can_recover:
            # Try to recover by returning to the agent that failed
            recovery_path = last_agent
        elif current_stage in stage_to_agent:
            # If we can't recover at the exact point, try to resume at the stage
            recovery_path = stage_to_agent[current_stage]

        return {
            "error_type": "transient" if can_recover else "persistent",
            "severity": "low" if can_recover else "high",
            "can_recover": can_recover,
            "recovery_path": recovery_path,
            "failed_agent": last_agent,
            "failed_stage": current_stage
        }

    async def _create_recovery_plan(self, error_analysis, state):
        """Create a plan for recovery."""
        recovery_path = error_analysis.get("recovery_path")
        failed_agent = error_analysis.get("failed_agent")

        return {
            "type": "agent_retry",
            "target_agent": recovery_path,
            "instructions": f"Retry processing from {recovery_path} agent",
            "state_modifications": {
                "skip_validation": True,
                "retry_count": getattr(state, "retry_count", 0) + 1,
                "previous_error": error_analysis.get("error_type")
            }
        }

    async def _create_fallback_response(self, error_analysis):
        """Create a graceful degradation response for the user."""
        # Check if this is a retry limit error
        if error_analysis.get("error_type") == "persistent_retry_limit":
            return "I've tried several times but I'm still having difficulty. Let's take a different approach."

        # In the mock, we return a simple fallback message for other errors
        return "I'm sorry, I wasn't able to complete your request at this time. Please try again later."
