from celery import shared_task
import logging
import asyncio
from typing import Dict, Any, Optional

from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow
from apps.main.graphs.onboarding_graph import run_onboarding_workflow
from apps.main.graphs.discussion_graph import run_discussion_workflow # Added import
from apps.user.models import UserProfile
from apps.main.models import HistoryEvent # Keep UserFeedback and ActivityTailored if needed by graphs
# Import UserFeedback and ActivityTailored if they are used indirectly by the workflows or history logging
# from apps.main.models import UserFeedback, ActivityTailored
from django.contrib.contenttypes.models import ContentType
from apps.main.services.event_service import EventService # Import EventService

logger = logging.getLogger(__name__)

@shared_task(bind=True, name="execute_graph_workflow")
def execute_graph_workflow(self, 
                           workflow_type: str, 
                           user_profile_id: str, 
                           initial_input: Dict[str, Any], 
                           workflow_id: Optional[str] = None):
    """
    Execute a complete workflow using LangGraph.
    
    Args:
        workflow_type: Type of workflow to execute (e.g., 'wheel_generation', 'onboarding')
        user_profile_id: The ID of the user profile
        initial_input: Initial context information
        workflow_id: Optional workflow ID (will generate a new one if not provided)
        
    Returns:
        dict: Workflow result
    """
    try:
        # Select the appropriate workflow based on type
        workflow_runners = {
            'wheel_generation': run_wheel_generation_workflow,
            'onboarding': run_onboarding_workflow,
            'discussion': run_discussion_workflow # Added discussion workflow
        }
        
        # Get the corresponding workflow runner
        workflow_runner = workflow_runners.get(workflow_type)
        if not workflow_runner:
            raise ValueError(f"Unknown workflow type: {workflow_type}")
        
        # Run the workflow (using asyncio to handle async methods)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                workflow_runner(
                    user_profile_id, 
                    initial_input, 
                    workflow_id
                )
            )
        finally:
            loop.close()
        
        # Record workflow completion in history
        try:
            # Get user profile content type
            user_profile = UserProfile.objects.get(id=user_profile_id)
            content_type = ContentType.objects.get_for_model(UserProfile)
            
            # Create history event
            HistoryEvent.objects.create(
                event_type=f'{workflow_type}_workflow',
                content_type=content_type,
                object_id=user_profile_id,
                user_profile=user_profile,
                details={
                    'workflow_id': workflow_id or result.get('workflow_id', str(self.request.id)),
                    'status': 'completed' if not result.get('error', None) else 'failed',
                    'workflow_type': workflow_type,
                    'workflow_summary': result.get('summary', {})
                }
            )
        except Exception as e:
            logger.error(f"Error recording workflow history: {str(e)}")
            # Non-critical error, don't re-raise

        # --- Send the final response back to the user ---
        try:
            # Attempt to extract the user-facing response from the result
            # The exact structure might vary depending on the workflow/agent output schema
            final_response_content = None
            if isinstance(result, dict):
                output_data = result.get('output_data', {})
                if isinstance(output_data, dict):
                    final_response_content = output_data.get('user_response') # Common key for chat responses

            if final_response_content:
                logger.info(f"Workflow {workflow_type} completed. Emitting final chat message for user {user_profile_id}.")
                EventService.emit_event_sync(
                    event_type='chat_message',
                    data={
                        'content': final_response_content,
                        'is_user': False # Message is from the agent
                    },
                    user_profile_id=user_profile_id
                    # session_id is not available here, rely on user_profile_id targeting
                )
            else:
                logger.warning(f"Workflow {workflow_type} completed but no 'user_response' found in result['output_data'] for user {user_profile_id}. Result keys: {result.keys() if isinstance(result, dict) else 'N/A'}")

        except Exception as e:
            logger.error(f"Error emitting final chat message after workflow {workflow_type} completion: {str(e)}", exc_info=True)
            # Also emit a debug event for this failure
            EventService.emit_event_sync(
                event_type='debug_info',
                data={
                    'level': 'error',
                    'message': f"Failed to emit final chat message after {workflow_type} workflow.",
                    'source': f"CeleryTask:{workflow_type}:ResultHandling",
                    'details': {'error': str(e)}
                },
                user_profile_id=user_profile_id
            )
        # --- End sending final response ---


        # Return result, converting to dict if needed
        return result.dict() if hasattr(result, 'dict') else result

    except Exception as e:
        import traceback
        # EventService is already imported at the top level now

        error_message = str(e)
        tb_str = traceback.format_exc()
        logger.error(f"Error executing {workflow_type} workflow: {error_message}", exc_info=True)

        # Emit debug info event with traceback
        # Use EventService.emit_event_sync as we are in a sync Celery task context
        EventService.emit_event_sync(
            event_type='debug_info',
            data={
                'level': 'error',
                'message': f"Celery task failed during {workflow_type} workflow execution.",
                'source': f"CeleryTask:{workflow_type}",
                'details': {
                    'error_message': error_message,
                    'exception_type': type(e).__name__,
                    'traceback': tb_str
                }
            },
            user_profile_id=user_profile_id
            # Assuming session_id isn't readily available here, rely on user_profile_id targeting
        )

        # Return error dictionary as before
        return {
            "error": error_message,
            "workflow_type": workflow_type,
            "workflow_id": workflow_id,
            "completed": False,
            "user_profile_id": user_profile_id
        }
