# backend/apps/main/tasks/onboarding_tasks.py

from celery import shared_task
import logging
import asyncio
import json

from apps.main.graphs.onboarding_graph import run_onboarding_workflow
from apps.user.models import UserProfile
from apps.main.models import HistoryEvent
from django.contrib.contenttypes.models import ContentType

logger = logging.getLogger(__name__)

# Removed redundant execute_onboarding_workflow task.
# The generic execute_graph_workflow task in agent_tasks.py handles
# launching the onboarding workflow when workflow_type='onboarding'.
