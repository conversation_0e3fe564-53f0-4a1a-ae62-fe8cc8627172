"""
Benchmark Tasks

This module defines Celery tasks for running benchmarks, including both agent benchmarks
and workflow benchmarks.
"""

import logging
import uuid
from typing import Dict, Any, Optional

from celery import shared_task
from asgiref.sync import async_to_sync

from apps.main.models import BenchmarkScenario
from apps.main.services.benchmark_manager import AgentBenchmarker

# Import workflow benchmark managers
from apps.main.services.async_workflow_manager import WorkflowBenchmarker
from apps.main.services.wheel_workflow_benchmark_manager import WheelWorkflowBenchmarkManager

logger = logging.getLogger(__name__)

@shared_task(bind=True, name='apps.main.tasks.benchmark_tasks.run_all_benchmarks_task')
def run_all_benchmarks_task(self, params):
    """
    Celery task to run all active benchmark scenarios with given parameters.
    """
    logger.info(f"Starting run_all_benchmarks_task with params: {params}")
    manager = AgentBenchmarker()
    total_scenarios = 0
    completed_count = 0
    failed_count = 0
    failed_scenarios = []

    try:
        # Fetch active scenarios synchronously within the task
        # Use sync_to_async if BenchmarkScenario.objects.filter needs it,
        # but standard Django ORM calls are often sync-safe in Celery tasks.
        # Let's assume it's safe for now. If issues arise, wrap in sync_to_async.
        active_scenarios = BenchmarkScenario.objects.filter(is_active=True).order_by('name')
        total_scenarios = active_scenarios.count()
        logger.info(f"Found {total_scenarios} active scenarios to benchmark.")

        for i, scenario in enumerate(active_scenarios):
            scenario_id = scenario.id
            scenario_name = scenario.name
            logger.info(f"Running benchmark for scenario {i+1}/{total_scenarios}: '{scenario_name}' (ID: {scenario_id})")

            # Update task state for progress tracking (optional but good practice)
            self.update_state(state='PROGRESS', meta={
                'current': i + 1,
                'total': total_scenarios,
                'status': f"Running scenario: {scenario_name}"
            })

            try:
                # Run the benchmark for the individual scenario using the manager
                # The manager's run_benchmark method is async, so we need async_to_sync
                benchmark_run = async_to_sync(manager.run_benchmark)(scenario_id, params)
                logger.info(f"Completed benchmark for scenario '{scenario_name}'. Run ID: {benchmark_run.id}")
                completed_count += 1
            except Exception as e:
                logger.error(f"Error running benchmark for scenario '{scenario_name}' (ID: {scenario_id}): {e}", exc_info=True)
                failed_count += 1
                failed_scenarios.append({'id': scenario_id, 'name': scenario_name, 'error': str(e)})
                # Decide whether to continue or stop on error. Let's continue for now.

        # Task completed
        result_message = f"Finished running all benchmarks. Total: {total_scenarios}, Completed: {completed_count}, Failed: {failed_count}."
        logger.info(result_message)
        if failed_scenarios:
             logger.warning(f"Failed scenarios: {failed_scenarios}")

        return {
            'status': 'SUCCESS',
            'total_scenarios': total_scenarios,
            'completed_count': completed_count,
            'failed_count': failed_count,
            'failed_scenarios': failed_scenarios,
            'message': result_message
        }

    except Exception as e:
        logger.error(f"Critical error during run_all_benchmarks_task: {e}", exc_info=True)
        # Update state to FAILURE
        self.update_state(state='FAILURE', meta={
            'exc_type': type(e).__name__,
            'exc_message': str(e),
            'status': 'Task failed critically.'
        })
        # Reraise the exception so Celery knows it failed critically
        raise

@shared_task(bind=True, name='apps.main.tasks.benchmark_tasks.run_workflow_benchmark')
def run_workflow_benchmark(self, benchmark_id: str, params: Dict[str, Any] = None, user_profile_id: Optional[str] = None):
    """
    Run a workflow benchmark.

    Args:
        benchmark_id: ID of the benchmark scenario
        params: Optional parameters for the benchmark
        user_profile_id: Optional ID of the user initiating the benchmark

    Returns:
        dict: Benchmark results
    """
    logger.info(f"Starting workflow benchmark task for scenario ID: {benchmark_id}")

    try:
        # Convert string ID to UUID
        scenario_id = uuid.UUID(benchmark_id)

        # Get the scenario to determine workflow type
        scenario = BenchmarkScenario.objects.get(id=scenario_id)
        workflow_type = scenario.metadata.get('workflow_type')

        if not workflow_type:
            raise ValueError(f"Scenario {scenario.name} is not configured for workflow benchmarking (missing workflow_type)")

        # Select the appropriate workflow benchmark manager
        if workflow_type == 'wheel_generation':
            benchmark_manager = WheelWorkflowBenchmarkManager()
        else:
            raise ValueError(f"Unsupported workflow type: {workflow_type}")

        # Run the benchmark with progress tracking
        benchmark_run = async_to_sync(benchmark_manager.execute_benchmark)(
            scenario_id=scenario_id,
            params=params,
            progress_callback=self.update_state,
            user_profile_id=user_profile_id
        )

        # Return the result
        return {
            'benchmark_run_id': str(benchmark_run.id),
            'scenario_name': benchmark_run.scenario.name,
            'workflow_type': workflow_type,
            'mean_duration': benchmark_run.mean_duration,
            'success_rate': benchmark_run.success_rate,
            'status': 'completed'
        }
    except Exception as e:
        logger.error(f"Error running workflow benchmark: {str(e)}", exc_info=True)
        # Update state to FAILURE
        self.update_state(state='FAILURE', meta={
            'exc_type': type(e).__name__,
            'exc_message': str(e),
            'status': 'Task failed.'
        })
        return {
            'benchmark_id': benchmark_id,
            'status': 'failed',
            'error': str(e)
        }

@shared_task(bind=True, name='apps.main.tasks.benchmark_tasks.run_all_workflow_benchmarks')
def run_all_workflow_benchmarks(self, params: Dict[str, Any] = None, workflow_type: Optional[str] = None):
    """
    Run all active workflow benchmark scenarios.

    Args:
        params: Optional parameters for the benchmarks
        workflow_type: Optional workflow type to filter scenarios

    Returns:
        dict: Summary of benchmark results
    """
    logger.info(f"Starting run_all_workflow_benchmarks task with params: {params}, workflow_type: {workflow_type}")

    total_scenarios = 0
    completed_count = 0
    failed_count = 0
    failed_scenarios = []

    try:
        # Build the filter for active scenarios
        filter_kwargs = {'is_active': True}

        # Add metadata__workflow_type filter if specified
        if workflow_type:
            filter_kwargs['metadata__workflow_type'] = workflow_type

        # Fetch active workflow benchmark scenarios
        active_scenarios = BenchmarkScenario.objects.filter(**filter_kwargs).order_by('name')
        total_scenarios = active_scenarios.count()
        logger.info(f"Found {total_scenarios} active workflow benchmark scenarios.")

        for i, scenario in enumerate(active_scenarios):
            scenario_id = scenario.id
            scenario_name = scenario.name
            scenario_workflow_type = scenario.metadata.get('workflow_type')

            logger.info(f"Running workflow benchmark {i+1}/{total_scenarios}: '{scenario_name}' (ID: {scenario_id}, Type: {scenario_workflow_type})")

            # Update task state for progress tracking
            self.update_state(state='PROGRESS', meta={
                'current': i + 1,
                'total': total_scenarios,
                'status': f"Running workflow benchmark: {scenario_name}"
            })

            try:
                # Run the workflow benchmark for this scenario
                # We use the run_workflow_benchmark task directly
                result = run_workflow_benchmark.apply_async(
                    args=[str(scenario_id)],
                    kwargs={'params': params}
                ).get(timeout=1800)  # 30-minute timeout

                if result.get('status') == 'completed':
                    logger.info(f"Completed workflow benchmark for scenario '{scenario_name}'. Run ID: {result.get('benchmark_run_id')}")
                    completed_count += 1
                else:
                    logger.error(f"Workflow benchmark failed for scenario '{scenario_name}': {result.get('error')}")
                    failed_count += 1
                    failed_scenarios.append({
                        'id': str(scenario_id),
                        'name': scenario_name,
                        'error': result.get('error')
                    })
            except Exception as e:
                logger.error(f"Error running workflow benchmark for scenario '{scenario_name}' (ID: {scenario_id}): {e}", exc_info=True)
                failed_count += 1
                failed_scenarios.append({
                    'id': str(scenario_id),
                    'name': scenario_name,
                    'error': str(e)
                })

        # Task completed
        result_message = f"Finished running all workflow benchmarks. Total: {total_scenarios}, Completed: {completed_count}, Failed: {failed_count}."
        logger.info(result_message)
        if failed_scenarios:
            logger.warning(f"Failed scenarios: {failed_scenarios}")

        return {
            'status': 'SUCCESS',
            'total_scenarios': total_scenarios,
            'completed_count': completed_count,
            'failed_count': failed_count,
            'failed_scenarios': failed_scenarios,
            'message': result_message
        }
    except Exception as e:
        logger.error(f"Critical error during run_all_workflow_benchmarks task: {e}", exc_info=True)
        # Update state to FAILURE
        self.update_state(state='FAILURE', meta={
            'exc_type': type(e).__name__,
            'exc_message': str(e),
            'status': 'Task failed critically.'
        })
        # Reraise the exception so Celery knows it failed critically
        raise


@shared_task(bind=True, name='apps.main.tasks.benchmark_tasks.run_template_test')
def run_template_test(self, scenario_id: str, template_data: Dict[str, Any], params: Dict[str, Any] = None):
    """
    Run a template test as a Celery task.

    Args:
        scenario_id: ID of the benchmark scenario
        template_data: Template data for evaluation
        params: Test parameters including selected combinations

    Returns:
        dict: Test results with runs data
    """
    logger.info(f"Starting template test task for scenario {scenario_id}")

    try:
        from django.shortcuts import get_object_or_404
        from apps.main.models import BenchmarkScenario
        from apps.main.services.benchmark_manager import AgentBenchmarker
        from asgiref.sync import async_to_sync
        import uuid

        # Get the scenario
        scenario = get_object_or_404(BenchmarkScenario, id=scenario_id)

        # Initialize benchmark manager
        manager = AgentBenchmarker()

        # Extract parameters
        runs = min(params.get('runs', 1), 5)  # Limit to 5 runs for template testing
        semantic_evaluation = params.get('semantic_evaluation', True)
        context_variables = params.get('context_variables', {})
        multi_range_evaluation = params.get('multi_range_contextual_evaluation', False)
        selected_combinations = params.get('selected_combinations', [])
        selected_combination_indices = params.get('selected_combination_indices', [])

        test_runs = []

        # Update task state
        self.update_state(state='PROGRESS', meta={
            'current': 0,
            'total': len(selected_combinations) if multi_range_evaluation and selected_combinations else runs,
            'status': 'Starting template test...'
        })

        if multi_range_evaluation and selected_combinations:
            # Multi-range evaluation with selected combinations
            logger.info(f"Running multi-range evaluation with {len(selected_combinations)} selected combinations")

            for i, context_combo in enumerate(selected_combinations):
                # Update progress
                self.update_state(state='PROGRESS', meta={
                    'current': i + 1,
                    'total': len(selected_combinations),
                    'status': f'Testing combination {i + 1}/{len(selected_combinations)}...'
                })

                try:
                    # Create benchmark parameters with specific context combination
                    benchmark_params = {
                        'runs': 1,  # Single run per context combination
                        'warmup_runs': 0,  # No warmup for template testing
                        'semantic_evaluation': semantic_evaluation,
                        'evaluation_template_data': template_data,
                        'context_variables': context_combo,  # Use specific context combination
                        'multi_range_contextual_evaluation': True,
                        'selected_combinations': [context_combo]  # Pass only this combination
                    }

                    # Run the benchmark with the template
                    run = async_to_sync(manager.run_benchmark)(
                        scenario_id=scenario.id,
                        params=benchmark_params,
                        user_profile_id=None
                    )

                    # Use the new property for robust token usage display
                    token_usage_str = run.token_usage_display

                    # Get range info for this context combination
                    range_info = context_combo.get('range_info', {})
                    range_key = next(iter(range_info.values())) if range_info else f'combination_{i+1}'

                    test_runs.append({
                        'id': str(run.id),
                        'success': run.success_rate >= 0.5,  # Consider successful if success rate >= 50%
                        'semantic_score': run.semantic_score or 0.0,
                        'execution_time': (run.mean_duration or 0.0) / 1000.0,  # Convert ms to seconds
                        'token_usage': token_usage_str,
                        'cost': float(run.estimated_cost or 0.0),
                        'error': None,  # BenchmarkRun doesn't store individual run errors
                        'context_combination': context_combo,  # Include context info for debugging
                        'range_key': range_key  # Include range identifier
                    })

                except Exception as e:
                    logger.error(f"Error running benchmark for context combination {i+1}: {e}")
                    test_runs.append({
                        'id': str(uuid.uuid4()),
                        'success': False,
                        'semantic_score': 0.0,
                        'execution_time': 0.0,
                        'token_usage': "0",
                        'cost': 0.0,
                        'error': str(e),
                        'context_combination': context_combo if 'context_combo' in locals() else {},
                        'range_key': f'combination_{i+1}_error'
                    })
        else:
            # Standard evaluation: run the benchmark multiple times with the same context
            logger.info(f"Running standard evaluation with {runs} runs")

            for i in range(runs):
                # Update progress
                self.update_state(state='PROGRESS', meta={
                    'current': i + 1,
                    'total': runs,
                    'status': f'Running test {i + 1}/{runs}...'
                })

                try:
                    # Create benchmark parameters with template data
                    benchmark_params = {
                        'runs': 1,  # Single run per iteration
                        'warmup_runs': 0,  # No warmup for template testing
                        'semantic_evaluation': semantic_evaluation,
                        'evaluation_template_data': template_data,
                        'context_variables': context_variables,
                        'multi_range_contextual_evaluation': False  # Explicitly set to False for standard evaluation
                    }

                    # Run the benchmark with the template
                    run = async_to_sync(manager.run_benchmark)(
                        scenario_id=scenario.id,
                        params=benchmark_params,
                        user_profile_id=None
                    )

                    # Use the new property for robust token usage display
                    token_usage_str = run.token_usage_display

                    test_runs.append({
                        'id': str(run.id),
                        'success': run.success_rate >= 0.5,  # Consider successful if success rate >= 50%
                        'semantic_score': run.semantic_score or 0.0,
                        'execution_time': (run.mean_duration or 0.0) / 1000.0,  # Convert ms to seconds
                        'token_usage': token_usage_str,
                        'cost': float(run.estimated_cost or 0.0),
                        'error': None,  # BenchmarkRun doesn't store individual run errors
                        'run_number': i + 1  # Include run number for standard evaluation
                    })

                except Exception as e:
                    logger.error(f"Error running benchmark run {i+1}: {e}")
                    test_runs.append({
                        'id': str(uuid.uuid4()),
                        'success': False,
                        'semantic_score': 0.0,
                        'execution_time': 0.0,
                        'token_usage': "0",
                        'cost': 0.0,
                        'error': str(e),
                        'run_number': i + 1
                    })

        # Task completed successfully
        self.update_state(state='SUCCESS', meta={
            'current': len(test_runs),
            'total': len(test_runs),
            'status': 'Template test completed successfully'
        })

        return {
            'success': True,
            'runs': test_runs,
            'template_name': template_data.get('name', 'Unnamed Template'),
            'scenario_name': scenario.name,
            'message': f"Template test completed with {len(test_runs)} runs."
        }

    except Exception as e:
        logger.error(f"Error in template test task: {e}", exc_info=True)

        # Update task state to FAILURE
        self.update_state(state='FAILURE', meta={
            'exc_type': type(e).__name__,
            'exc_message': str(e),
            'status': 'Template test failed'
        })

        # Return error result
        return {
            'success': False,
            'error': str(e),
            'message': f"Template test failed: {str(e)}"
        }