# backend/apps/main/tasks/wheel_generation_tasks.py

from celery import shared_task
import logging
import asyncio
import json
from typing import Dict, Any, Optional

from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow
from apps.user.models import UserProfile
from apps.main.models import HistoryEvent
from django.contrib.contenttypes.models import ContentType

logger = logging.getLogger(__name__)

@shared_task(bind=True, name="execute_wheel_generation_workflow")
def execute_wheel_generation_workflow(self, 
                                     user_profile_id: str, 
                                     context_packet: Dict[str, Any],
                                     workflow_id: Optional[str] = None):
    """
    Execute the complete wheel generation workflow.
    
    This task orchestrates the multi-agent wheel generation process, which:
    1. Starts with mentor agent gathering context
    2. Routes through multiple specialized agents
    3. Creates a personalized activity wheel
    4. Provides that wheel back to the user
    
    Args:
        user_profile_id: The ID of the user profile
        context_packet: Initial context information from user/system
        workflow_id: Optional workflow ID (will generate new one if not provided)
        
    Returns:
        dict: Wheel generation results including the wheel and agent outputs
    """
    try:
        logger.info(f"Starting wheel generation workflow for user {user_profile_id}")
        
        # Ensure context packet has workflow metadata
        if not context_packet.get("task_type"):
            context_packet["task_type"] = "wheel_generation"
            
        # Store the Celery task ID for tracking
        context_packet["celery_task_id"] = self.request.id
        
        # Run the workflow (using asyncio to handle async methods)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(run_wheel_generation_workflow(...))
        finally:
            loop.close()
        
        # Record workflow completion in history
        try:
            # Get user profile content type
            user_profile = UserProfile.objects.get(id=user_profile_id)
            content_type = ContentType.objects.get_for_model(UserProfile)
            
            # Extract the wheel from the result
            wheel_data = None
            if hasattr(result, 'wheel') and result.wheel:
                wheel_data = result.wheel
            elif isinstance(result, dict) and 'wheel' in result:
                wheel_data = result['wheel']
                
            # Create history event
            HistoryEvent.objects.create(
                event_type='wheel_generated',
                content_type=content_type,
                object_id=user_profile_id,
                user_profile=user_profile,
                details={
                    'workflow_id': workflow_id or result.workflow_id if hasattr(result, 'workflow_id') else str(self.request.id),
                    'status': 'completed' if not result.get('error', None) else 'failed',
                    'wheel_summary': {
                        'name': wheel_data.get('name', 'Activity Wheel') if wheel_data else None,
                        'item_count': len(wheel_data.get('items', [])) if wheel_data else 0
                    } if wheel_data else None,
                    'agent_flow': 'wheel_generation_graph'
                }
            )
        except Exception as e:
            logger.error(f"Error recording wheel generation history: {str(e)}")
            # Non-critical error, don't re-raise
            
        # Return result, converting to dict if needed
        if hasattr(result, 'dict'):
            return result.dict()
        elif isinstance(result, dict):
            return result
        else:
            return {"error": "Unknown result format", "completed": False}
            
    except Exception as e:
        logger.error(f"Error executing wheel generation workflow: {str(e)}", exc_info=True)
        return {
            "error": str(e),
            "workflow_id": workflow_id,
            "completed": False,
            "user_profile_id": user_profile_id,
            "task_type": "wheel_generation"
        }
        context_packet["celery_task_id"] = self.request.id