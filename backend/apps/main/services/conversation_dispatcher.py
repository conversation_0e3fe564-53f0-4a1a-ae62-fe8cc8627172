import asyncio
import uuid
import datetime
import logging
from typing import Dict, Any, Optional
import json
from channels.db import database_sync_to_async # Import the decorator
from channels.layers import get_channel_layer # Import channel layer
from datetime import datetime, timezone # Ensure timezone is imported

# Import tool utilities
from apps.main.agents.tools.tools_util import execute_tool

# Set up logger for the dispatcher
logger = logging.getLogger(__name__)

class ConversationDispatcher:
    """
    Top-level dispatcher that receives initial user messages,
    determines context, and routes to appropriate workflow graphs.

    This implementation directly uses tools instead of relying on
    the DispatcherAgentNode, simplifying the architecture.
    """

    def __init__(self, user_profile_id: str, user_ws_session_name: Optional[str] = None):
        """
        Initialize the conversation dispatcher.

        Args:
            user_profile_id: The ID of the user profile
            user_ws_session_name: Optional WebSocket session name for result routing
        """
        self.user_profile_id = user_profile_id
        self.user_ws_session_name = user_ws_session_name

        # Initialize LLM client for classification
        try:
            from apps.main.llm.service import RealLLMClient
            self.llm_client = RealLLMClient() # Attempt initialization
        except Exception as e:
            # --- BEGIN CHANGE ---
            # Log the warning AND send debug info if LLM client fails to initialize
            logger.warning(f"Failed to initialize LLM client: {str(e)}. Falling back to rule-based classification.")
            import traceback
            tb_str = traceback.format_exc()
            # We need to use asyncio.create_task because __init__ is synchronous,
            # but _send_debug_info is async. We can't await here.
            # This sends the debug message without blocking initialization.
            asyncio.create_task(self._send_debug_info(
                'error',
                f"LLM Client Initialization Failed: {str(e)}",
                {'exception_type': type(e).__name__, 'traceback': tb_str}
            ))
            self.llm_client = None # Set to None to allow fallback
            # --- END CHANGE ---

            logger.info(f"ConversationDispatcher initialized for user {user_profile_id} (LLM fallback active)")

    async def _send_debug_info(self, level: str, message: str, details: Optional[Dict[str, Any]] = None):
        """Helper to send debug info to the user's session group if available."""
        from apps.main.services.event_service import EventService

        await EventService.emit_debug_info(
            level=level,
            message=message,
            source='ConversationDispatcher',
            details=details,
            user_profile_id=self.user_profile_id,
            session_id=self.user_ws_session_name
        )

    async def process_message(self, user_message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process initial user message, extract context, and route to appropriate workflow.

        Args:
            user_message: The user's message content and metadata

        Returns:
            dict: Response data including workflow selection and context
        """
        logger.info(f"Processing message for user {self.user_profile_id}: {user_message.get('text', '')[:50]}...")

        try:
            # Generate a workflow ID for this conversation
            workflow_id = str(uuid.uuid4())

            # Step 1: Check profile completion status to determine if onboarding is needed
            profile_status = await self._check_profile_completion()

            # Step 2: Extract context from user message
            context_extraction = await self._extract_message_context(user_message)
            await self._send_debug_info('debug', 'Context extracted', {'context': context_extraction})

            # Step 3: Classify the message to determine appropriate workflow
            workflow_classification = await self._classify_message(
                user_message,
                context_extraction,
                profile_status
            )
            await self._send_debug_info('debug', 'Message classified', {'classification': workflow_classification})

            # Step 4: Build a standardized context packet
            context_packet = self._build_context_packet(context_extraction)

            # Step 5: Check if additional information is needed
            action_required_info = self._check_if_action_required(
                workflow_classification["workflow_type"],
                context_extraction
            )
            await self._send_debug_info('debug', 'Checked if action required', {'action_required': action_required_info or 'None'})

            # --- START CHANGE ---
            # Determine the actual workflow to launch
            workflow_to_launch = workflow_classification["workflow_type"]
            if action_required_info:
                # If action is required, override the launch target to 'discussion'
                workflow_to_launch = 'discussion'
                # Add target workflow and missing field to the context for the discussion flow
                context_packet['target_workflow'] = workflow_classification["workflow_type"]
                context_packet['missing_field'] = action_required_info['missing_field']
                # Removed 'collection_goal' for standardization, the discussion flow can use the missing_field context.
                await self._send_debug_info('info', f"Action required, overriding workflow to 'discussion'. Target: {workflow_classification['workflow_type']}", {'missing_field': action_required_info['missing_field']})
            # --- END CHANGE ---

            # Step 6: Launch the workflow (using the potentially overridden type)
            # Send the full context packet in debug details, not just keys
            await self._send_debug_info('info', f"Launching workflow: {workflow_to_launch}", {'workflow_id': workflow_id, 'context_packet': context_packet})
            self._launch_workflow(
                workflow_to_launch, # Use the potentially modified workflow type
                context_packet,
                workflow_id
            )

            # Prepare response with all the information
            response = {
                'workflow_id': workflow_id,
                # Report the *intended* workflow type, even if discussion is launched for info gathering
                'workflow_type': workflow_classification["workflow_type"],
                'confidence': workflow_classification["confidence"],
                'context_packet': context_packet, # Context now includes target/missing info if needed
                'status': 'initiated',
                'session_timestamp': context_packet.get('session_timestamp'),
                'estimated_completion_time': self._estimate_completion_time(workflow_to_launch) # Estimate based on actual launched workflow
            }

            # Add action required info if it exists (for the consumer/frontend awareness)
            if action_required_info:
                response['action_required'] = action_required_info

            # Step 7: Record workflow initiation (using the actual launched type)
            await self._record_workflow_initiation(
                workflow_to_launch, # Record the actual workflow launched
                workflow_id,
                context_packet
            )

            return response

        except Exception as e:
            logger.error(f"Error in dispatcher: {str(e)}", exc_info=True)
            # Return error response
            return {
                "status": "error",
                "error": str(e),
                "fallback_response": "I'm having trouble processing your request right now. Please try again."
            }
            # Send detailed debug info on error, including traceback
            import traceback
            tb_str = traceback.format_exc()
            await self._send_debug_info(
                'error',
                f"Dispatcher error: {str(e)}",
                {'exception_type': type(e).__name__, 'traceback': tb_str}
            )
            # Return the error dictionary instead of re-raising,
            # allowing the consumer to handle it gracefully.
            return {
                "status": "error",
                "error": str(e),
                "fallback_response": "An error occurred while processing your request. Details logged."
            }

    async def _check_profile_completion(self) -> float:
        """
        Check the user's profile completion status using the dedicated tool.

        Returns:
            float: Completion percentage (0.0 to 1.0)
        """
        try:
            # Use the get_user_profile tool and extract completion info
            # --- BEGIN FIX: Wrap arguments in 'input_data' ---
            result = await execute_tool(
                tool_code="get_user_profile",
                tool_input={"input_data": {"user_profile_id": self.user_profile_id}}, # Correct structure
                user_profile_id=self.user_profile_id, # Pass for event targeting
                session_id=self.user_ws_session_name # Pass for event targeting
            )
            # --- END FIX ---
            # Extract completion percentage from the nested structure
            user_profile_data = result.get("user_profile", {})
            return user_profile_data.get("profile_completion", 0.5) # Default if not found
        except Exception as e:
            logger.warning(f"Error getting user profile for completion check: {str(e)}")
            # Default to medium completion if the tool fails
            return 0.5

    async def _extract_message_context(self, user_message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract context from the user's message using the extract_context tool.

        Args:
            user_message: The user's message

        Returns:
            dict: Extracted context elements
        """
        try:
            # Use the extract_context tool with the message text
            # Ensure user_profile_id is passed as it's required by the tool signature
            result = await execute_tool(
                tool_code="extract_message_context",
                tool_input={
                    "message": user_message.get("text", ""), # Use 'message' key
                    "user_profile_id": self.user_profile_id # Pass user_profile_id
                    # Removed metadata as the tool doesn't accept it
                },
                user_profile_id=self.user_profile_id, # Pass for event targeting
                session_id=self.user_ws_session_name # Pass for event targeting
            )

            # Get recent user state to enrich context if available
            try:
                user_state = await execute_tool(
                    tool_code="get_user_state",
                    tool_input={"user_profile_id": self.user_profile_id},
                    user_profile_id=self.user_profile_id, # Pass for event targeting
                    session_id=self.user_ws_session_name # Pass for event targeting
                )

                # Merge user state with extracted context, prioritizing explicit context
                merged_context = {**user_state.get("current_state", {}), **result}
                return merged_context
            except:
                # Continue with just the extracted context if user state isn't available
                return result
        except Exception as e:
            logger.warning(f"Error extracting context: {str(e)}")
            # Return minimal context in case of failure, including enhanced context fields (schema v2.0.0)
            return {
                "mood": "",
                "environment": "",
                "time_availability": "",
                "focus": "",
                "extraction_confidence": 0.0,  # Indicate failure with 0 confidence

                # Enhanced context fields (schema v2.0.0)
                "user_state": {
                    "trust_level": 50,
                    "mood": "",
                    "environment": ""
                },
                "device_capabilities": {
                    "screen_size": "medium",
                    "input_method": "mixed",
                    "accessibility": {
                        "vision_impaired": False,
                        "hearing_impaired": False,
                        "motor_impaired": False,
                        "preferred_modality": "visual"
                    }
                },
                "time_context": {
                    "available_time": 0,
                    "time_of_day": "afternoon",
                    "time_zone": "UTC"
                },
                "user_preferences": {
                    "learning_style": "visual",
                    "communication_preferences": {
                        "verbosity": "moderate",
                        "formality": "neutral",
                        "feedback_frequency": "moderate"
                    }
                }
            }
            # Note: The following lines were removed as they caused syntax errors:
            # user_message: The user's message
            # context: Extracted context elements
            # profile_status: Profile completion status

    async def _classify_message(
        self,
        user_message: Dict[str, Any],
        context: Dict[str, Any],
        profile_status: float
    ) -> Dict[str, Any]:
        """
        Classify the user's message to determine the appropriate workflow.

        Args:
            user_message: The user's message
            context: Extracted context elements
            profile_status: Profile completion status

        Returns:
            dict: Classification result with workflow type and confidence
        """
        message_text = user_message.get("text", "")
        metadata = user_message.get("metadata", {})

        # Check metadata for explicit workflow requests (highest priority)
        if "requested_workflow" in metadata:
            return {
                "workflow_type": metadata["requested_workflow"],
                "confidence": 1.0,
                "reason": "Explicitly requested in metadata"
            }

        # Check for spin result metadata (high priority)
        if metadata.get("type") == "spin_result":
            return {
                "workflow_type": "post_spin",
                "confidence": 1.0,
                "reason": "Processing spin result",
                "activity_id": metadata.get("activity_id"),
                "activity_name": metadata.get("activity_name")
            }

        # Check profile completion status for new users (high priority)
        if profile_status < 0.5:
            return {
                "workflow_type": "user_onboarding",
                "confidence": 0.95,
                "reason": "Incomplete user profile"
            }

        # Fetch recent conversation history before attempting LLM classification
        history = []
        try:
            history_result = await execute_tool(
                tool_code="get_conversation_history",
                tool_input={"user_profile_id": self.user_profile_id, "limit": 5}, # Fetch last 5 messages
                user_profile_id=self.user_profile_id, # Pass for event targeting
                session_id=self.user_ws_session_name # Pass for event targeting
            )
            if isinstance(history_result, list):
                history = history_result
            elif isinstance(history_result, dict) and 'history' in history_result and isinstance(history_result['history'], list):
                 history = history_result['history']
            else:
                 logger.warning(f"Unexpected format for conversation history: {type(history_result)}")
        except Exception as e:
            logger.warning(f"Failed to fetch conversation history: {str(e)}")

        # Use LLM for classification if available, now including history
        if self.llm_client:
            try:
                llm_classification = await self._classify_with_llm(message_text, context, history)
                if llm_classification:
                    return llm_classification
            except Exception as e:
                logger.warning(f"LLM classification failed: {str(e)}. Falling back to rules.")

        # Fallback to rule-based classification (or if LLM fails)
        try:
            # Use the classify_message_intent tool (corrected name)
            result = await execute_tool(
                tool_code="classify_message_intent", # Corrected tool code
                tool_input={
                    "message": message_text, # Use 'message' key
                    "context": context, # Use 'context' key
                    "profile_completion": profile_status # Use 'profile_completion' key
                },
                user_profile_id=self.user_profile_id, # Pass for event targeting
                session_id=self.user_ws_session_name # Pass for event targeting
            )

            # The tool returns a dict with a 'classification' key
            classification_result = result.get("classification", {})

            # Apply business rules to refine the classification
            return self._apply_classification_rules(
                message_text,
                context,
                classification_result.get("workflow_type", "wheel_generation"),
                classification_result.get("confidence", 0.7)
            )

        except Exception as e:
            logger.warning(f"Error in tool-based workflow classification: {str(e)}")
            # Apply rules to the text directly as fallback
            # Apply business rules to refine the classification
            return self._apply_classification_rules(
                message_text,
                context,
                "wheel_generation",  # Default to wheel generation
                0  # Lower confidence for fallback
            )

    async def _classify_with_llm(self, message: str, context: Dict[str, Any], history: list) -> Optional[Dict[str, Any]]:
        """
        Use LLM to classify the user message into a workflow type, considering history.

        Args:
            message: The user's current message text.
            context: Extracted context from the current message.
            history: List of recent conversation messages (e.g., [{"role": "user", "content": "..."}, ...]).

        Returns:
            Optional[Dict[str, Any]]: Classification results or None if failed/invalid.
        """
        # Create a clear, structured prompt for the LLM
        system_prompt = """You are a workflow classifier for a personal growth application called 'Game of Life'.
Your task is to classify user messages into specific workflow types based on user intent and context. If you're not sure what the user wants, your best bet is to orient to "discussion" workflow.

WORKFLOW TYPES:
1. discussion - User is confused or needs to talk
2. wheel_generation - User wants activity suggestions or recommendations
3. activity_feedback - User is giving feedback about a completed activity
4. pre_spin_feedback - User is sharing  feelings about a wheel that just got generated
5. user_onboarding - User is new and needs guidance about the system
6. post_spin - User has selected an activity and needs details (usually handled by metadata)

Classify the message based on:
- Explicit requests (e.g., "I want an activity" -> wheel_generation)
- Implied intent (e.g., "I just finished meditating and..." -> activity_feedback)
- Context clues (e.g., "I'm feeling creative today" -> wheel_generation)

Respond with a JSON object containing:
{
  "workflow_type": "one of the workflow types listed above",
  "confidence": float between 0.0-1.0,
  "reason": "brief explanation of classification"
}"""

        # Format history for the prompt
        formatted_history = "\n".join([f"{msg.get('role', 'unknown').capitalize()}: {msg.get('content', '')}" for msg in history])

        # User message including context and history
        user_prompt = f"""Conversation History (most recent first):
{formatted_history if formatted_history else "No history available."}

---
Current Message: {message}

Extracted context:
Mood: {context.get('mood', 'Not specified')}
Environment: {context.get('environment', 'Not specified')}
Time availability: {context.get('time_availability', 'Not specified')}
Focus: {context.get('focus', 'Not specified')}

Classify this message into the most appropriate workflow type."""

        # Call the LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        try:
            llm_response = await self.llm_client.chat_completion(
                messages=messages,
                temperature=0.2  # Low temperature for more deterministic results
            )

            # Parse the response to extract the JSON
            if llm_response.is_text:
                content = llm_response.content
                # Try to extract JSON from the response
                try:
                    # First try direct JSON parsing
                    classification = json.loads(content)
                except json.JSONDecodeError:
                    # Try to extract from markdown code blocks
                    import re
                    json_match = re.search(r"```(?:json)?\s*([\s\S]*?)\s*```", content)
                    if json_match:
                        try:
                            classification = json.loads(json_match.group(1))
                        except json.JSONDecodeError:
                            logger.warning("Failed to parse JSON from code block")
                            return None
                    else:
                        # Try to find JSON-like structure with regex
                        json_like = re.search(r'(\{.*"workflow_type".*\})', content, re.DOTALL)
                        if json_like:
                            try:
                                classification = json.loads(json_like.group(1))
                            except json.JSONDecodeError:
                                logger.warning("Failed to parse JSON-like structure")
                                return None
                        else:
                            logger.warning("No JSON found in LLM response")
                            return None

                # Validate the classification
                if isinstance(classification, dict) and "workflow_type" in classification:
                    # Ensure workflow_type is valid
                    # Added "discussion" based on LLM prompt and workflow_analysis.md
                    # Removed "progress_review" as it's not implemented
                    valid_types = [
                        "discussion", "wheel_generation", "activity_feedback",
                        "pre_spin_feedback", "user_onboarding",
                        "post_spin"
                    ]

                    if classification["workflow_type"] in valid_types:
                        # Ensure confidence is valid
                        if "confidence" not in classification:
                            classification["confidence"] = 0.8
                        else:
                            # --- Proposed Robust Handling ---
                            try:
                                classification["confidence"] = min(1.0, max(0.0, float(classification["confidence"])))
                            except (ValueError, TypeError):
                                logger.warning(f"Invalid confidence value received: {classification['confidence']}. Defaulting to 0.8.")
                                classification["confidence"] = 0.8
                            # --- End Proposed Handling ---

                        # Ensure reason is present
                        if "reason" not in classification:
                            classification["reason"] = "Classified by LLM"

                        return classification

            logger.warning(f"Invalid LLM classification response: {content}")
            return None

        except Exception as e:
            logger.error(f"Error in LLM classification: {str(e)}", exc_info=True)
            return None

    def _apply_classification_rules(
        self,
        message: str,
        context: Dict[str, Any],
        workflow_type: str,
        confidence: float
    ) -> Dict[str, Any]:
        """
        Apply business rules to refine the classification.

        Args:
            message: The user's message text
            context: Extracted context
            workflow_type: Initial workflow type
            confidence: Initial confidence

        Returns:
            dict: Refined classification
        """
        # Convert message to lowercase for case-insensitive matching
        message_lower = message.lower()

        # Rule 1: Strong keywords override with higher confidence
        activity_keywords = ["wheel", "activity", "suggest", "suggestion", "recommend", "what should i do"]
        feedback_keywords = ["feedback", "completed", "finished", "done with", "i did"]
        pre_spin_keywords = ["before spinning", "about to spin", "before i spin"]
        # Removed progress_keywords as progress_review workflow is not implemented

        # Rule 1: Strong keywords override (Order matters!)
        # Check feedback first, as it's more specific than general activity requests
        if any(kw in message_lower for kw in feedback_keywords):
            return {
                'workflow_type': 'activity_feedback',
                'confidence': max(0.8, confidence),
                'reason': 'Feedback-related keywords detected'
            }
        # Then check for general activity requests
        elif any(kw in message_lower for kw in activity_keywords):
            return {
                'workflow_type': 'wheel_generation',
                'confidence': max(0.8, confidence),
                'reason': 'Activity-related keywords detected'
            }
        # Then check for pre-spin context gathering
        elif any(kw in message_lower for kw in pre_spin_keywords):
            return {
                'workflow_type': 'pre_spin_feedback',
                'confidence': max(0.8, confidence),
                'reason': 'Pre-spin related keywords detected'
            }

        # Removed progress_review check as workflow is not implemented

        # Rule 2: If confidence is low (e.g., < 0.65), default to discussion first
        # This handles ambiguous cases before potentially misinterpreting brief mood messages.
        if confidence < 0.65:
             return {
                'workflow_type': 'discussion',
                'confidence': 0.6, # Keep confidence relatively low for default
                'reason': 'Low confidence classification, defaulting to discussion'
            }

        # Rule 3: If the message contains significant mood information but little else (and confidence wasn't low)
        # This might indicate pre-spin feedback if not explicitly asking for activities.
        if context.get('mood') and len(message.split()) < 10:
            return {
                'workflow_type': 'pre_spin_feedback', # Changed from discussion, might need review
                'confidence': 0.7,
                'reason': 'Brief message with mood information'
            }

        # Default: If no specific rules matched and confidence is reasonable, return the original classification.
        return {
            'workflow_type': workflow_type,
            'confidence': confidence,
            'reason': 'Based on initial classification (LLM or tool)'
        }

    def _check_if_action_required(self, workflow_type: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Check if additional information is needed before proceeding.

        Args:
            workflow_type: The determined workflow type
            context: Extracted context

        Returns:
            dict or None: Action required information or None
        """
        # For wheel generation, time availability is important
        if workflow_type == 'wheel_generation' and not context.get('time_availability'):
            return {
                'type': 'gather_information',
                'missing_field': 'time_availability',
                'prompt': "Before I suggest activities, could you let me know how much time you have available right now?"
            }

        # For activity feedback, ensure we know which activity
        if workflow_type == 'activity_feedback' and 'activity_id' not in context:
            return {
                'type': 'gather_information',
                'missing_field': 'activity_id',
                'prompt': "I'd love to hear your feedback. Which activity are you providing feedback on?"
            }

        # No additional action required
        return None

    def _build_context_packet(self, context_extraction: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build a standardized context packet for downstream processing.

        Args:
            context_extraction: Extracted context elements

        Returns:
            dict: Standardized context packet with enhanced context fields (schema v2.0.0)
        """
        # Build the base context packet with standard fields
        context_packet = {
            'user_id': self.user_profile_id,
            'user_profile_id': self.user_profile_id,  # Include for backwards compatibility
            'session_timestamp': datetime.now().isoformat(),
            'reported_mood': context_extraction.get('mood', ''),
            'reported_environment': context_extraction.get('environment', ''),
            'reported_time_availability': context_extraction.get('time_availability', ''),
            'reported_focus': context_extraction.get('focus', ''),
            'reported_satisfaction': context_extraction.get('satisfaction', ''),
            'extraction_confidence': context_extraction.get('extraction_confidence', 0.5),
            'entities': context_extraction.get('extracted_entities', []),
            'user_ws_session_name': self.user_ws_session_name  # Add WebSocket session for result routing
        }

        # Add enhanced context fields (schema v2.0.0)
        if 'user_state' in context_extraction:
            context_packet['user_state'] = context_extraction.get('user_state')

        if 'device_capabilities' in context_extraction:
            context_packet['device_capabilities'] = context_extraction.get('device_capabilities')

        if 'time_context' in context_extraction:
            context_packet['time_context'] = context_extraction.get('time_context')

        if 'user_preferences' in context_extraction:
            context_packet['user_preferences'] = context_extraction.get('user_preferences')

        return context_packet

    def _estimate_completion_time(self, workflow_type: str) -> int:
        """
        Estimate workflow completion time in seconds.

        Args:
            workflow_type: The workflow type

        Returns:
            int: Estimated completion time in seconds
        """
        # Provide realistic estimates to set user expectations
        estimates = {
            'wheel_generation': 15,  # 15 seconds
            'activity_feedback': 10,  # 10 seconds
            'pre_spin_feedback': 5,   # 5 seconds
            'user_onboarding': 20,    # 20 seconds
            # 'progress_review': 12, # Removed - not implemented
            'post_spin': 8,           # 8 seconds
            'discussion': 10          # Added estimate for discussion
        }
        return estimates.get(workflow_type, 10)  # Default 10 seconds

    def _launch_workflow(self, workflow_type: str, context_packet: Dict[str, Any], workflow_id: str) -> None:
        """
        Launch the workflow asynchronously.

        Args:
            workflow_type: Type of workflow to start
            context_packet: Context information for the workflow
            workflow_id: Unique identifier for the workflow
        """
        # Import here to avoid circular imports
        from apps.main.tasks.agent_tasks import execute_graph_workflow

        # Prepare initial input for the workflow
        initial_input = {
            "task_type": workflow_type,
            "context_packet": context_packet,
            "workflow_type": workflow_type,  # Explicitly include for result handling
            "user_ws_session_name": self.user_ws_session_name  # Include for result routing
        }

        # Launch the Celery task
        try:
            execute_graph_workflow.delay(
                workflow_id=workflow_id,
                user_profile_id=self.user_profile_id,
                initial_input=initial_input,
                workflow_type=workflow_type # Pass workflow_type explicitly
            )
            logger.info(f"Launched workflow task: {workflow_type} ({workflow_id})")
        except Exception as e:
            logger.error(f"Error launching workflow task: {str(e)}", exc_info=True)
            # This doesn't raise an exception to allow the response to be returned

    @database_sync_to_async
    def _record_workflow_initiation_sync(self, workflow_type: str, workflow_id: str, context: Dict[str, Any]):
        """Synchronous helper to record workflow initiation."""
        from apps.main.models import HistoryEvent
        from django.contrib.contenttypes.models import ContentType
        from apps.user.models import UserProfile

        try:
            # Get user profile content type
            user_profile_content_type = ContentType.objects.get_for_model(UserProfile)

            # Create history event
            HistoryEvent.objects.create(
                event_type='workflow_initiated',
                content_type=user_profile_content_type,
                object_id=self.user_profile_id,
                user_profile_id=self.user_profile_id,
                details={
                    'workflow_type': workflow_type,
                    'workflow_id': workflow_id,
                    'initial_context': context
                }
            )
            logger.debug(f"Recorded workflow initiation event: {workflow_type} ({workflow_id})")
        except Exception as e:
            # Log the error but don't let it crash the main flow
            logger.error(f"Error recording workflow initiation in sync helper: {str(e)}", exc_info=True)


    async def _record_workflow_initiation(self, workflow_type: str, workflow_id: str,
                                        context: Dict[str, Any]) -> None:
        """
        Record workflow initiation in history (async wrapper).

        Args:
            workflow_type: Type of workflow initiated
            workflow_id: Unique identifier for the workflow
            context: Context information
        """
        try:
            # Call the synchronous helper using database_sync_to_async
            await self._record_workflow_initiation_sync(workflow_type, workflow_id, context)
        except Exception as e:
            # Log the warning if the async call itself fails (less likely)
            logger.warning(f"Failed to record workflow initiation (async wrapper): {str(e)}")
            # Non-critical error, don't re-raise


# Note: This function seems misplaced here and might be better in tasks or utils.
# It also has a potential issue mixing sync/async logic for testing.
# Keeping it as is for now, but flagging for potential refactor.
def execute_graph_workflow(workflow_id, user_profile_id, initial_input, workflow_type=None): # Added workflow_type default
    """
    Execute a complete LangGraph workflow.

    This function is a convenience wrapper for tests and direct calls
    that delegates to the Celery task.

    Args:
        workflow_id: ID for the workflow run
        user_profile_id: The user profile ID
        initial_input: Initial input for the workflow

    Returns:
        dict: Workflow result
    """
    from apps.main.tasks.agent_tasks import execute_graph_workflow as task
    import os

    # For testing environments, run synchronously
    if 'TESTING' in os.environ:
        # Import asyncio to run async function directly
        import asyncio
        from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow

        # If this is a wheel generation workflow, run it directly
        if initial_input.get('task_type') == 'wheel_generation':
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                run_wheel_generation_workflow(
                    user_profile_id=user_profile_id,
                    context_packet=initial_input,
                    workflow_id=workflow_id
                )
            )

        # Generic fallback
        return {'workflow_id': workflow_id, 'result': {'output_data': {}}}

    # For production, use the Celery task
    return task.delay(
        workflow_id=workflow_id,
        user_profile_id=user_profile_id,
        initial_input=initial_input,
        workflow_type=workflow_type # Pass workflow_type if provided
    )
