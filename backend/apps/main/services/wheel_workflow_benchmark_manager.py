# ACTIVE_FILE - 29-05-2025
"""
Wheel Generation Workflow Benchmark Manager

This module implements a concrete workflow benchmark manager for the wheel generation workflow.
It extends the AsyncWorkflowManager base class to provide specific implementation for
benchmarking the wheel generation workflow.
"""

import asyncio
import logging
import time
import uuid
import statistics
from typing import Dict, Any, List, Optional, Callable, Type, Union
from dataclasses import dataclass, field
from collections import defaultdict

from asgiref.sync import sync_to_async
from django.db import transaction
from django.utils import timezone

from apps.main.models import BenchmarkScenario, BenchmarkRun
from apps.main.services.async_workflow_manager import WorkflowBenchmarker, BenchmarkResult
from apps.main.testing.mock_tool_registry import MockToolRegistry
from apps.main.graphs.wheel_generation_graph import create_wheel_generation_graph, run_wheel_generation_workflow

# Configure logging
logger = logging.getLogger(__name__)

class WheelWorkflowBenchmarkManager(WorkflowBenchmarker):
    """
    Benchmark manager for the wheel generation workflow.

    This class extends AsyncWorkflowManager to provide specific implementation
    for benchmarking the wheel generation workflow.
    """

    async def _run_workflow_benchmark(self,
                                     scenario: BenchmarkScenario,
                                     workflow_type: str,
                                     mock_tools: MockToolRegistry,
                                     runs: int = 3,
                                     warmup_runs: int = 1,
                                     progress_callback: Optional[Callable] = None) -> BenchmarkResult:
        """
        Run a wheel generation workflow benchmark.

        Args:
            scenario: The benchmark scenario
            workflow_type: Type of workflow to benchmark (should be 'wheel_generation')
            mock_tools: Configured mock tool registry
            runs: Number of benchmark runs
            warmup_runs: Number of warmup runs
            progress_callback: Optional callback for progress reporting

        Returns:
            BenchmarkResult: The benchmark results
        """
        if workflow_type != 'wheel_generation':
            raise ValueError(f"Expected workflow_type 'wheel_generation', got '{workflow_type}'")

        # Initialize result object
        result = BenchmarkResult(
            workflow_type=workflow_type,
            scenario_name=scenario.name
        )

        # Extract input data from scenario
        input_data = scenario.input_data
        if not isinstance(input_data, dict):
            raise ValueError(f"Expected dict for input_data, got {type(input_data)}")

        # Ensure required fields are present
        user_profile_id = input_data.get('user_profile_id')
        if not user_profile_id:
            raise ValueError("Missing required field 'user_profile_id' in input_data")

        # Prepare context packet
        context_packet = input_data.get('context_packet', {})
        if not isinstance(context_packet, dict):
            context_packet = {}

        # Add task_type if not present
        if 'task_type' not in context_packet:
            context_packet['task_type'] = 'wheel_generation'

        # Track durations
        durations = []

        # Run warmup iterations
        for i in range(warmup_runs):
            try:
                logger.info(f"Running warmup iteration {i+1}/{warmup_runs} for scenario '{scenario.name}'")

                # Generate a unique workflow ID for this run
                workflow_id = str(uuid.uuid4())

                # Run the workflow with mock tools
                # Note: We're not measuring performance for warmup runs
                # MockToolRegistry doesn't support context manager protocol
                mock_tools.activate()
                try:
                    await run_wheel_generation_workflow(
                        user_profile_id=user_profile_id,
                        context_packet=context_packet,
                        workflow_id=workflow_id
                    )
                finally:
                    mock_tools.deactivate()
            except Exception as e:
                logger.warning(f"Error in warmup iteration {i+1}: {str(e)}", exc_info=True)

        # Run benchmark iterations
        successful_runs = 0

        for i in range(runs):
            try:
                logger.info(f"Running benchmark iteration {i+1}/{runs} for scenario '{scenario.name}'")

                if progress_callback:
                    progress_callback(state='PROGRESS', meta={
                        'current': 30 + int((i / runs) * 50),  # Scale from 30% to 80%
                        'total': 100,
                        'status': f"Running benchmark iteration {i+1}/{runs}"
                    })

                # Generate a unique workflow ID for this run
                workflow_id = str(uuid.uuid4())

                # Measure execution time
                start_time = time.monotonic()

                # Run the workflow with mock tools
                # MockToolRegistry doesn't support context manager protocol
                mock_tools.activate()
                try:
                    output = await run_wheel_generation_workflow(
                        user_profile_id=user_profile_id,
                        context_packet=context_packet,
                        workflow_id=workflow_id
                    )
                finally:
                    mock_tools.deactivate()

                # Calculate duration
                end_time = time.monotonic()
                duration = end_time - start_time
                durations.append(duration)

                # Store the last output
                if i == runs - 1:
                    result.last_output_data = output

                # Track successful runs
                successful_runs += 1

                # Collect tool usage statistics
                for tool_name, count in mock_tools.get_call_counts().items():
                    if tool_name in result.tool_call_counts:
                        result.tool_call_counts[tool_name] += count
                    else:
                        result.tool_call_counts[tool_name] = count

                # Collect stage timings from the workflow
                # Note: This assumes the workflow uses the StageTimer pattern
                if 'stage_timings' in output:
                    for stage, timing in output['stage_timings'].items():
                        result.stage_timings[stage].append(timing)

                # Collect token usage
                if 'token_usage' in output:
                    result.total_input_tokens += output['token_usage'].get('input_tokens', 0)
                    result.total_output_tokens += output['token_usage'].get('output_tokens', 0)

            except Exception as e:
                logger.error(f"Error in benchmark iteration {i+1}: {str(e)}", exc_info=True)
                result.errors.append(f"Error in iteration {i+1}: {str(e)}")

        # Calculate statistics
        if durations:
            result.mean_duration = statistics.mean(durations)
            result.median_duration = statistics.median(durations)
            result.min_duration = min(durations)
            result.max_duration = max(durations)
            result.std_dev = statistics.stdev(durations) if len(durations) > 1 else 0.0

        # Calculate success rate
        result.success_rate = successful_runs / runs if runs > 0 else 0.0

        return result
