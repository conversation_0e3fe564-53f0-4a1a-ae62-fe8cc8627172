"""
Schema Version Manager Service

This service provides functionality for managing schema versions, migrations,
and compatibility between different versions of schemas.

Key responsibilities:
1. Track schema versions
2. Provide migration utilities for schema changes
3. Ensure backward compatibility between schema versions
4. Convert between schema versions
"""

import logging
import re
import semver
from typing import Dict, Any, List, Optional, Callable, Tuple, Union

from .schema_registry import SchemaRegistry

# Configure logging
logger = logging.getLogger(__name__)

# Type definitions
MigrationFunc = Callable[[Dict[str, Any]], Dict[str, Any]]
SchemaVersion = Union[str, semver.VersionInfo]


class SchemaVersionManager:
    """
    Manages schema versions, migrations, and compatibility.

    This class provides methods for tracking schema versions, migrating between
    versions, and ensuring compatibility between different versions of schemas.
    """

    def __init__(self, registry: Optional[SchemaRegistry] = None):
        """
        Initialize the schema version manager.

        Args:
            registry: Optional schema registry to use.
                    If None, creates a new SchemaRegistry.
        """
        self.registry = registry or SchemaRegistry()
        self.migrations: Dict[str, Dict[Tuple[str, str], MigrationFunc]] = {}
        self.versioned_schemas: Dict[str, Dict[str, Dict[str, Any]]] = {}
        self.default_versions: Dict[str, str] = {}

    def register_schema_version(self, schema_type: str, version: str, schema: Dict[str, Any]) -> None:
        """
        Register a schema with a specific version.

        Args:
            schema_type: Type identifier for the schema
            version: Version string (semver format)
            schema: JSON Schema as a Python dict
        """
        # Validate version format
        try:
            semver.VersionInfo.parse(version)
        except ValueError:
            raise ValueError(f"Invalid version format: {version}. Must be semver format (e.g., 1.0.0)")

        # Initialize schema type if not exists
        if schema_type not in self.versioned_schemas:
            self.versioned_schemas[schema_type] = {}

        # Store the schema with its version
        self.versioned_schemas[schema_type][version] = schema

        # Update default version if this is newer
        if schema_type not in self.default_versions or self._is_newer_version(version, self.default_versions[schema_type]):
            self.default_versions[schema_type] = version
            # Also register with the schema registry (latest version)
            self.registry.register_schema(schema_type, schema)

        logger.debug(f"Registered schema '{schema_type}' version '{version}'")

    def get_schema_version(self, schema_type: str, version: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get a schema by type and version.

        Args:
            schema_type: Type of schema to retrieve
            version: Optional version string. If None, returns the latest version.

        Returns:
            The schema as a dict, or None if not found
        """
        if schema_type not in self.versioned_schemas:
            return None

        if version is None:
            # Get the latest version
            latest_version = self._get_latest_version(schema_type)
            if latest_version is None:
                return None
            return self.versioned_schemas[schema_type].get(latest_version)

        return self.versioned_schemas[schema_type].get(version)

    def list_schema_versions(self, schema_type: str) -> List[str]:
        """
        Get a list of all versions for a schema type.

        Args:
            schema_type: Type of schema

        Returns:
            List of version strings
        """
        if schema_type not in self.versioned_schemas:
            return []

        return sorted(
            self.versioned_schemas[schema_type].keys(),
            key=lambda v: semver.VersionInfo.parse(v)
        )

    def register_migration(
        self,
        schema_type: str,
        from_version: str,
        to_version: str,
        migration_func: MigrationFunc
    ) -> None:
        """
        Register a migration function between two schema versions.

        Args:
            schema_type: Type of schema
            from_version: Source version
            to_version: Target version
            migration_func: Function that transforms data from source to target version
        """
        # Initialize schema type if not exists
        if schema_type not in self.migrations:
            self.migrations[schema_type] = {}

        # Store the migration function
        self.migrations[schema_type][(from_version, to_version)] = migration_func

        logger.debug(f"Registered migration for '{schema_type}' from '{from_version}' to '{to_version}'")

    def migrate_data(
        self,
        schema_type: str,
        data: Dict[str, Any],
        from_version: str,
        to_version: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Migrate data from one schema version to another.

        Args:
            schema_type: Type of schema
            data: Data to migrate
            from_version: Source version
            to_version: Target version. If None, migrates to the latest version.

        Returns:
            Migrated data
        """
        if to_version is None:
            to_version = self._get_latest_version(schema_type)
            if to_version is None:
                raise ValueError(f"No versions found for schema type '{schema_type}'")

        # If versions are the same, return data as is
        if from_version == to_version:
            return data

        # Check if we have a direct migration path
        if schema_type in self.migrations and (from_version, to_version) in self.migrations[schema_type]:
            migration_func = self.migrations[schema_type][(from_version, to_version)]
            return migration_func(data)

        # Try to find a migration path
        path = self._find_migration_path(schema_type, from_version, to_version)
        if not path:
            raise ValueError(f"No migration path found from '{from_version}' to '{to_version}' for schema '{schema_type}'")

        # Apply migrations in sequence
        migrated_data = data
        for i in range(len(path) - 1):
            from_ver = path[i]
            to_ver = path[i + 1]
            migration_func = self.migrations[schema_type][(from_ver, to_ver)]
            migrated_data = migration_func(migrated_data)

        return migrated_data

    def validate_with_version(
        self,
        schema_type: str,
        instance: Dict[str, Any],
        version: Optional[str] = None
    ) -> Tuple[bool, List[str]]:
        """
        Validate an instance against a specific schema version.

        Args:
            schema_type: Type of schema to validate against
            instance: The object to validate
            version: Optional version string. If None, uses the latest version.

        Returns:
            Tuple of (is_valid, error_messages)
        """
        schema = self.get_schema_version(schema_type, version)
        if schema is None:
            return False, [f"Schema '{schema_type}' version '{version}' not found"]

        # Create a temporary registry with just this schema version
        temp_registry = SchemaRegistry()
        temp_registry.register_schema(schema_type, schema)

        # Validate using the registry
        return temp_registry.validate(schema_type, instance)

    def is_compatible(
        self,
        schema_type: str,
        instance: Dict[str, Any],
        from_version: str,
        to_version: Optional[str] = None
    ) -> Tuple[bool, List[str]]:
        """
        Check if data is compatible between schema versions.

        Args:
            schema_type: Type of schema
            instance: The object to check
            from_version: Source version
            to_version: Target version. If None, checks against the latest version.

        Returns:
            Tuple of (is_compatible, error_messages)
        """
        # First validate against the source version
        is_valid, errors = self.validate_with_version(schema_type, instance, from_version)
        if not is_valid:
            return False, [f"Data is not valid against source version '{from_version}': {errors}"]

        # Try to migrate the data
        try:
            migrated_data = self.migrate_data(schema_type, instance, from_version, to_version)
        except ValueError as e:
            return False, [str(e)]

        # Validate migrated data against target version
        is_valid, errors = self.validate_with_version(schema_type, migrated_data, to_version)
        if not is_valid:
            return False, [f"Migrated data is not valid against target version '{to_version}': {errors}"]

        return True, []

    def extract_version_from_schema(self, schema: Dict[str, Any]) -> Optional[str]:
        """
        Extract version information from a schema.

        Args:
            schema: JSON Schema as a Python dict

        Returns:
            Version string if found, None otherwise
        """
        # Check for version in standard locations
        if '$version' in schema:
            return schema['$version']

        if 'version' in schema:
            return schema['version']

        # Check for version in metadata
        if 'metadata' in schema and isinstance(schema['metadata'], dict):
            if 'version' in schema['metadata']:
                return schema['metadata']['version']

        # Try to extract from title or description
        if 'title' in schema and isinstance(schema['title'], str):
            version_match = re.search(r'v(\d+\.\d+\.\d+)', schema['title'])
            if version_match:
                return version_match.group(1)

        if 'description' in schema and isinstance(schema['description'], str):
            version_match = re.search(r'v(\d+\.\d+\.\d+)', schema['description'])
            if version_match:
                return version_match.group(1)

        return None

    def _is_latest_version(self, schema_type: str, version: str) -> bool:
        """
        Check if a version is the latest for a schema type.

        Args:
            schema_type: Type of schema
            version: Version to check

        Returns:
            True if it's the latest version, False otherwise
        """
        if schema_type not in self.versioned_schemas:
            return True

        if not self.versioned_schemas[schema_type]:
            return True

        latest_version = self._get_latest_version(schema_type)
        if latest_version is None:
            return True

        return semver.VersionInfo.parse(version) >= semver.VersionInfo.parse(latest_version)

    def _get_latest_version(self, schema_type: str) -> Optional[str]:
        """
        Get the latest version for a schema type.

        Args:
            schema_type: Type of schema

        Returns:
            Latest version string, or None if no versions exist
        """
        if schema_type not in self.versioned_schemas or not self.versioned_schemas[schema_type]:
            return None

        versions = list(self.versioned_schemas[schema_type].keys())
        return max(versions, key=lambda v: semver.VersionInfo.parse(v))

    def _is_newer_version(self, version1: str, version2: str) -> bool:
        """
        Check if version1 is newer than version2.

        Args:
            version1: First version string
            version2: Second version string

        Returns:
            True if version1 is newer than version2, False otherwise
        """
        try:
            v1 = semver.VersionInfo.parse(version1)
            v2 = semver.VersionInfo.parse(version2)
            return v1 > v2
        except ValueError:
            # If parsing fails, fall back to string comparison
            return version1 > version2

    def _find_migration_path(self, schema_type: str, from_version: str, to_version: str) -> Optional[List[str]]:
        """
        Find a path of migrations from one version to another.

        Args:
            schema_type: Type of schema
            from_version: Source version
            to_version: Target version

        Returns:
            List of version strings forming a path, or None if no path exists

        Raises:
            ValueError: If a circular migration path is detected
        """
        if schema_type not in self.migrations:
            return None

        # Build a graph of version connections
        graph = {}
        for (src, dst) in self.migrations[schema_type].keys():
            if src not in graph:
                graph[src] = []
            graph[src].append(dst)

        # Use BFS to find a path
        queue = [(from_version, [from_version])]
        visited = set([from_version])

        # Keep track of paths to detect cycles
        paths = {}
        paths[from_version] = [from_version]

        while queue:
            (node, path) = queue.pop(0)

            if node == to_version:
                # Check if the path contains a cycle
                if len(set(path)) < len(path):
                    # This means there's a duplicate version in the path
                    raise ValueError(f"Circular migration path detected: {path}")
                return path

            if node not in graph:
                continue

            for neighbor in graph[node]:
                # Check for circular paths
                if neighbor in path:
                    # This means we've already visited this version in this path
                    circular_path = path + [neighbor]
                    raise ValueError(f"Circular migration path detected: {circular_path}")

                if neighbor not in visited:
                    visited.add(neighbor)
                    new_path = path + [neighbor]
                    paths[neighbor] = new_path
                    queue.append((neighbor, new_path))

        return None
