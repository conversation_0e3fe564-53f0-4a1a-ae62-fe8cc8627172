"""
Benchmark service for workflow benchmarking.
Provides functions for running workflow benchmarks and managing benchmark results.
"""
import logging
from typing import Dict, Any, Optional
from asgiref.sync import sync_to_async
from apps.main.models import BenchmarkScenario, BenchmarkRun
from apps.main.services.async_workflow_manager import WorkflowBenchmarker

logger = logging.getLogger(__name__)


async def run_workflow_benchmark(
    scenario_id: str,
    params: Dict[str, Any],
    user_profile_id: Optional[str] = None
) -> str:
    """
    Run a workflow benchmark asynchronously via Celery.
    
    Args:
        scenario_id: The ID of the benchmark scenario to run
        params: Parameters for the benchmark run
        user_profile_id: Optional ID of the user profile to use for the benchmark
        
    Returns:
        The ID of the Celery task
    """
    from apps.main.tasks import run_workflow_benchmark
    
    # Start the Celery task
    task = run_workflow_benchmark.delay(
        scenario_id=scenario_id,
        params=params,
        user_profile_id=user_profile_id
    )
    
    return task.id


async def get_benchmark_results(scenario_id: str) -> Dict[str, Any]:
    """
    Get benchmark results for a specific scenario.
    
    Args:
        scenario_id: The ID of the benchmark scenario
        
    Returns:
        A dictionary containing benchmark results
    """
    # Get the scenario
    scenario = await sync_to_async(
        lambda: BenchmarkScenario.objects.get(id=scenario_id)
    )()
    
    # Get all benchmark runs for this scenario
    runs = await sync_to_async(
        lambda: list(BenchmarkRun.objects.filter(scenario=scenario))
    )()
    
    # Calculate aggregate statistics
    total_runs = len(runs)
    if total_runs == 0:
        return {
            "scenario_id": scenario_id,
            "scenario_name": scenario.name,
            "total_runs": 0,
            "results": []
        }
    
    # Extract results
    results = []
    for run in runs:
        results.append({
            "run_id": str(run.id),
            "agent_version": run.agent_version,
            "mean_duration": run.mean_duration,
            "success_rate": run.success_rate,
            "llm_calls": run.llm_calls,
            "tool_calls": run.tool_calls,
            "total_input_tokens": run.total_input_tokens,
            "total_output_tokens": run.total_output_tokens,
            "semantic_score": run.semantic_score,
            "created_at": run.created_at.isoformat(),
        })
    
    return {
        "scenario_id": scenario_id,
        "scenario_name": scenario.name,
        "total_runs": total_runs,
        "results": results
    }
