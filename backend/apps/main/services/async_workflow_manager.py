# ACTIVE_FILE - 29-05-2025
"""
WorkflowBenchmarker Service

This service provides a base class for benchmarking complete LangGraph workflows,
measuring performance, resource usage, and quality across multi-agent interactions.

Key responsibilities:
1. Execute workflow benchmarks with proper error handling
2. Track token usage and performance metrics
3. Store benchmark results
4. Report progress via WebSockets
"""

import asyncio
import logging
import re
import statistics
import time
import uuid
from typing import Dict, Any, List, Optional, Callable, Tuple, Union
from dataclasses import dataclass, field
from collections import defaultdict
from datetime import datetime

from asgiref.sync import sync_to_async
from django.db import transaction
from django.db.models import F
from django.utils import timezone
from django.conf import settings

from apps.main.models import BenchmarkScenario, BenchmarkRun, GenericAgent
from apps.main.services.event_service import EventService
from apps.main.services.semantic_evaluator import SemanticEvaluator
from apps.main.services.schema_validator_service import SchemaValidationService
from apps.main.testing.mock_tool_registry import MockToolRegistry

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class TokenTracker:
    """
    Tracks token usage across workflow execution.

    Attributes:
        run_id: UUID of the benchmark run
        input_tokens: Count of input tokens used
        output_tokens: Count of output tokens used
        stage_tokens: Dictionary mapping stage names to token counts
        model_tokens: Dictionary mapping model names to token counts
    """
    run_id: uuid.UUID
    input_tokens: int = 0
    output_tokens: int = 0
    stage_tokens: Dict[str, Dict[str, int]] = field(default_factory=lambda: defaultdict(lambda: {"input": 0, "output": 0}))
    model_tokens: Dict[str, Dict[str, int]] = field(default_factory=lambda: defaultdict(lambda: {"input": 0, "output": 0}))

    async def record_usage(self, input_tokens: int, output_tokens: int, stage: Optional[str] = None, model: Optional[str] = None):
        """
        Record token usage and update the benchmark run record.

        Args:
            input_tokens: Number of input tokens used
            output_tokens: Number of output tokens used
            stage: Optional stage name for tracking tokens by stage
            model: Optional model name for tracking tokens by model
        """
        self.input_tokens += input_tokens
        self.output_tokens += output_tokens

        # Track tokens by stage if provided
        if stage:
            self.stage_tokens[stage]["input"] += input_tokens
            self.stage_tokens[stage]["output"] += output_tokens

        # Track tokens by model if provided
        if model:
            self.model_tokens[model]["input"] += input_tokens
            self.model_tokens[model]["output"] += output_tokens

        # Update the benchmark run record in the database
        await sync_to_async(
            BenchmarkRun.objects.filter(id=self.run_id).update,
            thread_sensitive=True
        )(
            total_input_tokens=F('total_input_tokens') + input_tokens,
            total_output_tokens=F('total_output_tokens') + output_tokens,
            llm_calls=F('llm_calls') + 1
        )

    async def calculate_cost(self, input_price: float, output_price: float) -> float:
        """
        Calculate the cost of token usage.

        Args:
            input_price: Price per input token
            output_price: Price per output token

        Returns:
            float: Total cost
        """
        return (self.input_tokens * input_price) + (self.output_tokens * output_price)

    async def calculate_cost_by_model(self, model_prices: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """
        Calculate the cost of token usage by model.

        Args:
            model_prices: Dictionary mapping model names to price dictionaries
                          with "input" and "output" keys

        Returns:
            Dict[str, float]: Dictionary mapping model names to costs
        """
        costs = {}
        for model, tokens in self.model_tokens.items():
            if model in model_prices:
                input_price = model_prices[model].get("input", 0)
                output_price = model_prices[model].get("output", 0)
                costs[model] = (tokens["input"] * input_price) + (tokens["output"] * output_price)
        return costs

    async def calculate_cost_by_stage(self, input_price: float, output_price: float) -> Dict[str, float]:
        """
        Calculate the cost of token usage by stage.

        Args:
            input_price: Price per input token
            output_price: Price per output token

        Returns:
            Dict[str, float]: Dictionary mapping stage names to costs
        """
        costs = {}
        for stage, tokens in self.stage_tokens.items():
            costs[stage] = (tokens["input"] * input_price) + (tokens["output"] * output_price)
        return costs

    def get_token_usage_report(self) -> Dict[str, Any]:
        """
        Generate a detailed token usage report.

        Returns:
            Dict[str, Any]: Dictionary containing token usage information
        """
        return {
            "total": {
                "input": self.input_tokens,
                "output": self.output_tokens,
                "total": self.input_tokens + self.output_tokens
            },
            "by_stage": {
                stage: {
                    "input": tokens["input"],
                    "output": tokens["output"],
                    "total": tokens["input"] + tokens["output"]
                }
                for stage, tokens in self.stage_tokens.items()
            },
            "by_model": {
                model: {
                    "input": tokens["input"],
                    "output": tokens["output"],
                    "total": tokens["input"] + tokens["output"]
                }
                for model, tokens in self.model_tokens.items()
            }
        }


@dataclass
class StageTimer:
    """
    Enhanced timer for tracking execution time of workflow stages.

    Attributes:
        stages: Dictionary mapping stage names to timing information
    """
    stages: Dict[str, Dict[str, Any]] = field(default_factory=lambda: defaultdict(dict))

    async def start_stage(self, name: str):
        """
        Start timing a stage.

        Args:
            name: Name of the stage
        """
        self.stages[name]['start'] = time.monotonic()

    async def end_stage(self, name: str):
        """
        End timing a stage and calculate duration.

        Args:
            name: Name of the stage
        """
        if name in self.stages and 'start' in self.stages[name]:
            self.stages[name]['duration'] = time.monotonic() - self.stages[name]['start']
            self.stages[name]['end'] = time.monotonic()

    def get_stage_durations(self) -> Dict[str, float]:
        """
        Get durations for all completed stages.

        Returns:
            Dict mapping stage names to durations in seconds
        """
        return {name: data['duration'] for name, data in self.stages.items()
                if 'duration' in data}

    def get_stage_durations_ms(self) -> Dict[str, float]:
        """
        Get durations for all completed stages in milliseconds.

        Returns:
            Dict mapping stage names to durations in milliseconds
        """
        return {name: data['duration'] * 1000 for name, data in self.stages.items()
                if 'duration' in data}


@dataclass
class BenchmarkResult:
    """
    Stores the results of a workflow benchmark run.

    Attributes:
        workflow_type: Type of workflow benchmarked
        scenario_name: Name of the benchmark scenario
        mean_duration: Mean execution time in seconds
        median_duration: Median execution time in seconds
        min_duration: Minimum execution time in seconds
        max_duration: Maximum execution time in seconds
        std_dev: Standard deviation of execution times
        success_rate: Percentage of successful runs
        tool_call_counts: Dictionary mapping tool names to call counts
        stage_timings: Dictionary mapping stage names to timing statistics
        total_input_tokens: Total input tokens used
        total_output_tokens: Total output tokens used
        token_usage_by_stage: Dictionary mapping stage names to token usage
        token_usage_by_model: Dictionary mapping model names to token usage
        token_usage_report: Detailed token usage report
        cost_report: Detailed cost report
        last_output_data: Output data from the last run
        semantic_score: Overall semantic quality score (0.0 to 1.0)
        semantic_evaluation_details: Detailed reasoning for the semantic score
        semantic_evaluations: Full semantic evaluation results from all evaluator models
        errors: List of errors encountered during benchmark runs
    """
    workflow_type: str
    scenario_name: str
    mean_duration: float = 0.0
    median_duration: float = 0.0
    min_duration: float = 0.0
    max_duration: float = 0.0
    std_dev: float = 0.0
    success_rate: float = 0.0
    tool_call_counts: Dict[str, int] = field(default_factory=dict)
    stage_timings: Dict[str, List[float]] = field(default_factory=lambda: defaultdict(list))
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    token_usage_by_stage: Dict[str, Dict[str, int]] = field(default_factory=dict)
    token_usage_by_model: Dict[str, Dict[str, int]] = field(default_factory=dict)
    token_usage_report: Dict[str, Any] = field(default_factory=dict)
    cost_report: Dict[str, Any] = field(default_factory=dict)
    last_output_data: Dict[str, Any] = field(default_factory=dict)
    semantic_score: float = 0.0
    semantic_evaluation_details: str = ""
    semantic_evaluations: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert results to a dictionary.

        Returns:
            Dict representation of the benchmark results
        """
        return {
            'workflow_type': self.workflow_type,
            'scenario': self.scenario_name,
            'performance': {
                'mean_duration_s': round(self.mean_duration, 4),
                'median_duration_s': round(self.median_duration, 4),
                'min_duration_s': round(self.min_duration, 4),
                'max_duration_s': round(self.max_duration, 4),
                'std_dev': round(self.std_dev, 4),
                'success_rate': self.success_rate,
            },
            'operations': {
                'tool_calls': dict(sorted(self.tool_call_counts.items())),
                'total_input_tokens': self.total_input_tokens,
                'total_output_tokens': self.total_output_tokens,
            },
            'token_usage': self.token_usage_report,
            'cost': self.cost_report,
            'semantic_quality': {
                'overall_score': self.semantic_score,
                'evaluation_details': self.semantic_evaluation_details,
                'evaluations': self.semantic_evaluations
            },
            'stage_timings_raw': {k: v for k, v in self.stage_timings.items()},
            'errors': self.errors,
            'last_output': self.last_output_data,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BenchmarkResult':
        """
        Create a benchmark result from a dictionary.

        Args:
            data: Dictionary containing benchmark result data

        Returns:
            BenchmarkResult instance
        """
        # Extract performance metrics
        performance = data.get('performance', {})
        operations = data.get('operations', {})
        semantic_quality = data.get('semantic_quality', {})
        token_usage = data.get('token_usage', {})
        cost = data.get('cost', {})

        # Extract token usage by stage and model
        token_usage_by_stage = {}
        token_usage_by_model = {}

        if token_usage and 'by_stage' in token_usage:
            token_usage_by_stage = token_usage['by_stage']

        if token_usage and 'by_model' in token_usage:
            token_usage_by_model = token_usage['by_model']

        return cls(
            workflow_type=data.get('workflow_type', ''),
            scenario_name=data.get('scenario', ''),
            mean_duration=performance.get('mean_duration_s', 0.0),
            median_duration=performance.get('median_duration_s', 0.0),
            min_duration=performance.get('min_duration_s', 0.0),
            max_duration=performance.get('max_duration_s', 0.0),
            std_dev=performance.get('std_dev', 0.0),
            success_rate=performance.get('success_rate', 0.0),
            tool_call_counts=operations.get('tool_calls', {}),
            total_input_tokens=operations.get('total_input_tokens', 0),
            total_output_tokens=operations.get('total_output_tokens', 0),
            token_usage_by_stage=token_usage_by_stage,
            token_usage_by_model=token_usage_by_model,
            token_usage_report=token_usage,
            cost_report=cost,
            semantic_score=semantic_quality.get('overall_score', 0.0),
            semantic_evaluation_details=semantic_quality.get('evaluation_details', ''),
            semantic_evaluations=semantic_quality.get('evaluations', {}),
            stage_timings=data.get('stage_timings_raw', {}),
            last_output_data=data.get('last_output', {}),
            errors=data.get('errors', [])
        )

    def validate(self) -> bool:
        """
        Validate the benchmark result using Pydantic models.

        Returns:
            bool: True if validation passes, False otherwise
        """
        try:
            # Import here to avoid circular imports
            from apps.main.services.workflow_schemas import BenchmarkState

            # Create a benchmark state from the result
            state = BenchmarkState(
                scenario_id=str(self.scenario_name),
                workflow_type=self.workflow_type,
                current_run=0,
                total_runs=1,
                warmup_runs=0,
                stage="complete",
                errors=self.errors
            )

            # If we got here, validation passed
            return True
        except Exception as e:
            logger.error(f"Benchmark result validation failed: {str(e)}")
            return False

    def get_stage_performance_details(self) -> Dict[str, Dict[str, float]]:
        """
        Calculate stage performance statistics.

        Returns:
            Dict mapping stage names to performance metrics
        """
        stage_performance = {}

        for stage_name, timings in self.stage_timings.items():
            if not timings:
                continue

            # Calculate statistics
            count = len(timings)
            mean_ms = statistics.mean(timings) * 1000 if timings else 0

            stage_performance[stage_name] = {
                'count': count,
                'mean_ms': mean_ms
            }

            # Add median, min, max if we have enough data
            if count > 1:
                stage_performance[stage_name].update({
                    'median_ms': statistics.median(timings) * 1000,
                    'min_ms': min(timings) * 1000,
                    'max_ms': max(timings) * 1000
                })

            # Add standard deviation if we have enough data
            if count > 2:
                stage_performance[stage_name]['std_dev_ms'] = statistics.stdev(timings) * 1000

        return stage_performance


class WorkflowBenchmarker:
    """
    Base class for workflow benchmarking.

    This class provides the foundation for benchmarking complete LangGraph workflows,
    measuring performance, resource usage, and quality across multi-agent interactions.
    """

    def __init__(self):
        """Initialize the workflow manager."""
        self.token_tracker = None
        self.stage_timer = StageTimer()
        self.semantic_evaluator = SemanticEvaluator()
        self.schema_validator = SchemaValidationService()

    async def run_benchmark(self,
                          scenario,
                          benchmark_params: Dict[str, Any] = None,
                          progress_callback: Optional[Callable] = None) -> BenchmarkRun:
        """
        Run a workflow benchmark using a provided scenario.

        This method is used by subclasses to run a benchmark with a scenario object
        that has already been loaded. It delegates to the _run_workflow_benchmark method
        which must be implemented by subclasses.

        Args:
            scenario: The benchmark scenario object
            benchmark_params: Optional parameters for the benchmark
            progress_callback: Optional callback for progress reporting

        Returns:
            BenchmarkRun: The created benchmark run record
        """
        # Set up benchmark parameters
        params = {
            'runs': 3,
            'warmup_runs': 1,
            'semantic_evaluation': False,
            **(benchmark_params or {})
        }

        # Get workflow type from scenario metadata
        workflow_type = scenario.metadata.get('workflow_type')
        if not workflow_type:
            raise ValueError(f"Scenario {scenario.name} is not configured for workflow benchmarking")

        # Prepare mock tools if specified in scenario metadata
        mock_tools = await self._prepare_mock_tools(scenario)

        # Execute the workflow benchmark
        try:
            result = await self._run_workflow_benchmark(
                scenario=scenario,
                workflow_type=workflow_type,
                mock_tools=mock_tools,
                runs=params['runs'],
                warmup_runs=params['warmup_runs'],
                progress_callback=progress_callback
            )
        except Exception as e:
            logger.error(f"Error executing workflow benchmark: {str(e)}", exc_info=True)
            result = BenchmarkResult(
                workflow_type=workflow_type,
                scenario_name=scenario.name,
                errors=[f"Benchmark execution error: {str(e)}"]
            )

        # Store results
        benchmark_run = await self._store_results(
            scenario=scenario,
            result=result,
            params=params,
            user_profile_id=None
        )

        return benchmark_run

    async def execute_benchmark(self,
                               scenario_id: uuid.UUID,
                               params: Dict[str, Any] = None,
                               progress_callback: Optional[Callable] = None,
                               user_profile_id: Optional[str] = None) -> BenchmarkRun:
        """
        Execute a workflow benchmark based on a stored scenario.

        Args:
            scenario_id: ID of the benchmark scenario
            params: Optional parameters for the benchmark
            progress_callback: Optional callback for progress reporting
            user_profile_id: Optional ID of the user initiating the benchmark

        Returns:
            BenchmarkRun: The created benchmark run record
        """
        lock_key = f"workflow_benchmark_lock_scenario_{scenario_id}"
        lock_acquired = False
        lock_timeout = 60 * 10  # 10 minutes timeout
        benchmark_execution_error = None
        result_obj = None

        try:
            # Start overall timing
            await self.stage_timer.start_stage('total_execution')

            # Set up benchmark parameters
            benchmark_params = {
                'runs': 3,
                'warmup_runs': 1,
                'semantic_evaluation': False,
                **(params or {})
            }

            # Load the scenario
            await self.stage_timer.start_stage('load_scenario')
            scenario = await self._get_scenario_async(scenario_id)
            if not scenario:
                raise ValueError(f"Benchmark scenario with ID {scenario_id} not found")

            # Validate the scenario is for workflow benchmarking
            if not scenario.metadata.get('workflow_type'):
                raise ValueError(f"Scenario {scenario.name} is not configured for workflow benchmarking")

            # Validate the scenario against the workflow benchmark schema
            if benchmark_params.get('validate_schema', True):
                await self.stage_timer.start_stage('validate_schema')
                validation_result = await self._validate_workflow_scenario(scenario)
                if not validation_result['valid']:
                    error_message = f"Schema validation failed for scenario {scenario.name}: {', '.join(validation_result['errors'])}"
                    logger.warning(error_message)

                    # If strict validation is enabled, raise an error
                    if benchmark_params.get('strict_validation', False):
                        raise ValueError(error_message)
                await self.stage_timer.end_stage('validate_schema')

            workflow_type = scenario.metadata.get('workflow_type')
            await self.stage_timer.end_stage('load_scenario')

            # Report progress
            if progress_callback:
                progress_callback(state='PROGRESS', meta={
                    'current': 10,
                    'total': 100,
                    'status': f"Loaded scenario: {scenario.name}"
                })

            # Prepare mock tools if specified in scenario metadata
            await self.stage_timer.start_stage('prepare_mocks')
            mock_tools = await self._prepare_mock_tools(scenario)
            await self.stage_timer.end_stage('prepare_mocks')

            # Report progress
            if progress_callback:
                progress_callback(state='PROGRESS', meta={
                    'current': 20,
                    'total': 100,
                    'status': f"Prepared mock tools for scenario: {scenario.name}"
                })

            # Execute the workflow benchmark
            await self.stage_timer.start_stage('run_workflow_benchmark')
            try:
                result = await self._run_workflow_benchmark(
                    scenario=scenario,
                    workflow_type=workflow_type,
                    mock_tools=mock_tools,
                    runs=benchmark_params['runs'],
                    warmup_runs=benchmark_params['warmup_runs'],
                    progress_callback=progress_callback
                )
                result_obj = result  # Store successful result
            except Exception as e:
                logger.error(f"Error executing workflow benchmark: {str(e)}", exc_info=True)
                benchmark_execution_error = e
                result_obj = BenchmarkResult(
                    workflow_type=workflow_type,
                    scenario_name=scenario.name,
                    errors=[f"Benchmark execution error: {str(e)}"]
                )
            await self.stage_timer.end_stage('run_workflow_benchmark')

            # Report progress
            if progress_callback:
                progress_callback(state='PROGRESS', meta={
                    'current': 60,
                    'total': 100,
                    'status': f"Completed workflow benchmark execution for: {scenario.name}"
                })

            # Perform semantic evaluation if requested
            if benchmark_params.get('semantic_evaluation', False) and result_obj and not benchmark_execution_error:
                if progress_callback:
                    progress_callback(state='PROGRESS', meta={
                        'current': 70,
                        'total': 100,
                        'status': f"Performing semantic evaluation for: {scenario.name}"
                    })

                await self._evaluate_semantic_quality(
                    scenario=scenario,
                    result=result_obj,
                    user_profile_id=user_profile_id
                )

                if progress_callback:
                    progress_callback(state='PROGRESS', meta={
                        'current': 80,
                        'total': 100,
                        'status': f"Completed semantic evaluation for: {scenario.name}"
                    })
            else:
                # Report progress
                if progress_callback:
                    progress_callback(state='PROGRESS', meta={
                        'current': 80,
                        'total': 100,
                        'status': f"Skipped semantic evaluation for: {scenario.name}"
                    })

            # Store results
            await self.stage_timer.start_stage('store_results')
            benchmark_run = await self._store_results(
                scenario=scenario,
                result=result_obj,
                params=benchmark_params,
                user_profile_id=user_profile_id
            )
            await self.stage_timer.end_stage('store_results')

            # End overall timing
            await self.stage_timer.end_stage('total_execution')

            # Report progress
            if progress_callback:
                progress_callback(state='SUCCESS', meta={
                    'current': 100,
                    'total': 100,
                    'status': f"Completed workflow benchmark for: {scenario.name}",
                    'result': result_obj.to_dict() if result_obj else None
                })

            # Re-raise error if any
            if benchmark_execution_error:
                raise benchmark_execution_error

            return benchmark_run

        except Exception as e:
            logger.error(f"Error in execute_benchmark: {str(e)}", exc_info=True)

            # Report error via progress callback if available
            if progress_callback:
                progress_callback(state='FAILURE', meta={
                    'status': f"Error executing workflow benchmark: {str(e)}",
                    'error': str(e)
                })

            # Report error via EventService if user_profile_id is available
            if user_profile_id:
                await EventService.emit_debug_info(
                    event_type="workflow_benchmark_error",
                    data={
                        "error": str(e),
                        "scenario_id": str(scenario_id),
                        "params": params
                    },
                    user_profile_id=user_profile_id
                )

            # Re-raise the exception
            raise

    async def _get_scenario_async(self, scenario_id: uuid.UUID) -> Optional[BenchmarkScenario]:
        """
        Get a benchmark scenario by ID.

        Args:
            scenario_id: ID of the scenario

        Returns:
            Optional[BenchmarkScenario]: The scenario if found, None otherwise
        """
        try:
            return await sync_to_async(
                BenchmarkScenario.objects.get,
                thread_sensitive=True
            )(id=scenario_id)
        except BenchmarkScenario.DoesNotExist:
            return None

    async def _get_or_create_agent_def_async(self, agent_role: str) -> GenericAgent:
        """
        Get or create a GenericAgent for the given role.

        Args:
            agent_role: The role of the agent to get or create

        Returns:
            GenericAgent: The agent definition
        """
        # Import here to avoid circular imports
        from apps.main.models import GenericAgent
        import os

        # Check if we're in a test environment
        is_test = 'PYTEST_CURRENT_TEST' in os.environ

        # Create a function to get or create the agent
        @sync_to_async(thread_sensitive=True)
        def get_or_create_agent():
            # First try to get an existing agent
            existing_agent = GenericAgent.objects.filter(role=agent_role).first()
            if existing_agent:
                return existing_agent

            # If we're in a test environment, try to get any agent to avoid creating new ones
            if is_test:
                # Try to get any agent to use as a placeholder
                any_agent = GenericAgent.objects.first()
                if any_agent:
                    logger.info(f"Using existing agent {any_agent.role} as placeholder for {agent_role} in test environment")
                    return any_agent

            # If we're not in a test or couldn't find any agent, create a new one
            agent_def, created = GenericAgent.objects.get_or_create(
                role=agent_role,
                defaults={
                    'description': f'Agent definition for {agent_role}',
                    'system_instructions': 'Default instructions for workflow benchmarking.',
                    'input_schema': {},
                    'output_schema': {},
                    'state_schema': {},
                    'memory_schema': {},
                    'langgraph_node_class': 'apps.main.testing.mock_workflow.MockWorkflow',
                    'version': '1.0.0',
                    'is_active': True
                }
            )
            if created:
                logger.info(f"Created default GenericAgent for role: {agent_role}")
            return agent_def

        # Get or create the agent
        # Handle the case when get_or_create_agent is mocked and returns a coroutine
        result = get_or_create_agent()
        if hasattr(result, "__await__"):
            # If it's a coroutine, await it
            return await result
        # Otherwise, it's already the result (happens when mocked)
        return result

    async def _prepare_mock_tools(self, scenario: BenchmarkScenario) -> MockToolRegistry:
        """
        Prepare mock tools based on scenario metadata.

        Args:
            scenario: The benchmark scenario

        Returns:
            MockToolRegistry: Configured mock tool registry
        """
        # Initialize empty tool responses dictionary
        processed_tool_responses = {}
        tool_assertions = {}

        # Check if scenario metadata contains mock_tool_responses
        if 'mock_tool_responses' in scenario.metadata and isinstance(scenario.metadata['mock_tool_responses'], dict):
            mock_tool_responses = scenario.metadata['mock_tool_responses']

            # Process each tool's mock response configuration
            for tool_code, config_value in mock_tool_responses.items():
                # Create an async function to evaluate the template or condition
                async def create_response_func(config):
                    if isinstance(config, str):
                        # Simple template string
                        template = config

                        async def response_func(tool_input, call_count):
                            try:
                                # Check if the template is a JSON string
                                if isinstance(template, str) and (template.startswith('{') or template.startswith('[')):
                                    # For JSON strings, replace placeholders directly
                                    formatted = template.replace('{call_count}', str(call_count))
                                    # Replace tool_input placeholders if needed
                                    if '{tool_input}' in formatted:
                                        import json
                                        formatted = formatted.replace('{tool_input}', json.dumps(tool_input))
                                else:
                                    # For non-JSON strings, use format as before
                                    formatted = template.format(
                                        tool_input=tool_input,
                                        call_count=call_count
                                    )

                                # Check if this is an exception simulation
                                if isinstance(formatted, str) and '"$raise_exception"' in formatted:
                                    import json
                                    error_data = json.loads(formatted)
                                    if "$raise_exception" in error_data:
                                        from apps.main.agents.exceptions import SimulatedToolException
                                        raise SimulatedToolException(error_data["$raise_exception"])

                                return formatted
                            except Exception as e:
                                logger.error(f"Error formatting tool response template: {str(e)}", exc_info=True)
                                return f"Error: {str(e)}"

                        return response_func

                    elif isinstance(config, dict):
                        # Dictionary with response and assertions
                        template = config.get("response", "{}")
                        assertions = config.get("assertions", [])
                        delay = config.get("delay", 0)

                        # Store assertions for this tool
                        if assertions:
                            tool_assertions[tool_code] = assertions

                        async def response_func(tool_input, call_count):
                            try:
                                # Simulate delay if specified
                                if delay > 0:
                                    await asyncio.sleep(delay)

                                # Check if the template is a JSON string
                                if isinstance(template, str) and (template.startswith('{') or template.startswith('[')):
                                    # For JSON strings, replace placeholders directly
                                    formatted = template.replace('{call_count}', str(call_count))
                                    # Replace tool_input placeholders if needed
                                    if '{tool_input}' in formatted:
                                        import json
                                        formatted = formatted.replace('{tool_input}', json.dumps(tool_input))
                                else:
                                    # For non-JSON strings, use format as before
                                    formatted = template.format(
                                        tool_input=tool_input,
                                        call_count=call_count
                                    )

                                # Check if this is an exception simulation
                                if isinstance(formatted, str) and '"$raise_exception"' in formatted:
                                    import json
                                    error_data = json.loads(formatted)
                                    if "$raise_exception" in error_data:
                                        from apps.main.agents.exceptions import SimulatedToolException
                                        raise SimulatedToolException(error_data["$raise_exception"])

                                return formatted
                            except Exception as e:
                                logger.error(f"Error formatting tool response template: {str(e)}", exc_info=True)
                                return f"Error: {str(e)}"

                        return response_func

                    elif isinstance(config, list):
                        # List of condition-response pairs
                        conditions = config

                        async def response_func(tool_input, call_count):
                            try:
                                # Evaluate each condition in order
                                for condition_entry in conditions:
                                    condition = condition_entry.get("condition", "False")
                                    response_template = condition_entry.get("response", "{}")
                                    delay = condition_entry.get("delay", 0)
                                    assertions = condition_entry.get("assertions", [])

                                    # Store assertions for this condition
                                    if assertions:
                                        if tool_code not in tool_assertions:
                                            tool_assertions[tool_code] = []
                                        tool_assertions[tool_code].extend(assertions)

                                    # Evaluate the condition
                                    condition_result = eval(
                                        condition,
                                        {"__builtins__": {}},  # No built-ins for security
                                        {
                                            "tool_input": tool_input,
                                            "call_count": call_count,
                                            "get": lambda d, k, default=None: d.get(k, default) if isinstance(d, dict) else default,
                                            "contains": lambda d, k: k in d if isinstance(d, dict) else False,
                                            "matches": lambda s, pattern: re.search(pattern, s) is not None if isinstance(s, str) else False
                                        }
                                    )

                                    if condition_result:
                                        # Simulate delay if specified
                                        if delay > 0:
                                            await asyncio.sleep(delay)

                                        # Check if the template is a JSON string
                                        if isinstance(response_template, str) and (response_template.startswith('{') or response_template.startswith('[')):
                                            # For JSON strings, replace placeholders directly
                                            formatted = response_template.replace('{call_count}', str(call_count))
                                            # Replace tool_input placeholders if needed
                                            if '{tool_input}' in formatted:
                                                import json
                                                formatted = formatted.replace('{tool_input}', json.dumps(tool_input))
                                        else:
                                            # For non-JSON strings, use format as before
                                            formatted = response_template.format(
                                                tool_input=tool_input,
                                                call_count=call_count
                                            )

                                        # Check if this is an exception simulation
                                        if isinstance(formatted, str) and '"$raise_exception"' in formatted:
                                            import json
                                            error_data = json.loads(formatted)
                                            if "$raise_exception" in error_data:
                                                from apps.main.agents.exceptions import SimulatedToolException
                                                raise SimulatedToolException(error_data["$raise_exception"])

                                        return formatted

                                # No condition matched
                                return "{}"
                            except Exception as e:
                                logger.error(f"Error evaluating tool response condition: {str(e)}", exc_info=True)
                                return f"Error: {str(e)}"

                        return response_func

                    else:
                        # Unsupported configuration type
                        async def default_response_func(tool_input, call_count):
                            return "{}"

                        return default_response_func

                # Create and store the response function
                processed_tool_responses[tool_code] = await create_response_func(config_value)

        # Check if scenario metadata contains tool_assertions
        if 'tool_assertions' in scenario.metadata and isinstance(scenario.metadata['tool_assertions'], dict):
            # Merge with any assertions from mock_tool_responses
            scenario_assertions = scenario.metadata['tool_assertions']
            for tool_code, assertions in scenario_assertions.items():
                if tool_code in tool_assertions:
                    tool_assertions[tool_code].extend(assertions)
                else:
                    tool_assertions[tool_code] = assertions

        # Create and configure MockToolRegistry
        mock_tool_config = {
            "tool_responses": processed_tool_responses,
            "tool_assertions": tool_assertions
        }

        # Configure validation options if specified
        if 'mock_tool_validation' in scenario.metadata:
            validation_config = scenario.metadata['mock_tool_validation']
            mock_tool_config["validate_schemas"] = validation_config.get("validate_schemas", False)
            mock_tool_config["validate_params"] = validation_config.get("validate_params", False)
            mock_tool_config["strict_mode"] = validation_config.get("strict_mode", False)

        return MockToolRegistry(config=mock_tool_config)

    async def _evaluate_semantic_quality(self,
                                  scenario: BenchmarkScenario,
                                  result: BenchmarkResult,
                                  user_profile_id: Optional[str] = None) -> None:
        """
        Evaluate the semantic quality of the agent's response.

        Args:
            scenario: The benchmark scenario
            result: The benchmark results to update with semantic evaluation
            user_profile_id: Optional ID of the user initiating the benchmark
        """
        # Check if we have a response to evaluate
        if not result.last_output_data:
            logger.warning(f"Semantic evaluation skipped: No agent response available for scenario {scenario.name}")
            return

        # Extract the response text from the last output
        response_text = self._extract_response_text(result.last_output_data)
        if not response_text:
            logger.warning(f"Semantic evaluation skipped: Could not extract response text from output for scenario {scenario.name}")
            return

        # Check if scenario has evaluation criteria (either phase-aware or flat)
        has_criteria = False
        if scenario.metadata.get('expected_quality_criteria'):
            has_criteria = True
        elif scenario.metadata.get('evaluation_criteria_by_phase'):
            has_criteria = True

        if not has_criteria:
            logger.warning(f"Semantic evaluation skipped: No evaluation criteria defined for scenario {scenario.name}")
            return

        # Get evaluator models from scenario metadata or use default
        evaluator_models = scenario.metadata.get('evaluator_models', None)

        # Get trust level from input data if available
        trust_level = None
        if hasattr(scenario, 'input_data') and scenario.input_data:
            context_packet = scenario.input_data.get('context_packet', {})
            if isinstance(context_packet, dict):
                trust_level = context_packet.get('trust_level')

        if trust_level is not None:
            logger.info(f"Using trust level {trust_level} for semantic evaluation of scenario {scenario.name}")

        try:
            # Start timing semantic evaluation
            await self.stage_timer.start_stage('semantic_evaluation')

            # Get scenario context
            scenario_context = self._get_scenario_context(scenario)

            # Determine which criteria structure to use
            criteria_data = None

            # Check for contextual template first
            template_id = scenario.metadata.get('evaluation_template_id')
            template_name = scenario.metadata.get('evaluation_template_name')
            context = scenario.metadata.get('context')

            if template_id or template_name:
                # Load contextual template
                evaluation_template = await self._get_evaluation_template_async(template_id or template_name)
                if evaluation_template:
                    if evaluation_template.get('contextual_criteria') and context:
                        logger.info(f"Using contextual template with context: {context}")
                        # Adapt criteria based on context
                        criteria_data = self._adapt_criteria_for_context(evaluation_template, context)
                    else:
                        # Use base criteria from template
                        criteria_data = evaluation_template['criteria']
                        logger.info(f"Using base criteria from template.")
                else:
                    logger.warning(f"Evaluation template '{template_id or template_name}' not found.")

            # Fallback to existing logic if no template or template not found
            if criteria_data is None:
                if 'evaluation_criteria_by_phase' in scenario.metadata:
                    criteria_data = scenario.metadata
                else:
                    criteria_data = scenario.metadata.get('expected_quality_criteria', {})

            # Evaluate the response
            evaluation_results = await self.semantic_evaluator.evaluate_response(
                scenario_context=scenario_context,
                agent_response=response_text,
                criteria=criteria_data,
                evaluator_models=evaluator_models,
                user_profile_id=user_profile_id,
                trust_level=trust_level
            )

            # Store the results in the benchmark result
            result.semantic_evaluations = evaluation_results

            # Get the primary model results
            primary_model = evaluation_results.get('_meta', {}).get('primary_model')
            if primary_model and primary_model in evaluation_results:
                primary_results = evaluation_results[primary_model]
                if not primary_results.get('error', False):
                    result.semantic_score = primary_results.get('overall_score', 0.0)
                    result.semantic_evaluation_details = primary_results.get('overall_reasoning', '')

            # End timing semantic evaluation
            await self.stage_timer.end_stage('semantic_evaluation')

        except Exception as e:
            logger.error(f"Error evaluating semantic quality: {str(e)}", exc_info=True)
            result.errors.append(f"Semantic evaluation error: {str(e)}")

            # Report error via EventService if user_profile_id is available
            if user_profile_id:
                await EventService.emit_debug_info(
                    event_type="semantic_evaluation_error",
                    data={
                        "error": str(e),
                        "scenario_id": str(scenario.id),
                        "scenario_name": scenario.name
                    },
                    user_profile_id=user_profile_id
                )

    def _extract_response_text(self, output_data: Dict[str, Any]) -> str:
        """
        Extract response text from the agent's output data.

        Args:
            output_data: The agent's output data

        Returns:
            str: The extracted response text
        """
        # Try common response fields
        for field in ['response', 'response_text', 'message', 'text', 'content', 'answer']:
            if field in output_data and isinstance(output_data[field], str):
                return output_data[field]

        # Try nested fields
        for field in ['response', 'message', 'output']:
            if field in output_data and isinstance(output_data[field], dict):
                nested = output_data[field]
                for nested_field in ['text', 'content', 'message']:
                    if nested_field in nested and isinstance(nested[nested_field], str):
                        return nested[nested_field]

        # If we have a string value directly
        if isinstance(output_data, str):
            return output_data

        # Last resort: convert the whole output to string
        return str(output_data)

    def _get_scenario_context(self, scenario: BenchmarkScenario) -> str:
        """
        Get the context of a benchmark scenario for semantic evaluation.

        Args:
            scenario: The benchmark scenario

        Returns:
            str: The scenario context
        """
        context_parts = []

        # Add scenario name and description
        context_parts.append(f"Scenario: {scenario.name}")
        if scenario.description:
            context_parts.append(f"Description: {scenario.description}")

        # Add workflow type
        workflow_type = scenario.metadata.get('workflow_type', '')
        if workflow_type:
            context_parts.append(f"Workflow Type: {workflow_type}")

        # Add input data summary
        if scenario.input_data:
            context_parts.append("Input Data:")
            if isinstance(scenario.input_data, dict):
                for key, value in scenario.input_data.items():
                    if isinstance(value, (str, int, float, bool)):
                        context_parts.append(f"  {key}: {value}")
                    else:
                        context_parts.append(f"  {key}: <complex data>")
            else:
                context_parts.append(f"  {str(scenario.input_data)[:100]}...")

        # Add user profile context if available
        if scenario.metadata.get('user_profile_context'):
            context_parts.append("User Profile Context:")
            user_profile = scenario.metadata['user_profile_context']
            if isinstance(user_profile, dict):
                for key, value in user_profile.items():
                    if isinstance(value, (str, int, float, bool)):
                        context_parts.append(f"  {key}: {value}")
                    elif isinstance(value, dict):
                        context_parts.append(f"  {key}:")
                        for subkey, subvalue in value.items():
                            if isinstance(subvalue, (str, int, float, bool)):
                                context_parts.append(f"    {subkey}: {subvalue}")
            else:
                context_parts.append(f"  {str(user_profile)[:100]}...")

        return "\n".join(context_parts)

    @sync_to_async
    def _get_evaluation_template_sync(self, template_identifier: Union[str, int]) -> Optional[Dict[str, Any]]:
        """
        Synchronous helper to fetch an EvaluationCriteriaTemplate by name or ID.
        Returns a dictionary representation of the template.

        Args:
            template_identifier: Either template name (str) or template ID (int)
        """
        from apps.main.models import EvaluationCriteriaTemplate
        try:
            if isinstance(template_identifier, int):
                template = EvaluationCriteriaTemplate.objects.get(id=template_identifier)
                logger.debug(f"Found evaluation template by ID: {template_identifier}")
            else:
                template = EvaluationCriteriaTemplate.objects.get(name=template_identifier)
                logger.debug(f"Found evaluation template by name: {template_identifier}")

            # Convert the template to a dictionary
            template_dict = {
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'criteria': template.criteria,
                'contextual_criteria': template.contextual_criteria,
                'variable_ranges': template.variable_ranges,
                'workflow_type': template.workflow_type,
                'category': template.category,
                'is_active': template.is_active
            }
            return template_dict
        except EvaluationCriteriaTemplate.DoesNotExist:
            logger.warning(f"EvaluationCriteriaTemplate with identifier '{template_identifier}' not found.")
            return None
        except Exception as e:
            logger.error(f"Error fetching EvaluationCriteriaTemplate '{template_identifier}': {e}", exc_info=True)
            return None

    async def _get_evaluation_template_async(self, template_identifier: Union[str, int]) -> Optional[Dict[str, Any]]:
        """
        Async wrapper for getting evaluation template.

        Args:
            template_identifier: Either template name (str) or template ID (int)
        """
        return await self._get_evaluation_template_sync(template_identifier)

    def _adapt_criteria_for_context(self, template_dict: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adapt evaluation criteria based on contextual variables.

        Args:
            template_dict: Dictionary representation of EvaluationCriteriaTemplate
            context: Dictionary containing contextual variables

        Returns:
            Dictionary with adapted criteria
        """
        # Start with base criteria
        adapted_criteria = template_dict['criteria'].copy()
        contextual_criteria = template_dict.get('contextual_criteria', {})

        if not contextual_criteria or not context:
            return adapted_criteria

        # Apply trust level adaptations
        trust_level = context.get('trust_level')
        if trust_level is not None and 'trust_level' in contextual_criteria:
            trust_adaptations = self._get_range_adaptation(
                contextual_criteria['trust_level'], trust_level
            )
            adapted_criteria = self._merge_criteria(adapted_criteria, trust_adaptations)

        # Apply mood adaptations
        mood = context.get('mood', {})
        if mood and 'mood' in contextual_criteria:
            valence = mood.get('valence')
            arousal = mood.get('arousal')

            if valence is not None and 'valence' in contextual_criteria['mood']:
                valence_adaptations = self._get_range_adaptation(
                    contextual_criteria['mood']['valence'], valence
                )
                adapted_criteria = self._merge_criteria(adapted_criteria, valence_adaptations)

            if arousal is not None and 'arousal' in contextual_criteria['mood']:
                arousal_adaptations = self._get_range_adaptation(
                    contextual_criteria['mood']['arousal'], arousal
                )
                adapted_criteria = self._merge_criteria(adapted_criteria, arousal_adaptations)

        # Apply environment adaptations
        environment = context.get('environment', {})
        if environment and 'environment' in contextual_criteria:
            stress_level = environment.get('stress_level')
            time_pressure = environment.get('time_pressure')

            if stress_level is not None and 'stress_level' in contextual_criteria['environment']:
                stress_adaptations = self._get_range_adaptation(
                    contextual_criteria['environment']['stress_level'], stress_level
                )
                adapted_criteria = self._merge_criteria(adapted_criteria, stress_adaptations)

            if time_pressure is not None and 'time_pressure' in contextual_criteria['environment']:
                pressure_adaptations = self._get_range_adaptation(
                    contextual_criteria['environment']['time_pressure'], time_pressure
                )
                adapted_criteria = self._merge_criteria(adapted_criteria, pressure_adaptations)

        return adapted_criteria

    def _get_range_adaptation(self, range_criteria: Dict[str, Any], value: Union[int, float]) -> Dict[str, Any]:
        """
        Get criteria adaptations for a specific value within defined ranges.

        Args:
            range_criteria: Dictionary mapping ranges to criteria adaptations
            value: The value to find adaptations for

        Returns:
            Dictionary with criteria adaptations for the value's range
        """
        # Type safety check: ensure value is numeric
        if not isinstance(value, (int, float)):
            logger.warning(f"Expected numeric value for range adaptation, got {type(value)}: {value}")
            return {}

        for range_str, adaptations in range_criteria.items():
            if self._value_in_range(value, range_str):
                return adaptations
        return {}

    def _value_in_range(self, value: Union[int, float], range_str: str) -> bool:
        """
        Check if a value falls within a specified range string.

        Args:
            value: The value to check
            range_str: Range string like "0-39", "-1.0-0.0", etc.

        Returns:
            True if value is in range, False otherwise
        """
        try:
            # Type safety check: ensure value is numeric
            if not isinstance(value, (int, float)):
                logger.warning(f"Expected numeric value for range check, got {type(value)}: {value}")
                return False

            if '-' not in range_str:
                return False

            # Handle negative ranges like "-1.0-0.0"
            if range_str.startswith('-'):
                # Find the second dash
                second_dash = range_str.find('-', 1)
                if second_dash == -1:
                    return False
                min_val = float(range_str[:second_dash])
                max_val = float(range_str[second_dash + 1:])
            else:
                parts = range_str.split('-')
                if len(parts) != 2:
                    return False
                min_val = float(parts[0])
                max_val = float(parts[1])

            return min_val <= value <= max_val
        except (ValueError, IndexError):
            logger.warning(f"Invalid range format: {range_str}")
            return False
        except TypeError as e:
            logger.warning(f"Type error in range comparison: {e}. Value: {value}, Range: {range_str}")
            return False

    def _merge_criteria(self, base_criteria: Dict[str, Any], adaptations: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge base criteria with contextual adaptations.

        Args:
            base_criteria: Base evaluation criteria
            adaptations: Contextual adaptations to merge

        Returns:
            Merged criteria dictionary
        """
        merged = base_criteria.copy()

        for dimension, criteria_list in adaptations.items():
            if dimension in merged:
                # Extend existing dimension criteria
                if isinstance(merged[dimension], list) and isinstance(criteria_list, list):
                    merged[dimension].extend(criteria_list)
                else:
                    merged[dimension] = criteria_list
            else:
                # Add new dimension
                merged[dimension] = criteria_list

        return merged

    async def _run_workflow_benchmark(self,
                                     scenario: BenchmarkScenario,
                                     workflow_type: str,
                                     mock_tools: MockToolRegistry,
                                     runs: int = 3,
                                     warmup_runs: int = 1,
                                     progress_callback: Optional[Callable] = None) -> BenchmarkResult:
        """
        Run a workflow benchmark.

        This method should be implemented by subclasses to execute the specific workflow type.

        Args:
            scenario: The benchmark scenario
            workflow_type: Type of workflow to benchmark
            mock_tools: Configured mock tool registry
            runs: Number of benchmark runs
            warmup_runs: Number of warmup runs
            progress_callback: Optional callback for progress reporting

        Returns:
            BenchmarkResult: The benchmark results
        """
        raise NotImplementedError("Subclasses must implement _run_workflow_benchmark")

    async def _store_results(self,
                            scenario: BenchmarkScenario,
                            result: BenchmarkResult,
                            params: Dict[str, Any],
                            user_profile_id: Optional[str] = None) -> BenchmarkRun:
        """
        Store benchmark results in the database.

        Args:
            scenario: The benchmark scenario
            result: The benchmark results
            params: Parameters used for the benchmark
            user_profile_id: Optional ID of the user who initiated the benchmark

        Returns:
            BenchmarkRun: The created benchmark run record
        """
        # Get or create a default agent definition for the workflow role
        agent_def = await self._get_or_create_agent_def_async("workflow")
        # Convert durations from seconds to milliseconds for storage
        mean_duration_ms = result.mean_duration * 1000
        median_duration_ms = result.median_duration * 1000
        min_duration_ms = result.min_duration * 1000
        max_duration_ms = result.max_duration * 1000
        std_dev_ms = result.std_dev * 1000

        # Keep success rate as a fraction between 0 and 1
        success_rate = result.success_rate if result.success_rate <= 1 else result.success_rate / 100

        # Get tool call counts and breakdown
        tool_calls = sum(result.tool_call_counts.values())
        tool_breakdown = result.tool_call_counts

        # Calculate cost if model prices are provided in params
        cost_report = {}
        if params.get('model_prices'):
            # If token tracker is available, use it to calculate costs
            if self.token_tracker:
                # Calculate total cost
                total_cost = await self.token_tracker.calculate_cost(
                    input_price=params.get('model_prices', {}).get('default', {}).get('input', 0.001),
                    output_price=params.get('model_prices', {}).get('default', {}).get('output', 0.002)
                )

                # Calculate cost by model
                model_costs = await self.token_tracker.calculate_cost_by_model(params.get('model_prices', {}))

                # Calculate cost by stage
                stage_costs = await self.token_tracker.calculate_cost_by_stage(
                    input_price=params.get('model_prices', {}).get('default', {}).get('input', 0.001),
                    output_price=params.get('model_prices', {}).get('default', {}).get('output', 0.002)
                )

                # Create cost report
                cost_report = {
                    'total_cost': total_cost,
                    'by_model': model_costs,
                    'by_stage': stage_costs
                }

                # Update result with cost report
                result.cost_report = cost_report
            else:
                # Calculate simple cost based on total tokens
                input_price = params.get('model_prices', {}).get('default', {}).get('input', 0.001)
                output_price = params.get('model_prices', {}).get('default', {}).get('output', 0.002)
                total_cost = (result.total_input_tokens * input_price) + (result.total_output_tokens * output_price)
                cost_report = {
                    'total_cost': total_cost
                }
                result.cost_report = cost_report

        # Get token usage report
        token_usage_report = {}
        if self.token_tracker:
            token_usage_report = self.token_tracker.get_token_usage_report()
            result.token_usage_report = token_usage_report
            result.token_usage_by_stage = token_usage_report.get('by_stage', {})
            result.token_usage_by_model = token_usage_report.get('by_model', {})

        # Create benchmark run record
        try:
            # Use sync_to_async to wrap the database operation
            create_benchmark_run = sync_to_async(self._create_benchmark_run_sync, thread_sensitive=True)
            benchmark_run = await create_benchmark_run(
                scenario=scenario,
                agent_definition=agent_def,  # Pass the agent_def we got or created
                mean_duration_ms=mean_duration_ms,
                median_duration_ms=median_duration_ms,
                min_duration_ms=min_duration_ms,
                max_duration_ms=max_duration_ms,
                std_dev_ms=std_dev_ms,
                success_rate=success_rate,
                tool_calls=tool_calls,
                tool_breakdown=tool_breakdown,
                total_input_tokens=result.total_input_tokens,
                total_output_tokens=result.total_output_tokens,
                raw_results=result.to_dict(),
                parameters=params,
                runs_count=params.get('runs', 1),
                stage_performance_details={
                    stage: {
                        'mean_ms': sum(times) / len(times) * 1000 if times else 0,
                        'count': len(times)
                    } for stage, times in result.stage_timings.items()
                }
            )

            return benchmark_run
        except Exception as e:
            logger.error(f"Error storing benchmark results: {str(e)}", exc_info=True)

            # Report error via EventService if user_profile_id is available
            if user_profile_id:
                await EventService.emit_debug_info(
                    event_type="workflow_benchmark_error",
                    data={
                        "error": f"Error storing benchmark results: {str(e)}",
                        "scenario_name": scenario.name
                    },
                    user_profile_id=user_profile_id
                )

            raise

    def _create_benchmark_run_sync(self,
                                  scenario: BenchmarkScenario,
                                  agent_definition: Optional[GenericAgent] = None,
                                  mean_duration_ms: float = 0.0,
                                  median_duration_ms: float = 0.0,
                                  min_duration_ms: float = 0.0,
                                  max_duration_ms: float = 0.0,
                                  std_dev_ms: float = 0.0,
                                  success_rate: float = 0.0,
                                  tool_calls: int = 0,
                                  tool_breakdown: Optional[Dict[str, int]] = None,
                                  total_input_tokens: int = 0,
                                  total_output_tokens: int = 0,
                                  raw_results: Optional[Dict[str, Any]] = None,
                                  parameters: Optional[Dict[str, Any]] = None,
                                  runs_count: int = 0,
                                  stage_performance_details: Optional[Dict[str, Dict[str, Any]]] = None) -> BenchmarkRun:
        """
        Create a benchmark run record in the database (synchronous version).

        This method should be called via sync_to_async.

        Args:
            scenario: The benchmark scenario
            mean_duration_ms: Mean execution time in milliseconds
            median_duration_ms: Median execution time in milliseconds
            min_duration_ms: Minimum execution time in milliseconds
            max_duration_ms: Maximum execution time in milliseconds
            std_dev_ms: Standard deviation of execution times in milliseconds
            success_rate: Percentage of successful runs
            tool_calls: Total number of tool calls
            tool_breakdown: Dictionary mapping tool names to call counts
            total_input_tokens: Total input tokens used
            total_output_tokens: Total output tokens used
            raw_results: Raw benchmark results
            parameters: Parameters used for the benchmark
            runs_count: Number of benchmark runs
            stage_performance_details: Performance details for each stage

        Returns:
            BenchmarkRun: The created benchmark run record
        """
        with transaction.atomic():
            # Initialize default values for optional parameters
            if tool_breakdown is None:
                tool_breakdown = {}
            if raw_results is None:
                raw_results = {}
            if parameters is None:
                parameters = {}
            if stage_performance_details is None:
                stage_performance_details = {}

            # Use the provided agent_definition if available, otherwise try to find one
            if agent_definition is None:
                import os
                is_test = 'PYTEST_CURRENT_TEST' in os.environ

                # Get the agent definition for the workflow type
                workflow_type = raw_results.get('workflow_type')
                if workflow_type:
                    try:
                        agent_definition = GenericAgent.objects.filter(
                            role=f"workflow_{workflow_type}",
                            is_active=True
                        ).first()
                    except Exception as e:
                        logger.warning(f"Could not find agent definition for workflow type {workflow_type}: {str(e)}")

                # If still None, try to get a default workflow agent
                if agent_definition is None:
                    try:
                        agent_definition = GenericAgent.objects.filter(
                            role="workflow",
                            is_active=True
                        ).first()
                    except Exception as e:
                        logger.warning(f"Could not find default workflow agent definition: {str(e)}")

                # If still None and we're in a test environment, use any agent to avoid creating new ones
                if agent_definition is None and is_test:
                    try:
                        agent_definition = GenericAgent.objects.first()
                        if agent_definition:
                            logger.info(f"Using existing agent {agent_definition.role} as placeholder for workflow in test environment")
                    except Exception as e:
                        logger.warning(f"Could not find any agent definition to use as placeholder: {str(e)}")

                # If still None and not in a test environment, create a default agent definition
                if agent_definition is None and not is_test:
                    try:
                        agent_definition, created = GenericAgent.objects.get_or_create(
                            role="workflow",
                            defaults={
                                'description': 'Default workflow agent definition',
                                'system_instructions': 'Default instructions for workflow benchmarking.',
                                'input_schema': {},
                                'output_schema': {},
                                'state_schema': {},
                                'memory_schema': {},
                                'langgraph_node_class': 'apps.main.testing.mock_workflow.MockWorkflow',
                                'version': '1.0.0',
                                'is_active': True
                            }
                        )
                        if created:
                            logger.info("Created default GenericAgent for role 'workflow'")
                    except Exception as e:
                        logger.error(f"Failed to create default workflow agent definition: {str(e)}")
                        raise

            # Create the benchmark run record
            benchmark_run = BenchmarkRun.objects.create(
                scenario=scenario,
                agent_definition=agent_definition,
                agent_version=parameters.get('agent_version', 'unknown'),
                parameters=parameters,
                runs_count=runs_count,
                mean_duration=mean_duration_ms,
                median_duration=median_duration_ms,
                min_duration=min_duration_ms,
                max_duration=max_duration_ms,
                std_dev=std_dev_ms,
                success_rate=float(success_rate),
                llm_calls=parameters.get('llm_calls', 0),
                tool_calls=tool_calls,
                tool_breakdown=tool_breakdown,
                memory_operations=parameters.get('memory_operations', 0),
                total_input_tokens=total_input_tokens,
                total_output_tokens=total_output_tokens,
                estimated_cost=parameters.get('estimated_cost', 0.0),
                semantic_score=raw_results.get('semantic_quality', {}).get('overall_score', 0.0),
                semantic_evaluation_details=raw_results.get('semantic_quality', {}).get('evaluation_details', ''),
                semantic_evaluations=raw_results.get('semantic_quality', {}).get('evaluations', {}),
                raw_results=raw_results,
                stage_performance_details=stage_performance_details
            )

            return benchmark_run

    async def _validate_workflow_scenario(self, scenario: BenchmarkScenario) -> Dict[str, Any]:
        """
        Validate a workflow benchmark scenario against the schema.

        Args:
            scenario: BenchmarkScenario instance

        Returns:
            Dict with validation results
        """
        # Extract workflow benchmark data from scenario
        workflow_data = {
            'workflow_type': scenario.metadata.get('workflow_type', '')
        }

        # Copy relevant fields from metadata
        for field in ['mock_tool_responses', 'mock_tool_validation', 'warmup_runs',
                     'benchmark_runs', 'expected_quality_criteria', 'evaluator_models',
                     'expected_output', 'expected_stages', 'expected_tool_calls',
                     'timeout_seconds']:
            if field in scenario.metadata:
                workflow_data[field] = scenario.metadata[field]

        # Validate against schema
        is_valid, errors = self.schema_validator.validate_workflow_benchmark(workflow_data)

        return {
            'valid': is_valid,
            'errors': errors
        }