"""
Pydantic models for workflow state validation.

This module defines Pydantic models for validating workflow state and I/O.
These models are used to ensure that workflow components adhere to their contracts.
"""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator


class ToolCallRecord(BaseModel):
    """Record of a tool call."""
    tool_name: str = Field(..., description="Name of the tool that was called")
    input_params: Dict[str, Any] = Field(default_factory=dict, description="Input parameters passed to the tool")
    output: Any = Field(None, description="Output returned by the tool")
    error: Optional[str] = Field(None, description="Error message if the tool call failed")
    duration_ms: float = Field(0.0, description="Duration of the tool call in milliseconds")


class StageMetrics(BaseModel):
    """Metrics for a workflow stage."""
    duration_ms: float = Field(0.0, description="Duration of the stage in milliseconds")
    input_tokens: int = Field(0, description="Number of input tokens used in this stage")
    output_tokens: int = Field(0, description="Number of output tokens generated in this stage")
    tool_calls: List[ToolCallRecord] = Field(default_factory=list, description="Tool calls made during this stage")


class UserProfile(BaseModel):
    """User profile information."""
    id: str = Field(..., description="User profile ID")
    name: Optional[str] = Field(None, description="User's name")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences")
    traits: Dict[str, Any] = Field(default_factory=dict, description="User traits")
    limitations: Dict[str, Any] = Field(default_factory=dict, description="User limitations")


class Activity(BaseModel):
    """Activity information."""
    id: Optional[str] = Field(None, description="Activity ID")
    name: str = Field(..., description="Activity name")
    category: str = Field(..., description="Activity category")
    description: Optional[str] = Field(None, description="Activity description")
    duration_range: Optional[str] = Field(None, description="Activity duration range")
    instructions: Optional[str] = Field(None, description="Activity instructions")


class Wheel(BaseModel):
    """Wheel information."""
    id: Optional[str] = Field(None, description="Wheel ID")
    activities: List[Activity] = Field(..., description="Activities in the wheel")
    created_at: Optional[str] = Field(None, description="Wheel creation timestamp")
    user_profile_id: Optional[str] = Field(None, description="User profile ID")


class WheelGenerationState(BaseModel):
    """State for wheel generation workflow."""
    user_profile: Optional[UserProfile] = Field(None, description="User profile information")
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list, description="Conversation history")
    wheel: Optional[Wheel] = Field(None, description="Generated wheel")
    stage: str = Field("initial", description="Current workflow stage")
    stage_metrics: Dict[str, StageMetrics] = Field(default_factory=dict, description="Metrics for each stage")
    errors: List[str] = Field(default_factory=list, description="Errors encountered during workflow execution")
    
    @validator('stage')
    def validate_stage(cls, v):
        """Validate that the stage is one of the allowed values."""
        allowed_stages = [
            "initial", 
            "profile_analysis", 
            "activity_selection", 
            "wheel_generation", 
            "wheel_storage",
            "complete", 
            "error"
        ]
        if v not in allowed_stages:
            raise ValueError(f"Stage must be one of {allowed_stages}")
        return v


class BenchmarkState(BaseModel):
    """State for benchmark execution."""
    scenario_id: str = Field(..., description="Benchmark scenario ID")
    workflow_type: str = Field(..., description="Type of workflow being benchmarked")
    current_run: int = Field(0, description="Current run number")
    total_runs: int = Field(0, description="Total number of runs")
    warmup_runs: int = Field(0, description="Number of warmup runs")
    run_results: List[Dict[str, Any]] = Field(default_factory=list, description="Results of each run")
    stage: str = Field("initial", description="Current benchmark stage")
    errors: List[str] = Field(default_factory=list, description="Errors encountered during benchmark execution")
    
    @validator('stage')
    def validate_stage(cls, v):
        """Validate that the stage is one of the allowed values."""
        allowed_stages = [
            "initial", 
            "warmup", 
            "benchmark", 
            "analysis", 
            "complete", 
            "error"
        ]
        if v not in allowed_stages:
            raise ValueError(f"Stage must be one of {allowed_stages}")
        return v


# Add more workflow state models as needed
