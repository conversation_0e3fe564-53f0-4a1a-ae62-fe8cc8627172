# ACTIVE_FILE - 29-05-2025
"""
Schema registration and validation for benchmark system components.
This module handles the loading, registration, and validation of JSON schemas
for benchmark scenarios, user profiles, evaluation criteria, and tool expectations.
"""

import os
import json
import logging
import re
import jsonschema
import semver
from typing import Dict, Any, Optional, List, Tuple, Union

logger = logging.getLogger(__name__)

class SchemaRegistry:
    """
    Central registry for JSON schemas used in the benchmarking system.
    Provides methods to load, register, and validate against schemas.
    """

    SCHEMA_TYPES = [
        "user_profile",
        "situation",
        "evaluation_criteria",
        "tool_expectation",
        "workflow_benchmark",
        "evaluation_template"
    ]

    def __init__(self, schema_dir: Optional[str] = None):
        """
        Initialize the schema registry.

        Args:
            schema_dir: Directory containing schema files (*.schema.json)
                       If None, uses 'backend/schemas/' by default
        """
        self.schemas: Dict[str, Dict[str, Any]] = {}
        self.validators: Dict[str, jsonschema.validators.Validator] = {}

        # Store versioned schemas
        self.versioned_schemas: Dict[str, Dict[str, Dict[str, Any]]] = {}
        self.default_versions: Dict[str, str] = {}

        # Set default schema directory if not provided
        if schema_dir is None:
            # Get project root directory (assuming this file is in backend/apps/main/services/)
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..'))
            schema_dir = os.path.join(project_root, 'schemas')

        self.schema_dir = schema_dir

        # Ensure schema directory exists
        os.makedirs(self.schema_dir, exist_ok=True)

        # Load built-in schemas
        self._load_built_in_schemas()

        # Load schemas from directory
        self._load_schemas_from_directory()

    def _load_built_in_schemas(self):
        """Load the built-in schemas defined in this module."""
        try:
            # Define paths for our schema files
            user_profile_schema_path = os.path.join(self.schema_dir, 'user_profile.schema.json')
            situation_schema_path = os.path.join(self.schema_dir, 'situation.schema.json')
            evaluation_criteria_schema_path = os.path.join(self.schema_dir, 'evaluation_criteria.schema.json')
            tool_expectation_schema_path = os.path.join(self.schema_dir, 'tool_expectation.schema.json')
            workflow_benchmark_schema_path = os.path.join(self.schema_dir, 'workflow_benchmark.schema.json')
            evaluation_template_schema_path = os.path.join(self.schema_dir, 'evaluation_template.schema.json')

            # Load the built-in schema text. If the files don't exist, create them.
            schemas_to_save = {}

            # User profile schema
            if not os.path.exists(user_profile_schema_path):
                with open(user_profile_schema_path, 'w') as f:
                    user_profile_schema = self._get_user_profile_schema()
                    # Add version to schema
                    user_profile_schema['$version'] = "1.0.0"
                    json.dump(user_profile_schema, f, indent=2)
                    schemas_to_save['user_profile'] = user_profile_schema

            # Situation schema
            if not os.path.exists(situation_schema_path):
                with open(situation_schema_path, 'w') as f:
                    situation_schema = self._get_situation_schema()
                    # Add version to schema
                    situation_schema['$version'] = "1.0.0"
                    json.dump(situation_schema, f, indent=2)
                    schemas_to_save['situation'] = situation_schema

            # Evaluation criteria schema
            if not os.path.exists(evaluation_criteria_schema_path):
                with open(evaluation_criteria_schema_path, 'w') as f:
                    evaluation_criteria_schema = self._get_evaluation_criteria_schema()
                    # Add version to schema
                    evaluation_criteria_schema['$version'] = "1.0.0"
                    json.dump(evaluation_criteria_schema, f, indent=2)
                    schemas_to_save['evaluation_criteria'] = evaluation_criteria_schema

            # Tool expectation schema
            if not os.path.exists(tool_expectation_schema_path):
                with open(tool_expectation_schema_path, 'w') as f:
                    tool_expectation_schema = self._get_tool_expectation_schema()
                    # Add version to schema
                    tool_expectation_schema['$version'] = "1.0.0"
                    json.dump(tool_expectation_schema, f, indent=2)
                    schemas_to_save['tool_expectation'] = tool_expectation_schema

            # Workflow benchmark schema
            if not os.path.exists(workflow_benchmark_schema_path):
                # Create the directory structure for versioned schemas
                workflow_versions_dir = os.path.join(self.schema_dir, 'workflow_benchmark', 'versions')
                os.makedirs(workflow_versions_dir, exist_ok=True)

                # Save the current version to the main directory
                with open(workflow_benchmark_schema_path, 'w') as f:
                    workflow_schema = self._get_workflow_benchmark_schema()
                    # Add version to schema
                    workflow_schema['$version'] = "1.0.0"
                    json.dump(workflow_schema, f, indent=2)
                    schemas_to_save['workflow_benchmark'] = workflow_schema

                # Also save it as a versioned schema
                versioned_path = os.path.join(workflow_versions_dir, 'v1.0.0.schema.json')
                with open(versioned_path, 'w') as f:
                    json.dump(workflow_schema, f, indent=2)

            # Load evaluation template schema if it exists
            if os.path.exists(evaluation_template_schema_path):
                try:
                    with open(evaluation_template_schema_path, 'r') as f:
                        evaluation_template_schema = json.load(f)
                        schemas_to_save['evaluation_template'] = evaluation_template_schema
                except Exception as e:
                    logger.error(f"Error loading evaluation template schema: {e}", exc_info=True)

            # Register schemas created above
            for schema_type, schema in schemas_to_save.items():
                version = self._extract_version_from_schema(schema) or "1.0.0"
                self.register_schema(schema_type, schema, version)

        except Exception as e:
            logger.error(f"Error loading built-in schemas: {e}", exc_info=True)

    def _load_schemas_from_directory(self):
        """Load schemas from the schema directory."""
        try:
            if not os.path.exists(self.schema_dir):
                logger.warning(f"Schema directory '{self.schema_dir}' does not exist.")
                return

            # First, look for versioned schema directories
            for schema_type in self.SCHEMA_TYPES:
                versioned_dir = os.path.join(self.schema_dir, schema_type, 'versions')
                if os.path.exists(versioned_dir) and os.path.isdir(versioned_dir):
                    for filename in os.listdir(versioned_dir):
                        if filename.endswith('.schema.json'):
                            schema_path = os.path.join(versioned_dir, filename)
                            # Extract version from filename (e.g., v1.0.0.schema.json -> 1.0.0)
                            version_match = re.match(r'v?(\d+\.\d+\.\d+)\.schema\.json', filename)
                            if version_match:
                                version = version_match.group(1)
                                try:
                                    with open(schema_path, 'r') as f:
                                        schema = json.load(f)
                                        # Check if schema is a list instead of a dictionary
                                        if isinstance(schema, list):
                                            logger.error(f"Schema in {schema_path} is a list, but should be an object. Skipping.")
                                            continue
                                        self.register_schema(schema_type, schema, version)
                                        logger.info(f"Loaded schema '{schema_type}' version '{version}' from {schema_path}")
                                except Exception as e:
                                    logger.error(f"Error loading schema from {schema_path}: {e}", exc_info=True)

            # Then, load schemas from the main directory (these will be treated as the latest versions)
            for filename in os.listdir(self.schema_dir):
                if filename.endswith('.schema.json'):
                    schema_path = os.path.join(self.schema_dir, filename)
                    schema_type = filename.replace('.schema.json', '')

                    try:
                        with open(schema_path, 'r') as f:
                            schema = json.load(f)
                            # Check if schema is a list instead of a dictionary
                            if isinstance(schema, list):
                                logger.error(f"Schema in {schema_path} is a list, but should be an object. Skipping.")
                                continue
                            # Extract version from schema or use a default
                            version = self._extract_version_from_schema(schema) or "1.0.0"
                            self.register_schema(schema_type, schema, version)
                            logger.info(f"Loaded schema '{schema_type}' version '{version}' from {schema_path}")
                    except Exception as e:
                        logger.error(f"Error loading schema from {schema_path}: {e}", exc_info=True)

        except Exception as e:
            logger.error(f"Error loading schemas from directory: {e}", exc_info=True)

    def register_schema(self, schema_type: str, schema: Dict[str, Any], version: Optional[str] = None) -> None:
        """
        Register a schema with the registry.

        Args:
            schema_type: Type identifier for the schema
            schema: JSON Schema as a Python dict
            version: Optional version string (semver format). If None, extracts from schema or uses "1.0.0"
        """
        # Extract version from schema if not provided
        if version is None:
            version = self._extract_version_from_schema(schema) or "1.0.0"

        # Validate version format
        try:
            semver.VersionInfo.parse(version)
        except ValueError:
            logger.warning(f"Invalid version format: {version}. Using '1.0.0' instead.")
            version = "1.0.0"

        # Initialize schema type in versioned schemas if not exists
        if schema_type not in self.versioned_schemas:
            self.versioned_schemas[schema_type] = {}

        # Store the schema with its version
        self.versioned_schemas[schema_type][version] = schema

        # Update default version if this is newer
        if schema_type not in self.default_versions or self._is_newer_version(version, self.default_versions[schema_type]):
            self.default_versions[schema_type] = version
            # Update the main schemas and validators dictionaries with the latest version
            self.schemas[schema_type] = schema
            self.validators[schema_type] = jsonschema.validators.validator_for(schema)(schema)

        logger.debug(f"Registered schema '{schema_type}' version '{version}'")

    def validate(self, schema_type: str, instance: Dict[str, Any], version: Optional[str] = None) -> Tuple[bool, List[str]]:
        """
        Validate an instance against a registered schema.

        Args:
            schema_type: Type of schema to validate against
            instance: The object to validate
            version: Optional version string. If None, uses the default version.

        Returns:
            Tuple of (is_valid, error_messages)
        """
        # If version is specified, validate against that version
        if version is not None:
            if schema_type not in self.versioned_schemas or version not in self.versioned_schemas[schema_type]:
                return False, [f"Schema '{schema_type}' version '{version}' not registered"]

            # Create a validator for this specific version
            schema = self.versioned_schemas[schema_type][version]
            validator = jsonschema.validators.validator_for(schema)(schema)

            try:
                errors = list(validator.iter_errors(instance))
                if errors:
                    error_messages = [
                        f"{error.message} (at {'/'.join(str(p) for p in error.path)})"
                        for error in errors
                    ]
                    return False, error_messages
                return True, []
            except Exception as e:
                return False, [f"Validation error: {str(e)}"]

        # Otherwise, use the default validator
        if schema_type not in self.validators:
            return False, [f"Schema '{schema_type}' not registered"]

        try:
            errors = list(self.validators[schema_type].iter_errors(instance))
            if errors:
                error_messages = [
                    f"{error.message} (at {'/'.join(str(p) for p in error.path)})"
                    for error in errors
                ]
                return False, error_messages
            return True, []
        except Exception as e:
            return False, [f"Validation error: {str(e)}"]

    def get_schema(self, schema_type: str, version: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get a registered schema by type and version.

        Args:
            schema_type: Type of schema to retrieve
            version: Optional version string. If None, returns the default version.

        Returns:
            The schema as a dict, or None if not found
        """
        if version is not None:
            if schema_type not in self.versioned_schemas:
                return None
            return self.versioned_schemas[schema_type].get(version)

        return self.schemas.get(schema_type)

    def list_schemas(self) -> List[str]:
        """
        Get a list of all registered schema types.

        Returns:
            List of schema type identifiers
        """
        return list(self.schemas.keys())

    def list_schema_versions(self, schema_type: str) -> List[str]:
        """
        Get a list of all versions for a schema type.

        Args:
            schema_type: Type of schema

        Returns:
            List of version strings sorted by semver
        """
        if schema_type not in self.versioned_schemas:
            return []

        return sorted(
            self.versioned_schemas[schema_type].keys(),
            key=lambda v: semver.VersionInfo.parse(v)
        )

    def get_default_version(self, schema_type: str) -> Optional[str]:
        """
        Get the default version for a schema type.

        Args:
            schema_type: Type of schema

        Returns:
            Default version string, or None if schema type not registered
        """
        return self.default_versions.get(schema_type)

    def _extract_version_from_schema(self, schema: Dict[str, Any]) -> Optional[str]:
        """
        Extract version information from a schema.

        Args:
            schema: JSON Schema as a Python dict

        Returns:
            Version string if found, None otherwise
        """
        # Check for version in standard locations
        if '$version' in schema:
            return schema['$version']

        if 'version' in schema:
            return schema['version']

        # Check for version in metadata
        if 'metadata' in schema and isinstance(schema['metadata'], dict):
            if 'version' in schema['metadata']:
                return schema['metadata']['version']

        # Try to extract from title or description
        if 'title' in schema and isinstance(schema['title'], str):
            version_match = re.search(r'v(\d+\.\d+\.\d+)', schema['title'])
            if version_match:
                return version_match.group(1)

        if 'description' in schema and isinstance(schema['description'], str):
            version_match = re.search(r'v(\d+\.\d+\.\d+)', schema['description'])
            if version_match:
                return version_match.group(1)

        return None

    def _is_newer_version(self, version1: str, version2: str) -> bool:
        """
        Check if version1 is newer than version2.

        Args:
            version1: First version string
            version2: Second version string

        Returns:
            True if version1 is newer than version2, False otherwise
        """
        try:
            v1 = semver.VersionInfo.parse(version1)
            v2 = semver.VersionInfo.parse(version2)
            return v1 > v2
        except ValueError:
            # If parsing fails, fall back to string comparison
            return version1 > version2

    def _get_user_profile_schema(self) -> Dict[str, Any]:
        """Return the built-in user profile schema."""
        return {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "UserProfileSchema",
            "description": "Schema for defining user profiles in benchmark scenarios",
            "type": "object",
            "required": ["trust_phase"],
            "properties": {
                "profile_name": {
                    "type": "string",
                    "description": "Human-readable name for this profile"
                },
                "trust_phase": {
                    "type": "string",
                    "enum": ["Foundation", "Expansion", "Integration"],
                    "description": "Current trust phase of the user"
                },
                "trust_level": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 100,
                    "description": "Numeric trust level (0-100)"
                },
                "communication_preferences": {
                    "type": "object",
                    "properties": {
                        "tone": {
                            "type": "string",
                            "enum": ["warm", "analytical", "casual", "formal", "encouraging", "direct"],
                            "description": "Preferred communication tone"
                        },
                        "detail_level": {
                            "type": "string",
                            "enum": ["low", "medium", "high"],
                            "description": "Preferred level of detail in responses"
                        }
                    }
                },
                "personality_traits": {
                    "type": "object",
                    "description": "HEXACO personality model traits (0.0-1.0)",
                    "properties": {
                        "honesty_humility": {"type": "number", "minimum": 0, "maximum": 1},
                        "emotionality": {"type": "number", "minimum": 0, "maximum": 1},
                        "extraversion": {"type": "number", "minimum": 0, "maximum": 1},
                        "agreeableness": {"type": "number", "minimum": 0, "maximum": 1},
                        "conscientiousness": {"type": "number", "minimum": 0, "maximum": 1},
                        "openness": {"type": "number", "minimum": 0, "maximum": 1},
                        "neuroticism": {"type": "number", "minimum": 0, "maximum": 1}
                    }
                },
                "activity_history": {
                    "type": "object",
                    "properties": {
                        "completed_count": {"type": "integer", "minimum": 0},
                        "abandonment_rate": {"type": "number", "minimum": 0, "maximum": 1},
                        "average_rating": {"type": "number", "minimum": 0, "maximum": 5},
                        "domain_distribution": {
                            "type": "object",
                            "additionalProperties": {"type": "integer", "minimum": 0}
                        },
                        "challenge_progression": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "period": {"type": "string"},
                                    "avg_challenge": {"type": "number", "minimum": 0, "maximum": 100}
                                },
                                "required": ["period", "avg_challenge"]
                            }
                        }
                    }
                },
                "skill_assessments": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "skill": {"type": "string"},
                            "start_level": {"type": "number", "minimum": 0},
                            "current_level": {"type": "number", "minimum": 0}
                        },
                        "required": ["skill"]
                    }
                },
                "user_history": {
                    "type": "object",
                    "description": "Extended history for Integration phase users",
                    "properties": {
                        "total_activities": {"type": "integer", "minimum": 0},
                        "recent_activities": {"type": "integer", "minimum": 0},
                        "completion_rate": {"type": "number", "minimum": 0, "maximum": 1},
                        "average_feedback": {"type": "number", "minimum": 0, "maximum": 5}
                    }
                }
            }
        }

    def _get_situation_schema(self) -> Dict[str, Any]:
        """Return the built-in situation schema."""
        return {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "SituationSchema",
            "description": "Schema for defining benchmark situations and workflows",
            "type": "object",
            "required": ["workflow_type", "text"],
            "properties": {
                "workflow_type": {
                    "type": "string",
                    "enum": [
                        "wheel_generation",
                        "activity_feedback",
                        "discussion",
                        "onboarding",
                        "goal_setting"
                    ],
                    "description": "The type of workflow this situation should trigger"
                },
                "text": {
                    "type": "string",
                    "description": "The user input text that initiates this situation"
                },
                "user_id": {
                    "type": "string",
                    "description": "ID of the user in this situation"
                },
                "trust_level": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 100,
                    "description": "Trust level in this specific situation"
                },
                "activity_id": {
                    "type": "string",
                    "description": "ID of the activity being referenced (for activity_feedback)"
                },
                "feedback_rating": {
                    "type": "number",
                    "minimum": 1,
                    "maximum": 5,
                    "description": "Optional user rating of activity (for activity_feedback)"
                },
                "time_availability": {
                    "type": "number",
                    "description": "User's available time in minutes (for wheel_generation)"
                },
                "environment": {
                    "type": "object",
                    "description": "Environmental context for this situation",
                    "properties": {
                        "location": {"type": "string"},
                        "noise_level": {"type": "string", "enum": ["low", "medium", "high"]},
                        "available_resources": {"type": "array", "items": {"type": "string"}},
                        "social_context": {"type": "string", "enum": ["alone", "family", "friends", "public", "work"]}
                    }
                },
                "conversation_stage": {
                    "type": "string",
                    "enum": [
                        "initial_conversation",
                        "clarify_or_suggest",
                        "process_feedback",
                        "goal_definition",
                        "activity_recommendation"
                    ],
                    "description": "The current stage in the conversation flow"
                },
                "psychological_state": {
                    "type": "object",
                    "properties": {
                        "mood": {"type": "string"},
                        "energy_level": {"type": "string", "enum": ["low", "medium", "high"]},
                        "stress_level": {"type": "string", "enum": ["low", "medium", "high"]},
                        "motivation": {"type": "string", "enum": ["low", "medium", "high"]}
                    }
                },
                "expected_next_step": {
                    "type": "string",
                    "description": "Expected action from the agent (for testing workflow transitions)"
                }
            }
        }

    def _get_evaluation_criteria_schema(self) -> Dict[str, Any]:
        """Return the built-in evaluation criteria schema."""
        return {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "EvaluationCriteriaSchema",
            "description": "Schema for defining evaluation criteria in benchmark scenarios",
            "type": "object",
            "properties": {
                "criteria": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "description": {"type": "string"},
                            "weight": {"type": "number", "minimum": 0, "maximum": 1}
                        },
                        "required": ["name", "weight"]
                    }
                }
            },
            "required": ["criteria"]
        }

    def _get_tool_expectation_schema(self) -> Dict[str, Any]:
        """Return the built-in tool expectation schema."""
        return {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "ToolExpectationSchema",
            "description": "Schema for defining expected tool usage patterns in benchmark scenarios",
            "type": "object",
            "properties": {
                "required_tools": {
                    "type": "array",
                    "description": "Tools that must be called during the benchmark",
                    "items": {
                        "type": "string",
                        "description": "Tool code (e.g., 'get_user_profile')"
                    },
                    "minItems": 0
                },
                "restricted_tools": {
                    "type": "array",
                    "description": "Tools that should not be called during this benchmark",
                    "items": {
                        "type": "string"
                    },
                    "minItems": 0
                },
                "tool_expectations": {
                    "type": "object",
                    "description": "Detailed expectations for specific tools",
                    "additionalProperties": {
                        "type": "object",
                        "properties": {
                            "min_calls": {
                                "type": "integer",
                                "minimum": 0,
                                "description": "Minimum number of times this tool should be called"
                            },
                            "max_calls": {
                                "type": "integer",
                                "minimum": 0,
                                "description": "Maximum number of times this tool should be called"
                            },
                            "required_params": {
                                "type": "object",
                                "description": "Parameters that must be included in the tool call",
                                "additionalProperties": {
                                    "type": ["string", "number", "boolean", "object", "array", "null"]
                                }
                            },
                            "conditional_params": {
                                "type": "array",
                                "description": "Rules for parameters that must appear together or exclusively",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "condition": {
                                            "type": "string",
                                            "description": "Condition expression (e.g., 'if param1 then param2')"
                                        },
                                        "error_message": {
                                            "type": "string",
                                            "description": "Error message if condition is not met"
                                        }
                                    },
                                    "required": ["condition"]
                                }
                            }
                        }
                    }
                },
                "sequence_rules": {
                    "type": "array",
                    "description": "Rules for the sequence of tool calls",
                    "items": {
                        "type": "object",
                        "properties": {
                            "tool_sequence": {
                                "type": "array",
                                "description": "Expected sequence of tool calls (in order)",
                                "items": {
                                    "type": "string"
                                },
                                "minItems": 2
                            },
                            "exact_match": {
                                "type": "boolean",
                                "description": "Whether the sequence must match exactly (true) or just maintain relative order (false)",
                                "default": False
                            },
                            "allow_other_tools_between": {
                                "type": "boolean",
                                "description": "Whether other tools can be called between these tools",
                                "default": True
                            }
                        },
                        "required": ["tool_sequence"]
                    }
                },
                "mock_responses": {
                    "type": "object",
                    "description": "Mock responses for tools used in this benchmark",
                    "additionalProperties": {
                        "oneOf": [
                            {
                                "type": "string",
                                "description": "Simple response template (use {variable} placeholders)"
                            },
                            {
                                "type": "array",
                                "description": "Conditional responses based on tool input or call count",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "condition": {
                                            "type": "string",
                                            "description": "Python expression condition using tool_input and call_count variables"
                                        },
                                        "response": {
                                            "type": "string",
                                            "description": "Response template (use {variable} placeholders)"
                                        }
                                    },
                                    "required": ["condition", "response"]
                                }
                            }
                        ]
                    }
                },
                "error_simulations": {
                    "type": "array",
                    "description": "Configure simulated errors to test agent error handling",
                    "items": {
                        "type": "object",
                        "properties": {
                            "tool_code": {
                                "type": "string",
                                "description": "Tool that should return an error"
                            },
                            "trigger_condition": {
                                "type": "string",
                                "description": "When to trigger the error (e.g., 'call_count == 2')"
                            },
                            "error_message": {
                                "type": "string",
                                "description": "Error message to return"
                            },
                            "error_type": {
                                "type": "string",
                                "enum": ["validation", "not_found", "permission", "timeout", "server"],
                                "description": "Type of error to simulate"
                            }
                        },
                        "required": ["tool_code", "error_message"]
                    }
                }
            }
        }

    def _get_workflow_benchmark_schema(self) -> Dict[str, Any]:
        """Return the built-in workflow benchmark schema."""
        return {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "WorkflowBenchmarkSchema",
            "description": "Schema for defining workflow benchmarks",
            "type": "object",
            "required": ["name", "workflow_type", "description", "evaluation_criteria"],
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Human-readable name for this workflow benchmark"
                },
                "workflow_type": {
                    "type": "string",
                    "enum": [
                        "wheel_generation",
                        "activity_feedback",
                        "discussion",
                        "onboarding",
                        "goal_setting"
                    ],
                    "description": "The type of workflow this benchmark tests"
                },
                "description": {
                    "type": "string",
                    "description": "Detailed description of what this benchmark tests"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Tags for categorizing and filtering benchmarks"
                },
                "user_profile": {
                    "type": "object",
                    "description": "User profile for this benchmark",
                    "$ref": "#/definitions/user_profile"
                },
                "situation": {
                    "type": "object",
                    "description": "Situation for this benchmark",
                    "$ref": "#/definitions/situation"
                },
                "tool_expectations": {
                    "type": "object",
                    "description": "Tool expectations for this benchmark",
                    "$ref": "#/definitions/tool_expectation"
                },
                "evaluation_criteria": {
                    "type": "object",
                    "description": "Evaluation criteria for this benchmark",
                    "$ref": "#/definitions/evaluation_criteria"
                },
                "expected_outputs": {
                    "type": "object",
                    "description": "Expected outputs from the workflow",
                    "properties": {
                        "final_response": {
                            "type": "string",
                            "description": "Expected final response from the agent"
                        },
                        "generated_artifacts": {
                            "type": "array",
                            "description": "Expected artifacts generated by the workflow",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "type": {
                                        "type": "string",
                                        "description": "Type of artifact (e.g., 'wheel', 'goal', 'activity')"
                                    },
                                    "content": {
                                        "type": "object",
                                        "description": "Expected content of the artifact"
                                    }
                                },
                                "required": ["type"]
                            }
                        },
                        "state_changes": {
                            "type": "array",
                            "description": "Expected state changes caused by the workflow",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "entity_type": {
                                        "type": "string",
                                        "description": "Type of entity (e.g., 'user', 'activity', 'goal')"
                                    },
                                    "entity_id": {
                                        "type": "string",
                                        "description": "ID of the entity"
                                    },
                                    "field": {
                                        "type": "string",
                                        "description": "Field that should change"
                                    },
                                    "expected_value": {
                                        "description": "Expected value after the workflow"
                                    }
                                },
                                "required": ["entity_type", "field"]
                            }
                        }
                    }
                },
                "performance_expectations": {
                    "type": "object",
                    "description": "Performance expectations for this benchmark",
                    "properties": {
                        "max_tokens": {
                            "type": "integer",
                            "description": "Maximum number of tokens that should be used"
                        },
                        "max_tool_calls": {
                            "type": "integer",
                            "description": "Maximum number of tool calls that should be made"
                        },
                        "max_duration_seconds": {
                            "type": "number",
                            "description": "Maximum duration in seconds"
                        }
                    }
                },
                "metadata": {
                    "type": "object",
                    "description": "Additional metadata for this benchmark",
                    "properties": {
                        "author": {
                            "type": "string",
                            "description": "Author of this benchmark"
                        },
                        "created_at": {
                            "type": "string",
                            "format": "date-time",
                            "description": "Creation date"
                        },
                        "updated_at": {
                            "type": "string",
                            "format": "date-time",
                            "description": "Last update date"
                        },
                        "version": {
                            "type": "string",
                            "description": "Version of this benchmark"
                        },
                        "priority": {
                            "type": "string",
                            "enum": ["low", "medium", "high", "critical"],
                            "description": "Priority of this benchmark"
                        }
                    }
                }
            },
            "definitions": {
                "user_profile": {
                    "type": "object",
                    "required": ["trust_phase"],
                    "properties": {
                        "profile_name": {
                            "type": "string",
                            "description": "Human-readable name for this profile"
                        },
                        "trust_phase": {
                            "type": "string",
                            "enum": ["Foundation", "Expansion", "Integration"],
                            "description": "Current trust phase of the user"
                        },
                        "trust_level": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 100,
                            "description": "Numeric trust level (0-100)"
                        }
                    }
                },
                "situation": {
                    "type": "object",
                    "required": ["workflow_type", "text"],
                    "properties": {
                        "workflow_type": {
                            "type": "string",
                            "enum": [
                                "wheel_generation",
                                "activity_feedback",
                                "discussion",
                                "onboarding",
                                "goal_setting"
                            ],
                            "description": "The type of workflow this situation should trigger"
                        },
                        "text": {
                            "type": "string",
                            "description": "The user input text that initiates this situation"
                        }
                    }
                },
                "tool_expectation": {
                    "type": "object",
                    "properties": {
                        "required_tools": {
                            "type": "array",
                            "description": "Tools that must be called during the benchmark",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                },
                "evaluation_criteria": {
                    "type": "object",
                    "required": ["criteria"],
                    "properties": {
                        "criteria": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "description": {"type": "string"},
                                    "weight": {"type": "number", "minimum": 0, "maximum": 1}
                                },
                                "required": ["name", "weight"]
                            }
                        }
                    }
                }
            }
        }


