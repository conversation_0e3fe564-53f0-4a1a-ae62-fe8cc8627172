"""
Schema Migration Utility

This module provides utilities for migrating between different versions of schemas.
It includes functions for common migration operations and a registry of migrations.

Key responsibilities:
1. Provide common migration operations
2. Register migrations for schema types
3. Apply migrations to data
"""

import logging
import copy
from typing import Dict, Any, List, Optional, Callable, Tuple, Set

# Configure logging
logger = logging.getLogger(__name__)

# Type definitions
MigrationFunc = Callable[[Dict[str, Any]], Dict[str, Any]]


class SchemaMigrationUtility:
    """
    Utility for migrating between schema versions.
    
    This class provides methods for common migration operations and a registry
    of migrations for different schema types.
    """
    
    def __init__(self):
        """Initialize the schema migration utility."""
        self.migrations: Dict[str, Dict[Tuple[str, str], MigrationFunc]] = {}
    
    def register_migration(
        self,
        schema_type: str,
        from_version: str,
        to_version: str,
        migration_func: MigrationFunc
    ) -> None:
        """
        Register a migration function between two schema versions.
        
        Args:
            schema_type: Type of schema
            from_version: Source version
            to_version: Target version
            migration_func: Function that transforms data from source to target version
        """
        # Initialize schema type if not exists
        if schema_type not in self.migrations:
            self.migrations[schema_type] = {}
        
        # Store the migration function
        self.migrations[schema_type][(from_version, to_version)] = migration_func
        
        logger.debug(f"Registered migration for '{schema_type}' from '{from_version}' to '{to_version}'")
    
    def get_migration(
        self,
        schema_type: str,
        from_version: str,
        to_version: str
    ) -> Optional[MigrationFunc]:
        """
        Get a migration function between two schema versions.
        
        Args:
            schema_type: Type of schema
            from_version: Source version
            to_version: Target version
        
        Returns:
            Migration function if found, None otherwise
        """
        if schema_type not in self.migrations:
            return None
        
        return self.migrations[schema_type].get((from_version, to_version))
    
    def list_migrations(self, schema_type: Optional[str] = None) -> Dict[str, List[Tuple[str, str]]]:
        """
        List all registered migrations.
        
        Args:
            schema_type: Optional schema type to filter by
        
        Returns:
            Dict mapping schema types to lists of (from_version, to_version) tuples
        """
        if schema_type is not None:
            if schema_type not in self.migrations:
                return {schema_type: []}
            return {schema_type: list(self.migrations[schema_type].keys())}
        
        return {
            schema_type: list(migrations.keys())
            for schema_type, migrations in self.migrations.items()
        }
    
    # Common migration operations
    
    @staticmethod
    def add_field(data: Dict[str, Any], field_path: str, default_value: Any) -> Dict[str, Any]:
        """
        Add a field to the data with a default value.
        
        Args:
            data: Data to modify
            field_path: Path to the field (dot notation, e.g., 'metadata.version')
            default_value: Default value for the field
        
        Returns:
            Modified data
        """
        result = copy.deepcopy(data)
        parts = field_path.split('.')
        
        # Navigate to the parent object
        current = result
        for i, part in enumerate(parts[:-1]):
            if part not in current:
                current[part] = {}
            elif not isinstance(current[part], dict):
                # Convert to dict if not already
                current[part] = {"value": current[part]}
            current = current[part]
        
        # Set the field if it doesn't exist
        if parts[-1] not in current:
            current[parts[-1]] = default_value
        
        return result
    
    @staticmethod
    def remove_field(data: Dict[str, Any], field_path: str) -> Dict[str, Any]:
        """
        Remove a field from the data.
        
        Args:
            data: Data to modify
            field_path: Path to the field (dot notation, e.g., 'metadata.version')
        
        Returns:
            Modified data
        """
        result = copy.deepcopy(data)
        parts = field_path.split('.')
        
        # Navigate to the parent object
        current = result
        for i, part in enumerate(parts[:-1]):
            if part not in current or not isinstance(current[part], dict):
                # Field doesn't exist or parent is not a dict
                return result
            current = current[part]
        
        # Remove the field if it exists
        if parts[-1] in current:
            del current[parts[-1]]
        
        return result
    
    @staticmethod
    def rename_field(data: Dict[str, Any], old_path: str, new_path: str) -> Dict[str, Any]:
        """
        Rename a field in the data.
        
        Args:
            data: Data to modify
            old_path: Path to the old field (dot notation)
            new_path: Path to the new field (dot notation)
        
        Returns:
            Modified data
        """
        result = copy.deepcopy(data)
        
        # Get the value from the old path
        old_parts = old_path.split('.')
        old_value = None
        old_exists = True
        
        # Navigate to the old field
        current = result
        for i, part in enumerate(old_parts[:-1]):
            if part not in current or not isinstance(current[part], dict):
                old_exists = False
                break
            current = current[part]
        
        # Get the old value if it exists
        if old_exists and old_parts[-1] in current:
            old_value = current[old_parts[-1]]
            del current[old_parts[-1]]
        else:
            # Old field doesn't exist
            return result
        
        # Set the value at the new path
        new_parts = new_path.split('.')
        
        # Navigate to the parent of the new field
        current = result
        for i, part in enumerate(new_parts[:-1]):
            if part not in current:
                current[part] = {}
            elif not isinstance(current[part], dict):
                # Convert to dict if not already
                current[part] = {"value": current[part]}
            current = current[part]
        
        # Set the new field
        current[new_parts[-1]] = old_value
        
        return result
    
    @staticmethod
    def transform_field(
        data: Dict[str, Any],
        field_path: str,
        transform_func: Callable[[Any], Any]
    ) -> Dict[str, Any]:
        """
        Transform a field in the data using a function.
        
        Args:
            data: Data to modify
            field_path: Path to the field (dot notation)
            transform_func: Function to transform the field value
        
        Returns:
            Modified data
        """
        result = copy.deepcopy(data)
        parts = field_path.split('.')
        
        # Navigate to the parent object
        current = result
        for i, part in enumerate(parts[:-1]):
            if part not in current or not isinstance(current[part], dict):
                # Field doesn't exist or parent is not a dict
                return result
            current = current[part]
        
        # Transform the field if it exists
        if parts[-1] in current:
            current[parts[-1]] = transform_func(current[parts[-1]])
        
        return result
    
    @staticmethod
    def merge_objects(
        data: Dict[str, Any],
        field_path: str,
        merge_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Merge an object into a field in the data.
        
        Args:
            data: Data to modify
            field_path: Path to the field (dot notation)
            merge_data: Data to merge
        
        Returns:
            Modified data
        """
        result = copy.deepcopy(data)
        parts = field_path.split('.')
        
        # Navigate to the target object
        current = result
        for i, part in enumerate(parts):
            if part not in current:
                current[part] = {}
            elif not isinstance(current[part], dict):
                # Convert to dict if not already
                current[part] = {"value": current[part]}
            current = current[part]
        
        # Merge the data
        current.update(merge_data)
        
        return result
    
    @staticmethod
    def update_enum_values(
        data: Dict[str, Any],
        field_path: str,
        value_map: Dict[Any, Any]
    ) -> Dict[str, Any]:
        """
        Update enum values in a field.
        
        Args:
            data: Data to modify
            field_path: Path to the field (dot notation)
            value_map: Mapping from old values to new values
        
        Returns:
            Modified data
        """
        def transform_value(value):
            if isinstance(value, list):
                return [value_map.get(item, item) for item in value]
            return value_map.get(value, value)
        
        return SchemaMigrationUtility.transform_field(data, field_path, transform_value)
    
    @staticmethod
    def update_array_items(
        data: Dict[str, Any],
        array_path: str,
        item_transform: Callable[[Dict[str, Any]], Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Update items in an array field.
        
        Args:
            data: Data to modify
            array_path: Path to the array field (dot notation)
            item_transform: Function to transform each item
        
        Returns:
            Modified data
        """
        def transform_array(array):
            if not isinstance(array, list):
                return array
            return [item_transform(item) if isinstance(item, dict) else item for item in array]
        
        return SchemaMigrationUtility.transform_field(data, array_path, transform_array)
    
    @staticmethod
    def create_migration_chain(
        migrations: List[MigrationFunc]
    ) -> MigrationFunc:
        """
        Create a chain of migrations to be applied in sequence.
        
        Args:
            migrations: List of migration functions
        
        Returns:
            Combined migration function
        """
        def chain_migration(data: Dict[str, Any]) -> Dict[str, Any]:
            result = data
            for migration in migrations:
                result = migration(result)
            return result
        
        return chain_migration
