# backend/apps/main/services/wheel_item_analyzer.py

import logging
import statistics
from typing import Dict, List, Any, Optional, Tuple, Union
import asyncio

from django.db import models
from asgiref.sync import sync_to_async

from apps.main.utils.db_connection_utility import DatabaseConnectionUtility

logger = logging.getLogger(__name__)

class WheelItemAnalyzer:
    """
    Analyzer for wheel items to calculate challenge metrics and distribution statistics.

    This class provides methods to analyze wheel activities for their challenge level
    based on various factors like time requirements, resource complexity, and dependencies.
    It also calculates statistical metrics about the distribution of challenge levels
    across activities in a wheel.

    The analyzer uses the DatabaseConnectionUtility for database operations to ensure
    connection stability and proper error handling.
    """

    def __init__(self):
        """Initialize the WheelItemAnalyzer."""
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    @DatabaseConnectionUtility.with_retry(max_retries=3, retry_delay=0.5)
    def measure_challenge_level(self, items: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Analyze wheel items to measure challenge levels based on multiple factors.

        This method calculates challenge metrics for wheel activities based on:
        - Time requirements (duration, estimated_completion_time)
        - Resource complexity (resources_required, resource_intensity)
        - Dependencies between activities
        - Implementation difficulty (challenge_level field)

        Args:
            items: List of wheel activity dictionaries with challenge-related fields

        Returns:
            Dictionary containing challenge metrics:
            - overall_challenge: Overall challenge level (0-100)
            - time_challenge: Challenge based on time requirements (0-100)
            - resource_challenge: Challenge based on resource complexity (0-100)
            - dependency_challenge: Challenge based on dependencies (0-100)
            - implementation_challenge: Challenge based on implementation difficulty (0-100)
            - challenge_variance: Variance in challenge levels across activities
        """
        self.logger.info(f"Measuring challenge level for {len(items)} wheel items")

        if not items:
            self.logger.warning("No items provided for challenge level measurement")
            return {
                "overall_challenge": 0.0,
                "time_challenge": 0.0,
                "resource_challenge": 0.0,
                "dependency_challenge": 0.0,
                "implementation_challenge": 0.0,
                "challenge_variance": 0.0
            }

        try:
            # Calculate time-based challenge
            time_challenge = self._calculate_time_challenge(items)

            # Calculate resource-based challenge
            resource_challenge = self._calculate_resource_challenge(items)

            # Calculate dependency-based challenge
            dependency_challenge = self._calculate_dependency_challenge(items)

            # Calculate implementation challenge (from explicit challenge_level field)
            implementation_challenge = self._calculate_implementation_challenge(items)

            # Calculate overall challenge (weighted average)
            overall_challenge = self._calculate_overall_challenge(
                time_challenge,
                resource_challenge,
                dependency_challenge,
                implementation_challenge
            )

            # Calculate variance in challenge levels
            challenge_values = [
                item.get('challenge_level', 50) for item in items
                if isinstance(item.get('challenge_level'), (int, float))
            ]

            challenge_variance = 0.0
            if len(challenge_values) > 1:
                challenge_variance = statistics.variance(challenge_values)

            return {
                "overall_challenge": round(overall_challenge, 2),
                "time_challenge": round(time_challenge, 2),
                "resource_challenge": round(resource_challenge, 2),
                "dependency_challenge": round(dependency_challenge, 2),
                "implementation_challenge": round(implementation_challenge, 2),
                "challenge_variance": round(challenge_variance, 2)
            }

        except Exception as e:
            self.logger.error(f"Error measuring challenge level: {str(e)}", exc_info=True)
            # Return default values in case of error
            return {
                "overall_challenge": 50.0,  # Default medium challenge
                "time_challenge": 50.0,
                "resource_challenge": 50.0,
                "dependency_challenge": 50.0,
                "implementation_challenge": 50.0,
                "challenge_variance": 0.0,
                "error": str(e)
            }

    @DatabaseConnectionUtility.with_retry(max_retries=3, retry_delay=0.5)
    def calculate_distribution_metrics(self, items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate statistical metrics about the distribution of challenge levels.

        This method analyzes the distribution of challenge levels across activities
        to provide insights about the balance and variety of challenges in the wheel.

        Args:
            items: List of wheel activity dictionaries with challenge-related fields

        Returns:
            Dictionary containing distribution metrics:
            - challenge_mean: Mean challenge level across activities
            - challenge_median: Median challenge level
            - challenge_min: Minimum challenge level
            - challenge_max: Maximum challenge level
            - challenge_std_dev: Standard deviation of challenge levels
            - domain_distribution: Challenge distribution by domain
            - challenge_histogram: Histogram of challenge levels in buckets
        """
        self.logger.info(f"Calculating distribution metrics for {len(items)} wheel items")

        if not items:
            self.logger.warning("No items provided for distribution metrics calculation")
            return {
                "challenge_mean": 0.0,
                "challenge_median": 0.0,
                "challenge_min": 0.0,
                "challenge_max": 0.0,
                "challenge_std_dev": 0.0,
                "domain_distribution": {},
                "challenge_histogram": {}
            }

        try:
            # Extract challenge levels, ensuring they are numeric
            challenge_values = []
            for item in items:
                challenge_level = item.get('challenge_level')
                if isinstance(challenge_level, (int, float)):
                    challenge_values.append(challenge_level)
                else:
                    # Use a default value if challenge_level is missing or not numeric
                    challenge_values.append(50.0)  # Default medium challenge

            # Calculate basic statistics
            if not challenge_values:
                return {
                    "challenge_mean": 0.0,
                    "challenge_median": 0.0,
                    "challenge_min": 0.0,
                    "challenge_max": 0.0,
                    "challenge_std_dev": 0.0,
                    "domain_distribution": {},
                    "challenge_histogram": {}
                }

            challenge_mean = statistics.mean(challenge_values)
            challenge_median = statistics.median(challenge_values)
            challenge_min = min(challenge_values)
            challenge_max = max(challenge_values)

            challenge_std_dev = 0.0
            if len(challenge_values) > 1:
                challenge_std_dev = statistics.stdev(challenge_values)

            # Calculate domain distribution
            domain_distribution = self._calculate_domain_distribution(items)

            # Create challenge histogram (buckets of 10: 0-10, 11-20, etc.)
            challenge_histogram = self._create_challenge_histogram(challenge_values)

            return {
                "challenge_mean": round(challenge_mean, 2),
                "challenge_median": round(challenge_median, 2),
                "challenge_min": round(challenge_min, 2),
                "challenge_max": round(challenge_max, 2),
                "challenge_std_dev": round(challenge_std_dev, 2),
                "domain_distribution": domain_distribution,
                "challenge_histogram": challenge_histogram
            }

        except Exception as e:
            self.logger.error(f"Error calculating distribution metrics: {str(e)}", exc_info=True)
            # Return default values in case of error
            return {
                "challenge_mean": 50.0,
                "challenge_median": 50.0,
                "challenge_min": 0.0,
                "challenge_max": 100.0,
                "challenge_std_dev": 0.0,
                "domain_distribution": {},
                "challenge_histogram": {},
                "error": str(e)
            }

    async def measure_challenge_level_async(self, items: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Async wrapper for measure_challenge_level method.

        Args:
            items: List of wheel activity dictionaries

        Returns:
            Dictionary containing challenge metrics
        """
        return await sync_to_async(self.measure_challenge_level)(items)

    async def calculate_distribution_metrics_async(self, items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Async wrapper for calculate_distribution_metrics method.

        Args:
            items: List of wheel activity dictionaries

        Returns:
            Dictionary containing distribution metrics
        """
        return await sync_to_async(self.calculate_distribution_metrics)(items)

    def _calculate_time_challenge(self, items: List[Dict[str, Any]]) -> float:
        """
        Calculate challenge level based on time requirements.

        Args:
            items: List of wheel activity dictionaries

        Returns:
            Time-based challenge level (0-100)
        """
        if not items:
            return 0.0

        total_time = 0
        count = 0

        for item in items:
            # Check for activity data
            activity = item.get('activity', {})
            if not activity and 'activities' in item:
                # If activity is not directly in the item, try to find it by activity_id
                activity_id = item.get('activity_id')
                if activity_id:
                    for act in item.get('activities', []):
                        if act.get('id') == activity_id:
                            activity = act
                            break

            # If still no activity data, use the item itself
            if not activity:
                activity = item

            # Get time values with fallbacks
            duration = activity.get('duration', 0)
            estimated_completion_time = activity.get('estimated_completion_time', duration)

            # Use the larger of the two values
            time_value = max(duration, estimated_completion_time)

            if isinstance(time_value, (int, float)) and time_value > 0:
                total_time += time_value
                count += 1

        if count == 0:
            return 50.0  # Default medium challenge

        # Calculate average time
        avg_time = total_time / count

        # Map average time to challenge level (0-100)
        # Assuming longer activities are more challenging
        # 0-15 min: low challenge, 15-45 min: medium challenge, 45+ min: high challenge
        if avg_time < 15:
            time_challenge = 30.0 + (avg_time / 15.0) * 20.0  # 30-50
        elif avg_time < 45:
            time_challenge = 50.0 + ((avg_time - 15) / 30.0) * 20.0  # 50-70
        else:
            time_challenge = 70.0 + min(((avg_time - 45) / 60.0) * 30.0, 30.0)  # 70-100, capped at 100

        return time_challenge

    def _calculate_resource_challenge(self, items: List[Dict[str, Any]]) -> float:
        """
        Calculate challenge level based on resource complexity.

        Args:
            items: List of wheel activity dictionaries

        Returns:
            Resource-based challenge level (0-100)
        """
        if not items:
            return 0.0

        total_resource_score = 0
        count = 0

        for item in items:
            # Check for activity data
            activity = item.get('activity', {})
            if not activity and 'activities' in item:
                # If activity is not directly in the item, try to find it by activity_id
                activity_id = item.get('activity_id')
                if activity_id:
                    for act in item.get('activities', []):
                        if act.get('id') == activity_id:
                            activity = act
                            break

            # If still no activity data, use the item itself
            if not activity:
                activity = item

            # Calculate resource score based on:
            # 1. Number of resources required
            # 2. Resource intensity (if available)

            resources_required = activity.get('resources_required', [])
            resource_count = len(resources_required) if isinstance(resources_required, list) else 0

            # Get resource intensity with fallback
            resource_intensity = activity.get('resource_intensity', 'medium').lower()
            intensity_score = {
                'low': 1.0,
                'medium': 2.0,
                'high': 3.0
            }.get(resource_intensity, 2.0)

            # Calculate resource score
            resource_score = resource_count * intensity_score

            total_resource_score += resource_score
            count += 1

        if count == 0:
            return 50.0  # Default medium challenge

        # Calculate average resource score
        avg_resource_score = total_resource_score / count

        # Map average resource score to challenge level (0-100)
        # 0: no resources, 1-3: low challenge, 4-6: medium challenge, 7+: high challenge
        if avg_resource_score == 0:
            resource_challenge = 30.0  # Minimal resources
        elif avg_resource_score <= 3:
            resource_challenge = 30.0 + (avg_resource_score / 3.0) * 20.0  # 30-50
        elif avg_resource_score <= 6:
            resource_challenge = 50.0 + ((avg_resource_score - 3) / 3.0) * 20.0  # 50-70
        else:
            resource_challenge = 70.0 + min(((avg_resource_score - 6) / 6.0) * 30.0, 30.0)  # 70-100, capped at 100

        return resource_challenge

    def _calculate_dependency_challenge(self, items: List[Dict[str, Any]]) -> float:
        """
        Calculate challenge level based on dependencies between activities.

        Args:
            items: List of wheel activity dictionaries

        Returns:
            Dependency-based challenge level (0-100)
        """
        if not items:
            return 0.0

        # For now, use a simplified approach since dependency information
        # might not be explicitly available in the current data structure

        # Count activities with dependencies
        dependency_count = 0
        for item in items:
            # Check for activity data
            activity = item.get('activity', {})
            if not activity and 'activities' in item:
                activity_id = item.get('activity_id')
                if activity_id:
                    for act in item.get('activities', []):
                        if act.get('id') == activity_id:
                            activity = act
                            break

            # If still no activity data, use the item itself
            if not activity:
                activity = item

            # Check for dependencies
            dependencies = activity.get('dependencies', [])
            prerequisites = activity.get('prerequisites', [])

            if (isinstance(dependencies, list) and dependencies) or \
               (isinstance(prerequisites, list) and prerequisites):
                dependency_count += 1

        # Calculate dependency ratio
        dependency_ratio = dependency_count / len(items) if items else 0

        # Map dependency ratio to challenge level (0-100)
        # 0: no dependencies, 0.5: half activities have dependencies, 1.0: all activities have dependencies
        dependency_challenge = 30.0 + dependency_ratio * 70.0

        return dependency_challenge

    def _calculate_implementation_challenge(self, items: List[Dict[str, Any]]) -> float:
        """
        Calculate challenge level based on explicit challenge_level field.

        Args:
            items: List of wheel activity dictionaries

        Returns:
            Implementation-based challenge level (0-100)
        """
        if not items:
            return 0.0

        challenge_values = []

        for item in items:
            # Check for activity data
            activity = item.get('activity', {})
            if not activity and 'activities' in item:
                activity_id = item.get('activity_id')
                if activity_id:
                    for act in item.get('activities', []):
                        if act.get('id') == activity_id:
                            activity = act
                            break

            # If still no activity data, use the item itself
            if not activity:
                activity = item

            # Get challenge level with fallback
            challenge_level = activity.get('challenge_level')
            if isinstance(challenge_level, (int, float)):
                challenge_values.append(challenge_level)

        if not challenge_values:
            return 50.0  # Default medium challenge

        # Calculate average challenge level
        avg_challenge = statistics.mean(challenge_values)

        return avg_challenge

    def _calculate_overall_challenge(
        self,
        time_challenge: float,
        resource_challenge: float,
        dependency_challenge: float,
        implementation_challenge: float
    ) -> float:
        """
        Calculate overall challenge level as a weighted average of component challenges.

        Args:
            time_challenge: Time-based challenge level
            resource_challenge: Resource-based challenge level
            dependency_challenge: Dependency-based challenge level
            implementation_challenge: Implementation-based challenge level

        Returns:
            Overall challenge level (0-100)
        """
        # Define weights for each component
        # Implementation challenge (explicit challenge_level) gets highest weight
        weights = {
            'time': 0.2,
            'resource': 0.2,
            'dependency': 0.1,
            'implementation': 0.5
        }

        # Calculate weighted average
        overall_challenge = (
            weights['time'] * time_challenge +
            weights['resource'] * resource_challenge +
            weights['dependency'] * dependency_challenge +
            weights['implementation'] * implementation_challenge
        )

        return overall_challenge

    def _calculate_domain_distribution(self, items: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        Calculate challenge distribution by domain.

        Args:
            items: List of wheel activity dictionaries

        Returns:
            Dictionary mapping domains to challenge statistics
        """
        domain_data = {}

        for item in items:
            # Check for activity data
            activity = item.get('activity', {})
            if not activity and 'activities' in item:
                activity_id = item.get('activity_id')
                if activity_id:
                    for act in item.get('activities', []):
                        if act.get('id') == activity_id:
                            activity = act
                            break

            # If still no activity data, use the item itself
            if not activity:
                activity = item

            # Get domain and challenge level
            domain = activity.get('domain', 'unknown')
            challenge_level = activity.get('challenge_level')

            if not isinstance(challenge_level, (int, float)):
                challenge_level = 50.0  # Default medium challenge

            # Initialize domain entry if not exists
            if domain not in domain_data:
                domain_data[domain] = {
                    'count': 0,
                    'challenge_values': [],
                    'total_challenge': 0.0
                }

            # Update domain data
            domain_data[domain]['count'] += 1
            domain_data[domain]['challenge_values'].append(challenge_level)
            domain_data[domain]['total_challenge'] += challenge_level

        # Calculate statistics for each domain
        result = {}
        for domain, data in domain_data.items():
            if data['count'] > 0:
                challenge_values = data['challenge_values']

                result[domain] = {
                    'count': data['count'],
                    'mean_challenge': round(data['total_challenge'] / data['count'], 2),
                    'min_challenge': round(min(challenge_values), 2),
                    'max_challenge': round(max(challenge_values), 2)
                }

                if len(challenge_values) > 1:
                    result[domain]['std_dev'] = round(statistics.stdev(challenge_values), 2)
                else:
                    result[domain]['std_dev'] = 0.0

        return result

    def _create_challenge_histogram(self, challenge_values: List[float]) -> Dict[str, int]:
        """
        Create a histogram of challenge levels in buckets of 10.

        Args:
            challenge_values: List of challenge level values

        Returns:
            Dictionary mapping challenge ranges to counts
        """
        histogram = {
            '0-10': 0,
            '11-20': 0,
            '21-30': 0,
            '31-40': 0,
            '41-50': 0,
            '51-60': 0,
            '61-70': 0,
            '71-80': 0,
            '81-90': 0,
            '91-100': 0
        }

        for value in challenge_values:
            # Fix the histogram calculation to correctly categorize values
            # For example, 60 should be in the '51-60' bucket, not '61-70'
            if value <= 10:
                histogram['0-10'] += 1
            elif value <= 20:
                histogram['11-20'] += 1
            elif value <= 30:
                histogram['21-30'] += 1
            elif value <= 40:
                histogram['31-40'] += 1
            elif value <= 50:
                histogram['41-50'] += 1
            elif value <= 60:
                histogram['51-60'] += 1
            elif value <= 70:
                histogram['61-70'] += 1
            elif value <= 80:
                histogram['71-80'] += 1
            elif value <= 90:
                histogram['81-90'] += 1
            else:
                histogram['91-100'] += 1

        return histogram
