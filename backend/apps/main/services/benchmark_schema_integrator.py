"""
Utility for integrating schema validation with the benchmark system.
"""

import json
import logging
import os
from typing import Dict, Any, List, Tuple, Optional, Union

from .schema_validator_service import SchemaValidationService
from apps.main.models import BenchmarkScenario, EvaluationCriteriaTemplate
from django.db import transaction

logger = logging.getLogger(__name__)

class BenchmarkSchemaIntegrator:
    """
    Utility for integrating schema validation with the benchmark system.
    Provides methods to validate and convert between benchmark database records
    and schema-compliant JSON objects.
    """
    
    def __init__(self, validation_service: Optional[SchemaValidationService] = None):
        """
        Initialize the benchmark schema integrator.
        
        Args:
            validation_service: Optional validation service to use.
                              If None, creates a new SchemaValidationService.
        """
        self.validator = validation_service or SchemaValidationService()
    
    def validate_scenario(self, scenario: BenchmarkScenario) -> Dict[str, Any]:
        """
        Validate a BenchmarkScenario database record.
        
        Args:
            scenario: BenchmarkScenario database record
            
        Returns:
            Validation result dict
        """
        # Convert database model to dict for validation
        scenario_dict = {
            'name': scenario.name,
            'description': scenario.description,
            'agent_role': scenario.agent_role,
            'input_data': scenario.input_data,
            'metadata': scenario.metadata
        }
        
        return self.validator.validate_benchmark_scenario(scenario_dict)
    
    def validate_all_scenarios(self) -> Dict[str, Dict[str, Any]]:
        """
        Validate all active BenchmarkScenario records.
        
        Returns:
            Dict mapping scenario IDs to validation results
        """
        results = {}
        
        for scenario in BenchmarkScenario.objects.filter(is_active=True):
            results[str(scenario.id)] = {
                'name': scenario.name,
                'validation': self.validate_scenario(scenario)
            }
        
        return results
    
    def validate_evaluation_template(self, template: EvaluationCriteriaTemplate) -> Tuple[bool, List[str]]:
        """
        Validate an EvaluationCriteriaTemplate database record.
        
        Args:
            template: EvaluationCriteriaTemplate database record
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        # Convert criteria from database format
        template_dict = {
            'name': template.name,
            'description': template.description,
            'dimensions': template.criteria
        }
        
        return self.validator.validate_evaluation_criteria(template_dict)
    
    def import_evaluation_templates_from_scenarios(self) -> Dict[str, Dict[str, Any]]:
        """
        Extract and import evaluation criteria from existing scenarios into templates.
        
        Returns:
            Dict mapping template names to results
        """
        results = {}
        
        try:
            with transaction.atomic():
                for scenario in BenchmarkScenario.objects.filter(is_active=True):
                    if 'metadata' in scenario.__dict__ and 'expected_quality_criteria' in scenario.metadata:
                        criteria = scenario.metadata['expected_quality_criteria']
                        if not criteria:
                            continue
                        
                        template_name = f"{scenario.name} Evaluation"
                        dimension_weights = scenario.metadata.get('dimension_weights', {})
                        
                        # Check if template already exists
                        existing = EvaluationCriteriaTemplate.objects.filter(name=template_name).first()
                        if existing:
                            # Update existing template
                            existing.description = f"Evaluation criteria for {scenario.name}"
                            existing.criteria = criteria
                            # Add weights if available
                            if dimension_weights:
                                existing.criteria['dimension_weights'] = dimension_weights
                            existing.save()
                            
                            is_valid, errors = self.validate_evaluation_template(existing)
                            results[template_name] = {
                                'action': 'updated',
                                'valid': is_valid,
                                'errors': errors
                            }
                        else:
                            # Create new template
                            template = EvaluationCriteriaTemplate(
                                name=template_name,
                                description=f"Evaluation criteria for {scenario.name}",
                                criteria=criteria
                            )
                            # Add weights if available
                            if dimension_weights:
                                template.criteria['dimension_weights'] = dimension_weights
                            template.save()
                            
                            is_valid, errors = self.validate_evaluation_template(template)
                            results[template_name] = {
                                'action': 'created',
                                'valid': is_valid,
                                'errors': errors
                            }
        
        except Exception as e:
            logger.error(f"Error importing evaluation templates: {e}", exc_info=True)
            results['error'] = str(e)
        
        return results
    
    def export_scenarios_as_schema_compliant(self, output_dir: str) -> Dict[str, Any]:
        """
        Export all scenarios as schema-compliant JSON files.
        
        Args:
            output_dir: Directory to save the JSON files
            
        Returns:
            Dict with results of the export operation
        """
        results = {
            'total': 0,
            'valid': 0,
            'invalid': 0,
            'errors': []
        }
        
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            for scenario in BenchmarkScenario.objects.filter(is_active=True):
                results['total'] += 1
                
                # Validate the scenario
                validation = self.validate_scenario(scenario)
                
                # Prepare export data
                export_data = {
                    'name': scenario.name,
                    'description': scenario.description,
                    'agent_role': scenario.agent_role,
                    'input_data': scenario.input_data,
                    'metadata': scenario.metadata,
                    'is_active': scenario.is_active,
                    'validation_result': validation
                }
                
                # Add validation status suffix to filename
                status_suffix = '_valid' if validation['valid'] else '_invalid'
                filename = f"{scenario.name.replace(' ', '_')}{status_suffix}.json"
                file_path = os.path.join(output_dir, filename)
                
                with open(file_path, 'w') as f:
                    json.dump(export_data, f, indent=2)
                
                if validation['valid']:
                    results['valid'] += 1
                else:
                    results['invalid'] += 1
                    results['errors'].append({
                        'scenario': scenario.name,
                        'errors': validation['errors']
                    })
        
        except Exception as e:
            logger.error(f"Error exporting scenarios: {e}", exc_info=True)
            results['error'] = str(e)
        
        return results
    
    def update_scenario_with_schema_validated_data(self, scenario_id: int, data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Update a BenchmarkScenario with validated data.
        
        Args:
            scenario_id: ID of the scenario to update
            data: Schema-validated data to update the scenario with
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Validate data against schemas
            validation = self.validator.validate_benchmark_scenario(data)
            if not validation['valid']:
                return False, f"Validation failed: {', '.join(validation['errors'])}"
            
            with transaction.atomic():
                scenario = BenchmarkScenario.objects.get(id=scenario_id)
                
                # Update scenario fields
                scenario.name = data['name']
                scenario.description = data['description']
                scenario.agent_role = data['agent_role']
                scenario.input_data = data['input_data']
                scenario.metadata = data['metadata']
                
                scenario.save()
                
                return True, f"Scenario {scenario.name} updated successfully with schema-validated data."
        
        except BenchmarkScenario.DoesNotExist:
            return False, f"Scenario with ID {scenario_id} not found."
        except Exception as e:
            logger.error(f"Error updating scenario: {e}", exc_info=True)
            return False, f"Error updating scenario: {str(e)}"