# ACTIVE_FILE - 29-05-2025
# backend/apps/main/services/event_service.py

import asyncio
import json
import logging
from typing import Dict, Any, Optional, Union, List
from datetime import datetime, timezone
from channels.layers import get_channel_layer

logger = logging.getLogger(__name__)

class EventService:
    """
    Centralized service for emitting events throughout the Game of Life system.
    
    This service provides a standardized way for components to communicate
    without tight coupling. It leverages the existing Channels infrastructure
    to distribute events to appropriate WebSocket consumers.
    
    Events are routed to:
    - User-specific groups (user_session_group_{user_profile_id})
    - Admin listener groups (admin_listener_for_user_{user_profile_id})
    - Specific WebSocket sessions if provided
    """
    
    # Event types that follow the ApiContract.md definitions
    STANDARD_EVENT_TYPES = [
        'system_message', 'chat_message', 'processing_status', 
        'wheel_data', 'error', 'workflow_status', 'debug_info',
        'tool_argument_error' # Added new event type
    ]

    @staticmethod
    async def emit_event(
        event_type: str,
        data: Dict[str, Any],
        user_profile_id: Optional[str] = None,
        target_groups: Optional[List[str]] = None,
        session_id: Optional[str] = None
    ) -> bool:
        """
        Emit an event to appropriate channels.
        
        Args:
            event_type: Type of event (must match a handler in UserSessionConsumer)
            data: Event payload (structure depends on event_type)
            user_profile_id: Optional user ID to target
            target_groups: Optional list of additional group names to target
            session_id: Optional WebSocket session ID to target directly
            
        Returns:
            bool: True if event was successfully emitted, False otherwise
        """
        try:
            channel_layer = get_channel_layer()
            if not channel_layer:
                logger.warning("Channel layer not available, cannot emit event")
                return False
                
            # Format payload based on event type
            timestamp = datetime.now(timezone.utc).isoformat()
            
            # For standard event types, ensure we follow the API contract format
            if event_type in EventService.STANDARD_EVENT_TYPES:
                # Format according to expected event structure
                if event_type == 'debug_info':
                    payload = {
                        'type': 'debug_info',
                        'content': {
                            'timestamp': timestamp,
                            'source': data.get('source', 'event_service'),
                            'level': data.get('level', 'info'),
                            'message': data.get('message', ''),
                            'details': data.get('details', {})
                        }
                    }
                elif event_type == 'system_message':
                    payload = {
                        'type': 'system_message',
                        'content': data.get('content', '')
                    }
                elif event_type == 'chat_message':
                    payload = {
                        'type': 'chat_message',
                        'content': data.get('content', ''),
                        'is_user': data.get('is_user', False)
                    }
                elif event_type == 'error':
                    payload = {
                        'type': 'error',
                        'content': data.get('content', 'An error occurred')
                    }
                elif event_type == 'processing_status':
                    payload = {
                        'type': 'processing_status',
                        'status': data.get('status', 'processing')
                    }
                elif event_type == 'workflow_status':
                    payload = {
                        'type': 'workflow_status',
                        'workflow_id': data.get('workflow_id', ''),
                        'status': data.get('status', 'unknown')
                    }
                elif event_type == 'wheel_data':
                    payload = {
                        'type': 'wheel_data',
                        'wheel': data.get('wheel', {})
                    }
                elif event_type == 'tool_argument_error': # Added handler for new type
                    payload = {
                        'type': 'tool_argument_error',
                        'content': {
                            'tool_name': data.get('tool_name', 'unknown'),
                            'error_message': data.get('error_message', 'Missing tool arguments'),
                            'source': data.get('source', 'unknown')
                        }
                    }
                else:
                    # Generic fallback for other standard types
                    payload = {
                        'type': event_type,
                        **data
                    }
            else:
                # For custom event types, pass through as-is with type
                payload = {
                    'type': event_type,
                    **data
                }
            
            # Determine target groups
            targets = target_groups or []
            
            # Add standard groups based on user_profile_id if provided
            if user_profile_id:
                # Regular user session group
                targets.append(f"user_session_group_{user_profile_id}")
                
                # Admin monitoring group
                targets.append(f"admin_listener_for_user_{user_profile_id}")

            # Add benchmark dashboard group for all error/debug events
            if event_type in ['debug_info', 'error', 'tool_argument_error']:
                targets.append("benchmark_dashboard")
            
            # Add specific session if provided
            if session_id:
                targets.append(session_id)
                
            # Ensure we have at least one target
            if not targets:
                logger.warning(f"No targets specified for event {event_type}")
                return False
                
            # Send to all target groups
            success = True
            for target in targets:
                try:
                    await channel_layer.group_send(target, payload)
                    logger.debug(f"Sent {event_type} to group {target}")
                except Exception as e:
                    logger.error(f"Error sending {event_type} to group {target}: {e}")
                    success = False
            
            return success
        
        except Exception as e:
            logger.error(f"Error in event emission: {e}", exc_info=True)
            return False
    
    @staticmethod
    def emit_event_sync(
        event_type: str,
        data: Dict[str, Any],
        user_profile_id: Optional[str] = None,
        target_groups: Optional[List[str]] = None,
        session_id: Optional[str] = None
    ) -> bool:
        """
        Synchronous wrapper for emit_event.
        Creates an asyncio task for async contexts, or runs in event loop for sync contexts.
        
        Args:
            event_type: Type of event (must match a handler in UserSessionConsumer)
            data: Event payload (structure depends on event_type)
            user_profile_id: Optional user ID to target
            target_groups: Optional list of additional group names to target
            session_id: Optional WebSocket session ID to target directly
            
        Returns:
            bool: True if event was successfully queued or sent, False otherwise
        """
        try:
            # Try to get the running event loop
            try:
                loop = asyncio.get_event_loop()
                
                # If we're already in an event loop, create a task
                if loop.is_running():
                    asyncio.create_task(
                        EventService.emit_event(
                            event_type, data, user_profile_id, target_groups, session_id
                        )
                    )
                    return True
                else:
                    # Otherwise, run the coroutine directly
                    return loop.run_until_complete(
                        EventService.emit_event(
                            event_type, data, user_profile_id, target_groups, session_id
                        )
                    )
            except RuntimeError:
                # Handle case where there's no event loop (e.g., in Django view)
                logger.warning(f"No event loop available for sync event emission: {event_type}")
                
                # Create a new event loop for this operation
                new_loop = asyncio.new_event_loop()
                try:
                    return new_loop.run_until_complete(
                        EventService.emit_event(
                            event_type, data, user_profile_id, target_groups, session_id
                        )
                    )
                finally:
                    new_loop.close()
                    
        except Exception as e:
            logger.error(f"Error in sync event emission: {e}", exc_info=True)
            return False
            
    # Convenience methods for common event types
    
    @staticmethod
    async def emit_debug_info(
        level: str,
        message: str,
        source: str,
        details: Optional[Dict[str, Any]] = None,
        user_profile_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> bool:
        """
        Emit a debug_info event, following the standard format.
        
        Args:
            level: Debug level ('info', 'warning', 'error', 'debug')
            message: Main debug message
            source: Component that generated the debug info
            details: Optional detailed information
            user_profile_id: User profile ID to target
            session_id: Optional specific session ID
        
        Returns:
            bool: Success status
        """
        return await EventService.emit_event(
            event_type='debug_info',
            data={
                'level': level,
                'message': message,
                'source': source,
                'details': details or {}
            },
            user_profile_id=user_profile_id
            # session_id is intentionally omitted here to avoid duplicate delivery
            # Debug info should go to the user group and admin listener group only.
        )
        
    @staticmethod
    async def emit_error(
        message: str,
        user_profile_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> bool:
        """
        Emit an error event to the user.
        
        Args:
            message: Error message
            user_profile_id: User profile ID to target
            session_id: Optional specific session ID
        
        Returns:
            bool: Success status
        """
        return await EventService.emit_event(
            event_type='error',
            data={'content': message},
            user_profile_id=user_profile_id,
            session_id=session_id
        )

    @staticmethod
    async def emit_tool_argument_error(
        tool_name: str,
        error_message: str,
        source: str,
        user_profile_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> bool:
        """
        Emit a tool_argument_error event.

        Args:
            tool_name: Name of the tool that failed.
            error_message: Description of the argument error.
            source: Component where the error occurred.
            user_profile_id: User profile ID to target.
            session_id: Optional specific session ID.

        Returns:
            bool: Success status
        """
        return await EventService.emit_event(
            event_type='tool_argument_error',
            data={
                'tool_name': tool_name,
                'error_message': error_message,
                'source': source
            },
            user_profile_id=user_profile_id,
            session_id=session_id
        )
