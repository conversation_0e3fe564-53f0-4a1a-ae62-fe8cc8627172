# Model imports moved inside methods
# from apps.main.models import <PERSON><PERSON><PERSON>, Agent<PERSON><PERSON>ory, GenericAgent
# from apps.user.models import UserProfile
import asyncio
from django.db import transaction
from django.utils import timezone
from channels.db import database_sync_to_async # Added database_sync_to_async
from asgiref.sync import async_to_sync # Added async_to_sync

class DatabaseService:
    """Abstract base class for database operations"""

    # Keep load_agent_definition sync for now, assuming it might be called from sync contexts too
    def load_agent_definition(self, agent_role):
        """Load an agent definition by role"""
        raise NotImplementedError()
        
    def load_tools(self, agent_definition):
        """Load tools for an agent definition"""
        raise NotImplementedError()

    async def load_tools(self, agent_definition): # Make async
        """Load tools for an agent definition"""
        raise NotImplementedError()

    async def start_run(self, agent_definition, user_profile_id, input_data, state): # Make async
        """Start an agent run and record it in the database"""
        raise NotImplementedError()

    async def complete_run(self, run_id, output_data, state, memory_updates=None, status='completed', error_message=None): # Make async, add error_message
        """Complete an agent run and record results in the database"""
        raise NotImplementedError()

    async def get_memory(self, agent_role, user_profile_id, memory_key): # Make async
        """Get a memory item for an agent and user"""
        raise NotImplementedError()

    async def set_memory(self, agent_role, user_profile_id, memory_key, content, confidence=1.0, expires_at=None): # Make async, add expires_at
        """Store or update a memory item for an agent and user"""
        raise NotImplementedError()

class RealDatabaseService(DatabaseService):
    """Real implementation that uses Django ORM"""
    
    def load_agent_definition(self, agent_role):
        """Load an agent definition from the database"""
        from apps.main.models import GenericAgent # Import inside method
        # This remains synchronous as it's called from sync context potentially
        return GenericAgent.objects.get(role=agent_role, is_active=True)

    async def load_tools(self, agent_definition):
        """Load available tools for an agent asynchronously"""
        from channels.db import database_sync_to_async # Keep this import

        @database_sync_to_async
        def _get_tools():
            # No model imports needed here as we access via agent_definition relation
            # Access the related manager and fetch tools within the sync context
            # Convert to dicts here to avoid lazy loading later
            if agent_definition and hasattr(agent_definition, 'available_tools'):
                 # Eagerly fetch and convert to dicts
                return [tool.to_dict() for tool in agent_definition.available_tools.filter(is_active=True)]
            return []

        return await _get_tools()

    @database_sync_to_async
    def _start_run_sync(self, agent_definition, user_profile_id, input_data, state):
        """Synchronous helper to create the AgentRun record."""
        # Imports moved inside method
        from django.utils import timezone
        from apps.main.models import AgentRun
        from apps.user.models import UserProfile
        from django.db import transaction

        with transaction.atomic():
            # Ensure user_profile exists before creating the run
            try:
                user_profile = UserProfile.objects.get(id=user_profile_id)
            except UserProfile.DoesNotExist:
                # Handle case where user profile doesn't exist
                # Log error or raise a specific exception
                # For now, let's re-raise to indicate failure
                raise ValueError(f"UserProfile with id {user_profile_id} not found.")

            run = AgentRun.objects.create(
                agent=agent_definition, # Use the passed agent_definition object
                workflow_id=state.get('workflow_id', None),
                user_profile=user_profile, # Use the fetched user_profile object
                started_at=timezone.now(),
                input_data=input_data,
                initial_state=state,
                status='running'
            )
            return run # Return the created run object

    async def start_run(self, agent_definition, user_profile_id, input_data, state): # Keep async from base
        """Start an agent run and record it in the database (async wrapper)."""
        # Call the synchronous, decorated helper function
        return await self._start_run_sync(agent_definition, user_profile_id, input_data, state)

    @database_sync_to_async
    def _complete_run_sync(self, run_id, output_data, state, memory_updates_ref, status, error_message):
        """Synchronous helper to update the AgentRun record."""
        # Imports moved inside method
        from django.utils import timezone
        from apps.main.models import AgentRun # Import inside method
        from django.db import transaction
        import logging
        logger = logging.getLogger(__name__)

        run = None # Initialize run to None
        try:
            with transaction.atomic():
                run = AgentRun.objects.select_for_update().get(id=run_id) # Lock the row
                run.completed_at = timezone.now()
                run.output_data = output_data
                run.final_state = state
                # Store the reference to memory updates, not the updates themselves here
                run.memory_updates = memory_updates_ref
                run.status = status
                run.error_message = error_message
                run.save()
                logger.debug(f"Successfully saved AgentRun {run_id} with status {status}")
                # Return necessary info needed for async memory updates
                return {"agent_role": run.agent.role, "user_profile_id": run.user_profile_id}
        except AgentRun.DoesNotExist:
             logger.error(f"AgentRun with id {run_id} not found during completion.")
             return None # Indicate failure
        except Exception as e:
             logger.error(f"Error saving AgentRun {run_id} in _complete_run_sync: {e}", exc_info=True)
             # Depending on policy, might re-raise or return None
             return None # Indicate failure

    async def complete_run(self, run_id, output_data, state, memory_updates=None, status='completed', error_message=None): # Keep async from base
        """Complete an agent run: update AgentRun sync, then apply memory updates async."""
        import logging # Add logging
        logger = logging.getLogger(__name__) # Get logger

        # Step 1: Update the AgentRun record synchronously
        run_info = await self._complete_run_sync(
            run_id=run_id,
            output_data=output_data,
            state=state,
            memory_updates_ref=memory_updates, # Pass the original dict for storage in AgentRun.memory_updates
            status=status,
            error_message=error_message
        )

        if run_info is None:
             logger.error(f"Failed to update AgentRun record for run_id {run_id}. Skipping memory updates.")
             # Decide how to handle this - raise error? Return None?
             # For now, log and return None to indicate partial failure.
             return None

        # Step 2: Apply memory updates asynchronously (outside the sync transaction)
        if memory_updates:
            agent_role = run_info["agent_role"]
            user_profile_id = run_info["user_profile_id"]
            logger.debug(f"Applying {len(memory_updates)} memory updates for run {run_id} asynchronously...")
            tasks = []
            for key, content in memory_updates.items():
                confidence = 1.0
                expires_at = None
                if isinstance(content, dict):
                    confidence = content.pop('confidence', 1.0)
                    expires_at = content.pop('expires_at', None)
                    if not content:
                        content = None

                # Create an async task for each memory update
                tasks.append(
                    self.set_memory( # Call the async method directly
                        agent_role=agent_role,
                        user_profile_id=user_profile_id,
                        memory_key=key,
                        content=content,
                        confidence=confidence,
                        expires_at=expires_at
                    )
                )
            # Run memory updates concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            for i, result in enumerate(results):
                 if isinstance(result, Exception):
                      key = list(memory_updates.keys())[i] # Get corresponding key
                      logger.error(f"Error applying memory update for key '{key}' in run {run_id}: {result}", exc_info=result)
                      # Decide how to handle partial failures - log and continue?

        # Return something indicating success (e.g., the run_id or a status)
        # Since the AgentRun object was saved in the sync part, we can't return it directly here.
        # Returning the run_id might be sufficient.
        logger.debug(f"Completed run {run_id} processing (including async memory updates).")
        return {"run_id": run_id, "status": status} # Return dict indicating completion

    @database_sync_to_async
    def _get_memory_sync(self, agent_role, user_profile_id, memory_key):
        """Synchronous helper to get a memory item."""
        # Import moved inside method
        from apps.main.models import AgentMemory

        try:
            memory = AgentMemory.objects.get(
                agent_role=agent_role,
                user_profile_id=user_profile_id,
                memory_key=memory_key
            )
            # Return the content directly from the sync function
            return memory.content
        except AgentMemory.DoesNotExist:
            # Return None if not found
            return None

    async def get_memory(self, agent_role, user_profile_id, memory_key): # Keep async from base
        """Get a memory item for an agent and user (async wrapper)."""
        # Call the synchronous, decorated helper function
        return await self._get_memory_sync(agent_role, user_profile_id, memory_key)

    @database_sync_to_async
    def _set_memory_sync(self, agent_role, user_profile_id, memory_key, content, confidence, expires_at):
        """Synchronous helper to store or update a memory item."""
        # Imports moved inside method
        from apps.main.models import AgentMemory
        from django.db import transaction
        from django.utils import timezone

        with transaction.atomic():
            # Perform the update_or_create operation
            memory, created = AgentMemory.objects.update_or_create(
                 agent_role=agent_role,
                 user_profile_id=user_profile_id,
                 memory_key=memory_key,
                 defaults={
                     'content': content,
                     'confidence': confidence,
                     'expires_at': expires_at,
                     # Update last_accessed only if the record already existed
                     'last_accessed': timezone.now() if not created else None
                 }
             )

            # Update access count if the record was not created (i.e., it was updated)
            if not created:
                # Increment access count atomically to avoid race conditions if needed,
                # though within this transaction it might be okay.
                # Using F expressions is safer for concurrent updates if the transaction
                # isolation level isn't serializable.
                # from django.db.models import F
                # memory.access_count = F('access_count') + 1
                memory.access_count += 1
                memory.save(update_fields=['access_count', 'last_accessed']) # Ensure last_accessed is saved on update too
            # Return the memory object from the sync function
            return memory

    async def set_memory(self, agent_role, user_profile_id, memory_key, content, confidence=1.0, expires_at=None): # Keep async from base
        """Store or update a memory item for an agent and user (async wrapper)."""
        # Call the synchronous, decorated helper function
        return await self._set_memory_sync(agent_role, user_profile_id, memory_key, content, confidence, expires_at)
