"""
Workflow Result Handler Service

This service is responsible for processing workflow results and sending them
to the appropriate WebSocket clients. It acts as the bridge between the agent
workflow system and the real-time communication layer.

Key responsibilities:
1. Process completed workflow results
2. Format results based on workflow type
3. Send formatted results to WebSocket consumers
4. Handle error conditions gracefully
"""

import json
import logging
import asyncio
from typing import Dict, Any, Optional, List
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.db import close_old_connections

# Configure logging
logger = logging.getLogger(__name__)

class WorkflowResultHandler:
    """
    Service for processing workflow results and sending them to WebSocket clients.
    
    This class handles the final stage of the workflow process, ensuring that
    results are properly formatted and delivered to the appropriate clients.
    """
    
    def __init__(self):
        """Initialize the workflow result handler."""
        self.channel_layer = get_channel_layer()
    
    async def process_result(self, workflow_id: str, result: Dict[str, Any], task_id: str, workflow_type: str):
        """
        Process a completed workflow result and send it to the appropriate clients.
        
        This method is designed to be called by Celery signal handlers when a 
        workflow task completes.
        
        Args:
            workflow_id: Unique identifier for the workflow
            result: The workflow result data
            task_id: The Celery task ID
            workflow_type: Type of workflow (wheel_generation, activity_feedback, etc.)
        """
        try:
            logger.info(f"Processing workflow result: {workflow_id} ({workflow_type})")
            
            # Get the user session group from the result if available
            user_ws_session = result.get('user_ws_session_name')
            user_profile_id = result.get('user_profile_id')
            
            if not user_ws_session:
                logger.warning(f"No WebSocket session found in result for workflow: {workflow_id}")
                # Fallback to broadcasting to all game sessions if we can't find a specific one
                user_ws_session = 'game'
            
            # Format the result based on workflow type
            formatted_messages = self._format_result_messages(result, workflow_type)
            
            # Send all formatted messages to the client
            for message in formatted_messages:
                await self.channel_layer.group_send(user_ws_session, message)
                    
            # Send workflow status update
            await self.channel_layer.group_send(
                user_ws_session,
                {
                    'type': 'workflow_status',
                    'workflow_id': workflow_id,
                    'status': 'completed'
                }
            )
            
            logger.info(f"Successfully sent workflow result for: {workflow_id}")
            
        except Exception as e:
            logger.error(f"Error processing workflow result: {str(e)}", exc_info=True)
            
            # Try to send error message if possible
            try:
                if user_ws_session:
                    await self.channel_layer.group_send(
                        user_ws_session,
                        {
                            'type': 'error',
                            'content': "Error processing your request."
                        }
                    )
            except:
                pass
    

    def sync_process_result(self, workflow_id, result, task_id, workflow_type):
        """Synchronous wrapper for process_result"""
        import asyncio
        loop = asyncio.new_event_loop()
        try:
            return loop.run_until_complete(
                self.process_result(workflow_id, result, task_id, workflow_type)
            )
        finally:
            loop.close()

    def _format_result_messages(self, result: Dict[str, Any], workflow_type: str) -> List[Dict[str, Any]]:
        """
        Format result data into appropriate WebSocket messages based on workflow type.
        
        Different workflow types need different message formatting to meet the API contract.
        
        Args:
            result: The workflow result data
            workflow_type: Type of workflow
            
        Returns:
            List of formatted messages to send to the client
        """
        messages = []
        
        # Always send a processing_status message to indicate completion
        messages.append({
            'type': 'processing_status',
            'status': 'completed'
        })
        
        # Extract output_data from result if available
        output_data = result.get('output_data', {})
        if not output_data and 'result' in result:
            # Handle alternative result structure
            output_data = result['result'].get('output_data', {})
        
        # Handle specific workflow types
        if workflow_type == 'wheel_generation':
            # Add chat response if available
            if 'user_response' in output_data:
                messages.append({
                    'type': 'chat_message',
                    'content': output_data['user_response'],
                    'is_user': False
                })
            
            # Add wheel data if available
            if 'wheel' in output_data:
                messages.append({
                    'type': 'wheel_data',
                    'wheel': output_data['wheel']
                })
                
        elif workflow_type == 'post_spin':
            # Add activity details response
            if 'user_response' in output_data:
                messages.append({
                    'type': 'chat_message',
                    'content': output_data['user_response'],
                    'is_user': False
                })
                
            # Add activity details if available
            if 'activity_details' in output_data:
                messages.append({
                    'type': 'activity_details',
                    'details': output_data['activity_details']
                })

        elif workflow_type in ['activity_feedback', 'pre_spin_feedback', 'user_onboarding', 'progress_review', 'discussion']: # Added 'discussion'
            # These all send chat messages in response
            if 'user_response' in output_data:
                messages.append({
                    'type': 'chat_message',
                    'content': output_data['user_response'],
                    'is_user': False
                })
        
        # If no specific messages were added, add a generic message
        if not any(msg.get('type') == 'chat_message' for msg in messages):
            messages.append({
                'type': 'chat_message',
                'content': "I've processed your request.",
                'is_user': False
            })
        
        return messages

    async def handle_error(self, workflow_id: str, error: str, task_id: str, workflow_type: str = None, user_ws_session: str = None): # Changed to async def
        """
        Handle workflow errors and notify the client. (Now async)

        Args:
            workflow_id: Unique identifier for the workflow
            error: Error message or description
            task_id: The Celery task ID
            workflow_type: Optional workflow type
            user_ws_session: Optional WebSocket session name
        """
        try:
            logger.error(f"Workflow error: {workflow_id} - {error}")
            
            if not user_ws_session:
                # Fallback to game group
                user_ws_session = 'game'

            # Send error message directly using await
            await self.channel_layer.group_send(
                user_ws_session,
                {
                    'type': 'error',
                    'content': "Error processing your request."
                }
            )

            # Send processing status update directly using await
            await self.channel_layer.group_send(
                user_ws_session,
                {
                    'type': 'processing_status',
                    'status': 'error'
                }
            )

            # Send workflow status update directly using await
            await self.channel_layer.group_send(
                user_ws_session,
                {
                    'type': 'workflow_status',
                    'workflow_id': workflow_id,
                    'status': 'failed'
                }
            )
            
        except Exception as e:
            logger.error(f"Error sending workflow error notification: {str(e)}", exc_info=True)

    # Removed sync_handle_error wrapper as handle_error is now async

# Create singleton instance for importing
workflow_result_handler = WorkflowResultHandler()
