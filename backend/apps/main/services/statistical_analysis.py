"""
Statistical Analysis Service for Workflow Benchmarking

This service provides statistical analysis methods for benchmark results, including:
- <PERSON>'s t-test for comparing benchmark runs
- Confidence interval calculations
- P-value reporting for statistical significance
- Effect size calculations (<PERSON>'s d)
- Time series analysis for quality metrics
- Regression detection

The service is designed to work with the BenchmarkRun model and provides
both synchronous and asynchronous methods for analysis.
"""

import logging
import math
import statistics
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union
from decimal import Decimal
from scipy.stats import ttest_ind_from_stats, norm
from asgiref.sync import sync_to_async
from django.db.models import Avg, StdDev, Count, F, Q, Min, Max
from django.utils import timezone
from datetime import datetime, timedelta

# Import models
from apps.main.models import BenchmarkRun, BenchmarkScenario

logger = logging.getLogger(__name__)


class StatisticalAnalysisService:
    """
    Service for performing statistical analysis on benchmark results.
    """

    def __init__(self):
        """Initialize the statistical analysis service."""
        pass

    @sync_to_async
    def _get_benchmark_runs_sync(self, scenario_id=None, agent_role=None,
                                 start_date=None, end_date=None, limit=None):
        """
        Synchronous helper to fetch benchmark runs with filtering options.

        Args:
            scenario_id: Optional ID of the scenario to filter by
            agent_role: Optional agent role to filter by
            start_date: Optional start date for filtering
            end_date: Optional end date for filtering
            limit: Optional limit on the number of runs to return

        Returns:
            List of BenchmarkRun objects
        """
        queryset = BenchmarkRun.objects.all()

        if scenario_id:
            queryset = queryset.filter(scenario_id=scenario_id)

        if agent_role:
            queryset = queryset.filter(agent_definition__role=agent_role)

        if start_date:
            queryset = queryset.filter(execution_date__gte=start_date)

        if end_date:
            queryset = queryset.filter(execution_date__lte=end_date)

        queryset = queryset.order_by('-execution_date')

        if limit:
            queryset = queryset[:limit]

        return list(queryset)

    async def get_benchmark_runs(self, scenario_id=None, agent_role=None,
                                start_date=None, end_date=None, limit=None):
        """
        Asynchronous method to fetch benchmark runs with filtering options.

        Args:
            scenario_id: Optional ID of the scenario to filter by
            agent_role: Optional agent role to filter by
            start_date: Optional start date for filtering
            end_date: Optional end date for filtering
            limit: Optional limit on the number of runs to return

        Returns:
            List of BenchmarkRun objects
        """
        return await self._get_benchmark_runs_sync(
            scenario_id=scenario_id,
            agent_role=agent_role,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )

    @staticmethod
    def calculate_welchs_t_test(mean1, std1, n1, mean2, std2, n2):
        """
        Calculate Welch's t-test for two samples with unequal variances.

        Args:
            mean1: Mean of the first sample
            std1: Standard deviation of the first sample
            n1: Size of the first sample
            mean2: Mean of the second sample
            std2: Standard deviation of the second sample
            n2: Size of the second sample

        Returns:
            Tuple of (t-statistic, p-value)
        """
        # Check for valid inputs
        if n1 <= 1 or n2 <= 1:
            raise ValueError("Sample sizes must be greater than 1")

        if std1 <= 0 or std2 <= 0:
            raise ValueError("Standard deviations must be positive")

        # Calculate t-statistic and p-value using scipy
        t_stat, p_value = ttest_ind_from_stats(
            mean1=mean1,
            std1=std1,
            nobs1=n1,
            mean2=mean2,
            std2=std2,
            nobs2=n2,
            equal_var=False  # Use Welch's t-test (unequal variances)
        )

        # Check for NaN p-value
        if p_value is not None and math.isnan(p_value):
            p_value = None

        return t_stat, p_value

    @staticmethod
    def calculate_cohens_d(mean1, std1, n1, mean2, std2, n2):
        """
        Calculate Cohen's d effect size for two samples.

        Args:
            mean1: Mean of the first sample
            std1: Standard deviation of the first sample
            n1: Size of the first sample
            mean2: Mean of the second sample
            std2: Standard deviation of the second sample
            n2: Size of the second sample

        Returns:
            Cohen's d effect size
        """
        # Check for valid inputs
        if n1 <= 1 or n2 <= 1:
            raise ValueError("Sample sizes must be greater than 1")

        if std1 <= 0 or std2 <= 0:
            raise ValueError("Standard deviations must be positive")

        # Calculate pooled standard deviation
        pooled_std = math.sqrt(((n1 - 1) * std1**2 + (n2 - 1) * std2**2) / (n1 + n2 - 2))

        # Calculate Cohen's d
        d = abs(mean1 - mean2) / pooled_std

        return d

    @staticmethod
    def interpret_cohens_d(d):
        """
        Interpret Cohen's d effect size.

        Args:
            d: Cohen's d effect size

        Returns:
            String interpretation of the effect size
        """
        if d < 0.2:
            return "negligible"
        elif d < 0.5:
            return "small"
        elif d < 0.8:
            return "medium"
        else:
            return "large"

    @staticmethod
    def calculate_confidence_interval(mean, std, n, confidence_level=0.95):
        """
        Calculate confidence interval for a sample mean.

        Args:
            mean: Sample mean
            std: Sample standard deviation
            n: Sample size
            confidence_level: Confidence level (default: 0.95)

        Returns:
            Tuple of (lower_bound, upper_bound)
        """
        # Check for valid inputs
        if n <= 1:
            raise ValueError("Sample size must be greater than 1")

        if std <= 0:
            raise ValueError("Standard deviation must be positive")

        # Calculate standard error
        se = std / math.sqrt(n)

        # Calculate z-score for the given confidence level
        z = norm.ppf((1 + confidence_level) / 2)

        # Calculate margin of error
        margin_of_error = z * se

        # Calculate confidence interval
        lower_bound = mean - margin_of_error
        upper_bound = mean + margin_of_error

        return lower_bound, upper_bound

    @staticmethod
    def bootstrap_confidence_interval(data, confidence_level=0.95, n_resamples=1000):
        """
        Calculate bootstrap confidence interval for a sample.

        Args:
            data: Sample data
            confidence_level: Confidence level (default: 0.95)
            n_resamples: Number of bootstrap resamples (default: 1000)

        Returns:
            Tuple of (lower_bound, upper_bound)
        """
        if not data:
            raise ValueError("Data cannot be empty")

        # Convert data to numpy array
        data = np.array(data)

        # Generate bootstrap resamples
        bootstrap_means = []
        for _ in range(n_resamples):
            resample = np.random.choice(data, size=len(data), replace=True)
            bootstrap_means.append(np.mean(resample))

        # Calculate percentile confidence interval
        alpha = (1 - confidence_level) / 2
        lower_bound = np.percentile(bootstrap_means, alpha * 100)
        upper_bound = np.percentile(bootstrap_means, (1 - alpha) * 100)

        return lower_bound, upper_bound

    async def compare_benchmark_runs(self, run_id1, run_id2, alpha=0.05):
        """
        Compare two benchmark runs using Welch's t-test and Cohen's d.

        Args:
            run_id1: ID of the first benchmark run
            run_id2: ID of the second benchmark run
            alpha: Significance level (default: 0.05)

        Returns:
            Dictionary with comparison results
        """
        # Fetch the benchmark runs
        runs = await self._get_benchmark_runs_sync(limit=None)

        # Find the specified runs
        run1 = next((run for run in runs if str(run.id) == str(run_id1)), None)
        run2 = next((run for run in runs if str(run.id) == str(run_id2)), None)

        if not run1 or not run2:
            missing_runs = []
            if not run1:
                missing_runs.append(run_id1)
            if not run2:
                missing_runs.append(run_id2)
            raise ValueError(f"Benchmark run(s) not found: {', '.join(missing_runs)}")

        # Check if both runs have the necessary statistics
        required_stats = ['mean_duration', 'std_dev', 'runs_count']
        for run, run_id in [(run1, run_id1), (run2, run_id2)]:
            missing_stats = [stat for stat in required_stats if getattr(run, stat, None) is None]
            if missing_stats:
                raise ValueError(f"Benchmark run {run_id} is missing required statistics: {', '.join(missing_stats)}")

        # Calculate Welch's t-test
        t_stat, p_value = self.calculate_welchs_t_test(
            mean1=run1.mean_duration,
            std1=run1.std_dev,
            n1=run1.runs_count,
            mean2=run2.mean_duration,
            std2=run2.std_dev,
            n2=run2.runs_count
        )

        # Determine significance
        is_significant = p_value < alpha if p_value is not None else None

        # Calculate Cohen's d effect size
        cohens_d = self.calculate_cohens_d(
            mean1=run1.mean_duration,
            std1=run1.std_dev,
            n1=run1.runs_count,
            mean2=run2.mean_duration,
            std2=run2.std_dev,
            n2=run2.runs_count
        )

        # Interpret Cohen's d
        effect_size_interpretation = self.interpret_cohens_d(cohens_d)

        # Calculate confidence intervals
        ci_run1 = self.calculate_confidence_interval(
            mean=run1.mean_duration,
            std=run1.std_dev,
            n=run1.runs_count
        )

        ci_run2 = self.calculate_confidence_interval(
            mean=run2.mean_duration,
            std=run2.std_dev,
            n=run2.runs_count
        )

        # Determine if there's a regression
        is_regression = run1.mean_duration > run2.mean_duration if is_significant else None

        # Calculate percent change
        percent_change = ((run1.mean_duration - run2.mean_duration) / run2.mean_duration) * 100 if run2.mean_duration != 0 else None

        # Prepare comparison results
        comparison_results = {
            'run1': {
                'id': str(run1.id),
                'scenario_name': run1.scenario.name,
                'agent_role': run1.agent_definition.role,
                'execution_date': run1.execution_date.isoformat(),
                'mean_duration': run1.mean_duration,
                'std_dev': run1.std_dev,
                'runs_count': run1.runs_count,
                'confidence_interval': ci_run1
            },
            'run2': {
                'id': str(run2.id),
                'scenario_name': run2.scenario.name,
                'agent_role': run2.agent_definition.role,
                'execution_date': run2.execution_date.isoformat(),
                'mean_duration': run2.mean_duration,
                'std_dev': run2.std_dev,
                'runs_count': run2.runs_count,
                'confidence_interval': ci_run2
            },
            'statistical_comparison': {
                't_statistic': t_stat,
                'p_value': p_value,
                'is_significant': is_significant,
                'alpha': alpha,
                'cohens_d': cohens_d,
                'effect_size': effect_size_interpretation,
                'is_regression': is_regression,
                'percent_change': percent_change
            }
        }

        return comparison_results

    @sync_to_async
    def _get_benchmark_run_history_sync(self, scenario_id, agent_role=None, limit=10):
        """
        Synchronous helper to fetch benchmark run history for a scenario.

        Args:
            scenario_id: ID of the scenario
            agent_role: Optional agent role to filter by
            limit: Maximum number of runs to return

        Returns:
            List of BenchmarkRun objects
        """
        queryset = BenchmarkRun.objects.filter(scenario_id=scenario_id)

        if agent_role:
            queryset = queryset.filter(agent_definition__role=agent_role)

        queryset = queryset.order_by('-execution_date')

        if limit:
            queryset = queryset[:limit]

        return list(queryset)

    async def analyze_benchmark_trend(self, scenario_id, agent_role=None, time_window=30, min_runs=5):
        """
        Analyze the trend of benchmark results over time.

        Args:
            scenario_id: ID of the scenario
            agent_role: Optional agent role to filter by
            time_window: Time window in days (default: 30)
            min_runs: Minimum number of runs required for analysis (default: 5)

        Returns:
            Dictionary with trend analysis results
        """
        # Calculate start date based on time window
        start_date = timezone.now() - timedelta(days=time_window)

        # Fetch benchmark runs
        runs = await self._get_benchmark_run_history_sync(
            scenario_id=scenario_id,
            agent_role=agent_role
        )

        # Filter runs by start date
        runs = [run for run in runs if run.execution_date >= start_date]

        if len(runs) < min_runs:
            return {
                'status': 'insufficient_data',
                'message': f'Insufficient data for trend analysis. Found {len(runs)} runs, but {min_runs} are required.',
                'runs_found': len(runs),
                'min_runs_required': min_runs
            }

        # Sort runs by execution date
        runs.sort(key=lambda run: run.execution_date)

        # Extract data for analysis
        dates = [run.execution_date for run in runs]
        mean_durations = [run.mean_duration for run in runs]
        std_devs = [run.std_dev for run in runs]
        success_rates = [run.success_rate for run in runs]
        semantic_scores = [run.semantic_score for run in runs if run.semantic_score is not None]

        # Calculate trend for mean duration
        duration_trend = self._calculate_trend(mean_durations)

        # Calculate trend for success rate
        success_rate_trend = self._calculate_trend(success_rates)

        # Calculate trend for semantic score (if available)
        semantic_score_trend = self._calculate_trend(semantic_scores) if semantic_scores else None

        # Calculate regression detection
        regression_detected = self._detect_regression(runs)

        # Prepare trend analysis results
        trend_analysis = {
            'scenario_id': scenario_id,
            'agent_role': agent_role,
            'time_window_days': time_window,
            'runs_analyzed': len(runs),
            'date_range': {
                'start': dates[0].isoformat(),
                'end': dates[-1].isoformat()
            },
            'duration_metrics': {
                'trend': duration_trend,
                'current': mean_durations[-1],
                'min': min(mean_durations),
                'max': max(mean_durations),
                'percent_change': ((mean_durations[-1] - mean_durations[0]) / mean_durations[0]) * 100 if mean_durations[0] != 0 else None
            },
            'success_rate_metrics': {
                'trend': success_rate_trend,
                'current': success_rates[-1],
                'min': min(success_rates),
                'max': max(success_rates),
                'percent_change': ((success_rates[-1] - success_rates[0]) / success_rates[0]) * 100 if success_rates[0] != 0 else None
            },
            'regression_detection': regression_detected
        }

        # Add semantic score metrics if available
        if semantic_scores:
            trend_analysis['semantic_score_metrics'] = {
                'trend': semantic_score_trend,
                'current': semantic_scores[-1],
                'min': min(semantic_scores),
                'max': max(semantic_scores),
                'percent_change': ((semantic_scores[-1] - semantic_scores[0]) / semantic_scores[0]) * 100 if semantic_scores[0] != 0 else None
            }

        return trend_analysis

    @staticmethod
    def _calculate_trend(values):
        """
        Calculate the trend of a series of values.

        Args:
            values: List of values

        Returns:
            String indicating the trend direction
        """
        if not values or len(values) < 2:
            return "insufficient_data"

        # Calculate simple linear regression
        n = len(values)
        x = list(range(n))

        # Calculate means
        mean_x = sum(x) / n
        mean_y = sum(values) / n

        # Calculate slope
        numerator = sum((x[i] - mean_x) * (values[i] - mean_y) for i in range(n))
        denominator = sum((x[i] - mean_x) ** 2 for i in range(n))

        if denominator == 0:
            return "stable"

        slope = numerator / denominator

        # Determine trend direction
        if abs(slope) < 0.01 * mean_y:  # Less than 1% change per step
            return "stable"
        elif slope > 0:
            return "increasing"
        else:
            return "decreasing"

    @staticmethod
    def _detect_regression(runs):
        """
        Detect performance regression in a series of benchmark runs.

        Args:
            runs: List of BenchmarkRun objects

        Returns:
            Dictionary with regression detection results
        """
        if not runs or len(runs) < 3:
            return {
                'status': 'insufficient_data',
                'message': 'Insufficient data for regression detection.'
            }

        # Get the last three runs
        last_runs = runs[-3:]

        # Check if the last run is significantly worse than the previous runs
        last_run = last_runs[-1]
        prev_runs = last_runs[:-1]

        # Calculate mean and standard deviation of previous runs
        prev_mean_durations = [run.mean_duration for run in prev_runs]
        prev_mean = statistics.mean(prev_mean_durations)
        prev_std = statistics.stdev(prev_mean_durations) if len(prev_mean_durations) > 1 else 0

        # Check if the last run is significantly worse
        if prev_std > 0:
            z_score = (last_run.mean_duration - prev_mean) / prev_std

            # Define regression threshold (e.g., 2 standard deviations)
            regression_threshold = 2.0

            if z_score > regression_threshold:
                return {
                    'status': 'regression_detected',
                    'message': f'Performance regression detected. The last run is {z_score:.2f} standard deviations worse than the previous runs.',
                    'z_score': z_score,
                    'last_run_id': str(last_run.id),
                    'last_run_mean': last_run.mean_duration,
                    'previous_runs_mean': prev_mean,
                    'percent_worse': ((last_run.mean_duration - prev_mean) / prev_mean) * 100
                }

        return {
            'status': 'no_regression',
            'message': 'No performance regression detected.'
        }

    @sync_to_async
    def _get_benchmark_run_aggregates_sync(self, scenario_id=None, agent_role=None,
                                          start_date=None, end_date=None):
        """
        Synchronous helper to fetch benchmark run aggregates.

        Args:
            scenario_id: Optional ID of the scenario to filter by
            agent_role: Optional agent role to filter by
            start_date: Optional start date for filtering
            end_date: Optional end date for filtering

        Returns:
            Dictionary with aggregate statistics
        """
        queryset = BenchmarkRun.objects.all()

        if scenario_id:
            queryset = queryset.filter(scenario_id=scenario_id)

        if agent_role:
            queryset = queryset.filter(agent_definition__role=agent_role)

        if start_date:
            queryset = queryset.filter(execution_date__gte=start_date)

        if end_date:
            queryset = queryset.filter(execution_date__lte=end_date)

        # Calculate aggregates
        aggregates = queryset.aggregate(
            avg_mean_duration=Avg('mean_duration'),
            avg_std_dev=Avg('std_dev'),
            avg_success_rate=Avg('success_rate'),
            avg_semantic_score=Avg('semantic_score'),
            count=Count('id'),
            min_mean_duration=Min('mean_duration'),
            max_mean_duration=Max('mean_duration')
        )

        return aggregates

    async def get_benchmark_statistics(self, scenario_id=None, agent_role=None,
                                      start_date=None, end_date=None):
        """
        Get aggregate statistics for benchmark runs.

        Args:
            scenario_id: Optional ID of the scenario to filter by
            agent_role: Optional agent role to filter by
            start_date: Optional start date for filtering
            end_date: Optional end date for filtering

        Returns:
            Dictionary with aggregate statistics
        """
        # Calculate start date if not provided
        if not start_date:
            start_date = timezone.now() - timedelta(days=30)

        # Calculate end date if not provided
        if not end_date:
            end_date = timezone.now()

        # Fetch aggregates
        aggregates = await self._get_benchmark_run_aggregates_sync(
            scenario_id=scenario_id,
            agent_role=agent_role,
            start_date=start_date,
            end_date=end_date
        )

        # Prepare statistics
        statistics = {
            'time_period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            },
            'filters': {
                'scenario_id': scenario_id,
                'agent_role': agent_role
            },
            'count': aggregates['count'],
            'duration_statistics': {
                'average_mean': aggregates['avg_mean_duration'],
                'average_std_dev': aggregates['avg_std_dev'],
                'min_mean': aggregates['min_mean_duration'],
                'max_mean': aggregates['max_mean_duration']
            },
            'success_rate_statistics': {
                'average': aggregates['avg_success_rate']
            }
        }

        # Add semantic score statistics if available
        if aggregates['avg_semantic_score'] is not None:
            statistics['semantic_score_statistics'] = {
                'average': aggregates['avg_semantic_score']
            }

        return statistics

    @sync_to_async
    def _update_benchmark_run_comparison_sync(self, run_id, compared_to_run_id,
                                             p_value, is_significant, cohens_d):
        """
        Synchronous helper to update benchmark run comparison fields.

        Args:
            run_id: ID of the benchmark run to update
            compared_to_run_id: ID of the run being compared to
            p_value: P-value from the statistical test
            is_significant: Boolean indicating if the difference is significant
            cohens_d: Cohen's d effect size

        Returns:
            Boolean indicating if the update was successful
        """
        try:
            # Get the benchmark run
            run = BenchmarkRun.objects.get(id=run_id)

            # Convert NumPy values to Python native types
            if p_value is not None:
                if hasattr(p_value, 'item'):
                    p_value = p_value.item()  # Convert numpy scalar to Python scalar
                p_value = float(p_value)

            if is_significant is not None:
                if hasattr(is_significant, 'item'):
                    is_significant = is_significant.item()
                is_significant = bool(is_significant)

            if cohens_d is not None:
                if hasattr(cohens_d, 'item'):
                    cohens_d = cohens_d.item()
                cohens_d = float(cohens_d)

            # Update comparison fields
            run.compared_to_run_id = compared_to_run_id
            run.performance_p_value = p_value
            run.is_performance_significant = is_significant

            # Add Cohen's d to raw_results
            if 'statistical_comparison' not in run.raw_results:
                run.raw_results['statistical_comparison'] = {}

            run.raw_results['statistical_comparison']['cohens_d'] = cohens_d
            run.raw_results['statistical_comparison']['effect_size'] = self.interpret_cohens_d(cohens_d)

            # Save the changes
            run.save(update_fields=['compared_to_run_id', 'performance_p_value',
                                   'is_performance_significant', 'raw_results'])

            return True
        except BenchmarkRun.DoesNotExist:
            logger.error(f"Benchmark run {run_id} not found.")
            return False
        except Exception as e:
            logger.error(f"Error updating benchmark run comparison: {e}", exc_info=True)
            return False

    async def update_benchmark_run_comparison(self, run_id, compared_to_run_id):
        """
        Update benchmark run comparison fields.

        Args:
            run_id: ID of the benchmark run to update
            compared_to_run_id: ID of the run being compared to

        Returns:
            Dictionary with update results
        """
        # Fetch the benchmark runs
        runs = await self._get_benchmark_runs_sync(limit=None)

        # Find the specified runs
        run = next((r for r in runs if str(r.id) == str(run_id)), None)
        compared_to_run = next((r for r in runs if str(r.id) == str(compared_to_run_id)), None)

        if not run or not compared_to_run:
            missing_runs = []
            if not run:
                missing_runs.append(run_id)
            if not compared_to_run:
                missing_runs.append(compared_to_run_id)
            return {
                'status': 'error',
                'message': f"Benchmark run(s) not found: {', '.join(missing_runs)}"
            }

        # Check if both runs have the necessary statistics
        required_stats = ['mean_duration', 'std_dev', 'runs_count']
        for r, r_id in [(run, run_id), (compared_to_run, compared_to_run_id)]:
            missing_stats = [stat for stat in required_stats if getattr(r, stat, None) is None]
            if missing_stats:
                return {
                    'status': 'error',
                    'message': f"Benchmark run {r_id} is missing required statistics: {', '.join(missing_stats)}"
                }

        # Calculate Welch's t-test
        t_stat, p_value = self.calculate_welchs_t_test(
            mean1=run.mean_duration,
            std1=run.std_dev,
            n1=run.runs_count,
            mean2=compared_to_run.mean_duration,
            std2=compared_to_run.std_dev,
            n2=compared_to_run.runs_count
        )

        # Determine significance
        alpha = 0.05
        is_significant = p_value < alpha if p_value is not None else None

        # Calculate Cohen's d effect size
        cohens_d = self.calculate_cohens_d(
            mean1=run.mean_duration,
            std1=run.std_dev,
            n1=run.runs_count,
            mean2=compared_to_run.mean_duration,
            std2=compared_to_run.std_dev,
            n2=compared_to_run.runs_count
        )

        # Update benchmark run comparison fields
        update_successful = await self._update_benchmark_run_comparison_sync(
            run_id=run_id,
            compared_to_run_id=compared_to_run_id,
            p_value=p_value,
            is_significant=is_significant,
            cohens_d=cohens_d
        )

        if update_successful:
            return {
                'status': 'success',
                'message': f"Benchmark run {run_id} comparison updated successfully.",
                'comparison': {
                    'compared_to_run_id': str(compared_to_run_id),
                    't_statistic': t_stat,
                    'p_value': p_value,
                    'is_significant': is_significant,
                    'alpha': alpha,
                    'cohens_d': cohens_d,
                    'effect_size': self.interpret_cohens_d(cohens_d)
                }
            }
        else:
            return {
                'status': 'error',
                'message': f"Failed to update benchmark run {run_id} comparison."
            }
