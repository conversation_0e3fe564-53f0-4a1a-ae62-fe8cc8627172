# ACTIVE_FILE - 29-05-2025
"""
Service for validating benchmark components against registered schemas.
"""

import logging
from typing import Dict, Any, List, Tuple, Optional, Union

from .schema_registry import SchemaRegistry

logger = logging.getLogger(__name__)

class SchemaValidationService:
    """
    Service for validating benchmark components against JSON schemas.
    Provides high-level methods for validating specific components like
    user profiles, situations, evaluation criteria, and tool expectations.
    """

    def __init__(self, schema_registry: Optional[SchemaRegistry] = None):
        """
        Initialize the schema validation service.

        Args:
            schema_registry: Optional schema registry to use.
                           If None, creates a new SchemaRegistry.
        """
        self.registry = schema_registry or SchemaRegistry()

    def validate_user_profile(self, user_profile: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate a user profile against the user profile schema.

        Args:
            user_profile: User profile data to validate

        Returns:
            Tuple of (is_valid, error_messages)
        """
        return self.registry.validate("user_profile", user_profile)

    def validate_situation(self, situation: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate a situation against the situation schema.

        Args:
            situation: Situation data to validate

        Returns:
            Tuple of (is_valid, error_messages)
        """
        return self.registry.validate("situation", situation)

    def validate_evaluation_criteria(self, evaluation_criteria: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate evaluation criteria against the evaluation criteria schema.

        Args:
            evaluation_criteria: Evaluation criteria data to validate

        Returns:
            Tuple of (is_valid, error_messages)
        """
        return self.registry.validate("evaluation_criteria", evaluation_criteria)

    def validate_tool_expectation(self, tool_expectation: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate tool expectations against the tool expectation schema.

        Args:
            tool_expectation: Tool expectation data to validate

        Returns:
            Tuple of (is_valid, error_messages)
        """
        return self.registry.validate("tool_expectation", tool_expectation)

    def validate_workflow_benchmark(self, workflow_benchmark: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate a workflow benchmark scenario against the workflow benchmark schema.

        Args:
            workflow_benchmark: Workflow benchmark data to validate

        Returns:
            Tuple of (is_valid, error_messages)
        """
        return self.registry.validate("workflow_benchmark", workflow_benchmark)

    def validate_benchmark_scenario(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a complete benchmark scenario including all components.

        Args:
            scenario: Complete benchmark scenario data including metadata

        Returns:
            Dict with validation results for each component:
            {
                'valid': bool,  # Overall validity
                'errors': List[str],  # All errors found
                'components': {
                    'user_profile': {'valid': bool, 'errors': List[str]},
                    'situation': {'valid': bool, 'errors': List[str]},
                    'evaluation_criteria': {'valid': bool, 'errors': List[str]},
                    'tool_expectation': {'valid': bool, 'errors': List[str]}
                }
            }
        """
        result = {
            'valid': True,
            'errors': [],
            'components': {}
        }

        # Validate user profile if present
        if 'metadata' in scenario and 'user_profile_context' in scenario['metadata']:
            is_valid, errors = self.validate_user_profile(scenario['metadata']['user_profile_context'])
            result['components']['user_profile'] = {
                'valid': is_valid,
                'errors': errors
            }
            if not is_valid:
                result['valid'] = False
                result['errors'].extend([f"User profile: {error}" for error in errors])

        # Validate situation from input_data if present
        if 'input_data' in scenario and isinstance(scenario['input_data'], dict) and 'context_packet' in scenario['input_data']:
            is_valid, errors = self.validate_situation(scenario['input_data']['context_packet'])
            result['components']['situation'] = {
                'valid': is_valid,
                'errors': errors
            }
            if not is_valid:
                result['valid'] = False
                result['errors'].extend([f"Situation: {error}" for error in errors])

        # Also validate the situation field if present
        if 'situation' in scenario and isinstance(scenario['situation'], dict):
            # If the situation has workflow_type and text, validate it directly
            if 'workflow_type' in scenario['situation'] and 'text' in scenario['situation']:
                is_valid, errors = self.validate_situation(scenario['situation'])
                result['components']['situation_field'] = {
                    'valid': is_valid,
                    'errors': errors
                }
                if not is_valid:
                    result['valid'] = False
                    result['errors'].extend([f"Situation field: {error}" for error in errors])

        # Validate evaluation criteria if present
        if 'metadata' in scenario and 'expected_quality_criteria' in scenario['metadata']:
            # Convert from simple format to full schema format if needed
            criteria_data = scenario['metadata']['expected_quality_criteria']
            if isinstance(criteria_data, dict) and all(isinstance(v, list) for v in criteria_data.values()):
                # Simple format: {"Dimension": ["Criterion1", "Criterion2"]}
                criteria_data = {
                    'name': f"{scenario['name']} Evaluation Criteria",
                    'dimensions': criteria_data
                }

            is_valid, errors = self.validate_evaluation_criteria(criteria_data)
            result['components']['evaluation_criteria'] = {
                'valid': is_valid,
                'errors': errors
            }
            if not is_valid:
                result['valid'] = False
                result['errors'].extend([f"Evaluation criteria: {error}" for error in errors])

        # Validate tool expectations if present
        # Initialize tool_data with an empty mock_responses dictionary
        tool_data = {'mock_responses': {}}

        # Add mock_tool_responses if present and not None
        if 'metadata' in scenario and 'mock_tool_responses' in scenario['metadata'] and scenario['metadata']['mock_tool_responses'] is not None:
            tool_data['mock_responses'] = scenario['metadata']['mock_tool_responses']

            # Also check for mock_responses for backward compatibility
            if 'mock_responses' in scenario['metadata'] and scenario['metadata']['mock_responses'] is not None:
                # If both are present, merge them
                if 'mock_tool_responses' in scenario['metadata'] and scenario['metadata']['mock_tool_responses'] is not None:
                    # Create a copy to avoid modifying the original
                    merged_responses = dict(scenario['metadata']['mock_responses'])
                    merged_responses.update(scenario['metadata']['mock_tool_responses'])
                    tool_data['mock_responses'] = merged_responses
                else:
                    tool_data['mock_responses'] = scenario['metadata']['mock_responses']

            # Look for required_tools in metadata
            if 'required_tools' in scenario['metadata']:
                tool_data['required_tools'] = scenario['metadata']['required_tools']

            is_valid, errors = self.validate_tool_expectation(tool_data)
            result['components']['tool_expectation'] = {
                'valid': is_valid,
                'errors': errors
            }
            if not is_valid:
                result['valid'] = False
                result['errors'].extend([f"Tool expectation: {error}" for error in errors])

        # Validate workflow benchmark if workflow_type is present
        if 'metadata' in scenario and 'workflow_type' in scenario['metadata']:
            # Extract workflow benchmark data from scenario
            workflow_data = {
                'workflow_type': scenario['metadata']['workflow_type']
            }

            # Copy relevant fields from metadata
            for field in ['mock_tool_responses', 'mock_tool_validation', 'warmup_runs',
                         'benchmark_runs', 'expected_quality_criteria', 'evaluator_models',
                         'expected_output', 'expected_stages', 'expected_tool_calls',
                         'timeout_seconds']:
                if field in scenario['metadata']:
                    workflow_data[field] = scenario['metadata'][field]

            is_valid, errors = self.validate_workflow_benchmark(workflow_data)
            result['components']['workflow_benchmark'] = {
                'valid': is_valid,
                'errors': errors
            }
            if not is_valid:
                result['valid'] = False
                result['errors'].extend([f"Workflow benchmark: {error}" for error in errors])

        return result

    def get_schema(self, schema_type: str) -> Optional[Dict[str, Any]]:
        """
        Get a registered schema by type.

        Args:
            schema_type: Type of schema to retrieve

        Returns:
            The schema as a dict, or None if not found
        """
        return self.registry.get_schema(schema_type)

    def list_schemas(self) -> List[str]:
        """
        Get a list of all registered schema types.

        Returns:
            List of schema type identifiers
        """
        return self.registry.list_schemas()

    def register_schema(self, schema_type: str, schema: Dict[str, Any]) -> None:
        """
        Register a schema with the registry.

        Args:
            schema_type: Type identifier for the schema
            schema: JSON Schema as a Python dict
        """
        self.registry.register_schema(schema_type, schema)