# apps/main/agents/psy_agent.py

import logging # Import logging
from typing import Dict, Any, Optional # Import Optional
from pydantic import BaseModel
from asgiref.sync import sync_to_async # Import sync_to_async

from apps.main.agents.base_agent import LangGraphAgent
from apps.main.services.database_service import RealDatabaseService
from apps.main.llm.service import RealLLMClient
# Do NOT import Django models at module level to avoid AppRegistryNotReady errors
# from apps.main.models import LLMConfig

logger = logging.getLogger(__name__) # Add logger

class PsychologicalMonitoringAgent(LangGraphAgent):
    """
    Agent that assesses user's psychological state, trust level, and appropriate challenge level.
     Analyzes personality traits and belief systems to identify growth opportunities.
     """

    def __init__(self,
                 user_profile_id: str,
                 db_service=None,
                 llm_client=None,
                 llm_config: Optional[Any] = None): # Changed to use Any instead of LLMConfig
        # Pass LLM config up to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="psychological",
            llm_config=llm_config # Pass the LLMConfig object
        )

        # Use provided dependencies or create defaults
        self.db_service = db_service or RealDatabaseService()

        # Instantiate LLM client using config from base class if not provided externally
        if llm_client:
            self.llm_client = llm_client
        else:
            # RealLLMClient now requires an LLMConfig object
            if not self.llm_config:
                logger.debug(f"Instantiating RealLLMClient for PsychologicalMonitoringAgent without LLMConfig: will use default")

            logger.debug(f"Instantiating RealLLMClient for PsychologicalMonitoringAgent with LLMConfig: {self.llm_config.name if self.llm_config else 'None'}")
            self.llm_client = RealLLMClient(llm_config=self.llm_config) # Pass the LLMConfig object

        # Initialize definition and tools to None. They will be loaded lazily.
        self.agent_definition = None
        self.available_tools = []
        self.run_id = None # Initialize run_id

    async def _ensure_loaded(self):
        """Ensures agent definition and tools are loaded asynchronously."""
        if self.agent_definition is not None:
            # Already loaded
            return True

        logger.debug(f"Loading definition and tools for {self.agent_role}...")
        try:
            # Check if db_service.load_agent_definition is already async
            if hasattr(self.db_service, 'load_agent_definition') and callable(self.db_service.load_agent_definition):
                if hasattr(self.db_service.load_agent_definition, '__await__'):
                    # It's already an async method
                    self.agent_definition = await self.db_service.load_agent_definition(self.agent_role)
                else:
                    # It's a sync method, wrap it
                    load_def_sync = sync_to_async(self.db_service.load_agent_definition, thread_sensitive=True)
                    self.agent_definition = await load_def_sync(self.agent_role)
            else:
                raise RuntimeError(f"db_service has no load_agent_definition method")

            if self.agent_definition:
                # Check if db_service.load_tools is already async
                if hasattr(self.db_service, 'load_tools') and callable(self.db_service.load_tools):
                    if hasattr(self.db_service.load_tools, '__await__'):
                        # It's already an async method
                        self.available_tools = await self.db_service.load_tools(self.agent_definition)
                    else:
                        # It's a sync method, wrap it
                        load_tools_sync = sync_to_async(self.db_service.load_tools, thread_sensitive=True)
                        self.available_tools = await load_tools_sync(self.agent_definition)
                else:
                    raise RuntimeError(f"db_service has no load_tools method")

                logger.debug(f"Successfully loaded definition and {len(self.available_tools)} tools for {self.agent_role}")
                return True
            else:
                logger.error(f"Agent definition not found for role: {self.agent_role}")
                self.agent_definition = None # Ensure it's None if not found
                self.available_tools = []
                return False

        except Exception as e:
            logger.error(f"Failed to load definition/tools for {self.agent_role}: {e}", exc_info=True)
            self.agent_definition = None
            self.available_tools = []
            # Re-raise or handle as appropriate for the process method
            raise RuntimeError(f"Failed to load agent configuration for {self.agent_role}") from e

    async def process(self, state: BaseModel) -> Dict[str, Any]: # Return type changed
        """Assess user's psychological state and identify growth opportunities."""
        # --- Ensure Agent Definition and Tools are Loaded ---
        current_operation = "ensuring_agent_loaded"
        try:
            self.start_stage('psy_ensure_loaded')
            await self._ensure_loaded()
            self.stop_stage('psy_ensure_loaded')
            if self.agent_definition is None:
                 raise RuntimeError(f"Agent definition for role '{self.agent_role}' could not be loaded.")
        except Exception as load_error:
            self.stop_stage('psy_ensure_loaded') # Stop stage even on error
            error_message = f"Critical error: Failed to load agent configuration during '{current_operation}': {str(load_error)}"
            logger.error(error_message, exc_info=True)
            error_output_data = {
                "psychological_assessment": {},  # Include empty psychological_assessment
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation}
            }
            return {"error": error_message, "output_data": error_output_data}
        # --- End Loading Check ---

        current_operation = "extracting_state"
        # Extract relevant data from state
        context_packet = getattr(state, "context_packet", {})
        resource_context = getattr(state, "resource_context", {})
        engagement_analysis = getattr(state, "engagement_analysis", {})
        workflow_id = getattr(state, "workflow_id", None)

        # Convert user_profile_id to int for DB calls
        try:
            user_profile_id_int = int(self.user_profile_id)
        except ValueError:
            error_message = f"Invalid user_profile_id format: {self.user_profile_id}"
            logger.error(f"{error_message}. Cannot convert to int for DB.")
            # Include psychological_assessment key even on error
            error_output_data = {
                "psychological_assessment": {},
                "error": error_message,
                "debug": {"last_error": error_message}
            }
            return {"error": error_message, "output_data": error_output_data}

        current_operation = "starting_run"
        # Start profiling DB start
        self.start_stage('psy_db_start_run')
        # Start a run in the database - check if already async
        if hasattr(self.db_service.start_run, '__await__'):
            # It's already an async method
            run = await self.db_service.start_run(
                agent_definition=self.agent_definition, # Now guaranteed loaded
                user_profile_id=user_profile_id_int, # Use int ID
                input_data={ # Pass as input_data kwarg
                    "context_packet": context_packet,
                    "resource_context": resource_context,
                    "engagement_analysis": engagement_analysis
                },
                state={"workflow_id": workflow_id} # Pass as state kwarg
            )
        else:
            # It's a sync method, wrap it
            start_run_sync = sync_to_async(self.db_service.start_run, thread_sensitive=True)
            run = await start_run_sync(
                agent_definition=self.agent_definition, # Now guaranteed loaded
                user_profile_id=user_profile_id_int, # Use int ID
                input_data={ # Pass as input_data kwarg
                    "context_packet": context_packet,
                    "resource_context": resource_context,
                    "engagement_analysis": engagement_analysis
                },
                state={"workflow_id": workflow_id} # Pass as state kwarg
            )
        self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id'))
        self.stop_stage('psy_db_start_run')

        try:
            current_operation = "assessing_state"
            # Assess current psychological state
            self.start_stage('psy_assess_state')
            current_state = await self._assess_current_state(context_packet)
            self.stop_stage('psy_assess_state')

            current_operation = "determining_trust"
            # Determine trust phase
            self.start_stage('psy_determine_trust')
            trust_phase = await self._determine_trust_phase()
            self.stop_stage('psy_determine_trust')

            current_operation = "analyzing_traits"
            # Analyze personality traits
            self.start_stage('psy_analyze_traits')
            trait_analysis = await self._analyze_traits()
            self.stop_stage('psy_analyze_traits')

            current_operation = "analyzing_beliefs"
            # Identify belief impact
            self.start_stage('psy_analyze_beliefs')
            belief_analysis = await self._analyze_beliefs()
            self.stop_stage('psy_analyze_beliefs')

            current_operation = "identifying_growth"
            # Identify growth opportunities
            self.start_stage('psy_identify_growth')
            growth_opportunities = await self._identify_growth_opportunities(
                trait_analysis,
                belief_analysis,
                engagement_analysis
            )
            self.stop_stage('psy_identify_growth')

            current_operation = "recommending_challenge"
            # Recommend challenge calibration
            self.start_stage('psy_recommend_challenge')
            challenge_calibration = await self._recommend_challenge_calibration(
                trust_phase,
                trait_analysis,
                current_state
            )
            self.stop_stage('psy_recommend_challenge')

            current_operation = "combining_results"
            # Combine analyses into psychological assessment
            self.start_stage('psy_combine_results')
            psychological_assessment = {
                "current_state": current_state,
                "trust_phase": trust_phase,
                "trait_analysis": trait_analysis,
                "belief_analysis": belief_analysis,
                "growth_opportunities": growth_opportunities,
                "challenge_calibration": challenge_calibration,
                "analysis_timestamp": context_packet.get("session_timestamp", ""),
                "user_id": self.user_profile_id
            }
            self.stop_stage('psy_combine_results')

            # Output data including psychological assessment and routing
            output_data = {
                "psychological_assessment": psychological_assessment,
                "next_agent": "strategy"
            }

            # Prepare state updates dictionary
            state_updates = {"output_data": output_data}

            current_operation = "completing_run_success"
            # Start profiling DB complete (success)
            self.start_stage('psy_db_complete_run')
            # Complete the run - check if already async
            if hasattr(self.db_service.complete_run, '__await__'):
                # It's already an async method
                await self.db_service.complete_run(
                    run_id=self.run_id, # Use run_id kwarg
                    output_data=output_data, # Use output_data kwarg
                    state={"workflow_id": workflow_id}, # Use state kwarg
                    status='completed' # Use status kwarg
                )
            else:
                # It's a sync method, wrap it
                complete_run_sync = sync_to_async(self.db_service.complete_run, thread_sensitive=True)
                await complete_run_sync(
                    run_id=self.run_id, # Use run_id kwarg
                    output_data=output_data, # Use output_data kwarg
                    state={"workflow_id": workflow_id}, # Use state kwarg
                    status='completed' # Use status kwarg
                )
            self.stop_stage('psy_db_complete_run')

            # Add run_id to state updates before returning
            state_updates["run_id"] = self.run_id
            logger.debug(f"PsychologicalMonitoringAgent returning state updates: {state_updates}")
            return state_updates

        except Exception as e:
            error_type = type(e).__name__
            error_message = f"Error in psychological agent during '{current_operation}': {str(e)}"
            logger.error(f"Exception caught in PsychologicalMonitoringAgent process ({error_type} during {current_operation}): {error_message}", exc_info=True)

            # Prepare output data including the error and routing info for persistence
            # Include psychological_assessment key even on error
            error_output_data = {
                "psychological_assessment": {},
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation},
                "forwardTo": "error_handler" # Keep routing info if needed by graph
            }

            # Attempt to complete the run as failed - WRAPPED
            try:
                current_operation = "completing_run_failure"
                self.start_stage('psy_db_complete_run_error')
                # Check if complete_run is already async
                if hasattr(self.db_service.complete_run, '__await__'):
                    # It's already an async method
                    await self.db_service.complete_run(
                        run_id=self.run_id,
                        output_data=error_output_data, # Pass error details in output
                        state={"error_details": error_message}, # Simple state
                        status='failed',
                        error_message=error_message # Pass explicit error message if arg exists
                    )
                else:
                    # It's a sync method, wrap it
                    complete_run_sync = sync_to_async(self.db_service.complete_run, thread_sensitive=True)
                    await complete_run_sync(
                        run_id=self.run_id,
                        output_data=error_output_data, # Pass error details in output
                        state={"error_details": error_message}, # Simple state
                        status='failed',
                        error_message=error_message # Pass explicit error message if arg exists
                    )
                self.stop_stage('psy_db_complete_run_error')
            except Exception as db_error:
                 self.stop_stage('psy_db_complete_run_error') # Stop stage even on error
                 logger.error(f"Failed to complete run in DB after agent error: {db_error}", exc_info=True)

            # Return the state update for LangGraph, including output_data
            error_updates = {
                "error": error_message,
                "output_data": error_output_data, # Ensure output_data is included
                "forwardTo": "error_handler" # Keep routing info
            }
            if hasattr(self, 'run_id') and self.run_id:
                 error_updates["run_id"] = self.run_id
            return error_updates

    async def _assess_current_state(self, context_packet):
        """Assess user's current psychological state"""
        try:
            # Extract mood and context from packet
            reported_mood = context_packet.get("reported_mood", "")

            # Analyze mood and psychological state
            state_data = await self._call_tool(
                "analyze_psychological_state",
                {
                    "user_profile_id": self.user_profile_id,
                    "reported_mood": reported_mood,
                    "context": context_packet
                }
            )

            return {
                "mood": state_data.get("mood", reported_mood),
                "energy_level": state_data.get("energy_level", "medium"),
                "stress_level": state_data.get("stress_level", "medium"),
                "cognitive_load": state_data.get("cognitive_load", "medium"),
                "emotional_balance": state_data.get("emotional_balance", "neutral"),
                "confidence": state_data.get("confidence", 0.7)
            }

        except Exception as e:
            # Return basic state if tool fails
            return {
                "mood": reported_mood,
                "energy_level": "medium",
                "stress_level": "medium",
                "cognitive_load": "medium",
                "emotional_balance": "neutral",
                "confidence": 0.5
            }

    async def _determine_trust_phase(self):
        """Determine user's trust phase"""
        try:
            trust_data = await self._call_tool(
                "get_trust_metrics",
                {
                    "user_profile_id": self.user_profile_id
                }
            )

            # Determine phase based on trust level
            trust_level = trust_data.get("trust_level", 50)
            trust_phase = "Foundation" if trust_level < 60 else "Expansion"

            return {
                "phase": trust_phase,
                "trust_level": trust_level,
                "engagement_trust": trust_data.get("engagement_trust", 50),
                "action_trust": trust_data.get("action_trust", 50),
                "disclosure_trust": trust_data.get("disclosure_trust", 50),
                "phase_duration_days": trust_data.get("phase_duration_days", 0),
                "confidence": trust_data.get("confidence", 0.7)
            }

        except Exception as e:
            # Return default trust phase if tool fails
            return {
                "phase": "Foundation",
                "trust_level": 50,
                "engagement_trust": 50,
                "action_trust": 50,
                "disclosure_trust": 50,
                "phase_duration_days": 0,
                "confidence": 0.5
            }

    async def _analyze_traits(self):
        """Analyze user's personality traits"""
        try:
            trait_data = await self._call_tool(
                "get_trait_analysis",
                {
                    "user_profile_id": self.user_profile_id,
                    "framework": "HEXACO"
                }
            )

            return {
                "trait_values": trait_data.get("trait_values", {}),
                "dominant_traits": trait_data.get("dominant_traits", []),
                "underdeveloped_traits": trait_data.get("underdeveloped_traits", []),
                "trait_stability": trait_data.get("trait_stability", {}),
                "confidence": trait_data.get("confidence", 0.7)
            }

        except Exception as e:
            # Return empty trait analysis if tool fails
            return {
                "trait_values": {},
                "dominant_traits": [],
                "underdeveloped_traits": [],
                "trait_stability": {},
                "confidence": 0.5
            }

    async def _analyze_beliefs(self):
        """Analyze user's belief system"""
        try:
            belief_data = await self._call_tool(
                "get_belief_analysis",
                {
                    "user_profile_id": self.user_profile_id
                }
            )

            return {
                "core_beliefs": belief_data.get("core_beliefs", {}),
                "limiting_beliefs": belief_data.get("limiting_beliefs", []),
                "supportive_beliefs": belief_data.get("supportive_beliefs", []),
                "belief_strength": belief_data.get("belief_strength", {}),
                "belief_awareness": belief_data.get("belief_awareness", {}),
                "confidence": belief_data.get("confidence", 0.7)
            }

        except Exception as e:
            # Return empty belief analysis if tool fails
            return {
                "core_beliefs": {},
                "limiting_beliefs": [],
                "supportive_beliefs": [],
                "belief_strength": {},
                "belief_awareness": {},
                "confidence": 0.5
            }

    async def _identify_growth_opportunities(self, trait_analysis, belief_analysis, engagement_analysis):
        """Identify growth opportunities based on traits and beliefs"""
        try:
            growth_data = await self._call_tool(
                "identify_growth_opportunities",
                {
                    "user_profile_id": self.user_profile_id,
                    "trait_analysis": trait_analysis,
                    "belief_analysis": belief_analysis,
                    "engagement_analysis": engagement_analysis
                }
            )

            return {
                "priority_areas": growth_data.get("priority_areas", []),
                "recommended_trait_development": growth_data.get("recommended_trait_development", {}),
                "belief_challenge_areas": growth_data.get("belief_challenge_areas", []),
                "domain_exploration": growth_data.get("domain_exploration", {}),
                "confidence": growth_data.get("confidence", 0.7)
            }

        except Exception as e:
            # Return empty growth opportunities if tool fails
            return {
                "priority_areas": [],
                "recommended_trait_development": {},
                "belief_challenge_areas": [],
                "domain_exploration": {},
                "confidence": 0.5
            }

    async def _recommend_challenge_calibration(self, trust_phase, trait_analysis, current_state):
        """Recommend challenge calibration based on trust phase and traits"""
        try:
            challenge_data = await self._call_tool(
                "calculate_challenge_calibration",
                {
                    "user_profile_id": self.user_profile_id,
                    "trust_phase": trust_phase,
                    "trait_analysis": trait_analysis,
                    "current_state": current_state
                }
            )

            return {
                "overall_challenge_level": challenge_data.get("overall_challenge_level", 0.5),
                "domain_challenge_levels": challenge_data.get("domain_challenge_levels", {}),
                "trait_challenge_adjustments": challenge_data.get("trait_challenge_adjustments", {}),
                "safety_boundaries": challenge_data.get("safety_boundaries", {}),
                "confidence": challenge_data.get("confidence", 0.7)
            }

        except Exception as e:
            # Return default challenge calibration if tool fails
            return {
                "overall_challenge_level": 0.5,
                "domain_challenge_levels": {},
                "trait_challenge_adjustments": {},
                "safety_boundaries": {},
                "confidence": 0.5
            }

    async def _call_tool(self, tool_code, tool_input):
        """Call a tool and record the usage"""
        try:
            from apps.main.agents.tools.tools_util import execute_tool
            return await execute_tool(tool_code, tool_input, self.run_id)
        except Exception as e:
            # For testing environments, return a mock response
            return {}
