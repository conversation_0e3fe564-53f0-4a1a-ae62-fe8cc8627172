import asyncio
import datetime
import traceback # Import traceback module
from datetime import timezone # Add timezone import separately
import logging
from typing import TypeVar, Generic, Dict, Any, Tuple, Optional, Type, Union
from pydantic import BaseModel
from channels.layers import get_channel_layer # Import channel layer
import json

from apps.main.agents.benchmarking import StageTimer # Import json for details serialization
# LLMConfig import moved into methods/type hints as string literal

# Configure logging
logger = logging.getLogger(__name__)

StateT = TypeVar('StateT', bound=BaseModel)

class LangGraphAgent(Generic[StateT]):
    """
    Base class for agents that natively integrates with LangGraph.

    This class provides the interface that LangGraph expects while
    maintaining type safety and validation through generics.
    """

    def __init__(self,
                 user_profile_id: str,
                 agent_role: str,
                 llm_config: Optional['LLMConfig'] = None): # Changed to accept LLMConfig, use string literal hint
        """Initialize the agent with user ID, role, and optional LLM config."""
        self.user_profile_id = user_profile_id
        self.agent_role = agent_role
        self.run_id = None
        # Store LLM config for subclasses to use when creating their LLM client
        self.llm_config = llm_config # Store the LLMConfig object
        # Profiler type hint needs quotes if StageTimer is defined later or imported conditionally
        self.profiler: Optional['StageTimer'] = None # Add optional profiler attribute

    async def _send_debug_info(self, state: StateT, level: str, message: str, details: Optional[Dict[str, Any]] = None):
        """Helper to send debug info using the EventService."""
        from apps.main.services.event_service import EventService # Import locally

        # Attempt to get the session name from the state
        user_ws_session_name = getattr(state, 'user_ws_session_name', None)

        if not user_ws_session_name:
            # If not in state, try getting it from the context_packet if available
            context_packet = getattr(state, 'context_packet', {})
            if isinstance(context_packet, dict):
                 user_ws_session_name = context_packet.get('user_ws_session_name')

        # Note: EventService handles logging if session_id is missing or channel layer unavailable

        # Ensure details are JSON serializable (EventService might handle this, but good practice here too)
        serializable_details = {}
        if details:
            for key, value in details.items():
                try:
                    # Basic check, EventService might do more robust handling
                    json.dumps({key: value})
                    serializable_details[key] = value
                except (TypeError, OverflowError):
                    serializable_details[key] = f"<unserializable: {type(value).__name__}>"

        await EventService.emit_debug_info(
            level=level,
            message=message,
            source=f"Agent:{self.agent_role}", # Use agent role as source
            details=serializable_details,
            user_profile_id=self.user_profile_id, # Use agent's user_profile_id
            session_id=user_ws_session_name # Pass the specific session ID
        )

    # --- Profiling Helper Methods ---
    def start_stage(self, stage_name: str):
        """Starts timing a specific stage if the profiler is active."""
        if self.profiler:
            self.profiler.start(stage_name)
        # else: # Optional: Log if profiling is attempted but not active
        #     logger.debug(f"Profiler not active, skipping start for stage: {stage_name}")

    def stop_stage(self, stage_name: str):
        """Stops timing a specific stage if the profiler is active."""
        if self.profiler:
            self.profiler.stop(stage_name)
        # else: # Optional: Log if profiling is attempted but not active
        #     logger.debug(f"Profiler not active, skipping stop for stage: {stage_name}")
    # --- End Profiling Helper Methods ---

    async def process(self, state: StateT) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Process the state and return the agent's output and any state updates.

        This method should be implemented by subclasses to provide the agent-specific logic.

        Args:
            state: The current workflow state

        Returns:
            Tuple[Dict[str, Any], Dict[str, Any]]: (output_data, state_updates)
        """
        raise NotImplementedError("Subclasses must implement process()")

    async def _call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """
        Placeholder for calling a tool.
        This should be implemented or mocked appropriately.
        In a real scenario, this would likely interact with self.llm_client or a tool registry.
        """
        logger.info(f"Attempting to call tool '{tool_name}' with args: {arguments}")
        # In a real implementation, you'd call the tool here.
        # For testing purposes, this might be mocked by the test runner.
        # If this base method is called directly without mocking, raise an error.
        raise NotImplementedError(f"_call_tool is not implemented in the base class for tool: {tool_name}")

    async def __call__(self, state: StateT) -> StateT: # Changed to async def
        """
        Make the agent callable directly by LangGraph (asynchronously).

        This method:
        1. Captures the state before processing
        2. Runs the agent's process method
        3. Updates the state with the results
        4. Logs state transitions
        5. Returns the updated state

        Args:
            state: The current workflow state

        Returns:
            StateT: The updated state
        """
        # Make a copy of the initial state for comparison
        state_before = state.model_copy() if hasattr(state, 'model_copy') else state # Use model_copy for Pydantic v2

        # --- BEGIN CHANGE: Enrich start debug message ---
        start_details = {
            'workflow_id': getattr(state_before, 'workflow_id', 'N/A'),
            'run_id': getattr(state_before, 'run_id', 'N/A'),
            'current_stage': getattr(state_before, 'current_stage', 'N/A'),
            # Optionally add a snippet of conversation history if needed later
            # 'last_message': state_before.conversation_history[-1] if hasattr(state_before, 'conversation_history') and state_before.conversation_history else None
        }
        # Send debug info: Agent starting
        await self._send_debug_info(state, 'info', f"Agent '{self.agent_role}' starting process.", start_details)
        # --- END CHANGE ---

        try:
            # Start profiling the overall process method execution
            self.start_stage(f"{self.agent_role}_process_full") # Use helper

            # Run the agent's process method directly using await
            # Subclasses should use self.start_stage/self.stop_stage internally
            state_updates = await self.process(state)

            # Stop profiling the overall process method execution
            self.stop_stage(f"{self.agent_role}_process_full") # Use helper

            # Check if state_updates is a dict before accessing keys (moved check earlier)
            if not isinstance(state_updates, dict):
                logger.error(f"Agent {self.agent_role} process method did not return a dictionary. Returned type: {type(state_updates)}, value: {state_updates}")
                raise TypeError(f"Agent {self.agent_role} process method did not return a dictionary. Returned: {type(state_updates)}")

            # --- BEGIN CHANGE: Enrich end debug message ---
            end_details = {
                'run_id': state_updates.get('run_id', getattr(state, 'run_id', 'N/A')), # Get run_id from updates or state
                'output_keys': list(state_updates.keys()), # Keep the keys for reference
                'current_stage_updated': state_updates.get('current_stage', None), # Show if stage was updated
                # Add a snippet/summary of output_data if it's not too large
                'output_data_summary': str(state_updates.get('output_data', {}))[:200] + '...' if state_updates.get('output_data') else None
            }
            # Send debug info: Agent process completed
            await self._send_debug_info(state, 'info', f"Agent '{self.agent_role}' process completed.", end_details)
            # --- END CHANGE ---

            # Ensure output_data exists before accessing
            if "output_data" not in state_updates:
                # Log or raise a more informative error if output_data is missing
                logger.error(f"Agent {self.agent_role} process method returned state_updates without 'output_data' key. Keys: {state_updates.keys()}. Full dict: {state_updates}")
                # Create a default output_data with error information instead of raising an exception
                state_updates["output_data"] = {
                    "error": f"Missing 'output_data' in {self.agent_role} agent response",
                    "debug": {
                        "last_error": f"'output_data' not found in state_updates returned by {self.agent_role}.process",
                        "keys_present": list(state_updates.keys())
                    }
                }
                logger.warning(f"Created default output_data for {self.agent_role} agent due to missing 'output_data' key")

            output_data = state_updates["output_data"]

            # Store the output_data in state (if the state has an output_data field)
            if hasattr(state, "output_data"):
                state.output_data = output_data

            # Record the last agent that processed the state
            if hasattr(state, "last_agent"):
                state.last_agent = self.agent_role

            # Apply role-specific updates to state
            self._apply_role_specific_updates(state, output_data)

            # Apply general state updates from the state_updates dict
            for key, value in state_updates.items():
                if hasattr(state, key):
                    setattr(state, key, value)

            # Handle next_agent specification
            if hasattr(state, "next_agent"):
                if "next_agent" in output_data:
                    state.next_agent = output_data["next_agent"]
                elif "forwardTo" in output_data:
                    state.next_agent = output_data["forwardTo"]

            # Log state transitions for debugging
            self.log_state_transition(state_before, state)

            # In LangGraphAgent.__call__ method, add before returning state:
            if hasattr(state, "last_agent") and hasattr(state, "current_stage"):
                agent_stage_map = {
                    "resource": "resource_assessment",
                    "engagement": "engagement_analysis",
                    "psychological": "psychological_assessment",
                    "strategy": "strategy_formulation",
                    "activity": "activity_selection",
                    "ethical": "ethical_validation",
                    "orchestrator": "orchestration_initial"
                }

                # Only update if last_agent is in our map and differs from current stage
                if state.last_agent in agent_stage_map:
                    expected_stage = agent_stage_map[state.last_agent]
                    if state.current_stage != expected_stage:
                        logging.debug(f"Aligning current_stage to match last_agent: {state.current_stage} → {expected_stage}")
                        state.current_stage = expected_stage

            return state

        except Exception as e:
            # Handle errors
            error_message = f"Error in {self.agent_role} agent: {str(e)}"
            logger.error(error_message, exc_info=True)

            # Capture traceback
            tb_str = traceback.format_exc()

            # Log the details being sent for debugging
            debug_details_to_send = {
                'error_message': error_message,
                'exception_type': type(e).__name__,
                'traceback': tb_str # Add traceback here
            }
            logger.debug(f"Agent '{self.agent_role}' error details prepared for sending: {debug_details_to_send}")

            # Send debug info: Agent error with traceback
            await self._send_debug_info(
                state,
                'error',
                f"Agent '{self.agent_role}' encountered an error.",
                debug_details_to_send # Use the prepared dictionary
            )

            # Create a standardized state_updates dict with error information
            # Initialize with empty required fields based on agent role
            output_data = {
                "error": error_message,
                "debug": {
                    "last_error": error_message,
                    "exception_type": type(e).__name__,
                    "failed_operation": "agent_process"
                }
            }

            # Add required fields based on agent role
            role_specific_fields = {
                'resource': ['resource_context'],
                'psychological': ['psychological_assessment'],
                'strategy': ['strategy_framework'],
                'ethical': ['ethical_validation']
            }

            # If this agent has role-specific fields to add
            if self.agent_role in role_specific_fields:
                for field in role_specific_fields[self.agent_role]:
                    # Add empty field to ensure it's present
                    if field not in output_data:
                        output_data[field] = {}

            state_updates = {
                "output_data": output_data
            }

            # Set error information in state if the fields exist
            if hasattr(state, "error"):
                state.error = error_message
                state_updates["error"] = error_message

            if hasattr(state, "error_context"):
                error_context = {
                    "agent": self.agent_role,
                    "timestamp": str(datetime.datetime.now(timezone.utc)),
                    "exception": str(e)
                }
                state.error_context = error_context
                state_updates["error_context"] = error_context

            # Update output_data in state if it exists
            if hasattr(state, "output_data"):
                state.output_data = state_updates["output_data"]

            # Apply general state updates from the state_updates dict
            for key, value in state_updates.items():
                if hasattr(state, key):
                    setattr(state, key, value)

            return state
        finally:
            # No need to manage loop cleanup as we're using the existing async context
            pass

    def _apply_role_specific_updates(self, state: StateT, output_data: Dict[str, Any]) -> None:
        """Apply updates specific to this agent's role to ensure data persistence in state

        Args:
            state: The workflow state to update
            output_data: The output data from the agent's process method
        """
        # Map from agent roles to their specific output fields that should be stored in state
        role_specific_fields = {
            'mentor': ['context_packet'],
            'resource': ['resource_context'],
            'engagement': ['engagement_analysis'],
            'psychological': ['psychological_assessment'],
            'strategy': ['strategy_framework'],
            'activity': ['wheel'],
            'ethical': ['ethical_validation']
        }

        # If this agent has role-specific fields to update
        if self.agent_role in role_specific_fields:
            for field in role_specific_fields[self.agent_role]:
                # Only update if the field exists in both output_data and state
                if field in output_data and hasattr(state, field):
                    # Ensure we're storing the data in the state object
                    setattr(state, field, output_data[field])

    def log_state_transition(self, state_before, state_after):
        """Log details about state transitions for debugging"""
        try:
            # Create a copy of the state data to avoid modifying the original
            state_before_dict = state_before.dict() if hasattr(state_before, 'dict') else vars(state_before)
            state_after_dict = state_after.dict() if hasattr(state_after, 'dict') else vars(state_after)

            # Remove large objects that would clutter the logs
            for state_dict in [state_before_dict, state_after_dict]:
                for field in ['context_packet', 'resource_context', 'engagement_analysis',
                            'psychological_assessment', 'strategy_framework', 'wheel',
                            'ethical_validation']:
                    if field in state_dict and state_dict[field]:
                        state_dict[field] = f"<{len(str(state_dict[field]))} chars>"

            # Find differences
            diffs = {}
            for key in set(state_before_dict.keys()).union(state_after_dict.keys()):
                if key not in state_before_dict:
                    diffs[key] = f"Added: {state_after_dict[key]}"
                elif key not in state_after_dict:
                    diffs[key] = "Removed"
                elif state_before_dict[key] != state_after_dict[key]:
                    diffs[key] = f"Changed: {state_before_dict[key]} -> {state_after_dict[key]}"

            # Log the changes
            if diffs:
                #logger.debug(f"Agent {self.agent_role} state transition - Changes: {diffs}")
                pass

            # Also record in state's debug info
            if hasattr(state_after, 'output_data') and state_after.output_data is not None:
                if 'debug' not in state_after.output_data:
                    state_after.output_data['debug'] = {}
                if 'state_transitions' not in state_after.output_data['debug']:
                    state_after.output_data['debug']['state_transitions'] = []

                state_after.output_data['debug']['state_transitions'].append({
                    'agent': self.agent_role,
                    'changes': diffs,
                    'timestamp': str(datetime.datetime.now())
                })
        except Exception as e:
            logger.warning(f"Error logging state transition: {e}")
