# apps/main/agents/orchestrator_agent.py

from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from pydantic import BaseModel

import logging
from asgiref.sync import sync_to_async

from apps.main.agents.base_agent import LangGraphAgent
from apps.main.services.database_service import RealDatabaseService
from apps.main.llm.service import RealLL<PERSON>lient
# Use string literal for type hinting to avoid importing Django models at module level
# from apps.main.models import LLMConfig

logger = logging.getLogger(__name__)

class OrchestratorAgent(LangGraphAgent):
    """
     Orchestrator agent that coordinates the workflow between specialized agents.
     """

    def __init__(self,
                 user_profile_id: str,
                 db_service=None,
                 llm_client=None,
                 llm_config: Optional['LLMConfig'] = None): # Changed to accept LLMConfig
        # Pass LLM config up to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="orchestrator",
            llm_config=llm_config # Pass the LLMConfig object
        )

        # Use provided dependencies or create defaults
        self.db_service = db_service or RealDatabaseService()

        # Instantiate LLM client using config from base class if not provided externally
        if llm_client:
            self.llm_client = llm_client
        else:
            # RealLLMClient now requires an LLMConfig object
            if not self.llm_config:
                logger.debug(f"Instantiating RealLLMClient for OrchestratorAgent without LLMConfig: will use default")

            logger.debug(f"Instantiating RealLLMClient for OrchestratorAgent with LLMConfig: {self.llm_config.name if self.llm_config else 'None'}")
            self.llm_client = RealLLMClient(llm_config=self.llm_config) # Pass the LLMConfig object

        # Initialize definition and tools to None. They will be loaded lazily.
        self.agent_definition = None
        self.available_tools = []
        self.run_id = None # Initialize run_id

    async def _ensure_loaded(self):
        """Ensures agent definition and tools are loaded asynchronously."""
        if self.agent_definition is not None:
            # Already loaded
            return True

        logger.debug(f"Loading definition and tools for {self.agent_role}...")
        try:
            # Wrap synchronous DB call for definition
            load_def_sync = sync_to_async(self.db_service.load_agent_definition, thread_sensitive=True)

            self.agent_definition = await load_def_sync(self.agent_role)
            if self.agent_definition:
                # Wrap synchronous DB call for tools
                load_tools_sync = sync_to_async(self.db_service.load_tools, thread_sensitive=True)
                self.available_tools = await load_tools_sync(self.agent_definition)
                logger.debug(f"Successfully loaded definition and {len(self.available_tools)} tools for {self.agent_role}")
                return True
            else:
                logger.error(f"Agent definition not found for role: {self.agent_role}")
                self.agent_definition = None # Ensure it's None if not found
                self.available_tools = []
                return False

        except Exception as e:
            logger.error(f"Failed to load definition/tools for {self.agent_role}: {e}", exc_info=True)
            self.agent_definition = None
            self.available_tools = []
            # Re-raise or handle as appropriate for the process method
            raise RuntimeError(f"Failed to load agent configuration for {self.agent_role}") from e

    async def process(self, state: BaseModel) -> Dict[str, Any]: # Return type changed
        """Coordinate the workflow between specialized agents."""
        # --- Ensure Agent Definition and Tools are Loaded ---
        current_operation = "ensuring_agent_loaded"
        try:
            self.start_stage('orchestrator_ensure_loaded')
            await self._ensure_loaded()
            self.stop_stage('orchestrator_ensure_loaded')
            if self.agent_definition is None:
                 raise RuntimeError(f"Agent definition for role '{self.agent_role}' could not be loaded.")
        except Exception as load_error:
            self.stop_stage('orchestrator_ensure_loaded') # Stop stage even on error
            error_message = f"Critical error: Failed to load agent configuration during '{current_operation}': {str(load_error)}"
            logger.error(error_message, exc_info=True)
            error_output_data = {"error": error_message, "debug": {"last_error": error_message, "failed_operation": current_operation}}
            return {"error": error_message, "output_data": error_output_data}
        # --- End Loading Check ---

        current_operation = "extracting_state"
        # Extract relevant data from state
        context_packet = getattr(state, "context_packet", {})
        workflow_id = getattr(state, "workflow_id", None)
        current_stage = getattr(state, "current_stage", "orchestration_initial")

        # Access agent-specific outputs if available
        resource_context = getattr(state, "resource_context", None)
        engagement_analysis = getattr(state, "engagement_analysis", None)
        psychological_assessment = getattr(state, "psychological_assessment", None)
        strategy_framework = getattr(state, "strategy_framework", None)
        wheel = getattr(state, "wheel", None)
        ethical_validation = getattr(state, "ethical_validation", None)

        # Convert user_profile_id to int for DB calls
        try:
            user_profile_id_int = int(self.user_profile_id)
        except ValueError:
            error_message = f"Invalid user_profile_id format: {self.user_profile_id}"
            logger.error(f"{error_message}. Cannot convert to int for DB.")
            error_output_data = {"error": error_message, "debug": {"last_error": error_message}}
            return {"error": error_message, "output_data": error_output_data}

        current_operation = "starting_run"
        # Start profiling DB start
        self.start_stage('orchestrator_db_start_run')
        # Start a run in the database - WRAPPED
        start_run_sync = sync_to_async(self.db_service.start_run, thread_sensitive=True)
        run = await start_run_sync(
            agent_definition=self.agent_definition, # Now guaranteed loaded
            user_profile_id=user_profile_id_int, # Use int ID
            input_data={ # Pass as input_data kwarg
                "context_packet": context_packet,
                "current_stage": current_stage
            },
            state={"workflow_id": workflow_id} # Pass as state kwarg
        )
        self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id'))
        self.stop_stage('orchestrator_db_start_run')

        try:
            current_operation = "routing_logic"
            # Start profiling routing logic
            self.start_stage('orchestrator_routing_logic')
            # Determine next steps based on current stage
            # Use the current stage from the state, not the input_data
            current_stage_from_state = self._get_current_stage_from_state(state)
            logger.debug(f"Orchestrator current stage from state: {current_stage_from_state}")

            # Get the last agent from state
            last_agent = getattr(state, "last_agent", None)
            logger.debug(f"Orchestrator last agent from state: {last_agent}")

            if current_stage_from_state == "orchestration_initial":
                # Initial orchestration - route to resource agent
                logger.debug("Orchestrator: Initial stage, routing to resource agent.")
                output_data = {
                    "next_agent": "resource",
                    "orchestration_status": "initial_routing_complete"
                }

            elif current_stage_from_state == "resource_assessment" and last_agent == "resource":
                # After resource agent - route to engagement agent
                logger.debug("Orchestrator: Resource assessment complete, routing to engagement agent.")
                output_data = {
                    "next_agent": "engagement",
                    "orchestration_status": "resource_to_engagement_routing_complete"
                }

            elif current_stage_from_state == "ethical_validation" and ethical_validation:
                # Final integration after ethical validation
                logger.debug("Orchestrator: Ethical validation complete, routing to mentor for final response.")
                output_data = {
                    "next_agent": "mentor",
                    "orchestration_status": "final_integration_complete",
                    "user_response": "I've prepared some activities for you based on your preferences and context."
                }

            else:
                # Determine appropriate next agent based on available data
                logger.debug("Orchestrator: Determining next agent based on available data.")
                next_agent = self._determine_next_agent(
                    resource_context,
                    engagement_analysis,
                    psychological_assessment,
                    strategy_framework,
                    wheel,
                    ethical_validation
                )
                logger.debug(f"Orchestrator: Routing to {next_agent}.")
                output_data = {
                    "next_agent": next_agent,
                    "orchestration_status": f"routing_to_{next_agent}"
                }
            self.stop_stage('orchestrator_routing_logic')

            # Prepare state updates dictionary
            state_updates = {"output_data": output_data}

            current_operation = "completing_run_success"
            # Start profiling DB complete (success)
            self.start_stage('orchestrator_db_complete_run')
            # Complete the run - WRAPPED
            complete_run_sync = sync_to_async(self.db_service.complete_run, thread_sensitive=True)
            await complete_run_sync(
                run_id=self.run_id, # Use run_id kwarg
                output_data=output_data, # Use output_data kwarg
                state={"workflow_id": workflow_id}, # Use state kwarg
                status='completed' # Use status kwarg
            )
            self.stop_stage('orchestrator_db_complete_run')

            # Add run_id to state updates before returning
            state_updates["run_id"] = self.run_id
            logger.debug(f"OrchestratorAgent returning state updates: {state_updates}")
            return state_updates

        except Exception as e:
            error_type = type(e).__name__
            error_message = f"Error in orchestrator agent during '{current_operation}': {str(e)}"
            logger.error(f"Exception caught in OrchestratorAgent process ({error_type} during {current_operation}): {error_message}", exc_info=True)

            # Prepare output data including the error and routing info for persistence
            error_output_data = {
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation},
                "forwardTo": "error_handler" # Keep routing info if needed by graph
            }

            # Attempt to complete the run as failed - WRAPPED
            try:
                current_operation = "completing_run_failure"
                self.start_stage('orchestrator_db_complete_run_error')
                complete_run_sync = sync_to_async(self.db_service.complete_run, thread_sensitive=True)
                await complete_run_sync(
                    run_id=self.run_id,
                    output_data=error_output_data, # Pass error details in output
                    state={"error_details": error_message}, # Simple state
                    status='failed',
                    error_message=error_message # Pass explicit error message if arg exists
                )
                self.stop_stage('orchestrator_db_complete_run_error')
            except Exception as db_error:
                 self.stop_stage('orchestrator_db_complete_run_error') # Stop stage even on error
                 logger.error(f"Failed to complete run in DB after agent error: {db_error}", exc_info=True)

            # Return the state update for LangGraph, including output_data
            error_updates = {
                "error": error_message,
                "output_data": error_output_data, # Ensure output_data is included
                "forwardTo": "error_handler" # Keep routing info
            }
            if hasattr(self, 'run_id') and self.run_id:
                 error_updates["run_id"] = self.run_id
            return error_updates

    def _determine_next_agent(self, resource_context, engagement_analysis,
                             psychological_assessment, strategy_framework,
                             wheel, ethical_validation):
        """Determine the next agent in the sequence based on current data."""
        logger.debug(f"Determining next agent based on available data:")
        logger.debug(f"  resource_context: {bool(resource_context)}")
        logger.debug(f"  engagement_analysis: {bool(engagement_analysis)}")
        logger.debug(f"  psychological_assessment: {bool(psychological_assessment)}")
        logger.debug(f"  strategy_framework: {bool(strategy_framework)}")
        logger.debug(f"  wheel: {bool(wheel)}")
        logger.debug(f"  ethical_validation: {bool(ethical_validation)}")

        # Check for empty dictionaries as well as None
        if not resource_context or (isinstance(resource_context, dict) and not resource_context):
            logger.debug("Routing to resource agent (resource_context is empty or None)")
            return "resource"
        elif not engagement_analysis or (isinstance(engagement_analysis, dict) and not engagement_analysis):
            logger.debug("Routing to engagement agent (engagement_analysis is empty or None)")
            return "engagement"
        elif not psychological_assessment or (isinstance(psychological_assessment, dict) and not psychological_assessment):
            logger.debug("Routing to psychological agent (psychological_assessment is empty or None)")
            return "psychological"
        elif not strategy_framework or (isinstance(strategy_framework, dict) and not strategy_framework):
            logger.debug("Routing to strategy agent (strategy_framework is empty or None)")
            return "strategy"
        elif not wheel or (isinstance(wheel, dict) and not wheel):
            logger.debug("Routing to activity agent (wheel is empty or None)")
            return "activity"
        elif not ethical_validation or (isinstance(ethical_validation, dict) and not ethical_validation):
            logger.debug("Routing to ethical agent (ethical_validation is empty or None)")
            return "ethical"
        else:
            logger.debug("Routing to mentor agent (all data is present)")
            return "mentor"

    def _get_current_stage_from_state(self, state):
        """Extract the current stage from the state object."""
        # Try to get current_stage attribute
        current_stage = getattr(state, "current_stage", None)

        # If not found, try to get it from context_packet
        if not current_stage:
            context_packet = getattr(state, "context_packet", {})
            if isinstance(context_packet, dict):
                current_stage = context_packet.get("current_stage")

        # Default to initial orchestration if not found
        if not current_stage:
            current_stage = "orchestration_initial"

        return current_stage
