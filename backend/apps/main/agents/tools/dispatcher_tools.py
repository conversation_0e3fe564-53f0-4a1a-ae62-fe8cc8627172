from apps.main.agents.tools.tools_util import register_tool
import json
import logging
import time # Import time for performance tracking
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

@register_tool('extract_message_context')
async def extract_message_context(
    message: str,
    user_profile_id: str,
    include_historical_context: bool = False,
    extraction_level: str = "comprehensive",
    track_performance: bool = False # Add performance tracking flag
) -> Dict[str, Any]:
    """
    Extract contextual elements from a user message with high precision.

    This tool focuses exclusively on extracting context elements such as mood,
    environment, time availability, and focus from user messages, with optional
    historical context integration. Supports enhanced context schema v2.0.0.

    Input:
        message: The user's message text to analyze
        user_profile_id: UUID of the user profile
        include_historical_context: Whether to incorporate historical context
        extraction_level: Detail level ("basic", "standard", "comprehensive")

    Output:
        extracted_context: Dictionary with all extracted context elements
            mood: User's current emotional state
            environment: Where the user is or their surroundings
            time_availability: How much time the user has available
            focus: What the user is focused on or thinking about
            satisfaction: Whether the user is satisfied with previous experiences
            extraction_confidence: Confidence score for the extraction (0.0-1.0)
            extracted_entities: List of identified entities in the message
            user_state: Enhanced user state information (trust_level, mood, environment)
            device_capabilities: Information about user's device (screen_size, input_method, accessibility)
            time_context: Temporal context (available_time, time_of_day, time_zone)
            user_preferences: User preferences (learning_style, communication_preferences)
        historical_context: Previous context patterns if requested
        performance_metrics: Dictionary with timing information if track_performance=True
    """
    start_time = time.perf_counter() if track_performance else 0
    perf_metrics = {} if track_performance else None

    try:
        from apps.main.llm.service import RealLLMClient

        # Initialize LLM client
        llm_client = RealLLMClient()

        # Set up system prompt based on extraction level
        system_prompt = "You are a context extraction specialist focused on identifying specific elements from user messages."

        detail_instructions = {
            "basic": "Extract only the most obvious and explicitly stated contextual information.",
            "standard": "Extract both explicit and implied contextual elements with moderate inference.",
            "comprehensive": "Perform deep analysis to extract explicit, implied, and subtle contextual elements."
        }

        system_prompt += f"\n\n{detail_instructions.get(extraction_level, detail_instructions['standard'])}"

        system_prompt += """
Extract the following specific contextual information from the user's message:

1. Mood: The user's current emotional state or feelings
2. Environment: Physical location or surroundings of the user
3. Time Availability: How much time the user has available
4. Focus: What the user is currently thinking about or focused on
5. Satisfaction: Whether the user is satisfied with previous experiences

Also extract these enhanced context elements if possible:

6. User State:
   - Trust Level: User's trust level (0-100) if indicated
   - Mood: Detailed emotional state
   - Environment: Detailed description of surroundings

7. Device Capabilities:
   - Screen Size: Indication of device size (small, medium, large)
   - Input Method: How user is interacting (touch, keyboard, voice, mixed)
   - Accessibility Needs: Any accessibility requirements mentioned

8. Time Context:
   - Available Time: Numeric time available in minutes
   - Time of Day: Current time period (morning, afternoon, evening, night)
   - Time Zone: User's time zone if mentioned

9. User Preferences:
   - Learning Style: Preferred learning method (visual, auditory, reading, kinesthetic)
   - Communication Preferences: Preferred communication style (verbosity, formality, feedback frequency)

For each element, provide a confidence level (0-100) representing how certain you are about the extraction.
"""

        # Add guidance for very short messages
        if len(message) < 15:
            system_prompt += "\n\nNote: The user message is very brief. Extract context cautiously, acknowledging the limited information available due to brevity."

        # Retrieve and add historical context if requested BEFORE LLM call
        historical_data = None
        history_start_time = 0
        if include_historical_context:
            try:
                if track_performance: history_start_time = time.perf_counter()
                historical_data = await _get_historical_context(user_profile_id)
                if track_performance:
                    perf_metrics["history_fetch_duration_ms"] = (time.perf_counter() - history_start_time) * 1000

                if historical_data:
                    # Format historical context for the prompt
                    historical_prompt_section = "\n\nHistorical Context Patterns (use this to inform extraction):\n"
                    if historical_data.get("mood_patterns"):
                        historical_prompt_section += f"- Moods: {json.dumps(historical_data['mood_patterns'])}\n"
                    if historical_data.get("environment_patterns"):
                        historical_prompt_section += f"- Environments: {json.dumps(historical_data['environment_patterns'])}\n"
                    if historical_data.get("time_patterns"):
                        historical_prompt_section += f"- Time Availability: {json.dumps(historical_data['time_patterns'])}\n"
                    historical_prompt_section += f"- Based on {historical_data.get('data_points', 0)} recent data points."
                    system_prompt += historical_prompt_section
            except Exception as hist_err:
                # Log the error but don't fail the whole tool
                logger.warning(f"Failed to retrieve historical context for {user_profile_id}: {str(hist_err)}", exc_info=False) # Log as warning, don't need full traceback here

        # User message
        user_prompt = f"User message: \"{message}\""

        # Format messages for LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        # Call LLM
        llm_start_time = time.perf_counter() if track_performance else 0
        llm_response = await llm_client.chat_completion(
            messages=messages,
            temperature=0.1,  # Low temperature for more deterministic extraction
            max_tokens=800
        )
        if track_performance:
            perf_metrics["llm_call_duration_ms"] = (time.perf_counter() - llm_start_time) * 1000

        # Extract and structure the response
        structured_context = {}

        # Parse the content from the LLM response
        if llm_response.is_text and llm_response.content:
            try:
                # Try to parse as JSON first
                structured_context = json.loads(llm_response.content)
            except json.JSONDecodeError:
                # If not JSON, parse from text
                content = llm_response.content

                # Extract mood
                mood_context = _extract_field_from_text(content, "mood")
                if mood_context:
                    structured_context["mood"] = mood_context

                # Extract environment
                env_context = _extract_field_from_text(content, "environment")
                if env_context:
                    structured_context["environment"] = env_context

                # Extract time availability
                time_context = _extract_field_from_text(content, "time availability")
                if time_context:
                    structured_context["time_availability"] = time_context

                # Extract focus
                focus_context = _extract_field_from_text(content, "focus")
                if focus_context:
                    structured_context["focus"] = focus_context

                # Extract satisfaction
                satisfaction_context = _extract_field_from_text(content, "satisfaction")
                if satisfaction_context:
                    structured_context["satisfaction"] = satisfaction_context

                # Default confidence if not found
                structured_context["extraction_confidence"] = 0.7

        # Add historical context to the result if it was retrieved
        if historical_data:
             structured_context["historical_context"] = historical_data

        # Ensure consistent response structure with enhanced context fields
        result = {
            "extracted_context": {
                "mood": structured_context.get("mood", ""),
                "environment": structured_context.get("environment", ""),
                "time_availability": structured_context.get("time_availability", ""),
                "focus": structured_context.get("focus", ""),
                "satisfaction": structured_context.get("satisfaction", ""),
                "extraction_confidence": float(structured_context.get("extraction_confidence", 0.7)),
                "extracted_entities": structured_context.get("extracted_entities", []),

                # Enhanced context fields (schema v2.0.0)
                "user_state": structured_context.get("user_state", {
                    "trust_level": structured_context.get("trust_level", 50),
                    "mood": structured_context.get("mood", ""),
                    "environment": structured_context.get("environment", "")
                }),

                "device_capabilities": structured_context.get("device_capabilities", {
                    "screen_size": structured_context.get("screen_size", "medium"),
                    "input_method": structured_context.get("input_method", "mixed"),
                    "accessibility": structured_context.get("accessibility", {
                        "vision_impaired": False,
                        "hearing_impaired": False,
                        "motor_impaired": False,
                        "preferred_modality": "visual"
                    })
                }),

                "time_context": structured_context.get("time_context", {
                    "available_time": structured_context.get("available_time",
                                     _convert_time_to_minutes(structured_context.get("time_availability", ""))),
                    "time_of_day": structured_context.get("time_of_day", "afternoon"),
                    "time_zone": structured_context.get("time_zone", "UTC")
                }),

                "user_preferences": structured_context.get("user_preferences", {
                    "learning_style": structured_context.get("learning_style", "visual"),
                    "communication_preferences": structured_context.get("communication_preferences", {
                        "verbosity": "moderate",
                        "formality": "neutral",
                        "feedback_frequency": "moderate"
                    })
                })
            }
        }

        if "historical_context" in structured_context:
            result["historical_context"] = structured_context["historical_context"]

        if track_performance:
            perf_metrics["total_duration_ms"] = (time.perf_counter() - start_time) * 1000
            result["performance_metrics"] = perf_metrics

        return result

    except Exception as e:
        logger.error(f"Error in extract_message_context: {str(e)}", exc_info=True)
        error_result = {
            "extracted_context": {
                "mood": "",
                "environment": "",
                "time_availability": "",
                "focus": "",
                "satisfaction": "",
                "extraction_confidence": 0.3,
                "extracted_entities": [],

                # Include default values for enhanced context fields (schema v2.0.0)
                "user_state": {
                    "trust_level": 50,
                    "mood": "",
                    "environment": ""
                },
                "device_capabilities": {
                    "screen_size": "medium",
                    "input_method": "mixed",
                    "accessibility": {
                        "vision_impaired": False,
                        "hearing_impaired": False,
                        "motor_impaired": False,
                        "preferred_modality": "visual"
                    }
                },
                "time_context": {
                    "available_time": 0,
                    "time_of_day": "afternoon",
                    "time_zone": "UTC"
                },
                "user_preferences": {
                    "learning_style": "visual",
                    "communication_preferences": {
                        "verbosity": "moderate",
                        "formality": "neutral",
                        "feedback_frequency": "moderate"
                    }
                }
            },
            "error": str(e)
        }

        # Ensure performance metrics are added even on error if tracking was enabled
        if track_performance and perf_metrics is not None:
             perf_metrics["total_duration_ms"] = (time.perf_counter() - start_time) * 1000
             error_result["performance_metrics"] = perf_metrics

        return error_result

def _extract_field_from_text(text: str, field_name: str) -> Optional[str]:
    """Helper function to extract a field from text content"""
    import re

    # Look for patterns like "Mood: happy" or "Mood - happy"
    patterns = [
        rf"{field_name}:\s*(.*?)(?:\n|$)",
        rf"{field_name}\s*-\s*(.*?)(?:\n|$)",
        rf"{field_name}\s*=\s*(.*?)(?:\n|$)",
        rf"{field_name}:\s*.*?confidence:?\s*\d+.*?(.*?)(?:\n|$)"
    ]

    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(1).strip()

    return None

def _convert_time_to_minutes(time_str: str) -> int:
    """
    Convert a time string to minutes.

    Examples:
        "30 minutes" -> 30
        "1 hour" -> 60
        "1.5 hours" -> 90
        "2 hrs" -> 120
        "1 hour and 30 minutes" -> 90

    Args:
        time_str: String representation of time

    Returns:
        int: Time in minutes, or 0 if parsing fails
    """
    if not time_str:
        return 0

    import re

    # Try to extract hours
    hours = 0
    hour_patterns = [
        r'(\d+\.?\d*)\s*(?:hour|hr|h)s?',
        r'(\d+\.?\d*)\s*(?:hour|hr|h)s?'
    ]

    for pattern in hour_patterns:
        hour_match = re.search(pattern, time_str, re.IGNORECASE)
        if hour_match:
            try:
                hours = float(hour_match.group(1))
                break
            except ValueError:
                pass

    # Try to extract minutes
    minutes = 0
    minute_patterns = [
        r'(\d+\.?\d*)\s*(?:minute|min|m)s?',
        r'(\d+\.?\d*)\s*(?:minute|min|m)s?'
    ]

    for pattern in minute_patterns:
        minute_match = re.search(pattern, time_str, re.IGNORECASE)
        if minute_match:
            try:
                minutes = float(minute_match.group(1))
                break
            except ValueError:
                pass

    # If no specific units found, try to extract just a number
    if hours == 0 and minutes == 0:
        number_match = re.search(r'(\d+\.?\d*)', time_str)
        if number_match:
            try:
                # Assume the number is in minutes
                minutes = float(number_match.group(1))
            except ValueError:
                pass

    # Convert hours to minutes and return total
    total_minutes = int(hours * 60 + minutes)
    return total_minutes

async def _get_historical_context(user_profile_id: str) -> Dict[str, Any]:
    """Retrieve historical context patterns for the user"""
    try:
        from apps.main.models import HistoryEvent
        from django.db.models import Q
        from django.utils import timezone
        import datetime

        # Look for recent history events with context data
        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async

        @sync_to_async
        def get_recent_events():
            cutoff_date = timezone.now() - datetime.timedelta(days=7)
            events = HistoryEvent.objects.filter(
                Q(user_profile_id=user_profile_id) &
                Q(event_type__in=['mood_explicitly_updated', 'mood_inferred', 'workflow_initiated']) &
                Q(timestamp__gte=cutoff_date)
            ).order_by('-timestamp')[:10]

            return list(events.values('event_type', 'timestamp', 'details'))

        recent_events = await get_recent_events()

        # Extract patterns from recent events
        mood_patterns = []
        environment_patterns = []
        time_patterns = []

        for event in recent_events:
            details = event.get('details', {})

            # Extract mood patterns
            if event['event_type'] in ['mood_explicitly_updated', 'mood_inferred'] and 'description' in details:
                mood_patterns.append(details['description'])

            # Extract context from workflow initiation
            if event['event_type'] == 'workflow_initiated' and 'initial_context' in details:
                context = details['initial_context']
                if 'reported_environment' in context and context['reported_environment']:
                    environment_patterns.append(context['reported_environment'])
                if 'reported_time_availability' in context and context['reported_time_availability']:
                    time_patterns.append(context['reported_time_availability'])

        # Deduplicate and get the most frequent patterns
        from collections import Counter

        return {
            "mood_patterns": dict(Counter(mood_patterns).most_common(3)),
            "environment_patterns": dict(Counter(environment_patterns).most_common(3)),
            "time_patterns": dict(Counter(time_patterns).most_common(3)),
            "data_points": len(recent_events)
        }

    except Exception as e:
        logger.error(f"Error retrieving historical context: {str(e)}")
        return {}

from apps.main.agents.tools.tools_util import register_tool
import json
import logging
from typing import Dict, Any, List, Tuple

logger = logging.getLogger(__name__)

@register_tool('classify_message_intent')
async def classify_message_intent(
    message: str,
    context: Dict[str, Any],
    profile_completion: float,
    include_multi_intent_detection: bool = True
) -> Dict[str, Any]:
    """
    Classify user message intent with high precision for workflow routing.

    This tool specializes in classifying user intents and determining the appropriate
    workflow for processing, with ability to detect multiple intents in complex messages.

    Input:
        message: The user's message text to classify
        context: Extracted context from the message (mood, environment, etc.)
        profile_completion: Numeric value (0.0-1.0) indicating user profile completion
        include_multi_intent_detection: Whether to detect multiple intents

    Output:
        classification: Primary classification result
            workflow_type: Detected workflow type (wheel_generation, activity_feedback, etc.)
            confidence: Confidence score for classification (0.0-1.0)
            reason: Explanation for classification decision
            keywords_matched: List of keywords that influenced classification
        secondary_intents: List of secondary intents detected (if requested)
        action_required: Action requirement data if additional information is needed
    """
    try:
        from apps.main.llm.service import RealLLMClient

        # Initialize LLM client
        llm_client = RealLLMClient()

        # Apply business rules for quick classification before LLM
        rules_classification = _apply_classification_rules(message, context)

        # If rules give high confidence, return immediately for efficiency
        if rules_classification and rules_classification.get('confidence', 0) > 0.85:
            return {"classification": rules_classification}

        # Set up system prompt
        system_prompt = """You are a specialized intent classifier for a personal growth application.
Analyze the user message to determine the most appropriate workflow type from these options:

1. wheel_generation: User wants activity suggestions or is asking what to do
2. activity_feedback: User is providing feedback on a completed activity
3. pre_spin_feedback: User is providing context before spinning the wheel
4. post_spin: User has selected an activity from the wheel
5. progress_review: User wants information about their progress
6. user_onboarding: New user who needs guidance on getting started

Provide:
- A workflow_type from the list above
- A confidence score (0.0-1.0) for your classification
- A brief reason for your decision
- List of keywords in the message that influenced your decision
"""

        if include_multi_intent_detection:
            system_prompt += """
Also identify any secondary intents in the message. If the user has multiple requests
or needs in a single message, list them as secondary intents.
"""

        # Format context for LLM
        context_str = "\n".join([f"{key}: {value}" for key, value in context.items() if value])

        # User message with context
        user_prompt = f"""User message: "{message}"

Extracted context:
{context_str}

Profile completion: {profile_completion * 100:.1f}%

Classify this message into the most appropriate workflow type.
"""

        # Format messages for LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        # Call LLM
        llm_response = await llm_client.chat_completion(
            messages=messages,
            temperature=0.1,  # Low temperature for more deterministic classification
            max_tokens=800
        )

        # Parse the response
        classification_result = {}

        # First check if rules classification exists
        if rules_classification:
            # Use as fallback or potentially blend with LLM classification
            classification_result = rules_classification

        # Process the LLM response
        if llm_response.is_text and llm_response.content:
            # Try to parse as JSON
            try:
                llm_classification = json.loads(llm_response.content)
                if isinstance(llm_classification, dict):
                    # Update with LLM classification
                    classification_result = llm_classification
            except json.JSONDecodeError:
                # Parse from text using helper function
                llm_classification = _parse_classification_from_text(llm_response.content)
                if llm_classification:
                    classification_result = llm_classification

        # Check for action requirements
        action_required = _check_if_action_required(
            classification_result.get('workflow_type', 'wheel_generation'),
            context
        )

        # Specific handling for new users
        if profile_completion < 0.5:
            classification_result = {
                'workflow_type': 'user_onboarding',
                'confidence': 0.95,
                'reason': 'Incomplete user profile requires onboarding',
                'keywords_matched': ['new user', 'incomplete profile']
            }

        # Construct final response
        result = {
            "classification": {
                "workflow_type": classification_result.get('workflow_type', 'wheel_generation'),
                "confidence": classification_result.get('confidence', 0.7),
                "reason": classification_result.get('reason', 'Based on message analysis'),
                "keywords_matched": classification_result.get('keywords_matched', [])
            }
        }

        # Add secondary intents if available and requested
        if include_multi_intent_detection and 'secondary_intents' in classification_result:
            result["secondary_intents"] = classification_result['secondary_intents']

        # Add action requirements if present
        if action_required:
            result["action_required"] = action_required

        return result

    except Exception as e:
        logger.error(f"Error in classify_message_intent: {str(e)}", exc_info=True)
        return {
            "classification": {
                "workflow_type": "wheel_generation",  # Default to wheel generation
                "confidence": 0.5,
                "reason": "Default classification due to error",
                "keywords_matched": []
            },
            "error": str(e)
        }

def _apply_classification_rules(message: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Apply business rules to classify the message without LLM"""
    # Convert message to lowercase for case-insensitive matching
    message_lower = message.lower()

    # Define keyword lists for each workflow type
    keywords = {
        "wheel_generation": [
            "wheel", "activity", "suggestion", "recommend", "what should i do",
            "what can i do", "give me an activity", "want to do something"
        ],
        "activity_feedback": [
            "feedback", "completed", "finished", "done with", "i did",
            "tried", "done", "activity was", "activity is", "my experience"
        ],
        "pre_spin_feedback": [
            "before spinning", "about to spin", "before i spin", "i feel",
            "right now i'm", "currently i'm", "my mood", "i'm in", "i am in"
        ],
        "progress_review": [
            "how am i doing", "progress", "growth", "improvement", "learn",
            "statistics", "stats", "track", "history", "journey", "summary"
        ]
    }

    # Check each workflow type for keyword matches
    matched_keywords = []
    best_match = None
    highest_score = 0

    for workflow, word_list in keywords.items():
        score = 0
        wf_matched = []

        for keyword in word_list:
            if keyword in message_lower:
                score += 1
                wf_matched.append(keyword)

        # If we found matches and it's the best so far
        if score > 0 and score > highest_score:
            highest_score = score
            best_match = workflow
            matched_keywords = wf_matched

    # If we found a good match, return classification
    if best_match and highest_score > 0:
        # Calculate confidence based on number of keyword matches
        confidence = min(0.6 + (0.1 * highest_score), 0.9)

        return {
            'workflow_type': best_match,
            'confidence': confidence,
            'reason': f'Matched keywords for {best_match}',
            'keywords_matched': matched_keywords
        }

    # Rule for short messages with mood context
    if context.get('mood') and len(message.split()) < 10:
        return {
            'workflow_type': 'pre_spin_feedback',
            'confidence': 0.7,
            'reason': 'Brief message with mood information',
            'keywords_matched': ['mood update']
        }

    # No clear classification from rules
    return None

def _parse_classification_from_text(text: str) -> Dict[str, Any]:
    """Parse classification from text when JSON parsing fails"""
    import re

    result = {}

    # Extract workflow type
    workflow_match = re.search(r'workflow_type[:\s]+([a-z_]+)', text, re.IGNORECASE)
    if workflow_match:
        result['workflow_type'] = workflow_match.group(1).strip()

    # Extract confidence
    confidence_match = re.search(r'confidence[:\s]+(0\.\d+|\d+\.\d+|\d+)', text, re.IGNORECASE)
    if confidence_match:
        try:
            confidence = float(confidence_match.group(1))
            # Normalize to 0-1 if given as percentage
            if confidence > 1:
                confidence /= 100
            result['confidence'] = confidence
        except ValueError:
            result['confidence'] = 0.7

    # Extract reason
    reason_match = re.search(r'reason[:\s]+(.*?)(?:\n|$)', text, re.IGNORECASE)
    if reason_match:
        result['reason'] = reason_match.group(1).strip()

    # Extract keywords
    keyword_matches = re.search(r'keywords?[_\s]?matched[:\s]+(.*?)(?:\n\n|$)', text, re.IGNORECASE)
    if keyword_matches:
        keywords_text = keyword_matches.group(1)

        # Try to parse as a list
        if '[' in keywords_text and ']' in keywords_text:
            try:
                keywords_json = keywords_text[keywords_text.find('['):keywords_text.find(']')+1]
                keywords = json.loads(keywords_json)
                result['keywords_matched'] = keywords
            except:
                # Fallback to simple splitting
                result['keywords_matched'] = [k.strip() for k in keywords_text.split(',')]
        else:
            # Simple split by commas or spaces
            result['keywords_matched'] = [k.strip() for k in keywords_text.split(',')]

    # Extract secondary intents if present
    secondary_match = re.search(r'secondary[_\s]?intents?[:\s]+(.*?)(?:\n\n|$)', text, re.IGNORECASE)
    if secondary_match:
        try:
            secondary_text = secondary_match.group(1)
            if '[' in secondary_text:
                # Try to parse as JSON array
                sec_json = secondary_text[secondary_text.find('['):secondary_text.find(']')+1]
                result['secondary_intents'] = json.loads(sec_json)
            else:
                # Parse as comma-separated list
                result['secondary_intents'] = [intent.strip() for intent in secondary_text.split(',')]
        except:
            pass

    return result

def _check_if_action_required(workflow_type: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Check if additional information is needed before proceeding"""
    # For wheel generation, time availability is important
    if workflow_type == 'wheel_generation' and not context.get('time_availability'):
        return {
            'type': 'gather_information',
            'missing_field': 'time_availability',
            'prompt': "Before I suggest activities, could you let me know how much time you have available right now?"
        }

    # For activity feedback, ensure we know which activity
    if workflow_type == 'activity_feedback' and 'activity_id' not in context:
        return {
            'type': 'gather_information',
            'missing_field': 'activity_id',
            'prompt': "I'd love to hear your feedback. Which activity are you providing feedback on?"
        }

    # No additional action required
    return None

from apps.main.agents.tools.tools_util import register_tool
import json
import logging
from typing import Dict, Any, Optional
from django.utils import timezone

logger = logging.getLogger(__name__)

@register_tool('create_session_packet')
async def create_session_packet(
    user_profile_id: str,
    extracted_context: Dict[str, Any],
    workflow_type: str,
    session_data: Optional[Dict[str, Any]] = None,
    include_history: bool = False
) -> Dict[str, Any]:
    """
    Creates a standardized session context packet with metadata for downstream processing.

    This tool consolidates various data elements into a standardized context packet format
    that can be used consistently by all agents in the workflow.

    Input:
        user_profile_id: UUID of the user profile
        extracted_context: Dictionary with extracted contextual elements
        workflow_type: Type of workflow being initiated
        session_data: Additional session-specific data to include
        include_history: Whether to include recent session history

    Output:
        context_packet: Standardized context packet with all required fields
            user_id: UUID of the user profile
            session_id: Unique identifier for this session
            session_timestamp: ISO formatted timestamp for the session
            reported_mood: User's reported mood from context
            reported_environment: User's reported environment from context
            reported_time_availability: User's reported time availability
            reported_focus: User's reported focus or interest area
            reported_satisfaction: User's reported satisfaction level
            workflow_type: Type of workflow being initiated
            session_data: Additional session-specific data
            session_history: Recent session data if requested
    """
    try:
        session_id = f"session-{timezone.datetime.now().strftime('%Y%m%d%H%M%S')}-{user_profile_id[:8]}"

        # Build the base context packet
        context_packet = {
            "user_id": user_profile_id,
            "session_id": session_id,
            "session_timestamp": timezone.datetime.now().isoformat(),
            "reported_mood": extracted_context.get("mood", ""),
            "reported_environment": extracted_context.get("environment", ""),
            "reported_time_availability": extracted_context.get("time_availability", ""),
            "reported_focus": extracted_context.get("focus", ""),
            "reported_satisfaction": extracted_context.get("satisfaction", ""),
            "extraction_confidence": extracted_context.get("extraction_confidence", 0.5),
            "workflow_type": workflow_type,
        }

        # Add any additional session data
        if session_data:
            context_packet["session_data"] = session_data

        # Add user profile info
        user_profile_data = await _get_user_profile_data(user_profile_id)
        if user_profile_data:
            context_packet["user_profile"] = {
                "profile_name": user_profile_data.get("profile_name", ""),
                "trust_phase": user_profile_data.get("trust_phase", "Foundation"),
                "profile_completion": user_profile_data.get("profile_completion", 0.8)
            }

        # Add recent history if requested
        if include_history:
            recent_sessions = await _get_recent_sessions(user_profile_id)
            if recent_sessions:
                context_packet["session_history"] = recent_sessions

        # Record this session in history
        await _record_session_start(user_profile_id, context_packet)

        return {"context_packet": context_packet}

    except Exception as e:
        logger.error(f"Error in create_session_packet: {str(e)}", exc_info=True)
        # Return a minimal valid context packet even on error
        return {
            "context_packet": {
                "user_id": user_profile_id,
                "session_id": f"fallback-{timezone.datetime.now().strftime('%Y%m%d%H%M%S')}",
                "session_timestamp": timezone.datetime.now().isoformat(),
                "workflow_type": workflow_type,
                "error": str(e)
            }
        }

@register_tool('validate_session_requirements')
async def validate_session_requirements(
    context_packet: Dict[str, Any],
    workflow_type: str
) -> Dict[str, Any]:
    """
    Validates whether a context packet meets all requirements for a specific workflow.

    This tool checks if all necessary information is present for a given workflow type
    and identifies any missing critical fields.

    Input:
        context_packet: The context packet to validate
        workflow_type: The workflow type to validate against

    Output:
        validation_result: Result of the validation
            is_valid: Boolean indicating if the packet is valid
            missing_fields: List of required fields that are missing
            critical_fields: List of critical missing fields that must be addressed
            field_requirements: Dictionary of field requirements for this workflow
            action_required: Action requirement data if critical fields are missing
    """
    try:
        # Define required fields for each workflow type
        workflow_requirements = {
            "wheel_generation": {
                "required": ["user_id", "session_timestamp", "reported_environment"],
                "critical": ["reported_time_availability"],
                "optional": ["reported_mood", "reported_focus"]
            },
            "activity_feedback": {
                "required": ["user_id", "session_timestamp"],
                "critical": ["activity_id"],
                "optional": ["reported_satisfaction", "reported_mood"]
            },
            "pre_spin_feedback": {
                "required": ["user_id", "session_timestamp"],
                "critical": ["reported_mood"],
                "optional": ["reported_environment", "reported_time_availability"]
            },
            "post_spin": {
                "required": ["user_id", "session_timestamp"],
                "critical": ["activity_id"],
                "optional": ["reported_mood"]
            },
            "progress_review": {
                "required": ["user_id", "session_timestamp"],
                "critical": [],
                "optional": ["reported_focus", "timeframe"]
            },
            "user_onboarding": {
                "required": ["user_id", "session_timestamp"],
                "critical": [],
                "optional": ["reported_mood", "reported_focus"]
            }
        }

        # Get requirements for the specified workflow
        requirements = workflow_requirements.get(workflow_type, {
            "required": ["user_id", "session_timestamp"],
            "critical": [],
            "optional": []
        })

        # Check for missing fields
        missing_required = [field for field in requirements["required"]
                           if field not in context_packet or not context_packet[field]]

        missing_critical = [field for field in requirements["critical"]
                           if field not in context_packet or not context_packet[field]]

        # Determine if packet is valid (has all required fields)
        is_valid = len(missing_required) == 0

        # Determine if action is required (missing critical fields)
        action_required = None
        if missing_critical:
            action_required = {
                'type': 'gather_information',
                'missing_field': missing_critical[0],
                'prompt': _get_prompt_for_missing_field(missing_critical[0], workflow_type)
            }

        return {
            "validation_result": {
                "is_valid": is_valid,
                "missing_fields": missing_required + missing_critical,
                "critical_fields": missing_critical,
                "field_requirements": requirements,
                "action_required": action_required
            }
        }

    except Exception as e:
        logger.error(f"Error in validate_session_requirements: {str(e)}", exc_info=True)
        return {
            "validation_result": {
                "is_valid": False,
                "missing_fields": ["error_occurred"],
                "critical_fields": [],
                "field_requirements": {},
                "error": str(e)
            }
        }

@register_tool('merge_session_contexts')
async def merge_session_contexts(
    primary_context: Dict[str, Any],
    secondary_context: Dict[str, Any],
    override_primary: bool = False
) -> Dict[str, Any]:
    """
    Merges multiple context packets into a consolidated context.

    This tool combines contexts from multiple related messages or sessions,
    with configurable priority for resolving conflicts.

    Input:
        primary_context: The main context packet with higher priority
        secondary_context: Secondary context to merge in
        override_primary: Whether secondary context should override primary

    Output:
        merged_context: The consolidated context packet
            conflicts: List of fields that had conflicting values
            resolution_source: Which context was used for resolution
    """
    try:
        # Start with a copy of the primary context
        merged = primary_context.copy()
        conflicts = []

        # Track which context was used for each field
        resolution_source = {}

        # Merge in secondary context
        for key, value in secondary_context.items():
            if key not in merged or not merged[key]:
                # If field doesn't exist in primary or is empty, add it
                merged[key] = value
                resolution_source[key] = "secondary"
            elif key in merged and merged[key] != value:
                # If there's a conflict
                conflicts.append({
                    "field": key,
                    "primary_value": merged[key],
                    "secondary_value": value
                })

                # Determine which value to keep
                if override_primary:
                    merged[key] = value
                    resolution_source[key] = "secondary_override"
                else:
                    # Keep primary by default
                    resolution_source[key] = "primary_preserved"

        # Handle special fields - timestamps
        if "session_timestamp" in primary_context and "session_timestamp" in secondary_context:
            # Always use the most recent timestamp
            try:
                primary_time = timezone.parse_datetime(primary_context["session_timestamp"])
                secondary_time = timezone.parse_datetime(secondary_context["session_timestamp"])

                if secondary_time > primary_time:
                    merged["session_timestamp"] = secondary_context["session_timestamp"]
                    resolution_source["session_timestamp"] = "most_recent"
            except:
                # On parsing error, default behavior is to keep primary
                pass

        return {
            "merged_context": merged,
            "conflicts": conflicts,
            "resolution_source": resolution_source
        }

    except Exception as e:
        logger.error(f"Error in merge_session_contexts: {str(e)}", exc_info=True)
        return {
            "merged_context": primary_context,  # Return primary as fallback
            "conflicts": [],
            "resolution_source": {"error": str(e)},
            "error": str(e)
        }

async def _get_user_profile_data(user_profile_id: str) -> Dict[str, Any]:
    """Helper function to get user profile data"""
    try:
        from apps.user.models import UserProfile, TrustLevel
        from django.db.models import Q

        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async

        @sync_to_async
        def get_profile():
            profile = UserProfile.objects.get(id=user_profile_id)
            trust = TrustLevel.objects.filter(user_profile_id=user_profile_id).first()

            trust_phase = "Foundation"
            if trust and trust.value >= 60:
                trust_phase = "Expansion"

            # Calculate profile completion (simplified implementation)
            # In a real system, this would check all required profile fields
            profile_attrs = ['profile_name', 'demographic_data']
            completed_attrs = sum(1 for attr in profile_attrs if getattr(profile, attr, None))
            completion = completed_attrs / len(profile_attrs)

            return {
                "profile_name": profile.profile_name,
                "trust_phase": trust_phase,
                "profile_completion": completion
            }

        return await get_profile()

    except Exception as e:
        logger.error(f"Error getting user profile: {str(e)}")
        return {
            "profile_name": "",
            "trust_phase": "Foundation",
            "profile_completion": 0.5
        }

async def _get_recent_sessions(user_profile_id: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Helper function to get recent session data"""
    try:
        from apps.main.models import HistoryEvent
        from django.db.models import Q

        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async

        @sync_to_async
        def get_sessions():
            events = HistoryEvent.objects.filter(
                Q(user_profile_id=user_profile_id) &
                Q(event_type='session_started')
            ).order_by('-timestamp')[:limit]

            return [
                {
                    "session_id": event.details.get("session_id", ""),
                    "timestamp": event.timestamp.isoformat(),
                    "workflow_type": event.details.get("workflow_type", ""),
                    "context": event.details.get("context", {})
                }
                for event in events
            ]

        return await get_sessions()

    except Exception as e:
        logger.error(f"Error getting recent sessions: {str(e)}")
        return []

async def _record_session_start(user_profile_id: str, context_packet: Dict[str, Any]) -> None:
    """Helper function to record session start in history"""
    try:
        from apps.main.models import HistoryEvent
        from apps.user.models import UserProfile
        from django.contrib.contenttypes.models import ContentType

        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async

        @sync_to_async
        def create_event():
            # Get user profile content type
            content_type = ContentType.objects.get_for_model(UserProfile)

            # Create history event
            HistoryEvent.objects.create(
                event_type='session_started',
                content_type=content_type,
                object_id=user_profile_id,
                user_profile_id=user_profile_id,
                details={
                    "session_id": context_packet.get("session_id", ""),
                    "workflow_type": context_packet.get("workflow_type", ""),
                    "context": context_packet
                }
            )

        await create_event()

    except Exception as e:
        logger.error(f"Error recording session start: {str(e)}")

def _get_prompt_for_missing_field(field: str, workflow_type: str) -> str:
    """Get a user-friendly prompt for a missing field"""
    prompts = {
        "reported_time_availability": "Before I suggest activities, could you let me know how much time you have available?",
        "activity_id": "I'd love to help with that. Which activity are you referring to?",
        "reported_mood": "How are you feeling today?",
        "reported_environment": "Where are you right now? At home, work, or somewhere else?",
        "reported_focus": "What would you like to focus on today?"
    }

    # Return the appropriate prompt or a generic one
    return prompts.get(field, f"Could you provide your {field.replace('_', ' ')}?")

from apps.main.agents.tools.tools_util import register_tool
import json
import logging
from typing import Dict, Any, List, Optional
from django.utils import timezone

logger = logging.getLogger(__name__)

@register_tool('predict_workflow_success')
async def predict_workflow_success(
    user_profile_id: str,
    workflow_type: str,
    context_packet: Dict[str, Any],
    include_recommendations: bool = True
) -> Dict[str, Any]:
    """
    Predicts likelihood of workflow completion success based on context and history.

    This tool analyzes current user context, historical completion patterns, and
    context completeness to predict the probability of workflow success.

    Input:
        user_profile_id: UUID of the user profile
        workflow_type: The type of workflow being initiated
        context_packet: The current context packet with all fields
        include_recommendations: Whether to include improvement recommendations

    Output:
        prediction: Dictionary with success prediction details
            success_probability: Numerical probability of success (0.0-1.0)
            risk_factors: List of identified risk factors
            success_factors: List of identified success factors
            historical_completion_rate: Success rate of similar workflows
            recommendations: Suggestions to improve success probability (if requested)
    """
    try:
        # Get historical completion patterns
        historical_data = await _get_workflow_history(user_profile_id, workflow_type)

        # Calculate baseline probability from historical data
        baseline_probability = historical_data.get("completion_rate", 0.8)

        # Analyze context completeness
        context_score = _calculate_context_completeness(context_packet, workflow_type)

        # Identify risk and success factors
        risk_factors = []
        success_factors = []

        # Check for key risk factors
        if context_score < 0.7:
            risk_factors.append("Incomplete context information")

        if historical_data.get("recent_failures", 0) > 1:
            risk_factors.append("Recent workflow failures")

        if historical_data.get("completion_rate", 0) < 0.5:
            risk_factors.append("Low historical completion rate")

        # Check for success factors
        if context_score > 0.9:
            success_factors.append("Comprehensive context information")

        if historical_data.get("recent_completions", 0) > 1:
            success_factors.append("Recent workflow successes")

        if context_packet.get("reported_mood") in ["motivated", "focused", "energetic", "happy"]:
            success_factors.append("Positive user mood")

        # Calculate final success probability
        # Base probability adjusted by context score and historical patterns
        success_probability = baseline_probability * 0.4 + context_score * 0.6

        # Adjust for risk and success factors
        success_probability -= len(risk_factors) * 0.05
        success_probability += len(success_factors) * 0.05

        # Ensure probability is between 0 and 1
        success_probability = max(0.0, min(1.0, success_probability))

        # Generate recommendations if requested
        recommendations = []
        if include_recommendations and risk_factors:
            recommendations = _generate_recommendations(risk_factors, workflow_type, context_packet)

        return {
            "prediction": {
                "success_probability": success_probability,
                "risk_factors": risk_factors,
                "success_factors": success_factors,
                "historical_completion_rate": historical_data.get("completion_rate", None),
                "recommendations": recommendations if include_recommendations else None
            }
        }

    except Exception as e:
        logger.error(f"Error in predict_workflow_success: {str(e)}", exc_info=True)
        return {
            "prediction": {
                "success_probability": 0.7,  # Default reasonable probability
                "risk_factors": ["Error in prediction calculation"],
                "success_factors": [],
                "historical_completion_rate": None,
                "error": str(e)
            }
        }

@register_tool('estimate_workflow_duration')
async def estimate_workflow_duration(
    workflow_type: str,
    context_complexity: str = "medium",
    user_profile_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Provides accurate time estimates for workflow completion.

    This tool calculates expected duration for different workflow types,
    with adjustments based on complexity and historical patterns.

    Input:
        workflow_type: The type of workflow being initiated
        context_complexity: Complexity level ("low", "medium", "high")
        user_profile_id: Optional user profile ID for personalized estimates

    Output:
        duration: Dictionary with duration estimates
            estimated_seconds: Estimated duration in seconds
            min_seconds: Minimum likely duration
            max_seconds: Maximum likely duration
            human_readable: Human-readable duration estimate
            factors: Factors that influenced the estimate
    """
    try:
        # Base durations for different workflow types (in seconds)
        base_durations = {
            "wheel_generation": 15,   # 15 seconds
            "activity_feedback": 10,  # 10 seconds
            "pre_spin_feedback": 5,   # 5 seconds
            "user_onboarding": 20,    # 20 seconds
            "progress_review": 12,    # 12 seconds
            "post_spin": 8            # 8 seconds
        }

        # Complexity multipliers
        complexity_multipliers = {
            "low": 0.8,
            "medium": 1.0,
            "high": 1.3
        }

        # Get base duration and apply complexity multiplier
        base_duration = base_durations.get(workflow_type, 10)
        multiplier = complexity_multipliers.get(context_complexity, 1.0)

        estimated_seconds = base_duration * multiplier

        # Get historical data if user_profile_id is provided
        personal_adjustment = 1.0
        historical_factors = []

        if user_profile_id:
            history = await _get_workflow_history(user_profile_id, workflow_type)

            if history.get("avg_duration"):
                # Blend historical average with base estimate
                historical_avg = history.get("avg_duration")
                estimated_seconds = (estimated_seconds + historical_avg) / 2

                # Note as a factor
                if historical_avg > base_duration * 1.2:
                    historical_factors.append("Historical workflows took longer than average")
                elif historical_avg < base_duration * 0.8:
                    historical_factors.append("Historical workflows completed faster than average")

        # Calculate min/max ranges (±20%)
        min_seconds = max(1, estimated_seconds * 0.8)
        max_seconds = estimated_seconds * 1.2

        # Create human-readable estimate
        if estimated_seconds < 10:
            human_readable = "less than 10 seconds"
        elif estimated_seconds < 30:
            human_readable = "about 15-30 seconds"
        elif estimated_seconds < 60:
            human_readable = "less than a minute"
        else:
            minutes = estimated_seconds / 60
            human_readable = f"about {round(minutes)} minute{'s' if minutes >= 2 else ''}"

        # Combine all factors that influenced estimate
        factors = [
            f"{context_complexity.capitalize()} complexity workflow"
        ]
        factors.extend(historical_factors)

        return {
            "duration": {
                "estimated_seconds": round(estimated_seconds),
                "min_seconds": round(min_seconds),
                "max_seconds": round(max_seconds),
                "human_readable": human_readable,
                "factors": factors
            }
        }

    except Exception as e:
        logger.error(f"Error in estimate_workflow_duration: {str(e)}", exc_info=True)
        # Return reasonable defaults
        return {
            "duration": {
                "estimated_seconds": 15,
                "min_seconds": 10,
                "max_seconds": 20,
                "human_readable": "about 15 seconds",
                "factors": ["Default estimate due to error"],
                "error": str(e)
            }
        }

@register_tool('identify_optimal_agent_path')
async def identify_optimal_agent_path(
    workflow_type: str,
    context_packet: Dict[str, Any],
    user_profile_id: str
) -> Dict[str, Any]:
    """
    Determines the ideal sequence of agents for a given workflow.

    This tool analyzes the workflow type and context to provide an optimized
    agent execution path with conditional branches based on context.

    Input:
        workflow_type: The type of workflow being initiated
        context_packet: The current context packet
        user_profile_id: UUID of the user profile

    Output:
        agent_path: Dictionary with optimized agent path
            primary_path: Ordered list of primary agent roles
            conditional_branches: Conditions that may alter the path
            estimated_steps: Expected number of agent steps
            abbreviated_path: Flag indicating if path was shortened
            trust_phase_adaptations: How the path adapts to trust phase
    """
    try:
        # Default agent paths for different workflow types
        default_paths = {
            "wheel_generation": [
                "orchestrator",
                "resource",
                "engagement",
                "psychological",
                "strategy",
                "activity",
                "ethical",
                "mentor"
            ],
            "activity_feedback": [
                "orchestrator",
                "engagement",
                "psychological",
                "strategy",
                "mentor"
            ],
            "pre_spin_feedback": [
                "orchestrator",
                "psychological",
                "activity",
                "mentor"
            ],
            "post_spin": [
                "orchestrator",
                "activity",
                "mentor"
            ],
            "user_onboarding": [
                "orchestrator",
                "psychological",
                "mentor"
            ],
            "progress_review": [
                "orchestrator",
                "engagement",
                "psychological",
                "mentor"
            ]
        }

        # Get the default path for this workflow
        primary_path = default_paths.get(workflow_type, ["orchestrator", "mentor"])

        # Determine context complexity
        context_keys = set(context_packet.keys())
        required_keys = {"user_id", "session_timestamp", "workflow_type"}
        optional_keys = {"reported_mood", "reported_environment", "reported_time_availability",
                         "reported_focus", "reported_satisfaction"}

        context_completeness = len(context_keys.intersection(required_keys.union(optional_keys))) / len(required_keys.union(optional_keys))

        # Identify conditional branches based on context
        conditional_branches = []

        # Check if psychological context is missing
        if "reported_mood" not in context_packet or not context_packet["reported_mood"]:
            conditional_branches.append({
                "condition": "Missing mood information",
                "branch": ["psychological"] if "psychological" not in primary_path else []
            })

        # Check if activity information is needed but missing
        if workflow_type in ["activity_feedback", "post_spin"] and "activity_id" not in context_packet:
            conditional_branches.append({
                "condition": "Missing activity information",
                "branch": ["mentor", "activity"]
            })

        # Check trust phase for adaptations
        trust_phase = "Foundation"
        user_profile = context_packet.get("user_profile", {})
        if user_profile:
            trust_phase = user_profile.get("trust_phase", "Foundation")

        trust_phase_adaptations = {}

        # In Foundation phase, ensure ethical oversight
        if trust_phase == "Foundation" and "ethical" not in primary_path and workflow_type == "wheel_generation":
            trust_phase_adaptations["add_ethical_oversight"] = True

        # In Expansion phase, potentially skip some steps for efficiency
        if trust_phase == "Expansion" and len(primary_path) > 3:
            # For experienced users with high trust, we can sometimes streamline
            if context_completeness > 0.9:
                abbreviated_path = True
                # Remove some intermediate steps that might not be essential
                simplified_path = [p for p in primary_path if p in
                                  ["orchestrator", "psychological", "strategy", "activity", "mentor"]]
                trust_phase_adaptations["streamlined_path"] = simplified_path
            else:
                abbreviated_path = False
        else:
            abbreviated_path = False

        return {
            "agent_path": {
                "primary_path": primary_path,
                "conditional_branches": conditional_branches,
                "estimated_steps": len(primary_path) + sum(len(branch["branch"]) for branch in conditional_branches),
                "abbreviated_path": abbreviated_path,
                "trust_phase_adaptations": trust_phase_adaptations
            }
        }

    except Exception as e:
        logger.error(f"Error in identify_optimal_agent_path: {str(e)}", exc_info=True)
        # Return a simple default path
        return {
            "agent_path": {
                "primary_path": ["orchestrator", "mentor"],
                "conditional_branches": [],
                "estimated_steps": 2,
                "abbreviated_path": False,
                "trust_phase_adaptations": {},
                "error": str(e)
            }
        }

async def _get_workflow_history(user_profile_id: str, workflow_type: str) -> Dict[str, Any]:
    """Helper function to get workflow history and completion data"""
    try:
        from apps.main.models import HistoryEvent, AgentRun
        from django.db.models import Avg, Count, Q, F
        from django.utils import timezone
        import datetime

        # Calculate date for recent events
        cutoff_date = timezone.now() - datetime.timedelta(days=7)

        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async

        @sync_to_async
        def get_statistics():
            # Get workflow runs
            workflow_runs = AgentRun.objects.filter(
                user_profile_id=user_profile_id,
                initial_state__contains={"task_type": workflow_type}
            )

            # Calculate statistics
            total_runs = workflow_runs.count()
            completed_runs = workflow_runs.filter(status="completed").count()
            completion_rate = completed_runs / total_runs if total_runs > 0 else 0

            # Get average duration
            avg_duration = workflow_runs.filter(
                status="completed",
                completed_at__isnull=False
            ).annotate(
                duration=F('completed_at') - F('started_at')
            ).aggregate(avg=Avg('duration'))['avg']

            avg_seconds = avg_duration.total_seconds() if avg_duration else None

            # Get recent completions and failures
            recent_runs = workflow_runs.filter(started_at__gte=cutoff_date)
            recent_completions = recent_runs.filter(status="completed").count()
            recent_failures = recent_runs.filter(status__in=["failed", "timeout"]).count()

            return {
                "total_runs": total_runs,
                "completion_rate": completion_rate,
                "avg_duration": avg_seconds,
                "recent_completions": recent_completions,
                "recent_failures": recent_failures
            }

        return await get_statistics()

    except Exception as e:
        logger.error(f"Error getting workflow history: {str(e)}")
        return {
            "total_runs": 0,
            "completion_rate": 0.8,  # Default assumption
            "avg_duration": None,
            "recent_completions": 0,
            "recent_failures": 0
        }

def _calculate_context_completeness(context_packet: Dict[str, Any], workflow_type: str) -> float:
    """Calculate how complete the context information is"""
    # Define expected fields for different workflow types
    expected_fields = {
        "wheel_generation": [
            "user_id", "session_timestamp", "reported_environment",
            "reported_time_availability", "reported_mood", "reported_focus"
        ],
        "activity_feedback": [
            "user_id", "session_timestamp", "activity_id",
            "reported_satisfaction", "reported_mood"
        ],
        "pre_spin_feedback": [
            "user_id", "session_timestamp", "reported_mood",
            "reported_environment", "reported_time_availability"
        ],
        "post_spin": [
            "user_id", "session_timestamp", "activity_id", "reported_mood"
        ],
        "progress_review": [
            "user_id", "session_timestamp", "reported_focus"
        ],
        "user_onboarding": [
            "user_id", "session_timestamp", "reported_mood", "reported_focus"
        ]
    }

    # Get expected fields for this workflow type
    fields = expected_fields.get(workflow_type, ["user_id", "session_timestamp"])

    # Count how many expected fields are present and non-empty
    present_fields = sum(1 for field in fields
                         if field in context_packet and context_packet[field])

    # Calculate completeness score (0.0-1.0)
    return present_fields / len(fields)

def _generate_recommendations(
    risk_factors: List[str],
    workflow_type: str,
    context_packet: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """Generate recommendations to improve workflow success probability"""
    recommendations = []

    # Handle incomplete context
    if "Incomplete context information" in risk_factors:
        # Check which key fields are missing
        if workflow_type == "wheel_generation":
            if not context_packet.get("reported_time_availability"):
                recommendations.append({
                    "type": "gather_information",
                    "field": "time_availability",
                    "message": "Ask the user how much time they have available"
                })
            if not context_packet.get("reported_environment"):
                recommendations.append({
                    "type": "gather_information",
                    "field": "environment",
                    "message": "Ask the user about their current environment"
                })
        elif workflow_type in ["activity_feedback", "post_spin"]:
            if not context_packet.get("activity_id"):
                recommendations.append({
                    "type": "gather_information",
                    "field": "activity_id",
                    "message": "Ask the user which activity they're referring to"
                })

    # Handle historical failure patterns
    if "Recent workflow failures" in risk_factors or "Low historical completion rate" in risk_factors:
        recommendations.append({
            "type": "processing_adjustment",
            "adjustment": "additional_validation",
            "message": "Add extra validation steps during processing"
        })

        recommendations.append({
            "type": "processing_adjustment",
            "adjustment": "simplified_processing",
            "message": "Consider a simplified processing path with fewer agent steps"
        })

    return recommendations
