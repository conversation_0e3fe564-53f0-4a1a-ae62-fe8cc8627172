"""
Sentiment analysis tool for evaluating emotional content in user messages.

This tool provides nuanced sentiment analysis of conversation messages,
detecting emotions, sentiment polarity, and intensity to help the mentor
agent adapt its responses appropriately.
"""

import logging
import re
from typing import Dict, Any, List, Optional, Tuple, Set
import json
from datetime import datetime

import pytest
pytestmark = pytest.mark.django_db
# Import tool utilities
from apps.main.agents.tools.tools_util import register_tool

# Configure logging
logger = logging.getLogger(__name__)

# Emotion categories and associated keywords for keyword-based analysis
EMOTION_KEYWORDS = {
    # Positive emotions
    "joy": {"happy", "joyful", "delighted", "thrilled", "excited", "pleased", "glad", "content", "satisfied", "enjoying", "love", "wonderful", "great", "excellent"},
    "gratitude": {"thankful", "grateful", "appreciate", "thanks", "blessed", "fortunate"},
    "optimism": {"hopeful", "optimistic", "looking forward", "positive", "bright", "promising", "better"},
    "calm": {"relaxed", "peaceful", "calm", "serene", "tranquil", "centered", "balanced", "harmony"},
    "confidence": {"confident", "sure", "certain", "capable", "empowered", "strong", "can do"},
    "interest": {"curious", "interested", "intrigued", "fascinated", "engaged", "inspired"},

    # Negative emotions
    "anger": {"angry", "annoyed", "frustrated", "irritated", "mad", "furious", "upset", "outraged", "hate", "resent"},
    "sadness": {"sad", "unhappy", "depressed", "down", "blue", "gloomy", "miserable", "heartbroken", "disappointed", "hurt"},
    "fear": {"afraid", "scared", "fearful", "nervous", "anxious", "worried", "terrified", "dread", "panic", "uneasy"},
    "stress": {"stressed", "overwhelmed", "pressured", "tense", "strained", "burdened", "overloaded", "exhausted", "busy"},
    "shame": {"embarrassed", "ashamed", "guilty", "regret", "remorse"},
    "confusion": {"confused", "uncertain", "unsure", "puzzled", "lost", "ambivalent", "dilemma", "doubt", "perplexed"},
    "boredom": {"bored", "uninterested", "disengaged", "monotonous", "dull", "tedious", "uninspired"},
    "loneliness": {"lonely", "alone", "isolated", "abandoned", "disconnected", "neglected"}
}

# Words that intensify emotions
INTENSITY_MODIFIERS = {
    "high": {"extremely", "very", "really", "incredibly", "exceptionally", "deeply", "absolutely", "completely", "totally", "utterly", "severely", "highly", "immensely", "intensely", "especially", "remarkably"},
    "moderate": {"quite", "rather", "fairly", "pretty", "somewhat", "relatively", "moderately", "notably", "distinctly", "particularly"},
    "low": {"slightly", "a bit", "a little", "somewhat", "kind of", "sort of", "mildly", "marginally", "faintly", "barely"}
}

# Negation words that can flip sentiment
NEGATION_WORDS = {"not", "no", "never", "don't", "doesn't", "didn't", "can't", "cannot", "couldn't", "shouldn't", "wouldn't", "isn't", "aren't", "wasn't", "weren't", "none", "nothing", "neither", "nor"}

# Time-related words that can indicate present vs. past emotional states
TIME_INDICATORS = {
    "present": {"now", "today", "currently", "at the moment", "these days", "lately", "feeling", "am", "is", "are"},
    "past": {"was", "were", "felt", "used to", "before", "previously", "yesterday", "last week", "had been", "in the past"},
    "future": {"will", "going to", "expect", "anticipate", "hope to", "plan to", "looking forward to", "tomorrow", "soon", "next"}
}

# Simple sarcasm keywords for keyword-based detection
SARCASM_KEYWORDS = {"sure", "yeah right", "totally", "as if", "oh great", "wonderful", "just what I needed"}

@register_tool('evaluate_message_sentiment')
async def evaluate_message_sentiment(
    message: str,
    user_profile_id: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None,
    use_llm: bool = True
) -> Dict[str, Any]:
    """
    Analyzes the sentiment and emotional content of a message.

    Input:
        message: The text message to analyze
        user_profile_id: Optional UUID of the user profile for context
        context: Optional additional context for interpretation
        use_llm: Whether to use the LLM for deeper analysis (default: True)

    Output:
        sentiment: Overall sentiment (positive, negative, neutral)
        valence: Numerical sentiment score (-1.0 to 1.0)
        intensity: Intensity level of the emotion (0-100)
        confidence: Confidence in the analysis (0.0-1.0)
        primary_emotion: Primary detected emotion
        emotions: List of detected emotions with scores
        temporal_focus: Whether emotions refer to present, past, or future
        negated: Whether sentiment appears to be negated
        message_context: Additional contextual information
    """
    try:
        # Import essential components
        from apps.main.llm.client import LLMClient
        from django.conf import settings

        # Create default response structure
        result = {
            "sentiment": "neutral",
            "valence": 0.0,
            "intensity": 50,
            "confidence": 0.5,
            "primary_emotion": None,
            "emotions": [],
            "temporal_focus": "present",
            "negated": False,
            "analysis_method": "keyword-based"
        }

        # Process empty or very short messages
        if not message or len(message.strip()) < 3:
            result["confidence"] = 0.3
            return result

        # Normalize the message
        normalized_message = message.lower().strip()

        # Step 1: Initial keyword-based analysis
        emotion_scores, negated = keyword_analysis(normalized_message)

        # Detect sarcasm in keyword analysis (but don't return yet)
        sarcasm_detected = any(phrase in normalized_message for phrase in SARCASM_KEYWORDS)

        # Step 2: Determine primary emotion and convert to standard format
        emotions_list = []
        max_score = 0
        primary_emotion = None

        for emotion, score in emotion_scores.items():
            emotions_list.append({"name": emotion, "score": score})
            if score > max_score:
                max_score = score
                primary_emotion = emotion

        # Sort emotions by score (descending)
        emotions_list.sort(key=lambda x: x["score"], reverse=True)

        # Step 3: Calculate overall sentiment
        pos_emotions = {"joy", "gratitude", "optimism", "calm", "confidence", "interest"}
        neg_emotions = {"anger", "sadness", "fear", "stress", "shame", "confusion", "boredom", "loneliness"}

        pos_score = sum(emotion_scores.get(e, 0) for e in pos_emotions)
        neg_score = sum(emotion_scores.get(e, 0) for e in neg_emotions)

        # Adjust for negation
        if negated:
            pos_score, neg_score = neg_score, pos_score

        # Calculate valence (-1 to 1)
        if pos_score > 0 or neg_score > 0:
            valence = (pos_score - neg_score) / (pos_score + neg_score)
        else:
            valence = 0.0

        # Determine sentiment category with adjusted thresholds
        if valence > 0.15:  # Lowered positive threshold for better sensitivity
            sentiment = "positive"
        elif valence < -0.15:  # Consistent negative threshold
            sentiment = "negative"
        else:
            sentiment = "neutral"

        # Determine intensity (0-100)
        intensity = int(50 + (abs(valence) * 50))

        # Determine temporal focus
        temporal_focus = detect_temporal_focus(normalized_message)

        # Step 4: Use LLM for deeper analysis if requested
        # Always call LLM if use_llm is True, regardless of keyword score, for potentially better nuance.
        llm_analysis_result = None
        if use_llm:
            try:
                llm_analysis = await analyze_with_llm(message, context, user_profile_id)

                # Update results with LLM analysis if successful
                if llm_analysis:
                    # Determine analysis method based on how much we're using LLM results
                    # If we're using mostly LLM results, set method to "LLM"
                    # If we're blending keyword and LLM results, set method to "hybrid (keyword + LLM)"
                    keyword_score_significant = max_score > 0.4

                    if keyword_score_significant:
                        # Both keyword and LLM analysis contributed significantly
                        analysis_method = "hybrid (keyword + LLM)"
                    else:
                        # LLM analysis is primary
                        analysis_method = "LLM"

                    # Blend keyword and LLM analysis (give LLM more weight)
                    result["sentiment"] = llm_analysis.get("sentiment", sentiment)
                    result["valence"] = (valence + 2 * llm_analysis.get("valence", valence)) / 3
                    result["intensity"] = (intensity + 2 * llm_analysis.get("intensity", intensity)) / 3
                    result["primary_emotion"] = llm_analysis.get("primary_emotion", primary_emotion)

                    # Update emotions list if LLM provided it
                    if "emotions" in llm_analysis and llm_analysis["emotions"]:
                        result["emotions"] = llm_analysis["emotions"]
                    else:
                        result["emotions"] = emotions_list[:5]  # Limit to top 5

                    # Update confidence and other details
                    result["confidence"] = llm_analysis.get("confidence", 0.8)
                    result["temporal_focus"] = llm_analysis.get("temporal_focus", temporal_focus)
                    result["negated"] = llm_analysis.get("negated", negated)

                    # Set analysis method - prefer LLM's own designation if available,
                    # otherwise use our determination based on keyword significance
                    result["analysis_method"] = llm_analysis.get("analysis_method", analysis_method)

                    # Include any additional context LLM detected
                    if "message_context" in llm_analysis:
                        result["message_context"] = llm_analysis["message_context"]

                    # Add user historical context if available, even after successful LLM analysis
                    if user_profile_id:
                        user_context = await get_user_sentiment_context(user_profile_id)
                        if user_context:
                            result["user_context"] = user_context

                    # If LLM handled sarcasm, we might not need the keyword override
                    # Check if LLM detected sarcasm or if keyword sarcasm should override
                    # Use safer access with .lower() to handle case variations
                    llm_detected_sarcasm = any(e.get("name", "").lower() == "sarcasm" for e in result.get("emotions", [])) or (result.get("primary_emotion", "").lower() == "sarcasm")

                    if sarcasm_detected and not llm_detected_sarcasm:
                        # Apply keyword sarcasm override if LLM didn't catch it
                        result["sentiment"] = "negative"
                        result["valence"] = -0.5 # Override valence
                        result["primary_emotion"] = "sarcasm" # Override primary emotion
                        # Ensure sarcasm is in the list
                        if not any(e.get("name", "").lower() == "sarcasm" for e in result["emotions"]):
                             result["emotions"].insert(0, {"name": "sarcasm", "score": 0.9}) # Add sarcasm with high score
                        result["confidence"] = max(result["confidence"], 0.85) # Boost confidence for detected sarcasm

                    return result # Return LLM/Hybrid result

            except Exception as e:
                logger.warning(f"LLM analysis failed, falling back to keyword analysis: {str(e)}")
                # Continue with keyword analysis results below

        # Step 5: Update result with keyword analysis if LLM not used or failed
        result["sentiment"] = sentiment
        result["valence"] = valence
        result["intensity"] = intensity
        result["primary_emotion"] = primary_emotion
        result["emotions"] = emotions_list[:5]  # Limit to top 5
        result["confidence"] = 0.7 if max_score > 0.6 else 0.5
        result["temporal_focus"] = temporal_focus
        result["negated"] = negated
        result["analysis_method"] = "keyword-based" # Explicitly set method

        # Step 6: Apply keyword-based sarcasm override if detected and LLM didn't run/succeed
        if sarcasm_detected:
            result["sentiment"] = "negative"
            result["valence"] = -0.5 # Override valence
            result["primary_emotion"] = "sarcasm" # Override primary emotion
            # Ensure sarcasm is in the list - use safer access with .lower() to handle case variations
            if not any(e.get("name", "").lower() == "sarcasm" for e in result["emotions"]):
                 result["emotions"].insert(0, {"name": "sarcasm", "score": 0.9}) # Add sarcasm with high score
            result["confidence"] = max(result["confidence"], 0.85) # Boost confidence for detected sarcasm

        # Add user historical context if available
        if user_profile_id:
            user_context = await get_user_sentiment_context(user_profile_id)
            if user_context:
                result["user_context"] = user_context

        return result

    except Exception as e:
        logger.error(f"Error analyzing message sentiment: {str(e)}", exc_info=True)
        return {
            "sentiment": "neutral",
            "confidence": 0.3,
            "analysis_method": "keyword-based", # Ensure method is set even in error
            "error": str(e)
        }

def keyword_analysis(text: str) -> Tuple[Dict[str, float], bool]:
    """
    Analyze text using keyword-based approach to detect emotions.

    Args:
        text: Normalized text (lowercase) to analyze

    Returns:
        Tuple containing:
        1. Dictionary of emotion scores (0.0-1.0)
        2. Boolean indicating if sentiment is negated
    """
    # Tokenize and preprocess text
    words = re.findall(r'\b\w+\b', text.lower())
    word_set = set(words)
    phrases = [text[max(0, m.start()-20):m.end()+20] for m in re.finditer(r'\b\w+\b', text)]

    # Check for negation
    negated = any(neg in word_set for neg in NEGATION_WORDS) or any(neg in text for neg in NEGATION_WORDS)

    # Initialize scores for each emotion
    emotion_scores = {emotion: 0.0 for emotion in EMOTION_KEYWORDS}

    # Calculate base scores from direct keyword matches
    for emotion, keywords in EMOTION_KEYWORDS.items():
        matches = sum(1 for keyword in keywords if keyword in word_set or keyword in text)
        if matches > 0:
            # Assign a base score per match, capped at 1.0
            # Reason: Original formula (matches / (len(words) / 5 + 1)) was too sensitive to message length,
            # resulting in low scores for clear keywords in longer messages. This simpler approach gives
            # more weight to keyword presence. 0.6 chosen so one match is significant, two maxes out.
            emotion_scores[emotion] = min(1.0, matches * 0.6)

    # Check for intensity modifiers
    intensity_modifier = 1.0
    for level, modifiers in INTENSITY_MODIFIERS.items():
        if any(mod in text for mod in modifiers):
            if level == "high":
                intensity_modifier = 1.5
            elif level == "moderate":
                intensity_modifier = 1.2
            else:  # low
                intensity_modifier = 0.7
            break

    # Apply intensity modifier
    for emotion in emotion_scores:
        emotion_scores[emotion] *= intensity_modifier

    # Cap values at 1.0
    emotion_scores = {k: min(v, 1.0) for k, v in emotion_scores.items()}

    return emotion_scores, negated

def detect_temporal_focus(text: str) -> str:
    """
    Detect whether the text is focused on present, past, or future emotions.

    Args:
        text: The text to analyze

    Returns:
        String indicating "present", "past", or "future"
    """
    present_count = sum(1 for indicator in TIME_INDICATORS["present"] if indicator in text)
    past_count = sum(1 for indicator in TIME_INDICATORS["past"] if indicator in text)
    future_count = sum(1 for indicator in TIME_INDICATORS["future"] if indicator in text)

    if past_count > present_count and past_count > future_count:
        return "past"
    elif future_count > present_count and future_count > past_count:
        return "future"
    else:
        return "present"  # Default to present if tied or no indicators

async def analyze_with_llm(message: str, context: Optional[Dict[str, Any]], user_profile_id: Optional[str]) -> Optional[Dict[str, Any]]:
    """
    Perform deeper sentiment analysis using the LLM.

    Args:
        message: The message to analyze
        context: Additional context for interpretation
        user_profile_id: User profile ID for personalization

    Returns:
        Dictionary containing sentiment analysis results or None on failure
    """
    try:
        # Import LLM client
        from apps.main.llm.client import LLMClient

        # Create LLM client
        llm_client = LLMClient()

        # Construct the prompt
        system_prompt = """You are an expert sentiment and emotion analyzer. Analyze the user's message to detect:
1. Overall sentiment (positive, negative, or neutral)
2. Valence score (-1.0 to 1.0 where -1 is very negative, 0 is neutral, 1 is very positive)
3. Emotional intensity (0-100)
4. Primary emotion present (can be 'sarcasm' if detected)
5. All emotions detected with their relative strength (include 'sarcasm' if detected)
6. Whether the sentiment is being negated (e.g., "not happy")
7. Temporal focus: Choose ONLY ONE dominant focus from 'present', 'past', or 'future', even if the message mentions multiple timeframes. Default to 'present' if unsure or mixed.

Your analysis should be detailed but concise. Pay special attention to nuances like sarcasm, mixed emotions, and cultural context.
You MUST respond with ONLY a valid JSON object using this exact schema:
{
  "sentiment": "positive/negative/neutral",
  "valence": float (-1.0 to 1.0),
  "intensity": int (0-100),
  "primary_emotion": "string",
  "emotions": [{"name": "emotion_name", "score": float}],
  "confidence": float (0.0-1.0),
  "temporal_focus": "present/past/future",
  "negated": boolean,
  "analysis_method": "llm-based",
  "message_context": "additional observations about the message"
}"""

        # Build user prompt with context if available
        user_prompt = f"Analyze the sentiment and emotions in this message: \"{message}\""

        if context and isinstance(context, dict):
            context_str = "\n\nAdditional context:\n"
            for key, value in context.items():
                if isinstance(value, (str, int, float, bool)):
                    context_str += f"- {key}: {value}\n"
            user_prompt += context_str

        # Call the LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await llm_client.chat_completion(messages=messages) # Request JSON output

        # Define required keys for validation
        required_keys = {"sentiment", "valence", "intensity", "primary_emotion", "emotions", "confidence", "temporal_focus", "negated"}

        # Parse the response using a multi-stage approach
        if response and response.is_text:
            content = response.content

            # Stage 1: Try direct JSON parsing
            try:
                result = json.loads(content)
                if all(key in result for key in required_keys):
                    # Ensure analysis_method is set
                    if "analysis_method" not in result:
                        result["analysis_method"] = "llm-based"
                    return result
                else:
                    missing_keys = required_keys - set(result.keys())
                    logger.warning(f"LLM response missing required keys: {missing_keys}")
            except json.JSONDecodeError:
                # Continue to fallback methods
                pass

            # Stage 2: Try to extract JSON from markdown code blocks
            json_patterns = [
                r"```(?:json)?\s*([\s\S]*?)\s*```",  # Markdown code block
                r"`([\s\S]*?)`",                     # Inline code
                r"(\{[\s\S]*\})",                    # Any JSON-like object
                r'(\{[^{]*?"sentiment"[^}]*\})',     # JSON with sentiment field
            ]

            for pattern in json_patterns:
                json_match = re.search(pattern, content)
                if json_match:
                    try:
                        json_str = json_match.group(1).strip()
                        # Clean up common issues
                        json_str = re.sub(r'([{,])\s*([a-zA-Z0-9_]+):', r'\1"\2":', json_str)  # Add quotes to keys
                        json_str = re.sub(r',\s*}', '}', json_str)  # Remove trailing commas

                        result = json.loads(json_str)
                        if all(key in result for key in required_keys):
                            # Ensure analysis_method is set
                            if "analysis_method" not in result:
                                result["analysis_method"] = "llm-based"
                            return result
                    except json.JSONDecodeError:
                        continue  # Try next pattern

            # Stage 3: Construct a minimal valid response if we can extract key fields
            try:
                # Extract sentiment
                sentiment_match = re.search(r'"sentiment"\s*:\s*"([^"]+)"', content)
                valence_match = re.search(r'"valence"\s*:\s*([-+]?\d*\.\d+|\d+)', content)
                emotion_match = re.search(r'"primary_emotion"\s*:\s*"([^"]+)"', content)

                if sentiment_match and valence_match and emotion_match:
                    sentiment = sentiment_match.group(1)
                    valence = float(valence_match.group(1))
                    primary_emotion = emotion_match.group(1)

                    # Construct a minimal valid response
                    result = {
                        "sentiment": sentiment,
                        "valence": valence,
                        "intensity": 50,  # Default
                        "primary_emotion": primary_emotion,
                        "emotions": [{"name": primary_emotion, "score": 0.8}],
                        "confidence": 0.7,  # Default
                        "temporal_focus": "present",  # Default
                        "negated": False,  # Default
                        "analysis_method": "llm-based",
                        "message_context": "Extracted from partial LLM response"
                    }

                    # Try to extract other fields if available
                    intensity_match = re.search(r'"intensity"\s*:\s*(\d+)', content)
                    if intensity_match:
                        result["intensity"] = int(intensity_match.group(1))

                    confidence_match = re.search(r'"confidence"\s*:\s*([-+]?\d*\.\d+|\d+)', content)
                    if confidence_match:
                        result["confidence"] = float(confidence_match.group(1))

                    temporal_match = re.search(r'"temporal_focus"\s*:\s*"([^"]+)"', content)
                    if temporal_match:
                        result["temporal_focus"] = temporal_match.group(1)

                    negated_match = re.search(r'"negated"\s*:\s*(true|false)', content, re.IGNORECASE)
                    if negated_match:
                        result["negated"] = negated_match.group(1).lower() == "true"

                    # Check for sarcasm in emotions or context
                    if "sarcasm" in content.lower() or "sarcastic" in content.lower():
                        # Add sarcasm to emotions if not already present
                        if not any(e.get("name", "").lower() == "sarcasm" for e in result["emotions"]):
                            result["emotions"].append({"name": "sarcasm", "score": 0.8})

                        # If sarcasm is prominent, make it the primary emotion
                        if "sarcasm" in content.lower() and "primary" in content.lower():
                            result["primary_emotion"] = "sarcasm"

                        # Adjust sentiment for sarcasm (usually negative)
                        if result["sentiment"] == "positive" and "sarcasm" in content.lower():
                            result["sentiment"] = "negative"
                            result["valence"] = -0.5

                    return result
            except Exception as e:
                logger.warning(f"Failed to extract partial sentiment data: {str(e)}")

            # Log the full response for debugging
            logger.warning(f"Failed to parse LLM response with all methods. Full response: {content}")
            return None

        logger.warning(f"LLM response was not text or was empty: {response}")
        return None

    except Exception as e:
        logger.error(f"Error in LLM sentiment analysis: {str(e)}", exc_info=True)
        return None

from channels.db import database_sync_to_async

async def get_user_sentiment_context(user_profile_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve historical sentiment context for a user.

    Args:
        user_profile_id: The user profile ID

    Returns:
        Dictionary with user's sentiment history or None if unavailable
    """
    try:
        # Import necessary models
        from apps.main.models import HistoryEvent
        from apps.user.models import UserProfile, CurrentMood
        from django.utils import timezone
        import datetime

        @database_sync_to_async
        def _get_user_profile(user_profile_id):
            # Use get() to match the test's expectations
            return UserProfile.objects.get(id=user_profile_id)

        @database_sync_to_async
        def _get_current_mood(user_profile):
            if not user_profile: return None
            try:
                mood = CurrentMood.objects.get(user_profile=user_profile)
                return {
                    "description": mood.description,
                    "height": mood.height,
                    "user_awareness": mood.user_awareness,
                    "processed_at": mood.processed_at.isoformat() if mood.processed_at else None
                }
            except CurrentMood.DoesNotExist:
                return None

        @database_sync_to_async
        def _get_recent_events(user_profile):
            if not user_profile: return []
            return list(HistoryEvent.objects.filter(
                user_profile=user_profile,
                event_type='sentiment_analysis',
                timestamp__gte=timezone.now() - datetime.timedelta(days=7)
            ).order_by('-timestamp')[:5])

        user_profile = await _get_user_profile(user_profile_id)
        if not user_profile:
             logger.warning(f"User profile not found for ID: {user_profile_id}")
             return None # Exit early if user profile doesn't exist

        current_mood = await _get_current_mood(user_profile)
        recent_events = await _get_recent_events(user_profile)

        if not recent_events and not current_mood:
            return None

        # Extract sentiment trends
        sentiment_history = []
        for event in recent_events:
            # Ensure details is a dict before accessing keys
            if isinstance(event.details, dict) and 'sentiment' in event.details:
                sentiment_history.append({
                    "timestamp": event.timestamp.isoformat(),
                    "sentiment": event.details.get('sentiment'),
                    "primary_emotion": event.details.get('primary_emotion'),
                    "valence": event.details.get('valence')
                })

        # Safely extract dominant sentiment from history
        dominant_sentiment = None
        if sentiment_history and len(sentiment_history) > 0:
            # Get the most recent sentiment entry
            most_recent = sentiment_history[0]
            if isinstance(most_recent, dict):
                dominant_sentiment = most_recent.get('sentiment')

        return {
            "current_mood": current_mood,
            "sentiment_history": sentiment_history,
            "dominant_sentiment": dominant_sentiment
        }

    except Exception as e:
        logger.warning(f"Error retrieving user sentiment context for {user_profile_id}: {str(e)}")
        return None

# Create a history event for sentiment analysis
async def record_sentiment_analysis(user_profile_id: str, analysis_result: Dict[str, Any]) -> None:
    """
    Record the sentiment analysis in the history system.

    Args:
        user_profile_id: The user profile ID
        analysis_result: The sentiment analysis result
    """
    try:
        # Import necessary models
        from apps.main.models import HistoryEvent
        from apps.user.models import UserProfile
        from django.contrib.contenttypes.models import ContentType

        @database_sync_to_async
        def _get_user_profile(user_profile_id):
            # Use get() to match the test's expectations
            return UserProfile.objects.get(id=user_profile_id)

        @database_sync_to_async
        def _create_history_event(user_profile, analysis_result):
            if not user_profile: return # Don't create event if user profile doesn't exist

            # Ensure analysis_result is a dict
            if not isinstance(analysis_result, dict):
                logger.warning(f"Invalid analysis_result type for recording: {type(analysis_result)}")
                return

            HistoryEvent.objects.create(
                event_type='sentiment_analysis',
                user_profile=user_profile,
                content_type=ContentType.objects.get_for_model(UserProfile),
                object_id=user_profile.id,
                details={
                    'sentiment': analysis_result.get('sentiment'),
                    'valence': analysis_result.get('valence'),
                    'intensity': analysis_result.get('intensity'),
                    'primary_emotion': analysis_result.get('primary_emotion'),
                    'temporal_focus': analysis_result.get('temporal_focus'),
                    'confidence': analysis_result.get('confidence'),
                    'analysis_method': analysis_result.get('analysis_method') # Ensure this is included
                }
            )

        user_profile = await _get_user_profile(user_profile_id)
        await _create_history_event(user_profile, analysis_result)

    except Exception as e:
        logger.warning(f"Failed to record sentiment analysis for {user_profile_id}: {str(e)}")
