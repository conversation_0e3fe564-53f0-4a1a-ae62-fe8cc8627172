from apps.main.agents.tools.tools_util import register_tool

import logging
from typing import Dict, Any, TYPE_CHECKING, Optional, List

# Use TYPE_CHECKING to allow type hints without causing runtime import errors
if TYPE_CHECKING:
    from apps.user.models import UserProfile, Belief, UserGoal, UserTraitInclination, TrustLevel, UserDemographics, CurrentMood, GenericTrait, GenericBelief, PersonalResource
    from apps.activity.models import ActivityTailored, ActivityInfluencedBy, EntityDomainRelationship, GenericActivity, UserRequirement, EnvironmentRequirement, ResourceRequirement, Domain, GenericEnvironment
    from django.contrib.contenttypes.models import ContentType

logger = logging.getLogger(__name__)


@register_tool('get_user_profile')
async def get_user_profile(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Retrieves comprehensive user profile information including demographics, traits, goals, and current context.

    Input:
        user_profile_id: UUID of the user profile
        include_beliefs: Whether to include user beliefs (default: True)
        include_goals: Whether to include user goals (default: True)
        include_traits: Whether to include personality traits (default: True)
        include_environment: Whether to include current environment details (default: True)

    Output:
        user_profile: Dictionary with user profile details
        id: UUID of the user profile
        profile_name: Display name of the user
        demographics: Basic demographic information
        traits: User personality trait inclinations (if requested)
        goals: User goals and aspirations (if requested)
        beliefs: User belief system information (if requested)
        current_environment: Current environment context (if requested)
        current_mood: Current mood information (if available)
        trust_level: Trust metrics and phase information
    """
    # Defer imports until needed
    from channels.db import database_sync_to_async
    from apps.main.services.event_service import EventService
    from apps.user.models import UserProfile, TrustLevel
    from django.core.exceptions import SynchronousOnlyOperation

    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    include_beliefs = input_data.get('include_beliefs', True)
    include_goals = input_data.get('include_goals', True)
    include_traits = input_data.get('include_traits', True)
    include_environment = input_data.get('include_environment', True)

    if not user_profile_id:
        return {"error": "user_profile_id is required"}

    try:
        # Define prefetch/select lists
        prefetch_related_list: List[str] = []
        select_related_list: List[str] = ['demographics'] # Always try to select demographics

        if include_beliefs:
            prefetch_related_list.append('beliefs__generic_belief') # Prefetch generic belief too
        if include_goals:
            prefetch_related_list.append('user_goals')
        if include_traits:
            prefetch_related_list.append('trait_inclinations__generic_trait')
        if include_environment:
            # Select related for direct FK, prefetch for related FKs on the environment
            select_related_list.append('current_environment__generic_environment')
            prefetch_related_list.append('current_environment__domain_relationships__domain') # Prefetch domains via environment

        # Select related for mood if possible (might be null)
        select_related_list.append('current_mood')
        # Select related for trust level if possible (might be null)
        select_related_list.append('trust_level')


        # Wrap the synchronous ORM call with database_sync_to_async
        @database_sync_to_async
        def get_profile_from_db_sync(profile_id: str, select_fields: List[str], prefetch_fields: List[str]) -> Optional['UserProfile']:
            # Defer model import inside sync function
            from apps.user.models import UserProfile
            try:
                # Filter out potential None values if select_related fields are nullable
                valid_select_fields = [f for f in select_fields if f]
                valid_prefetch_fields = [f for f in prefetch_fields if f]
                return UserProfile.objects.select_related(*valid_select_fields).prefetch_related(*valid_prefetch_fields).get(id=profile_id)
            except UserProfile.DoesNotExist:
                return None

        user_profile: Optional['UserProfile'] = await get_profile_from_db_sync(user_profile_id, select_related_list, prefetch_related_list)

        if not user_profile:
             logger.warning(f"User profile not found: {user_profile_id}")
             return {"error": f"User profile with ID {user_profile_id} not found"}

        # Build the response
        result: Dict[str, Any] = {
            "id": str(user_profile.id),
            "profile_name": user_profile.profile_name,
        }

        # Add demographics if available (already selected)
        demographics: Optional['UserDemographics'] = getattr(user_profile, 'demographics', None)
        if demographics:
            result["demographics"] = {
                "full_name": demographics.full_name,
                "age": demographics.age,
                "gender": demographics.gender,
                "location": demographics.location,
                "language": demographics.language,
                "occupation": demographics.occupation
            }
        else:
             result["demographics"] = None # Explicitly set to None if not found

        # Add traits if requested (prefetched)
        if include_traits:
            traits_list = []
            # Iterate over prefetched data in async context
            trait_inclinations: List['UserTraitInclination'] = list(user_profile.trait_inclinations.all())
            for trait_inclination in trait_inclinations:
                generic_trait: Optional['GenericTrait'] = getattr(trait_inclination, 'generic_trait', None) # Access prefetched
                if generic_trait:
                    traits_list.append({
                        "id": generic_trait.id,
                        "code": generic_trait.code,
                        "name": generic_trait.name,
                        "trait_type": generic_trait.trait_type,
                        "strength": trait_inclination.strength,
                        "awareness": trait_inclination.awareness
                    })
            result["traits"] = traits_list

        # Add goals if requested (prefetched)
        if include_goals:
            goals_list = []
            user_goals: List['UserGoal'] = list(user_profile.user_goals.all())
            for goal in user_goals:
                goals_list.append({
                    "id": str(goal.id),
                    "title": goal.title,
                    "description": goal.description,
                    "importance_according_user": goal.importance_according_user,
                    "importance_according_system": goal.importance_according_system,
                    "strength": goal.strength,
                    "goal_type": goal.__class__.__name__,
                    "created_at": goal.created_at.isoformat() if goal.created_at else None
                })
            result["goals"] = goals_list

        # Add beliefs if requested (prefetched)
        if include_beliefs:
            beliefs_list = []
            beliefs: List['Belief'] = list(user_profile.beliefs.all())
            for belief in beliefs:
                generic_belief: Optional['GenericBelief'] = getattr(belief, 'generic_belief', None) # Access prefetched
                beliefs_list.append({
                    "id": str(belief.id),
                    "content": belief.content,
                    "last_updated": belief.last_updated.isoformat() if belief.last_updated else None,
                    "user_confidence": belief.user_confidence,
                    "system_confidence": belief.system_confidence,
                    "emotionality": belief.emotionality,
                    "stability": belief.stability,
                    "user_awareness": belief.user_awareness,
                    "generic_belief_id": generic_belief.id if generic_belief else None
                })
            result["beliefs"] = beliefs_list

        # Add current environment if requested (selected/prefetched)
        if include_environment:
            env = getattr(user_profile, 'current_environment', None)
            if env:
                environment_data = {
                    "id": str(env.id),
                    "name": env.environment_name,
                    "description": env.environment_description
                }
                # Add generic environment data if available (selected)
                gen_env: Optional['GenericEnvironment'] = getattr(env, 'generic_environment', None)
                if gen_env:
                    environment_data["generic_environment"] = {
                        "id": gen_env.id,
                        "code": gen_env.code,
                        "name": gen_env.name,
                        "is_indoor": gen_env.is_indoor,
                        "primary_category": gen_env.primary_category
                    }

                # Add domain support information (prefetched)
                domain_summary = {}
                domain_relationships: List['EntityDomainRelationship'] = list(env.domain_relationships.all())
                for rel in domain_relationships:
                     domain: Optional['Domain'] = getattr(rel, 'domain', None)
                     if domain:
                          domain_summary[domain.code] = rel.strength # Example summary
                environment_data["domain_support"] = domain_summary # Use prefetched data

                result["current_environment"] = environment_data
            else:
                 result["current_environment"] = None # Explicitly set to None

        # Add current mood if available (selected)
        mood: Optional['CurrentMood'] = getattr(user_profile, 'current_mood', None)
        if mood:
            result["current_mood"] = {
                "description": mood.description,
                "height": mood.height,
                "user_awareness": mood.user_awareness,
                "processed_at": mood.processed_at.isoformat() if mood.processed_at else None
            }
        else:
             result["current_mood"] = None # Explicitly set to None

        # Add trust level information (selected)
        trust_level: Optional['TrustLevel'] = getattr(user_profile, 'trust_level', None)
        if trust_level:
            result["trust_level"] = {
                "value": trust_level.value,
                # Ensure these fields exist on TrustLevel model before uncommenting
                # "aggregate_type": trust_level.aggregate_type,
                # "aggregate_id": trust_level.aggregate_id,
                # "notes": trust_level.notes,
                "phase": "Expansion" if trust_level.value >= 60 else "Foundation"
            }
        else:
            # Default trust level information if not found
            result["trust_level"] = {
                "value": 50,  # Default middle value
                "phase": "Foundation",  # Default to Foundation phase
                "note": "Trust level not yet established"
            }

        return {"user_profile": result}

    except SynchronousOnlyOperation as sync_error:
        # This might still happen if prefetching fails or isn't comprehensive
        user_facing_error_message = "A technical issue occurred while retrieving the user profile (Sync error). Please try again later or contact support."
        debug_message = f"Synchronous database call detected in get_user_profile for ID {user_profile_id}: {sync_error}"
        logger.error(debug_message, exc_info=True)
        await EventService.emit_debug_info(
            level='error', message=debug_message, source='get_user_profile_tool',
            details={'user_profile_id': str(user_profile_id), 'exception': str(sync_error)},
            user_profile_id=str(user_profile_id)
        )
        await EventService.emit_error(message=user_facing_error_message, user_profile_id=str(user_profile_id))
        return {"error": debug_message}
    except Exception as e:
        # Catch specific exceptions if needed, e.g., ValidationError
        error_message = f"An unexpected error occurred while retrieving user profile: {type(e).__name__} - {str(e)}"
        logger.exception(f"Error retrieving user profile for ID {user_profile_id}")
        await EventService.emit_error(
            message="An unexpected error occurred while retrieving your profile.",
            user_profile_id=str(user_profile_id)
        )
        return {"error": error_message}


@register_tool('get_activity_details')
async def get_activity_details(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Retrieves comprehensive details about a tailored activity for presentation to the user.

    Input:
        activity_id: UUID of the tailored activity
        include_influences: Whether to include influence information (default: True)
        include_domains: Whether to include domain information (default: True)
        include_requirements: Whether to include requirement details (default: True)

    Output:
        activity: Dictionary with activity details
            id: UUID of the activity
            name: Name of the activity
            description: Detailed description of the activity
            instructions: Step-by-step instructions for completing the activity
            challenge_rating: Overall challenge rating of the activity
            challenge_breakdown: Challenge levels across different personality dimensions
            duration_range: Expected time required for the activity
            domains: Activity domains and their strengths (if requested)
            influences: Factors that influenced activity selection (if requested)
            requirements: Activity requirements details (if requested)
            value_proposition: The benefits and growth opportunities of the activity
    """
    # Defer imports
    from channels.db import database_sync_to_async
    from apps.activity.models import ActivityTailored
    from django.contrib.contenttypes.models import ContentType
    from django.core.exceptions import SynchronousOnlyOperation
    from apps.main.services.event_service import EventService # Assuming needed for error handling

    # Extract required parameters
    activity_id = input_data.get('activity_id')
    include_influences = input_data.get('include_influences', True)
    include_domains = input_data.get('include_domains', True)
    include_requirements = input_data.get('include_requirements', True)

    if not activity_id:
        return {"error": "activity_id is required"}

    try:
        # Define prefetch/select lists
        select_related_list: List[str] = ['user_profile', 'generic_activity']
        prefetch_related_list: List[str] = []

        if include_domains:
            prefetch_related_list.append('domain_relationships__domain')
        if include_influences:
            # Prefetch related influencing entities as well
            prefetch_related_list.append('influences__content_type')
            prefetch_related_list.append('influences__influencing_entity') # Important!
        if include_requirements:
            prefetch_related_list.append('user_requirements__content_type')
            prefetch_related_list.append('env_requirements')
            prefetch_related_list.append('resource_requirements__personal_resource')

        # Wrap the main activity fetch
        @database_sync_to_async
        def get_activity_sync(act_id: str, select_fields: List[str], prefetch_fields: List[str]) -> Optional['ActivityTailored']:
            # Defer model import
            from apps.activity.models import ActivityTailored
            try:
                # Filter out potential None values
                valid_select_fields = [f for f in select_fields if f]
                valid_prefetch_fields = [f for f in prefetch_fields if f]
                return ActivityTailored.objects.select_related(*valid_select_fields).prefetch_related(*valid_prefetch_fields).get(id=act_id)
            except ActivityTailored.DoesNotExist:
                return None

        # Await the database call
        activity: Optional['ActivityTailored'] = await get_activity_sync(activity_id, select_related_list, prefetch_related_list)

        if not activity:
            logger.warning(f"Activity not found: {activity_id}")
            return {"error": f"Activity with ID {activity_id} not found"}

        # Build the response (synchronous part)
        result: Dict[str, Any] = {
            "id": str(activity.id),
            "name": activity.name,
            "description": activity.description,
            "instructions": activity.instructions,
            "challenge_rating": activity.base_challenge_rating,
            "challenge_breakdown": activity.challengingness, # Assuming this is a JSONField or similar
            "duration_range": activity.duration_range,
            "version": activity.version,
            "tailorization_level": activity.tailorization_level
        }

        # Add generic activity reference (selected)
        generic_activity: Optional['GenericActivity'] = getattr(activity, 'generic_activity', None)
        if generic_activity:
            result["generic_activity"] = {
                "id": generic_activity.id,
                "name": generic_activity.name,
                "code": generic_activity.code
            }
        else:
            result["generic_activity"] = None

        # Add domains if requested (prefetched)
        if include_domains:
            domains_list = []
            domain_relationships: List['EntityDomainRelationship'] = list(activity.domain_relationships.all())
            for rel in domain_relationships:
                domain: Optional['Domain'] = getattr(rel, 'domain', None) # Access prefetched
                if domain:
                    domains_list.append({
                        "id": domain.id,
                        "name": domain.name,
                        "code": domain.code,
                        "strength": rel.strength,
                        "strength_display": rel.get_strength_display(), # Assuming this method doesn't hit DB
                        "primary_category": domain.primary_category
                    })
            result["domains"] = domains_list

        # Add influences if requested (prefetched)
        if include_influences:
            influences_list = []
            influences: List['ActivityInfluencedBy'] = list(activity.influences.all())
            for influence in influences:
                # Access prefetched related objects
                content_type: Optional['ContentType'] = getattr(influence, 'content_type', None)
                entity: Optional[Any] = getattr(influence, 'influencing_entity', None) # Prefetched generic relation

                if content_type and entity:
                    entity_type = content_type.model
                    entity_data = {}

                    # Safely access attributes based on expected type (no DB hit)
                    if entity_type == 'usergoal':
                        entity_data = {
                            "title": getattr(entity, 'title', 'N/A'),
                            "importance": getattr(entity, 'importance_according_user', 0),
                            "goal_type": entity.__class__.__name__
                        }
                    elif entity_type == 'belief':
                        entity_data = {
                            "content": getattr(entity, 'content', 'N/A'),
                            "user_confidence": getattr(entity, 'user_confidence', 0),
                            "emotionality": getattr(entity, 'emotionality', 0)
                        }
                    elif entity_type == 'usertraitinclination': # Check actual model name
                        # Access generic_trait via the entity (should be prefetched via UserProfile if needed, or handle differently)
                        # This might require adjusting the prefetch strategy if not already covered
                        # For now, assume basic fields are available
                        trait_name = "Unknown Trait"
                        # Example: If generic_trait was prefetched elsewhere and attached
                        # generic_trait = getattr(entity, 'generic_trait', None)
                        # if generic_trait: trait_name = generic_trait.name
                        entity_data = {
                            "trait_name": trait_name,
                            "strength": getattr(entity, 'strength', 0)
                        }

                    influences_list.append({
                        "type": entity_type,
                        "strength": influence.influence_strength,
                        "note": influence.note,
                        "entity_data": entity_data
                    })
            result["influences"] = influences_list

        # Add requirements if requested (prefetched)
        if include_requirements:
            user_reqs = []
            user_requirements: List['UserRequirement'] = list(activity.user_requirements.all())
            for req in user_requirements:
                content_type: Optional['ContentType'] = getattr(req, 'content_type', None)
                user_reqs.append({
                    "type": content_type.model if content_type else 'unknown',
                    "required_level": req.required_level,
                    "optional": req.optional,
                    "resource_type_name": req.resource_type_name
                })

            env_reqs = []
            env_requirements: List['EnvironmentRequirement'] = list(activity.env_requirements.all())
            for req in env_requirements:
                env_reqs.append({
                    "env_type": req.env_type,
                    "required_level": req.required_level,
                    "optional": req.optional,
                    "details": req.env_detail
                })

            resource_reqs = []
            resource_requirements: List['ResourceRequirement'] = list(activity.resource_requirements.all())
            for req in resource_requirements:
                personal_resource: Optional['PersonalResource'] = getattr(req, 'personal_resource', None) # Access prefetched
                resource_reqs.append({
                    "resource_name": personal_resource.specific_name if personal_resource else "Unknown",
                    "quantity": req.quantity_required,
                    "optional": req.optional
                })
            result["requirements"] = {
                "user": user_reqs,
                "environment": env_reqs,
                "resources": resource_reqs
            }

        # Generate value proposition (sync helper using processed data)
        value_proposition = _generate_value_proposition(activity, result.get("influences", []), result.get("domains", []))
        result["value_proposition"] = value_proposition

        return {"activity": result}

    except SynchronousOnlyOperation as sync_error:
        # Handle potential sync errors during processing prefetched data if not careful
        user_facing_error_message = "A technical issue occurred while retrieving activity details (Sync error)."
        debug_message = f"Synchronous database call detected in get_activity_details for ID {activity_id}: {sync_error}"
        logger.error(debug_message, exc_info=True)
        # Consider emitting events similar to get_user_profile if EventService is available
        # await EventService.emit_debug_info(...)
        # await EventService.emit_error(...)
        return {"error": debug_message}
    except Exception as e:
        error_message = f"An unexpected error occurred while retrieving activity details: {type(e).__name__} - {str(e)}"
        logger.exception(f"Error retrieving activity details for ID {activity_id}")
        # Consider emitting events
        # await EventService.emit_error(...)
        return {"error": error_message}


def _generate_value_proposition(activity: 'ActivityTailored', influences: List[Dict], domains: List[Dict]) -> Dict[str, Any]:
    """Helper function to generate a value proposition based on activity data (remains synchronous)"""
    value_points = []

    # Use influences to identify growth opportunities
    if influences:
        for influence in influences:
            entity_data = influence.get("entity_data", {})
            if influence.get("type") == "usergoal" and influence.get("strength", 0) > 50:
                value_points.append(f"Supports your goal: {entity_data.get('title', 'personal development')}")
            elif influence.get("type") == "belief" and influence.get("strength", 0) > 70:
                 value_points.append(f"Reinforces belief: '{entity_data.get('content', 'a positive outlook')[:30]}...'")
            elif influence.get("type") == "belief" and 30 <= influence.get("strength", 0) <= 70:
                 value_points.append(f"Challenges belief: '{entity_data.get('content', 'a limiting perspective')[:30]}...'")
            # Add handling for usertraitinclination if needed

    # Use domains to identify skill development areas
    if domains:
        primary_domains = sorted([d for d in domains if d.get("strength", 0) >= 70], key=lambda x: x.get("strength", 0), reverse=True)
        if primary_domains:
            domain_names = [d.get("name") for d in primary_domains[:2]]
            if len(domain_names) == 1:
                value_points.append(f"Develops skills in {domain_names[0]}")
            elif len(domain_names) == 2:
                value_points.append(f"Develops skills in {domain_names[0]} and {domain_names[1]}")

    # Use challenge rating to identify growth opportunity
    challenge_rating = activity.base_challenge_rating
    if challenge_rating is not None: # Check if challenge rating exists
        if challenge_rating < 30:
            value_points.append("Offers a comfortable experience to build confidence")
        elif challenge_rating < 60:
            value_points.append("Provides balanced challenge for steady growth")
        else:
            value_points.append("Stretches your boundaries for significant growth")

    # If we don't have enough value points, add generic ones
    if not value_points: # Add default if list is empty
        value_points.append("Supports your personal development journey")
        value_points.append("Offers a chance to learn and grow")

    # Combine value points into a proposition
    summary = "This activity offers multiple benefits for your growth" if len(value_points) >= 3 else "This activity was selected to support your development"

    return {
        "summary": summary,
        "points": list(set(value_points)) # Ensure unique points
    }
