"""
Mentor agent tools for conversation management and goal tracking.

These tools support the discussion workflow by providing functionality for:
1. Retrieving conversation history
2. Storing conversation memory for future reference
3. Tracking and updating goal completion status
"""

import logging
import uuid
import datetime
from typing import Dict, Any, List, Optional, Union

import pytest


pytestmark = pytest.mark.django_db
from channels.db import database_sync_to_async

# Import tool utilities
from apps.main.agents.tools.tools_util import register_tool

# Configure logging
logger = logging.getLogger(__name__)

@register_tool('get_conversation_history')
async def get_conversation_history(
    user_profile_id: str,
    limit: int = 10,
    skip_system_messages: bool = True,
    include_metadata: bool = False
) -> Dict[str, Any]:
    """
    Retrieves recent conversation history for a user.
    
    Input:
        user_profile_id: UUID of the user profile
        limit: Maximum number of messages to retrieve (default: 10)
        skip_system_messages: Whether to exclude system messages (default: True)
        include_metadata: Whether to include message metadata (default: False)
        
    Output:
        history: List of conversation messages
            role: Role of the message sender (user/assistant)
            content: Text content of the message
            timestamp: When the message was sent (if include_metadata=True)
        count: Total number of messages returned
    """
    try:
        # Import models
        from apps.main.models import HistoryEvent
        from apps.user.models import UserProfile
        from django.utils import timezone

        # Wrap DB calls
        @database_sync_to_async
        def _get_user_profile(profile_id):
            return UserProfile.objects.get(id=profile_id)

        @database_sync_to_async
        def _get_history_events(profile, event_types, count_limit):
            # Convert the queryset to a list within the sync context
            return list(HistoryEvent.objects.filter(
                user_profile=profile,
                event_type__in=event_types
            ).order_by('-timestamp')[:count_limit])

        # Get the user profile asynchronously
        user_profile = await _get_user_profile(user_profile_id)

        # Query history events asynchronously
        history_events = await _get_history_events(
            user_profile,
            ['chat_message', 'user_message', 'assistant_message'],
            limit
        )

        # Format the conversation history
        messages = []
        for event in reversed(history_events):  # Reverse to get chronological order
            # Determine message role
            if event.event_type == 'user_message':
                role = 'user'
            elif event.event_type == 'assistant_message':
                role = 'assistant'
            elif event.details.get('is_user', False):
                role = 'user'
            else:
                role = 'assistant'
                
            # Skip system messages if requested
            if skip_system_messages and role == 'system':
                continue
                
            # Create message object
            message = {
                "role": role,
                "content": event.details.get('message', event.details.get('content', ''))
            }
            
            # Add metadata if requested
            if include_metadata:
                message["timestamp"] = event.timestamp.isoformat() if event.timestamp else None
                message["message_id"] = str(event.id)
                if 'metadata' in event.details:
                    message["metadata"] = event.details['metadata']
            
            messages.append(message)
        
        return {
            "history": messages,
            "count": len(messages)
        }
        
    except UserProfile.DoesNotExist:
        logger.warning(f"User profile with ID {user_profile_id} not found")
        return {
            "history": [],
            "count": 0,
            "error": f"User profile with ID {user_profile_id} not found"
        }
    except Exception as e:
        logger.error(f"Error retrieving conversation history: {str(e)}", exc_info=True)
        return {
            "history": [],
            "count": 0,
            "error": str(e)
        }

@register_tool('store_conversation_memory')
async def store_conversation_memory(
    user_profile_id: str,
    memory_key: str,
    content: Any,
    confidence: float = 0.8,
    expires_at: Optional[str] = None
) -> Dict[str, Any]:
    """
    Stores conversation-related information in agent memory for future reference.
    
    Input:
        user_profile_id: UUID of the user profile
        memory_key: Key to identify the memory item
        content: Information to store (can be text or structured data)
        confidence: Confidence level for this memory (0.0-1.0)
        expires_at: Optional expiration date in ISO format
        
    Output:
        success: Whether the operation was successful
        memory_id: Unique identifier for the stored memory
        stored_at: Timestamp when the memory was stored
    """
    try:
        # Import models
        from apps.main.models import AgentMemory
        from apps.user.models import UserProfile
        from django.utils import timezone
        import datetime
        
        # Get the user profile
        @database_sync_to_async
        def _get_user_profile(profile_id):
            return UserProfile.objects.get(id=profile_id)

        # Parse expiration date if provided
        expiration = None
        if expires_at:
            try:
                expiration = datetime.datetime.fromisoformat(expires_at)
            except ValueError:
                logger.warning(f"Invalid expiration date format: {expires_at}")

        @database_sync_to_async
        def _update_or_create_memory(user_profile, memory_key, content, confidence, expiration):
            memory, created = AgentMemory.objects.update_or_create(
                agent_role='mentor',  # Using 'mentor' as the agent role
                user_profile=user_profile,
                memory_key=memory_key,
                defaults={
                    'content': content,
                    'confidence': confidence,
                    'expires_at': expiration
                }
            )
            if not created:
                memory.access_count += 1
                memory.last_accessed = timezone.now()
                memory.save(update_fields=['access_count', 'last_accessed'])
            return memory, created

        @database_sync_to_async
        def _create_history_event(user_profile, memory, created):
            from django.contrib.contenttypes.models import ContentType
            from apps.main.models import HistoryEvent
            HistoryEvent.objects.create(
                event_type='memory_stored',
                user_profile=user_profile,
                content_type=ContentType.objects.get_for_model(AgentMemory),
                object_id=str(memory.id),  # Convert to string
                details={
                    'memory_key': memory_key,
                    'confidence': confidence,
                    'agent_role': 'mentor',
                    'is_new': created
                }
            )

        user_profile = await _get_user_profile(user_profile_id)
        memory, created = await _update_or_create_memory(user_profile, memory_key, content, confidence, expiration)
        await _create_history_event(user_profile, memory, created)

        return {
            "success": True,
            "memory_id": str(memory.id),
            "stored_at": timezone.now().isoformat()
        }

    except UserProfile.DoesNotExist:
        logger.warning(f"User profile with ID {user_profile_id} not found")
        return {
            "success": False,
            "error": f"User profile with ID {user_profile_id} not found"
        }
    except Exception as e:
        logger.error(f"Error storing conversation memory: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }

@register_tool('update_goal_completion_status')
async def update_goal_completion_status(
    user_profile_id: str,
    goal_id: str,
    status: str,
    collected_value: Optional[Any] = None,
    notes: Optional[str] = None
) -> Dict[str, Any]:
    """
    Updates the completion status of a conversation goal, particularly useful
    for tracking information collection goals in the discussion workflow.
    
    Input:
        user_profile_id: UUID of the user profile
        goal_id: Identifier for the goal (e.g., "info_collection_time_availability")
        status: New status for the goal (e.g., "completed", "in_progress", "failed")
        collected_value: Optional value collected when goal is information gathering
        notes: Optional notes about the goal completion
        
    Output:
        success: Whether the operation was successful
        goal_id: The goal identifier that was updated
        status: The updated status
    """
    try:
        # Import models
        from apps.main.models import AgentGoal, HistoryEvent # Corrected ConversationGoal to AgentGoal
        from apps.user.models import UserProfile
        from django.utils import timezone
        from django.contrib.contenttypes.models import ContentType
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)

        # TODO: Refactor this tool to align with the actual AgentGoal model structure.
        #       - Use appropriate fields for identification (e.g., description or a dedicated ID).
        #       - Handle status updates appropriately (e.g., store in notes or add a status field to AgentGoal).
        #       - Remove references to non-existent fields like 'goal_type', 'created_at', 'completed_at'.

        # Try to get existing goal or create a new one
        # WARNING: Using 'goal_id' (input param) to match AgentGoal's 'description' field based on assumption.
        # This needs verification and likely refactoring. AgentGoal also needs user_goal_id and priority.
        goal, created = AgentGoal.objects.get_or_create( # Corrected ConversationGoal to AgentGoal
            user_profile=user_profile,
            description=goal_id, # Assuming goal_id maps to description
            defaults={
                # 'goal_type': 'information_collection', # Field doesn't exist on AgentGoal
                # 'status': 'initiated', # Field doesn't exist on AgentGoal
                # 'created_at': timezone.now() # Field doesn't exist on AgentGoal
                'priority': 5, # Provide a default priority
                'user_goal_id': uuid.uuid4() # Provide a default user_goal_id (consider if this should be passed in)
            }
        )

        # WARNING: AgentGoal model doesn't have status, completed_at, collected_value, or notes fields.
        # These updates will not persist on the goal object itself.
        # The status, collected_value, and notes are captured in the HistoryEvent details below.
        # goal.status = status # Field doesn't exist
        # if status == 'completed':
        #     goal.completed_at = timezone.now() # Field doesn't exist
        # if collected_value is not None:
        #     goal.collected_value = collected_value # Field doesn't exist
        # if notes:
        #     goal.notes = notes # Field doesn't exist

        # Save the goal instance if it was newly created or if other fields were modified (none in this case)
        # If AgentGoal had other modifiable fields relevant here, they would be set above and saved here.
        if created:
             goal.save() # Save only if created or if other valid fields were modified
        
        # Create a history event for this goal update
        HistoryEvent.objects.create(
            event_type='goal_status_updated',
            user_profile=user_profile,
            content_type=ContentType.objects.get_for_model(AgentGoal), # Corrected ConversationGoal to AgentGoal
            object_id=str(goal.id),  # Convert to string
            details={
                'goal_id': goal_id, # Keep goal_id here as it's the identifier passed to the tool
                'status': status,
                'collected_value': collected_value,
                'notes': notes
            }
        )
        
        # If this was an information collection goal and it's now completed,
        # store the collected information in memory for easy retrieval
        if status == 'completed' and collected_value is not None:
            # Import AgentMemory model
            from apps.main.models import AgentMemory
            
            # Extract the field name from the goal_id
            # Assuming format like "info_collection_time_availability"
            field_name = goal_id.split('_')[-1] if '_' in goal_id else goal_id
            
            # Store in agent memory
            AgentMemory.objects.update_or_create(
                agent_role='mentor',
                user_profile=user_profile,
                memory_key=field_name,
                defaults={
                    'content': collected_value,
                    'confidence': 0.95,  # High confidence for directly collected info
                    'last_accessed': timezone.now()
                }
            )
        
        return {
            "success": True,
            "goal_id": goal_id,
            "status": status
        }
        
    except UserProfile.DoesNotExist:
        logger.warning(f"User profile with ID {user_profile_id} not found")
        return {
            "success": False,
            "error": f"User profile with ID {user_profile_id} not found"
        }
    except Exception as e:
        logger.error(f"Error updating goal completion status: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }
