from apps.main.agents.tools.tools_util import register_tool
import json
import logging
from typing import Dict, Any, List, Optional
from django.utils import timezone
import datetime
from collections import Counter

logger = logging.getLogger(__name__)

@register_tool('get_user_engagement_status')
async def get_user_engagement_status(
    user_profile_id: str,
    timeframe: str = "recent",
    include_trend_analysis: bool = True
) -> Dict[str, Any]:
    """
    Analyzes current user engagement metrics to understand user state.
    
    This tool examines recent user interactions, activity completions, and
    feedback patterns to assess user engagement and satisfaction levels.
    
    Input:
        user_profile_id: UUID of the user profile
        timeframe: Time period to analyze ("recent", "weekly", "monthly", "all_time")
        include_trend_analysis: Whether to include trend analysis over time
        
    Output:
        engagement_status: Dictionary with user engagement analysis
            current_engagement_level: Overall engagement score (0.0-1.0)
            activity_completion_rate: Rate of activity completion
            average_activity_satisfaction: User satisfaction with activities
            interaction_patterns: Analysis of interaction patterns
            recent_mood_trends: Analysis of mood trends
            engagement_trends: Engagement trends over time (if requested)
            suggestions: Engagement improvement suggestions
    """
    try:
        # Define timeframe in days
        timeframe_days = {
            "recent": 7,
            "weekly": 7,
            "monthly": 30,
            "all_time": 365  # Use a year as practical "all time"
        }.get(timeframe, 7)
        
        # Get activity completion data
        activity_data = await _get_activity_completion_data(user_profile_id, timeframe_days)
        
        # Get interaction data
        interaction_data = await _get_interaction_data(user_profile_id, timeframe_days)
        
        # Get mood data
        mood_data = await _get_mood_data(user_profile_id, timeframe_days)
        
        # Calculate overall engagement level
        # A weighted combination of activity completion, interaction frequency, and feedback quality
        activity_weight = 0.5
        interaction_weight = 0.3
        feedback_weight = 0.2
        
        engagement_level = (
            activity_data.get("completion_rate", 0) * activity_weight +
            interaction_data.get("engagement_score", 0) * interaction_weight +
            activity_data.get("average_satisfaction", 0) / 100 * feedback_weight  # Normalize to 0-1
        )
        
        # Identify interaction patterns
        interaction_patterns = _analyze_interaction_patterns(interaction_data)
        
        # Generate engagement trends if requested
        engagement_trends = None
        if include_trend_analysis:
            engagement_trends = await _analyze_engagement_trends(user_profile_id, timeframe_days)
        
        # Generate suggestions based on engagement data
        suggestions = _generate_engagement_suggestions(
            engagement_level, 
            activity_data, 
            interaction_data,
            mood_data
        )
        
        return {
            "engagement_status": {
                "current_engagement_level": engagement_level,
                "activity_completion_rate": activity_data.get("completion_rate", 0),
                "average_activity_satisfaction": activity_data.get("average_satisfaction", 0),
                "interaction_patterns": interaction_patterns,
                "recent_mood_trends": mood_data.get("trends", {}),
                "engagement_trends": engagement_trends,
                "suggestions": suggestions
            }
        }
        
    except Exception as e:
        logger.error(f"Error in get_user_engagement_status: {str(e)}", exc_info=True)
        return {
            "engagement_status": {
                "current_engagement_level": 0.5,  # Default middle value
                "activity_completion_rate": 0,
                "average_activity_satisfaction": 0,
                "interaction_patterns": {},
                "recent_mood_trends": {},
                "engagement_trends": None,
                "suggestions": [],
                "error": str(e)
            }
        }

@register_tool('detect_emotional_state_shifts')
async def detect_emotional_state_shifts(
    user_profile_id: str,
    current_message: str,
    context_packet: Dict[str, Any],
    lookback_period: int = 7
) -> Dict[str, Any]:
    """
    Recognizes significant changes in user emotional state.
    
    This tool analyzes current message content against historical mood data
    to identify emotional shifts requiring special handling.
    
    Input:
        user_profile_id: UUID of the user profile
        current_message: The user's current message text
        context_packet: Current context with mood information
        lookback_period: Days to look back for mood history
        
    Output:
        emotional_analysis: Dictionary with emotional shift analysis
            current_emotional_state: Current detected emotional state
            previous_emotional_state: Previous recorded emotional state
            detected_shift: Whether a significant shift was detected
            shift_magnitude: Magnitude of the emotional shift (0.0-1.0)
            shift_direction: Direction of shift (positive/negative/neutral)
            recommended_approach: Handling approach for detected shift
            confidence: Confidence in the shift detection
    """
    try:
        # Extract current mood from context
        current_mood = context_packet.get("reported_mood", "")
        
        # If no current mood provided, analyze from message
        if not current_mood:
            current_mood = await _extract_mood_from_text(current_message)
        
        # Get historical mood data
        mood_history = await _get_mood_history(user_profile_id, lookback_period)
        previous_mood = mood_history.get("most_recent_mood", "neutral")
        
        # Categorize moods
        positive_moods = ["happy", "excited", "energetic", "enthusiastic", "joyful", "pleased", 
                          "content", "satisfied", "motivated", "focused"]
        negative_moods = ["sad", "angry", "frustrated", "anxious", "worried", "stressed", 
                         "depressed", "upset", "annoyed", "disappointed"]
        neutral_moods = ["neutral", "calm", "okay", "fine", "balanced", "normal"]
        
        # Determine emotional categories
        current_category = _categorize_mood(current_mood, positive_moods, negative_moods, neutral_moods)
        previous_category = _categorize_mood(previous_mood, positive_moods, negative_moods, neutral_moods)
        
        # Detect if a shift occurred
        detected_shift = current_category != previous_category
        
        # Calculate shift magnitude (0.0-1.0)
        # Full shift between positive and negative = 1.0
        # Shift from neutral to positive/negative = 0.5
        # No shift = 0.0
        shift_magnitude = 0.0
        
        if detected_shift:
            if (current_category == "positive" and previous_category == "negative") or \
               (current_category == "negative" and previous_category == "positive"):
                shift_magnitude = 1.0
            else:
                shift_magnitude = 0.5
                
        # Determine shift direction
        shift_direction = "neutral"
        if detected_shift:
            if current_category == "positive":
                shift_direction = "positive"
            elif current_category == "negative":
                shift_direction = "negative"
        
        # Generate handling recommendations
        recommended_approach = _generate_emotional_shift_recommendations(
            current_category,
            previous_category,
            shift_magnitude,
            mood_history
        )
        
        # Calculate confidence based on context information
        # Higher confidence if mood was explicitly provided vs. extracted
        confidence = 0.9 if "reported_mood" in context_packet else 0.7
        
        return {
            "emotional_analysis": {
                "current_emotional_state": {
                    "mood": current_mood,
                    "category": current_category
                },
                "previous_emotional_state": {
                    "mood": previous_mood,
                    "category": previous_category
                },
                "detected_shift": detected_shift,
                "shift_magnitude": shift_magnitude,
                "shift_direction": shift_direction,
                "recommended_approach": recommended_approach,
                "confidence": confidence
            }
        }
        
    except Exception as e:
        logger.error(f"Error in detect_emotional_state_shifts: {str(e)}", exc_info=True)
        return {
            "emotional_analysis": {
                "current_emotional_state": {"mood": "unknown", "category": "neutral"},
                "previous_emotional_state": {"mood": "unknown", "category": "neutral"},
                "detected_shift": False,
                "shift_magnitude": 0.0,
                "shift_direction": "neutral",
                "recommended_approach": {
                    "priority": "normal",
                    "suggestions": ["Proceed with standard interaction approach"]
                },
                "confidence": 0.3,
                "error": str(e)
            }
        }

@register_tool('check_workflow_history')
async def check_workflow_history(
    user_profile_id: str,
    workflow_type: str = None,
    timeframe: int = 30,
    include_failures: bool = True
) -> Dict[str, Any]:
    """
    Examines recent workflow patterns for a user.
    
    This tool analyzes the user's recent workflow history to identify patterns,
    preferences, and potential issues in workflow execution.
    
    Input:
        user_profile_id: UUID of the user profile
        workflow_type: Optional specific workflow type to analyze
        timeframe: Days to look back for workflow history
        include_failures: Whether to include failed workflows in analysis
        
    Output:
        workflow_analysis: Dictionary with workflow history analysis
            recent_workflows: Count of recent workflows
            completion_rate: Rate of workflow completions
            preferred_workflows: Most frequently initiated workflows
            common_failure_points: Common points of workflow failure
            workflow_efficiency: Efficiency metrics for workflows
            patterns: Identified patterns in workflow usage
            recommendations: Workflow handling recommendations
    """
    try:
        # Get workflow history data
        workflow_data = await _get_comprehensive_workflow_history(
            user_profile_id, 
            workflow_type, 
            timeframe,
            include_failures
        )
        
        # Calculate completion rate
        total_workflows = workflow_data.get("total_count", 0)
        completed_workflows = workflow_data.get("completed_count", 0)
        
        completion_rate = completed_workflows / total_workflows if total_workflows > 0 else 0
        
        # Extract preferred workflows
        workflow_counts = workflow_data.get("workflow_counts", {})
        preferred_workflows = [
            {
                "type": wf_type,
                "count": count,
                "percentage": (count / total_workflows * 100) if total_workflows > 0 else 0
            }
            for wf_type, count in sorted(workflow_counts.items(), key=lambda x: x[1], reverse=True)
        ]
        
        # Analyze failure patterns
        common_failure_points = []
        if include_failures:
            failure_data = workflow_data.get("failure_data", {})
            for agent, failures in failure_data.items():
                if failures > 0:
                    common_failure_points.append({
                        "agent": agent,
                        "failures": failures,
                        "percentage": (failures / (total_workflows - completed_workflows) * 100) 
                                     if (total_workflows - completed_workflows) > 0 else 0
                    })
            
            # Sort by failure count
            common_failure_points.sort(key=lambda x: x["failures"], reverse=True)
        
        # Calculate workflow efficiency
        avg_duration = workflow_data.get("avg_duration", 0)
        min_duration = workflow_data.get("min_duration", 0)
        max_duration = workflow_data.get("max_duration", 0)
        
        # Identify patterns
        patterns = _analyze_workflow_patterns(workflow_data)
        
        # Generate recommendations
        recommendations = _generate_workflow_recommendations(
            completion_rate,
            preferred_workflows,
            common_failure_points,
            patterns
        )
        
        return {
            "workflow_analysis": {
                "recent_workflows": total_workflows,
                "completion_rate": completion_rate,
                "preferred_workflows": preferred_workflows[:3],  # Top 3
                "common_failure_points": common_failure_points[:3],  # Top 3
                "workflow_efficiency": {
                    "avg_duration_seconds": avg_duration,
                    "min_duration_seconds": min_duration,
                    "max_duration_seconds": max_duration,
                    "efficiency_rating": _calculate_efficiency_rating(avg_duration, completion_rate)
                },
                "patterns": patterns,
                "recommendations": recommendations
            }
        }
        
    except Exception as e:
        logger.error(f"Error in check_workflow_history: {str(e)}", exc_info=True)
        return {
            "workflow_analysis": {
                "recent_workflows": 0,
                "completion_rate": 0,
                "preferred_workflows": [],
                "common_failure_points": [],
                "workflow_efficiency": {
                    "avg_duration_seconds": 0,
                    "min_duration_seconds": 0,
                    "max_duration_seconds": 0,
                    "efficiency_rating": "unknown"
                },
                "patterns": [],
                "recommendations": [],
                "error": str(e)
            }
        }

# Helper functions for activity completion data
async def _get_activity_completion_data(user_profile_id: str, timeframe_days: int) -> Dict[str, Any]:
    """Get activity completion data for the user"""
    try:
        from apps.main.models import HistoryEvent
        from apps.activity.models import ActivityTailored
        from django.db.models import Avg, Count, Q
        from django.utils import timezone
        
        # Calculate cutoff date
        cutoff_date = timezone.now() - datetime.timedelta(days=timeframe_days)
        
        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async
        
        @sync_to_async
        def get_activity_data():
            # Get completed activities
            completed_events = HistoryEvent.objects.filter(
                user_profile_id=user_profile_id,
                event_type='activity_completed',
                timestamp__gte=cutoff_date
            )
            
            completed_count = completed_events.count()
            
            # Get refused activities
            refused_events = HistoryEvent.objects.filter(
                user_profile_id=user_profile_id,
                event_type='activity_reaction',
                details__contains={"accepted": False},
                timestamp__gte=cutoff_date
            )
            
            refused_count = refused_events.count()
            
            # Calculate completion rate
            total = completed_count + refused_count
            completion_rate = completed_count / total if total > 0 else 0
            
            # Get average satisfaction
            avg_satisfaction = 0
            if completed_count > 0:
                satisfaction_sum = 0
                satisfaction_count = 0
                
                for event in completed_events:
                    if 'satisfaction' in event.details:
                        satisfaction_sum += event.details['satisfaction']
                        satisfaction_count += 1
                
                if satisfaction_count > 0:
                    avg_satisfaction = satisfaction_sum / satisfaction_count
            
            # Get domain distribution
            domain_counts = {}
            for event in completed_events:
                # Try to get the activity to find its domain
                try:
                    # This is a simplified approach; in a real system we would use more efficient queries
                    activity_id = event.object_id
                    activity = ActivityTailored.objects.get(id=activity_id)
                    domain = activity.get_primary_domain()
                    
                    if domain:
                        domain_name = domain.primary_category
                        if domain_name not in domain_counts:
                            domain_counts[domain_name] = 0
                        domain_counts[domain_name] += 1
                except:
                    pass
            
            return {
                "completed_count": completed_count,
                "refused_count": refused_count,
                "completion_rate": completion_rate,
                "average_satisfaction": avg_satisfaction,
                "domain_distribution": domain_counts
            }
        
        return await get_activity_data()
        
    except Exception as e:
        logger.error(f"Error getting activity completion data: {str(e)}")
        return {
            "completed_count": 0,
            "refused_count": 0,
            "completion_rate": 0,
            "average_satisfaction": 0,
            "domain_distribution": {}
        }

# Helper functions for interaction data
async def _get_interaction_data(user_profile_id: str, timeframe_days: int) -> Dict[str, Any]:
    """Get user interaction data"""
    try:
        from apps.main.models import HistoryEvent
        from django.db.models import Count, Q
        from django.utils import timezone
        
        # Calculate cutoff date
        cutoff_date = timezone.now() - datetime.timedelta(days=timeframe_days)
        
        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async
        
        @sync_to_async
        def get_interaction_data():
            # Get all user interactions
            interactions = HistoryEvent.objects.filter(
                user_profile_id=user_profile_id,
                timestamp__gte=cutoff_date
            )
            
            total_interactions = interactions.count()
            
            # Count interactions by type
            interaction_types = interactions.values('event_type').annotate(
                count=Count('event_type')
            ).order_by('-count')
            
            # Convert to dictionary
            type_counts = {item['event_type']: item['count'] for item in interaction_types}
            
            # Get time of day distribution
            time_of_day = {}
            for interaction in interactions:
                hour = interaction.timestamp.hour
                period = "morning" if 5 <= hour < 12 else \
                         "afternoon" if 12 <= hour < 17 else \
                         "evening" if 17 <= hour < 22 else \
                         "night"
                
                if period not in time_of_day:
                    time_of_day[period] = 0
                time_of_day[period] += 1
            
            # Calculate engagement score (0.0-1.0)
            # A simple approach is interactions per day relative to expected
            days_in_period = min(timeframe_days, (timezone.now() - cutoff_date).days + 1)
            interactions_per_day = total_interactions / days_in_period if days_in_period > 0 else 0
            
            # Normalize: 3+ interactions per day = 1.0, none = 0.0
            engagement_score = min(1.0, interactions_per_day / 3)
            
            return {
                "total_interactions": total_interactions,
                "interaction_types": type_counts,
                "time_of_day_distribution": time_of_day,
                "interactions_per_day": interactions_per_day,
                "engagement_score": engagement_score
            }
        
        return await get_interaction_data()
        
    except Exception as e:
        logger.error(f"Error getting interaction data: {str(e)}")
        return {
            "total_interactions": 0,
            "interaction_types": {},
            "time_of_day_distribution": {},
            "interactions_per_day": 0,
            "engagement_score": 0
        }

async def _get_mood_data(user_profile_id: str, timeframe_days: int) -> Dict[str, Any]:
    """Get mood data for the user"""
    try:
        from apps.main.models import HistoryEvent
        from apps.user.models import CurrentMood
        from django.db.models import Q
        from django.utils import timezone
        
        # Calculate cutoff date
        cutoff_date = timezone.now() - datetime.timedelta(days=timeframe_days)
        
        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async
        
        @sync_to_async
        def get_mood_data():
            # Get current mood
            try:
                current_mood = CurrentMood.objects.get(user_profile_id=user_profile_id)
                current = {
                    "description": current_mood.description,
                    "intensity": current_mood.height
                }
            except:
                current = None
            
            # Get mood history events
            mood_events = HistoryEvent.objects.filter(
                Q(user_profile_id=user_profile_id) & 
                Q(event_type__in=['mood_explicitly_updated', 'mood_inferred']) &
                Q(timestamp__gte=cutoff_date)
            ).order_by('timestamp')
            
            # Extract mood descriptions and intensities
            mood_history = []
            mood_descriptions = []
            
            for event in mood_events:
                if 'description' in event.details:
                    mood_descriptions.append(event.details['description'])
                    
                    mood_history.append({
                        "timestamp": event.timestamp.isoformat(),
                        "description": event.details.get('description', ''),
                        "intensity": event.details.get('height', 50),
                        "inferred": event.event_type == 'mood_inferred'
                    })
            
            # Identify mood trends
            mood_counts = Counter(mood_descriptions)
            common_moods = [
                {"mood": mood, "count": count}
                for mood, count in mood_counts.most_common(3)
            ]
            
            # Determine mood volatility
            mood_changes = 0
            for i in range(1, len(mood_history)):
                if mood_history[i]['description'] != mood_history[i-1]['description']:
                    mood_changes += 1
            
            volatility = mood_changes / len(mood_history) if len(mood_history) > 1 else 0
            
            # Determine overall trend direction
            trend_direction = "stable"
            if len(mood_history) >= 2:
                # Simplistic approach - compare first and last mood
                positive_moods = ["happy", "excited", "energetic", "satisfied", "motivated"]
                negative_moods = ["sad", "angry", "frustrated", "anxious", "stressed"]
                
                first_mood = mood_history[0]['description'].lower()
                last_mood = mood_history[-1]['description'].lower()
                
                first_positive = any(pos in first_mood for pos in positive_moods)
                first_negative = any(neg in first_mood for neg in negative_moods)
                
                last_positive = any(pos in last_mood for pos in positive_moods)
                last_negative = any(neg in last_mood for neg in negative_moods)
                
                if first_negative and last_positive:
                    trend_direction = "improving"
                elif first_positive and last_negative:
                    trend_direction = "declining"
            
            return {
                "current": current,
                "history": mood_history,
                "common_moods": common_moods,
                "volatility": volatility,
                "trends": {
                    "direction": trend_direction,
                    "common_moods": common_moods
                }
            }
        
        return await get_mood_data()
        
    except Exception as e:
        logger.error(f"Error getting mood data: {str(e)}")
        return {
            "current": None,
            "history": [],
            "common_moods": [],
            "volatility": 0,
            "trends": {
                "direction": "unknown",
                "common_moods": []
            }
        }

def _analyze_interaction_patterns(interaction_data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze interaction data for patterns"""
    patterns = {}
    
    # Determine preferred interaction time
    time_distribution = interaction_data.get("time_of_day_distribution", {})
    if time_distribution:
        max_time = max(time_distribution.items(), key=lambda x: x[1])[0]
        patterns["preferred_time"] = max_time
    else:
        patterns["preferred_time"] = "unknown"
    
    # Determine interaction frequency
    interactions_per_day = interaction_data.get("interactions_per_day", 0)
    if interactions_per_day < 0.5:
        patterns["frequency"] = "infrequent"
    elif interactions_per_day < 1.5:
        patterns["frequency"] = "moderate"
    else:
        patterns["frequency"] = "frequent"
    
    # Determine most common interaction type
    interaction_types = interaction_data.get("interaction_types", {})
    if interaction_types:
        most_common = max(interaction_types.items(), key=lambda x: x[1])[0]
        patterns["primary_interaction"] = most_common
    else:
        patterns["primary_interaction"] = "unknown"
    
    return patterns

async def _analyze_engagement_trends(user_profile_id: str, timeframe_days: int) -> Dict[str, Any]:
    """Analyze engagement trends over time"""
    try:
        from apps.main.models import HistoryEvent
        from django.db.models import Count, Q
        from django.utils import timezone
        
        # Calculate cutoff date
        cutoff_date = timezone.now() - datetime.timedelta(days=timeframe_days)
        
        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async
        
        @sync_to_async
        def get_trend_data():
            # Get weekly data points
            weekly_data = []
            for week_offset in range(0, timeframe_days // 7 + 1):
                week_start = cutoff_date + datetime.timedelta(days=week_offset * 7)
                week_end = min(week_start + datetime.timedelta(days=7), timezone.now())
                
                # Skip partial weeks less than 2 days
                if (week_end - week_start).days < 2:
                    continue
                
                # Get interactions for this week
                interactions = HistoryEvent.objects.filter(
                    user_profile_id=user_profile_id,
                    timestamp__gte=week_start,
                    timestamp__lt=week_end
                ).count()
                
                # Get completed activities for this week
                completed = HistoryEvent.objects.filter(
                    user_profile_id=user_profile_id,
                    event_type='activity_completed',
                    timestamp__gte=week_start,
                    timestamp__lt=week_end
                ).count()
                
                # Get refused activities for this week
                refused = HistoryEvent.objects.filter(
                    user_profile_id=user_profile_id,
                    event_type='activity_reaction',
                    details__contains={"accepted": False},
                    timestamp__gte=week_start,
                    timestamp__lt=week_end
                ).count()
                
                # Calculate engagement metrics
                total_activities = completed + refused
                completion_rate = completed / total_activities if total_activities > 0 else 0
                
                weekly_data.append({
                    "week_start": week_start.isoformat(),
                    "week_end": week_end.isoformat(),
                    "interactions": interactions,
                    "completed_activities": completed,
                    "refused_activities": refused,
                    "completion_rate": completion_rate
                })
            
            # Determine overall trends
            if len(weekly_data) >= 2:
                first_week = weekly_data[0]
                last_week = weekly_data[-1]
                
                # Calculate change percentages
                interaction_change = ((last_week["interactions"] - first_week["interactions"]) / 
                                     first_week["interactions"]) if first_week["interactions"] > 0 else 0
                
                completion_change = ((last_week["completion_rate"] - first_week["completion_rate"]) /
                                    first_week["completion_rate"]) if first_week["completion_rate"] > 0 else 0
                
                trend_direction = "stable"
                if interaction_change > 0.2 and completion_change > 0.1:
                    trend_direction = "improving"
                elif interaction_change < -0.2 and completion_change < -0.1:
                    trend_direction = "declining"
                
                return {
                    "weekly_data": weekly_data,
                    "trend_direction": trend_direction,
                    "interaction_change_pct": interaction_change * 100,
                    "completion_rate_change_pct": completion_change * 100
                }
                
            return {
                "weekly_data": weekly_data,
                "trend_direction": "insufficient_data",
                "interaction_change_pct": 0,
                "completion_rate_change_pct": 0
            }
        
        return await get_trend_data()
        
    except Exception as e:
        logger.error(f"Error analyzing engagement trends: {str(e)}")
        return {
            "weekly_data": [],
            "trend_direction": "unknown",
            "interaction_change_pct": 0,
            "completion_rate_change_pct": 0
        }
    
def _generate_engagement_suggestions(
    engagement_level: float,
    activity_data: Dict[str, Any],
    interaction_data: Dict[str, Any],
    mood_data: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """Generate suggestions for improving user engagement"""
    suggestions = []
    
    # Low overall engagement
    if engagement_level < 0.3:
        suggestions.append({
            "type": "engagement",
            "priority": "high",
            "suggestion": "User has very low engagement. Consider proactive outreach and simplified activities."
        })
    elif engagement_level < 0.6:
        suggestions.append({
            "type": "engagement",
            "priority": "medium",
            "suggestion": "User has moderate engagement. Provide more personalized activity recommendations."
        })
    
    # Low activity completion rate
    if activity_data.get("completion_rate", 1.0) < 0.5:
        suggestions.append({
            "type": "activity",
            "priority": "high",
            "suggestion": "Low activity completion rate. Consider offering less challenging activities."
        })
    
    # Infrequent interactions
    if interaction_data.get("interactions_per_day", 0) < 0.5:
        suggestions.append({
            "type": "interaction",
            "priority": "medium",
            "suggestion": "Infrequent user interactions. Consider gentle re-engagement messaging."
        })
    
    # Mood-based suggestions
    mood_trends = mood_data.get("trends", {})
    if mood_trends.get("direction") == "declining":
        suggestions.append({
            "type": "mood",
            "priority": "high",
            "suggestion": "User mood appears to be declining. Use more supportive and encouraging language."
        })
    
    # Default suggestion if none generated
    if not suggestions:
        suggestions.append({
            "type": "general",
            "priority": "low",
            "suggestion": "User is engaging well. Continue with current approach."
        })
    
    return suggestions

async def _extract_mood_from_text(message: str) -> str:
    """Extract mood from message text using LLM"""
    try:
        from apps.main.llm.service import RealLLMClient
        
        # Initialize LLM client
        llm_client = RealLLMClient()
        
        # System prompt for mood extraction
        system_prompt = """You are a mood detection specialist. Analyze the user's message to determine their current mood or emotional state.
Respond with just a single word or short phrase describing the mood (e.g., "happy", "frustrated", "anxious", "excited").
If you can't determine a clear mood, respond with "neutral".
"""
        
        # Format messages for LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": message}
        ]
        
        # Call LLM
        llm_response = await llm_client.chat_completion(
            messages=messages,
            temperature=0.1,  # Low temperature for more consistent responses
            max_tokens=50  # Short response needed
        )
        
        # Extract mood from response
        if llm_response.is_text and llm_response.content:
            # Clean up response to just get the mood
            mood = llm_response.content.strip().lower()
            
            # Remove any punctuation
            import re
            mood = re.sub(r'[^\w\s]', '', mood)
            
            # If multiple words, take just the first (likely the mood adjective)
            if ' ' in mood:
                mood = mood.split()[0]
            
            return mood
        
        return "neutral"  # Default fallback
        
    except Exception as e:
        logger.error(f"Error extracting mood from text: {str(e)}")
        return "neutral"  # Default fallback

async def _get_mood_history(user_profile_id: str, lookback_period: int) -> Dict[str, Any]:
    """Get historical mood data for the user"""
    try:
        from apps.main.models import HistoryEvent
        from django.db.models import Q
        from django.utils import timezone
        
        # Calculate cutoff date
        cutoff_date = timezone.now() - datetime.timedelta(days=lookback_period)
        
        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async
        
        @sync_to_async
        def get_mood_history():
            # Get mood events
            mood_events = HistoryEvent.objects.filter(
                Q(user_profile_id=user_profile_id) & 
                Q(event_type__in=['mood_explicitly_updated', 'mood_inferred']) &
                Q(timestamp__gte=cutoff_date)
            ).order_by('-timestamp')
            
            # Get most recent mood
            most_recent_mood = "neutral"
            if mood_events.exists():
                most_recent = mood_events.first()
                if 'description' in most_recent.details:
                    most_recent_mood = most_recent.details['description']
            
            # Get all mood descriptions
            mood_descriptions = []
            for event in mood_events:
                if 'description' in event.details:
                    mood_descriptions.append(event.details['description'])
            
            # Count frequency of each mood
            from collections import Counter
            mood_counts = Counter(mood_descriptions)
            
            # Get common moods
            common_moods = [
                {"mood": mood, "count": count}
                for mood, count in mood_counts.most_common(3)
            ]
            
            return {
                "most_recent_mood": most_recent_mood,
                "mood_counts": dict(mood_counts),
                "common_moods": common_moods,
                "total_records": len(mood_descriptions)
            }
        
        return await get_mood_history()
        
    except Exception as e:
        logger.error(f"Error getting mood history: {str(e)}")
        return {
            "most_recent_mood": "neutral",
            "mood_counts": {},
            "common_moods": [],
            "total_records": 0
        }

def _categorize_mood(mood: str, positive_moods: List[str], negative_moods: List[str], neutral_moods: List[str]) -> str:
    """Categorize a mood as positive, negative, or neutral"""
    mood_lower = mood.lower()
    
    # Check each category
    for pos_mood in positive_moods:
        if pos_mood in mood_lower:
            return "positive"
    
    for neg_mood in negative_moods:
        if neg_mood in mood_lower:
            return "negative"
    
    for neut_mood in neutral_moods:
        if neut_mood in mood_lower:
            return "neutral"
    
    # Default to neutral if no match
    return "neutral"

def _generate_emotional_shift_recommendations(
    current_category: str,
    previous_category: str,
    shift_magnitude: float,
    mood_history: Dict[str, Any]
) -> Dict[str, Any]:
    """Generate recommendations for handling emotional shifts"""
    # For significant shifts between positive and negative
    if shift_magnitude > 0.9:
        if current_category == "negative" and previous_category == "positive":
            return {
                "priority": "high",
                "approach": "supportive",
                "suggestions": [
                    "Use empathetic and validating language",
                    "Offer simpler activities with high success probability",
                    "Acknowledge the emotional shift in a supportive way"
                ]
            }
        elif current_category == "positive" and previous_category == "negative":
            return {
                "priority": "medium",
                "approach": "encouraging",
                "suggestions": [
                    "Capitalize on positive mood with slightly more challenging activities",
                    "Use enthusiastic language that matches their energy",
                    "Acknowledge the positive shift in an affirming way"
                ]
            }
    
    # For moderate shifts
    elif shift_magnitude > 0.4:
        if current_category == "negative":
            return {
                "priority": "medium",
                "approach": "gentle",
                "suggestions": [
                    "Use slightly more supportive language",
                    "Offer activities that match their current energy level",
                    "Be mindful of the shift without explicitly mentioning it"
                ]
            }
        elif current_category == "positive":
            return {
                "priority": "low",
                "approach": "responsive",
                "suggestions": [
                    "Match the user's positive tone",
                    "Consider activities that capitalize on their current mood",
                    "Maintain the positive momentum"
                ]
            }
    
    # For more stable emotional states
    else:
        if current_category == "negative":
            return {
                "priority": "medium",
                "approach": "supportive",
                "suggestions": [
                    "Use supportive language consistently",
                    "Favor activities with high success probability",
                    "Focus on small wins and gradual progress"
                ]
            }
        elif current_category == "positive":
            return {
                "priority": "low",
                "approach": "reinforcing",
                "suggestions": [
                    "Reinforce positive patterns",
                    "Continue with balanced challenge levels",
                    "Use positive but not overly enthusiastic language"
                ]
            }
        else:  # neutral
            return {
                "priority": "normal",
                "approach": "balanced",
                "suggestions": [
                    "Maintain standard interaction approach",
                    "Use neutral to slightly positive language",
                    "Offer balanced activities"
                ]
            }

async def _get_comprehensive_workflow_history(
    user_profile_id: str,
    workflow_type: Optional[str],
    timeframe: int,
    include_failures: bool
) -> Dict[str, Any]:
    """Get comprehensive workflow history data"""
    try:
        from apps.main.models import HistoryEvent, AgentRun
        from django.db.models import Avg, Min, Max, Count, Q, F
        from django.utils import timezone
        
        # Calculate cutoff date
        cutoff_date = timezone.now() - datetime.timedelta(days=timeframe)
        
        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async
        
        @sync_to_async
        def get_workflow_data():
            # Create base queryset
            workflow_runs = AgentRun.objects.filter(
                user_profile_id=user_profile_id,
                started_at__gte=cutoff_date
            )
            
            # Filter by workflow type if specified
            if workflow_type:
                workflow_runs = workflow_runs.filter(
                    initial_state__contains={"task_type": workflow_type}
                )
            
            # Count all workflows
            total_count = workflow_runs.count()
            
            # Count completed workflows
            completed_count = workflow_runs.filter(status="completed").count()
            
            # Count workflows by type
            workflow_counts = {}
            for run in workflow_runs:
                if 'task_type' in run.initial_state:
                    wf_type = run.initial_state['task_type']
                    if wf_type not in workflow_counts:
                        workflow_counts[wf_type] = 0
                    workflow_counts[wf_type] += 1
            
            # Analyze failures if requested
            failure_data = {}
            if include_failures:
                failed_runs = workflow_runs.filter(status__in=["failed", "timeout"])
                
                # Count failures by agent
                for run in failed_runs:
                    agent_role = run.agent.role if run.agent else "unknown"
                    if agent_role not in failure_data:
                        failure_data[agent_role] = 0
                    failure_data[agent_role] += 1
            
            # Calculate duration statistics for completed runs
            duration_stats = workflow_runs.filter(
                status="completed",
                completed_at__isnull=False
            ).annotate(
                duration=F('completed_at') - F('started_at')
            ).aggregate(
                avg=Avg('duration'),
                min=Min('duration'),
                max=Max('duration')
            )
            
            # Convert durations to seconds
            avg_duration = duration_stats['avg'].total_seconds() if duration_stats['avg'] else 0
            min_duration = duration_stats['min'].total_seconds() if duration_stats['min'] else 0
            max_duration = duration_stats['max'].total_seconds() if duration_stats['max'] else 0
            
            return {
                "total_count": total_count,
                "completed_count": completed_count,
                "workflow_counts": workflow_counts,
                "failure_data": failure_data,
                "avg_duration": avg_duration,
                "min_duration": min_duration,
                "max_duration": max_duration
            }
        
        return await get_workflow_data()
        
    except Exception as e:
        logger.error(f"Error getting comprehensive workflow history: {str(e)}")
        return {
            "total_count": 0,
            "completed_count": 0,
            "workflow_counts": {},
            "failure_data": {},
            "avg_duration": 0,
            "min_duration": 0,
            "max_duration": 0
        }

def _analyze_workflow_patterns(workflow_data: Dict[str, Any]) -> List[Dict[str, str]]:
    """Analyze workflow data to identify patterns"""
    patterns = []
    
    # Check completion rate
    total = workflow_data.get("total_count", 0)
    completed = workflow_data.get("completed_count", 0)
    completion_rate = completed / total if total > 0 else 0
    
    if completion_rate < 0.5:
        patterns.append({
            "pattern": "low_completion_rate",
            "description": "User has a low workflow completion rate",
            "significance": "high"
        })
    elif completion_rate > 0.9:
        patterns.append({
            "pattern": "high_completion_rate",
            "description": "User has a very high workflow completion rate",
            "significance": "medium"
        })
    
    # Check workflow preferences
    workflow_counts = workflow_data.get("workflow_counts", {})
    if workflow_counts:
        # Find most preferred workflow
        preferred = max(workflow_counts.items(), key=lambda x: x[1])
        patterns.append({
            "pattern": "preferred_workflow",
            "description": f"User primarily engages with {preferred[0]} workflows",
            "significance": "medium"
        })
        
        # Check for workflow diversity
        if len(workflow_counts) == 1:
            patterns.append({
                "pattern": "limited_workflow_diversity",
                "description": "User only engages with one workflow type",
                "significance": "medium"
            })
    
    # Check for failure patterns
    failure_data = workflow_data.get("failure_data", {})
    if failure_data:
        # Find most common failure point
        if workflow_data.get("total_count", 0) > 0:
            failure_rate = sum(failure_data.values()) / workflow_data["total_count"]
            if failure_rate > 0.2:
                patterns.append({
                    "pattern": "high_failure_rate",
                    "description": "User experiences frequent workflow failures",
                    "significance": "high"
                })
        
        # Identify specific agent failures
        for agent, count in failure_data.items():
            if count > 1:
                patterns.append({
                    "pattern": f"agent_failure_{agent}",
                    "description": f"Repeated failures with {agent} agent",
                    "significance": "high"
                })
    
    return patterns

def _generate_workflow_recommendations(
    completion_rate: float,
    preferred_workflows: List[Dict[str, Any]],
    common_failure_points: List[Dict[str, Any]],
    patterns: List[Dict[str, str]]
) -> List[Dict[str, Any]]:
    """Generate recommendations based on workflow analysis"""
    recommendations = []
    
    # Handle low completion rate
    if completion_rate < 0.6:
        recommendations.append({
            "type": "workflow_adjustment",
            "priority": "high",
            "suggestion": "Use simpler workflow paths with fewer agent steps"
        })
    
    # Handle failure points
    for failure in common_failure_points[:2]:  # Top 2 failure points
        agent = failure.get("agent", "unknown")
        recommendations.append({
            "type": "failure_mitigation",
            "priority": "high",
            "suggestion": f"Implement additional validation before {agent} agent"
        })
    
    # Leverage preferred workflows
    if preferred_workflows:
        top_workflow = preferred_workflows[0]["type"]
        recommendations.append({
            "type": "preference_optimization",
            "priority": "medium",
            "suggestion": f"Optimize handling of {top_workflow} workflows which the user engages with most"
        })
    
    # Address specific patterns
    for pattern in patterns:
        if pattern["pattern"] == "limited_workflow_diversity":
            recommendations.append({
                "type": "diversity",
                "priority": "low",
                "suggestion": "Encourage user to explore other system capabilities"
            })
        elif pattern["pattern"] == "high_failure_rate":
            recommendations.append({
                "type": "reliability",
                "priority": "high",
                "suggestion": "Implement enhanced error recovery and simplified fallback flows"
            })
    
    # Default recommendation if none generated
    if not recommendations:
        recommendations.append({
            "type": "general",
            "priority": "low",
            "suggestion": "Current workflow handling appears effective. Continue with standard approach."
        })
    
    return recommendations

def _calculate_efficiency_rating(avg_duration: float, completion_rate: float) -> str:
    """Calculate an efficiency rating based on duration and completion rate"""
    # No data case
    if avg_duration is None:
        return "unknown"
        
    # Calculate a composite score from duration and completion rate
    # Lower duration and higher completion rate = better efficiency
    score = 0
    
    # Duration component (shorter is better)
    if avg_duration < 5:
        score += 4
    elif avg_duration < 10:
        score += 3
    elif avg_duration < 15:
        score += 2
    elif avg_duration < 30:
        score += 1
    
    # Completion rate component
    if completion_rate > 0.9:
        score += 4
    elif completion_rate > 0.7:
        score += 3
    elif completion_rate > 0.5:
        score += 2
    elif completion_rate > 0.3:
        score += 1
    
    # Convert score to rating
    if score >= 7:
        return "excellent"
    elif score >= 5:
        return "good"
    elif score >= 3:
        return "average"
    elif score >= 1:
        return "below_average"
    else:
        return "poor"

@register_tool('analyze_mood_progression')
async def analyze_mood_progression(
    user_profile_id: str,
    timeframe_days: int = 30
) -> Dict[str, Any]:
    """
    Analyzes mood progression and patterns over time.
    
    This tool examines the user's mood history to identify trends,
    recurring patterns, and potential correlations with activities.
    
    Input:
        user_profile_id: UUID of the user profile
        timeframe_days: Number of days to look back
        
    Output:
        mood_analysis: Dictionary with mood progression analysis
            overall_trend: The general trend direction of mood
            volatility: Measurement of mood stability/changes
            common_moods: Most frequently reported moods
            mood_activity_correlations: Activities correlated with mood changes
            recommendations: Suggested approaches based on mood patterns
    """
    try:
        # Get comprehensive mood history
        mood_history = await _get_comprehensive_mood_history(user_profile_id, timeframe_days)
        
        # Calculate overall trend
        trend_direction = mood_history.get("trend_direction", "stable")
        
        # Calculate volatility
        volatility = mood_history.get("volatility", 0.5)
        
        # Get common moods
        common_moods = mood_history.get("common_moods", [])
        
        # Get mood-activity correlations
        correlations = await _analyze_mood_activity_correlations(user_profile_id, timeframe_days)
        
        # Generate recommendations
        recommendations = _generate_mood_recommendations(mood_history, correlations)
        
        return {
            "mood_analysis": {
                "overall_trend": trend_direction,
                "volatility": volatility,
                "common_moods": common_moods,
                "mood_activity_correlations": correlations,
                "recommendations": recommendations
            }
        }
        
    except Exception as e:
        logger.error(f"Error in analyze_mood_progression: {str(e)}", exc_info=True)
        return {
            "mood_analysis": {
                "overall_trend": "unknown",
                "volatility": 0.5,
                "common_moods": [],
                "mood_activity_correlations": [],
                "recommendations": [
                    "Continue with standard mood-aware interactions"
                ],
                "error": str(e)
            }
        }

async def _get_comprehensive_mood_history(user_profile_id: str, timeframe_days: int) -> Dict[str, Any]:
    """Get detailed mood history with trend analysis"""
    try:
        from apps.main.models import HistoryEvent
        from django.db.models import Q
        from django.utils import timezone
        import datetime
        
        # Calculate cutoff date
        cutoff_date = timezone.now() - datetime.timedelta(days=timeframe_days)
        
        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async
        
        @sync_to_async
        def get_mood_history():
            # Get mood events
            mood_events = HistoryEvent.objects.filter(
                Q(user_profile_id=user_profile_id) & 
                Q(event_type__in=['mood_explicitly_updated', 'mood_inferred']) &
                Q(timestamp__gte=cutoff_date)
            ).order_by('timestamp')
            
            # Extract mood descriptions and timestamps
            mood_points = []
            mood_descriptions = []
            
            for event in mood_events:
                if 'description' in event.details:
                    mood_descriptions.append(event.details['description'])
                    
                    mood_points.append({
                        "timestamp": event.timestamp.isoformat(),
                        "description": event.details.get('description', ''),
                        "intensity": event.details.get('height', 50),
                        "category": _categorize_mood_simple(event.details.get('description', '')),
                        "inferred": event.event_type == 'mood_inferred'
                    })
            
            # Calculate mood counts
            from collections import Counter
            mood_counts = Counter(mood_descriptions)
            
            # Get common moods
            common_moods = [
                {"mood": mood, "count": count}
                for mood, count in mood_counts.most_common(3)
            ]
            
            # Calculate mood volatility
            mood_changes = 0
            for i in range(1, len(mood_points)):
                if mood_points[i]['category'] != mood_points[i-1]['category']:
                    mood_changes += 1
            
            volatility = mood_changes / len(mood_points) if len(mood_points) > 1 else 0
            
            # Calculate trend direction
            trend_direction = "stable"
            if len(mood_points) >= 3:
                # Use a more sophisticated approach for trend analysis
                # Count direction changes between consecutive points
                positive_shifts = 0
                negative_shifts = 0
                
                for i in range(1, len(mood_points)):
                    prev_category = mood_points[i-1]['category']
                    curr_category = mood_points[i]['category']
                    
                    if prev_category == "negative" and curr_category in ["neutral", "positive"]:
                        positive_shifts += 1
                    elif prev_category == "neutral" and curr_category == "positive":
                        positive_shifts += 1
                    elif prev_category == "positive" and curr_category in ["neutral", "negative"]:
                        negative_shifts += 1
                    elif prev_category == "neutral" and curr_category == "negative":
                        negative_shifts += 1
                
                # Determine overall trend direction
                if positive_shifts > negative_shifts * 1.5:
                    trend_direction = "improving"
                elif negative_shifts > positive_shifts * 1.5:
                    trend_direction = "declining"
                
            return {
                "mood_points": mood_points,
                "common_moods": common_moods,
                "volatility": volatility,
                "trend_direction": trend_direction,
                "total_records": len(mood_points)
            }
        
        return await get_mood_history()
        
    except Exception as e:
        logger.error(f"Error getting comprehensive mood history: {str(e)}")
        return {
            "mood_points": [],
            "common_moods": [],
            "volatility": 0.5,
            "trend_direction": "stable",
            "total_records": 0
        }

async def _analyze_mood_activity_correlations(user_profile_id: str, timeframe_days: int) -> List[Dict[str, Any]]:
    """Analyze correlations between moods and activities"""
    try:
        from apps.main.models import HistoryEvent
        from django.db.models import Q
        from django.utils import timezone
        import datetime
        
        # Calculate cutoff date
        cutoff_date = timezone.now() - datetime.timedelta(days=timeframe_days)
        
        # Use database_sync_to_async to make this asynchronous
        from asgiref.sync import sync_to_async
        
        @sync_to_async
        def analyze_correlations():
            # Get completed activities
            activity_events = HistoryEvent.objects.filter(
                Q(user_profile_id=user_profile_id) & 
                Q(event_type='activity_completed') &
                Q(timestamp__gte=cutoff_date)
            ).order_by('timestamp')
            
            # Get mood events for the same period
            mood_events = HistoryEvent.objects.filter(
                Q(user_profile_id=user_profile_id) & 
                Q(event_type__in=['mood_explicitly_updated', 'mood_inferred']) &
                Q(timestamp__gte=cutoff_date)
            ).order_by('timestamp')
            
            # Map activities to nearest subsequent mood update
            activity_mood_pairs = []
            
            for activity in activity_events:
                # Find the closest mood event after this activity
                for mood in mood_events:
                    if mood.timestamp > activity.timestamp:
                        # Calculate time difference in minutes
                        time_diff = (mood.timestamp - activity.timestamp).total_seconds() / 60
                        
                        # Consider correlations only if mood was reported within 2 hours of activity
                        if time_diff <= 120:
                            activity_mood_pairs.append({
                                'activity_id': activity.object_id,
                                'activity_details': activity.details,
                                'subsequent_mood': mood.details.get('description', ''),
                                'time_diff_minutes': time_diff
                            })
                        
                        # Only use the first mood event after the activity
                        break
            
            # Analyze correlations
            domain_mood_correlations = {}
            
            # Simplified algorithm for correlations by domain
            for pair in activity_mood_pairs:
                # Try to get activity domain
                domain = "unknown"
                if 'domain' in pair['activity_details']:
                    domain = pair['activity_details']['domain']
                
                mood_category = _categorize_mood_simple(pair['subsequent_mood'])
                
                if domain not in domain_mood_correlations:
                    domain_mood_correlations[domain] = {'positive': 0, 'neutral': 0, 'negative': 0}
                
                domain_mood_correlations[domain][mood_category] += 1
            
            # Format correlations as a list
            correlations = []
            for domain, counts in domain_mood_correlations.items():
                total = sum(counts.values())
                if total >= 2:  # Only include if we have at least 2 data points
                    # Calculate the dominant mood for this domain
                    dominant_mood = max(counts.items(), key=lambda x: x[1])[0]
                    
                    correlations.append({
                        'domain': domain,
                        'dominant_mood': dominant_mood,
                        'mood_distribution': {
                            'positive': counts['positive'] / total if total > 0 else 0,
                            'neutral': counts['neutral'] / total if total > 0 else 0,
                            'negative': counts['negative'] / total if total > 0 else 0
                        },
                        'confidence': min(0.5 + (total / 10), 0.9),  # Higher confidence with more data points
                        'sample_size': total
                    })
            
            return correlations
        
        return await analyze_correlations()
        
    except Exception as e:
        logger.error(f"Error analyzing mood-activity correlations: {str(e)}")
        return []

def _categorize_mood_simple(mood: str) -> str:
    """Simplified categorization of mood as positive, negative, or neutral"""
    # Convert to lowercase for case-insensitive matching
    mood_lower = mood.lower()
    
    # Lists of mood terms
    positive_moods = ["happy", "excited", "energetic", "enthusiastic", "joyful", "pleased", 
                      "content", "satisfied", "motivated", "focused", "relaxed", "good",
                      "optimistic", "inspired", "confident", "thankful", "grateful", "hopeful"]
    
    negative_moods = ["sad", "angry", "frustrated", "anxious", "worried", "stressed", 
                     "depressed", "upset", "annoyed", "disappointed", "tired", "exhausted",
                     "overwhelmed", "fearful", "irritated", "confused", "dejected", "unhappy"]
    
    # Check positive moods
    for pos in positive_moods:
        if pos in mood_lower:
            return "positive"
    
    # Check negative moods
    for neg in negative_moods:
        if neg in mood_lower:
            return "negative"
    
    # Default to neutral
    return "neutral"

def _generate_mood_recommendations(mood_history: Dict[str, Any], correlations: List[Dict[str, Any]]) -> List[str]:
    """Generate recommendations based on mood analysis"""
    recommendations = []
    
    # Handle different trend directions
    trend = mood_history.get("trend_direction", "stable")
    
    if trend == "improving":
        recommendations.append(
            "User's mood is trending positive - leverage this momentum with slightly more challenging activities"
        )
    elif trend == "declining":
        recommendations.append(
            "User's mood is trending negative - focus on supportive interactions and confidence-building activities"
        )
    elif trend == "stable":
        volatility = mood_history.get("volatility", 0.5)
        if volatility > 0.7:
            recommendations.append(
                "User's mood is highly variable - provide stability through consistent, predictable interactions"
            )
        else:
            recommendations.append(
                "User's mood is relatively stable - maintain current approach with gradual challenge increases"
            )
    
    # Add correlation-based recommendations
    for correlation in correlations:
        if correlation['confidence'] >= 0.7:
            if correlation['dominant_mood'] == 'positive':
                recommendations.append(
                    f"{correlation['domain']} activities appear to positively impact user mood - consider recommending more"
                )
            elif correlation['dominant_mood'] == 'negative':
                recommendations.append(
                    f"{correlation['domain']} activities appear to negatively impact user mood - approach with caution"
                )
    
    # If no specific recommendations, add a default
    if not recommendations:
        recommendations.append(
            "Continue with standard mood-aware interactions while gathering more mood data"
        )
    
    return recommendations