import asyncio
from django.db import transaction
from django.utils import timezone
import logging
from typing import Dict, Any, List, Optional, Union
from apps.main.agents.tools.tools_util import register_tool
from django.contrib.contenttypes.models import ContentType
from django.db.models import Count, Avg, Max, Min
from channels.db import database_sync_to_async

logger = logging.getLogger(__name__)

@register_tool('handle_activity_refusal')
async def handle_activity_refusal(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Processes user refusal of an activity and provides alternative suggestions.
    
    Input:
        user_profile_id: UUID of the user profile
        activity_id: UUID of the refused activity
        refusal_reason: Reason for refusal (time_constraint, difficulty, interest, etc.)
        refusal_comment: User's comment explaining the refusal
        request_alternative: Whether to request an alternative activity
        
    Output:
        result: Dictionary with refusal handling results
            recorded: Whether the refusal was successfully recorded
            understanding_message: Supportive response acknowledging the refusal
            alternative_suggestion: Alternative activity suggestion if requested
            trust_impact: Any information about trust impact
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    activity_id = input_data.get('activity_id')
    refusal_reason = input_data.get('refusal_reason', 'unspecified')
    refusal_comment = input_data.get('refusal_comment', '')
    request_alternative = input_data.get('request_alternative', True)
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    if not activity_id:
        return {"error": "activity_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, TrustLevel
        from apps.activity.models import ActivityTailored
        from apps.main.models import HistoryEvent, UserFeedback
        from apps.main.tasks.agent_tasks import process_post_spin_feedback
        
        # Get the user profile and activity
        user_profile = UserProfile.objects.get(id=user_profile_id)
        activity = ActivityTailored.objects.get(id=activity_id)
        
        # Record the refusal
        feedback_data = {
            'refused': True,
            'reason': refusal_reason,
            'comment': refusal_comment,
            'timestamp': timezone.datetime.now().isoformat()
        }
        
        # Create user feedback record
        feedback = UserFeedback.objects.create(
            feedback_type='post_spin',
            content_type=ContentType.objects.get_for_model(activity),
            object_id=activity.id,
            user_profile=user_profile,
            user_comment=refusal_comment,
            criticality=3,  # Medium-high criticality for refusals
            context_data=feedback_data
        )
        
        # Create history event
        event = HistoryEvent.objects.create(
            event_type='activity_refusal',
            content_type=ContentType.objects.get_for_model(activity),
            object_id=activity.id,
            user_profile=user_profile,
            details={
                'reason': refusal_reason,
                'comment': refusal_comment
            }
        )
        
        # Trigger the feedback processing task asynchronously
        # This will handle any needed adjustments to trust levels, etc.
        process_post_spin_feedback.delay(
            user_profile_id=str(user_profile.id),
            activity_id=str(activity.id),
            feedback_data=feedback_data
        )
        
        # Generate understanding message based on refusal reason
        understanding_message = _generate_refusal_understanding(refusal_reason, refusal_comment)
        
        # Get alternative suggestion if requested
        alternative_suggestion = None
        if request_alternative:
            alternative_suggestion = await _get_alternative_activity(user_profile, activity, refusal_reason)
        
        # Determine trust impact
        trust_impact = {
            "significant": False,
            "message": "Your choice has been respected and will help us better understand your preferences."
        }
        
        # Check if this is a pattern of refusals that might impact trust
        recent_refusals = HistoryEvent.objects.filter(
            event_type='activity_refusal',
            user_profile=user_profile,
            timestamp__gte=timezone.datetime.now() - timezone.timedelta(days=7)
        ).count()
        
        if recent_refusals >= 3:
            trust_impact["significant"] = True
            trust_impact["message"] = "I notice you've declined several activities recently. Would you like to discuss what kinds of activities might better match your interests?"
        
        return {
            "result": {
                "recorded": True,
                "understanding_message": understanding_message,
                "alternative_suggestion": alternative_suggestion,
                "trust_impact": trust_impact
            }
        }
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except ActivityTailored.DoesNotExist:
        return {"error": f"Activity with ID {activity_id} not found"}
    except Exception as e:
        logger.exception("Error handling activity refusal")
        return {"error": str(e)}


def _generate_refusal_understanding(reason: str, comment: str) -> str:
    """Helper function to generate appropriate understanding message for refusals"""
    base_messages = {
        "time_constraint": "I understand you don't have time for this activity right now. That's completely fine.",
        "difficulty": "I understand this activity feels too challenging right now. That's absolutely okay.",
        "interest": "I understand this activity doesn't interest you. That's valuable feedback.",
        "resources": "I understand you don't have the necessary resources for this activity. That's helpful to know.",
        "mood": "I understand this activity doesn't match your current mood. That's completely valid.",
        "environment": "I understand your current environment isn't suitable for this activity. That makes sense.",
        "unspecified": "I understand you'd prefer not to do this activity. That's completely fine."
    }
    
    message = base_messages.get(reason, base_messages["unspecified"])
    
    # Add personalized response if there's a comment
    if comment:
        message += " Thank you for sharing your thoughts - this helps me better understand your preferences."
    else:
        message += " Your feedback helps me suggest more suitable activities in the future."
    
    return message


@database_sync_to_async
def _sync_get_alternative_activity_data(user_profile_id, refused_activity_id, refusal_reason):
    """Synchronous helper to fetch alternative activity data from the database."""
    from apps.main.models import HistoryEvent
    from apps.activity.models import ActivityTailored, EntityDomainRelationship # Renamed model
    from apps.user.models import UserProfile
    from django.db.models import Q, Count

    try:
        user_profile = UserProfile.objects.get(id=user_profile_id)
        refused_activity = ActivityTailored.objects.get(id=refused_activity_id)

        completed_activities_ids = list(HistoryEvent.objects.filter(
            event_type='activity_completed',
            user_profile=user_profile
        ).values_list('object_id', flat=True))

        refused_domains_ids = list(refused_activity.domain_relationships.values_list('domain_id', flat=True))

        # Base query
        query = ActivityTailored.objects.filter(
            user_profile=user_profile
        ).exclude(
            id=refused_activity.id
        ).exclude(
            id__in=completed_activities_ids
        )

        # Adjust query based on refusal reason
        if refusal_reason == 'difficulty':
            query = query.filter(
                base_challenge_rating__lt=refused_activity.base_challenge_rating
            ).order_by('base_challenge_rating')
        elif refusal_reason == 'interest':
            query = query.exclude(
                domain_relationships__domain_id__in=refused_domains_ids
            )
        elif refusal_reason == 'time_constraint':
            query = query.order_by('duration_range')
        elif refusal_reason == 'resources':
            query = query.annotate(
                resource_count=Count('resource_requirements')
            ).order_by('resource_count')
        else:
            if refused_domains_ids:
                # Exclude activities that share *any* domain with the refused one
                query = query.exclude(domain_relationships__domain_id__in=refused_domains_ids)


        # Get potential alternatives (execute query)
        alternatives = list(query[:3]) # Use list() to evaluate the QuerySet

        if not alternatives:
            # Broader search if no specific alternatives found
            alternatives = list(ActivityTailored.objects.filter(
                user_profile=user_profile
            ).exclude(
                id=refused_activity.id
            ).exclude(
                id__in=completed_activities_ids
            )[:3])

        if alternatives:
            alternative = alternatives[0]
            return {
                "id": str(alternative.id),
                "name": alternative.name,
                "description": alternative.description,
                "challenge_rating": alternative.base_challenge_rating,
                "duration_range": alternative.duration_range, # Pass duration separately
            }
        return None

    except UserProfile.DoesNotExist:
        logger.error(f"User profile {user_profile_id} not found in _sync_get_alternative_activity_data")
        return None
    except ActivityTailored.DoesNotExist:
        logger.error(f"Refused activity {refused_activity_id} not found in _sync_get_alternative_activity_data")
        return None
    except Exception as e:
        logger.exception("Database error in _sync_get_alternative_activity_data")
        return None


async def _get_alternative_activity(user_profile, refused_activity, refusal_reason):
    """Helper function to get an alternative activity suggestion (async wrapper)."""
    try:
        alternative_data = await _sync_get_alternative_activity_data(
            user_profile.id, refused_activity.id, refusal_reason
        )

        if alternative_data:
            # Format the message using the fetched data
            return {
                "id": alternative_data["id"],
                "name": alternative_data["name"],
                "description": alternative_data["description"],
                "challenge_rating": alternative_data["challenge_rating"],
                "message": f"Instead, you might enjoy {alternative_data['name']}, which requires {alternative_data['duration_range']}."
            }
        return None

    except Exception as e:
        # Catch potential errors during the await or formatting
        logger.exception("Error getting alternative activity in async wrapper")
        return None


@register_tool('format_activity_instructions')
async def format_activity_instructions(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Formats activity instructions in a user-friendly, engaging way for presentation.
    
    Input:
        activity_id: UUID of the activity
        user_profile_id: UUID of the user profile
        format_style: Desired formatting style (simple, step_by_step, detailed)
        include_preparation: Whether to include preparation steps
        include_reflection: Whether to include reflection prompts
        
    Output:
        formatted_instructions: Dictionary with formatted instructions
            introduction: Introduction to the activity
            preparation: Preparation steps (if requested)
            main_steps: List of main activity steps
            reflection_prompts: Reflection prompts (if requested)
            time_estimates: Time estimates for each phase
            tips: Helpful tips for successful completion
    """
    # Extract required parameters
    activity_id = input_data.get('activity_id')
    user_profile_id = input_data.get('user_profile_id')
    format_style = input_data.get('format_style', 'step_by_step')
    include_preparation = input_data.get('include_preparation', True)
    include_reflection = input_data.get('include_reflection', True)
    
    if not activity_id:
        return {"error": "activity_id is required"}
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, TrustLevel
        from apps.activity.models import ActivityTailored
        
        # Get the user profile and activity
        user_profile = UserProfile.objects.get(id=user_profile_id)
        activity = ActivityTailored.objects.get(id=activity_id)
        
        # Determine trust phase for appropriate framing
        try:
            trust_level = TrustLevel.objects.get(user_profile=user_profile)
            trust_phase = "Expansion" if trust_level.value >= 60 else "Foundation"
        except TrustLevel.DoesNotExist:
            trust_phase = "Foundation"  # Default
        
        # Parse the raw instructions
        raw_instructions = activity.instructions
        
        # Generate an introduction that frames the activity appropriately
        introduction = _generate_activity_introduction(activity, trust_phase)
        
        # Extract and format the main steps
        main_steps = _extract_activity_steps(raw_instructions, format_style)
        
        # Generate preparation steps if requested
        preparation = None
        if include_preparation:
            preparation = _generate_preparation_steps(activity, format_style)
        
        # Generate reflection prompts if requested
        reflection_prompts = None
        if include_reflection:
            reflection_prompts = _generate_reflection_prompts(activity)
        
        # Generate time estimates
        time_estimates = _generate_time_estimates(activity)
        
        # Generate helpful tips
        tips = _generate_activity_tips(activity, trust_phase)
        
        # Create the formatted instructions
        formatted_instructions = {
            "introduction": introduction,
            "main_steps": main_steps,
            "time_estimates": time_estimates,
            "tips": tips
        }
        
        if preparation:
            formatted_instructions["preparation"] = preparation
            
        if reflection_prompts:
            formatted_instructions["reflection_prompts"] = reflection_prompts
        
        return {"formatted_instructions": formatted_instructions}
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except ActivityTailored.DoesNotExist:
        return {"error": f"Activity with ID {activity_id} not found"}
    except Exception as e:
        logger.exception("Error formatting activity instructions")
        return {"error": str(e)}


def _generate_activity_introduction(activity, trust_phase):
    """Helper function to generate an activity introduction"""
    name = activity.name
    description = activity.description
    
    if trust_phase == "Foundation":
        # More supportive, reassuring introduction
        introduction = f"Let's explore '{name}' together. {description}"
        introduction += " This activity is designed to be a comfortable yet engaging experience."
    else:
        # More growth-oriented introduction
        introduction = f"Let's dive into '{name}'. {description}"
        introduction += " This activity will give you an opportunity to develop new capabilities and insights."
    
    return introduction


def _extract_activity_steps(raw_instructions, format_style):
    """Helper function to extract and format activity steps"""
    # Try to detect if instructions already have numbered steps
    if any(line.strip().startswith(str(i) + ".") for i in range(1, 6) for line in raw_instructions.split('\n')):
        # Instructions already have numbered steps
        steps = []
        current_step = None
        
        for line in raw_instructions.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            # Check if this line starts a new step
            import re
            step_match = re.match(r'^(\d+)\.\s+(.*)$', line)
            
            if step_match:
                # If we have a current step, add it to steps
                if current_step:
                    steps.append(current_step)
                
                # Start a new step
                step_num = step_match.group(1)
                step_text = step_match.group(2)
                current_step = {"step": int(step_num), "content": step_text}
            elif current_step:
                # Continue current step
                current_step["content"] += " " + line
        
        # Add the last step if there is one
        if current_step:
            steps.append(current_step)
    else:
        # Split instructions by sentences or paragraphs
        import re
        sentences = re.split(r'(?<=[.!?])\s+', raw_instructions)
        
        # Group sentences into steps (around 2-3 sentences per step)
        steps = []
        current_step = None
        
        for i, sentence in enumerate(sentences):
            if i % 2 == 0:  # Start a new step every 2 sentences
                # If we have a current step, add it to steps
                if current_step:
                    steps.append(current_step)
                
                # Start a new step
                current_step = {"step": len(steps) + 1, "content": sentence}
            elif current_step:
                # Continue current step
                current_step["content"] += " " + sentence
        
        # Add the last step if there is one
        if current_step:
            steps.append(current_step)
    
    # Format based on style
    if format_style == 'simple':
        # Keep it minimal
        return [{"step": step["step"], "content": step["content"]} for step in steps]
    elif format_style == 'detailed':
        # Add more context and emphasis
        formatted_steps = []
        for step in steps:
            # Find important terms to emphasize
            content = step["content"]
            
            # Add a descriptive title if possible
            words = content.split()
            title = " ".join(words[:3]) + "..."
            
            formatted_steps.append({
                "step": step["step"],
                "title": title,
                "content": content
            })
        return formatted_steps
    else:  # 'step_by_step'
        # Default format
        return [{"step": step["step"], "content": step["content"]} for step in steps]


def _generate_preparation_steps(activity, format_style):
    """Helper function to generate preparation steps"""
    preparation = []
    
    # Check if there are any resource requirements
    resource_reqs = activity.resource_requirements.all()
    if resource_reqs.exists():
        for req in resource_reqs:
            preparation.append(
                f"Gather {req.quantity_required} {req.personal_resource.specific_name}" +
                ("" if req.quantity_required == 1 else "s")
            )
    
    # Check if there are any environment requirements
    env_reqs = activity.env_requirements.all()
    if env_reqs.exists():
        for req in env_reqs:
            preparation.append(f"Ensure you have access to {req.env_type}")
    
    # Add general preparation based on activity type
    domains = activity.domain_relationships.all()
    domain_types = [rel.domain.code for rel in domains]
    
    if any(d.startswith('physical') for d in domain_types):
        preparation.append("Wear comfortable clothing suitable for physical movement")
    
    if any(d.startswith('creative') for d in domain_types):
        preparation.append("Find a space where you feel creatively inspired")
    
    if any(d.startswith('reflective') for d in domain_types):
        preparation.append("Find a quiet space where you won't be interrupted")
    
    # If no specific preparations, add generic ones
    if not preparation:
        preparation.append("Find a suitable time and space for this activity")
        preparation.append("Review the main steps to ensure you understand what to expect")
    
    # Add time preparation
    if activity.duration_range:
        preparation.append(f"Set aside {activity.duration_range} for this activity")
    
    return preparation


def _generate_reflection_prompts(activity):
    """Helper function to generate reflection prompts"""
    prompts = []
    
    # Basic reflection prompts for all activities
    prompts.append("What did you notice about yourself during this activity?")
    prompts.append("What was the most interesting or surprising part of this experience?")
    
    # Add domain-specific reflection prompts
    domains = activity.domain_relationships.all()
    for rel in domains:
        domain = rel.domain
        if domain.code.startswith('physical'):
            prompts.append("How did your body feel during and after this activity?")
        elif domain.code.startswith('creative'):
            prompts.append("What new ideas or perspectives emerged during this creative process?")
        elif domain.code.startswith('social'):
            prompts.append("How did this activity affect your sense of connection with others?")
        elif domain.code.startswith('reflective'):
            prompts.append("What insights about yourself did you gain from this reflection?")
        elif domain.code.startswith('productive'):
            prompts.append("How did this activity impact your sense of accomplishment?")
    
    # Add challenge-related reflection
    if activity.base_challenge_rating > 70:
        prompts.append("This activity was designed to stretch your comfort zone. How did you respond to that challenge?")
    elif activity.base_challenge_rating < 30:
        prompts.append("This activity was designed to be accessible and comforting. How did that feel for you?")
    
    # Limit to 3-4 prompts
    return prompts[:4]


def _generate_time_estimates(activity):
    """Helper function to generate time estimates"""
    time_estimates = {}
    
    # Parse duration range
    duration_range = activity.duration_range
    if duration_range:
        # Try to extract numerical values
        import re
        numbers = re.findall(r'\d+', duration_range)
        
        if len(numbers) == 2:
            # Range like "10-20 minutes"
            min_time = int(numbers[0])
            max_time = int(numbers[1])
            
            # Calculate preparation time (usually 10-20% of activity time)
            prep_min = max(5, int(min_time * 0.1))
            prep_max = max(10, int(max_time * 0.2))
            
            # Calculate reflection time (usually 15-25% of activity time)
            reflection_min = max(5, int(min_time * 0.15))
            reflection_max = max(10, int(max_time * 0.25))
            
            time_estimates = {
                "preparation": f"{prep_min}-{prep_max} minutes",
                "main_activity": f"{min_time}-{max_time} minutes",
                "reflection": f"{reflection_min}-{reflection_max} minutes",
                "total": f"{min_time + prep_min + reflection_min}-{max_time + prep_max + reflection_max} minutes"
            }
        elif len(numbers) == 1:
            # Single value like "30 minutes"
            time = int(numbers[0])
            
            # Calculate preparation time
            prep_time = max(5, int(time * 0.15))
            
            # Calculate reflection time
            reflection_time = max(5, int(time * 0.2))
            
            time_estimates = {
                "preparation": f"{prep_time} minutes",
                "main_activity": f"{time} minutes",
                "reflection": f"{reflection_time} minutes",
                "total": f"{time + prep_time + reflection_time} minutes"
            }
        else:
            # No numerical values found, use generic times
            time_estimates = {
                "preparation": "5-10 minutes",
                "main_activity": duration_range,
                "reflection": "5-15 minutes",
                "total": f"{duration_range} plus 10-25 minutes for preparation and reflection"
            }
    else:
        # No duration information, use generic estimates
        time_estimates = {
            "preparation": "5-10 minutes",
            "main_activity": "20-40 minutes",
            "reflection": "5-15 minutes",
            "total": "30-65 minutes"
        }
    
    return time_estimates


def _generate_activity_tips(activity, trust_phase):
    """Helper function to generate helpful tips"""
    tips = []
    
    # Generic tips based on trust phase
    if trust_phase == "Foundation":
        tips.append("Remember, there's no 'right' way to do this activity - simply explore at your own pace")
        tips.append("Feel free to adapt the activity to better suit your comfort level")
    else:
        tips.append("Try to stay present with any moments of challenge - they often hold the greatest growth")
        tips.append("Notice what this activity reveals about your patterns and preferences")
    
    # Domain-specific tips
    domains = activity.domain_relationships.all()
    for rel in domains:
        domain = rel.domain
        if domain.code.startswith('physical'):
            tips.append("Pay attention to your body's signals and adjust as needed")
        elif domain.code.startswith('creative'):
            tips.append("Try to suspend self-judgment during the creative process")
        elif domain.code.startswith('social'):
            tips.append("Focus on authentic connection rather than 'perfect' social performance")
        elif domain.code.startswith('reflective'):
            tips.append("Approach your thoughts with curiosity rather than criticism")
        elif domain.code.startswith('productive'):
            tips.append("Focus on the process rather than just the end result")
    
    # Challenge-related tips
    if activity.base_challenge_rating > 70:
        tips.append("It's normal if parts of this activity feel challenging - that's where growth happens")
    
    # Limit to 3 tips
    return tips[:3]


# --- Refactored User History Analysis ---

@database_sync_to_async
def _sync_get_user_history_analysis_data(user_profile_id: str, time_period: str, include_feedback: bool) -> Optional[Dict[str, Any]]:
    """Synchronous helper to fetch all necessary data for user history analysis."""
    from apps.user.models import UserProfile
    from apps.activity.models import ActivityTailored
    from apps.main.models import HistoryEvent, UserFeedback
    from django.contrib.contenttypes.models import ContentType
    from django.utils import timezone
    from django.db.models import Count, Avg, Max, Min
    from django.db.models.functions import ExtractHour, ExtractWeekDay, TruncDate

    try:
        user_profile = UserProfile.objects.get(id=user_profile_id)

        # --- Time Filters ---
        time_filters = {}
        now = timezone.now() # Use timezone.now() for consistency
        if time_period == 'last_week':
            time_filters['timestamp__gte'] = now - timezone.timedelta(days=7)
        elif time_period == 'last_month':
            time_filters['timestamp__gte'] = now - timezone.timedelta(days=30)

        # --- Fetch History Events ---
        history_events_qs = HistoryEvent.objects.filter(
            user_profile=user_profile,
            **time_filters
        ).order_by('timestamp')
        # Convert QuerySet to list of dicts to pass data, not QuerySets
        history_events_data = list(history_events_qs.values(
            'id', 'event_type', 'content_type_id', 'object_id', 'timestamp', 'details'
        ))

        # --- Fetch Feedback Events (if requested) ---
        feedback_events_data = []
        if include_feedback:
            feedback_time_filters = {}
            if 'timestamp__gte' in time_filters:
                 # Use created_on for feedback filtering
                feedback_time_filters['created_on__gte'] = time_filters['timestamp__gte']

            feedback_events_qs = UserFeedback.objects.filter(
                user_profile=user_profile,
                **feedback_time_filters
            ).order_by('created_on')
            feedback_events_data = list(feedback_events_qs.values(
                'id', 'feedback_type', 'user_comment', 'criticality', 'created_on'
            ))

        # --- Fetch Activity Details (for completed/refused activities) ---
        activity_tailored_ct_id = ContentType.objects.get_for_model(ActivityTailored).id
        activity_ids = set()
        for event in history_events_data:
            if event['content_type_id'] == activity_tailored_ct_id and event['object_id']:
                 # Ensure object_id is not None before adding
                 activity_ids.add(event['object_id'])

        activities_data = {}
        if activity_ids:
            activities_qs = ActivityTailored.objects.filter(id__in=activity_ids).prefetch_related('domain_relationships__domain')
            activities_data = {
                str(activity.id): {
                    'name': activity.name,
                    'base_challenge_rating': activity.base_challenge_rating,
                    'domains': [
                        {'name': rel.domain.name, 'code': rel.domain.code, 'strength': rel.strength}
                        for rel in activity.domain_relationships.all()
                    ]
                }
                for activity in activities_qs
            }

        return {
            "history_events": history_events_data,
            "feedback_events": feedback_events_data,
            "activities_details": activities_data,
            "activity_content_type_id": activity_tailored_ct_id,
            "first_event_timestamp": history_events_qs.first().timestamp if history_events_qs.exists() else None,
            "last_event_timestamp": history_events_qs.last().timestamp if history_events_qs.exists() else None,
        }

    except UserProfile.DoesNotExist:
        logger.error(f"User profile {user_profile_id} not found in _sync_get_user_history_analysis_data")
        return None
    except Exception as e:
        logger.exception("Database error in _sync_get_user_history_analysis_data")
        return None


@register_tool('get_user_history_analysis')
async def get_user_history_analysis(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyzes a user's activity history to identify patterns, preferences, and growth trajectory. (Async wrapper)

    Input:
        user_profile_id: UUID of the user profile
        time_period: Time period to analyze (last_week, last_month, all_time)
        analysis_focus: Specific aspect to focus on (patterns, preferences, growth, engagement)
        include_feedback: Whether to include feedback analysis (default: True)

    Output:
        analysis: Dictionary with history analysis results
            activity_patterns: Patterns in activity engagement and completion
            domain_preferences: Domain preferences based on engagement
            growth_trajectory: Growth and challenge progression over time
            engagement_metrics: Engagement and participation metrics
            feedback_themes: Common themes from user feedback (if requested)
            recommendations: Personalized recommendations based on analysis
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    time_period = input_data.get('time_period', 'all_time')
    analysis_focus = input_data.get('analysis_focus', 'all')
    include_feedback = input_data.get('include_feedback', True)

    if not user_profile_id:
        return {"error": "user_profile_id is required"}

    try:
        # Fetch data asynchronously using the sync helper
        analysis_data = await _sync_get_user_history_analysis_data(
            user_profile_id, time_period, include_feedback
        )

        if analysis_data is None:
            # Error occurred during data fetching
            return {"error": "Failed to retrieve user history data"}

        # Unpack fetched data
        history_events = analysis_data["history_events"]
        feedback_events = analysis_data["feedback_events"]
        activities_details = analysis_data["activities_details"]
        activity_ct_id = analysis_data["activity_content_type_id"]
        first_event_ts = analysis_data["first_event_timestamp"]
        last_event_ts = analysis_data["last_event_timestamp"]

        # Determine what analyses to perform
        perform_all = analysis_focus == 'all'
        analyze_patterns = perform_all or analysis_focus == 'patterns'
        analyze_preferences = perform_all or analysis_focus == 'preferences'
        analyze_growth = perform_all or analysis_focus == 'growth'
        analyze_engagement = perform_all or analysis_focus == 'engagement'

        # Initialize result containers
        result = {}

        # --- Perform Analyses (using fetched data) ---

        if analyze_patterns:
            activity_patterns = _analyze_activity_patterns(history_events)
            result['activity_patterns'] = activity_patterns

        if analyze_preferences:
            domain_preferences = _analyze_domain_preferences(history_events, activities_details, activity_ct_id)
            result['domain_preferences'] = domain_preferences

        if analyze_growth:
            growth_trajectory = _analyze_growth_trajectory(history_events, activities_details, activity_ct_id)
            result['growth_trajectory'] = growth_trajectory

        if analyze_engagement:
            engagement_metrics = _analyze_engagement_metrics(history_events, first_event_ts, last_event_ts)
            result['engagement_metrics'] = engagement_metrics

        if include_feedback:
            feedback_themes = _analyze_feedback_themes(feedback_events)
            result['feedback_themes'] = feedback_themes

        # Generate recommendations based on analyses
        recommendations = _generate_history_recommendations(result)
        result['recommendations'] = recommendations

        return {"analysis": result}

    except Exception as e:
        logger.exception("Error analyzing user history in async wrapper")
        return {"error": str(e)}


def _analyze_activity_patterns(history_events_data: List[Dict]) -> Dict:
    """Analyzes activity patterns from fetched event data."""
    completion_events = [e for e in history_events_data if e['event_type'] == 'activity_completed']
    refusal_events = [e for e in history_events_data if e['event_type'] == 'activity_refusal']

    # --- Timing Patterns ---
    time_distribution = {'morning': 0, 'afternoon': 0, 'evening': 0, 'night': 0}
    day_counts = {'Monday': 0, 'Tuesday': 0, 'Wednesday': 0, 'Thursday': 0, 'Friday': 0, 'Saturday': 0, 'Sunday': 0}
    days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    for event in completion_events:
        ts = event['timestamp']
        hour = ts.hour
        # Weekday: Monday is 0 and Sunday is 6
        day_idx = ts.weekday()
        day_counts[days_of_week[day_idx]] += 1

        if 5 <= hour < 12: time_distribution['morning'] += 1
        elif 12 <= hour < 17: time_distribution['afternoon'] += 1
        elif 17 <= hour < 22: time_distribution['evening'] += 1
        else: time_distribution['night'] += 1

    # --- Completion Rates ---
    total_activities = len(completion_events) + len(refusal_events)
    completion_rate = (len(completion_events) / total_activities * 100) if total_activities > 0 else 0

    # --- Duration Patterns ---
    duration_data = []
    for event in completion_events:
        details = event.get('details', {})
        if isinstance(details, dict) and 'duration' in details and isinstance(details['duration'], (int, float)):
             # Check if details is a dict and duration exists and is numeric
             duration_data.append(details['duration'])

    avg_duration = sum(duration_data) / len(duration_data) if duration_data else None

    return {
        "time_of_day_distribution": time_distribution,
        "day_of_week_distribution": day_counts,
        "completion_rate": completion_rate,
        "average_duration": avg_duration,
        "total_activities_completed": len(completion_events),
        "total_activities_refused": len(refusal_events)
    }


def _analyze_domain_preferences(history_events_data: List[Dict], activities_details: Dict, activity_ct_id: int) -> Dict:
    """Analyzes domain preferences from fetched event and activity data."""
    completion_events = [e for e in history_events_data if e['event_type'] == 'activity_completed' and e['content_type_id'] == activity_ct_id]
    refusal_events = [e for e in history_events_data if e['event_type'] == 'activity_refusal' and e['content_type_id'] == activity_ct_id]

    domain_engagement = {}
    domain_refusals = {}

    # --- Calculate Engagement ---
    for event in completion_events:
        activity_id = str(event['object_id'])
        if activity_id in activities_details:
            activity = activities_details[activity_id]
            for domain_info in activity.get('domains', []):
                domain_name = domain_info['name']
                engagement_value = domain_info.get('strength', 50) / 100.0 # Default strength 50 if missing

                if domain_name in domain_engagement:
                    domain_engagement[domain_name]['count'] += 1
                    domain_engagement[domain_name]['weighted_engagement'] += engagement_value
                else:
                    domain_engagement[domain_name] = {
                        'code': domain_info.get('code', ''),
                        'count': 1,
                        'weighted_engagement': engagement_value
                    }

    # --- Calculate Refusals ---
    for event in refusal_events:
        activity_id = str(event['object_id'])
        if activity_id in activities_details:
            activity = activities_details[activity_id]
            for domain_info in activity.get('domains', []):
                domain_name = domain_info['name']
                domain_refusals[domain_name] = domain_refusals.get(domain_name, 0) + 1

    # --- Calculate Percentages and Sort ---
    total_engagements = sum(data['count'] for data in domain_engagement.values())
    for domain, data in domain_engagement.items():
        data['percentage'] = (data['count'] / total_engagements * 100) if total_engagements > 0 else 0

    sorted_domains = sorted(domain_engagement.items(), key=lambda x: x[1]['weighted_engagement'], reverse=True)
    top_domains = [domain for domain, data in sorted_domains[:3]]

    # --- Determine Avoided Domains ---
    avoided_domains = []
    for domain, refusal_count in domain_refusals.items():
        completion_count = domain_engagement.get(domain, {}).get('count', 0)
        total_interactions = refusal_count + completion_count
        if total_interactions > 0:
            refusal_rate = refusal_count / total_interactions
            if refusal_rate > 0.5: # More than 50% refusal rate
                avoided_domains.append(domain)

    return {
        "domain_engagement": domain_engagement,
        "top_domains": top_domains,
        "avoided_domains": avoided_domains
    }


def _analyze_growth_trajectory(history_events_data: List[Dict], activities_details: Dict, activity_ct_id: int) -> Dict:
    """Analyzes growth trajectory from fetched event and activity data."""
    completion_events = sorted(
        [e for e in history_events_data if e['event_type'] == 'activity_completed' and e['content_type_id'] == activity_ct_id],
        key=lambda x: x['timestamp']
    )

    challenge_trajectory = []
    for event in completion_events:
        activity_id = str(event['object_id'])
        if activity_id in activities_details:
            activity = activities_details[activity_id]
            challenge_trajectory.append({
                'timestamp': event['timestamp'].isoformat(),
                'challenge_level': activity.get('base_challenge_rating', 50), # Default 50
                'activity_name': activity.get('name', 'Unknown Activity')
            })

    # --- Calculate Trend Statistics ---
    trend = "no_data"
    initial_challenge = 0
    current_challenge = 0
    avg_challenge = 0
    growth_phases = []

    if challenge_trajectory:
        challenge_levels = [item['challenge_level'] for item in challenge_trajectory]
        initial_challenge = challenge_levels[0]
        current_challenge = challenge_levels[-1]
        avg_challenge = sum(challenge_levels) / len(challenge_levels)

        if len(challenge_levels) >= 3:
            # Simple trend check: compare start and end, allowing for some noise
            if current_challenge > initial_challenge * 1.15: trend = "increasing" # 15% increase
            elif current_challenge < initial_challenge * 0.85: trend = "decreasing" # 15% decrease
            else: trend = "stable"
        else:
            trend = "insufficient_data"

        # --- Analyze Growth Phases (Simplified) ---
        # This requires more complex logic (e.g., moving averages, slope changes)
        # For now, we'll keep it simple and rely on the overall trend.
        # A more robust implementation could be added here later.
        pass # Placeholder for future phase analysis logic

    return {
        "challenge_trajectory": challenge_trajectory,
        "initial_challenge": initial_challenge,
        "current_challenge": current_challenge,
        "average_challenge": avg_challenge,
        "trend": trend,
        "growth_phases": growth_phases # Currently empty
    }


def _analyze_engagement_metrics(history_events_data: List[Dict], first_event_ts, last_event_ts) -> Dict:
    """Analyzes engagement metrics from fetched event data and timestamps."""
    total_events = len(history_events_data)
    activity_completions = sum(1 for e in history_events_data if e['event_type'] == 'activity_completed')
    activity_refusals = sum(1 for e in history_events_data if e['event_type'] == 'activity_refusal')
    wheel_spins = sum(1 for e in history_events_data if e['event_type'] == 'wheel_spin')
    check_in_types = {'daily_check_in', 'weekly_check_in', 'morning_check_in', 'evening_check_in'}
    check_ins = sum(1 for e in history_events_data if e['event_type'] in check_in_types)

    activity_interaction_rate = ((activity_completions + activity_refusals) / total_events * 100) if total_events > 0 else 0
    completion_rate = (activity_completions / (activity_completions + activity_refusals) * 100) if (activity_completions + activity_refusals) > 0 else 0

    # --- Session Frequency ---
    unique_session_dates = set(e['timestamp'].date() for e in history_events_data)
    unique_sessions = len(unique_session_dates)
    session_frequency = 0
    days_span = 0

    if unique_sessions > 0 and first_event_ts and last_event_ts:
        # Ensure timestamps are timezone-aware if needed, though date() handles it
        days_span = (last_event_ts.date() - first_event_ts.date()).days + 1
        session_frequency = unique_sessions / days_span if days_span > 0 else unique_sessions # Avoid division by zero if span is < 1 day

    avg_events_per_session = total_events / unique_sessions if unique_sessions > 0 else 0

    return {
        "total_events": total_events,
        "activity_completions": activity_completions,
        "activity_refusals": activity_refusals,
        "wheel_spins": wheel_spins,
        "check_ins": check_ins,
        "activity_interaction_rate": activity_interaction_rate,
        "completion_rate": completion_rate,
        "unique_sessions": unique_sessions,
        "session_frequency_per_day": session_frequency, # Renamed for clarity
        "days_span_analyzed": days_span,
        "avg_events_per_session": avg_events_per_session
    }


def _analyze_feedback_themes(feedback_events_data: List[Dict]) -> Dict:
    """Analyzes feedback themes from fetched feedback data."""
    positive_themes = {}
    negative_themes = {}
    neutral_themes = {}
    positive_count = 0
    negative_count = 0
    neutral_count = 0

    for feedback in feedback_events_data:
        sentiment = "neutral"
        criticality = feedback.get('criticality', 3) # Default to neutral
        feedback_type = feedback.get('feedback_type', '')

        # Simplified sentiment determination
        if feedback_type.startswith('positive') or criticality <= 2:
            sentiment = "positive"
            positive_count += 1
        elif feedback_type.startswith('negative') or criticality >= 4:
            sentiment = "negative"
            negative_count += 1
        else:
            neutral_count += 1

        keywords = _extract_keywords_from_feedback(feedback.get('user_comment', ''))
        theme_map = {'positive': positive_themes, 'negative': negative_themes, 'neutral': neutral_themes}[sentiment]
        for keyword in keywords:
            theme_map[keyword] = theme_map.get(keyword, 0) + 1

    # --- Sort and Get Top Themes ---
    sorted_positive = sorted(positive_themes.items(), key=lambda x: x[1], reverse=True)
    sorted_negative = sorted(negative_themes.items(), key=lambda x: x[1], reverse=True)
    sorted_neutral = sorted(neutral_themes.items(), key=lambda x: x[1], reverse=True)

    top_positive = [{"theme": theme, "count": count} for theme, count in sorted_positive[:5]]
    top_negative = [{"theme": theme, "count": count} for theme, count in sorted_negative[:5]]
    top_neutral = [{"theme": theme, "count": count} for theme, count in sorted_neutral[:5]]

    # --- Calculate Sentiment Distribution ---
    total_feedback = len(feedback_events_data)
    sentiment_distribution = {
        "positive": (positive_count / total_feedback * 100) if total_feedback > 0 else 0,
        "neutral": (neutral_count / total_feedback * 100) if total_feedback > 0 else 0,
        "negative": (negative_count / total_feedback * 100) if total_feedback > 0 else 0
    }

    return {
        "top_positive_themes": top_positive,
        "top_negative_themes": top_negative,
        "top_neutral_themes": top_neutral,
        "sentiment_distribution": sentiment_distribution,
        "total_feedback": total_feedback
    }


def _extract_keywords_from_feedback(comment: Optional[str]) -> List[str]:
    """Helper function to extract keywords from feedback text (remains synchronous)."""
    # Simple keyword extraction based on word frequency
    if not comment:
        return []

    # Clean the text
    import re
    cleaned_text = re.sub(r'[^\w\s]', '', comment.lower())

    # Split into words
    words = cleaned_text.split()

    # Remove common stop words (Consider using a library like NLTK for better stop word lists if needed)
    stop_words = {
        'the', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'of',
        'is', 'am', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has',
        'had', 'do', 'does', 'did', 'i', 'me', 'my', 'mine', 'myself', 'you',
        'your', 'yours', 'yourself', 'he', 'him', 'his', 'himself', 'she', 'her',
        'hers', 'herself', 'it', 'its', 'itself', 'we', 'us', 'our', 'ours',
        'ourselves', 'they', 'them', 'their', 'theirs', 'themselves', 'this',
        'that', 'these', 'those', 'but', 'because', 'if', 'or', 'as', 'until',
        'while', 'by', 'about', 'against', 'between', 'into', 'through', 'during',
        'before', 'after', 'above', 'below', 'from', 'up', 'down', 'out', 'off',
        'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there',
        'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few',
        'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only',
        'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will',
        'just', 'don', 'should', 'now', 'activity', 'like', 'feel', 'really' # Added some domain specific ones
    }

    filtered_words = [w for w in words if w not in stop_words and len(w) > 2] # Allow slightly shorter words

    # Count word frequencies
    word_count = {}
    for word in filtered_words:
        word_count[word] = word_count.get(word, 0) + 1

    # Extract keywords (words with frequency > 1 or top N if few words)
    keywords = []
    if filtered_words:
        # Prioritize words appearing more than once
        keywords = [word for word, count in word_count.items() if count > 1]
        # If few frequent words, add the most frequent single words
        if len(keywords) < 3:
            single_words = sorted([word for word, count in word_count.items() if count == 1], key=lambda w: len(w), reverse=True)
            keywords.extend(single_words[:3 - len(keywords)])
        # If still no keywords (e.g., very short comment), take the longest words
        if not keywords:
             keywords = sorted(filtered_words, key=len, reverse=True)[:min(3, len(filtered_words))]

    return keywords[:5] # Limit number of keywords


def _generate_history_recommendations(analysis_results: Dict) -> List[Dict]:
    """Generates recommendations based on the aggregated analysis results (synchronous)."""
    recommendations = []

    # --- Activity Patterns Recommendations ---
    if 'activity_patterns' in analysis_results:
        patterns = analysis_results['activity_patterns']
        if 'time_of_day_distribution' in patterns:
            time_dist = patterns['time_of_day_distribution']
            if time_dist and sum(time_dist.values()) > 0: # Check if there's data
                preferred_time = max(time_dist.items(), key=lambda item: item[1])[0]
                recommendations.append({
                    "type": "timing",
                    "recommendation": f"Suggest activities during the {preferred_time}, as this seems to be the user's most active time.",
                    "data_point": f"Highest activity completion count during the {preferred_time}."
                })
        if 'completion_rate' in patterns:
            rate = patterns['completion_rate']
            if rate < 60 and patterns.get('total_activities_completed', 0) + patterns.get('total_activities_refused', 0) > 3: # Only if rate is low and enough data
                recommendations.append({
                    "type": "engagement",
                    "recommendation": "Consider offering slightly less challenging or different types of activities to improve the completion rate.",
                    "data_point": f"Activity completion rate is {rate:.1f}%."
                })

    # --- Domain Preferences Recommendations ---
    if 'domain_preferences' in analysis_results:
        prefs = analysis_results['domain_preferences']
        if prefs.get('top_domains'):
            top_domain = prefs['top_domains'][0]
            recommendations.append({
                "type": "domains",
                "recommendation": f"Focus on suggesting activities within the '{top_domain}' domain, as the user engages well with it.",
                "data_point": f"'{top_domain}' is among the most engaged domains."
            })
        if prefs.get('avoided_domains'):
            avoided_domain = prefs['avoided_domains'][0]
            recommendations.append({
                "type": "domains",
                "recommendation": f"Explore why activities in the '{avoided_domain}' domain might be refused. Perhaps offer simpler variations.",
                "data_point": f"Activities in the '{avoided_domain}' domain have a higher refusal rate."
            })

    # --- Growth Trajectory Recommendations ---
    if 'growth_trajectory' in analysis_results:
        growth = analysis_results['growth_trajectory']
        trend = growth.get('trend', 'no_data')
        avg_challenge = growth.get('average_challenge', 50)
        if trend == "increasing":
            recommendations.append({
                "type": "challenge",
                "recommendation": "The user is handling increasing challenge well. Continue gradual increases.",
                "data_point": f"Challenge level trend is increasing (average: {avg_challenge:.0f})."
            })
        elif trend == "stable":
             recommendations.append({
                "type": "challenge",
                "recommendation": "Challenge level is stable. Consider introducing slightly more challenging options periodically.",
                "data_point": f"Challenge level trend is stable (average: {avg_challenge:.0f})."
            })
        elif trend == "decreasing":
            recommendations.append({
                "type": "challenge",
                "recommendation": "User seems to be preferring lower challenge activities. Focus on engagement before pushing difficulty.",
                "data_point": f"Challenge level trend is decreasing (average: {avg_challenge:.0f})."
            })

    # --- Engagement Metrics Recommendations ---
    if 'engagement_metrics' in analysis_results:
        eng = analysis_results['engagement_metrics']
        freq = eng.get('session_frequency_per_day', 0)
        days_span = eng.get('days_span_analyzed', 0)
        if days_span > 7 and freq < 0.5: # If analyzed over a week and frequency is less than every other day
            recommendations.append({
                "type": "frequency",
                "recommendation": "User engagement frequency is somewhat low. Consider strategies to encourage more regular check-ins or activity attempts.",
                "data_point": f"Average session frequency is {freq:.2f} sessions/day over {days_span} days."
            })

    # --- Feedback Themes Recommendations ---
    if 'feedback_themes' in analysis_results:
        feedback = analysis_results['feedback_themes']
        if feedback.get('top_negative_themes'):
            neg_theme = feedback['top_negative_themes'][0]['theme']
            recommendations.append({
                "type": "feedback",
                "recommendation": f"Address user feedback, particularly concerning '{neg_theme}'.",
                "data_point": f"'{neg_theme}' is a common theme in negative feedback."
            })
        if feedback.get('top_positive_themes'):
            pos_theme = feedback['top_positive_themes'][0]['theme']
            recommendations.append({
                "type": "feedback",
                "recommendation": f"Reinforce positive experiences related to '{pos_theme}'.",
                "data_point": f"'{pos_theme}' is a common theme in positive feedback."
            })


    # Prioritize and limit recommendations
    # Simple prioritization: Challenge > Domains > Engagement > Feedback > Timing
    priority_order = {"challenge": 1, "domains": 2, "engagement": 3, "feedback": 4, "frequency": 5, "timing": 6}
    sorted_recommendations = sorted(recommendations, key=lambda r: priority_order.get(r['type'], 99))

    return sorted_recommendations[:5] # Limit to top 5 recommendations
