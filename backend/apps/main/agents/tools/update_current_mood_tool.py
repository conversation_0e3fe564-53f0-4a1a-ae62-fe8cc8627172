import logging
import traceback
from typing import Any, Dict
from django.contrib.contenttypes.models import ContentType

from apps.main.agents.tools.tools_util import register_tool


logger = logging.getLogger(__name__)

@register_tool('update_current_mood')
async def update_current_mood(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Updates or creates the current mood record for a user based on interaction analysis.
    
    Input:
        user_profile_id: UUID of the user profile
        description: Text description of the mood
        height: Intensity level of the mood (0-100)
        user_awareness: Level of user's awareness of their mood (0-100)
        inferred_from_text: Whether the mood was inferred from user text (vs explicitly stated)
        source_text: Optional text from which the mood was inferred
        
    Output:
        mood: Dictionary with the updated mood information
            id: ID of the mood record
            description: Text description of the mood
            height: Intensity level of the mood
            user_awareness: Level of user's awareness
            processed_at: Timestamp when the mood was processed
            previous_mood: Previous mood information if available
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    description = input_data.get('description')
    height = input_data.get('height')
    user_awareness = input_data.get('user_awareness', 50)  # Default medium awareness
    inferred_from_text = input_data.get('inferred_from_text', False)
    source_text = input_data.get('source_text', '')

    # Enhanced logging for debugging
    logger.info(f"update_current_mood called with input_data: {input_data}")
    logger.info(f"user_profile_id type: {type(user_profile_id)}, value: {user_profile_id}")

    if not user_profile_id:
        return {"error": "user_profile_id is required"}

    if not description:
        return {"error": "description is required"}

    if height is None:
        return {"error": "height is required"}

    # Validate user_profile_id type and convert if necessary
    if not isinstance(user_profile_id, (int, str)):
        # Check if it's a model instance that might have been passed by mistake
        if hasattr(user_profile_id, 'id'):
            logger.warning(f"user_profile_id appears to be a model instance: {user_profile_id}, extracting ID")
            user_profile_id = user_profile_id.id
        else:
            logger.error(f"Invalid user_profile_id type: {type(user_profile_id)}, value: {user_profile_id}")
            return {"error": f"user_profile_id must be an integer or string, got {type(user_profile_id).__name__}: {user_profile_id}"}

    # Ensure user_profile_id is an integer
    try:
        user_profile_id = int(user_profile_id)
        logger.info(f"Converted user_profile_id to integer: {user_profile_id}")
    except (ValueError, TypeError) as e:
        logger.error(f"Could not convert user_profile_id to integer: {user_profile_id}, error: {e}")
        return {"error": f"user_profile_id must be convertible to integer, got: {user_profile_id}"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, CurrentMood
        from django.utils import timezone
        import datetime
        
        # Get the user profile with enhanced error handling (using async)
        logger.info(f"Attempting to get UserProfile with id: {user_profile_id}")
        try:
            user_profile = await UserProfile.objects.aget(id=user_profile_id)
            logger.info(f"Successfully retrieved UserProfile: {user_profile}")
            logger.info(f"UserProfile ID: {user_profile.id}, User: {user_profile.user}")
        except Exception as e:
            logger.error(f"Error getting UserProfile with id {user_profile_id}: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise
        
        # Check for existing mood record to preserve it as previous_mood
        previous_mood = None
        logger.info(f"Checking for existing CurrentMood for UserProfile: {user_profile}")
        logger.info(f"UserProfile type: {type(user_profile)}, UserProfile.id: {user_profile.id}")

        # Defensive check to ensure we have a valid UserProfile object
        if not hasattr(user_profile, 'id') or not isinstance(user_profile.id, int):
            logger.error(f"Invalid UserProfile object: {user_profile}, type: {type(user_profile)}")
            return {"error": f"Invalid UserProfile object: {user_profile}"}

        try:
            current_mood = await CurrentMood.objects.aget(user_profile_id=user_profile.id)
            logger.info(f"Found existing CurrentMood: {current_mood}")
            previous_mood = {
                "description": current_mood.description,
                "height": current_mood.height,
                "user_awareness": current_mood.user_awareness,
                "processed_at": current_mood.processed_at.isoformat() if current_mood.processed_at else None
            }

            # Update the existing mood record
            current_mood.description = description
            current_mood.height = height
            current_mood.user_awareness = user_awareness
            current_mood.inferred_from_text = inferred_from_text # Add missing update
            current_mood.processed_at = timezone.datetime.now()
            current_mood.effective_start = timezone.datetime.now() # Update start time
            current_mood.effective_end = timezone.datetime.now() # Update end time (or set based on duration?)
            current_mood.duration_estimate = "1 day" # Reset duration estimate?
            # Note: The logic for effective_end and duration_estimate might need refinement based on desired behavior.
            # Setting effective_end=now effectively ends the previous mood state immediately.
            await current_mood.asave() # Save the updated existing mood

        except CurrentMood.DoesNotExist:
            logger.info(f"No existing CurrentMood found, creating new one for UserProfile: {user_profile}")
            # Create new mood record
            try:
                # Additional defensive checks before creating CurrentMood
                logger.info(f"About to create CurrentMood with UserProfile: {user_profile}")
                logger.info(f"UserProfile.__dict__: {user_profile.__dict__}")

                # Ensure we're passing a proper UserProfile instance
                if not isinstance(user_profile, UserProfile):
                    logger.error(f"user_profile is not a UserProfile instance: {type(user_profile)}")
                    return {"error": f"Invalid UserProfile type: {type(user_profile)}"}

                current_mood = await CurrentMood.objects.acreate( # Use async create
                    user_profile=user_profile,
                    description=description,
                    height=height,
                    user_awareness=user_awareness,
                    processed_at=timezone.datetime.now(),
                    effective_start=timezone.datetime.now(), # Use datetime for effective_start
                    duration_estimate="1 day",
                    effective_end=timezone.datetime.now() # Set effective_end to now for new moods
                )
                logger.info(f"Successfully created new CurrentMood: {current_mood}")
            except Exception as create_error:
                logger.error(f"Error creating CurrentMood: {create_error}")
                logger.error(f"UserProfile: {user_profile}, type: {type(user_profile)}")
                logger.error(f"UserProfile.id: {user_profile.id}, type: {type(user_profile.id)}")
                logger.error(f"UserProfile.user: {user_profile.user}, type: {type(user_profile.user)}")
                logger.error(f"Full traceback: {traceback.format_exc()}")
                raise
        
        # Create a history event for mood update
        from apps.main.models import HistoryEvent
        
        event_type = "mood_explicitly_updated" if not inferred_from_text else "mood_inferred"
        
        HistoryEvent.objects.create(
            event_type=event_type,
            user_profile=user_profile,
            content_type=ContentType.objects.get_for_model(CurrentMood),
            object_id=str(current_mood.id),  # Convert to string explicitly
            details={
                "description": description,
                "height": height,
                "user_awareness": user_awareness,
                "inferred_from_text": inferred_from_text,
                "source_text": source_text[:200] if source_text else None  # Limit length
            }
        )
        
        # Prepare response
        mood_data = {
            "id": current_mood.id,
            "description": current_mood.description,
            "height": current_mood.height,
            "user_awareness": current_mood.user_awareness,
            "processed_at": current_mood.processed_at.isoformat() if current_mood.processed_at else None
        }
        
        if previous_mood:
            mood_data["previous_mood"] = previous_mood
        
        return {"mood": mood_data}
    
    except UserProfile.DoesNotExist:
        logger.error(f"UserProfile with ID {user_profile_id} not found")
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error updating current mood")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        logger.error(f"Input data was: {input_data}")
        return {"error": f"Error updating current mood: {str(e)}"}
