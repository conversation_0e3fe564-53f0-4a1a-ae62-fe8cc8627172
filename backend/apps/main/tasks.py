"""
Celery tasks for the main app.
"""
import logging
from typing import Dict, Any, Optional
from celery import shared_task
from asgiref.sync import async_to_sync
from apps.main.services.async_workflow_manager import WorkflowBenchmarker
from apps.main.services.event_service import EventService

logger = logging.getLogger(__name__)


@shared_task(bind=True, name="run_workflow_benchmark")
def run_workflow_benchmark(
    self,
    scenario_id: str,
    params: Dict[str, Any],
    user_profile_id: Optional[str] = None
) -> str:
    """
    Run a workflow benchmark.
    
    Args:
        scenario_id: The ID of the benchmark scenario to run
        params: Parameters for the benchmark run
        user_profile_id: Optional ID of the user profile to use for the benchmark
        
    Returns:
        The ID of the benchmark run
    """
    task_id = self.request.id
    
    # Create an event emitter for progress reporting
    async def progress_callback(state, meta):
        await EventService.emit_event(
            event_type="benchmark.progress",
            data={
                "task_id": task_id,
                "state": state,
                "meta": meta
            }
        )
    
    try:
        # Run the benchmark
        workflow_manager = WorkflowBenchmarker()
        benchmark_run = async_to_sync(workflow_manager.execute_benchmark)(
            scenario_id=scenario_id,
            params=params,
            progress_callback=progress_callback,
            user_profile_id=user_profile_id
        )
        
        # Return the benchmark run ID
        return str(benchmark_run.id)
    
    except Exception as e:
        # Log the error
        logger.error(f"Error running workflow benchmark: {e}", exc_info=True)
        
        # Emit an error event
        async_to_sync(EventService.emit_event)(
            event_type="benchmark.error",
            data={
                "task_id": task_id,
                "error": str(e)
            }
        )
        
        # Re-raise the exception
        raise
