# backend/apps/main/utils/db_connection_monitor.py
import logging
import inspect
import traceback
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from django.db import connections

logger = logging.getLogger(__name__)

class DBConnectionMonitor:
    """
    Utility class for monitoring database connections.
    
    This class provides methods to track, compare, and clean up database connections,
    helping to identify and fix connection leaks in tests and production code.
    """
    
    @staticmethod
    def get_connection_snapshot() -> Dict[str, Dict[str, Any]]:
        """
        Get a snapshot of all current database connections.
        
        Returns:
            dict: Dictionary of connection information keyed by connection alias
        """
        connection_snapshot = {}
        
        # Get all connections from Django's connection pool
        for alias in connections:
            conn = connections[alias]
            
            # Check if the connection is open
            is_open = conn.connection is not None and not conn.connection.closed
            
            # Get connection information
            connection_info = {
                "alias": alias,
                "is_open": is_open,
                "vendor": conn.vendor if hasattr(conn, 'vendor') else 'unknown',
                "connection_obj": conn,
                "source": DBConnectionMonitor.get_connection_source(conn) if is_open else None,
                "creation_time": getattr(conn, '_creation_time', None),
            }
            
            connection_snapshot[alias] = connection_info
        
        return connection_snapshot
    
    @staticmethod
    def get_connection_source(conn) -> Dict[str, Any]:
        """
        Get source information (file, line number) for a connection.
        
        Args:
            conn: Database connection object
            
        Returns:
            dict: Source information including file, line number, and stack trace
        """
        # Try to get stack trace from connection creation
        stack_trace = getattr(conn, '_stack_trace', None)
        
        # If no stored stack trace, get current stack
        if not stack_trace:
            stack_trace = traceback.extract_stack()
        
        # Find the first frame that's not in Django's db module
        source_frame = None
        for frame in reversed(stack_trace):
            if 'django/db' not in frame.filename and 'conftest.py' not in frame.filename:
                source_frame = frame
                break
        
        if source_frame:
            return {
                "file": source_frame.filename,
                "line": source_frame.lineno,
                "function": source_frame.name,
                "stack_trace": stack_trace
            }
        
        return {
            "file": "unknown",
            "line": 0,
            "function": "unknown",
            "stack_trace": stack_trace
        }
    
    @staticmethod
    def compare_snapshots(before: Dict[str, Dict[str, Any]], after: Dict[str, Dict[str, Any]]) -> Tuple[Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]]]:
        """
        Compare two connection snapshots and return new, closed, and leaked connections.
        
        Args:
            before: Connection snapshot from before
            after: Connection snapshot from after
            
        Returns:
            tuple: (new_connections, closed_connections, leaked_connections)
        """
        # Find new connections (in after but not in before)
        new_connection_aliases = set(after.keys()) - set(before.keys())
        new_connections = {alias: after[alias] for alias in new_connection_aliases}
        
        # Find connections that were open before but are now closed
        closed_connections = {}
        for alias in before:
            if alias in after:
                if before[alias]["is_open"] and not after[alias]["is_open"]:
                    closed_connections[alias] = before[alias]
        
        # Find leaked connections (open in both before and after)
        leaked_connections = {}
        for alias in before:
            if alias in after:
                if before[alias]["is_open"] and after[alias]["is_open"]:
                    leaked_connections[alias] = after[alias]
        
        return new_connections, closed_connections, leaked_connections
    
    @staticmethod
    def cleanup_leaked_connections(before: Dict[str, Dict[str, Any]], after: Dict[str, Dict[str, Any]]) -> int:
        """
        Close connections that were created during the test but not closed.
        
        Args:
            before: Connection snapshot from before the test
            after: Connection snapshot from after the test
            
        Returns:
            int: Number of connections closed
        """
        # Find leaked connections
        _, _, leaked_connections = DBConnectionMonitor.compare_snapshots(before, after)
        
        # Close leaked connections
        closed_count = 0
        for alias, conn_info in leaked_connections.items():
            conn = conn_info["connection_obj"]
            if conn.connection is not None and not conn.connection.closed:
                try:
                    logger.warning(f"Closing leaked connection: {alias}")
                    conn.close()
                    closed_count += 1
                except Exception as e:
                    logger.error(f"Error closing connection {alias}: {e}")
        
        return closed_count
    
    @staticmethod
    def add_timeout_to_connections() -> None:
        """
        Add timeout parameters to all database connections.
        """
        for alias in connections:
            conn = connections[alias]
            # For PostgreSQL
            if hasattr(conn, 'vendor') and conn.vendor == 'postgresql':
                # Set statement timeout to 30 seconds (30000 ms)
                if conn.connection is not None and not conn.connection.closed:
                    try:
                        with conn.cursor() as cursor:
                            cursor.execute("SET statement_timeout = 30000")
                        logger.info(f"Set statement_timeout for connection {alias}")
                    except Exception as e:
                        logger.error(f"Error setting statement_timeout for connection {alias}: {e}")
    
    @staticmethod
    def log_connection_statistics(initial_connections: Dict[str, Dict[str, Any]], final_connections: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Log statistics about database connections and return a summary.
        
        Args:
            initial_connections: Connection snapshot from before
            final_connections: Connection snapshot from after
            
        Returns:
            dict: Summary of connection statistics
        """
        # Compare snapshots
        new_connections, closed_connections, leaked_connections = DBConnectionMonitor.compare_snapshots(
            initial_connections, final_connections
        )
        
        # Calculate open connections
        open_count = sum(1 for c in final_connections.values() if c["is_open"])
        
        # Log connection statistics
        logger.info(
            f"DB connection monitor summary: {len(new_connections)} new, "
            f"{len(closed_connections)} closed, {len(leaked_connections)} leaked, "
            f"{open_count} total open"
        )
        
        # Log details about leaked connections
        if leaked_connections:
            logger.warning(f"Found {len(leaked_connections)} leaked database connections")
            for alias, conn_info in leaked_connections.items():
                source = conn_info.get("source", {})
                logger.warning(
                    f"Leaked connection: {alias} from {source.get('file', 'unknown')}:"
                    f"{source.get('line', 0)} in {source.get('function', 'unknown')}"
                )
        
        return {
            "new_connections": len(new_connections),
            "closed_connections": len(closed_connections),
            "leaked_connections": len(leaked_connections),
            "open_connections": open_count,
            "leaked_connection_details": [
                {
                    "alias": alias,
                    "source": conn_info.get("source", {})
                }
                for alias, conn_info in leaked_connections.items()
            ]
        }
