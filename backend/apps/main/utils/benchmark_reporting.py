# ACTIVE_FILE - 29-05-2025
import json
import html
from datetime import datetime
from typing import List, Dict, Any, Optional

def _format_duration(milliseconds: Optional[float]) -> str:
    """Formats duration in milliseconds to seconds string."""
    if milliseconds is None:
        return "N/A"
    return f"{milliseconds / 1000:.3f} s"

def _format_percentage(value: Optional[float]) -> str:
    """Formats a float (0.0-1.0) into a percentage string."""
    if value is None:
        return "N/A"
    return f"{value * 100:.1f}%"

def _generate_semantic_details_html(evaluations: Dict[str, Any]) -> str:
    """Generates HTML for the semantic_evaluations dictionary."""
    if not evaluations:
        return "<p>No detailed semantic evaluations available.</p>"

    details_html = "<h4>Detailed Semantic Evaluations:</h4>"
    for model_name, eval_data in evaluations.items():
        details_html += f"<h5>Evaluator: {html.escape(model_name)}</h5>"
        if eval_data.get('error'):
            details_html += f"<p><strong>Error during evaluation:</strong> {html.escape(str(eval_data.get('error_details', 'Unknown error')))}</p>"
            continue

        overall_score = eval_data.get('overall_score')
        overall_reasoning = eval_data.get('overall_reasoning', 'N/A')
        details_html += f"<p><strong>Overall Score:</strong> {overall_score if overall_score is not None else 'N/A'}<br>"
        details_html += f"<strong>Overall Reasoning:</strong> {html.escape(overall_reasoning)}</p>"

        dimensions = eval_data.get('dimensions')
        if dimensions:
            details_html += "<ul>"
            for dim_name, dim_data in dimensions.items():
                score = dim_data.get('score')
                reasoning = dim_data.get('reasoning', 'N/A')
                details_html += f"<li><strong>{html.escape(dim_name)}:</strong> Score: {score if score is not None else 'N/A'} ({html.escape(reasoning)})</li>"
            details_html += "</ul>"
        else:
            details_html += "<p>No dimensional breakdown provided.</p>"
    return details_html

def generate_html_report(results_data: List[Dict[str, Any]], output_path: str):
    """
    Generates an HTML report from a list of benchmark run results.

    Args:
        results_data: A list of dictionaries, where each dictionary represents
                      a BenchmarkRun record (or similar structure).
        output_path: The file path to save the generated HTML report.
    """
    report_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Benchmark Report</title>
    <style>
        body {{ font-family: sans-serif; line-height: 1.6; padding: 20px; }}
        h1, h2, h3, h4 {{ color: #333; }}
        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .summary-table th {{ min-width: 100px; }}
        .details-section {{ border: 1px solid #eee; padding: 15px; margin-bottom: 20px; background-color: #f9f9f9; }}
        .errors {{ color: red; }}
        pre {{ background-color: #eee; padding: 10px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word; }}
    </style>
</head>
<body>
    <h1>Benchmark Report</h1>
    <p>Generated on: {report_time}</p>

    <h2>Summary</h2>
    <table class="summary-table">
        <thead>
            <tr>
                <th>Scenario</th>
                <th>Agent Role</th>
                <th>Agent Version</th>
                <th>LLM</th>
                <th>Date</th>
                <th>Mean Duration</th>
                <th>Median Duration</th>
                <th>Success Rate</th>
                <th>Avg. Semantic Score</th>
            </tr>
        </thead>
        <tbody>
"""
    # Populate Summary Table
    for run in results_data:
        scenario = run.get('scenario', {})
        agent_def = run.get('agent_definition', {})
        html_content += f"""
            <tr>
                <td>{html.escape(scenario.get('name', 'N/A'))} v{scenario.get('version', 'N/A')}</td>
                <td>{html.escape(agent_def.get('role', 'N/A'))}</td>
                <td>{html.escape(run.get('agent_version', 'N/A')[:8])}</td>
                <td>{html.escape(run.get('llm_model', 'N/A'))}</td>
                <td>{run.get('execution_date', 'N/A')}</td>
                <td>{_format_duration(run.get('mean_duration'))}</td>
                <td>{_format_duration(run.get('median_duration'))}</td>
                <td>{_format_percentage(run.get('success_rate'))}</td>
                <td>{run.get('semantic_score', 'N/A')}</td>
            </tr>
"""
    html_content += """
        </tbody>
    </table>

    <h2>Run Details</h2>
"""
    # Populate Detailed Sections
    for i, run in enumerate(results_data):
        scenario = run.get('scenario', {})
        agent_def = run.get('agent_definition', {})
        run_id = run.get('id', f"run-{i+1}")
        html_content += f"""
    <div class="details-section">
        <h3>Run: {html.escape(scenario.get('name', 'N/A'))} v{scenario.get('version', 'N/A')} ({html.escape(agent_def.get('role', 'N/A'))})</h3>
        <p><strong>Run ID (DB):</strong> {run_id}<br>
           <strong>Agent Version:</strong> {html.escape(run.get('agent_version', 'N/A'))}<br>
           <strong>LLM Model:</strong> {html.escape(run.get('llm_model', 'N/A'))}<br>
           <strong>Execution Date:</strong> {run.get('execution_date', 'N/A')}</p>

        <h4>Performance Metrics</h4>
        <ul>
            <li>Runs Count: {run.get('runs_count', 'N/A')}</li>
            <li>Mean Duration: {_format_duration(run.get('mean_duration'))}</li>
            <li>Median Duration: {_format_duration(run.get('median_duration'))}</li>
            <li>Min Duration: {_format_duration(run.get('min_duration'))}</li>
            <li>Max Duration: {_format_duration(run.get('max_duration'))}</li>
            <li>Std Dev Duration: {_format_duration(run.get('std_dev'))}</li>
            <li>Success Rate: {_format_percentage(run.get('success_rate'))}</li>
        </ul>

        <h4>Operational Metrics</h4>
        <ul>
            <li>LLM Calls: {run.get('llm_calls', 'N/A')}</li>
            <li>Tool Calls: {run.get('tool_calls', 'N/A')}</li>
            <li>Memory Operations: {run.get('memory_operations', 'N/A')}</li>
        </ul>
"""
        tool_breakdown = run.get('tool_breakdown', {})
        if tool_breakdown:
            html_content += "<h5>Tool Breakdown:</h5><ul>"
            for tool, count in sorted(tool_breakdown.items()):
                html_content += f"<li>{html.escape(tool)}: {count}</li>"
            html_content += "</ul>"

        html_content += f"""
        <h4>Semantic Evaluation</h4>
        <p><strong>Overall Score (Primary):</strong> {run.get('semantic_score', 'N/A')}</p>
        <p><strong>Overall Reasoning (Primary):</strong></p>
        <pre>{html.escape(run.get('semantic_evaluation_details', {}).get('overall_reasoning', 'N/A'))}</pre>
        {_generate_semantic_details_html(run.get('semantic_evaluations', {}))}
"""
        # Raw Results (optional, could be large)
        # raw_results = run.get('raw_results', {})
        # if raw_results:
        #     html_content += f"""
        # <h4>Raw Results Snippet</h4>
        # <pre>{html.escape(json.dumps(raw_results, indent=2)[:1000])}...</pre>
        # """

        html_content += "</div>" # End details-section

    html_content += """
</body>
</html>
"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"HTML report generated successfully at: {output_path}")
    except IOError as e:
        print(f"Error writing HTML report to {output_path}: {e}")

# Example Usage (if run directly, for testing)
if __name__ == '__main__':
    # Example data structure similar to what BenchmarkRun might provide
    # (or what the run_benchmarks command collects before saving)
    example_results = [
        {
            "id": "uuid-1",
            "scenario": {"name": "Onboarding", "version": 1},
            "agent_definition": {"role": "Mentor"},
            "agent_version": "abc123def",
            "llm_model": "gpt-4",
            "execution_date": "2025-04-15T12:00:00Z",
            "runs_count": 10,
            "mean_duration": 1567.8,
            "median_duration": 1500.0,
            "min_duration": 1200.5,
            "max_duration": 2100.0,
            "std_dev": 250.1,
            "success_rate": 0.9,
            "llm_calls": 18,
            "tool_calls": 5,
            "memory_operations": 2,
            "tool_breakdown": {"get_user_profile": 3, "update_user_skill": 2},
            "semantic_score": 0.85,
            "semantic_evaluation_details": {"overall_reasoning": "The agent provided clear and concise onboarding steps."},
            "semantic_evaluations": {
                "gpt-4-eval": {
                    "overall_score": 0.85,
                    "overall_reasoning": "Good clarity.",
                    "dimensions": {
                        "Clarity": {"score": 0.9, "reasoning": "Very clear."},
                        "Completeness": {"score": 0.8, "reasoning": "Mostly complete."}
                    },
                    "error": False
                },
                 "claude-eval": {
                    "overall_score": None,
                    "overall_reasoning": None,
                    "dimensions": None,
                    "error": True,
                    "error_details": "API Timeout"
                }
            },
            "raw_results": {"details": "..."}
        }
        # Add more run dictionaries here if needed
    ]
    generate_html_report(example_results, "benchmark_report_example.html")
