from apps.main.llm.client import LLMClient
import json
from typing import Dict, List, Any, Optional, Union
import logging

logger = logging.getLogger(__name__)

from .response import ResponseType, LLMResponse, ToolCall
from .service import RealLLMClient

class AgentLLMExecutor:
    """Executor for running agent-specific LLM processing"""
    
    def __init__(self, agent_role, user_profile_id):
        self.agent_role = agent_role
        self.user_profile_id = user_profile_id
        self.llm_client = RealLLMClient()  # Use the abstraction instead of direct client
    
    async def execute(self, input_data: Dict[str, Any], 
                     system_instructions: str,
                     available_tools: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        Execute agent processing with LLM
        
        Args:
            input_data: Data to process
            system_instructions: System instructions for the agent
            available_tools: Tools available to this agent
            
        Returns:
            dict: Processed output from LLM
        """
        # Format messages for the LLM
        messages = [
            {"role": "system", "content": system_instructions}
        ]
        
        # Format input data as a user message
        input_message = self._format_input_message(input_data)
        messages.append({"role": "user", "content": input_message})
        
        # Format tools for the LLM if available
        tools_param = None
        if available_tools:
            tools_param = self._format_tools(available_tools)
        
        # Call the LLM and get standardized response
        llm_response = await self.llm_client.chat_completion(
            messages=messages,
            temperature=0.2,  # Lower temperature for agent behavior
            tools=tools_param
        )
        
        # Return the structured response directly for agents that can handle it
        return self._process_llm_response(llm_response)
    
    def _process_llm_response(self, llm_response: LLMResponse) -> Dict[str, Any]:
        """
        Process the LLMResponse into a format that can be used by agents
        while preserving the response type information.
        
        Args:
            llm_response: The structured LLM response
            
        Returns:
            dict: Processed response that maintains type information
        """
        # Process based on response type
        if llm_response.is_tool_call:
            # For tool calls, return a dictionary with the tool call information
            # but also include the original response for advanced processing
            tool_call = llm_response.get_first_tool_call()
            
            return {
                "response_type": "tool_call",
                "tool_call": {
                    "name": tool_call.tool_name,
                    "input": tool_call.tool_input,
                    "id": tool_call.id
                },
                "content": llm_response.content,
                "_original_response": llm_response  # Include the original for agents that can use it
            }
        elif llm_response.is_text:
            try:
                # Try to parse as JSON if it's a text response
                if llm_response.content and (
                    llm_response.content.strip().startswith('{') and 
                    llm_response.content.strip().endswith('}')):
                    parsed_json = json.loads(llm_response.content)
                    # Add response type information
                    if isinstance(parsed_json, dict):
                        parsed_json["response_type"] = "json"
                        parsed_json["_original_response"] = llm_response
                        return parsed_json
                
                # Check for JSON in markdown
                if llm_response.content and "```json" in llm_response.content and "```" in llm_response.content:
                    json_str = llm_response.content.split("```json")[1].split("```")[0].strip()
                    parsed_json = json.loads(json_str)
                    # Add response type information
                    if isinstance(parsed_json, dict):
                        parsed_json["response_type"] = "json"
                        parsed_json["_original_response"] = llm_response
                        return parsed_json
                
                # Return as text content if can't parse as JSON
                return {
                    "response_type": "text",
                    "content": llm_response.content,
                    "_original_response": llm_response
                }
            except Exception as e:
                logger.debug(f"Error parsing JSON from LLM response: {str(e)}")
                return {
                    "response_type": "text",
                    "content": llm_response.content,
                    "_original_response": llm_response
                }
        else:
            # Handle empty response case
            logger.error("Received empty response from LLM")
            return {
                "response_type": "error",
                "error": "Empty response from LLM"
            }
    
    def _format_input_message(self, input_data: Dict[str, Any]) -> str:
        """Format input data as a message for the LLM"""
        return (
            f"Process the following input data for the {self.agent_role} agent:\n\n"
            f"```json\n{json.dumps(input_data, indent=2)}\n```\n\n"
            "Please analyze this data according to your role and provide a structured response."
        )
    
    def _format_tools(self, tools: List[Dict]) -> List[Dict]:
        """Format tools for the LLM API"""
        formatted_tools = []
        
        for tool in tools:
            formatted_tool = {
                "type": "function",
                "function": {
                    "name": tool["code"],
                    "description": tool["description"],
                    "parameters": tool["input_schema"]
                }
            }
            formatted_tools.append(formatted_tool)
            
        return formatted_tools