from enum import Enum
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

class ResponseType(Enum):
    TEXT = "text"
    TOOL_CALL = "tool_call"
    ERROR = "error"

@dataclass
class ToolCall:
    """Represents a single tool call request from the LLM"""
    tool_name: str
    tool_input: Dict[str, Any]
    id: Optional[str] = None

@dataclass
class LLMResponse:
    """Standardized response object for any LLM provider"""
    response_type: ResponseType
    content: Optional[str] = None
    tool_calls: Optional[List[ToolCall]] = None
    input_tokens: Optional[int] = None # Added for token tracking
    output_tokens: Optional[int] = None # Added for token tracking
    raw_response: Optional[Any] = None  # Store original response for debugging

    @property
    def is_tool_call(self) -> bool:
        return self.response_type == ResponseType.TOOL_CALL and self.tool_calls
        
    @property
    def is_text(self) -> bool:
        return self.response_type == ResponseType.TEXT
        
    def get_first_tool_call(self) -> Optional[ToolCall]:
        """Convenience method to get the first tool call when only one is expected"""
        if self.is_tool_call and self.tool_calls:
            return self.tool_calls[0]
        return None
