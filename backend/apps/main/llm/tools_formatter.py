"""
Tool formatting utilities for LLM API compatibility.

This module provides utilities to convert between internal tool representations
and the format expected by external LLM APIs.
"""
from typing import Dict, Any, List, Optional


def convert_tool_to_llm_format(
    tool_code: str,
    description: str,
    input_schema: Dict[str, Any],
    output_schema: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Convert a tool definition to the format expected by LLM APIs.
    
    Args:
        tool_code: The unique identifier for the tool
        description: A description of what the tool does
        input_schema: JSON schema defining the tool's input parameters
        output_schema: Optional JSON schema defining the tool's output format
        
    Returns:
        Dict containing the tool in LLM-compatible format
    """
    return {
        "type": "function",
        "function": {
            "name": tool_code,
            "description": description,
            "parameters": input_schema
        }
    }


def convert_tools_for_llm(tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Convert a list of internal tool definitions to LLM API format.
    
    Args:
        tools: List of internal tool definitions with 'code', 'description', and 'input_schema'
        
    Returns:
        List of tools in LLM-compatible format
    """
    converted_tools = []
    
    for tool in tools:
        # Get required fields with sensible defaults
        tool_code = tool.get('code', '')
        description = tool.get('description', f'Tool for {tool_code}')
        input_schema = tool.get('input_schema', {"type": "object", "properties": {}})
        
        # Skip tools without a code
        if not tool_code:
            continue
            
        # Convert to LLM format
        llm_tool = convert_tool_to_llm_format(
            tool_code=tool_code,
            description=description,
            input_schema=input_schema
        )
        
        converted_tools.append(llm_tool)
    
    return converted_tools


def parse_tool_call_from_llm(tool_call: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse a tool call response from an LLM into a standardized internal format.
    
    Args:
        tool_call: The tool call data from the LLM response
        
    Returns:
        Dict with 'tool_name' and 'tool_input' fields
    """
    # Handle various LLM API response formats
    if 'function' in tool_call:
        # OpenAI/Mistral format
        function_data = tool_call.get('function', {})
        return {
            'tool_name': function_data.get('name', ''),
            'tool_input': function_data.get('arguments', {})
        }
    elif 'name' in tool_call and 'input' in tool_call:
        # Anthropic format
        return {
            'tool_name': tool_call.get('name', ''),
            'tool_input': tool_call.get('input', {})
        }
    else:
        # Unknown format - try to extract common fields
        return {
            'tool_name': tool_call.get('name', tool_call.get('tool_name', '')),
            'tool_input': tool_call.get('input', tool_call.get('arguments', tool_call.get('tool_input', {})))
        }