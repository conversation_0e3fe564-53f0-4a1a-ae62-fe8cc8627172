from import_export import resources
from .models import LLMConfig

class LLMConfigResource(resources.ModelResource):
    class Meta:
        model = LLMConfig
        fields = (
            'id',
            'name',
            'model_name',
            'temperature',
            'input_token_price',
            'output_token_price',
            'is_evaluation',
            'is_default',
        )
        # Optional: Specify fields to use for identifying existing records
        # import_id_fields = ('name',)
        # Ensure all fields are included for import/export consistency
        skip_unchanged = True
        report_skipped = False # Report skipped rows for debugging
        use_transactions = True

    # No custom import_data override
