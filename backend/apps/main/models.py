
# ACTIVE_FILE - 29-05-2025

import logging
from django.db import models
import uuid
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType

###############################################################################
# Agents Package
###############################################################################



from django.db import models
import uuid
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.serializers.json import DjangoJSONEncoder
import jsonschema
from django.core.exceptions import ValidationError

logger = logging.getLogger(__name__)

class LLMConfig(models.Model):
    """
    Stores configuration parameters for a specific Large Language Model setup.
    Allows reusing LLM settings across different parts of the system like
    Generic Agents and Benchmark Runs.
    """
    id = models.AutoField(primary_key=True)
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Unique identifier for this LLM configuration (e.g., 'gpt-4-turbo-standard', 'claude-3-opus-creative')."
    )
    model_name = models.CharField(
        max_length=100,
        help_text="The identifier of the LLM model used (e.g., 'gpt-4-1106-preview', 'claude-3-opus-20240229')."
    )
    temperature = models.FloatField(
        null=True,
        blank=True,
        help_text="The temperature setting for the LLM (controls randomness). Null means use default."
    )
    input_token_price = models.DecimalField(
        max_digits=10,
        decimal_places=6,
        null=True,
        blank=True,
        help_text="Price per (million) input token for this LLM (for cost estimation)."
    )
    output_token_price = models.DecimalField(
        max_digits=10,
        decimal_places=6,
        null=True,
        blank=True,
        help_text="Price per (million) output token for this LLM (for cost estimation)."
    )
    is_evaluation = models.BooleanField(
        default=False,
        help_text="Indicates if this LLM configuration is for evaluation purposes."
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_default = models.BooleanField(
        default=False,
        help_text="If True, this configuration will be used as the default when no specific configuration is provided."
    )

    class Meta:
        verbose_name = "LLM Configuration"
        verbose_name_plural = "LLM Configurations"
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['model_name']),
            models.Index(fields=['is_default']),
            models.Index(fields=['is_evaluation']), # Add index for the new field
        ]
        constraints = [
            # Ensure only one default for non-evaluation configs
            models.UniqueConstraint(
                fields=['is_evaluation'], # Check uniqueness based on this field...
                condition=models.Q(is_default=True, is_evaluation=False), # ...only for rows matching this condition
                name='unique_default_non_evaluation_config'
            ),
            # Ensure only one default for evaluation configs
            models.UniqueConstraint(
                fields=['is_evaluation'], # Check uniqueness based on this field...
                condition=models.Q(is_default=True, is_evaluation=True), # ...only for rows matching this condition
                name='unique_default_evaluation_config'
            ),
        ]

    def __str__(self):
        status = []
        if self.is_default:
            status.append("Default")
        if self.is_evaluation:
            status.append("Evaluation")
        status_str = f" ({', '.join(status)})" if status else ""
        return f"{self.name}{status_str}"

    def save(self, *args, **kwargs):
        """
        Ensure only one default exists per evaluation status (True/False).
        """
        if self.is_default:
            # Unset other defaults *with the same evaluation status* before saving this one.
            LLMConfig.objects.filter(
                is_default=True,
                is_evaluation=self.is_evaluation # Scope by evaluation status
            ).exclude(pk=self.pk).update(is_default=False)
        super().save(*args, **kwargs)

    @classmethod
    def get_default(cls, evaluation: bool = False):
        """
        Returns the default LLMConfig instance for the specified evaluation status.
        Defaults to finding the non-evaluation default (evaluation=False).
        Returns None if no default is set for that status.
        """
        try:
            return cls.objects.get(is_default=True, is_evaluation=evaluation)
        except cls.DoesNotExist:
            return None
        except cls.MultipleObjectsReturned:
            # This shouldn't happen due to the constraints, but log if it does.
            logger.error(
                f"Multiple default LLMConfigs found for evaluation={evaluation}. "
                f"Database constraints may not be applied correctly."
            )
            # Fallback: return the first one found
            return cls.objects.filter(is_default=True, is_evaluation=evaluation).first()


class AgentRole(models.TextChoices):
    """
    Defines the specific roles for agents in the Game of Life system.
    Each role corresponds to specific responsibilities and permissions.
    """
    MENTOR = 'mentor', 'Mentor Agent'
    ORCHESTRATOR = 'orchestrator', 'Orchestrator Agent'
    RESOURCE = 'resource', 'Resource & Capacity Management Agent'
    ENGAGEMENT = 'engagement', 'Engagement & Pattern Analytics Agent'
    PSYCHOLOGICAL = 'psychological', 'Psychological Monitoring Agent'
    STRATEGY = 'strategy', 'Strategy Agent'
    ACTIVITY = 'activity', 'Wheel/Activity Agent'
    ETHICAL = 'ethical', 'Ethical Oversight Agent'
    DISPATCHER = 'dispatcher', "Dispatcher Agent"


class AgentTool(models.Model):
    """
    Represents a tool that agents can utilize to interact with the system.
    Each tool provides specific capabilities and access patterns.
    """
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=50, unique=True, help_text="Unique code for this tool (e.g., 'user_traits_query')")
    name = models.CharField(max_length=255, help_text="Human-readable name for this tool")
    description = models.TextField(help_text="Detailed description of what this tool does")

    # Tool specifications
    input_schema = models.JSONField(help_text="JSON Schema defining expected input format")
    output_schema = models.JSONField(help_text="JSON Schema defining expected output format")
    function_path = models.CharField(
        max_length=255,
        help_text="Fully qualified path to the tool function (e.g., 'apps.main.agents.tools.user_traits_handler')"
    )
    is_active = models.BooleanField(default=True, help_text="Whether this tool is available for use")

    # Access control
    allowed_agent_roles = models.JSONField(
        default=list,
        help_text="List of agent roles (from AgentRole) that can use this tool"
    )

    # Performance metrics
    avg_response_time = models.FloatField(default=0.0, help_text="Average response time in seconds")
    usage_count = models.IntegerField(default=0, help_text="Total number of times this tool has been used")
    error_rate = models.FloatField(default=0.0, help_text="Percentage of tool invocations that resulted in errors")
    definition_hash = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        help_text="SHA-256 hash of the tool's definition (name, desc, path, schemas) for idempotency checks"
    )

    class Meta:
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['definition_hash']), # Index the new hash field
        ]
        verbose_name = "Agent Tool"
        verbose_name_plural = "Agent Tools"

    def __str__(self):
        return self.name

    def validate_input(self, input_data):
        """Validates input against the input schema"""
        try:
            jsonschema.validate(instance=input_data, schema=self.input_schema)
            return True, None
        except jsonschema.exceptions.ValidationError as e:
            return False, str(e)

    def to_dict(self):
        """Returns a dictionary representation of the tool for agent consumption"""
        return {
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'input_schema': self.input_schema,
            'output_schema': self.output_schema,
        }

class BenchmarkMetric(models.Model):
    """
    Defines metrics for benchmarking agent performance.
    These metrics are used to evaluate the quality and efficiency of agents.
    """
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=50, unique=True, help_text="Unique code for this metric")
    name = models.CharField(max_length=255, help_text="Human-readable name for this metric")
    description = models.TextField(help_text="Description of what this metric measures")

    # Metric specifications
    data_type = models.CharField(
        max_length=20,
        choices=[
            ('numeric', 'Numeric'),
            ('boolean', 'Boolean'),
            ('categorical', 'Categorical'),
            ('duration', 'Duration'),
        ],
        help_text="Type of data this metric records"
    )

    # Measurement properties
    unit = models.CharField(max_length=50, blank=True, null=True, help_text="Unit of measurement (e.g., 'seconds', 'percent')")
    target_value = models.JSONField(null=True, blank=True, help_text="Target or ideal value for this metric")

    # For categorical metrics
    valid_values = models.JSONField(null=True, blank=True, help_text="For categorical metrics, the list of valid values")

    # Role applicability
    applicable_roles = models.JSONField(
        default=list,
        help_text="List of agent roles this metric applies to"
    )

    class Meta:
        indexes = [
            models.Index(fields=['code']),
        ]
        verbose_name = "Benchmark Metric"
        verbose_name_plural = "Benchmark Metrics"

    def __str__(self):
        return self.name

    def validate_value(self, value):
        """Validates that a metric value is appropriate for this metric's type"""
        if self.data_type == 'numeric':
            return isinstance(value, (int, float))
        elif self.data_type == 'boolean':
            return isinstance(value, bool)
        elif self.data_type == 'categorical':
            return value in self.valid_values
        elif self.data_type == 'duration':
            return isinstance(value, (int, float)) and value >= 0
        return False


class GenericAgent(models.Model):
    """
    Defines a blueprint for a specific agent role in the system.
    This model stores the core configuration and capabilities of each agent type.
    """
    id = models.AutoField(primary_key=True)

    # Basic identification
    role = models.CharField(
        max_length=20,
        choices=AgentRole.choices,
        unique=True,
        help_text="The specific role this agent fulfills in the system"
    )
    version = models.CharField(
        max_length=20,
        default="1.0.0",
        help_text="Semantic version of this agent definition"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this agent definition is currently active"
    )

    # Agent configuration
    description = models.TextField(
        help_text="Comprehensive description of this agent's purpose and responsibilities"
    )
    system_instructions = models.TextField(
        help_text="Base instructions defining the agent's core behavior and limitations"
    )

    # Schema definitions (using JSON Schema format)
    input_schema = models.JSONField(
        help_text="JSON Schema defining the expected input format for this agent"
    )
    output_schema = models.JSONField(
        help_text="JSON Schema defining the required output format from this agent"
    )
    state_schema = models.JSONField(
        help_text="JSON Schema defining the structure of this agent's state in Langgraph",
        default=dict
    )
    memory_schema = models.JSONField(
        help_text="JSON Schema defining the structure of this agent's persistent memory",
        default=dict
    )

    # Capabilities
    available_tools = models.ManyToManyField(
        AgentTool,
        related_name="available_to_agents",
        help_text="Tools this agent can utilize"
    )

    # Data access permissions
    read_models = models.JSONField(
        default=list,
        help_text="List of models this agent has read access to"
    )
    write_models = models.JSONField(
        default=list,
        help_text="List of models this agent has write access to"
    )
    recommend_models = models.JSONField(
        default=list,
        help_text="List of models this agent can create recommendations for"
    )

    # Benchmarking
    benchmark_metrics = models.ManyToManyField(
        BenchmarkMetric,
        related_name="measured_on_agents",
        help_text="Metrics used to benchmark this agent's performance"
    )

    # LLM Configuration
    llm_config = models.ForeignKey(
        LLMConfig,
        on_delete=models.PROTECT, # Changed from SET_NULL to prevent deletion if used
        null=True, # Changed from True
        blank=True, # Changed from True
        related_name="generic_agents", # Changed related_name for clarity
        help_text="The LLM configuration to use for this agent role. Cannot be null."
    )

    # Implementation details
    langgraph_node_class = models.CharField(
        max_length=255,
        help_text="Fully qualified class name for this agent's Langgraph node implementation"
    )
    processing_timeout = models.IntegerField(
        default=30,
        help_text="Maximum allowed processing time in seconds before timeout"
    )

    # For tracking purposes
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['role']),
            models.Index(fields=['is_active']),
        ]
        verbose_name = "Generic Agent"
        verbose_name_plural = "Generic Agents"

    def __str__(self):
        return f"{self.get_role_display()} v{self.version}"

    def validate_input(self, input_data):
        """Validates input against the input schema"""
        try:
            jsonschema.validate(instance=input_data, schema=self.input_schema)
            return True, None
        except jsonschema.exceptions.ValidationError as e:
            return False, str(e)

    def validate_output(self, output_data):
        """Validates output against the output schema"""
        try:
            jsonschema.validate(instance=output_data, schema=self.output_schema)
            return True, None
        except jsonschema.exceptions.ValidationError as e:
            return False, str(e)

    def get_available_tools_dict(self):
        """Returns a list of available tools in dictionary format"""
        return [tool.to_dict() for tool in self.available_tools.filter(is_active=True)]

    def get_benchmark_metrics(self):
        """Returns the benchmark metrics configured for this agent"""
        return list(self.benchmark_metrics.all().values('code', 'name', 'data_type', 'unit'))

class AgentRun(models.Model):
    """
    Records a specific execution of an agent with all inputs, outputs, and performance metrics.
    This provides full traceability and benchmarking capabilities.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Run metadata
    agent = models.ForeignKey(
        GenericAgent,
        on_delete=models.PROTECT,
        related_name="runs",
        help_text="The agent that was executed"
    )
    workflow_id = models.UUIDField(
        help_text="Identifier for the broader workflow this run is part of"
    )
    user_profile = models.ForeignKey(
        'user.UserProfile',
        on_delete=models.CASCADE,
        related_name="agent_runs",
        help_text="The user this agent run was performed for"
    )

    # Timing information
    started_at = models.DateTimeField(help_text="When this agent run started")
    completed_at = models.DateTimeField(null=True, blank=True, help_text="When this agent run completed")

    # Run details
    input_data = models.JSONField(
        encoder=DjangoJSONEncoder,
        help_text="The input data provided to the agent"
    )
    output_data = models.JSONField(
        encoder=DjangoJSONEncoder,
        null=True,
        blank=True,
        help_text="The output data produced by the agent"
    )
    initial_state = models.JSONField(
        encoder=DjangoJSONEncoder,
        help_text="The initial state passed to the agent"
    )
    final_state = models.JSONField(
        encoder=DjangoJSONEncoder,
        null=True,
        blank=True,
        help_text="The final state after agent execution"
    )
    memory_updates = models.JSONField(
        encoder=DjangoJSONEncoder,
        null=True,
        blank=True,
        help_text="Changes made to the agent's persistent memory"
    )

    # Execution status
    status = models.CharField(
        max_length=20,
        choices=[
            ('running', 'Running'),
            ('completed', 'Completed'),
            ('failed', 'Failed'),
            ('timeout', 'Timeout'),
        ],
        default='running',
        help_text="Current status of this agent run"
    )
    error_message = models.TextField(
        null=True,
        blank=True,
        help_text="Error message if this run failed"
    )

    # Tool usage
    tool_calls = models.JSONField(
        encoder=DjangoJSONEncoder,
        default=list,
        help_text="Records of all tool calls made during this run"
    )

    class Meta:
        indexes = [
            models.Index(fields=['workflow_id']),
            models.Index(fields=['agent']),
            models.Index(fields=['user_profile']),
            models.Index(fields=['status']),
            models.Index(fields=['started_at']),
        ]
        verbose_name = "Agent Run"
        verbose_name_plural = "Agent Runs"

    def __str__(self):
        return f"{self.agent.get_role_display()} Run {self.id}"

    @property
    def duration(self):
        """Returns the duration of this run in seconds, or None if not completed"""
        if not self.completed_at:
            return None
        duration = self.completed_at - self.started_at
        return duration.total_seconds()

    def add_tool_call(self, tool_code, input_data, output_data, start_time, end_time, error=None):
        """Adds a record of a tool call to this run"""
        tool_call = {
            'tool_code': tool_code,
            'input': input_data,
            'output': output_data,
            'started_at': start_time.isoformat(),
            'completed_at': end_time.isoformat(),
            'duration_seconds': (end_time - start_time).total_seconds(),
            'error': error
        }
        if not self.tool_calls:
            self.tool_calls = []
        self.tool_calls.append(tool_call)
        self.save(update_fields=['tool_calls'])


class AgentMetric(models.Model):
    """
    Records benchmark metric measurements for specific agent runs.
    This enables performance tracking and quality assessment.
    """
    id = models.AutoField(primary_key=True)

    # Relationships
    agent_run = models.ForeignKey(
        AgentRun,
        on_delete=models.CASCADE,
        related_name="metrics",
        help_text="The agent run this metric is for"
    )
    metric = models.ForeignKey(
        BenchmarkMetric,
        on_delete=models.PROTECT,
        related_name="measurements",
        help_text="The metric being measured"
    )

    # Measurement
    value = models.JSONField(
        encoder=DjangoJSONEncoder,
        help_text="The measured value (could be number, boolean, string, etc.)"
    )

    # Context
    context = models.JSONField(
        encoder=DjangoJSONEncoder,
        default=dict,
        blank=True,
        help_text="Additional context about this measurement"
    )

    # Metadata
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['agent_run']),
            models.Index(fields=['metric']),
            models.Index(fields=['timestamp']),
        ]
        verbose_name = "Agent Metric"
        verbose_name_plural = "Agent Metrics"

    def __str__(self):
        return f"{self.metric.name} for {self.agent_run.id}"

    def clean(self):
        """Validates that the value is appropriate for this metric's type"""
        if not self.metric.validate_value(self.value):
            raise ValidationError({
                'value': f'Value not valid for metric type {self.metric.data_type}'
            })


class AgentMemory(models.Model):
    """
    Stores persistent memory for a specific agent role.
    This enables agents to maintain state across different runs and workflows.
    """
    id = models.AutoField(primary_key=True)

    # Identification
    agent_role = models.CharField(
        max_length=20,
        choices=AgentRole.choices,
        help_text="The agent role this memory belongs to"
    )
    user_profile = models.ForeignKey(
        'user.UserProfile',
        on_delete=models.CASCADE,
        related_name="agent_memories",
        help_text="The user this memory is associated with"
    )
    memory_key = models.CharField(
        max_length=255,
        help_text="Key identifying this specific memory item"
    )

    # Memory content
    content = models.JSONField(
        encoder=DjangoJSONEncoder,
        help_text="The actual memory content stored as JSON"
    )

    # Metadata
    confidence = models.FloatField(
        default=1.0,
        help_text="Confidence score (0.0-1.0) for this memory"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Memory lifecycle
    last_accessed = models.DateTimeField(null=True, blank=True)
    access_count = models.IntegerField(default=0)
    expires_at = models.DateTimeField(null=True, blank=True)

    # For tracking
    created_by_run = models.ForeignKey(
        AgentRun,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_memories",
        help_text="The agent run that created this memory"
    )

    class Meta:
        indexes = [
            models.Index(fields=['agent_role', 'user_profile', 'memory_key']),
            models.Index(fields=['user_profile']),
            models.Index(fields=['created_at']),
            models.Index(fields=['updated_at']),
            models.Index(fields=['expires_at']),
        ]
        verbose_name = "Agent Memory"
        verbose_name_plural = "Agent Memories"
        unique_together = ['agent_role', 'user_profile', 'memory_key']

    def __str__(self):
        return f"{self.memory_key} for {self.get_agent_role_display()}/{self.user_profile.profile_name}"

    def access(self):
        """Records an access to this memory item"""
        from django.utils import timezone
        self.last_accessed = timezone.datetime.now()
        self.access_count += 1
        self.save(update_fields=['last_accessed', 'access_count'])
        return self.content

    def update_content(self, new_content, confidence=None):
        """Updates the content of this memory"""
        from django.utils import timezone
        self.content = new_content
        if confidence is not None:
            self.confidence = max(0.0, min(1.0, confidence))
        self.updated_at = timezone.datetime.now()
        self.save(update_fields=['content', 'confidence', 'updated_at'])


class AgentRecommendation(models.Model):
    """
    Stores recommendations made by agents for data model updates.
    These are reviewed and applied by the Orchestrator Agent.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Source information
    created_by_run = models.ForeignKey(
        AgentRun,
        on_delete=models.CASCADE,
        related_name="recommendations",
        help_text="The agent run that created this recommendation"
    )

    # Target information
    target_model = models.CharField(
        max_length=255,
        help_text="The model this recommendation applies to (e.g., 'user.UserTraitInclination')"
    )
    target_instance_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="ID of the specific instance to update, or null for new instances"
    )

    # Recommendation details
    operation = models.CharField(
        max_length=20,
        choices=[
            ('create', 'Create'),
            ('update', 'Update'),
            ('delete', 'Delete'),
        ],
        help_text="The operation to perform"
    )
    field_updates = models.JSONField(
        encoder=DjangoJSONEncoder,
        help_text="For create/update operations, the field values to set"
    )

    # Justification
    rationale = models.TextField(
        help_text="Explanation for why this recommendation is being made"
    )
    evidence = models.JSONField(
        encoder=DjangoJSONEncoder,
        help_text="Supporting evidence for this recommendation"
    )
    confidence = models.FloatField(
        default=0.8,
        help_text="Confidence score (0.0-1.0) for this recommendation"
    )

    # Workflow
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected'),
            ('applied', 'Applied'),
        ],
        default='pending',
        help_text="Current status of this recommendation"
    )
    reviewed_by = models.ForeignKey(
        AgentRun,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="reviewed_recommendations",
        help_text="The agent run that reviewed this recommendation"
    )
    applied_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    priority = models.IntegerField(
        default=5,
        help_text="Priority from 1 (highest) to 10 (lowest)"
    )

    class Meta:
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['created_by_run']),
            models.Index(fields=['target_model']),
            models.Index(fields=['priority']),
            models.Index(fields=['created_at']),
        ]
        verbose_name = "Agent Recommendation"
        verbose_name_plural = "Agent Recommendations"

    def __str__(self):
        op_map = {'create': 'Create', 'update': 'Update', 'delete': 'Delete'}
        target = f"{self.target_instance_id}" if self.target_instance_id else "new instance"
        return f"{op_map.get(self.operation, self.operation)} {self.target_model} {target}"


###############################################################################
# Benchmarking Package
###############################################################################

class BenchmarkTag(models.Model):
    """A tag for categorizing BenchmarkScenarios."""
    name = models.CharField(max_length=50, unique=True, help_text="Unique name for the tag (e.g., 'feature-x', 'regression', 'performance').")
    description = models.TextField(blank=True, help_text="Optional description for the tag.")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Benchmark Tag"
        verbose_name_plural = "Benchmark Tags"
        ordering = ['name']

    def __str__(self):
        return self.name

class BenchmarkScenario(models.Model):
    """Defines a reusable scenario for benchmarking agents."""
    name = models.CharField(max_length=100)
    description = models.TextField()
    agent_role = models.CharField(max_length=50) # Consider using AgentRole choices here later
    input_data = models.JSONField()
    metadata = models.JSONField(default=dict, help_text="Expected output, evaluation criteria (dictionary mapping dimension names to criteria lists), etc.")
    tags = models.ManyToManyField(
        BenchmarkTag,
        blank=True,
        related_name="scenarios",
        help_text="Tags for organizing and filtering scenarios."
    )
    is_active = models.BooleanField(default=True)
    version = models.PositiveIntegerField(default=1, help_text="Version number for this scenario definition.")
    parent_scenario = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='child_scenarios',
        help_text="Link to the previous version of this scenario, if any."
    )
    is_latest = models.BooleanField(default=True, help_text="Indicates if this is the latest version of the scenario.")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    variations = models.ManyToManyField(
        'self',
        blank=True,
        symmetrical=False,
        related_name='base_scenario_set', # Scenarios that this one is a variation of
        help_text="Link to other scenarios that this one is considered a variation of."
    )

    class Meta:
        verbose_name = "Benchmark Scenario"
        verbose_name_plural = "Benchmark Scenarios"
        unique_together = ('name', 'version') # Ensure unique version per name
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['agent_role']),
            models.Index(fields=['is_active']),
            models.Index(fields=['version']),
            models.Index(fields=['is_latest']),
        ]

    def __str__(self):
        return f"{self.name} v{self.version} ({self.agent_role})"

class BenchmarkRun(models.Model):
    """Records a specific benchmark execution."""
    scenario = models.ForeignKey(BenchmarkScenario, on_delete=models.CASCADE, related_name="runs")
    agent_definition = models.ForeignKey('GenericAgent', on_delete=models.CASCADE, related_name="benchmark_runs")
    agent_version = models.CharField(max_length=40, help_text="Git hash or version identifier")
    # llm_model = models.CharField(max_length=100) # Deprecated, use llm_config
    execution_date = models.DateTimeField(auto_now_add=True)
    parameters = models.JSONField(default=dict, help_text="Benchmark parameters used for this run")

    # LLM Configuration used for the AGENT during this run
    llm_config = models.ForeignKey(
        LLMConfig,
        on_delete=models.SET_NULL, # Keep the run record even if config is deleted
        null=True,
        blank=True, # Allow runs before this field existed
        related_name="benchmark_runs",
        help_text="The LLM configuration used by the agent during this benchmark run."
    )

    # LLM model used for semantic evaluation
    evaluator_llm_model = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="The identifier of the LLM model used for semantic evaluation."
    )

    # Performance metrics
    runs_count = models.IntegerField(help_text="Number of times the scenario was executed")
    mean_duration = models.FloatField(help_text="Mean execution time in milliseconds")
    median_duration = models.FloatField(help_text="Median execution time in milliseconds")
    min_duration = models.FloatField(help_text="Minimum execution time in milliseconds")
    max_duration = models.FloatField(help_text="Maximum execution time in milliseconds")
    std_dev = models.FloatField(help_text="Standard deviation of execution time in milliseconds")
    success_rate = models.FloatField(help_text="Percentage of successful runs (0.0 to 1.0)")

    # Operational metrics
    llm_calls = models.IntegerField(default=0, help_text="Total number of LLM calls during the benchmark")
    tool_calls = models.IntegerField(default=0, help_text="Total number of tool calls during the benchmark")
    tool_breakdown = models.JSONField(default=dict, help_text="Count of calls per tool code")
    memory_operations = models.IntegerField(default=0, help_text="Total number of memory operations")

    # Semantic metrics (if applicable)
    semantic_score = models.FloatField(null=True, blank=True, help_text="Overall semantic quality score (0.0 to 1.0) from the primary evaluator, potentially averaged across dimensions.")
    semantic_evaluation_details = models.JSONField(default=dict, help_text="Detailed overall semantic evaluation reasoning from the primary evaluator LLM.")
    semantic_evaluations = models.JSONField(
        default=dict,
        help_text="Stores multi-dimensional evaluation results from multiple LLMs. Maps model name to {'dimensions': {'dimension_name': {'score': float, 'reasoning': str}}, 'overall_score': float, 'overall_reasoning': str, 'error': bool}."
    )

    # Raw data for detailed analysis
    raw_results = models.JSONField(default=dict, help_text="Raw results from the underlying benchmark tool")

    # LLM Configuration used for the AGENT during this run (REMOVED FIELDS)
    # agent_llm_model_name = models.CharField(...) # REMOVED
    # llm_temperature = models.FloatField(...) # REMOVED
    # llm_input_token_price = models.DecimalField(...) # REMOVED
    # llm_output_token_price = models.DecimalField(...) # REMOVED

    # Token Usage & Cost
    total_input_tokens = models.IntegerField(
        null=True,
        blank=True,
        help_text="Total number of input tokens processed by the LLM during the benchmark."
    )
    total_output_tokens = models.IntegerField(
        null=True,
        blank=True,
        help_text="Total number of output tokens generated by the LLM during the benchmark."
    )
    estimated_cost = models.DecimalField(
        max_digits=10,
        decimal_places=6,
        null=True,
        blank=True,
        help_text="Estimated cost of the LLM usage for this benchmark run based on token counts and prices."
    )

    # Statistical comparison fields
    compared_to_run = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='comparisons',
        help_text="Reference to the previous run used for statistical comparison."
    )
    performance_p_value = models.FloatField(
        null=True,
        blank=True,
        help_text="P-value from statistical test comparing performance (e.g., duration) to the previous run."
    )
    is_performance_significant = models.BooleanField(
        null=True,
        blank=True,
        help_text="Indicates if the performance difference compared to the previous run is statistically significant (e.g., p < 0.05)."
    )

    # Stage-level performance (aggregated)
    stage_performance_details = models.JSONField(
        default=dict,
        blank=True,
        help_text="Aggregated performance metrics (mean, median, std_dev, etc. in ms) for each profiled stage."
    )

    # Added field for last response length
    last_response_length = models.IntegerField(
        null=True,
        blank=True,
        help_text="Length of the agent's last response in characters, if available."
    )

    class Meta:
        verbose_name = "Benchmark Run"
        verbose_name_plural = "Benchmark Runs"
        ordering = ['-execution_date']
        indexes = [
            models.Index(fields=['scenario']),
            models.Index(fields=['agent_definition']),
            models.Index(fields=['execution_date']),
            # models.Index(fields=['llm_model']), # Deprecated
            models.Index(fields=['llm_config']), # New index
        ]

    def __str__(self):
        return f"Benchmark: {self.scenario.name} - {self.execution_date.strftime('%Y-%m-%d %H:%M')}"

    # Consider adding a property to easily access llm details via llm_config if needed
    @property
    def agent_llm_model_name(self):
        return self.llm_config.model_name if self.llm_config else None

    @property
    def llm_temperature(self):
        return self.llm_config.temperature if self.llm_config else None

    @property
    def llm_input_token_price(self):
        return self.llm_config.input_token_price if self.llm_config else None

    @property
    def llm_output_token_price(self):
        return self.llm_config.output_token_price if self.llm_config else None

    @property
    def total_tokens(self):
        """Calculate total tokens from input and output tokens."""
        input_tokens = self.total_input_tokens or 0
        output_tokens = self.total_output_tokens or 0
        return input_tokens + output_tokens

    @property
    def token_usage_display(self):
        """Get a human-readable token usage string with k formatting for thousands."""
        input_tokens = self.total_input_tokens or 0
        output_tokens = self.total_output_tokens or 0
        total = input_tokens + output_tokens

        def format_tokens(count):
            """Format token count with 'k' for thousands."""
            if count >= 1000:
                return f"{count/1000:.1f}k"
            return str(count)

        if total == 0:
            return "0"
        elif input_tokens > 0 and output_tokens > 0:
            return f"{format_tokens(input_tokens)}+{format_tokens(output_tokens)}={format_tokens(total)}"
        else:
            return format_tokens(total)


class EvaluationCriteriaTemplate(models.Model):
    """
    Stores reusable templates for evaluating benchmark results with contextual awareness.

    Supports evaluation criteria that vary based on contextual variables like:
    - Trust level (0-100)
    - User mood (valence: -1.0 to 1.0, arousal: -1.0 to 1.0)
    - Environment (stress_level: 0-100, time_pressure: 0-100)
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Unique name for this evaluation template (e.g., 'Clarity and Conciseness', 'Safety Check')."
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of what this template evaluates."
    )
    workflow_type = models.CharField(
        max_length=50,
        blank=True,
        help_text="Specific workflow type this template is designed for (empty for all types)."
    )
    category = models.CharField(
        max_length=20,
        choices=[
            ('semantic', 'Semantic Analysis'),
            ('quality', 'Quality Assessment'),
            ('phase', 'Phase-based Evaluation'),
            ('contextual', 'Contextual Evaluation'),
            ('custom', 'Custom Evaluation'),
        ],
        default='quality',
        help_text="Category of evaluation this template performs."
    )
    criteria = models.JSONField(
        default=dict,
        help_text="Base evaluation criteria structure (e.g., dimension names to criteria lists)."
    )
    contextual_criteria = models.JSONField(
        default=dict,
        help_text="Contextual evaluation criteria that vary based on variables like trust_level, mood, environment."
    )
    variable_ranges = models.JSONField(
        default=dict,
        help_text="Defines the ranges for contextual variables this template supports."
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this template is active and available for use."
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Evaluation Criteria Template"
        verbose_name_plural = "Evaluation Criteria Templates"
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['workflow_type']),
            models.Index(fields=['category']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name

    def get_criteria_for_context(self, context: dict) -> dict:
        """
        Get evaluation criteria adapted for the given context.

        Args:
            context: Dictionary containing contextual variables like:
                - trust_level: int (0-100)
                - mood: dict with valence (-1.0 to 1.0) and arousal (-1.0 to 1.0)
                - environment: dict with stress_level (0-100) and time_pressure (0-100)

        Returns:
            dict: Adapted evaluation criteria for the context
        """
        # Start with base criteria
        adapted_criteria = self.criteria.copy()

        # Apply contextual adaptations if available
        if self.contextual_criteria and context:
            trust_level = context.get('trust_level', 50)
            mood = context.get('mood', {})
            environment = context.get('environment', {})

            # Apply trust level adaptations
            if 'trust_level' in self.contextual_criteria:
                trust_adaptations = self._get_range_adaptation(
                    self.contextual_criteria['trust_level'],
                    trust_level
                )
                adapted_criteria = self._merge_criteria(adapted_criteria, trust_adaptations)

            # Apply mood adaptations
            if 'mood' in self.contextual_criteria and mood:
                valence = mood.get('valence', 0.0)
                arousal = mood.get('arousal', 0.0)

                if 'valence' in self.contextual_criteria['mood']:
                    valence_adaptations = self._get_range_adaptation(
                        self.contextual_criteria['mood']['valence'],
                        valence
                    )
                    adapted_criteria = self._merge_criteria(adapted_criteria, valence_adaptations)

                if 'arousal' in self.contextual_criteria['mood']:
                    arousal_adaptations = self._get_range_adaptation(
                        self.contextual_criteria['mood']['arousal'],
                        arousal
                    )
                    adapted_criteria = self._merge_criteria(adapted_criteria, arousal_adaptations)

            # Apply environment adaptations
            if 'environment' in self.contextual_criteria and environment:
                stress_level = environment.get('stress_level', 0)
                time_pressure = environment.get('time_pressure', 0)

                if 'stress_level' in self.contextual_criteria['environment']:
                    stress_adaptations = self._get_range_adaptation(
                        self.contextual_criteria['environment']['stress_level'],
                        stress_level
                    )
                    adapted_criteria = self._merge_criteria(adapted_criteria, stress_adaptations)

                if 'time_pressure' in self.contextual_criteria['environment']:
                    pressure_adaptations = self._get_range_adaptation(
                        self.contextual_criteria['environment']['time_pressure'],
                        time_pressure
                    )
                    adapted_criteria = self._merge_criteria(adapted_criteria, pressure_adaptations)

        return adapted_criteria

    def _get_range_adaptation(self, range_criteria: dict, value: float) -> dict:
        """
        Get criteria adaptation for a specific value within defined ranges.

        Args:
            range_criteria: Dictionary mapping ranges to criteria adaptations
            value: The current value to find adaptations for

        Returns:
            dict: Criteria adaptations for the value
        """
        # Type safety check: ensure value is numeric
        if not isinstance(value, (int, float)):
            logger.warning(f"Expected numeric value for range adaptation, got {type(value)}: {value}")
            return {}

        for range_key, adaptations in range_criteria.items():
            if self._value_in_range(value, range_key):
                return adaptations
        return {}

    def _value_in_range(self, value: float, range_str: str) -> bool:
        """
        Check if a value falls within a specified range string.

        Range formats supported:
        - "0-39": value >= 0 and value <= 39
        - "40-69": value >= 40 and value <= 69
        - "70-100": value >= 70 and value <= 100
        - "-1.0-0.0": value >= -1.0 and value <= 0.0
        - "0.0-1.0": value >= 0.0 and value <= 1.0
        """
        try:
            # Type safety check: ensure value is numeric
            if not isinstance(value, (int, float)):
                logger.warning(f"Expected numeric value for range check, got {type(value)}: {value}")
                return False

            if '-' not in range_str:
                return False

            # Handle negative ranges like "-1.0-0.0"
            if range_str.startswith('-'):
                # Find the second dash
                parts = range_str[1:].split('-', 1)
                if len(parts) != 2:
                    return False
                min_val = -float(parts[0])
                max_val = float(parts[1])
            else:
                parts = range_str.split('-')
                if len(parts) != 2:
                    return False
                min_val = float(parts[0])
                max_val = float(parts[1])

            return min_val <= value <= max_val
        except (ValueError, IndexError):
            return False
        except TypeError as e:
            logger.warning(f"Type error in range comparison: {e}. Value: {value}, Range: {range_str}")
            return False

    def _merge_criteria(self, base_criteria: dict, adaptations: dict) -> dict:
        """
        Merge adaptation criteria into base criteria.

        Args:
            base_criteria: Base evaluation criteria
            adaptations: Contextual adaptations to apply

        Returns:
            dict: Merged criteria
        """
        merged = base_criteria.copy()

        for dimension, criteria_list in adaptations.items():
            if dimension in merged:
                # Extend existing criteria
                if isinstance(merged[dimension], list) and isinstance(criteria_list, list):
                    merged[dimension].extend(criteria_list)
                else:
                    merged[dimension] = criteria_list
            else:
                # Add new dimension
                merged[dimension] = criteria_list

        return merged


class CustomAgent(models.Model):
    """
    Represents an AI agent in the system based on a generic agent.

    A specialized AI agent derived from a generic agent with specific customization.

    Attributes:
        generic_agent (ForeignKey): The base template for this custom agent
        user_profile (ForeignKey): The specific user this agent is created for
        name (CharField): Unique name for this agent instance
        description (TextField): Detailed description of the agent's role and characteristics
        instruction (TextField): Personalized instructions for the agent based on user context
    """
    generic_agent = models.ForeignKey(GenericAgent, on_delete=models.CASCADE, related_name="custom_agents")
    user_profile = models.ForeignKey(
        'user.UserProfile',
        on_delete=models.CASCADE,
        related_name="custom_agents"
    )
    name = models.CharField(max_length=255, help_text="User-specific name of the customized agent.")
    description = models.TextField(help_text="Tailored description of this agent instance's responsibilities for a specific user.")
    instruction = models.TextField(help_text="Personalized instructions for the agent based on user profile and interaction history.")

    class Meta:
        ordering = ['name']
        unique_together = ['user_profile', 'generic_agent']  # Ensure one custom agent per user per generic agent type

    def __str__(self):
        return f"{self.name} for {self.user_profile.profile_name}"


class AgentGoal(models.Model):
    """
    Represents goals pursued by an agent for a user.

    Stores objectives assigned to an agent to track progress.

    Attributes:
        id (BigAutoField): Unique identifier for the goal.
        custom_agent (ForeignKey):
        description (TextField):
            Example: "Expand Wilhelm's comfort zone by introducing one social activity per week."
        priority (IntegerField):
            Example: 2 (Medium priority, allowing for higher prioritization of physical activities)
        user_goal_id (UUIDField):
    """
    id = models.BigAutoField(primary_key=True)
    custom_agent = models.ForeignKey(CustomAgent, on_delete=models.CASCADE, related_name="agent_goals")
    description = models.TextField(help_text="Specific description of what the agent aims to achieve for the user.")
    priority = models.IntegerField(help_text="Numerical priority ranking (1-5, with 1 being highest) to help agent prioritize competing goals.")
    user_goal_id = models.UUIDField(help_text="Reference to the corresponding user goal this agent goal supports.")

    class Meta:
        ordering = ['id']

    def __str__(self):
        return f"Goal: {self.description[:30]}"





###############################################################################
# Memory Package
###############################################################################

class HistoryEvent(models.Model):
    timestamp = models.DateTimeField(auto_now_add=True)
    event_type = models.CharField(max_length=100)

    # Entity that triggered the event
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.CharField(max_length=255)
    related_object = GenericForeignKey('content_type', 'object_id')

    # Optional secondary entity
    secondary_content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='secondary_events'
    )
    secondary_object_id = models.CharField(max_length=255, null=True, blank=True)
    secondary_object = GenericForeignKey('secondary_content_type', 'secondary_object_id')

    # Details
    details = models.JSONField(default=dict)
    user_profile = models.ForeignKey('user.UserProfile', on_delete=models.CASCADE, null=True)

    class Meta:
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['secondary_content_type', 'secondary_object_id']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['event_type']),
        ]

class UserFeedback(models.Model):
    feedback_type = models.CharField(max_length=255)

    # Entity the feedback relates to
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.CharField(max_length=255)
    related_object = GenericForeignKey('content_type', 'object_id')

    created_on = models.DateTimeField(auto_now_add=True)
    user_profile = models.ForeignKey('user.UserProfile', on_delete=models.CASCADE)
    user_comment = models.TextField()
    criticality = models.IntegerField()
    context_data = models.JSONField(default=dict)
    slack_payload = models.JSONField(default=dict)


###############################################################################
# Game Mechanics Package
###############################################################################

class Wheel(models.Model):
    """
    Represents the spinning mechanism for activity selection.

    This model defines a wheel used for selecting activities in a gamified
    environment. Each wheel can have multiple items with probability weights.

    Attributes:
        id (CharField): Unique identifier for the wheel.
        name (CharField):
            Example: "Philipp's Daily Challenge Wheel - Foundation Phase"
        created_by (CharField):
            Example: "agent_wheel_specialist_1"
        created_at (DateField):
    """
    name = models.CharField(
        max_length=255,
        help_text="Descriptive name of the wheel that may include user name and phase information."
    )
    created_by = models.CharField(
        max_length=255,
        help_text="Identifier for the agent that generated this wheel."
    )
    created_at = models.DateField(
        help_text="Date when the wheel was created for the user."
    )

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class WheelItem(models.Model):
    """
    Represents a segment within the wheel corresponding to a tailored activity.

    Each WheelItem is part of a Wheel and has a probability weight that determines
    its likelihood of being selected. It is linked to a specific tailored activity.

    Attributes:
        id (CharField): Unique identifier for the wheel item.
        wheel (ForeignKey):
        percentage (FloatField):
            Example: 15.5 (Creative activities given higher probability to match Philipp's preferences)
        activity_tailored (OneToOneField):
    """
    id = models.CharField(
        primary_key=True,
        max_length=50,
        help_text="Unique identifier for this specific segment of the wheel."
    )
    wheel = models.ForeignKey(
        Wheel,
        on_delete=models.CASCADE,
        related_name="items"
    )
    percentage = models.FloatField(
        help_text="Probability weight between 0.0 and 100.0 that determines likelihood of selection based on user traits and trust level."
    )
    activity_tailored = models.OneToOneField(
        "activity.ActivityTailored",
        on_delete=models.CASCADE,
        related_name="wheel_item",
        help_text="Reference to the customized activity that will be assigned if this wheel segment is selected."
    )

    class Meta:
        ordering = ['id']

    def __str__(self):
        return f"WheelItem {self.id} for {self.wheel.name}"


###############################################################################
# Seeding Command Tracking
###############################################################################

class AppliedSeedingCommand(models.Model):
    """
    Tracks which seeding management commands have been successfully applied.
    This prevents re-running seeding commands unnecessarily, e.g., during container restarts.
    """
    command_name = models.CharField(
        max_length=255,
        unique=True,
        help_text="Name of the management command that was applied (e.g., 'seed_db_40_envs')."
    )
    applied_at = models.DateTimeField(
        auto_now_add=True,
        help_text="Timestamp when the command was successfully applied."
    )

    class Meta:
        verbose_name = "Applied Seeding Command"
        verbose_name_plural = "Applied Seeding Commands"
        indexes = [
            models.Index(fields=['command_name']),
        ]

    def __str__(self):
        return f"{self.command_name} applied at {self.applied_at.strftime('%Y-%m-%d %H:%M:%S')}"
