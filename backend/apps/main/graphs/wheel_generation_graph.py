# backend/apps/main/graphs/wheel_generation_graph.py

from typing import Any, Dict, Optional, List, Literal
import uuid
import logging
from pydantic import BaseModel, Field
from langgraph.graph import END, StateGraph

# Import agent nodes
from apps.main.agents.orchestrator_agent import OrchestratorAgent
from apps.main.agents.resource_agent import ResourceAgent
from apps.main.agents.engagement_agent import EngagementAndPatternAgent
from apps.main.agents.psy_agent import PsychologicalMonitoringAgent
from apps.main.agents.strategy_agent import StrategyAgent
from apps.main.agents.wheel_activity_agent import WheelAndActivityAgent
from apps.main.agents.ethical_agent import EthicalAgent
from apps.main.agents.error_handler import <PERSON>rrorHandlerAgent

# Set up a dedicated logger for the wheel generation workflow
logger = logging.getLogger(__name__)

# Define the possible workflow stages based on the flow documentation
WorkflowStage = Literal[
    "orchestration_initial",
    "resource_assessment",
    "engagement_analysis",
    "psychological_assessment", 
    "strategy_formulation",
    "activity_selection",
    "ethical_validation",
    "orchestration_final",
    "error_handling",
    "workflow_complete"
]

class WheelGenerationState(BaseModel):
    """
    State model for the wheel generation workflow.
    Tracks data flow between agents and current workflow stage.
    """
    # Workflow identification
    workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_profile_id: str
    user_ws_session_name: Optional[str] = None
    
    # Input/context data
    context_packet: Dict[str, Any] = Field(default_factory=dict)
    
    # Agent outputs
    resource_context: Optional[Dict[str, Any]] = None
    engagement_analysis: Optional[Dict[str, Any]] = None
    psychological_assessment: Optional[Dict[str, Any]] = None
    strategy_framework: Optional[Dict[str, Any]] = None
    wheel: Optional[Dict[str, Any]] = None
    ethical_validation: Optional[Dict[str, Any]] = None
    
    # Output data from the most recent agent (for communication between agents)
    output_data: Dict[str, Any] = Field(default_factory=dict)
    
    # Routing information
    next_agent: Optional[str] = None
    
    # Workflow state tracking
    current_stage: WorkflowStage = "orchestration_initial"
    last_agent: Optional[str] = None
    error: Optional[str] = None
    error_context: Optional[Dict[str, Any]] = None
    completed: bool = False


def create_wheel_generation_graph(user_profile_id: str) -> StateGraph:
    """
    Create a LangGraph workflow for wheel generation based on the defined flow document.
    
    This implements the multi-agent workflow where:
    1. Orchestrator Agent coordinates specialized agents
    2. Resource & Capacity Agent analyzes user resources/environment
    3. Engagement & Pattern Agent analyzes user history
    4. Psychological Monitoring Agent assesses user psychological state
    5. Strategy Agent synthesizes inputs into a strategy
    6. Wheel/Activity Agent selects activities and builds the wheel
    7. Ethical Oversight Agent validates the wheel
    8. Orchestrator Agent handles final integration
    
    Args:
        user_profile_id: The ID of the user profile this workflow is for
        
    Returns:
        StateGraph: The configured wheel generation workflow graph
    """
    # Enable logging immediately
    logger.info(f"Creating wheel generation graph for user {user_profile_id}")
    
    # Initialize the graph with the state model
    workflow = StateGraph(WheelGenerationState)
    
    # Add all agent nodes to the graph
    workflow.add_node("orchestrator", OrchestratorAgent(user_profile_id))
    workflow.add_node("resource", ResourceAgent(user_profile_id))
    workflow.add_node("engagement", EngagementAndPatternAgent(user_profile_id))
    workflow.add_node("psychological", PsychologicalMonitoringAgent(user_profile_id))
    workflow.add_node("strategy", StrategyAgent(user_profile_id))
    workflow.add_node("activity", WheelAndActivityAgent(user_profile_id))
    workflow.add_node("ethical", EthicalAgent(user_profile_id))
    workflow.add_node("error_handler", ErrorHandlerAgent(user_profile_id))
    
    # Define routing logic for each node
    
    # 1. Orchestrator Agent: Workflow coordination
    def route_from_orchestrator(state: WheelGenerationState):
        """Route from the Orchestrator Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Orchestrator → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")
        
        # Check for errors first
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            return "error_handler"

        # Prioritize explicit routing decision from the orchestrator agent's output
        next_agent_from_output = state.output_data.get("next_agent")
        
        if next_agent_from_output:
            logger.debug(f"🧭 Orchestrator specified next_agent: '{next_agent_from_output}'")
            if next_agent_from_output == "end":
                logger.debug(f"🏁 Workflow completion signaled by orchestrator. Ending workflow.")
                state.completed = True
                state.current_stage = "workflow_complete"
                return END
            else:
                # Map agent name to the corresponding stage for state tracking
                stage_map = {
                    "resource": "resource_assessment",
                    "engagement": "engagement_analysis",
                    "psychological": "psychological_assessment",
                    "strategy": "strategy_formulation",
                    "activity": "activity_selection",
                    "ethical": "ethical_validation",
                    "orchestrator": "orchestration_final" # Handle routing back to orchestrator
                }
                if next_agent_from_output in stage_map:
                    logger.debug(f"✅ Setting current_stage to: '{stage_map[next_agent_from_output]}'")
                    state.current_stage = stage_map[next_agent_from_output]
                    logger.debug(f"🔄 ROUTING DECISION: Orchestrator → {next_agent_from_output}")
                    return next_agent_from_output
                else:
                    # If next_agent is unknown, treat as error
                    error_msg = f"Orchestrator specified unknown next_agent: {next_agent_from_output}"
                    logger.error(f"❌ {error_msg}")
                    state.error = error_msg
                    state.current_stage = "error_handling"
                    logger.debug(f"🔄 ROUTING DECISION: Orchestrator → error_handler (unknown agent)")
                    return "error_handler"

        # Fallback logic (should ideally not be needed if orchestrator always provides next_agent)
        # This covers the initial call where output_data might be empty before the first mock runs
        if state.current_stage == "orchestration_initial":
             # On the very first entry, route to resource
             logger.debug(f"🧪 Initial orchestration detected. Default routing to resource agent.")
             state.current_stage = "resource_assessment"
             logger.debug(f"🔄 ROUTING DECISION: Orchestrator → resource (initial default)")
             return "resource"

        # If no explicit next_agent and not initial stage, consider it an error
        error_msg = f"Orchestrator did not specify next_agent. Current stage: {state.current_stage}, Last agent: {state.last_agent}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Orchestrator → error_handler (missing next_agent)")
        return "error_handler"
    
    # 2. Resource & Capacity Agent
    def route_from_resource(state: WheelGenerationState):
        """Route from the Resource Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Resource → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")
        
        # Check for errors
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            logger.debug(f"🔄 ROUTING DECISION: Resource → error_handler (error in state)")
            return "error_handler"
            
        # After resource assessment, go to engagement analysis
        if (state.current_stage == "resource_assessment" and 
            state.last_agent == "resource" and 
            state.resource_context):
            # According to the flow, engagement analysis happens next
            logger.debug(f"✅ Resource context available. Proceeding to engagement analysis.")
            state.current_stage = "engagement_analysis"  # This ensures engagement agent gets the correct stage
            logger.debug(f"🔄 ROUTING DECISION: Resource → engagement")
            return "engagement"
            
        # Error case - unexpected state
        error_msg = f"Unexpected state in resource routing: {state.current_stage}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Resource → error_handler (unexpected state)")
        return "error_handler"
    
    # 3. Engagement & Pattern Analytics Agent
    def route_from_engagement(state: WheelGenerationState):
        """Route from the Engagement Agent to the next agent in the flow."""
        # Check for errors
        if state.error:
            state.current_stage = "error_handling"
            return "error_handler"
            
        # Simple and elegant routing condition
        if (state.current_stage == "engagement_analysis" and 
            state.last_agent == "engagement" and 
            state.engagement_analysis):
            state.current_stage = "psychological_assessment"
            return "psychological"
            
        # Error case - unexpected state
        state.error = f"Unexpected state in engagement routing: {state.current_stage}"
        state.current_stage = "error_handling"
        return "error_handler"
    
    # 4. Psychological Monitoring Agent
    def route_from_psychological(state: WheelGenerationState):
        """Route from the Psychological Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Psychological → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")
        
        # Check for errors
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            logger.debug(f"🔄 ROUTING DECISION: Psychological → error_handler (error in state)")
            return "error_handler"
            
        # After psychological assessment, go to strategy formulation
        if (state.current_stage == "psychological_assessment" and 
            state.last_agent == "psychological" and 
            state.psychological_assessment):
            logger.debug(f"✅ Psychological assessment available. Proceeding to strategy formulation.")
            state.current_stage = "strategy_formulation"
            logger.debug(f"🔄 ROUTING DECISION: Psychological → strategy")
            return "strategy"
            
        # Error case - unexpected state
        error_msg = f"Unexpected state in psychological routing: {state.current_stage}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Psychological → error_handler (unexpected state)")
        return "error_handler"
    
    # 5. Strategy Agent
    def route_from_strategy(state: WheelGenerationState):
        """Route from the Strategy Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Strategy → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")
        
        # Check for errors
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            logger.debug(f"🔄 ROUTING DECISION: Strategy → error_handler (error in state)")
            return "error_handler"
            
        # After strategy formulation, go to activity selection
        if (state.current_stage == "strategy_formulation" and 
            state.last_agent == "strategy" and 
            state.strategy_framework):
            logger.debug(f"✅ Strategy framework available. Proceeding to activity selection.")
            state.current_stage = "activity_selection"
            logger.debug(f"🔄 ROUTING DECISION: Strategy → activity")
            return "activity"
            
        # Error case - unexpected state
        error_msg = f"Unexpected state in strategy routing: {state.current_stage}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Strategy → error_handler (unexpected state)")
        return "error_handler"
    
    # 6. Wheel/Activity Agent
    def route_from_activity(state: WheelGenerationState):
        """Route from the Activity Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Activity → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")
        
        # Check for errors
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            logger.debug(f"🔄 ROUTING DECISION: Activity → error_handler (error in state)")
            return "error_handler"
            
        # After activity selection, go to ethical validation
        if (state.current_stage == "activity_selection" and 
            state.last_agent == "activity" and 
            state.wheel):
            logger.debug(f"✅ Wheel data available. Proceeding to ethical validation.")
            state.current_stage = "ethical_validation"
            logger.debug(f"🔄 ROUTING DECISION: Activity → ethical")
            return "ethical"
            
        # Error case - unexpected state
        error_msg = f"Unexpected state in activity routing: {state.current_stage}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Activity → error_handler (unexpected state)")
        return "error_handler"
    
    # 7. Ethical Oversight Agent
    def route_from_ethical(state: WheelGenerationState):
        """Route from the Ethical Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Ethical → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")
        
        # Check for errors
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            logger.debug(f"🔄 ROUTING DECISION: Ethical → error_handler (error in state)")
            return "error_handler"
            
        # After ethical validation, go back to orchestrator for final integration
        if (state.current_stage == "ethical_validation" and 
            state.last_agent == "ethical" and 
            state.ethical_validation):
            logger.debug(f"✅ Ethical validation available. Proceeding to final orchestration.")
            state.current_stage = "orchestration_final"
            logger.debug(f"🔄 ROUTING DECISION: Ethical → orchestrator (final)")
            return "orchestrator"
            
        # Error case - unexpected state
        error_msg = f"Unexpected state in ethical routing: {state.current_stage}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Ethical → error_handler (unexpected state)")
        return "error_handler"
    
    # 8. Error Handler
    def route_from_error_handler(state: WheelGenerationState):
        """Route from the Error Handler to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Error Handler → ? [Stage: {state.current_stage}, Error: {state.error and 'Yes' or 'No'}]")
        
        # If the error handler output indicates recovery is possible
        recovery_agent = None

        # If the state already shows we're coming from error_handler, we may be in a loop
        if state.last_agent == "error_handler" and state.current_stage == "error_handling":
            logger.warning("Potential loop detected! Ending workflow.")
            state.completed = True
            state.output_data["user_response"] = "I apologize, but I'm having trouble recovering from an error."
            return END
        
        if "next_agent" in state.output_data:
            recovery_agent = state.output_data["next_agent"]
            logger.debug(f"✅ Error handler specified next_agent: '{recovery_agent}'")
        elif state.output_data.get("error_handled", False):
            # If error is handled but no specific next agent, use current stage to determine
            stage_to_agent = {
                "orchestration_initial": "orchestrator",
                "resource_assessment": "resource",
                "engagement_analysis": "engagement",
                "psychological_assessment": "psychological", 
                "strategy_formulation": "strategy",
                "activity_selection": "activity",
                "ethical_validation": "ethical",
                "orchestration_final": "orchestrator"
            }
            recovery_agent = stage_to_agent.get(state.current_stage)
            logger.debug(f"ℹ️ Error handled, determining recovery agent from stage '{state.current_stage}': '{recovery_agent}'")
        
        # If we have a recovery target, proceed there
        if recovery_agent and recovery_agent != "end":
            # Clear the error state since we're recovering
            logger.debug(f"✅ Error recovery possible. Clearing error state and routing to '{recovery_agent}'")
            state.error = None
            state.error_context = None
            
            # Clear next_agent to avoid conflicts
            state.next_agent = None
            
            logger.debug(f"🔄 ROUTING DECISION: Error Handler → {recovery_agent} (recovery)")
            return recovery_agent
        
        # Cannot recover, end the workflow
        if recovery_agent == "end":
            logger.debug(f"🏁 Error handler signaled workflow end.")
        else:
            logger.error(f"❌ Cannot recover from error, no valid recovery agent determined.")
            
        state.error = state.error or "Unrecoverable error"
        state.completed = True
        state.current_stage = "workflow_complete"
        logger.debug(f"🔄 ROUTING DECISION: Error Handler → END (unrecoverable)")
        return END
    
    # Set up conditional edges for all agents
    workflow.add_conditional_edges(
        "orchestrator",
        route_from_orchestrator,
        {
            "resource": "resource",
            "engagement": "engagement",
            "psychological": "psychological",
            "strategy": "strategy",
            "activity": "activity",
            "ethical": "ethical",
            "error_handler": "error_handler",
            END: END
        }
    )
    
    workflow.add_conditional_edges(
        "resource",
        route_from_resource,
        {
            "engagement": "engagement",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "engagement",
        route_from_engagement,
        {
            "psychological": "psychological",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "psychological",
        route_from_psychological,
        {
            "strategy": "strategy",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "strategy",
        route_from_strategy,
        {
            "activity": "activity",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "activity",
        route_from_activity,
        {
            "ethical": "ethical",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "ethical",
        route_from_ethical,
        {
            "orchestrator": "orchestrator",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "error_handler",
        route_from_error_handler,
        {
            "orchestrator": "orchestrator",
            "resource": "resource",
            "engagement": "engagement",
            "psychological": "psychological",
            "strategy": "strategy",
            "activity": "activity",
            "ethical": "ethical",
            END: END
        }
    )
    
    # Set the entry point to the Orchestrator Agent, which handles initial routing
    workflow.set_entry_point("orchestrator")
    
    return workflow


async def run_wheel_generation_workflow(user_profile_id: str, context_packet: Dict[str, Any], 
                                      workflow_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Run the wheel generation workflow for a specific user.
    
    Args:
        user_profile_id: The ID of the user profile
        context_packet: Initial context information from the user
        workflow_id: Optional workflow ID (will generate a new one if not provided)
        
    Returns:
        Dict[str, Any]: Final state and output from the workflow
    """
    # Create the workflow
    workflow = create_wheel_generation_graph(user_profile_id)
    
    # Compile the workflow
    app = workflow.compile()
    
    # Set up the initial state
    if not workflow_id:
        workflow_id = str(uuid.uuid4())
        
    # Create initial state with user details and context
    initial_state = WheelGenerationState(
        workflow_id=workflow_id,
        user_profile_id=user_profile_id,
        context_packet=context_packet,
        user_ws_session_name=context_packet.get('user_ws_session_name')
    )
    
    # Log workflow initiation
    logger.info(f"🚀 Starting wheel generation workflow {workflow_id} for user {user_profile_id}")
    
    try:
        # Execute the workflow
        result = await app.ainvoke(initial_state)
        logger.info(f"✅ Completed wheel generation workflow {workflow_id}")
        
        # Format output data for the response
        output_data = {}
        
        # Include the wheel in output_data if available
        if hasattr(result, 'wheel') and result.wheel:
            output_data["wheel"] = result.wheel
            logger.debug(f"📊 Wheel included in output with {len(result.wheel.get('items', []))} items")
            
        # Include user_response from output_data if available
        if hasattr(result, 'output_data') and isinstance(result.output_data, dict):
            if 'user_response' in result.output_data:
                output_data["user_response"] = result.output_data['user_response']
                logger.debug(f"💬 User response included in output: {output_data['user_response'][:50]}...")
                
        # Create the workflow result
        workflow_result = {
            "workflow_id": workflow_id,
            "user_profile_id": user_profile_id,
            "completed": getattr(result, 'completed', True),
            "output_data": output_data,
            "session_timestamp": context_packet.get('session_timestamp'),
            "user_ws_session_name": context_packet.get('user_ws_session_name'),
            "workflow_type": "wheel_generation"  # Explicitly include for result handling
        }
        
        return workflow_result
            
    except Exception as e:
        logger.error(f"❌ Error in wheel generation workflow {workflow_id}: {str(e)}", exc_info=True)
        # Return error state
        return {
            "error": str(e),
            "workflow_id": workflow_id,
            "completed": False,
            "user_profile_id": user_profile_id,
            "user_ws_session_name": context_packet.get('user_ws_session_name'),
            "workflow_type": "wheel_generation",
            "output_data": {
                "user_response": "I encountered an issue while processing your request. Please try again."
            }
        }