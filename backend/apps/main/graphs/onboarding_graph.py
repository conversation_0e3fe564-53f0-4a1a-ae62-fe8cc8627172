# backend/apps/main/agents/nodes/onboarding_graph.py

from typing import Any, Dict, Optional
import uuid
from pydantic import BaseModel, <PERSON>
from langgraph.graph import END, StateGraph

from apps.main.agents.mentor_agent import MentorAgent
from apps.main.agents.error_handler import <PERSON>rror<PERSON><PERSON>ler<PERSON>gent


def create_onboarding_graph(user_profile_id):
    """
    Create a simple LangGraph workflow for the onboarding process.
    
    This minimal graph involves only the Mentor agent and handles the 
    initial user interaction for onboarding.
    
    Args:
        user_profile_id: The ID of the user profile this workflow is for
        
    Returns:
        StateGraph: The configured onboarding workflow graph
    """
    # Define the workflow state structure
    class OnboardingState(BaseModel):
        workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
        user_input: Optional[Dict[str, Any]] = None
        current_context: Dict[str, Any] = Field(default_factory=dict)
        processed_responses: Dict[str, Any] = Field(default_factory=dict)
        last_agent: Optional[str] = None
        error: Optional[str] = None
        completed: bool = False
        onboarding_stage: str = "initial"  # Tracks which part of onboarding we're in
    
    # Create the graph
    workflow = StateGraph(OnboardingState)
    
    # Add agent nodes - for onboarding we only need mentor and error handler
    workflow.add_node("mentor", MentorAgent(user_profile_id))
    workflow.add_node("error_handler", ErrorHandlerAgent(user_profile_id))
    
    # Define the error handler routing
    def handle_error(state):
        state.error = f"Error in agent: {state.last_agent}"
        return state
    
    # Add error handling to the mentor node
    workflow.add_edge_with_handler("mentor", "error_handler", handle_error)
    
    # Define the mentor's routing logic
    def route_from_mentor(state: OnboardingState):
        # Check for error
        if state.error:
            return "error_handler"
        
        # Check if we're done with onboarding
        if state.onboarding_stage == "completed":
            state.completed = True
            return END
        
        # For simplicity in this initial version, we'll just have the mentor
        # handle all stages of onboarding by looping back to itself
        return "mentor"
    
    # Add conditional edges from mentor
    workflow.add_conditional_edges(
        "mentor",
        route_from_mentor,
        {
            "mentor": "mentor",  # Loop back for multi-step onboarding
            "error_handler": "error_handler",
            END: END
        }
    )
    
    # Add edge from error handler back to mentor for recovery
    workflow.add_edge("error_handler", "mentor")
    
    # Set the entry point
    workflow.set_entry_point("mentor")
    
    return workflow


async def run_onboarding_workflow(user_profile_id, initial_input):
    """
    Run the onboarding workflow with the provided user input.
    
    Args:
        user_profile_id: The ID of the user profile
        initial_input: The initial input from the user
        
    Returns:
        dict: The final state and output
    """
    # Create the workflow
    workflow = create_onboarding_graph(user_profile_id)
    
    # Compile the workflow
    app = workflow.compile()
    
    # Run the workflow
    result = await app.ainvoke({
        "input": initial_input,
        "state": {"workflow_id": str(uuid.uuid4()), "onboarding_stage": "initial"}
    })
    
    return result
