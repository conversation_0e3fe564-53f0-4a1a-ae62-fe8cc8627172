# backend/apps/main/graphs/discussion_graph.py

from typing import Dict, Any, List, Optional, Tuple
import uuid
import logging
# Removed BaseModel, Field, Literal imports as they are now in state_models
from langgraph.graph import END, StateGraph
from apps.main.services.event_service import EventService # Import EventService

# Import state model from the new file
from .state_models import DiscussionState, WorkflowTransitionRequest

from apps.main.agents.mentor_agent import MentorAgent
from apps.main.services.conversation_dispatcher import ConversationDispatcher

# Configure logging
logger = logging.getLogger(__name__)
# Removed basicConfig call to avoid potential conflicts if logging is configured elsewhere
# logging.basicConfig(level=logging.DEBUG)

# --- REMOVED Model Definitions (Moved to state_models.py) ---
# DiscussionStage, WorkflowTransitionRequest, DiscussionState definitions are removed from here.
# --- End Removal ---


def create_discussion_graph(user_profile_id: str) -> StateGraph:
    """
    Create a LangGraph workflow for discussion/reflection interactions.
    
    This workflow is designed for:
    1. Deep conversational exploration
    2. User reflection and guidance
    3. Potential profile or goal refinement
    4. Seamless transitions to other workflows when appropriate
    
    Args:
        user_profile_id: The ID of the user profile
        
    Returns:
        StateGraph: The configured discussion workflow graph
    """
    # Initialize the graph with the state model
    workflow = StateGraph(DiscussionState)
    
    # Add the Mentor Agent as the primary (and only) node
    workflow.add_node("mentor", MentorAgent(user_profile_id))
    
    # Define routing logic for the Mentor Agent
    def route_from_mentor(state: DiscussionState):
        # Check for errors first
        if state.error:
            logger.error(f"Workflow {state.workflow_id} ending due to error: {state.error}")
            return END

        # Check if a specific transition to another workflow was requested by the agent
        if state.transition_request is not None:
            logger.info(f"Workflow {state.workflow_id} ending to transition to {state.transition_request.target_workflow}.")
            return END # End this graph to allow transition

        # Check the stage determined by the agent in the previous step
        # If the agent set the stage to 'workflow_complete', end the graph.
        if state.current_stage == "workflow_complete":
            logger.info(f"Workflow {state.workflow_id} ending as stage is 'workflow_complete'.")
            return END
        else:
            # Otherwise, continue the loop by calling the mentor agent again
            logger.debug(f"Workflow {state.workflow_id} continuing to stage {state.current_stage}, routing back to mentor.")
            return "mentor"
    
    # Add conditional edges for mentor node
    workflow.add_conditional_edges(
        "mentor",
        route_from_mentor,
        {
            "mentor": "mentor",
            END: END
        }
    )
    
    # Set the entry point to the Mentor Agent
    workflow.set_entry_point("mentor")
    
    return workflow

async def run_discussion_workflow(
    user_profile_id: str, 
    context_packet: Dict[str, Any], 
    workflow_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Run the discussion workflow for a specific user.
    
    Args:
        user_profile_id: The ID of the user profile
        context_packet: Initial context information from the user
        workflow_id: Optional workflow ID (will generate a new one if not provided)
        
    Returns:
        Dict[str, Any]: Final state and output from the workflow
    """
    # Create the workflow
    workflow = create_discussion_graph(user_profile_id)
    
    # Compile the workflow
    app = workflow.compile()
    
    # Set up the initial state
    if not workflow_id:
        workflow_id = str(uuid.uuid4())
        
    # Create initial state with user details and context
    initial_state = DiscussionState(
        workflow_id=workflow_id,
        user_profile_id=user_profile_id,
        initial_context_packet=context_packet,
        user_ws_session_name=context_packet.get('user_ws_session_name')
    )
    
    # Log workflow initiation
    logger.info(f"Starting discussion workflow {workflow_id} for user {user_profile_id}")
    
    try:
        # Execute the workflow
        result = await app.ainvoke(initial_state)
        logger.info(f"Completed discussion workflow {workflow_id}")
        try:
            result = DiscussionState(**result)
        except Exception as e:
            # --- BEGIN CHANGE: Log the problematic result for debugging ---
            logger.error(f"Failed to construct DiscussionState from result: {result}. Error: {e}", exc_info=True)
            # Re-raise or handle appropriately, maybe return an error dict
            raise ValueError(f"Cannot construct state from result: {e}") # Re-raise for clarity
            # --- END CHANGE ---

        # Process the result first
        processed_result = _process_discussion_result(result)

        # --- REMOVED: Sending final agent response via EventService ---
        # This responsibility is now handled centrally by the calling Celery task
        # (execute_graph_workflow in agent_tasks.py) based on the workflow result.
        # This prevents duplicate messages from being sent.
        # --- END REMOVAL ---

        # Check if there's a transition request to another workflow
        if result.transition_request is not None:
            logger.info(f"Detected workflow transition request to: {result.transition_request.target_workflow}")
            # Process the transition
            # Convert the Pydantic model back to a dict for the handler if needed,
            # or update the handler to accept the model. Assuming handler needs dict for now.
            transition_request_dict = result.transition_request.model_dump(exclude_none=True)
            transition_result = await handle_workflow_transition(
                user_profile_id,
                transition_request_dict,
                result.user_ws_session_name
            )

            # Blend results
            # processed_result = _process_discussion_result(result) # Already done above
            processed_result["transitioned_to"] = result.transition_request.target_workflow
            processed_result["transition_result"] = transition_result

            return processed_result # Return after handling transition

        # Return standard processed result (even if message was sent)
        # The return value of the Celery task might still be useful for logging/history
        return processed_result
    
    except Exception as e:
        logger.error(f"Error in discussion workflow {workflow_id}: {str(e)}", exc_info=True)
        # Return error state
        return {
            "error": str(e),
            "workflow_id": workflow_id,
            "completed": False,
            "user_profile_id": user_profile_id
        }

async def handle_workflow_transition(
    user_profile_id: str,
    transition_request_dict: Dict[str, Any], # Expecting a dict now
    user_ws_session_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Handle the transition from discussion workflow to another workflow type.
    
    Args:
        user_profile_id: The user's profile ID
        transition_request: Details about the requested workflow transition
        user_ws_session_name: Optional WebSocket session for result routing
        
    Returns:
        Dict with transition results
    """
    try:
        # Create a conversation dispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=user_profile_id,
            user_ws_session_name=user_ws_session_name
        )
        
        # Prepare the message for the dispatcher using the dict
        # Important: Include the requested_workflow in metadata
        user_message = {
            "text": transition_request_dict.get("message", ""),
            "metadata": {
                "requested_workflow": transition_request_dict.get("target_workflow"), # Use correct key
                "transition_context": transition_request_dict.get("context", {}),
                "from_workflow": "discussion"
            }
        }
        
        # Process the message through the dispatcher
        # This will initiate the new workflow
        result = await dispatcher.process_message(user_message)
        
        return {
            "status": "transition_initiated",
            "to_workflow": transition_request_dict.get("target_workflow"), # Use correct key
            "dispatcher_result": result
        }
        
    except Exception as e:
        logger.error(f"Error handling workflow transition: {str(e)}", exc_info=True)
        return {
            "status": "transition_failed",
            "error": str(e)
        }

def _process_discussion_result(result: DiscussionState) -> Dict[str, Any]:
    """
    Process the final result of the discussion workflow.
    
    Args:
        result: The final workflow state
        
    Returns:
        Dict containing processed workflow results
    """
    # Extract key information
    profile_updates = result.profile_updates if hasattr(result, 'profile_updates') else {}
    output_data = result.output_data if hasattr(result, 'output_data') else {}
    
    # Prepare the result dictionary
    processed_result = {
        "workflow_id": result.workflow_id,
        "user_profile_id": result.user_profile_id,
        "completed": result.completed,
        "conversation_history": result.conversation_history,
        "profile_updates": profile_updates,
        "output_data": output_data,
        # --- BEGIN CHANGE: Add user_ws_session_name ---
        "user_ws_session_name": result.user_ws_session_name
        # --- END CHANGE ---
    }

    # Add error information if present
    if result.error:
        processed_result["error"] = result.error
        
    # Add transition request information if present
    if hasattr(result, 'transition_request') and result.transition_request:
        processed_result["transition_requested"] = True
        processed_result["requested_workflow"] = result.transition_request.target_workflow # Access attribute
    
    return processed_result
