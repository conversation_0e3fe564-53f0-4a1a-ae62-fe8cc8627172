# backend/apps/main/graphs/state_models.py

from typing import Dict, Any, List, Optional, Literal
import uuid
from pydantic import BaseModel, Field

# Define possible workflow stages
DiscussionStage = Literal[
    "initial_conversation",
    "context_deepening",
    "reflection",
    "guidance_preparation",
    "workflow_transition",
    "workflow_complete"
]

# Define a model for the transition request structure
class WorkflowTransitionRequest(BaseModel):
    """Represents a request to transition to another workflow."""
    target_workflow: str = Field(..., description="The type of workflow to transition to.")
    message: Optional[str] = Field(None, description="Optional message to pass to the next workflow.")
    context: Dict[str, Any] = Field(default_factory=dict, description="Contextual data for the next workflow.")

class DiscussionState(BaseModel):
    """
    State model for the discussion workflow.
    Tracks conversational context and user interaction details.
    """
    # Workflow identification
    workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_profile_id: str
    user_ws_session_name: Optional[str] = None

    # Conversational context
    initial_context_packet: Dict[str, Any] = Field(default_factory=dict)
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list)

    # Agent outputs and state tracking
    output_data: Dict[str, Any] = Field(default_factory=dict)

    # Workflow state tracking
    current_stage: DiscussionStage = "initial_conversation"
    last_agent: Optional[str] = "mentor"
    error: Optional[str] = None
    completed: bool = False

    # User profile updates to track
    profile_updates: Dict[str, Any] = Field(default_factory=dict)

    # Agent Run ID tracking
    run_id: Optional[uuid.UUID] = Field(None, description="The UUID of the last agent run associated with this state.")

    # Workflow transition information - Use the specific model
    # No need for string literal here as the definition is in the same file
    transition_request: Optional[WorkflowTransitionRequest] = None
