"""
Enhance <PERSON><PERSON> analytics views with detailed LLM configuration and prompt information.

This migration updates the existing Grafana views to include:
- Detailed LLM configuration parameters (temperature, pricing)
- Prompt template information and versioning
- Enhanced contextual performance analysis
- Better data for actionable insights in dashboards

Based on first usage feedback to improve dashboard effectiveness.
"""

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ('main', '0007_create_grafana_analytics_views'),
    ]

    operations = [
        # Drop existing views first
        migrations.RunSQL(
            sql="DROP VIEW IF EXISTS grafana_llm_performance CASCADE;",
            reverse_sql="-- No reverse needed"
        ),
        migrations.RunSQL(
            sql="DROP VIEW IF EXISTS grafana_prompt_analytics CASCADE;",
            reverse_sql="-- No reverse needed"
        ),
        migrations.RunSQL(
            sql="DROP VIEW IF EXISTS grafana_contextual_evaluation CASCADE;",
            reverse_sql="-- No reverse needed"
        ),

        # Enhanced LLM Performance Analytics View
        migrations.RunSQL(
            sql="""
            CREATE VIEW grafana_llm_performance AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                COALESCE(lc.model_name, 'unknown') as llm_model,
                br.llm_config_id,
                -- LLM Configuration Details
                lc.name as llm_config_name,
                lc.temperature as llm_temperature,
                lc.input_token_price,
                lc.output_token_price,
                lc.is_evaluation as is_evaluation_model,
                lc.is_default as is_default_config,
                -- Performance Metrics
                br.success_rate,
                br.mean_duration as mean_duration_ms,
                br.median_duration as median_duration_ms,
                br.total_input_tokens,
                br.total_output_tokens,
                (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0)) as total_tokens,
                br.estimated_cost,
                br.semantic_score,
                -- Scenario and Agent Information
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                ga.system_instructions as prompt_template,
                br.agent_version as prompt_version,
                -- Context Variables
                (br.parameters->>'context_variables')::jsonb as context_variables,
                CASE
                    WHEN (br.parameters->'context_variables'->>'trust_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->>'trust_level')::int
                    ELSE NULL
                END as trust_level,
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'valence')::float
                    ELSE NULL
                END as mood_valence,
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'arousal') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'arousal')::float
                    ELSE NULL
                END as mood_arousal,
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int
                    ELSE NULL
                END as stress_level,
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int
                    ELSE NULL
                END as time_pressure,
                -- Performance Categories
                CASE
                    WHEN br.success_rate >= 0.8 AND br.semantic_score >= 8.0 THEN 'Excellent'
                    WHEN br.success_rate >= 0.6 AND br.semantic_score >= 6.0 THEN 'Good'
                    WHEN br.success_rate >= 0.4 AND br.semantic_score >= 4.0 THEN 'Fair'
                    ELSE 'Poor'
                END as performance_category,
                -- Cost Efficiency
                CASE
                    WHEN br.success_rate > 0
                    THEN br.estimated_cost / br.success_rate
                    ELSE NULL
                END as cost_per_success
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_llm_performance;"
        ),

        # Enhanced Prompt Analytics View
        migrations.RunSQL(
            sql="""
            CREATE VIEW grafana_prompt_analytics AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                ga.system_instructions as prompt_template,
                br.agent_version as prompt_version,
                -- LLM Configuration
                COALESCE(lc.model_name, 'unknown') as llm_model,
                lc.name as llm_config_name,
                lc.temperature as llm_temperature,
                -- Performance Metrics
                br.success_rate,
                br.semantic_score,
                br.mean_duration as mean_duration_ms,
                br.total_input_tokens,
                br.total_output_tokens,
                br.estimated_cost,
                -- Scenario Information
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                -- Prompt Effectiveness Score
                CASE
                    WHEN br.semantic_score IS NOT NULL AND br.success_rate IS NOT NULL
                    THEN (br.semantic_score * 0.6 + br.success_rate * 10 * 0.4)
                    WHEN br.semantic_score IS NOT NULL
                    THEN br.semantic_score
                    WHEN br.success_rate IS NOT NULL
                    THEN br.success_rate * 10
                    ELSE NULL
                END as prompt_effectiveness_score,
                -- Version Comparison
                LAG(br.success_rate) OVER (
                    PARTITION BY br.agent_definition_id, bs.id
                    ORDER BY br.execution_date
                ) as previous_success_rate,
                LAG(br.semantic_score) OVER (
                    PARTITION BY br.agent_definition_id, bs.id
                    ORDER BY br.execution_date
                ) as previous_semantic_score,
                LAG(br.agent_version) OVER (
                    PARTITION BY br.agent_definition_id, bs.id
                    ORDER BY br.execution_date
                ) as previous_prompt_version,
                -- Prompt Length Analysis
                LENGTH(ga.system_instructions) as prompt_length,
                -- Context Variables for Prompt Analysis
                (br.parameters->>'context_variables')::jsonb as context_variables
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_prompt_analytics;"
        ),

        # Enhanced Contextual Evaluation View
        migrations.RunSQL(
            sql="""
            CREATE VIEW grafana_contextual_evaluation AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                -- LLM Configuration Details
                COALESCE(lc.model_name, 'unknown') as llm_model,
                lc.name as llm_config_name,
                lc.temperature as llm_temperature,
                -- Performance Metrics
                br.success_rate,
                br.semantic_score,
                br.mean_duration as mean_duration_ms,
                br.estimated_cost,
                -- Scenario and Prompt Information
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                ga.system_instructions as prompt_template,
                br.agent_version as prompt_version,
                -- Context Variables
                CASE
                    WHEN (br.parameters->'context_variables'->>'trust_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->>'trust_level')::int
                    ELSE NULL
                END as trust_level,
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'valence')::float
                    ELSE NULL
                END as mood_valence,
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'arousal') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'arousal')::float
                    ELSE NULL
                END as mood_arousal,
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int
                    ELSE NULL
                END as stress_level,
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int
                    ELSE NULL
                END as time_pressure,
                -- Context Categories
                CASE
                    WHEN (br.parameters->'context_variables'->>'trust_level')::int BETWEEN 0 AND 39 THEN 'Foundation'
                    WHEN (br.parameters->'context_variables'->>'trust_level')::int BETWEEN 40 AND 69 THEN 'Expansion'
                    WHEN (br.parameters->'context_variables'->>'trust_level')::int BETWEEN 70 AND 100 THEN 'Integration'
                    ELSE 'Unknown'
                END as trust_phase,
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence')::float >= 0 AND (br.parameters->'context_variables'->'mood'->>'arousal')::float >= 0 THEN 'Happy/Excited'
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence')::float >= 0 AND (br.parameters->'context_variables'->'mood'->>'arousal')::float < 0 THEN 'Content/Calm'
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence')::float < 0 AND (br.parameters->'context_variables'->'mood'->>'arousal')::float >= 0 THEN 'Angry/Stressed'
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence')::float < 0 AND (br.parameters->'context_variables'->'mood'->>'arousal')::float < 0 THEN 'Sad/Tired'
                    ELSE 'Unknown'
                END as mood_quadrant,
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int BETWEEN 0 AND 30 THEN 'Low'
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int BETWEEN 31 AND 70 THEN 'Medium'
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int BETWEEN 71 AND 100 THEN 'High'
                    ELSE 'Unknown'
                END as stress_category,
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int BETWEEN 0 AND 30 THEN 'Relaxed'
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int BETWEEN 31 AND 70 THEN 'Moderate'
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int BETWEEN 71 AND 100 THEN 'Urgent'
                    ELSE 'Unknown'
                END as time_pressure_category,
                -- Performance Analysis by Context
                CASE
                    WHEN br.success_rate >= 0.8 THEN 'High Performance'
                    WHEN br.success_rate >= 0.6 THEN 'Good Performance'
                    WHEN br.success_rate >= 0.4 THEN 'Fair Performance'
                    ELSE 'Poor Performance'
                END as context_performance_category
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL
              AND br.parameters->'context_variables' IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_contextual_evaluation;"
        ),
    ]
