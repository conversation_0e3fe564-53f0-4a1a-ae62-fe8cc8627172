"""
Add database views for prompt content and conversation analysis.

This migration creates views that expose:
- Full prompt content for analysis
- Conversation flow and agent communications
- Raw results and semantic evaluation details
- Enhanced prompt analysis capabilities

Based on feedback to show actual prompt content and agent communications.
"""

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ('main', '0008_enhance_grafana_views_for_dashboard_improvements'),
    ]

    operations = [
        # View for Prompt Content Analysis
        migrations.RunSQL(
            sql="""
            CREATE VIEW grafana_prompt_content AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                ga.system_instructions as full_prompt_content,
                br.agent_version as prompt_version,
                -- LLM Configuration
                COALESCE(lc.model_name, 'unknown') as llm_model,
                lc.name as llm_config_name,
                lc.temperature as llm_temperature,
                -- Performance Metrics
                br.success_rate,
                br.semantic_score,
                br.estimated_cost,
                -- Scenario Information
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                bs.description as scenario_description,
                bs.input_data as scenario_input,
                -- Prompt Analysis
                LENGTH(ga.system_instructions) as prompt_length,
                CASE
                    WHEN ga.system_instructions LIKE '%{%}%' THEN 'Template-based'
                    WHEN LENGTH(ga.system_instructions) > 2000 THEN 'Detailed'
                    WHEN LENGTH(ga.system_instructions) > 1000 THEN 'Standard'
                    ELSE 'Concise'
                END as prompt_style,
                -- Context Variables
                (br.parameters->>'context_variables')::jsonb as context_variables,
                -- Effectiveness Metrics
                CASE
                    WHEN br.semantic_score IS NOT NULL AND br.success_rate IS NOT NULL
                    THEN (br.semantic_score * 0.6 + br.success_rate * 10 * 0.4)
                    WHEN br.semantic_score IS NOT NULL
                    THEN br.semantic_score
                    WHEN br.success_rate IS NOT NULL
                    THEN br.success_rate * 10
                    ELSE NULL
                END as prompt_effectiveness_score
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_prompt_content;"
        ),

        # View for Conversation Analysis
        migrations.RunSQL(
            sql="""
            CREATE VIEW grafana_conversation_analysis AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                -- LLM Configuration
                COALESCE(lc.model_name, 'unknown') as llm_model,
                lc.name as llm_config_name,
                -- Scenario Information
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                bs.input_data as scenario_input,
                -- Raw Results and Communications
                br.raw_results as conversation_data,
                br.semantic_evaluation_details as evaluation_details,
                br.semantic_evaluations as detailed_evaluations,
                -- Performance Metrics
                br.success_rate,
                br.semantic_score,
                br.mean_duration as mean_duration_ms,
                br.total_input_tokens,
                br.total_output_tokens,
                br.estimated_cost,
                -- Tool Usage
                br.tool_calls,
                br.tool_breakdown,
                -- Stage Performance
                br.stage_performance_details,
                -- Response Analysis
                br.last_response_length,
                CASE
                    WHEN br.last_response_length IS NOT NULL AND br.last_response_length > 0
                    THEN ROUND(((br.total_output_tokens::numeric / br.last_response_length) * 100)::numeric, 2)
                    ELSE NULL
                END as tokens_per_char_ratio,
                -- Context Variables
                (br.parameters->>'context_variables')::jsonb as context_variables,
                -- Communication Quality Indicators
                CASE
                    WHEN br.semantic_score >= 8.0 THEN 'Excellent Communication'
                    WHEN br.semantic_score >= 6.0 THEN 'Good Communication'
                    WHEN br.semantic_score >= 4.0 THEN 'Fair Communication'
                    ELSE 'Poor Communication'
                END as communication_quality
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_conversation_analysis;"
        ),

        # View for Detailed Benchmark Analysis
        migrations.RunSQL(
            sql="""
            CREATE VIEW grafana_detailed_benchmark_analysis AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                ga.system_instructions as full_prompt,
                br.agent_version as prompt_version,
                -- LLM Configuration Details
                COALESCE(lc.model_name, 'unknown') as llm_model,
                lc.name as llm_config_name,
                lc.temperature as llm_temperature,
                lc.input_token_price,
                lc.output_token_price,
                -- Scenario Details
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                bs.description as scenario_description,
                bs.input_data as scenario_input,
                -- Performance Metrics
                br.success_rate,
                br.semantic_score,
                br.mean_duration as mean_duration_ms,
                br.total_input_tokens,
                br.total_output_tokens,
                br.estimated_cost,
                -- Raw Data for Analysis
                br.raw_results as raw_conversation_data,
                br.semantic_evaluation_details,
                br.semantic_evaluations,
                br.stage_performance_details,
                br.tool_breakdown,
                -- Context and Parameters
                br.parameters as full_parameters,
                (br.parameters->>'context_variables')::jsonb as context_variables,
                -- Analysis Fields
                LENGTH(ga.system_instructions) as prompt_length,
                br.tool_calls as total_tool_calls,
                br.last_response_length,
                -- Efficiency Metrics
                CASE
                    WHEN br.total_input_tokens > 0 AND br.mean_duration > 0
                    THEN ROUND((br.total_input_tokens::numeric / (br.mean_duration / 1000.0))::numeric, 2)
                    ELSE NULL
                END as tokens_per_second,
                CASE
                    WHEN br.success_rate > 0
                    THEN ROUND((br.estimated_cost / br.success_rate)::numeric, 6)
                    ELSE NULL
                END as cost_per_success
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_detailed_benchmark_analysis;"
        ),
    ]
