"""
Create database views for Grafana analytics.

This migration creates optimized views for benchmark analytics that will be used
by Grafana dashboards to visualize LLM performance, prompt effectiveness, and
contextual evaluation metrics.
"""

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ('main', '0004_add_contextual_evaluation_fields'),
    ]

    operations = [
        # View for LLM Performance Analytics
        migrations.RunSQL(
            sql="""
            CREATE OR REPLACE VIEW grafana_llm_performance AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                COALESCE(lc.model_name, 'unknown') as llm_model,
                br.llm_config_id,
                br.success_rate,
                br.mean_duration as mean_duration_ms,
                br.median_duration as median_duration_ms,
                br.total_input_tokens,
                br.total_output_tokens,
                (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0)) as total_tokens,
                br.estimated_cost,
                br.semantic_score,
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                'medium' as difficulty_level,
                -- Extract context variables from parameters
                (br.parameters->>'context_variables')::jsonb as context_variables,
                -- Extract trust level
                CASE
                    WHEN (br.parameters->'context_variables'->>'trust_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->>'trust_level')::int
                    ELSE NULL
                END as trust_level,
                -- Extract mood valence
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'valence')::float
                    ELSE NULL
                END as mood_valence,
                -- Extract mood arousal
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'arousal') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'arousal')::float
                    ELSE NULL
                END as mood_arousal,
                -- Extract stress level
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int
                    ELSE NULL
                END as stress_level,
                -- Extract time pressure
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int
                    ELSE NULL
                END as time_pressure,
                -- Calculate cost per token
                CASE
                    WHEN (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0)) > 0
                    THEN br.estimated_cost / (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0))
                    ELSE NULL
                END as cost_per_token,
                -- Performance category based on success rate
                CASE
                    WHEN br.success_rate >= 0.9 THEN 'Excellent'
                    WHEN br.success_rate >= 0.7 THEN 'Good'
                    WHEN br.success_rate >= 0.5 THEN 'Fair'
                    ELSE 'Poor'
                END as performance_category
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_llm_performance;"
        ),

        # View for Prompt Engineering Analytics
        migrations.RunSQL(
            sql="""
            CREATE OR REPLACE VIEW grafana_prompt_analytics AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                ga.system_instructions as prompt_template,
                br.agent_version,
                COALESCE(lc.model_name, 'unknown') as llm_model,
                br.success_rate,
                br.semantic_score,
                br.mean_duration as mean_duration_ms,
                br.total_input_tokens,
                br.total_output_tokens,
                br.estimated_cost,
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                'medium' as difficulty_level,
                -- Calculate prompt effectiveness score
                CASE
                    WHEN br.semantic_score IS NOT NULL AND br.success_rate IS NOT NULL
                    THEN (br.semantic_score * 0.6 + br.success_rate * 10 * 0.4)
                    WHEN br.semantic_score IS NOT NULL
                    THEN br.semantic_score
                    WHEN br.success_rate IS NOT NULL
                    THEN br.success_rate * 10
                    ELSE NULL
                END as prompt_effectiveness_score,
                -- Prompt version comparison
                LAG(br.success_rate) OVER (
                    PARTITION BY br.agent_definition_id, bs.id
                    ORDER BY br.execution_date
                ) as previous_success_rate,
                LAG(br.semantic_score) OVER (
                    PARTITION BY br.agent_definition_id, bs.id
                    ORDER BY br.execution_date
                ) as previous_semantic_score
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_prompt_analytics;"
        ),

        # View for Contextual Evaluation Analytics
        migrations.RunSQL(
            sql="""
            CREATE OR REPLACE VIEW grafana_contextual_evaluation AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                COALESCE(lc.model_name, 'unknown') as llm_model,
                br.success_rate,
                br.semantic_score,
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                -- Context variables
                CASE
                    WHEN (br.parameters->'context_variables'->>'trust_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->>'trust_level')::int
                    ELSE NULL
                END as trust_level,
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'valence')::float
                    ELSE NULL
                END as mood_valence,
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'arousal') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'arousal')::float
                    ELSE NULL
                END as mood_arousal,
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int
                    ELSE NULL
                END as stress_level,
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int
                    ELSE NULL
                END as time_pressure,
                -- Trust phase categorization
                CASE
                    WHEN (br.parameters->'context_variables'->>'trust_level')::int BETWEEN 0 AND 39 THEN 'Foundation'
                    WHEN (br.parameters->'context_variables'->>'trust_level')::int BETWEEN 40 AND 69 THEN 'Expansion'
                    WHEN (br.parameters->'context_variables'->>'trust_level')::int BETWEEN 70 AND 100 THEN 'Integration'
                    ELSE 'Unknown'
                END as trust_phase,
                -- Mood quadrant
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence')::float > 0
                         AND (br.parameters->'context_variables'->'mood'->>'arousal')::float > 0 THEN 'High Arousal Positive'
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence')::float > 0
                         AND (br.parameters->'context_variables'->'mood'->>'arousal')::float <= 0 THEN 'Low Arousal Positive'
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence')::float <= 0
                         AND (br.parameters->'context_variables'->'mood'->>'arousal')::float > 0 THEN 'High Arousal Negative'
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence')::float <= 0
                         AND (br.parameters->'context_variables'->'mood'->>'arousal')::float <= 0 THEN 'Low Arousal Negative'
                    ELSE 'Unknown'
                END as mood_quadrant,
                -- Stress category
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int BETWEEN 0 AND 30 THEN 'Low Stress'
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int BETWEEN 31 AND 70 THEN 'Medium Stress'
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int BETWEEN 71 AND 100 THEN 'High Stress'
                    ELSE 'Unknown'
                END as stress_category,
                -- Time pressure category
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int BETWEEN 0 AND 30 THEN 'Relaxed'
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int BETWEEN 31 AND 70 THEN 'Moderate'
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int BETWEEN 71 AND 100 THEN 'Urgent'
                    ELSE 'Unknown'
                END as time_pressure_category
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL
              AND br.parameters->'context_variables' IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_contextual_evaluation;"
        ),

        # View for Cost and Resource Analytics
        migrations.RunSQL(
            sql="""
            CREATE OR REPLACE VIEW grafana_cost_analytics AS
            SELECT
                br.id as run_id,
                br.execution_date,
                DATE_TRUNC('day', br.execution_date) as execution_date_day,
                DATE_TRUNC('hour', br.execution_date) as execution_date_hour,
                br.agent_definition_id,
                ga.role as agent_role,
                COALESCE(lc.model_name, 'unknown') as llm_model,
                br.total_input_tokens,
                br.total_output_tokens,
                (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0)) as total_tokens,
                br.estimated_cost,
                br.mean_duration as mean_duration_ms,
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                -- Cost efficiency metrics
                CASE
                    WHEN br.success_rate > 0
                    THEN br.estimated_cost / br.success_rate
                    ELSE NULL
                END as cost_per_success,
                CASE
                    WHEN (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0)) > 0
                    THEN br.estimated_cost / (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0))
                    ELSE NULL
                END as cost_per_token,
                CASE
                    WHEN br.semantic_score > 0
                    THEN br.estimated_cost / br.semantic_score
                    ELSE NULL
                END as cost_per_quality_point,
                -- Token efficiency
                CASE
                    WHEN br.mean_duration > 0
                    THEN (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0)) / (br.mean_duration / 1000.0)
                    ELSE NULL
                END as tokens_per_second,
                -- Model cost comparison
                ROW_NUMBER() OVER (
                    PARTITION BY bs.id, DATE_TRUNC('day', br.execution_date)
                    ORDER BY br.estimated_cost ASC
                ) as daily_cost_rank_by_scenario
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL
              AND br.estimated_cost IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_cost_analytics;"
        ),
    ]
