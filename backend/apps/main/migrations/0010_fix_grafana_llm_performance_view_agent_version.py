# Generated by Django 5.2.1 on 2024-12-27 12:00

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0009_add_prompt_content_and_conversation_views'),
    ]

    operations = [
        # Fix grafana_llm_performance view to include agent_version column
        migrations.RunSQL(
            sql="""
            DROP VIEW IF EXISTS grafana_llm_performance;
            CREATE VIEW grafana_llm_performance AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                ga.version as agent_version,
                COALESCE(lc.model_name, 'unknown') as llm_model,
                br.llm_config_id,
                -- LLM Configuration Details
                lc.name as llm_config_name,
                lc.temperature as llm_temperature,
                lc.input_token_price,
                lc.output_token_price,
                lc.is_evaluation as is_evaluation_model,
                lc.is_default as is_default_config,
                -- Performance Metrics
                br.success_rate,
                br.mean_duration as mean_duration_ms,
                br.median_duration as median_duration_ms,
                br.total_input_tokens,
                br.total_output_tokens,
                (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0)) as total_tokens,
                br.estimated_cost,
                br.semantic_score,
                -- Scenario Information
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                bs.description as scenario_description,
                -- Context Variables
                CASE
                    WHEN (br.parameters->'context_variables'->>'trust_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->>'trust_level')::int
                    ELSE NULL
                END as trust_level,
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'valence')::float
                    ELSE NULL
                END as mood_valence,
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->>'arousal') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'arousal')::float
                    ELSE NULL
                END as mood_arousal,
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int
                    ELSE NULL
                END as stress_level,
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int
                    ELSE NULL
                END as time_pressure,
                -- Performance Categories
                CASE
                    WHEN br.success_rate >= 0.9 THEN 'Excellent'
                    WHEN br.success_rate >= 0.8 THEN 'Good'
                    WHEN br.success_rate >= 0.6 THEN 'Fair'
                    ELSE 'Poor'
                END as performance_category,
                -- Cost Efficiency
                CASE
                    WHEN br.success_rate > 0
                    THEN br.estimated_cost / br.success_rate
                    ELSE NULL
                END as cost_per_success,
                CASE
                    WHEN (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0)) > 0
                    THEN br.estimated_cost / (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0))
                    ELSE NULL
                END as cost_per_token
            FROM main_benchmarkrun br
            JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_llm_performance;"
        ),
    ]
