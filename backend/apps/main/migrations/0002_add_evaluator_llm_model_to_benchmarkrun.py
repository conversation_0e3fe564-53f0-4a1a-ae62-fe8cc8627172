from django.db import migrations, models
from django.db.migrations.operations.base import Operation


class ConditionalAddField(Operation):
    """
    Add a field to a model, only if it doesn't already exist.
    """
    reduces_to_sql = True
    reversible = True

    def __init__(self, model_name, name, field):
        self.model_name = model_name
        self.name = name
        self.field = field

    def state_forwards(self, app_label, state):
        # Only modify the state if the field doesn't exist
        model_state = state.models[app_label, self.model_name.lower()]
        if self.name not in model_state.fields:
            model_state.fields.append((self.name, self.field.clone()))

    def database_forwards(self, app_label, schema_editor, from_state, to_state):
        # Check if the column already exists
        table_name = f"{app_label}_{self.model_name.lower()}"
        column_name = self.name

        # Get the list of columns in the table
        with schema_editor.connection.cursor() as cursor:
            cursor.execute(
                f"SELECT column_name FROM information_schema.columns WHERE table_name = %s AND column_name = %s",
                [table_name, column_name]
            )
            column_exists = bool(cursor.fetchone())

        # Only add the column if it doesn't exist
        if not column_exists:
            from_model = from_state.apps.get_model(app_label, self.model_name)
            to_model = to_state.apps.get_model(app_label, self.model_name)

            # Add the field
            schema_editor.add_field(
                from_model,
                to_model._meta.get_field(self.name),
            )

    def database_backwards(self, app_label, schema_editor, from_state, to_state):
        # Standard removal of the field
        from_model = from_state.apps.get_model(app_label, self.model_name)
        schema_editor.remove_field(
            from_model,
            from_model._meta.get_field(self.name),
        )

    def describe(self):
        return f"Add field {self.name} to {self.model_name} (if it doesn't exist)"


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0001_initial'),
    ]

    operations = [
        ConditionalAddField(
            model_name='benchmarkrun',
            name='evaluator_llm_model',
            field=models.CharField(
                blank=True,
                help_text='The identifier of the LLM model used for semantic evaluation.',
                max_length=100,
                null=True
            ),
        ),
    ]
