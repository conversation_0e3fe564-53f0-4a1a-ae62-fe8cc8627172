# Generated by Django 5.2 on 2025-05-22 14:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0003_merge_20250426_1126'),
    ]

    operations = [
        migrations.AddField(
            model_name='evaluationcriteriatemplate',
            name='category',
            field=models.CharField(choices=[('semantic', 'Semantic Analysis'), ('quality', 'Quality Assessment'), ('phase', 'Phase-based Evaluation'), ('contextual', 'Contextual Evaluation'), ('custom', 'Custom Evaluation')], default='quality', help_text='Category of evaluation this template performs.', max_length=20),
        ),
        migrations.AddField(
            model_name='evaluationcriteriatemplate',
            name='contextual_criteria',
            field=models.JSONField(default=dict, help_text='Contextual evaluation criteria that vary based on variables like trust_level, mood, environment.'),
        ),
        migrations.AddField(
            model_name='evaluationcriteriatemplate',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Whether this template is active and available for use.'),
        ),
        migrations.AddField(
            model_name='evaluationcriteriatemplate',
            name='variable_ranges',
            field=models.JSO<PERSON>ield(default=dict, help_text='Defines the ranges for contextual variables this template supports.'),
        ),
        migrations.AddField(
            model_name='evaluationcriteriatemplate',
            name='workflow_type',
            field=models.CharField(blank=True, help_text='Specific workflow type this template is designed for (empty for all types).', max_length=50),
        ),
        migrations.AlterField(
            model_name='evaluationcriteriatemplate',
            name='criteria',
            field=models.JSONField(default=dict, help_text='Base evaluation criteria structure (e.g., dimension names to criteria lists).'),
        ),
        migrations.AddIndex(
            model_name='evaluationcriteriatemplate',
            index=models.Index(fields=['workflow_type'], name='main_evalua_workflo_e78bd9_idx'),
        ),
        migrations.AddIndex(
            model_name='evaluationcriteriatemplate',
            index=models.Index(fields=['category'], name='main_evalua_categor_519c89_idx'),
        ),
        migrations.AddIndex(
            model_name='evaluationcriteriatemplate',
            index=models.Index(fields=['is_active'], name='main_evalua_is_acti_961a78_idx'),
        ),
    ]
