# Generated by Django 5.2 on 2025-04-25 14:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0001_initial'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='llmconfig',
            name='unique_default_llm_config',
        ),
        migrations.AddIndex(
            model_name='llmconfig',
            index=models.Index(fields=['is_evaluation'], name='main_llmcon_is_eval_755d92_idx'),
        ),
        migrations.AddConstraint(
            model_name='llmconfig',
            constraint=models.UniqueConstraint(condition=models.Q(('is_default', True), ('is_evaluation', False)), fields=('is_evaluation',), name='unique_default_non_evaluation_config'),
        ),
        migrations.AddConstraint(
            model_name='llmconfig',
            constraint=models.UniqueConstraint(condition=models.Q(('is_default', True), ('is_evaluation', True)), fields=('is_evaluation',), name='unique_default_evaluation_config'),
        ),
    ]
