# Generated by Django 5.2 on 2025-04-25 14:07

import django.core.serializers.json
import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('activity', '0002_initial'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BenchmarkTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Unique name for the tag (e.g., 'feature-x', 'regression', 'performance').", max_length=50, unique=True)),
                ('description', models.TextField(blank=True, help_text='Optional description for the tag.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Benchmark Tag',
                'verbose_name_plural': 'Benchmark Tags',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Wheel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Descriptive name of the wheel that may include user name and phase information.', max_length=255)),
                ('created_by', models.CharField(help_text='Identifier for the agent that generated this wheel.', max_length=255)),
                ('created_at', models.DateField(help_text='Date when the wheel was created for the user.')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='AgentTool',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text="Unique code for this tool (e.g., 'user_traits_query')", max_length=50, unique=True)),
                ('name', models.CharField(help_text='Human-readable name for this tool', max_length=255)),
                ('description', models.TextField(help_text='Detailed description of what this tool does')),
                ('input_schema', models.JSONField(help_text='JSON Schema defining expected input format')),
                ('output_schema', models.JSONField(help_text='JSON Schema defining expected output format')),
                ('function_path', models.CharField(help_text="Fully qualified path to the tool function (e.g., 'apps.main.agents.tools.user_traits_handler')", max_length=255)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this tool is available for use')),
                ('allowed_agent_roles', models.JSONField(default=list, help_text='List of agent roles (from AgentRole) that can use this tool')),
                ('avg_response_time', models.FloatField(default=0.0, help_text='Average response time in seconds')),
                ('usage_count', models.IntegerField(default=0, help_text='Total number of times this tool has been used')),
                ('error_rate', models.FloatField(default=0.0, help_text='Percentage of tool invocations that resulted in errors')),
                ('definition_hash', models.CharField(blank=True, help_text="SHA-256 hash of the tool's definition (name, desc, path, schemas) for idempotency checks", max_length=64, null=True)),
            ],
            options={
                'verbose_name': 'Agent Tool',
                'verbose_name_plural': 'Agent Tools',
                'indexes': [models.Index(fields=['code'], name='main_agentt_code_0b6631_idx'), models.Index(fields=['is_active'], name='main_agentt_is_acti_179edc_idx'), models.Index(fields=['definition_hash'], name='main_agentt_definit_2dc96a_idx')],
            },
        ),
        migrations.CreateModel(
            name='AppliedSeedingCommand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('command_name', models.CharField(help_text="Name of the management command that was applied (e.g., 'seed_db_40_envs').", max_length=255, unique=True)),
                ('applied_at', models.DateTimeField(auto_now_add=True, help_text='Timestamp when the command was successfully applied.')),
            ],
            options={
                'verbose_name': 'Applied Seeding Command',
                'verbose_name_plural': 'Applied Seeding Commands',
                'indexes': [models.Index(fields=['command_name'], name='main_applie_command_0d8ccc_idx')],
            },
        ),
        migrations.CreateModel(
            name='BenchmarkMetric',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text='Unique code for this metric', max_length=50, unique=True)),
                ('name', models.CharField(help_text='Human-readable name for this metric', max_length=255)),
                ('description', models.TextField(help_text='Description of what this metric measures')),
                ('data_type', models.CharField(choices=[('numeric', 'Numeric'), ('boolean', 'Boolean'), ('categorical', 'Categorical'), ('duration', 'Duration')], help_text='Type of data this metric records', max_length=20)),
                ('unit', models.CharField(blank=True, help_text="Unit of measurement (e.g., 'seconds', 'percent')", max_length=50, null=True)),
                ('target_value', models.JSONField(blank=True, help_text='Target or ideal value for this metric', null=True)),
                ('valid_values', models.JSONField(blank=True, help_text='For categorical metrics, the list of valid values', null=True)),
                ('applicable_roles', models.JSONField(default=list, help_text='List of agent roles this metric applies to')),
            ],
            options={
                'verbose_name': 'Benchmark Metric',
                'verbose_name_plural': 'Benchmark Metrics',
                'indexes': [models.Index(fields=['code'], name='main_benchm_code_dd52ca_idx')],
            },
        ),
        migrations.CreateModel(
            name='BenchmarkScenario',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('agent_role', models.CharField(max_length=50)),
                ('input_data', models.JSONField()),
                ('metadata', models.JSONField(default=dict, help_text='Expected output, evaluation criteria (dictionary mapping dimension names to criteria lists), etc.')),
                ('is_active', models.BooleanField(default=True)),
                ('version', models.PositiveIntegerField(default=1, help_text='Version number for this scenario definition.')),
                ('is_latest', models.BooleanField(default=True, help_text='Indicates if this is the latest version of the scenario.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent_scenario', models.ForeignKey(blank=True, help_text='Link to the previous version of this scenario, if any.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child_scenarios', to='main.benchmarkscenario')),
                ('variations', models.ManyToManyField(blank=True, help_text='Link to other scenarios that this one is considered a variation of.', related_name='base_scenario_set', to='main.benchmarkscenario')),
                ('tags', models.ManyToManyField(blank=True, help_text='Tags for organizing and filtering scenarios.', related_name='scenarios', to='main.benchmarktag')),
            ],
            options={
                'verbose_name': 'Benchmark Scenario',
                'verbose_name_plural': 'Benchmark Scenarios',
            },
        ),
        migrations.CreateModel(
            name='CustomAgent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='User-specific name of the customized agent.', max_length=255)),
                ('description', models.TextField(help_text="Tailored description of this agent instance's responsibilities for a specific user.")),
                ('instruction', models.TextField(help_text='Personalized instructions for the agent based on user profile and interaction history.')),
                ('user_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_agents', to='user.userprofile')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='AgentGoal',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('description', models.TextField(help_text='Specific description of what the agent aims to achieve for the user.')),
                ('priority', models.IntegerField(help_text='Numerical priority ranking (1-5, with 1 being highest) to help agent prioritize competing goals.')),
                ('user_goal_id', models.UUIDField(help_text='Reference to the corresponding user goal this agent goal supports.')),
                ('custom_agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='agent_goals', to='main.customagent')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='EvaluationCriteriaTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Unique name for this evaluation template (e.g., 'Clarity and Conciseness', 'Safety Check').", max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Detailed description of what this template evaluates.')),
                ('criteria', models.JSONField(default=dict, help_text='The structure of the evaluation criteria (e.g., a dictionary mapping dimension names to detailed instructions or rating scales).')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Evaluation Criteria Template',
                'verbose_name_plural': 'Evaluation Criteria Templates',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='main_evalua_name_f4e05c_idx')],
            },
        ),
        migrations.CreateModel(
            name='GenericAgent',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('role', models.CharField(choices=[('mentor', 'Mentor Agent'), ('orchestrator', 'Orchestrator Agent'), ('resource', 'Resource & Capacity Management Agent'), ('engagement', 'Engagement & Pattern Analytics Agent'), ('psychological', 'Psychological Monitoring Agent'), ('strategy', 'Strategy Agent'), ('activity', 'Wheel/Activity Agent'), ('ethical', 'Ethical Oversight Agent'), ('dispatcher', 'Dispatcher Agent')], help_text='The specific role this agent fulfills in the system', max_length=20, unique=True)),
                ('version', models.CharField(default='1.0.0', help_text='Semantic version of this agent definition', max_length=20)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this agent definition is currently active')),
                ('description', models.TextField(help_text="Comprehensive description of this agent's purpose and responsibilities")),
                ('system_instructions', models.TextField(help_text="Base instructions defining the agent's core behavior and limitations")),
                ('input_schema', models.JSONField(help_text='JSON Schema defining the expected input format for this agent')),
                ('output_schema', models.JSONField(help_text='JSON Schema defining the required output format from this agent')),
                ('state_schema', models.JSONField(default=dict, help_text="JSON Schema defining the structure of this agent's state in Langgraph")),
                ('memory_schema', models.JSONField(default=dict, help_text="JSON Schema defining the structure of this agent's persistent memory")),
                ('read_models', models.JSONField(default=list, help_text='List of models this agent has read access to')),
                ('write_models', models.JSONField(default=list, help_text='List of models this agent has write access to')),
                ('recommend_models', models.JSONField(default=list, help_text='List of models this agent can create recommendations for')),
                ('langgraph_node_class', models.CharField(help_text="Fully qualified class name for this agent's Langgraph node implementation", max_length=255)),
                ('processing_timeout', models.IntegerField(default=30, help_text='Maximum allowed processing time in seconds before timeout')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('available_tools', models.ManyToManyField(help_text='Tools this agent can utilize', related_name='available_to_agents', to='main.agenttool')),
                ('benchmark_metrics', models.ManyToManyField(help_text="Metrics used to benchmark this agent's performance", related_name='measured_on_agents', to='main.benchmarkmetric')),
            ],
            options={
                'verbose_name': 'Generic Agent',
                'verbose_name_plural': 'Generic Agents',
            },
        ),
        migrations.AddField(
            model_name='customagent',
            name='generic_agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_agents', to='main.genericagent'),
        ),
        migrations.CreateModel(
            name='AgentRun',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('workflow_id', models.UUIDField(help_text='Identifier for the broader workflow this run is part of')),
                ('started_at', models.DateTimeField(help_text='When this agent run started')),
                ('completed_at', models.DateTimeField(blank=True, help_text='When this agent run completed', null=True)),
                ('input_data', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The input data provided to the agent')),
                ('output_data', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The output data produced by the agent', null=True)),
                ('initial_state', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The initial state passed to the agent')),
                ('final_state', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The final state after agent execution', null=True)),
                ('memory_updates', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder, help_text="Changes made to the agent's persistent memory", null=True)),
                ('status', models.CharField(choices=[('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed'), ('timeout', 'Timeout')], default='running', help_text='Current status of this agent run', max_length=20)),
                ('error_message', models.TextField(blank=True, help_text='Error message if this run failed', null=True)),
                ('tool_calls', models.JSONField(default=list, encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='Records of all tool calls made during this run')),
                ('user_profile', models.ForeignKey(help_text='The user this agent run was performed for', on_delete=django.db.models.deletion.CASCADE, related_name='agent_runs', to='user.userprofile')),
                ('agent', models.ForeignKey(help_text='The agent that was executed', on_delete=django.db.models.deletion.PROTECT, related_name='runs', to='main.genericagent')),
            ],
            options={
                'verbose_name': 'Agent Run',
                'verbose_name_plural': 'Agent Runs',
            },
        ),
        migrations.CreateModel(
            name='HistoryEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('event_type', models.CharField(max_length=100)),
                ('object_id', models.CharField(max_length=255)),
                ('secondary_object_id', models.CharField(blank=True, max_length=255, null=True)),
                ('details', models.JSONField(default=dict)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('secondary_content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='secondary_events', to='contenttypes.contenttype')),
                ('user_profile', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='user.userprofile')),
            ],
        ),
        migrations.CreateModel(
            name='LLMConfig',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(help_text="Unique identifier for this LLM configuration (e.g., 'gpt-4-turbo-standard', 'claude-3-opus-creative').", max_length=100, unique=True)),
                ('model_name', models.CharField(help_text="The identifier of the LLM model used (e.g., 'gpt-4-1106-preview', 'claude-3-opus-20240229').", max_length=100)),
                ('temperature', models.FloatField(blank=True, help_text='The temperature setting for the LLM (controls randomness). Null means use default.', null=True)),
                ('input_token_price', models.DecimalField(blank=True, decimal_places=6, help_text='Price per (million) input token for this LLM (for cost estimation).', max_digits=10, null=True)),
                ('output_token_price', models.DecimalField(blank=True, decimal_places=6, help_text='Price per (million) output token for this LLM (for cost estimation).', max_digits=10, null=True)),
                ('is_evaluation', models.BooleanField(default=False, help_text='Indicates if this LLM configuration is for evaluation purposes.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_default', models.BooleanField(default=False, help_text='If True, this configuration will be used as the default when no specific configuration is provided.')),
            ],
            options={
                'verbose_name': 'LLM Configuration',
                'verbose_name_plural': 'LLM Configurations',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='main_llmcon_name_2e61a8_idx'), models.Index(fields=['model_name'], name='main_llmcon_model_n_948738_idx'), models.Index(fields=['is_default'], name='main_llmcon_is_defa_69abc7_idx')],
                'constraints': [models.UniqueConstraint(condition=models.Q(('is_default', True)), fields=('is_default',), name='unique_default_llm_config')],
            },
        ),
        migrations.AddField(
            model_name='genericagent',
            name='llm_config',
            field=models.ForeignKey(blank=True, help_text='The LLM configuration to use for this agent role. Cannot be null.', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='generic_agents', to='main.llmconfig'),
        ),
        migrations.CreateModel(
            name='BenchmarkRun',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('agent_version', models.CharField(help_text='Git hash or version identifier', max_length=40)),
                ('execution_date', models.DateTimeField(auto_now_add=True)),
                ('parameters', models.JSONField(default=dict, help_text='Benchmark parameters used for this run')),
                ('evaluator_llm_model', models.CharField(blank=True, help_text='The identifier of the LLM model used for semantic evaluation.', max_length=100, null=True)),
                ('runs_count', models.IntegerField(help_text='Number of times the scenario was executed')),
                ('mean_duration', models.FloatField(help_text='Mean execution time in milliseconds')),
                ('median_duration', models.FloatField(help_text='Median execution time in milliseconds')),
                ('min_duration', models.FloatField(help_text='Minimum execution time in milliseconds')),
                ('max_duration', models.FloatField(help_text='Maximum execution time in milliseconds')),
                ('std_dev', models.FloatField(help_text='Standard deviation of execution time in milliseconds')),
                ('success_rate', models.FloatField(help_text='Percentage of successful runs (0.0 to 1.0)')),
                ('llm_calls', models.IntegerField(default=0, help_text='Total number of LLM calls during the benchmark')),
                ('tool_calls', models.IntegerField(default=0, help_text='Total number of tool calls during the benchmark')),
                ('tool_breakdown', models.JSONField(default=dict, help_text='Count of calls per tool code')),
                ('memory_operations', models.IntegerField(default=0, help_text='Total number of memory operations')),
                ('semantic_score', models.FloatField(blank=True, help_text='Overall semantic quality score (0.0 to 1.0) from the primary evaluator, potentially averaged across dimensions.', null=True)),
                ('semantic_evaluation_details', models.JSONField(default=dict, help_text='Detailed overall semantic evaluation reasoning from the primary evaluator LLM.')),
                ('semantic_evaluations', models.JSONField(default=dict, help_text="Stores multi-dimensional evaluation results from multiple LLMs. Maps model name to {'dimensions': {'dimension_name': {'score': float, 'reasoning': str}}, 'overall_score': float, 'overall_reasoning': str, 'error': bool}.")),
                ('raw_results', models.JSONField(default=dict, help_text='Raw results from the underlying benchmark tool')),
                ('total_input_tokens', models.IntegerField(blank=True, help_text='Total number of input tokens processed by the LLM during the benchmark.', null=True)),
                ('total_output_tokens', models.IntegerField(blank=True, help_text='Total number of output tokens generated by the LLM during the benchmark.', null=True)),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=6, help_text='Estimated cost of the LLM usage for this benchmark run based on token counts and prices.', max_digits=10, null=True)),
                ('performance_p_value', models.FloatField(blank=True, help_text='P-value from statistical test comparing performance (e.g., duration) to the previous run.', null=True)),
                ('is_performance_significant', models.BooleanField(blank=True, help_text='Indicates if the performance difference compared to the previous run is statistically significant (e.g., p < 0.05).', null=True)),
                ('stage_performance_details', models.JSONField(blank=True, default=dict, help_text='Aggregated performance metrics (mean, median, std_dev, etc. in ms) for each profiled stage.')),
                ('last_response_length', models.IntegerField(blank=True, help_text="Length of the agent's last response in characters, if available.", null=True)),
                ('compared_to_run', models.ForeignKey(blank=True, help_text='Reference to the previous run used for statistical comparison.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='comparisons', to='main.benchmarkrun')),
                ('scenario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='runs', to='main.benchmarkscenario')),
                ('agent_definition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='benchmark_runs', to='main.genericagent')),
                ('llm_config', models.ForeignKey(blank=True, help_text='The LLM configuration used by the agent during this benchmark run.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='benchmark_runs', to='main.llmconfig')),
            ],
            options={
                'verbose_name': 'Benchmark Run',
                'verbose_name_plural': 'Benchmark Runs',
                'ordering': ['-execution_date'],
            },
        ),
        migrations.CreateModel(
            name='UserFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feedback_type', models.CharField(max_length=255)),
                ('object_id', models.CharField(max_length=255)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('user_comment', models.TextField()),
                ('criticality', models.IntegerField()),
                ('context_data', models.JSONField(default=dict)),
                ('slack_payload', models.JSONField(default=dict)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.userprofile')),
            ],
        ),
        migrations.CreateModel(
            name='WheelItem',
            fields=[
                ('id', models.CharField(help_text='Unique identifier for this specific segment of the wheel.', max_length=50, primary_key=True, serialize=False)),
                ('percentage', models.FloatField(help_text='Probability weight between 0.0 and 100.0 that determines likelihood of selection based on user traits and trust level.')),
                ('activity_tailored', models.OneToOneField(help_text='Reference to the customized activity that will be assigned if this wheel segment is selected.', on_delete=django.db.models.deletion.CASCADE, related_name='wheel_item', to='activity.activitytailored')),
                ('wheel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='main.wheel')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='AgentRecommendation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('target_model', models.CharField(help_text="The model this recommendation applies to (e.g., 'user.UserTraitInclination')", max_length=255)),
                ('target_instance_id', models.CharField(blank=True, help_text='ID of the specific instance to update, or null for new instances', max_length=255, null=True)),
                ('operation', models.CharField(choices=[('create', 'Create'), ('update', 'Update'), ('delete', 'Delete')], help_text='The operation to perform', max_length=20)),
                ('field_updates', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='For create/update operations, the field values to set')),
                ('rationale', models.TextField(help_text='Explanation for why this recommendation is being made')),
                ('evidence', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='Supporting evidence for this recommendation')),
                ('confidence', models.FloatField(default=0.8, help_text='Confidence score (0.0-1.0) for this recommendation')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('applied', 'Applied')], default='pending', help_text='Current status of this recommendation', max_length=20)),
                ('applied_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('priority', models.IntegerField(default=5, help_text='Priority from 1 (highest) to 10 (lowest)')),
                ('created_by_run', models.ForeignKey(help_text='The agent run that created this recommendation', on_delete=django.db.models.deletion.CASCADE, related_name='recommendations', to='main.agentrun')),
                ('reviewed_by', models.ForeignKey(blank=True, help_text='The agent run that reviewed this recommendation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_recommendations', to='main.agentrun')),
            ],
            options={
                'verbose_name': 'Agent Recommendation',
                'verbose_name_plural': 'Agent Recommendations',
                'indexes': [models.Index(fields=['status'], name='main_agentr_status_d534df_idx'), models.Index(fields=['created_by_run'], name='main_agentr_created_9aa0ab_idx'), models.Index(fields=['target_model'], name='main_agentr_target__9891d6_idx'), models.Index(fields=['priority'], name='main_agentr_priorit_083788_idx'), models.Index(fields=['created_at'], name='main_agentr_created_d8919f_idx')],
            },
        ),
        migrations.CreateModel(
            name='AgentMemory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('agent_role', models.CharField(choices=[('mentor', 'Mentor Agent'), ('orchestrator', 'Orchestrator Agent'), ('resource', 'Resource & Capacity Management Agent'), ('engagement', 'Engagement & Pattern Analytics Agent'), ('psychological', 'Psychological Monitoring Agent'), ('strategy', 'Strategy Agent'), ('activity', 'Wheel/Activity Agent'), ('ethical', 'Ethical Oversight Agent'), ('dispatcher', 'Dispatcher Agent')], help_text='The agent role this memory belongs to', max_length=20)),
                ('memory_key', models.CharField(help_text='Key identifying this specific memory item', max_length=255)),
                ('content', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The actual memory content stored as JSON')),
                ('confidence', models.FloatField(default=1.0, help_text='Confidence score (0.0-1.0) for this memory')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_accessed', models.DateTimeField(blank=True, null=True)),
                ('access_count', models.IntegerField(default=0)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('user_profile', models.ForeignKey(help_text='The user this memory is associated with', on_delete=django.db.models.deletion.CASCADE, related_name='agent_memories', to='user.userprofile')),
                ('created_by_run', models.ForeignKey(blank=True, help_text='The agent run that created this memory', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_memories', to='main.agentrun')),
            ],
            options={
                'verbose_name': 'Agent Memory',
                'verbose_name_plural': 'Agent Memories',
                'indexes': [models.Index(fields=['agent_role', 'user_profile', 'memory_key'], name='main_agentm_agent_r_85520a_idx'), models.Index(fields=['user_profile'], name='main_agentm_user_pr_820d85_idx'), models.Index(fields=['created_at'], name='main_agentm_created_3a3c29_idx'), models.Index(fields=['updated_at'], name='main_agentm_updated_d8dfef_idx'), models.Index(fields=['expires_at'], name='main_agentm_expires_794bca_idx')],
                'unique_together': {('agent_role', 'user_profile', 'memory_key')},
            },
        ),
        migrations.CreateModel(
            name='AgentMetric',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('value', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The measured value (could be number, boolean, string, etc.)')),
                ('context', models.JSONField(blank=True, default=dict, encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='Additional context about this measurement')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('agent_run', models.ForeignKey(help_text='The agent run this metric is for', on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='main.agentrun')),
                ('metric', models.ForeignKey(help_text='The metric being measured', on_delete=django.db.models.deletion.PROTECT, related_name='measurements', to='main.benchmarkmetric')),
            ],
            options={
                'verbose_name': 'Agent Metric',
                'verbose_name_plural': 'Agent Metrics',
                'indexes': [models.Index(fields=['agent_run'], name='main_agentm_agent_r_7faf1d_idx'), models.Index(fields=['metric'], name='main_agentm_metric__e1752c_idx'), models.Index(fields=['timestamp'], name='main_agentm_timesta_99a419_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='benchmarkscenario',
            index=models.Index(fields=['name'], name='main_benchm_name_b5478f_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkscenario',
            index=models.Index(fields=['agent_role'], name='main_benchm_agent_r_8ffd58_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkscenario',
            index=models.Index(fields=['is_active'], name='main_benchm_is_acti_38a284_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkscenario',
            index=models.Index(fields=['version'], name='main_benchm_version_eb2e1f_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkscenario',
            index=models.Index(fields=['is_latest'], name='main_benchm_is_late_13fc2a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='benchmarkscenario',
            unique_together={('name', 'version')},
        ),
        migrations.AlterUniqueTogether(
            name='customagent',
            unique_together={('user_profile', 'generic_agent')},
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['workflow_id'], name='main_agentr_workflo_d7697f_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['agent'], name='main_agentr_agent_i_7bd18f_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['user_profile'], name='main_agentr_user_pr_c66229_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['status'], name='main_agentr_status_36e159_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['started_at'], name='main_agentr_started_442aa8_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['content_type', 'object_id'], name='main_histor_content_17e564_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['secondary_content_type', 'secondary_object_id'], name='main_histor_seconda_1d95ff_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['timestamp'], name='main_histor_timesta_aec51d_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['event_type'], name='main_histor_event_t_015083_idx'),
        ),
        migrations.AddIndex(
            model_name='genericagent',
            index=models.Index(fields=['role'], name='main_generi_role_f109cf_idx'),
        ),
        migrations.AddIndex(
            model_name='genericagent',
            index=models.Index(fields=['is_active'], name='main_generi_is_acti_cd58c1_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkrun',
            index=models.Index(fields=['scenario'], name='main_benchm_scenari_d7541e_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkrun',
            index=models.Index(fields=['agent_definition'], name='main_benchm_agent_d_8702f2_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkrun',
            index=models.Index(fields=['execution_date'], name='main_benchm_executi_bd4db9_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkrun',
            index=models.Index(fields=['llm_config'], name='main_benchm_llm_con_96fc10_idx'),
        ),
    ]
