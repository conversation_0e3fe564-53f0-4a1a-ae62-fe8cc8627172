# Generated migration for chronological events view for Logs plugin dashboard

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0009_add_prompt_content_and_conversation_views'),
    ]

    operations = [
        # Create chronological events view for Logs plugin dashboard
        migrations.RunSQL(
            sql="""
            CREATE OR REPLACE VIEW grafana_chronological_events AS
            WITH stage_events AS (
                -- Extract individual stage events from stage performance data
                SELECT
                    br.id as run_id,
                    br.execution_date as run_start_time,
                    br.scenario_id,
                    bs.name as scenario_name,
                    br.agent_definition_id,
                    ga.role as agent_role,
                    COALESCE(lc.model_name, 'unknown') as llm_model,
                    lc.name as llm_config_name,

                    -- Stage information from stage_performance_details JSONB object
                    stage_name as event_name,
                    'stage' as event_type,
                    br.execution_date + (INTERVAL '1 second' * (ROW_NUMBER() OVER (PARTITION BY br.id ORDER BY stage_name))) as event_timestamp,

                    -- Event details as JSON for filtering
                    jsonb_build_object(
                        'stage_name', stage_name,
                        'status', 'COMPLETED', -- Default status since stage_performance_details only contains completed stages
                        'mean_duration_ms', COALESCE((stage_info->>'mean_duration_ms')::float, 0),
                        'median_duration_ms', COALESCE((stage_info->>'median_duration_ms')::float, 0),
                        'min_duration_ms', COALESCE((stage_info->>'min_duration_ms')::float, 0),
                        'max_duration_ms', COALESCE((stage_info->>'max_duration_ms')::float, 0),
                        'std_dev_ms', COALESCE((stage_info->>'std_dev_ms')::float, 0),
                        'count', COALESCE((stage_info->>'count')::int, 0)
                    ) as event_details,

                    -- Performance context
                    br.success_rate,
                    br.semantic_score,
                    br.mean_duration as run_duration_ms,
                    br.total_input_tokens,
                    br.total_output_tokens,
                    br.estimated_cost,

                    -- Context variables
                    COALESCE(ROUND((br.parameters->'context_variables'->>'trust_level')::float), 50) as trust_level,
                    COALESCE((br.parameters->'context_variables'->'mood'->>'valence')::float, 0) as mood_valence,
                    COALESCE(ROUND((br.parameters->'context_variables'->'environment'->>'stress_level')::float), 0) as stress_level

                FROM main_benchmarkrun br
                JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
                JOIN main_genericagent ga ON br.agent_definition_id = ga.id
                LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
                CROSS JOIN LATERAL jsonb_each(
                    CASE
                        WHEN br.stage_performance_details IS NOT NULL AND jsonb_typeof(br.stage_performance_details) = 'object'
                        THEN br.stage_performance_details
                        ELSE '{}'::jsonb
                    END
                ) as stage_data(stage_name, stage_info)
                WHERE br.execution_date IS NOT NULL
                  AND br.stage_performance_details IS NOT NULL
                  AND br.stage_performance_details != '{}'::jsonb
            ),

            tool_events AS (
                -- Extract tool call events from tool breakdown
                SELECT
                    br.id as run_id,
                    br.execution_date as run_start_time,
                    br.scenario_id,
                    bs.name as scenario_name,
                    br.agent_definition_id,
                    ga.role as agent_role,
                    COALESCE(lc.model_name, 'unknown') as llm_model,
                    lc.name as llm_config_name,

                    -- Tool information
                    tool_name as event_name,
                    'tool_call' as event_type,
                    br.execution_date + (INTERVAL '1 second' * (ROW_NUMBER() OVER (PARTITION BY br.id ORDER BY tool_name))) as event_timestamp,

                    -- Event details as JSON
                    jsonb_build_object(
                        'tool_name', tool_name,
                        'call_count', tool_count,
                        'effectiveness', CASE
                            WHEN tool_count::int > 0 AND br.success_rate > 0.7 THEN 'high'
                            WHEN tool_count::int > 0 AND br.success_rate > 0.4 THEN 'medium'
                            WHEN tool_count::int > 0 THEN 'low'
                            ELSE 'unused'
                        END
                    ) as event_details,

                    -- Performance context
                    br.success_rate,
                    br.semantic_score,
                    br.mean_duration as run_duration_ms,
                    br.total_input_tokens,
                    br.total_output_tokens,
                    br.estimated_cost,

                    -- Context variables
                    COALESCE(ROUND((br.parameters->'context_variables'->>'trust_level')::float), 50) as trust_level,
                    COALESCE((br.parameters->'context_variables'->'mood'->>'valence')::float, 0) as mood_valence,
                    COALESCE(ROUND((br.parameters->'context_variables'->'environment'->>'stress_level')::float), 0) as stress_level

                FROM main_benchmarkrun br
                JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
                JOIN main_genericagent ga ON br.agent_definition_id = ga.id
                LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
                CROSS JOIN LATERAL jsonb_each_text(
                    CASE
                        WHEN br.tool_breakdown IS NOT NULL
                        THEN br.tool_breakdown
                        ELSE '{}'::jsonb
                    END
                ) as tool_data(tool_name, tool_count)
                WHERE br.execution_date IS NOT NULL
                  AND br.tool_breakdown IS NOT NULL
                  AND tool_count::int > 0
            ),

            evaluation_events AS (
                -- Extract semantic evaluation events
                SELECT
                    br.id as run_id,
                    br.execution_date as run_start_time,
                    br.scenario_id,
                    bs.name as scenario_name,
                    br.agent_definition_id,
                    ga.role as agent_role,
                    COALESCE(lc.model_name, 'unknown') as llm_model,
                    lc.name as llm_config_name,

                    'semantic_evaluation' as event_name,
                    'evaluation' as event_type,
                    br.execution_date + INTERVAL '1 minute' as event_timestamp,

                    -- Event details as JSON
                    jsonb_build_object(
                        'semantic_score', br.semantic_score,
                        'evaluation_details', LEFT(COALESCE(br.semantic_evaluation_details::text, 'No details'), 200),
                        'quality_category', CASE
                            WHEN br.semantic_score >= 8 THEN 'excellent'
                            WHEN br.semantic_score >= 6 THEN 'good'
                            WHEN br.semantic_score >= 4 THEN 'fair'
                            ELSE 'poor'
                        END
                    ) as event_details,

                    -- Performance context
                    br.success_rate,
                    br.semantic_score,
                    br.mean_duration as run_duration_ms,
                    br.total_input_tokens,
                    br.total_output_tokens,
                    br.estimated_cost,

                    -- Context variables
                    COALESCE(ROUND((br.parameters->'context_variables'->>'trust_level')::float), 50) as trust_level,
                    COALESCE((br.parameters->'context_variables'->'mood'->>'valence')::float, 0) as mood_valence,
                    COALESCE(ROUND((br.parameters->'context_variables'->'environment'->>'stress_level')::float), 0) as stress_level

                FROM main_benchmarkrun br
                JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
                JOIN main_genericagent ga ON br.agent_definition_id = ga.id
                LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
                WHERE br.execution_date IS NOT NULL
                  AND br.semantic_score IS NOT NULL
            )

            -- Combine all events and order chronologically
            SELECT * FROM stage_events
            UNION ALL
            SELECT * FROM tool_events
            UNION ALL
            SELECT * FROM evaluation_events
            ORDER BY run_id, event_timestamp;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_chronological_events;"
        ),
    ]
