# Generated migration to fix missing columns in Grafana views

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0008_enhance_grafana_views_for_dashboard_improvements'),
    ]

    operations = [
        # Drop and recreate grafana_llm_performance view to include missing columns
        migrations.RunSQL(
            sql="""
            DROP VIEW IF EXISTS grafana_llm_performance;
            CREATE VIEW grafana_llm_performance AS
            SELECT
                br.id as run_id,
                br.execution_date,
                br.agent_definition_id,
                ga.role as agent_role,
                br.agent_version,
                COALESCE(lc.model_name, 'unknown') as llm_model,
                br.llm_config_id,
                lc.name as llm_config_name,
                lc.temperature as llm_temperature,
                lc.input_token_price,
                lc.output_token_price,
                br.success_rate,
                br.mean_duration as mean_duration_ms,
                br.median_duration as median_duration_ms,
                br.total_input_tokens,
                br.total_output_tokens,
                (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0)) as total_tokens,
                br.estimated_cost,
                br.semantic_score,
                bs.name as scenario_name,
                bs.agent_role as workflow_type,
                bs.description as scenario_description,
                -- Extract context variables from parameters
                CASE
                    WHEN (br.parameters->'context_variables'->'trust_level'->>'value') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'trust_level'->>'value')::int
                    WHEN (br.parameters->'context_variables'->>'trust_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->>'trust_level')::int
                    ELSE NULL
                END as trust_level,
                -- Extract mood valence
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->'valence'->>'value') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->'valence'->>'value')::float
                    WHEN (br.parameters->'context_variables'->'mood'->>'valence') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'valence')::float
                    ELSE NULL
                END as mood_valence,
                -- Extract mood arousal
                CASE
                    WHEN (br.parameters->'context_variables'->'mood'->'arousal'->>'value') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->'arousal'->>'value')::float
                    WHEN (br.parameters->'context_variables'->'mood'->>'arousal') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'mood'->>'arousal')::float
                    ELSE NULL
                END as mood_arousal,
                -- Extract stress level
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->'stress_level'->>'value') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->'stress_level'->>'value')::int
                    WHEN (br.parameters->'context_variables'->'environment'->>'stress_level') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'stress_level')::int
                    ELSE NULL
                END as stress_level,
                -- Extract time pressure
                CASE
                    WHEN (br.parameters->'context_variables'->'environment'->'time_pressure'->>'value') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->'time_pressure'->>'value')::int
                    WHEN (br.parameters->'context_variables'->'environment'->>'time_pressure') IS NOT NULL
                    THEN (br.parameters->'context_variables'->'environment'->>'time_pressure')::int
                    ELSE NULL
                END as time_pressure,
                -- Performance category based on success rate
                CASE
                    WHEN br.success_rate >= 0.9 THEN 'Excellent'
                    WHEN br.success_rate >= 0.7 THEN 'Good'
                    WHEN br.success_rate >= 0.5 THEN 'Fair'
                    ELSE 'Poor'
                END as performance_category,
                -- Cost per success
                CASE
                    WHEN br.success_rate > 0
                    THEN br.estimated_cost / br.success_rate
                    ELSE NULL
                END as cost_per_success,
                -- Cost per token
                CASE
                    WHEN (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0)) > 0
                    THEN br.estimated_cost / (COALESCE(br.total_input_tokens, 0) + COALESCE(br.total_output_tokens, 0))
                    ELSE NULL
                END as cost_per_token,
                -- Add prompt_version column (using agent_version as fallback)
                COALESCE(br.agent_version, '1.0.0') as prompt_version
            FROM main_benchmarkrun br
            LEFT JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            LEFT JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL
            ORDER BY br.execution_date DESC;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_llm_performance;"
        ),

        # Create missing view for detailed benchmark analysis
        migrations.RunSQL(
            sql="""
            DROP VIEW IF EXISTS grafana_detailed_benchmark_analysis;
            CREATE VIEW grafana_detailed_benchmark_analysis AS
            SELECT
                br.id as run_id,
                br.execution_date,
                ga.role as agent_role,
                bs.agent_role as workflow_type,
                lc.name as llm_config_name,
                COALESCE(br.agent_version, '1.0.0') as prompt_version,
                br.success_rate,
                br.semantic_score,
                bs.name as scenario_name,
                COALESCE(lc.model_name, 'unknown') as llm_model,
                -- Calculate prompt length from system instructions
                LENGTH(COALESCE(ga.system_instructions, '')) as prompt_length,
                br.total_input_tokens,
                br.total_output_tokens,
                -- Use system instructions as full prompt
                COALESCE(ga.system_instructions, 'No prompt available') as full_prompt,
                -- Use raw_results as conversation data
                COALESCE(br.raw_results::text, '{}') as raw_conversation_data
            FROM main_benchmarkrun br
            LEFT JOIN main_benchmarkscenario bs ON br.scenario_id = bs.id
            LEFT JOIN main_genericagent ga ON br.agent_definition_id = ga.id
            LEFT JOIN main_llmconfig lc ON br.llm_config_id = lc.id
            WHERE br.execution_date IS NOT NULL
            ORDER BY br.execution_date DESC;
            """,
            reverse_sql="DROP VIEW IF EXISTS grafana_detailed_benchmark_analysis;"
        ),
    ]
