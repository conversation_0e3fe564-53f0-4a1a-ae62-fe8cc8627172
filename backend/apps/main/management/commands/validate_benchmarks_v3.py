"""
Django management command for validating benchmark scenarios using Pydantic models.
"""

import os
import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, Set
import re

from django.core.management.base import BaseCommand, CommandError

from apps.main.models import BenchmarkScenario, EvaluationCriteriaTemplate
from apps.main.schemas.benchmark import (
    PydanticSchemaValidator, BenchmarkNamingConvention
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Add a handler to output logs to the console
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)


class Command(BaseCommand):
    help = 'Validate benchmark scenarios using Pydantic models with enhanced error reporting'

    def add_arguments(self, parser):
        parser.add_argument(
            '--scenario-id',
            type=int,
            help='Validate a specific scenario by ID'
        )
        parser.add_argument(
            '--agent-role',
            type=str,
            help='Validate scenarios for a specific agent role'
        )
        parser.add_argument(
            '--file',
            type=str,
            help='Validate a specific file'
        )
        parser.add_argument(
            '--directory',
            type=str,
            help='Validate all files in a specific directory'
        )
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix common issues in scenarios'
        )
        parser.add_argument(
            '--rename',
            action='store_true',
            help='Rename files to follow the naming convention'
        )
        parser.add_argument(
            '--export',
            action='store_true',
            help='Export scenario definitions as schema-compliant JSON files'
        )
        parser.add_argument(
            '--output-dir',
            type=str,
            default='validated_scenarios',
            help='Directory to save validated scenario files (defaults to "validated_scenarios")'
        )
        parser.add_argument(
            '--report-format',
            type=str,
            choices=['json', 'text'],
            default='text',
            help='Format for validation reports (defaults to "text")'
        )
        parser.add_argument(
            '--validate-files',
            action='store_true',
            help='Validate scenario files in the benchmark data directory'
        )
        parser.add_argument(
            '--validate-structure',
            action='store_true',
            help='Validate the directory structure'
        )
        parser.add_argument(
            '--validate-names',
            action='store_true',
            help='Validate file names against the naming convention'
        )
        parser.add_argument(
            '--comprehensive',
            action='store_true',
            help='Perform comprehensive validation (structure, files, names, and database)'
        )

    def handle(self, *args, **options):
        # Set up event loop and run async logic
        try:
            asyncio.run(self.async_handle(**options))
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"An unexpected error occurred: {e}"))
            import traceback
            self.stderr.write(traceback.format_exc())
            raise

    async def async_handle(self, **options):
        # Initialize validator
        validator = PydanticSchemaValidator()

        # Extract options
        scenario_id = options.get('scenario_id')
        agent_role = options.get('agent_role')
        file_path = options.get('file')
        directory = options.get('directory')
        fix = options.get('fix')
        rename = options.get('rename')
        export = options.get('export')
        output_dir = options.get('output_dir')
        report_format = options.get('report_format')
        validate_files = options.get('validate_files')
        validate_structure = options.get('validate_structure')
        validate_names = options.get('validate_names')
        comprehensive = options.get('comprehensive')

        # Determine validation mode
        if comprehensive:
            validate_files = True
            validate_structure = True
            validate_names = True

        # Initialize results
        results = {
            'timestamp': datetime.now().isoformat(),
            'database_scenarios': {
                'checked': 0,
                'issues_found': 0,
                'fixes_applied': 0,
                'results': {}
            },
            'file_scenarios': {
                'checked': 0,
                'valid': 0,
                'invalid': 0,
                'results': []
            },
            'directory_structure': {
                'valid': True,
                'missing_dirs': [],
                'extra_dirs': []
            },
            'naming_convention': {
                'checked': 0,
                'valid': 0,
                'invalid': 0,
                'renamed': 0,
                'results': []
            }
        }

        # Validate directory structure if requested
        if validate_structure or comprehensive:
            self.stdout.write("Validating directory structure...")
            structure_result = await self.validate_directory_structure()
            results['directory_structure'] = structure_result

            if structure_result['valid']:
                self.stdout.write(self.style.SUCCESS("Directory structure is valid"))
            else:
                self.stdout.write(self.style.ERROR("Directory structure is invalid"))
                if structure_result['missing_dirs']:
                    self.stdout.write("Missing directories:")
                    for dir_path in structure_result['missing_dirs']:
                        self.stdout.write(f"  - {dir_path}")
                if structure_result['extra_dirs']:
                    self.stdout.write("Unexpected directories:")
                    for dir_path in structure_result['extra_dirs']:
                        self.stdout.write(f"  - {dir_path}")

        # Validate specific file if requested
        if file_path:
            self.stdout.write(f"Validating file: {file_path}")
            file_result = await self.validate_file(file_path, validator, fix=fix)
            results['file_scenarios']['checked'] = 1
            results['file_scenarios']['valid'] = 1 if file_result['valid'] else 0
            results['file_scenarios']['invalid'] = 0 if file_result['valid'] else 1
            results['file_scenarios']['results'] = [file_result]

            if file_result['valid']:
                self.stdout.write(self.style.SUCCESS(f"File {file_path} is valid"))
            else:
                self.stdout.write(self.style.ERROR(f"File {file_path} is invalid"))
                for error in file_result['errors']:
                    self.stdout.write(f"  - {error}")

            # Validate name if requested
            if validate_names:
                name_result = self.validate_file_name(file_path, rename=rename)
                results['naming_convention']['checked'] = 1
                results['naming_convention']['valid'] = 1 if name_result['valid'] else 0
                results['naming_convention']['invalid'] = 0 if name_result['valid'] else 1
                results['naming_convention']['renamed'] = 1 if name_result.get('renamed') else 0
                results['naming_convention']['results'] = [name_result]

                if name_result['valid']:
                    self.stdout.write(self.style.SUCCESS(f"File name {os.path.basename(file_path)} follows the naming convention"))
                else:
                    self.stdout.write(self.style.ERROR(f"File name {os.path.basename(file_path)} does not follow the naming convention"))
                    if 'suggestion' in name_result:
                        self.stdout.write(f"  Suggested name: {name_result['suggestion']}")
                    if name_result.get('renamed'):
                        self.stdout.write(self.style.SUCCESS(f"  Renamed to: {name_result['new_name']}"))

        # Validate directory if requested
        elif directory:
            self.stdout.write(f"Validating directory: {directory}")
            dir_results = await self.validate_directory(directory, validator, fix=fix, rename=rename and validate_names)
            results['file_scenarios']['checked'] = dir_results['total']
            results['file_scenarios']['valid'] = dir_results['valid']
            results['file_scenarios']['invalid'] = dir_results['invalid']
            results['file_scenarios']['results'] = dir_results['results']

            if validate_names:
                results['naming_convention']['checked'] = dir_results['naming']['checked']
                results['naming_convention']['valid'] = dir_results['naming']['valid']
                results['naming_convention']['invalid'] = dir_results['naming']['invalid']
                results['naming_convention']['renamed'] = dir_results['naming']['renamed']
                results['naming_convention']['results'] = dir_results['naming']['results']

            if dir_results['invalid'] == 0:
                self.stdout.write(self.style.SUCCESS(f"All {dir_results['total']} files in {directory} are valid"))
            else:
                self.stdout.write(self.style.ERROR(f"{dir_results['invalid']} of {dir_results['total']} files in {directory} are invalid"))
                for file_result in dir_results['results']:
                    if not file_result['valid']:
                        self.stdout.write(f"  - {file_result['file_path']}")
                        for error in file_result['errors']:
                            self.stdout.write(f"    - {error}")

            if validate_names:
                if dir_results['naming']['invalid'] == 0:
                    self.stdout.write(self.style.SUCCESS(f"All {dir_results['naming']['checked']} file names in {directory} follow the naming convention"))
                else:
                    self.stdout.write(self.style.ERROR(f"{dir_results['naming']['invalid']} of {dir_results['naming']['checked']} file names in {directory} do not follow the naming convention"))
                    if dir_results['naming']['renamed'] > 0:
                        self.stdout.write(self.style.SUCCESS(f"  {dir_results['naming']['renamed']} files were renamed"))

        # Validate all scenario files if requested
        elif validate_files or comprehensive:
            self.stdout.write("Validating all scenario files...")
            all_results = await self.validate_all_files(validator, fix=fix, rename=rename and validate_names)
            results['file_scenarios']['checked'] = all_results['total']
            results['file_scenarios']['valid'] = all_results['valid']
            results['file_scenarios']['invalid'] = all_results['invalid']
            results['file_scenarios']['results'] = all_results['results']

            if validate_names:
                results['naming_convention']['checked'] = all_results['naming']['checked']
                results['naming_convention']['valid'] = all_results['naming']['valid']
                results['naming_convention']['invalid'] = all_results['naming']['invalid']
                results['naming_convention']['renamed'] = all_results['naming']['renamed']
                results['naming_convention']['results'] = all_results['naming']['results']

            if all_results['invalid'] == 0:
                self.stdout.write(self.style.SUCCESS(f"All {all_results['total']} scenario files are valid"))
            else:
                self.stdout.write(self.style.ERROR(f"{all_results['invalid']} of {all_results['total']} scenario files are invalid"))
                for file_result in all_results['results']:
                    if not file_result['valid']:
                        self.stdout.write(f"  - {file_result['file_path']}")
                        for error in file_result['errors'][:5]:  # Limit to first 5 errors
                            self.stdout.write(f"    - {error}")
                        if len(file_result['errors']) > 5:
                            self.stdout.write(f"    - ... and {len(file_result['errors']) - 5} more errors")

            if validate_names:
                if all_results['naming']['invalid'] == 0:
                    self.stdout.write(self.style.SUCCESS(f"All {all_results['naming']['checked']} file names follow the naming convention"))
                else:
                    self.stdout.write(self.style.ERROR(f"{all_results['naming']['invalid']} of {all_results['naming']['checked']} file names do not follow the naming convention"))
                    if all_results['naming']['renamed'] > 0:
                        self.stdout.write(self.style.SUCCESS(f"  {all_results['naming']['renamed']} files were renamed"))

        # Validate database scenarios if no specific validation mode is requested
        # or if scenario_id or agent_role is specified
        if not (validate_files or validate_structure or validate_names or file_path or directory) or scenario_id or agent_role:
            # Load benchmarks to validate
            if scenario_id:
                try:
                    scenarios = [BenchmarkScenario.objects.get(id=scenario_id)]
                    self.stdout.write(f"Validating benchmark scenario: {scenarios[0].name}")
                except BenchmarkScenario.DoesNotExist:
                    raise CommandError(f"Benchmark scenario with ID {scenario_id} not found")
            elif agent_role:
                scenarios = BenchmarkScenario.objects.filter(agent_role=agent_role, is_active=True)
                self.stdout.write(f"Validating {scenarios.count()} benchmark scenarios for agent role: {agent_role}")
            else:
                scenarios = BenchmarkScenario.objects.filter(is_active=True)
                self.stdout.write(f"Validating all {scenarios.count()} active benchmark scenarios")

            # Process scenarios
            validation_results = {}
            issues_found = 0
            fixes_applied = 0

            for scenario in scenarios:
                # Convert to dict for validation
                scenario_dict = {
                    'name': scenario.name,
                    'description': scenario.description,
                    'agent_role': scenario.agent_role,
                    'input_data': scenario.input_data,
                    'metadata': scenario.metadata,
                    'is_active': scenario.is_active
                }

                # Validate scenario
                validation_result = validator.validate_benchmark_scenario(scenario_dict)
                validation_results[str(scenario.id)] = {
                    'name': scenario.name,
                    'result': validation_result
                }

                if not validation_result['valid']:
                    issues_found += 1

                    # Try to fix common issues if requested
                    if fix:
                        fixed = await self.fix_scenario(scenario, validation_result)
                        if fixed:
                            fixes_applied += 1
                            validation_results[str(scenario.id)]['fixed'] = True

            # Update results
            results['database_scenarios']['checked'] = len(scenarios)
            results['database_scenarios']['issues_found'] = issues_found
            results['database_scenarios']['fixes_applied'] = fixes_applied
            results['database_scenarios']['results'] = validation_results

            # Export if requested
            if export:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                export_dir = f"{output_dir}_{timestamp}"

                self.stdout.write(f"Exporting schema-compliant scenario definitions to {export_dir}...")
                export_result = await self.export_scenarios(scenarios, export_dir, validator)

                if 'error' in export_result:
                    self.stdout.write(self.style.ERROR(f"Error exporting scenarios: {export_result['error']}"))
                else:
                    self.stdout.write(self.style.SUCCESS(
                        f"Exported {export_result['total']} scenarios: "
                        f"{export_result['valid']} valid, {export_result['invalid']} invalid"
                    ))

        # Output results
        if report_format == 'json':
            # Output JSON report
            self.stdout.write(json.dumps(results, indent=2))
        else:
            # Output text report
            self.stdout.write("\nValidation Results Summary:")
            self.stdout.write("==========================")

            if validate_structure or comprehensive:
                if results['directory_structure']['valid']:
                    self.stdout.write(self.style.SUCCESS("✓ Directory structure is valid"))
                else:
                    self.stdout.write(self.style.ERROR("✗ Directory structure is invalid"))
                    self.stdout.write(f"  Missing directories: {len(results['directory_structure']['missing_dirs'])}")
                    self.stdout.write(f"  Extra directories: {len(results['directory_structure']['extra_dirs'])}")

            if file_path or directory or validate_files or comprehensive:
                if results['file_scenarios']['invalid'] == 0:
                    self.stdout.write(self.style.SUCCESS(
                        f"✓ All {results['file_scenarios']['checked']} scenario files are valid"
                    ))
                else:
                    self.stdout.write(self.style.ERROR(
                        f"✗ {results['file_scenarios']['invalid']} of {results['file_scenarios']['checked']} "
                        f"scenario files are invalid"
                    ))

            if validate_names or comprehensive:
                if results['naming_convention']['invalid'] == 0:
                    self.stdout.write(self.style.SUCCESS(
                        f"✓ All {results['naming_convention']['checked']} file names follow the naming convention"
                    ))
                else:
                    self.stdout.write(self.style.ERROR(
                        f"✗ {results['naming_convention']['invalid']} of {results['naming_convention']['checked']} "
                        f"file names do not follow the naming convention"
                    ))
                    if results['naming_convention']['renamed'] > 0:
                        self.stdout.write(self.style.SUCCESS(
                            f"  {results['naming_convention']['renamed']} files were renamed"
                        ))

            if not (validate_files or validate_structure or validate_names or file_path or directory) or scenario_id or agent_role:
                self.stdout.write(f"Database scenarios checked: {results['database_scenarios']['checked']}")
                self.stdout.write(f"Issues found: {results['database_scenarios']['issues_found']}")

                if fix:
                    self.stdout.write(f"Fixes applied: {results['database_scenarios']['fixes_applied']}")

                if results['database_scenarios']['issues_found'] == 0:
                    self.stdout.write(self.style.SUCCESS("✓ All database scenarios are valid"))
                else:
                    self.stdout.write(self.style.ERROR(
                        f"✗ {results['database_scenarios']['issues_found']} database scenarios have issues"
                    ))

            # Overall result
            all_valid = (
                results['directory_structure']['valid'] and
                results['file_scenarios']['invalid'] == 0 and
                results['naming_convention']['invalid'] == 0 and
                results['database_scenarios']['issues_found'] == 0
            )

            if all_valid:
                self.stdout.write(self.style.SUCCESS("\nAll validations passed successfully!"))
            else:
                self.stdout.write(self.style.ERROR("\nSome validations failed. See details above."))

                if not fix and results['database_scenarios']['issues_found'] > 0:
                    self.stdout.write(
                        "\nTip: Run with --fix to attempt automatic fixes for common issues."
                    )

                if not rename and results['naming_convention']['invalid'] > 0:
                    self.stdout.write(
                        "\nTip: Run with --rename to automatically rename files to follow the naming convention."
                    )

    async def validate_directory_structure(self) -> Dict[str, Any]:
        """
        Validate the benchmark directory structure.

        Returns:
            Dictionary with validation results
        """
        base_path = 'backend/testing/benchmark_data'
        expected_dirs = [
            'agents/mentor/wheel_generation',
            'agents/mentor/discussion',
            'agents/mentor/feedback',
            'agents/orchestrator',
            'agents/strategy',
            'workflows/wheel_generation',
            'workflows/activity_feedback',
            'templates/evaluation_criteria'
        ]

        results = {
            'valid': True,
            'missing_dirs': [],
            'extra_dirs': []
        }

        # Check for missing directories
        logger.info("Checking for missing expected directories...")
        for dir_path in expected_dirs:
            full_path = os.path.join(base_path, dir_path)
            if not os.path.exists(full_path):
                logger.warning(f"Missing expected directory: {dir_path}")
                results['valid'] = False
                results['missing_dirs'].append(dir_path)
            else:
                logger.info(f"Found expected directory: {dir_path}")

        # Check for unexpected directories
        logger.info("Checking for unexpected directories...")
        for root, dirs, _ in os.walk(base_path):
            rel_path = os.path.relpath(root, base_path)
            if rel_path == '.':
                continue

            # Skip the scenarios directory (old structure)
            if rel_path == 'scenarios':
                logger.warning(f"Dismissing old scenarios directory: {rel_path}")
                continue

            # Check if this directory is expected
            if rel_path not in expected_dirs and not any(rel_path.startswith(d + '/') for d in expected_dirs):
                logger.warning(f"Found unexpected directory: {rel_path}")
                results['extra_dirs'].append(rel_path)
            else:
                logger.info(f"Checking expected directory: {rel_path}")

        return results

    async def validate_file(self, file_path: str, validator: PydanticSchemaValidator, fix: bool = False) -> Dict[str, Any]:
        """
        Validate a single file.

        Args:
            file_path: Path to the file to validate
            validator: Schema validator to use
            fix: Whether to attempt to fix issues

        Returns:
            Dictionary with validation results
        """
        try:
            # Determine schema type based on file path
            schema_type = None
            if 'agents/' in file_path:
                schema_type = 'benchmark_scenario'
            elif 'workflows/' in file_path:
                schema_type = 'benchmark_scenario'
            elif 'templates/evaluation_criteria' in file_path:
                schema_type = 'evaluation_criteria'

            # Validate the file
            validation_result = validator.validate_file(file_path, schema_type)

            result = {
                'file_path': file_path,
                'valid': validation_result.is_valid,
                'errors': validation_result.errors
            }

            # Try to fix issues if requested
            if fix and not validation_result.is_valid:
                fixed_result = await self.fix_file(file_path, validation_result.errors)
                if fixed_result['fixed']:
                    result['fixed'] = True
                    result['fixes'] = fixed_result['fixes']

                    # Re-validate after fixing
                    revalidation = validator.validate_file(file_path, schema_type)
                    result['valid'] = revalidation.is_valid
                    result['errors'] = revalidation.errors

            return result
        except Exception as e:
            logger.error(f"Error validating file {file_path}", exc_info=True)
            return {
                'file_path': file_path,
                'valid': False,
                'errors': [f"Validation error: {str(e)}"]
            }

    def validate_file_name(self, file_path: str, rename: bool = False) -> Dict[str, Any]:
        """
        Validate a file name against the naming convention.

        Args:
            file_path: Path to the file
            rename: Whether to rename the file if it doesn't follow the convention

        Returns:
            Dictionary with validation results
        """
        file_name = os.path.basename(file_path)

        # Skip non-JSON files
        if not file_name.endswith('.json'):
            return {
                'file_path': file_path,
                'valid': True,
                'message': "Not a JSON file, skipping name validation"
            }

        # Skip template files
        if 'templates/' in file_path:
            return {
                'file_path': file_path,
                'valid': True,
                'message': "Template file, skipping name validation"
            }

        # Validate against naming convention
        is_valid = BenchmarkNamingConvention.validate_name(file_name)
        result = {
            'file_path': file_path,
            'valid': is_valid
        }

        if not is_valid:
            # Try to suggest a better name
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                # Extract agent role and metadata
                agent_role = data.get('agent_role', 'unknown')
                metadata = data.get('metadata', {})

                # Get existing IDs to avoid duplicates
                existing_ids = self.get_existing_ids(os.path.dirname(file_path))

                # Suggest a name
                suggested_name = BenchmarkNamingConvention.suggest_name_from_metadata(
                    metadata, agent_role, existing_ids
                )

                result['suggestion'] = suggested_name + '.json'

                # Rename if requested
                if rename:
                    new_path = os.path.join(os.path.dirname(file_path), suggested_name + '.json')
                    os.rename(file_path, new_path)
                    result['renamed'] = True
                    result['new_name'] = suggested_name + '.json'
                    result['new_path'] = new_path
            except Exception as e:
                logger.error(f"Error suggesting name for {file_path}", exc_info=True)
                result['error'] = f"Error suggesting name: {str(e)}"

        return result

    def get_existing_ids(self, directory: str) -> Set[int]:
        """
        Get existing IDs from files in a directory.

        Args:
            directory: Directory to scan

        Returns:
            Set of existing IDs
        """
        ids = set()
        for file_name in os.listdir(directory):
            if file_name.endswith('.json'):
                try:
                    # Try to extract ID from file name
                    match = re.search(r'_(\d+)\.json$', file_name)
                    if match:
                        ids.add(int(match.group(1)))
                except Exception:
                    pass
        return ids

    async def validate_directory(self, directory: str, validator: PydanticSchemaValidator,
                               fix: bool = False, rename: bool = False) -> Dict[str, Any]:
        """
        Validate all files in a directory.

        Args:
            directory: Directory to validate
            validator: Schema validator to use
            fix: Whether to attempt to fix issues
            rename: Whether to rename files that don't follow the naming convention

        Returns:
            Dictionary with validation results
        """
        results = {
            'total': 0,
            'valid': 0,
            'invalid': 0,
            'results': [],
            'naming': {
                'checked': 0,
                'valid': 0,
                'invalid': 0,
                'renamed': 0,
                'results': []
            }
        }

        # Get all JSON files in the directory
        json_files = []
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.json'):
                    json_files.append(os.path.join(root, file))

        results['total'] = len(json_files)

        # Validate each file
        for file_path in json_files:
            # Validate schema
            file_result = await self.validate_file(file_path, validator, fix=fix)
            results['results'].append(file_result)

            if file_result['valid']:
                results['valid'] += 1
            else:
                results['invalid'] += 1

            # Validate name
            if rename:
                name_result = self.validate_file_name(file_path, rename=rename)
                results['naming']['checked'] += 1
                results['naming']['results'].append(name_result)

                if name_result['valid']:
                    results['naming']['valid'] += 1
                else:
                    results['naming']['invalid'] += 1
                    if name_result.get('renamed'):
                        results['naming']['renamed'] += 1
                        # Update file path in results if renamed
                        file_result['file_path'] = name_result['new_path']

        return results

    async def validate_all_files(self, validator: PydanticSchemaValidator,
                               fix: bool = False, rename: bool = False) -> Dict[str, Any]:
        """
        Validate all benchmark files.

        Args:
            validator: Schema validator to use
            fix: Whether to attempt to fix issues
            rename: Whether to rename files that don't follow the naming convention

        Returns:
            Dictionary with validation results
        """
        base_path = 'backend/testing/benchmark_data'

        # Get all JSON files
        json_files = []
        for root, _, files in os.walk(base_path):
            for file in files:
                if file.endswith('.json'):
                    file_path = os.path.join(root, file)
                    # Exclude template files from scenario files
                    if '/templates/evaluation_criteria/' not in file_path.replace('\\', '/'):
                        json_files.append(file_path)

        results = {
            'total': len(json_files),
            'valid': 0,
            'invalid': 0,
            'results': [],
            'naming': {
                'checked': 0,
                'valid': 0,
                'invalid': 0,
                'renamed': 0,
                'results': []
            }
        }

        # Validate each file
        for file_path in json_files:
            # Validate schema
            file_result = await self.validate_file(file_path, validator, fix=fix)
            results['results'].append(file_result)

            if file_result['valid']:
                results['valid'] += 1
            else:
                results['invalid'] += 1

            # Validate name
            if rename:
                name_result = self.validate_file_name(file_path, rename=rename)
                results['naming']['checked'] += 1
                results['naming']['results'].append(name_result)

                if name_result['valid']:
                    results['naming']['valid'] += 1
                else:
                    results['naming']['invalid'] += 1
                    if name_result.get('renamed'):
                        results['naming']['renamed'] += 1
                        # Update file path in results if renamed
                        file_result['file_path'] = name_result['new_path']

        return results

    async def fix_file(self, file_path: str, errors: List[str]) -> Dict[str, Any]:
        """
        Fix common issues in a scenario file.

        Args:
            file_path: Path to the file to fix
            errors: List of validation errors

        Returns:
            Dictionary with fix results
        """
        import json

        result = {
            'fixed': False,
            'fixes': []
        }

        try:
            # Read the file
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Fix common issues
            for error in errors:
                if 'workflow_type' in error.lower():
                    # Add default workflow_type based on file path or agent role
                    if 'metadata' not in data:
                        data['metadata'] = {}

                    # Try to infer workflow type from path or use default
                    if 'wheel_generation' in file_path:
                        data['metadata']['workflow_type'] = 'wheel_generation'
                    else:
                        data['metadata']['workflow_type'] = 'test_workflow'

                    result['fixes'].append(f"Added workflow_type: {data['metadata']['workflow_type']}")
                    result['fixed'] = True

                elif 'expected_quality_criteria' in error.lower():
                    # Add basic expected_quality_criteria
                    if 'metadata' not in data:
                        data['metadata'] = {}

                    data['metadata']['expected_quality_criteria'] = {
                        'Content': ['relevant', 'helpful'],
                        'Tone': ['supportive', 'friendly']
                    }

                    result['fixes'].append("Added basic expected_quality_criteria")
                    result['fixed'] = True

            # Write the file back if we made changes
            if result['fixed']:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Error fixing file {file_path}: {str(e)}", exc_info=True)
            result['fixed'] = False
            result['fixes'] = [f"Error fixing file: {str(e)}"]

        return result

    async def fix_scenario(self, scenario, validation_result: Dict[str, Any]) -> bool:
        """
        Fix common issues in a database scenario.

        Args:
            scenario: BenchmarkScenario instance to fix
            validation_result: Validation result with errors

        Returns:
            bool: True if fixes were applied, False otherwise
        """
        from asgiref.sync import sync_to_async

        fixed = False

        try:
            errors = validation_result.get('errors', [])

            for error in errors:
                if 'workflow_type' in error.lower():
                    # Add default workflow_type
                    if not scenario.metadata:
                        scenario.metadata = {}

                    # Try to infer workflow type from agent role
                    if scenario.agent_role == 'mentor':
                        scenario.metadata['workflow_type'] = 'wheel_generation'
                    else:
                        scenario.metadata['workflow_type'] = 'test_workflow'

                    fixed = True

                elif 'expected_quality_criteria' in error.lower():
                    # Add basic expected_quality_criteria
                    if not scenario.metadata:
                        scenario.metadata = {}

                    scenario.metadata['expected_quality_criteria'] = {
                        'Content': ['relevant', 'helpful'],
                        'Tone': ['supportive', 'friendly']
                    }

                    fixed = True

            # Save the scenario if we made changes
            if fixed:
                await sync_to_async(scenario.save)()

        except Exception as e:
            logger.error(f"Error fixing scenario {scenario.id}: {str(e)}", exc_info=True)
            fixed = False

        return fixed

    async def export_scenarios(self, scenarios, export_dir: str, validator: PydanticSchemaValidator) -> Dict[str, Any]:
        """
        Export scenarios to schema-compliant JSON files.

        Args:
            scenarios: List of BenchmarkScenario instances to export
            export_dir: Directory to export files to
            validator: Schema validator to use

        Returns:
            Dictionary with export results
        """
        import json
        from apps.main.schemas.benchmark import BenchmarkNamingConvention

        result = {
            'total': len(scenarios),
            'valid': 0,
            'invalid': 0,
            'exported_files': []
        }

        try:
            # Create export directory
            os.makedirs(export_dir, exist_ok=True)

            for scenario in scenarios:
                try:
                    # Convert scenario to dict
                    scenario_dict = {
                        'name': scenario.name,
                        'description': scenario.description,
                        'agent_role': scenario.agent_role,
                        'input_data': scenario.input_data,
                        'metadata': scenario.metadata,
                        'is_active': scenario.is_active
                    }

                    # Validate the scenario
                    validation_result = validator.validate_benchmark_scenario(scenario_dict)

                    # Generate filename
                    try:
                        filename = BenchmarkNamingConvention.suggest_name_from_metadata(scenario.metadata)
                    except:
                        filename = f"{scenario.agent_role}_{scenario.name.replace(' ', '_')}"

                    # Add validation status to filename
                    if validation_result['valid']:
                        filename += "_valid.json"
                        result['valid'] += 1
                    else:
                        filename += "_invalid.json"
                        result['invalid'] += 1

                    # Write the file
                    file_path = os.path.join(export_dir, filename)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(scenario_dict, f, indent=2, ensure_ascii=False)

                    result['exported_files'].append({
                        'scenario_id': scenario.id,
                        'filename': filename,
                        'valid': validation_result['valid'],
                        'errors': validation_result.get('errors', [])
                    })

                except Exception as e:
                    logger.error(f"Error exporting scenario {scenario.id}: {str(e)}", exc_info=True)
                    result['invalid'] += 1

        except Exception as e:
            logger.error(f"Error creating export directory {export_dir}: {str(e)}", exc_info=True)
            return {'error': str(e)}

        return result