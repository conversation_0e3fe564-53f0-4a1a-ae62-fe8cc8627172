import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.contrib.auth.models import User
from apps.user.models import GenericUserLimitation # Removed unused imports

class Command(BaseCommand):
    help = 'Seeds generic user limitations. Skips if already applied by run_seeders.'
    # Re-add command name definition
    COMMAND_NAME = os.path.splitext(os.path.basename(__file__))[0]

    def handle(self, *args, **kwargs):
                # Check environment variable to potentially skip idempotency check
        skip_check = os.environ.get('SKIP_SEEDER_IDEMPOTENCY_CHECK') == 'true'

        if not skip_check:
            # Import and perform the check only if the environment variable is not set
            from apps.main.models import AppliedSeedingCommand # Import locally
            if AppliedSeedingCommand.objects.filter(command_name=self.COMMAND_NAME).exists():
                self.stdout.write(self.style.WARNING(f"Command '{self.COMMAND_NAME}' has already been applied by run_seeders. Skipping."))
                return
        else:
             self.stdout.write(self.style.WARNING(f"Skipping AppliedSeedingCommand check for '{self.COMMAND_NAME}' due to environment variable."))

        self.stdout.write(self.style.SUCCESS(f"Running command '{self.COMMAND_NAME}' logic (will skip individual existing items)..."))

        try:
            # 2. Execute the seeding logic within a transaction
            with transaction.atomic():
                # Create a superuser (this part is already idempotent)
                if not User.objects.filter(username="admin").exists():
                    User.objects.create_superuser("admin", "<EMAIL>", "0000")
                    self.stdout.write(self.style.SUCCESS("Superuser created: admin / 0000"))
                else:
                    self.stdout.write(self.style.WARNING("Superuser 'admin' already exists. Skipping creation."))

                # Seed limitations (this method handles internal idempotency)
                created_count = self.seed_generic_limitations()

                # Ensure AppliedSeedingCommand.objects.create(...) remains removed from here.
                # run_seeders.py handles the recording upon successful completion of this command.
                self.stdout.write(self.style.SUCCESS(f"Finished command '{self.COMMAND_NAME}' logic! Created {created_count} new generic limitations."))

        except Exception as e:
            # Catch any exception during the transaction
            self.stderr.write(self.style.ERROR(f"Command '{self.COMMAND_NAME}' failed internally: {str(e)}"))
            # Re-raise the exception so run_seeders knows it failed and won't record success
            raise CommandError(f"Command '{self.COMMAND_NAME}' failed: {e}") from e


    def seed_generic_limitations(self):
        """
        Create a comprehensive list of potential user limitations if they don't exist.
        Returns the count of newly created limitations.
        """
        self.stdout.write("Seeding generic limitations...")
        limitations = [
            # Physical Limitations
            {"code": "phys_mobility_general", "description": "General mobility limitations affecting overall movement", "limitation_type": "PHYSICAL"},
            {"code": "phys_mobility_upper", "description": "Limited mobility in upper body or arms", "limitation_type": "PHYSICAL"},
            {"code": "phys_mobility_lower", "description": "Limited mobility in lower body or legs", "limitation_type": "PHYSICAL"},
            {"code": "phys_stamina", "description": "Limited physical stamina or endurance", "limitation_type": "PHYSICAL"},
            {"code": "phys_strength", "description": "Limited physical strength", "limitation_type": "PHYSICAL"},
            {"code": "phys_balance", "description": "Balance or coordination challenges", "limitation_type": "PHYSICAL"},
            {"code": "phys_dexterity", "description": "Reduced fine motor skills or dexterity", "limitation_type": "PHYSICAL"},
            {"code": "phys_vision", "description": "Visual impairment", "limitation_type": "PHYSICAL"},
            {"code": "phys_hearing", "description": "Hearing impairment", "limitation_type": "PHYSICAL"},
            {"code": "phys_speech", "description": "Speech or communication difficulties", "limitation_type": "PHYSICAL"},
            {"code": "phys_respiration", "description": "Respiratory limitations (asthma, COPD, etc.)", "limitation_type": "PHYSICAL"},
            {"code": "phys_cardiovascular", "description": "Cardiovascular limitations", "limitation_type": "PHYSICAL"},
            {"code": "phys_chronic_pain", "description": "Chronic pain condition", "limitation_type": "PHYSICAL"},
            
            # Cognitive Limitations
            {"code": "cog_attention", "description": "Attention or focus difficulties", "limitation_type": "COGNITIVE"},
            {"code": "cog_processing", "description": "Information processing speed limitations", "limitation_type": "COGNITIVE"},
            {"code": "cog_memory", "description": "Memory or recall challenges", "limitation_type": "COGNITIVE"},
            {"code": "cog_executive", "description": "Executive function limitations", "limitation_type": "COGNITIVE"},
            {"code": "cog_learning", "description": "Learning or comprehension challenges", "limitation_type": "COGNITIVE"},
            {"code": "cog_literacy", "description": "Reading or writing difficulties", "limitation_type": "COGNITIVE"},
            {"code": "cog_math", "description": "Mathematical or numerical processing limitations", "limitation_type": "COGNITIVE"},
            
            # Emotional/Psychological Limitations
            {"code": "psych_anxiety", "description": "Anxiety-related limitations", "limitation_type": "PSYCHOLOGICAL"},
            {"code": "psych_depression", "description": "Depression-related limitations", "limitation_type": "PSYCHOLOGICAL"},
            {"code": "psych_stress", "description": "Stress sensitivity or management difficulties", "limitation_type": "PSYCHOLOGICAL"},
            {"code": "psych_emotional_regulation", "description": "Emotional regulation challenges", "limitation_type": "PSYCHOLOGICAL"},
            {"code": "psych_social_anxiety", "description": "Social anxiety or discomfort", "limitation_type": "PSYCHOLOGICAL"},
            {"code": "psych_confidence", "description": "Self-confidence or self-esteem limitations", "limitation_type": "PSYCHOLOGICAL"},
            {"code": "psych_trauma", "description": "Trauma-related sensitivities", "limitation_type": "PSYCHOLOGICAL"},
            {"code": "psych_motivation", "description": "Motivation or initiative difficulties", "limitation_type": "PSYCHOLOGICAL"},
            
            # Social Limitations
            {"code": "social_communication", "description": "Social communication challenges", "limitation_type": "SOCIAL"},
            {"code": "social_interpretation", "description": "Difficulty interpreting social cues", "limitation_type": "SOCIAL"},
            {"code": "social_group", "description": "Discomfort in group settings", "limitation_type": "SOCIAL"},
            {"code": "social_strangers", "description": "Difficulty interacting with unfamiliar people", "limitation_type": "SOCIAL"},
            {"code": "social_conflict", "description": "Challenges handling social conflict or criticism", "limitation_type": "SOCIAL"},
            
            # Environmental Limitations
            {"code": "env_outdoor", "description": "Limitations in outdoor environments", "limitation_type": "ENVIRONMENTAL"},
            {"code": "env_noise", "description": "Sensitivity to loud or persistent noise", "limitation_type": "ENVIRONMENTAL"},
            {"code": "env_light", "description": "Sensitivity to bright or flashing lights", "limitation_type": "ENVIRONMENTAL"},
            {"code": "env_temperature", "description": "Sensitivity to temperature extremes", "limitation_type": "ENVIRONMENTAL"},
            {"code": "env_crowds", "description": "Discomfort in crowded spaces", "limitation_type": "ENVIRONMENTAL"},
            {"code": "env_allergens", "description": "Environmental allergies or sensitivities", "limitation_type": "ENVIRONMENTAL"},
            
            # Temporal/Scheduling Limitations
            {"code": "time_morning", "description": "Difficulty with morning activities", "limitation_type": "TEMPORAL"},
            {"code": "time_evening", "description": "Difficulty with evening activities", "limitation_type": "TEMPORAL"},
            {"code": "time_duration", "description": "Limitations with extended duration activities", "limitation_type": "TEMPORAL"},
            {"code": "time_regularity", "description": "Challenges maintaining regular schedules", "limitation_type": "TEMPORAL"},
            {"code": "time_transitions", "description": "Difficulty with transitions between activities", "limitation_type": "TEMPORAL"},
            
            # Resource Limitations
            {"code": "res_financial", "description": "Financial resource limitations", "limitation_type": "RESOURCE"},
            {"code": "res_transportation", "description": "Transportation access limitations", "limitation_type": "RESOURCE"},
            {"code": "res_space", "description": "Limited physical space availability", "limitation_type": "RESOURCE"},
            {"code": "res_equipment", "description": "Limited access to necessary equipment", "limitation_type": "RESOURCE"},
            {"code": "res_digital", "description": "Limited digital device or internet access", "limitation_type": "RESOURCE"},
            {"code": "res_support", "description": "Limited social or professional support", "limitation_type": "RESOURCE"}
        ]
        
        limitation_count = 0
        for limitation in limitations:
            # Check if limitation already exists to avoid duplicates
            if not GenericUserLimitation.objects.filter(code=limitation["code"]).exists():
                GenericUserLimitation.objects.create(
                    code=limitation["code"],
                    description=limitation["description"],
                    limitation_type=limitation["limitation_type"]
                )
                limitation_count += 1
                
        self.stdout.write(self.style.SUCCESS(f"Created {limitation_count} generic user limitations"))
