from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction
from datetime import date, datetime, timedelta
import uuid
from django.contrib.contenttypes.models import ContentType

from apps.user.models import (
    UserProfile, Demographics, UserGoal, Aspiration, Intention, 
    Inspiration, GoalInspiration, TrustLevel, Preference, Belief, 
    BeliefEvidence, BeliefInfluence, CurrentMood, UserTraitInclination, 
    GenericTrait, UserEnvironment, GenericEnvironment, 
    UserEnvironmentPhysicalProperties, UserEnvironmentSocialContext,
    UserEnvironmentActivitySupport, UserEnvironmentPsychologicalQualities,
    Inventory, UserResource, GenericResource, Skill, GenericSkill,
    UserLimitation, GenericUserLimitation, TraitType,
    GenericEnvironmentDomainRelationship # Added import
)
from apps.activity.models import GenericDomain # Added import

class Command(BaseCommand):
    help = 'Seeds the database with PhiPhi\'s user profile data if the user does not already exist.'

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS("Checking for existing PhiPhi user..."))

        # Check if the user 'phiphi' already exists
        if User.objects.filter(username="phiphi").exists():
            self.stdout.write(self.style.WARNING("User 'phiphi' already exists. Skipping seeding."))
            return  # Exit the command early

        # If user doesn't exist, proceed with seeding
        self.stdout.write(self.style.SUCCESS("User 'phiphi' not found. Proceeding with seeding..."))
        
        # Create necessary basic data
        with transaction.atomic():
            self.create_phiphi_user() # This will now only run if the user didn't exist
            self.create_phiphi_demographics()
            self.create_phiphi_environment()
            self.create_phiphi_traits()
            self.create_phiphi_beliefs()
            self.create_phiphi_goals()
            self.create_phiphi_inspirations()
            self.create_phiphi_limitations()
            self.create_phiphi_skills()
            self.create_phiphi_resources()
            self.create_phiphi_preferences()
            self.create_phiphi_mood()
            self.create_phiphi_trust_level()
            self.create_belief_influences()
        
        self.stdout.write(self.style.SUCCESS("PhiPhi's user profile data has been successfully seeded!"))

    def create_phiphi_user(self):
        """Create PhiPhi's user and user profile (assumes user doesn't exist yet)"""
        # Create Django user
        user = User.objects.create_user(
            username="phiphi",
            email="<EMAIL>",
            password="farmlife2025",
            first_name="PhiPhi",
            last_name="Schmidt"
        )
        # Grant admin privileges
        user.is_staff = True
        user.is_superuser = True
        user.save()
        self.stdout.write(self.style.SUCCESS("PhiPhi's user account created and granted admin privileges"))

        # Create UserProfile
        user_profile = UserProfile.objects.create(
            user=user,
            profile_name="PhiPhi"  # Environment will be set later
        )
        self.stdout.write(self.style.SUCCESS("PhiPhi's user profile created"))
        
        self.user_profile = user_profile # Store for other methods

    def create_phiphi_demographics(self):
        """Create PhiPhi's demographic information"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # Create demographics if it doesn't exist
        if not Demographics.objects.filter(user_profile=self.user_profile).exists():
            Demographics.objects.create(
                user_profile=self.user_profile,
                full_name="PhiPhi Schmidt",
                age=22,
                gender="Male",
                location="Lyon, France (originally from Frankfurt, Germany)",
                language="German, English, some French",
                occupation="Undefined / Traveler",
                personal_prefs_json={
                    "likes": ["traveling", "AI", "sailing", "nature", "farm life"],
                    "dislikes": ["conventional career paths", "structured education systems"],
                    "personality_notes": "PhiPhi is reflective, creative, and independent. He values personal freedom and exploration over traditional stability.",
                    "education": "Elite boarding school (incomplete)",
                    "family_background": "Family in Frankfurt with conventional expectations",
                    "lifestyle": "Currently living on a friend's run-down farm near Lyon",
                    "future_vision": "Aspires to create impactful AI-based tools and possibly buy a farm in Italy"
                }
            )
            self.stdout.write(self.style.SUCCESS("PhiPhi's demographics created"))
        else:
            self.stdout.write(self.style.SUCCESS("Using existing PhiPhi demographics"))
            
    def create_phiphi_environment(self):
        """Create PhiPhi's environment - the farm near Lyon"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # First check if a GenericEnvironment for rural farm exists, if not create it
        farm_env_code = "out_nat_rural_farm"
        if not GenericEnvironment.objects.filter(code=farm_env_code).exists():
            generic_farm_env = GenericEnvironment.objects.create(
                code=farm_env_code,
                name="Rural Farm",
                description="An agricultural property with open spaces, natural elements, and basic rural infrastructure.",
                is_indoor=False,
                primary_category="natural",
                typical_space_size="large",
                typical_privacy_level=85,
                # Removed invalid activity support fields:
                # physical_activity_support=80,
                # creative_activity_support=65,
                # social_activity_support=40,
                # learning_activity_support=55,
                # reflective_activity_support=90,
                archetype_attributes={
                    "common_features": ["open land", "basic structures", "agricultural elements", "natural surroundings"],
                    "typical_activities": ["physical labor", "maintenance", "gardening", "outdoor exploration"],
                    "weather_exposure": "high",
                    "typical_challenges": ["resource limitations", "weather dependency", "isolation"]
                }
            )
            self.stdout.write(self.style.SUCCESS("Created generic farm environment"))
            # Add domain relationships for the generic farm environment
            self._create_generic_environment_domain_relationships(generic_farm_env)
        else:
            generic_farm_env = GenericEnvironment.objects.get(code=farm_env_code)
            # Ensure relationships exist even if environment wasn't just created
            self._create_generic_environment_domain_relationships(generic_farm_env)

        # Now create PhiPhi's specific farm environment if it doesn't exist
        env_name = "Lyon Rural Farm"
        if not UserEnvironment.objects.filter(user_profile=self.user_profile, environment_name=env_name).exists():
            farm_env = UserEnvironment.objects.create(
                user_profile=self.user_profile,
                environment_name=env_name,
                environment_description="A rural property with cultivated farmland surrounding it. PhiPhi lives in a small caravan 50 meters from the main house, allowing privacy and connection to nature. There's a workshop with tools, wood, musical instruments like a guitar and shruti box. A river is located about 5km away, which PhiPhi sometimes runs to. The nearest small town Marcigny is 20 minutes by car, while Roanne (40 minutes away) has more social activities like dance events.",
                generic_environment=generic_farm_env,
                effective_start=date(2024, 10, 1),  # Assuming PhiPhi has been there for a few months
                is_current=True,
                environment_details={
                    "property_size": "approximately 5 hectares",
                    "building_condition": "run-down but functional",
                    "distance_to_city": "20 min to Marcigny, 40 min to Roanne",
                    "living_arrangements": "separate caravan 50m from main house",
                    "internet": "extremely reliable - set up for AI work",
                    "neighbors": "sparse, mostly cultivated farmland around",
                    "transportation": "shared car with friend, bicycle and monocycle available",
                    "main_challenges": ["isolation", "limited social opportunities", "distance from amenities"],
                    "main_attractions": ["solitude", "freedom", "space for projects", "good internet for AI work", "workshop with tools"],
                    "musical_instruments": ["guitar", "shruti box"],
                    "nearby_features": ["river (5km away)", "workshop", "cultivated farmland"]
                }
            )
            
            # Create physical properties for the farm
            UserEnvironmentPhysicalProperties.objects.create(
                user_environment=farm_env,
                rurality=85,
                noise_level=20,
                light_quality=90,  # Good natural light during the day
                temperature_range="variable",  # Changes with seasons significantly
                accessibility=60,  # Flat farmland is generally accessible
                air_quality=95,  # Fresh country air
                has_natural_elements=True,
                surface_type="mixed",
                water_proximity=40,  # River about 5km away
                space_size="large"
            )
            
            # Create social context for the farm
            UserEnvironmentSocialContext.objects.create(
                user_environment=farm_env,
                privacy_level=90,  # Very private
                typical_occupancy=15,  # Only PhiPhi and occasionally his friend
                social_interaction_level=25,  # Limited social interactions - once per week with locals
                formality_level=10,  # Extremely casual
                safety_level=75,  # Generally safe rural area
                supervision_level=5,  # Almost none
                cultural_diversity=40  # Some diversity given French/German context
            )
            
            # Create activity support for the farm
            UserEnvironmentActivitySupport.objects.create(
                user_environment=farm_env,
                digital_connectivity=90,  # Very good internet for AI work
                resource_availability=50,  # Workshop with tools, wood, basic materials
                domain_specific_support={
                    'phys_active': 75,       # Good for physical work, repairs, running to river
                    'creative_making': 80,   # Workshop, space for projects
                    'creative_music': 70,    # Musical instruments available
                    'soc_connecting': 30,    # Limited due to isolation
                    'intel_learning': 85,    # Excellent internet connection for AI learning
                    'refl_mindful': 95,      # Excellent for reflection and introspection
                    'prod_skill': 100        # Excellent internet for AI skill development
                },
                time_availability={
                    "weekdays": ["all_day"],
                    "weekends": ["all_day"],
                    "typical_day": {
                        "wake_up": "10:30 AM",
                        "sleep": "2:00 AM",
                        "meal_pattern": "intermittent fasting"
                    },
                    "seasonal_opportunities": {
                        "summer": "potential work at festivals, food truck"
                    }
                }
            )
            
            # Create psychological qualities for the farm
            UserEnvironmentPsychologicalQualities.objects.create(
                user_environment=farm_env,
                restorative_quality=85,  # Very restorative compared to city life
                stimulation_level=45,  # Moderate - peaceful but can be monotonous
                aesthetic_appeal=75,  # Natural beauty despite being mostly cultivated farmland
                novelty_level=70,  # Different from PhiPhi's previous environments
                comfort_level=60,  # Basic comfort in caravan
                personal_significance=85,  # Represents freedom and new chapter
                emotional_associations={
                    "freedom": 90,
                    "peace": 75,
                    "possibility": 80,
                    "creativity": 85,
                    "isolation": 65,
                    "friendship": 80,  # Evening conversations with friend
                    "learning": 85   # AI projects and self-development
                }
            )
            
            # Update user profile to set current environment
            self.user_profile.current_environment = farm_env
            self.user_profile.save()
            
            self.stdout.write(self.style.SUCCESS("PhiPhi's farm environment created with all properties"))
        else:
            farm_env = UserEnvironment.objects.get(user_profile=self.user_profile, environment_name=env_name)
            self.user_profile.current_environment = farm_env
            self.user_profile.save()
            self.stdout.write(self.style.SUCCESS("Using existing PhiPhi farm environment"))
        
        self.farm_environment = farm_env

    def _create_generic_environment_domain_relationships(self, generic_env):
        """Helper to create domain relationships for a generic environment."""
        # Define domain codes and their support strength for a Rural Farm
        # Strengths based on GenericEnvironmentDomainRelationship choices
        domain_support = {
            'phys_active': 70,  # Significant support for physical activity
            'creative_making': 70, # Significant support for creative making (workshop)
            'creative_music': 70, # Significant support for music (instruments)
            'soc_connecting': 10, # Minimal support for social connection (isolation)
            'intel_learning': 100, # Perfect support for learning (good internet)
            'refl_mindful': 100, # Perfect support for reflection/mindfulness (solitude)
            'prod_skill': 100, # Perfect support for skill development (AI work)
        }

        created_count = 0
        for domain_code, strength in domain_support.items():
            try:
                domain = GenericDomain.objects.get(code=domain_code)
                relationship, created = GenericEnvironmentDomainRelationship.objects.update_or_create(
                    generic_environment=generic_env,
                    domain=domain,
                    defaults={'strength': strength}
                )
                if created:
                    created_count += 1
            except GenericDomain.DoesNotExist:
                self.stdout.write(self.style.WARNING(f"GenericDomain with code '{domain_code}' not found. Skipping relationship."))
            except Exception as e:
                 self.stdout.write(self.style.ERROR(f"Error creating relationship for {domain_code}: {e}"))

        if created_count > 0:
            self.stdout.write(self.style.SUCCESS(f"Created {created_count} domain relationships for {generic_env.name}"))


    def create_phiphi_traits(self):
        """Create PhiPhi's personality traits based on HEXACO model"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # Make sure all HEXACO traits exist in the database (usually added through seed_db_10_hexacos.py)
        # This is just a check to ensure we can reference them
        if GenericTrait.objects.count() < 24:  # 24 HEXACO traits
            self.stdout.write(self.style.WARNING("Warning: HEXACO traits not found in the database. Run seed_db_10_hexacos first."))
            return
        
        # Honesty-Humility traits
        self._create_trait_inclination("honesty_sincerity", 75, 70)  # Values genuine self-expression
        self._create_trait_inclination("honesty_fairness", 80, 85)  # Strong sense of fairness
        self._create_trait_inclination("honesty_greed_avoidance", 85, 80)  # Low interest in material wealth/status
        self._create_trait_inclination("honesty_modesty", 60, 55)  # Somewhat self-assured but not arrogant
        
        # Emotionality traits
        self._create_trait_inclination("emotion_fearfulness", 50, 60)  # Moderate anxiety about future
        self._create_trait_inclination("emotion_anxiety", 65, 50)  # Significant anxiety about wasting potential
        self._create_trait_inclination("emotion_dependence", 35, 60)  # Values independence
        self._create_trait_inclination("emotion_sentimentality", 75, 65)  # Forms emotional connections to places and ideals
        
        # Extraversion traits
        self._create_trait_inclination("extra_self_esteem", 60, 50)  # Moderate self-confidence with some doubts
        self._create_trait_inclination("extra_social_boldness", 55, 65)  # Moderately comfortable socially
        self._create_trait_inclination("extra_sociability", 50, 70)  # Enjoys socializing but also values solitude
        self._create_trait_inclination("extra_liveliness", 70, 65)  # Enthusiastic about new experiences
        
        # Agreeableness traits
        self._create_trait_inclination("agree_forgiveness", 65, 60)  # Generally forgiving
        self._create_trait_inclination("agree_gentleness", 70, 75)  # Kind and gentle temperament
        self._create_trait_inclination("agree_flexibility", 65, 60)  # Somewhat flexible but has principles
        self._create_trait_inclination("agree_patience", 55, 60)  # Moderate patience
        
        # Conscientiousness traits
        self._create_trait_inclination("consc_organization", 40, 65)  # Struggles with organization
        self._create_trait_inclination("consc_diligence", 35, 55)  # Difficulty with follow-through
        self._create_trait_inclination("consc_perfectionism", 70, 50)  # High standards but inconsistent application
        self._create_trait_inclination("consc_prudence", 45, 60)  # Often impulsive rather than deliberate
        
        # Openness traits
        self._create_trait_inclination("open_aesthetic", 85, 75)  # Strong appreciation for beauty
        self._create_trait_inclination("open_inquisitive", 90, 85)  # Very curious and interested in ideas
        self._create_trait_inclination("open_creativity", 80, 75)  # Highly creative
        self._create_trait_inclination("open_unconventional", 85, 70)  # Rejects conventional paths
        
        self.stdout.write(self.style.SUCCESS("PhiPhi's personality traits created/updated"))

    def _create_trait_inclination(self, trait_code, strength, awareness):
        """Helper method to create or update a trait inclination"""
        try:
            generic_trait = GenericTrait.objects.get(code=trait_code)
            trait, created = UserTraitInclination.objects.update_or_create(
                user_profile=self.user_profile,
                generic_trait=generic_trait,
                defaults={
                    'strength': strength,
                    'awareness': awareness
                }
            )
            
            if created:
                self.stdout.write(f"Created trait: {trait_code} (strength: {strength}, awareness: {awareness})")
            else:
                self.stdout.write(f"Updated trait: {trait_code} (strength: {strength}, awareness: {awareness})")
                
        except GenericTrait.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Error: GenericTrait with code {trait_code} not found"))

    def create_phiphi_beliefs(self):
        """Create PhiPhi's core beliefs"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # Create core beliefs about himself and his capabilities
        belief_data = [
            {
                "content": "I have a 'genius spirit' within me and am capable of great things.",
                "user_confidence": 75,
                "system_confidence": 65,
                "emotionality": 80,
                "stability": 60,
                "user_awareness": 70
            },
            {
                "content": "The Game of Life is a divinely given idea with potential to be infinitely expansive, even like a new form of government.",
                "user_confidence": 90,
                "system_confidence": 50,
                "emotionality": 85,
                "stability": 75,
                "user_awareness": 80
            },
            {
                "content": "My peak is yet to come, not behind me.",
                "user_confidence": 65,
                "system_confidence": 80,
                "emotionality": 70,
                "stability": 50,
                "user_awareness": 60
            },
            {
                "content": "I have a strong creative drive and inventor spirit.",
                "user_confidence": 85,
                "system_confidence": 90,
                "emotionality": 60,
                "stability": 85,
                "user_awareness": 90
            },
            {
                "content": "Honest and truthful journaling is a powerful tool for self-discovery and change.",
                "user_confidence": 95,
                "system_confidence": 90,
                "emotionality": 65,
                "stability": 90,
                "user_awareness": 85
            },
            {
                "content": "I can manifest things in my life through intention.",
                "user_confidence": 70,
                "system_confidence": 40,
                "emotionality": 75,
                "stability": 55,
                "user_awareness": 65
            },
            {
                "content": "Writing sharpens my mind and makes me more articulate and powerful.",
                "user_confidence": 85,
                "system_confidence": 80,
                "emotionality": 60,
                "stability": 80,
                "user_awareness": 75
            },
            {
                "content": "Fear of not being worthy sometimes holds me back.",
                "user_confidence": 70,
                "system_confidence": 85,
                "emotionality": 90,
                "stability": 65,
                "user_awareness": 60
            },
            {
                "content": "Perfectionism and getting lost in details prevents me from making progress.",
                "user_confidence": 75,
                "system_confidence": 90,
                "emotionality": 65,
                "stability": 70,
                "user_awareness": 70
            },
            {
                "content": "I need to collapse the gap between idea and experiment.",
                "user_confidence": 85,
                "system_confidence": 90,
                "emotionality": 60,
                "stability": 75,
                "user_awareness": 80
            },
            {
                "content": "I sometimes fear social connection despite desiring it deeply.",
                "user_confidence": 80,
                "system_confidence": 85,
                "emotionality": 85,
                "stability": 75,
                "user_awareness": 70
            },
            {
                "content": "Playfulness is the best way in which we can learn as humans.",
                "user_confidence": 90,
                "system_confidence": 85,
                "emotionality": 70,
                "stability": 85,
                "user_awareness": 85
            },
            {
                "content": "Less but better - essentialism is key to making progress.",
                "user_confidence": 80,
                "system_confidence": 90,
                "emotionality": 50,
                "stability": 65,
                "user_awareness": 75
            }
        ]
        
        # Create or update beliefs
        for belief_info in belief_data:
            belief, created = Belief.objects.update_or_create(
                user_profile=self.user_profile,
                content=belief_info["content"],
                defaults={
                    "last_updated": date.today(),
                    "user_confidence": belief_info["user_confidence"],
                    "system_confidence": belief_info["system_confidence"],
                    "emotionality": belief_info["emotionality"],
                    "stability": belief_info["stability"],
                    "user_awareness": belief_info["user_awareness"]
                }
            )
            
            if created:
                self.stdout.write(f"Created belief: {belief.content[:30]}...")
            else:
                self.stdout.write(f"Updated belief: {belief.content[:30]}...")
        
        self.stdout.write(self.style.SUCCESS("PhiPhi's beliefs created/updated"))
        
        # Add evidence for some key beliefs
        self._add_belief_evidence()

    def _add_belief_evidence(self):
        """Add evidence for key beliefs"""
        # Find beliefs to add evidence to
        try:
            creative_belief = Belief.objects.get(
                user_profile=self.user_profile,
                content="I have a strong creative drive and inventor spirit."
            )
            
            # Add evidence for creative belief
            BeliefEvidence.objects.get_or_create(
                belief=creative_belief,
                evidence_type="EXTERNAL_FEEDBACK",
                description="Simon said that 'creative director of the game of life fits me very well because my fantasy is very active'",
                credibility_score=0.85,
                source="Friend's observation"
            )
            
            BeliefEvidence.objects.get_or_create(
                belief=creative_belief,
                evidence_type="EXPERIENCE",
                description="Successfully brainstormed many innovative ideas with Guillaume for the Game of Life",
                credibility_score=0.90,
                source="Personal experience"
            )
            
            # Add evidence for fear of social connection belief
            social_fear_belief = Belief.objects.get(
                user_profile=self.user_profile,
                content="I sometimes fear social connection despite desiring it deeply."
            )
            
            BeliefEvidence.objects.get_or_create(
                belief=social_fear_belief,
                evidence_type="PATTERN",
                description="Recognizes fear of calling people and being super free and open in social situations",
                credibility_score=0.90,
                source="Self-observation"
            )
            
            BeliefEvidence.objects.get_or_create(
                belief=social_fear_belief,
                evidence_type="EXPERIENCE",
                description="Past tendency toward social isolation despite value placed on connection",
                credibility_score=0.85,
                source="Life pattern"
            )
            
            # Add evidence for perfectionism belief
            perfectionism_belief = Belief.objects.get(
                user_profile=self.user_profile,
                content="Perfectionism and getting lost in details prevents me from making progress."
            )
            
            BeliefEvidence.objects.get_or_create(
                belief=perfectionism_belief,
                evidence_type="PATTERN",
                description="History of abandoned projects due to getting caught in details",
                credibility_score=0.95,
                source="Self-observation"
            )
            
            BeliefEvidence.objects.get_or_create(
                belief=perfectionism_belief,
                evidence_type="EXPERIENCE",
                description="Game of Life project sometimes stalls when trying to perfect small elements",
                credibility_score=0.90,
                source="Current project experience"
            )
            
            self.stdout.write(self.style.SUCCESS("Added evidence for key beliefs"))
            
        except Belief.DoesNotExist:
            self.stdout.write(self.style.WARNING("Could not find beliefs to add evidence to"))

    def create_phiphi_goals(self):
        """Create PhiPhi's goals, intentions, and aspirations"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # Create longer-term aspirations
        farm_aspiration = Aspiration.objects.update_or_create(
            user_profile=self.user_profile,
            title="Buy and renovate an old farm in Italy",
            defaults={
                "description": "Purchase an old farm in Italy with my mother and renovate it into a creative retreat where creativity can flourish and I can live connected to nature and family.",
                "importance_according_user": 85,
                "importance_according_system": 75,
                "strength": 60,
                "domain": "Life Vision",
                "horizon": "Long-term",
                "level_of_ambition": "High",
                "effective_start": date.today(),
                "duration_estimate": "5-10 years",
                "effective_end": date.today() + timedelta(days=365*7)  # 7 years from now
            }
        )[0]
        
        game_aspiration = Aspiration.objects.update_or_create(
            user_profile=self.user_profile,
            title="Create the 'Game of Life' as a transformative tool",
            defaults={
                "description": "Develop the Game of Life into a powerful, intuitive tool that helps people break comfort zones, discover themselves, and live more fulfilling lives. Make it 'everybody's own little treasure chest' that empowers connection with truth and intuition.",
                "importance_according_user": 95,
                "importance_according_system": 90,
                "strength": 85,
                "domain": "Career/Impact",
                "horizon": "Medium to Long-term",
                "level_of_ambition": "Very High",
                "effective_start": date.today(),
                "duration_estimate": "2-5 years",
                "effective_end": date.today() + timedelta(days=365*3)  # 3 years from now
            }
        )[0]
        
        personal_aspiration = Aspiration.objects.update_or_create(
            user_profile=self.user_profile,
            title="Become someone who radiates unconditional love",
            defaults={
                "description": "Develop into a person who 'just glows from inner fire' and radiates an 'atmospheric fire of unconditional love'. Be a source of warmth and inspiration for others.",
                "importance_according_user": 90,
                "importance_according_system": 80,
                "strength": 75,
                "domain": "Personal Growth",
                "horizon": "Lifelong",
                "level_of_ambition": "High",
                "effective_start": date.today(),
                "duration_estimate": "Ongoing",
                "effective_end": date.today() + timedelta(days=365*20)  # Symbolic long timeframe
            }
        )[0]
        
        family_aspiration = Aspiration.objects.update_or_create(
            user_profile=self.user_profile,
            title="Heal deeply to become a good father",
            defaults={
                "description": "Heal myself deeply and develop the capacity to be a good father who can raise happy, well-equipped children. Create a life filled with children I can nurture and guide.",
                "importance_according_user": 80,
                "importance_according_system": 75,
                "strength": 65,
                "domain": "Family",
                "horizon": "Long-term",
                "level_of_ambition": "High",
                "effective_start": date.today(),
                "duration_estimate": "5-10 years",
                "effective_end": date.today() + timedelta(days=365*8)  # 8 years from now
            }
        )[0]
        
        # Create more immediate intentions
        ai_intention = Intention.objects.update_or_create(
            user_profile=self.user_profile,
            title="Master AI in a pragmatic, essentialist way",
            defaults={
                "description": "Develop practical AI skills, particularly with Langgraph and Django, to quickly turn ideas into functional prototypes. Focus on 'copying mastery' through practical application rather than just theory.",
                "importance_according_user": 95,
                "importance_according_system": 90,
                "strength": 80,
                "start_date": date.today(),
                "due_date": date.today() + timedelta(days=180),  # 6 months from now
                "is_completed": False,
                "progress_notes": "Working on Game of Life AI components with Guillaume, exploring Langgraph and Django implementations",
                "effective_start": date.today(),
                "duration_estimate": "6 months",
                "effective_end": date.today() + timedelta(days=180)
            }
        )[0]
        
        communication_intention = Intention.objects.update_or_create(
            user_profile=self.user_profile,
            title="Improve compassionate communication skills",
            defaults={
                "description": "Develop better compassionate communication and deep listening skills. Be more present in conversations, truly understand others, and express truth with both honesty and compassion.",
                "importance_according_user": 85,
                "importance_according_system": 80,
                "strength": 65,
                "start_date": date.today(),
                "due_date": date.today() + timedelta(days=90),  # 3 months from now
                "is_completed": False,
                "progress_notes": "Practicing with Guillaume, noticing when I become defensive or unclear when explaining concepts",
                "effective_start": date.today(),
                "duration_estimate": "3 months",
                "effective_end": date.today() + timedelta(days=90)
            }
        )[0]
        
        seasonal_intention = Intention.objects.update_or_create(
            user_profile=self.user_profile,
            title="Work at a summer festival in a food truck",
            defaults={
                "description": "Find work at a summer festival, possibly in a food truck, to connect with people and have a positive service experience similar to previous work at the Demeter farm cafe.",
                "importance_according_user": 75,
                "importance_according_system": 65,
                "strength": 60,
                "start_date": date.today(),
                "due_date": date(2025, 7, 1),  # Next summer
                "is_completed": False,
                "progress_notes": "Researching festival opportunities near Roanne and Lyon",
                "effective_start": date.today(),
                "duration_estimate": "2-3 months",
                "effective_end": date(2025, 9, 1)
            }
        )[0]
        
        project_intention = Intention.objects.update_or_create(
            user_profile=self.user_profile,
            title="Break the cycle of abandoned projects",
            defaults={
                "description": "Develop consistency in following through with projects, particularly the Game of Life, by applying essentialist principles and focusing on concrete steps rather than getting lost in details.",
                "importance_according_user": 90,
                "importance_according_system": 95,
                "strength": 70,
                "start_date": date.today(),
                "due_date": date.today() + timedelta(days=120),  # 4 months from now
                "is_completed": False,
                "progress_notes": "Using 25-minute work intervals, journaling for accountability, seeking help to prioritize next steps",
                "effective_start": date.today(),
                "duration_estimate": "4 months",
                "effective_end": date.today() + timedelta(days=120)
            }
        )[0]
        
        skills_intention = Intention.objects.update_or_create(
            user_profile=self.user_profile,
            title="Learn to ride the monocycle",
            defaults={
                "description": "Master riding the monocycle available at the farm as a fun physical challenge and skill development.",
                "importance_according_user": 65,
                "importance_according_system": 55,
                "strength": 50,
                "start_date": date.today(),
                "due_date": date.today() + timedelta(days=60),  # 2 months from now
                "is_completed": False,
                "progress_notes": "Haven't started yet, but monocycle is available at the farm",
                "effective_start": date.today(),
                "duration_estimate": "2 months",
                "effective_end": date.today() + timedelta(days=60)
            }
        )[0]
        
        # Create intentions for daily practices
        yoga_goal = Intention.objects.update_or_create(
            user_profile=self.user_profile,
            title="Restart daily yoga and meditation practice",
            defaults={
                "description": "Return to a consistent yoga and meditation practice that has been recently neglected due to focus on AI project.",
                "importance_according_user": 80,
                "importance_according_system": 85,
                "strength": 55,
                "start_date": date.today() - timedelta(days=10),
                "due_date": date.today() + timedelta(days=30),
                "is_completed": False,
                "progress_notes": "Trying to re-establish the habit. Aiming for 15 minutes daily.",
                "effective_start": date.today() - timedelta(days=10),
                "duration_estimate": "40 days",
                "effective_end": date.today() + timedelta(days=30)
            }
        )[0]
        
        journal_goal = Intention.objects.update_or_create(
            user_profile=self.user_profile,
            title="Maintain regular journaling practice",
            defaults={
                "description": "Continue daily journaling as a tool for self-discovery and processing experiences.",
                "importance_according_user": 85,
                "importance_according_system": 80,
                "strength": 75,
                "start_date": date.today() - timedelta(days=90),
                "due_date": date.today() + timedelta(days=365), # Ongoing intention for the next year
                "is_completed": False,
                "progress_notes": "Journaling most days, finding it helpful for clarity.",
                "effective_start": date.today() - timedelta(days=90),
                "duration_estimate": "1 year (ongoing)",
                "effective_end": date.today() + timedelta(days=365)
            }
        )[0]
        
        self.stdout.write(self.style.SUCCESS("PhiPhi's goals, intentions, and aspirations created/updated"))

    def create_phiphi_inspirations(self):
        """Create PhiPhi's inspirations and connect them to goals"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # Create inspirations
        inspirations_data = [
            {
                "source": "Rick Rubin",
                "description": "Inspired by Rick Rubin's ability to 'glow from inner fire' and be a source of warmth and inspiration.",
                "strength": 85,
                "reference_url": "https://example.com/rick-rubin"
            },
            {
                "source": "Grandfather (Agriculture Professor)",
                "description": "Deeply admired grandfather who was an agriculture professor - intellectual yet connected to nature. A role model for combining academic knowledge with practical wisdom.",
                "strength": 90,
                "reference_url": ""
            },
            {
                "source": "Travel experiences with friend",
                "description": "The friend who got Philipp into traveling and continues to be a philosophical conversation partner. Represents freedom and authentic exploration.",
                "strength": 85,
                "reference_url": ""
            },
            {
                "source": "Demeter farm cafe work experience",
                "description": "Previous positive experience working at a Demeter farm cafe in Germany, enjoying connecting with customers despite sometimes stressful conditions.",
                "strength": 75,
                "reference_url": ""
            },
            {
                "source": "Sailing experience in Biscay Bay",
                "description": "Transformative (though traumatic) experience sailing alone in Biscay Bay during a storm. Though it led to abandoning the sailing dream, it was a profound life lesson about limits and courage.",
                "strength": 80,
                "reference_url": ""
            },
            {
                "source": "AI pioneers and researchers",
                "description": "Inspired by visionaries who are leveraging AI for positive human development and those creating innovative systems like agent frameworks.",
                "strength": 90,
                "reference_url": "https://example.com/ai-research"
            }
        ]
        
        created_inspirations = []
        
        # Create or update inspirations
        for insp_data in inspirations_data:
            inspiration, created = Inspiration.objects.update_or_create(
                user_profile=self.user_profile,
                source=insp_data["source"],
                defaults={
                    "description": insp_data["description"],
                    "strength": insp_data["strength"],
                    "reference_url": insp_data["reference_url"]
                }
            )
            
            created_inspirations.append(inspiration)
            
            if created:
                self.stdout.write(f"Created inspiration: {inspiration.source}")
            else:
                self.stdout.write(f"Updated inspiration: {inspiration.source}")
        
        # Connect inspirations to goals
        try:
            # Get goals to connect inspirations to
            personal_aspiration = Aspiration.objects.get(
                user_profile=self.user_profile,
                title="Become someone who radiates unconditional love"
            )
            
            farm_aspiration = Aspiration.objects.get(
                user_profile=self.user_profile,
                title="Buy and renovate an old farm in Italy"
            )
            
            game_aspiration = Aspiration.objects.get(
                user_profile=self.user_profile,
                title="Create the 'Game of Life' as a transformative tool"
            )
            
            project_intention = Intention.objects.get(
                user_profile=self.user_profile,
                title="Break the cycle of abandoned projects"
            )
            
            seasonal_intention = Intention.objects.get(
                user_profile=self.user_profile,
                title="Work at a summer festival in a food truck"
            )
            
            ai_intention = Intention.objects.get(
                user_profile=self.user_profile,
                title="Master AI in a pragmatic, essentialist way"
            )
            
            # Connect Rick Rubin to personal aspiration
            rick_rubin = Inspiration.objects.get(user_profile=self.user_profile, source="Rick Rubin")
            GoalInspiration.objects.get_or_create(
                inspiration=rick_rubin,
                user_goal=personal_aspiration,
                defaults={
                    "note": "Rick Rubin represents the kind of presence and impact Philipp aspires to have.",
                    "strength": 85
                }
            )
            
            # Connect Grandfather to farm aspiration
            grandfather = Inspiration.objects.get(user_profile=self.user_profile, source="Grandfather (Agriculture Professor)")
            GoalInspiration.objects.get_or_create(
                inspiration=grandfather,
                user_goal=farm_aspiration,
                defaults={
                    "note": "Grandfather's academic approach to agriculture inspires the vision of a farm that balances intellect and nature.",
                    "strength": 90
                }
            )
            
            # Connect travel friend to project intention
            travel_friend = Inspiration.objects.get(user_profile=self.user_profile, source="Travel experiences with friend")
            GoalInspiration.objects.get_or_create(
                inspiration=travel_friend,
                user_goal=project_intention,
                defaults={
                    "note": "Working with friend on AI projects provides accountability and philosophical grounding.",
                    "strength": 80
                }
            )
            
            # Connect Demeter cafe to seasonal intention
            demeter_cafe = Inspiration.objects.get(user_profile=self.user_profile, source="Demeter farm cafe work experience")
            GoalInspiration.objects.get_or_create(
                inspiration=demeter_cafe,
                user_goal=seasonal_intention,
                defaults={
                    "note": "Previous positive experience serving customers at the Demeter cafe inspires desire to work at festivals.",
                    "strength": 85
                }
            )
            
            # Connect sailing experience to project intention (learning from past abandonments)
            sailing = Inspiration.objects.get(user_profile=self.user_profile, source="Sailing experience in Biscay Bay")
            GoalInspiration.objects.get_or_create(
                inspiration=sailing,
                user_goal=project_intention,
                defaults={
                    "note": "The abandoned sailing dream serves as a reminder of the pattern Philipp wants to break.",
                    "strength": 75
                }
            )
            
            # Connect AI pioneers to Game of Life aspiration and AI intention
            ai_pioneers = Inspiration.objects.get(user_profile=self.user_profile, source="AI pioneers and researchers")
            GoalInspiration.objects.get_or_create(
                inspiration=ai_pioneers,
                user_goal=game_aspiration,
                defaults={
                    "note": "AI visionaries inspire the technological execution of the Game of Life concept.",
                    "strength": 90
                }
            )
            
            GoalInspiration.objects.get_or_create(
                inspiration=ai_pioneers,
                user_goal=ai_intention,
                defaults={
                    "note": "Learning approach modeled after pragmatic AI implementers rather than pure theorists.",
                    "strength": 85
                }
            )
            
            self.stdout.write(self.style.SUCCESS("Connected inspirations to goals"))
            
        except (Aspiration.DoesNotExist, Intention.DoesNotExist, Inspiration.DoesNotExist) as e:
            self.stdout.write(self.style.ERROR(f"Error connecting inspirations to goals: {e}"))

    def create_phiphi_limitations(self):
        """Create PhiPhi's limitations"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # Define limitations
        limitations_data = [
            {
                "code": "cog_attention",
                "severity": 70,
                "is_unlimited": False, # Changed from is_permanent
                "user_awareness": 65
                # Removed notes field
            },
            {
                "code": "psych_motivation",
                "severity": 65,
                "is_unlimited": False, # Changed from is_permanent
                "user_awareness": 70
                # Removed notes field
            },
            {
                "code": "social_communication",
                "severity": 60,
                "is_unlimited": False, # Changed from is_permanent
                "user_awareness": 75
                # Removed notes field
            },
            {
                "code": "psych_social_anxiety",
                "severity": 75,
                "is_unlimited": False, # Changed from is_permanent
                "user_awareness": 80
                # Removed notes field
            },
            {
                "code": "time_morning",
                "severity": 80,
                "is_unlimited": False, # Changed from is_permanent
                "user_awareness": 90
                # Removed notes field
            },
            {
                "code": "cog_executive",
                "severity": 65,
                "is_unlimited": False, # Changed from is_permanent
                "user_awareness": 60
                # Removed notes field
            },
            {
                "code": "time_transitions",
                "severity": 70,
                "is_unlimited": False, # Changed from is_permanent
                "user_awareness": 65
                # Removed notes field
            }
        ]
        
        # Create or update limitations
        for limitation_data in limitations_data:
            try:
                generic_limitation = GenericUserLimitation.objects.get(code=limitation_data["code"])
                
                limitation, created = UserLimitation.objects.update_or_create(
                    user_profile=self.user_profile,
                    generic_limitation=generic_limitation,
                    defaults={
                        "severity": limitation_data["severity"],
                        "valid_until": date.today() + timedelta(days=180),  # Review in 6 months
                        "is_unlimited": limitation_data["is_unlimited"], # Changed from is_permanent
                        "user_awareness": limitation_data["user_awareness"],
                        "effective_start": date.today() - timedelta(days=90),  # Started 3 months ago
                        "duration_estimate": "Ongoing with therapy and self-work",
                        "effective_end": date.today() + timedelta(days=365)  # Projected for a year
                        # Removed notes field assignment
                    }
                )
                
                if created:
                    self.stdout.write(f"Created limitation: {generic_limitation.description}")
                else:
                    self.stdout.write(f"Updated limitation: {generic_limitation.description}")
                    
            except GenericUserLimitation.DoesNotExist:
                self.stdout.write(self.style.WARNING(f"Generic limitation with code {limitation_data['code']} not found"))
        
        self.stdout.write(self.style.SUCCESS("PhiPhi's limitations created/updated"))

    def create_phiphi_skills(self):
        """Create PhiPhi's skills"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # First, create generic skills if they don't exist
        generic_skills_data = [
            {
                "code": "tech_coding_python",
                "description": "Python programming skills",
                "skill_type": "TECHNICAL"
            },
            {
                "code": "tech_ai_concepts",
                "description": "Understanding of AI concepts and systems",
                "skill_type": "TECHNICAL"
            },
            {
                "code": "soft_communication",
                "description": "Verbal and written communication skills",
                "skill_type": "SOFT"
            },
            {
                "code": "tech_graphic_design",
                "description": "Graphic design skills and knowledge",
                "skill_type": "TECHNICAL"
            },
            {
                "code": "soft_empathy",
                "description": "Empathy and emotional intelligence",
                "skill_type": "SOFT"
            },
            {
                "code": "tech_sailing",
                "description": "Sailing skills and nautical knowledge",
                "skill_type": "TECHNICAL"
            },
            {
                "code": "tech_writing",
                "description": "Creative and analytical writing skills",
                "skill_type": "TECHNICAL"
            },
            {
                "code": "soft_service",
                "description": "Customer service and hospitality skills",
                "skill_type": "SOFT"
            },
            {
                "code": "soft_introspection",
                "description": "Self-reflection and personal insight capabilities",
                "skill_type": "SOFT"
            },
            {
                "code": "soft_philosophical",
                "description": "Ability to engage with philosophical concepts and discussions",
                "skill_type": "SOFT"
            }
        ]
        
        # Create generic skills
        for skill_data in generic_skills_data:
            GenericSkill.objects.get_or_create(
                code=skill_data["code"],
                defaults={
                    "description": skill_data["description"]
                    # Removed "skill_type": skill_data["skill_type"] as it's not a valid field
                }
            )
        
        # Define user skills
        skills_data = [
            {
                "generic_skill_code": "tech_coding_python",
                "description": "Beginning to learn Python for AI development, particularly with a focus on implementing the Game of Life",
                "level": 25,
                "user_awareness": 80,
                "user_enjoyment": 85,
                "note": "Actively working to improve this skill as it's central to the Game of Life project"
            },
            {
                "generic_skill_code": "tech_ai_concepts",
                "description": "Understanding of AI concepts, especially in relation to language models and agent frameworks",
                "level": 40,
                "user_awareness": 85,
                "user_enjoyment": 95,
                "note": "Very passionate about AI and its potential, though still developing practical implementation skills"
            },
            {
                "generic_skill_code": "soft_communication",
                "description": "Ability to express ideas clearly and engage in philosophical conversations, though sometimes struggles to articulate complex concepts without triggering resistance",
                "level": 70,
                "user_awareness": 75,
                "user_enjoyment": 80,
                "note": "Strong in philosophical conversation, working on compassionate communication and clear articulation of complex ideas"
            },
            {
                "generic_skill_code": "tech_graphic_design",
                "description": "Previous experience as a freelance graphic designer, though currently not actively practicing",
                "level": 65,
                "user_awareness": 90,
                "user_enjoyment": 60,
                "note": "Past professional experience but not currently a focus area"
            },
            {
                "generic_skill_code": "soft_empathy",
                "description": "Natural capacity for empathy and understanding others' perspectives, though sometimes hindered by social anxiety",
                "level": 80,
                "user_awareness": 70,
                "user_enjoyment": 85,
                "note": "Core strength that aligns with values of compassion and connection"
            },
            {
                "generic_skill_code": "tech_sailing",
                "description": "Experience sailing, including owning a sailboat and navigating challenging conditions, though traumatized by storm experience",
                "level": 60,
                "user_awareness": 95,
                "user_enjoyment": 40,
                "note": "Mixed feelings due to traumatic experience in Biscay Bay"
            },
            {
                "generic_skill_code": "tech_writing",
                "description": "Strong journaling practice and ability to express thoughts clearly in writing, believes writing sharpens the mind",
                "level": 85,
                "user_awareness": 90,
                "user_enjoyment": 90,
                "note": "Uses writing as a tool for self-discovery and processing emotions"
            },
            {
                "generic_skill_code": "soft_service",
                "description": "Experience working in a Demeter farm cafe, enjoyed connecting with customers despite sometimes stressful conditions",
                "level": 75,
                "user_awareness": 85,
                "user_enjoyment": 80,
                "note": "Positive past experience that may inform future work at festivals"
            },
            {
                "generic_skill_code": "soft_introspection",
                "description": "Highly developed ability to examine personal thoughts, feelings, and motivations with significant depth and honesty",
                "level": 90,
                "user_awareness": 85,
                "user_enjoyment": 90,
                "note": "Core strength and daily practice through journaling and meditation"
            },
            {
                "generic_skill_code": "soft_philosophical",
                "description": "Excellent ability to engage with abstract concepts and philosophical discussions, especially around purpose, meaning, and consciousness",
                "level": 85,
                "user_awareness": 80,
                "user_enjoyment": 95,
                "note": "Regular philosophical conversations with friend at the farm; natural inclination toward deep thinking"
            }
        ]
        
        # Create or update skills
        for skill_data in skills_data:
            try:
                generic_skill = GenericSkill.objects.get(code=skill_data["generic_skill_code"])
                
                skill, created = Skill.objects.update_or_create(
                    user_profile=self.user_profile,
                    generic_skill=generic_skill,
                    defaults={
                        "description": skill_data["description"],
                        "level": skill_data["level"],
                        "user_awareness": skill_data["user_awareness"],
                        "user_enjoyment": skill_data["user_enjoyment"],
                        "note": skill_data["note"],
                        "last_practiced": date.today() - timedelta(days=7),
                        "acquisition_date": date.today() - timedelta(days=365*2)  # Acquired approximately 2 years ago
                    }
                )
                
                if created:
                    self.stdout.write(f"Created skill: {generic_skill.description}")
                else:
                    self.stdout.write(f"Updated skill: {generic_skill.description}")
                    
            except GenericSkill.DoesNotExist:
                self.stdout.write(self.style.WARNING(f"Generic skill with code {skill_data['generic_skill_code']} not found"))
        
        self.stdout.write(self.style.SUCCESS("PhiPhi's skills created/updated"))

    def create_phiphi_resources(self):
        """Create resources available to PhiPhi"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        if not hasattr(self, 'farm_environment'):
            try:
                self.farm_environment = UserEnvironment.objects.get(
                    user_profile=self.user_profile,
                    environment_name="Lyon Rural Farm"
                )
            except UserEnvironment.DoesNotExist:
                self.stdout.write(self.style.ERROR("Farm environment not found. Run create_phiphi_environment first."))
                return
        
        # Create generic resources if needed
        generic_resources_data = [
            {
                "code": "tech_laptop",
                "resource_type": "Computer Equipment",
                "description": "Laptop for development and online work",
                "operational_cost": 20,
                "acquisition_cost": 800
            },
            {
                "code": "workshop_tools",
                "resource_type": "Workshop Equipment",
                "description": "Basic tools for woodworking and repairs",
                "operational_cost": 10,
                "acquisition_cost": 600
            },
            {
                "code": "music_instruments",
                "resource_type": "Musical Instruments",
                "description": "Various musical instruments for creative expression",
                "operational_cost": 5,
                "acquisition_cost": 700
            },
            {
                "code": "transport_bicycle",
                "resource_type": "Transportation",
                "description": "Bicycle for local travel",
                "operational_cost": 10,
                "acquisition_cost": 500
            },
            {
                "code": "transport_specialcycle",
                "resource_type": "Transportation",
                "description": "Specialized cycle (unicycle, monocycle, etc.)",
                "operational_cost": 5,
                "acquisition_cost": 400
            },
            {
                "code": "connectivity_internet",
                "resource_type": "Digital Connectivity",
                "description": "High-speed internet connection",
                "operational_cost": 30,
                "acquisition_cost": 400
            },
            {
                "code": "material_wood",
                "resource_type": "Building Materials",
                "description": "Wood for construction and creative projects",
                "operational_cost": 15,
                "acquisition_cost": 400
            },
            {
                "code": "space_workshop",
                "resource_type": "Workspace",
                "description": "Dedicated workshop space for projects",
                "operational_cost": 10,
                "acquisition_cost": 0  # Already part of the farm
            },
            {
                "code": "books_reading",
                "resource_type": "Educational Materials",
                "description": "Books and reading materials for learning and leisure",
                "operational_cost": 0,
                "acquisition_cost": 200
            },
            {
                "code": "ai_access",
                "resource_type": "Digital Tools",
                "description": "Access to AI tools and platforms for development",
                "operational_cost": 20,
                "acquisition_cost": 0
            }
        ]
        
        # Create generic resources
        generic_resources = {}
        for res_data in generic_resources_data:
            generic_resource, created = GenericResource.objects.get_or_create(
                code=res_data["code"],
                defaults={
                    "resource_type": res_data["resource_type"],
                    "description": res_data["description"],
                    "op_cost": res_data["operational_cost"], # Corrected field name
                    "acq_cost": res_data["acquisition_cost"]  # Corrected field name
                }
            )
            
            generic_resources[res_data["code"]] = generic_resource
            
            if created:
                self.stdout.write(f"Created generic resource: {generic_resource.description}")
            else:
                self.stdout.write(f"Using existing generic resource: {generic_resource.description}")
        
        # Create inventory for the farm environment
        inventory, created = Inventory.objects.get_or_create(
            user_environment=self.farm_environment,
            defaults={
                "valid_until": date.today() + timedelta(days=180),  # 6 months validity
                "notes": "Inventory of available resources on the rural farm near Lyon"
            }
        )
        
        if created:
            self.stdout.write(f"Created inventory for {self.farm_environment.environment_name}")
        else:
            self.stdout.write(f"Using existing inventory for {self.farm_environment.environment_name}")
        
        # Create user resources
        user_resources_data = [
            {
                "specific_name": "Development Laptop",
                "generic_resource_code": "tech_laptop",
                "location_details": "Small office space shared with friend",
                "ownership_details": "Personal laptop",
                "contact_info": "N/A (personal item)",
                "notes": "Laptop used for AI development and learning programming",
                "quantity": 1,
                "condition": "Good",
                "availability": "Always"
            },
            {
                "specific_name": "Farm Workshop Tools",
                "generic_resource_code": "workshop_tools",
                "location_details": "Workshop on the farm property",
                "ownership_details": "Belongs to the farm/friend",
                "contact_info": "Ask friend for access if needed",
                "notes": "Various tools for repair projects and woodworking",
                "quantity": 1,
                "condition": "Mixed - some good, some worn",
                "availability": "When friend isn't using them"
            },
            {
                "specific_name": "Guitar",
                "generic_resource_code": "music_instruments",
                "location_details": "In PhiPhi's caravan",
                "ownership_details": "Personal instrument",
                "contact_info": "N/A (personal item)",
                "notes": "Acoustic guitar for creative expression",
                "quantity": 1,
                "condition": "Good",
                "availability": "Always"
            },
            {
                "specific_name": "Shruti Box",
                "generic_resource_code": "music_instruments",
                "location_details": "In PhiPhi's caravan",
                "ownership_details": "Personal instrument",
                "contact_info": "N/A (personal item)",
                "notes": "Drone instrument used for meditation and music creation",
                "quantity": 1,
                "condition": "Excellent",
                "availability": "Always"
            },
            {
                "specific_name": "Farm Bicycle",
                "generic_resource_code": "transport_bicycle",
                "location_details": "Stored in main farm building",
                "ownership_details": "Belongs to the farm/friend but available for use",
                "contact_info": "Ask friend if needed",
                "notes": "Used for trips to the river and local exploration",
                "quantity": 1,
                "condition": "Fair - needs maintenance",
                "availability": "Most days"
            },
            {
                "specific_name": "Monocycle",
                "generic_resource_code": "transport_specialcycle",
                "location_details": "Stored in main farm building",
                "ownership_details": "Belongs to the farm/friend",
                "contact_info": "Ask friend for permission",
                "notes": "PhiPhi wants to learn to ride this",
                "quantity": 1,
                "condition": "Good",
                "availability": "When not in use"
            },
            {
                "specific_name": "Fiber Internet Connection",
                "generic_resource_code": "connectivity_internet",
                "location_details": "Throughout the property",
                "ownership_details": "Subscription paid by friend for AI work",
                "contact_info": "Friend manages the account",
                "notes": "Excellent connection quality for AI development work",
                "quantity": 1,
                "condition": "Excellent",
                "availability": "24/7"
            },
            {
                "specific_name": "Workshop Wood Supply",
                "generic_resource_code": "material_wood",
                "location_details": "Stored in workshop",
                "ownership_details": "Farm resources, free to use with permission",
                "contact_info": "Ask friend before using large amounts",
                "notes": "Various wood pieces useful for small construction projects",
                "quantity": "Various",
                "condition": "Mixed",
                "availability": "With permission"
            },
            {
                "specific_name": "Farm Workshop Space",
                "generic_resource_code": "space_workshop",
                "location_details": "Dedicated building on the farm property",
                "ownership_details": "Shared space, belongs to the farm",
                "contact_info": "Coordinate with friend for larger projects",
                "notes": "Space for physical projects and repairs",
                "quantity": 1,
                "condition": "Functional but cluttered",
                "availability": "Most days"
            },
            {
                "specific_name": "Philosophy and Programming Books",
                "generic_resource_code": "books_reading",
                "location_details": "In PhiPhi's caravan and main house",
                "ownership_details": "Mix of personal and borrowed from friend",
                "contact_info": "N/A (available on site)",
                "notes": "Collection of books on philosophy, consciousness, and programming",
                "quantity": 25,
                "condition": "Good",
                "availability": "Always"
            },
            {
                "specific_name": "Claude and Mistral AI Access",
                "generic_resource_code": "ai_access",
                "location_details": "Online - accessible from laptop",
                "ownership_details": "Personal subscriptions",
                "contact_info": "N/A (personal accounts)",
                "notes": "Paid access to advanced AI tools for Game of Life development",
                "quantity": 1,
                "condition": "Active subscriptions",
                "availability": "24/7"
            }
        ]
        
        # Create user resources
        for res_data in user_resources_data:
            try:
                generic_resource = generic_resources.get(res_data["generic_resource_code"])
                
                if not generic_resource:
                    generic_resource = GenericResource.objects.get(code=res_data["generic_resource_code"])
                
                user_resource, created = UserResource.objects.update_or_create(
                    specific_name=res_data["specific_name"],
                    user_environment=self.farm_environment,
                    defaults={
                        "location_details": res_data["location_details"],
                        "ownership_details": res_data["ownership_details"],
                        "contact_info": res_data["contact_info"],
                        "notes": res_data["notes"],
                        # Removed invalid fields: quantity, condition, availability, inventory
                        "generic_resource": generic_resource
                    }
                )
                
                if created:
                    self.stdout.write(f"Created user resource: {user_resource.specific_name}")
                else:
                    self.stdout.write(f"Updated user resource: {user_resource.specific_name}")
                    
            except GenericResource.DoesNotExist:
                self.stdout.write(self.style.WARNING(f"Generic resource with code {res_data['generic_resource_code']} not found"))
        
        self.stdout.write(self.style.SUCCESS("PhiPhi's resources created/updated"))

    def create_phiphi_preferences(self):
        """Create PhiPhi's preferences"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        if not hasattr(self, 'farm_environment'):
            try:
                self.farm_environment = UserEnvironment.objects.get(
                    user_profile=self.user_profile,
                    environment_name="Lyon Rural Farm"
                )
            except UserEnvironment.DoesNotExist:
                self.stdout.write(self.style.ERROR("Farm environment not found. Run create_phiphi_environment first."))
                return
        
        # Define preferences
        preferences_data = [
            {
                "pref_name": "Evening Cannabis Use",
                "pref_description": "Enjoys smoking cannabis with friend in the evenings as a social and relaxation ritual.",
                "pref_strength": 70,
                "user_awareness": 85,
                "effective_start": date.today() - timedelta(days=120),  # Started since being at the farm
                "duration_estimate": "While at the farm",
                "effective_end": date.today() + timedelta(days=365),  # 1 year from now
                "environment_specific": True
            },
            {
                "pref_name": "Philosophical Conversations",
                "pref_description": "Deeply enjoys deep, philosophical conversations, particularly with farm friend.",
                "pref_strength": 90,
                "user_awareness": 95,
                "effective_start": date.today() - timedelta(days=365*3),  # Long-standing preference
                "duration_estimate": "Lifelong",
                "effective_end": date.today() + timedelta(days=365*5),  # 5 years (symbolic)
                "environment_specific": False
            },
            {
                "pref_name": "Creative Problem-Solving",
                "pref_description": "Preference for approaching problems through creative, non-conventional thinking rather than structured methodologies.",
                "pref_strength": 85,
                "user_awareness": 75,
                "effective_start": date.today() - timedelta(days=365*10),  # Long-standing preference
                "duration_estimate": "Lifelong",
                "effective_end": date.today() + timedelta(days=365*5),  # 5 years (symbolic)
                "environment_specific": False
            },
            {
                "pref_name": "Authentic Self-Expression",
                "pref_description": "Strong preference for authentic self-expression and environments that support this.",
                "pref_strength": 95,
                "user_awareness": 90,
                "effective_start": date.today() - timedelta(days=365*5),  # Developed over years
                "duration_estimate": "Lifelong",
                "effective_end": date.today() + timedelta(days=365*5),  # 5 years (symbolic)
                "environment_specific": False
            },
            {
                "pref_name": "Evening Wakefulness",
                "pref_description": "Preference for staying up late (until around 2 AM) and waking up later (around 10:30 AM).",
                "pref_strength": 85,
                "user_awareness": 90,
                "effective_start": date.today() - timedelta(days=365),  # Long-standing preference
                "duration_estimate": "Ongoing",
                "effective_end": date.today() + timedelta(days=730),  # 2 years from now
                "environment_specific": False
            },
            {
                "pref_name": "Evening Reading",
                "pref_description": "Enjoys reading before bed as a way to relax and transition to sleep.",
                "pref_strength": 80,
                "user_awareness": 95,
                "effective_start": date.today() - timedelta(days=365*2),  # Long-standing habit
                "duration_estimate": "Ongoing",
                "effective_end": date.today() + timedelta(days=730),  # 2 years from now
                "environment_specific": False
            },
            {
                "pref_name": "Intermittent Fasting",
                "pref_description": "Prefers to practice intermittent fasting as an eating pattern.",
                "pref_strength": 75,
                "user_awareness": 90,
                "effective_start": date.today() - timedelta(days=180),  # Started 6 months ago
                "duration_estimate": "Ongoing as long as beneficial",
                "effective_end": date.today() + timedelta(days=365),  # 1 year from now
                "environment_specific": False
            },
            {
                "pref_name": "Yoga and Meditation",
                "pref_description": "Enjoys and values yoga and meditation practice, even though recently neglected due to AI project focus.",
                "pref_strength": 85,
                "user_awareness": 80,
                "effective_start": date.today() - timedelta(days=365),  # Started a year ago
                "duration_estimate": "Ongoing with fluctuations",
                "effective_end": date.today() + timedelta(days=730),  # 2 years from now
                "environment_specific": False
            },
            {
                "pref_name": "Natural Environment Access",
                "pref_description": "Strong preference for living and working in environments with natural elements and outdoor access.",
                "pref_strength": 90,
                "user_awareness": 85,
                "effective_start": date.today() - timedelta(days=365*5),  # Long-standing preference
                "duration_estimate": "Lifelong",
                "effective_end": date.today() + timedelta(days=365*10),  # 10 years (symbolic)
                "environment_specific": False
            },
            {
                "pref_name": "Solitary Work Environment",
                "pref_description": "Preference for working in quiet, low-distraction environments without frequent interruptions.",
                "pref_strength": 85,
                "user_awareness": 90,
                "effective_start": date.today() - timedelta(days=365*2),  # Recognized over past couple years
                "duration_estimate": "Ongoing",
                "effective_end": date.today() + timedelta(days=730),  # 2 years from now
                "environment_specific": False
            }
        ]
        
        # Create or update preferences
        for pref_data in preferences_data:
            preference, created = Preference.objects.update_or_create(
                user_profile=self.user_profile,
                pref_name=pref_data["pref_name"],
                defaults={
                    "pref_description": pref_data["pref_description"],
                    "pref_strength": pref_data["pref_strength"],
                    "user_awareness": pref_data["user_awareness"],
                    "environment": self.farm_environment if pref_data["environment_specific"] else None,
                    "effective_start": pref_data["effective_start"],
                    "duration_estimate": pref_data["duration_estimate"],
                    "effective_end": pref_data["effective_end"]
                }
            )
            
            if created:
                self.stdout.write(f"Created preference: {preference.pref_name}")
            else:
                self.stdout.write(f"Updated preference: {preference.pref_name}")
        
        self.stdout.write(self.style.SUCCESS("PhiPhi's preferences created/updated"))

    def create_phiphi_mood(self):
        """Create PhiPhi's current mood"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # Create or update current mood
        now = datetime.now()
        mood, created = CurrentMood.objects.update_or_create(
            user_profile=self.user_profile,
            defaults={
                "description": "Excited about AI learning and the Game of Life project, but occasionally anxious about following through and making concrete progress. Feeling a blend of creative inspiration and impatience to see results.",
                "height": 75,  # Moderate to high intensity
                # Removed invalid fields: valence, last_reported, mood_factors
                "user_awareness": 80,
                "processed_at": now,
                "effective_start": now.date() - timedelta(days=7),  # A general mood over the past week
                "duration_estimate": "Fluctuating daily with focus on AI project",
                "effective_end": now.date() + timedelta(days=14)  # Expected to evolve over next two weeks
            }
        )
        
        if created:
            self.stdout.write("Created PhiPhi's current mood")
        else:
            self.stdout.write("Updated PhiPhi's current mood")
        
        self.stdout.write(self.style.SUCCESS("PhiPhi's current mood created/updated"))

    def create_phiphi_trust_level(self):
        """Create PhiPhi's trust level"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # Create or update trust level
        trust_level, created = TrustLevel.objects.update_or_create(
            user_profile=self.user_profile,
            defaults={
                "value": 65,  # Moderate trust level - open but cautious
                "aggregate_type": "Foundation",  # Trust phase as seen in agent tests
                "aggregate_id": str(uuid.uuid4()),
                "notes": "PhiPhi is open to the Game of Life concept and eager for change, but has a history of abandoned projects that creates some hesitation. Positive about the transformative potential but aware of his own patterns of enthusiasm followed by loss of interest."
            }
        )
        
        if created:
            self.stdout.write("Created PhiPhi's trust level")
        else:
            self.stdout.write("Updated PhiPhi's trust level")
        
        self.stdout.write(self.style.SUCCESS("PhiPhi's trust level created/updated"))

    def create_belief_influences(self):
        """Create connections between beliefs and other entities"""
        if not hasattr(self, 'user_profile'):
            self.user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        try:
            # Find key beliefs
            game_belief = Belief.objects.get(
                user_profile=self.user_profile,
                content="The Game of Life is a divinely given idea with potential to be infinitely expansive, even like a new form of government."
            )
            
            perfectionism_belief = Belief.objects.get(
                user_profile=self.user_profile,
                content="Perfectionism and getting lost in details prevents me from making progress."
            )
            
            social_fear_belief = Belief.objects.get(
                user_profile=self.user_profile,
                content="I sometimes fear social connection despite desiring it deeply."
            )
            
            playfulness_belief = Belief.objects.get(
                user_profile=self.user_profile,
                content="Playfulness is the best way in which we can learn as humans."
            )
            
            # Find entities to connect with beliefs
            game_aspiration = Aspiration.objects.get(
                user_profile=self.user_profile,
                title="Create the 'Game of Life' as a transformative tool"
            )
            
            project_intention = Intention.objects.get(
                user_profile=self.user_profile,
                title="Break the cycle of abandoned projects"
            )
            
            communication_intention = Intention.objects.get(
                user_profile=self.user_profile,
                title="Improve compassionate communication skills"
            )
            
            # Create connections between beliefs and other entities
            
            # Game of Life belief influences aspiration
            game_belief_influence = BeliefInfluence.objects.update_or_create(
                belief=game_belief,
                content_type=ContentType.objects.get_for_model(game_aspiration),
                object_id=game_aspiration.id,
                defaults={
                    "influence_strength": 95,  # Very strong positive influence
                    "note": "The belief in the divine origin and expansive potential of the Game of Life greatly motivates and shapes Philipp's aspiration to create it."
                }
            )[0]
            
            # Perfectionism belief influences project intention
            perfectionism_belief_influence = BeliefInfluence.objects.update_or_create(
                belief=perfectionism_belief,
                content_type=ContentType.objects.get_for_model(project_intention),
                object_id=project_intention.id,
                defaults={
                    "influence_strength": -75,  # Strong negative influence
                    "note": "Philipp's awareness of how perfectionism and getting lost in details has hindered his progress in the past directly informs his intention to break the cycle of abandoned projects."
                }
            )[0]
            
            # Social fear belief influences communication intention
            social_fear_belief_influence = BeliefInfluence.objects.update_or_create(
                belief=social_fear_belief,
                content_type=ContentType.objects.get_for_model(communication_intention),
                object_id=communication_intention.id,
                defaults={
                    "influence_strength": -65,  # Moderate negative influence
                    "note": "Philipp's fear of social connection creates resistance to fully embracing compassionate communication skills, even though he desires to improve in this area."
                }
            )[0]
            
            # Playfulness belief influences game aspiration
            playfulness_belief_influence = BeliefInfluence.objects.update_or_create(
                belief=playfulness_belief,
                content_type=ContentType.objects.get_for_model(game_aspiration),
                object_id=game_aspiration.id,
                defaults={
                    "influence_strength": 90,  # Strong positive influence
                    "note": "Philipp's belief in playfulness as the best way to learn is a foundational principle of the Game of Life concept."
                }
            )[0]
            
            self.stdout.write(self.style.SUCCESS("Created belief influences"))
            
        except (Belief.DoesNotExist, Aspiration.DoesNotExist, Intention.DoesNotExist) as e:
            self.stdout.write(self.style.ERROR(f"Error creating belief influences: {e}"))
