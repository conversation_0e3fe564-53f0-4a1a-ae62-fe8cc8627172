import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.user.models import GenericBelief, BeliefType
from apps.main.models import AppliedSeedingCommand # Import the tracking model

class Command(BaseCommand):
    help = 'Seeds generic belief types. Skips if already applied by run_seeders.'
    # Re-add command name definition
    COMMAND_NAME = os.path.splitext(os.path.basename(__file__))[0]

    def handle(self, *args, **kwargs):
        # Re-add the initial check for the command itself
        if AppliedSeedingCommand.objects.filter(command_name=self.COMMAND_NAME).exists():
            self.stdout.write(self.style.WARNING(f"Command '{self.COMMAND_NAME}' has already been applied by run_seeders. Skipping."))
            return

        self.stdout.write(self.style.SUCCESS(f"Running command '{self.COMMAND_NAME}' logic (will update/create individual items)..."))

        try:
            # 2. Execute the seeding logic within a transaction
            with transaction.atomic():
                # Seed beliefs (this method handles internal idempotency via update_or_create)
                created_count, updated_count = self.seed_generic_beliefs()

                # Ensure AppliedSeedingCommand.objects.create(...) remains removed from here.
                # run_seeders.py handles the recording upon successful completion of this command.
                self.stdout.write(self.style.SUCCESS(f"Finished command '{self.COMMAND_NAME}' logic! Created {created_count} new beliefs, updated {updated_count} existing beliefs."))

        except Exception as e:
            # Catch any exception during the transaction
            self.stderr.write(self.style.ERROR(f"Command '{self.COMMAND_NAME}' failed internally: {str(e)}"))
            # Re-raise the exception so run_seeders knows it failed and won't record success
            raise CommandError(f"Command '{self.COMMAND_NAME}' failed: {e}") from e

    def seed_generic_beliefs(self):
        """
        Create or update a comprehensive list of generic belief types.
        Returns the count of newly created and updated beliefs.
        """
        self.stdout.write("Seeding generic belief types...")
        created_count = 0
        updated_count = 0

        # Dictionary of belief types grouped by category
        beliefs = {
            BeliefType.SELF_WORTH: [
                {
                    'code': 'self_worth_inherent',
                    'name': 'Inherent Value',
                    'description': 'Belief that one has intrinsic value regardless of achievements or external validation.',
                    'typical_stability': 70
                },
                {
                    'code': 'self_worth_conditional',
                    'name': 'Conditional Self-Worth',
                    'description': 'Belief that one\'s value depends on meeting certain conditions, achievements, or standards.',
                    'typical_stability': 75
                },
                {
                    'code': 'self_worth_comparative',
                    'name': 'Comparative Value',
                    'description': 'Belief that one\'s worth is determined by comparison to others.',
                    'typical_stability': 65
                },
                {
                    'code': 'self_worth_permanent',
                    'name': 'Permanence of Worth',
                    'description': 'Belief that one\'s value is stable and enduring rather than fluctuating based on circumstances.',
                    'typical_stability': 80
                }
            ],
            
            BeliefType.SELF_EFFICACY: [
                {
                    'code': 'self_efficacy_general',
                    'name': 'General Self-Efficacy',
                    'description': 'Belief in one\'s overall ability to perform tasks and achieve goals across various domains.',
                    'typical_stability': 65
                },
                {
                    'code': 'self_efficacy_domain',
                    'name': 'Domain-Specific Efficacy',
                    'description': 'Belief in one\'s ability to succeed in specific areas or types of tasks.',
                    'typical_stability': 60
                },
                {
                    'code': 'self_efficacy_control',
                    'name': 'Control Beliefs',
                    'description': 'Beliefs about the degree of control one has over outcomes and life circumstances.',
                    'typical_stability': 70
                },
                {
                    'code': 'self_efficacy_resilience',
                    'name': 'Resilience Beliefs',
                    'description': 'Beliefs about one\'s ability to recover from setbacks and adapt to challenges.',
                    'typical_stability': 65
                }
            ],
            
            BeliefType.IDENTITY: [
                {
                    'code': 'identity_core',
                    'name': 'Core Identity',
                    'description': 'Fundamental beliefs about who one is as a person, including essential traits and characteristics.',
                    'typical_stability': 85
                },
                {
                    'code': 'identity_role',
                    'name': 'Role Identity',
                    'description': 'Beliefs about one\'s purpose or function in various social contexts and relationships.',
                    'typical_stability': 75
                },
                {
                    'code': 'identity_malleability',
                    'name': 'Identity Malleability',
                    'description': 'Beliefs about whether one\'s identity is fixed or can change over time.',
                    'typical_stability': 70
                },
                {
                    'code': 'identity_integration',
                    'name': 'Identity Integration',
                    'description': 'Beliefs about how various aspects of identity connect or conflict with each other.',
                    'typical_stability': 65
                }
            ],
            
            BeliefType.POTENTIAL: [
                {
                    'code': 'potential_capacity',
                    'name': 'Growth Capacity',
                    'description': 'Beliefs about the extent to which one can develop new abilities and qualities.',
                    'typical_stability': 60
                },
                {
                    'code': 'potential_trajectory',
                    'name': 'Growth Trajectory',
                    'description': 'Beliefs about the pace and pattern of personal development over time.',
                    'typical_stability': 55
                },
                {
                    'code': 'potential_limitation',
                    'name': 'Inherent Limitations',
                    'description': 'Beliefs about fundamental constraints on one\'s development or achievement.',
                    'typical_stability': 75
                },
                {
                    'code': 'potential_agency',
                    'name': 'Growth Agency',
                    'description': 'Beliefs about one\'s ability to actively direct and shape personal development.',
                    'typical_stability': 65
                }
            ],
            
            BeliefType.SKILL_LEARNING: [
                {
                    'code': 'learning_intelligence',
                    'name': 'Intelligence Beliefs',
                    'description': 'Beliefs about whether intelligence is fixed or can be developed through effort.',
                    'typical_stability': 70
                },
                {
                    'code': 'learning_effort',
                    'name': 'Effort Value',
                    'description': 'Beliefs about the relationship between effort and achievement in skill development.',
                    'typical_stability': 65
                },
                {
                    'code': 'learning_mistakes',
                    'name': 'Learning from Mistakes',
                    'description': 'Beliefs about the role of failures and mistakes in the learning process.',
                    'typical_stability': 60
                },
                {
                    'code': 'learning_efficiency',
                    'name': 'Learning Efficiency',
                    'description': 'Beliefs about one\'s rate of skill acquisition relative to others or to personal expectations.',
                    'typical_stability': 55
                }
            ],
            
            BeliefType.TALENT_INNATE: [
                {
                    'code': 'talent_inborn',
                    'name': 'Innate Talents',
                    'description': 'Beliefs about inherent abilities or predispositions for certain domains.',
                    'typical_stability': 75
                },
                {
                    'code': 'talent_domain',
                    'name': 'Domain-Specific Talent',
                    'description': 'Beliefs about one\'s innate abilities in particular areas of activity.',
                    'typical_stability': 70
                },
                {
                    'code': 'talent_discovery',
                    'name': 'Talent Discovery',
                    'description': 'Beliefs about how and when talents are identified or revealed.',
                    'typical_stability': 60
                },
                {
                    'code': 'talent_development',
                    'name': 'Talent Development',
                    'description': 'Beliefs about the relationship between innate talent and deliberate practice.',
                    'typical_stability': 65
                }
            ],
            
            BeliefType.FEAR_PATTERN: [
                {
                    'code': 'fear_failure',
                    'name': 'Fear of Failure',
                    'description': 'Beliefs about the consequences and meaning of failure or defeat.',
                    'typical_stability': 75
                },
                {
                    'code': 'fear_rejection',
                    'name': 'Fear of Rejection',
                    'description': 'Beliefs about social exclusion, abandonment, or disapproval.',
                    'typical_stability': 80
                },
                {
                    'code': 'fear_uncertainty',
                    'name': 'Fear of Uncertainty',
                    'description': 'Beliefs about ambiguity, unpredictability, and the unknown.',
                    'typical_stability': 70
                },
                {
                    'code': 'fear_vulnerability',
                    'name': 'Fear of Vulnerability',
                    'description': 'Beliefs about the risks associated with emotional openness or authenticity.',
                    'typical_stability': 75
                }
            ],
            
            BeliefType.AVOIDANCE: [
                {
                    'code': 'avoidance_emotional',
                    'name': 'Emotional Avoidance',
                    'description': 'Beliefs about the need to suppress or escape from difficult emotions.',
                    'typical_stability': 75
                },
                {
                    'code': 'avoidance_conflict',
                    'name': 'Conflict Avoidance',
                    'description': 'Beliefs about the nature and consequences of interpersonal disagreement.',
                    'typical_stability': 70
                },
                {
                    'code': 'avoidance_risk',
                    'name': 'Risk Avoidance',
                    'description': 'Beliefs about the balance between safety and opportunity in decision-making.',
                    'typical_stability': 65
                },
                {
                    'code': 'avoidance_discomfort',
                    'name': 'Discomfort Tolerance',
                    'description': 'Beliefs about one\'s capacity to endure physical or psychological discomfort.',
                    'typical_stability': 60
                }
            ],
            
            BeliefType.TRAUMA_RESPONSE: [
                {
                    'code': 'trauma_safety',
                    'name': 'Safety Beliefs',
                    'description': 'Beliefs about personal security and the predictability of harm.',
                    'typical_stability': 85
                },
                {
                    'code': 'trauma_trust',
                    'name': 'Trust After Trauma',
                    'description': 'Beliefs about the reliability and intentions of others in light of past betrayal or harm.',
                    'typical_stability': 80
                },
                {
                    'code': 'trauma_control',
                    'name': 'Post-Trauma Control',
                    'description': 'Beliefs about one\'s ability to prevent or manage negative events after experiencing trauma.',
                    'typical_stability': 75
                },
                {
                    'code': 'trauma_meaning',
                    'name': 'Trauma Meaning-Making',
                    'description': 'Beliefs about the significance or purpose of traumatic experiences.',
                    'typical_stability': 70
                }
            ],
            
            BeliefType.SOCIAL_CONNECTION: [
                {
                    'code': 'social_trust',
                    'name': 'Interpersonal Trust',
                    'description': 'Beliefs about the general trustworthiness and reliability of other people.',
                    'typical_stability': 75
                },
                {
                    'code': 'social_reciprocity',
                    'name': 'Social Reciprocity',
                    'description': 'Beliefs about fairness and balance in relationships and social exchanges.',
                    'typical_stability': 70
                },
                {
                    'code': 'social_understanding',
                    'name': 'Social Understanding',
                    'description': 'Beliefs about one\'s ability to understand and navigate social dynamics.',
                    'typical_stability': 65
                },
                {
                    'code': 'social_intimacy',
                    'name': 'Intimacy Beliefs',
                    'description': 'Beliefs about emotional closeness and vulnerability in relationships.',
                    'typical_stability': 75
                }
            ],
            
            BeliefType.BELONGING: [
                {
                    'code': 'belonging_acceptance',
                    'name': 'Social Acceptance',
                    'description': 'Beliefs about being welcomed and valued by others.',
                    'typical_stability': 70
                },
                {
                    'code': 'belonging_connection',
                    'name': 'Social Connection',
                    'description': 'Beliefs about the nature and importance of community and friendship.',
                    'typical_stability': 75
                },
                {
                    'code': 'belonging_identity',
                    'name': 'Group Identity',
                    'description': 'Beliefs about one\'s membership in and identification with social groups.',
                    'typical_stability': 80
                },
                {
                    'code': 'belonging_exclusion',
                    'name': 'Social Exclusion',
                    'description': 'Beliefs about rejection, isolation, or being an outsider.',
                    'typical_stability': 75
                }
            ],
            
            BeliefType.TRUST: [
                {
                    'code': 'trust_self',
                    'name': 'Self-Trust',
                    'description': 'Beliefs about one\'s own reliability, judgment, and decision-making capacity.',
                    'typical_stability': 65
                },
                {
                    'code': 'trust_others',
                    'name': 'Trust in Others',
                    'description': 'Beliefs about the reliability, benevolence, and integrity of other people.',
                    'typical_stability': 70
                },
                {
                    'code': 'trust_systems',
                    'name': 'Trust in Systems',
                    'description': 'Beliefs about the fairness and reliability of social, economic, or political institutions.',
                    'typical_stability': 75
                },
                {
                    'code': 'trust_recovery',
                    'name': 'Trust Recovery',
                    'description': 'Beliefs about the possibility of rebuilding trust after it has been damaged.',
                    'typical_stability': 60
                }
            ],
            
            BeliefType.MEANING: [
                {
                    'code': 'meaning_purpose',
                    'name': 'Life Purpose',
                    'description': 'Beliefs about one\'s unique contribution, role, or ultimate destination in life.',
                    'typical_stability': 75
                },
                {
                    'code': 'meaning_coherence',
                    'name': 'Life Coherence',
                    'description': 'Beliefs about the order, structure, and comprehensibility of life events.',
                    'typical_stability': 70
                },
                {
                    'code': 'meaning_significance',
                    'name': 'Personal Significance',
                    'description': 'Beliefs about the impact and importance of one\'s existence and actions.',
                    'typical_stability': 80
                },
                {
                    'code': 'meaning_transcendence',
                    'name': 'Transcendent Meaning',
                    'description': 'Beliefs about spiritual or existential dimensions of life\'s meaning.',
                    'typical_stability': 85
                }
            ],
            
            BeliefType.WORLD_VIEW: [
                {
                    'code': 'worldview_justice',
                    'name': 'Just World',
                    'description': 'Beliefs about fairness, justice, and whether people generally get what they deserve.',
                    'typical_stability': 80
                },
                {
                    'code': 'worldview_benevolence',
                    'name': 'World Benevolence',
                    'description': 'Beliefs about whether the world is fundamentally good, safe, and supportive.',
                    'typical_stability': 75
                },
                {
                    'code': 'worldview_control',
                    'name': 'World Controllability',
                    'description': 'Beliefs about whether life events are orderly and predictable versus chaotic and random.',
                    'typical_stability': 70
                },
                {
                    'code': 'worldview_complexity',
                    'name': 'World Complexity',
                    'description': 'Beliefs about whether the world is simple and straightforward versus complex and nuanced.',
                    'typical_stability': 65
                }
            ],
            
            BeliefType.CHANGE_ORIENTATION: [
                {
                    'code': 'change_attitude',
                    'name': 'Change Attitude',
                    'description': 'Beliefs about whether change is generally positive or negative.',
                    'typical_stability': 65
                },
                {
                    'code': 'change_pace',
                    'name': 'Change Pace',
                    'description': 'Beliefs about the ideal rate of change in life and society.',
                    'typical_stability': 60
                },
                {
                    'code': 'change_adaptability',
                    'name': 'Personal Adaptability',
                    'description': 'Beliefs about one\'s capacity to adjust to new circumstances.',
                    'typical_stability': 55
                },
                {
                    'code': 'change_permanence',
                    'name': 'Change Permanence',
                    'description': 'Beliefs about whether changes tend to be lasting or temporary.',
                    'typical_stability': 70
                }
            ],
            
            BeliefType.SUCCESS_DEFINITION: [
                {
                    'code': 'success_criteria',
                    'name': 'Success Criteria',
                    'description': 'Beliefs about what constitutes achievement, accomplishment, and worthwhile outcomes.',
                    'typical_stability': 70
                },
                {
                    'code': 'success_external',
                    'name': 'External Success',
                    'description': 'Beliefs about the importance of recognition, status, and validation from others.',
                    'typical_stability': 75
                },
                {
                    'code': 'success_internal',
                    'name': 'Internal Success',
                    'description': 'Beliefs about personal standards and subjective measures of achievement.',
                    'typical_stability': 65
                },
                {
                    'code': 'success_holistic',
                    'name': 'Holistic Success',
                    'description': 'Beliefs about balance across different life domains as a measure of success.',
                    'typical_stability': 60
                }
            ],
            
            BeliefType.GOAL_ACHIEVEMENT: [
                {
                    'code': 'goal_difficulty',
                    'name': 'Goal Difficulty',
                    'description': 'Beliefs about the appropriate level of challenge for personal goals.',
                    'typical_stability': 60
                },
                {
                    'code': 'goal_path',
                    'name': 'Goal Path',
                    'description': 'Beliefs about the linearity or complexity of the path to achievement.',
                    'typical_stability': 65
                },
                {
                    'code': 'goal_timeframe',
                    'name': 'Goal Timeframe',
                    'description': 'Beliefs about optimal timelines for accomplishment and progress evaluation.',
                    'typical_stability': 55
                },
                {
                    'code': 'goal_flexibility',
                    'name': 'Goal Flexibility',
                    'description': 'Beliefs about adaptability versus commitment in goal pursuit.',
                    'typical_stability': 50
                }
            ],
            
            BeliefType.EFFORT_BELIEF: [
                {
                    'code': 'effort_value',
                    'name': 'Effort Value',
                    'description': 'Beliefs about the relationship between effort and outcomes.',
                    'typical_stability': 70
                },
                {
                    'code': 'effort_persistence',
                    'name': 'Persistence Value',
                    'description': 'Beliefs about the importance of continued effort despite obstacles or setbacks.',
                    'typical_stability': 65
                },
                {
                    'code': 'effort_efficiency',
                    'name': 'Effort Efficiency',
                    'description': 'Beliefs about working smart versus working hard.',
                    'typical_stability': 60
                },
                {
                    'code': 'effort_sacrifice',
                    'name': 'Sacrifice Value',
                    'description': 'Beliefs about the necessity and virtue of sacrifice in achievement.',
                    'typical_stability': 75
                }
            ],
            
            BeliefType.EMOTIONAL_CONTROL: [
                {
                    'code': 'emotion_regulation',
                    'name': 'Emotion Regulation',
                    'description': 'Beliefs about one\'s ability to manage and modify emotional responses.',
                    'typical_stability': 65
                },
                {
                    'code': 'emotion_expression',
                    'name': 'Emotion Expression',
                    'description': 'Beliefs about the appropriateness and consequences of expressing emotions.',
                    'typical_stability': 70
                },
                {
                    'code': 'emotion_awareness',
                    'name': 'Emotional Awareness',
                    'description': 'Beliefs about the importance and utility of recognizing one\'s emotions.',
                    'typical_stability': 60
                },
                {
                    'code': 'emotion_intelligence',
                    'name': 'Emotional Intelligence',
                    'description': 'Beliefs about one\'s capacity to understand and respond to emotions in self and others.',
                    'typical_stability': 65
                }
            ],
            
            BeliefType.VULNERABILITY: [
                {
                    'code': 'vulnerability_safety',
                    'name': 'Vulnerability Safety',
                    'description': 'Beliefs about whether emotional openness leads to harm or connection.',
                    'typical_stability': 75
                },
                {
                    'code': 'vulnerability_strength',
                    'name': 'Vulnerability Strength',
                    'description': 'Beliefs about whether vulnerability is a weakness or a form of courage.',
                    'typical_stability': 70
                },
                {
                    'code': 'vulnerability_boundaries',
                    'name': 'Vulnerability Boundaries',
                    'description': 'Beliefs about appropriate limits for emotional disclosure.',
                    'typical_stability': 65
                },
                {
                    'code': 'vulnerability_reception',
                    'name': 'Vulnerability Reception',
                    'description': 'Beliefs about how others typically respond to displays of vulnerability.',
                    'typical_stability': 75
                }
            ],
            
            BeliefType.RESILIENCE: [
                {
                    'code': 'resilience_recovery',
                    'name': 'Recovery Capacity',
                    'description': 'Beliefs about one\'s ability to bounce back from adversity or failure.',
                    'typical_stability': 65
                },
                {
                    'code': 'resilience_growth',
                    'name': 'Post-Stress Growth',
                    'description': 'Beliefs about whether challenges lead to deterioration or development.',
                    'typical_stability': 60
                },
                {
                    'code': 'resilience_resources',
                    'name': 'Coping Resources',
                    'description': 'Beliefs about available support systems and strategies for coping with difficulty.',
                    'typical_stability': 55
                },
                {
                    'code': 'resilience_meaning',
                    'name': 'Adversity Meaning',
                    'description': 'Beliefs about the significance and purpose of hardship in life.',
                    'typical_stability': 70
                }
            ]
        }

        # Create or update each belief type
        for category, belief_list in beliefs.items():
            self.stdout.write(f"Processing {category} belief types...")
            
            for belief_data in belief_list:
                belief, created = GenericBelief.objects.update_or_create(
                    code=belief_data['code'],
                    defaults={
                        'category': category,
                        'name': belief_data['name'],
                        'description': belief_data['description'],
                        'typical_stability': belief_data['typical_stability']
                    }
                )
                
                if created:
                    created_count += 1
                else:
                    updated_count += 1
        
        # Return counts
        return created_count, updated_count
