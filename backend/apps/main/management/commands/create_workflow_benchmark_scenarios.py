"""
Management command to create workflow benchmark scenarios from JSON files.

This command loads workflow benchmark scenarios from JSON files and creates
BenchmarkScenario records in the database.
"""

import json
import logging
import os
from typing import List, Dict, Any

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from apps.main.models import BenchmarkScenario, BenchmarkTag

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create workflow benchmark scenarios from JSON files'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            help='Path to JSON file containing workflow benchmark scenarios'
        )
        parser.add_argument(
            '--dir',
            type=str,
            help='Directory containing JSON files with workflow benchmark scenarios'
        )
        parser.add_argument(
            '--validate',
            action='store_true',
            help='Validate scenarios against schema before creating'
        )
    
    def handle(self, *args, **options):
        file_path = options.get('file')
        dir_path = options.get('dir')
        validate = options.get('validate', False)
        
        if not file_path and not dir_path:
            raise CommandError("Either --file or --dir must be specified")
        
        if file_path and dir_path:
            raise CommandError("Only one of --file or --dir can be specified")
        
        if file_path:
            # Load scenarios from a single file
            self.stdout.write(f"Loading workflow benchmark scenarios from {file_path}")
            try:
                scenarios = self._load_scenarios_from_file(file_path)
                self._create_scenarios(scenarios, validate)
            except Exception as e:
                raise CommandError(f"Error loading scenarios from {file_path}: {str(e)}")
        else:
            # Load scenarios from all JSON files in the directory
            self.stdout.write(f"Loading workflow benchmark scenarios from directory {dir_path}")
            try:
                for filename in os.listdir(dir_path):
                    if filename.endswith('.json'):
                        file_path = os.path.join(dir_path, filename)
                        self.stdout.write(f"Processing {file_path}")
                        scenarios = self._load_scenarios_from_file(file_path)
                        self._create_scenarios(scenarios, validate)
            except Exception as e:
                raise CommandError(f"Error loading scenarios from directory {dir_path}: {str(e)}")
    
    def _load_scenarios_from_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Load scenarios from a JSON file.
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            List of scenario dictionaries
        """
        try:
            with open(file_path, 'r') as f:
                scenarios = json.load(f)
            
            if not isinstance(scenarios, list):
                raise ValueError("JSON file must contain a list of scenarios")
            
            return scenarios
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in {file_path}: {str(e)}")
        except Exception as e:
            raise ValueError(f"Error reading {file_path}: {str(e)}")
    
    def _create_scenarios(self, scenarios: List[Dict[str, Any]], validate: bool = False):
        """
        Create BenchmarkScenario records from scenario dictionaries.
        
        Args:
            scenarios: List of scenario dictionaries
            validate: Whether to validate scenarios against schema
        """
        created_count = 0
        updated_count = 0
        error_count = 0
        
        for scenario_data in scenarios:
            try:
                # Validate the scenario has workflow_type in metadata
                metadata = scenario_data.get('metadata', {})
                if not metadata.get('workflow_type'):
                    self.stdout.write(self.style.WARNING(
                        f"Skipping scenario '{scenario_data.get('name')}': Missing workflow_type in metadata"
                    ))
                    error_count += 1
                    continue
                
                # Validate against schema if requested
                if validate:
                    self._validate_scenario(scenario_data)
                
                # Extract tags
                tags = scenario_data.pop('tags', [])
                
                # Create or update the scenario
                with transaction.atomic():
                    scenario, created = BenchmarkScenario.objects.update_or_create(
                        name=scenario_data['name'],
                        defaults={
                            'description': scenario_data.get('description', ''),
                            'agent_role': scenario_data.get('agent_role', 'workflow'),
                            'input_data': scenario_data.get('input_data', {}),
                            'metadata': metadata,
                            'is_active': scenario_data.get('is_active', True),
                            'version': scenario_data.get('version', '1.0'),
                            'is_latest': True
                        }
                    )
                    
                    # Add tags
                    if tags:
                        for tag_name in tags:
                            tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name)
                            scenario.tags.add(tag)
                    
                    if created:
                        created_count += 1
                        self.stdout.write(self.style.SUCCESS(
                            f"Created scenario '{scenario.name}' (ID: {scenario.id})"
                        ))
                    else:
                        updated_count += 1
                        self.stdout.write(self.style.SUCCESS(
                            f"Updated scenario '{scenario.name}' (ID: {scenario.id})"
                        ))
            except Exception as e:
                error_count += 1
                self.stdout.write(self.style.ERROR(
                    f"Error creating scenario '{scenario_data.get('name', 'unknown')}': {str(e)}"
                ))
        
        # Print summary
        self.stdout.write(f"Created {created_count} scenarios, updated {updated_count} scenarios, {error_count} errors")
    
    def _validate_scenario(self, scenario_data: Dict[str, Any]):
        """
        Validate a scenario against schema.
        
        Args:
            scenario_data: Scenario dictionary
        """
        # Import validation service here to avoid circular imports
        from apps.main.services.schema_validation_service import SchemaValidationService
        
        # Initialize validation service
        validation_service = SchemaValidationService()
        
        # Validate the scenario
        validation_result = validation_service.validate_benchmark_scenario(scenario_data)
        
        if not validation_result.is_valid:
            error_messages = []
            for component, errors in validation_result.errors.items():
                error_messages.append(f"{component}: {', '.join(errors)}")
            
            raise ValueError(f"Schema validation failed: {'; '.join(error_messages)}")
