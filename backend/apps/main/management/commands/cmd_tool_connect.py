from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
import logging
from typing import Dict, List, Set

from apps.main.models import Agent<PERSON><PERSON>, GenericAgent, AgentTool

logger = logging.getLogger(__name__)

# Define which tools should be available to which agent roles
AGENT_TOOL_MAPPING = {
    AgentRole.DISPATCHER: [
        # Core message analysis and context extraction tools
        'get_user_profile',  # To check profile completion status
        'extract_context',   # Detailed context extraction from natural language
        
        # Additional tools to support comprehensive message classification
        'get_communication_guidelines',  # To understand user communication preferences
        'get_recent_interactions',  # To provide conversational context
        
        # Tools for workflow routing and decision support
        'classify_workflow',  # Potential tool for advanced workflow classification
        'get_workflow_history',  # To understand past interaction patterns
        
        # User state and onboarding tools
        'check_profile_completion',  # Explicit tool for checking profile readiness
        'get_user_state',  # To retrieve current user context and state
    ],
    AgentRole.MENTOR: [
        'get_user_profile',
        'get_activity_details',
        'record_user_feedback',
        'get_recent_interactions',
        'update_current_mood',
        'get_communication_guidelines',
        'explain_activity_selection',
        'present_activity_options',
        'generate_reflection_prompt',
        'format_activity_instructions',
        'handle_activity_refusal',
    ],
    AgentRole.ORCHESTRATOR: [
        'get_user_profile',
        'get_activity_details',
        'get_recent_interactions',
        'get_wheel_details',
    ],
    AgentRole.RESOURCE: [
        'get_user_profile',
        'get_activity_details',
        'get_recent_interactions',
    ],
    AgentRole.ENGAGEMENT: [
        'get_user_profile',
        'get_recent_interactions',
        'get_user_history_analysis',
    ],
    AgentRole.PSYCHOLOGICAL: [
        'get_user_profile',
        'get_recent_interactions',
        'update_current_mood',
        'get_user_history_analysis',
    ],
    AgentRole.STRATEGY: [
        'get_user_profile',
        'get_activity_details',
        'get_recent_interactions',
        'get_user_history_analysis',
    ],
    AgentRole.ACTIVITY: [
        'get_user_profile',
        'get_activity_details',
        'get_wheel_details',
        'present_activity_options',
    ],
    AgentRole.ETHICAL: [
        'get_user_profile',
        'get_activity_details',
        'get_recent_interactions',
    ],
}

# Define tools that should be available to all agents
COMMON_TOOLS = [
    # No common tools defined - if needed, add them here
]


class Command(BaseCommand):
    help = 'Connects agents to their appropriate tools based on agent roles'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing tool connections before creating new ones',
        )
        parser.add_argument(
            '--role',
            type=str,
            help='Only update tools for a specific agent role',
            choices=[role[0] for role in AgentRole.choices],
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Provide detailed output of operations',
        )

    def handle(self, *args, **options):
        reset = options.get('reset', False)
        specific_role = options.get('role')
        verbose = options.get('verbose', False)
        
        try:
            with transaction.atomic():
                # Get all active tools
                all_tools = {tool.code: tool for tool in AgentTool.objects.filter(is_active=True)}
                
                if not all_tools:
                    raise CommandError("No active tools found in the database. Make sure tools are registered first.")
                
                # Process each agent role
                roles_to_process = [specific_role] if specific_role else [role[0] for role in AgentRole.choices]
                
                for role in roles_to_process:
                    self.update_agent_tools(role, all_tools, reset, verbose)
                
                self.stdout.write(self.style.SUCCESS(
                    f"Successfully connected agents to their tools for {len(roles_to_process)} roles"
                ))
                
        except Exception as e:
            logger.exception("Error connecting agents to tools")
            raise CommandError(f"Failed to connect agents to tools: {str(e)}")

    def update_agent_tools(self, role: str, all_tools: Dict[str, AgentTool], reset: bool, verbose: bool):
        """Update tools for a specific agent role"""
        # Get tool codes this role should have
        tool_codes = set(AGENT_TOOL_MAPPING.get(role, []) + COMMON_TOOLS)
        
        # Find agents with this role
        agents = GenericAgent.objects.filter(role=role, is_active=True)
        
        if not agents.exists():
            if verbose:
                self.stdout.write(self.style.WARNING(f"No active agents found with role '{role}'"))
            return
        
        # Process each agent
        for agent in agents:
            if verbose:
                self.stdout.write(f"Processing agent: {agent.get_role_display()} (v{agent.version})")
            
            # Get current tools
            current_tools = set(agent.available_tools.all())
            
            # Get tools that should be added
            tools_to_add = []
            missing_tools = []
            
            for code in tool_codes:
                if code in all_tools:
                    tools_to_add.append(all_tools[code])
                else:
                    missing_tools.append(code)
            
            # Reset if requested
            if reset:
                if verbose:
                    self.stdout.write(f"  Resetting tools for {agent.get_role_display()}")
                agent.available_tools.clear()
                current_tools = set()
            
            # Add tools
            for tool in tools_to_add:
                if tool not in current_tools:
                    agent.available_tools.add(tool)
                    if verbose:
                        self.stdout.write(f"  Added tool: {tool.name}")
            
            # Report any missing tools
            if missing_tools and verbose:
                self.stdout.write(self.style.WARNING(
                    f"  Missing tools for {agent.get_role_display()}: {', '.join(missing_tools)}"
                ))
                
            # Verify tool's allowed_agent_roles includes this agent's role
            for tool in tools_to_add:
                if role not in tool.allowed_agent_roles:
                    if verbose:
                        self.stdout.write(self.style.WARNING(
                            f"  Adding role '{role}' to allowed_agent_roles for tool: {tool.name}"
                        ))
                    tool.allowed_agent_roles.append(role)
                    tool.save()