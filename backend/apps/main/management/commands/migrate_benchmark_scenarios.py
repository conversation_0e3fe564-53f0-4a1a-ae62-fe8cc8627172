"""
Django management command for migrating benchmark scenarios to the new directory structure.
"""

import os
import json
import logging
import asyncio
from django.core.management.base import BaseCommand
from apps.main.services.benchmark_migration import BenchmarkMigration
from apps.main.services.benchmark_validation import BenchmarkValidation

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Migrates benchmark scenarios to the new directory structure'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            help='Path to a specific JSON file to migrate'
        )
        parser.add_argument(
            '--validate-only',
            action='store_true',
            help='Only validate the directory structure without migrating'
        )
        parser.add_argument(
            '--report',
            action='store_true',
            help='Generate a comprehensive migration report'
        )
        parser.add_argument(
            '--report-file',
            type=str,
            help='Path to save the migration report (JSON format)'
        )

    def handle(self, *args, **options):
        # Set up event loop and run async logic
        try:
            asyncio.run(self.async_handle(**options))
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"An unexpected error occurred: {e}"))
            import traceback
            self.stderr.write(traceback.format_exc())
            raise

    async def async_handle(self, **options):
        # First, ensure the directory structure exists
        await self._setup_directory_structure()
        
        # If validate-only, just validate and exit
        if options.get('validate_only'):
            await self._validate_structure()
            return
            
        # If report, generate a report and exit
        if options.get('report'):
            await self._generate_report(options.get('report_file'))
            return
            
        # Otherwise, migrate scenarios
        if options.get('file'):
            await self._migrate_file(options.get('file'))
        else:
            await self._migrate_all()

    async def _setup_directory_structure(self):
        """Ensure the directory structure exists"""
        # Import and run the setup_benchmark_structure command
        from django.core.management import call_command
        call_command('setup_benchmark_structure')
        self.stdout.write("Directory structure verified")

    async def _validate_structure(self):
        """Validate the directory structure"""
        validator = BenchmarkValidation()
        result = await validator.validate_directory_structure()
        
        if result['valid']:
            self.stdout.write(self.style.SUCCESS("Directory structure is valid"))
        else:
            self.stdout.write(self.style.ERROR("Directory structure is invalid"))
            if result['missing_dirs']:
                self.stdout.write("Missing directories:")
                for dir_path in result['missing_dirs']:
                    self.stdout.write(f"  - {dir_path}")
            if result['extra_dirs']:
                self.stdout.write("Unexpected directories:")
                for dir_path in result['extra_dirs']:
                    self.stdout.write(f"  - {dir_path}")

    async def _generate_report(self, report_file=None):
        """Generate a comprehensive migration report"""
        validator = BenchmarkValidation()
        report = await validator.generate_migration_report()
        
        # Print summary
        self.stdout.write("Migration Report Summary:")
        
        # Directory structure
        dir_result = report['directory_structure']
        if dir_result['valid']:
            self.stdout.write(self.style.SUCCESS("✓ Directory structure is valid"))
        else:
            self.stdout.write(self.style.ERROR("✗ Directory structure is invalid"))
            if dir_result['missing_dirs']:
                self.stdout.write("  Missing directories:")
                for dir_path in dir_result['missing_dirs']:
                    self.stdout.write(f"    - {dir_path}")
        
        # Scenarios
        scenario_result = report['scenarios']
        if scenario_result['valid']:
            self.stdout.write(self.style.SUCCESS(f"✓ All {scenario_result['total']} scenarios are valid"))
        else:
            self.stdout.write(self.style.ERROR(
                f"✗ {scenario_result['invalid_count']} of {scenario_result['total']} scenarios are invalid"
            ))
        
        # Templates
        template_result = report['templates']
        if template_result['valid']:
            self.stdout.write(self.style.SUCCESS(f"✓ All {template_result['total']} templates are valid"))
        else:
            self.stdout.write(self.style.ERROR(
                f"✗ {template_result['invalid_count']} of {template_result['total']} templates are invalid"
            ))
        
        # Save report to file if requested
        if report_file:
            os.makedirs(os.path.dirname(os.path.abspath(report_file)), exist_ok=True)
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            self.stdout.write(self.style.SUCCESS(f"Report saved to {report_file}"))

    async def _migrate_file(self, file_path):
        """Migrate scenarios from a specific file"""
        if not os.path.exists(file_path):
            self.stderr.write(self.style.ERROR(f"File not found: {file_path}"))
            return
            
        self.stdout.write(f"Migrating scenarios from {file_path}...")
        
        migration = BenchmarkMigration()
        result = await migration._migrate_file(file_path)
        
        if result['success']:
            self.stdout.write(self.style.SUCCESS(
                f"Successfully migrated {result['succeeded']} of {result['total']} scenarios"
            ))
        else:
            self.stdout.write(self.style.ERROR(
                f"Migration failed: {result['succeeded']} of {result['total']} scenarios migrated"
            ))
            
        # Print details of failed scenarios
        if result['failed'] > 0:
            self.stdout.write("Failed scenarios:")
            for scenario_result in result['scenario_results']:
                if not scenario_result['success']:
                    self.stdout.write(f"  - {scenario_result.get('target_path', 'unknown')}")
                    for error in scenario_result.get('errors', []):
                        self.stdout.write(f"    - {error}")

    async def _migrate_all(self):
        """Migrate all scenarios"""
        self.stdout.write("Migrating all scenarios...")
        
        migration = BenchmarkMigration()
        result = await migration.migrate_all_scenarios()
        
        if result['success']:
            self.stdout.write(self.style.SUCCESS(
                f"Successfully migrated {result['succeeded']} of {result['total']} scenarios"
            ))
        else:
            self.stdout.write(self.style.ERROR(
                f"Migration failed: {result['succeeded']} of {result['total']} scenarios migrated"
            ))
            
        # Print details of failed scenarios
        if result['failed'] > 0:
            self.stdout.write("Failed scenarios:")
            for file_result in result['scenario_results']:
                if not file_result['success']:
                    self.stdout.write(f"  - {file_result.get('file_path', 'unknown')}")
                    if 'error' in file_result:
                        self.stdout.write(f"    - {file_result['error']}")
                    
                    # Print details of failed scenarios in this file
                    for scenario_result in file_result.get('scenario_results', []):
                        if not scenario_result['success']:
                            self.stdout.write(f"    - {scenario_result.get('target_path', 'unknown')}")
                            for error in scenario_result.get('errors', []):
                                self.stdout.write(f"      - {error}")
