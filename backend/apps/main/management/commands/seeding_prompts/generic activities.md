following current process of seeding the db with GenericActivity objects using 'seed_db_70_activities.py', create the 'self.create_productive_activities()' function, respecting the following guidelines.
---
# Guidelines

Coverage across all domains to ensure good representation from each activity domain:
	*Physical activities (cardio, strength, flexibility, dance, etc.)
	*Social activities (connection, group dynamics, communication, etc.)
	*Creative activities (visual arts, music, writing, culinary, etc.)
	*Intellectual activities (learning, problem-solving, languages, etc.)
	*Reflective activities (meditation, journaling, mindfulness, etc.)
	*Emotional activities (emotional awareness, expression, regulation, etc.)
	*Exploratory activities (travel, risk-taking, sensory exploration, etc.)
	*Productive activities (organization, habit formation, skill development, etc.)
	*Leisure activities (relaxation, games, entertainment, hobbies, etc.)
	
Challenge diversity: Include activities with varying difficulty levels:
	*Beginner-friendly with minimal trait requirements
	*Intermediate with moderate requirements
	*Advanced with significant trait/skill requirements
	*Expert-level activities for specific domains
	
Rich connections to catalogs:
	*Link activities to appropriate traits (e.g., a social activity should reference extraversion traits)
	*Connect to relevant environments (e.g., outdoor activities should require outdoor environments)
	*Reference beliefs that the activity might challenge or reinforce
	*Include limitation considerations that might affect participation
Detailed metadata:
	*Clear instructions for activity execution
	*Realistic duration ranges
	*Well-defined social requirements
	*Appropriate tags for filtering