"""
Django management command for loading contextual evaluation templates from JSON files.
"""

import os
import json
import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.main.models import EvaluationCriteriaTemplate

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Load contextual evaluation templates from JSON files'

    def add_arguments(self, parser):
        parser.add_argument(
            '--templates-dir',
            type=str,
            default='testing/benchmark_data/contextual_templates',
            help='Directory containing contextual template JSON files'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update existing templates'
        )

    def handle(self, *args, **options):
        templates_dir = options['templates_dir']
        force = options['force']
        
        if not os.path.exists(templates_dir):
            self.stdout.write(
                self.style.ERROR(f"Templates directory not found: {templates_dir}")
            )
            return

        self.stdout.write(f"Loading contextual templates from {templates_dir}...")
        
        templates_loaded = 0
        templates_updated = 0
        templates_skipped = 0
        
        for filename in os.listdir(templates_dir):
            if not filename.endswith('.json'):
                continue
                
            template_path = os.path.join(templates_dir, filename)
            
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                
                # Process single template or list of templates
                templates = template_data if isinstance(template_data, list) else [template_data]
                
                for template in templates:
                    result = self._load_template(template, force)
                    if result == 'loaded':
                        templates_loaded += 1
                    elif result == 'updated':
                        templates_updated += 1
                    else:
                        templates_skipped += 1
                        
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error processing {template_path}: {e}")
                )
                logger.error(f"Error loading contextual template from {template_path}", exc_info=True)
        
        self.stdout.write(
            self.style.SUCCESS(
                f"Contextual templates processing complete: "
                f"{templates_loaded} loaded, {templates_updated} updated, {templates_skipped} skipped"
            )
        )

    def _load_template(self, template_data, force=False):
        """Load a single contextual template."""
        name = template_data.get('name')
        if not name:
            self.stdout.write(
                self.style.WARNING("Skipping template - missing name")
            )
            return 'skipped'
        
        description = template_data.get('description', '')
        workflow_type = template_data.get('workflow_type', '')
        category = template_data.get('category', 'contextual')
        criteria = template_data.get('criteria', {})
        contextual_criteria = template_data.get('contextual_criteria', {})
        variable_ranges = template_data.get('variable_ranges', {})
        is_active = template_data.get('is_active', True)
        
        if not criteria:
            self.stdout.write(
                self.style.WARNING(f"Skipping template '{name}' - missing criteria")
            )
            return 'skipped'
        
        try:
            with transaction.atomic():
                # Check if template already exists
                existing_template = EvaluationCriteriaTemplate.objects.filter(name=name).first()
                
                if existing_template and not force:
                    self.stdout.write(
                        self.style.WARNING(f"Template '{name}' already exists. Use --force to update.")
                    )
                    return 'skipped'
                
                # Create or update template
                template_obj, created = EvaluationCriteriaTemplate.objects.update_or_create(
                    name=name,
                    defaults={
                        'description': description,
                        'workflow_type': workflow_type,
                        'category': category,
                        'criteria': criteria,
                        'contextual_criteria': contextual_criteria,
                        'variable_ranges': variable_ranges,
                        'is_active': is_active
                    }
                )
                
                if created:
                    self.stdout.write(f"Created contextual template: {name}")
                    return 'loaded'
                else:
                    self.stdout.write(f"Updated contextual template: {name}")
                    return 'updated'
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error creating/updating template '{name}': {e}")
            )
            logger.error(f"Error creating/updating contextual template '{name}'", exc_info=True)
            return 'skipped'
