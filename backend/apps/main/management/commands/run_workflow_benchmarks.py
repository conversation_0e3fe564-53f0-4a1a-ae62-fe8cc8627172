"""
Management command to run workflow benchmarks.

This command allows running workflow benchmarks from the command line.
"""

import json
import logging
import uuid
from typing import Dict, Any, Optional

from django.core.management.base import BaseCommand, CommandError
from asgiref.sync import async_to_sync

from apps.main.models import BenchmarkScenario
from apps.main.services.wheel_workflow_benchmark_manager import WheelWorkflowBenchmarkManager

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Run workflow benchmarks for specified scenarios or workflow types'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--scenario-id',
            type=str,
            help='ID of a specific benchmark scenario to run'
        )
        parser.add_argument(
            '--workflow-type',
            type=str,
            help='Type of workflow to benchmark (e.g., wheel_generation)'
        )
        parser.add_argument(
            '--params',
            type=str,
            default='{}',
            help='JSON string of parameters for the benchmark'
        )
        parser.add_argument(
            '--output-file',
            type=str,
            help='Path to save benchmark results as JSON'
        )
    
    def handle(self, *args, **options):
        scenario_id = options.get('scenario_id')
        workflow_type = options.get('workflow_type')
        params_str = options.get('params', '{}')
        output_file = options.get('output_file')
        
        # Parse parameters
        try:
            params = json.loads(params_str)
            if not isinstance(params, dict):
                raise ValueError("Parameters must be a JSON object")
        except json.JSONDecodeError as e:
            raise CommandError(f"Invalid JSON in --params: {e}")
        
        # Validate arguments
        if not scenario_id and not workflow_type:
            raise CommandError("Either --scenario-id or --workflow-type must be specified")
        
        # Run benchmarks
        if scenario_id:
            # Run a specific scenario
            try:
                scenario_uuid = uuid.UUID(scenario_id)
                self.run_single_benchmark(scenario_uuid, params, output_file)
            except ValueError:
                raise CommandError(f"Invalid UUID format: {scenario_id}")
        else:
            # Run all scenarios for a workflow type
            self.run_workflow_type_benchmarks(workflow_type, params, output_file)
    
    def run_single_benchmark(self, scenario_id: uuid.UUID, params: Dict[str, Any], output_file: Optional[str] = None):
        """
        Run a benchmark for a specific scenario.
        
        Args:
            scenario_id: ID of the benchmark scenario
            params: Parameters for the benchmark
            output_file: Optional path to save results as JSON
        """
        try:
            # Get the scenario
            scenario = BenchmarkScenario.objects.get(id=scenario_id)
            self.stdout.write(f"Running workflow benchmark for scenario: {scenario.name}")
            
            # Get workflow type from scenario metadata
            workflow_type = scenario.metadata.get('workflow_type')
            if not workflow_type:
                raise CommandError(f"Scenario {scenario.name} is not configured for workflow benchmarking (missing workflow_type)")
            
            # Select the appropriate workflow benchmark manager
            if workflow_type == 'wheel_generation':
                benchmark_manager = WheelWorkflowBenchmarkManager()
            else:
                raise CommandError(f"Unsupported workflow type: {workflow_type}")
            
            # Run the benchmark
            benchmark_run = async_to_sync(benchmark_manager.execute_benchmark)(
                scenario_id=scenario_id,
                params=params,
                progress_callback=self._progress_callback
            )
            
            # Print results
            self.stdout.write(self.style.SUCCESS(f"Benchmark completed for scenario: {scenario.name}"))
            self.stdout.write(f"Mean duration: {benchmark_run.mean_duration:.2f} ms")
            self.stdout.write(f"Success rate: {benchmark_run.success_rate:.2f}%")
            
            # Save results to file if specified
            if output_file:
                with open(output_file, 'w') as f:
                    json.dump(benchmark_run.raw_results, f, indent=2)
                self.stdout.write(self.style.SUCCESS(f"Results saved to {output_file}"))
            
        except BenchmarkScenario.DoesNotExist:
            raise CommandError(f"Benchmark scenario with ID {scenario_id} not found")
        except Exception as e:
            raise CommandError(f"Error running benchmark: {str(e)}")
    
    def run_workflow_type_benchmarks(self, workflow_type: str, params: Dict[str, Any], output_file: Optional[str] = None):
        """
        Run benchmarks for all scenarios of a specific workflow type.
        
        Args:
            workflow_type: Type of workflow to benchmark
            params: Parameters for the benchmarks
            output_file: Optional path to save results as JSON
        """
        try:
            # Get all active scenarios for the workflow type
            scenarios = BenchmarkScenario.objects.filter(
                is_active=True,
                metadata__workflow_type=workflow_type
            ).order_by('name')
            
            if not scenarios:
                self.stdout.write(self.style.WARNING(f"No active scenarios found for workflow type: {workflow_type}"))
                return
            
            self.stdout.write(f"Running {scenarios.count()} workflow benchmarks for type: {workflow_type}")
            
            # Select the appropriate workflow benchmark manager
            if workflow_type == 'wheel_generation':
                benchmark_manager = WheelWorkflowBenchmarkManager()
            else:
                raise CommandError(f"Unsupported workflow type: {workflow_type}")
            
            # Run benchmarks for each scenario
            results = []
            for i, scenario in enumerate(scenarios):
                self.stdout.write(f"Running benchmark {i+1}/{scenarios.count()}: {scenario.name}")
                
                try:
                    # Run the benchmark
                    benchmark_run = async_to_sync(benchmark_manager.execute_benchmark)(
                        scenario_id=scenario.id,
                        params=params,
                        progress_callback=self._progress_callback
                    )
                    
                    # Print results
                    self.stdout.write(self.style.SUCCESS(f"Benchmark completed for scenario: {scenario.name}"))
                    self.stdout.write(f"Mean duration: {benchmark_run.mean_duration:.2f} ms")
                    self.stdout.write(f"Success rate: {benchmark_run.success_rate:.2f}%")
                    
                    # Add to results
                    results.append({
                        'scenario_id': str(scenario.id),
                        'scenario_name': scenario.name,
                        'mean_duration': benchmark_run.mean_duration,
                        'success_rate': benchmark_run.success_rate,
                        'benchmark_run_id': str(benchmark_run.id)
                    })
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Error running benchmark for scenario {scenario.name}: {str(e)}"))
                    results.append({
                        'scenario_id': str(scenario.id),
                        'scenario_name': scenario.name,
                        'error': str(e)
                    })
            
            # Save results to file if specified
            if output_file:
                with open(output_file, 'w') as f:
                    json.dump({
                        'workflow_type': workflow_type,
                        'total_scenarios': scenarios.count(),
                        'completed': len([r for r in results if 'error' not in r]),
                        'failed': len([r for r in results if 'error' in r]),
                        'results': results
                    }, f, indent=2)
                self.stdout.write(self.style.SUCCESS(f"Results saved to {output_file}"))
            
        except Exception as e:
            raise CommandError(f"Error running benchmarks: {str(e)}")
    
    def _progress_callback(self, state: str, meta: Dict[str, Any]):
        """
        Callback for reporting benchmark progress.
        
        Args:
            state: Current state of the benchmark
            meta: Additional metadata about the progress
        """
        if state == 'PROGRESS':
            current = meta.get('current', 0)
            total = meta.get('total', 100)
            status = meta.get('status', '')
            
            # Calculate percentage
            percent = int(current / total * 100) if total > 0 else 0
            
            # Print progress
            self.stdout.write(f"Progress: {percent}% - {status}")
        elif state == 'SUCCESS':
            self.stdout.write(self.style.SUCCESS("Benchmark completed successfully"))
        elif state == 'FAILURE':
            self.stdout.write(self.style.ERROR(f"Benchmark failed: {meta.get('error', 'Unknown error')}"))
