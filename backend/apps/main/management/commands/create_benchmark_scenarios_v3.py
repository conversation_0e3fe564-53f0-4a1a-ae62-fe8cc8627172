"""
Django management command for creating benchmark scenarios with improved encoding detection.
"""

import os
import json
import logging
import asyncio
import chardet
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings
from asgiref.sync import sync_to_async
from apps.main.models import BenchmarkScenario, BenchmarkTag, EvaluationCriteriaTemplate

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create benchmark scenarios with improved encoding detection'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            help='Path to a JSON file containing an array of scenario definitions'
        )
        parser.add_argument(
            '--dir',
            type=str,
            help='Directory containing JSON files with scenario definitions'
        )
        parser.add_argument(
            '--validate-schema',
            action='store_true',
            help='Validate scenarios against schema before creating'
        )

    def handle(self, *args, **options):
        # Print current directory and database connection for debugging
        self.stdout.write(f"Current directory: {os.getcwd()}")
        self.stdout.write(f"Database settings: {settings.DATABASES['default']}")

        # Print Python version and encoding
        import sys
        self.stdout.write(f"Python version: {sys.version}")
        self.stdout.write(f"Default encoding: {sys.getdefaultencoding()}")

        # Set up event loop and run async logic
        try:
            asyncio.run(self.async_handle(**options))
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"An unexpected error occurred: {e}"))
            import traceback
            self.stderr.write(traceback.format_exc())
            raise

    async def async_handle(self, **options):
        # Determine the source of scenarios
        if options.get('file'):
            await self.create_from_file(options['file'], options)
        elif options.get('dir'):
            await self.create_from_directory(options['dir'], options)
        else:
            self.stdout.write(self.style.ERROR("Please provide either --file or --dir option"))

    async def create_from_file(self, file_path, options):
        """Create scenarios from a JSON file with improved encoding detection"""
        self.stdout.write(f"Creating scenarios from {file_path}...")

        try:
            # Read the file in binary mode for encoding detection
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            # Detect encoding
            result = chardet.detect(raw_data)
            encoding = result['encoding'] or 'utf-8'
            confidence = result['confidence']

            self.stdout.write(f"Detected encoding: {encoding} (confidence: {confidence})")

            # Try to decode with detected encoding
            try:
                content = raw_data.decode(encoding)
                self.stdout.write(f"Successfully decoded with {encoding}")
            except UnicodeDecodeError as e:
                self.stdout.write(self.style.WARNING(f"Error decoding with {encoding}: {e}"))

                # Try alternative encodings
                alternative_encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                decoded_successfully = False
                for alt_encoding in alternative_encodings:
                    if alt_encoding != encoding:
                        try:
                            content = raw_data.decode(alt_encoding)
                            self.stdout.write(f"Successfully decoded with alternative encoding: {alt_encoding}")
                            decoded_successfully = True
                            break
                        except UnicodeDecodeError:
                            self.stdout.write(self.style.WARNING(f"Failed to decode with {alt_encoding}"))
                if not decoded_successfully:
                    raise CommandError(f"Could not decode file {file_path} with any encoding")

        except FileNotFoundError:
            raise CommandError(f"Scenario file not found: {file_path}")
        except Exception as e:
            raise CommandError(f"Error reading scenario file {file_path}: {e}")

        # Try to parse as JSON
        try:
            scenarios_data = json.loads(content)
            # If the loaded data is not a list, wrap it in a list
            if not isinstance(scenarios_data, list):
                scenarios_data = [scenarios_data]
        except json.JSONDecodeError as e:
            raise CommandError(f"Error decoding JSON from {file_path}: {e}")

        # Create scenarios
        success_count = 0
        fail_count = 0

        for scenario_data in scenarios_data:
            try:
                await self.create_scenario(scenario_data, options)
                success_count += 1
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"Error creating scenario '{scenario_data.get('name', 'unknown')}': {str(e)}"))
                fail_count += 1

        self.stdout.write(self.style.SUCCESS(f"Created {success_count} scenarios, failed {fail_count}"))

    async def create_scenario(self, scenario_data, options=None):
        """Create a single scenario in the database"""
        # Extract scenario properties
        scenario_name = scenario_data.get('name')
        if not scenario_name:
            raise ValueError("Scenario name is required")

        agent_role = scenario_data.get('agent_role')
        if not agent_role:
            raise ValueError("Agent role is required")

        description = scenario_data.get('description', '')
        input_data = scenario_data.get('input_data', {})
        metadata = scenario_data.get('metadata', {})
        tags_data = scenario_data.get('tags', [])
        is_active = scenario_data.get('is_active', True)

        # Check if scenario already exists
        try:
            existing_scenario = await sync_to_async(BenchmarkScenario.objects.get)(
                name=scenario_name,
                agent_role=agent_role,
                is_latest=True
            )

            # Update the scenario
            existing_scenario.description = description
            existing_scenario.input_data = input_data
            existing_scenario.metadata = metadata
            existing_scenario.is_active = is_active
            await sync_to_async(existing_scenario.save)()

            # Update tags
            tag_objs = []
            for tag_name in tags_data:
                tag, _ = await sync_to_async(BenchmarkTag.objects.get_or_create)(name=tag_name)
                tag_objs.append(tag)
            if tag_objs:
                await sync_to_async(existing_scenario.tags.set)(tag_objs)

            self.stdout.write(f"Updated scenario: {scenario_name}")
            return existing_scenario

        except BenchmarkScenario.DoesNotExist:
            # Create a new scenario
            new_scenario = await sync_to_async(BenchmarkScenario.objects.create)(
                name=scenario_name,
                agent_role=agent_role,
                description=description,
                input_data=input_data,
                metadata=metadata,
                is_active=is_active,
                version=1,
                is_latest=True
            )

            # Add tags
            tag_objs = []
            for tag_name in tags_data:
                tag, _ = await sync_to_async(BenchmarkTag.objects.get_or_create)(name=tag_name)
                tag_objs.append(tag)
            if tag_objs:
                await sync_to_async(new_scenario.tags.set)(tag_objs)

            self.stdout.write(f"Created scenario: {scenario_name}")
            return new_scenario
