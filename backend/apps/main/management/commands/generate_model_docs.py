# core/management/commands/generate_docs.py
import os
import inspect
import importlib
import ast
import re
from django.core.management.base import BaseCommand
from django.apps import apps
from django.conf import settings
from django.utils import timezone
from django.db import models

class Command(BaseCommand):
    help = 'Generate markdown documentation from model docstrings'

    def add_arguments(self, parser):
        parser.add_argument('--output', type=str, default='docs/build/models.md',
                            help='Output file path')
        parser.add_argument('--template', type=str, default=None,
                            help='Template file path')
        parser.add_argument('--apps', type=str, nargs='+',
                            help='Specific apps to document (space-separated)')

    def handle(self, *args, **options):
        output_path = options['output']
        template_path = options['template']
        specific_apps = options['apps']
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Get all installed apps or specific apps if specified
        if specific_apps:
            django_apps = [app for app in apps.get_app_configs() 
                          if app.name in specific_apps or app.label in specific_apps]
        else:
            django_apps = apps.get_app_configs()
            
        # List to store all model documentation
        all_docs = []
        
        for app in django_apps:
            if app.models_module:
                app_path = app.module.__path__[0]
                models_file_path = os.path.join(app_path, 'models.py')
                
                # Skip if models.py doesn't exist (might be using models package)
                if not os.path.exists(models_file_path):
                    models_dir = os.path.join(app_path, 'models')
                    if os.path.isdir(models_dir):
                        # Process models package
                        app_doc = self.process_models_package(app, models_dir)
                    else:
                        # Skip this app if no models are found
                        continue
                else:
                    # Process models.py file
                    app_doc = self.process_models_file(app, models_file_path)
                
                all_docs.append(app_doc)
        
        # If a template is provided, use it
        if template_path and os.path.exists(template_path):
            with open(template_path, 'r') as f:
                template = f.read()
                
            # Replace a placeholder in the template with the documentation
            content = template.replace('{{content}}', '\n\n'.join(all_docs))
            
            # Handle date formatting if using Django template tags
            now = timezone.datetime.now()
            content = content.replace("{% now \"Y-m-d\" %}", now.strftime("%Y-%m-%d"))
            content = content.replace("{% now \"Y-m-d H:i\" %}", now.strftime("%Y-%m-%d %H:%M"))
        else:
            # Default template
            title = "Django Models Documentation"
            content = f"# {title}\n\n"
            content += "Generated documentation for Django models.\n\n"
            content += '\n\n'.join(all_docs)
            
            # Add generation timestamp
            content += f"\n\n---\nGenerated on: {timezone.datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        # Write the documentation to the output file
        with open(output_path, 'w') as f:
            f.write(content)
            
        self.stdout.write(self.style.SUCCESS(
            f'Documentation successfully generated at {output_path}'
        ))
    
    def process_models_file(self, app, file_path):
        """Process a models.py file and extract models in source order."""
        # App heading
        app_doc = f"# package 'apps.{str(app.verbose_name).lower() or app.label.title()}'\n\n"
        # Parse the Python file to get model definitions in order
        try:
            with open(file_path, 'r') as f:
                file_content = f.read()
            
            tree = ast.parse(file_content)
            
            # Get model class definitions in source order
            model_names = []
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # Check if this might be a model class
                    is_model = False
                    for base in node.bases:
                        if isinstance(base, ast.Name) and base.id == 'Model':
                            is_model = True
                        elif isinstance(base, ast.Attribute) and base.attr == 'Model':
                            is_model = True
                    
                    if is_model:
                        model_names.append(node.name)
            
            # Process each model in the order found in the source
            for model_name in model_names:
                # Get model class
                model = None
                for m in app.get_models():
                    if m.__name__ == model_name:
                        model = m
                        break
                
                if model:
                    model_doc = self.document_model(model, file_content)
                    app_doc += model_doc
        
        except Exception as e:
            app_doc += f"Error processing models in {file_path}: {str(e)}\n\n"
            
        return app_doc
        
    def process_models_package(self, app, models_dir):
        """Process a models package directory."""
        # App heading
        app_doc = f"# package '{app.verbose_name or app.label.title()}'\n\n"
        
        # Get all model files content
        model_files_content = {}
        for root, dirs, files in os.walk(models_dir):
            for file in files:
                if file.endswith('.py') and file != '__init__.py':
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r') as f:
                        model_files_content[file_path] = f.read()
        
        # Process init file first to understand import order
        init_path = os.path.join(models_dir, '__init__.py')
        if os.path.exists(init_path):
            try:
                with open(init_path, 'r') as f:
                    init_content = f.read()
                
                # Try to determine import order
                tree = ast.parse(init_content)
                imported_models = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.ImportFrom) and node.module:
                        module_parts = node.module.split('.')
                        if len(module_parts) > 0 and module_parts[-1] != '__init__':
                            # This is likely importing from a models submodule
                            for alias in node.names:
                                imported_models.append(alias.name)
            except Exception:
                # Fall back to alphabetical order of models if import parsing fails
                imported_models = [m.__name__ for m in sorted(app.get_models(), key=lambda m: m.__name__)]
        else:
            # Fall back to alphabetical order of models
            imported_models = [m.__name__ for m in sorted(app.get_models(), key=lambda m: m.__name__)]
        
        # Document models in the order determined by imports
        for model_name in imported_models:
            # Get model class
            model = None
            for m in app.get_models():
                if m.__name__ == model_name:
                    model = m
                    break
            
            if model:
                # Find the content where this model might be defined
                file_content = None
                for content in model_files_content.values():
                    if f"class {model_name}(" in content:
                        file_content = content
                        break
                
                model_doc = self.document_model(model, file_content)
                app_doc += model_doc
            
        # Check if any models were missed (not imported in __init__.py)
        documented_models = set(imported_models)
        all_models = set(m.__name__ for m in app.get_models())
        missed_models = all_models - documented_models
        
        if missed_models:
            for model_name in sorted(missed_models):
                # Get model class
                model = None
                for m in app.get_models():
                    if m.__name__ == model_name:
                        model = m
                        break
                
                if model:
                    # Find the content where this model might be defined
                    file_content = None
                    for content in model_files_content.values():
                        if f"class {model_name}(" in content:
                            file_content = content
                            break
                    
                    model_doc = self.document_model(model, file_content)
                    app_doc += model_doc
        
        return app_doc
    
    def get_example_value(self, field):
        """Generate an example value for a field based on its type."""
        field_type = field.get_internal_type()
        
        # Common example values for Django field types
        example_values = {
            'AutoField': '1',
            'BigAutoField': '1',
            'BigIntegerField': '9223372036854775807',
            'BinaryField': 'b\'binary data\'',
            'BooleanField': 'True',
            'CharField': '"example"',
            'DateField': '2025-03-12',
            'DateTimeField': '2025-03-12 14:30:45',
            'DecimalField': '123.45',
            'DurationField': '3 days, 0:00:00',
            'EmailField': '"<EMAIL>"',
            'FileField': '"uploads/example.txt"',
            'FilePathField': '"/path/to/file.txt"',
            'FloatField': '123.456',
            'ImageField': '"images/example.jpg"',
            'IntegerField': '42',
            'IPAddressField': '"***********"',
            'GenericIPAddressField': '"2001:db8::1"',
            'JSONField': '{"key": "value"}',
            'NullBooleanField': 'True',
            'PositiveIntegerField': '42',
            'PositiveSmallIntegerField': '42',
            'SlugField': '"example-slug"',
            'SmallIntegerField': '42',
            'TextField': '"Example text content"',
            'TimeField': '14:30:45',
            'URLField': '"https://example.com"',
            'UUIDField': '"550e8400-e29b-41d4-a716-************"',
            'ForeignKey': '1',
            'ManyToManyField': '[1, 2, 3]',
            'OneToOneField': '1',
        }
        
        # Check for choices
        if hasattr(field, 'choices') and field.choices:
            if field.choices:
                first_choice = field.choices[0][0]
                return f'"{first_choice}"' if isinstance(first_choice, str) else str(first_choice)
        
        # Return the example value based on the field type
        if field_type in example_values:
            return example_values[field_type]
        else:
            return '"example"'
    
    def extract_fields_from_docstring(self, docstring):
        """Extract field descriptions from Google-style docstring."""
        field_descriptions = {}
        if not docstring:
            return field_descriptions
        
        # Check for Attributes section in Google-style docstring
        attr_pattern = r"Attributes:\s*\n((?:\s+\w+\s*\([^)]*\)?\s*:[^:]*(?:\n\s+[^:]*)*)*)"
        attr_match = re.search(attr_pattern, docstring, re.DOTALL)
        
        if attr_match:
            attr_section = attr_match.group(1)
            # Extract each attribute
            attr_pattern = r"\s+(\w+)\s*(?:\([^)]*\))?\s*:(.*?)(?=\n\s+\w+\s*:|$)"
            for attr_match in re.finditer(attr_pattern, attr_section, re.DOTALL):
                field_name = attr_match.group(1).strip()
                description = attr_match.group(2).strip()
                # Clean up the description (remove extra spaces and newlines)
                description = re.sub(r'\s+', ' ', description).strip()
                field_descriptions[field_name] = description
        
        return field_descriptions
    
    def document_model(self, model, file_content=None):
        """Generate documentation for a single model."""
        # Model heading and docstring
        model_doc = f"## {model.__name__}\n\n"
        
        # Extract field descriptions from docstring if available
        if model.__doc__:
            full_docstring = model.__doc__
            # Only display the part before the "Attributes:" section
            if "Attributes:" in full_docstring:
                summary_doc = full_docstring.split("Attributes:", 1)[0].strip()
            else:
                summary_doc = full_docstring.strip()
            model_doc += f"{self.format_docstring(summary_doc)}\n\n"
            # Extract field descriptions and examples from the full docstring
            field_docs = self.extract_fields_and_examples_from_docstring(full_docstring)
        else:
            field_docs = {}
        
        # Add field documentation as a 4-column table
        model_doc += "### Attributes\n\n"
        
        # Table header
        model_doc += "| Name | Type | Description | Example |\n"
        model_doc += "|------|------|-------------|--------|\n"
        
        # Add table rows for each field
        for field in model._meta.fields:
            field_name = field.name
            field_type = field.get_internal_type()
            
            description = field.help_text if field.help_text else ""
            example = self.get_example_value(field)  # Default fallback
            
            # Check if we have docstring data for this field
            if field_name in field_docs:
                field_data = field_docs[field_name]
                
                # Use docstring description if available and not empty
                if field_data.get('description') and field_data['description'].strip():
                    description = field_data['description']
                    
                # Use docstring example if available
                if field_data.get('example') and field_data['example'].strip():
                    example = field_data['example']
            
            # Add table row for the field
            model_doc += f"| {field_name} | {field_type} | {description} | {example} |\n"
        
        model_doc += "\n"
        
        # Document methods (rest of code remains unchanged)
        methods = inspect.getmembers(model, predicate=inspect.isfunction)
        
        # Get model's base class methods to filter them out
        base_methods = set()
        for base in model.__bases__:
            if base is models.Model or issubclass(base, models.Model):
                base_methods.update(name for name, _ in inspect.getmembers(base, predicate=inspect.isfunction))
        
        # Filter out methods from models.Model and private methods
        methods_with_docs = [(name, method) for name, method in methods
                            if method.__doc__ and not name.startswith('_') and name not in base_methods]
        
        if methods_with_docs:
            model_doc += "### Methods\n\n"
            
            for name, method in methods_with_docs:
                model_doc += f"#### {name}\n\n"
                model_doc += f"{self.format_docstring(method.__doc__)}\n\n"
        
        return model_doc

    def extract_fields_and_examples_from_docstring(self, docstring):
        """Extract field descriptions and examples from docstring."""
        import re
        
        field_docs = {}
        
        # If no Attributes section, return empty dict
        if "Attributes:" not in docstring:
            return field_docs
        
        # Extract the Attributes section
        attrs_section = docstring.split("Attributes:", 1)[1].strip()
        
        # Parse each field entry in the attributes section
        # Match lines that start with a field name followed by a type in parentheses
        field_pattern = r'\s*(\w+)\s*\([^)]+\):\s*(.*?)(?=\n\s*\w+\s*\([^)]+\):|$)'
        for match in re.finditer(field_pattern, attrs_section, re.DOTALL):
            field_name = match.group(1)
            field_content = match.group(2).strip()
            
            # Check if the field content has an Example
            if "Example:" in field_content:
                parts = field_content.split("Example:", 1)
                description = parts[0].strip()
                example = parts[1].strip()
                
                # Special handling: if the example starts with a quote, preserve it exactly as is
                if example.startswith('"') or example.startswith("'"):
                    # Keep the quoted text exactly as-is
                    pass
            else:
                description = field_content
                example = ""
            
            field_docs[field_name] = {
                'description': description,
                'example': example
            }
        
        return field_docs
        
    def format_docstring(self, docstring):
        """Format a docstring for markdown output.
        
        Preserves Google-style formatting and indentation.
        
        Args:
            docstring: The raw docstring to format
            
        Returns:
            A string containing the formatted docstring ready for markdown
        """
        if not docstring:
            return ""
            
        # Remove leading indentation while preserving relative indentation
        lines = docstring.strip().split('\n')
        if len(lines) <= 1:
            return docstring.strip()
            
        # Find the minimum indentation (excluding empty lines)
        min_indent = min((len(line) - len(line.lstrip())) 
                         for line in lines[1:] if line.strip())
        
        # Remove the minimum indentation from all lines
        result = [lines[0].strip()]
        for line in lines[1:]:
            if line.strip():
                # Only remove the minimum indentation
                result.append(line[min_indent:])
            else:
                result.append('')
        
        # Improve Google-style section formatting
        formatted_lines = []
        in_section = False
        
        for line in result:
            # Check for Google-style section headers
            if line.strip() and line.strip().endswith(':') and not line.startswith('    '):
                in_section = True
                formatted_lines.append(f"**{line.strip()}**")
            elif in_section and line.strip() and not line.startswith('    '):
                # End of section
                in_section = False
                formatted_lines.append(line)
            else:
                # Regular line or section content
                formatted_lines.append(line)
        
        # Join lines and return
        return '\n'.join(formatted_lines)