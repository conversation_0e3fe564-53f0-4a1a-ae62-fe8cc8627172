"""
Management command to create sample contextual evaluation templates.
"""
from django.core.management.base import BaseCommand
from apps.main.models import EvaluationCriteriaTemplate


class Command(BaseCommand):
    help = 'Create sample contextual evaluation templates'

    def handle(self, *args, **options):
        # Sample contextual template for wheel generation
        wheel_template, created = EvaluationCriteriaTemplate.objects.get_or_create(
            name="Contextual Wheel Generation Evaluation",
            defaults={
                'description': 'Evaluation template that adapts criteria based on user trust level, mood, and environment',
                'workflow_type': 'wheel_generation',
                'category': 'contextual',
                'criteria': {
                    "Content": ["Relevance", "Personalization", "Feasibility"],
                    "Tone": ["Appropriate", "Supportive"],
                    "Structure": ["Clear", "Organized"]
                },
                'contextual_criteria': {
                    "trust_level": {
                        "0-39": {
                            "Tone": ["Simple", "Clear", "Reassuring", "Non-threatening"],
                            "Approach": ["Step-by-step", "Basic", "Gentle introduction"],
                            "Content": ["Safe options", "Low-risk activities", "Familiar contexts"]
                        },
                        "40-69": {
                            "Tone": ["Encouraging", "Supportive", "Motivating", "Collaborative"],
                            "Approach": ["Guided exploration", "Moderate challenge", "Building confidence"],
                            "Content": ["Balanced options", "Some stretch goals", "Growth-oriented"]
                        },
                        "70-100": {
                            "Tone": ["Collaborative", "Empowering", "Challenging", "Inspiring"],
                            "Approach": ["Advanced options", "Independent exploration", "Push boundaries"],
                            "Content": ["Ambitious goals", "Creative challenges", "Leadership opportunities"]
                        }
                    },
                    "mood": {
                        "valence": {
                            "-1.0-0.0": {
                                "Tone": ["Gentle", "Understanding", "Patient", "Compassionate"],
                                "Content": ["Mood-lifting activities", "Comfort-focused", "Low pressure"]
                            },
                            "0.0-1.0": {
                                "Tone": ["Enthusiastic", "Energetic", "Positive", "Celebratory"],
                                "Content": ["High-energy options", "Social activities", "Achievement-focused"]
                            }
                        },
                        "arousal": {
                            "-1.0-0.0": {
                                "Content": ["Calming activities", "Relaxation-focused", "Peaceful options"]
                            },
                            "0.0-1.0": {
                                "Content": ["Stimulating activities", "Dynamic options", "Exciting challenges"]
                            }
                        }
                    },
                    "environment": {
                        "stress_level": {
                            "0-30": {
                                "Approach": ["Detailed explanations", "Comprehensive options", "Thorough planning"],
                                "Content": ["Complex activities", "Multi-step processes", "Learning opportunities"]
                            },
                            "31-70": {
                                "Approach": ["Balanced detail", "Focused suggestions", "Moderate complexity"],
                                "Content": ["Manageable challenges", "Clear outcomes", "Structured activities"]
                            },
                            "71-100": {
                                "Approach": ["Concise", "Essential-only", "Quick decisions"],
                                "Content": ["Simple activities", "Immediate gratification", "Stress-relief focused"]
                            }
                        },
                        "time_pressure": {
                            "0-30": {
                                "Content": ["Long-term projects", "Detailed activities", "Skill development"]
                            },
                            "31-70": {
                                "Content": ["Medium-duration activities", "Balanced time commitment"]
                            },
                            "71-100": {
                                "Content": ["Quick activities", "Immediate options", "Time-efficient choices"]
                            }
                        }
                    }
                },
                'variable_ranges': {
                    "trust_level": {
                        "min": 0,
                        "max": 100,
                        "description": "User's trust level in the system and recommendations"
                    },
                    "mood": {
                        "valence": {
                            "min": -1.0,
                            "max": 1.0,
                            "description": "Emotional valence from negative to positive"
                        },
                        "arousal": {
                            "min": -1.0,
                            "max": 1.0,
                            "description": "Emotional arousal from calm to excited"
                        }
                    },
                    "environment": {
                        "stress_level": {
                            "min": 0,
                            "max": 100,
                            "description": "Current environmental stress level"
                        },
                        "time_pressure": {
                            "min": 0,
                            "max": 100,
                            "description": "Time pressure in current environment"
                        }
                    }
                },
                'is_active': True
            }
        )

        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created contextual template: {wheel_template.name}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Template already exists: {wheel_template.name}')
            )

        # Sample contextual template for discussion workflow
        discussion_template, created = EvaluationCriteriaTemplate.objects.get_or_create(
            name="Contextual Discussion Evaluation",
            defaults={
                'description': 'Evaluation template for discussion workflows with contextual adaptations',
                'workflow_type': 'discussion',
                'category': 'contextual',
                'criteria': {
                    "Engagement": ["Active listening", "Thoughtful responses", "Question quality"],
                    "Empathy": ["Understanding", "Validation", "Support"],
                    "Guidance": ["Helpful suggestions", "Appropriate direction", "Skill building"]
                },
                'contextual_criteria': {
                    "trust_level": {
                        "0-39": {
                            "Engagement": ["Gentle probing", "Safe space creation", "Non-judgmental"],
                            "Empathy": ["Extra validation", "Reassurance", "Patience"]
                        },
                        "40-69": {
                            "Engagement": ["Balanced questioning", "Encouraging participation"],
                            "Guidance": ["Collaborative problem-solving", "Skill development"]
                        },
                        "70-100": {
                            "Engagement": ["Deep exploration", "Challenge assumptions"],
                            "Guidance": ["Advanced techniques", "Independent thinking"]
                        }
                    },
                    "mood": {
                        "valence": {
                            "-1.0-0.0": {
                                "Empathy": ["Extra compassion", "Emotional support", "Gentle approach"],
                                "Guidance": ["Mood-sensitive suggestions", "Comfort-focused"]
                            },
                            "0.0-1.0": {
                                "Engagement": ["Enthusiastic participation", "Energy matching"],
                                "Guidance": ["Growth-oriented", "Achievement-focused"]
                            }
                        }
                    }
                },
                'variable_ranges': {
                    "trust_level": {"min": 0, "max": 100},
                    "mood": {
                        "valence": {"min": -1.0, "max": 1.0},
                        "arousal": {"min": -1.0, "max": 1.0}
                    },
                    "environment": {
                        "stress_level": {"min": 0, "max": 100},
                        "time_pressure": {"min": 0, "max": 100}
                    }
                },
                'is_active': True
            }
        )

        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created contextual template: {discussion_template.name}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Template already exists: {discussion_template.name}')
            )

        self.stdout.write(
            self.style.SUCCESS('Sample contextual evaluation templates creation completed!')
        )
