# backend/apps/coverage_dashboard/management/commands/run_tests.py
import os
import json
import subprocess
import datetime
from django.core.management.base import BaseCommand
from pathlib import Path

class Command(BaseCommand):
    help = 'Run tests and update coverage data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose', '-v',
            action='store_true',
            help='Run tests with verbose output',
        )
        parser.add_argument(
            'test_path',
            nargs='?',
            default=None,
            help='Specific test path to run (e.g., apps/main/tests/test_agents/)',
        )

    def handle(self, *args, **options):
        is_ci = options.get('ci', False)
        verbose = options.get('verbose', False)
        test_path = options.get('test_path')

        self.stdout.write('Running tests with coverage...')
        
        # Set environment variables for the test run
        env = os.environ.copy()
        env.update({
            'DJANGO_SETTINGS_MODULE': 'config.settings.test',
            'TESTING': 'true',
            'PYTEST_DJANGO_AUTODISCOVER': '0',
            'DJANGO_SKIP_CHECKS': '1',
            'DJANGO_ALLOW_ASYNC_UNSAFE': 'true',
        })
        
        # Construct pytest command
        cmd = ['python', '-m', 'pytest']
        
        # Add verbosity flags if requested
        if verbose:
            cmd.append('-v')
            
        # Add specific test path if provided
        if test_path:
            cmd.append(test_path)
            
        # Run the tests
        self.stdout.write(' '.join(cmd))
        
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True
        )
        
        self.stdout.write(result.stdout)
        if result.stderr:
            self.stdout.write(self.style.WARNING(result.stderr))
            
        # Make sure coverage.json exists and add timestamp
        base_dir = Path(__file__).resolve().parent.parent.parent.parent
        coverage_path = os.path.join(base_dir, 'coverage.json')
        
        if os.path.exists(coverage_path):
            try:
                # Initialize coverage_data and run_number
                coverage_data = {}
                run_number = 0
                
                # Try to load existing data to get the last run number
                if os.path.exists(coverage_path):
                    with open(coverage_path, 'r') as f:
                        try:
                            coverage_data = json.load(f)
                            run_number = coverage_data.get('run_number', 0)
                        except json.JSONDecodeError:
                            self.stdout.write(self.style.WARNING(f'Could not decode existing {coverage_path}. Starting run number from 0.'))
                
                # Increment run number
                run_number += 1
                
                # Update coverage data with new run number and timestamp
                # Note: The actual coverage metrics ('agents', 'modules', 'test_types') 
                # are expected to be generated by pytest-cov via pyproject.toml settings.
                # This script primarily adds metadata. If pytest-cov didn't generate the file,
                # coverage_data might only contain the metadata below.
                coverage_data['run_number'] = run_number
                coverage_data['last_updated'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # Write the updated data back
                with open(coverage_path, 'w') as f:
                    json.dump(coverage_data, f, indent=2)
                    
                # Print summary
                self.stdout.write(self.style.SUCCESS(f'Tests completed (Run #{run_number}) and coverage.json updated'))
                self.stdout.write(f"Timestamp: {coverage_data['last_updated']}")
                # Display other stats if available
                if 'test_types' in coverage_data:
                    self.stdout.write(f"Test Types: {coverage_data['test_types']}")
                if 'modules' in coverage_data:
                    self.stdout.write(f"Modules Covered: {len(coverage_data['modules'])}")
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error processing or updating coverage.json: {e}'))
        # No 'else' needed here as we attempt to create/update the file regardless
            
        return result.returncode == 0
