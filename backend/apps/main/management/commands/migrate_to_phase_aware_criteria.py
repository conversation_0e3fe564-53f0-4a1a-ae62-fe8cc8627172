"""
Management command to migrate existing benchmark scenarios to phase-aware criteria.

This command updates existing benchmark scenarios to use the new phase-aware criteria structure.
"""

import logging
from django.core.management.base import BaseCommand
from django.db import transaction

from apps.main.models import BenchmarkScenario
from apps.main.services.evaluation_criteria_migration import migrate_criteria_to_phase_aware

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Migrate existing benchmark scenarios to use phase-aware criteria"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be migrated without making changes",
        )
        parser.add_argument(
            "--scenario-id",
            type=str,
            help="Migrate a specific scenario by ID",
        )
        parser.add_argument(
            "--agent-role",
            type=str,
            help="Migrate scenarios for a specific agent role",
        )

    def handle(self, *args, **options):
        dry_run = options.get("dry_run", False)
        scenario_id = options.get("scenario_id")
        agent_role = options.get("agent_role")

        # Get scenarios to migrate
        scenarios = BenchmarkScenario.objects.filter(is_active=True)
        
        if scenario_id:
            scenarios = scenarios.filter(id=scenario_id)
            if not scenarios.exists():
                self.stdout.write(self.style.ERROR(f"No scenario found with ID {scenario_id}"))
                return
                
        if agent_role:
            scenarios = scenarios.filter(agent_role=agent_role)
            if not scenarios.exists():
                self.stdout.write(self.style.ERROR(f"No scenarios found for agent role {agent_role}"))
                return
                
        # Count scenarios
        total_scenarios = scenarios.count()
        self.stdout.write(self.style.SUCCESS(f"Found {total_scenarios} scenarios to migrate"))
        
        # Track statistics
        migrated_count = 0
        already_migrated_count = 0
        no_criteria_count = 0
        error_count = 0
        
        # Process each scenario
        for scenario in scenarios:
            try:
                # Check if already using phase-aware criteria
                if "evaluation_criteria_by_phase" in scenario.metadata:
                    self.stdout.write(f"Scenario '{scenario.name}' already uses phase-aware criteria")
                    already_migrated_count += 1
                    continue
                    
                # Check if scenario has criteria
                if "expected_quality_criteria" not in scenario.metadata:
                    self.stdout.write(f"Scenario '{scenario.name}' has no criteria to migrate")
                    no_criteria_count += 1
                    continue
                    
                # Migrate criteria
                original_criteria = scenario.metadata.get("expected_quality_criteria", {})
                phase_aware_criteria = migrate_criteria_to_phase_aware({"criteria": original_criteria})
                
                # Show what would be migrated
                self.stdout.write(f"Scenario '{scenario.name}':")
                self.stdout.write(f"  Original criteria: {original_criteria}")
                self.stdout.write(f"  Phase-aware criteria: {phase_aware_criteria}")
                
                # Update scenario if not dry run
                if not dry_run:
                    with transaction.atomic():
                        # Keep original criteria for backward compatibility
                        scenario.metadata["expected_quality_criteria"] = original_criteria
                        
                        # Add phase-aware criteria
                        scenario.metadata["evaluation_criteria_by_phase"] = phase_aware_criteria["evaluation_criteria_by_phase"]
                        
                        # Save scenario
                        scenario.save()
                        
                    self.stdout.write(self.style.SUCCESS(f"  Migrated successfully"))
                    migrated_count += 1
                else:
                    self.stdout.write(self.style.WARNING(f"  Would migrate (dry run)"))
                    migrated_count += 1
                    
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error migrating scenario '{scenario.name}': {e}"))
                error_count += 1
                
        # Show summary
        self.stdout.write("\nMigration Summary:")
        self.stdout.write(f"  Total scenarios: {total_scenarios}")
        self.stdout.write(f"  Already using phase-aware criteria: {already_migrated_count}")
        self.stdout.write(f"  No criteria to migrate: {no_criteria_count}")
        self.stdout.write(f"  Successfully migrated: {migrated_count}")
        self.stdout.write(f"  Errors: {error_count}")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("\nThis was a dry run. No changes were made."))
            self.stdout.write(self.style.WARNING("Run without --dry-run to apply the changes."))
        else:
            self.stdout.write(self.style.SUCCESS("\nMigration completed successfully."))
