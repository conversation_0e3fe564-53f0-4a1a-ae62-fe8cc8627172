"""
Django management command for setting up the benchmark directory structure.
"""

import os
import logging
from django.core.management.base import BaseCommand

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Sets up the benchmark directory structure'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of directories even if they already exist'
        )

    def handle(self, *args, **options):
        base_dir = 'backend/testing/benchmark_data'
        force = options.get('force', False)
        
        # Define directory structure
        directories = [
            'agents/mentor/wheel_generation',
            'agents/mentor/discussion',
            'agents/mentor/feedback',
            'agents/orchestrator',
            'agents/strategy',
            'workflows/wheel_generation',
            'workflows/activity_feedback',
            'templates/evaluation_criteria'
        ]

        # Create directories
        created_count = 0
        for dir_path in directories:
            full_path = os.path.join(base_dir, dir_path)
            if force and os.path.exists(full_path):
                self.stdout.write(f"Directory already exists: {full_path} (--force ignored for safety)")
            
            if not os.path.exists(full_path):
                os.makedirs(full_path, exist_ok=True)
                created_count += 1
                self.stdout.write(f"Created directory: {full_path}")
            else:
                self.stdout.write(f"Directory already exists: {full_path}")
            
        self.stdout.write(self.style.SUCCESS(f'Directory structure created: {created_count} new directories'))
