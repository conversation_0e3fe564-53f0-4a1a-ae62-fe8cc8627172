"""
Management command to seed benchmark scenarios.

This command creates sample benchmark scenarios for testing and development.
"""

import json
import uuid
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.main.models import BenchmarkScenario, BenchmarkTag


class Command(BaseCommand):
    """Command to seed benchmark scenarios."""
    
    help = "Creates sample benchmark scenarios for testing and development"
    
    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation of scenarios even if they already exist'
        )
    
    def handle(self, *args, **options):
        """Execute the command."""
        force = options.get('force', False)
        
        # Create benchmark scenarios
        self.create_wheel_workflow_scenario(force)
        self.create_mentor_workflow_scenario(force)
        
        self.stdout.write(self.style.SUCCESS('Successfully seeded benchmark scenarios'))
    
    @transaction.atomic
    def create_wheel_workflow_scenario(self, force=False):
        """Create a wheel workflow benchmark scenario."""
        # Check if scenario already exists
        scenario_name = "wheel_workflow_benchmark"
        if BenchmarkScenario.objects.filter(name=scenario_name).exists() and not force:
            self.stdout.write(f"Scenario '{scenario_name}' already exists, skipping")
            return
        
        # Create or update scenario
        scenario, created = BenchmarkScenario.objects.update_or_create(
            name=scenario_name,
            defaults={
                'version': 1,
                'description': "Benchmark scenario for wheel generation workflow",
                'is_active': True,
                'metadata': {
                    'workflow_type': 'wheel_generation',
                    'mock_tool_responses': {
                        'get_user_profile': {
                            'response': json.dumps({
                                'id': str(uuid.uuid4()),
                                'name': 'Test User',
                                'preferences': {
                                    'activity_types': ['outdoor', 'creative', 'learning']
                                },
                                'traits': {
                                    'openness': 0.8,
                                    'conscientiousness': 0.7,
                                    'extraversion': 0.6,
                                    'agreeableness': 0.9,
                                    'neuroticism': 0.3
                                },
                                'limitations': {
                                    'physical': ['none'],
                                    'time': ['weekdays_evening_only']
                                }
                            }),
                            'assertions': [
                                {
                                    'type': 'param_check',
                                    'param': 'user_id',
                                    'should_exist': True
                                }
                            ],
                            'delay': 0.1
                        },
                        'get_user_activities': [
                            {
                                'condition': "get(tool_input, 'user_id') is not None",
                                'response': json.dumps({
                                    'activities': [
                                        {
                                            'id': str(uuid.uuid4()),
                                            'name': 'Mindful Walking',
                                            'category': 'wellness',
                                            'description': 'A mindful walking exercise in nature'
                                        },
                                        {
                                            'id': str(uuid.uuid4()),
                                            'name': 'Journal Writing',
                                            'category': 'creative',
                                            'description': 'Reflective journal writing exercise'
                                        }
                                    ]
                                }),
                                'assertions': [
                                    {
                                        'type': 'call_count',
                                        'expected': 1
                                    }
                                ]
                            },
                            {
                                'condition': "True",
                                'response': '{"error": "Missing user_id parameter"}',
                                'assertions': [
                                    {
                                        'type': 'should_not_be_called',
                                        'message': "Tool called without user_id parameter"
                                    }
                                ]
                            }
                        ],
                        'store_wheel': {
                            'response': json.dumps({
                                'id': str(uuid.uuid4()),
                                'status': 'success'
                            }),
                            'assertions': [
                                {
                                    'type': 'param_check',
                                    'param': 'wheel',
                                    'should_exist': True
                                },
                                {
                                    'type': 'param_check',
                                    'param': 'user_id',
                                    'should_exist': True
                                }
                            ]
                        }
                    },
                    'mock_tool_validation': {
                        'validate_schemas': True,
                        'validate_params': True,
                        'strict_mode': False
                    },
                    'warmup_runs': 2,
                    'benchmark_runs': 5
                }
            }
        )
        
        # Add tags
        workflow_tag, _ = BenchmarkTag.objects.get_or_create(name="workflow")
        wheel_tag, _ = BenchmarkTag.objects.get_or_create(name="wheel_generation")
        scenario.tags.add(workflow_tag, wheel_tag)
        
        action = "Created" if created else "Updated"
        self.stdout.write(f"{action} scenario '{scenario_name}'")
    
    @transaction.atomic
    def create_mentor_workflow_scenario(self, force=False):
        """Create a mentor workflow benchmark scenario."""
        # Check if scenario already exists
        scenario_name = "mentor_workflow_benchmark"
        if BenchmarkScenario.objects.filter(name=scenario_name).exists() and not force:
            self.stdout.write(f"Scenario '{scenario_name}' already exists, skipping")
            return
        
        # Create or update scenario
        scenario, created = BenchmarkScenario.objects.update_or_create(
            name=scenario_name,
            defaults={
                'version': 1,
                'description': "Benchmark scenario for mentor conversation workflow",
                'is_active': True,
                'metadata': {
                    'workflow_type': 'mentor_conversation',
                    'mock_tool_responses': {
                        'get_user_profile': json.dumps({
                            'id': str(uuid.uuid4()),
                            'name': 'Test User',
                            'preferences': {
                                'communication_style': 'direct',
                                'interests': ['technology', 'fitness', 'reading']
                            },
                            'traits': {
                                'openness': 0.7,
                                'conscientiousness': 0.8,
                                'extraversion': 0.5,
                                'agreeableness': 0.6,
                                'neuroticism': 0.4
                            }
                        }),
                        'get_conversation_history': json.dumps({
                            'messages': [
                                {
                                    'role': 'user',
                                    'content': "I'm feeling stuck with my current routine."
                                },
                                {
                                    'role': 'assistant',
                                    'content': "I understand feeling stuck can be frustrating. What aspects of your routine feel most challenging right now?"
                                },
                                {
                                    'role': 'user',
                                    'content': "I don't have enough variety in my activities."
                                }
                            ]
                        }),
                        'store_conversation_memory': json.dumps({
                            'id': str(uuid.uuid4()),
                            'status': 'success'
                        })
                    },
                    'warmup_runs': 1,
                    'benchmark_runs': 3
                }
            }
        )
        
        # Add tags
        workflow_tag, _ = BenchmarkTag.objects.get_or_create(name="workflow")
        mentor_tag, _ = BenchmarkTag.objects.get_or_create(name="mentor")
        conversation_tag, _ = BenchmarkTag.objects.get_or_create(name="conversation")
        scenario.tags.add(workflow_tag, mentor_tag, conversation_tag)
        
        action = "Created" if created else "Updated"
        self.stdout.write(f"{action} scenario '{scenario_name}'")
