# ACTIVE_FILE - 29-05-2025
import asyncio
import json
import logging
import os
import traceback # For detailed error reporting
from asgiref.sync import sync_to_async # For converting sync functions to async
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db.models import Q # For complex lookups

# Corrected import paths based on project structure
from apps.main.models import BenchmarkScenario, BenchmarkRun, BenchmarkTag, GenericAgent # Added BenchmarkTag and GenericAgent
from apps.main.services.benchmark_manager import AgentBenchmarker
from apps.main.utils.benchmark_reporting import generate_html_report # Import the report generator

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Run agent benchmarks with tracking and storage'

    def add_arguments(self, parser):
        parser.add_argument(
            '--scenario-id',
            type=int,
            help='ID of a specific scenario to run'
        )
        parser.add_argument(
            '--scenario-name',
            type=str,
            help='Run scenarios whose names contain this string (case-insensitive)'
        )
        parser.add_argument(
            '--agent-role',
            type=str,
            help='Run scenarios only for this specific agent role'
        )
        parser.add_argument(
            '--tags',
            type=str,
            help='Comma-separated list of tags to filter scenarios by (e.g., "feature-x,regression")'
        )
        parser.add_argument(
            '--runs',
            type=int,
            default=3,
            help='Number of benchmark runs per scenario (default: 3)'
        )
        parser.add_argument(
            '--warmup',
            type=int,
            default=1,
            help='Number of warmup runs before actual benchmarking (default: 1)'
        )
        parser.add_argument(
            '--semantic',
            action='store_true',
            help='Enable semantic evaluation (if supported by scenario/manager)'
        )
        parser.add_argument(
            '--output',
            type=str,
            help='Optional: Output JSON file path to save summary results'
        )
        parser.add_argument(
            '--agent-version',
            type=str,
            default=os.environ.get('AGENT_VERSION', 'local-dev'),
            help='Identifier for the agent version being benchmarked (default: env var AGENT_VERSION or "local-dev")'
        )
        # Removed --use-real-llm and --llm-model flags
        parser.add_argument(
            '--agent-llm-config-name',
            type=str,
            default='gpt-4-turbo-standard', # Default to a standard config
            help='Name of the LLMConfig entry to use for the agent being benchmarked (default: gpt-4-turbo-standard)'
        )
        parser.add_argument(
            '--use-real-llm',
            action='store_true',
            help='Use a real LLM instead of mock. Requires --llm-model to be set to a non-mock value.'
        )
        parser.add_argument(
            '--generate-report',
            type=str,
            help='Optional: Generate an HTML report and save it to this file path'
        )


    def handle(self, *args, **options):
        # LLM config name is now directly specified via --agent-llm-config-name
        agent_llm_config_name = options['agent_llm_config_name']

        # Validate --use-real-llm and --llm-model combination
        use_real_llm = options.get('use_real_llm', False)
        llm_model = options.get('llm_model', 'mock')
        if use_real_llm and llm_model == 'mock':
            raise CommandError("If --use-real-llm is set, you must also provide a specific --llm-model other than 'mock'.")

        # Set up event loop and run async logic
        try:
            # Pass the config name directly to the async runner
            asyncio.run(self.run_benchmarks(agent_llm_config_name, **options))
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"An unexpected error occurred: {e}"))
            self.stderr.write(traceback.format_exc())
            raise CommandError("Benchmark execution failed.")


    async def run_benchmarks(self, agent_llm_config_name, **options): # Accept config name
        benchmark_manager = AgentBenchmarker()

        # Determine which scenarios to run based on filters
        # Always start by filtering for the latest active versions
        scenario_filters = Q(is_active=True) & Q(is_latest=True)
        if options.get('scenario_id'):
            # If a specific ID is given, we rely on BenchmarkManager._get_scenario_sync
            # to fetch the *actual* latest version even if an old ID is provided.
            # So, we filter by ID here, but the manager handles the version logic.
            scenario_filters &= Q(id=options['scenario_id'])
        if options.get('scenario_name'):
            scenario_filters &= Q(name__icontains=options['scenario_name'])
        if options.get('agent_role'):
            scenario_filters &= Q(agent_role=options['agent_role'])

        # Handle tag filtering
        tag_names = []
        if options.get('tags'):
            tag_names = [tag.strip() for tag in options['tags'].split(',') if tag.strip()]
            if tag_names:
                # Filter scenarios that have ALL the specified tags
                # scenario_filters &= Q(tags__name__in=tag_names) # This would be OR
                for tag_name in tag_names:
                    scenario_filters &= Q(tags__name=tag_name)


        # Fetch scenarios asynchronously using sync_to_async
        @sync_to_async
        def _get_scenarios():
            queryset = BenchmarkScenario.objects.filter(scenario_filters)
            if tag_names:
                 # Ensure distinct results when filtering by multiple tags
                 queryset = queryset.distinct()
            return list(queryset)

        scenarios = await _get_scenarios()

        if not scenarios:
            self.stdout.write(self.style.WARNING('No active scenarios found matching the specified filters.'))
            # Optionally list available scenarios here if needed
            return

        self.stdout.write(f"Found {len(scenarios)} scenario(s) to benchmark.")

        # Run benchmarks for each scenario
        results_summary = [] # For JSON output
        full_results_for_report = [] # For HTML report
        has_errors = False
        for scenario in scenarios:
            self.stdout.write(f"\n--- Running Benchmark: '{scenario.name}' (ID: {scenario.id}, Agent: {scenario.agent_role}) ---")

            try:
                # Prepare parameters for this specific run
                params = {
                    'runs': options['runs'],
                    'warmup_runs': options['warmup'],
                    'semantic_evaluation': options['semantic'],
                    'agent_version': options['agent_version'],
                    'agent_llm_config_name': agent_llm_config_name, # Pass the config name
                    'use_real_llm': options.get('use_real_llm', False), # Pass use_real_llm flag
                    # Add any other scenario-specific params if needed from scenario.metadata perhaps
                }

                # Run the benchmark via the manager, passing user_profile_id as None
                benchmark_run = await benchmark_manager.run_benchmark(
                    scenario_id=scenario.id,
                    params=params,
                    user_profile_id=None # No specific user context in management command
                )

                # Add summary to results list (for JSON output)
                run_summary = {
                    'run_id': str(benchmark_run.id),
                    'scenario_id': scenario.id,
                    'scenario_name': scenario.name,
                    'agent_role': scenario.agent_role,
                    'agent_version': benchmark_run.agent_version,
                    'llm_model': benchmark_run.llm_model,
                    'mean_duration_ms': benchmark_run.mean_duration,
                    'success_rate': benchmark_run.success_rate,
                    'tool_calls': benchmark_run.tool_calls,
                    'llm_calls': benchmark_run.llm_calls,
                    'semantic_score': benchmark_run.semantic_score,
                    # Add run_id explicitly as string for output consistency
                    'run_id': str(benchmark_run.id)
                }
                results_summary.append(run_summary)

                # Collect full data for HTML report (convert model instances to dicts)
                # Need to fetch related objects asynchronously if not already loaded
                @sync_to_async
                def _get_related_data(run_instance):
                    # Check if run_instance is a MagicMock (for testing)
                    from unittest.mock import MagicMock
                    if isinstance(run_instance, MagicMock):
                        # Handle mock objects directly without database access
                        scenario_data = {
                            'id': getattr(run_instance.scenario, 'id', 0),
                            'name': getattr(run_instance.scenario, 'name', 'Mock Scenario'),
                            'version': getattr(run_instance.scenario, 'version', 1),
                        }

                        agent_def_data = {
                            'id': getattr(run_instance.agent_definition, 'id', 0) if hasattr(run_instance, 'agent_definition') else 0,
                            'role': getattr(run_instance.agent_definition, 'role', 'mock_role') if hasattr(run_instance, 'agent_definition') else 'mock_role',
                        }

                        # Create a dictionary with expected fields from the mock
                        run_data = {
                            'id': getattr(run_instance, 'id', 0),
                            'execution_date': timezone.now().isoformat(),
                            'mean_duration': getattr(run_instance, 'mean_duration', 0),
                            'success_rate': getattr(run_instance, 'success_rate', 0),
                            'tool_calls': getattr(run_instance, 'tool_calls', 0),
                            'llm_calls': getattr(run_instance, 'llm_calls', 0),
                            'semantic_score': getattr(run_instance, 'semantic_score', None),
                            'agent_version': getattr(run_instance, 'agent_version', 'mock'),
                            'llm_model': getattr(run_instance, 'llm_model', 'mock'),
                            'evaluator_llm_model': getattr(run_instance, 'evaluator_llm_model', None),
                        }
                    else:
                        # Real database access for actual BenchmarkRun instances
                        try:
                            # Eager load related fields needed for the report
                            run_instance = BenchmarkRun.objects.select_related(
                                'scenario', 'agent_definition'
                            ).get(id=run_instance.id)

                            scenario_data = {
                                'id': run_instance.scenario.id,
                                'name': run_instance.scenario.name,
                                'version': run_instance.scenario.version,
                                # Add other scenario fields if needed
                            }

                            agent_def_data = {
                                'id': run_instance.agent_definition.id,
                                'role': run_instance.agent_definition.role,
                                # Add other agent definition fields if needed
                            }

                            # Convert the main run instance to a dictionary
                            # Note: JSONFields are already dicts/lists
                            run_data = {
                                field.name: getattr(run_instance, field.name)
                                for field in run_instance._meta.fields
                                if field.name not in ['scenario', 'agent_definition'] # Avoid recursion
                            }

                            # Convert datetime to string for consistency if needed by report func
                            run_data['execution_date'] = run_instance.execution_date.isoformat() if run_instance.execution_date else None
                        except Exception as e:
                            logger.warning(f"Error fetching related data for benchmark run {run_instance.id}: {e}")
                            # Create minimal data structure to avoid breaking the report
                            run_data = {
                                'id': getattr(run_instance, 'id', 0),
                                'execution_date': timezone.now().isoformat(),
                                'error': f"Failed to load complete data: {str(e)}"
                            }
                            scenario_data = {'id': 0, 'name': 'Unknown', 'version': 0}
                            agent_def_data = {'id': 0, 'role': 'unknown'}

                    # Add the related data back
                    run_data['scenario'] = scenario_data
                    run_data['agent_definition'] = agent_def_data

                    return run_data

                full_run_data = await _get_related_data(benchmark_run)
                full_results_for_report.append(full_run_data)


                # Display results for this run
                self.stdout.write(self.style.SUCCESS(f"Benchmark completed successfully for '{scenario.name}'"))
                self.stdout.write(f"  Run ID: {benchmark_run.id}")
                self.stdout.write(f"  Mean Duration: {benchmark_run.mean_duration:.2f} ms")
                self.stdout.write(f"  Success Rate: {benchmark_run.success_rate:.1%}")
                self.stdout.write(f"  Tool Calls: {benchmark_run.tool_calls}")
                self.stdout.write(f"  LLM Calls: {benchmark_run.llm_calls}")
                if benchmark_run.semantic_score is not None:
                    self.stdout.write(f"  Semantic Score: {benchmark_run.semantic_score:.2f}")

            except (ValueError, ImportError, RuntimeError, Exception) as e:
                has_errors = True
                self.stderr.write(self.style.ERROR(f"Error running benchmark for '{scenario.name}': {e}"))
                # Log the full traceback for debugging purposes
                logger.error(f"Benchmark failed for scenario ID {scenario.id}", exc_info=True)
                # Add error entry to summary
                results_summary.append({
                    'scenario_id': scenario.id,
                    'scenario_name': scenario.name,
                    'agent_role': scenario.agent_role,
                    'status': 'failed',
                    'error': str(e)
                })

        # Save results summary to output file if specified
        output_file = options.get('output')
        if output_file and results_summary:
            try:
                # Ensure directory exists if path includes directories
                output_dir = os.path.dirname(output_file)
                if output_dir:
                    os.makedirs(output_dir, exist_ok=True)

                with open(output_file, 'w') as f:
                    json.dump({
                        'benchmark_run_timestamp': timezone.now().isoformat(),
                        'filters_used': {k: v for k, v in options.items() if v is not None and k not in ['verbosity', 'settings', 'pythonpath', 'traceback', 'no_color', 'force_color', 'skip_checks', 'generate_report']}, # Exclude report path
                        'results': results_summary
                    }, f, indent=2)
                self.stdout.write(self.style.SUCCESS(f"\nBenchmark summary saved to {output_file}"))
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"\nError saving results summary to {output_file}: {e}"))

        # Generate HTML report if requested
        report_file = options.get('generate_report')
        if report_file and full_results_for_report:
            try:
                # Ensure directory exists
                report_dir = os.path.dirname(report_file)
                if report_dir:
                    os.makedirs(report_dir, exist_ok=True)
                # Call the report generation function (sync)
                generate_html_report(full_results_for_report, report_file)
                self.stdout.write(self.style.SUCCESS(f"HTML report generated at {report_file}"))
            except Exception as e:
                 self.stderr.write(self.style.ERROR(f"\nError generating HTML report to {report_file}: {e}"))
                 logger.error(f"Failed to generate HTML report", exc_info=True)


        if has_errors:
             self.stderr.write(self.style.WARNING("\nOne or more benchmarks failed. Check logs for details."))
        else:
             self.stdout.write(self.style.SUCCESS("\nAll specified benchmarks completed."))
