"""
Management command to fix static files issues.

This command:
1. Ensures all static directories exist
2. <PERSON>reates symlinks if needed
3. Handles the frontend/dist directory issue
"""
import os
import logging
from pathlib import Path
from django.core.management.base import BaseCommand
from django.conf import settings
from django.core.management import call_command

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fixes static files issues and ensures all directories exist'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting static files fix...'))
        
        # Get the base directory
        base_dir = settings.BASE_DIR
        
        # Ensure static root exists
        static_root = Path(settings.STATIC_ROOT)
        os.makedirs(static_root, exist_ok=True)
        self.stdout.write(f"Ensured static root exists at: {static_root}")
        
        # Ensure static directories exist
        for static_dir in settings.STATICFILES_DIRS:
            if isinstance(static_dir, (str, Path)):
                path = Path(static_dir)
                os.makedirs(path, exist_ok=True)
                self.stdout.write(f"Ensured static directory exists at: {path}")
            elif isinstance(static_dir, (list, tuple)) and len(static_dir) == 2:
                prefix, path = static_dir
                os.makedirs(Path(path), exist_ok=True)
                self.stdout.write(f"Ensured prefixed static directory exists at: {path} with prefix {prefix}")
        
        # Check for frontend/dist in STATICFILES_DIRS
        frontend_dist_path = os.path.join(base_dir, 'frontend', 'dist')
        frontend_dist_in_settings = False
        
        # Check if frontend/dist is in STATICFILES_DIRS
        for static_dir in settings.STATICFILES_DIRS:
            if isinstance(static_dir, (str, Path)) and str(static_dir) == frontend_dist_path:
                frontend_dist_in_settings = True
                break
            elif isinstance(static_dir, (list, tuple)) and len(static_dir) == 2:
                prefix, path = static_dir
                if str(path) == frontend_dist_path:
                    frontend_dist_in_settings = True
                    break
        
        # Handle frontend/dist directory
        if frontend_dist_in_settings:
            self.stdout.write(self.style.WARNING(f"Found frontend/dist in STATICFILES_DIRS but directory doesn't exist"))
            self.stdout.write("Creating empty directory to prevent errors")
            os.makedirs(frontend_dist_path, exist_ok=True)
            
            # Create a README file to explain the purpose of this directory
            readme_path = os.path.join(frontend_dist_path, 'README.md')
            with open(readme_path, 'w') as f:
                f.write("""# Frontend Dist Directory

This directory was automatically created by the `fix_static_files` management command.

It exists to prevent Django from raising errors when looking for static files in the
frontend/dist directory, which is referenced in STATICFILES_DIRS but doesn't actually
exist in the Docker container.

For proper frontend integration, you should:
1. Build the frontend assets
2. Copy them to this directory or configure Django to look elsewhere
""")
            
            # Create an empty CSS and JS file to ensure the directory is not empty
            os.makedirs(os.path.join(frontend_dist_path, 'css'), exist_ok=True)
            os.makedirs(os.path.join(frontend_dist_path, 'js'), exist_ok=True)
            
            with open(os.path.join(frontend_dist_path, 'css', 'placeholder.css'), 'w') as f:
                f.write("/* Placeholder CSS file */\n")
            
            with open(os.path.join(frontend_dist_path, 'js', 'placeholder.js'), 'w') as f:
                f.write("// Placeholder JavaScript file\n")
        
        # Run collectstatic
        self.stdout.write(self.style.SUCCESS('Running collectstatic...'))
        call_command('collectstatic', interactive=False, verbosity=2)
        
        self.stdout.write(self.style.SUCCESS('Static files fix completed successfully!'))
