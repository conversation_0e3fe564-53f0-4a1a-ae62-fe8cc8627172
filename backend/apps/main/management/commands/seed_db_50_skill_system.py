import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.user.models import (
    SkillAttribute, AttributeTraitInfluence, SkillDefinition,
    SkillAttributeComposition, SkillDomainApplication, TraitType
)
from apps.activity.models import GenericDomain
from apps.user.models import GenericTrait
from apps.main.models import AppliedSeedingCommand # Import the tracking model


class Command(BaseCommand):
    help = 'Seeds the dynamic skill system (attributes, influences, definitions, applications). Skips if already applied by run_seeders.'
    # Re-add command name definition
    COMMAND_NAME = os.path.splitext(os.path.basename(__file__))[0]

    def handle(self, *args, **kwargs):
        # Re-add the initial check for the command itself
        if AppliedSeedingCommand.objects.filter(command_name=self.COMMAND_NAME).exists():
            self.stdout.write(self.style.WARNING(f"Command '{self.COMMAND_NAME}' has already been applied by run_seeders. Skipping."))
            return

        self.stdout.write(self.style.SUCCESS(f"Running command '{self.COMMAND_NAME}' logic (will update/create individual items)..."))

        try:
            # 2. Execute the seeding logic within a transaction
            # Note: Individual methods already use @transaction.atomic,
            # but wrapping the whole handle ensures the AppliedSeedingCommand
            # is only created if ALL steps succeed.
            with transaction.atomic():
                # Seed the fundamental attributes
                self.seed_fundamental_attributes()

                # Seed the trait influences
                self.seed_trait_influences()

                # Seed skill definitions
                self.seed_skill_definitions()

                # Seed domain applications
                self.seed_domain_applications()

                # Ensure AppliedSeedingCommand.objects.create(...) remains removed from here.
                # run_seeders.py handles the recording upon successful completion of this command.
                self.stdout.write(self.style.SUCCESS(f"Finished command '{self.COMMAND_NAME}' logic! Skill system seeded."))

        except Exception as e:
            # Catch any exception during the transaction
            self.stderr.write(self.style.ERROR(f"Command '{self.COMMAND_NAME}' failed internally: {str(e)}"))
            # Re-raise the exception so run_seeders knows it failed and won't record success
            raise CommandError(f"Command '{self.COMMAND_NAME}' failed: {e}") from e


    # Keep existing @transaction.atomic decorators on individual methods
    # as they provide atomicity for each specific seeding step.
    @transaction.atomic
    def seed_fundamental_attributes(self):
        """Seed the core skill attributes that form the foundation of all skills."""
        self.stdout.write("Seeding fundamental skill attributes...")
        
        # Core cognitive attributes
        cognitive_attributes = [
            {
                'code': 'cog_analytical',
                'name': 'Analytical Reasoning',
                'description': 'Ability to break down complex information, identify patterns, and draw logical conclusions.',
                'base_decay_rate': 20,  # Decays relatively slowly
                'development_difficulty': 70,
                'development_timeframe': 'long'
            },
            {
                'code': 'cog_creative',
                'name': 'Creative Thinking',
                'description': 'Ability to generate novel ideas, make unexpected connections, and approach problems from multiple perspectives.',
                'base_decay_rate': 25,
                'development_difficulty': 60,
                'development_timeframe': 'medium'
            },
            {
                'code': 'cog_spatial',
                'name': 'Spatial Reasoning',
                'description': 'Ability to visualize and manipulate objects and spatial relationships in the mind.',
                'base_decay_rate': 30,
                'development_difficulty': 65,
                'development_timeframe': 'medium'
            },
            {
                'code': 'cog_verbal',
                'name': 'Verbal Processing',
                'description': 'Ability to understand, process, and produce language effectively.',
                'base_decay_rate': 15,  # Very slow decay
                'development_difficulty': 50,
                'development_timeframe': 'long'
            },
            {
                'code': 'cog_numerical',
                'name': 'Numerical Processing',
                'description': 'Ability to work with numbers, understand mathematical concepts, and perform calculations.',
                'base_decay_rate': 35,
                'development_difficulty': 75,
                'development_timeframe': 'long'
            },
            {
                'code': 'cog_memory',
                'name': 'Memory Capacity',
                'description': 'Ability to encode, store, and retrieve information effectively.',
                'base_decay_rate': 40,
                'development_difficulty': 60,
                'development_timeframe': 'medium'
            },
            {
                'code': 'cog_attention',
                'name': 'Sustained Attention',
                'description': 'Ability to maintain focus on tasks or stimuli over time without distraction.',
                'base_decay_rate': 45,  # Decays quickly without practice
                'development_difficulty': 55,
                'development_timeframe': 'medium'
            },
        ]
        
        # Physical attributes
        physical_attributes = [
            {
                'code': 'phys_strength',
                'name': 'Physical Strength',
                'description': 'Muscular power and force generation capacity.',
                'base_decay_rate': 50,  # Decays relatively quickly
                'development_difficulty': 60,
                'development_timeframe': 'medium'
            },
            {
                'code': 'phys_endurance',
                'name': 'Physical Endurance',
                'description': 'Ability to sustain physical activity over extended periods.',
                'base_decay_rate': 45,
                'development_difficulty': 65,
                'development_timeframe': 'medium'
            },
            {
                'code': 'phys_flexibility',
                'name': 'Flexibility',
                'description': 'Range of motion in joints and muscles.',
                'base_decay_rate': 40,
                'development_difficulty': 50,
                'development_timeframe': 'short'
            },
            {
                'code': 'phys_balance',
                'name': 'Balance & Coordination',
                'description': 'Ability to maintain equilibrium and coordinate movements effectively.',
                'base_decay_rate': 35,
                'development_difficulty': 65,
                'development_timeframe': 'medium'
            },
            {
                'code': 'phys_fine_motor',
                'name': 'Fine Motor Control',
                'description': 'Precision in small, detailed movements, particularly of the hands and fingers.',
                'base_decay_rate': 30,
                'development_difficulty': 70,
                'development_timeframe': 'long'
            },
        ]
        
        # Social attributes
        social_attributes = [
            {
                'code': 'soc_empathy',
                'name': 'Empathic Understanding',
                'description': 'Ability to perceive and understand others\' emotions, perspectives, and needs.',
                'base_decay_rate': 20,  # Decays slowly
                'development_difficulty': 60,
                'development_timeframe': 'long'
            },
            {
                'code': 'soc_expression',
                'name': 'Emotional Expression',
                'description': 'Ability to convey one\'s feelings, intentions, and ideas clearly to others.',
                'base_decay_rate': 25,
                'development_difficulty': 55,
                'development_timeframe': 'medium'
            },
            {
                'code': 'soc_persuasion',
                'name': 'Persuasive Communication',
                'description': 'Ability to influence others\' thoughts or actions through communication.',
                'base_decay_rate': 30,
                'development_difficulty': 70,
                'development_timeframe': 'long'
            },
            {
                'code': 'soc_conflict',
                'name': 'Conflict Resolution',
                'description': 'Ability to navigate and resolve interpersonal conflicts constructively.',
                'base_decay_rate': 35,
                'development_difficulty': 75,
                'development_timeframe': 'long'
            },
            {
                'code': 'soc_leadership',
                'name': 'Leadership Capacity',
                'description': 'Ability to inspire, direct, and coordinate group efforts toward goals.',
                'base_decay_rate': 30,
                'development_difficulty': 80,
                'development_timeframe': 'long'
            },
        ]
        
        # Emotional/self-regulation attributes
        emotional_attributes = [
            {
                'code': 'emot_awareness',
                'name': 'Emotional Awareness',
                'description': 'Ability to recognize and understand one\'s own emotional states.',
                'base_decay_rate': 20,
                'development_difficulty': 65,
                'development_timeframe': 'medium'
            },
            {
                'code': 'emot_regulation',
                'name': 'Emotional Regulation',
                'description': 'Ability to manage emotional responses adaptively in various situations.',
                'base_decay_rate': 30,
                'development_difficulty': 75,
                'development_timeframe': 'long'
            },
            {
                'code': 'emot_resilience',
                'name': 'Psychological Resilience',
                'description': 'Ability to adapt to adversity, recover from setbacks, and grow from challenges.',
                'base_decay_rate': 25,
                'development_difficulty': 80,
                'development_timeframe': 'long'
            },
            {
                'code': 'emot_mindfulness',
                'name': 'Mindful Awareness',
                'description': 'Ability to maintain present-moment awareness with acceptance and without judgment.',
                'base_decay_rate': 40,  # Requires regular practice
                'development_difficulty': 60,
                'development_timeframe': 'medium'
            },
            {
                'code': 'emot_expression',
                'name': 'Emotional Expression',
                'description': 'Ability to express one\'s personal emotion.',
                'base_decay_rate': 40,  # Requires regular practice
                'development_difficulty': 60,
                'development_timeframe': 'medium'
            },
        ]
        
        # Creative/artistic attributes
        creative_attributes = [
            {
                'code': 'create_visual',
                'name': 'Visual Creativity',
                'description': 'Ability to conceptualize and create visually expressive or innovative content.',
                'base_decay_rate': 30,
                'development_difficulty': 65,
                'development_timeframe': 'long'
            },
            {
                'code': 'create_verbal',
                'name': 'Verbal Creativity',
                'description': 'Ability to use language in novel, expressive, or evocative ways.',
                'base_decay_rate': 25,
                'development_difficulty': 60,
                'development_timeframe': 'medium'
            },
            {
                'code': 'create_musical',
                'name': 'Musical Aptitude',
                'description': 'Sensitivity to and ability to work with musical elements like rhythm, pitch, and harmony.',
                'base_decay_rate': 35,
                'development_difficulty': 70,
                'development_timeframe': 'long'
            },
            {
                'code': 'create_bodily',
                'name': 'Kinesthetic Creativity',
                'description': 'Ability to express ideas and emotions through body movement and physical performance.',
                'base_decay_rate': 40,
                'development_difficulty': 65,
                'development_timeframe': 'medium'
            },
        ]
        
        # Practical/technical attributes
        practical_attributes = [
            {
                'code': 'tech_digital',
                'name': 'Digital Literacy',
                'description': 'Ability to use, understand, and adapt to digital tools and technologies.',
                'base_decay_rate': 40,  # Changes quickly with technology
                'development_difficulty': 55,
                'development_timeframe': 'medium'
            },
            {
                'code': 'tech_mechanical',
                'name': 'Mechanical Aptitude',
                'description': 'Understanding of mechanical principles and ability to work with physical systems.',
                'base_decay_rate': 30,
                'development_difficulty': 65,
                'development_timeframe': 'medium'
            },
            {
                'code': 'tech_project',
                'name': 'Project Organization',
                'description': 'Ability to plan, organize, and execute complex multi-step projects.',
                'base_decay_rate': 25,
                'development_difficulty': 60,
                'development_timeframe': 'medium'
            },
        ]
        
        # Combine all attributes
        all_attributes = (
            cognitive_attributes + 
            physical_attributes + 
            social_attributes + 
            emotional_attributes + 
            creative_attributes + 
            practical_attributes
        )
        
        # Create or update attributes
        attribute_count = 0
        for attr_data in all_attributes:
            attribute, created = SkillAttribute.objects.update_or_create(
                code=attr_data['code'],
                defaults={
                    'name': attr_data['name'],
                    'description': attr_data['description'],
                    'base_decay_rate': attr_data['base_decay_rate'],
                    'development_difficulty': attr_data['development_difficulty'],
                    'development_timeframe': attr_data['development_timeframe']
                }
            )
            
            if created:
                attribute_count += 1
                
        self.stdout.write(self.style.SUCCESS(f"Created {attribute_count} attributes."))

    @transaction.atomic
    def seed_trait_influences(self):
        """Seed the influences between personality traits and skill attributes."""
        self.stdout.write("Seeding trait-attribute influences...")
        
        # Define trait influences
        # Format: (trait_code, attribute_code, impact_value)
        influences = [
            # Openness traits
            ('open_aesthetic', 'create_visual', 3),   # Strong positive influence
            ('open_aesthetic', 'create_musical', 2),  # Moderate positive
            ('open_creativity', 'cog_creative', 3),   # Strong positive
            ('open_creativity', 'create_verbal', 2),  # Moderate positive
            ('open_inquisitive', 'cog_analytical', 2),  # Moderate positive
            ('open_unconventional', 'cog_creative', 2),  # Moderate positive
            
            # Conscientiousness traits
            ('consc_organization', 'tech_project', 3),  # Strong positive
            ('consc_diligence', 'phys_endurance', 2),   # Moderate positive
            ('consc_diligence', 'cog_attention', 3),    # Strong positive
            ('consc_perfectionism', 'phys_fine_motor', 2),  # Moderate positive
            ('consc_perfectionism', 'cog_attention', 1),     # Slight positive
            ('consc_prudence', 'emot_regulation', 2),    # Moderate positive
            
            # Extraversion traits
            ('extra_sociability', 'soc_expression', 3),  # Strong positive
            ('extra_social_boldness', 'soc_leadership', 3),  # Strong positive
            ('extra_self_esteem', 'emot_resilience', 2),  # Moderate positive
            ('extra_liveliness', 'phys_endurance', 1),  # Slight positive
            
            # Agreeableness traits
            ('agree_forgiveness', 'soc_conflict', 3),  # Strong positive
            ('agree_gentleness', 'soc_empathy', 2),    # Moderate positive
            ('agree_flexibility', 'emot_regulation', 1),  # Slight positive
            ('agree_patience', 'cog_attention', 1),    # Slight positive
            
            # Emotionality traits
            ('emotion_anxiety', 'cog_attention', -1),   # Slight negative
            ('emotion_anxiety', 'emot_resilience', -2),  # Moderate negative
            ('emotion_sentimentality', 'soc_empathy', 3),  # Strong positive
            ('emotion_fearfulness', 'phys_strength', -1),  # Slight negative
            
            # Honesty-Humility traits
            ('honesty_fairness', 'soc_leadership', 1),  # Slight positive
            ('honesty_modesty', 'soc_persuasion', -1),   # Slight negative
            ('honesty_sincerity', 'soc_empathy', 1),    # Slight positive
        ]
        
        # Create or update influences
        influence_count = 0
        for trait_code, attr_code, impact in influences:
            try:
                trait = GenericTrait.objects.get(code=trait_code)
                attribute = SkillAttribute.objects.get(code=attr_code)
                
                influence, created = AttributeTraitInfluence.objects.update_or_create(
                    generic_trait=trait,
                    attribute=attribute,
                    defaults={'impact': impact}
                )
                
                if created:
                    influence_count += 1
                    
            except (GenericTrait.DoesNotExist, SkillAttribute.DoesNotExist):
                self.stdout.write(self.style.WARNING(f"Could not create influence: {trait_code} → {attr_code}"))
                continue
                
        self.stdout.write(self.style.SUCCESS(f"Created {influence_count} trait influences."))

    @transaction.atomic
    def seed_skill_definitions(self):
        """Seed sample skill definitions composed of attributes."""
        self.stdout.write("Seeding skill definitions and compositions...")
        
        # Define sample skills
        # Format: (code, name, description, tags, attribute_compositions)
        skill_definitions = [
            # Creative writing skill
            {
                'code': 'creative_writing',
                'name': 'Creative Writing',
                'description': 'Ability to craft engaging written content with originality and expressiveness.',
                'tags': ['creative', 'communication', 'artistic'],
                'attribute_compositions': [
                    ('create_verbal', 3.0),     # Primary component
                    ('cog_creative', 2.5),      # Strong component
                    ('cog_verbal', 2.0),        # Moderate component
                    ('tech_project', 1.0),      # Minor component
                    ('emot_awareness', 0.5),    # Slight component
                ]
            },
            
            # Public speaking skill
            {
                'code': 'public_speaking',
                'name': 'Public Speaking',
                'description': 'Ability to deliver clear, compelling presentations to audiences of various sizes.',
                'tags': ['communication', 'social', 'professional'],
                'attribute_compositions': [
                    ('soc_expression', 3.0),     # Primary component
                    ('cog_verbal', 2.0),         # Strong component
                    ('emot_regulation', 2.0),    # Strong component
                    ('soc_persuasion', 1.5),     # Moderate component
                    ('cog_attention', 0.5),      # Slight component
                ]
            },
            
            # Problem solving skill
            {
                'code': 'problem_solving',
                'name': 'Problem Solving',
                'description': 'Ability to identify challenges, analyze possible solutions, and implement effective approaches.',
                'tags': ['cognitive', 'analytical', 'professional'],
                'attribute_compositions': [
                    ('cog_analytical', 3.0),     # Primary component
                    ('cog_creative', 2.0),       # Strong component
                    ('tech_project', 1.5),       # Moderate component
                    ('emot_resilience', 1.0),    # Minor component
                    ('soc_conflict', 0.5),       # Slight component
                ]
            },
            
            # Visual art skill
            {
                'code': 'visual_art',
                'name': 'Visual Art Creation',
                'description': 'Ability to express ideas, emotions, and concepts through visual media and techniques.',
                'tags': ['creative', 'artistic', 'visual'],
                'attribute_compositions': [
                    ('create_visual', 3.0),      # Primary component
                    ('cog_spatial', 2.0),        # Strong component
                    ('phys_fine_motor', 2.0),    # Strong component
                    ('cog_creative', 1.5),       # Moderate component
                    ('emot_expression', 1.0),    # Minor component
                ]
            },
            
            # Physical fitness skill
            {
                'code': 'physical_fitness',
                'name': 'Physical Fitness Training',
                'description': 'Knowledge and ability to develop physical capabilities through targeted exercise and recovery.',
                'tags': ['physical', 'health', 'lifestyle'],
                'attribute_compositions': [
                    ('phys_strength', 2.5),      # Primary component
                    ('phys_endurance', 2.5),     # Primary component
                    ('phys_flexibility', 1.5),   # Moderate component
                    ('emot_regulation', 1.0),    # Minor component
                    ('tech_project', 0.5),       # Slight component
                ]
            },
            
            # Emotional intelligence skill
            {
                'code': 'emotional_intelligence',
                'name': 'Emotional Intelligence',
                'description': 'Ability to recognize, understand, and manage emotions in oneself and others.',
                'tags': ['social', 'emotional', 'interpersonal'],
                'attribute_compositions': [
                    ('emot_awareness', 2.5),     # Primary component
                    ('soc_empathy', 2.5),        # Primary component
                    ('emot_regulation', 2.0),    # Strong component
                    ('soc_expression', 1.0),     # Minor component
                    ('emot_resilience', 1.0),    # Minor component
                ]
            },
            
            # Programming skill
            {
                'code': 'programming',
                'name': 'Computer Programming',
                'description': 'Ability to design, write, test, and maintain computer code to create software.',
                'tags': ['technical', 'digital', 'professional'],
                'attribute_compositions': [
                    ('cog_analytical', 3.0),     # Primary component
                    ('tech_digital', 2.5),       # Strong component
                    ('cog_creative', 1.5),       # Moderate component
                    ('tech_project', 1.5),       # Moderate component
                    ('cog_attention', 1.0),      # Minor component
                ]
            },
            
            # Leadership skill
            {
                'code': 'leadership',
                'name': 'Leadership',
                'description': 'Ability to guide, inspire, and coordinate groups toward shared objectives.',
                'tags': ['social', 'professional', 'management'],
                'attribute_compositions': [
                    ('soc_leadership', 3.0),     # Primary component
                    ('soc_persuasion', 2.0),     # Strong component
                    ('emot_regulation', 1.5),    # Moderate component
                    ('tech_project', 1.5),       # Moderate component
                    ('soc_empathy', 1.0),        # Minor component
                ]
            },
            
            # Meditation skill
            {
                'code': 'meditation',
                'name': 'Meditation Practice',
                'description': 'Ability to cultivate focused attention, awareness, and mental clarity through structured mindfulness techniques.',
                'tags': ['mindfulness', 'emotional', 'spiritual'],
                'attribute_compositions': [
                    ('emot_mindfulness', 3.0),   # Primary component
                    ('cog_attention', 2.5),      # Strong component
                    ('emot_regulation', 2.0),    # Strong component
                    ('emot_awareness', 1.5),     # Moderate component
                    ('emot_resilience', 1.0),    # Minor component
                ]
            },
            
            # Negotiation skill
            {
                'code': 'negotiation',
                'name': 'Negotiation',
                'description': 'Ability to reach agreements through effective communication, understanding of interests, and strategic problem-solving.',
                'tags': ['social', 'professional', 'communication'],
                'attribute_compositions': [
                    ('soc_persuasion', 3.0),     # Primary component
                    ('soc_empathy', 2.0),        # Strong component
                    ('cog_analytical', 1.5),     # Moderate component
                    ('emot_regulation', 1.5),    # Moderate component
                    ('soc_conflict', 1.0),       # Minor component
                ]
            },
        ]
        
        # Create or update skills
        skill_count = 0
        composition_count = 0
        
        for skill_data in skill_definitions:
            # Get or create the skill definition
            skill, created = SkillDefinition.objects.update_or_create(
                code=skill_data['code'],
                defaults={
                    'name': skill_data['name'],
                    'description': skill_data['description'],
                    'tags': skill_data['tags']
                }
            )
            
            if created:
                skill_count += 1
                
            # Create or update compositions
            for attr_code, weight in skill_data['attribute_compositions']:
                try:
                    attribute = SkillAttribute.objects.get(code=attr_code)
                    
                    composition, comp_created = SkillAttributeComposition.objects.update_or_create(
                        skill=skill,
                        attribute=attribute,
                        defaults={'weight': weight}
                    )
                    
                    if comp_created:
                        composition_count += 1
                        
                except SkillAttribute.DoesNotExist:
                    self.stdout.write(self.style.WARNING(f"Attribute not found: {attr_code}"))
                    continue
                    
        self.stdout.write(self.style.SUCCESS(f"Created {skill_count} skills with {composition_count} attribute compositions."))

    @transaction.atomic
    def seed_domain_applications(self):
        """Seed domain applications for the skills."""
        self.stdout.write("Seeding skill-domain applications...")
        
        # Define domain applications
        # Format: (skill_code, domain_code, relevance, transfer_coefficient, domain_specific_properties)
        domain_applications = [
            # Creative writing applications
            ('creative_writing', 'creative_writing', 100, 1.0, {}),  # Perfect relevance in own domain
            ('creative_writing', 'creative_visual', 30, 0.7, {'decay_rate': 20}),  # Moderate relevance
            ('creative_writing', 'intel_language', 70, 0.9, {}),  # Strong relevance
            
            # Public speaking applications
            ('public_speaking', 'soc_comm', 100, 1.0, {}),  # Perfect relevance 
            ('public_speaking', 'soc_leadership', 70, 0.8, {}),  # Strong relevance
            ('public_speaking', 'soc_connecting', 30, 0.7, {}),  # Moderate relevance
            
            # Problem solving applications
            ('problem_solving', 'intel_problem', 100, 1.0, {}),  # Perfect relevance
            ('problem_solving', 'intel_strategic', 70, 0.9, {}),  # Strong relevance
            ('problem_solving', 'prod_career', 70, 0.8, {}),  # Strong relevance
            
            # Visual art applications
            ('visual_art', 'creative_visual', 100, 1.0, {}),  # Perfect relevance
            ('visual_art', 'creative_craft', 70, 0.8, {}),  # Strong relevance
            ('visual_art', 'creative_observation', 30, 0.7, {}),  # Moderate relevance
            
            # Physical fitness applications
            ('physical_fitness', 'phys_cardio', 100, 1.0, {}),  # Perfect relevance
            ('physical_fitness', 'phys_strength', 100, 1.0, {}),  # Perfect relevance
            ('physical_fitness', 'phys_flexibility', 70, 0.9, {}),  # Strong relevance
            ('physical_fitness', 'phys_sports', 70, 0.8, {}),  # Strong relevance
            ('physical_fitness', 'emot_stress', 30, 0.6, {}),  # Moderate relevance
            
            # Emotional intelligence applications
            ('emotional_intelligence', 'emot_aware', 100, 1.0, {}),  # Perfect relevance
            ('emotional_intelligence', 'emot_regulate', 100, 1.0, {}),  # Perfect relevance
            ('emotional_intelligence', 'soc_empathy', 70, 0.9, {}),  # Strong relevance
            ('emotional_intelligence', 'soc_connecting', 70, 0.8, {}),  # Strong relevance
            ('emotional_intelligence', 'soc_family', 30, 0.7, {}),  # Moderate relevance
            
            # Programming applications
            ('programming', 'intel_tech', 100, 1.0, {}),  # Perfect relevance
            ('programming', 'intel_problem', 70, 0.8, {'attribute_modifiers': {'cog_analytical': 1.2}}),  # Strong relevance
            ('programming', 'prod_skill', 70, 0.8, {}),  # Strong relevance
            ('programming', 'creative_design', 30, 0.6, {}),  # Moderate relevance
            
            # Leadership applications
            ('leadership', 'soc_leadership', 100, 1.0, {}),  # Perfect relevance
            ('leadership', 'prod_career', 70, 0.8, {}),  # Strong relevance
            ('leadership', 'soc_group', 70, 0.9, {}),  # Strong relevance
            ('leadership', 'soc_conflict', 30, 0.7, {}),  # Moderate relevance
            
            # Meditation applications
            ('meditation', 'refl_meditate', 100, 1.0, {}),  # Perfect relevance
            ('meditation', 'refl_mindful', 100, 1.0, {}),  # Perfect relevance
            ('meditation', 'emot_stress', 70, 0.9, {}),  # Strong relevance
            ('meditation', 'refl_comfort', 30, 0.7, {}),  # Moderate relevance
            
            # Negotiation applications
            ('negotiation', 'soc_comm', 100, 1.0, {}),  # Perfect relevance
            ('negotiation', 'soc_conflict', 100, 1.0, {}),  # Perfect relevance
            ('negotiation', 'prod_career', 70, 0.8, {}),  # Strong relevance
            ('negotiation', 'soc_leadership', 30, 0.7, {}),  # Moderate relevance
        ]
        
        # Create or update domain applications
        application_count = 0
        for skill_code, domain_code, relevance, transfer, properties in domain_applications:
            try:
                skill = SkillDefinition.objects.get(code=skill_code)
                domain = GenericDomain.objects.get(code=domain_code)
                
                application, created = SkillDomainApplication.objects.update_or_create(
                    skill=skill,
                    domain=domain,
                    defaults={
                        'relevance': relevance,
                        'transfer_coefficient': transfer,
                        'domain_specific_properties': properties
                    }
                )
                
                if created:
                    application_count += 1
                    
            except SkillDefinition.DoesNotExist:
                self.stdout.write(self.style.WARNING(
                    f"SkillDefinition not found: {skill_code}"
                ))
                continue
            except GenericDomain.DoesNotExist:
                self.stdout.write(self.style.WARNING(
                    f"GenericDomain not found: {domain_code}"
                ))
                continue
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Error creating domain application for {skill_code} → {domain_code}: {str(e)}"
                ))
                continue
                
        self.stdout.write(self.style.SUCCESS(f"Created {application_count} domain applications."))
