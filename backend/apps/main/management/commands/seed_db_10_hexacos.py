import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.contrib.auth.models import User
from apps.user.models import GenericTrait, TraitType # Removed unused imports

class Command(BaseCommand):
    help = 'Seeds HEXACO traits and creates a superuser if they do not exist. Skips if already applied by run_seeders.'
    # Re-add command name definition
    COMMAND_NAME = os.path.splitext(os.path.basename(__file__))[0]

    def handle(self, *args, **kwargs):
        # Check environment variable to potentially skip idempotency check
        skip_check = os.environ.get('SKIP_SEEDER_IDEMPOTENCY_CHECK') == 'true'

        if not skip_check:
            # Import and perform the check only if the environment variable is not set
            from apps.main.models import AppliedSeedingCommand # Import locally
            if AppliedSeedingCommand.objects.filter(command_name=self.COMMAND_NAME).exists():
                self.stdout.write(self.style.WARNING(f"Command '{self.COMMAND_NAME}' has already been applied by run_seeders. Skipping."))
                return
        else:
             self.stdout.write(self.style.WARNING(f"Skipping AppliedSeedingCommand check for '{self.COMMAND_NAME}' due to environment variable."))

        self.stdout.write(self.style.SUCCESS(f"Running command '{self.COMMAND_NAME}' logic (will skip individual existing items)..."))

        try:
            # 2. Execute the seeding logic within a transaction
            with transaction.atomic():
                # Create a superuser (this part is already idempotent)
                if not User.objects.filter(username="admin").exists():
                    User.objects.create_superuser("admin", "<EMAIL>", "0000")
                    self.stdout.write(self.style.SUCCESS("Superuser created: admin / 0000"))
                else:
                    self.stdout.write(self.style.WARNING("Superuser 'admin' already exists. Skipping creation."))

                # Seed HEXACO traits (this method handles internal idempotency)
                created_count = self.seed_hexaco_traits()

                # Ensure AppliedSeedingCommand.objects.create(...) remains removed from here.
                # run_seeders.py handles the recording upon successful completion of this command.
                self.stdout.write(self.style.SUCCESS(f"Finished command '{self.COMMAND_NAME}' logic! Created {created_count} new HEXACO traits."))

        except Exception as e:
            # Catch any exception during the transaction
            self.stderr.write(self.style.ERROR(f"Command '{self.COMMAND_NAME}' failed internally: {str(e)}"))
            # Re-raise the exception so run_seeders knows it failed and won't record success
            raise CommandError(f"Command '{self.COMMAND_NAME}' failed: {e}") from e


    def seed_hexaco_traits(self):
        """
        Create the 24 traits from the HEXACO personality model if they don't exist.
        Returns the count of newly created traits.
        """
        self.stdout.write("Seeding HEXACO traits...")
        # Dictionary of all 24 HEXACO traits grouped by dimension
        hexaco_traits = {
            TraitType.HONESTYHUMILITY: [
                {"code": "honesty_sincerity", "name": "Sincerity", "description": "Genuineness in self-expression and relationships without manipulation."},
                {"code": "honesty_fairness", "name": "Fairness", "description": "Tendency to avoid exploiting others for personal gain."},
                {"code": "honesty_greed_avoidance", "name": "Greed Avoidance", "description": "Level of disinterest in luxury, wealth, and social status."},
                {"code": "honesty_modesty", "name": "Modesty", "description": "Tendency to be humble and unassuming about achievements."}
            ],
            TraitType.EMOTIONALITY: [
                {"code": "emotion_fearfulness", "name": "Fearfulness", "description": "Tendency to experience fear in response to potential dangers."},
                {"code": "emotion_anxiety", "name": "Anxiety", "description": "Tendency to worry in a variety of contexts."},
                {"code": "emotion_dependence", "name": "Dependence", "description": "Need for emotional support and reassurance from others."},
                {"code": "emotion_sentimentality", "name": "Sentimentality", "description": "Tendency to form strong emotional bonds and empathic responses."}
            ],
            TraitType.EXTRAVERSION: [
                {"code": "extra_self_esteem", "name": "Social Self-Esteem", "description": "Confidence and positive self-evaluation in social contexts."},
                {"code": "extra_social_boldness", "name": "Social Boldness", "description": "Comfort in a variety of social situations and leadership roles."},
                {"code": "extra_sociability", "name": "Sociability", "description": "Enjoyment of social gatherings and interactions with others."},
                {"code": "extra_liveliness", "name": "Liveliness", "description": "Energy level and enthusiasm in social and activity contexts."}
            ],
            TraitType.AGREEABLENESS: [
                {"code": "agree_forgiveness", "name": "Forgiveness", "description": "Willingness to trust and forgive those who have caused harm."},
                {"code": "agree_gentleness", "name": "Gentleness", "description": "Tendency to be mild and lenient in interactions with others."},
                {"code": "agree_flexibility", "name": "Flexibility", "description": "Willingness to compromise and cooperate with others."},
                {"code": "agree_patience", "name": "Patience", "description": "Tendency to remain calm rather than becoming angry."}
            ],
            TraitType.CONSCIENTIOUSNESS: [
                {"code": "consc_organization", "name": "Organization", "description": "Tendency to seek order and structure in the physical environment."},
                {"code": "consc_diligence", "name": "Diligence", "description": "Work ethic and persistence in pursuing goals."},
                {"code": "consc_perfectionism", "name": "Perfectionism", "description": "Thoroughness and concern with details and accuracy."},
                {"code": "consc_prudence", "name": "Prudence", "description": "Tendency to deliberate carefully and inhibit impulses."}
            ],
            TraitType.OPENNESS: [
                {"code": "open_aesthetic", "name": "Aesthetic Appreciation", "description": "Enjoyment of beauty in art, music, and nature."},
                {"code": "open_inquisitive", "name": "Inquisitiveness", "description": "Interest in exploring new ideas and understanding complex concepts."},
                {"code": "open_creativity", "name": "Creativity", "description": "Preference for innovation and experimentation."},
                {"code": "open_unconventional", "name": "Unconventionality", "description": "Willingness to accept the unusual and challenge tradition."}
            ]
        }

        # Create each trait
        trait_count = 0
        for trait_type, traits in hexaco_traits.items():
            for trait in traits:
                # Check if trait already exists to avoid duplicates
                if not GenericTrait.objects.filter(code=trait["code"]).exists():
                    GenericTrait.objects.create(
                        code=trait["code"],
                        trait_type=trait_type,
                        name=trait["name"],
                        description=trait["description"]
                    )
                    trait_count += 1
        
        self.stdout.write(self.style.SUCCESS(f"Created {trait_count} HEXACO traits"))
