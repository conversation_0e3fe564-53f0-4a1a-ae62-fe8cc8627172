import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.user.models import GenericEnvironment, GenericEnvironmentDomainRelationship
from apps.activity.models import GenericDomain

class Command(BaseCommand):
    help = 'Creates predefined GenericEnvironment objects with domain relationships. Skips if already applied.'
    # Re-add command name definition
    COMMAND_NAME = os.path.splitext(os.path.basename(__file__))[0]

    # Removed add_arguments

    def handle(self, *args, **options):
                # Check environment variable to potentially skip idempotency check
        skip_check = os.environ.get('SKIP_SEEDER_IDEMPOTENCY_CHECK') == 'true'

        if not skip_check:
            # Import and perform the check only if the environment variable is not set
            from apps.main.models import AppliedSeedingCommand # Import locally
            if AppliedSeedingCommand.objects.filter(command_name=self.COMMAND_NAME).exists():
                self.stdout.write(self.style.WARNING(f"Command '{self.COMMAND_NAME}' has already been applied by run_seeders. Skipping."))
                return
        else:
             self.stdout.write(self.style.WARNING(f"Skipping AppliedSeedingCommand check for '{self.COMMAND_NAME}' due to environment variable."))

        self.stdout.write(self.style.SUCCESS(f"Running command '{self.COMMAND_NAME}' logic (will skip individual existing items)..."))

        # Track statistics for reporting within this run
        self.stats = {
            'created': 0,
            'updated': 0, # This will likely remain 0 as we skip existing items internally now
            'skipped': 0, # Tracks individual items skipped within the command run
            'errors': 0
        }

        try:
            # 2. Execute the seeding logic within a transaction
            with transaction.atomic():
                # Call the original seeding methods
                self.seed_environments('residential', self.get_residential_environments())
                self.seed_environments('professional', self.get_professional_environments())
                self.seed_environments('educational', self.get_educational_environments())
                self.seed_environments('commercial', self.get_commercial_environments())
                self.seed_environments('natural', self.get_natural_environments())
                self.seed_environments('transportation', self.get_transportation_environments())
                self.seed_environments('cultural', self.get_cultural_environments())
                self.seed_environments('healthcare', self.get_healthcare_environments())

                # Check for errors during the seeding process before marking as complete
                if self.stats['errors'] > 0:
                     # If errors occurred, raise an exception to rollback the transaction
                      # and prevent marking the command as applied by run_seeders.
                      raise CommandError(f"Errors occurred during seeding for {self.COMMAND_NAME}. Transaction rolled back.")

                # Ensure AppliedSeedingCommand.objects.create(...) remains removed from here.
                # run_seeders.py handles the recording upon successful completion of this command.

        except Exception as e:
            # Catch any exception during the transaction, including the CommandError raised above
            self.stderr.write(self.style.ERROR(f"Command '{self.COMMAND_NAME}' failed internally: {str(e)}"))
            # Re-raise the exception so run_seeders knows it failed and won't record success
            raise CommandError(f"Command '{self.COMMAND_NAME}' failed: {e}") from e

        # Report results (only shown if the command logic ran successfully)
        self.stdout.write(self.style.SUCCESS(
            f"Finished command '{self.COMMAND_NAME}' logic! Created: {self.stats['created']}, "
            # f"Updated: {self.stats['updated']}, "
            f"Skipped Items: {self.stats['skipped']}, Item Errors: {self.stats['errors']}"
        ))

    def seed_environments(self, primary_category, environments):
        """Seed environments for a specific primary category"""
        self.stdout.write(f"Processing category: {primary_category}")
        # Process each environment
        for env_data in environments:
            try:
                # Use the existing create_or_update logic which handles skipping individual items
                result = self.create_or_update_environment(env_data, primary_category)
                self.stats[result] += 1
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"Error processing environment {env_data.get('name', 'unknown')}: {str(e)}"))
                self.stats['errors'] += 1 # Track errors to potentially rollback

    def create_or_update_environment(self, env_data, primary_category):
        """
        Creates a GenericEnvironment if it doesn't exist based on 'code'.
        If it exists, it skips the creation. This acts as a secondary check.
        The primary check is now at the command level in handle().
        """
        code = env_data.get('code')
        if not code:
             self.stderr.write(self.style.ERROR(f"Environment data missing 'code': {env_data.get('name', 'unknown')}"))
             raise ValueError("Environment data must include a 'code'.") # Raise error to trigger rollback

        try:
            # Attempt to get the environment by code
            GenericEnvironment.objects.get(code=code)
            # If it exists, skip this specific item
            self.stdout.write(f"Skipping existing environment item: {env_data['name']} [{code}]")
            return 'skipped'
        except GenericEnvironment.DoesNotExist:
            # If it doesn't exist, proceed to create it
            pass

        # Prepare environment data for creation
        env_fields = {
            'code': code, # Ensure code is included
            'name': env_data['name'],
            'description': env_data.get('description', ''),
            'is_active': env_data.get('is_active', True),
            'is_indoor': env_data.get('is_indoor', True),
            'primary_category': primary_category,
            'typical_space_size': env_data.get('typical_space_size', 'medium'),
            'typical_privacy_level': env_data.get('typical_privacy_level', 50),
            'archetype_attributes': env_data.get('archetype_attributes', {})
        }

        # Create new environment (no update logic needed here anymore)
        # The transaction is handled in the handle() method
        environment = GenericEnvironment.objects.create(**env_fields)
        self.stdout.write(self.style.SUCCESS(f"Created environment item: {environment.name} [{environment.code}]"))

        # Process domain relationships for the newly created environment
        self.process_domain_relationships(environment, env_data.get('domain_relationships', []))

        return 'created' # Only 'created' or 'skipped' are relevant now for items

    def process_domain_relationships(self, environment, relationships):
        """Create domain relationships for the environment"""
        # No need to clear existing relationships as we only create environments now
        if not relationships:
            return

        # Create new relationships
        relationships_to_create = []
        for rel_data in relationships:
            try:
                domain = GenericDomain.objects.get(code=rel_data['domain_code'])
                relationships_to_create.append(
                    GenericEnvironmentDomainRelationship(
                        generic_environment=environment,
                        domain=domain,
                        strength=rel_data.get('strength', 30)  # Default to MODERATE
                    )
                )
            except GenericDomain.DoesNotExist:
                self.stderr.write(self.style.WARNING(
                    f"Domain '{rel_data['domain_code']}' not found for relationship on environment '{environment.name}'. Skipping relationship."
                ))
                # Optionally track this as an error if relationships are critical
                # self.stats['errors'] += 1

        # Bulk create relationships for efficiency
        if relationships_to_create:
            GenericEnvironmentDomainRelationship.objects.bulk_create(relationships_to_create)

    # --- Keep all get_*_environments methods as they are ---
    def get_healthcare_environments(self):
        return [
            {
                'code': 'ind_healthcare_hospital_ward',
                'name': 'Hospital General Ward',
                'description': 'A typical hospital inpatient ward where multiple patients receive care in a shared space with beds separated by curtains or partitions. Features regular monitoring by nursing staff, medical equipment, and limited personal space.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['hospital beds', 'monitoring equipment', 'nurse call systems', 'oxygen supplies', 'privacy curtains'],
                    'typical_noise_level': 'moderate',
                    'lighting': 'fluorescent',
                    'visiting_hours': 'restricted',
                    'staff_presence': 'consistent'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'social', 'strength': 20},
                    {'domain_code': 'physical', 'strength': -50},
                    {'domain_code': 'creative', 'strength': 10},
                    {'domain_code': 'intellectual', 'strength': 40}
                ]
            },
            {
                'code': 'ind_healthcare_private_room',
                'name': 'Hospital Private Room',
                'description': 'An individual hospital room designed for a single patient, offering increased privacy, personal space, and often amenities like a private bathroom, television, and space for visitors.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'medium',
                'typical_privacy_level': 80,
                'archetype_attributes': {
                    'common_features': ['adjustable bed', 'private bathroom', 'television', 'visitor seating', 'medical equipment'],
                    'typical_noise_level': 'low',
                    'lighting': 'adjustable',
                    'visiting_hours': 'flexible',
                    'staff_presence': 'periodic'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'physical', 'strength': -30},
                    {'domain_code': 'creative', 'strength': 50},
                    {'domain_code': 'intellectual', 'strength': 60}
                ]
            },
            {
                'code': 'ind_healthcare_icu',
                'name': 'Intensive Care Unit',
                'description': 'A specialized hospital department providing intensive treatment and monitoring for critically ill patients. Features advanced life support equipment, continuous monitoring, and a high staff-to-patient ratio.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['advanced monitoring equipment', 'ventilators', 'central monitoring station', 'isolation capabilities', 'specialized staff'],
                    'typical_noise_level': 'high',
                    'lighting': 'constant',
                    'visiting_hours': 'highly restricted',
                    'staff_presence': 'continuous',
                    'stress_level': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': -20},
                    {'domain_code': 'social', 'strength': -50},
                    {'domain_code': 'physical', 'strength': -100},
                    {'domain_code': 'creative', 'strength': -70},
                    {'domain_code': 'intellectual', 'strength': -30}
                ]
            },
            {
                'code': 'ind_healthcare_waiting_room',
                'name': 'Medical Waiting Room',
                'description': 'A communal space where patients and their companions wait before appointments or procedures. Usually furnished with chairs, reading materials, and sometimes a television to help pass time.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'medium',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['seating areas', 'reception desk', 'reading materials', 'television', 'health information posters'],
                    'typical_noise_level': 'moderate',
                    'lighting': 'bright',
                    'duration_of_stay': 'variable',
                    'stress_level': 'moderate to high'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 20},
                    {'domain_code': 'social', 'strength': 10},
                    {'domain_code': 'physical', 'strength': -20},
                    {'domain_code': 'creative', 'strength': 30},
                    {'domain_code': 'intellectual', 'strength': 50}
                ]
            },
            {
                'code': 'ind_healthcare_outpatient_clinic',
                'name': 'Outpatient Clinic',
                'description': 'A medical facility providing diagnosis, care, and treatment for patients who do not require overnight hospitalization. Typically includes examination rooms, specialty equipment, and consultation spaces.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'large',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['examination rooms', 'medical equipment', 'consultation areas', 'check-in desk', 'health records systems'],
                    'typical_noise_level': 'low to moderate',
                    'lighting': 'bright',
                    'appointment_required': 'typically yes',
                    'specialized_staff': 'yes'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'physical', 'strength': 0},
                    {'domain_code': 'creative', 'strength': 20},
                    {'domain_code': 'intellectual', 'strength': 70}
                ]
            },
            {
                'code': 'ind_healthcare_physical_therapy',
                'name': 'Physical Therapy Center',
                'description': 'A specialized facility equipped for rehabilitation and physical therapy services. Features exercise equipment, treatment tables, and open spaces for movement-based therapies.',
                'is_indoor': True, 
                'primary_category': 'healthcare',
                'typical_space_size': 'large',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['exercise equipment', 'treatment tables', 'parallel bars', 'training stairs', 'therapy pools'],
                    'typical_noise_level': 'moderate',
                    'lighting': 'bright',
                    'activity_level': 'high',
                    'specialized_staff': 'physical therapists'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 30},
                    {'domain_code': 'intellectual', 'strength': 60}
                ]
            },
            {
                'code': 'ind_healthcare_mental_health_facility',
                'name': 'Mental Health Facility',
                'description': 'A facility designed for the treatment and support of individuals with mental health conditions. Includes therapy rooms, community spaces, and secure environments tailored to various psychiatric needs.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'large',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['therapy rooms', 'communal areas', 'secure environments', 'observation capabilities', 'calming décor'],
                    'typical_noise_level': 'controlled',
                    'lighting': 'adjustable, often natural',
                    'security_level': 'variable',
                    'specialized_staff': 'mental health professionals'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 100},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'emotional', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'intellectual', 'strength': 60}
                ]
            },
            {
                'code': 'ind_healthcare_dental_office',
                'name': 'Dental Office',
                'description': 'A healthcare facility specialized in oral health, featuring dental chairs, specialized equipment for examination and treatment of teeth and gums, and often x-ray capabilities.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'medium',
                'typical_privacy_level': 75,
                'archetype_attributes': {
                    'common_features': ['dental chairs', 'x-ray equipment', 'dental tools', 'sterilization area', 'oral hygiene displays'],
                    'typical_noise_level': 'moderate with equipment noise',
                    'lighting': 'very bright, focused',
                    'appointment_based': 'yes',
                    'specialized_staff': 'dentists, dental hygienists'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 0},
                    {'domain_code': 'social', 'strength': 20},
                    {'domain_code': 'physical', 'strength': -20},
                    {'domain_code': 'creative', 'strength': -10},
                    {'domain_code': 'intellectual', 'strength': 40}
                ]
            },
            {
                'code': 'ind_healthcare_laboratory',
                'name': 'Medical Laboratory',
                'description': 'A specialized facility where clinical specimens are analyzed to obtain information about the health of a patient. Features specialized equipment for testing, analyzing samples, and research.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'medium',
                'typical_privacy_level': 90,
                'archetype_attributes': {
                    'common_features': ['testing equipment', 'sample storage', 'sterile workstations', 'computers', 'specialized testing areas'],
                    'typical_noise_level': 'low with equipment hum',
                    'lighting': 'bright',
                    'restricted_access': 'yes',
                    'specialized_staff': 'laboratory technicians, scientists'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'social', 'strength': -30},
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 40},
                    {'domain_code': 'productive_practical', 'strength': 80}
                ]
            },
            {
                'code': 'ind_healthcare_imaging_center',
                'name': 'Medical Imaging Center',
                'description': 'A facility specializing in diagnostic imaging services such as X-rays, MRIs, CT scans, and ultrasounds to help diagnose and monitor medical conditions.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'large',
                'typical_privacy_level': 85,
                'archetype_attributes': {
                    'common_features': ['MRI machines', 'CT scanners', 'X-ray equipment', 'ultrasound rooms', 'image interpretation areas'],
                    'typical_noise_level': 'variable (equipment can be loud)',
                    'lighting': 'controlled',
                    'electromagnetic fields': 'present',
                    'specialized_staff': 'radiologists, technicians'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 10},
                    {'domain_code': 'social', 'strength': -20},
                    {'domain_code': 'physical', 'strength': -40},
                    {'domain_code': 'intellectual', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 0}
                ]
            },
            {
                'code': 'ind_healthcare_pharmacy',
                'name': 'Pharmacy',
                'description': 'A facility where medications are dispensed, medication information is provided, and sometimes basic health screenings are offered. Features medication storage, consultation areas, and health product displays.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['prescription counter', 'medication shelves', 'consultation area', 'health products', 'information displays'],
                    'typical_noise_level': 'low to moderate',
                    'lighting': 'bright',
                    'professional_guidance': 'available',
                    'specialized_staff': 'pharmacists, pharmacy technicians'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'physical', 'strength': 10},
                    {'domain_code': 'intellectual', 'strength': 60},
                    {'domain_code': 'productive_practical', 'strength': 70}
                ]
            },
            {
                'code': 'ind_healthcare_hospice',
                'name': 'Hospice Care Center',
                'description': 'A facility specializing in care for individuals with terminal illnesses, focusing on comfort, dignity, and quality of life rather than curative treatment. Designed to create a home-like, peaceful environment.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'medium',
                'typical_privacy_level': 75,
                'archetype_attributes': {
                    'common_features': ['patient rooms', 'family areas', 'comfort care equipment', 'meditation spaces', 'garden access'],
                    'typical_noise_level': 'very low',
                    'lighting': 'soft, natural when possible',
                    'emotional_atmosphere': 'supportive, peaceful',
                    'specialized_staff': 'palliative care specialists, counselors'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 100},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'spiritual_existential', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 90},
                    {'domain_code': 'physical', 'strength': -60}
                ]
            },
            {
                'code': 'ind_healthcare_emergency_room',
                'name': 'Emergency Department',
                'description': 'A critical care facility designed for rapid treatment of acute illnesses and injuries. Features specialized equipment for emergency medicine, triage areas, and trauma rooms.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['triage area', 'trauma rooms', 'resuscitation equipment', 'monitoring systems', 'ambulance access'],
                    'typical_noise_level': 'high',
                    'lighting': 'bright, constant',
                    'pace': 'fast, unpredictable',
                    'stress_level': 'very high',
                    'specialized_staff': 'emergency physicians, trauma nurses'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': -70},
                    {'domain_code': 'social', 'strength': -40},
                    {'domain_code': 'physical', 'strength': -90},
                    {'domain_code': 'creative', 'strength': -80},
                    {'domain_code': 'intellectual', 'strength': -20}
                ]
            },
            {
                'code': 'ind_healthcare_rehab_center',
                'name': 'Rehabilitation Center',
                'description': 'A facility dedicated to helping patients recover physical, mental, and cognitive abilities after illness, injury, or surgery through therapeutic programs and specialized care.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'large',
                'typical_privacy_level': 50,
                'archetype_attributes': {
                    'common_features': ['therapy gyms', 'adaptive equipment', 'therapy rooms', 'training apartments', 'group activity spaces'],
                    'typical_noise_level': 'moderate',
                    'lighting': 'bright, often natural',
                    'activity_level': 'high',
                    'goal_orientation': 'strong',
                    'specialized_staff': 'rehabilitation specialists, therapists'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'productive_practical', 'strength': 80}
                ]
            },
            {
                'code': 'out_healthcare_healing_garden',
                'name': 'Therapeutic Healing Garden',
                'description': 'An outdoor space specifically designed to promote healing and wellbeing within a healthcare setting. Features sensory plants, accessible pathways, seating areas, and elements designed to reduce stress and promote recovery.',
                'is_indoor': False,
                'primary_category': 'healthcare',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['sensory plants', 'accessible paths', 'water features', 'seating areas', 'shade structures'],
                    'typical_noise_level': 'low with natural sounds',
                    'lighting': 'natural',
                    'therapeutic_elements': 'nature exposure, fresh air',
                    'weather_dependent': 'yes'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 100},
                    {'domain_code': 'physical', 'strength': 40},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'emotional', 'strength': 90},
                    {'domain_code': 'spiritual_existential', 'strength': 80}
                ]
            },
            {
                'code': 'ind_healthcare_maternity_ward',
                'name': 'Maternity Ward',
                'description': 'A specialized hospital unit devoted to caring for women during childbirth and postpartum recovery, as well as their newborn babies. Features specialized equipment for labor, delivery, and infant care.',
                'is_indoor': True,
                'primary_category': 'healthcare',
                'typical_space_size': 'large',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['labor and delivery rooms', 'birthing beds', 'fetal monitors', 'infant care stations', 'nursing support areas'],
                    'typical_noise_level': 'moderate',
                    'lighting': 'adjustable',
                    'emotional_atmosphere': 'mix of excitement and tension',
                    'specialized_staff': 'obstetricians, midwives, neonatal nurses'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'emotional', 'strength': 90},
                    {'domain_code': 'physical', 'strength': -30},
                    {'domain_code': 'reflective', 'strength': 50},
                    {'domain_code': 'spiritual_existential', 'strength': 70}
                ]
            }
        ]

    def get_cultural_environments(self):
        return [
            {
                'code': 'ind_cultural_museum',
                'name': 'Museum',
                'description': 'An indoor space dedicated to the preservation and exhibition of artistic, cultural, historical, or scientific artifacts. Museums provide a quiet, contemplative environment for learning and appreciation.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['exhibits', 'informational displays', 'guided tours', 'gift shop', 'quiet spaces'],
                    'typical_noise_level': 'low',
                    'typical_lighting': 'controlled',
                    'accessibility': 'high',
                    'formality': 'medium'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 50},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'physical', 'strength': -30}
                ]
            },
            {
                'code': 'ind_cultural_artgallery',
                'name': 'Art Gallery',
                'description': 'A dedicated indoor space for displaying visual art, primarily paintings, sculptures, photographs, and other aesthetic creations. Galleries provide a focused environment for artistic appreciation and contemplation.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['artwork displays', 'white spaces', 'benches', 'mood lighting', 'guided tours'],
                    'typical_noise_level': 'very low',
                    'typical_lighting': 'carefully designed',
                    'accessibility': 'high',
                    'formality': 'medium-high'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'intellectual', 'strength': 70},
                    {'domain_code': 'emotional', 'strength': 60},
                    {'domain_code': 'physical', 'strength': -40}
                ]
            },
            {
                'code': 'ind_cultural_theater',
                'name': 'Theater',
                'description': 'An indoor venue designed for live performances such as plays, musicals, dance, or other staged productions. Theaters feature seating arrangements focused on a central stage or performance space.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'large',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['stage', 'audience seating', 'lighting systems', 'acoustics', 'lobby'],
                    'typical_noise_level': 'variable',
                    'typical_lighting': 'controlled',
                    'accessibility': 'medium',
                    'formality': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'physical', 'strength': -10}
                ]
            },
            {
                'code': 'ind_cultural_concerthall',
                'name': 'Concert Hall',
                'description': 'A specialized indoor venue designed for musical performances, featuring exceptional acoustics and seating oriented toward a central stage. Concert halls provide an immersive audio experience for audience members.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'large',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['stage', 'orchestra pit', 'superior acoustics', 'tiered seating', 'foyer'],
                    'typical_noise_level': 'controlled',
                    'typical_lighting': 'adjustable',
                    'accessibility': 'medium',
                    'formality': 'very high'
                },
                'domain_relationships': [
                    {'domain_code': 'emotional', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'physical', 'strength': -20}
                ]
            },
            {
                'code': 'ind_cultural_library',
                'name': 'Library',
                'description': 'An indoor space dedicated to the collection and preservation of books, periodicals, and other media. Libraries provide quiet environments for reading, research, study, and community programs.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'large',
                'typical_privacy_level': 50,
                'archetype_attributes': {
                    'common_features': ['bookshelves', 'reading areas', 'study tables', 'computer stations', 'reference desk'],
                    'typical_noise_level': 'very low',
                    'typical_lighting': 'moderate',
                    'accessibility': 'high',
                    'formality': 'medium'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'emotional', 'strength': 40},
                    {'domain_code': 'physical', 'strength': -50}
                ]
            },
            {
                'code': 'ind_cultural_cinemacomplex',
                'name': 'Cinema Complex',
                'description': 'An indoor facility with multiple screening rooms for showing films. Cinema complexes include comfortable seating, large screens, and advanced audio systems for immersive viewing experiences.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['screening rooms', 'concession stands', 'tiered seating', 'surround sound', 'lobby'],
                    'typical_noise_level': 'variable',
                    'typical_lighting': 'dim',
                    'accessibility': 'medium-high',
                    'formality': 'low'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'physical', 'strength': -60}
                ]
            },
            {
                'code': 'ind_cultural_artisanworkshop',
                'name': 'Artisan Workshop',
                'description': 'An indoor space equipped for creating handcrafted work in traditional arts, crafts, or trades. Workshops typically include specialized tools, materials, and workstations for various creative practices.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['workbenches', 'specialized tools', 'storage', 'materials', 'display area'],
                    'typical_noise_level': 'medium',
                    'typical_lighting': 'bright',
                    'accessibility': 'medium',
                    'formality': 'low'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'productive_practical', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'physical', 'strength': 50},
                    {'domain_code': 'social', 'strength': -10}
                ]
            },
            {
                'code': 'ind_cultural_dancestudio',
                'name': 'Dance Studio',
                'description': 'An indoor space designed specifically for dance practice, training, and rehearsal. Dance studios feature appropriate flooring, mirrors, barres, and sound systems to support various dance forms.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['sprung floors', 'wall mirrors', 'barres', 'sound system', 'changing areas'],
                    'typical_noise_level': 'medium',
                    'typical_lighting': 'bright',
                    'accessibility': 'medium',
                    'formality': 'medium'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 90},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'intellectual', 'strength': 20}
                ]
            },
            {
                'code': 'ind_cultural_recordingstudio',
                'name': 'Recording Studio',
                'description': 'A specially designed indoor space for recording audio with controlled acoustics, sound insulation, and professional recording equipment. Recording studios provide isolated environments for music production, voiceover, and other audio work.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'small',
                'typical_privacy_level': 90,
                'archetype_attributes': {
                    'common_features': ['sound booth', 'control room', 'soundproofing', 'microphones', 'monitors'],
                    'typical_noise_level': 'very low',
                    'typical_lighting': 'adjustable',
                    'accessibility': 'medium',
                    'formality': 'medium-high'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'productive_practical', 'strength': 80},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 50},
                    {'domain_code': 'social', 'strength': -20}
                ]
            },
            {
                'code': 'ind_cultural_culinaryarts',
                'name': 'Culinary Arts Center',
                'description': 'An indoor facility dedicated to cooking education, demonstrations, and cultural food experiences. Culinary centers include cooking stations, specialized equipment, and areas for instruction and tasting.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['cooking stations', 'demonstration area', 'specialized equipment', 'pantry', 'dining area'],
                    'typical_noise_level': 'medium-high',
                    'typical_lighting': 'bright',
                    'accessibility': 'medium',
                    'formality': 'medium'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 90},
                    {'domain_code': 'productive_practical', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 50},
                    {'domain_code': 'intellectual', 'strength': 40}
                ]
            },
            {
                'code': 'out_cultural_amphitheater',
                'name': 'Outdoor Amphitheater',
                'description': 'An open-air venue with a central performance space surrounded by tiered seating. Amphitheaters are designed for theatrical performances, concerts, and other cultural events in natural settings.',
                'is_indoor': False,
                'primary_category': 'cultural',
                'typical_space_size': 'large',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['stage', 'tiered seating', 'natural acoustics', 'open sky', 'surrounding landscape'],
                    'typical_noise_level': 'variable',
                    'typical_lighting': 'natural with evening lighting',
                    'accessibility': 'medium',
                    'formality': 'medium'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 90},
                    {'domain_code': 'emotional', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 40},
                    {'domain_code': 'exploratory_adventurous', 'strength': 30}
                ]
            },
            {
                'code': 'out_cultural_heritage',
                'name': 'Heritage Site',
                'description': 'An outdoor location of historical or cultural significance, often preserved as a landmark or monument. Heritage sites include ruins, historic buildings, archaeological sites, and culturally important landscapes.',
                'is_indoor': False,
                'primary_category': 'cultural',
                'typical_space_size': 'large',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['historic structures', 'informational plaques', 'guided tours', 'preserved artifacts', 'historical landscaping'],
                    'typical_noise_level': 'low',
                    'typical_lighting': 'natural',
                    'accessibility': 'variable',
                    'formality': 'medium-high'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'exploratory_adventurous', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 40}
                ]
            },
            {
                'code': 'out_cultural_sculpture',
                'name': 'Sculpture Garden',
                'description': 'An outdoor area dedicated to displaying three-dimensional artworks in a landscaped setting. Sculpture gardens combine artistic appreciation with natural elements for a multisensory experience.',
                'is_indoor': False,
                'primary_category': 'cultural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['sculptures', 'pathways', 'seating areas', 'landscaping', 'informational displays'],
                    'typical_noise_level': 'low',
                    'typical_lighting': 'natural with evening accent lighting',
                    'accessibility': 'high',
                    'formality': 'medium'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'emotional', 'strength': 60},
                    {'domain_code': 'social', 'strength': 40}
                ]
            },
            {
                'code': 'out_cultural_festival',
                'name': 'Festival Grounds',
                'description': 'Temporary or permanent outdoor spaces designed for cultural festivals, musical events, and community celebrations. Festival grounds typically accommodate large crowds with multiple activity areas.',
                'is_indoor': False,
                'primary_category': 'cultural',
                'typical_space_size': 'vast',
                'typical_privacy_level': 0,
                'archetype_attributes': {
                    'common_features': ['stages', 'vendor booths', 'food areas', 'portable facilities', 'open gathering spaces'],
                    'typical_noise_level': 'high',
                    'typical_lighting': 'natural with evening lighting systems',
                    'accessibility': 'medium',
                    'formality': 'low'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 90},
                    {'domain_code': 'emotional', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 70},
                    {'domain_code': 'leisure_recreational', 'strength': 90}
                ]
            },
            {
                'code': 'ind_cultural_arthouse',
                'name': 'Art House Cinema',
                'description': 'A specialized indoor cinema focusing on independent, foreign, documentary, or experimental films. Art house cinemas provide spaces for viewing films outside mainstream commercial releases.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['intimate screening rooms', 'discussion spaces', 'cafe area', 'film posters', 'curated programming'],
                    'typical_noise_level': 'low',
                    'typical_lighting': 'dim',
                    'accessibility': 'medium',
                    'formality': 'medium'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'emotional', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'social', 'strength': 40}
                ]
            },
            {
                'code': 'ind_cultural_operahouse',
                'name': 'Opera House',
                'description': 'A grand indoor venue specifically designed for operatic performances, featuring exceptional acoustics, elaborate stage facilities, and formal audience seating. Opera houses embody cultural tradition and artistic excellence.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['grand auditorium', 'ornate design', 'orchestra pit', 'private boxes', 'elaborate stage'],
                    'typical_noise_level': 'controlled',
                    'typical_lighting': 'dramatic',
                    'accessibility': 'medium',
                    'formality': 'very high'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'spiritual_existential', 'strength': 60},
                    {'domain_code': 'physical', 'strength': -10}
                ]
            },
            {
                'code': 'out_cultural_openairdance',
                'name': 'Open-Air Dance Plaza',
                'description': 'A public outdoor space designed or adapted for community dance events, often featuring a central dance floor with surrounding seating areas. These spaces encourage spontaneous or organized social dancing.',
                'is_indoor': False,
                'primary_category': 'cultural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['dance floor', 'perimeter seating', 'bandstand', 'lighting', 'open accessible layout'],
                    'typical_noise_level': 'high',
                    'typical_lighting': 'natural with evening lighting',
                    'accessibility': 'high',
                    'formality': 'low'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'social', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': -20}
                ]
            },
            {
                'code': 'ind_cultural_historicalarchive',
                'name': 'Historical Archive',
                'description': 'A dedicated indoor facility for preserving, organizing, and providing access to historical documents, records, and artifacts. Archives maintain controlled environments to protect sensitive materials while enabling research.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 80,
                'archetype_attributes': {
                    'common_features': ['document storage', 'reading room', 'climate control', 'digital access stations', 'conservation areas'],
                    'typical_noise_level': 'very low',
                    'typical_lighting': 'controlled',
                    'accessibility': 'medium',
                    'formality': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'spiritual_existential', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 40},
                    {'domain_code': 'physical', 'strength': -60}
                ]
            },
            {
                'code': 'ind_cultural_musicschool',
                'name': 'Music School',
                'description': 'An educational facility dedicated to music instruction featuring practice rooms, classrooms, performance spaces, and specialized equipment. Music schools provide environments optimized for learning and practicing musical skills.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'large',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['practice rooms', 'soundproofing', 'instrument storage', 'recital halls', 'music libraries'],
                    'typical_noise_level': 'variable',
                    'typical_lighting': 'bright',
                    'accessibility': 'medium',
                    'formality': 'medium-high'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'intellectual', 'strength': 80},
                    {'domain_code': 'productive_practical', 'strength': 70},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 40}
                ]
            },
            {
                'code': 'ind_cultural_literarysalon',
                'name': 'Literary Salon',
                'description': 'An intimate indoor space dedicated to literary discussion, readings, and intellectual exchange. Literary salons provide comfortable seating, appropriate acoustics, and an atmosphere conducive to sharing and discussing written works.',
                'is_indoor': True,
                'primary_category': 'cultural',
                'typical_space_size': 'small',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['comfortable seating', 'bookshelves', 'reading areas', 'coffee service', 'intimate lighting'],
                    'typical_noise_level': 'low',
                    'typical_lighting': 'warm',
                    'accessibility': 'medium',
                    'formality': 'medium'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'emotional', 'strength': 60}
                ]
            }
        ]

    def get_transportation_environments(self):
        return [
            {
                'code': 'ind_trans_train',
                'name': 'Passenger Train',
                'description': 'An interior environment of a passenger train carriage, typically featuring rows of seats, windows with changing scenery, and limited movement space. These environments combine social proximity with transient communities of travelers.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['seats', 'windows', 'luggage racks', 'tray tables', 'restrooms'],
                    'typical_noise_level': 'moderate',
                    'movement_constraints': 'significant',
                    'connectivity': 'variable',
                    'typical_duration': '30 minutes to several hours'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'intellectual', 'strength': 50},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'physical', 'strength': -30},
                    {'domain_code': 'creative_writing', 'strength': 50}
                ]
            },
            
            {
                'code': 'ind_trans_subway',
                'name': 'Subway/Metro Train',
                'description': 'The interior of an urban rapid transit vehicle, characterized by limited space, standing areas with handholds, frequent stops, and a diverse mix of passengers. This environment combines brief travel times with high passenger density and urban connectivity.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'medium',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['seats', 'standing areas', 'handrails', 'advertisements', 'route maps'],
                    'typical_noise_level': 'high',
                    'movement_constraints': 'significant',
                    'connectivity': 'poor to none',
                    'typical_duration': '5 to 30 minutes'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'intellectual', 'strength': 20},
                    {'domain_code': 'social', 'strength': -10},
                    {'domain_code': 'physical', 'strength': -50},
                    {'domain_code': 'creative_observation', 'strength': 40}
                ]
            },
            
            {
                'code': 'ind_trans_bus',
                'name': 'Public Bus',
                'description': 'Interior of a public transportation bus with rows of seats, standing areas, and windows viewing urban or suburban landscapes. These spaces feature varied occupancy patterns, frequent stops, and diverse passenger interactions.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'small',
                'typical_privacy_level': 15,
                'archetype_attributes': {
                    'common_features': ['seats', 'stop request buttons', 'advertisements', 'handrails', 'limited movement space'],
                    'typical_noise_level': 'moderate to high',
                    'movement_constraints': 'significant',
                    'connectivity': 'variable',
                    'typical_duration': '10 to 45 minutes'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'intellectual', 'strength': 30},
                    {'domain_code': 'social', 'strength': 10},
                    {'domain_code': 'physical', 'strength': -40},
                    {'domain_code': 'creative_observation', 'strength': 50}
                ]
            },
            
            {
                'code': 'ind_trans_car',
                'name': 'Private Car',
                'description': 'The interior cabin of a personal automobile, providing a private, controlled environment while traveling. This space offers personalized temperature, sound, and seating preferences with limited movement but high control over the immediate environment.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'tiny',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['adjustable seats', 'entertainment system', 'climate control', 'personal storage', 'seat belts'],
                    'typical_noise_level': 'low to moderate',
                    'movement_constraints': 'high',
                    'connectivity': 'good',
                    'typical_duration': '15 minutes to several hours'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'intel_audio', 'strength': 80},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'physical', 'strength': -70},
                    {'domain_code': 'creative_auditory', 'strength': 60}
                ]
            },
            
            {
                'code': 'ind_trans_rideshare',
                'name': 'Rideshare Vehicle',
                'description': 'The interior of a hired car service (taxi, Uber, Lyft, etc.) with a driver and potentially other passengers. This environment combines private transportation with varying levels of social interaction and transient occupancy.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'tiny',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['seats', 'seatbelts', 'driver interaction', 'shared space', 'urban connectivity'],
                    'typical_noise_level': 'low to moderate',
                    'movement_constraints': 'high',
                    'connectivity': 'good',
                    'typical_duration': '10 to 45 minutes'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'intellectual', 'strength': 20},
                    {'domain_code': 'physical', 'strength': -80},
                    {'domain_code': 'creative', 'strength': 20}
                ]
            },
            
            {
                'code': 'ind_trans_airport',
                'name': 'Airport Terminal',
                'description': 'A large indoor facility for air travelers featuring waiting areas, security checkpoints, retail spaces, and boarding gates. These environments combine transient occupancy with diverse activities and varying levels of privacy and stimulation.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'vast',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['seating areas', 'information displays', 'security checkpoints', 'retail outlets', 'food services'],
                    'typical_noise_level': 'moderate to high',
                    'movement_constraints': 'moderate',
                    'connectivity': 'excellent',
                    'typical_duration': '1 to 4 hours'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'intellectual', 'strength': 60},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'phys_chill', 'strength': 40},
                    {'domain_code': 'creative_observation', 'strength': 70}
                ]
            },
            
            {
                'code': 'ind_trans_airplane',
                'name': 'Airplane Cabin',
                'description': 'The pressurized passenger area of a commercial aircraft, featuring rows of seats, limited movement space, and shared facilities. This environment combines social proximity with physical constraints and unique sensory conditions.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['seats', 'overhead bins', 'food service', 'windows', 'entertainment systems'],
                    'typical_noise_level': 'moderate',
                    'movement_constraints': 'very high',
                    'connectivity': 'limited to none',
                    'typical_duration': '1 to 12+ hours',
                    'atmospheric_conditions': 'pressurized, dry'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'intellectual', 'strength': 70},
                    {'domain_code': 'social', 'strength': 10},
                    {'domain_code': 'physical', 'strength': -80},
                    {'domain_code': 'creative_writing', 'strength': 50}
                ]
            },
            
            {
                'code': 'ind_trans_cruise_ship',
                'name': 'Cruise Ship',
                'description': 'The interior of a passenger cruise vessel, featuring diverse spaces from cabins to entertainment venues. This floating environment combines multiple types of spaces for varied activities while traveling over water.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'large',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['cabins', 'dining areas', 'entertainment venues', 'deck access', 'shops'],
                    'typical_noise_level': 'variable',
                    'movement_constraints': 'moderate',
                    'connectivity': 'variable',
                    'typical_duration': 'days to weeks',
                    'motion_characteristics': 'gentle rocking'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'intellectual', 'strength': 60}
                ]
            },
            
            {
                'code': 'ind_trans_ferry',
                'name': 'Passenger Ferry',
                'description': 'The interior spaces of a water vessel designed for transporting people across bodies of water. Typically featuring seating areas, viewing decks, and basic amenities for relatively short journeys.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'medium',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['seating areas', 'viewing windows', 'small cafe', 'restrooms', 'occasional outdoor deck access'],
                    'typical_noise_level': 'moderate',
                    'movement_constraints': 'moderate',
                    'connectivity': 'variable',
                    'typical_duration': '20 minutes to a few hours',
                    'motion_characteristics': 'noticeable rocking'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'physical', 'strength': 20},
                    {'domain_code': 'creative_observation', 'strength': 70},
                    {'domain_code': 'intellectual', 'strength': 50}
                ]
            },
            
            {
                'code': 'ind_trans_train_station',
                'name': 'Train Station',
                'description': 'A facility designed for train arrivals, departures, and passenger services. These spaces range from historic architectural showcases to utilitarian transit hubs, combining waiting areas with retail and information services.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'large',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['waiting areas', 'ticket counters', 'information boards', 'retail shops', 'food vendors'],
                    'typical_noise_level': 'high',
                    'movement_constraints': 'low',
                    'connectivity': 'excellent',
                    'typical_duration': '15 minutes to 1 hour',
                    'historical_significance': 'often present'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'phys_chill', 'strength': 50},
                    {'domain_code': 'creative_observation', 'strength': 80},
                    {'domain_code': 'intellectual', 'strength': 40}
                ]
            },
            
            {
                'code': 'ind_trans_bus_station',
                'name': 'Bus Terminal',
                'description': 'A facility for bus arrivals, departures, and passenger services, typically featuring waiting areas, ticketing services, and basic amenities. These environments serve as connection points for local, regional, and long-distance bus travel.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'medium',
                'typical_privacy_level': 15,
                'archetype_attributes': {
                    'common_features': ['waiting areas', 'ticket counters', 'departure gates', 'restrooms', 'small shops'],
                    'typical_noise_level': 'moderate to high',
                    'movement_constraints': 'low',
                    'connectivity': 'good',
                    'typical_duration': '15 minutes to 1 hour'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'physical', 'strength': 30},
                    {'domain_code': 'creative_observation', 'strength': 60},
                    {'domain_code': 'intellectual', 'strength': 30}
                ]
            },
            
            {
                'code': 'out_trans_bikepath',
                'name': 'Dedicated Bike Path',
                'description': 'A designated outdoor route specifically designed for bicycle travel, separated from motor vehicle traffic. These paths provide safe, often scenic routes for cycling through urban or natural environments.',
                'is_indoor': False,
                'primary_category': 'transportation',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['paved surfaces', 'directional signage', 'occasional benches', 'natural surroundings', 'distance markers'],
                    'typical_noise_level': 'low to moderate',
                    'movement_constraints': 'low',
                    'connectivity': 'variable',
                    'typical_duration': '15 minutes to several hours',
                    'weather_exposure': 'complete'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'creative_observation', 'strength': 70},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'intellectual', 'strength': 50}
                ]
            },
            
            {
                'code': 'out_trans_sidewalk',
                'name': 'Urban Sidewalk',
                'description': 'Pedestrian pathways alongside roads in urban and suburban environments. These interconnected walkways feature varied stimuli, social encounters, and urban infrastructure while providing dedicated space for walking.',
                'is_indoor': False,
                'primary_category': 'transportation',
                'typical_space_size': 'medium',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['paved surfaces', 'street furniture', 'crossing signals', 'storefront adjacency', 'street trees'],
                    'typical_noise_level': 'moderate to high',
                    'movement_constraints': 'low',
                    'connectivity': 'excellent',
                    'typical_duration': '5 minutes to 1 hour',
                    'weather_exposure': 'complete'
                },
                'domain_relationships': [
                    {'domain_code': 'phys_chill', 'strength': 80},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'creative_observation', 'strength': 70},
                    {'domain_code': 'intellectual', 'strength': 30}
                ]
            },
            
            {
                'code': 'out_trans_highway_rest',
                'name': 'Highway Rest Area',
                'description': 'Roadside facilities providing travelers with places to park, rest, and access basic services. These areas typically feature parking, restrooms, picnic areas, and informational displays in a primarily outdoor setting.',
                'is_indoor': False,
                'primary_category': 'transportation',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['parking areas', 'picnic tables', 'restroom buildings', 'informational kiosks', 'trash receptacles'],
                    'typical_noise_level': 'moderate',
                    'movement_constraints': 'low',
                    'connectivity': 'variable',
                    'typical_duration': '15 to 45 minutes',
                    'weather_exposure': 'significant with shelter options'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'physical', 'strength': 40},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'creative', 'strength': 40},
                    {'domain_code': 'intellectual', 'strength': 30}
                ]
            },
            
            {
                'code': 'ind_trans_cable_car',
                'name': 'Cable Car/Gondola',
                'description': 'Enclosed aerial transportation cabins suspended from cables that transport passengers over terrain or between buildings. These unique environments combine movement constraints with exceptional views and distinctive sensory experiences.',
                'is_indoor': True,
                'primary_category': 'transportation',
                'typical_space_size': 'tiny',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['viewing windows', 'bench seating', 'handholds', 'informational signage', 'height exposure'],
                    'typical_noise_level': 'low',
                    'movement_constraints': 'very high',
                    'connectivity': 'none',
                    'typical_duration': '5 to 30 minutes',
                    'motion_characteristics': 'swaying, occasional bumps'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'creative_observation', 'strength': 90},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'physical', 'strength': -60},
                    {'domain_code': 'intellectual', 'strength': 40}
                ]
            },
            
            {
                'code': 'out_trans_ferry_deck',
                'name': 'Ferry Outdoor Deck',
                'description': 'The open-air upper or side deck areas of passenger ferries, providing views, fresh air, and outdoor seating while in transit. These spaces combine transportation with elements of recreational outdoor environments.',
                'is_indoor': False,
                'primary_category': 'transportation',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['railings', 'bench seating', 'viewing areas', 'wind exposure', 'directional signage'],
                    'typical_noise_level': 'moderate to high',
                    'movement_constraints': 'moderate',
                    'connectivity': 'poor',
                    'typical_duration': '20 minutes to a few hours',
                    'weather_exposure': 'complete',
                    'motion_characteristics': 'noticeable rocking'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'creative_observation', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 30},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'intellectual', 'strength': 50}
                ]
            }
        ]

    def get_natural_environments(self):
        return [
            {
                'code': 'out_nat_forest',
                'name': 'Forest',
                'description': 'A wooded area with a variety of trees, plants, and wildlife. Offers natural shade, biodiversity, and opportunities for hiking, wildlife observation, and nature connection.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'vast',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['trees', 'trails', 'wildlife', 'undergrowth', 'streams'],
                    'typical_noise_level': 'low',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'biodiversity_level': 'high',
                    'terrain_difficulty': 'moderate'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'exploratory_adventurous', 'strength': 85},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'spiritual_existential', 'strength': 80},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'intellectual', 'strength': 50}
                ]
            },
            {
                'code': 'out_nat_beach',
                'name': 'Beach',
                'description': 'A sandy or rocky shoreline along a body of water (ocean, sea, lake, or river). Provides open space, water access, and sensory experiences including the sounds of waves and the feeling of sand underfoot.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'vast',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['sand', 'water', 'waves', 'horizon view', 'sunshine'],
                    'typical_noise_level': 'moderate',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'water_proximity': 'immediate',
                    'terrain_difficulty': 'low'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 85},
                    {'domain_code': 'social', 'strength': 75},
                    {'domain_code': 'leisure_recreational', 'strength': 95},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'spiritual_existential', 'strength': 80},
                    {'domain_code': 'emotional', 'strength': 85}
                ]
            },
            {
                'code': 'out_nat_mountain',
                'name': 'Mountain',
                'description': 'Elevated terrain characterized by steep slopes, peaks, and valleys. Offers challenging physical activities, breathtaking views, and a sense of accomplishment when summits are reached.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'vast',
                'typical_privacy_level': 80,
                'archetype_attributes': {
                    'common_features': ['elevation', 'peaks', 'trails', 'rocks', 'scenic views'],
                    'typical_noise_level': 'very low',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'elevation_changes': 'significant',
                    'terrain_difficulty': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'exploratory_adventurous', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'spiritual_existential', 'strength': 90},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'creative', 'strength': 75},
                    {'domain_code': 'emotional', 'strength': 80}
                ]
            },
            {
                'code': 'out_nat_meadow',
                'name': 'Meadow',
                'description': 'An open grassland area with wildflowers and low vegetation. Provides wide-open space, contact with diverse plant life, and opportunities for relaxation and nature observation.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'large',
                'typical_privacy_level': 50,
                'archetype_attributes': {
                    'common_features': ['grass', 'wildflowers', 'open sky', 'insects', 'gentle terrain'],
                    'typical_noise_level': 'low',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'biodiversity_level': 'moderate',
                    'terrain_difficulty': 'low'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'leisure_recreational', 'strength': 75},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'spiritual_existential', 'strength': 75},
                    {'domain_code': 'social', 'strength': 65},
                    {'domain_code': 'emotional', 'strength': 80}
                ]
            },
            {
                'code': 'out_nat_desert',
                'name': 'Desert',
                'description': 'Arid landscape with minimal vegetation, characterized by sand, rocks, and extreme temperature variations. Offers unique geological features, wide-open spaces, and clarity of night skies.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'vast',
                'typical_privacy_level': 90,
                'archetype_attributes': {
                    'common_features': ['sand', 'cacti', 'rocks', 'minimal vegetation', 'extreme temperatures'],
                    'typical_noise_level': 'very low',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'water_availability': 'very low',
                    'terrain_difficulty': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'exploratory_adventurous', 'strength': 90},
                    {'domain_code': 'spiritual_existential', 'strength': 95},
                    {'domain_code': 'reflective', 'strength': 95},
                    {'domain_code': 'creative', 'strength': 75},
                    {'domain_code': 'physical', 'strength': 70},
                    {'domain_code': 'social', 'strength': 20},
                    {'domain_code': 'intellectual', 'strength': 60}
                ]
            },
            {
                'code': 'out_nat_wetland',
                'name': 'Wetland',
                'description': 'Areas saturated with water, such as marshes, swamps, and bogs. Rich in biodiversity, these environments offer unique plant and animal observation opportunities and peaceful water features.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'large',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['water', 'reeds', 'diverse wildlife', 'mud', 'aquatic plants'],
                    'typical_noise_level': 'moderate',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'biodiversity_level': 'very high',
                    'terrain_difficulty': 'moderate'
                },
                'domain_relationships': [
                    {'domain_code': 'exploratory_adventurous', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 75},
                    {'domain_code': 'intellectual', 'strength': 85},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'spiritual_existential', 'strength': 65},
                    {'domain_code': 'social', 'strength': 30}
                ]
            },
            {
                'code': 'out_nat_river',
                'name': 'River',
                'description': 'Flowing body of water with surrounding riverbanks and shoreline. Provides opportunities for water activities, wildlife observation, and the soothing sounds of flowing water.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'large',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['flowing water', 'riverbanks', 'fish', 'river stones', 'riparian vegetation'],
                    'typical_noise_level': 'moderate',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'water_proximity': 'immediate',
                    'terrain_difficulty': 'variable'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 85},
                    {'domain_code': 'exploratory_adventurous', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'leisure_recreational', 'strength': 85},
                    {'domain_code': 'creative', 'strength': 75},
                    {'domain_code': 'spiritual_existential', 'strength': 80},
                    {'domain_code': 'social', 'strength': 60}
                ]
            },
            {
                'code': 'out_nat_lake',
                'name': 'Lake',
                'description': 'Still body of water surrounded by shoreline and natural features. Offers calm water activities, scenic views, and a peaceful atmosphere for reflection and recreation.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'vast',
                'typical_privacy_level': 50,
                'archetype_attributes': {
                    'common_features': ['still water', 'shoreline', 'aquatic life', 'surrounding vegetation', 'bird activity'],
                    'typical_noise_level': 'low',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'water_proximity': 'immediate',
                    'terrain_difficulty': 'low'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 95},
                    {'domain_code': 'physical', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 75},
                    {'domain_code': 'spiritual_existential', 'strength': 85},
                    {'domain_code': 'emotional', 'strength': 80}
                ]
            },
            {
                'code': 'out_nat_cliffside',
                'name': 'Cliffside',
                'description': 'Steep rock face overlooking the surrounding landscape, often with dramatic views. Provides perspectives on vast expanses, challenging activities, and a sense of exposure to the elements.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'large',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['steep drop', 'rocks', 'panoramic views', 'wind exposure', 'sparse vegetation'],
                    'typical_noise_level': 'low to moderate',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'elevation': 'high',
                    'terrain_difficulty': 'very high'
                },
                'domain_relationships': [
                    {'domain_code': 'exploratory_adventurous', 'strength': 95},
                    {'domain_code': 'physical', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'spiritual_existential', 'strength': 85},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'emotional', 'strength': 85},
                    {'domain_code': 'social', 'strength': 20}
                ]
            },
            {
                'code': 'out_nat_garden',
                'name': 'Garden',
                'description': 'Cultivated outdoor space with designed plantings, paths, and features. Combines natural elements with human design to create a space for relaxation, observation, and engagement with plants.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['plants', 'flowers', 'paths', 'benches', 'designed spaces'],
                    'typical_noise_level': 'low',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'biodiversity_level': 'high',
                    'terrain_difficulty': 'very low'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 85},
                    {'domain_code': 'productive_practical', 'strength': 80},
                    {'domain_code': 'leisure_recreational', 'strength': 75},
                    {'domain_code': 'spiritual_existential', 'strength': 75},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'social', 'strength': 50}
                ]
            },
            {
                'code': 'out_nat_tundra',
                'name': 'Tundra',
                'description': 'Cold, treeless plain with permafrost underground. Characterized by vast open spaces, extreme conditions, and unique flora and fauna adapted to harsh environments.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'vast',
                'typical_privacy_level': 95,
                'archetype_attributes': {
                    'common_features': ['permafrost', 'low vegetation', 'extreme weather', 'open expanses', 'sparse wildlife'],
                    'typical_noise_level': 'very low',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'temperature_range': 'very cold',
                    'terrain_difficulty': 'moderate to high'
                },
                'domain_relationships': [
                    {'domain_code': 'exploratory_adventurous', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 85},
                    {'domain_code': 'spiritual_existential', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 75},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'intellectual', 'strength': 70},
                    {'domain_code': 'social', 'strength': 10}
                ]
            },
            {
                'code': 'out_nat_savanna',
                'name': 'Savanna',
                'description': 'Grassland ecosystem with scattered trees and diverse wildlife. Provides open vistas, wildlife observation opportunities, and connection to primal landscapes.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'vast',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['tall grass', 'scattered trees', 'diverse wildlife', 'open expanses', 'seasonal water sources'],
                    'typical_noise_level': 'low',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'biodiversity_level': 'high',
                    'terrain_difficulty': 'low'
                },
                'domain_relationships': [
                    {'domain_code': 'exploratory_adventurous', 'strength': 85},
                    {'domain_code': 'physical', 'strength': 75},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'spiritual_existential', 'strength': 85},
                    {'domain_code': 'intellectual', 'strength': 75},
                    {'domain_code': 'social', 'strength': 30}
                ]
            },
            {
                'code': 'ind_nat_conservatory',
                'name': 'Conservatory',
                'description': 'Indoor space with glass walls and ceiling designed to house and display plants. Provides a controlled natural environment for plant observation, learning, and relaxation in all weather conditions.',
                'is_indoor': True,
                'primary_category': 'natural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['glass structure', 'exotic plants', 'controlled climate', 'walking paths', 'informational displays'],
                    'typical_noise_level': 'low',
                    'weather_dependent': False,
                    'seasonal_variations': False,
                    'biodiversity_level': 'high',
                    'terrain_difficulty': 'very low'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 85},
                    {'domain_code': 'intellectual', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 75},
                    {'domain_code': 'leisure_recreational', 'strength': 70},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'physical', 'strength': 30},
                    {'domain_code': 'spiritual_existential', 'strength': 65}
                ]
            },
            {
                'code': 'ind_nat_atrium',
                'name': 'Indoor Atrium',
                'description': 'Open central space within a building, often featuring plants, water features, and natural light. Combines architectural elements with natural features to create a transitional space between indoors and outdoors.',
                'is_indoor': True,
                'primary_category': 'natural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['plants', 'natural light', 'indoor fountains', 'seating areas', 'architectural elements'],
                    'typical_noise_level': 'moderate',
                    'weather_dependent': False,
                    'seasonal_variations': False,
                    'ambient_environment': 'controlled',
                    'terrain_difficulty': 'very low'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'social', 'strength': 75},
                    {'domain_code': 'creative', 'strength': 65},
                    {'domain_code': 'leisure_recreational', 'strength': 60},
                    {'domain_code': 'spiritual_existential', 'strength': 55},
                    {'domain_code': 'physical', 'strength': 20},
                    {'domain_code': 'intellectual', 'strength': 60}
                ]
            },
            {
                'code': 'ind_nat_botanical_garden',
                'name': 'Indoor Botanical Garden',
                'description': 'Large indoor facility dedicated to plant collection, cultivation, and display. Offers educational opportunities, diverse plant experiences, and calm natural spaces regardless of outside weather.',
                'is_indoor': True,
                'primary_category': 'natural',
                'typical_space_size': 'large',
                'typical_privacy_level': 25,
                'archetype_attributes': {
                    'common_features': ['diverse plants', 'themed sections', 'educational displays', 'controlled climate', 'walking paths'],
                    'typical_noise_level': 'low',
                    'weather_dependent': False,
                    'seasonal_variations': False,
                    'biodiversity_level': 'very high',
                    'terrain_difficulty': 'very low'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 85},
                    {'domain_code': 'creative', 'strength': 75},
                    {'domain_code': 'leisure_recreational', 'strength': 80},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'spiritual_existential', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 40}
                ]
            },
            {
                'code': 'ind_nat_aquarium',
                'name': 'Aquarium',
                'description': 'Indoor facility housing aquatic life in tanks and habitats. Provides opportunities to observe underwater ecosystems and marine life in a controlled environment with educational components.',
                'is_indoor': True,
                'primary_category': 'natural',
                'typical_space_size': 'large',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['water tanks', 'aquatic life', 'educational displays', 'dark ambient lighting', 'viewing areas'],
                    'typical_noise_level': 'low',
                    'weather_dependent': False,
                    'seasonal_variations': False,
                    'biodiversity_level': 'high',
                    'terrain_difficulty': 'very low'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 75},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'leisure_recreational', 'strength': 85},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'emotional', 'strength': 75},
                    {'domain_code': 'physical', 'strength': 20}
                ]
            },
            {
                'code': 'ind_nat_indoor_waterfall',
                'name': 'Indoor Waterfall Environment',
                'description': 'Built environment featuring artificial waterfalls, streams, and related natural elements. Combines moving water, plants, and designed spaces to create a sensory-rich natural experience indoors.',
                'is_indoor': True,
                'primary_category': 'natural',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['waterfall', 'flowing water', 'rocks', 'indoor plants', 'seating areas'],
                    'typical_noise_level': 'moderate',
                    'weather_dependent': False,
                    'seasonal_variations': False,
                    'humidity_level': 'high',
                    'terrain_difficulty': 'very low'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'emotional', 'strength': 85},
                    {'domain_code': 'creative', 'strength': 75},
                    {'domain_code': 'leisure_recreational', 'strength': 70},
                    {'domain_code': 'spiritual_existential', 'strength': 80},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'physical', 'strength': 20}
                ]
            },
            {
                'code': 'out_nat_volcanic_area',
                'name': 'Volcanic Area',
                'description': 'Landscape shaped by volcanic activity, featuring lava fields, geothermal features, and unique geology. Offers otherworldly scenery, dynamic natural processes, and educational opportunities about Earth\'s forces.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'large',
                'typical_privacy_level': 50,
                'archetype_attributes': {
                    'common_features': ['volcanic rock', 'steam vents', 'unusual geology', 'sparse vegetation', 'hot springs'],
                    'typical_noise_level': 'variable',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'air_quality': 'variable',
                    'terrain_difficulty': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'exploratory_adventurous', 'strength': 90},
                    {'domain_code': 'intellectual', 'strength': 85},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 85},
                    {'domain_code': 'spiritual_existential', 'strength': 75},
                    {'domain_code': 'physical', 'strength': 70},
                    {'domain_code': 'social', 'strength': 30}
                ]
            },
            {
                'code': 'out_nat_canyon',
                'name': 'Canyon',
                'description': 'Deep ravine between cliffs often carved by rivers over millennia. Provides dramatic vertical perspectives, challenging terrain, and exposure to geological history through visible rock layers.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'vast',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['steep walls', 'river bottom', 'exposed rock layers', 'echoes', 'microclimate'],
                    'typical_noise_level': 'low',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'geological_features': 'prominent',
                    'terrain_difficulty': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'exploratory_adventurous', 'strength': 95},
                    {'domain_code': 'physical', 'strength': 85},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'spiritual_existential', 'strength': 85},
                    {'domain_code': 'intellectual', 'strength': 75},
                    {'domain_code': 'social', 'strength': 30}
                ]
            },
            {
                'code': 'out_nat_coral_reef',
                'name': 'Coral Reef',
                'description': 'Underwater ecosystem built by colonies of coral polyps, teeming with marine life. Provides immersive aquatic experiences, colorful visual stimuli, and opportunities to observe complex ecological relationships.',
                'is_indoor': False,
                'primary_category': 'natural',
                'typical_space_size': 'large',
                'typical_privacy_level': 80,
                'archetype_attributes': {
                    'common_features': ['coral structures', 'diverse sea life', 'clear water', 'wave action', 'underwater currents'],
                    'typical_noise_level': 'low (underwater)',
                    'weather_dependent': True,
                    'seasonal_variations': True,
                    'biodiversity_level': 'extremely high',
                    'terrain_difficulty': 'moderate (requires swimming skills)'
                },
                'domain_relationships': [
                    {'domain_code': 'exploratory_adventurous', 'strength': 90},
                    {'domain_code': 'physical', 'strength': 80},
                    {'domain_code': 'intellectual', 'strength': 85},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'spiritual_existential', 'strength': 70},
                    {'domain_code': 'social', 'strength': 40}
                ]
            },
            {
                'code': 'ind_nat_terrarium',
                'name': 'Terrarium Room',
                'description': 'Space featuring multiple enclosed mini-ecosystems in glass containers. Provides opportunities to observe self-contained natural worlds and intricate ecological relationships at a small scale.',
                'is_indoor': True,
                'primary_category': 'natural',
                'typical_space_size': 'small',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['glass containers', 'miniature ecosystems', 'controlled environments', 'diverse plants', 'decorative elements'],
                    'typical_noise_level': 'very low',
                    'weather_dependent': False,
                    'seasonal_variations': False,
                    'environment_control': 'high',
                    'terrain_difficulty': 'none'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 85},
                    {'domain_code': 'intellectual', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'productive_practical', 'strength': 75},
                    {'domain_code': 'leisure_recreational', 'strength': 65},
                    {'domain_code': 'emotional', 'strength': 60},
                    {'domain_code': 'physical', 'strength': 10}
                ]
            }
        ]

    def get_recreational_environments(self):
        return [
            {
                'code': 'ind_rec_gym',
                'name': 'Fitness Gym',
                'description': 'A dedicated indoor facility with various exercise equipment, weight machines, cardio stations, and possibly group exercise rooms. Designed for physical conditioning, strength training, and general fitness activities.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'large',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['weight machines', 'cardio equipment', 'mirrors', 'fitness instructors', 'locker rooms', 'shower facilities'],
                    'typical_noise_level': 'moderate',
                    'membership_required': True,
                    'typical_hours': '6:00-22:00',
                    'air_conditioning': True
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': -30},
                    {'domain_code': 'creative', 'strength': -50}
                ]
            },
            {
                'code': 'ind_rec_yoga_studio',
                'name': 'Yoga Studio',
                'description': 'A tranquil indoor space designed for yoga practice, meditation, and mindful movement. Features cushioned flooring, minimal aesthetic, and a calming atmosphere conducive to focus and inner awareness.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['yoga mats', 'props', 'dim lighting', 'natural elements', 'sound system', 'changing areas'],
                    'typical_noise_level': 'very low',
                    'temperature': 'warm',
                    'scent': 'incense or essential oils',
                    'flooring': 'wood or cushioned'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 100},
                    {'domain_code': 'spiritual_existential', 'strength': 80},
                    {'domain_code': 'social', 'strength': 10}
                ]
            },
            {
                'code': 'ind_rec_bowling',
                'name': 'Bowling Alley',
                'description': 'An indoor recreational facility featuring lanes for bowling, automatic scoring systems, and rental equipment. Often includes arcade games, food service, and social areas designed for casual entertainment.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['bowling lanes', 'ball return systems', 'scoring screens', 'rental shoes', 'food service', 'arcade games'],
                    'typical_noise_level': 'high',
                    'lighting': 'variable, often dim with neon accents',
                    'seating': 'group arrangements',
                    'food_available': True
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'physical', 'strength': 50},
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': -70}
                ]
            },
            {
                'code': 'ind_rec_arcade',
                'name': 'Gaming Arcade',
                'description': 'An indoor entertainment center filled with video games, redemption games, and interactive gaming experiences. Designed for casual play, competition, and social interaction around digital entertainment.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['arcade machines', 'prize counters', 'multiplayer games', 'VR experiences', 'lighting effects', 'sound effects'],
                    'typical_noise_level': 'very high',
                    'lighting': 'dark with game illumination',
                    'stimulation_level': 'very high',
                    'food_available': True
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'intellectual', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': -80}
                ]
            },
            {
                'code': 'ind_rec_movie_theater',
                'name': 'Movie Theater',
                'description': 'An indoor venue designed for film viewing with large screens, immersive sound systems, and tiered seating. Offers a shared viewing experience in a controlled environment optimized for audiovisual entertainment.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['large screens', 'surround sound', 'tiered seating', 'concession stands', 'dim lighting', 'climate control'],
                    'typical_noise_level': 'low during films, high during transitions',
                    'comfort_level': 'designed for 2-3 hour sitting',
                    'food_available': True,
                    'social_expectation': 'quiet during performance'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 50},
                    {'domain_code': 'social', 'strength': 10}
                ]
            },
            {
                'code': 'ind_rec_pool_hall',
                'name': 'Billiards Club',
                'description': 'An indoor recreational space centered around billiards tables and sometimes featuring other table games like foosball or darts. Combines skill-based gaming with social interaction in a casual atmosphere.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['pool tables', 'cues', 'chalk', 'score boards', 'bar seating', 'ambient lighting'],
                    'typical_noise_level': 'moderate',
                    'lighting': 'focused on tables, dimmer surroundings',
                    'beverage_service': True,
                    'skill_level_variation': 'beginners to experts'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': 10}
                ]
            },
            {
                'code': 'ind_rec_indoor_climbing',
                'name': 'Indoor Climbing Gym',
                'description': 'An indoor facility with artificial climbing walls of various heights and difficulties, featuring holds, routes, and safety systems. Designed for sport climbing, bouldering, and training in a controlled environment.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['climbing walls', 'bouldering areas', 'safety equipment', 'chalk stations', 'route markings', 'training areas'],
                    'typical_noise_level': 'moderate',
                    'ceiling_height': 'very high',
                    'safety_equipment': 'required',
                    'instruction_available': True
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'exploratory_adventurous', 'strength': 80},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'emotional', 'strength': 50}
                ]
            },
            {
                'code': 'ind_rec_swimming_pool',
                'name': 'Indoor Swimming Pool',
                'description': 'A climate-controlled aquatic facility with one or more swimming pools designed for exercise, recreation, and instruction. May include lanes for lap swimming, diving areas, and shallow sections for casual use.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'large',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['swimming lanes', 'changing rooms', 'showers', 'lifeguard stations', 'pool equipment', 'temperature control'],
                    'typical_noise_level': 'high with acoustics',
                    'humidity': 'high',
                    'temperature': 'warm',
                    'chemical_presence': 'chlorine or similar',
                    'safety_rules': 'strictly enforced'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'leisure_recreational', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'social', 'strength': 30}
                ]
            },
            {
                'code': 'ind_rec_dance_studio',
                'name': 'Dance Studio',
                'description': 'A specialized indoor space for dance practice and instruction, featuring mirrors, barres, appropriate flooring, and sound systems. Designed for movement arts ranging from ballet to contemporary styles.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['wall mirrors', 'dance barres', 'sprung floors', 'sound system', 'changing areas', 'observation space'],
                    'typical_noise_level': 'moderate to high during practice',
                    'flooring': 'specialty dance surface',
                    'climate_control': 'important for physical activity',
                    'instructor_presence': 'common'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'social', 'strength': 60}
                ]
            },
            {
                'code': 'ind_rec_board_game_cafe',
                'name': 'Board Game Café',
                'description': 'A cozy indoor venue combining café service with an extensive library of board games, card games, and tabletop activities. Designed for social gaming experiences in a comfortable setting with food and beverage options.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['game library', 'large tables', 'comfortable seating', 'café counter', 'game instructions', 'demonstration areas'],
                    'typical_noise_level': 'moderate',
                    'lighting': 'well-lit for game play',
                    'food_service': True,
                    'stay_duration': 'extended',
                    'group_focused': True
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'intellectual', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 50}
                ]
            },
            {
                'code': 'ind_rec_trampoline_park',
                'name': 'Trampoline Park',
                'description': 'An indoor recreational facility featuring interconnected trampolines, foam pits, dodgeball courts, and other bounce-based activities. Designed for high-energy play, physical activity, and adventurous recreation.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'large',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['trampoline floors', 'foam pits', 'safety padding', 'activity zones', 'observation areas', 'safety monitors'],
                    'typical_noise_level': 'very high',
                    'ceiling_height': 'high',
                    'safety_waivers': 'required',
                    'physical_intensity': 'high',
                    'age_variation': 'typically appeals to youth and young adults'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'leisure_recreational', 'strength': 90},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': -80}
                ]
            },
            {
                'code': 'out_rec_playground',
                'name': 'Public Playground',
                'description': 'An outdoor recreational space designed primarily for children, featuring play equipment like swings, slides, climbing structures, and open areas. Promotes physical activity, social interaction, and imaginative play in a public setting.',
                'is_indoor': False,
                'primary_category': 'recreational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['play structures', 'swings', 'slides', 'climbing equipment', 'benches', 'safety surfacing'],
                    'typical_noise_level': 'high',
                    'weather_dependent': True,
                    'supervision_needed': 'for young children',
                    'accessibility': 'varies by facility',
                    'operating_hours': 'typically dawn to dusk'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 90},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': -30}
                ]
            },
            {
                'code': 'out_rec_skatepark',
                'name': 'Skate Park',
                'description': 'An outdoor facility designed specifically for skateboarding, BMX riding, and sometimes inline skating. Features ramps, rails, pipes, bowls, and other structures for performing tricks and developing skills in action sports.',
                'is_indoor': False,
                'primary_category': 'recreational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['ramps', 'half-pipes', 'grinding rails', 'bowls', 'flat surfaces', 'seating areas'],
                    'typical_noise_level': 'high',
                    'surface_material': 'concrete or wood',
                    'weather_dependent': True,
                    'skill_level_range': 'beginner to expert',
                    'community_culture': 'strong'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 90},
                    {'domain_code': 'exploratory_adventurous', 'strength': 100},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 60}
                ]
            },
            {
                'code': 'out_rec_sports_field',
                'name': 'Sports Field Complex',
                'description': 'An outdoor facility with multiple fields and courts for organized sports and casual play. May include soccer fields, baseball diamonds, tennis courts, and supporting amenities designed for team and individual sports activities.',
                'is_indoor': False,
                'primary_category': 'recreational',
                'typical_space_size': 'vast',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['marked fields', 'goals/nets', 'bleachers', 'restroom facilities', 'parking', 'lighting systems'],
                    'typical_noise_level': 'moderate to high during events',
                    'weather_dependent': True,
                    'maintenance_level': 'regular',
                    'multipurpose_capability': 'high',
                    'scheduling_system': 'often present'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'emotional', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': -40}
                ]
            },
            {
                'code': 'out_rec_beach',
                'name': 'Public Beach',
                'description': 'A recreational shoreline area where land meets a body of water, typically featuring sand or pebbles, swimming areas, and various amenities. Supports swimming, sunbathing, beach sports, and passive enjoyment of the natural water setting.',
                'is_indoor': False,
                'primary_category': 'recreational',
                'typical_space_size': 'vast',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['sandy shore', 'swimming areas', 'lifeguard stations', 'changing facilities', 'concession stands', 'shade structures'],
                    'typical_noise_level': 'moderate to high in popular areas',
                    'weather_dependent': True,
                    'natural_elements': 'strong presence',
                    'water_quality': 'variable',
                    'seasonal_variation': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 80},
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 60}
                ]
            },
            {
                'code': 'out_rec_golf_course',
                'name': 'Golf Course',
                'description': 'A large outdoor facility designed for playing golf, featuring manicured grass areas, hazards, putting greens, and typically 9 or 18 holes. Combines elements of sport, leisure, social interaction, and appreciation of landscaped natural settings.',
                'is_indoor': False,
                'primary_category': 'recreational',
                'typical_space_size': 'vast',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['fairways', 'greens', 'tee boxes', 'sand traps', 'water hazards', 'clubhouse'],
                    'typical_noise_level': 'low',
                    'etiquette_expectations': 'high',
                    'equipment_required': True,
                    'pace_expectations': 'moderate',
                    'environmental_impact': 'significant water and land use'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 70},
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 50}
                ]
            },
            {
                'code': 'out_rec_hiking_trail',
                'name': 'Hiking Trail System',
                'description': 'A network of outdoor paths designed for pedestrian use through natural environments, with varying degrees of development, markers, and amenities. Facilitates exercise, nature exploration, and scenic appreciation.',
                'is_indoor': False,
                'primary_category': 'recreational',
                'typical_space_size': 'vast',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['marked paths', 'trail signs', 'natural terrain', 'scenic views', 'rest areas', 'trailheads'],
                    'typical_noise_level': 'low',
                    'difficulty_range': 'easy to challenging',
                    'weather_dependent': True,
                    'natural_hazards': 'present',
                    'solitude_potential': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 90},
                    {'domain_code': 'exploratory_adventurous', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'spiritual_existential', 'strength': 70}
                ]
            },
            {
                'code': 'out_rec_dog_park',
                'name': 'Dog Park',
                'description': 'A designated outdoor area where dogs can exercise and play off-leash in a controlled environment under owner supervision. Features fencing, waste stations, water sources, and sometimes separate areas for different dog sizes or temperaments.',
                'is_indoor': False,
                'primary_category': 'recreational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['fenced areas', 'double-gate entry', 'waste stations', 'water fountains', 'shade structures', 'seating'],
                    'typical_noise_level': 'high',
                    'pet_focused': True,
                    'surface_variation': 'grass, dirt, or mulch',
                    'social_expectations': 'monitoring your dog',
                    'rules_structure': 'posted regulations'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 80},
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 20}
                ]
            },
            {
                'code': 'out_rec_water_park',
                'name': 'Outdoor Water Park',
                'description': 'A recreational facility featuring water-based attractions such as slides, wave pools, lazy rivers, and splash pads. Designed for aquatic play and cooling recreation during warm weather.',
                'is_indoor': False,
                'primary_category': 'recreational',
                'typical_space_size': 'large',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['water slides', 'wave pools', 'lazy rivers', 'splash areas', 'changing facilities', 'concession stands'],
                    'typical_noise_level': 'very high',
                    'seasonal_operation': True,
                    'weather_dependent': True,
                    'safety_staff': 'lifeguards present',
                    'age_variation': 'facilities for various ages'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 90},
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': -60}
                ]
            },
            {
                'code': 'out_rec_outdoor_fitness',
                'name': 'Outdoor Fitness Area',
                'description': 'A public exercise zone featuring weather-resistant equipment designed for strength training, flexibility, and cardiovascular workouts. Allows for free, accessible physical activity in an open-air environment.',
                'is_indoor': False,
                'primary_category': 'recreational',
                'typical_space_size': 'small',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['resistance equipment', 'pull-up bars', 'balance beams', 'instructional signage', 'durable surfaces', 'body-weight stations'],
                    'typical_noise_level': 'moderate',
                    'weather_dependent': True,
                    'accessibility': 'free and public',
                    'equipment_durability': 'designed for outdoor elements',
                    'user_guidance': 'usually minimal'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': 20},
                    {'domain_code': 'spiritual_existential', 'strength': 10}
                ]
            },
            {
                'code': 'ind_rec_escape_room',
                'name': 'Escape Room Facility',
                'description': 'An immersive indoor entertainment venue where teams solve puzzles, find clues, and complete objectives to "escape" from themed rooms within a time limit. Combines problem-solving with storytelling and team collaboration.',
                'is_indoor': True,
                'primary_category': 'recreational',
                'typical_space_size': 'small',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['themed rooms', 'puzzles', 'hidden objects', 'locks and keys', 'monitoring systems', 'countdown timers'],
                    'typical_noise_level': 'low to moderate',
                    'immersion_level': 'high',
                    'group_activity': True,
                    'time_limited': True,
                    'narrative_element': 'strong'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'leisure_recreational', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 70}
                ]
            }
        ]

    def get_commercial_environments(self):
        return [
            {
                'code': 'ind_comm_cafe',
                'name': 'Café',
                'description': 'A cozy indoor café environment with seating, ambient music, and a social atmosphere. Cafés typically offer beverages, light meals, and often provide Wi-Fi access for patrons who wish to work or socialize.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['tables', 'chairs', 'counter', 'background music', 'food service', 'wifi'],
                    'typical_noise_level': 'moderate',
                    'seating_capacity': 'varies',
                    'common_activities': ['working', 'socializing', 'eating', 'reading'],
                    'peak_hours': ['morning', 'lunch', 'afternoon'],
                    'lighting': 'warm'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'intellectual', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'productive_practical', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'physical', 'strength': -20}
                ]
            },
            {
                'code': 'ind_comm_restaurant',
                'name': 'Restaurant',
                'description': 'An establishment where meals are prepared and served to customers, ranging from casual diners to fine dining experiences. Restaurants provide tables, seating, and service staff in a controlled indoor environment.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['tables', 'chairs', 'service staff', 'background music', 'dining areas'],
                    'typical_noise_level': 'moderate to high',
                    'seating_capacity': 'medium to large',
                    'common_activities': ['eating', 'socializing', 'celebrating', 'business meetings'],
                    'peak_hours': ['lunch', 'dinner'],
                    'lighting': 'varies by establishment'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'leisure_recreational', 'strength': 70},
                    {'domain_code': 'emotional', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': -10},
                    {'domain_code': 'productive_practical', 'strength': -30},
                    {'domain_code': 'physical', 'strength': -50}
                ]
            },
            {
                'code': 'ind_comm_retail_store',
                'name': 'Retail Store',
                'description': 'A commercial space designed for selling goods to consumers. Retail stores feature display areas, merchandise arranged for browsing, checkout counters, and sometimes fitting rooms or testing areas for products.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['display shelves', 'checkout counters', 'merchandise displays', 'staff', 'security cameras'],
                    'typical_noise_level': 'moderate',
                    'customer_capacity': 'varies by store size',
                    'common_activities': ['shopping', 'browsing', 'purchasing', 'return transactions'],
                    'peak_hours': ['afternoon', 'evening', 'weekends'],
                    'lighting': 'bright'
                },
                'domain_relationships': [
                    {'domain_code': 'productive_practical', 'strength': 60},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'leisure_recreational', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': -40},
                    {'domain_code': 'intellectual', 'strength': -30},
                    {'domain_code': 'creative', 'strength': -20}
                ]
            },
            {
                'code': 'ind_comm_mall',
                'name': 'Shopping Mall',
                'description': 'A large indoor complex housing multiple retail stores, restaurants, and sometimes entertainment venues. Malls typically feature wide corridors for foot traffic, common areas, food courts, and various amenities for shoppers.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'large',
                'typical_privacy_level': 5,
                'archetype_attributes': {
                    'common_features': ['multiple stores', 'food court', 'restrooms', 'seating areas', 'elevators/escalators', 'information desks'],
                    'typical_noise_level': 'high',
                    'visitor_capacity': 'very large',
                    'common_activities': ['shopping', 'eating', 'socializing', 'walking', 'window shopping'],
                    'peak_hours': ['afternoon', 'evening', 'weekends'],
                    'lighting': 'bright'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'productive_practical', 'strength': 50},
                    {'domain_code': 'physical', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': -60},
                    {'domain_code': 'intellectual', 'strength': -40}
                ]
            },
            {
                'code': 'ind_comm_supermarket',
                'name': 'Supermarket',
                'description': 'A large retail store specializing in groceries and household items, with organized aisles, refrigerated sections, and checkout areas. Supermarkets provide a self-service shopping experience for food and daily necessities.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'large',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['product aisles', 'shopping carts', 'checkout lanes', 'refrigerated sections', 'produce areas'],
                    'typical_noise_level': 'moderate',
                    'customer_capacity': 'large',
                    'common_activities': ['grocery shopping', 'comparing products', 'checkout'],
                    'peak_hours': ['after work hours', 'weekends'],
                    'lighting': 'bright'
                },
                'domain_relationships': [
                    {'domain_code': 'productive_practical', 'strength': 90},
                    {'domain_code': 'physical', 'strength': 20},
                    {'domain_code': 'social', 'strength': 10},
                    {'domain_code': 'creative', 'strength': -10},
                    {'domain_code': 'reflective', 'strength': -50},
                    {'domain_code': 'intellectual', 'strength': -30}
                ]
            },
            {
                'code': 'ind_comm_bookstore',
                'name': 'Bookstore',
                'description': 'A retail establishment specializing in books, with shelves organized by genre or topic, reading areas, and sometimes a café section. Bookstores provide a quiet, browsing-friendly environment for literature enthusiasts.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['bookshelves', 'reading nooks', 'checkout counter', 'new release displays', 'sometimes café areas'],
                    'typical_noise_level': 'low',
                    'customer_capacity': 'medium',
                    'common_activities': ['browsing books', 'reading', 'purchasing', 'author events'],
                    'peak_hours': ['afternoons', 'evenings', 'weekends'],
                    'lighting': 'medium to bright'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'leisure_recreational', 'strength': 60},
                    {'domain_code': 'social', 'strength': 20},
                    {'domain_code': 'physical', 'strength': -30}
                ]
            },
            {
                'code': 'ind_comm_cinema',
                'name': 'Movie Theater',
                'description': 'An entertainment venue with screening rooms featuring large screens, seating arranged in rows, and advanced sound systems for showing films. Movie theaters also typically include concession stands and lobby areas.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['movie screens', 'stadium seating', 'concession stands', 'lobby area', 'sound systems'],
                    'typical_noise_level': 'varies (quiet during shows, loud before/after)',
                    'customer_capacity': 'large',
                    'common_activities': ['watching films', 'socializing before/after shows', 'eating snacks'],
                    'peak_hours': ['evenings', 'weekends'],
                    'lighting': 'dim to dark (during shows)'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 80},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'intellectual', 'strength': 40},
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'physical', 'strength': -80},
                    {'domain_code': 'productive_practical', 'strength': -70}
                ]
            },
            {
                'code': 'ind_comm_salon_spa',
                'name': 'Salon & Spa',
                'description': 'A service-oriented business providing beauty treatments, hair styling, and wellness services. Salon and spa environments feature treatment stations, relaxation areas, and specialized equipment for various beauty and wellness services.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['treatment chairs/beds', 'mirrors', 'specialist equipment', 'reception area', 'product displays'],
                    'typical_noise_level': 'low to moderate',
                    'customer_capacity': 'small to medium',
                    'common_activities': ['beauty treatments', 'relaxation services', 'consultations'],
                    'peak_hours': ['during business hours', 'weekends'],
                    'lighting': 'bright in treatment areas, dim in relaxation areas'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'emotional', 'strength': 80},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'productive_practical', 'strength': -40},
                    {'domain_code': 'intellectual', 'strength': -30}
                ]
            },
            {
                'code': 'ind_comm_hotel_lobby',
                'name': 'Hotel Lobby',
                'description': 'The entrance and common area of a hotel, featuring reception desks, seating areas, and sometimes amenities like bars or restaurants. Hotel lobbies are designed to welcome guests and provide communal spaces for relaxation and casual meetings.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['reception desk', 'lounge seating', 'concierge services', 'sometimes dining/bar areas', 'wifi'],
                    'typical_noise_level': 'low to moderate',
                    'visitor_capacity': 'medium',
                    'common_activities': ['check-in/out', 'waiting', 'casual meetings', 'socializing'],
                    'peak_hours': ['check-in/out times', 'evening'],
                    'lighting': 'medium'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'productive_practical', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'leisure_recreational', 'strength': 30},
                    {'domain_code': 'intellectual', 'strength': 20},
                    {'domain_code': 'physical', 'strength': -20}
                ]
            },
            {
                'code': 'ind_comm_arcade',
                'name': 'Gaming Arcade',
                'description': 'An entertainment venue filled with video games, redemption games, and sometimes physical activities like bowling or mini-golf. Arcades feature a lively atmosphere with colorful lighting, game sounds, and various interactive entertainment options.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 15,
                'archetype_attributes': {
                    'common_features': ['game machines', 'prize counters', 'vending areas', 'bright displays', 'sometimes food service'],
                    'typical_noise_level': 'high',
                    'customer_capacity': 'medium to large',
                    'common_activities': ['playing games', 'socializing', 'competing'],
                    'peak_hours': ['afternoons', 'evenings', 'weekends'],
                    'lighting': 'varied, often dim with bright game displays'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 40},
                    {'domain_code': 'creative', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': -70},
                    {'domain_code': 'intellectual', 'strength': -20},
                    {'domain_code': 'productive_practical', 'strength': -60}
                ]
            },
            {
                'code': 'ind_comm_bar_nightclub',
                'name': 'Bar or Nightclub',
                'description': 'An establishment serving alcoholic beverages, often featuring music, dancing areas, and social spaces. Bars and nightclubs are designed for evening entertainment, socializing, and sometimes live performances.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 25,
                'archetype_attributes': {
                    'common_features': ['bar counter', 'seating areas', 'sometimes dance floor', 'sound system', 'lighting effects'],
                    'typical_noise_level': 'high',
                    'customer_capacity': 'medium to large',
                    'common_activities': ['drinking', 'socializing', 'dancing', 'watching performances'],
                    'peak_hours': ['evenings', 'nights', 'weekends'],
                    'lighting': 'dim, often with colored or dynamic lighting'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 100},
                    {'domain_code': 'leisure_recreational', 'strength': 90},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': -80},
                    {'domain_code': 'intellectual', 'strength': -70},
                    {'domain_code': 'productive_practical', 'strength': -90}
                ]
            },
            {
                'code': 'ind_comm_cowork_space',
                'name': 'Coworking Space',
                'description': 'A shared working environment that provides desks, offices, meeting rooms, and amenities for independent professionals and small teams. Coworking spaces combine productive work environments with community-building elements.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['workstations', 'meeting rooms', 'wifi', 'printing facilities', 'kitchenette', 'lounge areas'],
                    'typical_noise_level': 'low to moderate',
                    'capacity': 'varies by facility',
                    'common_activities': ['working', 'meetings', 'networking', 'collaborating'],
                    'peak_hours': ['business hours'],
                    'lighting': 'bright'
                },
                'domain_relationships': [
                    {'domain_code': 'productive_practical', 'strength': 100},
                    {'domain_code': 'intellectual', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'physical', 'strength': -30},
                    {'domain_code': 'leisure_recreational', 'strength': -20}
                ]
            },
            {
                'code': 'out_comm_market',
                'name': 'Outdoor Market',
                'description': 'An open-air commercial space with multiple vendors selling goods from stalls or booths. Outdoor markets may specialize in produce, crafts, antiques, or offer a variety of merchandise in a vibrant, community-oriented setting.',
                'is_indoor': False,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 5,
                'archetype_attributes': {
                    'common_features': ['vendor stalls', 'walkways', 'sometimes covered areas', 'product displays'],
                    'typical_noise_level': 'high',
                    'weather_dependent': True,
                    'common_activities': ['shopping', 'browsing', 'socializing', 'eating'],
                    'peak_hours': ['mornings', 'weekends'],
                    'lighting': 'natural daylight'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'productive_practical', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'leisure_recreational', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': -40},
                    {'domain_code': 'intellectual', 'strength': -10}
                ]
            },
            {
                'code': 'out_comm_shopping_street',
                'name': 'Shopping Street',
                'description': 'A public thoroughfare lined with retail shops, restaurants, and other commercial establishments. Shopping streets combine pedestrian access with commercial offerings in an open, urban environment.',
                'is_indoor': False,
                'primary_category': 'commercial',
                'typical_space_size': 'large',
                'typical_privacy_level': 5,
                'archetype_attributes': {
                    'common_features': ['storefronts', 'sidewalks', 'sometimes pedestrian-only areas', 'benches', 'signage'],
                    'typical_noise_level': 'moderate to high',
                    'weather_dependent': True,
                    'common_activities': ['shopping', 'window shopping', 'walking', 'socializing', 'dining'],
                    'peak_hours': ['afternoons', 'weekends'],
                    'lighting': 'natural daylight, street lighting at night'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'productive_practical', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': -30},
                    {'domain_code': 'intellectual', 'strength': -20}
                ]
            },
            {
                'code': 'out_comm_food_truck_park',
                'name': 'Food Truck Park',
                'description': 'An outdoor venue where multiple mobile food vendors gather, typically featuring seating areas, sometimes entertainment, and a variety of cuisine options. Food truck parks offer casual dining in a social, often festive atmosphere.',
                'is_indoor': False,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['food trucks', 'picnic tables', 'trash bins', 'sometimes shade structures', 'sometimes entertainment areas'],
                    'typical_noise_level': 'moderate to high',
                    'weather_dependent': True,
                    'common_activities': ['eating', 'socializing', 'browsing food options'],
                    'peak_hours': ['lunch', 'dinner', 'weekends'],
                    'lighting': 'natural daylight, sometimes string lights for evening'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'leisure_recreational', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 40},
                    {'domain_code': 'creative', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': -50},
                    {'domain_code': 'intellectual', 'strength': -30},
                    {'domain_code': 'productive_practical', 'strength': -20}
                ]
            },
            {
                'code': 'ind_comm_fitness_center',
                'name': 'Fitness Center',
                'description': 'A facility equipped with exercise machines, weights, and sometimes group fitness rooms and pools for physical activity. Fitness centers provide structured environments for workouts, training, and health improvement.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['exercise equipment', 'changing rooms', 'sometimes pools or courts', 'reception area', 'often TVs'],
                    'typical_noise_level': 'moderate to high',
                    'customer_capacity': 'medium to large',
                    'common_activities': ['exercising', 'training', 'classes', 'socializing'],
                    'peak_hours': ['before/after work hours'],
                    'lighting': 'bright'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'productive_practical', 'strength': 70},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'intellectual', 'strength': -40},
                    {'domain_code': 'creative', 'strength': -30}
                ]
            }
        ]
    


    def get_commercial_environments(self):
        return [
            {
                'code': 'ind_comm_cafe',
                'name': 'Café',
                'description': 'A cozy indoor café environment with seating, ambient music, and a social atmosphere. Cafés typically offer beverages, light meals, and often provide Wi-Fi access for patrons who wish to work or socialize.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['tables', 'chairs', 'counter', 'background music', 'food service', 'wifi'],
                    'typical_noise_level': 'moderate',
                    'seating_capacity': 'varies',
                    'common_activities': ['working', 'socializing', 'eating', 'reading'],
                    'peak_hours': ['morning', 'lunch', 'afternoon'],
                    'lighting': 'warm'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'intellectual', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'productive_practical', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'physical', 'strength': -20}
                ]
            },
            {
                'code': 'ind_comm_restaurant',
                'name': 'Restaurant',
                'description': 'An establishment where meals are prepared and served to customers, ranging from casual diners to fine dining experiences. Restaurants provide tables, seating, and service staff in a controlled indoor environment.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['tables', 'chairs', 'service staff', 'background music', 'dining areas'],
                    'typical_noise_level': 'moderate to high',
                    'seating_capacity': 'medium to large',
                    'common_activities': ['eating', 'socializing', 'celebrating', 'business meetings'],
                    'peak_hours': ['lunch', 'dinner'],
                    'lighting': 'varies by establishment'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'leisure_recreational', 'strength': 70},
                    {'domain_code': 'emotional', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': -10},
                    {'domain_code': 'productive_practical', 'strength': -30},
                    {'domain_code': 'physical', 'strength': -50}
                ]
            },
            {
                'code': 'ind_comm_retail_store',
                'name': 'Retail Store',
                'description': 'A commercial space designed for selling goods to consumers. Retail stores feature display areas, merchandise arranged for browsing, checkout counters, and sometimes fitting rooms or testing areas for products.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['display shelves', 'checkout counters', 'merchandise displays', 'staff', 'security cameras'],
                    'typical_noise_level': 'moderate',
                    'customer_capacity': 'varies by store size',
                    'common_activities': ['shopping', 'browsing', 'purchasing', 'return transactions'],
                    'peak_hours': ['afternoon', 'evening', 'weekends'],
                    'lighting': 'bright'
                },
                'domain_relationships': [
                    {'domain_code': 'productive_practical', 'strength': 60},
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'leisure_recreational', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': -40},
                    {'domain_code': 'intellectual', 'strength': -30},
                    {'domain_code': 'creative', 'strength': -20}
                ]
            },
            {
                'code': 'ind_comm_mall',
                'name': 'Shopping Mall',
                'description': 'A large indoor complex housing multiple retail stores, restaurants, and sometimes entertainment venues. Malls typically feature wide corridors for foot traffic, common areas, food courts, and various amenities for shoppers.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'large',
                'typical_privacy_level': 5,
                'archetype_attributes': {
                    'common_features': ['multiple stores', 'food court', 'restrooms', 'seating areas', 'elevators/escalators', 'information desks'],
                    'typical_noise_level': 'high',
                    'visitor_capacity': 'very large',
                    'common_activities': ['shopping', 'eating', 'socializing', 'walking', 'window shopping'],
                    'peak_hours': ['afternoon', 'evening', 'weekends'],
                    'lighting': 'bright'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'productive_practical', 'strength': 50},
                    {'domain_code': 'physical', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': -60},
                    {'domain_code': 'intellectual', 'strength': -40}
                ]
            },
            {
                'code': 'ind_comm_supermarket',
                'name': 'Supermarket',
                'description': 'A large retail store specializing in groceries and household items, with organized aisles, refrigerated sections, and checkout areas. Supermarkets provide a self-service shopping experience for food and daily necessities.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'large',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['product aisles', 'shopping carts', 'checkout lanes', 'refrigerated sections', 'produce areas'],
                    'typical_noise_level': 'moderate',
                    'customer_capacity': 'large',
                    'common_activities': ['grocery shopping', 'comparing products', 'checkout'],
                    'peak_hours': ['after work hours', 'weekends'],
                    'lighting': 'bright'
                },
                'domain_relationships': [
                    {'domain_code': 'productive_practical', 'strength': 90},
                    {'domain_code': 'physical', 'strength': 20},
                    {'domain_code': 'social', 'strength': 10},
                    {'domain_code': 'creative', 'strength': -10},
                    {'domain_code': 'reflective', 'strength': -50},
                    {'domain_code': 'intellectual', 'strength': -30}
                ]
            },
            {
                'code': 'ind_comm_bookstore',
                'name': 'Bookstore',
                'description': 'A retail establishment specializing in books, with shelves organized by genre or topic, reading areas, and sometimes a café section. Bookstores provide a quiet, browsing-friendly environment for literature enthusiasts.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['bookshelves', 'reading nooks', 'checkout counter', 'new release displays', 'sometimes café areas'],
                    'typical_noise_level': 'low',
                    'customer_capacity': 'medium',
                    'common_activities': ['browsing books', 'reading', 'purchasing', 'author events'],
                    'peak_hours': ['afternoons', 'evenings', 'weekends'],
                    'lighting': 'medium to bright'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'leisure_recreational', 'strength': 60},
                    {'domain_code': 'social', 'strength': 20},
                    {'domain_code': 'physical', 'strength': -30}
                ]
            },
            {
                'code': 'ind_comm_cinema',
                'name': 'Movie Theater',
                'description': 'An entertainment venue with screening rooms featuring large screens, seating arranged in rows, and advanced sound systems for showing films. Movie theaters also typically include concession stands and lobby areas.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['movie screens', 'stadium seating', 'concession stands', 'lobby area', 'sound systems'],
                    'typical_noise_level': 'varies (quiet during shows, loud before/after)',
                    'customer_capacity': 'large',
                    'common_activities': ['watching films', 'socializing before/after shows', 'eating snacks'],
                    'peak_hours': ['evenings', 'weekends'],
                    'lighting': 'dim to dark (during shows)'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 80},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'intellectual', 'strength': 40},
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'physical', 'strength': -80},
                    {'domain_code': 'productive_practical', 'strength': -70}
                ]
            },
            {
                'code': 'ind_comm_salon_spa',
                'name': 'Salon & Spa',
                'description': 'A service-oriented business providing beauty treatments, hair styling, and wellness services. Salon and spa environments feature treatment stations, relaxation areas, and specialized equipment for various beauty and wellness services.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['treatment chairs/beds', 'mirrors', 'specialist equipment', 'reception area', 'product displays'],
                    'typical_noise_level': 'low to moderate',
                    'customer_capacity': 'small to medium',
                    'common_activities': ['beauty treatments', 'relaxation services', 'consultations'],
                    'peak_hours': ['during business hours', 'weekends'],
                    'lighting': 'bright in treatment areas, dim in relaxation areas'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'emotional', 'strength': 80},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'productive_practical', 'strength': -40},
                    {'domain_code': 'intellectual', 'strength': -30}
                ]
            },
            {
                'code': 'ind_comm_hotel_lobby',
                'name': 'Hotel Lobby',
                'description': 'The entrance and common area of a hotel, featuring reception desks, seating areas, and sometimes amenities like bars or restaurants. Hotel lobbies are designed to welcome guests and provide communal spaces for relaxation and casual meetings.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['reception desk', 'lounge seating', 'concierge services', 'sometimes dining/bar areas', 'wifi'],
                    'typical_noise_level': 'low to moderate',
                    'visitor_capacity': 'medium',
                    'common_activities': ['check-in/out', 'waiting', 'casual meetings', 'socializing'],
                    'peak_hours': ['check-in/out times', 'evening'],
                    'lighting': 'medium'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'productive_practical', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'leisure_recreational', 'strength': 30},
                    {'domain_code': 'intellectual', 'strength': 20},
                    {'domain_code': 'physical', 'strength': -20}
                ]
            },
            {
                'code': 'ind_comm_arcade',
                'name': 'Gaming Arcade',
                'description': 'An entertainment venue filled with video games, redemption games, and sometimes physical activities like bowling or mini-golf. Arcades feature a lively atmosphere with colorful lighting, game sounds, and various interactive entertainment options.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 15,
                'archetype_attributes': {
                    'common_features': ['game machines', 'prize counters', 'vending areas', 'bright displays', 'sometimes food service'],
                    'typical_noise_level': 'high',
                    'customer_capacity': 'medium to large',
                    'common_activities': ['playing games', 'socializing', 'competing'],
                    'peak_hours': ['afternoons', 'evenings', 'weekends'],
                    'lighting': 'varied, often dim with bright game displays'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 40},
                    {'domain_code': 'creative', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': -70},
                    {'domain_code': 'intellectual', 'strength': -20},
                    {'domain_code': 'productive_practical', 'strength': -60}
                ]
            },
            {
                'code': 'ind_comm_bar_nightclub',
                'name': 'Bar or Nightclub',
                'description': 'An establishment serving alcoholic beverages, often featuring music, dancing areas, and social spaces. Bars and nightclubs are designed for evening entertainment, socializing, and sometimes live performances.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 25,
                'archetype_attributes': {
                    'common_features': ['bar counter', 'seating areas', 'sometimes dance floor', 'sound system', 'lighting effects'],
                    'typical_noise_level': 'high',
                    'customer_capacity': 'medium to large',
                    'common_activities': ['drinking', 'socializing', 'dancing', 'watching performances'],
                    'peak_hours': ['evenings', 'nights', 'weekends'],
                    'lighting': 'dim, often with colored or dynamic lighting'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 100},
                    {'domain_code': 'leisure_recreational', 'strength': 90},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': -80},
                    {'domain_code': 'intellectual', 'strength': -70},
                    {'domain_code': 'productive_practical', 'strength': -90}
                ]
            },
            {
                'code': 'ind_comm_cowork_space',
                'name': 'Coworking Space',
                'description': 'A shared working environment that provides desks, offices, meeting rooms, and amenities for independent professionals and small teams. Coworking spaces combine productive work environments with community-building elements.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['workstations', 'meeting rooms', 'wifi', 'printing facilities', 'kitchenette', 'lounge areas'],
                    'typical_noise_level': 'low to moderate',
                    'capacity': 'varies by facility',
                    'common_activities': ['working', 'meetings', 'networking', 'collaborating'],
                    'peak_hours': ['business hours'],
                    'lighting': 'bright'
                },
                'domain_relationships': [
                    {'domain_code': 'productive_practical', 'strength': 100},
                    {'domain_code': 'intellectual', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'physical', 'strength': -30},
                    {'domain_code': 'leisure_recreational', 'strength': -20}
                ]
            },
            {
                'code': 'out_comm_market',
                'name': 'Outdoor Market',
                'description': 'An open-air commercial space with multiple vendors selling goods from stalls or booths. Outdoor markets may specialize in produce, crafts, antiques, or offer a variety of merchandise in a vibrant, community-oriented setting.',
                'is_indoor': False,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 5,
                'archetype_attributes': {
                    'common_features': ['vendor stalls', 'walkways', 'sometimes covered areas', 'product displays'],
                    'typical_noise_level': 'high',
                    'weather_dependent': True,
                    'common_activities': ['shopping', 'browsing', 'socializing', 'eating'],
                    'peak_hours': ['mornings', 'weekends'],
                    'lighting': 'natural daylight'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'productive_practical', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'leisure_recreational', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': -40},
                    {'domain_code': 'intellectual', 'strength': -10}
                ]
            },
            {
                'code': 'out_comm_shopping_street',
                'name': 'Shopping Street',
                'description': 'A public thoroughfare lined with retail shops, restaurants, and other commercial establishments. Shopping streets combine pedestrian access with commercial offerings in an open, urban environment.',
                'is_indoor': False,
                'primary_category': 'commercial',
                'typical_space_size': 'large',
                'typical_privacy_level': 5,
                'archetype_attributes': {
                    'common_features': ['storefronts', 'sidewalks', 'sometimes pedestrian-only areas', 'benches', 'signage'],
                    'typical_noise_level': 'moderate to high',
                    'weather_dependent': True,
                    'common_activities': ['shopping', 'window shopping', 'walking', 'socializing', 'dining'],
                    'peak_hours': ['afternoons', 'weekends'],
                    'lighting': 'natural daylight, street lighting at night'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'productive_practical', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': -30},
                    {'domain_code': 'intellectual', 'strength': -20}
                ]
            },
            {
                'code': 'out_comm_food_truck_park',
                'name': 'Food Truck Park',
                'description': 'An outdoor venue where multiple mobile food vendors gather, typically featuring seating areas, sometimes entertainment, and a variety of cuisine options. Food truck parks offer casual dining in a social, often festive atmosphere.',
                'is_indoor': False,
                'primary_category': 'commercial',
                'typical_space_size': 'medium',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['food trucks', 'picnic tables', 'trash bins', 'sometimes shade structures', 'sometimes entertainment areas'],
                    'typical_noise_level': 'moderate to high',
                    'weather_dependent': True,
                    'common_activities': ['eating', 'socializing', 'browsing food options'],
                    'peak_hours': ['lunch', 'dinner', 'weekends'],
                    'lighting': 'natural daylight, sometimes string lights for evening'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'leisure_recreational', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 40},
                    {'domain_code': 'creative', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': -50},
                    {'domain_code': 'intellectual', 'strength': -30},
                    {'domain_code': 'productive_practical', 'strength': -20}
                ]
            },
            {
                'code': 'ind_comm_fitness_center',
                'name': 'Fitness Center',
                'description': 'A facility equipped with exercise machines, weights, and sometimes group fitness rooms and pools for physical activity. Fitness centers provide structured environments for workouts, training, and health improvement.',
                'is_indoor': True,
                'primary_category': 'commercial',
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['exercise equipment', 'changing rooms', 'sometimes pools or courts', 'reception area', 'often TVs'],
                    'typical_noise_level': 'moderate to high',
                    'customer_capacity': 'medium to large',
                    'common_activities': ['exercising', 'training', 'classes', 'socializing'],
                    'peak_hours': ['before/after work hours'],
                    'lighting': 'bright'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'productive_practical', 'strength': 70},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'intellectual', 'strength': -40},
                    {'domain_code': 'creative', 'strength': -30}
                ]
            }
        ]

    def get_educational_environments(self):
        return [
            {
                'code': 'ind_edu_classroom',
                'name': 'Traditional Classroom',
                'description': 'A standard classroom setting with desks arranged in rows or groups, a whiteboard/blackboard, and typical educational materials. This environment is designed for structured learning activities with teacher-led instruction and student participation.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['desks', 'chairs', 'whiteboard/blackboard', 'projector', 'teacher desk'],
                    'typical_noise_level': 'moderate',
                    'seating_capacity': '20-30 students',
                    'lighting': 'fluorescent',
                    'technology_access': 'moderate'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'physical', 'strength': -30}
                ]
            },
            {
                'code': 'ind_edu_lecture_hall',
                'name': 'University Lecture Hall',
                'description': 'A large tiered auditorium-style room designed for lectures and presentations to large groups of students. Often features fixed seating with small writing surfaces and excellent sight lines to the presenter area.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'large',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['tiered seating', 'podium', 'projection system', 'microphone system', 'lecture capture technology'],
                    'typical_noise_level': 'low to moderate',
                    'seating_capacity': '100-500 students',
                    'lighting': 'adjustable',
                    'acoustics': 'optimized for speaking'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'social', 'strength': 10},
                    {'domain_code': 'creative', 'strength': 0},
                    {'domain_code': 'physical', 'strength': -70}
                ]
            },
            {
                'code': 'ind_edu_lab_science',
                'name': 'Science Laboratory',
                'description': 'A specialized classroom equipped with scientific equipment for practical experiments and demonstrations. Features lab benches, sinks, safety equipment, and storage for materials and specimens.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['lab benches', 'sinks', 'gas outlets', 'fume hoods', 'safety equipment', 'specialized instruments'],
                    'typical_noise_level': 'moderate',
                    'safety_protocols': 'high',
                    'ventilation': 'enhanced',
                    'storage': 'secure chemical and equipment storage'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'exploratory_adventurous', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 50},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': 30}
                ]
            },
            {
                'code': 'ind_edu_computer_lab',
                'name': 'Computer Laboratory',
                'description': 'A dedicated room filled with computer workstations arranged for individual or collaborative work. Designed for digital learning, programming, research, and media production activities.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['computer workstations', 'specialized software', 'projector', 'printer', 'high-speed internet'],
                    'typical_noise_level': 'low',
                    'technology_access': 'high',
                    'seating_arrangement': 'rows or clusters',
                    'security': 'monitored access'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'productive_practical', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 50},
                    {'domain_code': 'physical', 'strength': -80}
                ]
            },
            {
                'code': 'ind_edu_art_studio',
                'name': 'Art Studio Classroom',
                'description': 'A bright, open space designed for visual arts instruction and creation. Features easels, tables, sinks, storage for materials, and display areas for works in progress and completed pieces.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['easels', 'art tables', 'sinks', 'drying racks', 'storage cabinets', 'display walls'],
                    'typical_noise_level': 'low to moderate',
                    'lighting': 'natural light preferred',
                    'ventilation': 'good (for paints/chemicals)',
                    'floor_type': 'durable, easy-to-clean'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'emotional', 'strength': 80},
                    {'domain_code': 'intellectual', 'strength': 60},
                    {'domain_code': 'social', 'strength': 40}
                ]
            },
            {
                'code': 'ind_edu_music_room',
                'name': 'Music Classroom',
                'description': 'A specialized classroom for music instruction and practice, with acoustical treatments, instrument storage, and space for group performances. May include practice rooms attached.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 50,
                'archetype_attributes': {
                    'common_features': ['music stands', 'piano', 'instrument storage', 'acoustical treatment', 'recording equipment', 'sound system'],
                    'typical_noise_level': 'high (controlled)',
                    'acoustics': 'specially designed',
                    'seating_arrangement': 'flexible', 
                    'soundproofing': 'enhanced'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 90},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'physical', 'strength': 50}
                ]
            },
            {
                'code': 'ind_edu_library',
                'name': 'Educational Library',
                'description': 'A quiet study environment with extensive collections of books, research materials, and digital resources. Features varied study spaces for individual and group work, reference assistance, and computer access.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'large',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['bookshelves', 'study tables', 'computer stations', 'reference desk', 'private study carrels', 'group study rooms'],
                    'typical_noise_level': 'very low',
                    'technology_access': 'moderate to high',
                    'lighting': 'well-lit reading areas',
                    'resources': 'physical and digital collections'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'social', 'strength': -30},
                    {'domain_code': 'physical', 'strength': -90}
                ]
            },
            {
                'code': 'ind_edu_maker_space',
                'name': 'Educational Makerspace',
                'description': 'A collaborative workshop environment equipped with tools and materials for hands-on creation, design, and engineering projects. Supports innovation, experimentation, and practical skill development.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['3D printers', 'laser cutters', 'electronics stations', 'hand tools', 'project tables', 'material storage'],
                    'typical_noise_level': 'moderate to high',
                    'safety_protocols': 'strict',
                    'technology_access': 'high',
                    'flexibility': 'reconfigurable spaces'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'productive_practical', 'strength': 100},
                    {'domain_code': 'exploratory_adventurous', 'strength': 90},
                    {'domain_code': 'intellectual', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'social', 'strength': 70}
                ]
            },
            {
                'code': 'ind_edu_theater',
                'name': 'Educational Theater',
                'description': 'A performance space with stage, lighting, seating, and backstage areas for theatrical productions, presentations, and performances. Used for drama education and school productions.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'large',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['stage', 'seating', 'lighting system', 'sound system', 'curtains', 'backstage area', 'dressing rooms'],
                    'typical_noise_level': 'variable',
                    'lighting': 'controllable theatrical lighting',
                    'acoustics': 'designed for performance',
                    'seating_capacity': '100-500 people'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 90},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'physical', 'strength': 70}
                ]
            },
            {
                'code': 'ind_edu_language_lab',
                'name': 'Language Learning Laboratory',
                'description': 'A specialized classroom equipped with audio-visual technology for language instruction. Features individual listening stations, recording capabilities, and interactive software for practice and assessment.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['headphone stations', 'language software', 'recording equipment', 'interactive displays', 'cultural materials'],
                    'typical_noise_level': 'low',
                    'technology_access': 'high',
                    'acoustics': 'designed for clear audio',
                    'privacy': 'individual booths or stations'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 40},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'physical', 'strength': -60}
                ]
            },
            {
                'code': 'ind_edu_conference_room',
                'name': 'Educational Conference Room',
                'description': 'A meeting space designed for small to medium-sized groups, often used for faculty meetings, student group work, thesis defenses, and small seminars. Features a central table or configurable seating arrangement.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'small',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['conference table', 'comfortable chairs', 'presentation screen', 'whiteboard', 'videoconference equipment'],
                    'typical_noise_level': 'low',
                    'technology_access': 'moderate to high',
                    'privacy': 'often has doors or is soundproofed',
                    'formality': 'professional atmosphere'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'intellectual', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'creative', 'strength': 50},
                    {'domain_code': 'physical', 'strength': -70}
                ]
            },
            {
                'code': 'ind_edu_gymnasium',
                'name': 'Educational Gymnasium',
                'description': 'A large indoor space designed for physical education, sports training, and school athletic events. Features a main court area, sometimes with bleachers, and often includes storage for sports equipment.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'large',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['hardwood court', 'basketball hoops', 'bleachers', 'equipment storage', 'locker rooms nearby'],
                    'typical_noise_level': 'high',
                    'ceiling_height': 'high',
                    'flooring': 'specialized sports surface',
                    'versatility': 'can be used for multiple activities and events'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'emotional', 'strength': 60},
                    {'domain_code': 'intellectual', 'strength': -20},
                    {'domain_code': 'reflective', 'strength': -50}
                ]
            },
            {
                'code': 'out_edu_campus_quad',
                'name': 'University Quadrangle',
                'description': 'An open outdoor space at the center of a college or university campus, typically surrounded by academic buildings. Used for casual gathering, studying, events, and transit between classes.',
                'is_indoor': False,
                'primary_category': 'educational',
                'typical_space_size': 'large',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['grass areas', 'pathways', 'benches', 'shade trees', 'landmark features'],
                    'typical_noise_level': 'moderate',
                    'weather_dependent': True,
                    'accessibility': 'central location',
                    'historical_significance': 'often has campus traditions associated'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'physical', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'intellectual', 'strength': 40},
                    {'domain_code': 'leisure_recreational', 'strength': 80}
                ]
            },
            {
                'code': 'out_edu_school_yard',
                'name': 'School Playground/Yard',
                'description': 'An outdoor space at a primary or secondary school designed for recreation, physical education, and social interaction during breaks. May include play equipment, sports areas, and seating.',
                'is_indoor': False,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['playground equipment', 'sports fields/courts', 'paved areas', 'seating', 'shade structures'],
                    'typical_noise_level': 'high',
                    'weather_dependent': True,
                    'safety': 'supervised during school hours',
                    'boundaries': 'typically fenced'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'social', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'intellectual', 'strength': -10},
                    {'domain_code': 'reflective', 'strength': -30}
                ]
            },
            {
                'code': 'out_edu_outdoor_classroom',
                'name': 'Outdoor Learning Space',
                'description': 'A designated outdoor area designed specifically for educational activities. May include seating arrangements, teaching walls or boards, shade structures, and demonstration areas for nature study.',
                'is_indoor': False,
                'primary_category': 'educational',
                'typical_space_size': 'small',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['seating circles', 'teaching board/wall', 'shade structure', 'natural elements', 'demonstration gardens'],
                    'typical_noise_level': 'moderate',
                    'weather_dependent': True,
                    'natural_light': 'abundant',
                    'connection_to_nature': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'exploratory_adventurous', 'strength': 80},
                    {'domain_code': 'social', 'strength': 60}
                ]
            },
            {
                'code': 'ind_edu_stem_lab',
                'name': 'STEM Learning Laboratory',
                'description': 'An integrated laboratory environment designed for interdisciplinary science, technology, engineering, and mathematics education. Features flexible workspaces and tools for hands-on experimentation and project-based learning.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['flexible workstations', 'digital displays', 'prototyping tools', 'robotics equipment', 'scientific instruments', 'collaboration spaces'],
                    'typical_noise_level': 'moderate',
                    'technology_access': 'very high',
                    'adaptability': 'designed for multiple teaching modes',
                    'integrates': 'physical and digital learning'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'exploratory_adventurous', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'productive_practical', 'strength': 90},
                    {'domain_code': 'social', 'strength': 60}
                ]
            },
            {
                'code': 'ind_edu_quiet_study',
                'name': 'Silent Study Room',
                'description': 'A designated quiet space for focused individual study with minimal distractions. Features individual carrels or tables and strict noise policies to maintain a concentrated atmosphere.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 80,
                'archetype_attributes': {
                    'common_features': ['individual carrels', 'task lighting', 'power outlets', 'comfortable seating', 'sound dampening'],
                    'typical_noise_level': 'very low',
                    'technology_restrictions': 'often limits phone use',
                    'lighting': 'designed to reduce eye strain',
                    'rules': 'strict noise policies'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 30},
                    {'domain_code': 'social', 'strength': -90},
                    {'domain_code': 'physical', 'strength': -90}
                ]
            },
            {
                'code': 'ind_edu_virtual_reality',
                'name': 'Virtual Reality Learning Lab',
                'description': 'A specialized lab equipped with virtual reality headsets and stations for immersive educational experiences. Used for simulations, virtual field trips, and interactive 3D learning across various subjects.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 50,
                'archetype_attributes': {
                    'common_features': ['VR headsets and equipment', 'open floor space', 'powerful computers', 'instructor station', 'projection for observers'],
                    'typical_noise_level': 'low',
                    'technology_access': 'cutting-edge',
                    'safety': 'clearance zones around VR users',
                    'versatility': 'applicable across disciplines'
                },
                'domain_relationships': [
                    {'domain_code': 'exploratory_adventurous', 'strength': 100},
                    {'domain_code': 'intellectual', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 50},
                    {'domain_code': 'social', 'strength': -20}
                ]
            },
            {
                'code': 'ind_edu_sensory_room',
                'name': 'Educational Sensory Room',
                'description': 'A specialized environment designed to develop students\' sensory processing skills through various stimuli including lights, textures, sounds, and aromas. Particularly valuable for special education and early childhood development.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'small',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['sensory wall panels', 'adjustable lighting', 'comfortable seating', 'sound equipment', 'tactile objects', 'aromatherapy options'],
                    'typical_noise_level': 'controlled',
                    'stimulation': 'carefully designed sensory inputs',
                    'adaptability': 'can be adjusted for calming or stimulating',
                    'accessibility': 'designed for diverse needs'
                },
                'domain_relationships': [
                    {'domain_code': 'emotional', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 70},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'intellectual', 'strength': 50}
                ]
            },
            {
                'code': 'ind_edu_cooking_classroom',
                'name': 'Culinary Education Kitchen',
                'description': 'A specialized classroom equipped with cooking stations, appliances, and preparation areas for culinary education. Designed for food science, nutrition, and cooking skills instruction.',
                'is_indoor': True,
                'primary_category': 'educational',
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['cooking stations', 'demonstration area', 'appliances', 'utensils', 'food storage', 'preparation surfaces'],
                    'typical_noise_level': 'moderate to high',
                    'safety_equipment': 'fire suppression, first aid',
                    'ventilation': 'commercial grade',
                    'hygiene': 'designed for food safety'
                },
                'domain_relationships': [
                    {'domain_code': 'productive_practical', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 90},
                    {'domain_code': 'physical', 'strength': 70},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'intellectual', 'strength': 60}
                ]
            }
        ]

    

    
    def get_professional_environments(self): 
        
        return [
            {
                'code': 'ind_prof_office_private',
                'name': 'Private Office',
                'description': 'A dedicated office space for a single professional, typically enclosed with walls and a door offering privacy for focused work, confidential calls, and meetings. Often contains a desk, ergonomic chair, computer setup, filing cabinets, and personal storage.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 85,
                'archetype_attributes': {
                    'common_features': ['desk', 'ergonomic chair', 'computer', 'bookshelves', 'filing cabinets', 'whiteboard', 'meeting table'],
                    'typical_noise_level': 'low',
                    'typical_interruptions': 'minimal',
                    'lighting': 'controllable',
                    'window_access': 'common'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'physical', 'strength': -30}
                ]
            },
            {
                'code': 'ind_prof_cubicle',
                'name': 'Office Cubicle',
                'description': 'A partially enclosed workspace within a larger office floor plan, separated by dividers or partitions that provide limited visual privacy but little sound isolation. Typically includes a desk, computer workstation, and limited personal storage.',
                'is_indoor': True,
                'typical_space_size': 'small',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['desk', 'office chair', 'computer', 'filing cabinet', 'partition walls', 'overhead storage'],
                    'typical_noise_level': 'moderate',
                    'typical_interruptions': 'frequent',
                    'lighting': 'standardized',
                    'window_access': 'limited'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'physical', 'strength': -50}
                ]
            },
            {
                'code': 'ind_prof_openplan',
                'name': 'Open-Plan Office',
                'description': 'A large, open workspace where multiple employees work without walls or partitions between them. Designed to encourage collaboration and communication, but often lacks privacy and can be noisy. Usually features clusters of desks or workstations grouped by team or function.',
                'is_indoor': True,
                'typical_space_size': 'large',
                'typical_privacy_level': 15,
                'archetype_attributes': {
                    'common_features': ['communal desks', 'office chairs', 'computer stations', 'shared storage', 'collaboration zones', 'whiteboards'],
                    'typical_noise_level': 'high',
                    'typical_interruptions': 'very frequent',
                    'lighting': 'standardized',
                    'window_access': 'shared'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 40},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': -20},
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'physical', 'strength': -30}
                ]
            },
            {
                'code': 'ind_prof_conference',
                'name': 'Conference Room',
                'description': 'A dedicated meeting space designed for formal gatherings, presentations, and discussions. Usually features a large central table with multiple chairs, presentation equipment, and sometimes video conferencing capabilities. Designed for group collaboration and decision-making.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['conference table', 'ergonomic chairs', 'projector', 'whiteboard', 'video conferencing equipment', 'presentation screen'],
                    'typical_noise_level': 'variable',
                    'typical_interruptions': 'scheduled',
                    'lighting': 'adjustable',
                    'window_access': 'often available'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'social', 'strength': 100},
                    {'domain_code': 'physical', 'strength': -40}
                ]
            },
            {
                'code': 'ind_prof_breakroom',
                'name': 'Office Break Room',
                'description': 'A casual space within a workplace designated for employees to take breaks, eat meals, and socialize. Typically includes kitchen facilities, tables and chairs, and sometimes recreational elements. Designed to provide respite from work and foster informal interactions.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['tables', 'chairs', 'kitchen appliances', 'coffee machine', 'refrigerator', 'microwave', 'vending machines', 'water dispenser'],
                    'typical_noise_level': 'moderate to high',
                    'typical_interruptions': 'frequent',
                    'lighting': 'bright',
                    'window_access': 'common'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': -20},
                    {'domain_code': 'creative', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': 10},
                    {'domain_code': 'social', 'strength': 100},
                    {'domain_code': 'leisure_recreational', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 10}
                ]
            },
            {
                'code': 'ind_prof_creativestudio',
                'name': 'Creative Studio',
                'description': 'A flexible workspace designed specifically for creative professionals such as designers, architects, or artists. Features large work surfaces, specialized equipment, and space for displaying and reviewing creative work. Promotes visual thinking and creative collaboration.',
                'is_indoor': True,
                'typical_space_size': 'large',
                'typical_privacy_level': 50,
                'archetype_attributes': {
                    'common_features': ['drafting tables', 'design computers', 'material samples', 'prototyping space', 'pin-up walls', 'specialized equipment', 'storage for supplies'],
                    'typical_noise_level': 'moderate',
                    'typical_interruptions': 'collaborative',
                    'lighting': 'specialized (often natural)',
                    'window_access': 'premium'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 70},
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 20}
                ]
            },
            {
                'code': 'ind_prof_techlab',
                'name': 'Technical Laboratory',
                'description': 'A specialized workspace equipped for technical research, development, or testing. Contains specialized equipment, instruments, and safety features specific to the field (e.g., electronics, chemistry, biology). Designed for precise, controlled work and experimentation.',
                'is_indoor': True,
                'typical_space_size': 'large',
                'typical_privacy_level': 75,
                'archetype_attributes': {
                    'common_features': ['workbenches', 'specialized equipment', 'safety stations', 'storage cabinets', 'testing apparatus', 'computer stations', 'ventilation systems'],
                    'typical_noise_level': 'variable',
                    'typical_interruptions': 'protocol-based',
                    'lighting': 'task-specific',
                    'window_access': 'limited (controlled environment)'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'physical', 'strength': 40}
                ]
            },
            {
                'code': 'ind_prof_coworking',
                'name': 'Coworking Space',
                'description': 'A shared workplace where professionals from different organizations work independently or collaboratively. Offers a combination of open workspaces, private areas, and communal facilities. Designed to provide professional amenities while fostering community and networking.',
                'is_indoor': True,
                'typical_space_size': 'large',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['hot desks', 'dedicated desks', 'private phone booths', 'meeting rooms', 'lounge areas', 'kitchen facilities', 'networking events'],
                    'typical_noise_level': 'moderate',
                    'typical_interruptions': 'variable',
                    'lighting': 'well-designed',
                    'window_access': 'premium feature'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 50},
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'physical', 'strength': -20}
                ]
            },
            {
                'code': 'ind_prof_servercenter',
                'name': 'Data Center / Server Room',
                'description': 'A specialized facility dedicated to housing computer systems, servers, and networking equipment. Features extensive cooling systems, security measures, and power redundancies. Designed for maximum uptime and equipment protection rather than human comfort.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 95,
                'archetype_attributes': {
                    'common_features': ['server racks', 'cooling systems', 'raised floors', 'cable management', 'monitoring stations', 'backup power systems', 'security controls'],
                    'typical_noise_level': 'high (equipment)',
                    'typical_interruptions': 'minimal (restricted access)',
                    'lighting': 'functional',
                    'window_access': 'rare or none',
                    'temperature': 'cold (equipment optimized)'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 50},
                    {'domain_code': 'creative', 'strength': -30},
                    {'domain_code': 'reflective', 'strength': -20},
                    {'domain_code': 'social', 'strength': -70},
                    {'domain_code': 'physical', 'strength': -50}
                ]
            },
            {
                'code': 'ind_prof_trainingroom',
                'name': 'Training Room',
                'description': 'A specialized space designed for professional education, workshops, and skills development. Typically arranged classroom-style or in flexible configurations to facilitate learning. Equipped with presentation technologies and interactive tools.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['classroom seating', 'instructor station', 'projector', 'whiteboard', 'training computers', 'handout materials', 'flipcharts'],
                    'typical_noise_level': 'moderate',
                    'typical_interruptions': 'structured',
                    'lighting': 'bright, even',
                    'window_access': 'variable'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'educational', 'strength': 100},
                    {'domain_code': 'physical', 'strength': 10}
                ]
            },
            {
                'code': 'ind_prof_callcenter',
                'name': 'Call Center',
                'description': 'A workspace designed for high-volume telephone or digital customer interactions. Features numerous workstations, often closely arranged, with acoustic considerations. Optimized for communication efficiency and monitoring capabilities.',
                'is_indoor': True,
                'typical_space_size': 'large',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['workstations', 'headsets', 'computer terminals', 'supervisor stations', 'acoustic panels', 'call monitoring systems', 'break areas'],
                    'typical_noise_level': 'high',
                    'typical_interruptions': 'constant',
                    'lighting': 'uniform',
                    'window_access': 'limited',
                    'supervision_level': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 30},
                    {'domain_code': 'creative', 'strength': -20},
                    {'domain_code': 'reflective', 'strength': -50},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'physical', 'strength': -60}
                ]
            },
            {
                'code': 'out_prof_campusgrounds',
                'name': 'Corporate Campus Grounds',
                'description': 'The outdoor areas surrounding corporate buildings, often landscaped and designed for employee use. May include walking paths, seating areas, and outdoor meeting spaces. Provides natural elements within a professional setting.',
                'is_indoor': False,
                'typical_space_size': 'large',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['walking paths', 'outdoor seating', 'landscaped gardens', 'outdoor meeting areas', 'shade structures', 'water features', 'wifi coverage'],
                    'typical_noise_level': 'low to moderate',
                    'typical_interruptions': 'environmental',
                    'lighting': 'natural (weather dependent)',
                    'weather_dependent': True
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 80},
                    {'domain_code': 'exploratory_adventurous', 'strength': 30}
                ]
            },
            {
                'code': 'ind_prof_rooftopspace',
                'name': 'Office Rooftop Space',
                'description': 'A converted rooftop area on an office building designed for work, breaks, or events. Combines outdoor elements with workplace proximity. Often features views and open air while maintaining professional amenities.',
                'is_indoor': False,
                'typical_space_size': 'medium',
                'typical_privacy_level': 50,
                'archetype_attributes': {
                    'common_features': ['outdoor furniture', 'shade structures', 'plants', 'power outlets', 'wifi access', 'weather protection', 'safety features', 'city views'],
                    'typical_noise_level': 'moderate',
                    'typical_interruptions': 'weather dependent',
                    'lighting': 'natural with supplemental',
                    'weather_dependent': True
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 60},
                    {'domain_code': 'creative', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'physical', 'strength': 40}
                ]
            },
            {
                'code': 'ind_prof_executivesuite',
                'name': 'Executive Suite',
                'description': 'A premium office space for senior management, featuring high-end furnishings, enhanced privacy, and dedicated meeting areas. Often includes a private office, reception area, and personal bathroom. Designed to facilitate leadership functions and confidential discussions.',
                'is_indoor': True,
                'typical_space_size': 'large',
                'typical_privacy_level': 90,
                'archetype_attributes': {
                    'common_features': ['executive desk', 'premium seating', 'meeting table', 'reception area', 'private bathroom', 'decorative elements', 'technology integration', 'sound insulation'],
                    'typical_noise_level': 'very low',
                    'typical_interruptions': 'screened',
                    'lighting': 'custom designed',
                    'window_access': 'premium views'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 100},
                    {'domain_code': 'social', 'strength': 60},
                    {'domain_code': 'physical', 'strength': -40}
                ]
            },
            {
                'code': 'ind_prof_workshop',
                'name': 'Professional Workshop',
                'description': 'A hands-on workspace designed for building, repairing, or manipulating physical objects. Contains specialized tools, equipment, and durable work surfaces. Emphasizes functionality and safety over comfort.',
                'is_indoor': True,
                'typical_space_size': 'large',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['workbenches', 'tool storage', 'specialized equipment', 'safety equipment', 'ventilation systems', 'material storage', 'utility sinks'],
                    'typical_noise_level': 'high',
                    'typical_interruptions': 'project-based',
                    'lighting': 'task-oriented',
                    'window_access': 'limited',
                    'durability': 'high'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 20},
                    {'domain_code': 'social', 'strength': 30},
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'productive_practical', 'strength': 100}
                ]
            },
            {
                'code': 'ind_prof_quietroom',
                'name': 'Quiet Work Room',
                'description': 'A designated silent workspace within an office environment where talking and phone calls are prohibited. Features enhanced sound insulation and minimal distractions. Designed for deep focus and concentration on complex tasks.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['individual workstations', 'sound-absorbing materials', 'noise-cancelling design', 'comfortable seating', 'good lighting', 'minimal decoration'],
                    'typical_noise_level': 'very low',
                    'typical_interruptions': 'rare',
                    'lighting': 'non-distracting',
                    'window_access': 'limited or filtered',
                    'electronics_policy': 'silent mode only'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 100},
                    {'domain_code': 'social', 'strength': -80},
                    {'domain_code': 'physical', 'strength': -60}
                ]
            }
        ]

    def get_residential_environments(self):

        return [
            {
                'code': 'ind_residential_living_room',
                'name': 'Living Room',
                'description': 'The main social space in a home, typically featuring comfortable seating, entertainment options, and decorative elements. Serves as a gathering place for family and guests.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['sofa', 'tv', 'coffee table', 'bookshelves', 'decorative items'],
                    'typical_noise_level': 'moderate',
                    'lighting': 'adjustable',
                    'floor_type': 'carpet or hardwood',
                    'social_purpose': 'gathering, relaxation, entertainment'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'leisure_recreational', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 40},
                    {'domain_code': 'intellectual', 'strength': 30}
                ]
            },
            
            {
                'code': 'ind_residential_bedroom',
                'name': 'Bedroom',
                'description': 'A private space primarily for sleeping and personal activities. Often personalized to reflect individual tastes and preferences, serving as a retreat from shared household spaces.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 90,
                'archetype_attributes': {
                    'common_features': ['bed', 'wardrobe', 'nightstand', 'personal items', 'mirror'],
                    'typical_noise_level': 'low',
                    'lighting': 'adjustable',
                    'floor_type': 'carpet or hardwood',
                    'social_purpose': 'solitude, rest, intimacy'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'spiritual_existential', 'strength': 60},
                    {'domain_code': 'social', 'strength': 20}
                ]
            },
            
            {
                'code': 'ind_residential_home_office',
                'name': 'Home Office',
                'description': 'A dedicated workspace within a residential setting, designed for productivity and focus. Typically equipped with work essentials and minimized distractions.',
                'is_indoor': True,
                'typical_space_size': 'small',
                'typical_privacy_level': 75,
                'archetype_attributes': {
                    'common_features': ['desk', 'office chair', 'computer', 'stationery', 'shelving'],
                    'typical_noise_level': 'very low',
                    'lighting': 'task-oriented',
                    'floor_type': 'hardwood or laminate',
                    'social_purpose': 'work, focus, productivity'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 90},
                    {'domain_code': 'productive_practical', 'strength': 100},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'social', 'strength': -20}
                ]
            },
            
            {
                'code': 'ind_residential_kitchen',
                'name': 'Kitchen',
                'description': 'The culinary heart of the home where meals are prepared and often shared. Combines functionality with social aspects of food preparation and consumption.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 30,
                'archetype_attributes': {
                    'common_features': ['stove', 'refrigerator', 'sink', 'countertops', 'cabinets', 'cooking utensils'],
                    'typical_noise_level': 'moderate to high',
                    'lighting': 'bright task lighting',
                    'floor_type': 'tile or vinyl',
                    'social_purpose': 'meal preparation, casual dining, socializing'
                },
                'domain_relationships': [
                    {'domain_code': 'productive_practical', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 80},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'leisure_recreational', 'strength': 50}
                ]
            },
            
            {
                'code': 'ind_residential_dining_room',
                'name': 'Dining Room',
                'description': 'A dedicated space for formal and family meals. Combines functionality with social interaction around food consumption and conversation.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['dining table', 'chairs', 'sideboards', 'serving ware', 'decorative elements'],
                    'typical_noise_level': 'moderate',
                    'lighting': 'adjustable, often featuring hanging fixtures',
                    'floor_type': 'hardwood or tile',
                    'social_purpose': 'shared meals, conversation, hosting'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'leisure_recreational', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 40},
                    {'domain_code': 'emotional', 'strength': 50},
                    {'domain_code': 'productive_practical', 'strength': 30}
                ]
            },
            
            {
                'code': 'ind_residential_bathroom',
                'name': 'Bathroom',
                'description': 'A private space for personal hygiene and self-care routines. Combines functionality with comfort elements that support wellness activities.',
                'is_indoor': True,
                'typical_space_size': 'small',
                'typical_privacy_level': 95,
                'archetype_attributes': {
                    'common_features': ['toilet', 'sink', 'shower/bathtub', 'mirror', 'storage cabinets'],
                    'typical_noise_level': 'low with background water sounds',
                    'lighting': 'bright task lighting',
                    'floor_type': 'tile or vinyl',
                    'social_purpose': 'personal hygiene, self-care, solitude'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 70},
                    {'domain_code': 'emotional', 'strength': 60},
                    {'domain_code': 'physical', 'strength': 80},
                    {'domain_code': 'social', 'strength': -50},
                    {'domain_code': 'productive_practical', 'strength': 30}
                ]
            },
            
            {
                'code': 'ind_residential_basement',
                'name': 'Basement',
                'description': 'A below-ground level space in a home, often used for storage, recreation, or as a flexible multi-purpose area. Typically less finished than main living areas.',
                'is_indoor': True,
                'typical_space_size': 'large',
                'typical_privacy_level': 80,
                'archetype_attributes': {
                    'common_features': ['storage areas', 'recreational equipment', 'utility systems', 'sometimes workout equipment'],
                    'typical_noise_level': 'variable, often insulated from rest of house',
                    'lighting': 'often limited natural light, reliant on artificial lighting',
                    'floor_type': 'concrete, carpet, or vinyl',
                    'social_purpose': 'recreation, storage, projects, sometimes isolation'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 70},
                    {'domain_code': 'leisure_recreational', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 60},
                    {'domain_code': 'productive_practical', 'strength': 50},
                    {'domain_code': 'social', 'strength': 40}
                ]
            },
            
            {
                'code': 'ind_residential_attic',
                'name': 'Attic',
                'description': 'An upper-level space below the roof, often used for storage or converted into living space. Characterized by sloped ceilings and sometimes limited accessibility.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 85,
                'archetype_attributes': {
                    'common_features': ['storage boxes', 'insulation', 'sometimes converted into bedroom or office'],
                    'typical_noise_level': 'very low',
                    'lighting': 'often limited, may have dormer windows',
                    'floor_type': 'plywood, sometimes finished with carpet or other materials',
                    'social_purpose': 'storage, solitude, sometimes specialized workspace'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'spiritual_existential', 'strength': 60},
                    {'domain_code': 'social', 'strength': -30},
                    {'domain_code': 'productive_practical', 'strength': 40}
                ]
            },
            
            {
                'code': 'ind_residential_hallway',
                'name': 'Hallway',
                'description': 'A transitional space connecting different rooms in a home. Primarily functional but often decorated with personal items or artwork.',
                'is_indoor': True,
                'typical_space_size': 'small',
                'typical_privacy_level': 20,
                'archetype_attributes': {
                    'common_features': ['walkway', 'hanging art', 'occasional furniture', 'doors to other rooms'],
                    'typical_noise_level': 'variable based on home activity',
                    'lighting': 'typically ambient, sometimes with wall fixtures',
                    'floor_type': 'hardwood, carpet, or tile',
                    'social_purpose': 'transition, movement, brief encounters'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 30},
                    {'domain_code': 'social', 'strength': 20},
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'emotional', 'strength': 20},
                    {'domain_code': 'productive_practical', 'strength': 10}
                ]
            },
            
            {
                'code': 'ind_residential_guest_room',
                'name': 'Guest Room',
                'description': 'A bedroom specifically designated for visitors. Often neutral in decor and equipped with essentials for temporary stays.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['bed', 'nightstand', 'empty closet space', 'neutral decor', 'sometimes desk'],
                    'typical_noise_level': 'low',
                    'lighting': 'adjustable, typically includes bedside lamp',
                    'floor_type': 'carpet or hardwood',
                    'social_purpose': 'hospitality, temporary accommodation'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 50},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'emotional', 'strength': 40},
                    {'domain_code': 'leisure_recreational', 'strength': 40},
                    {'domain_code': 'spiritual_existential', 'strength': 30}
                ]
            },
            
            {
                'code': 'out_residential_backyard',
                'name': 'Backyard',
                'description': 'The private outdoor space behind a residential dwelling. Often used for recreation, relaxation, gardening, and outdoor entertaining.',
                'is_indoor': False,
                'typical_space_size': 'medium',
                'typical_privacy_level': 70,
                'archetype_attributes': {
                    'common_features': ['lawn', 'garden beds', 'patio/deck', 'outdoor furniture', 'sometimes play equipment'],
                    'typical_noise_level': 'variable, affected by neighborhood and nature sounds',
                    'lighting': 'natural daylight, sometimes outdoor fixtures for evening',
                    'floor_type': 'grass, soil, sometimes hardscaping',
                    'social_purpose': 'outdoor relaxation, entertainment, nature connection'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 80},
                    {'domain_code': 'leisure_recreational', 'strength': 90},
                    {'domain_code': 'social', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'creative', 'strength': 50},
                    {'domain_code': 'spiritual_existential', 'strength': 60}
                ]
            },
            
            {
                'code': 'out_residential_front_yard',
                'name': 'Front Yard',
                'description': 'The outdoor space between a residential dwelling and the street. Functions as both a transitional space and a public-facing representation of the home.',
                'is_indoor': False,
                'typical_space_size': 'small',
                'typical_privacy_level': 10,
                'archetype_attributes': {
                    'common_features': ['lawn', 'walkway', 'landscaping', 'sometimes driveway', 'decorative elements'],
                    'typical_noise_level': 'moderate, affected by street activity',
                    'lighting': 'natural daylight, sometimes path or accent lighting',
                    'floor_type': 'grass, soil, paved areas',
                    'social_purpose': 'curb appeal, neighborhood engagement, transition'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 40},
                    {'domain_code': 'physical', 'strength': 50},
                    {'domain_code': 'creative', 'strength': 40},
                    {'domain_code': 'productive_practical', 'strength': 30},
                    {'domain_code': 'reflective', 'strength': 20}
                ]
            },
            
            {
                'code': 'ind_residential_sunroom',
                'name': 'Sunroom',
                'description': 'A room with extensive window area designed to allow in abundant natural light, often serving as a transitional space between indoors and outdoors.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 40,
                'archetype_attributes': {
                    'common_features': ['large windows', 'comfortable seating', 'plants', 'sometimes wicker or outdoor-inspired furniture'],
                    'typical_noise_level': 'low to moderate',
                    'lighting': 'primarily natural during day, supplemented with artificial evening lighting',
                    'floor_type': 'tile, hardwood, or indoor-outdoor materials',
                    'social_purpose': 'relaxation, nature appreciation, reading, small gatherings'
                },
                'domain_relationships': [
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'spiritual_existential', 'strength': 80},
                    {'domain_code': 'leisure_recreational', 'strength': 60},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'social', 'strength': 40}
                ]
            },
            
            {
                'code': 'out_residential_patio_deck',
                'name': 'Patio/Deck',
                'description': 'An outdoor living space attached to a residence, typically featuring hard surfaces and designed for outdoor dining, entertaining, and relaxation.',
                'is_indoor': False,
                'typical_space_size': 'medium',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['outdoor furniture', 'grill/bbq', 'sometimes outdoor dining set', 'sometimes container plants'],
                    'typical_noise_level': 'moderate',
                    'lighting': 'natural during day, often string lights or fixtures for evening',
                    'floor_type': 'wood decking, concrete, stone, or composite materials',
                    'social_purpose': 'outdoor entertaining, dining, relaxation'
                },
                'domain_relationships': [
                    {'domain_code': 'social', 'strength': 80},
                    {'domain_code': 'leisure_recreational', 'strength': 90},
                    {'domain_code': 'physical', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 50},
                    {'domain_code': 'productive_practical', 'strength': 40}
                ]
            },
            
            {
                'code': 'ind_residential_laundry_room',
                'name': 'Laundry Room',
                'description': 'A utility space dedicated to washing, drying, and sometimes folding clothes. Primarily functional but increasingly designed for comfort and efficiency.',
                'is_indoor': True,
                'typical_space_size': 'small',
                'typical_privacy_level': 60,
                'archetype_attributes': {
                    'common_features': ['washing machine', 'dryer', 'folding area', 'storage for laundry supplies', 'sometimes sink'],
                    'typical_noise_level': 'high when machines running',
                    'lighting': 'bright, functional lighting',
                    'floor_type': 'tile, vinyl, or other water-resistant materials',
                    'social_purpose': 'utility, occasional productive conversations'
                },
                'domain_relationships': [
                    {'domain_code': 'productive_practical', 'strength': 100},
                    {'domain_code': 'physical', 'strength': 40},
                    {'domain_code': 'reflective', 'strength': 30},
                    {'domain_code': 'social', 'strength': 20},
                    {'domain_code': 'creative', 'strength': 10}
                ]
            },
            
            {
                'code': 'ind_residential_home_gym',
                'name': 'Home Gym',
                'description': 'A dedicated space for physical exercise within a residence, equipped with fitness equipment and often designed to motivate physical activity.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 75,
                'archetype_attributes': {
                    'common_features': ['exercise equipment', 'mirrors', 'rubber flooring', 'sometimes TV/screen', 'storage for accessories'],
                    'typical_noise_level': 'moderate to high during use',
                    'lighting': 'bright, energizing lighting',
                    'floor_type': 'rubber mats, foam tiles, or specialized gym flooring',
                    'social_purpose': 'physical fitness, sometimes shared workouts'
                },
                'domain_relationships': [
                    {'domain_code': 'physical', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 60},
                    {'domain_code': 'reflective', 'strength': 50},
                    {'domain_code': 'productive_practical', 'strength': 70},
                    {'domain_code': 'social', 'strength': 20}
                ]
            },
            
            {
                'code': 'ind_residential_hobby_room',
                'name': 'Hobby Room',
                'description': 'A space dedicated to creative and leisure pursuits, customized for specific interests such as crafting, model building, or artistic endeavors.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 80,
                'archetype_attributes': {
                    'common_features': ['work surface', 'storage for supplies', 'specialized tools', 'display areas', 'comfortable seating'],
                    'typical_noise_level': 'variable depending on activity',
                    'lighting': 'bright task lighting, sometimes natural light preferred',
                    'floor_type': 'durable and easy to clean',
                    'social_purpose': 'creative expression, skill development, occasional collaboration'
                },
                'domain_relationships': [
                    {'domain_code': 'creative', 'strength': 100},
                    {'domain_code': 'leisure_recreational', 'strength': 90},
                    {'domain_code': 'productive_practical', 'strength': 70},
                    {'domain_code': 'reflective', 'strength': 60},
                    {'domain_code': 'emotional', 'strength': 50},
                    {'domain_code': 'social', 'strength': 30}
                ]
            },
            
            {
                'code': 'ind_residential_game_room',
                'name': 'Game Room',
                'description': 'A recreational space designed for entertainment through games, featuring video games, board games, or recreational equipment like pool tables or ping pong.',
                'is_indoor': True,
                'typical_space_size': 'medium',
                'typical_privacy_level': 50,
                'archetype_attributes': {
                    'common_features': ['gaming console/PC', 'comfortable seating', 'game storage', 'sometimes recreational tables', 'snack/drink area'],
                    'typical_noise_level': 'moderate to high',
                    'lighting': 'adjustable, often dimmed for screen use',
                    'floor_type': 'carpet or hardwood',
                    'social_purpose': 'entertainment, competition, social bonding'
                },
                'domain_relationships': [
                    {'domain_code': 'leisure_recreational', 'strength': 100},
                    {'domain_code': 'social', 'strength': 90},
                    {'domain_code': 'intellectual', 'strength': 60},
                    {'domain_code': 'emotional', 'strength': 50},
                    {'domain_code': 'physical', 'strength': 40}
                ]
            },
            
            {
                'code': 'out_residential_garden',
                'name': 'Garden',
                'description': 'A cultivated outdoor space dedicated to growing plants, whether ornamental, edible, or both. Provides connection with natural cycles and opportunities for nurturing living things.',
                'is_indoor': False,
                'typical_space_size': 'medium',
                'typical_privacy_level': 65,
                'archetype_attributes': {
                    'common_features': ['plant beds', 'garden tools', 'irrigation system', 'sometimes seating area', 'pathways'],
                    'typical_noise_level': 'low, mainly natural sounds',
                    'lighting': 'natural daylight, sometimes path lighting for evening',
                    'floor_type': 'soil, mulch, sometimes stone pathways',
                    'social_purpose': 'cultivation, nature connection, sometimes sharing produce'
                },
                'domain_relationships': [
                    {'domain_code': 'productive_practical', 'strength': 90},
                    {'domain_code': 'physical', 'strength': 80},
                    {'domain_code': 'reflective', 'strength': 80},
                    {'domain_code': 'creative', 'strength': 70},
                    {'domain_code': 'spiritual_existential', 'strength': 80},
                    {'domain_code': 'emotional', 'strength': 60},
                    {'domain_code': 'social', 'strength': 30}
                ]
            },
            
            {
                'code': 'ind_residential_meditation_space',
                'name': 'Meditation Space',
                'description': 'A quiet area specifically designated for mindfulness, meditation, or spiritual practices. Designed to minimize distractions and foster inner calm.',
                'is_indoor': True,
                'typical_space_size': 'small',
                'typical_privacy_level': 90,
                'archetype_attributes': {
                    'common_features': ['comfortable floor cushions', 'minimal decor', 'perhaps altar or focal point', 'sometimes aromatherapy elements'],
                    'typical_noise_level': 'very low',
                    'lighting': 'soft, often natural or adjustable',
                    'floor_type': 'carpet, cushioned surfaces, or traditional tatami',
                    'social_purpose': 'inner reflection, spiritual practice, sometimes shared meditation'
                },
                'domain_relationships': [
                    {'domain_code': 'spiritual_existential', 'strength': 100},
                    {'domain_code': 'reflective', 'strength': 100},
                    {'domain_code': 'emotional', 'strength': 80},
                    {'domain_code': 'social', 'strength': -50},
                    {'domain_code': 'physical', 'strength': 40}
                ]
            },
            
            {
                'code': 'ind_residential_reading_nook',
                'name': 'Reading Nook',
                'description': 'A small, cozy corner designed specifically for reading and quiet contemplation. Often featuring comfortable seating and good lighting in a secluded area.',
                'is_indoor': True,
                'typical_space_size': 'tiny',
                'typical_privacy_level': 85,
                'archetype_attributes': {
                    'common_features': ['comfortable armchair or window seat', 'good reading light', 'bookshelf or book storage', 'sometimes throw blanket', 'small side table'],
                    'typical_noise_level': 'very low',
                    'lighting': 'focused task lighting for reading',
                    'floor_type': 'varies, often matching adjacent room',
                    'social_purpose': 'solitary enjoyment, intellectual stimulation'
                },
                'domain_relationships': [
                    {'domain_code': 'intellectual', 'strength': 90},
                    {'domain_code': 'reflective', 'strength': 90},
                    {'domain_code': 'emotional', 'strength': 70},
                    {'domain_code': 'spiritual_existential', 'strength': 60},
                    {'domain_code': 'social', 'strength': -40},
                    {'domain_code': 'leisure_recreational', 'strength': 80}
                ]
            }
        ]
