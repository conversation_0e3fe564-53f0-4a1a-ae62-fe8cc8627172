import json
import os
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.conf import settings
from django.utils import timezone
from apps.main.models import LLMConfig, AppliedSeedingCommand

class Command(BaseCommand):
    help = 'Seeds the database with initial LLM configurations from a JSON file.'
    command_name = os.path.splitext(os.path.basename(__file__))[0]

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force execution even if the command has already been applied',
        )

    def handle(self, *args, **options):
        force = options.get('force', False)

        # Check if this command has already been applied
        if AppliedSeedingCommand.objects.filter(command_name=self.command_name).exists() and not force:
            self.stdout.write(self.style.SUCCESS(f"Seeding command '{self.command_name}' already applied. Skipping."))
            return

        if force:
            self.stdout.write(self.style.WARNING(f"Force flag detected. Running command regardless of previous execution."))

        seed_file_path = os.path.join(settings.BASE_DIR, 'testing', 'seed_data', 'llm_configs.json')

        if not os.path.exists(seed_file_path):
            self.stderr.write(self.style.ERROR(f"Seed file not found at {seed_file_path}"))
            return

        try:
            with open(seed_file_path, 'r') as f:
                data = json.load(f)
        except json.JSONDecodeError:
            self.stderr.write(self.style.ERROR(f"Error decoding JSON from {seed_file_path}"))
            return
        except IOError as e:
            self.stderr.write(self.style.ERROR(f"Error reading file {seed_file_path}: {e}"))
            return

        created_count = 0
        updated_count = 0

        for item in data:
            name = item.get('name')
            if not name:
                self.stderr.write(self.style.WARNING("Skipping item with missing 'name'."))
                continue

            defaults = {
                'model_name': item.get('model_name'),
                'temperature': item.get('temperature'),
                # Convert price strings to Decimal, handle None
                'input_token_price': Decimal(item['input_token_price']) if item.get('input_token_price') else None,
                'output_token_price': Decimal(item['output_token_price']) if item.get('output_token_price') else None,
                # Use is_evaluation from JSON data, fallback to name prefix check
                'is_evaluation': item.get('is_evaluation', name.startswith('eval-')),
                # Use is_default from JSON data
                'is_default': item.get('is_default', False),
            }
            # Remove None values from defaults to avoid overwriting existing fields with None
            defaults = {k: v for k, v in defaults.items() if v is not None}


            try:
                obj, created = LLMConfig.objects.update_or_create(
                    name=name,
                    defaults=defaults
                )
                if created:
                    created_count += 1
                    self.stdout.write(self.style.SUCCESS(f"Created LLMConfig: {name}"))
                else:
                    updated_count += 1
                    # Optionally log updates if needed, but might be verbose
                    # self.stdout.write(f"Updated LLMConfig: {name}")
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"Error processing item '{name}': {e}"))

        # Record that this command has been successfully applied
        if force:
            # Update or create the record
            AppliedSeedingCommand.objects.update_or_create(
                command_name=self.command_name,
                defaults={'applied_at': timezone.now()}
            )
        else:
            # Just create a new record
            AppliedSeedingCommand.objects.create(command_name=self.command_name)

        self.stdout.write(self.style.SUCCESS(
            f"Seeding complete for '{self.command_name}'. "
            f"Created: {created_count}, Updated: {updated_count}."
        ))
