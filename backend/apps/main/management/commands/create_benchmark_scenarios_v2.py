# ACTIVE_FILE - 29-05-2025
"""
Django management command for creating benchmark scenarios with support for the new directory structure.
"""

import os
import json
import logging
import asyncio

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings
from apps.main.models import BenchmarkScenario, BenchmarkTag, EvaluationCriteriaTemplate
from apps.main.services.schema_validator_service import SchemaValidationService

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create benchmark scenarios with new directory structure support'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            help='Path to a JSON file containing an array of scenario definitions'
        )
        parser.add_argument(
            '--dir',
            type=str,
            help='Directory containing JSON files with scenario definitions'
        )
        parser.add_argument(
            '--category',
            choices=['agents', 'workflows', 'all'],
            default='all',
            help='Category of scenarios to create'
        )
        parser.add_argument(
            '--agent-type',
            type=str,
            help='Type of agent (e.g., mentor, orchestrator)'
        )
        parser.add_argument(
            '--workflow-type',
            type=str,
            help='Type of workflow (e.g., wheel_generation, discussion)'
        )
        parser.add_argument(
            '--validate-schema',
            action='store_true',
            help='Validate scenarios against schema before creating'
        )
        parser.add_argument(
            '--create-templates',
            action='store_true',
            help='Extract and create evaluation templates from scenarios'
        )

    def handle(self, *args, **options):
        # Print current directory and database connection for debugging
        self.stdout.write(f"Current directory: {os.getcwd()}")
        self.stdout.write(f"Database connection: {settings.DATABASES['default']['HOST']}")

        # Print Python version and encoding
        import sys
        self.stdout.write(f"Python version: {sys.version}")
        self.stdout.write(f"Default encoding: {sys.getdefaultencoding()}")

        # Set up event loop and run async logic
        try:
            asyncio.run(self.async_handle(**options))
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"An unexpected error occurred: {e}"))
            import traceback
            self.stderr.write(traceback.format_exc())
            raise

    async def async_handle(self, **options):
        # Determine the source of scenarios
        if options.get('file'):
            await self.create_from_file(options['file'], options)
        elif options.get('dir'):
            await self.create_from_directory(options['dir'], options)
        else:
            # Use the new directory structure
            base_dir = 'backend/testing/benchmark_data'
            category = options.get('category', 'all')

            if category == 'agents':
                agent_type = options.get('agent_type')
                workflow_type = options.get('workflow_type')
                await self.create_agent_scenarios(base_dir, agent_type, workflow_type, options)
            elif category == 'workflows':
                workflow_type = options.get('workflow_type')
                await self.create_workflow_scenarios(base_dir, workflow_type, options)
            elif category == 'all':
                await self.create_all_scenarios(base_dir, options)
            else:
                raise CommandError(f"Invalid category: {category}")

    async def create_from_file(self, file_path, options):
        """Create scenarios from a JSON file with improved encoding detection and normalization"""
        self.stdout.write(f"Creating scenarios from {file_path}...")

        try:
            # Read the file in binary mode for encoding detection
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            # Detect encoding
            import chardet
            result = chardet.detect(raw_data)
            encoding = result['encoding'] or 'utf-8'
            confidence = result['confidence']

            self.stdout.write(f"Detected encoding: {encoding} (confidence: {confidence})")

            # Try to decode with detected encoding
            try:
                content = raw_data.decode(encoding)
                self.stdout.write(f"Successfully decoded with {encoding}")

                # Normalize line endings (convert Windows CRLF to Unix LF)
                content = content.replace('\r\n', '\n').replace('\r', '\n')

                # Remove multiple consecutive newlines
                import re
                content = re.sub(r'\n{2,}', '\n', content)

                self.stdout.write("Normalized line endings and removed consecutive newlines")

                # Try to parse as JSON
                try:
                    scenarios_data = json.loads(content)
                    if not isinstance(scenarios_data, list):
                        scenarios_data = [scenarios_data]
                    self.stdout.write("Successfully parsed JSON content")
                except json.JSONDecodeError as e:
                    raise CommandError(f"Error decoding JSON from {file_path}: {e}")
            except UnicodeDecodeError as e:
                self.stdout.write(self.style.WARNING(f"Error decoding with {encoding}: {e}"))

                # Try alternative encodings
                alternative_encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                for alt_encoding in alternative_encodings:
                    if alt_encoding != encoding:
                        try:
                            content = raw_data.decode(alt_encoding)
                            self.stdout.write(f"Successfully decoded with alternative encoding: {alt_encoding}")

                            # Normalize line endings (convert Windows CRLF to Unix LF)
                            content = content.replace('\r\n', '\n').replace('\r', '\n')

                            # Remove multiple consecutive newlines
                            import re
                            content = re.sub(r'\n{2,}', '\n', content)

                            self.stdout.write("Normalized line endings and removed consecutive newlines")

                            # Try to parse as JSON
                            try:
                                scenarios_data = json.loads(content)
                                if not isinstance(scenarios_data, list):
                                    scenarios_data = [scenarios_data]
                                self.stdout.write("Successfully parsed JSON content")
                                break
                            except json.JSONDecodeError as e:
                                self.stdout.write(self.style.WARNING(f"JSON parsing error with {alt_encoding}: {e}"))

                        except UnicodeDecodeError:
                            self.stdout.write(self.style.WARNING(f"Failed to decode with {alt_encoding}"))
                else:
                    raise CommandError(f"Could not decode file {file_path} with any encoding")

        except FileNotFoundError:
            raise CommandError(f"Scenario file not found: {file_path}")
        except Exception as e:
            raise CommandError(f"Error reading scenario file {file_path}: {e}")

        # Validate scenarios if requested
        if options.get('validate_schema'):
            await self.validate_scenarios(scenarios_data)

        # Create scenarios
        success_count = 0
        fail_count = 0

        for scenario_data in scenarios_data:
            try:
                await self.create_scenario(scenario_data, options)
                success_count += 1
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"Error creating scenario '{scenario_data.get('name', 'unknown')}': {e}"))
                fail_count += 1

        self.stdout.write(self.style.SUCCESS(f"Created {success_count} scenarios, failed {fail_count}"))

    async def create_from_directory(self, dir_path, options):
        """Create scenarios from a directory of JSON files"""
        self.stdout.write(f"Creating scenarios from directory {dir_path}...")

        if not os.path.exists(dir_path):
            raise CommandError(f"Directory not found: {dir_path}")

        # Find all JSON files in the directory
        scenario_files = []
        for root, _, files in os.walk(dir_path):
            for file in files:
                if file.endswith('.json'):
                    scenario_files.append(os.path.join(root, file))

        if not scenario_files:
            self.stdout.write(self.style.WARNING(f"No JSON files found in {dir_path}"))
            return

        # Process each file
        total_success = 0
        total_fail = 0

        for file_path in scenario_files:
            self.stdout.write(f"Processing {file_path}...")
            try:
                # Read the file in binary mode for encoding detection
                with open(file_path, 'rb') as f:
                    raw_data = f.read()

                # Detect encoding
                import chardet
                result = chardet.detect(raw_data)
                encoding = result['encoding'] or 'utf-8'
                confidence = result['confidence']

                self.stdout.write(f"Detected encoding: {encoding} (confidence: {confidence})")

                # Try to decode with detected encoding
                try:
                    content = raw_data.decode(encoding)
                    self.stdout.write(f"Successfully decoded with {encoding}")

                    # Normalize line endings (convert Windows CRLF to Unix LF)
                    content = content.replace('\r\n', '\n').replace('\r', '\n')

                    # Remove multiple consecutive newlines
                    import re
                    content = re.sub(r'\n{2,}', '\n', content)

                    self.stdout.write("Normalized line endings and removed consecutive newlines")

                    # Try to parse as JSON
                    try:
                        scenarios_data = json.loads(content)
                        if not isinstance(scenarios_data, list):
                            scenarios_data = [scenarios_data]
                        self.stdout.write("Successfully parsed JSON content")
                    except json.JSONDecodeError as e:
                        self.stderr.write(self.style.ERROR(f"Error decoding JSON from {file_path}: {e}"))
                        total_fail += 1
                        continue
                except UnicodeDecodeError as e:
                    self.stdout.write(self.style.WARNING(f"Error decoding with {encoding}: {e}"))

                    # Try alternative encodings
                    alternative_encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                    for alt_encoding in alternative_encodings:
                        if alt_encoding != encoding:
                            try:
                                content = raw_data.decode(alt_encoding)
                                self.stdout.write(f"Successfully decoded with alternative encoding: {alt_encoding}")

                                # Normalize line endings (convert Windows CRLF to Unix LF)
                                content = content.replace('\r\n', '\n').replace('\r', '\n')

                                # Remove multiple consecutive newlines
                                import re
                                content = re.sub(r'\n{2,}', '\n', content)

                                self.stdout.write("Normalized line endings and removed consecutive newlines")

                                # Try to parse as JSON
                                try:
                                    scenarios_data = json.loads(content)
                                    if not isinstance(scenarios_data, list):
                                        scenarios_data = [scenarios_data]
                                    self.stdout.write("Successfully parsed JSON content")
                                    break
                                except json.JSONDecodeError as e:
                                    self.stdout.write(self.style.WARNING(f"JSON parsing error with {alt_encoding}: {e}"))

                            except UnicodeDecodeError:
                                self.stdout.write(self.style.WARNING(f"Failed to decode with {alt_encoding}"))
                    else:
                        self.stderr.write(self.style.ERROR(f"Could not decode file {file_path} with any encoding"))
                        total_fail += 1
                        continue
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"Error reading file {file_path}: {e}"))
                total_fail += 1
                continue

            # Validate scenarios if requested
            if options.get('validate_schema'):
                await self.validate_scenarios(scenarios_data)

            # Create scenarios
            success_count = 0
            fail_count = 0

            for scenario_data in scenarios_data:
                try:
                    await self.create_scenario(scenario_data, options)
                    success_count += 1
                except Exception as e:
                    self.stderr.write(self.style.ERROR(f"Error creating scenario '{scenario_data.get('name', 'unknown')}': {e}"))
                    fail_count += 1

            self.stdout.write(f"Created {success_count} scenarios, failed {fail_count} from {file_path}")
            total_success += success_count
            total_fail += fail_count

        self.stdout.write(self.style.SUCCESS(f"Total: Created {total_success} scenarios, failed {total_fail}"))

    async def create_agent_scenarios(self, base_dir, agent_type=None, workflow_type=None, options=None):
        """Create agent scenarios from the new directory structure"""
        self.stdout.write("Creating agent scenarios...")

        # Determine the directory to search
        agents_dir = os.path.join(base_dir, 'agents')
        if not os.path.exists(agents_dir):
            raise CommandError(f"Agents directory not found: {agents_dir}")

        # Find all agent directories
        agent_dirs = []
        if agent_type:
            agent_dir = os.path.join(agents_dir, agent_type)
            if os.path.exists(agent_dir) and os.path.isdir(agent_dir):
                agent_dirs.append(agent_dir)
            else:
                raise CommandError(f"Agent directory not found: {agent_dir}")
        else:
            for item in os.listdir(agents_dir):
                item_path = os.path.join(agents_dir, item)
                if os.path.isdir(item_path):
                    agent_dirs.append(item_path)

        if not agent_dirs:
            self.stdout.write(self.style.WARNING("No agent directories found"))
            return

        # Process each agent directory
        total_success = 0
        total_fail = 0

        for agent_dir in agent_dirs:
            agent_name = os.path.basename(agent_dir)
            self.stdout.write(f"Processing agent: {agent_name}")

            # Find all workflow directories for this agent
            workflow_dirs = []
            if workflow_type:
                workflow_dir = os.path.join(agent_dir, workflow_type)
                if os.path.exists(workflow_dir) and os.path.isdir(workflow_dir):
                    workflow_dirs.append(workflow_dir)
                else:
                    self.stdout.write(self.style.WARNING(f"Workflow directory not found: {workflow_dir}"))
                    continue
            else:
                for item in os.listdir(agent_dir):
                    item_path = os.path.join(agent_dir, item)
                    if os.path.isdir(item_path):
                        workflow_dirs.append(item_path)

            if not workflow_dirs:
                self.stdout.write(self.style.WARNING(f"No workflow directories found for agent: {agent_name}"))
                continue

            # Process each workflow directory
            for workflow_dir in workflow_dirs:
                workflow_name = os.path.basename(workflow_dir)
                self.stdout.write(f"Processing workflow: {workflow_name}")

                # Find all JSON files in this workflow directory
                scenario_files = []
                for item in os.listdir(workflow_dir):
                    if item.endswith('.json'):
                        scenario_files.append(os.path.join(workflow_dir, item))

                if not scenario_files:
                    self.stdout.write(self.style.WARNING(f"No scenario files found for workflow: {workflow_name}"))
                    continue

                # Process each scenario file
                for file_path in scenario_files:
                    try:
                        # Read the file in binary mode for encoding detection
                        with open(file_path, 'rb') as f:
                            raw_data = f.read()

                        # Detect encoding
                        import chardet
                        result = chardet.detect(raw_data)
                        encoding = result['encoding'] or 'utf-8'
                        confidence = result['confidence']

                        self.stdout.write(f"Detected encoding: {encoding} (confidence: {confidence})")

                        # Try to decode with detected encoding
                        try:
                            content = raw_data.decode(encoding)
                            self.stdout.write(f"Successfully decoded with {encoding}")

                            # Normalize line endings (convert Windows CRLF to Unix LF)
                            content = content.replace('\r\n', '\n').replace('\r', '\n')

                            # Remove multiple consecutive newlines
                            import re
                            content = re.sub(r'\n{2,}', '\n', content)

                            self.stdout.write("Normalized line endings and removed consecutive newlines")

                            # Try to parse as JSON
                            try:
                                scenario_data = json.loads(content)
                                self.stdout.write("Successfully parsed JSON content")

                                # Ensure agent_role is set correctly
                                scenario_data['agent_role'] = agent_name

                                # Add workflow_type to metadata if not present
                                if 'metadata' not in scenario_data:
                                    scenario_data['metadata'] = {}
                                if 'workflow_type' not in scenario_data['metadata']:
                                    scenario_data['metadata']['workflow_type'] = workflow_name

                                # Create the scenario
                                await self.create_scenario(scenario_data, options)
                                total_success += 1
                                self.stdout.write(self.style.SUCCESS(f"Created scenario from {file_path}"))
                            except json.JSONDecodeError as e:
                                self.stderr.write(self.style.ERROR(f"Error decoding JSON from {file_path}: {e}"))
                                total_fail += 1
                        except UnicodeDecodeError as e:
                            self.stdout.write(self.style.WARNING(f"Error decoding with {encoding}: {e}"))

                            # Try alternative encodings
                            alternative_encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                            for alt_encoding in alternative_encodings:
                                if alt_encoding != encoding:
                                    try:
                                        content = raw_data.decode(alt_encoding)
                                        self.stdout.write(f"Successfully decoded with alternative encoding: {alt_encoding}")

                                        # Normalize line endings (convert Windows CRLF to Unix LF)
                                        content = content.replace('\r\n', '\n').replace('\r', '\n')

                                        # Remove multiple consecutive newlines
                                        import re
                                        content = re.sub(r'\n{2,}', '\n', content)

                                        self.stdout.write("Normalized line endings and removed consecutive newlines")

                                        # Try to parse as JSON
                                        try:
                                            scenario_data = json.loads(content)
                                            self.stdout.write("Successfully parsed JSON content")

                                            # Ensure agent_role is set correctly
                                            scenario_data['agent_role'] = agent_name

                                            # Add workflow_type to metadata if not present
                                            if 'metadata' not in scenario_data:
                                                scenario_data['metadata'] = {}
                                            if 'workflow_type' not in scenario_data['metadata']:
                                                scenario_data['metadata']['workflow_type'] = workflow_name

                                            # Create the scenario
                                            await self.create_scenario(scenario_data, options)
                                            total_success += 1
                                            self.stdout.write(self.style.SUCCESS(f"Created scenario from {file_path}"))
                                            break
                                        except json.JSONDecodeError as e:
                                            self.stdout.write(self.style.WARNING(f"JSON parsing error with {alt_encoding}: {e}"))

                                    except UnicodeDecodeError:
                                        self.stdout.write(self.style.WARNING(f"Failed to decode with {alt_encoding}"))
                            else:
                                self.stderr.write(self.style.ERROR(f"Could not decode file {file_path} with any encoding"))
                                total_fail += 1
                    except Exception as e:
                        self.stderr.write(self.style.ERROR(f"Error processing file {file_path}: {e}"))
                        total_fail += 1

        self.stdout.write(self.style.SUCCESS(f"Total: Created {total_success} agent scenarios, failed {total_fail}"))

    async def create_workflow_scenarios(self, base_dir, workflow_type=None, options=None):
        """Create workflow scenarios from the new directory structure"""
        self.stdout.write("Creating workflow scenarios...")

        # Determine the directory to search
        workflows_dir = os.path.join(base_dir, 'workflows')
        if not os.path.exists(workflows_dir):
            raise CommandError(f"Workflows directory not found: {workflows_dir}")

        # Find all workflow directories
        workflow_dirs = []
        if workflow_type:
            workflow_dir = os.path.join(workflows_dir, workflow_type)
            if os.path.exists(workflow_dir) and os.path.isdir(workflow_dir):
                workflow_dirs.append(workflow_dir)
            else:
                raise CommandError(f"Workflow directory not found: {workflow_dir}")
        else:
            for item in os.listdir(workflows_dir):
                item_path = os.path.join(workflows_dir, item)
                if os.path.isdir(item_path):
                    workflow_dirs.append(item_path)

        if not workflow_dirs:
            self.stdout.write(self.style.WARNING("No workflow directories found"))
            return

        # Process each workflow directory
        total_success = 0
        total_fail = 0

        for workflow_dir in workflow_dirs:
            workflow_name = os.path.basename(workflow_dir)
            self.stdout.write(f"Processing workflow: {workflow_name}")

            # Find all JSON files in this workflow directory
            scenario_files = []
            for item in os.listdir(workflow_dir):
                if item.endswith('.json'):
                    scenario_files.append(os.path.join(workflow_dir, item))

            if not scenario_files:
                self.stdout.write(self.style.WARNING(f"No scenario files found for workflow: {workflow_name}"))
                continue

            # Process each scenario file
            for file_path in scenario_files:
                try:
                    # Read the file in binary mode for encoding detection
                    with open(file_path, 'rb') as f:
                        raw_data = f.read()

                    # Detect encoding
                    import chardet
                    result = chardet.detect(raw_data)
                    encoding = result['encoding'] or 'utf-8'

                    # Try to decode with detected encoding
                    try:
                        content = raw_data.decode(encoding)

                        # Try to parse as JSON
                        try:
                            scenario_data = json.loads(content)
                            # Ensure agent_role is set correctly
                            scenario_data['agent_role'] = 'workflow'

                            # Add workflow_type to metadata if not present
                            if 'metadata' not in scenario_data:
                                scenario_data['metadata'] = {}
                            if 'workflow_type' not in scenario_data['metadata']:
                                scenario_data['metadata']['workflow_type'] = workflow_name

                            # Create the scenario
                            await self.create_scenario(scenario_data, options)
                            total_success += 1
                            self.stdout.write(self.style.SUCCESS(f"Created scenario from {file_path}"))
                        except json.JSONDecodeError as e:
                            self.stderr.write(self.style.ERROR(f"Error decoding JSON from {file_path}: {e}"))
                            total_fail += 1
                    except UnicodeDecodeError as e:
                        self.stderr.write(self.style.ERROR(f"Error decoding file {file_path} with {encoding}: {e}"))
                        total_fail += 1
                except Exception as e:
                    self.stderr.write(self.style.ERROR(f"Error processing file {file_path}: {e}"))
                    total_fail += 1

        self.stdout.write(self.style.SUCCESS(f"Total: Created {total_success} workflow scenarios, failed {total_fail}"))

    async def create_all_scenarios(self, base_dir, options=None):
        """Create all scenarios from the new directory structure"""
        self.stdout.write("Creating all scenarios...")

        # Create agent scenarios
        await self.create_agent_scenarios(base_dir, None, None, options)

        # Create workflow scenarios
        await self.create_workflow_scenarios(base_dir, None, options)

    async def validate_scenarios(self, scenarios_data):
        """Validate scenarios against schema"""
        self.stdout.write("Validating scenarios...")

        validator = SchemaValidationService()
        invalid_count = 0

        for scenario_data in scenarios_data:
            validation_result = validator.validate_benchmark_scenario(scenario_data)
            if not validation_result['valid']:
                invalid_count += 1
                self.stdout.write(self.style.WARNING(
                    f"Invalid scenario '{scenario_data.get('name', 'unknown')}': {validation_result['errors']}"
                ))

        if invalid_count > 0:
            self.stdout.write(self.style.WARNING(f"Found {invalid_count} invalid scenarios"))
        else:
            self.stdout.write(self.style.SUCCESS("All scenarios are valid"))

    def create_scenario_with_raw_sql(self, scenario_data, options=None):
        """Create a scenario using raw SQL to bypass psycopg2 encoding issues"""
        import uuid
        from datetime import datetime
        from django.db import connection

        # Extract scenario properties
        scenario_name = scenario_data.get('name')
        if not scenario_name:
            raise ValueError("Scenario name is required")

        agent_role = scenario_data.get('agent_role')
        if not agent_role:
            raise ValueError("Agent role is required")

        description = scenario_data.get('description', '')
        input_data = scenario_data.get('input_data', {})
        metadata = scenario_data.get('metadata', {})
        tags_data = scenario_data.get('tags', [])
        is_active = scenario_data.get('is_active', True)

        # Ensure all string values are properly encoded
        def ensure_utf8(value):
            if isinstance(value, str):
                # Normalize line endings
                value = value.replace('\r\n', '\n').replace('\r', '\n')
                # Encode and decode to ensure valid UTF-8
                try:
                    value.encode('utf-8').decode('utf-8')
                    return value
                except UnicodeError:
                    # If there's an encoding error, try to fix it
                    try:
                        # Try to encode as latin-1 and then decode as utf-8
                        return value.encode('latin-1').decode('utf-8')
                    except UnicodeError:
                        # If that fails, replace problematic characters
                        return value.encode('utf-8', errors='replace').decode('utf-8')
            elif isinstance(value, dict):
                return {k: ensure_utf8(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [ensure_utf8(item) for item in value]
            else:
                return value

        # Ensure all string values are properly encoded
        description = ensure_utf8(description)
        input_data = ensure_utf8(input_data)
        metadata = ensure_utf8(metadata)

        # Generate UUID and timestamp
        scenario_id = str(uuid.uuid4())
        now = datetime.now()

        # Convert JSON fields to strings
        input_data_json = json.dumps(input_data, ensure_ascii=False)
        metadata_json = json.dumps(metadata, ensure_ascii=False)

        try:
            with connection.cursor() as cursor:
                # Check if scenario already exists
                cursor.execute("""
                    SELECT id FROM main_benchmarkscenario
                    WHERE name = %s AND agent_role = %s AND is_latest = true
                """, [scenario_name, agent_role])

                existing_row = cursor.fetchone()

                if existing_row:
                    # Update existing scenario
                    cursor.execute("""
                        UPDATE main_benchmarkscenario
                        SET description = %s, input_data = %s, metadata = %s,
                            is_active = %s, updated_at = %s
                        WHERE id = %s
                    """, [description, input_data_json, metadata_json, is_active, now, existing_row[0]])

                    scenario_id = existing_row[0]
                    self.stdout.write(f"Updated scenario: {scenario_name}")
                else:
                    # Create new scenario
                    cursor.execute("""
                        INSERT INTO main_benchmarkscenario
                        (id, name, agent_role, description, input_data, metadata,
                         is_active, version, is_latest, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, [scenario_id, scenario_name, agent_role, description,
                          input_data_json, metadata_json, is_active, 1, True, now, now])

                    self.stdout.write(f"Created scenario: {scenario_name}")

                # Handle tags
                if tags_data:
                    # Clear existing tags for this scenario
                    cursor.execute("""
                        DELETE FROM main_benchmarkscenario_tags
                        WHERE benchmarkscenario_id = %s
                    """, [scenario_id])

                    for tag_name in tags_data:
                        # Create tag if it doesn't exist
                        tag_id = str(uuid.uuid4())
                        cursor.execute("""
                            INSERT INTO main_benchmarktag (id, name, created_at, updated_at)
                            VALUES (%s, %s, %s, %s)
                            ON CONFLICT (name) DO NOTHING
                        """, [tag_id, tag_name, now, now])

                        # Create association
                        cursor.execute("""
                            INSERT INTO main_benchmarkscenario_tags (benchmarkscenario_id, benchmarktag_id)
                            SELECT %s, id FROM main_benchmarktag WHERE name = %s
                            ON CONFLICT DO NOTHING
                        """, [scenario_id, tag_name])

                return scenario_id

        except Exception as e:
            self.stderr.write(self.style.ERROR(f"Error in raw SQL operation: {e}"))
            raise

    async def create_scenario(self, scenario_data, options=None):
        """Create a single scenario in the database with fallback to SQL file generation"""
        try:
            # Try the standard Django ORM approach first
            return await self._create_scenario_orm(scenario_data, options)
        except Exception as e:
            if "'utf-8' codec can't decode byte" in str(e):
                self.stdout.write(self.style.WARNING("Database connection issue detected. Generating SQL file instead."))
                # Generate SQL file as fallback
                return self.generate_sql_file_for_scenario(scenario_data, options)
            else:
                raise

    def generate_sql_file_for_scenario(self, scenario_data, options=None):
        """Generate SQL file for scenario creation when database connection fails"""
        import uuid
        from datetime import datetime

        # Extract scenario properties
        scenario_name = scenario_data.get('name')
        if not scenario_name:
            raise ValueError("Scenario name is required")

        agent_role = scenario_data.get('agent_role')
        if not agent_role:
            raise ValueError("Agent role is required")

        description = scenario_data.get('description', '')
        input_data = scenario_data.get('input_data', {})
        metadata = scenario_data.get('metadata', {})
        tags_data = scenario_data.get('tags', [])
        is_active = scenario_data.get('is_active', True)

        # Ensure all string values are properly encoded
        def ensure_utf8(value):
            if isinstance(value, str):
                # Normalize line endings
                value = value.replace('\r\n', '\n').replace('\r', '\n')
                # Encode and decode to ensure valid UTF-8
                try:
                    value.encode('utf-8').decode('utf-8')
                    return value
                except UnicodeError:
                    # If there's an encoding error, try to fix it
                    try:
                        # Try to encode as latin-1 and then decode as utf-8
                        return value.encode('latin-1').decode('utf-8')
                    except UnicodeError:
                        # If that fails, replace problematic characters
                        return value.encode('utf-8', errors='replace').decode('utf-8')
            elif isinstance(value, dict):
                return {k: ensure_utf8(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [ensure_utf8(item) for item in value]
            else:
                return value

        # Ensure all string values are properly encoded
        description = ensure_utf8(description)
        input_data = ensure_utf8(input_data)
        metadata = ensure_utf8(metadata)

        # Generate UUID and timestamp
        scenario_id = str(uuid.uuid4())
        now = datetime.now().isoformat()

        # Convert JSON fields to strings
        input_data_json = json.dumps(input_data, ensure_ascii=False)
        metadata_json = json.dumps(metadata, ensure_ascii=False)

        # Escape SQL strings
        def escape_sql_string(value):
            if value is None:
                return 'NULL'
            if not isinstance(value, str):
                value = str(value)
            value = value.replace("'", "''")
            return f"'{value}'"

        # Generate SQL statements
        sql_statements = []

        # SQL for creating the scenario
        scenario_sql = f"""
INSERT INTO main_benchmarkscenario (
    id, name, agent_role, description, input_data, metadata,
    is_active, version, is_latest, created_at, updated_at
) VALUES (
    {escape_sql_string(scenario_id)},
    {escape_sql_string(scenario_name)},
    {escape_sql_string(agent_role)},
    {escape_sql_string(description)},
    {escape_sql_string(input_data_json)},
    {escape_sql_string(metadata_json)},
    {is_active},
    1,
    true,
    {escape_sql_string(now)},
    {escape_sql_string(now)}
) ON CONFLICT (name, agent_role) WHERE is_latest = true DO UPDATE SET
    description = EXCLUDED.description,
    input_data = EXCLUDED.input_data,
    metadata = EXCLUDED.metadata,
    is_active = EXCLUDED.is_active,
    updated_at = EXCLUDED.updated_at;"""

        sql_statements.append(scenario_sql)

        # SQL for creating tags and associations
        for tag_name in tags_data:
            tag_id = str(uuid.uuid4())

            # Create tag if it doesn't exist
            tag_sql = f"""
INSERT INTO main_benchmarktag (id, name, created_at, updated_at)
VALUES ({escape_sql_string(tag_id)}, {escape_sql_string(tag_name)}, {escape_sql_string(now)}, {escape_sql_string(now)})
ON CONFLICT (name) DO NOTHING;"""

            sql_statements.append(tag_sql)

            # Create association
            association_sql = f"""
INSERT INTO main_benchmarkscenario_tags (benchmarkscenario_id, benchmarktag_id)
SELECT {escape_sql_string(scenario_id)}, id
FROM main_benchmarktag
WHERE name = {escape_sql_string(tag_name)}
ON CONFLICT DO NOTHING;"""

            sql_statements.append(association_sql)

        # Write SQL to file
        sql_filename = f"scenario_{scenario_name.replace(' ', '_').replace('-', '_').lower()}.sql"
        sql_filepath = os.path.join(os.getcwd(), sql_filename)

        try:
            with open(sql_filepath, 'w', encoding='utf-8') as f:
                f.write("-- SQL statements for creating benchmark scenario\n")
                f.write(f"-- Scenario: {scenario_name}\n")
                f.write(f"-- Generated at: {now}\n\n")

                f.write("BEGIN;\n\n")

                for sql in sql_statements:
                    f.write(sql.strip() + "\n\n")

                f.write("COMMIT;\n")

            self.stdout.write(self.style.SUCCESS(f"Generated SQL file: {sql_filepath}"))
            self.stdout.write(self.style.WARNING("Please execute this SQL file manually in your database."))

            return scenario_id

        except Exception as e:
            self.stderr.write(self.style.ERROR(f"Error writing SQL file: {e}"))
            raise

    async def _create_scenario_orm(self, scenario_data, options=None):
        """Create scenario using Django ORM (original implementation)"""
        # Extract scenario properties
        scenario_name = scenario_data.get('name')
        if not scenario_name:
            raise ValueError("Scenario name is required")

        agent_role = scenario_data.get('agent_role')
        if not agent_role:
            raise ValueError("Agent role is required")

        description = scenario_data.get('description', '')
        input_data = scenario_data.get('input_data', {})
        metadata = scenario_data.get('metadata', {})
        tags_data = scenario_data.get('tags', [])
        is_active = scenario_data.get('is_active', True)

        # Create evaluation template if requested
        if options and options.get('create_templates') and 'expected_quality_criteria' in metadata:
            await self.create_evaluation_template(scenario_data)

        # Ensure all string values are properly encoded
        def ensure_utf8(value):
            if isinstance(value, str):
                # Normalize line endings
                value = value.replace('\r\n', '\n').replace('\r', '\n')
                # Encode and decode to ensure valid UTF-8
                try:
                    value.encode('utf-8').decode('utf-8')
                    return value
                except UnicodeError:
                    # If there's an encoding error, try to fix it
                    try:
                        # Try to encode as latin-1 and then decode as utf-8
                        return value.encode('latin-1').decode('utf-8')
                    except UnicodeError:
                        # If that fails, replace problematic characters
                        return value.encode('utf-8', errors='replace').decode('utf-8')
            elif isinstance(value, dict):
                return {k: ensure_utf8(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [ensure_utf8(item) for item in value]
            else:
                return value

        # Ensure all string values are properly encoded
        description = ensure_utf8(description)
        input_data = ensure_utf8(input_data)
        metadata = ensure_utf8(metadata)

        # Check if scenario already exists
        try:
            existing_scenario = BenchmarkScenario.objects.get(
                name=scenario_name,
                agent_role=agent_role,
                is_latest=True
            )

            # Update the scenario
            existing_scenario.description = description
            existing_scenario.input_data = input_data
            existing_scenario.metadata = metadata
            existing_scenario.is_active = is_active
            existing_scenario.save()

            # Update tags
            tag_objs = []
            for tag_name in tags_data:
                tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name)
                tag_objs.append(tag)
            if tag_objs:
                existing_scenario.tags.set(tag_objs)

            self.stdout.write(f"Updated scenario: {scenario_name}")
            return existing_scenario

        except BenchmarkScenario.DoesNotExist:
            # Create a new scenario
            new_scenario = BenchmarkScenario.objects.create(
                name=scenario_name,
                agent_role=agent_role,
                description=description,
                input_data=input_data,
                metadata=metadata,
                is_active=is_active,
                version=1,
                is_latest=True
            )

            # Add tags
            tag_objs = []
            for tag_name in tags_data:
                tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name)
                tag_objs.append(tag)
            if tag_objs:
                new_scenario.tags.set(tag_objs)

            self.stdout.write(f"Created scenario: {scenario_name}")
            return new_scenario

    async def create_evaluation_template(self, scenario_data):
        """Extract and create an evaluation template from a scenario"""
        # Extract template properties
        agent_role = scenario_data.get('agent_role', 'unknown')
        workflow_type = scenario_data.get('metadata', {}).get('workflow_type', 'general')
        criteria = scenario_data.get('metadata', {}).get('expected_quality_criteria')

        if not criteria:
            return None

        template_name = f"{agent_role}_{workflow_type}_criteria"
        description = f"Evaluation criteria for {agent_role} in {workflow_type} workflow"

        # Create or update the template
        try:
            template, created = EvaluationCriteriaTemplate.objects.update_or_create(
                name=template_name,
                defaults={
                    'description': description,
                    'criteria': {
                        'dimensions': criteria
                    }
                }
            )

            action = "Created" if created else "Updated"
            self.stdout.write(f"{action} evaluation template: {template_name}")
            return template

        except Exception as e:
            self.stderr.write(self.style.ERROR(f"Error creating evaluation template: {e}"))
            return None
