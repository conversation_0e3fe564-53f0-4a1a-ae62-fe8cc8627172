import os
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.conf import settings

class Command(BaseCommand):
    help = 'Automatically collects static files on startup'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Collecting static files...'))
        
        # Ensure the static directories exist
        os.makedirs(settings.STATIC_ROOT, exist_ok=True)
        
        # Run collectstatic with the --noinput flag to not ask for confirmation
        call_command('collectstatic', interactive=False, verbosity=0)
        
        self.stdout.write(self.style.SUCCESS('Static files collected successfully.'))