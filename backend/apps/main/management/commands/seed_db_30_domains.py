import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.activity.models import GenericDomain

class Command(BaseCommand):
    help = 'Seeds generic domains for activities. Skips if already applied by run_seeders.'
    # Re-add command name definition
    COMMAND_NAME = os.path.splitext(os.path.basename(__file__))[0]

    def handle(self, *args, **kwargs):
                # Check environment variable to potentially skip idempotency check
        skip_check = os.environ.get('SKIP_SEEDER_IDEMPOTENCY_CHECK') == 'true'

        if not skip_check:
            # Import and perform the check only if the environment variable is not set
            from apps.main.models import AppliedSeedingCommand # Import locally
            if AppliedSeedingCommand.objects.filter(command_name=self.COMMAND_NAME).exists():
                self.stdout.write(self.style.WARNING(f"Command '{self.COMMAND_NAME}' has already been applied by run_seeders. Skipping."))
                return
        else:
             self.stdout.write(self.style.WARNING(f"Skipping AppliedSeedingCommand check for '{self.COMMAND_NAME}' due to environment variable."))

        self.stdout.write(self.style.SUCCESS(f"Running command '{self.COMMAND_NAME}' logic (will update/create individual items)..."))

        try:
            # 2. Execute the seeding logic within a transaction
            with transaction.atomic():
                # Seed domains (this method handles internal idempotency via update_or_create)
                created_count, updated_count = self.seed_generic_domains()

                # Ensure AppliedSeedingCommand.objects.create(...) remains removed from here.
                # run_seeders.py handles the recording upon successful completion of this command.
                self.stdout.write(self.style.SUCCESS(f"Finished command '{self.COMMAND_NAME}' logic! Created {created_count} new domains, updated {updated_count} existing domains."))

        except Exception as e:
            # Catch any exception during the transaction
            self.stderr.write(self.style.ERROR(f"Command '{self.COMMAND_NAME}' failed internally: {str(e)}"))
            # Re-raise the exception so run_seeders knows it failed and won't record success
            raise CommandError(f"Command '{self.COMMAND_NAME}' failed: {e}") from e

    def seed_generic_domains(self):
        """
        Create or update a comprehensive list of generic domains.
        Returns the count of newly created and updated domains.
        """
        self.stdout.write("Seeding generic domains...")
        created_count = 0
        updated_count = 0

        # List of domains organized by primary category
        domains = {
            GenericDomain.PrimaryCategoryChoices.PHYSICAL: [ # Use renamed model
                {
                    'code': GenericDomain.PrimaryCategoryChoices.PHYSICAL, # Use renamed model
                    'name': GenericDomain.PrimaryCategoryChoices.PHYSICAL.label, # Use renamed model
                    'description': 'a broad domain encompassing all physical activities that engage the body and promote health and fitness.'
                },
                {
                    'code': 'phys_cardio',
                    'name': 'Cardiovascular Exercise',
                    'description': 'Activities that increase heart rate and improve cardiovascular health, such as running, cycling, or swimming.'
                },
                {
                    'code': 'phys_strength',
                    'name': 'Strength Training',
                    'description': 'Activities focused on building muscle strength through resistance, such as weightlifting or bodyweight exercises.'
                },
                {
                    'code': 'phys_chill',
                    'name': 'Physical but chill',
                    'description': 'Activities that are physical, but somehow chill (like walking).'
                },
                {
                    'code': 'phys_flexibility',
                    'name': 'Flexibility & Mobility',
                    'description': 'Activities that improve range of motion, joint health, and muscle elasticity, such as yoga, stretching, or pilates.'
                },
                {
                    'code': 'phys_sports',
                    'name': 'Recreational Sports',
                    'description': 'Structured physical activities with rules and competitive elements, including team sports or individual athletic pursuits.'
                },
                {
                    'code': 'phys_outdoor',
                    'name': 'Outdoor Activities',
                    'description': 'Physical activities conducted in natural settings, such as hiking, climbing, or kayaking.'
                },
                {
                    'code': 'phys_dance',
                    'name': 'Dance & Movement',
                    'description': 'Expressive physical activities involving rhythmic body movement, including various dance styles and movement practices.'
                },
                {
                    'code': 'phys_martial',
                    'name': 'Martial Arts',
                    'description': 'Structured systems of combat and self-defense training that often incorporate physical conditioning and philosophical elements.'
                },
                {
                    'code': 'phys_balance',
                    'name': 'Balance & Coordination',
                    'description': 'Activities that develop proprioception, stability, and motor control.'
                }
            ],
            
            GenericDomain.PrimaryCategoryChoices.SOCIAL: [ # Use renamed model
                {
                    'code': GenericDomain.PrimaryCategoryChoices.SOCIAL, # Use renamed model
                    'name': GenericDomain.PrimaryCategoryChoices.SOCIAL.label, # Use renamed model
                    'description': 'a broad domain encompassing all social activities that engage users with others.'
                },
                {
                    'code': 'soc_connecting',
                    'name': 'Social Connection',
                    'description': 'Activities focused on forming or strengthening interpersonal bonds through conversation, shared experiences, or mutual support.'
                },
                {
                    'code': 'soc_group',
                    'name': 'Group Dynamics',
                    'description': 'Activities involving interaction within larger social structures, such as team building, community engagement, or navigating organizational systems.'
                },
                {
                    'code': 'soc_comm',
                    'name': 'Communication Skills',
                    'description': 'Activities developing verbal and non-verbal communication, including public speaking, active listening, or conflict resolution.'
                },
                {
                    'code': 'soc_empathy',
                    'name': 'Empathy Building',
                    'description': 'Activities designed to enhance understanding of others\' perspectives, emotions, and experiences.'
                },
                {
                    'code': 'soc_network',
                    'name': 'Networking',
                    'description': 'Structured activities to expand professional or personal circles, create new connections, and develop a support system.'
                },
                {
                    'code': 'soc_romance',
                    'name': 'Romantic Relationship',
                    'description': 'Activities focused on developing or strengthening intimate partner relationships.'
                },
                {
                    'code': 'soc_family',
                    'name': 'Family Bonding',
                    'description': 'Activities that enhance connections between family members and foster a supportive family environment.'
                },
                {
                    'code': 'soc_leadership',
                    'name': 'Leadership',
                    'description': 'Activities that develop skills in guiding, influencing, and motivating others toward shared goals.'
                },
                {
                    'code': 'soc_conflict',
                    'name': 'Conflict Resolution',
                    'description': 'Activities that develop skills in solving conflicts.'
                }
            ],
            
            GenericDomain.PrimaryCategoryChoices.CREATIVE: [ # Use renamed model
                {
                    'code': GenericDomain.PrimaryCategoryChoices.CREATIVE, # Use renamed model
                    'name': GenericDomain.PrimaryCategoryChoices.CREATIVE.label, # Use renamed model
                    'description': 'a broad domain encompassing all creative activities.'
                },
                {
                    'code': 'creative_visual',
                    'name': 'Visual Arts',
                    'description': 'Creative expression through visual mediums including painting, drawing, sculpture, or digital art.'
                },
                {
                    'code': 'creative_observation',
                    'name': 'Creative observation',
                    'description': 'Creative thoughts from observing.'
                },
                {
                    'code': 'creative_auditory',
                    'name': 'Creative auditory',
                    'description': 'Creative thoughts from listening.'
                },
                {
                    'code': 'creative_music',
                    'name': 'Music Creation',
                    'description': 'Activities involving creating or performing music, such as playing instruments, singing, composing, or producing.'
                },
                {
                    'code': 'creative_writing',
                    'name': 'Creative Writing',
                    'description': 'Expression through written language, including fiction, poetry, journaling, or scriptwriting.'
                },
                {
                    'code': 'creative_design',
                    'name': 'Design Thinking',
                    'description': 'Problem-solving through creative design processes, including graphic design, product design, or architectural thinking.'
                },
                {
                    'code': 'creative_culinary',
                    'name': 'Culinary Arts',
                    'description': 'Creative expression through food preparation, recipe development, or gastronomic experimentation.'
                },
                {
                    'code': 'creative_perform',
                    'name': 'Performance Arts',
                    'description': 'Creative activities involving live presentation, such as theater, comedy, or storytelling.'
                },
                {
                    'code': 'creative_craft',
                    'name': 'Crafts & Making',
                    'description': 'Hands-on creation of physical objects, including textile arts, woodworking, pottery, or DIY projects.'
                },
                {
                    'code': 'creative_improv',
                    'name': 'Improvisation',
                    'description': 'Spontaneous creative expression and adaptation without predetermined structure.'
                }
            ],
            
            GenericDomain.PrimaryCategoryChoices.INTELLECTUAL: [ # Use renamed model
                {
                    'code': GenericDomain.PrimaryCategoryChoices.INTELLECTUAL, # Use renamed model
                    'name': GenericDomain.PrimaryCategoryChoices.INTELLECTUAL.label, # Use renamed model
                    'description': 'a broad domain encompassing all intellectual activities.'
                },
                {
                    'code': 'intel_learn',
                    'name': 'Learning & Study',
                    'description': 'Structured acquisition of knowledge or skills in specific domains through courses, research, or self-directed study.'
                },
                {
                    'code': 'intel_problem',
                    'name': 'Problem Solving',
                    'description': 'Activities that develop analytical thinking and systematic approaches to overcoming challenges.'
                },
                {
                    'code': 'intel_audio',
                    'name': 'Intellectual audio',
                    'description': 'Activities that requires focused listening.'
                },
                {
                    'code': 'intel_strategic',
                    'name': 'Strategic Thinking',
                    'description': 'Long-term planning and development of approaches to achieve goals while considering multiple variables and possible outcomes.'
                },
                {
                    'code': 'intel_curiosity',
                    'name': 'Intellectual Curiosity',
                    'description': 'Exploration of ideas driven by interest and wonder, such as reading broadly or engaging with new concepts.'
                },
                {
                    'code': 'intel_language',
                    'name': 'Language & Linguistics',
                    'description': 'Activities focused on language acquisition, linguistic analysis, or cross-cultural communication.'
                },
                {
                    'code': 'intel_debate',
                    'name': 'Critical Discourse',
                    'description': 'Engagement in reasoned argument, debate, or discussion that evaluates ideas from multiple perspectives.'
                },
                {
                    'code': 'intel_science',
                    'name': 'Scientific Inquiry',
                    'description': 'Activities involving observation, hypothesis formation, experimentation, and evidence-based reasoning.'
                },
                {
                    'code': 'intel_tech',
                    'name': 'Technology & Digital Skills',
                    'description': 'Learning and applying digital tools, programming, or computational thinking.'
                }
            ],
            
            GenericDomain.PrimaryCategoryChoices.REFLECTIVE: [ # Use renamed model
                {
                    'code': GenericDomain.PrimaryCategoryChoices.REFLECTIVE, # Use renamed model
                    'name': GenericDomain.PrimaryCategoryChoices.REFLECTIVE.label, # Use renamed model
                    'description': 'a broad domain encompassing all reflective activities.'
                },
                {
                    'code': 'refl_meditate',
                    'name': 'Meditation',
                    'description': 'Practices that develop focused attention, awareness, and mental clarity through various meditation techniques.'
                },
                {
                    'code': 'refl_journal',
                    'name': 'Journaling & Self-Reflection',
                    'description': 'Written or structured contemplation of personal experiences, emotions, and thoughts to gain insight.'
                },
                {
                    'code': 'refl_mindful',
                    'name': 'Mindfulness Practice',
                    'description': 'Activities cultivating present-moment awareness and non-judgmental observation of internal and external experiences.'
                },
                {
                    'code': 'refl_values',
                    'name': 'Values Clarification',
                    'description': 'Exploration and identification of personal principles, ethics, and priorities that guide decision-making.'
                },
                {
                    'code': 'refl_persp',
                    'name': 'Perspective Taking',
                    'description': 'Activities that encourage viewing situations from different angles or through others\' experiences.'
                },
                {
                    'code': 'refl_philos',
                    'name': 'Philosophical Contemplation',
                    'description': 'Engagement with fundamental questions about existence, meaning, ethics, and the nature of reality.'
                },
                {
                    'code': 'refl_grat',
                    'name': 'Gratitude Practice',
                    'description': 'Structured recognition and appreciation of positive aspects of life and experiences.'
                },
                {
                    'code': 'refl_comfort',
                    'name': 'Comfort Zone Reflection',
                    'description': 'Examination of personal boundaries, limitations, and opportunities for growth through controlled discomfort.'
                }
            ],
            
            GenericDomain.PrimaryCategoryChoices.EMOTIONAL: [ # Use renamed model
                {
                    'code': GenericDomain.PrimaryCategoryChoices.EMOTIONAL, # Use renamed model
                    'name': GenericDomain.PrimaryCategoryChoices.EMOTIONAL.label, # Use renamed model
                    'description': 'a broad domain encompassing all activities that engege the users with their emotions.'
                },
                {
                    'code': 'emot_aware',
                    'name': 'Emotional Awareness',
                    'description': 'Activities that develop recognition and understanding of emotional states in oneself and others.'
                },
                {
                    'code': 'emot_regulate',
                    'name': 'Emotion Regulation',
                    'description': 'Practices for managing emotional responses, processing difficult feelings, and developing emotional resilience.'
                },
                {
                    'code': 'emot_express',
                    'name': 'Emotional Expression',
                    'description': 'Healthy outlets for communicating and releasing emotions through verbal, physical, or creative means.'
                },
                {
                    'code': 'emot_compass',
                    'name': 'Self-Compassion',
                    'description': 'Activities that nurture kindness and understanding toward oneself, especially during challenges or failures.'
                },
                {
                    'code': 'emot_joy',
                    'name': 'Joy & Pleasure',
                    'description': 'Intentional engagement in activities that bring happiness, satisfaction, or delight.'
                },
                {
                    'code': 'emot_stress',
                    'name': 'Stress Management',
                    'description': 'Techniques and practices that reduce or help cope with stress and anxiety.'
                },
                {
                    'code': 'emot_forgive',
                    'name': 'Forgiveness & Letting Go',
                    'description': 'Processes for releasing resentment, anger, or attachment to past hurts or disappointments.'
                },
                {
                    'code': 'emot_comfort',
                    'name': 'Comfort & Nurturing',
                    'description': 'Activities that provide emotional security, soothing, and care during difficult times.'
                }
            ],
            
            GenericDomain.PrimaryCategoryChoices.SPIRITUAL_EXISTENTIAL: [ # Use renamed model
                {
                    'code': GenericDomain.PrimaryCategoryChoices.SPIRITUAL_EXISTENTIAL, # Use renamed model
                    'name': GenericDomain.PrimaryCategoryChoices.SPIRITUAL_EXISTENTIAL.label, # Use renamed model
                    'description': 'a broad domain encompassing all spiritual activities.'
                },
                {
                    'code': 'spirit_purpose',
                    'name': 'Purpose & Meaning',
                    'description': 'Exploration of personal significance, life direction, and sources of meaning beyond immediate goals.'
                },
                {
                    'code': 'spirit_connect',
                    'name': 'Spiritual Connection',
                    'description': 'Activities that foster connection with transcendent elements, whether through religious practices or secular spirituality.'
                },
                {
                    'code': 'spirit_ritual',
                    'name': 'Ritual & Practice',
                    'description': 'Structured ceremonies or habits that connect to cultural, spiritual, or personal significance beyond their practical function.'
                },
                {
                    'code': 'spirit_nature',
                    'name': 'Nature Connection',
                    'description': 'Experiences that develop awareness of and relationship with the natural world and ecological systems.'
                },
                {
                    'code': 'spirit_transced',
                    'name': 'Transcendent Experience',
                    'description': 'Activities oriented toward peak experiences, states of flow, or moments that transcend ordinary consciousness.'
                },
                {
                    'code': 'spirit_death',
                    'name': 'Mortality Contemplation',
                    'description': 'Reflective consideration of life\'s impermanence and one\'s relationship with the concept of death.'
                },
                {
                    'code': 'spirit_commun',
                    'name': 'Spiritual Community',
                    'description': 'Engagement with others who share similar existential or spiritual orientations, practices, or beliefs.'
                },
                {
                    'code': 'spirit_wisdom',
                    'name': 'Wisdom Traditions',
                    'description': 'Exploration of or participation in established philosophical, spiritual, or cultural wisdom traditions.'
                }
            ],
            
            GenericDomain.PrimaryCategoryChoices.EXPLORATORY_ADVENTUROUS: [ # Use renamed model
                {
                    'code': GenericDomain.PrimaryCategoryChoices.EXPLORATORY_ADVENTUROUS, # Use renamed model
                    'name': GenericDomain.PrimaryCategoryChoices.EXPLORATORY_ADVENTUROUS.label, # Use renamed model
                    'description': 'a broad domain encompassing all exploratory and adventurous activities.'
                },
                {
                    'code': 'explor_travel',
                    'name': 'Travel & Discovery',
                    'description': 'Activities involving geographical exploration, cultural immersion, or visiting new places.'
                },
                {
                    'code': 'explor_risk',
                    'name': 'Controlled Risk-Taking',
                    'description': 'Activities that involve stepping beyond comfort zones with calculated challenges and reasonable safety measures.'
                },
                {
                    'code': 'explor_sensory',
                    'name': 'Sensory Exploration',
                    'description': 'Experiences that engage or expand awareness through the five senses in novel or intensive ways.'
                },
                {
                    'code': 'explor_cultural',
                    'name': 'Cultural Exploration',
                    'description': 'Engagement with unfamiliar traditions, perspectives, or creative expressions from different cultures.'
                },
                {
                    'code': 'explor_novel',
                    'name': 'Novelty Seeking',
                    'description': 'Pursuit of new experiences, skills, or environments purely for the sake of freshness and variety.'
                },
                {
                    'code': 'explor_adren',
                    'name': 'Adrenaline Activities',
                    'description': 'High-energy experiences that produce excitement, thrill, or exhilaration through physical or psychological intensity.'
                },
                {
                    'code': 'explor_improv',
                    'name': 'Spontaneity & Improvisation',
                    'description': 'Activities with minimal planning that embrace unexpected outcomes and in-the-moment decision making.'
                },
                {
                    'code': 'explor_unknown',
                    'name': 'Embracing Uncertainty',
                    'description': 'Deliberate engagement with the unknown or unpredictable aspects of life or specific domains.'
                }
            ],
            
            GenericDomain.PrimaryCategoryChoices.PRODUCTIVE_PRACTICAL: [ # Use renamed model
                {
                    'code': GenericDomain.PrimaryCategoryChoices.PRODUCTIVE_PRACTICAL, # Use renamed model
                    'name': GenericDomain.PrimaryCategoryChoices.PRODUCTIVE_PRACTICAL.label, # Use renamed model
                    'description': 'a broad domain encompassing all productive and practical activities.'
                },
                {
                    'code': 'prod_organize',
                    'name': 'Organization & Planning',
                    'description': 'Systems and activities for creating order, structure, and efficiency in physical spaces or processes.'
                },
                {
                    'code': 'prod_habit',
                    'name': 'Habit Formation',
                    'description': 'Development of consistent beneficial behaviors through intentional practice and routine building.'
                },
                {
                    'code': 'prod_time',
                    'name': 'Time Management',
                    'description': 'Approaches to allocating time effectively based on priorities, energy levels, and productivity principles.'
                },
                {
                    'code': 'prod_skill',
                    'name': 'Practical Skill Development',
                    'description': 'Learning and application of concrete abilities useful in everyday life or specific contexts.'
                },
                {
                    'code': 'prod_financial',
                    'name': 'Financial Management',
                    'description': 'Activities related to budgeting, saving, investing, or otherwise managing personal economic resources.'
                },
                {
                    'code': 'prod_career',
                    'name': 'Career Development',
                    'description': 'Advancement of professional skills, networks, or positioning within work contexts.'
                },
                {
                    'code': 'prod_health',
                    'name': 'Health & Wellness Systems',
                    'description': 'Practical approaches to maintaining or improving physical and mental wellbeing through consistent practices.'
                },
                {
                    'code': 'prod_home',
                    'name': 'Home Management',
                    'description': 'Activities related to maintaining, improving, or organizing living spaces for functionality and comfort.'
                }
            ],
            
            GenericDomain.PrimaryCategoryChoices.LEISURE_RECREATIONAL: [ # Use renamed model
                {
                    'code': GenericDomain.PrimaryCategoryChoices.LEISURE_RECREATIONAL, # Use renamed model
                    'name': GenericDomain.PrimaryCategoryChoices.LEISURE_RECREATIONAL.label, # Use renamed model
                    'description': 'a broad domain encompassing all recreational and leisure activities.'
                },
                {
                    'code': 'leisure_relax',
                    'name': 'Relaxation',
                    'description': 'Activities specifically oriented toward rest, recovery, and the release of tension or effort.'
                },
                {
                    'code': 'leisure_play',
                    'name': 'Play & Games',
                    'description': 'Engagement in activities pursued primarily for enjoyment rather than external outcomes, including structured games or free play.'
                },
                {
                    'code': 'leisure_entertain',
                    'name': 'Entertainment Consumption',
                    'description': 'Enjoyment of media, performances, or experiences created by others, such as films, music, or shows.'
                },
                {
                    'code': 'leisure_collect',
                    'name': 'Collection & Curation',
                    'description': 'Activities involving gathering, organizing, and appreciating objects or information of personal interest.'
                },
                {
                    'code': 'leisure_nature',
                    'name': 'Recreational Nature Activities',
                    'description': 'Enjoyable outdoor pursuits that connect with natural environments in a leisurely context.'
                },
                {
                    'code': 'leisure_social',
                    'name': 'Social Recreation',
                    'description': 'Leisure activities specifically focused on enjoying time with others in low-pressure social contexts.'
                },
                {
                    'code': 'leisure_hobby',
                    'name': 'Hobby Participation',
                    'description': 'Regular engagement in non-professional interests that provide satisfaction and enjoyment.'
                },
                {
                    'code': 'leisure_festive',
                    'name': 'Celebration & Festivity',
                    'description': 'Participation in events or traditions that mark special occasions or cultural significance in enjoyable ways.'
                }
            ]
        }
        
        # Create or update domains by category
        for category, domain_list in domains.items():
            self.stdout.write(f"Processing {category} domains...")
            
            for domain_data in domain_list:
                domain, created = GenericDomain.objects.update_or_create( # Use renamed model
                    code=domain_data['code'],
                    defaults={
                        'name': domain_data['name'],
                        'description': domain_data['description'],
                        'primary_category': category
                    }
                )
                
                if created:
                    created_count += 1
                else:
                    updated_count += 1
        
        # Return counts
        return created_count, updated_count
