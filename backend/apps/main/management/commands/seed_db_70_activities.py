import os
import datetime
import random
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.contrib.contenttypes.models import ContentType
from apps.activity.models import (
    GenericActivity, GenericDomain, EntityDomainRelationship,
    GenericActivityUserRequirement, GenericActivityResourceRequirement,
    GenericActivityEnvRequirement, GenericActivityUserRequirementSummary,
    TaggedItem, Tag
)
from apps.user.models import (
    GenericTrait, GenericResource, GenericEnvironment, 
    GenericUserLimitation, GenericBelief
)
# Removed import of AppliedSeedingCommand


class Command(BaseCommand):
    help = 'Seeds the database with a comprehensive catalog of generic activities.'
    # Removed COMMAND_NAME definition

    def handle(self, *args, **kwargs):
        # Removed check for AppliedSeedingCommand

        self.stdout.write(self.style.SUCCESS("Seeding generic activities..."))

        try:
            # Execute the seeding logic within a transaction
            with transaction.atomic():
                # Get required content types for generic foreign keys
                trait_ct = ContentType.objects.get_for_model(GenericTrait)
                belief_ct = ContentType.objects.get_for_model(GenericBelief)
                limitation_ct = ContentType.objects.get_for_model(GenericUserLimitation)
                
                # Load necessary reference data
                domains = {d.code: d for d in GenericDomain.objects.all()} # Use renamed model
                traits = {t.code: t for t in GenericTrait.objects.all()}
                environments = {e.code: e for e in GenericEnvironment.objects.all()}
                resources = {r.code: r for r in GenericResource.objects.all()}
                limitations = {l.code: l for l in GenericUserLimitation.objects.all()}
                beliefs = {b.code: b for b in GenericBelief.objects.all()}
                
                # Create activities by category
                self.create_physical_activities(domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                self.create_social_activities(domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                self.create_intellectual_activities(domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                self.create_reflective_activities(domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                self.create_emotional_activities(domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                self.create_exploratory_activities(domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                self.create_productive_activities(domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                self.create_leisure_activities(domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                
                
                # Create requirement summaries for all activities
                activities_count = GenericActivity.objects.count()
                self.stdout.write(f"Creating requirement summaries for {activities_count} activities...")
                for activity in GenericActivity.objects.all():
                    self._create_or_update_summary(activity)
                
                self.stdout.write(self.style.SUCCESS(f"Successfully created {activities_count} generic activities"))

                # Removed recording of AppliedSeedingCommand
                self.stdout.write(self.style.SUCCESS("Finished seeding generic activities!"))

        except Exception as e:
            # Catch any exception during the transaction
            self.stderr.write(self.style.ERROR(f"Seeding generic activities failed: {str(e)}"))
            # Re-raise the exception so run_seeders knows it failed
            raise CommandError(f"Seeding generic activities failed: {e}") from e


    # --- Keep existing helper methods below ---
    # (create_leisure_activities, create_productive_activities, etc.)
    # ... (rest of the file remains the same) ...

    def create_leisure_activities(self, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct):
        """Create leisure activities across different sub-domains including relaxation, games, entertainment, and hobbies"""
        activities = [
            # RELAXATION ACTIVITIES (Beginner-friendly)
            {
                'code': 'leisure_guided_meditation',
                'name': 'Guided Meditation Session',
                'description': 'A short guided meditation to promote relaxation and mindfulness',
                'duration_range': '10-20 minutes',
                'instructions': 'Find a quiet space where you won\'t be disturbed. Sit or lie down comfortably. Use a guided meditation app or video. Focus on your breath and follow the guide\'s instructions, gently bringing your attention back when your mind wanders.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'leisure_relaxation',
                'secondary_domains': [
                    {'domain': 'refl_mindful', 'strength': 70},
                    {'domain': 'refl_emotional', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_aesthetic', 'level': 20, 'optional': True},
                    {'trait': 'emo_anxiety', 'level': 30, 'optional': True, 'notes': 'Higher anxiety may make starting meditation more challenging'}
                ],
                'resource_requirements': [
                    {'resource': 'tech_smartphone', 'quantity': 1, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_hearing', 'level': 30, 'impact_type': 'adaptation', 'notes': 'Visual guidance or text instructions can be used instead of audio'}
                ],
                'belief_interactions': [
                    {'belief': 'emotional_control', 'impact_type': 'reinforce', 'notes': 'Reinforces beliefs about ability to regulate emotions'}
                ],
                'tags': ['relaxation', 'mindfulness', 'stress-relief', 'beginner-friendly', 'solo']
            },
            {
                'code': 'leisure_reading_fiction',
                'name': 'Leisure Fiction Reading',
                'description': 'Reading fiction for pleasure and relaxation',
                'duration_range': '15-60 minutes',
                'instructions': 'Choose a fiction book that interests you. Find a comfortable spot with good lighting. Read at your own pace, focusing on enjoying the story rather than analyzing it. Take breaks if your eyes get tired.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'leisure_entertainment',
                'secondary_domains': [
                    {'domain': 'leisure_relaxation', 'strength': 70},
                    {'domain': 'creative_imagination', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'open_intellect', 'level': 30, 'optional': False},
                    {'trait': 'consc_organization', 'level': 20, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'edu_book', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_comfortable', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_vision', 'level': 70, 'impact_type': 'adaptation', 'notes': 'Audiobooks or e-readers with adjustable text can be used'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_learning', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in ability to process and enjoy complex information'}
                ],
                'tags': ['reading', 'relaxation', 'entertainment', 'beginner-friendly', 'solo']
            },
            
            # GAMES & ENTERTAINMENT (Intermediate)
            {
                'code': 'leisure_board_game_night',
                'name': 'Board Game Night',
                'description': 'An evening playing board games with friends or family',
                'duration_range': '1-3 hours',
                'instructions': 'Invite 2-4 people over. Choose games that match the group\'s interests and experience levels. Set up in a comfortable space with good lighting and enough table space. Take time to explain rules clearly to new players. Focus on enjoyment rather than competition.',
                'social_requirements': {'min_participants': 2, 'max_participants': 6, 'ideal_participants': 4},
                'primary_domain': 'leisure_games',
                'secondary_domains': [
                    {'domain': 'soc_connecting', 'strength': 70},
                    {'domain': 'intel_strategic', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'extra_sociability', 'level': 50, 'optional': False},
                    {'trait': 'agree_flexibility', 'level': 40, 'optional': False},
                    {'trait': 'open_intellect', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'leisure_board_games', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_social_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_manual_dexterity', 'level': 40, 'impact_type': 'adaptation', 'notes': 'Choose games with larger pieces or team up with a partner'},
                    {'limitation': 'cogn_complex_rules', 'level': 50, 'impact_type': 'adaptation', 'notes': 'Select simpler games or partner with an experienced player'}
                ],
                'belief_interactions': [
                    {'belief': 'social_connection', 'impact_type': 'reinforce', 'notes': 'Reinforces beliefs about social connection through shared activities'}
                ],
                'tags': ['games', 'social', 'entertainment', 'strategy', 'competition', 'cooperation']
            },
            {
                'code': 'leisure_video_gaming',
                'name': 'Casual Video Gaming Session',
                'description': 'Playing video games as a recreational activity',
                'duration_range': '30-90 minutes',
                'instructions': 'Choose a game that matches your interests and skill level. Set up in a comfortable position to avoid strain. Set a timer to avoid extended play sessions. Take short breaks every 30 minutes to rest your eyes and stretch.',
                'social_requirements': {'min_participants': 1, 'max_participants': 4},
                'primary_domain': 'leisure_digital',
                'secondary_domains': [
                    {'domain': 'leisure_games', 'strength': 70},
                    {'domain': 'intel_strategic', 'strength': 40},
                ],
                'trait_requirements': [
                    {'trait': 'consc_diligence', 'level': 30, 'optional': False},
                    {'trait': 'open_intellect', 'level': 20, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'tech_gaming', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_digital', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_manual_dexterity', 'level': 60, 'impact_type': 'adaptation', 'notes': 'Adaptive controllers or games with simpler controls can be used'},
                    {'limitation': 'phys_vision', 'level': 50, 'impact_type': 'adaptation', 'notes': 'Games with audio cues or adjustable visual settings'}
                ],
                'belief_interactions': [
                    {'belief': 'technology_adoption', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about technology use for recreation'},
                    {'belief': 'skill_learning', 'impact_type': 'reinforce', 'notes': 'Reinforces beliefs about learning new skills'}
                ],
                'tags': ['digital', 'games', 'entertainment', 'technology', 'solo', 'multiplayer']
            },
            
            # HOBBIES (Advanced)
            {
                'code': 'leisure_home_brewing',
                'name': 'Beginner Home Brewing',
                'description': 'Starting a home brewing project to create your own beer or kombucha',
                'duration_range': '2-3 hours (setup) + 1-4 weeks (brewing)',
                'instructions': 'Research basic brewing methods and gather equipment. Sanitize all equipment thoroughly. Follow a beginner recipe precisely. Record your process for future reference. Monitor fermentation regularly. Be patient during the brewing process.',
                'social_requirements': {'min_participants': 1, 'max_participants': 2},
                'primary_domain': 'leisure_crafting',
                'secondary_domains': [
                    {'domain': 'intel_scientific', 'strength': 60},
                    {'domain': 'productive_cooking', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'consc_organization', 'level': 70, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 60, 'optional': False},
                    {'trait': 'open_intellect', 'level': 50, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'cook_equipment', 'quantity': 1, 'optional': False},
                    {'resource': 'cook_ingredients', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_kitchen', 'optional': False},
                    {'env': 'ind_storage', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_manual_dexterity', 'level': 50, 'impact_type': 'limitation', 'notes': 'Some brewing steps require precise handling'},
                    {'limitation': 'phys_lifting', 'level': 40, 'impact_type': 'limitation', 'notes': 'Brewing equipment and ingredients can be heavy'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_practical', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about ability to complete complex multi-step processes'}
                ],
                'tags': ['crafting', 'brewing', 'science', 'cooking', 'long-term', 'precision']
            },
            {
                'code': 'leisure_nature_photography',
                'name': 'Nature Photography Outing',
                'description': 'A photography excursion focused on capturing natural landscapes or wildlife',
                'duration_range': '1-4 hours',
                'instructions': 'Choose a natural location with interesting features. Visit during golden hour (early morning or late afternoon) for optimal lighting. Bring appropriate camera equipment and weather protection. Move slowly and observe surroundings carefully. Experiment with different angles and compositions. Review and select best photos afterward.',
                'social_requirements': {'min_participants': 1, 'max_participants': 3},
                'primary_domain': 'leisure_outdoor',
                'secondary_domains': [
                    {'domain': 'creative_visual', 'strength': 70},
                    {'domain': 'exploratory_adventurous', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'open_aesthetic', 'level': 60, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 50, 'optional': False},
                    {'trait': 'open_creativity', 'level': 40, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'tech_camera', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'outdoor_nature', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility', 'level': 60, 'impact_type': 'limitation', 'notes': 'Some natural areas may have challenging terrain'},
                    {'limitation': 'phys_weather_sensitivity', 'level': 50, 'impact_type': 'limitation', 'notes': 'Outdoor conditions can be unpredictable'}
                ],
                'belief_interactions': [
                    {'belief': 'self_expression', 'impact_type': 'reinforce', 'notes': 'Reinforces beliefs about creative self-expression'},
                    {'belief': 'appreciation_nature', 'impact_type': 'reinforce', 'notes': 'Strengthens connection to natural world'}
                ],
                'tags': ['photography', 'outdoor', 'creative', 'nature', 'visual', 'skill-building']
            },
            
            # EXPERT-LEVEL ACTIVITIES
            {
                'code': 'leisure_chess_tournament',
                'name': 'Chess Tournament Participation',
                'description': 'Competing in an organized chess tournament',
                'duration_range': '4-8 hours',
                'instructions': 'Register for an appropriate level tournament. Review opening strategies and endgames beforehand. Bring necessary equipment and refreshments. Maintain focus during matches. Analyze games afterward to identify improvement areas. Engage respectfully with opponents.',
                'social_requirements': {'min_participants': 6, 'max_participants': 50, 'not_participants': 1},
                'primary_domain': 'leisure_competition',
                'secondary_domains': [
                    {'domain': 'intel_strategic', 'strength': 100},
                    {'domain': 'soc_competition', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'consc_diligence', 'level': 80, 'optional': False},
                    {'trait': 'open_intellect', 'level': 70, 'optional': False},
                    {'trait': 'emo_anxiety', 'level': 60, 'optional': False, 'notes': 'Managing tournament stress requires emotional regulation'}
                ],
                'resource_requirements': [
                    {'resource': 'leisure_chess_set', 'quantity': 1, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'public_formal', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cogn_attention', 'level': 80, 'impact_type': 'limitation', 'notes': 'Chess requires sustained mental focus'},
                    {'limitation': 'soc_anxiety', 'level': 70, 'impact_type': 'limitation', 'notes': 'Tournament settings involve social pressure and observation'}
                ],
                'belief_interactions': [
                    {'belief': 'competition_mindset', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about competitive performance'},
                    {'belief': 'self_efficacy_intellectual', 'impact_type': 'challenge', 'notes': 'Tests belief in strategic thinking abilities'}
                ],
                'tags': ['chess', 'competition', 'strategy', 'intellectual', 'tournament', 'expert']
            },
            {
                'code': 'leisure_musical_performance',
                'name': 'Musical Instrument Performance',
                'description': 'Performing music on an instrument for an audience',
                'duration_range': '10-60 minutes',
                'instructions': 'Select appropriate music pieces within your skill level. Practice regularly leading up to the performance. Arrange venue and audience (can be informal among friends). Prepare the performance space. Focus on expression rather than technical perfection. Embrace mistakes as part of the learning process.',
                'social_requirements': {'min_participants': 1, 'performer': 1, 'audience': 1, 'max_audience': 50},
                'primary_domain': 'leisure_artistic',
                'secondary_domains': [
                    {'domain': 'creative_performance', 'strength': 100},
                    {'domain': 'soc_expression', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'extra_sociability', 'level': 70, 'optional': False},
                    {'trait': 'open_aesthetic', 'level': 60, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 80, 'optional': False},
                    {'trait': 'emo_anxiety', 'level': 70, 'optional': False, 'notes': 'Managing performance anxiety is crucial'}
                ],
                'resource_requirements': [
                    {'resource': 'creative_instrument', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'public_performance', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_manual_dexterity', 'level': 70, 'impact_type': 'limitation', 'notes': 'Instrument playing requires fine motor control'},
                    {'limitation': 'phys_hearing', 'level': 60, 'impact_type': 'limitation', 'notes': 'Hearing is important for musical performance'},
                    {'limitation': 'soc_performance_anxiety', 'level': 90, 'impact_type': 'limitation', 'notes': 'Performance requires managing significant anxiety'}
                ],
                'belief_interactions': [
                    {'belief': 'self_expression', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about artistic self-expression'},
                    {'belief': 'performance_under_pressure', 'impact_type': 'challenge', 'notes': 'Tests belief in ability to perform under observation'}
                ],
                'tags': ['music', 'performance', 'artistic', 'creative', 'skill-showcase', 'expert']
            },
            
            # ADDITIONAL DIVERSE ACTIVITIES
            {
                'code': 'leisure_puzzle_solving',
                'name': 'Jigsaw Puzzle Session',
                'description': 'Assembling a jigsaw puzzle as a relaxing leisure activity',
                'duration_range': '30-120 minutes',
                'instructions': 'Choose a puzzle with an appropriate piece count for your experience. Clear a flat surface that can remain undisturbed. Sort edge pieces first. Group similar colors or patterns. Work on small sections. Take breaks to avoid eye strain. Consider framing the completed puzzle.',
                'social_requirements': {'min_participants': 1, 'max_participants': 4},
                'primary_domain': 'leisure_puzzles',
                'secondary_domains': [
                    {'domain': 'leisure_relaxation', 'strength': 70},
                    {'domain': 'intel_pattern', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'consc_diligence', 'level': 40, 'optional': False},
                    {'trait': 'open_intellect', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'leisure_puzzle', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_table_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_vision', 'level': 50, 'impact_type': 'adaptation', 'notes': 'Puzzles with larger pieces or high contrast images can help'},
                    {'limitation': 'phys_manual_dexterity', 'level': 40, 'impact_type': 'adaptation', 'notes': 'Puzzles with larger pieces can be easier to manipulate'}
                ],
                'belief_interactions': [
                    {'belief': 'patience_value', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in the value of patience and persistence'}
                ],
                'tags': ['puzzles', 'relaxation', 'cognitive', 'solo', 'group', 'concentration']
            },
            {
                'code': 'leisure_gardening_session',
                'name': 'Container Gardening Session',
                'description': 'Tending to potted plants or a small container garden',
                'duration_range': '20-60 minutes',
                'instructions': 'Choose appropriate plants for your space and expertise. Use quality potting soil and containers with drainage. Water appropriately for each plant type. Remove dead leaves and spent blooms. Check for pests and diseases. Consider fertilizing if needed. Enjoy the sensory experience of working with plants.',
                'social_requirements': {'min_participants': 1, 'max_participants': 2},
                'primary_domain': 'leisure_nature',
                'secondary_domains': [
                    {'domain': 'productive_practical', 'strength': 50},
                    {'domain': 'leisure_care', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'consc_diligence', 'level': 40, 'optional': False},
                    {'trait': 'open_aesthetic', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'garden_tools', 'quantity': 1, 'optional': False},
                    {'resource': 'garden_plants', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_balcony', 'optional': True},
                    {'env': 'outdoor_garden', 'optional': True}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility', 'level': 40, 'impact_type': 'adaptation', 'notes': 'Raised containers can reduce need for bending'},
                    {'limitation': 'phys_lifting', 'level': 30, 'impact_type': 'adaptation', 'notes': 'Small containers or help with setup can accommodate lifting limitations'},
                    {'limitation': 'allergy_pollen', 'level': 60, 'impact_type': 'limitation', 'notes': 'Some plants may trigger allergies'}
                ],
                'belief_interactions': [
                    {'belief': 'nurturing_mindset', 'impact_type': 'reinforce', 'notes': 'Reinforces beliefs about care and nurturing'},
                    {'belief': 'connection_nature', 'impact_type': 'reinforce', 'notes': 'Strengthens connection to natural world'},
                    {'belief': 'delayed_gratification', 'impact_type': 'reinforce', 'notes': 'Reinforces value of patience for long-term rewards'}
                ],
                'tags': ['gardening', 'nature', 'relaxation', 'nurturing', 'sensory', 'ongoing']
            },
            {
                'code': 'leisure_cooking_new_recipe',
                'name': 'Cooking a New Recipe',
                'description': 'Exploring culinary creativity by preparing a new recipe',
                'duration_range': '45-120 minutes',
                'instructions': 'Select a recipe that interests you but is within your skill level. Read the entire recipe before starting. Gather and prepare all ingredients (mise en place). Follow instructions carefully the first time. Taste and adjust seasonings as needed. Plate thoughtfully and enjoy mindfully. Make notes for future attempts.',
                'social_requirements': {'min_participants': 1, 'max_participants': 3},
                'primary_domain': 'leisure_culinary',
                'secondary_domains': [
                    {'domain': 'productive_cooking', 'strength': 100},
                    {'domain': 'creative_experimenting', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'consc_organization', 'level': 50, 'optional': False},
                    {'trait': 'open_creativity', 'level': 40, 'optional': True},
                    {'trait': 'consc_diligence', 'level': 30, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'cook_equipment', 'quantity': 1, 'optional': False},
                    {'resource': 'cook_ingredients', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_kitchen', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_manual_dexterity', 'level': 50, 'impact_type': 'adaptation', 'notes': 'Adaptive kitchen tools can help with chopping and manipulation'},
                    {'limitation': 'phys_standing', 'level': 40, 'impact_type': 'adaptation', 'notes': 'Seated preparation options can accommodate standing limitations'},
                    {'limitation': 'cogn_executive', 'level': 40, 'impact_type': 'adaptation', 'notes': 'Simplified recipes or breaking tasks into steps can help'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_practical', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in ability to learn new practical skills'},
                    {'belief': 'creativity_expression', 'impact_type': 'reinforce', 'notes': 'Supports belief in creative expression through practical means'}
                ],
                'tags': ['cooking', 'culinary', 'creativity', 'practical', 'sensory', 'skill-building']
            },
            {
                'code': 'leisure_movie_night',
                'name': 'Themed Movie Night',
                'description': 'Organizing and enjoying a movie viewing experience with a specific theme',
                'duration_range': '2-4 hours',
                'instructions': 'Select a theme (director, genre, era, etc.). Choose 1-2 films that fit the theme. Prepare thematically appropriate snacks and environment. Research interesting facts about the films to share. Discuss the films afterward, comparing themes, techniques, or performances.',
                'social_requirements': {'min_participants': 1, 'max_participants': 8, 'ideal_participants': 3},
                'primary_domain': 'leisure_entertainment',
                'secondary_domains': [
                    {'domain': 'soc_connecting', 'strength': 50},
                    {'domain': 'creative_appreciation', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'open_aesthetic', 'level': 40, 'optional': False},
                    {'trait': 'open_intellect', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'tech_screen', 'quantity': 1, 'optional': False},
                    {'resource': 'leisure_snacks', 'quantity': 1, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_comfortable', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_hearing', 'level': 40, 'impact_type': 'adaptation', 'notes': 'Subtitles or volume adjustments can accommodate hearing limitations'},
                    {'limitation': 'phys_vision', 'level': 50, 'impact_type': 'adaptation', 'notes': 'Audio descriptions or verbal summaries can help'}
                ],
                'belief_interactions': [
                    {'belief': 'appreciation_art', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in the value of artistic appreciation'},
                    {'belief': 'shared_experience', 'impact_type': 'reinforce', 'notes': 'Strengthens belief in the importance of shared cultural experiences'}
                ],
                'tags': ['entertainment', 'movies', 'relaxation', 'social', 'discussion', 'cultural']
            },
            {
                'code': 'leisure_casual_hiking',
                'name': 'Casual Nature Hike',
                'description': 'A leisurely walk through natural surroundings for enjoyment and relaxation',
                'duration_range': '30-120 minutes',
                'instructions': 'Choose an established trail appropriate for your fitness level. Wear comfortable clothing and appropriate footwear. Bring water and weather protection. Walk at a relaxed pace, taking time to observe surroundings. Take photos or mental notes of interesting features. Practice mindful awareness of natural sounds and sensations.',
                'social_requirements': {'min_participants': 1, 'max_participants': 6},
                'primary_domain': 'leisure_outdoor',
                'secondary_domains': [
                    {'domain': 'phys_low_intensity', 'strength': 50},
                    {'domain': 'refl_mindful', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_aesthetic', 'level': 40, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 20, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'outdoor_footwear', 'quantity': 1, 'optional': False},
                    {'resource': 'outdoor_water', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'outdoor_nature', 'optional': False},
                    {'env': 'outdoor_trail', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility', 'level': 60, 'impact_type': 'limitation', 'notes': 'Trails with uneven terrain may be challenging'},
                    {'limitation': 'phys_stamina', 'level': 50, 'impact_type': 'adaptation', 'notes': 'Choose shorter trails or incorporate rest stops'},
                    {'limitation': 'allergy_outdoor', 'level': 70, 'impact_type': 'limitation', 'notes': 'Seasonal allergies may affect outdoor enjoyment'}
                ],
                'belief_interactions': [
                    {'belief': 'connection_nature', 'impact_type': 'reinforce', 'notes': 'Reinforces connection to natural environment'},
                    {'belief': 'holistic_wellness', 'impact_type': 'reinforce', 'notes': 'Supports belief in balanced physical and mental well-being'}
                ],
                'tags': ['hiking', 'outdoor', 'nature', 'walking', 'relaxation', 'exploration']
            }
        ]
        
        # Create the activities
        created_count = 0
        for activity_data in activities:
            if not GenericActivity.objects.filter(code=activity_data['code']).exists():
                self._create_activity(activity_data, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                created_count += 1
        
        self.stdout.write(f"Created {created_count} leisure activities")

    def create_productive_activities(self, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct):
        """Create productive activities across different sub-domains including organization, planning, habit formation, and skill development"""
        activities = [
            # Organization & Planning Activities
            {
                'code': 'prod_desk_organization',
                'name': 'Workspace Organization',
                'description': 'A methodical decluttering and organization of your workspace to improve focus and efficiency',
                'duration_range': '30-60 minutes',
                'instructions': 'Remove everything from your desk. Clean the surface thoroughly. Sort items into categories: essential daily tools, occasional-use items, and things to discard/store elsewhere. Return only essential items to your primary workspace, arranging them by frequency of use. Create dedicated spaces for each category. Complete by testing the new arrangement with a 5-minute work simulation.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'productive_practical',
                'secondary_domains': [
                    {'domain': 'productive_mindful', 'strength': 30},
                    {'domain': 'productive_planning', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'consc_organization', 'level': 40, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_cleaning_tools', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_home_office', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_arms', 'level': 40, 'impact_type': 'limitation'},
                    {'limitation': 'phys_energy', 'level': 30, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_organization', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about organizational capabilities'},
                    {'belief': 'effort_belief_productivity', 'impact_type': 'reinforce', 'notes': 'Reinforces belief that effort leads to better productivity'}
                ],
                'tags': ['productive', 'organization', 'decluttering', 'focus', 'beginner-friendly']
            },
            {
                'code': 'prod_weekly_planning',
                'name': 'Weekly Planning Session',
                'description': 'A structured session to plan your upcoming week, prioritize tasks, and set realistic goals',
                'duration_range': '20-40 minutes',
                'instructions': 'Find a quiet space with your planner or digital planning tool. Begin by reviewing last week\'s achievements and unfinished tasks. List all upcoming commitments and deadlines. Break down large projects into actionable steps. Assign each task to a specific day, being mindful not to overcommit. Identify your top 3 priorities for the week. End by scheduling necessary preparation time for important events.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'productive_planning',
                'secondary_domains': [
                    {'domain': 'reflective_analytical', 'strength': 50},
                    {'domain': 'productive_practical', 'strength': 40},
                ],
                'trait_requirements': [
                    {'trait': 'consc_prudence', 'level': 50, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 40, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_planner', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cogn_focus', 'level': 60, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'goal_achievement_planning', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in structured planning for goal achievement'},
                    {'belief': 'change_orientation_control', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in ability to control outcomes through planning'}
                ],
                'tags': ['productive', 'planning', 'organization', 'goal-setting', 'time-management']
            },
            
            # Habit Formation Activities
            {
                'code': 'prod_habit_tracker',
                'name': 'Habit Tracker Setup and Usage',
                'description': 'Creating and implementing a personal habit tracking system to monitor and reinforce positive daily behaviors',
                'duration_range': '15-20 minutes',
                'instructions': 'Choose 1-3 specific habits you want to develop. Create a simple tracking system (paper, digital app, or spreadsheet). Define clear success criteria for each habit - be specific about what counts as completion. Design your tracking method with visual cues (checkboxes, colors, etc.). Place your tracker somewhere highly visible. Set a consistent daily review time. For the first week, focus on consistency rather than perfection.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'productive_habits',
                'secondary_domains': [
                    {'domain': 'productive_practical', 'strength': 50},
                    {'domain': 'reflective_mindful', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'consc_diligence', 'level': 45, 'optional': False},
                    {'trait': 'consc_prudence', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_journal', 'quantity': 1, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_home', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cogn_memory', 'level': 30, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_change', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about ability to change habits'},
                    {'belief': 'goal_achievement_consistency', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in incremental progress'}
                ],
                'tags': ['productive', 'habit-formation', 'self-improvement', 'accountability', 'beginner-friendly']
            },
            {
                'code': 'prod_morning_routine',
                'name': 'Morning Routine Establishment',
                'description': 'Designing and implementing a consistent morning routine to start each day with intention and productivity',
                'duration_range': '30-45 minutes',
                'instructions': 'Reflect on your ideal morning activities and energy levels. Design a 30-minute sequence of 3-5 activities that energize you (e.g., hydration, movement, planning). Create visual cues for each step (sticky notes, checklist). Prepare necessary items the night before. For the first week, set alarms for each segment of your routine. Perform the routine in the same order each day, adjusting timing as needed. Record observations about energy levels and mood effects.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'productive_habits',
                'secondary_domains': [
                    {'domain': 'productive_practical', 'strength': 60},
                    {'domain': 'refl_mindful', 'strength': 40},
                ],
                'trait_requirements': [
                    {'trait': 'consc_organization', 'level': 40, 'optional': False},
                    {'trait': 'emo_resilience', 'level': 30, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_home', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cogn_circadian', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'phys_energy', 'level': 50, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_discipline', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about self-discipline'},
                    {'belief': 'identity_proactive', 'impact_type': 'reinforce', 'notes': 'Reinforces identity as a proactive person'}
                ],
                'tags': ['productive', 'routine-building', 'morning', 'habit-formation', 'self-discipline']
            },
            
            # Skill Development Activities
            {
                'code': 'prod_delib_practice',
                'name': 'Deliberate Practice Session',
                'description': 'A focused skill development session using deliberate practice principles to target improvement in a specific area',
                'duration_range': '25-45 minutes',
                'instructions': 'Select one specific sub-skill to improve. Break it down into its fundamental components. Identify your current performance edge (what\'s challenging but achievable). Create a 25-minute practice structure focusing solely on that edge. Remove distractions completely. Set clear success metrics before beginning. During practice, maintain full concentration and immediately correct errors. After practice, briefly note what improved and what still needs work. Schedule your next practice session within 48 hours.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'productive_skill',
                'secondary_domains': [
                    {'domain': 'reflective_analytical', 'strength': 50},
                    {'domain': 'productive_focus', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'consc_diligence', 'level': 60, 'optional': False},
                    {'trait': 'open_inquisitiveness', 'level': 40, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_learning_tools', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_focus', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cogn_focus', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'phys_energy', 'level': 40, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'talent_innate', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about fixed talent versus skill development'},
                    {'belief': 'skill_learning_growth', 'impact_type': 'reinforce', 'notes': 'Reinforces growth mindset regarding skill acquisition'}
                ],
                'tags': ['productive', 'skill-development', 'deliberate-practice', 'focus', 'mastery']
            },
            {
                'code': 'prod_learn_reflection',
                'name': 'Learning Reflection Process',
                'description': 'A structured reflection process to consolidate learning from recent experiences or educational activities',
                'duration_range': '15-25 minutes',
                'instructions': 'Select a recent learning experience or project. Create three written sections: 1) What I did (facts and details), 2) What I learned (insights and lessons), and 3) How I\'ll apply this (specific future applications). For each section, write 3-5 detailed bullet points. Identify connections between this new knowledge and your existing skills. Determine one specific action you\'ll take within a week to apply what you\'ve learned. Schedule this action in your calendar before concluding.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'productive_skill',
                'secondary_domains': [
                    {'domain': 'reflective_analytical', 'strength': 70},
                    {'domain': 'productive_planning', 'strength': 40},
                ],
                'trait_requirements': [
                    {'trait': 'open_inquisitiveness', 'level': 50, 'optional': False},
                    {'trait': 'consc_prudence', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_journal', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cogn_memory', 'level': 40, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'skill_learning_metacognition', 'impact_type': 'reinforce', 'notes': 'Reinforces value of reflection in learning process'},
                    {'belief': 'potential_growth', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in continuous improvement'}
                ],
                'tags': ['productive', 'learning', 'reflection', 'knowledge-integration', 'metacognition']
            },
            
            # Time Management & Focus Activities
            {
                'code': 'prod_pomodoro',
                'name': 'Pomodoro Focus Session',
                'description': 'Using the Pomodoro Technique to enhance focus and productivity while managing mental energy',
                'duration_range': '30-90 minutes',
                'instructions': 'Select a single task to focus on. Remove potential distractions from your environment. Set a timer for 25 minutes and work exclusively on your chosen task until the timer sounds. Take a mandatory 5-minute break (stand up, stretch, hydrate). For longer sessions, repeat the cycle up to 4 times, then take a longer 15-30 minute break. Record what you accomplished during each session and note any insights about your focus patterns.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'productive_focus',
                'secondary_domains': [
                    {'domain': 'productive_practical', 'strength': 60},
                    {'domain': 'productive_time', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'consc_prudence', 'level': 40, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 50, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_timer', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_focus', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cogn_focus', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'phys_energy', 'level': 30, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'emotional_control_distraction', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about distractibility'},
                    {'belief': 'effort_belief_persistence', 'impact_type': 'reinforce', 'notes': 'Reinforces value of structured effort'}
                ],
                'tags': ['productive', 'focus', 'time-management', 'pomodoro', 'concentration']
            },
            {
                'code': 'prod_time_audit',
                'name': 'Personal Time Audit',
                'description': 'Conducting a systematic audit of how you spend your time to identify patterns and opportunities for optimization',
                'duration_range': '20-30 minutes',
                'instructions': 'Create a time log template with hourly slots. For 2-3 typical days, record your activities in 30-minute increments (can be done retroactively for yesterday). Categorize each activity (e.g., work, leisure, maintenance, transit). Highlight activities that energized you in one color and those that drained you in another. Calculate total time per category. Identify three patterns or insights about your time use. Determine one specific change to implement based on your findings.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'productive_time',
                'secondary_domains': [
                    {'domain': 'reflective_analytical', 'strength': 70},
                    {'domain': 'productive_planning', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'consc_organization', 'level': 40, 'optional': False},
                    {'trait': 'open_inquisitiveness', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_journal', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet_space', 'optional': False}
                ],
                'belief_interactions': [
                    {'belief': 'success_definition_efficiency', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about productivity and time use'},
                    {'belief': 'effort_belief_intention', 'impact_type': 'reinforce', 'notes': 'Reinforces connection between intention and time allocation'}
                ],
                'tags': ['productive', 'time-management', 'self-analysis', 'efficiency', 'optimization']
            },
            
            # Project & Task Management Activities
            {
                'code': 'prod_next_action',
                'name': 'Next Action Definition',
                'description': 'Breaking down complex projects into clear, actionable next steps to overcome procrastination and build momentum',
                'duration_range': '15-25 minutes',
                'instructions': 'Select a stalled project or ambiguous task. Write down exactly what the successful outcome looks like. List all constraints or requirements. Ask yourself: "What\'s the very next physical action needed to move forward?" Define this action in concrete, specific terms (e.g., "Email Sarah requesting the Q3 data" not "Get Q3 data"). Ensure the action takes less than 30 minutes to complete. Identify when and where you\'ll take this action. Remove any obstacles to completing it.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'productive_practical',
                'secondary_domains': [
                    {'domain': 'productive_planning', 'strength': 70},
                    {'domain': 'productive_habits', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'consc_diligence', 'level': 40, 'optional': False},
                    {'trait': 'consc_prudence', 'level': 50, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_planner', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cogn_decision', 'level': 50, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'fear_pattern_procrastination', 'impact_type': 'challenge', 'notes': 'Challenges avoidance behaviors'},
                    {'belief': 'goal_achievement_steps', 'impact_type': 'reinforce', 'notes': 'Reinforces incremental progress approach'}
                ],
                'tags': ['productive', 'task-management', 'GTD', 'procrastination', 'action-oriented']
            },
            {
                'code': 'prod_weekly_review',
                'name': 'Weekly Review Process',
                'description': 'A comprehensive weekly review to maintain organization, track progress, and adjust priorities for the coming week',
                'duration_range': '30-60 minutes',
                'instructions': 'Schedule an uninterrupted block at the end of your week. Start by clearing your workspace and inboxes (email, messages, notes). Review your completed tasks and milestones from the past week. Check all ongoing projects for next actions. Review upcoming deadlines and commitments. Capture any new ideas or tasks. Update your calendar and task list for the coming week. Define your top three priorities for next week. End by writing a brief reflection on the past week\'s progress and lessons.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'productive_planning',
                'secondary_domains': [
                    {'domain': 'reflective_analytical', 'strength': 60},
                    {'domain': 'productive_practical', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'consc_organization', 'level': 60, 'optional': False},
                    {'trait': 'consc_prudence', 'level': 50, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_planner', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cogn_focus', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'cogn_memory', 'level': 40, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'change_orientation_control', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in ability to manage developments'},
                    {'belief': 'goal_achievement_tracking', 'impact_type': 'reinforce', 'notes': 'Reinforces value of systematic progress monitoring'}
                ],
                'tags': ['productive', 'organization', 'review', 'planning', 'reflection']
            }
        ]
        
        # Create the activities
        created_count = 0
        for activity_data in activities:
            if not GenericActivity.objects.filter(code=activity_data['code']).exists():
                self._create_activity(activity_data, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                created_count += 1
        
        self.stdout.write(f"Created {created_count} productive activities")
    
    def create_exploratory_activities(self, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct):
        """Create exploratory activities across different sub-domains"""
        activities = [
            # Travel & Discovery
            {
                'code': 'expl_local_discovery',
                'name': 'Local Neighborhood Exploration',
                'description': 'Explore unfamiliar streets and areas in your neighborhood to discover new places and perspectives',
                'duration_range': '30-90 minutes',
                'instructions': 'Choose an area nearby that you rarely visit. Walk without a specific destination in mind, taking at least 3 turns you wouldn\'t normally take. Notice details like architecture, plants, community spaces, and local businesses. Take mental or physical notes of interesting discoveries.',
                'social_requirements': {'min_participants': 1, 'max_participants': 3},
                'primary_domain': 'exploratory_adventure',
                'secondary_domains': [
                    {'domain': 'phys_walking', 'strength': 50},
                    {'domain': 'refl_mindful', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_curiosity', 'level': 40, 'optional': False},
                    {'trait': 'extra_liveliness', 'level': 20, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'access_walkable_area', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_comfortable_shoes', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'outdoor_urban', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_lower', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'phys_sensory_visual', 'level': 50, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'world_view_safety', 'impact_type': 'challenge', 'notes': 'Gently challenges beliefs about familiar vs. unfamiliar environments'}
                ],
                'tags': ['exploratory', 'walking', 'discovery', 'urban', 'beginner-friendly']
            },
            
            # Sensory Exploration
            {
                'code': 'expl_sensory_blindfolded',
                'name': 'Blindfolded Sensory Experience',
                'description': 'Explore your environment using non-visual senses with a blindfold for heightened sensory awareness',
                'duration_range': '15-30 minutes',
                'instructions': 'With a trusted companion as guide, put on a blindfold in a safe environment. Explore the space using touch, smell, hearing, and taste (if appropriate). Have your companion guide you to different objects, textures, and sources of sound. Describe your experiences and perceptions without visual input.',
                'social_requirements': {'min_participants': 2, 'max_participants': 2},
                'primary_domain': 'exploratory_sensory',
                'secondary_domains': [
                    {'domain': 'refl_mindful', 'strength': 70},
                    {'domain': 'soc_trust', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'open_curiosity', 'level': 50, 'optional': False},
                    {'trait': 'emo_fearfulness', 'level': 40, 'optional': False, 'impact_type': 'limitation'},
                    {'trait': 'agree_trust', 'level': 60, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'equip_blindfold', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_safe_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_sensory_auditory', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'phys_sensory_tactile', 'level': 60, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'trust_others', 'impact_type': 'challenge', 'notes': 'Challenges trust and comfort with vulnerability'}
                ],
                'tags': ['exploratory', 'sensory', 'trust', 'intermediate']
            },
            
            # Culinary Exploration
            {
                'code': 'expl_new_cuisine',
                'name': 'Unfamiliar Cuisine Tasting',
                'description': 'Explore flavors and dishes from a cuisine you\'ve never tried before',
                'duration_range': '60-120 minutes',
                'instructions': 'Research a cuisine you\'ve never experienced. Visit a restaurant serving this cuisine or prepare 1-3 dishes at home following authentic recipes. Before tasting, learn about the cultural context of the food. As you taste, pay attention to unfamiliar flavors, textures, and ingredients. Note your responses and how they might differ from your usual preferences.',
                'social_requirements': {'min_participants': 1, 'max_participants': 4},
                'primary_domain': 'exploratory_culinary',
                'secondary_domains': [
                    {'domain': 'creative_culinary', 'strength': 50},
                    {'domain': 'leisure_food', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'open_unconventionality', 'level': 40, 'optional': False},
                    {'trait': 'open_aesthetic', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'finance_dining', 'quantity': 1, 'optional': True},
                    {'resource': 'equip_kitchen_basic', 'quantity': 1, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_kitchen', 'optional': True},
                    {'env': 'commercial_restaurant', 'optional': True}
                ],
                'limitation_considerations': [
                    {'limitation': 'health_dietary', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'phys_sensory_taste', 'level': 80, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'worldview_diversity', 'impact_type': 'reinforce', 'notes': 'Reinforces appreciation for cultural diversity'}
                ],
                'tags': ['exploratory', 'culinary', 'cultural', 'sensory', 'beginner-friendly']
            },
            
            # Digital Exploration
            {
                'code': 'expl_virtual_tour',
                'name': 'Virtual Museum or Landmark Tour',
                'description': 'Explore world-class museums, historical sites, or natural wonders through immersive virtual tours',
                'duration_range': '30-60 minutes',
                'instructions': 'Select a museum, historical site, or natural wonder that offers a virtual tour. Navigate through the virtual space, taking time to examine exhibits, architecture, or natural features in detail. Research contextual information about what you\'re seeing. Note questions that arise and interesting discoveries.',
                'social_requirements': {'min_participants': 1, 'max_participants': 2},
                'primary_domain': 'exploratory_cultural',
                'secondary_domains': [
                    {'domain': 'int_learning', 'strength': 70},
                    {'domain': 'leisure_media', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_curiosity', 'level': 50, 'optional': False},
                    {'trait': 'open_intellectual', 'level': 40, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'tech_computer_internet', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_full', 'level': 10, 'impact_type': 'adaptation', 'notes': 'Provides exploration for those with mobility limitations'},
                    {'limitation': 'phys_sensory_visual', 'level': 80, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'worldview_diversity', 'impact_type': 'reinforce', 'notes': 'Reinforces appreciation for cultural and historical diversity'}
                ],
                'tags': ['exploratory', 'virtual', 'cultural', 'educational', 'beginner-friendly']
            },
            
            # Nature Exploration
            {
                'code': 'expl_nature_microadventure',
                'name': 'Nature Microadventure',
                'description': 'A brief outdoor adventure exploring a natural area you haven\'t visited before',
                'duration_range': '60-180 minutes',
                'instructions': 'Identify a nearby natural area (forest, lake, river, park) that you haven\'t explored. Pack water, a small snack, and basic safety supplies. Upon arrival, choose a path or direction that interests you. Move at a relaxed pace, pausing frequently to observe plants, wildlife, geological features, and the broader ecosystem. Document interesting finds through photos or notes.',
                'social_requirements': {'min_participants': 1, 'max_participants': 4},
                'primary_domain': 'exploratory_nature',
                'secondary_domains': [
                    {'domain': 'phys_outdoor', 'strength': 70},
                    {'domain': 'refl_mindful', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_curiosity', 'level': 50, 'optional': False},
                    {'trait': 'emo_fearfulness', 'level': 30, 'optional': False, 'impact_type': 'limitation'}
                ],
                'resource_requirements': [
                    {'resource': 'equip_outdoor_basic', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_water_bottle', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'outdoor_nature', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_lower', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'phys_endurance', 'level': 50, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'world_view_nature', 'impact_type': 'reinforce', 'notes': 'Reinforces connection with natural world'}
                ],
                'tags': ['exploratory', 'nature', 'outdoor', 'adventure', 'intermediate']
            },
            
            # Risk-Taking (Emotional)
            {
                'code': 'expl_vulnerability_share',
                'name': 'Vulnerability Sharing Circle',
                'description': 'A structured experience of sharing personal vulnerabilities in a safe group setting',
                'duration_range': '60-90 minutes',
                'instructions': 'Gather 3-5 trusted friends or family members. Create clear agreements about confidentiality and non-judgment. Each person takes turns sharing a personal vulnerability, challenge, or growth area for 3-5 minutes without interruption. After each share, other participants may offer brief supportive responses but no advice. Close with reflections on the experience of both sharing and listening.',
                'social_requirements': {'min_participants': 3, 'max_participants': 6},
                'primary_domain': 'exploratory_emotional',
                'secondary_domains': [
                    {'domain': 'soc_connecting', 'strength': 80},
                    {'domain': 'emo_expression', 'strength': 90},
                ],
                'trait_requirements': [
                    {'trait': 'agree_trust', 'level': 70, 'optional': False},
                    {'trait': 'emo_anxiety', 'level': 50, 'optional': False, 'impact_type': 'limitation'},
                    {'trait': 'extra_sociability', 'level': 40, 'optional': True}
                ],
                'resource_requirements': [],
                'env_requirements': [
                    {'env': 'ind_private_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_social_anxiety', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'cog_emotional_regulation', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'vulnerability_strength', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about vulnerability as weakness'},
                    {'belief': 'social_connection', 'impact_type': 'reinforce', 'notes': 'Reinforces beliefs about authentic connection'}
                ],
                'tags': ['exploratory', 'emotional', 'social', 'vulnerability', 'advanced']
            },
            
            # Creative Risk-Taking
            {
                'code': 'expl_improv_session',
                'name': 'Improvisational Performance',
                'description': 'An introductory experience in improvisational performance without preparation',
                'duration_range': '30-60 minutes',
                'instructions': 'With 2-6 people, take turns initiating improvisational scenarios. Start with simple word association games, then progress to short scenes where participants must respond in the moment without planning. Use the "Yes, and..." principle to build on others\' contributions rather than negating them. Rotate who initiates scenes to ensure everyone practices both leading and following.',
                'social_requirements': {'min_participants': 2, 'max_participants': 7},
                'primary_domain': 'exploratory_creative',
                'secondary_domains': [
                    {'domain': 'creative_performance', 'strength': 80},
                    {'domain': 'soc_comm', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'open_creativity', 'level': 60, 'optional': False},
                    {'trait': 'extra_sociability', 'level': 50, 'optional': False},
                    {'trait': 'emo_anxiety', 'level': 40, 'optional': False, 'impact_type': 'limitation'}
                ],
                'resource_requirements': [],
                'env_requirements': [
                    {'env': 'ind_open_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_social_anxiety', 'level': 80, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'control_perfection', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about control and perfectionism'}
                ],
                'tags': ['exploratory', 'creative', 'performance', 'social', 'intermediate']
            },
            
            # Physical Risk-Taking
            {
                'code': 'expl_bouldering_intro',
                'name': 'Introductory Indoor Bouldering',
                'description': 'Try indoor rock climbing without ropes (bouldering) at a climbing gym with safety mats',
                'duration_range': '60-90 minutes',
                'instructions': 'Visit a climbing gym that offers bouldering. Take an introductory lesson to learn proper falling technique and basic climbing movements. Start with the easiest routes (V0-V1) and focus on balance and technique rather than strength. Rest between attempts and observe others\' climbing styles. Challenge yourself to complete at least three different routes.',
                'social_requirements': {'min_participants': 1, 'max_participants': 3},
                'primary_domain': 'exploratory_physical',
                'secondary_domains': [
                    {'domain': 'phys_strength', 'strength': 60},
                    {'domain': 'phys_balance', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'open_curiosity', 'level': 40, 'optional': False},
                    {'trait': 'emo_fearfulness', 'level': 60, 'optional': False, 'impact_type': 'limitation'},
                    {'trait': 'consc_prudence', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'finance_activity_fee', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_athletic_wear', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'commercial_gym', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_upper', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'phys_strength', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'health_joint', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_physical', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about physical capabilities and risk tolerance'}
                ],
                'tags': ['exploratory', 'physical', 'adventure', 'challenge', 'intermediate']
            },
            
            # Cultural Exploration
            {
                'code': 'expl_cultural_event',
                'name': 'Cultural Festival Immersion',
                'description': 'Attend a cultural festival or celebration from a tradition different from your own',
                'duration_range': '120-240 minutes',
                'instructions': 'Research upcoming cultural festivals in your area. Choose one representing a culture or tradition unfamiliar to you. Before attending, learn basics about the culture\'s history, values, and festival significance. At the event, sample traditional foods, observe performances, engage with artisans, and respectfully participate in appropriate activities. Approach with curiosity and openness, asking questions when appropriate.',
                'social_requirements': {'min_participants': 1, 'max_participants': 4},
                'primary_domain': 'exploratory_cultural',
                'secondary_domains': [
                    {'domain': 'soc_connecting', 'strength': 50},
                    {'domain': 'leisure_social', 'strength': 40},
                    {'domain': 'int_learning', 'strength': 60}
                ],
                'trait_requirements': [
                    {'trait': 'open_unconventionality', 'level': 50, 'optional': False},
                    {'trait': 'extra_sociability', 'level': 40, 'optional': True},
                    {'trait': 'agree_flexibility', 'level': 50, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'finance_entertainment', 'quantity': 1, 'optional': True},
                    {'resource': 'access_transportation', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'outdoor_public', 'optional': True},
                    {'env': 'commercial_event', 'optional': True}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_sensory_auditory', 'level': 50, 'impact_type': 'limitation', 'notes': 'Events may be loud'},
                    {'limitation': 'cog_social_anxiety', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'worldview_diversity', 'impact_type': 'reinforce', 'notes': 'Reinforces appreciation for cultural diversity'},
                    {'belief': 'identity_ethnocentric', 'impact_type': 'challenge', 'notes': 'Challenges ethnocentric beliefs'}
                ],
                'tags': ['exploratory', 'cultural', 'social', 'learning', 'intermediate']
            },
            
            # Advanced Explorer
            {
                'code': 'expl_solo_adventure',
                'name': 'Solo Day Adventure',
                'description': 'A full-day solo expedition to a new location, following minimal planning but maximum exploration',
                'duration_range': '4-8 hours',
                'instructions': 'Select a destination 1-3 hours from home you\'ve never visited (natural area, small town, urban district). Create a minimal plan with safety considerations, but leave most of the day unstructured. Bring essentials (water, food, map, etc.) and transportation. Upon arrival, follow your curiosity, making spontaneous decisions about where to go. Interact with locals, discover hidden spots, and try unplanned activities. Document your journey and reflections.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'exploratory_adventure',
                'secondary_domains': [
                    {'domain': 'exploratory_challenge', 'strength': 80},
                    {'domain': 'refl_solitude', 'strength': 70},
                    {'domain': 'int_learning', 'strength': 50}
                ],
                'trait_requirements': [
                    {'trait': 'open_curiosity', 'level': 80, 'optional': False},
                    {'trait': 'emo_fearfulness', 'level': 70, 'optional': False, 'impact_type': 'limitation'},
                    {'trait': 'consc_organization', 'level': 60, 'optional': False},
                    {'trait': 'extra_social_boldness', 'level': 70, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'finance_travel', 'quantity': 1, 'optional': False},
                    {'resource': 'access_transportation', 'quantity': 1, 'optional': False},
                    {'resource': 'tech_navigation', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_day_pack', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'outdoor_variable', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_full', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'phys_endurance', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'cog_spatial_navigation', 'level': 80, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_independence', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about independence and self-reliance'},
                    {'belief': 'world_view_safety', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about comfort and safety'},
                    {'belief': 'change_orientation', 'impact_type': 'reinforce', 'notes': 'Reinforces comfort with uncertainty and spontaneity'}
                ],
                'tags': ['exploratory', 'adventure', 'solo', 'challenge', 'expert']
            }
        ]
        
        # Create the activities
        created_count = 0
        for activity_data in activities:
            if not GenericActivity.objects.filter(code=activity_data['code']).exists():
                self._create_activity(activity_data, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                created_count += 1
        
        self.stdout.write(f"Created {created_count} exploratory activities")

    def create_reflective_activities(self, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct):
        """Create reflective activities across different sub-domains"""
        activities = [
            # Beginner-level mindfulness practice
            {
                'code': 'refl_mindful_breathing',
                'name': 'Five-Minute Mindful Breathing',
                'description': 'A short mindful breathing exercise to center yourself and develop present-moment awareness',
                'duration_range': '5-10 minutes',
                'instructions': 'Find a comfortable seated position. Set a timer for 5 minutes. Close your eyes or maintain a soft gaze. Focus your attention on your breath - the sensation of air moving in and out of your body. When your mind wanders, gently bring your attention back to your breath without judgment.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'refl_mindful',
                'secondary_domains': [
                    {'domain': 'refl_present', 'strength': 70},
                    {'domain': 'emotional_awareness', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_present', 'level': 20, 'optional': False},
                    {'trait': 'consc_focus', 'level': 30, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cognitive_attention', 'level': 30, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_mental', 'impact_type': 'reinforce', 'notes': 'Builds confidence in ability to regulate attention'}
                ],
                'tags': ['mindfulness', 'beginner-friendly', 'stress-reduction', 'meditation']
            },
            
            # Intermediate journaling practice
            {
                'code': 'refl_gratitude_journal',
                'name': 'Gratitude Journaling Practice',
                'description': 'A structured journaling exercise focused on cultivating gratitude and positive awareness',
                'duration_range': '10-15 minutes',
                'instructions': 'Find a quiet space with your journal. Set a timer for 10 minutes. Write down three things you\'re grateful for today, being as specific as possible. For each item, explore why it matters to you and how it affects your life. Try to identify new items each day rather than repeating the same ones.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'refl_journal',
                'secondary_domains': [
                    {'domain': 'emotional_positive', 'strength': 70},
                    {'domain': 'creative_writing', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_introspection', 'level': 40, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 35, 'optional': False},
                    {'trait': 'emo_vulnerability', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_journal', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_writing_tool', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_private', 'optional': True}
                ],
                'belief_interactions': [
                    {'belief': 'potential_growth', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in positive change potential'},
                    {'belief': 'avoidance_negativity', 'impact_type': 'challenge', 'notes': 'Challenges tendency to focus only on negative aspects'}
                ],
                'tags': ['journaling', 'gratitude', 'intermediate', 'emotional-well-being']
            },
            
            # Advanced self-reflection practice
            {
                'code': 'refl_values_alignment',
                'name': 'Values Alignment Reflection',
                'description': 'A deep reflective process examining how your daily actions align with your core values',
                'duration_range': '30-40 minutes',
                'instructions': 'Begin by listing 5-7 core values that are most important to you (e.g., honesty, compassion, growth). For each value, reflect on: 1) How this value shows up in your life currently, 2) Areas where your actions may not align with this value, 3) One specific change you could make to better embody this value. Write detailed notes for each section.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'refl_identity',
                'secondary_domains': [
                    {'domain': 'refl_journal', 'strength': 70},
                    {'domain': 'emotional_awareness', 'strength': 50},
                    {'domain': 'intellectual_philosophy', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_introspection', 'level': 60, 'optional': False},
                    {'trait': 'honhum_sincere', 'level': 50, 'optional': False},
                    {'trait': 'consc_prudence', 'level': 45, 'optional': True},
                    {'trait': 'open_creativity', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_journal', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet', 'optional': False},
                    {'env': 'ind_private', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cognitive_metacognition', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'emotional_regulation', 'level': 50, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'identity_core', 'impact_type': 'challenge', 'notes': 'Challenges assumptions about fixed identity'},
                    {'belief': 'meaning_purpose', 'impact_type': 'reinforce', 'notes': 'Reinforces connection to deeper meaning'}
                ],
                'tags': ['values', 'advanced', 'self-reflection', 'purpose']
            },
            
            # Loving-kindness meditation (intermediate)
            {
                'code': 'refl_loving_kindness',
                'name': 'Loving-Kindness Meditation',
                'description': 'A meditation practice focused on developing compassion for yourself and others',
                'duration_range': '15-20 minutes',
                'instructions': 'Sit comfortably with eyes closed. Begin by directing well-wishes toward yourself using phrases like "May I be happy. May I be healthy. May I be safe." After 5 minutes, direct these wishes toward a loved one. Next, toward a neutral person. Finally, toward someone challenging. Maintain a steady breath throughout.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'refl_mindful',
                'secondary_domains': [
                    {'domain': 'emotional_compassion', 'strength': 70},
                    {'domain': 'soc_empathy', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'agree_compassion', 'level': 50, 'optional': False},
                    {'trait': 'emo_vulnerability', 'level': 40, 'optional': False},
                    {'trait': 'open_present', 'level': 35, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cognitive_attention', 'level': 40, 'impact_type': 'limitation'},
                    {'limitation': 'emotional_regulation', 'level': 50, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'social_belonging', 'impact_type': 'reinforce', 'notes': 'Reinforces connection to others'},
                    {'belief': 'self_worth', 'impact_type': 'challenge', 'notes': 'Challenges negative self-perception'}
                ],
                'tags': ['meditation', 'compassion', 'intermediate', 'emotional-development']
            },
            
            # Body scan meditation (beginner to intermediate)
            {
                'code': 'refl_body_scan',
                'name': 'Progressive Body Scan',
                'description': 'A somatic awareness meditation that guides attention through the entire body',
                'duration_range': '15-25 minutes',
                'instructions': 'Lie down comfortably or sit in a relaxed position. Starting from your toes and moving upward, systematically bring attention to each part of your body. Notice sensations without judgment - temperature, pressure, tingling, tension. Spend about 30 seconds on each body region before moving to the next.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'refl_somatic',
                'secondary_domains': [
                    {'domain': 'refl_mindful', 'strength': 70},
                    {'domain': 'phys_relax', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'open_present', 'level': 30, 'optional': False},
                    {'trait': 'consc_focus', 'level': 35, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_comfortable', 'optional': False},
                    {'env': 'ind_quiet', 'optional': True}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_spine', 'level': 30, 'impact_type': 'adaptation', 'notes': 'Can be performed in any comfortable position'},
                    {'limitation': 'cognitive_attention', 'level': 40, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'mind_body_connection', 'impact_type': 'reinforce', 'notes': 'Reinforces mind-body awareness'},
                    {'belief': 'emotional_embodiment', 'impact_type': 'reinforce', 'notes': 'Builds awareness of emotional states as physical sensations'}
                ],
                'tags': ['meditation', 'body-awareness', 'stress-reduction', 'beginner-friendly']
            },
            
            # Expert-level contemplative practice
            {
                'code': 'refl_vision_quest',
                'name': 'Personal Vision Quest',
                'description': 'An intensive solo retreat designed for deep personal insight and transformation',
                'duration_range': '4-8 hours',
                'instructions': 'Prepare by selecting a natural location where you can be alone and undisturbed. Bring minimal supplies: water, journal, and weather-appropriate clothing. Begin with setting an intention for your quest. Spend the time in contemplation, allowing yourself to be present with whatever arises - thoughts, emotions, insights. Maintain periods of silent sitting, walking meditation, and journaling. End by capturing key insights and creating a symbolic ritual to mark the experience.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'refl_contemplative',
                'secondary_domains': [
                    {'domain': 'refl_present', 'strength': 70},
                    {'domain': 'refl_identity', 'strength': 70},
                    {'domain': 'exploratory_self', 'strength': 50},
                    {'domain': 'phys_outdoor', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_introspection', 'level': 70, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 60, 'optional': False},
                    {'trait': 'emo_vulnerability', 'level': 65, 'optional': False},
                    {'trait': 'open_aesthetics', 'level': 40, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_journal', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_water_bottle', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'outdoor_natural', 'optional': False},
                    {'env': 'outdoor_isolated', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_general', 'level': 50, 'impact_type': 'limitation'},
                    {'limitation': 'cognitive_metacognition', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'emotional_regulation', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'identity_core', 'impact_type': 'challenge', 'notes': 'Deeply challenges assumptions about fixed identity'},
                    {'belief': 'meaning_purpose', 'impact_type': 'reinforce', 'notes': 'Strongly reinforces connection to life meaning'},
                    {'belief': 'change_orientation', 'impact_type': 'challenge', 'notes': 'Challenges resistance to transformation'}
                ],
                'tags': ['expert', 'transformative', 'vision-quest', 'solitude', 'nature']
            },
            
            # Beginner-friendly mindful walking
            {
                'code': 'refl_mindful_walking',
                'name': 'Mindful Walking Practice',
                'description': 'A simple walking meditation that connects movement, breath, and awareness',
                'duration_range': '10-20 minutes',
                'instructions': 'Find a quiet path where you can walk uninterrupted. Walk at a slightly slower pace than normal. Bring your attention to the sensation of walking - your feet touching the ground, the movement of your legs, and the rhythm of your breath. When your mind wanders, gently return focus to the physical sensations of walking.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'refl_movement',
                'secondary_domains': [
                    {'domain': 'refl_mindful', 'strength': 70},
                    {'domain': 'phys_light', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_present', 'level': 25, 'optional': False},
                    {'trait': 'consc_focus', 'level': 20, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'outdoor_path', 'optional': True},
                    {'env': 'ind_spacious', 'optional': True}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_lower', 'level': 40, 'impact_type': 'limitation', 'notes': 'Can be adapted to seated movement for those with mobility limitations'}
                ],
                'belief_interactions': [
                    {'belief': 'mind_body_connection', 'impact_type': 'reinforce', 'notes': 'Strengthens awareness of mind-body integration'}
                ],
                'tags': ['mindfulness', 'walking', 'beginner-friendly', 'movement']
            },
            
            # Intermediate reflection practice
            {
                'code': 'refl_life_review',
                'name': 'Life Chapter Review',
                'description': 'A reflective practice examining significant chapters of your life and extracting wisdom',
                'duration_range': '30-45 minutes',
                'instructions': 'Divide your life into 3-5 significant chapters (e.g., childhood, education, career beginnings). For each chapter, write responses to: 1) What were the defining moments? 2) What important lesson did you learn? 3) How did this chapter shape who you are today? 4) If you could offer advice to yourself during this time, what would it be?',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'refl_life_review',
                'secondary_domains': [
                    {'domain': 'refl_journal', 'strength': 70},
                    {'domain': 'emotional_insight', 'strength': 50},
                    {'domain': 'emotional_processing', 'strength': 40},
                ],
                'trait_requirements': [
                    {'trait': 'open_introspection', 'level': 50, 'optional': False},
                    {'trait': 'emo_vulnerability', 'level': 45, 'optional': False},
                    {'trait': 'consc_organization', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_journal', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_private', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cognitive_memory', 'level': 50, 'impact_type': 'limitation'},
                    {'limitation': 'emotional_processing', 'level': 50, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'meaning_purpose', 'impact_type': 'reinforce', 'notes': 'Strengthens narrative coherence and life meaning'},
                    {'belief': 'identity_core', 'impact_type': 'challenge', 'notes': 'Challenges fixed self-concept by highlighting growth over time'}
                ],
                'tags': ['self-reflection', 'intermediate', 'life-story', 'meaning-making']
            },
            
            # Advanced reflective dialogue practice
            {
                'code': 'refl_shadow_dialogue',
                'name': 'Shadow Self Dialogue',
                'description': 'A journaling practice to engage with disowned or repressed aspects of the self',
                'duration_range': '25-40 minutes',
                'instructions': 'Begin by identifying a trait, emotion, or behavior you tend to judge or avoid in yourself. Create a written dialogue between your conscious self and this "shadow" aspect. Start with introducing yourself to this part, asking it questions about its purpose and needs. Allow responses to emerge without censoring. Conclude by finding one small step toward integration or acceptance of this aspect.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'refl_shadow',
                'secondary_domains': [
                    {'domain': 'refl_journal', 'strength': 70},
                    {'domain': 'emotional_processing', 'strength': 70},
                    {'domain': 'creative_writing', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_introspection', 'level': 60, 'optional': False},
                    {'trait': 'emo_vulnerability', 'level': 70, 'optional': False},
                    {'trait': 'honhum_sincere', 'level': 55, 'optional': False},
                    {'trait': 'open_creativity', 'level': 40, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_journal', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_private', 'optional': False},
                    {'env': 'ind_quiet', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'emotional_regulation', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'cognitive_metacognition', 'level': 65, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'emotional_control', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about emotional suppression'},
                    {'belief': 'vulnerability', 'impact_type': 'challenge', 'notes': 'Directly challenges fear of vulnerability'},
                    {'belief': 'identity_core', 'impact_type': 'challenge', 'notes': 'Challenges simplified self-concept'}
                ],
                'tags': ['advanced', 'shadow-work', 'psychological', 'integration', 'emotional-growth']
            },
            
            # Reflective practice with sensory component
            {
                'code': 'refl_tea_ceremony',
                'name': 'Mindful Tea Ceremony',
                'description': 'A contemplative ritual using tea preparation and consumption as a focus for presence',
                'duration_range': '15-25 minutes',
                'instructions': 'Select a quiet space and a favorite tea. Bring full awareness to each step: heating water, preparing your cup, steeping the tea. Notice the sounds, scents, colors, and steam. When ready to drink, take small sips, fully experiencing the temperature, taste, and sensation. Between sips, notice your breath and bodily sensations. Maintain silence throughout the practice.',
                'social_requirements': {'min_participants': 1, 'max_participants': 2},
                'primary_domain': 'refl_ritual',
                'secondary_domains': [
                    {'domain': 'refl_present', 'strength': 70},
                    {'domain': 'refl_somatic', 'strength': 50},
                    {'domain': 'leisure_relaxation', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_present', 'level': 40, 'optional': False},
                    {'trait': 'consc_focus', 'level': 30, 'optional': True},
                    {'trait': 'open_aesthetics', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'food_tea', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_kettle', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_arms', 'level': 40, 'impact_type': 'adaptation', 'notes': 'Can be adapted with assistance for tea preparation'}
                ],
                'belief_interactions': [
                    {'belief': 'present_moment', 'impact_type': 'reinforce', 'notes': 'Reinforces value of present moment awareness'},
                    {'belief': 'ritual_meaning', 'impact_type': 'reinforce', 'notes': 'Builds appreciation for ritual and intention'}
                ],
                'tags': ['mindfulness', 'ritual', 'sensory-awareness', 'intermediate', 'tea']
            }
        ]
        
        # Create the activities
        created_count = 0
        for activity_data in activities:
            if not GenericActivity.objects.filter(code=activity_data['code']).exists():
                self._create_activity(activity_data, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                created_count += 1
        
        self.stdout.write(f"Created {created_count} reflective activities")
    
    def create_emotional_activities(self, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct):
        """Create emotional activities across different sub-domains focusing on emotional awareness, expression, and regulation"""
        activities = [
            # Emotional awareness activities
            {
                'code': 'emotional_daily_check',
                'name': 'Emotional Check-In',
                'description': 'A brief daily practice of identifying and labeling current emotions to build emotional awareness',
                'duration_range': '5-10 minutes',
                'instructions': 'Find a quiet moment. Take three deep breaths. Ask yourself: "What am I feeling right now?" Identify the primary emotion(s) and any physical sensations associated with them. Write down the emotion(s) and briefly note what might be causing them.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'emotional_awareness',
                'secondary_domains': [
                    {'domain': 'refl_mindful', 'strength': 70},
                    {'domain': 'refl_journal', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_self_aware', 'level': 20, 'optional': False},
                    {'trait': 'emo_sensitivity', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'stationery_journal', 'quantity': 1, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet_space', 'optional': False}
                ],
                'belief_interactions': [
                    {'belief': 'emotional_control', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about ignoring or suppressing emotions'}
                ],
                'tags': ['emotional', 'awareness', 'beginner-friendly', 'daily-practice']
            },
            
            # Emotional expression activities
            {
                'code': 'emotional_letter_release',
                'name': 'Unsent Letter',
                'description': 'Writing an unsent letter to express difficult emotions in a safe, controlled way',
                'duration_range': '15-30 minutes',
                'instructions': 'Identify a situation or person that evokes strong emotions. Write a letter expressing your complete thoughts and feelings without censoring yourself. Be honest about your hurt, anger, or disappointment. After writing, you can choose to safely destroy the letter as a symbolic release.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'emotional_expression',
                'secondary_domains': [
                    {'domain': 'creative_writing', 'strength': 50},
                    {'domain': 'refl_cbt', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_self_aware', 'level': 40, 'optional': False},
                    {'trait': 'emo_sensitivity', 'level': 50, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'stationery_paper', 'quantity': 3, 'optional': False},
                    {'resource': 'stationery_pen', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_private_room', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cognitive_focus', 'level': 40, 'impact_type': 'limitation'},
                    {'limitation': 'emo_regulation', 'level': 60, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'vulnerability', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about emotional vulnerability being weakness'}
                ],
                'tags': ['emotional', 'expression', 'writing', 'intermediate', 'catharsis']
            },
            
            # Emotional regulation activities
            {
                'code': 'emotional_calm_anchor',
                'name': 'Emotional Anchoring Technique',
                'description': 'A practice for developing an emotional "anchor" to help regulate intense emotions',
                'duration_range': '10-15 minutes',
                'instructions': 'Sit comfortably and recall a time when you felt completely calm, safe, and at peace. Notice all sensory details of this memory. What did you see? Hear? Feel? As you immerse in this memory, choose a physical gesture (like pressing thumb and forefinger together) to serve as your "anchor." Practice this anchor while in the calm state. In daily life, use this anchor when you need to regulate strong emotions.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'emotional_regulation',
                'secondary_domains': [
                    {'domain': 'refl_mindful', 'strength': 70},
                    {'domain': 'refl_cbt', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'open_self_aware', 'level': 40, 'optional': False},
                    {'trait': 'consc_discipline', 'level': 50, 'optional': False},
                    {'trait': 'emo_sensitivity', 'level': 60, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'emo_regulation', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'cognitive_focus', 'level': 50, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'emotional_control', 'impact_type': 'enhancement', 'notes': 'Enhances beliefs about ability to regulate emotions'},
                    {'belief': 'self_efficacy_general', 'impact_type': 'enhancement', 'notes': 'Reinforces sense of personal agency'}
                ],
                'tags': ['emotional', 'regulation', 'technique', 'intermediate', 'mindfulness']
            },
            
            # Advanced emotional awareness and intelligence
            {
                'code': 'emotional_shadow_integration',
                'name': 'Shadow Emotion Integration',
                'description': 'An advanced practice for identifying and integrating disowned or "shadow" emotions',
                'duration_range': '45-60 minutes',
                'instructions': 'Begin with 5 minutes of centering meditation. Reflect on a pattern of emotional triggers that consistently affect you strongly. Ask: "What emotion am I avoiding or denying here?" Write without censoring for 20 minutes about the uncomfortable emotion, exploring its origins and messages. Then create a dialogue with this emotion, asking what it needs and how it might serve you. Close with a self-compassion meditation.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'emotional_awareness',
                'secondary_domains': [
                    {'domain': 'refl_depth', 'strength': 80},
                    {'domain': 'refl_journal', 'strength': 50},
                    {'domain': 'spiritual_internal', 'strength': 30}
                ],
                'trait_requirements': [
                    {'trait': 'open_self_aware', 'level': 80, 'optional': False},
                    {'trait': 'emo_sensitivity', 'level': 70, 'optional': False},
                    {'trait': 'open_complexity', 'level': 60, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'stationery_journal', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_private_room', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'emo_regulation', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'cognitive_focus', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'psych_anxiety', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_worth', 'impact_type': 'challenge', 'notes': 'Challenges core beliefs about acceptability of all emotional states'},
                    {'belief': 'vulnerability', 'impact_type': 'challenge', 'notes': 'Deeply challenges resistance to emotional vulnerability'}
                ],
                'tags': ['emotional', 'advanced', 'depth-work', 'shadow', 'integration', 'psychological']
            },
            
            # Social-emotional activities
            {
                'code': 'emotional_empathy_practice',
                'name': 'Active Empathy Practice',
                'description': 'A structured conversation to develop emotional empathy and perspective-taking',
                'duration_range': '20-30 minutes',
                'instructions': 'With a partner, take turns sharing a current challenge (nothing too personal for this exercise). As the listener, focus fully on understanding their emotional experience. Ask: "How does that feel?" and "What\'s that like for you?" without offering solutions. Reflect back what you hear about their emotions. Then switch roles. Afterward, discuss what you learned about each other\'s emotional experiences.',
                'social_requirements': {'min_participants': 2, 'max_participants': 2},
                'primary_domain': 'emotional_empathy',
                'secondary_domains': [
                    {'domain': 'soc_comm', 'strength': 80},
                    {'domain': 'soc_connecting', 'strength': 60}
                ],
                'trait_requirements': [
                    {'trait': 'extra_sociability', 'level': 40, 'optional': False},
                    {'trait': 'agree_gentleness', 'level': 50, 'optional': False},
                    {'trait': 'emo_sensitivity', 'level': 60, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'social_anxiety', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'comm_verbal', 'level': 50, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'social_connection', 'impact_type': 'enhancement', 'notes': 'Reinforces beliefs about the value of emotional connection'}
                ],
                'tags': ['emotional', 'social', 'empathy', 'listening', 'intermediate', 'communication']
            },
            
            # Emotional creativity activities
            {
                'code': 'emotional_color_expression',
                'name': 'Emotion Color Mapping',
                'description': 'An artistic exercise for visualizing and expressing emotions through color and form',
                'duration_range': '20-40 minutes',
                'instructions': 'Gather art supplies with various colors. Take a few moments to center yourself and identify 3-5 emotions you\'re currently experiencing or have felt strongly recently. For each emotion, choose colors that represent it to you. Create an abstract expression of each emotion using those colors. No artistic skill required—focus on the process of translating feelings to visual form. When complete, reflect on any insights about your emotional landscape.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'emotional_expression',
                'secondary_domains': [
                    {'domain': 'creative_visual', 'strength': 70},
                    {'domain': 'emotional_awareness', 'strength': 50}
                ],
                'trait_requirements': [
                    {'trait': 'open_creativity', 'level': 40, 'optional': False},
                    {'trait': 'emo_sensitivity', 'level': 40, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'art_basic_supplies', 'quantity': 1, 'optional': False},
                    {'resource': 'art_paper', 'quantity': 5, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_creative_space', 'optional': True},
                    {'env': 'ind_home', 'optional': True}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_fine_motor', 'level': 30, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'open_creativity', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about creative self-expression'}
                ],
                'tags': ['emotional', 'creative', 'artistic', 'expression', 'beginner-friendly', 'visual']
            },
            
            # Emotional wisdom development
            {
                'code': 'emotional_pattern_mapping',
                'name': 'Emotional Pattern Mapping',
                'description': 'An analytical exercise to identify recurring emotional patterns and triggers',
                'duration_range': '30-45 minutes',
                'instructions': 'Create a table with columns for: Situation, Initial Emotion, Intensity (1-10), Secondary Emotions, Thoughts, Physical Sensations, and Behaviors. For one week, record instances of strong emotions. After gathering data, look for patterns in triggers, thought processes, and reactive behaviors. Circle recurring themes. Write a reflection on what you\'re learning about your emotional patterns and how this awareness might help you respond differently.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'emotional_awareness',
                'secondary_domains': [
                    {'domain': 'intellectual_analytical', 'strength': 60},
                    {'domain': 'refl_cbt', 'strength': 70}
                ],
                'trait_requirements': [
                    {'trait': 'open_self_aware', 'level': 60, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 50, 'optional': False},
                    {'trait': 'emo_sensitivity', 'level': 50, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'stationery_journal', 'quantity': 1, 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cognitive_focus', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'emo_regulation', 'level': 50, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'emotional_control', 'impact_type': 'enhancement', 'notes': 'Enhances understanding of emotional patterns for better regulation'},
                    {'belief': 'self_efficacy_general', 'impact_type': 'enhancement', 'notes': 'Reinforces ability to understand and manage oneself'}
                ],
                'tags': ['emotional', 'analytical', 'pattern-recognition', 'intermediate', 'self-knowledge']
            },
            
            # Beginner emotional regulation
            {
                'code': 'emotional_grounding_five',
                'name': '5-4-3-2-1 Emotional Grounding',
                'description': 'A simple sensory awareness technique to regulate overwhelming emotions',
                'duration_range': '3-5 minutes',
                'instructions': 'When feeling emotionally overwhelmed: Name 5 things you can see around you, 4 things you can touch or feel, 3 things you can hear, 2 things you can smell, and 1 thing you can taste. Focus on each sensory experience fully before moving to the next. Take slow breaths throughout. This technique helps bring you back to the present moment when emotions feel overwhelming.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'emotional_regulation',
                'secondary_domains': [
                    {'domain': 'refl_mindful', 'strength': 70},
                    {'domain': 'refl_resil', 'strength': 40}
                ],
                'trait_requirements': [
                    {'trait': 'open_self_aware', 'level': 20, 'optional': False}
                ],
                'env_requirements': [],
                'limitation_considerations': [
                    {'limitation': 'emo_regulation', 'level': 30, 'impact_type': 'limitation'},
                    {'limitation': 'cognitive_focus', 'level': 20, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'emotional_control', 'impact_type': 'enhancement', 'notes': 'Builds confidence in ability to self-regulate'}
                ],
                'tags': ['emotional', 'regulation', 'grounding', 'beginner-friendly', 'quick', 'mindfulness']
            }
        ]
        
        # Create the activities
        created_count = 0
        for activity_data in activities:
            if not GenericActivity.objects.filter(code=activity_data['code']).exists():
                self._create_activity(activity_data, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                created_count += 1
        
        self.stdout.write(f"Created {created_count} emotional activities")

    def create_intellectual_activities(self, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct):
        """Create intellectual activities across different sub-domains"""
        activities = [
            # Learning & Knowledge Acquisition
            {
                'code': 'int_learn_skill_research',
                'name': 'Skill Research',
                'description': 'Investigate and research a new skill you are interested in learning',
                'duration_range': '30-60 minutes',
                'instructions': 'Choose a skill you\'ve wanted to learn. Spend time researching online, finding learning resources, comparing approaches, and creating a simple learning plan. Focus on understanding the fundamentals and identifying first steps.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'int_learning',
                'secondary_domains': [
                    {'domain': 'int_research', 'strength': 70},
                    {'domain': 'int_planning', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_intellect', 'level': 40, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'tech_computer', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet', 'optional': False}
                ],
                'belief_interactions': [
                    {'belief': 'skill_learning_adaptability', 'impact_type': 'requirement', 'notes': 'Reinforces growth mindset beliefs'}
                ],
                'tags': ['intellectual', 'learning', 'research', 'beginner-friendly']
            },
            
            # Reading & Literary Analysis
            {
                'code': 'int_read_critical_article',
                'name': 'Critical Article Analysis',
                'description': 'Read and analyze a thought-provoking article, identifying key arguments and assumptions',
                'duration_range': '20-40 minutes',
                'instructions': 'Select an article on a complex topic. Read it once for general understanding. Then reread while making notes on: main claims, evidence provided, underlying assumptions, and logical structure. Identify any weaknesses in the argument and consider alternative perspectives.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'int_analysis',
                'secondary_domains': [
                    {'domain': 'int_reading', 'strength': 70},
                    {'domain': 'int_critical', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'open_intellect', 'level': 50, 'optional': False},
                    {'trait': 'consc_attention', 'level': 40, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_attention', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'cog_reading', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'worldview_openness', 'impact_type': 'challenge', 'notes': 'Challenges existing worldview beliefs'}
                ],
                'tags': ['intellectual', 'reading', 'critical-thinking', 'intermediate']
            },
            
            # Problem Solving
            {
                'code': 'int_solve_puzzle',
                'name': 'Logic Puzzle Challenge',
                'description': 'Tackle a logic puzzle that requires systematic thinking and deduction',
                'duration_range': '15-30 minutes',
                'instructions': 'Select a logic puzzle such as a sudoku, nonogram, or logic grid. Before starting, familiarize yourself with the rules. Work methodically, taking notes if needed. When stuck, take a brief pause before returning with fresh perspective.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'int_problem',
                'secondary_domains': [
                    {'domain': 'int_logic', 'strength': 100},
                    {'domain': 'leisure_games', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_intellect', 'level': 30, 'optional': False},
                    {'trait': 'consc_attention', 'level': 40, 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_attention', 'level': 50, 'impact_type': 'limitation'},
                    {'limitation': 'cog_processing', 'level': 40, 'impact_type': 'limitation'}
                ],
                'tags': ['intellectual', 'problem-solving', 'logic', 'beginner-friendly']
            },
            
            # Language Learning
            {
                'code': 'int_lang_daily_practice',
                'name': 'Language Daily Practice Session',
                'description': 'Spend time practicing a foreign language through structured exercises',
                'duration_range': '15-30 minutes',
                'instructions': 'Choose a language learning app or resource. Spend 5 minutes reviewing previously learned material. Then spend 10 minutes on new vocabulary or grammar concepts. Finish with 5 minutes of practical application like forming sentences or listening comprehension.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'int_language',
                'secondary_domains': [
                    {'domain': 'int_learning', 'strength': 70},
                    {'domain': 'int_memory', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_intellect', 'level': 30, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 40, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'tech_phone', 'quantity': 1, 'optional': False}
                ],
                'belief_interactions': [
                    {'belief': 'skill_learning_adaptability', 'impact_type': 'requirement', 'notes': 'Reinforces growth mindset in language acquisition'}
                ],
                'tags': ['intellectual', 'language', 'learning', 'daily-practice']
            },
            
            # Strategic Thinking
            {
                'code': 'int_strategy_game',
                'name': 'Strategic Board Game',
                'description': 'Play a strategic board or digital game that requires forward planning and tactical thinking',
                'duration_range': '30-90 minutes',
                'instructions': 'Select a strategy game (chess, Go, digital strategy game, etc.). If needed, review the basic rules. Focus on thinking multiple steps ahead and adapting your strategy based on changing circumstances. After playing, reflect briefly on your decisions.',
                'social_requirements': {'min_participants': 1, 'max_participants': 2},
                'primary_domain': 'int_strategy',
                'secondary_domains': [
                    {'domain': 'int_problem', 'strength': 70},
                    {'domain': 'leisure_games', 'strength': 70},
                    {'domain': 'soc_competing', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_intellect', 'level': 40, 'optional': False},
                    {'trait': 'consc_planning', 'level': 50, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'equip_board_game', 'quantity': 1, 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_processing', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'cog_decision', 'level': 50, 'impact_type': 'limitation'}
                ],
                'tags': ['intellectual', 'strategy', 'games', 'intermediate']
            },
            
            # Information Synthesis
            {
                'code': 'int_mind_mapping',
                'name': 'Mind Mapping Session',
                'description': 'Create a visual mind map to organize information and generate connections on a topic',
                'duration_range': '20-40 minutes',
                'instructions': 'Choose a topic or problem to explore. Write it in the center of a blank page. Branch out with related subtopics or ideas, using keywords and simple illustrations. Create additional levels of branches as needed. Look for connections between different branches and mark them.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'int_creative_thinking',
                'secondary_domains': [
                    {'domain': 'int_organization', 'strength': 70},
                    {'domain': 'creative_ideation', 'strength': 50},
                ],
                'trait_requirements': [
                    {'trait': 'open_intellect', 'level': 40, 'optional': False},
                    {'trait': 'open_creativity', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'stationery_paper', 'quantity': 1, 'optional': False},
                    {'resource': 'stationery_markers', 'quantity': 1, 'optional': True}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_organization', 'level': 40, 'impact_type': 'limitation'}
                ],
                'tags': ['intellectual', 'organization', 'creativity', 'beginner-friendly']
            },
            
            # Advanced Learning
            {
                'code': 'int_learn_advanced_concept',
                'name': 'Deep Dive Learning Session',
                'description': 'Master a complex concept through intensive focused learning and application',
                'duration_range': '60-90 minutes',
                'instructions': 'Select a challenging concept in your field of interest. Begin by finding 2-3 high-quality explanations. Study each, taking notes on different perspectives. Attempt to explain the concept in your own words. Apply the concept to a specific example or problem. Finally, identify questions for further exploration.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'int_learning',
                'secondary_domains': [
                    {'domain': 'int_research', 'strength': 70},
                    {'domain': 'int_critical', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'open_intellect', 'level': 70, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 60, 'optional': False},
                    {'trait': 'consc_attention', 'level': 50, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'tech_computer', 'quantity': 1, 'optional': False},
                    {'resource': 'stationery_paper', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'ind_quiet', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_attention', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'cog_processing', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'skill_learning_adaptability', 'impact_type': 'requirement', 'notes': 'Tests beliefs about intellectual capabilities'},
                    {'belief': 'potential_limitless', 'impact_type': 'requirement', 'notes': 'Reinforces growth mindset'}
                ],
                'tags': ['intellectual', 'deep-learning', 'focus', 'advanced']
            },
            
            # Debate & Critical Thinking
            {
                'code': 'int_argue_opposing_view',
                'name': 'Steel Man Argument Exercise',
                'description': 'Develop and articulate the strongest possible version of a viewpoint you disagree with',
                'duration_range': '30-45 minutes',
                'instructions': 'Select a topic where you have a strong opinion. Research the opposing viewpoint, looking for the strongest and most reasonable arguments. Write a persuasive paragraph supporting this opposing view, being as fair and charitable as possible. Then identify why reasonable people might hold this view.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'int_critical',
                'secondary_domains': [
                    {'domain': 'int_ethics', 'strength': 70},
                    {'domain': 'int_perspective', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'open_intellect', 'level': 60, 'optional': False},
                    {'trait': 'open_non_conformity', 'level': 50, 'optional': False},
                    {'trait': 'honhum_fairness', 'level': 50, 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_fixed_mindset', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'worldview_openness', 'impact_type': 'challenge', 'notes': 'Directly challenges closed worldview belief systems'}
                ],
                'tags': ['intellectual', 'critical-thinking', 'perspective-taking', 'advanced']
            },
            
            # Invention & Design
            {
                'code': 'int_design_solution',
                'name': 'Everyday Problem Redesign',
                'description': 'Identify an everyday problem and design a creative solution',
                'duration_range': '45-60 minutes',
                'instructions': 'Observe your environment and identify a small, everyday problem or inefficiency. Define the problem clearly. Brainstorm at least 5 possible solutions. Select the most promising one and sketch or describe it in detail. Consider materials, functionality, and how users would interact with it.',
                'social_requirements': {'min_participants': 1, 'max_participants': 3},
                'primary_domain': 'int_design',
                'secondary_domains': [
                    {'domain': 'int_problem', 'strength': 70},
                    {'domain': 'creative_ideation', 'strength': 70},
                ],
                'trait_requirements': [
                    {'trait': 'open_creativity', 'level': 60, 'optional': False},
                    {'trait': 'open_intellect', 'level': 50, 'optional': False},
                    {'trait': 'consc_planning', 'level': 40, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'stationery_paper', 'quantity': 1, 'optional': False},
                    {'resource': 'stationery_pencil', 'quantity': 1, 'optional': False}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_problem', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about problem-solving abilities'}
                ],
                'tags': ['intellectual', 'design', 'problem-solving', 'creativity', 'intermediate']
            },
            
            # Teaching & Communication
            {
                'code': 'int_teach_concept',
                'name': 'Teach-Back Exercise',
                'description': 'Solidify your understanding of a concept by teaching it to someone else',
                'duration_range': '30-45 minutes',
                'instructions': 'Select a concept you know well or want to master. Prepare a 5-minute explanation as if teaching someone unfamiliar with the topic. Include an analogy and a practical example. Practice delivering your explanation clearly. If possible, present to someone and ask for feedback.',
                'social_requirements': {'min_participants': 1, 'max_participants': 2},
                'primary_domain': 'int_teaching',
                'secondary_domains': [
                    {'domain': 'soc_comm', 'strength': 70},
                    {'domain': 'int_organization', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'open_intellect', 'level': 40, 'optional': False},
                    {'trait': 'extra_sociability', 'level': 30, 'optional': True},
                    {'trait': 'consc_organization', 'level': 30, 'optional': True}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_teaching', 'impact_type': 'challenge', 'notes': 'Tests beliefs about teaching and communication abilities'}
                ],
                'tags': ['intellectual', 'teaching', 'communication', 'intermediate']
            }
        ]
        
        # Create the activities
        created_count = 0
        for activity_data in activities:
            if not GenericActivity.objects.filter(code=activity_data['code']).exists():
                self._create_activity(activity_data, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                created_count += 1
        
        self.stdout.write(f"Created {created_count} intellectual activities")


    def create_physical_activities(self, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct):
        """Create physical activities across different sub-domains"""
        activities = [
            # BEGINNER LEVEL - Cardiovascular Activities
            {
                'code': 'phys_morning_jog',
                'name': 'Morning Jog',
                'description': 'A refreshing jog to start the day with light cardio exercise',
                'duration_range': '20-30 minutes',
                'instructions': 'Start with a 5-minute walking warm-up. Jog at a comfortable pace where you can still hold a conversation. Focus on steady breathing and relaxed form. End with a 5-minute walking cool-down.',
                'social_requirements': {'min_participants': 1, 'max_participants': 3},
                'primary_domain': 'phys_cardio',
                'secondary_domains': [
                    {'domain': 'phys_outdoor', 'strength': 70},
                    {'domain': 'refl_mindful', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'consc_diligence', 'level': 40, 'optional': False},
                    {'trait': 'extra_liveliness', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_running_shoes', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'outdoor_path', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_lower', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'phys_respiration', 'level': 60, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_physical', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about physical capabilities'}
                ],
                'tags': ['physical', 'outdoor', 'morning', 'beginner-friendly', 'cardio']
            },
            
            # INTERMEDIATE LEVEL - Strength Training
            {
                'code': 'phys_bodyweight_circuit',
                'name': 'Bodyweight Circuit Training',
                'description': 'A sequence of bodyweight exercises performed with minimal rest to build strength and endurance',
                'duration_range': '30-45 minutes',
                'instructions': 'Perform each exercise for 45 seconds followed by 15 seconds of rest: 1) Push-ups 2) Squats 3) Plank 4) Lunges 5) Mountain climbers. Complete 3-5 circuits with 1-minute rest between circuits. Focus on proper form rather than speed.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'phys_strength',
                'secondary_domains': [
                    {'domain': 'phys_cardio', 'strength': 50},
                    {'domain': 'refl_discipline', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'consc_diligence', 'level': 60, 'optional': False},
                    {'trait': 'emo_courage', 'level': 50, 'optional': False},
                    {'trait': 'extra_energy', 'level': 50, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_exercise_mat', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_timer', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'indoor_empty_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_upper', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'phys_mobility_lower', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'phys_strength', 'level': 60, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_physical', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about physical capabilities and endurance'},
                    {'belief': 'goal_achievement', 'impact_type': 'reinforce', 'notes': 'Reinforces beliefs about personal achievement through measurable progress'}
                ],
                'tags': ['physical', 'strength', 'indoor', 'intermediate', 'no-equipment', 'circuit-training']
            },
            
            # ADVANCED LEVEL - Flexibility & Movement
            {
                'code': 'phys_yoga_flow',
                'name': 'Advanced Vinyasa Yoga Flow',
                'description': 'A dynamic sequence of yoga postures synchronized with breath to build strength, flexibility, and mindfulness',
                'duration_range': '60-75 minutes',
                'instructions': 'Begin with 5 minutes of seated meditation and breath awareness. Move through sun salutations to warm up. Progress to standing poses, balances, inversions, and backbends. Include challenging poses like crow pose, handstand, and deep backbends. End with seated poses and 5-minute savasana.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'phys_flexibility',
                'secondary_domains': [
                    {'domain': 'phys_strength', 'strength': 70},
                    {'domain': 'refl_mindful', 'strength': 80},
                    {'domain': 'spirit_practice', 'strength': 50}
                ],
                'trait_requirements': [
                    {'trait': 'consc_organization', 'level': 70, 'optional': False},
                    {'trait': 'open_aesthetics', 'level': 60, 'optional': False},
                    {'trait': 'emo_balance', 'level': 70, 'optional': False},
                ],
                'resource_requirements': [
                    {'resource': 'equip_yoga_mat', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_yoga_block', 'quantity': 2, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'indoor_quiet', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_upper', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'phys_mobility_lower', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'phys_flexibility', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'phys_balance', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'mind_body_connection', 'impact_type': 'reinforce', 'notes': 'Reinforces belief in the connection between mind and body'},
                    {'belief': 'self_worth', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about physical limitations and self-imposed boundaries'}
                ],
                'tags': ['physical', 'yoga', 'flexibility', 'mindfulness', 'advanced', 'indoor', 'strength']
            },
            
            # BEGINNER LEVEL - Dance & Movement
            {
                'code': 'phys_dance_basics',
                'name': 'Beginner Dance Fundamentals',
                'description': 'An introduction to basic dance movements and rhythms for complete beginners',
                'duration_range': '30-45 minutes',
                'instructions': 'Begin with a simple warm-up to get your body moving. Learn 5 basic steps: step-touch, grapevine, box step, pivot turn, and basic rhythm step. Practice each move slowly, then with music. Combine steps into a short sequence. End with a gentle cool-down stretch.',
                'social_requirements': {'min_participants': 1, 'max_participants': 1},
                'primary_domain': 'phys_dance',
                'secondary_domains': [
                    {'domain': 'creative_movement', 'strength': 50},
                    {'domain': 'soc_self_express', 'strength': 30}
                ],
                'trait_requirements': [
                    {'trait': 'open_creativity', 'level': 30, 'optional': False},
                    {'trait': 'extra_sociability', 'level': 20, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'media_music', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'indoor_empty_space', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_lower', 'level': 50, 'impact_type': 'limitation'},
                    {'limitation': 'phys_balance', 'level': 40, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_expression', 'impact_type': 'reinforce', 'notes': 'Reinforces beliefs about self-expression and creativity'},
                    {'belief': 'self_consciousness', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about feeling judged during physical expression'}
                ],
                'tags': ['physical', 'dance', 'beginner-friendly', 'indoor', 'movement', 'creative']
            },
            
            # EXPERT LEVEL - Specialized Physical Training
            {
                'code': 'phys_parkour_advanced',
                'name': 'Advanced Urban Parkour',
                'description': 'A high-skill discipline involving navigating complex urban environments through running, jumping, and climbing movements',
                'duration_range': '60-90 minutes',
                'instructions': 'Begin with a 15-minute dynamic warm-up. Practice precision jumps between obstacles, rail balancing, wall runs, vaults, and drop techniques. Focus on flow between movements and safe landing mechanics. Include 3-4 challenging routes that combine multiple techniques. End with a cool-down and mobility work.',
                'social_requirements': {'min_participants': 2, 'max_participants': 5},
                'primary_domain': 'phys_specialized',
                'secondary_domains': [
                    {'domain': 'phys_strength', 'strength': 80},
                    {'domain': 'explore_risk', 'strength': 70},
                    {'domain': 'phys_balance', 'strength': 90}
                ],
                'trait_requirements': [
                    {'trait': 'emo_courage', 'level': 80, 'optional': False},
                    {'trait': 'consc_diligence', 'level': 70, 'optional': False},
                    {'trait': 'open_risk', 'level': 80, 'optional': False},
                    {'trait': 'extra_energy', 'level': 80, 'optional': False}
                ],
                'resource_requirements': [
                    {'resource': 'equip_protective_gear', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_training_shoes', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'outdoor_urban', 'optional': False},
                    {'env': 'outdoor_park', 'optional': True}
                ],
                'limitation_considerations': [
                    {'limitation': 'phys_mobility_upper', 'level': 90, 'impact_type': 'limitation'},
                    {'limitation': 'phys_mobility_lower', 'level': 90, 'impact_type': 'limitation'},
                    {'limitation': 'phys_balance', 'level': 90, 'impact_type': 'limitation'},
                    {'limitation': 'phys_strength', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'phys_endurance', 'level': 80, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_physical', 'impact_type': 'challenge', 'notes': 'Strongly challenges beliefs about physical capabilities and limitations'},
                    {'belief': 'risk_taking', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about personal risk assessment and management'},
                    {'belief': 'change_orientation', 'impact_type': 'reinforce', 'notes': 'Reinforces adaptability and comfort with dynamic environments'}
                ],
                'tags': ['physical', 'outdoor', 'expert', 'urban', 'parkour', 'advanced-skills', 'strength', 'balance']
            }
        ]
        
        # Create the activities
        created_count = 0
        for activity_data in activities:
            if not GenericActivity.objects.filter(code=activity_data['code']).exists():
                self._create_activity(activity_data, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                created_count += 1
        
        self.stdout.write(f"Created {created_count} physical activities")
    
    def create_social_activities(self, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct):
        """Create social activities across different sub-domains"""
        activities = [
            # BEGINNER LEVEL - Basic Connection
            {
                'code': 'soc_coffee_chat',
                'name': 'Coffee with a Friend',
                'description': 'A casual coffee meeting with a friend to catch up and connect',
                'duration_range': '30-60 minutes',
                'instructions': 'Arrange to meet at a coffee shop or similar venue. Begin with light conversation to establish comfort. Practice balanced dialogue by asking open-ended questions and active listening. Share recent experiences or thoughts that are meaningful to you. Focus on quality of connection rather than quantity of topics covered.',
                'social_requirements': {'min_participants': 2, 'max_participants': 2},
                'primary_domain': 'soc_connecting',
                'secondary_domains': [
                    {'domain': 'soc_comm', 'strength': 70},
                    {'domain': 'leisure_social', 'strength': 30},
                ],
                'trait_requirements': [
                    {'trait': 'extra_sociability', 'level': 40, 'optional': False},
                    {'trait': 'agree_gentleness', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'finance_small', 'quantity': 1, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'commercial_cafe', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_social_anxiety', 'level': 50, 'impact_type': 'limitation'},
                    {'limitation': 'cog_hearing', 'level': 40, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'social_connection', 'impact_type': 'reinforce', 'notes': 'Reinforces beliefs about the value of social connections'},
                    {'belief': 'self_expression', 'impact_type': 'challenge', 'notes': 'Gently challenges comfort with self-disclosure'}
                ],
                'tags': ['social', 'conversation', 'friendship', 'beginner-friendly', 'low-pressure']
            },
            
            # INTERMEDIATE LEVEL - Group Dynamics
            {
                'code': 'soc_dinner_party_host',
                'name': 'Host a Small Dinner Party',
                'description': 'Plan and host a dinner gathering for 3-5 friends or acquaintances',
                'duration_range': '3-4 hours',
                'instructions': 'Plan a menu one week in advance. Send invitations with clear details. Prepare your space to be welcoming and comfortable. Cook or arrange food ahead of time when possible. Create a simple conversation starter activity for when guests arrive. Balance hosting duties with being present with your guests. Focus on creating a warm atmosphere rather than perfection.',
                'social_requirements': {'min_participants': 4, 'max_participants': 6},
                'primary_domain': 'soc_hosting',
                'secondary_domains': [
                    {'domain': 'soc_group', 'strength': 70},
                    {'domain': 'creative_culinary', 'strength': 50},
                    {'domain': 'prod_planning', 'strength': 40}
                ],
                'trait_requirements': [
                    {'trait': 'extra_sociability', 'level': 60, 'optional': False},
                    {'trait': 'consc_organization', 'level': 60, 'optional': False},
                    {'trait': 'agree_patience', 'level': 50, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'home_dining_space', 'quantity': 1, 'optional': False},
                    {'resource': 'home_kitchen', 'quantity': 1, 'optional': False},
                    {'resource': 'finance_medium', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'residential_home', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_social_anxiety', 'level': 70, 'impact_type': 'limitation'},
                    {'limitation': 'cog_multitasking', 'level': 60, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'belonging', 'impact_type': 'reinforce', 'notes': 'Reinforces sense of community and belonging'},
                    {'belief': 'self_worth', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about social acceptance and approval'}
                ],
                'tags': ['social', 'hosting', 'cooking', 'intermediate', 'group', 'planning', 'home']
            },
            
            # ADVANCED LEVEL - Networking
            {
                'code': 'soc_professional_networking',
                'name': 'Professional Networking Event',
                'description': 'Attend and actively participate in a professional networking event in your field',
                'duration_range': '2-3 hours',
                'instructions': 'Research the event and attendees beforehand. Prepare a concise introduction about yourself and your work. Set a goal to connect meaningfully with 3-5 new people. Ask thoughtful questions about others\' work before sharing about your own. Exchange contact information with promising connections. Follow up within 48 hours with personalized messages to your new contacts.',
                'social_requirements': {'min_participants': 10, 'max_participants': 100},
                'primary_domain': 'soc_networking',
                'secondary_domains': [
                    {'domain': 'soc_comm', 'strength': 80},
                    {'domain': 'prod_career', 'strength': 70},
                    {'domain': 'soc_self_present', 'strength': 60}
                ],
                'trait_requirements': [
                    {'trait': 'extra_sociability', 'level': 70, 'optional': False},
                    {'trait': 'extra_social_boldness', 'level': 70, 'optional': False},
                    {'trait': 'consc_prudence', 'level': 60, 'optional': True},
                    {'trait': 'honhum_modesty', 'level': 40, 'impact_type': 'requirement', 'notes': 'Balance between self-promotion and genuine interest in others'}
                ],
                'resource_requirements': [
                    {'resource': 'equip_professional_attire', 'quantity': 1, 'optional': False},
                    {'resource': 'equip_business_cards', 'quantity': 1, 'optional': True}
                ],
                'env_requirements': [
                    {'env': 'commercial_conference', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_social_anxiety', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'cog_attention', 'level': 60, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'self_efficacy_social', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about social networking abilities'},
                    {'belief': 'social_trust', 'impact_type': 'challenge', 'notes': 'Challenges beliefs about professional relationships'},
                    {'belief': 'success_definition', 'impact_type': 'reinforce', 'notes': 'Reinforces career-oriented success definitions'}
                ],
                'tags': ['social', 'professional', 'networking', 'communication', 'advanced', 'career']
            },
            
            # BEGINNER LEVEL - Supportive Communication
            {
                'code': 'soc_active_listening',
                'name': 'Active Listening Practice',
                'description': 'A structured conversation exercise to develop active listening skills',
                'duration_range': '20-30 minutes',
                'instructions': 'Find a partner willing to share about a topic they care about (not deeply personal for this exercise). Set a timer for 10 minutes. During this time, focus entirely on listening without planning your response. Use nonverbal cues to show engagement. When they finish a thought, paraphrase what you heard before asking follow-up questions. After 10 minutes, switch roles. End by sharing your experience of listening and being listened to.',
                'social_requirements': {'min_participants': 2, 'max_participants': 2},
                'primary_domain': 'soc_comm',
                'secondary_domains': [
                    {'domain': 'soc_connecting', 'strength': 60},
                    {'domain': 'emot_empathy', 'strength': 50}
                ],
                'trait_requirements': [
                    {'trait': 'agree_patience', 'level': 40, 'optional': False},
                    {'trait': 'agree_gentleness', 'level': 30, 'optional': True},
                    {'trait': 'emo_empathy', 'level': 30, 'optional': True}
                ],
                'resource_requirements': [
                    {'resource': 'equip_timer', 'quantity': 1, 'optional': False}
                ],
                'env_requirements': [
                    {'env': 'indoor_quiet', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_attention', 'level': 60, 'impact_type': 'limitation'},
                    {'limitation': 'cog_hearing', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'social_connection', 'impact_type': 'reinforce', 'notes': 'Reinforces the value of genuine connection through listening'},
                    {'belief': 'self_importance', 'impact_type': 'challenge', 'notes': 'Challenges ego-centric conversation patterns'}
                ],
                'tags': ['social', 'communication', 'listening', 'beginner-friendly', 'interpersonal-skills']
            },
            
            # EXPERT LEVEL - Conflict Resolution
            {
                'code': 'soc_difficult_conversation',
                'name': 'Navigating a Difficult Conversation',
                'description': 'Address a challenging interpersonal issue using advanced communication techniques',
                'duration_range': '30-60 minutes',
                'instructions': 'Identify a significant unresolved issue with someone important in your life. Prepare by clarifying your intentions, identifying specific behaviors (not character judgments), and considering their perspective. Request a private conversation. Begin by expressing appreciation for the relationship. Use "I" statements to explain your experience without blame. Listen to their perspective without interrupting. Look for common ground and shared values. Focus on specific future behaviors rather than past mistakes. End with clear agreements about moving forward.',
                'social_requirements': {'min_participants': 2, 'max_participants': 2},
                'primary_domain': 'soc_conflict',
                'secondary_domains': [
                    {'domain': 'soc_comm', 'strength': 90},
                    {'domain': 'emot_regulation', 'strength': 80},
                    {'domain': 'emot_self_aware', 'strength': 70}
                ],
                'trait_requirements': [
                    {'trait': 'emo_balance', 'level': 80, 'optional': False},
                    {'trait': 'agree_forgiveness', 'level': 70, 'optional': False},
                    {'trait': 'agree_patience', 'level': 70, 'optional': False},
                    {'trait': 'consc_prudence', 'level': 70, 'optional': True},
                    {'trait': 'honhum_fairness', 'level': 60, 'optional': True}
                ],
                'resource_requirements': [],
                'env_requirements': [
                    {'env': 'indoor_quiet', 'optional': False},
                    {'env': 'indoor_private', 'optional': False}
                ],
                'limitation_considerations': [
                    {'limitation': 'cog_emotional_regulation', 'level': 80, 'impact_type': 'limitation'},
                    {'limitation': 'cog_social_anxiety', 'level': 70, 'impact_type': 'limitation'}
                ],
                'belief_interactions': [
                    {'belief': 'conflict_avoidance', 'impact_type': 'challenge', 'notes': 'Directly challenges beliefs about conflict avoidance'},
                    {'belief': 'trust_relationships', 'impact_type': 'challenge', 'notes': 'Challenges trust in relationship repair'},
                    {'belief': 'vulnerability', 'impact_type': 'challenge', 'notes': 'Challenges comfort with emotional vulnerability'},
                    {'belief': 'forgiveness', 'impact_type': 'reinforce', 'notes': 'Reinforces value of forgiveness and repair'}
                ],
                'tags': ['social', 'communication', 'conflict-resolution', 'expert', 'relationships', 'emotional']
            }
        ]
        
        # Create the activities
        created_count = 0
        for activity_data in activities:
            if not GenericActivity.objects.filter(code=activity_data['code']).exists():
                self._create_activity(activity_data, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct)
                created_count += 1
        
        self.stdout.write(f"Created {created_count} social activities")

    def _create_activity(self, data, domains, traits, environments, resources, limitations, beliefs, trait_ct, belief_ct, limitation_ct):
        """Create a single generic activity with all its relationships"""
        # Create base activity
        activity = GenericActivity.objects.create(
            code=data['code'],
            name=data['name'],
            description=data['description'],
            created_on=datetime.date.today(),
            duration_range=data['duration_range'],
            instructions=data.get('instructions', ''),
            social_requirements=data.get('social_requirements', {})
        )
        
        # Create domain relationships
        # Primary domain (strength 100)
        if 'primary_domain' in data and data['primary_domain'] in domains:
            EntityDomainRelationship.objects.create( # Use renamed model
                content_type=ContentType.objects.get_for_model(activity),
                object_id=activity.id,
                domain=domains[data['primary_domain']],
                strength=EntityDomainRelationship.RelationshipStrength.PRIMARY # Use renamed model
            )
            # Removed adding to direct M2M field 'domain'
        
        # Secondary domains
        for domain_data in data.get('secondary_domains', []):
            domain_code = domain_data['domain']
            if domain_code in domains:
                EntityDomainRelationship.objects.create( # Use renamed model
                    content_type=ContentType.objects.get_for_model(activity),
                    object_id=activity.id,
                    domain=domains[domain_code],
                    strength=domain_data['strength']
                )
                # Removed adding to direct M2M field 'domain'
        
        # Create trait requirements
        for trait_data in data.get('trait_requirements', []):
            trait_code = trait_data['trait']
            if trait_code in traits:
                GenericActivityUserRequirement.objects.create(
                    generic_activity=activity,
                    content_type=trait_ct,
                    object_id=traits[trait_code].id,
                    level_required=trait_data['level'],
                    optional=trait_data.get('optional', False),
                    impact_type=GenericActivityUserRequirement.ImpactType.REQUIREMENT,
                    notes=trait_data.get('notes', '')
                )
        
        # Create resource requirements
        for resource_data in data.get('resource_requirements', []):
            resource_code = resource_data['resource']
            if resource_code in resources:
                GenericActivityResourceRequirement.objects.create(
                    generic_activity=activity,
                    resource_base=resources[resource_code],
                    quantity_required=resource_data.get('quantity', 1),
                    optional=resource_data.get('optional', False)
                )
        
        # Create environment requirements
        for env_data in data.get('env_requirements', []):
            env_code = env_data['env']
            if env_code in environments:
                GenericActivityEnvRequirement.objects.create(
                    generic_activity=activity,
                    env=environments[env_code],
                    optional=env_data.get('optional', False)
                )
        
        # Create limitation considerations
        for limitation_data in data.get('limitation_considerations', []):
            limitation_code = limitation_data['limitation']
            if limitation_code in limitations:
                GenericActivityUserRequirement.objects.create(
                    generic_activity=activity,
                    content_type=limitation_ct,
                    object_id=limitations[limitation_code].id,
                    level_required=limitation_data.get('level', 50),
                    optional=limitation_data.get('optional', False),
                    impact_type=limitation_data.get('impact_type', 'limitation'),
                    notes=limitation_data.get('notes', '')
                )
        
        # Create belief interactions
        for belief_data in data.get('belief_interactions', []):
            belief_code = belief_data['belief']
            if belief_code in beliefs:
                GenericActivityUserRequirement.objects.create(
                    generic_activity=activity,
                    content_type=belief_ct,
                    object_id=beliefs[belief_code].id,
                    level_required=belief_data.get('level', 50),
                    optional=belief_data.get('optional', True),
                    impact_type=belief_data.get('impact_type', 'requirement'),
                    notes=belief_data.get('notes', '')
                )
        
        # Create tags
        for tag_name in data.get('tags', []):
            tag, created = Tag.objects.get_or_create(
                name=tag_name,
                defaults={'slug': tag_name.lower().replace(' ', '-')}
            )
            TaggedItem.objects.create(
                tag=tag,
                content_type=ContentType.objects.get_for_model(activity),
                object_id=activity.id
            )
        
        return activity

    def _create_or_update_summary(self, activity):
        """Create or update the requirement summary for an activity"""
        try:
            summary = GenericActivityUserRequirementSummary.objects.get(generic_activity=activity)
        except GenericActivityUserRequirementSummary.DoesNotExist:
            summary = GenericActivityUserRequirementSummary(
                generic_activity=activity,
                difficulty_rating=0,
                requirements_summary={},
                limitations_summary={}
            )
        
        summary.update_summary()
        return summary
