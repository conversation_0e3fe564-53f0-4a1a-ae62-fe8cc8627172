"""
Django management command for seeding benchmark schemas and evaluation templates.
This command integrates with the existing DB reset/seeding system.
"""

import os
import json
import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.main.models import AppliedSeedingCommand, EvaluationCriteriaTemplate
from apps.main.services.schema_registry import SchemaRegistry

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Seed benchmark schemas and evaluation templates'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--templates-dir',
            type=str,
            help='Directory containing evaluation template JSON files'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force reseeding even if already applied'
        )
    
    def handle(self, *args, **options):
        command_name = 'seed_benchmark_schemas'
        
        # Check if this command has already been applied
        if not options['force'] and AppliedSeedingCommand.objects.filter(command_name=command_name).exists():
            self.stdout.write(self.style.SUCCESS(f"Seeding command '{command_name}' has already been applied."))
            return
        
        # Initialize schema registry to create standard schemas
        self.stdout.write("Initializing schema registry and creating standard schemas...")
        registry = SchemaRegistry()
        
        schemas_created = len(registry.list_schemas())
        self.stdout.write(self.style.SUCCESS(f"Created {schemas_created} standard schemas."))
        
        # Seed evaluation templates from directory if provided
        templates_dir = options.get('templates_dir')
        if templates_dir and os.path.exists(templates_dir):
            self.stdout.write(f"Seeding evaluation templates from {templates_dir}...")
            templates_created = self._seed_evaluation_templates(templates_dir)
            self.stdout.write(self.style.SUCCESS(f"Created {templates_created} evaluation templates."))
        
        # Mark command as applied
        AppliedSeedingCommand.objects.create(command_name=command_name)
        self.stdout.write(self.style.SUCCESS(f"Seeding command '{command_name}' marked as applied."))
    
    def _seed_evaluation_templates(self, templates_dir):
        """Seed evaluation templates from JSON files."""
        templates_created = 0
        
        for filename in os.listdir(templates_dir):
            if not filename.endswith('.json'):
                continue
                
            template_path = os.path.join(templates_dir, filename)
            
            try:
                with open(template_path, 'r') as f:
                    template_data = json.load(f)
                
                # Process single template or list of templates
                templates = template_data if isinstance(template_data, list) else [template_data]
                
                with transaction.atomic():
                    for template in templates:
                        name = template.get('name')
                        if not name:
                            self.stdout.write(self.style.WARNING(f"Skipping template in {filename} - missing name"))
                            continue
                        
                        description = template.get('description', '')
                        dimensions = template.get('dimensions')
                        
                        if not dimensions:
                            self.stdout.write(self.style.WARNING(f"Skipping template '{name}' - missing dimensions"))
                            continue
                        
                        # Extract criteria and additional fields
                        criteria = {
                            'dimensions': dimensions,
                        }
                        
                        # Copy additional fields if present
                        for field in ['dimension_weights', 'scoring_thresholds', 'evaluation_models', 
                                     'prompt_template', 'expected_criteria_format', 'auto_calibrate']:
                            if field in template:
                                criteria[field] = template[field]
                        
                        # Update or create template
                        template_obj, created = EvaluationCriteriaTemplate.objects.update_or_create(
                            name=name,
                            defaults={
                                'description': description,
                                'criteria': criteria,
                            }
                        )
                        
                        if created:
                            templates_created += 1
                            self.stdout.write(f"Created template: {name}")
                        else:
                            self.stdout.write(f"Updated template: {name}")
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error processing {template_path}: {e}"))
                logger.error(f"Error seeding evaluation template from {template_path}", exc_info=True)
        
        return templates_created