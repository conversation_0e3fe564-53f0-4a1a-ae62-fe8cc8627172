"""
Django management command to test the benchmark API fixes.
"""

from django.core.management.base import BaseCommand
from apps.main.models import BenchmarkRun
import json


class Command(BaseCommand):
    help = 'Test the benchmark API fixes'

    def handle(self, *args, **options):
        self.stdout.write("Testing benchmark API fixes...")
        
        # Check if we have any benchmark runs
        runs_count = BenchmarkRun.objects.count()
        self.stdout.write(f"Total benchmark runs in database: {runs_count}")
        
        if runs_count == 0:
            self.stdout.write(self.style.WARNING("No benchmark runs found. Cannot test API response."))
            return
        
        # Get a sample run
        sample_run = BenchmarkRun.objects.first()
        self.stdout.write(f"Sample run ID: {sample_run.id}")
        
        # Test the token usage display property
        self.stdout.write(f"Token usage display: {sample_run.token_usage_display}")
        self.stdout.write(f"Total input tokens: {sample_run.total_input_tokens}")
        self.stdout.write(f"Total output tokens: {sample_run.total_output_tokens}")
        self.stdout.write(f"Estimated cost: {sample_run.estimated_cost}")
        
        # Test context variable extraction
        params = sample_run.parameters or {}
        context_vars = params.get('context_variables', {})
        
        self.stdout.write("\n=== Context Variables ===")
        self.stdout.write(f"Trust level: {context_vars.get('trust_level', 'N/A')}")
        
        mood = context_vars.get('mood', {})
        valence = mood.get('valence', 'N/A') if isinstance(mood, dict) else 'N/A'
        arousal = mood.get('arousal', 'N/A') if isinstance(mood, dict) else 'N/A'
        self.stdout.write(f"Valence: {valence}")
        self.stdout.write(f"Arousal: {arousal}")
        
        environment = context_vars.get('environment', {})
        stress_level = environment.get('stress_level', 'N/A') if isinstance(environment, dict) else 'N/A'
        time_pressure = environment.get('time_pressure', 'N/A') if isinstance(environment, dict) else 'N/A'
        self.stdout.write(f"Stress level: {stress_level}")
        self.stdout.write(f"Time pressure: {time_pressure}")
        
        # Test the API response format simulation
        self.stdout.write("\n=== Simulated API Response ===")
        
        # Format token usage for display
        token_usage_display = sample_run.token_usage_display if hasattr(sample_run, 'token_usage_display') else 'N/A'
        if sample_run.total_input_tokens and sample_run.total_output_tokens:
            token_usage_display = f"{sample_run.total_input_tokens}+{sample_run.total_output_tokens}={sample_run.total_tokens}"
        elif sample_run.total_tokens:
            token_usage_display = str(sample_run.total_tokens)

        # Format cost for display
        cost_display = f"${float(sample_run.estimated_cost):.6f}" if sample_run.estimated_cost else '$0.000000'

        api_response = {
            'id': sample_run.id,
            'scenario_id': sample_run.scenario_id,
            'scenario_name': sample_run.scenario.name if sample_run.scenario else 'N/A',
            'agent_role': sample_run.agent_definition.role if sample_run.agent_definition else 'N/A',
            'execution_date': sample_run.execution_date.isoformat(),
            'success_rate': sample_run.success_rate,
            'semantic_score': sample_run.semantic_score,
            'mean_duration': sample_run.mean_duration,
            'trust_level': context_vars.get('trust_level', 'N/A'),
            'valence': valence,
            'arousal': arousal,
            'stress_level': stress_level,
            'time_pressure': time_pressure,
            'token_usage': token_usage_display,
            'cost': cost_display,
            'total_input_tokens': sample_run.total_input_tokens,
            'total_output_tokens': sample_run.total_output_tokens,
            'estimated_cost': float(sample_run.estimated_cost) if sample_run.estimated_cost else 0.0,
        }
        
        self.stdout.write(json.dumps(api_response, indent=2, default=str))
        
        self.stdout.write(self.style.SUCCESS("\n=== API FIXES VERIFICATION ==="))
        
        # Check if our fixes are working
        fixes_working = True
        
        if 'token_usage' not in api_response or api_response['token_usage'] == 'N/A':
            if sample_run.total_input_tokens or sample_run.total_output_tokens:
                self.stdout.write(self.style.ERROR("❌ Token usage not properly formatted"))
                fixes_working = False
            else:
                self.stdout.write(self.style.WARNING("⚠️  No token data available to test"))
        else:
            self.stdout.write(self.style.SUCCESS("✅ Token usage properly formatted"))
        
        if 'cost' not in api_response or not api_response['cost'].startswith('$'):
            self.stdout.write(self.style.ERROR("❌ Cost not properly formatted"))
            fixes_working = False
        else:
            self.stdout.write(self.style.SUCCESS("✅ Cost properly formatted"))
        
        # Check context variables
        context_fields = ['trust_level', 'valence', 'arousal', 'stress_level', 'time_pressure']
        for field in context_fields:
            if field in api_response:
                self.stdout.write(self.style.SUCCESS(f"✅ {field} field present"))
            else:
                self.stdout.write(self.style.ERROR(f"❌ {field} field missing"))
                fixes_working = False
        
        if fixes_working:
            self.stdout.write(self.style.SUCCESS("\n🎉 All API fixes are working correctly!"))
        else:
            self.stdout.write(self.style.ERROR("\n❌ Some API fixes need attention"))
        
        self.stdout.write("\nDone!")
