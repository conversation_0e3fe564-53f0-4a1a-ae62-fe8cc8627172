{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div id="content-main">
    <h1>{{ title }}</h1>
    <p>Send a raw JSON message to the WebSocket consumer as if it came from the frontend.</p>

    <form method="post">
        {% csrf_token %}
        <fieldset class="module aligned">
            <h2>Send Message</h2>
            <div class="form-row">
                <label for="id_user_id">Target User ID:</label>
                <input type="number" name="user_id" id="id_user_id" value="{{ last_user_id|default:'1' }}" required>
                <p class="help">Specify the user ID whose WebSocket session group should receive the message (e.g., 'user_ws_1').</p>
            </div>
            <div class="form-row">
                <label for="id_simple_message">Simple Text Message:</label>
                <textarea name="simple_message" id="id_simple_message" rows="3" cols="80"></textarea>
                <p class="help">Enter plain text here to automatically format it as a 'chat_message' JSON below.</p>
            </div>
            <div class="form-row">
                <label for="id_message_json">Formatted Message (JSON):</label>
                <textarea name="message_json" id="id_message_json" rows="10" cols="80" required>{{ last_sent_message|default:'{\n  "type": "chat_message",\n  "payload": {\n    "message": "Hello from admin!"\n  }\n}' }}</textarea>
                <p class="help">Enter the full JSON payload or use the 'Simple Text Message' field above.</p>
            </div>
        </fieldset>
        <div class="submit-row">
            <button type="button" id="send-message-button" class="default">Send Message</button> {# Changed type to button #}
        </div>
    </form>

    {% if send_status or send_error %}
    <fieldset class="module">
        <h2>Last Send Attempt</h2>
        {% if send_status %}
        <p style="color: green;">{{ send_status }}</p>
        {% endif %}
        {% if send_error %}
        <p style="color: red;">Error: {{ send_error }}</p>
        {% endif %}
    </fieldset>
    {% endif %}

    <fieldset class="module">
        <h2>Message History / Responses (Placeholder)</h2>
        <div id="message-history" style="height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; background-color: #f9f9f9;">
            <!-- Messages will appear here -->
        </div>
    </fieldset>

</div>
{% endblock %}

{% block extrastyle %}
{{ block.super }}
<style>
    #id_message_json {
        font-family: monospace;
    }
    .form-row label {
        width: 12em; /* Adjust label width if needed */
    }
    #message-history .msg-container {
        border-bottom: 1px solid #eee;
        margin-bottom: 5px;
        padding-bottom: 5px;
        font-family: sans-serif;
        font-size: 0.9em;
    }
    #message-history .msg-timestamp {
        color: #888;
        font-size: 0.9em;
        margin-right: 5px;
    }
    #message-history .msg-source {
        font-weight: bold;
    }
    #message-history .msg-content {
        margin-top: 3px;
        white-space: pre-wrap; /* Allow wrapping but preserve whitespace */
        word-wrap: break-word;
    }
    #message-history .msg-content pre {
        margin: 0;
        padding: 5px;
        background-color: #f0f0f0;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-family: monospace;
        font-size: 1.1em; /* Make JSON slightly larger */
        white-space: pre-wrap; /* Ensure JSON wraps */
        word-wrap: break-word;
    }
    /* Source/Direction Specific */
    #message-history .msg-sent .msg-source { color: #007bff; } /* Blue */
    #message-history .msg-received .msg-source { color: #28a745; } /* Green */
    #message-history .msg-system .msg-source { color: #6c757d; } /* Gray */
    /* Message Type Specific */
    #message-history .msg-type-chat_message .msg-content { /* No specific style needed? */ }
    #message-history .msg-type-workflow_status .msg-content { font-style: italic; color: #6a0dad; } /* Purple */
    #message-history .msg-type-wheel_data .msg-content pre { background-color: #e0f7fa; } /* Light Cyan */
    #message-history .msg-type-error .msg-content { color: #dc3545; font-weight: bold; } /* Red */
    #message-history .msg-type-monitoring_status .msg-content { font-style: italic; color: #17a2b8; } /* Teal */
    /* General Error */
    #message-history .msg-error {
        color: #dc3545; /* Red */
        border-left: 3px solid #dc3545;
        padding-left: 5px;
    }
    #message-history .msg-success {
        color: #28a745; /* Green */
        border-left: 3px solid #28a745;
        padding-left: 5px;
    }

</style>
{% endblock %}

{% block extrahead %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const simpleMessageTextarea = document.getElementById('id_simple_message');
    const jsonMessageTextarea = document.getElementById('id_message_json');

    simpleMessageTextarea.addEventListener('input', function() {
        const messageText = this.value.trim();
        const currentUserId = userIdInput.value; // Get the current User ID

        if (messageText && currentUserId) { // Ensure User ID is also present
            const messageJson = {
                type: "chat_message",
                // Add user_profile_id at the top level as expected by the consumer
                user_profile_id: currentUserId,
                // Keep payload structure if needed by specific handlers,
                // but ensure core info is accessible.
                // Let's simplify and put message directly in content as per consumer logic
                content: {
                     message: messageText,
                     user_profile_id: currentUserId // Also include here for handler compatibility
                     // Add timestamp or metadata if needed later
                }
                // If payload structure is strictly required by some downstream process:
                // payload: {
                //     message: messageText
                // }
            };
            // Format JSON nicely with indentation
            jsonMessageTextarea.value = JSON.stringify(messageJson, null, 2);
        } else if (!currentUserId) {
             addHistoryMessage("System", "Please enter a Target User ID before typing a simple message.", "error");
             jsonMessageTextarea.value = ''; // Clear JSON if no user ID
        }
         else {
            // Optionally clear or set default if simple text is empty
            // jsonMessageTextarea.value = ''; // Or set a default JSON
        }
    });

    // Optional: If user edits JSON directly, clear the simple text field
    jsonMessageTextarea.addEventListener('input', function() {
       // Check if the content matches the standard chat message format
       try {
           const currentJson = JSON.parse(this.value);
           if (currentJson.type === 'chat_message' && currentJson.payload && typeof currentJson.payload.message === 'string') {
               // If it looks like a chat message generated from simple text, do nothing
           } else {
               // If it's a different structure, clear the simple text field
               simpleMessageTextarea.value = '';
           }
       } catch (e) {
           // If JSON is invalid, clear simple text field
           simpleMessageTextarea.value = '';
       }
    });
});

// --- WebSocket Logic for Real-time History ---
let historyDiv = null;
let userIdInput = null; // Declare globally, assign inside DOMContentLoaded
let messageForm = null; // Declare globally, assign inside DOMContentLoaded
let jsonMessageTextarea = null; // Declare globally, assign inside DOMContentLoaded

let adminSocket = null;
let currentMonitoredUserId = null;

function connectAdminWebSocket() {
    const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
    const wsPath = wsScheme + '://' + window.location.host + "/ws/admin-tester/";
    
    addHistoryMessage("System", `Attempting to connect to ${wsPath}...`);
    
    adminSocket = new WebSocket(wsPath);

    adminSocket.onopen = function(e) {
        addHistoryMessage("System", "Admin WebSocket connection established.");
        // Re-fetch the element inside the callback to ensure it's available
        const currentUserIdInput = document.getElementById('id_user_id');
        if (currentUserIdInput) {
            // Start monitoring the initial user ID
            startMonitoringUser(currentUserIdInput.value);
        } else {
            addHistoryMessage("System", "Error: Could not find User ID input field on connect.", "error");
            console.error("Error: Could not find #id_user_id element inside WebSocket onopen.");
        }
    };

    adminSocket.onmessage = function(e) {
        console.log("Admin WS message received:", e.data); // <-- Add log
        try { // <-- Add try...catch
            const data = JSON.parse(e.data);
            console.log("Parsed data:", data); // <-- Add log
            logReceivedMessage(data);
        } catch (error) {
            console.error("Error parsing or logging received message:", error); // <-- Add error log
            addHistoryMessage("System", `Error processing received message: ${error}`, "error"); // Display error in UI
        }
    };

    adminSocket.onerror = function(e) {
        addHistoryMessage("System", "Admin WebSocket error.", "error");
        console.error('Admin WebSocket error:', e);
    };

    adminSocket.onclose = function(e) {
        addHistoryMessage("System", `Admin WebSocket connection closed (Code: ${e.code}). Attempting to reconnect...`, "error");
        adminSocket = null;
        currentMonitoredUserId = null;
        // Optional: Implement reconnection logic (e.g., exponential backoff)
        setTimeout(connectAdminWebSocket, 5000); // Simple reconnect after 5s
    };
}

function startMonitoringUser(userId) {
    if (adminSocket && adminSocket.readyState === WebSocket.OPEN && userId && userId !== currentMonitoredUserId) {
        const message = {
            type: 'monitor_user',
            user_id: userId
        };
        adminSocket.send(JSON.stringify(message));
        addHistoryMessage("System", `Requested monitoring for User ID: ${userId}`);
        // We'll update currentMonitoredUserId upon confirmation from backend
    } else if (userId === currentMonitoredUserId) {
         addHistoryMessage("System", `Already monitoring User ID: ${userId}`);
    } else if (!userId) {
         addHistoryMessage("System", "Cannot monitor: User ID is empty.", "error");
    } else {
         addHistoryMessage("System", "Cannot monitor: Admin WebSocket not connected.", "error");
    }
}

function addHistoryMessage(source, messageContent, source_type = "system") { // source_type: system, sent, received
    const messageElement = document.createElement('div');
    const timestamp = new Date().toLocaleTimeString();
    messageElement.classList.add('msg-container');
    messageElement.classList.add(`msg-${source_type}`); // Add class based on source

    let contentHtml = "";
    let messageType = null; // To store the type extracted from JSON payload
    let isError = false;

    if (typeof messageContent === 'object' && messageContent !== null) {
        // Attempt to parse message type and error status from the object
        messageType = messageContent.type || null;
        isError = messageContent.error === true || messageContent.status === 'error'; // Check for common error indicators

        // Add specific type class if available
        if (messageType) {
            messageElement.classList.add(`msg-type-${messageType}`);
        }
        // Add error class if applicable
        if (isError) {
            messageElement.classList.add('msg-error');
        } else if (messageContent.status === 'success') {
             messageElement.classList.add('msg-success');
        }

        contentHtml = `<pre>${JSON.stringify(messageContent, null, 2)}</pre>`;
    } else {
        // Handle plain string messages (mostly for system messages)
        contentHtml = String(messageContent).replace(/\n/g, '<br>');
        // Check if the string itself indicates an error for system messages
        if (source_type === 'system' && (String(messageContent).toLowerCase().includes('error') || String(messageContent).toLowerCase().includes('closed') || String(messageContent).toLowerCase().includes('failed'))) {
             messageElement.classList.add('msg-error');
        } else if (source_type === 'system' && String(messageContent).toLowerCase().includes('success')) {
             messageElement.classList.add('msg-success');
        }
    }

    messageElement.innerHTML = `
        <span class="msg-timestamp">[${timestamp}]</span>
        <span class="msg-source">${source}:</span>
        <div class="msg-content">${contentHtml}</div>
    `;

    historyDiv.appendChild(messageElement);
    // Scroll to the bottom
    historyDiv.scrollTop = historyDiv.scrollHeight;
}


function logReceivedMessage(data) {
    console.log("logReceivedMessage called with:", data); // <-- Add log
    // Data from AdminTesterConsumer should have a specific structure
    if (data.type === 'monitoring_status') {
        // System message about monitoring status
        currentMonitoredUserId = data.status === 'success' ? data.user_id : null;
        addHistoryMessage("System", data.message, "system"); // Let addHistoryMessage handle error/success class based on content
    } else if (data.type === 'user_message_broadcast') {
        // Actual message intercepted from the user's session
        const direction = data.direction || 'unknown'; // server_to_client or client_to_server
        const sourceLabel = direction === 'server_to_client'
            ? `Server -> User ${data.target_user_id}`
            : `User ${data.target_user_id} -> Server`;
        console.log("Broadcasting message:", data.message, "Direction:", direction); // <-- Add log
        // Pass the actual message payload and mark as 'received' type for styling
        addHistoryMessage(sourceLabel, data.message, "received");
    } else {
        // Fallback for unexpected messages from the admin socket
        console.warn("Unexpected message type from Admin WS:", data); // <-- Add log
        addHistoryMessage("Admin WS", data, "system");
    }
}

// --- Event Listeners ---
document.addEventListener('DOMContentLoaded', function() {
    // Assign DOM element variables now that the DOM is loaded
    historyDiv = document.getElementById('message-history');
    userIdInput = document.getElementById('id_user_id');
    messageForm = document.querySelector('#content-main form');
    jsonMessageTextarea = document.getElementById('id_message_json'); // Assign here too

    if (!historyDiv || !userIdInput || !messageForm || !jsonMessageTextarea) {
        console.error("Fatal Error: Could not find one or more required elements (#message-history, #id_user_id, form, #id_message_json)!");
        return; // Stop execution if crucial elements are missing
    }

    // Existing simple text -> JSON logic (using jsonMessageTextarea)...
    const simpleMessageTextarea = document.getElementById('id_simple_message'); // This was already inside, which is fine
    if (simpleMessageTextarea && userIdInput && jsonMessageTextarea) { // Check all required elements
        simpleMessageTextarea.addEventListener('input', function() {
            const messageText = this.value.trim();
            const currentUserId = userIdInput.value; // Get the current User ID

            if (messageText && currentUserId) { // Ensure User ID is also present
                 const messageJson = {
                    type: "chat_message",
                    // Add user_profile_id at the top level as expected by the consumer's receive method
                    user_profile_id: currentUserId,
                    // The consumer's handle_chat_message expects 'message' within the 'content' dict
                    content: {
                         message: messageText,
                         user_profile_id: currentUserId // Include here too for clarity/safety
                    }
                };
                jsonMessageTextarea.value = JSON.stringify(messageJson, null, 2);
            } else if (!currentUserId) {
                // Optionally provide feedback if user ID is missing
                // addHistoryMessage("System", "Please enter a Target User ID.", "error");
                jsonMessageTextarea.value = ''; // Clear if no user ID
            } else {
                 // Clear if simple text is empty but user ID is present
                 jsonMessageTextarea.value = '';
            }
        });

        // Update the JSON listener to check the new structure
        jsonMessageTextarea.addEventListener('input', function() {
           try {
               const currentJson = JSON.parse(this.value);
               // Check if it looks like the structure generated above
               if (!(currentJson.type === 'chat_message' &&
                     currentJson.user_profile_id &&
                     currentJson.content &&
                     typeof currentJson.content.message === 'string')) {
                   // If it's a different structure, clear the simple text field
                   simpleMessageTextarea.value = '';
               }
           } catch (e) {
               // If JSON is invalid, clear simple text field
               simpleMessageTextarea.value = '';
           }
        });

    } else {
         console.error("Error: Could not find simple message textarea, user ID input, or JSON textarea.");
    }
    // --- End of Simple Text <-> JSON Logic ---


    // Connect WebSocket on page load (Should only be called once)
    connectAdminWebSocket();

    // Monitor user ID changes
    userIdInput.addEventListener('change', function() {
        startMonitoringUser(this.value);
    });

    // Handle button click asynchronously
    const sendButton = document.getElementById('send-message-button');
    sendButton.addEventListener('click', function() {
        // No need for event.preventDefault() as it's not a submit button

        const userId = userIdInput.value;
        const jsonPayload = jsonMessageTextarea.value;
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        let payloadForLog = jsonPayload; // Keep this for logging

        try {
            // Try to parse for pretty printing in the log
            payloadForLog = JSON.parse(jsonPayload);
        } catch (e) {
            // If not valid JSON, log the raw string
            payloadForLog = jsonPayload;
        }
        // Log the message being sent
        addHistoryMessage(`Admin -> User ${userId} (via Fetch)`, payloadForLog, "sent");

        // Send data using Fetch API
        fetch(messageForm.action, { // Use the form's action URL
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest' // Optional: Helps Django identify AJAX
            },
            body: JSON.stringify({
                user_id: userId,
                message_json: jsonPayload
            })
        })
        .then(response => response.json()) // Expect JSON response from the view
        .then(data => {
            // Update the status section dynamically
            updateSendStatus(data.send_status, data.send_error);
        })
        .catch(error => {
            console.error('Error sending message via fetch:', error);
            updateSendStatus(null, `Client-side error: ${error.message}`);
        });
    });

    // Function to update the status display area
    function updateSendStatus(status, error) {
        let statusFieldset = document.querySelector('#last-send-status-fieldset'); // Changed const to let
        if (!statusFieldset) {
             // Create the fieldset if it doesn't exist (e.g., on first async send)
             const newFieldset = document.createElement('fieldset');
             newFieldset.id = 'last-send-status-fieldset';
             newFieldset.className = 'module';
             newFieldset.innerHTML = '<h2>Last Send Attempt</h2><div id="send-status-content"></div>';
             messageForm.insertAdjacentElement('afterend', newFieldset); // Insert after the form
             statusFieldset = newFieldset;
        }

        const statusContentDiv = statusFieldset.querySelector('#send-status-content'); // Add an ID here too
        statusContentDiv.innerHTML = ''; // Clear previous status

        if (status) {
            const p = document.createElement('p');
            p.style.color = 'green';
            p.textContent = status;
            statusContentDiv.appendChild(p);
        }
        if (error) {
            const p = document.createElement('p');
            p.style.color = 'red';
            p.textContent = `Error: ${error}`;
            statusContentDiv.appendChild(p);
        }
         statusFieldset.style.display = (status || error) ? 'block' : 'none'; // Show/hide fieldset
    }

    // Add IDs to the status fieldset and its content area in the initial HTML
    // (or create them dynamically as shown above)
    const initialStatusFieldset = document.querySelector('form + fieldset.module'); // Select the fieldset after the form
    if (initialStatusFieldset && initialStatusFieldset.querySelector('h2').textContent === 'Last Send Attempt') {
        initialStatusFieldset.id = 'last-send-status-fieldset';
        const contentDiv = initialStatusFieldset.querySelector('p')?.parentNode; // Find parent of status paragraphs
        if (contentDiv) {
            contentDiv.id = 'send-status-content';
        }
    }


}); // End DOMContentLoaded

</script>
{% endblock %}
