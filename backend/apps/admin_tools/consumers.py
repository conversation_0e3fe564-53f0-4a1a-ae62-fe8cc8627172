import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async

logger = logging.getLogger(__name__)

class BenchmarkDashboardConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for the benchmark admin dashboard.
    Sends error and debug events immediately to connected staff users.
    """

    async def connect(self):
        user = self.scope.get('user')
        if not user or not user.is_authenticated or not user.is_staff:
            logger.warning(f"BenchmarkDashboardConsumer: Unauthorized connection attempt.")
            await self.close()
            return

        self.group_name = "benchmark_dashboard"

        # Accept connection
        await self.accept()

        # Add this channel to the benchmark_dashboard group
        await self.channel_layer.group_add(self.group_name, self.channel_name)
        logger.info(f"BenchmarkDashboardConsumer: User '{user.username}' connected and added to group '{self.group_name}'.")

    async def disconnect(self, close_code):
        # Remove this channel from the group
        await self.channel_layer.group_discard(self.group_name, self.channel_name)
        logger.info(f"BenchmarkDashboardConsumer: Disconnected channel {self.channel_name} from group '{self.group_name}'.")

    async def receive(self, text_data=None, bytes_data=None):
        # This consumer is read-only from client side; ignore any received messages
        pass

    # Handler for events sent to the group
    async def debug_info(self, event):
        # Forward debug_info events to the WebSocket client
        await self.send(text_data=json.dumps(event))

    async def error(self, event):
        # Forward error events to the WebSocket client
        await self.send(text_data=json.dumps(event))

    async def tool_argument_error(self, event):
        # Forward tool_argument_error events to the WebSocket client
        await self.send(text_data=json.dumps(event))


class AdminTesterConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for admin testing purposes.
    Accepts connections from authenticated staff users and joins 'admin_tester' group.
    """

    async def connect(self):
        user = self.scope.get('user')
        if not user or not user.is_authenticated or not user.is_staff:
            logger.warning(f"AdminTesterConsumer: Unauthorized connection attempt.")
            await self.close()
            return

        self.group_name = "admin_tester"

        # Accept connection
        await self.accept()

        # Add this channel to the admin_tester group
        await self.channel_layer.group_add(self.group_name, self.channel_name)
        logger.info(f"AdminTesterConsumer: User '{user.username}' connected and added to group '{self.group_name}'.")

    async def disconnect(self, close_code):
        # Remove this channel from the group
        await self.channel_layer.group_discard(self.group_name, self.channel_name)
        logger.info(f"AdminTesterConsumer: Disconnected channel {self.channel_name} from group '{self.group_name}'.")

    async def receive(self, text_data=None, bytes_data=None):
        # Echo received messages back to the client as JSON
        if text_data:
            await self.send(text_data=json.dumps({"echo": text_data}))

    # Example handler for group messages
    async def admin_test_event(self, event):
        # Forward admin_test_event to the WebSocket client
        await self.send(text_data=json.dumps(event))
