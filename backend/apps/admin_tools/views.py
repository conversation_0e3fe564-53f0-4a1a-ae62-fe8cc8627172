"""
Admin tools views for the Game of Life project.

This module contains views for the admin tools, including:
- Dashboard views
- Benchmark run views
- History views
"""

import json
import logging
from datetime import datetime
from django.http import JsonResponse, HttpResponse, Http404, HttpResponseForbidden
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.db import transaction
from django.views import View
from django.db.models import Count
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync, sync_to_async
from channels.db import database_sync_to_async
import traceback

# Import models and services needed for benchmarking
from apps.main.models import BenchmarkScenario, BenchmarkRun, GenericAgent, BenchmarkTag
from apps.main.services.benchmark_manager import AgentBenchmarker
from django.utils.dateparse import parse_date
from celery import current_app as celery_app # Import Celery app instance
import asyncio # For potential async operations if needed directly in view
import uuid # For handling UUIDs in API responses

# Import benchmark management views - these are now in the benchmark package
from apps.admin_tools.benchmark import views as benchmark_views

logger = logging.getLogger(__name__)

# Ensure only staff members can access this view
@staff_member_required
def websocket_tester_view(request):
    """
    Admin view to send messages to the WebSocket consumer and view history.
    """
    context = {
        'title': 'WebSocket Tester',
        'has_permission': True, # Required for admin templates
        'site_header': 'Game of Life Admin', # Optional: Customize header
        'opts': '', # Placeholder for model options if extending ModelAdmin
        'app_label': 'admin_tools', # App label for breadcrumbs/context
    }

    if request.method == 'POST':
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest' or \
                  request.content_type == 'application/json'

        send_status = None
        send_error = None
        user_id = None
        message_json = None

        try:
            if is_ajax:
                # Handle Fetch request (JSON body)
                data = json.loads(request.body)
                user_id = data.get('user_id')
                message_json = data.get('message_json')
            else:
                # Handle standard form submission (fallback)
                user_id = request.POST.get('user_id')
                message_json = request.POST.get('message_json')
                context['last_user_id'] = user_id
                context['last_sent_message'] = message_json

            if not user_id or not message_json:
                raise ValueError("Missing user_id or message_json")

            # Validate JSON and send message (common logic)
            message_data = json.loads(message_json) # Validate JSON structure
            channel_layer = get_channel_layer()
            # Target the predictable group name that the UserSessionConsumer joins
            target_group_name = f"user_session_group_{user_id}"

            async_to_sync(channel_layer.group_send)(
                target_group_name, # Send to the user-specific group
                {
                    # Use the custom type that maps to the 'admin_message' handler
                    "type": "admin.message",
                    # Pass the raw JSON string as 'text' which the handler expects
                    "text": message_json
                }
            )
            send_status = f"Message successfully sent to group '{target_group_name}' via admin.message handler." # Update status message

        except json.JSONDecodeError:
            send_error = "Invalid JSON format. Please check your input."
        except ValueError as e:
             send_error = str(e)
        except Exception as e:
            send_error = f"An error occurred while sending: {e}"

        if is_ajax:
            # Return JSON response for Fetch request
            return JsonResponse({'send_status': send_status, 'send_error': send_error})
        else:
            # Update context for standard form submission render
            context['send_status'] = send_status
            context['send_error'] = send_error

    # Render the full page for GET requests or standard POST fallback
    return render(request, 'admin_tools/websocket_tester.html', context)


###############################################################################
# Dashboard Views
###############################################################################

@staff_member_required
def dashboard(request):
    """Admin dashboard view."""
    context = {
        'title': 'Admin Dashboard',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    # Get recent benchmark runs with related data
    recent_runs = BenchmarkRun.objects.select_related('scenario', 'agent_definition').order_by('-execution_date')[:10]
    context['recent_runs'] = recent_runs

    # Get active benchmark scenarios
    active_scenarios = BenchmarkScenario.objects.filter(is_active=True).count()
    context['active_scenarios'] = active_scenarios

    # Get total benchmark runs
    total_runs = BenchmarkRun.objects.count()
    context['total_runs'] = total_runs

    # Get success rate (using success_rate field, considering >= 0.5 as success)
    if total_runs > 0:
        success_runs = BenchmarkRun.objects.filter(success_rate__gte=0.5).count()
        context['success_rate'] = (success_runs / total_runs * 100)
    else:
        context['success_rate'] = 0

    # Prepare recent runs data for JavaScript (for error status updates)
    recent_runs_data = []
    for run in recent_runs:
        # Check if run has errors in raw_results
        has_errors = False
        if run.raw_results and isinstance(run.raw_results, dict):
            errors = run.raw_results.get('errors', [])
            has_errors = isinstance(errors, list) and len(errors) > 0

        recent_runs_data.append({
            'id': run.id,
            'has_errors': has_errors
        })

    import json
    context['recent_runs_json'] = json.dumps(recent_runs_data)

    return render(request, 'admin_tools/benchmark_dashboard.html', context)

###############################################################################
# Benchmark Run Views
###############################################################################

@staff_member_required
def benchmark_run(request, run_id=None):
    """Admin view for benchmark run details."""
    context = {
        'title': 'Benchmark Run',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    if run_id:
        # Get the benchmark run
        run = get_object_or_404(BenchmarkRun, id=run_id)
        context['run'] = run

        # Get the scenario
        scenario = run.scenario
        context['scenario'] = scenario

        # Get the agent
        agent = run.agent
        context['agent'] = agent

        # Get the run details
        context['run_details'] = {
            'id': run.id,
            'execution_date': run.execution_date,
            'success': run.success,
            'success_rate': run.success_rate,
            'semantic_score': run.semantic_score,
            'execution_time': run.execution_time,
            'token_usage': run.token_usage,
            'cost': run.cost,
            'evaluator_llm_model': run.evaluator_llm_model,
        }

        # Get the run output
        context['run_output'] = run.output

        # Get the run evaluation
        context['run_evaluation'] = run.evaluation

        return render(request, 'admin_tools/benchmark_run.html', context)
    else:
        # Redirect to the dashboard
        return redirect('game_of_life_admin:dashboard')

###############################################################################
# History Views
###############################################################################

@staff_member_required
def history(request):
    """Admin view for benchmark run history."""
    context = {
        'title': 'Benchmark History',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    # Get all benchmark runs
    runs = BenchmarkRun.objects.all().order_by('-execution_date')
    context['runs'] = runs

    # Get success rate
    success_runs = runs.filter(success=True).count()
    context['success_rate'] = (success_runs / runs.count() * 100) if runs.count() > 0 else 0

    # Get average semantic score
    semantic_scores = [run.semantic_score for run in runs if run.semantic_score is not None]
    context['avg_semantic_score'] = sum(semantic_scores) / len(semantic_scores) if semantic_scores else 0

    # Get total token usage
    token_usage = sum([run.token_usage for run in runs if run.token_usage is not None])
    context['token_usage'] = token_usage

    # Get total cost
    cost = sum([run.cost for run in runs if run.cost is not None])
    context['cost'] = cost

    return render(request, 'admin_tools/history.html', context)

###############################################################################
# API Views
###############################################################################

class BenchmarkRunView(View):
    """API View for benchmark run operations."""

    async def _check_permissions(self, request):
        """Async-safe permission check."""
        is_authenticated = await sync_to_async(lambda: request.user.is_authenticated)()
        if not is_authenticated:
            return False
        is_active = await sync_to_async(lambda: request.user.is_active)()
        is_staff = await sync_to_async(lambda: request.user.is_staff)()
        return is_active and is_staff

    def _check_permissions_sync(self, request):
        """Sync version of permission check."""
        return (request.user.is_authenticated and
                request.user.is_active and
                request.user.is_staff)

    def dispatch(self, request, *args, **kwargs):
        # Perform permission check before calling get or post
        if not self._check_permissions_sync(request):
            return HttpResponseForbidden("You do not have permission to access this resource.")
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, run_id=None):
        """Get benchmark run details."""
        try:
            if run_id:
                # Get a specific run
                # Get a specific run (now sync)
                run = get_object_or_404(BenchmarkRun.objects.select_related('scenario', 'agent_definition'), id=run_id)
                run_data = {
                    'id': run.id,
                    'scenario': run.scenario.name if run.scenario else 'N/A',
                    'agent_role': run.agent_definition.role if run.agent_definition else 'N/A',
                    'execution_date': run.execution_date.isoformat(),
                    'success_rate': run.success_rate,
                    'semantic_score': run.semantic_score,
                    'mean_duration': run.mean_duration,
                    'median_duration': run.median_duration,
                    'min_duration': run.min_duration,
                    'max_duration': run.max_duration,
                    'std_dev': run.std_dev,
                    'llm_calls': run.llm_calls,
                    'tool_calls': run.tool_calls,
                    'tool_breakdown': run.tool_breakdown,
                    'token_usage': run.total_tokens,
                    'estimated_cost': float(run.estimated_cost) if run.estimated_cost else 0.0,
                    'evaluator_llm_model': run.evaluator_llm_model,
                    'parameters': run.parameters,
                    'raw_results': run.raw_results,
                }

                return JsonResponse(run_data)
            else:
                # List runs with filtering
                scenario_id = request.GET.get('scenario_id')
                agent_id = request.GET.get('agent_id')
                success = request.GET.get('success')
                start_date = request.GET.get('start_date')
                end_date = request.GET.get('end_date')

                # List runs with filtering (now sync)
                queryset = BenchmarkRun.objects.select_related('scenario', 'agent_definition')

                if scenario_id:
                    queryset = queryset.filter(scenario_id=scenario_id)

                if agent_id:
                    queryset = queryset.filter(agent_definition_id=agent_id)

                if success is not None:
                    success_bool = success.lower() == 'true'
                    # Use success_rate >= 0.5 to determine success
                    if success_bool:
                        queryset = queryset.filter(success_rate__gte=0.5)
                    else:
                        queryset = queryset.filter(success_rate__lt=0.5)

                if start_date:
                    start_date_obj = parse_date(start_date)
                    if start_date_obj:
                        queryset = queryset.filter(execution_date__gte=start_date_obj)

                if end_date:
                    end_date_obj = parse_date(end_date)
                    if end_date_obj:
                        queryset = queryset.filter(execution_date__lte=end_date_obj)

                # Convert to list of dicts
                runs = queryset.order_by('-execution_date')
                runs_data = []
                for run in runs:
                    try:
                        # Extract evaluation variables from parameters
                        params = run.parameters or {}
                        context_vars = params.get('context_variables', {})

                        # Also check in evaluation_template_data if not found in context_variables
                        if not context_vars:
                            template_data = params.get('evaluation_template_data', {})
                            context_vars = template_data.get('context_variables', {})

                        # Extract individual evaluation variables with proper handling of different formats
                        try:
                            trust_level_raw = context_vars.get('trust_level', 'N/A')
                            if isinstance(trust_level_raw, dict) and 'value' in trust_level_raw:
                                trust_level = trust_level_raw['value']
                            elif isinstance(trust_level_raw, (int, float)):
                                trust_level = trust_level_raw
                            else:
                                trust_level = 'N/A'
                        except Exception:
                            trust_level = 'N/A'

                        # Extract valence - check both nested and direct formats
                        try:
                            valence_raw = context_vars.get('valence', 'N/A')
                            if valence_raw == 'N/A':
                                # Try nested format
                                mood = context_vars.get('mood', {})
                                valence_raw = mood.get('valence', 'N/A') if isinstance(mood, dict) else 'N/A'

                            if isinstance(valence_raw, dict) and 'value' in valence_raw:
                                valence = valence_raw['value']
                            elif isinstance(valence_raw, (int, float)):
                                valence = valence_raw
                            else:
                                valence = 'N/A'
                        except Exception:
                            valence = 'N/A'

                        # Extract arousal - check both nested and direct formats
                        try:
                            arousal_raw = context_vars.get('arousal', 'N/A')
                            if arousal_raw == 'N/A':
                                # Try nested format
                                mood = context_vars.get('mood', {})
                                arousal_raw = mood.get('arousal', 'N/A') if isinstance(mood, dict) else 'N/A'

                            if isinstance(arousal_raw, dict) and 'value' in arousal_raw:
                                arousal = arousal_raw['value']
                            elif isinstance(arousal_raw, (int, float)):
                                arousal = arousal_raw
                            else:
                                arousal = 'N/A'
                        except Exception:
                            arousal = 'N/A'

                        # Extract stress level - check both nested and direct formats
                        try:
                            stress_level_raw = context_vars.get('stress_level', 'N/A')
                            if stress_level_raw == 'N/A':
                                # Try nested format
                                environment = context_vars.get('environment', {})
                                stress_level_raw = environment.get('stress_level', 'N/A') if isinstance(environment, dict) else 'N/A'

                            if isinstance(stress_level_raw, dict) and 'value' in stress_level_raw:
                                stress_level = stress_level_raw['value']
                            elif isinstance(stress_level_raw, (int, float)):
                                stress_level = stress_level_raw
                            else:
                                stress_level = 'N/A'
                        except Exception:
                            stress_level = 'N/A'

                        # Extract time pressure - check both nested and direct formats
                        try:
                            time_pressure_raw = context_vars.get('time_pressure', 'N/A')
                            if time_pressure_raw == 'N/A':
                                # Try nested format
                                environment = context_vars.get('environment', {})
                                time_pressure_raw = environment.get('time_pressure', 'N/A') if isinstance(environment, dict) else 'N/A'

                            if isinstance(time_pressure_raw, dict) and 'value' in time_pressure_raw:
                                time_pressure = time_pressure_raw['value']
                            elif isinstance(time_pressure_raw, (int, float)):
                                time_pressure = time_pressure_raw
                            else:
                                time_pressure = 'N/A'
                        except Exception:
                            time_pressure = 'N/A'

                        # Format token usage for display with safe property access
                        try:
                            # Use raw numbers for frontend display (not the k-formatted version)
                            if run.total_input_tokens is not None and run.total_output_tokens is not None:
                                total_tokens = (run.total_input_tokens or 0) + (run.total_output_tokens or 0)
                                token_usage_display = f"{run.total_input_tokens}+{run.total_output_tokens}={total_tokens}"
                            elif run.total_input_tokens is not None or run.total_output_tokens is not None:
                                # Handle case where only one is set
                                input_tokens = run.total_input_tokens or 0
                                output_tokens = run.total_output_tokens or 0
                                total_tokens = input_tokens + output_tokens
                                token_usage_display = f"{input_tokens}+{output_tokens}={total_tokens}"
                            else:
                                # Both total_input_tokens and total_output_tokens are None
                                token_usage_display = "N/A"
                        except Exception as e:
                            logger.warning(f"Error formatting token usage for run {run.id}: {e}")
                            token_usage_display = "N/A"

                        # Format cost for display with safe conversion
                        try:
                            if run.estimated_cost is not None:
                                cost_display = f"${float(run.estimated_cost):.6f}"
                            else:
                                cost_display = '$0.000000'
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error formatting cost for run {run.id}: {e}")
                            cost_display = '$0.000000'

                        runs_data.append({
                            'id': run.id,
                            'scenario_id': run.scenario_id,
                            'scenario_name': run.scenario.name if run.scenario else 'N/A',
                            'agent_role': run.agent_definition.role if run.agent_definition else 'N/A',
                            'execution_date': run.execution_date.isoformat(),
                            'success_rate': run.success_rate,
                            'semantic_score': run.semantic_score,
                            'mean_duration': run.mean_duration,
                            'trust_level': trust_level,
                            'valence': valence,
                            'arousal': arousal,
                            'stress_level': stress_level,
                            'time_pressure': time_pressure,
                            'token_usage': token_usage_display,
                            'cost': cost_display,
                            'total_input_tokens': run.total_input_tokens,
                            'total_output_tokens': run.total_output_tokens,
                            'estimated_cost': float(run.estimated_cost) if run.estimated_cost else 0.0,
                            'context_variables': context_vars,  # Include full context variables for frontend processing
                        })
                    except Exception as e:
                        logger.error(f"Error processing run {run.id}: {e}", exc_info=True)

                return JsonResponse({'runs': runs_data})

        except Exception as e:
            logger.error("Error in BenchmarkRunView.get", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request):
        """Run a benchmark scenario."""
        try:
            data = json.loads(request.body)

            # Check if this is an evaluation template test request
            if 'evaluation_template_id' in data or 'evaluation_template_data' in data:
                return self._handle_template_test_sync(data)

            # Original benchmark run logic
            # Validate required fields
            required_fields = ['scenario_id']
            for field in required_fields:
                if field not in data:
                    return JsonResponse({'error': f"Missing required field: {field}"}, status=400)

            # Get parameters
            params = data.get('params', {})
            runs = params.get('runs', 1)
            semantic_evaluation = params.get('semantic_evaluation', True)

            # Get the scenario (now sync)
            scenario = get_object_or_404(BenchmarkScenario, id=data['scenario_id'])

            # Launch benchmark task (now sync)
            from celery import current_app as celery_app

            task_params = {
                'runs': runs,
                'semantic_evaluation': semantic_evaluation,
            }

            # Add optional parameters
            if 'llm_config_id' in params:
                task_params['llm_config_id'] = params['llm_config_id']
            if 'evaluation_llm_config_id' in params:
                task_params['evaluation_llm_config_id'] = params['evaluation_llm_config_id']
            if 'context_variables' in params:
                task_params['context_variables'] = params['context_variables']

            # Launch the task
            task = celery_app.send_task(
                'apps.main.tasks.benchmark_tasks.run_workflow_benchmark',
                args=[str(scenario.id)],
                kwargs={'params': task_params}
            )

            task_id = task.id

            return JsonResponse({
                'success': True,
                'task_id': task_id,
                'message': f"Benchmark task launched successfully."
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': "Invalid JSON body"}, status=400)
        except Exception as e:
            logger.error("Error in BenchmarkRunView.post", exc_info=True)

            # Emit error event via WebSocket for real-time feedback
            try:
                from apps.main.services.event_service import EventService
                import traceback
                import asyncio

                # Create task to emit error event (since this is a sync context)
                asyncio.create_task(
                    EventService.emit_debug_info(
                        level='error',
                        message=f"Benchmark API error: {str(e)}",
                        source='BenchmarkRunView',
                        details={
                            'error_type': type(e).__name__,
                            'error_message': str(e),
                            'traceback': traceback.format_exc(),
                            'endpoint': 'POST /admin/benchmarks/api/run/'
                        }
                    )
                )
            except Exception as emit_error:
                logger.warning(f"Failed to emit error event: {emit_error}")

            return JsonResponse({'error': str(e)}, status=500)

    def _handle_template_test_sync(self, data):
        """Handle evaluation template testing requests (sync version)."""
        try:
            # Validate required fields for template testing
            if 'scenario_id' not in data:
                return JsonResponse({'error': "Missing required field: scenario_id"}, status=400)

            # Get parameters
            params = data.get('params', {})
            context_variables = params.get('context_variables', {})

            # Get the scenario and template (now sync)
            from apps.main.models import EvaluationCriteriaTemplate
            scenario = get_object_or_404(BenchmarkScenario, id=data['scenario_id'])

            # Handle both saved templates and template data
            if 'evaluation_template_id' in data:
                template = get_object_or_404(EvaluationCriteriaTemplate, id=data['evaluation_template_id'])
                template_data = {
                    'id': template.id,
                    'name': template.name,
                    'description': template.description,
                    'criteria': template.criteria,
                    'contextual_criteria': template.contextual_criteria,
                    'context_variables': context_variables
                }
            elif 'evaluation_template_data' in data:
                template_data = data['evaluation_template_data']
                template_data['context_variables'] = context_variables
            else:
                raise ValueError("Either evaluation_template_id or evaluation_template_data must be provided")

            # Start the Celery task for template testing
            from apps.main.tasks.benchmark_tasks import run_template_test

            task = run_template_test.delay(
                scenario_id=str(scenario.id),
                template_data=template_data,
                params=params
            )

            logger.info(f"Started template test task {task.id} for scenario {scenario.id}")

            return JsonResponse({
                'success': True,
                'task_id': task.id,
                'message': f'Template test started for scenario "{scenario.name}". Task ID: {task.id}'
            })

        except Exception as e:
            logger.error("Error in template test", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    async def _handle_template_test(self, data):
        """Handle evaluation template testing requests."""
        try:
            # Validate required fields for template testing
            if 'scenario_id' not in data:
                return JsonResponse({'error': "Missing required field: scenario_id"}, status=400)

            # Get parameters
            params = data.get('params', {})
            runs = min(params.get('runs', 1), 5)  # Limit to 5 runs for template testing
            semantic_evaluation = params.get('semantic_evaluation', True)
            context_variables = params.get('context_variables', {})
            multi_range_evaluation = params.get('multi_range_contextual_evaluation', False)
            selected_combinations = params.get('selected_combinations', [])
            selected_combination_indices = params.get('selected_combination_indices', [])

            # Get the scenario and template
            @database_sync_to_async
            def _get_scenario_and_template():
                from apps.main.models import EvaluationCriteriaTemplate
                scenario = get_object_or_404(BenchmarkScenario, id=data['scenario_id'])

                # Handle both saved templates and template data
                if 'evaluation_template_id' in data:
                    template = get_object_or_404(EvaluationCriteriaTemplate, id=data['evaluation_template_id'])
                    template_data = {
                        'id': template.id,
                        'name': template.name,
                        'description': template.description,
                        'criteria': template.criteria,
                        'contextual_criteria': template.contextual_criteria,
                        'context_variables': context_variables
                    }
                elif 'evaluation_template_data' in data:
                    template_data = data['evaluation_template_data']
                    template_data['context_variables'] = context_variables
                    template = None  # No saved template
                else:
                    raise ValueError("Either evaluation_template_id or evaluation_template_data must be provided")

                return scenario, template, template_data

            scenario, template, template_data = await _get_scenario_and_template()

            # Start the Celery task for template testing instead of running synchronously
            from apps.main.tasks.benchmark_tasks import run_template_test

            task = run_template_test.delay(
                scenario_id=str(scenario.id),
                template_data=template_data,
                params=params
            )

            logger.info(f"Started template test task {task.id} for scenario {scenario.id}")

            return JsonResponse({
                'success': True,
                'task_id': task.id,
                'message': f'Template test started for scenario "{scenario.name}". Task ID: {task.id}'
            })

        except Exception as e:
            logger.error("Error in template test", exc_info=True)

            # Emit error event via WebSocket for real-time feedback
            try:
                from apps.main.services.event_service import EventService
                import traceback
                import asyncio

                # Create task to emit error event
                asyncio.create_task(
                    EventService.emit_debug_info(
                        level='error',
                        message=f"Template test error: {str(e)}",
                        source='BenchmarkRunView',
                        details={
                            'error_type': type(e).__name__,
                            'error_message': str(e),
                            'traceback': traceback.format_exc(),
                            'endpoint': 'POST /admin/benchmarks/api/run/ (template test)'
                        }
                    )
                )
            except Exception as emit_error:
                logger.warning(f"Failed to emit error event: {emit_error}")

            return JsonResponse({'error': str(e)}, status=500)


class BenchmarkRunStopView(View):
    """Stop a running benchmark task."""

    async def post(self, request, task_id):
        """Stop a benchmark task by task ID."""
        try:
            # Import Celery app
            from celery import current_app as celery_app

            # Revoke the task
            celery_app.control.revoke(task_id, terminate=True)

            logger.info(f"Stopped benchmark task: {task_id}")

            return JsonResponse({
                'success': True,
                'message': f'Task {task_id} has been stopped.'
            })

        except Exception as e:
            logger.error(f"Error stopping task {task_id}: {e}", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)


class BenchmarkTaskStatusView(View):
    """Check the status of a benchmark task."""

    async def get(self, request, task_id):
        """Get the status of a benchmark task by task ID."""
        try:
            from celery.result import AsyncResult

            # Get the task result
            result = AsyncResult(task_id)

            # Check task status
            if result.ready():
                if result.failed():
                    return JsonResponse({
                        'status': 'failed',
                        'error': str(result.result),
                        'task_id': task_id
                    })
                else:
                    # Task completed successfully
                    task_result = result.result
                    return JsonResponse({
                        'status': 'completed',
                        'result': task_result,
                        'task_id': task_id,
                        'runs': task_result.get('runs', []) if isinstance(task_result, dict) else []
                    })
            else:
                # Task is still running or pending
                status = result.status.lower() if result.status else 'pending'

                # Get task metadata if available
                meta = result.info if hasattr(result, 'info') and result.info else {}

                return JsonResponse({
                    'status': status,
                    'task_id': task_id,
                    'meta': meta
                })

        except Exception as e:
            logger.error(f"Error checking task status {task_id}: {e}", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)