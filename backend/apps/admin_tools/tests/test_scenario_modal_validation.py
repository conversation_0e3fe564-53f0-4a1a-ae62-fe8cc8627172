"""
Unit tests for scenario modal validation logic.

This module tests the validation logic for the scenario editing modal
without requiring database connections.
"""

import unittest
import json


class ScenarioValidationTests(unittest.TestCase):
    """Test scenario validation logic."""

    def test_validate_basic_scenario_data(self):
        """Test validation of basic scenario data structure."""
        # Valid scenario data
        valid_data = {
            'name': 'Test Scenario',
            'description': 'A test scenario',
            'agent_role': 'mentor',
            'input_data': {
                'user_message': 'Test message',
                'context_packet': {
                    'workflow_type': 'wheel_generation'
                }
            },
            'metadata': {
                'workflow_type': 'wheel_generation',
                'expected_quality_criteria': {
                    'Content': ['Relevant', 'Helpful']
                }
            },
            'is_active': True,
            'tags': ['test', 'scenario']
        }
        
        errors = self.validate_scenario_data(valid_data)
        self.assertEqual(len(errors), 0, "Valid data should not have errors")

    def test_validate_missing_required_fields(self):
        """Test validation with missing required fields."""
        # Missing name
        invalid_data = {
            'description': 'A test scenario',
            'agent_role': 'mentor',
            'input_data': {},
            'metadata': {}
        }
        
        errors = self.validate_scenario_data(invalid_data)
        self.assertIn('Name is required', errors)

    def test_validate_empty_required_fields(self):
        """Test validation with empty required fields."""
        invalid_data = {
            'name': '',  # Empty name
            'agent_role': '',  # Empty agent role
            'input_data': {},  # Empty input data
            'metadata': {}  # Empty metadata
        }
        
        errors = self.validate_scenario_data(invalid_data)
        self.assertIn('Name is required', errors)
        self.assertIn('Agent role is required', errors)
        self.assertIn('Input data is required', errors)
        self.assertIn('Metadata is required', errors)

    def test_validate_workflow_type_consistency(self):
        """Test validation of workflow type consistency."""
        # Workflow type mismatch between metadata and form
        inconsistent_data = {
            'name': 'Test Scenario',
            'agent_role': 'mentor',
            'input_data': {
                'user_message': 'Test message'
            },
            'metadata': {
                'workflow_type': 'goal_setting'  # Different from expected
            }
        }
        
        errors = self.validate_scenario_data(inconsistent_data)
        # Should pass basic validation but could warn about consistency
        self.assertIsInstance(errors, list)

    def test_context_variables_structure(self):
        """Test validation of context variables structure."""
        # Valid context variables
        valid_context = {
            'trust_level': 75,
            'mood': {
                'valence': 0.5,
                'arousal': 0.2
            },
            'environment': {
                'stress_level': 30,
                'time_pressure': 25
            }
        }
        
        self.assertTrue(self.validate_context_variables(valid_context))

    def test_context_variables_ranges(self):
        """Test validation of context variable ranges."""
        # Test trust level range
        self.assertTrue(self.validate_trust_level(0))
        self.assertTrue(self.validate_trust_level(50))
        self.assertTrue(self.validate_trust_level(100))
        self.assertFalse(self.validate_trust_level(-1))
        self.assertFalse(self.validate_trust_level(101))

        # Test mood ranges
        self.assertTrue(self.validate_mood_value(-1.0))
        self.assertTrue(self.validate_mood_value(0.0))
        self.assertTrue(self.validate_mood_value(1.0))
        self.assertFalse(self.validate_mood_value(-1.1))
        self.assertFalse(self.validate_mood_value(1.1))

        # Test environment ranges
        self.assertTrue(self.validate_environment_value(0))
        self.assertTrue(self.validate_environment_value(50))
        self.assertTrue(self.validate_environment_value(100))
        self.assertFalse(self.validate_environment_value(-1))
        self.assertFalse(self.validate_environment_value(101))

    def test_trust_phase_calculation(self):
        """Test trust phase calculation logic."""
        self.assertEqual(self.get_trust_phase(25), 'Foundation')
        self.assertEqual(self.get_trust_phase(39), 'Foundation')
        self.assertEqual(self.get_trust_phase(40), 'Expansion')
        self.assertEqual(self.get_trust_phase(69), 'Expansion')
        self.assertEqual(self.get_trust_phase(70), 'Integration')
        self.assertEqual(self.get_trust_phase(100), 'Integration')

    def test_mood_quadrant_calculation(self):
        """Test mood quadrant calculation logic."""
        self.assertEqual(self.get_mood_quadrant(0.5, 0.5), 'Excited/Happy')
        self.assertEqual(self.get_mood_quadrant(0.5, -0.5), 'Calm/Content')
        self.assertEqual(self.get_mood_quadrant(-0.5, 0.5), 'Stressed/Angry')
        self.assertEqual(self.get_mood_quadrant(-0.5, -0.5), 'Sad/Depressed')
        self.assertEqual(self.get_mood_quadrant(0.0, 0.0), 'Neutral')

    def test_json_validation(self):
        """Test JSON validation for input data and metadata."""
        # Valid JSON
        valid_json = '{"key": "value", "number": 123}'
        self.assertTrue(self.is_valid_json(valid_json))

        # Invalid JSON
        invalid_json = '{"key": "value", "number": 123'  # Missing closing brace
        self.assertFalse(self.is_valid_json(invalid_json))

        # Empty JSON should be valid
        empty_json = '{}'
        self.assertTrue(self.is_valid_json(empty_json))

    def test_tag_validation(self):
        """Test tag validation logic."""
        # Valid tags
        valid_tags = ['tag1', 'tag2', 'test-tag']
        self.assertTrue(self.validate_tags(valid_tags))

        # Empty tags should be valid
        empty_tags = []
        self.assertTrue(self.validate_tags(empty_tags))

        # Tags with empty strings should be filtered
        tags_with_empty = ['tag1', '', 'tag2', '   ']
        filtered_tags = self.filter_tags(tags_with_empty)
        self.assertEqual(filtered_tags, ['tag1', 'tag2'])

    # Helper methods that simulate the validation logic

    def validate_scenario_data(self, scenario_data):
        """Simulate scenario data validation."""
        errors = []

        # Required fields
        if not scenario_data.get('name', '').strip():
            errors.append('Name is required')
        if not scenario_data.get('agent_role', '').strip():
            errors.append('Agent role is required')
        if not scenario_data.get('input_data') or not isinstance(scenario_data['input_data'], dict) or len(scenario_data['input_data']) == 0:
            errors.append('Input data is required')
        if not scenario_data.get('metadata') or not isinstance(scenario_data['metadata'], dict) or len(scenario_data['metadata']) == 0:
            errors.append('Metadata is required')

        # Workflow type validation
        if scenario_data.get('metadata') and not scenario_data['metadata'].get('workflow_type'):
            errors.append('Workflow type is required in metadata')

        return errors

    def validate_context_variables(self, context_vars):
        """Validate context variables structure."""
        if not isinstance(context_vars, dict):
            return False

        # Validate trust level
        if 'trust_level' in context_vars:
            if not self.validate_trust_level(context_vars['trust_level']):
                return False

        # Validate mood
        if 'mood' in context_vars:
            mood = context_vars['mood']
            if not isinstance(mood, dict):
                return False
            if 'valence' in mood and not self.validate_mood_value(mood['valence']):
                return False
            if 'arousal' in mood and not self.validate_mood_value(mood['arousal']):
                return False

        # Validate environment
        if 'environment' in context_vars:
            env = context_vars['environment']
            if not isinstance(env, dict):
                return False
            if 'stress_level' in env and not self.validate_environment_value(env['stress_level']):
                return False
            if 'time_pressure' in env and not self.validate_environment_value(env['time_pressure']):
                return False

        return True

    def validate_trust_level(self, value):
        """Validate trust level range (0-100)."""
        return isinstance(value, (int, float)) and 0 <= value <= 100

    def validate_mood_value(self, value):
        """Validate mood value range (-1.0 to 1.0)."""
        return isinstance(value, (int, float)) and -1.0 <= value <= 1.0

    def validate_environment_value(self, value):
        """Validate environment value range (0-100)."""
        return isinstance(value, (int, float)) and 0 <= value <= 100

    def get_trust_phase(self, trust_level):
        """Calculate trust phase from trust level."""
        if trust_level >= 70:
            return 'Integration'
        elif trust_level >= 40:
            return 'Expansion'
        else:
            return 'Foundation'

    def get_mood_quadrant(self, valence, arousal):
        """Calculate mood quadrant from valence and arousal."""
        if valence > 0 and arousal > 0:
            return 'Excited/Happy'
        elif valence > 0 and arousal < 0:
            return 'Calm/Content'
        elif valence < 0 and arousal > 0:
            return 'Stressed/Angry'
        elif valence < 0 and arousal < 0:
            return 'Sad/Depressed'
        else:
            return 'Neutral'

    def is_valid_json(self, json_string):
        """Check if a string is valid JSON."""
        try:
            json.loads(json_string)
            return True
        except (json.JSONDecodeError, TypeError):
            return False

    def validate_tags(self, tags):
        """Validate tags list."""
        return isinstance(tags, list) and all(isinstance(tag, str) for tag in tags)

    def filter_tags(self, tags):
        """Filter out empty tags."""
        return [tag.strip() for tag in tags if tag.strip()]


if __name__ == '__main__':
    unittest.main()
