# backend/apps/admin_tools/tests/test_benchmark_management_view.py

import pytest
import json
import logging
from unittest.mock import patch

from django.urls import reverse
from django.test import Client
from django.contrib.auth import get_user_model

from apps.main.models import BenchmarkScenario, BenchmarkTag, EvaluationCriteriaTemplate, GenericAgent

User = get_user_model()
logger = logging.getLogger(__name__)

# Import the class-scoped cleanup fixture from conftest.py
from conftest import clean_db_for_class

@pytest.mark.django_db(transaction=True)
class TestBenchmarkManagementView:
    """Tests for the benchmark_management view."""

    @pytest.fixture(scope="class", autouse=True)
    def setup_and_teardown(self, clean_db_for_class):
        """Set up test data and clean up after tests."""
        # This will run the clean_db_for_class fixture before and after the class
        yield

    def test_management_access_anonymous(self, client):
        """Test anonymous users are redirected to login."""
        url = reverse('admin:benchmark_management')
        response = client.get(url)
        assert response.status_code == 302
        assert '/admin/login/' in response.url

    def test_management_access_regular_user(self, client):
        """Test regular users are redirected to login."""
        # Create a regular user
        user = User.objects.create_user(username='regular_user', password='password')
        client.force_login(user)

        url = reverse('admin:benchmark_management')
        response = client.get(url)
        assert response.status_code == 302
        assert '/admin/login/' in response.url

    def test_management_access_staff_user(self, client):
        """Test staff users can access the management page."""
        # Create a staff user
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_management')
        response = client.get(url)
        assert response.status_code == 200
        assert 'Benchmark Management' in response.content.decode()

    def test_management_context_data(self, client):
        """Test the management page context data."""
        # Create test data
        agent_role = "test_agent"
        GenericAgent.objects.create(
            role=agent_role,
            version="1.0.0",
            description="Test agent",
            system_instructions="Test instructions",
            input_schema={},
            output_schema={},
            langgraph_node_class="test.node"
        )

        tag = BenchmarkTag.objects.create(name="test_tag")

        scenario = BenchmarkScenario.objects.create(
            name="Test Scenario",
            description="Test description",
            agent_role=agent_role,
            input_data={"test": "data"},
            metadata={"workflow_type": "test_workflow"},
            is_active=True
        )
        scenario.tags.add(tag)

        template, _ = EvaluationCriteriaTemplate.objects.get_or_create(
            name="Test Template",
            defaults={
                "description": "Test description",
                "criteria": {"Content": ["Test criterion"]}
            }
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_management')
        response = client.get(url)

        # Check context data
        assert response.status_code == 200
        assert 'scenarios' in response.context
        assert 'tags' in response.context
        assert 'templates' in response.context
        assert 'workflow_types' in response.context
        assert 'agent_roles' in response.context

        # Check that our test data is in the context
        assert len(response.context['scenarios']) == 1
        assert response.context['scenarios'][0].name == "Test Scenario"

        assert len(response.context['tags']) == 1
        assert response.context['tags'][0].name == "test_tag"

        assert len(response.context['templates']) == 1
        assert response.context['templates'][0].name == "Test Template"

        assert len(response.context['workflow_types']) == 1
        assert "test_workflow" in response.context['workflow_types']

        assert len(response.context['agent_roles']) == 1
        assert agent_role in response.context['agent_roles']

    @patch('apps.admin_tools.views.BenchmarkScenario.objects.filter')
    def test_management_db_error(self, mock_filter, client):
        """Test handling of database errors."""
        # Mock a database error
        mock_filter.side_effect = Exception("DB Error!")

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_management')
        response = client.get(url)

        # Check that the view handles the error gracefully
        assert response.status_code == 200
        assert 'error' in response.context
        assert 'DB Error!' in response.context['error']

        # Check that empty lists are provided for context variables
        assert response.context['scenarios'] == []
        assert response.context['tags'] == []
        assert response.context['templates'] == []
        assert response.context['workflow_types'] == []
        assert response.context['agent_roles'] == []


@pytest.mark.django_db(transaction=True)
class TestBenchmarkScenarioAPI:
    """Tests for the BenchmarkScenarioView API."""

    @pytest.fixture(scope="class", autouse=True)
    def setup_and_teardown(self, clean_db_for_class):
        """Set up test data and clean up after tests."""
        # This will run the clean_db_for_class fixture before and after the class
        yield

    def test_scenario_api_access_anonymous(self, client):
        """Test anonymous users cannot access the API."""
        url = reverse('admin:benchmark_scenarios_api')
        response = client.get(url)
        assert response.status_code == 403

    def test_scenario_api_access_regular_user(self, client):
        """Test regular users cannot access the API."""
        # Create a regular user
        user = User.objects.create_user(username='regular_user', password='password')
        client.force_login(user)

        url = reverse('admin:benchmark_scenarios_api')
        response = client.get(url)
        assert response.status_code == 403

    def test_scenario_api_list(self, client):
        """Test listing scenarios via the API."""
        # Create test data
        agent_role = "test_agent"
        tag = BenchmarkTag.objects.create(name="test_tag")

        scenario = BenchmarkScenario.objects.create(
            name="Test Scenario",
            description="Test description",
            agent_role=agent_role,
            input_data={"test": "data"},
            metadata={"workflow_type": "test_workflow"},
            is_active=True,
            is_latest=True
        )
        scenario.tags.add(tag)

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenarios_api')
        response = client.get(url)

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        assert 'scenarios' in data
        assert len(data['scenarios']) == 1
        assert data['scenarios'][0]['name'] == "Test Scenario"
        assert data['scenarios'][0]['agent_role'] == agent_role
        assert data['scenarios'][0]['workflow_type'] == "test_workflow"
        assert data['scenarios'][0]['is_active'] is True
        assert len(data['scenarios'][0]['tags']) == 1
        assert data['scenarios'][0]['tags'][0]['name'] == "test_tag"

    def test_scenario_api_detail(self, client):
        """Test getting scenario details via the API."""
        # Create test data
        agent_role = "test_agent"
        tag = BenchmarkTag.objects.create(name="test_tag")

        scenario = BenchmarkScenario.objects.create(
            name="Test Scenario",
            description="Test description",
            agent_role=agent_role,
            input_data={"test": "data"},
            metadata={"workflow_type": "test_workflow"},
            is_active=True
        )
        scenario.tags.add(tag)

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenario_detail_api', kwargs={'scenario_id': scenario.id})
        response = client.get(url)

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        # The API returns the scenario data wrapped in a 'scenario' key
        scenario_data = data['scenario']
        assert scenario_data['name'] == "Test Scenario"
        assert scenario_data['description'] == "Test description"
        assert scenario_data['agent_role'] == agent_role
        assert scenario_data['input_data'] == {"test": "data"}
        assert scenario_data['metadata'] == {"workflow_type": "test_workflow"}
        assert scenario_data['is_active'] is True
        assert len(scenario_data['tags']) == 1
        assert scenario_data['tags'][0]['name'] == "test_tag"

    def test_scenario_api_create(self, client):
        """Test creating a scenario via the API."""
        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenarios_api')
        data = {
            "name": "New Scenario",
            "description": "New description",
            "agent_role": "test_agent",
            "input_data": {"test": "data"},
            "metadata": {"workflow_type": "test_workflow"},
            "is_active": True,
            "tags": ["new_tag"]
        }

        response = client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )

        # Check response
        assert response.status_code == 200
        result = json.loads(response.content)
        assert result['success'] is True
        assert 'id' in result

        # Check that the scenario was created
        scenario = BenchmarkScenario.objects.get(name="New Scenario")
        assert scenario.description == "New description"
        assert scenario.agent_role == "test_agent"
        assert scenario.input_data == {"test": "data"}
        assert scenario.metadata == {"workflow_type": "test_workflow"}
        assert scenario.is_active is True

        # Check that the tag was created and associated
        assert scenario.tags.count() == 1
        assert scenario.tags.first().name == "new_tag"

    def test_scenario_api_update(self, client):
        """Test updating a scenario via the API."""
        # Create test data
        agent_role = "test_agent"
        tag = BenchmarkTag.objects.create(name="test_tag")

        scenario = BenchmarkScenario.objects.create(
            name="Test Scenario",
            description="Test description",
            agent_role=agent_role,
            input_data={"test": "data"},
            metadata={"workflow_type": "test_workflow"},
            is_active=True
        )
        scenario.tags.add(tag)

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenario_detail_api', kwargs={'scenario_id': scenario.id})
        data = {
            "name": "Updated Scenario",
            "description": "Updated description",
            "agent_role": "updated_agent",
            "input_data": {"updated": "data"},
            "metadata": {"workflow_type": "updated_workflow"},
            "is_active": False,
            "tags": ["updated_tag"]
        }

        response = client.put(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )

        # Check response
        assert response.status_code == 200
        result = json.loads(response.content)
        assert result['success'] is True

        # Check that the scenario was updated
        scenario.refresh_from_db()
        assert scenario.name == "Updated Scenario"
        assert scenario.description == "Updated description"
        assert scenario.agent_role == "updated_agent"
        assert scenario.input_data == {"updated": "data"}
        assert scenario.metadata == {"workflow_type": "updated_workflow"}
        assert scenario.is_active is False

        # Check that the tag was updated
        assert scenario.tags.count() == 1
        assert scenario.tags.first().name == "updated_tag"

    def test_scenario_api_delete(self, client):
        """Test deleting a scenario via the API."""
        # Create test data
        scenario = BenchmarkScenario.objects.create(
            name="Test Scenario",
            description="Test description",
            agent_role="test_agent",
            input_data={"test": "data"},
            metadata={"workflow_type": "test_workflow"},
            is_active=True
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenario_detail_api', kwargs={'scenario_id': scenario.id})
        response = client.delete(url)

        # Check response
        assert response.status_code == 200
        result = json.loads(response.content)
        assert result['success'] is True

        # Check that the scenario was deleted
        assert not BenchmarkScenario.objects.filter(id=scenario.id).exists()

    def test_scenario_api_filter_by_agent_role(self, client):
        """Test filtering scenarios by agent role."""
        # Create test data
        BenchmarkScenario.objects.create(
            name="Scenario 1",
            agent_role="agent1",
            input_data={},
            metadata={},
            is_latest=True
        )

        BenchmarkScenario.objects.create(
            name="Scenario 2",
            agent_role="agent2",
            input_data={},
            metadata={},
            is_latest=True
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenarios_api') + '?agent_role=agent1'
        response = client.get(url)

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        assert len(data['scenarios']) == 1
        assert data['scenarios'][0]['name'] == "Scenario 1"

    def test_scenario_api_filter_by_tag(self, client):
        """Test filtering scenarios by tag."""
        # Create test data
        tag1 = BenchmarkTag.objects.create(name="tag1")
        tag2 = BenchmarkTag.objects.create(name="tag2")

        scenario1 = BenchmarkScenario.objects.create(
            name="Scenario 1",
            agent_role="agent",
            input_data={},
            metadata={},
            is_latest=True
        )
        scenario1.tags.add(tag1)

        scenario2 = BenchmarkScenario.objects.create(
            name="Scenario 2",
            agent_role="agent",
            input_data={},
            metadata={},
            is_latest=True
        )
        scenario2.tags.add(tag2)

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenarios_api') + f'?tag={tag1.id}'
        response = client.get(url)

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        assert len(data['scenarios']) == 1
        assert data['scenarios'][0]['name'] == "Scenario 1"

    def test_scenario_api_filter_by_workflow_type(self, client):
        """Test filtering scenarios by workflow type."""
        # Create test data
        BenchmarkScenario.objects.create(
            name="Scenario 1",
            agent_role="agent",
            input_data={},
            metadata={"workflow_type": "workflow1"},
            is_latest=True
        )

        BenchmarkScenario.objects.create(
            name="Scenario 2",
            agent_role="agent",
            input_data={},
            metadata={"workflow_type": "workflow2"},
            is_latest=True
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenarios_api') + '?workflow_type=workflow1'
        response = client.get(url)

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        assert len(data['scenarios']) == 1
        assert data['scenarios'][0]['name'] == "Scenario 1"

    def test_scenario_api_filter_by_is_active(self, client):
        """Test filtering scenarios by is_active."""
        # Create test data
        BenchmarkScenario.objects.create(
            name="Scenario 1",
            agent_role="agent",
            input_data={},
            metadata={},
            is_active=True,
            is_latest=True
        )

        BenchmarkScenario.objects.create(
            name="Scenario 2",
            agent_role="agent",
            input_data={},
            metadata={},
            is_active=False,
            is_latest=True
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenarios_api') + '?is_active=true'
        response = client.get(url)

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        assert len(data['scenarios']) == 1
        assert data['scenarios'][0]['name'] == "Scenario 1"

    def test_scenario_api_batch_activate(self, client):
        """Test batch activating scenarios via the API."""
        # Create test data
        scenario1 = BenchmarkScenario.objects.create(
            name="Scenario 1",
            agent_role="agent",
            input_data={},
            metadata={},
            is_active=False,
            is_latest=True
        )

        scenario2 = BenchmarkScenario.objects.create(
            name="Scenario 2",
            agent_role="agent",
            input_data={},
            metadata={},
            is_active=False,
            is_latest=True
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenarios_api')
        response = client.patch(
            url,
            data=json.dumps({
                'scenario_ids': [scenario1.id, scenario2.id],
                'set_active': True
            }),
            content_type='application/json'
        )

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True
        assert 'message' in data

        # Check that the scenarios were activated
        scenario1.refresh_from_db()
        scenario2.refresh_from_db()
        assert scenario1.is_active is True
        assert scenario2.is_active is True

    def test_scenario_api_batch_add_tag(self, client):
        """Test batch adding tags to scenarios via the API."""
        # Create test data
        scenario1 = BenchmarkScenario.objects.create(
            name="Scenario 1",
            agent_role="agent",
            input_data={},
            metadata={},
            is_active=True,
            is_latest=True
        )

        scenario2 = BenchmarkScenario.objects.create(
            name="Scenario 2",
            agent_role="agent",
            input_data={},
            metadata={},
            is_active=True,
            is_latest=True
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenarios_api')
        response = client.patch(
            url,
            data=json.dumps({
                'scenario_ids': [scenario1.id, scenario2.id],
                'add_tag': 'test_tag'
            }),
            content_type='application/json'
        )

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True

        # Check that the tag was added to the scenarios
        scenario1.refresh_from_db()
        scenario2.refresh_from_db()
        assert scenario1.tags.filter(name='test_tag').exists()
        assert scenario2.tags.filter(name='test_tag').exists()


@pytest.mark.django_db(transaction=True)
class TestEvaluationCriteriaTemplateAPI:
    """Tests for the EvaluationCriteriaTemplateView API."""

    @pytest.fixture(scope="class", autouse=True)
    def setup_and_teardown(self, clean_db_for_class):
        """Set up test data and clean up after tests."""
        # This will run the clean_db_for_class fixture before and after the class
        yield

    def test_template_api_access_anonymous(self, client):
        """Test anonymous users cannot access the API."""
        url = reverse('admin:evaluation_templates_api')
        response = client.get(url)
        assert response.status_code == 403

    def test_template_api_access_regular_user(self, client):
        """Test regular users cannot access the API."""
        # Create a regular user
        user = User.objects.create_user(username='regular_user', password='password')
        client.force_login(user)

        url = reverse('admin:evaluation_templates_api')
        response = client.get(url)
        assert response.status_code == 403

    def test_template_api_list(self, client):
        """Test listing templates via the API."""
        # Create test data
        template, _ = EvaluationCriteriaTemplate.objects.get_or_create(
            name="Test Template",
            defaults={
                "description": "Test description",
                "criteria": {"Content": ["Test criterion"]}
            }
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:evaluation_templates_api')
        response = client.get(url)

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        assert 'templates' in data
        assert len(data['templates']) == 1
        assert data['templates'][0]['name'] == "Test Template"
        assert data['templates'][0]['description'] == "Test description"

    def test_template_api_detail(self, client):
        """Test getting template details via the API."""
        # Create test data
        template, _ = EvaluationCriteriaTemplate.objects.get_or_create(
            name="Test Template",
            defaults={
                "description": "Test description",
                "criteria": {"Content": ["Test criterion"]}
            }
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:evaluation_template_detail_api', kwargs={'template_id': template.id})
        response = client.get(url)

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['name'] == "Test Template"
        assert data['description'] == "Test description"
        assert data['criteria'] == {"Content": ["Test criterion"]}

    def test_template_api_create(self, client):
        """Test creating a template via the API."""
        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:evaluation_templates_api')
        data = {
            "name": "New Template",
            "description": "New description",
            "criteria": {"Content": ["New criterion"]}
        }

        response = client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )

        # Check response
        assert response.status_code == 200
        result = json.loads(response.content)
        assert result['success'] is True
        assert 'id' in result

        # Check that the template was created
        template = EvaluationCriteriaTemplate.objects.get(name="New Template")
        assert template.description == "New description"
        assert template.criteria == {"Content": ["New criterion"]}

    def test_template_api_update(self, client):
        """Test updating a template via the API."""
        # Create test data
        template, _ = EvaluationCriteriaTemplate.objects.get_or_create(
            name="Test Template",
            defaults={
                "description": "Test description",
                "criteria": {"Content": ["Test criterion"]}
            }
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:evaluation_template_detail_api', kwargs={'template_id': template.id})
        data = {
            "name": "Updated Template",
            "description": "Updated description",
            "criteria": {"Content": ["Updated criterion"]}
        }

        response = client.put(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )

        # Check response
        assert response.status_code == 200
        result = json.loads(response.content)
        assert result['success'] is True

        # Check that the template was updated
        template.refresh_from_db()
        assert template.name == "Updated Template"
        assert template.description == "Updated description"
        assert template.criteria == {"Content": ["Updated criterion"]}

    def test_template_api_delete(self, client):
        """Test deleting a template via the API."""
        # Create test data
        template, _ = EvaluationCriteriaTemplate.objects.get_or_create(
            name="Test Template",
            defaults={
                "description": "Test description",
                "criteria": {"Content": ["Test criterion"]}
            }
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:evaluation_template_detail_api', kwargs={'template_id': template.id})
        response = client.delete(url)

        # Check response
        assert response.status_code == 200
        result = json.loads(response.content)
        assert result['success'] is True

        # Check that the template was deleted
        assert not EvaluationCriteriaTemplate.objects.filter(id=template.id).exists()


@pytest.mark.django_db(transaction=True)
class TestWorkflowTypeAPI:
    """Tests for the WorkflowTypeView API."""

    @pytest.fixture(scope="class", autouse=True)
    def setup_and_teardown(self, clean_db_for_class):
        """Set up test data and clean up after tests."""
        # This will run the clean_db_for_class fixture before and after the class
        yield

    def test_workflow_type_api_access_anonymous(self, client):
        """Test anonymous users cannot access the API."""
        url = reverse('admin:workflow_types_api')
        response = client.get(url)
        assert response.status_code == 403

    def test_workflow_type_api_access_regular_user(self, client):
        """Test regular users cannot access the API."""
        # Create a regular user
        user = User.objects.create_user(username='regular_user', password='password')
        client.force_login(user)

        url = reverse('admin:workflow_types_api')
        response = client.get(url)
        assert response.status_code == 403

    def test_workflow_type_api_list(self, client):
        """Test listing workflow types via the API."""
        # Create test data
        agent_role = "test_agent"

        # Create scenarios with different workflow types
        scenario1 = BenchmarkScenario.objects.create(
            name="Test Scenario 1",
            description="Test description",
            agent_role=agent_role,
            input_data={"test": "data"},
            metadata={"workflow_type": "test_workflow_1"},
            is_active=True,
            is_latest=True
        )

        scenario2 = BenchmarkScenario.objects.create(
            name="Test Scenario 2",
            description="Test description",
            agent_role=agent_role,
            input_data={"test": "data"},
            metadata={"workflow_type": "test_workflow_2"},
            is_active=True,
            is_latest=True
        )

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:workflow_types_api')
        response = client.get(url)

        # Check response
        assert response.status_code == 200
        data = json.loads(response.content)
        assert 'workflow_types' in data
        assert 'stats' in data
        assert len(data['workflow_types']) == 2

        # Check that workflow types are included (as simple strings)
        assert "test_workflow_1" in data['workflow_types']
        assert "test_workflow_2" in data['workflow_types']

        # Check that stats are included (as objects with detailed information)
        assert len(data['stats']) == 2
        for stat in data['stats']:
            assert 'name' in stat
            assert 'scenario_count' in stat
            assert 'active_count' in stat
            if stat['name'] == 'test_workflow_1':
                assert stat['scenario_count'] == 1
                assert stat['active_count'] == 1
            elif stat['name'] == 'test_workflow_2':
                assert stat['scenario_count'] == 1
                assert stat['active_count'] == 1


@pytest.mark.django_db(transaction=True)
class TestBenchmarkValidationAPI:
    """Tests for the BenchmarkValidationView API."""

    @pytest.fixture(scope="class", autouse=True)
    def setup_and_teardown(self, clean_db_for_class):
        """Set up test data and clean up after tests."""
        # This will run the clean_db_for_class fixture before and after the class
        yield

    def test_validation_api_access_anonymous(self, client):
        """Test anonymous users cannot access the API."""
        url = reverse('admin:benchmark_validation_api')
        response = client.post(url, data=json.dumps({}), content_type='application/json')
        assert response.status_code == 403

    def test_validation_api_access_regular_user(self, client):
        """Test regular users cannot access the API."""
        # Create a regular user
        user = User.objects.create_user(username='regular_user', password='password')
        client.force_login(user)

        url = reverse('admin:benchmark_validation_api')
        response = client.post(url, data=json.dumps({}), content_type='application/json')
        assert response.status_code == 403

    @patch('apps.admin_tools.benchmark.views.call_command')
    def test_validation_api_single_scenario(self, mock_call_command, client):
        """Test validating a single scenario via the API."""
        # Create test data
        scenario = BenchmarkScenario.objects.create(
            name="Test Scenario",
            description="Test description",
            agent_role="test_agent",
            input_data={"test": "data"},
            metadata={"workflow_type": "test_workflow"},
            is_active=True
        )

        # Mock the call_command function
        mock_call_command.return_value = None

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_scenario_validation_api', kwargs={'scenario_id': scenario.id})
        response = client.post(url, data=json.dumps({}), content_type='application/json')

        # Check response
        assert response.status_code == 200
        result = json.loads(response.content)
        assert result['success'] is True
        assert 'result' in result
        assert result['result']['scenario_id'] == scenario.id
        assert result['result']['scenario_name'] == "Test Scenario"
        assert 'validation_output' in result['result']

        # Check that call_command was called with the correct arguments
        mock_call_command.assert_called_once()
        args, kwargs = mock_call_command.call_args
        assert args[0] == 'validate_benchmarks_v2'
        # In the actual implementation, scenario_id is passed as a positional argument
        assert args[1] == '--scenario-id'
        assert args[2] == str(scenario.id)
        assert args[3] == '--validate-structure'
        assert args[4] == '--validate-templates'

    @patch('apps.admin_tools.benchmark.views.call_command')
    def test_validation_api_multiple_scenarios(self, mock_call_command, client):
        """Test validating multiple scenarios via the API."""
        # Create test data
        scenario1 = BenchmarkScenario.objects.create(
            name="Scenario 1",
            description="Description 1",
            agent_role="agent1",
            input_data={"test": "data1"},
            metadata={"workflow_type": "workflow1"},
            is_active=True
        )

        scenario2 = BenchmarkScenario.objects.create(
            name="Scenario 2",
            description="Description 2",
            agent_role="agent2",
            input_data={"test": "data2"},
            metadata={"workflow_type": "workflow2"},
            is_active=True
        )

        # Mock the call_command function
        mock_call_command.return_value = None

        # Create a staff user and log in
        staff_user = User.objects.create_user(username='staff_user', password='password', is_staff=True)
        client.force_login(staff_user)

        url = reverse('admin:benchmark_validation_api')
        data = {
            "scenario_ids": [scenario1.id, scenario2.id]
        }

        response = client.post(url, data=json.dumps(data), content_type='application/json')

        # Check response
        assert response.status_code == 200
        result = json.loads(response.content)
        assert result['success'] is True
        assert 'results' in result
        assert len(result['results']) == 2

        # Check that call_command was called twice with the correct arguments
        assert mock_call_command.call_count == 2
