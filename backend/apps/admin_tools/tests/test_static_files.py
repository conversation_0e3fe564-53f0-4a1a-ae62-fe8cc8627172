"""
Tests for static files functionality.

This module contains tests to ensure that static files are properly configured and served.
"""

import os
from django.conf import settings
from django.contrib.staticfiles import finders


class TestStaticFiles:
    """Tests for static files functionality."""

    def test_staticfiles_dirs_configuration(self):
        """Test that STATICFILES_DIRS is properly configured."""
        # Check that STATICFILES_DIRS is a list
        assert isinstance(settings.STATICFILES_DIRS, list), "STATICFILES_DIRS should be a list"

        # Check that it contains the expected directories
        static_dirs = [str(path) for path in settings.STATICFILES_DIRS]
        assert any('static' in path for path in static_dirs), "STATICFILES_DIRS should include 'static' directory"
        assert any('frontend/dist' in path for path in static_dirs), "STATICFILES_DIRS should include 'frontend/dist' directory"

    def test_static_root_configuration(self):
        """Test that STATIC_ROOT is properly configured."""
        # Check that STATIC_ROOT is set
        assert settings.STATIC_ROOT, "STATIC_ROOT should be set"

        # Check that it's a string or Path-like object
        assert isinstance(settings.STATIC_ROOT, (str, os.PathLike)), "STATIC_ROOT should be a string or Path-like object"

    def test_admin_static_files_registered(self):
        """Test that admin static files are registered with the static files finder."""
        # Check for admin CSS files
        admin_css = finders.find('admin/css/jsoneditor.css')
        assert admin_css is not None, "Admin CSS file not found by static files finder"

        # Check for admin JS files
        admin_js = finders.find('admin/js/jsoneditor.js')
        assert admin_js is not None, "Admin JS file not found by static files finder"

        # Check for benchmark management JS file
        benchmark_js = finders.find('admin/js/benchmark_management.js')
        assert benchmark_js is not None, "Benchmark management JS file not found by static files finder"
