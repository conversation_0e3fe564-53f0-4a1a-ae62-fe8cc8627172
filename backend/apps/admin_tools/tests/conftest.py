"""
Fixtures for admin_tools tests.
"""
import pytest
from decimal import Decimal
from unittest.mock import MagicMock

from apps.main.tests.test_agents.conftest import TestLLMConfig


@pytest.fixture
def admin_llm_config():
    """
    Fixture providing an LLMConfig object for admin tests.
    
    Returns a TestLLMConfig with a real model name to avoid validation errors.
    """
    # Create a TestLLMConfig with a real string for model_name
    test_llm_config = TestLLMConfig(
        name="admin_test_llm",
        model_name="mistral-small-latest",  # Use a real string for model_name
        temperature=0.5,
        input_token_price=Decimal("0.01"),
        output_token_price=Decimal("0.02"),
        is_default=False,
        is_evaluation=False
    )

    return test_llm_config
