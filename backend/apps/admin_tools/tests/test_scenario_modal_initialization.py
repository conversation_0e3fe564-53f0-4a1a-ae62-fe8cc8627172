"""
Tests for scenario modal initialization and availability.

This module tests that the scenario editing modal is properly initialized
and available when needed.
"""

import unittest
from unittest.mock import Mock, patch
from django.test import TestCase
from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse


class ScenarioModalInitializationTest(TestCase):
    """Test scenario modal initialization."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.staff_user = User.objects.create_user(
            username='staff_user',
            password='testpass123',
            is_staff=True,
            is_active=True
        )

    def test_scenario_modal_scripts_included_in_template(self):
        """Test that scenario modal scripts are included in the benchmark management template."""
        self.client.force_login(self.staff_user)

        url = reverse('game_of_life_admin:benchmark_management')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)

        # Check that the scenario modal scripts are included
        content = response.content.decode('utf-8')
        self.assertIn('scenario_modal_utils.js', content,
                     "scenario_modal_utils.js should be included in the template")
        self.assertIn('scenario_editing_modal.js', content,
                     "scenario_editing_modal.js should be included in the template")

    def test_script_loading_order(self):
        """Test that scripts are loaded in the correct order."""
        self.client.force_login(self.staff_user)

        url = reverse('game_of_life_admin:benchmark_management')
        response = self.client.get(url)

        content = response.content.decode('utf-8')

        # Find the positions of the script tags
        utils_pos = content.find('scenario_modal_utils.js')
        modal_pos = content.find('scenario_editing_modal.js')
        main_pos = content.find('benchmark_management.js')

        # Verify that utils comes before modal, and modal comes before main
        self.assertNotEqual(utils_pos, -1, "scenario_modal_utils.js not found")
        self.assertNotEqual(modal_pos, -1, "scenario_editing_modal.js not found")
        self.assertNotEqual(main_pos, -1, "benchmark_management.js not found")

        self.assertLess(utils_pos, modal_pos,
                       "scenario_modal_utils.js should load before scenario_editing_modal.js")
        self.assertLess(modal_pos, main_pos,
                       "scenario_editing_modal.js should load before benchmark_management.js")


class ScenarioModalAvailabilityTest(unittest.TestCase):
    """Test scenario modal availability in JavaScript context."""

    def test_scenario_modal_initialization_failure_before_fix(self):
        """Test that reproduces the original ScenarioEditingModal not available error."""
        # Simulate the JavaScript environment where window.scenarioEditingModal is undefined

        # Mock the window object
        mock_window = Mock()
        mock_window.scenarioEditingModal = None
        mock_window.ScenarioEditingModal = None
        mock_window.showError = Mock()

        # Mock console.error
        mock_console = Mock()

        # Simulate the OLD editScenario function behavior (before fix)
        def old_edit_scenario(scenario_id):
            print(f"editScenario called for ID: {scenario_id} - attempting to show modal")

            # Use the new modular scenario editing modal
            if mock_window.scenarioEditingModal:
                mock_window.scenarioEditingModal.show(scenario_id)
            else:
                mock_console.error('ScenarioEditingModal not available')
                mock_window.showError('Scenario editor not available. Please refresh the page.')
                return False
            return True

        # Test the failure case
        result = old_edit_scenario('123')

        # Verify that the error was logged and showError was called
        mock_console.error.assert_called_with('ScenarioEditingModal not available')
        mock_window.showError.assert_called_with('Scenario editor not available. Please refresh the page.')
        self.assertFalse(result, "editScenario should return False when modal is not available")

    def test_scenario_modal_initialization_with_fallback_fix(self):
        """Test the new editScenario function with fallback initialization."""
        # Mock the window object with ScenarioEditingModal class available but instance not created
        mock_window = Mock()
        mock_window.scenarioEditingModal = None
        mock_modal_class = Mock()
        mock_modal_instance = Mock()
        mock_modal_instance.show = Mock()
        mock_modal_class.return_value = mock_modal_instance
        mock_window.ScenarioEditingModal = mock_modal_class
        mock_window.showError = Mock()

        # Mock console
        mock_console = Mock()

        # Simulate the NEW editScenario function behavior (with fix)
        def new_edit_scenario(scenario_id):
            print(f"editScenario called for ID: {scenario_id} - attempting to show modal")

            # Function to show the modal
            def show_modal():
                if mock_window.scenarioEditingModal:
                    mock_window.scenarioEditingModal.show(scenario_id)
                    return True
                else:
                    mock_console.error('ScenarioEditingModal not available')
                    mock_window.showError('Scenario editor not available. Please refresh the page.')
                    return False

            # Check if modal is already available
            if mock_window.scenarioEditingModal:
                return show_modal()

            # If not available, try to initialize it
            if mock_window.ScenarioEditingModal and not mock_window.scenarioEditingModal:
                print('Initializing ScenarioEditingModal...')
                mock_window.scenarioEditingModal = mock_window.ScenarioEditingModal()
                return show_modal()

            # Fallback to error
            mock_console.error('ScenarioEditingModal not available')
            mock_window.showError('Scenario editor not available. Please refresh the page.')
            return False

        # Test the success case with fallback initialization
        result = new_edit_scenario('123')

        # Verify that the modal class was instantiated and show was called
        mock_modal_class.assert_called_once()
        mock_modal_instance.show.assert_called_with('123')
        mock_console.error.assert_not_called()
        mock_window.showError.assert_not_called()
        self.assertTrue(result, "editScenario should return True when modal can be initialized")

    def test_scenario_modal_initialization_success(self):
        """Test successful scenario modal initialization."""
        # Mock the window object with a properly initialized modal
        mock_window = Mock()
        mock_modal = Mock()
        mock_modal.show = Mock()
        mock_window.scenarioEditingModal = mock_modal
        mock_window.showError = Mock()

        # Mock console.error
        mock_console = Mock()

        # Simulate the editScenario function behavior
        def edit_scenario(scenario_id):
            print(f"editScenario called for ID: {scenario_id} - attempting to show modal")

            # Use the new modular scenario editing modal
            if mock_window.scenarioEditingModal:
                mock_window.scenarioEditingModal.show(scenario_id)
                return True
            else:
                mock_console.error('ScenarioEditingModal not available')
                mock_window.showError('Scenario editor not available. Please refresh the page.')
                return False

        # Test the success case
        result = edit_scenario('123')

        # Verify that the modal.show was called and no error occurred
        mock_modal.show.assert_called_with('123')
        mock_console.error.assert_not_called()
        mock_window.showError.assert_not_called()
        self.assertTrue(result, "editScenario should return True when modal is available")

    def test_dom_content_loaded_timing(self):
        """Test that modal initialization happens after DOM content is loaded."""
        # Mock DOM and window objects
        mock_document = Mock()
        mock_window = Mock()

        # Track if addEventListener was called for DOMContentLoaded
        dom_loaded_listeners = []

        def mock_add_event_listener(event_type, callback):
            if event_type == 'DOMContentLoaded':
                dom_loaded_listeners.append(callback)

        mock_document.addEventListener = mock_add_event_listener

        # Simulate the scenario modal initialization code
        def initialize_scenario_modal():
            def on_dom_loaded():
                mock_window.scenarioEditingModal = Mock()
                mock_window.ScenarioEditingModal = Mock()

            mock_document.addEventListener('DOMContentLoaded', on_dom_loaded)

        # Initialize the modal
        initialize_scenario_modal()

        # Verify that a DOMContentLoaded listener was added
        self.assertEqual(len(dom_loaded_listeners), 1,
                        "Should have one DOMContentLoaded listener")

        # Simulate DOM content loaded event
        dom_loaded_listeners[0]()

        # Verify that the modal was initialized
        self.assertIsNotNone(mock_window.scenarioEditingModal,
                           "scenarioEditingModal should be initialized after DOMContentLoaded")

    def test_script_dependency_order(self):
        """Test that script dependencies are loaded in correct order."""
        # Mock the loading sequence
        loaded_scripts = []

        def mock_script_load(script_name):
            loaded_scripts.append(script_name)

            # Simulate what each script does when loaded
            if script_name == 'scenario_modal_utils.js':
                # Utils should be available first
                return {'ScenarioModalUtils': Mock()}
            elif script_name == 'scenario_editing_modal.js':
                # Modal should be available after utils
                if 'scenario_modal_utils.js' in loaded_scripts:
                    return {'ScenarioEditingModal': Mock()}
                else:
                    raise Exception("ScenarioModalUtils not available")
            elif script_name == 'benchmark_management.js':
                # Main script should be able to use the modal
                if 'scenario_editing_modal.js' in loaded_scripts:
                    return {'editScenario': Mock()}
                else:
                    raise Exception("ScenarioEditingModal not available")

        # Test correct loading order
        try:
            mock_script_load('scenario_modal_utils.js')
            mock_script_load('scenario_editing_modal.js')
            mock_script_load('benchmark_management.js')
            success = True
        except Exception as e:
            success = False
            error_message = str(e)

        self.assertTrue(success, "Scripts should load successfully in correct order")

        # Test incorrect loading order (should fail)
        loaded_scripts.clear()
        try:
            mock_script_load('benchmark_management.js')  # Load main script first
            success = False  # Should not reach here
        except Exception as e:
            success = True  # Expected to fail
            self.assertIn("ScenarioEditingModal not available", str(e))

        self.assertTrue(success, "Loading scripts in wrong order should fail")


if __name__ == '__main__':
    unittest.main()
