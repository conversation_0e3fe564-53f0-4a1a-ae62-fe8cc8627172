from django.contrib import admin
from django import forms
from django.utils.html import format_html
from config.admin import admin_site # Import the custom admin site
from django.db.models import Count
from django.contrib.admin.widgets import FilteredSelectMultiple
from django.contrib.auth.models import User # <-- Import User model
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin # <-- Import standard UserAdmin
from .models import (
    GenericEnvironment, GenericEnvironmentDomainRelationship, 
    UserEnvironment, UserEnvironmentPhysicalProperties, 
    UserEnvironmentSocialContext, UserEnvironmentActivitySupport,
    UserEnvironmentPsychologicalQualities,
    UserProfile, Demographics, GenericTrait,
    GenericBelief, Belief, GenericBeliefDomainRelationship
)
from apps.activity.models import GenericDomain # Renamed import

# @admin.register(GenericTrait) # Use admin_site.register instead
class GenericTraitAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'trait_type', 'description')
    list_filter = ('trait_type',)
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'trait_type', 'description')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )

class GenericEnvironmentDomainRelationshipInline(admin.TabularInline):
    model = GenericEnvironmentDomainRelationship
    extra = 1
    verbose_name = "Domain Support Relationship"
    verbose_name_plural = "Domain Support Relationships"
    raw_id_fields = ['domain']
    autocomplete_fields = ['domain']
    
    # Add a column that shows a visual representation of the support level
    readonly_fields = ['get_strength_display_visual']
    
    def get_strength_display_visual(self, obj):
        # Create a visual representation of the strength, color-coded
        strength = obj.strength
        if strength >= 70:
            color = 'green'
        elif strength >= 30:
            color = 'blue'
        elif strength >= 0:
            color = 'gray'
        elif strength >= -30:
            color = 'orange'
        else:
            color = 'red'
            
        # Create a progress bar style display
        bar_width = abs(strength)
        direction = 'right' if strength >= 0 else 'left'
        
        return format_html(
            '<div style="width:100%; background-color:#f0f0f0; height:20px; position:relative;">'
            '<div style="position:absolute; left:50%; top:0; bottom:0; width:1px; background-color:#999;"></div>'
            '<div style="position:absolute; {}: 50%; width:{}%; background-color:{}; height:100%;">'
            '<span style="padding:0 5px; line-height:20px; color:white;">{}</span>'
            '</div></div>',
            'left' if direction == 'right' else 'right',
            bar_width,
            color,
            strength
        )
    get_strength_display_visual.short_description = "Support Level"

class GenericEnvironmentAdminForm(forms.ModelForm):
    """
    Custom form for GenericEnvironment admin with domain categorization
    """
    class Meta:
        model = GenericEnvironment
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Ensure the primary_category field uses the choices from model
        if 'primary_category' in self.fields:
            self.fields['primary_category'].widget = forms.Select(
                choices=GenericEnvironment.PrimaryCategory.choices
            )
        
        # Group domains by primary category for better organization in the admin interface
        from django.db.models import Count
        domain_categories = GenericDomain.objects.values('primary_category').annotate( # Use renamed model
            count=Count('id')).order_by('primary_category')
        
        # Add category counts to help filter field
        if 'domain_relationships' in self.fields:
            self.fields['domain_relationships'].help_text += '<br>Domains by category: ' + ', '.join(
                [f"{d['primary_category']} ({d['count']})" for d in domain_categories if d['primary_category']]
            )

# @admin.register(GenericEnvironment) # Use admin_site.register instead
class GenericEnvironmentAdmin(admin.ModelAdmin):
    form = GenericEnvironmentAdminForm
    list_display = ('name', 'code', 'get_primary_domain_display', 'is_indoor',
                    'get_primary_category_display', 'is_active', 'get_domain_support_count')
    list_filter = ('is_indoor', 'is_active', 'primary_category', 'domain_relationships')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [GenericEnvironmentDomainRelationshipInline]
    # Removed domain_relationships from autocomplete_fields since it has a custom through model
    
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Classification', {
            'fields': ('is_indoor', 'primary_category'),
            'description': 'Basic classification of this environment type'
        }),
        # Removed the domain_relationships field from fieldsets since it has a custom through model
        # Domain relationships are managed through the inline instead
        ('Environment Properties', {
            'fields': ('typical_space_size', 'typical_privacy_level', 'archetype_attributes'),
            'classes': ('collapse',),
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    def get_primary_domain_display(self, obj):
        primary_rel = obj.domain_relationships_details.filter(
            strength=GenericEnvironmentDomainRelationship.RelationshipStrength.PERFECT
        ).first()
        
        if primary_rel:
            return format_html(
                '<span style="color: #417690; font-weight: bold;">{}</span>',
                primary_rel.domain.name
            )
        return '-'
    get_primary_domain_display.short_description = 'Primary Domain'
    
    def get_domain_support_count(self, obj):
        return obj.domain_relationships_details.count()
    get_domain_support_count.short_description = 'Domain Support Count'
    
    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related(
            'domain_relationships_details',
            'domain_relationships_details__domain'
        )
    
    def save_related(self, request, form, formsets, change):
        """Ensure that domain relationships are properly maintained when saving"""
        super().save_related(request, form, formsets, change)
        # Check for missing domain relationships based on the domain_relationships field
        if form.instance and form.cleaned_data.get('domain_relationships'):
            existing_rels = form.instance.domain_relationships_details.values_list('domain_id', flat=True)
            for domain in form.cleaned_data['domain_relationships']:
                if domain.id not in existing_rels:
                    # Create a default relationship with MODERATE strength
                    GenericEnvironmentDomainRelationship.objects.create(
                        generic_environment=form.instance,
                        domain=domain,
                        strength=GenericEnvironmentDomainRelationship.RelationshipStrength.MODERATE
                    )

# @admin.register(GenericEnvironmentDomainRelationship) # Use admin_site.register instead
class GenericEnvironmentDomainRelationshipAdmin(admin.ModelAdmin):
    list_display = ('generic_environment', 'domain', 'strength', 'get_strength_visual', 'get_domain_category')
    list_filter = ('strength', 'domain', 'domain__primary_category', 'generic_environment')
    search_fields = ('generic_environment__name', 'domain__name')
    autocomplete_fields = ['domain', 'generic_environment']
    
    def get_domain_category(self, obj):
        if obj.domain.primary_category:
            return obj.domain.get_primary_category_display()
        return 'Not categorized'
    get_domain_category.short_description = 'Domain Category'
    
    def get_primary_category_display(self, obj):
        """Display the primary category with proper formatting"""
        if obj.primary_category:
            return obj.get_primary_category_display()
        return 'Not categorized'
    get_primary_category_display.short_description = 'Category'
    
    def get_strength_visual(self, obj):
        strength = obj.strength
        if strength >= 70:
            color = 'green'
            label = 'Strong Support'
        elif strength >= 30:
            color = 'blue'
            label = 'Moderate Support'
        elif strength >= 0:
            color = 'gray'
            label = 'Neutral'
        elif strength >= -30:
            color = 'orange'
            label = 'Somewhat Discouraging'
        else:
            color = 'red'
            label = 'Strongly Discouraging'
            
        return format_html(
            '<span style="color: {}; font-weight: bold;">{} ({})</span>',
            color, label, strength
        )
    get_strength_visual.short_description = 'Support Level'

# Form for user environment activity support with domain-specific overrides
class UserEnvironmentActivitySupportAdminForm(forms.ModelForm):
    """
    Custom form for UserEnvironmentActivitySupport with dynamic domain support fields
    """
    class Meta:
        model = UserEnvironmentActivitySupport
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # If this is an existing instance, add dynamic fields for domain support
        if self.instance and self.instance.pk and self.instance.user_environment and self.instance.user_environment.generic_environment:
            gen_env = self.instance.user_environment.generic_environment
            
            # Get the domain support summary from generic environment
            domain_rels = gen_env.domain_relationships_details.all().select_related('domain')
            
            # Add dynamic fields for each domain
            for rel in domain_rels:
                domain = rel.domain
                field_name = f"domain_support_{domain.code}"
                
                # Get current value (user override or default from generic environment)
                initial_value = self.instance.domain_specific_support.get(
                    domain.code, 
                    rel.strength
                )
                
                # Add the field to the form
                self.fields[field_name] = forms.IntegerField(
                    label=f"{domain.name} Support",
                    help_text=f"Override support level for {domain.name} activities (default: {rel.strength})",
                    min_value=-100,
                    max_value=100,
                    initial=initial_value,
                    required=False
                )
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Initialize domain_specific_support if needed
        if not instance.domain_specific_support:
            instance.domain_specific_support = {}
        
        # Update domain_specific_support with values from dynamic fields
        if instance.user_environment.generic_environment:
            for field_name, value in self.cleaned_data.items():
                if field_name.startswith('domain_support_') and value is not None:
                    domain_code = field_name.replace('domain_support_', '')
                    instance.domain_specific_support[domain_code] = value
        
        if commit:
            instance.save()
        return instance

class UserEnvironmentPhysicalPropertiesInline(admin.StackedInline):
    model = UserEnvironmentPhysicalProperties
    fieldsets = (
        (None, {
            'fields': ('rurality', 'noise_level', 'light_quality', 'temperature_range'),
        }),
        ('Additional Properties', {
            'fields': ('accessibility', 'air_quality', 'has_natural_elements', 
                      'surface_type', 'water_proximity', 'space_size'),
            'classes': ('collapse',),
        }),
    )

class UserEnvironmentSocialContextInline(admin.StackedInline):
    model = UserEnvironmentSocialContext
    fieldsets = (
        (None, {
            'fields': ('privacy_level', 'typical_occupancy', 'social_interaction_level'),
        }),
        ('Additional Properties', {
            'fields': ('formality_level', 'safety_level', 'supervision_level', 'cultural_diversity'),
            'classes': ('collapse',),
        }),
    )

class UserEnvironmentPsychologicalQualitiesInline(admin.StackedInline):
    model = UserEnvironmentPsychologicalQualities
    fieldsets = (
        (None, {
            'fields': ('restorative_quality', 'comfort_level', 'personal_significance'),
        }),
        ('Additional Properties', {
            'fields': ('stimulation_level', 'aesthetic_appeal', 'novelty_level', 
                      'emotional_associations'),
            'classes': ('collapse',),
        }),
    )

class UserEnvironmentActivitySupportInline(admin.StackedInline):
    model = UserEnvironmentActivitySupport
    form = UserEnvironmentActivitySupportAdminForm
    
    def get_fieldsets(self, request, obj=None):
        """Dynamically update the fieldsets to include domain support fields"""
        # Base fieldsets that are always included
        fieldsets = [
            (None, {
                'fields': ('digital_connectivity', 'resource_availability', 'time_availability'),
            }),
        ]
        
        # If this is an existing instance with a generic environment
        if obj and hasattr(obj, 'generic_environment') and obj.generic_environment:
            # Get all domain relationships
            domain_relationships = obj.generic_environment.domain_relationships_details.all()
            
            # If there are domain relationships, add a Domain Support fieldset
            if domain_relationships.exists():
                try:
                    # Create a list of domain support field names
                    domain_fields = [f"domain_support_{rel.domain.code}" for rel in domain_relationships]
                    
                    fieldsets.append(
                        ('Domain Support Overrides', {
                            'fields': domain_fields,
                            'description': 'Override support levels for specific activity domains in this environment',
                            'classes': ('wide',),
                        })
                    )
                except Exception as e:
                    # Handle potential errors gracefully
                    fieldsets.append(
                        ('Domain Support Overrides', {
                            'fields': [],
                            'description': f'Error loading domain fields: {str(e)}. Save the environment first.',
                            'classes': ('wide',),
                        })
                    )
        
        return fieldsets

# Register UserProfile so it can be used in autocomplete_fields
# @admin.register(UserProfile) # Use admin_site.register instead
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('profile_name', 'user', 'current_environment')
    search_fields = ('profile_name', 'user__username')
    raw_id_fields = ('current_environment',)

# Optional: Register Demographics as an inline for UserProfile
class DemographicsInline(admin.StackedInline):
    model = Demographics
    can_delete = False

# Add the inline to UserProfileAdmin
# UserProfileAdmin.inlines = [DemographicsInline] # Moved to explicit registration below

# @admin.register(UserEnvironment) # Use admin_site.register instead
class UserEnvironmentAdmin(admin.ModelAdmin):
    inlines = [
        UserEnvironmentPhysicalPropertiesInline,
        UserEnvironmentSocialContextInline,
        UserEnvironmentActivitySupportInline,
        UserEnvironmentPsychologicalQualitiesInline,
    ]
    list_display = ('environment_name', 'user_profile', 'generic_environment', 
                    'is_current', 'effective_start', 'get_expiration_status')
    list_filter = ('is_current', 'generic_environment', 'user_profile')
    search_fields = ('environment_name', 'environment_description', 'user_profile__profile_name')
    readonly_fields = ('get_domain_support_summary',)
    autocomplete_fields = ['generic_environment', 'user_profile']
    filter_horizontal = ['domains']  # Use filter_horizontal instead of autocomplete for many-to-many
    
    fieldsets = (
        (None, {
            'fields': ('user_profile', 'environment_name', 'environment_description')
        }),
        ('Environment Classification', {
            'fields': ('generic_environment', 'domains'),
            'description': 'Base type and associated domains'
        }),
        ('Activity Support Summary', {
            'fields': ('get_domain_support_summary',),
            'description': 'Visual summary of domain support - save and refresh to update',
            'classes': ('collapse',),
        }),
        ('Status & Timeline', {
            'fields': ('is_current', 'effective_start', 'effective_end', 'environment_details'),
        }),
    )
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Customize the queryset for the generic_environment field to display primary_category"""
        if db_field.name == "generic_environment":
            kwargs["queryset"] = GenericEnvironment.objects.all().order_by('primary_category', 'name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def get_domain_support_summary(self, obj):
        """Generate a visual summary of domain support levels"""
        if not obj.pk or not obj.generic_environment:
            return "Save environment first to see domain support summary"
            
        try:
            # Get domain support summary
            if hasattr(obj, 'activity_support'):
                # Get all domain relationships from generic environment
                gen_env = obj.generic_environment
                domain_rels = gen_env.domain_relationships_details.all().select_related('domain')
                
                # Build an HTML table for the summary
                html = '<table style="width:100%; border-collapse:collapse;">'
                html += '<tr><th style="text-align:left;">Domain</th><th>Support Level</th><th>Override</th></tr>'
                
                for rel in domain_rels:
                    domain = rel.domain
                    # Check if there's a user override
                    default_value = rel.strength
                    user_value = None
                    is_override = False
                    
                    if hasattr(obj.activity_support, 'domain_specific_support'):
                        user_value = obj.activity_support.domain_specific_support.get(domain.code)
                        is_override = user_value is not None
                    
                    value = user_value if is_override else default_value
                    
                    # Determine color based on value
                    if value >= 70:
                        color = 'green'
                        label = 'Strong Support'
                    elif value >= 30:
                        color = 'blue'
                        label = 'Moderate Support'
                    elif value >= 0:
                        color = 'gray'
                        label = 'Neutral'
                    elif value >= -30:
                        color = 'orange'
                        label = 'Somewhat Discouraging'
                    else:
                        color = 'red'
                        label = 'Strongly Discouraging'
                    
                    # Add row to table
                    html += f'<tr style="border-bottom:1px solid #eee;">'
                    html += f'<td style="padding:8px 0;">{domain.name}</td>'
                    html += f'<td style="padding:8px 0; text-align:center;"><span style="color:{color}; font-weight:bold;">{label} ({value})</span></td>'
                    html += f'<td style="padding:8px 0; text-align:center;">'
                    if is_override:
                        html += f'<span style="background:#ffe0e0; padding:2px 5px; border-radius:3px;">User Override</span>'
                    else:
                        html += f'<span style="color:#999;">Using Default</span>'
                    html += '</td></tr>'
                
                html += '</table>'
                return format_html(html)
            else:
                return "Activity support information not available"
        except Exception as e:
            return f"Error generating summary: {str(e)}"
    
    get_domain_support_summary.short_description = "Domain Support Summary"
    
    def get_expiration_status(self, obj):
        """Display the expiration status of the environment"""
        import datetime
        today = datetime.date.today()
        
        if not obj.effective_end:
            return format_html('<span style="color:green;">Ongoing</span>')
        
        days_left = (obj.effective_end - today).days
        
        if days_left < 0:
            return format_html('<span style="color:red;">Expired</span>')
        elif days_left < 30:
            return format_html('<span style="color:orange;">Expiring soon ({} days)</span>', days_left)
        else:
            return format_html('<span style="color:green;">Active ({} days left)</span>', days_left)
    
    get_expiration_status.short_description = "Status"
    
    def save_model(self, request, obj, form, change):
        """Ensure proper relationships are maintained when saving"""
        # Save the model first
        super().save_model(request, obj, form, change)
        
        # If a generic environment is selected, ensure activity support exists
        if obj.generic_environment and not hasattr(obj, 'activity_support'):
            activity_support = UserEnvironmentActivitySupport.objects.create(
                user_environment=obj,
                digital_connectivity=50,  # Default values
                resource_availability=50,
                time_availability={"weekdays": ["evening"], "weekends": ["all_day"]},
                domain_specific_support={}
            )
            
            # Initialize domain_specific_support from generic environment
            if hasattr(activity_support, 'update_from_generic_environment'):
                activity_support.update_from_generic_environment()
        
        # If domains were selected, make sure they're properly associated with the environment
        if form.cleaned_data.get('domains'):
            obj.domains.set(form.cleaned_data['domains'])




# =========================================================
# Skill Admin
# ========================================================

from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Count, Avg, Sum

from apps.user.models import (
    SkillAttribute, AttributeTraitInfluence,
    SkillDefinition, SkillAttributeComposition, SkillDomainApplication,
    UserAttributeProficiency
)


class AttributeTraitInfluenceInline(admin.TabularInline):
    model = AttributeTraitInfluence
    extra = 1
    autocomplete_fields = ['generic_trait']
    classes = ['collapse']


# @admin.register(SkillAttribute) # Use admin_site.register instead
class SkillAttributeAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'code', 'development_difficulty', 'base_decay_rate',
        'development_timeframe', 'get_skill_count', 'get_trait_influences_display'
    )
    list_filter = ('development_timeframe', 'development_difficulty')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [AttributeTraitInfluenceInline]
    
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'description')
        }),
        ('Development Properties', {
            'fields': ('development_difficulty', 'development_timeframe', 'base_decay_rate'),
            'classes': ('collapse',),
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    def get_skill_count(self, obj):
        return obj.skill_usages.count()
    get_skill_count.short_description = 'Skills'
    
    def get_trait_influences_display(self, obj):
        influences = obj.trait_influences.all()
        if not influences:
            return '-'
        
        result = []
        for influence in influences:
            if influence.impact > 0:
                color = "green"
                symbol = "+"
            elif influence.impact < 0:
                color = "red"
                symbol = "-"
            else:
                color = "gray"
                symbol = "="
                
            result.append(
                f'<span style="color:{color};font-weight:bold;">{symbol}{abs(influence.impact)}</span> {influence.generic_trait.name}'
            )
        
        return format_html(", ".join(result))
    get_trait_influences_display.short_description = 'Trait Influences'


class SkillAttributeCompositionInline(admin.TabularInline):
    model = SkillAttributeComposition
    extra = 1
    autocomplete_fields = ['attribute']
    min_num = 1
    
    # Add custom column for visualization of weights
    readonly_fields = ['weight_visualization']
    
    def weight_visualization(self, obj):
        if not obj.pk:
            return '-'
        
        # Create a visual bar representation of the weight
        width_pct = min(100, obj.weight * 10)  # Scale: 1.0 = 10%, 10.0 = 100%
        bar_html = f"""
        <div style="background-color:#f0f0f0;width:100%;height:20px;position:relative;">
            <div style="position:absolute;left:0;top:0;bottom:0;width:{width_pct}%;background-color:#79aec8;"></div>
            <div style="position:absolute;left:0;top:0;bottom:0;width:100%;text-align:center;line-height:20px;">{obj.weight}</div>
        </div>
        """
        return format_html(bar_html)
    weight_visualization.short_description = "Weight"


class SkillDomainApplicationInline(admin.TabularInline):
    model = SkillDomainApplication
    extra = 1
    autocomplete_fields = ['domain']
    
    readonly_fields = ['relevance_visualization']
    
    def relevance_visualization(self, obj):
        if not obj.pk:
            return '-'
        
        # Visualize the relevance
        relevance = obj.relevance
        if relevance >= 70:
            color = 'green'
            label = 'Critical'
        elif relevance >= 30:
            color = 'blue'
            label = 'Important'
        else:
            color = 'gray'
            label = 'Supportive'
            
        return format_html(
            '<span style="color: {}; font-weight: bold;">{} ({})</span>',
            color, label, relevance
        )
    relevance_visualization.short_description = "Relevance"


class TagFilter(admin.SimpleListFilter):
    """
    Custom filter to filter skills by tag (handles ArrayField)
    """
    title = 'Tags'
    parameter_name = 'tag'
    
    def lookups(self, request, model_admin):
        # Get all unique tags across all skills
        all_tags = set()
        for skill in SkillDefinition.objects.all():
            if skill.tags:
                all_tags.update(skill.tags)
        
        # Return them as options for the filter
        return [(tag, tag) for tag in sorted(all_tags)]
    
    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        
        # Filter by the chosen tag (need to use __contains for ArrayField)
        # Note: This is PostgreSQL specific syntax
        return queryset.filter(tags__contains=[self.value()])

# @admin.register(SkillDefinition) # Use admin_site.register instead
class SkillDefinitionAdmin(admin.ModelAdmin):
    list_display = (
        'get_skill_name_with_complexity', 'code', 'get_attribute_count', 'get_domain_count',
        'get_primary_attributes', 'get_tags_display'
    )
    list_filter = (TagFilter, 'domain_applications__domain',)
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at', 'updated_at', 'get_attribute_composition_chart',
                      'get_domain_visualization', 'get_related_skills_visualization')
    # Removed filter_horizontal = ('tags',) as tags is an ArrayField, not a ManyToManyField
    inlines = [SkillAttributeCompositionInline, SkillDomainApplicationInline]
    
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'description')
        }),
        ('Classification', {
            'fields': ('tags',),  # Keep tags as a normal field
            'classes': ('collapse',),
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    # Add tabs or panels for the visualization data
    tabs = [
        ('Attribute Composition', {
            'fields': ('get_attribute_composition_chart',),
        }),
        ('Domain Applications', {
            'fields': ('get_domain_visualization',),
        }),
        ('Related Skills', {
            'fields': ('get_related_skills_visualization',),
        }),
    ]
    
    def get_fieldsets(self, request, obj=None):
        """
        Add visualization fields only on the change form, not the add form
        """
        fieldsets = super().get_fieldsets(request, obj)
        if obj:  # Only include visualization fields when editing an existing object
            for name, options in self.tabs:
                fieldsets = fieldsets + ((name, options),)
        return fieldsets
    
    def get_attribute_count(self, obj):
        """Return the number of attributes associated with this skill"""
        return obj.attribute_compositions.count()
    get_attribute_count.short_description = 'Attributes'
    
    def get_domain_count(self, obj):
        """Return the number of domains this skill applies to"""
        return obj.domain_applications.count()
    get_domain_count.short_description = 'Domains'
    
    def get_primary_attributes(self, obj):
        """Return formatted string of primary attributes"""
        top_attributes = obj.attribute_compositions.order_by('-weight')[:3]
        if not top_attributes:
            return '-'
        
        result = []
        for comp in top_attributes:
            result.append(f"{comp.attribute.name}")
        
        return format_html(", ".join(result))
    get_primary_attributes.short_description = 'Primary Attributes'
    
    def get_tags_display(self, obj):
        """Format tags for display in list view"""
        if not obj.tags:
            return '-'
        
        tags_html = []
        for tag in obj.tags[:3]:  # Show up to 3 tags
            tags_html.append(f'<span style="background:#f0f0f0;padding:2px 5px;border-radius:3px;">{tag}</span>')
        
        if len(obj.tags) > 3:
            tags_html.append(f'<span style="color:#999;">+{len(obj.tags) - 3} more</span>')
            
        return format_html(" ".join(tags_html))
    get_tags_display.short_description = 'Tags'

    # Add a formfield_for_dbfield method to customize the tags field display
    def formfield_for_dbfield(self, db_field, request=None, **kwargs):
        if db_field.name == 'tags':
            from django import forms
            help_text = db_field.help_text or ''
            help_text += ' (Enter tags as comma-separated values)'
            kwargs['help_text'] = help_text
            
            # If you're editing an existing object, format the tags for display
            if request and request.resolver_match and request.resolver_match.kwargs.get('object_id'):
                obj_id = request.resolver_match.kwargs.get('object_id')
                try:
                    obj = self.model.objects.get(pk=obj_id)
                    if obj.tags:  # Check if tags exist and are not None
                        kwargs['initial'] = ', '.join(obj.tags)
                except (self.model.DoesNotExist, ValueError):
                    pass
                    
            return forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), **kwargs)
        return super().formfield_for_dbfield(db_field, request, **kwargs)
    
    def save_model(self, request, obj, form, change):
        # Process the tags from the form
        if 'tags' in form.cleaned_data:
            if isinstance(form.cleaned_data['tags'], str):
                # Split by comma and strip whitespace
                tags = [tag.strip() for tag in form.cleaned_data['tags'].split(',') if tag.strip()]
                obj.tags = tags
            elif form.cleaned_data['tags'] is None:
                # Handle None value
                obj.tags = []
        super().save_model(request, obj, form, change)
    
    def get_attribute_composition_chart(self, obj):
        """
        Display a visual chart of attribute compositions and their weights
        """
        if not obj.pk:
            return "Save the skill first to see attribute visualization"
            
        compositions = obj.attribute_compositions.select_related('attribute').order_by('-weight')
        
        if not compositions.exists():
            return "No attributes associated with this skill yet"
            
        # Calculate total weight for percentage calculation
        total_weight = sum(comp.weight for comp in compositions)
        
        # Start building the visualization
        html = '<div style="width:100%; margin-top:10px;">'
        
        # Add a title
        html += '<h3>Attribute Composition</h3>'
        
        # Create a bar chart
        for comp in compositions:
            # Calculate percentage based on weight
            percentage = (comp.weight / total_weight) * 100
            # Determine color based on weight (higher weight = darker color)
            color_intensity = min(255, int(155 + (100 - percentage)))
            bar_color = f"rgb({color_intensity}, {color_intensity}, 255)"
            
            # Create the bar
            html += f'''
            <div style="margin-bottom:8px; width:100%;">
                <div style="display:flex; justify-content:space-between; margin-bottom:3px;">
                    <strong>{comp.attribute.name}</strong>
                    <span>Weight: {comp.weight:.1f} ({percentage:.1f}%)</span>
                </div>
                <div style="width:100%; background-color:#f0f0f0; height:20px; border-radius:3px; overflow:hidden;">
                    <div style="width:{percentage}%; background-color:{bar_color}; height:100%; border-radius:3px;"></div>
                </div>
                <div style="font-size:0.9em; color:#666; margin-top:2px;">{comp.attribute.description[:100]}...</div>
            </div>
            '''
        
        html += '</div>'
        return format_html(html)
    get_attribute_composition_chart.short_description = "Attribute Composition"
    
    def get_skill_name_with_complexity(self, obj):
        """
        Display a more informative skill name with complexity indicator
        """
        # Count attributes to determine complexity
        attribute_count = obj.attribute_compositions.count()
        
        # Determine complexity level based on attribute count
        if attribute_count >= 8:
            complexity = "High"
            color = "#e53935"  # Red
        elif attribute_count >= 5:
            complexity = "Medium"
            color = "#fb8c00"  # Orange
        elif attribute_count >= 2:
            complexity = "Low"
            color = "#43a047"  # Green
        else:
            complexity = "Basic"
            color = "#757575"  # Gray
            
        # Create the HTML for the display
        html = f'''
        <div>
            <strong>{obj.name}</strong>
            <div style="display:inline-block; margin-left:10px; padding:2px 5px; font-size:0.8em; 
                        background-color:{color}; color:white; border-radius:3px;">
                {complexity} Complexity
            </div>
        </div>
        '''
        
        return format_html(html)
    get_skill_name_with_complexity.short_description = "Skill"
    get_skill_name_with_complexity.admin_order_field = 'name'  # Enable sorting by name
    
    def get_domain_visualization(self, obj):
        """
        Display a visual representation of domain applications
        """
        if not obj.pk:
            return "Save the skill first to see domain visualization"
            
        applications = obj.domain_applications.select_related('domain').order_by('-relevance')
        
        if not applications.exists():
            return "No domains associated with this skill yet"
            
        # Start building the visualization
        html = '<div style="width:100%; margin-top:10px;">'
        
        # Add a title
        html += '<h3>Domain Applications</h3>'
        
        # Create a table for applications
        html += '''
        <table style="width:100%; border-collapse:collapse; margin-top:10px;">
            <thead>
                <tr style="background-color:#f5f5f5;">
                    <th style="padding:8px; text-align:left; border-bottom:1px solid #ddd;">Domain</th>
                    <th style="padding:8px; text-align:left; border-bottom:1px solid #ddd;">Relevance</th>
                    <th style="padding:8px; text-align:left; border-bottom:1px solid #ddd;">Transfer Rate</th>
                    <th style="padding:8px; text-align:left; border-bottom:1px solid #ddd;">Category</th>
                </tr>
            </thead>
            <tbody>
        '''
        
        for app in applications:
            # Determine color based on relevance
            if app.relevance >= 70:
                color = '#4CAF50'  # Green for critical
                relevance_text = 'Critical'
            elif app.relevance >= 30:
                color = '#2196F3'  # Blue for important
                relevance_text = 'Important'
            else:
                color = '#9E9E9E'  # Gray for supportive
                relevance_text = 'Supportive'
                
            transfer_text = f"{app.transfer_coefficient:.1f}x"
            category = app.domain.primary_category or 'Uncategorized'
                
            html += f'''
            <tr style="border-bottom:1px solid #eee;">
                <td style="padding:8px;">{app.domain.name}</td>
                <td style="padding:8px;">
                    <span style="display:inline-block; width:15px; height:15px; background-color:{color}; border-radius:50%; margin-right:5px;"></span>
                    {relevance_text} ({app.relevance})
                </td>
                <td style="padding:8px;">{transfer_text}</td>
                <td style="padding:8px;">{category}</td>
            </tr>
            '''
        
        html += '''
            </tbody>
        </table>
        '''
        
        html += '</div>'
        return format_html(html)
    get_domain_visualization.short_description = "Domain Applications"
    
    def get_related_skills_visualization(self, obj):
        """
        Display related skills (prerequisites and enables relationships)
        """
        if not obj.pk:
            return "Save the skill first to see related skills"
            
        # Get prerequisites
        prerequisites = obj.prerequisites.all().select_related()
        
        # Get skills that this skill enables
        enables = obj.enables.all().select_related()
        
        # Get parent skill if applicable
        parent = obj.parent_skill
        
        # Get sub-skills
        sub_skills = obj.sub_skills.all().select_related()
        
        # Start building the visualization
        html = '<div style="width:100%; margin-top:10px;">'
        
        # Parent skill
        if parent:
            html += f'''
            <div style="margin-bottom:20px;">
                <h3>Parent Skill</h3>
                <div style="border:1px solid #ddd; padding:10px; border-radius:4px; background-color:#f8f8f8;">
                    <strong>{parent.name}</strong> ({parent.code})
                    <div style="margin-top:5px; color:#666;">{parent.description[:150]}...</div>
                </div>
            </div>
            '''
        
        # Display prerequisites in a card layout
        if prerequisites.exists():
            html += '<h3>Prerequisites</h3>'
            html += '<div style="display:flex; flex-wrap:wrap; gap:15px; margin-bottom:20px;">'
            
            for prereq in prerequisites:
                html += f'''
                <div style="flex:1; min-width:250px; border:1px solid #ddd; padding:10px; border-radius:4px; background-color:#f5f5f5;">
                    <div style="margin-bottom:5px;">
                        <strong>{prereq.name}</strong> ({prereq.code})
                    </div>
                    <div style="font-size:0.9em; color:#666;">{prereq.description[:100]}...</div>
                </div>
                '''
                
            html += '</div>'
        else:
            html += '<h3>Prerequisites</h3><p>No prerequisites defined for this skill</p>'
        
        # Display enabled skills in a card layout
        if enables.exists():
            html += '<h3>Enables</h3>'
            html += '<div style="display:flex; flex-wrap:wrap; gap:15px; margin-bottom:20px;">'
            
            for enabled in enables:
                html += f'''
                <div style="flex:1; min-width:250px; border:1px solid #ddd; padding:10px; border-radius:4px; background-color:#f5f5f5;">
                    <div style="margin-bottom:5px;">
                        <strong>{enabled.name}</strong> ({enabled.code})
                    </div>
                    <div style="font-size:0.9em; color:#666;">{enabled.description[:100]}...</div>
                </div>
                '''
                
            html += '</div>'
        else:
            html += '<h3>Enables</h3><p>This skill does not enable any other skills</p>'
        
        # Display sub-skills
        if sub_skills.exists():
            html += '<h3>Sub-Skills</h3>'
            html += '<div style="display:flex; flex-wrap:wrap; gap:15px;">'
            
            for sub in sub_skills:
                html += f'''
                <div style="flex:1; min-width:250px; border:1px solid #ddd; padding:10px; border-radius:4px; background-color:#f5f5f5;">
                    <div style="margin-bottom:5px;">
                        <strong>{sub.name}</strong> ({sub.code})
                    </div>
                    <div style="font-size:0.9em; color:#666;">{sub.description[:100]}...</div>
                </div>
                '''
                
            html += '</div>'
        else:
            html += '<h3>Sub-Skills</h3><p>This skill does not have any sub-skills</p>'
            
        html += '</div>'
        return format_html(html)
    get_related_skills_visualization.short_description = "Related Skills"


# @admin.register(AttributeTraitInfluence) # Use admin_site.register instead
class AttributeTraitInfluenceAdmin(admin.ModelAdmin):
    list_display = ('attribute', 'generic_trait', 'impact', 'get_impact_display_visual')
    list_filter = ('impact', 'generic_trait__trait_type')
    search_fields = ('attribute__name', 'generic_trait__name', 'notes')
    autocomplete_fields = ['attribute', 'generic_trait']
    
    def get_impact_display_visual(self, obj):
        impact = obj.impact
        
        # Determine color based on impact
        if impact > 1:
            color = 'green'
        elif impact > 0:
            color = 'lightgreen'
        elif impact < -1:
            color = 'red'
        elif impact < 0:
            color = 'salmon'
        else:
            color = 'gray'
            
        # Create visual representation of impact
        bar_width = abs(impact) * 20  # Scale: impact of 3 = 60px
        direction = 'right' if impact >= 0 else 'left'
        
        bar_html = f"""
        <div style="width:100%; background-color:#f0f0f0; height:20px; position:relative;">
        <div style="position:absolute; left:50%; top:0; bottom:0; width:1px; background-color:#999;"></div>
        <div style="position:absolute; {direction}:50%; width:{bar_width}px; background-color:{color}; height:100%;">
        <span style="padding:0 5px; line-height:20px; color:{'white' if abs(impact) > 1 else 'black'};">{impact}</span>
        </div></div>
        """
        return format_html(bar_html)
    get_impact_display_visual.short_description = 'Impact'


# @admin.register(SkillDomainApplication) # Use admin_site.register instead
class SkillDomainApplicationAdmin(admin.ModelAdmin):
    list_display = ('skill', 'domain', 'relevance', 'transfer_coefficient', 'get_properties_summary')
    list_filter = ('relevance', 'domain')
    search_fields = ('skill__name', 'domain__name')
    autocomplete_fields = ['skill', 'domain']
    
    fieldsets = (
        (None, {
            'fields': ('skill', 'domain', 'relevance', 'transfer_coefficient')
        }),
        ('Advanced Properties', {
            'fields': ('domain_specific_properties',),
            'classes': ('collapse',),
            'description': 'JSON field for domain-specific properties like decay rates, context modifiers, etc.'
        }),
    )
    
    def get_properties_summary(self, obj):
        if not obj.domain_specific_properties:
            return '-'
            
        # Format key properties for display
        properties = []
        for key, value in obj.domain_specific_properties.items():
            if key == 'decay_rate':
                properties.append(f"Decay: {value}")
            elif key == 'attribute_modifiers':
                properties.append(f"Attr Mods: {len(value)}")
            else:
                properties.append(f"{key}: {value}")
                
        return ', '.join(properties[:3])
    get_properties_summary.short_description = 'Properties'


# @admin.register(UserAttributeProficiency) # Use admin_site.register instead
class UserAttributeProficiencyAdmin(admin.ModelAdmin):
    list_display = (
        'user_profile', 'attribute', 'level', 'get_effective_level',
        'last_practiced', 'practice_frequency'
    )
    list_filter = ('practice_frequency', 'formal_training', 'attribute')
    search_fields = ('user_profile__profile_name', 'attribute__name', 'notes')
    autocomplete_fields = ['user_profile', 'attribute']
    readonly_fields = ('created_at', 'updated_at', 'get_effective_level')
    
    fieldsets = (
        (None, {
            'fields': ('user_profile', 'attribute', 'level', 'user_awareness')
        }),
        ('Practice Information', {
            'fields': ('last_practiced', 'practice_frequency', 'formal_training'),
        }),
        ('Additional Information', {
            'fields': ('notes', 'get_effective_level'),
            'classes': ('collapse',),
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    def get_effective_level(self, obj):
        from apps.user.services.skill_service import SkillService
        
        if not obj.pk:
            return '-'
            
        effective = SkillService.calculate_effective_attribute_level(obj)
        
        # Color coding based on decay
        if effective >= obj.level * 0.9:
            color = 'green'
            status = 'Current'
        elif effective >= obj.level * 0.7:
            color = 'orange'
            status = 'Decaying'
        else:
            color = 'red'
            status = 'Rusty'
            
        return format_html(
            '<span style="color:{};">{:.1f} ({})</span>',
            color, effective, status
        )
    get_effective_level.short_description = 'Effective Level'

# Add these to backend/apps/user/admin.py

class GenericBeliefDomainRelationshipInline(admin.TabularInline):
    model = GenericBeliefDomainRelationship
    extra = 1
    autocomplete_fields = ['domain']
    
    readonly_fields = ['impact_visualization']
    
    def impact_visualization(self, obj):
        if not obj.pk:
            return '-'
        
        # Visualize the impact
        impact = obj.impact
        if impact > 2:
            color = 'green'
            label = 'Strongly Supportive'
        elif impact > 0:
            color = 'lightgreen'
            label = 'Supportive'
        elif impact < -2:
            color = 'red'
            label = 'Strongly Limiting'
        elif impact < 0:
            color = 'salmon'
            label = 'Limiting'
        else:
            color = 'gray'
            label = 'Neutral'
            
        return format_html(
            '<span style="color: {}; font-weight: bold;">{} ({})</span>',
            color, label, impact
        )
    impact_visualization.short_description = "Impact"


# @admin.register(GenericBelief) # Use admin_site.register instead
class GenericBeliefAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'category', 'typical_stability', 'get_domains_count')
    list_filter = ('category', 'typical_stability', 'domains')
    search_fields = ('name', 'code', 'description')
    inlines = [GenericBeliefDomainRelationshipInline]
    
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'category', 'description')
        }),
        ('Properties', {
            'fields': ('typical_stability',),
            'classes': ('collapse',),
        }),
    )
    
    def get_domains_count(self, obj):
        return obj.domains.count()
    get_domains_count.short_description = 'Domain Relationships'


# @admin.register(GenericBeliefDomainRelationship) # Use admin_site.register instead
class GenericBeliefDomainRelationshipAdmin(admin.ModelAdmin):
    list_display = ('generic_belief', 'domain', 'impact', 'get_impact_display_visual')
    list_filter = ('impact', 'domain__primary_category')
    search_fields = ('generic_belief__name', 'domain__name', 'notes')
    
    def get_impact_display_visual(self, obj):
        impact = obj.impact
        
        # Determine color based on impact
        if impact > 2:
            color = 'green'
        elif impact > 0:
            color = 'lightgreen'
        elif impact < -2:
            color = 'red'
        elif impact < 0:
            color = 'salmon'
        else:
            color = 'gray'
            
        # Create visual representation of impact
        bar_width = abs(impact) * 20  # Scale: impact of 3 = 60px
        direction = 'right' if impact >= 0 else 'left'
        
        bar_html = f"""
        <div style="width:100%; background-color:#f0f0f0; height:20px; position:relative;">
        <div style="position:absolute; left:50%; top:0; bottom:0; width:1px; background-color:#999;"></div>
        <div style="position:absolute; {direction}:50%; width:{bar_width}px; background-color:{color}; height:100%;">
        <span style="padding:0 5px; line-height:20px; color:{'white' if abs(impact) > 1 else 'black'};">{impact}</span>
        </div></div>
        """
        return format_html(bar_html)
    get_impact_display_visual.short_description = 'Impact'


# Update the existing BeliefAdmin class to include generic_belief field
# Find class that registers the Belief model and update it:

# Inside the BeliefAdmin class (if it exists)
# Add 'generic_belief' to list_display, raw_id_fields or autocomplete_fields

# If BeliefAdmin doesn't exist, create it:
# @admin.register(Belief) # Use admin_site.register instead
class BeliefAdmin(admin.ModelAdmin):
    list_display = ('content', 'user_profile', 'stability', 'emotionality', 'user_confidence', 'generic_belief')
    list_filter = ('stability', 'user_awareness', 'generic_belief__category')
    search_fields = ('content', 'user_profile__profile_name')
    raw_id_fields = ('user_profile',)
    autocomplete_fields = ('generic_belief',)
    
    fieldsets = (
        (None, {
            'fields': ('user_profile', 'content', 'generic_belief', 'last_updated')
        }),
        ('Attributes', {
            'fields': ('user_confidence', 'system_confidence', 'emotionality', 'stability', 'user_awareness'),
        }),
    )


# =========================================================
# Explicit Registrations with Custom Admin Site
# =========================================================
admin_site.register(GenericTrait, GenericTraitAdmin)
admin_site.register(GenericEnvironment, GenericEnvironmentAdmin)
admin_site.register(GenericEnvironmentDomainRelationship, GenericEnvironmentDomainRelationshipAdmin)

# Re-add the inline for UserProfile during registration
UserProfileAdmin.inlines = [DemographicsInline]
admin_site.register(UserProfile, UserProfileAdmin)

admin_site.register(UserEnvironment, UserEnvironmentAdmin)
admin_site.register(SkillAttribute, SkillAttributeAdmin)
admin_site.register(SkillDefinition, SkillDefinitionAdmin)
admin_site.register(AttributeTraitInfluence, AttributeTraitInfluenceAdmin)
admin_site.register(SkillDomainApplication, SkillDomainApplicationAdmin)
admin_site.register(UserAttributeProficiency, UserAttributeProficiencyAdmin)
admin_site.register(GenericBelief, GenericBeliefAdmin)
admin_site.register(GenericBeliefDomainRelationship, GenericBeliefDomainRelationshipAdmin)
admin_site.register(Belief, BeliefAdmin)
admin_site.register(SkillAttributeComposition) # Register the missing model

# Register the standard User model with the custom admin site
# We can use the standard UserAdmin or customize it if needed
admin_site.register(User, BaseUserAdmin)

# Note: Demographics is handled via inline, no separate registration needed unless standalone access is required.
# Similarly, other models used only in inlines (like UserEnvironmentPhysicalProperties) don't need separate registration here unless linked directly.
