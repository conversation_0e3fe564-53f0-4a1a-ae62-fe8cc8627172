from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from apps.user.api.views import (
    SkillAttributeViewSet,
    SkillDefinitionViewSet,
    UserAttributeProficiencyViewSet,
    ActivitySkillAnalysisViewSet
)

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'skill-attributes', SkillAttributeViewSet, basename='skill-attribute')
router.register(r'skill-definitions', SkillDefinitionViewSet, basename='skill-definition')
router.register(r'user-proficiencies', UserAttributeProficiencyViewSet, basename='user-proficiency')
router.register(r'activity-skills', ActivitySkillAnalysisViewSet, basename='activity-skill')

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
]