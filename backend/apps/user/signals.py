from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import UserGoal, UserTraitInclination, Belief

@receiver(post_save, sender=UserGoal)
def log_goal_changes(sender, instance, created, **kwargs):
    """
    Automatically log goal creation and updates to the HistoryEvent model.
    """
    from apps.main.models import HistoryEvent
    from django.contrib.contenttypes.models import ContentType
    
    content_type = ContentType.objects.get_for_model(instance)
    
    HistoryEvent.objects.create(
        event_type='goal_created' if created else 'goal_updated',
        content_type=content_type,
        object_id=str(instance.id),  # Convert to string
        user_profile=instance.user_profile,
        details={
            'title': instance.title,
            'importance': instance.importance_according_user,
            'goal_type': instance.__class__.__name__
        }
    )

@receiver(post_save, sender=UserTraitInclination)
def log_trait_changes(sender, instance, created, **kwargs):
    """
    Log trait inclination creation and updates.
    """
    from apps.main.models import HistoryEvent
    from django.contrib.contenttypes.models import ContentType
    
    content_type = ContentType.objects.get_for_model(instance)
    
    HistoryEvent.objects.create(
        event_type='trait_inclination_created' if created else 'trait_inclination_updated',
        content_type=content_type,
        object_id=str(instance.id),  # Convert to string
        user_profile=instance.user_profile,
        details={
            'trait': instance.generic_trait.name,
            'strength': instance.strength
        }
    )

@receiver(post_save, sender=Belief)
def log_belief_changes(sender, instance, created, **kwargs):
    """
    Log belief creation and updates.
    """
    from apps.main.models import HistoryEvent
    from django.contrib.contenttypes.models import ContentType
    
    content_type = ContentType.objects.get_for_model(instance)
    
    HistoryEvent.objects.create(
        event_type='belief_created' if created else 'belief_updated',
        content_type=content_type,
        object_id=str(instance.id),  # Convert to string
        user_profile=instance.user_profile,
        details={
            'content': instance.content,
            'user_confidence': instance.user_confidence,
            'emotionality': instance.emotionality
        }
    )