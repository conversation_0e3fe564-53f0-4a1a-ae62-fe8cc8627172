from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q, F, Count, Avg
from django.shortcuts import get_object_or_404
from django.utils.dateparse import parse_date

from apps.user.models import (
    SkillAttribute, SkillDefinition, SkillAttributeComposition,
    SkillDomainApplication, UserAttributeProficiency
)
from apps.user.services.skill_service import SkillService
from apps.utils.activity_skill_integration import ActivitySkillIntegration
from apps.activity.models import GenericActivity, ActivityTailored


class SkillAttributeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for skill attributes.
    
    Attributes are fundamental building blocks that combine to form skills.
    This viewset provides read-only access to the attribute catalog.
    """
    queryset = SkillAttribute.objects.all().order_by('name')
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        # This would be implemented with DRF serializers
        pass
    
    def list(self, request):
        """List all skill attributes with optional filtering."""
        queryset = self.filter_queryset(self.get_queryset())
        
        # Apply additional filters if specified
        development_timeframe = request.query_params.get('timeframe')
        if development_timeframe:
            queryset = queryset.filter(development_timeframe=development_timeframe)
            
        difficulty_min = request.query_params.get('difficulty_min')
        difficulty_max = request.query_params.get('difficulty_max')
        if difficulty_min:
            queryset = queryset.filter(development_difficulty__gte=int(difficulty_min))
        if difficulty_max:
            queryset = queryset.filter(development_difficulty__lte=int(difficulty_max))
        
        # Return formatted response
        # In a real implementation, you'd use a serializer
        result = []
        for attr in queryset:
            result.append({
                'id': attr.id,
                'code': attr.code,
                'name': attr.name,
                'description': attr.description,
                'base_decay_rate': attr.base_decay_rate,
                'development_difficulty': attr.development_difficulty,
                'development_timeframe': attr.development_timeframe,
            })
        
        return Response(result)
    
    def retrieve(self, request, pk=None):
        """Get detailed information about a specific attribute."""
        attribute = self.get_object()
        
        # Get trait influences
        trait_influences = []
        for influence in attribute.trait_influences.select_related('generic_trait').all():
            trait_influences.append({
                'trait_id': influence.generic_trait.id,
                'trait_name': influence.generic_trait.name,
                'trait_type': influence.generic_trait.trait_type,
                'impact': influence.impact,
                'impact_display': influence.get_impact_display(),
            })
        
        # Get skills using this attribute
        skills_using = []
        for comp in attribute.skill_usages.select_related('skill').all():
            skills_using.append({
                'skill_id': comp.skill.id,
                'skill_name': comp.skill.name,
                'weight': comp.weight
            })
        
        # Return detailed response
        result = {
            'id': attribute.id,
            'code': attribute.code,
            'name': attribute.name,
            'description': attribute.description,
            'base_decay_rate': attribute.base_decay_rate,
            'development_difficulty': attribute.development_difficulty,
            'development_timeframe': attribute.development_timeframe,
            'created_at': attribute.created_at,
            'updated_at': attribute.updated_at,
            'trait_influences': trait_influences,
            'skills_using': skills_using
        }
        
        return Response(result)
    
    @action(detail=True, methods=['get'])
    def user_proficiency(self, request, pk=None):
        """Get the current user's proficiency in this attribute."""
        attribute = self.get_object()
        user_profile = request.user.profiles.first()  # Assuming the first profile
        
        if not user_profile:
            return Response(
                {"error": "No user profile found"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get proficiency if it exists
        proficiency = UserAttributeProficiency.objects.filter(
            user_profile=user_profile,
            attribute=attribute
        ).first()
        
        if not proficiency:
            return Response({
                'attribute_id': attribute.id,
                'attribute_name': attribute.name,
                'has_proficiency': False
            })
        
        # Calculate effective level
        effective_level = SkillService.calculate_effective_attribute_level(proficiency)
        
        # Return proficiency details
        result = {
            'attribute_id': attribute.id,
            'attribute_name': attribute.name,
            'has_proficiency': True,
            'level': proficiency.level,
            'effective_level': effective_level,
            'last_practiced': proficiency.last_practiced,
            'practice_frequency': proficiency.practice_frequency,
            'user_awareness': proficiency.user_awareness,
            'formal_training': proficiency.formal_training,
        }
        
        return Response(result)


class SkillDefinitionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for skill definitions.
    
    Skills are composed of attributes and can be applied in various domains.
    This viewset provides read-only access to the skill catalog.
    """
    queryset = SkillDefinition.objects.all().order_by('name')
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        # This would be implemented with DRF serializers
        pass
    
    def list(self, request):
        """List all skills with optional filtering."""
        queryset = self.get_queryset()
        
        # Apply tag filtering if specified
        tags = request.query_params.getlist('tag')
        if tags:
            # Filter for skills that have ALL the specified tags
            for tag in tags:
                queryset = queryset.filter(tags__contains=[tag])
        
        # Return formatted response
        result = []
        for skill in queryset:
            # Get primary attributes
            primary_attributes = skill.attribute_compositions.order_by('-weight')[:3]
            attributes = [
                {'id': comp.attribute.id, 'name': comp.attribute.name, 'weight': comp.weight}
                for comp in primary_attributes
            ]
            
            result.append({
                'id': skill.id,
                'code': skill.code,
                'name': skill.name,
                'description': skill.description,
                'tags': skill.tags,
                'primary_attributes': attributes,
                'domain_count': skill.domain_applications.count(),
            })
        
        return Response(result)
    
    def retrieve(self, request, pk=None):
        """Get detailed information about a specific skill."""
        skill = self.get_object()
        
        # Get attribute compositions
        compositions = []
        for comp in skill.attribute_compositions.select_related('attribute').all():
            compositions.append({
                'attribute_id': comp.attribute.id,
                'attribute_name': comp.attribute.name,
                'attribute_code': comp.attribute.code,
                'weight': comp.weight,
            })
        
        # Get domain applications
        applications = []
        for app in skill.domain_applications.select_related('domain').all():
            applications.append({
                'domain_id': app.domain.id,
                'domain_name': app.domain.name,
                'domain_code': app.domain.code,
                'relevance': app.relevance,
                'relevance_display': app.get_relevance_display(),
                'transfer_coefficient': app.transfer_coefficient,
            })
        
        # Return detailed response
        result = {
            'id': skill.id,
            'code': skill.code,
            'name': skill.name,
            'description': skill.description,
            'tags': skill.tags,
            'created_at': skill.created_at,
            'updated_at': skill.updated_at,
            'attribute_compositions': compositions,
            'domain_applications': applications,
        }
        
        return Response(result)
    
    @action(detail=True, methods=['get'])
    def user_level(self, request, pk=None):
        """Get the current user's level in this skill."""
        skill = self.get_object()
        user_profile = request.user.profiles.first()  # Assuming the first profile
        
        if not user_profile:
            return Response(
                {"error": "No user profile found"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get optional domain context
        domain_id = request.query_params.get('domain_id')
        domain = None
        if domain_id:
            from apps.activity.models import GenericDomain # Renamed import
            try:
                domain = GenericDomain.objects.get(id=domain_id) # Use renamed model
            except GenericDomain.DoesNotExist: # Use renamed model
                pass
        
        # Calculate user's skill level
        skill_result = SkillService.get_user_skill_level(user_profile, skill, domain)
        
        # Return skill level details
        result = {
            'skill_id': skill.id,
            'skill_name': skill.name,
            'domain_id': domain.id if domain else None,
            'domain_name': domain.name if domain else None,
            'level': skill_result['level'],
            'details': skill_result['details'],
            'missing_attributes': skill_result['missing_attributes'],
        }
        
        return Response(result)


class UserAttributeProficiencyViewSet(viewsets.ModelViewSet):
    """
    API endpoint for user attribute proficiencies.
    
    This viewset provides CRUD operations for managing a user's proficiency
    in specific attributes. It supports updating proficiency levels, recording
    practice sessions, and tracking attribute development over time.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        user_profile = self.request.user.profiles.first()
        if not user_profile:
            return UserAttributeProficiency.objects.none()
            
        return UserAttributeProficiency.objects.filter(
            user_profile=user_profile
        ).select_related('attribute')
    
    def get_serializer_class(self):
        # This would be implemented with DRF serializers
        pass
    
    def list(self, request):
        """List all proficiencies for the current user."""
        queryset = self.get_queryset()
        
        # Apply filters if specified
        min_level = request.query_params.get('min_level')
        if min_level:
            queryset = queryset.filter(level__gte=int(min_level))
            
        # Return formatted response
        result = []
        for prof in queryset:
            # Calculate effective level
            effective_level = SkillService.calculate_effective_attribute_level(prof)
            
            result.append({
                'id': prof.id,
                'attribute_id': prof.attribute.id,
                'attribute_name': prof.attribute.name,
                'attribute_code': prof.attribute.code,
                'level': prof.level,
                'effective_level': effective_level,
                'last_practiced': prof.last_practiced,
                'practice_frequency': prof.practice_frequency,
            })
        
        return Response(result)
    
    def retrieve(self, request, pk=None):
        """Get detailed information about a specific proficiency."""
        proficiency = self.get_object()
        
        # Calculate effective level
        effective_level = SkillService.calculate_effective_attribute_level(proficiency)
        
        # Return detailed response
        result = {
            'id': proficiency.id,
            'attribute_id': proficiency.attribute.id,
            'attribute_name': proficiency.attribute.name,
            'attribute_code': proficiency.attribute.code,
            'level': proficiency.level,
            'effective_level': effective_level,
            'last_practiced': proficiency.last_practiced,
            'practice_frequency': proficiency.practice_frequency,
            'user_awareness': proficiency.user_awareness,
            'formal_training': proficiency.formal_training,
            'notes': proficiency.notes,
            'created_at': proficiency.created_at,
            'updated_at': proficiency.updated_at,
        }
        
        return Response(result)
    
    @action(detail=False, methods=['post'])
    def record_practice(self, request):
        """Record practice of attributes."""
        user_profile = request.user.profiles.first()
        if not user_profile:
            return Response(
                {"error": "No user profile found"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get attribute IDs from request
        attribute_ids = request.data.get('attribute_ids', [])
        if not attribute_ids:
            return Response(
                {"error": "No attributes specified"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get optional context
        context = request.data.get('context', {})
        
        # Record usage
        result = SkillService.record_attribute_usage(user_profile, attribute_ids, context)
        
        return Response(result)
    
    @action(detail=True, methods=['post'])
    def update_level(self, request, pk=None):
        """
        Update the proficiency level of an attribute.
        
        This endpoint is used to manually adjust a user's proficiency level,
        typically after assessment or training.
        """
        proficiency = self.get_object()
        
        # Get new level from request
        new_level = request.data.get('level')
        if new_level is None:
            return Response(
                {"error": "No level specified"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            new_level = int(new_level)
            if new_level < 0 or new_level > 100:
                raise ValueError("Level must be between 0 and 100")
        except ValueError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update proficiency
        proficiency.level = new_level
        proficiency.save(update_fields=['level'])
        
        # Return updated proficiency
        return Response({
            'id': proficiency.id,
            'attribute_id': proficiency.attribute.id,
            'attribute_name': proficiency.attribute.name,
            'level': proficiency.level,
            'updated_at': proficiency.updated_at,
        })


class ActivitySkillAnalysisViewSet(viewsets.ViewSet):
    """
    API endpoint for analyzing skills related to activities.
    
    This viewset provides endpoints for querying skill requirements
    and user skill matches for specific activities.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def activity_requirements(self, request):
        """Get skill requirements for a specific activity."""
        activity_id = request.query_params.get('activity_id')
        activity_type = request.query_params.get('activity_type', 'generic')
        
        if not activity_id:
            return Response(
                {"error": "No activity specified"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the activity
        try:
            if activity_type == 'tailored':
                activity = ActivityTailored.objects.get(id=activity_id)
            else:
                activity = GenericActivity.objects.get(id=activity_id)
        except (GenericActivity.DoesNotExist, ActivityTailored.DoesNotExist):
            return Response(
                {"error": "Activity not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get optional challenge level
        challenge_level = request.query_params.get('challenge_level')
        if challenge_level:
            try:
                challenge_level = int(challenge_level)
            except ValueError:
                challenge_level = None
        
        # Calculate requirements
        requirements = ActivitySkillIntegration.calculate_activity_skill_requirements(
            activity, challenge_level
        )
        
        return Response(requirements)
    
    @action(detail=False, methods=['get'])
    def user_skill_match(self, request):
        """Analyze a user's skill match for a specific activity."""
        activity_id = request.query_params.get('activity_id')
        activity_type = request.query_params.get('activity_type', 'generic')
        
        if not activity_id:
            return Response(
                {"error": "No activity specified"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the activity
        try:
            if activity_type == 'tailored':
                activity = ActivityTailored.objects.get(id=activity_id)
            else:
                activity = GenericActivity.objects.get(id=activity_id)
        except (GenericActivity.DoesNotExist, ActivityTailored.DoesNotExist):
            return Response(
                {"error": "Activity not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get the user profile
        user_profile = request.user.profiles.first()
        if not user_profile:
            return Response(
                {"error": "No user profile found"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Analyze skill match
        match_analysis = ActivitySkillIntegration.analyze_activity_skill_match(
            activity, user_profile
        )
        
        return Response(match_analysis)
    
    @action(detail=False, methods=['post'])
    def record_activity_completion(self, request):
        """Record attribute usage for a completed activity."""
        activity_id = request.data.get('activity_id')
        activity_type = request.data.get('activity_type', 'generic')
        
        if not activity_id:
            return Response(
                {"error": "No activity specified"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the activity
        try:
            if activity_type == 'tailored':
                activity = ActivityTailored.objects.get(id=activity_id)
            else:
                activity = GenericActivity.objects.get(id=activity_id)
        except (GenericActivity.DoesNotExist, ActivityTailored.DoesNotExist):
            return Response(
                {"error": "Activity not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get the user profile
        user_profile = request.user.profiles.first()
        if not user_profile:
            return Response(
                {"error": "No user profile found"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Record attribute usage
        result = ActivitySkillIntegration.record_activity_attribute_usage(
            activity, user_profile
        )
        
        return Response(result)
