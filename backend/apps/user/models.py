from enum import Enum
from django.db import models
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey, GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
import slugify
from apps.common.fields import *
from djmoney.models.fields import MoneyField

# ==================================
# Helper classes
# ==================================

from django.utils.html import format_html

class EnvironmentDomainSupportMixin:
    """
    Mixin that can be added to GenericEnvironment or UserEnvironment models
    to provide consistent domain support display methods.
    """
    
    def get_domain_support_rating(self, domain_code=None, domain_id=None):
        """
        Get the support rating for a specific domain.
        
        Args:
            domain_code: The code of the domain (e.g., 'physical')
            domain_id: The ID of the domain
            
        Returns:
            int: Support rating from 0 to 100
        """
        # For UserEnvironment
        if hasattr(self, 'generic_environment') and hasattr(self, 'activity_support'):
            # Try user-specific override first
            if hasattr(self.activity_support, 'domain_specific_support'):
                if domain_code and domain_code in self.activity_support.domain_specific_support:
                    return self.activity_support.domain_specific_support[domain_code]
            
            # Fall back to generic environment
            if self.generic_environment:
                return self.generic_environment.get_domain_support_rating(domain_code, domain_id)
            return 0
            
        # For GenericEnvironment
        if domain_id:
            try:
                rel = self.domain_relationships_details.get(domain_id=domain_id)
                return rel.strength
            except self.domain_relationships_details.model.DoesNotExist:
                return 0
                
        if domain_code:
            try:
                # This requires an extra query but is more flexible
                from apps.activity.models import GenericDomain # Renamed import
                domain = GenericDomain.objects.get(code=domain_code) # Use renamed model
                return self.get_domain_support_rating(domain_id=domain.id)
            except GenericDomain.DoesNotExist: # Use renamed model
                return 0
                
        return 0
    
    def get_domain_support_display(self, domain_code=None, domain_id=None):
        """
        Get a human-readable display of the support rating for a specific domain.
        
        Args:
            domain_code: The code of the domain (e.g., 'physical')
            domain_id: The ID of the domain
            
        Returns:
            str: HTML-formatted support rating display
        """
        rating = self.get_domain_support_rating(domain_code, domain_id)
        
        if rating >= 80:
            color = 'green'
            level = 'Excellent'
        elif rating >= 60:
            color = 'seagreen'
            level = 'Good'
        elif rating >= 40:
            color = 'limegreen'
            level = 'Moderate'
        elif rating >= 20:
            color = 'forestgreen'
            level = 'Limited'
        elif rating >= 0:
            color = 'darkgreen'
            level = 'Minimal'
        elif rating >= -20:
            color = 'darkred'
            level = 'Slightly Discouraged'
        elif rating >= -40:
            color = 'firebrick'
            level = 'Moderately Discouraged'
        elif rating >= -60:
            color = 'crimson'
            level = 'Strongly Discouraged'
        else:
            color = 'red'
            level = 'Extremely Discouraged'
            
        return format_html(
            '<span style="color: {}; font-weight: bold;">{} ({})</span>',
            color, level, rating
        )
    
    def get_domain_support_summary(self):
        """
        Get a summary of domain support for all associated domains.
        
        Returns:
            dict: Domain code to support rating mapping
        """
        # For UserEnvironment
        if hasattr(self, 'generic_environment') and self.generic_environment:
            # Get the generic environment's domains
            gen_env = self.generic_environment
            domain_rels = gen_env.domain_relationships_details.all().select_related('domain')
            
            summary = {}
            for rel in domain_rels:
                domain_code = rel.domain.code
                # Start with generic environment rating
                rating = rel.strength
                
                # Apply user-specific override if available
                if (hasattr(self, 'activity_support') and 
                    hasattr(self.activity_support, 'domain_specific_support') and
                    domain_code in self.activity_support.domain_specific_support):
                    rating = self.activity_support.domain_specific_support[domain_code]
                
                summary[domain_code] = {
                    'name': rel.domain.name,
                    'rating': rating,
                    'category': rel.domain.primary_category,
                    'is_override': (hasattr(self, 'activity_support') and 
                                   hasattr(self.activity_support, 'domain_specific_support') and
                                   domain_code in self.activity_support.domain_specific_support)
                }
            
            return summary
            
        # For GenericEnvironment
        summary = {}
        domain_rels = self.domain_relationships_details.all().select_related('domain')
        
        for rel in domain_rels:
            domain_code = rel.domain.code
            summary[domain_code] = {
                'name': rel.domain.name,
                'rating': rel.strength,
                'category': rel.domain.primary_category
            }
            
        return summary

###############################################################################
# Abstract Base Class
###############################################################################

class TemporalRecord(models.Model):
    """
    Abstract model providing a common structure for time-bound entities.

    A model that tracks time-bound records with start and end dates, along with
    an estimate for the duration of the active period.

    Attributes:
        effective_start(DateField): 
            Example: "2025-03-12"  # The start date when a transformative activity begins.
        duration_estimate(CharField): 
            Example: "30 days"  # For example, an estimated period for a challenge.
        effective_end(DateField): 
            Example: "2025-04-11"  # The end date when the activity or challenge concludes.
    """
    effective_start = models.DateField(help_text="Start date when the record becomes active.")
    duration_estimate = models.CharField(max_length=50, help_text="Estimated active period (e.g., '30 days').")
    effective_end = models.DateField(help_text="End date when the record ceases to be active.")

    def clean(self):
        if self.effective_end and self.effective_start and self.effective_end < self.effective_start:
            raise ValidationError({'effective_end': 'End date cannot be before start date'})
    
    class Meta:
        abstract = True


###############################################################################
# User Core Models
###############################################################################

class UserProfile(models.Model):
    """
    Represents a player in "The Game", linking core identification with psychological profiles and resources.

    A user profile model that stores core identification and additional data for a player in the game,
    including linking the user to their current environment and profile name.

    Attributes:
        user(ForeignKey to AUTH_USER_MODEL): 
            Example: "Django User instance with username 'player1'"
        profile_name(CharField): 
            Example: "PlayerOne"  # The display or login name of the user.
        current_environment(ForeignKey to UserEnvironment): 
            Example: "Instance of UserEnvironment representing 'Home'"
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="profiles")
    profile_name = models.CharField(max_length=255, help_text="User's display or login name.")
    current_environment = models.ForeignKey("UserEnvironment", on_delete=models.SET_NULL, null=True, blank=True, help_text="Current environment of the user.")

    class Meta:
        ordering = ['profile_name']

    def __str__(self):
        return self.profile_name


class Demographics(models.Model):
    """
    Captures basic demographic details of the user.

    A model to store basic personal details about the user, including their name, age, gender, location, and occupation.

    Attributes:
        user_profile(OneToOneField to UserProfile): 
            Example: "Link to corresponding UserProfile instance"
        full_name(CharField): 
            Example: "Alice Johnson"
        age(IntegerField): 
            Example: 28
        gender(CharField): 
            Example: "Female"
        location(CharField): 
            Example: "New York, USA"
        language(CharField): 
            Example: "English"
        occupation(CharField): 
            Example: "Software Engineer"
        personal_prefs_json(JSONField): 
            Example: "{\"likes\": [\"music\", \"gaming\"]}"
    """
    user_profile = models.OneToOneField(UserProfile, on_delete=models.CASCADE, related_name="demographics")
    full_name = models.CharField(max_length=255, help_text="User's full name.")
    age = models.IntegerField(help_text="User's age.")
    gender = models.CharField(max_length=50, help_text="User's gender.")
    location = models.CharField(max_length=255, help_text="User's location or residence.")
    language = models.CharField(max_length=50, help_text="Preferred or native language.")
    occupation = models.CharField(max_length=255, help_text="User's occupation.")
    personal_prefs_json = models.JSONField(help_text="JSON containing personal preferences.")

    class Meta:
        ordering = ['full_name']

    def __str__(self):
        return self.full_name


###############################################################################
# User Psyche Profile Models
###############################################################################

class UserGoal(models.Model):
    """
    Serves as the base entity for user-defined goals (including intentions and aspirations).

    A model to represent goals defined by the user, including details like title, description, and importance.
    It tracks both user-specific and system-defined evaluations.

    Attributes:
        user_profile(ForeignKey to UserProfile): 
            Example: "Link to a UserProfile instance"
        title(CharField): 
            Example: "Start a daily mindfulness routine"
        description(TextField): 
            Example: "Practice mindfulness meditation for 10 minutes each morning."
        importance_according_user(IntegerField): 
            Example: 80
        importance_according_system(IntegerField): 
            Example: 75
        strength(IntegerField): 
            Example: 60
        created_at(DateTimeField): 
            Example: "2025-03-12T08:30:00Z"
        updated_at(DateTimeField): 
            Example: "2025-03-12T08:30:00Z"
        inspirations(ManyToManyField to Inspiration): 
            Example: "List of Inspiration instances, e.g., inspiration from a TED Talk"
    """
    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name="user_goals")
    title = models.CharField(max_length=255, help_text="A short descriptive title for the goal.")
    description = models.TextField(help_text="Detailed explanation of the goal.")
    importance_according_user = ValidatedRangeField(help_text="Importance as perceived by the user (0-100).")
    importance_according_system = ValidatedRangeField(help_text="System's evaluation of importance (0-100).")
    strength = ValidatedRangeField(help_text="Current momentum (0-100).")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    inspirations = models.ManyToManyField('Inspiration', through='GoalInspiration', related_name="influenced_goals", help_text="Inspirations linked to this goal.")
    influences = GenericRelation(
        'activity.ActivityInfluencedBy',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='user_goal'
    )

    @property
    def goal_type(self):
        """Return user-friendly goal type name."""
        return self.__class__.__name__
    
    def get_specific(self):
        """Ensure we always work with the most specific version of the goal."""
        return self.get_real_instance()
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user_profile']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return self.title


class Intention(UserGoal, TemporalRecord):
    """
    An actionable, time-bound goal extending UserGoal for short-term change.

    An intention represents a user-defined goal with a time-bound nature. This class extends UserGoal and TemporalRecord
    to track the time frame and progress of the goal.

    Attributes:
        start_date(DateField): 
            Example: "2025-03-12"  # The date when the intention starts.
        due_date(DateField): 
            Example: "2025-03-19"  # Target completion date for the intention.
        is_completed(BooleanField): 
            Example: False  # Indicates that the intention is still active.
        progress_notes(TextField): 
            Example: "Started with a brief meditation session, feeling calm and focused."
    """
    start_date = models.DateField(help_text="Date when the intention starts.")
    due_date = models.DateField(help_text="Target completion date.")
    is_completed = models.BooleanField(default=False, help_text="Indicates whether the intention is fulfilled.")
    progress_notes = models.TextField(help_text="Ongoing notes or reflections on progress.")

    def __str__(self):
        return f"Intention: {self.title}"


class Aspiration(UserGoal, TemporalRecord):
    """
    Captures long-term, visionary goals extending UserGoal.

    Aspiration represents long-term goals with broader and visionary objectives. It extends the UserGoal and TemporalRecord classes
    to track a more expansive time horizon and ambition level.

    Attributes:
        domain(CharField): 
            Example: "Career"  # The field or area of the aspiration.
        horizon(CharField): 
            Example: "Long-term"  # The time frame of the aspiration.
        level_of_ambition(CharField): 
            Example: "High"  # Descriptor indicating the intensity of the aspiration.
    """
    domain = models.CharField(max_length=255, help_text="Field or area of the aspiration (e.g., 'Career').")
    horizon = models.CharField(max_length=255, help_text="Time frame of the aspiration (e.g., 'Long-term').")
    level_of_ambition = models.CharField(max_length=50, help_text="Descriptor indicating intensity (e.g., 'High').")
    
    def __str__(self):
        return f"Aspiration: {self.title}"
    
    


class Inspiration(models.Model):
    """
    Records external or internal influences that motivate user goals.

    A model to capture the external or internal inspirations that affect the user's goals, such as motivational sources or events.

    Attributes:
        user_profile(ForeignKey to UserProfile): 
            Example: "Link to a UserProfile instance"
        source(CharField): 
            Example: "TED Talk"  # The origin of the inspiration.
        description(TextField): 
            Example: "A talk on overcoming personal limitations."
        strength(IntegerField): 
            Example: 85
        reference_url(URLField): 
            Example: "https://www.ted.com/talks/speaker"
        created_at(DateTimeField): 
            Example: "2025-03-12T08:30:00Z"
        updated_at(DateTimeField): 
            Example: "2025-03-12T08:30:00Z"
    """
    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name="inspirations")
    source = models.CharField(max_length=255, help_text="Origin of the inspiration (e.g., 'TED Talk').")
    description = models.TextField(help_text="Detailed description of the inspirational content.")
    strength = ValidatedRangeField(help_text="Inspirational impact rating (0-100).")
    reference_url = models.URLField(help_text="URL pointing to the source material.")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    influences = GenericRelation(
        'activity.ActivityInfluencedBy',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='inspiration'
    )

    class Meta:
        ordering = ['source']

    def __str__(self):
        return self.source


class GoalInspiration(models.Model):
    """
    Establishes a relationship between a goal and its sources of inspiration.

    A model that links specific inspirations to a user's goals, capturing the influence of external or internal sources on the goals.

    Attributes:
        user_goal(ForeignKey to UserGoal): 
            Example: "Link to a UserGoal instance"
        inspiration(ForeignKey to Inspiration): 
            Example: "Link to an Inspiration instance"
        strength(IntegerField):
        note(TextField): 
            Example: "Inspired by the innovative ideas from the TED Talk."
    """
    user_goal = models.ForeignKey(UserGoal, on_delete=models.CASCADE)
    inspiration = models.ForeignKey(Inspiration, on_delete=models.CASCADE)
    note = models.TextField(help_text="Commentary on the relationship.")
    strength = ValidatedRangeField(
        help_text="How strongly this inspiration influenced the goal (0-100)",
        default=50
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['user_goal']),
            models.Index(fields=['inspiration']),
        ]
        unique_together = ['user_goal', 'inspiration']

    def __str__(self):
        return f"{self.user_goal.title} - {self.inspiration.source}"


class TrustLevel(models.Model):
    """
    Quantifies the user's trust level across domains.

    A model representing the user's trust level, capturing their confidence in various areas, 
    as well as providing an aggregate type and specific identifier for the trust level.

    Attributes:
        user_profile(OneToOneField to UserProfile): 
            Example: "Link to a UserProfile instance"
        value(IntegerField): 
            Example: 90  # Trust level score on a 0-100 scale.
        aggregate_type(CharField): 
            Example: "Skill"  # For example, the trust might relate to a specific skill.
        aggregate_id(CharField): 
            Example: "SKL123"  # Identifier for the aggregate.
        notes(TextField): 
            Example: "Consistently high trust in decision-making abilities."
    """
    user_profile = models.OneToOneField(UserProfile, on_delete=models.CASCADE, related_name="trust_level")
    value = ValidatedRangeField(help_text="Trust level score (0-100).")
    aggregate_type = models.CharField(max_length=255, help_text="Name of the aggregate (e.g., 'Skill').")
    aggregate_id = models.CharField(max_length=255, help_text="Identifier for the aggregate.")
    notes = models.TextField(help_text="Additional context or commentary.")

    class Meta:
        ordering = ['user_profile']

    def __str__(self):
        return f"TrustLevel for {self.user_profile.profile_name}: {self.value}"


class Preference(TemporalRecord):
    """
    Represents a user preference, reflecting likes or habits.

    A model capturing a user's preferences, which can include various likes, dislikes, and habits, 
    along with their importance and awareness of the preferences.

    Attributes:
        user_profile(ForeignKey to UserProfile): 
            Example: "Link to a UserProfile instance"
        pref_name(CharField): 
            Example: "Morning Workouts"  # Name of the preference.
        pref_description(TextField): 
            Example: "Enjoys starting the day with light exercise."
        pref_strength(IntegerField): 
            Example: 70  # Importance rating on a 0-100 scale.
        user_awareness(IntegerField): 
            Example: 80  # How aware the user is of this preference.
        environment(ForeignKey to UserEnvironment): 
            Example: "Link to a UserEnvironment instance such as 'Gym'"
    """
    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name="preferences")
    pref_name = models.CharField(max_length=255, help_text="Name of the preference (e.g., 'Morning Workouts').")
    pref_description = models.TextField(help_text="Detailed description of the preference.")
    pref_strength = ValidatedRangeField(help_text="Importance rating (0-100).")
    user_awareness = ValidatedRangeField(help_text="User's awareness of this preference (0-100).")
    environment = models.ForeignKey("UserEnvironment", on_delete=models.SET_NULL, null=True, blank=True, help_text="Optional associated environment.")

    influences = GenericRelation(
        'activity.ActivityInfluencedBy',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='preference'
    )
    class Meta:
        ordering = ['pref_name']

    def __str__(self):
        return self.pref_name

class BeliefType(models.TextChoices):
    # Core Self-Perception Beliefs
    SELF_WORTH = "SELF_WORTH", "Self-Worth and Fundamental Value"
    SELF_EFFICACY = "SELF_EFFICACY", "Personal Capability and Competence"
    IDENTITY = "IDENTITY", "Core Identity and Purpose"
    
    # Capability and Potential Beliefs
    POTENTIAL = "POTENTIAL", "Personal Growth and Potential"
    SKILL_LEARNING = "SKILL_LEARN", "Learning and Skill Acquisition"
    TALENT_INNATE = "TALENT", "Innate Talents and Limitations"
    
    # Psychological Barriers and Limitations
    FEAR_PATTERN = "FEAR", "Fundamental Fears and Anxieties"
    AVOIDANCE = "AVOIDANCE", "Coping and Avoidance Mechanisms"
    TRAUMA_RESPONSE = "TRAUMA", "Trauma-Informed Belief Patterns"
    
    # Relational and Social Beliefs
    SOCIAL_CONNECTION = "SOCIAL", "Interpersonal Relationships"
    BELONGING = "BELONGING", "Sense of Belonging and Acceptance"
    TRUST = "TRUST", "Trust in Self and Others"
    
    # Existential and Philosophical Beliefs
    MEANING = "MEANING", "Life Purpose and Meaning"
    WORLD_VIEW = "WORLDVIEW", "Fundamental Worldview"
    CHANGE_ORIENTATION = "CHANGE", "Attitude Towards Change and Uncertainty"
    
    # Achievement and Goal-Related Beliefs
    SUCCESS_DEFINITION = "SUCCESS", "Personal Definition of Success"
    GOAL_ACHIEVEMENT = "GOAL", "Goal Attainment and Motivation"
    EFFORT_BELIEF = "EFFORT", "Beliefs About Effort and Reward"
    
    # Emotional Regulation Beliefs
    EMOTIONAL_CONTROL = "EMOTION_CONTROL", "Emotional Management"
    VULNERABILITY = "VULNERABILITY", "Openness to Emotional Vulnerability"
    RESILIENCE = "RESILIENCE", "Psychological Resilience"

# Add these models to backend/apps/user/models.py

class GenericBeliefDomainRelationship(models.Model):
    """
    Maps the relationship between belief types and activity domains with impact direction and strength.
    
    This allows modeling how specific belief types affect activities in particular domains:
    - Positive impact: Belief is supportive/helpful for activities in this domain
    - Negative impact: Belief creates resistance/difficulty for activities in this domain
    """
    class ImpactStrength(models.IntegerChoices):
        STRONGLY_NEGATIVE = -3, 'Strongly Limiting'
        MODERATELY_NEGATIVE = -2, 'Moderately Limiting'
        SLIGHTLY_NEGATIVE = -1, 'Slightly Limiting'
        NEUTRAL = 0, 'Neutral'
        SLIGHTLY_POSITIVE = 1, 'Slightly Supportive'
        MODERATELY_POSITIVE = 2, 'Moderately Supportive'
        STRONGLY_POSITIVE = 3, 'Strongly Supportive'
    
    generic_belief = models.ForeignKey(
        'GenericBelief',
        on_delete=models.CASCADE,
        related_name='domain_relationships_details'
    )
    domain = models.ForeignKey(
        'activity.GenericDomain', # Renamed target model
        on_delete=models.CASCADE,
        related_name='belief_relationships'
    )
    impact = models.IntegerField(
        choices=ImpactStrength.choices,
        default=ImpactStrength.NEUTRAL,
        help_text="How this belief affects activities in this domain"
    )
    notes = models.TextField(
        blank=True,
        help_text="Explanation of how/why this belief affects activities in this domain"
    )
    
    class Meta:
        unique_together = ['generic_belief', 'domain']
        verbose_name = "Belief-Domain Relationship"
        verbose_name_plural = "Belief-Domain Relationships"

    def __str__(self):
        return f"{self.generic_belief.name} → {self.domain.name}: {self.get_impact_display()}"


class GenericBelief(models.Model):
    """
    Template for common belief patterns that can be instantiated as user-specific beliefs.
    
    This model enables systematic cataloging of belief types, their typical characteristics,
    and their relationships to activity domains.
    """
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=50, unique=True, help_text="Unique code like 'self_efficacy_general'")
    category = models.CharField(
        max_length=50, 
        choices=BeliefType.choices,  # Use the existing BeliefType choices
        help_text="Category of belief (e.g., 'SELF_EFFICACY', 'FEAR')"
    )
    name = models.CharField(max_length=255, help_text="Name of the belief pattern")
    description = models.TextField(help_text="Description of this belief type and how it affects behavior")
    typical_stability = ValidatedRangeField(
        help_text="How resistant this belief type typically is to change",
        default=50
    )
    domains = models.ManyToManyField(
        'activity.GenericDomain', # Renamed target model
        through='GenericBeliefDomainRelationship',
        related_name='related_beliefs',
        help_text="Activity domains this belief affects"
    )
    
    # Connect to existing activity requirement system
    activity_requirements = GenericRelation(
        'activity.GenericActivityUserRequirement',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='generic_belief'
    )
    
    class Meta:
        verbose_name = "Generic Belief"
        verbose_name_plural = "Generic Beliefs"
        
    def __str__(self):
        return self.name


class Belief(models.Model):
    """
    Encapsulates a cognitive proposition held by the user.

    A model representing a belief held by the user, capturing the content of the belief, 
    confidence levels, emotionality, and stability over time.

    Attributes:
        user_profile(ForeignKey to UserProfile): 
            Example: "Link to a UserProfile instance"
        content(TextField): 
            Example: "I can overcome any challenge if I stay persistent."
        last_updated(DateField): 
            Example: "2025-03-12"
        user_confidence(IntegerField): 
            Example: 85
        system_confidence(IntegerField): 
            Example: 80
        emotionality(IntegerField): 
            Example: 10  # Scale might range from -100 to 100.
        stability(IntegerField): 
            Example: 70
        user_awareness(IntegerField): 
            Example: 90
    """
    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name="beliefs")
    content = models.TextField(help_text="Core statement or proposition.")
    last_updated = models.DateField(help_text="Date of the most recent update.")
    user_confidence = ValidatedRangeField(help_text="User's personal conviction (0-100).")
    system_confidence = ValidatedRangeField(help_text="System's evaluation (0-100).")
    emotionality = ExtendedRangeField(help_text="Emotional charge (-100 to 100).")
    stability = ValidatedRangeField(help_text="Resistance to change (0-100).")
    user_awareness = ValidatedRangeField(help_text="Awareness level (0-100).")
    influences = GenericRelation(
        'activity.ActivityInfluencedBy',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='belief'
    )
    generic_belief = models.ForeignKey(
        GenericBelief, 
        on_delete=models.SET_NULL,
        null=True, 
        blank=True,
        related_name="user_beliefs",
        help_text="Reference to the generic belief template this belief is based on"
    )

    class Meta:
        ordering = ['-last_updated']

    def __str__(self):
        return self.content[:50]


class BeliefEvidence(models.Model):
    """
    Represents evidence that supports or contradicts a belief.

    A model for recording evidence that either supports or contradicts a belief, including a description,
    credibility score, and the source of the evidence.

    Attributes:
        belief(ForeignKey to Belief): 
            Example: "Link to a Belief instance"
        evidence_type(CharField): 
            Example: "EXPERIENCE"
        description(TextField): 
            Example: "Overcame previous failures by adopting a new mindset."
        credibility_score(FloatField): 
            Example: 0.95  # A score representing the reliability of the evidence.
        source(CharField): 
            Example: "User's personal experience"
    """
    belief = models.ForeignKey(Belief, on_delete=models.CASCADE, related_name="evidences")
    evidence_type = models.CharField(max_length=100, help_text="Category of evidence (e.g., 'EXPERIENCE').")
    description = models.TextField(help_text="Detailed description including context.")
    credibility_score = models.FloatField(help_text="Reliability measure of the evidence.")
    source = models.CharField(max_length=255, help_text="Origin of the evidence.")

    class Meta:
        ordering = ['belief']

    def __str__(self):
        return f"Evidence for {self.belief.id}"


class BeliefInfluence(models.Model):
    """
    Represents the influence a belief has on another target entity.

    A model to capture the influence of a belief on other entities, such as goals or capabilities, 
    including the strength of that influence.

    Attributes:
        belief(ForeignKey to Belief): 
            Example: "Link to a Belief instance"
        target_entity_id(UUIDField): 
            Example: "550e8400-e29b-41d4-a716-************"
        target_entity_type(CharField): 
            Example: "Goal" or "Skill"
        influence_strength(IntegerField): 
            Example: 50  # Influence strength on a scale (e.g., -100 to 100).
        note(TextField): 
            Example: "This belief positively influences goal-setting."
    """
    belief = models.ForeignKey(Belief, on_delete=models.CASCADE, related_name="influences")
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.UUIDField()
    target_entity = GenericForeignKey('content_type', 'object_id')
    influence_strength = ExtendedRangeField(help_text="Strength of influence (-100 to 100)")
    note = models.TextField(help_text="Additional remarks")

    class Meta:
        ordering = ['belief']
        constraints = [
            models.CheckConstraint(
                check=models.Q(influence_strength__gte=-100) & models.Q(influence_strength__lte=100),
                name="belief_influence_strength_range"
            ),
            models.UniqueConstraint(
                fields=['belief', 'content_type', 'object_id'],
                name="unique_belief_influence"
            )
        ]

    def __str__(self):
        return f"Influence of {self.belief.id} on {self.target_entity_type}"


class CurrentMood(TemporalRecord):
    """
    Tracks the user's immediate emotional state for adaptive interventions.

    A model that captures the user's current emotional state, including the intensity of their mood, 
    awareness, and timestamp for adaptive interventions.

    Attributes:
        user_profile(OneToOneField to UserProfile): 
            Example: "Link to a UserProfile instance"
        description(TextField): 
            Example: "Feeling motivated and calm."
        height(IntegerField): 
            Example: 75  # Intensity level on a scale from 0 to 100.
        user_awareness(IntegerField): 
            Example: 85  # The user's awareness of their current mood.
        processed_at(DateTimeField): 
            Example: "2025-03-12T09:00:00Z"
    """
    user_profile = models.OneToOneField(UserProfile, on_delete=models.CASCADE, related_name="current_mood")
    description = models.TextField(help_text="Description of the current mood.")
    height = ValidatedRangeField(help_text="Intensity of the mood (0-100).")
    user_awareness = ValidatedRangeField(help_text="Awareness level (0-100).")
    processed_at = models.DateTimeField(help_text="Timestamp when the mood was recorded.")
    inferred_from_text = models.BooleanField(default=False, help_text="Was this mood inferred from text analysis?") # Added field
    influences = GenericRelation(
        'activity.ActivityInfluencedBy',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='current_mood'
    )

    class Meta:
        ordering = ['processed_at']

    def __str__(self):
        return f"Mood for {self.user_profile.profile_name}"


###############################################################################
# Catalog & Shared Entities (User & Activities)
###############################################################################

class TraitType(models.TextChoices):
    OPENNESS = "OPEN", "Openness"
    CONSCIENTIOUSNESS = "CONS", "Conscientiousness"
    EXTRAVERSION = "EXTR", "Extraversion"
    AGREEABLENESS = "AGRE", "Agreeableness"
    EMOTIONALITY = "EMO", "Emotionality"
    HONESTYHUMILITY = "HONHUM", "Honesty-Humility"
    UNDEFINED = "UND", "Undefined"

class GenericTrait(models.Model):
    """
    Provides metadata for traits referenced in user inclinations.

    A model that holds metadata related to traits which are referenced in the user's inclinations,
    providing a description and creation timestamps for each trait.

    Attributes:
        id(AutoField): 
            Example: 1
        code(CharField): 
            Example: "openness_creativity"
        trait_type(CharField): 
            Example: "OPEN"  # Corresponds to 'Openness'.
        name(CharField): 
            Example: "Openness to Experience"
        description(TextField): 
            Example: "Willingness to try new things and be open to new experiences."
        created_at(DateTimeField): 
            Example: "2025-03-12T08:30:00Z"
        updated_at(DateTimeField): 
            Example: "2025-03-12T08:30:00Z"
    """
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=50, unique=True, help_text="Unique, human-readable code for this trait (e.g., 'openness_creativity')")
    trait_type = models.CharField(max_length=50, choices=TraitType.choices)
    name = models.CharField(max_length=255, help_text="Name of the trait.")
    description = models.TextField(help_text="Detailed description of the trait.")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    activity_requirements = GenericRelation(
        'activity.GenericActivityUserRequirement',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='trait'
    )

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['code']),  # Add index for faster lookups by code
        ]

    def __str__(self):
        return self.name
    

class UserTraitInclination(models.Model):
    """
    Represents individual trait inclinations of the user.

    A model that captures the individual inclinations a user has toward specific traits, 
    including strength and awareness.

    Attributes:
        user_profile(ForeignKey to UserProfile): 
            Example: "Link to a UserProfile instance"
        generic_trait(ForeignKey to GenericTrait): 
            Example: "Link to a GenericTrait instance (e.g., Openness)"
        strength(FloatField): 
            Example: 70.5
        awareness(IntegerField): 
            Example: 80
    """
    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name="trait_inclinations")
    generic_trait = models.ForeignKey(GenericTrait, on_delete=models.CASCADE, related_name="inclinations")
    strength = models.FloatField(help_text="Strength of the inclination.")
    awareness = ValidatedRangeField(help_text="User awareness (0-100).")
    required_by_activity = GenericRelation(
        'activity.ActivityUserRequirement',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='trait_inclination'
    )

    class Meta:
        ordering = ['user_profile']
        constraints = [
            models.CheckConstraint(
                check=models.Q(strength__gte=0) & models.Q(strength__lte=100),
                name="trait_strength_range"
            ),
            models.UniqueConstraint(
                fields=['user_profile', 'generic_trait'],
                name="unique_user_trait"
            )
        ]

    def __str__(self):
        return f"{self.user_profile.profile_name} inclination for {self.generic_trait.name}"


from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
import slugify
from django.conf import settings

from django.db import models
import slugify
from django.core.validators import MinValueValidator, MaxValueValidator

class GenericEnvironment(models.Model):
    """
    Catalog of archetypal environment templates available for users to select.
    
    This model represents the general categories of environments that users can reference
    when creating their specific environments. It describes the broad characteristics
    without user-specific details.
    """

    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=50, unique=True, help_text="Unique, human-readable code for this environment (e.g., 'ind_home')")
    name = models.CharField(max_length=255, help_text="Name of the archetypal environment.")
    description = models.TextField(help_text="General description of this environment type.")
    is_active = models.BooleanField(default=True, help_text="Flag indicating if this environment type is available for selection.")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Core environment classification
    is_indoor = models.BooleanField(default=True, help_text="Whether the environment is typically indoors or outdoors")
    class PrimaryCategory(models.TextChoices):
        RESIDENTIAL = 'residential', 'Residential'
        PROFESSIONAL = 'professional', 'Professional'
        EDUCATIONAL = 'educational', 'Educational'
        COMMERCIAL = 'commercial', 'Commercial'
        RECREATIONAL = 'recreational', 'Recreational'
        NATURAL = 'natural', 'Natural'
        TRANSPORTATION = 'transportation', 'Transportation'
        CULTURAL = 'cultural', 'Cultural'
        HEALTHCARE = 'healthcare', 'Healthcare'
        OTHER = 'other', 'Other'
    
    primary_category = models.CharField(
        max_length=20, 
        choices=PrimaryCategory.choices, 
        help_text="Primary category of the environment"
    )
    # Domain relationships similar to GenericActivity
    domain_relationships = models.ManyToManyField(
        'activity.GenericDomain', # Renamed target model
        through='GenericEnvironmentDomainRelationship',
        related_name='related_environments',
        help_text="Associated domains with relationship strength"
    )
    
    # Typical characteristics
    SPACE_SIZE_CHOICES = [
        ('tiny', 'Tiny'),
        ('small', 'Small'),
        ('medium', 'Medium'),
        ('large', 'Large'),
        ('vast', 'Vast')
    ]
    
    typical_space_size = models.CharField(
        max_length=10,
        choices=SPACE_SIZE_CHOICES,
        default='medium',
        help_text="Typical size classification of this environment type"
    )
    
    typical_privacy_level = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=50,
        help_text="Typical privacy level: Public (0) to extremely private (100)"
    )
    
    # Flexible JSON field for miscellaneous properties
    archetype_attributes = models.JSONField(
        default=dict,
        help_text="Additional attributes typical of this environment type"
    )

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_indoor']),
        ]
        verbose_name = "Generic Environment"
        verbose_name_plural = "Generic Environments"

    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.code:
            # Generate a code if not provided
            prefix = "ind" if self.is_indoor else "out"
            # Get primary domain if available
            primary_domain = self.get_primary_domain()
            if primary_domain:
                domain_prefix = primary_domain.code[:3]
            else:
                domain_prefix = "gen"  # generic fallback
            slug = slugify(self.name)[:20]
            self.code = f"{prefix}_{domain_prefix}_{slug}"
        super().save(*args, **kwargs)
    
    def get_primary_domain(self):
        """Return the primary domain of this environment based on relationship strength"""
        primary_rel = self.domain_relationships_details.filter(
            strength=self.domain_relationships_details.model.RelationshipStrength.PERFECT
        ).first()
        
        return primary_rel.domain if primary_rel else None
    
    def get_domains_by_strength(self, min_strength=None):
        """
        Return domains ordered by relationship strength (strongest first).
        Optionally filter by minimum strength.
        """
        relationships = self.domain_relationships_details.all()
        if min_strength is not None:
            relationships = relationships.filter(strength__gte=min_strength)
        return relationships.order_by('-strength')
    
    def get_activity_support_for_domain(self, domain_id):
        """
        Returns the support level (0-100) for a specific activity domain.
        Higher values indicate better support for activities in that domain.
        """
        try:
            relationship = self.domain_relationships_details.get(domain_id=domain_id)
            return relationship.strength
        except self.domain_relationships_details.model.DoesNotExist:
            return 0
    
    def get_domain_support_mapping(self):
        """
        Returns a dictionary mapping domain categories to support levels.
        Useful for quickly determining how well this environment supports
        different types of activities.
        """
        support_mapping = {}
        relationships = self.domain_relationships_details.all().select_related('domain')
        
        for rel in relationships:
            category = rel.domain.get_primary_category_display() if rel.domain.primary_category else 'Other'
            if category not in support_mapping or rel.strength > support_mapping[category]:
                support_mapping[category] = rel.strength
        
        return support_mapping


class GenericEnvironmentDomainRelationship(models.Model):
    """
    Maps the relationship between environments and domains with a strength indicator.
    Similar to GenericDomainRelationship for consistency.
    
    The strength value represents how well the environment supports activities
    in the associated domain. Higher values indicate better support.
    """
    class RelationshipStrength(models.IntegerChoices):
        STRONGLY_DISCOURAGED = -100, 'Strongly Discouraged'
        DISCOURAGED = -70, 'Discouraged'
        SLIGHTLY_DISCOURAGED = -30, 'Slightly Discouraged'
        NEUTRAL = 0, 'Neutral'
        MINIMAL = 10, 'Minimal'
        MODERATE = 30, 'Moderate'
        SIGNIFICANT = 70, 'Significant'
        PERFECT = 100, 'Perfect'

    generic_environment = models.ForeignKey(
        GenericEnvironment,
        on_delete=models.CASCADE,
        related_name='domain_relationships_details'
    )
    domain = models.ForeignKey(
        'activity.GenericDomain', # Renamed target model
        on_delete=models.CASCADE,
        related_name='environment_relationships'
    )
    strength = models.SmallIntegerField(
        choices=RelationshipStrength.choices,
        default=RelationshipStrength.NEUTRAL,
        help_text="How this environment affects activities in this domain (-100 to 100). Negative values indicate discouragement."
    )

    class Meta:
        unique_together = ['generic_environment', 'domain']
        verbose_name = "Environment-Domain Relationship"
        verbose_name_plural = "Environment-Domain Relationships"
        indexes = [
            models.Index(fields=['generic_environment']),
            models.Index(fields=['domain']),
        ]

    def __str__(self):
        return f"{self.generic_environment.name} - {self.domain.name} ({self.get_strength_display()})"


class UserEnvironment(models.Model):
    """
    Models a specific environment instance for a user.

    This model represents a user's personal environment with detailed properties
    that impact activity recommendations and personalizations.
    """
    id = models.BigAutoField(primary_key=True)
    user_profile = models.ForeignKey('user.UserProfile', on_delete=models.CASCADE, related_name="environments")
    environment_name = models.CharField(max_length=255, help_text="User's personal name for this environment")
    environment_description = models.TextField(help_text="User's description of their specific environment")
    generic_environment = models.ForeignKey(GenericEnvironment, on_delete=models.SET_NULL, null=True, blank=True,
                                         help_text="The archetypal environment this is based on")
    effective_start = models.DateField(help_text="When this environment became available to the user")
    effective_end = models.DateField(null=True, blank=True, help_text="When this environment will no longer be available")
    is_current = models.BooleanField(default=True, help_text="Whether this is a current environment for the user")
    
    # Add direct access to domains for convenience
    domains = models.ManyToManyField(
        'activity.GenericDomain', # Renamed target model
        related_name='user_environments',
        blank=True,
        help_text="Activity domains relevant to this specific user environment"
    )
    
    # Basic JSON field for miscellaneous properties
    environment_details = models.JSONField(default=dict, help_text="Additional environment details specific to this user")

    class Meta:
        ordering = ['-is_current', 'environment_name']
        indexes = [
            models.Index(fields=['user_profile', 'is_current']),
            models.Index(fields=['generic_environment']),
        ]
        verbose_name = "User Environment"
        verbose_name_plural = "User Environments"

    def __str__(self):
        return f"{self.environment_name} ({self.user_profile.profile_name})"
    
    def get_domains(self):
        """
        Get all domains associated with this environment, 
        combining explicitly linked domains and those from the generic environment.
        """
        domain_ids = set(self.domains.values_list('id', flat=True))
        
        # Add domains from generic environment if available
        if self.generic_environment:
            generic_domain_ids = self.generic_environment.domain_relationships.values_list('id', flat=True)
            domain_ids.update(generic_domain_ids)
            
            # Add primary domain from generic environment if present
            primary_domain = self.generic_environment.get_primary_domain()
            if primary_domain:
                domain_ids.add(primary_domain.id)
        
        from apps.activity.models import GenericDomain # Renamed import
        return GenericDomain.objects.filter(id__in=domain_ids) # Use renamed model


class UserEnvironmentPhysicalProperties(models.Model):
    """
    Physical properties of a user's specific environment.
    
    Captures detailed physical characteristics of a user's environment that affect activity suitability.
    
    Attributes:
        user_environment (OneToOneField): The related UserEnvironment
        rurality (IntegerField):
            Example: 20  # Urban (0) to rural (100) scale
        noise_level (IntegerField): 
            Example: 20  # Very quiet (0) to extremely loud (100)
        light_quality (IntegerField):
            Example: 70  # Very dark (0) to very bright (100)
        temperature_range (CharField):
            Example: "moderate"
        accessibility (IntegerField):
            Example: 90  # Not accessible (0) to fully accessible (100)
        air_quality (IntegerField):
            Example: 80  # Poor (0) to excellent (100)
        has_natural_elements (BooleanField):
            Example: True  # Presence of plants, trees, or natural features
        surface_type (CharField):
            Example: "mixed"
    """
    user_environment = models.OneToOneField(
        UserEnvironment,
        on_delete=models.CASCADE,
        related_name='physical_properties'
    )
    
    rurality = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Urban (0) to rural (100) scale"
    )
    
    noise_level = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Very quiet (0) to extremely loud (100)"
    )
    
    light_quality = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Very dark (0) to very bright (100)"
    )
    
    TEMPERATURE_CHOICES = [
        ('cold', 'Cold'),
        ('cool', 'Cool'),
        ('moderate', 'Moderate'),
        ('warm', 'Warm'),
        ('hot', 'Hot'),
        ('variable', 'Highly Variable')
    ]
    
    temperature_range = models.CharField(
        max_length=15,
        choices=TEMPERATURE_CHOICES,
        default='moderate',
        help_text="Typical temperature condition of the environment"
    )
    
    accessibility = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Not accessible (0) to fully accessible (100) for people with mobility limitations"
    )
    
    air_quality = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Poor (0) to excellent (100) air quality"
    )
    
    has_natural_elements = models.BooleanField(
        default=False,
        help_text="Presence of plants, trees, or natural features"
    )
    
    SURFACE_CHOICES = [
        ('hard', 'Hard (concrete, tile, etc.)'),
        ('soft', 'Soft (carpet, grass, etc.)'),
        ('mixed', 'Mixed surfaces'),
        ('variable', 'Highly variable')
    ]
    
    surface_type = models.CharField(
        max_length=15,
        choices=SURFACE_CHOICES,
        default='mixed',
        help_text="Primary surface type of the environment"
    )
    
    water_proximity = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=0,
        help_text="No water (0) to immediate water access (100)"
    )
    
    space_size = models.CharField(
        max_length=10,
        choices=GenericEnvironment.SPACE_SIZE_CHOICES,
        default='medium',
        help_text="Size classification of this specific environment"
    )
    
    class Meta:
        verbose_name = "User Environment Physical Properties"
        verbose_name_plural = "User Environment Physical Properties"


class UserEnvironmentSocialContext(models.Model):
    """
    Social context of a user's specific environment.
    
    Describes social aspects of a user's environment that affect activity suitability.
    
    Attributes:
        user_environment (OneToOneField): The related UserEnvironment
        privacy_level (IntegerField):
            Example: 80  # Public (0) to extremely private (100)
        typical_occupancy (IntegerField):
            Example: 40  # Empty (0) to extremely crowded (100)
        social_interaction_level (IntegerField):
            Example: 60  # No interaction (0) to constant interaction (100)
        formality_level (IntegerField):
            Example: 70  # Very casual (0) to very formal (100)
        safety_level (IntegerField):
            Example: 90  # Unsafe (0) to very safe (100)
        supervision_level (CharField):
            Example: "none"
        cultural_diversity (IntegerField):
            Example: 50  # Homogeneous (0) to highly diverse (100)
    """
    user_environment = models.OneToOneField(
        UserEnvironment,
        on_delete=models.CASCADE,
        related_name='social_context'
    )
    
    privacy_level = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Public (0) to extremely private (100)"
    )
    
    typical_occupancy = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Empty (0) to extremely crowded (100)"
    )
    
    social_interaction_level = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="No interaction (0) to constant interaction (100)"
    )
    
    formality_level = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Very casual (0) to very formal (100)"
    )
    
    safety_level = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Unsafe (0) to very safe (100)"
    )
    
    SUPERVISION_CHOICES = [
        ('none', 'None'),
        ('minimal', 'Minimal'),
        ('moderate', 'Moderate'),
        ('high', 'High'),
        ('constant', 'Constant')
    ]

    
    supervision_level = models.CharField(
        max_length=15,
        choices=SUPERVISION_CHOICES,
        default='none',
        help_text="Level of supervision or monitoring in the environment"
    )
    
    cultural_diversity = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=50,
        help_text="Homogeneous (0) to highly diverse (100)"
    )
    
    class Meta:
        verbose_name = "Environment Social Context"
        verbose_name_plural = "Environment Social Contexts"

class UserEnvironmentActivitySupport(models.Model):
    """
    Activity support aspects of a user's specific environment.
    
    Evaluates how well the user's environment supports different activity types,
    extending the domain support relationships from the generic environment with user-specific details.
    
    Attributes:
        user_environment (OneToOneField): The related UserEnvironment
        digital_connectivity (IntegerField):
            Example: 90  # No connectivity (0) to excellent connectivity (100)
        resource_availability (IntegerField):
            Example: 40  # No resources (0) to abundant resources (100)
        time_availability (JSONField):
            Example: {"weekdays": ["morning", "evening"], "weekends": ["all_day"]}
        domain_specific_support (JSONField):
            Example: {"creative": 80, "physical": 30} # User-specific overrides to the generic environment's domain support
    """
    user_environment = models.OneToOneField(
        UserEnvironment,
        on_delete=models.CASCADE,
        related_name='activity_support'
    )
    
    digital_connectivity = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="No connectivity (0) to excellent connectivity (100)"
    )
    
    resource_availability = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="No resources (0) to abundant resources (100)"
    )
    
    time_availability = models.JSONField(
        default=dict,
        help_text="JSON describing when this specific environment is available to the user"
    )
    
    domain_specific_support = models.JSONField(
        default=dict,
        help_text="User-specific domain support levels, overriding the generic environment's defaults"
    )
    
    class Meta:
        verbose_name = "User Environment Activity Support"
        verbose_name_plural = "User Environment Activity Supports"
    
    def get_support_for_domain(self, domain_id, domain_code=None):
        """
        Returns the support level for a specific domain, with user-specific overrides.
        Falls back to the generic environment's support level if no override exists.
        """
        # Try to get from user-specific overrides first
        if domain_code and domain_code in self.domain_specific_support:
            return self.domain_specific_support[domain_code]
        
        # Fall back to generic environment's support level
        if self.user_environment.generic_environment:
            return self.user_environment.generic_environment.get_activity_support_for_domain(domain_id)
        
        # Default value if nothing else is available
        return 0
    
    def update_from_generic_environment(self):
        """
        Updates the domain_specific_support field based on the generic environment's
        support levels. This can be called when initially creating the user environment
        to copy the base values, which can then be customized.
        """
        if not self.user_environment.generic_environment:
            return
        
        # Get all domain relationships from the generic environment
        gen_env = self.user_environment.generic_environment
        support_values = {}
        
        for rel in gen_env.domain_relationships_details.select_related('domain'):
            support_values[rel.domain.code] = rel.strength
        
        # Update our domain_specific_support field with these values
        if not self.domain_specific_support:
            self.domain_specific_support = {}
            
        self.domain_specific_support.update(support_values)
        self.save()


class UserEnvironmentPsychologicalQualities(models.Model):
    """
    Psychological qualities of a user's specific environment.
    
    Describes user-specific aspects of an environment that influence mood, creativity, and psychological responses.
    These qualities can vary significantly based on the user's unique perceptions and experiences.
    
    Attributes:
        user_environment (OneToOneField): The related UserEnvironment
        restorative_quality (IntegerField):
            Example: 75  # Not restorative (0) to highly restorative (100)
        stimulation_level (IntegerField):
            Example: 40  # Under-stimulating (0) to over-stimulating (100)
        aesthetic_appeal (IntegerField):
            Example: 65  # Unappealing (0) to highly appealing (100)
        novelty_level (IntegerField):
            Example: 30  # Very familiar (0) to completely novel (100)
        comfort_level (IntegerField):
            Example: 80  # Uncomfortable (0) to very comfortable (100)
        personal_significance (IntegerField):
            Example: 90  # None (0) to deep significance (100)
        emotional_associations (JSONField):
            Example: {"calm": 80, "focus": 70, "happiness": 65}
    """
    user_environment = models.OneToOneField(
        UserEnvironment,
        on_delete=models.CASCADE,
        related_name='psychological_qualities'
    )
    
    restorative_quality = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Not restorative (0) to highly restorative (100) for this specific user"
    )
    
    stimulation_level = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Under-stimulating (0) to over-stimulating (100) for this specific user"
    )
    
    aesthetic_appeal = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Unappealing (0) to highly appealing (100) for this specific user"
    )
    
    novelty_level = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Very familiar (0) to completely novel (100) for this specific user"
    )
    
    comfort_level = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Uncomfortable (0) to very comfortable (100) for this specific user"
    )
    
    personal_significance = ValidatedRangeField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=0,
        help_text="How personally significant this environment is to the user"
    )
    
    emotional_associations = models.JSONField(
        default=dict,
        help_text="User's emotional associations with this specific environment"
    )
    
    class Meta:
        verbose_name = "User Environment Psychological Qualities"
        verbose_name_plural = "User Environment Psychological Qualities"
class Inventory(models.Model):
    """
    Represents a snapshot of the user's available resources for a specific time period.

    A model that captures the inventory of resources available to a user within a specific environment,
    along with a validity period for the inventory and additional remarks.

    Attributes:
        user_environment(ForeignKey to UserEnvironment): 
            Example: "Link to a UserEnvironment instance"
        valid_until(DateField): 
            Example: "2025-04-01"
        notes(TextField): 
            Example: "Contains basic workout equipment and mindfulness journal."
    """
    user_environment = models.ForeignKey(UserEnvironment, on_delete=models.CASCADE, related_name="inventories")
    valid_until = models.DateField(help_text="Date until which the inventory is valid.")
    notes = models.TextField(help_text="Additional remarks.")

    class Meta:
        ordering = ['valid_until']

    def __str__(self):
        return f"Inventory for {self.user_environment.environment_name}"


class GenericResource(models.Model):
    """
    Defines baseline properties for any resource used in the game.

    A model that represents a generic resource used in the game, including the resource category, 
    description, and its associated costs.

    Attributes:
        id(AutoField): 
            Example: 1
        code(CharField):
            Example: "equip_yoga_mat"  # Unique code following the [category]_[item]_[variant] format
        resource_type(CharField): 
            Example: "sport equipment"
        description(TextField): 
            Example: "A set of dumbbells and a yoga mat."
        op_cost(IntegerField): 
            Example: 20
        acq_cost(IntegerField): 
            Example: 50
    """
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=50, unique=True, help_text="Unique, human-readable code for this resource (e.g., 'equip_yoga_mat')")
    resource_type = models.CharField(max_length=255, help_text="Category of the resource (e.g., 'sport equipment').")
    description = models.TextField(help_text="Brief explanation of the resource.")
    op_cost = MoneyField(
        max_digits=10, 
        decimal_places=2, 
        default_currency='USD',
        help_text="Operational cost"
    )
    acq_cost = MoneyField(
        max_digits=10, 
        decimal_places=2, 
        default_currency='USD',
        help_text="Acquisition cost"
    )

    class Meta:
        ordering = ['resource_type']
        indexes = [
            models.Index(fields=['code']),  # Add index for faster lookups by code
        ]

    def __str__(self):
        return self.resource_type


class UserResource(models.Model):
    """
    A specific resource a user has access to, extending GenericResource.

    A model that represents a specific resource a user has access to, including its unique name, associated environment,
    and additional details about the resource.

    Attributes:
        specific_name(CharField): 
            Example: "Yoga Mat"
        user_environment(ForeignKey to UserEnvironment): 
            Example: "Link to a UserEnvironment instance"
        location_details(TextField): 
            Example: "Stored in the living room"
        ownership_details(TextField): 
            Example: "Owned by the user"
        contact_info(CharField): 
            Example: "<EMAIL>"
        notes(TextField): 
            Example: "Recently cleaned and in good condition."
        generic_resource(ForeignKey to GenericResource): 
            Example: "Link to a GenericResource instance (e.g., 'sport equipment')"
    """
    specific_name = models.CharField(max_length=255, help_text="Unique name or label for the resource.")
    user_environment = models.ForeignKey(UserEnvironment, on_delete=models.CASCADE, related_name="user_resources")
    location_details = models.TextField(help_text="Extra location information for the resource.")
    ownership_details = models.TextField(help_text="Details regarding ownership or usage.")
    contact_info = models.CharField(max_length=255, help_text="Contact information for resource usage requests.")
    notes = models.TextField(help_text="Additional remarks.")
    generic_resource = models.ForeignKey(GenericResource, on_delete=models.SET_NULL, null=True, blank=True, help_text="Based on a generic resource if applicable.")

    class Meta:
        ordering = ['specific_name']

    def __str__(self):
        return self.specific_name

class SkillDomainRelationship(models.Model):
    class RelationshipStrength(models.IntegerChoices):
        MINIMAL = 10, 'Minimal'
        MODERATE = 30, 'Moderate'
        SIGNIFICANT = 70, 'Significant'
        PRIMARY = 100, 'Primary'
    
    class ApplicationContext(models.TextChoices):
        NOVICE = 'novice', 'Novice Application'
        INTERMEDIATE = 'intermediate', 'Intermediate Application'
        ADVANCED = 'advanced', 'Advanced Application'
        EXPERT = 'expert', 'Expert Application'
    
    generic_skill = models.ForeignKey(
        'user.GenericSkill',
        on_delete=models.CASCADE,
        related_name='domain_relationships_details'
    )
    domain = models.ForeignKey(
        'activity.GenericDomain', # Renamed target model
        on_delete=models.CASCADE,
        related_name='skill_relationships'
    )
    strength = models.PositiveSmallIntegerField(
        choices=RelationshipStrength.choices,
        default=RelationshipStrength.MODERATE,
        help_text="How strongly this skill relates to this domain"
    )
    
    # New fields
    application_context = models.CharField(
        max_length=20,
        choices=ApplicationContext.choices,
        default=ApplicationContext.INTERMEDIATE,
        help_text="At what skill level this relationship becomes most relevant"
    )
    
    transfer_coefficient = models.FloatField(
        default=1.0,
        validators=[MinValueValidator(0.1), MaxValueValidator(2.0)],
        help_text="Multiplier for how effectively this skill transfers to this domain (1.0 = direct application)"
    )
    
    skill_ceiling = models.IntegerField(
        default=100,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        help_text="Maximum effectiveness this skill can provide in this domain (e.g., 80 means this skill alone can only address 80% of domain challenges)"
    )
    
    complementary_skills = models.ManyToManyField(
        'user.GenericSkill',
        related_name='complementary_to',
        blank=True,
        help_text="Other skills that enhance effectiveness when combined with this one in this domain"
    )
    
    notes = models.TextField(
        blank=True,
        help_text="Context-specific information about how this skill applies in this domain"
    )


class SkillTraitRelationship(models.Model):
    """
    Maps the relationship between skills and personality traits with impact direction and strength.
    
    This allows modeling how specific traits affect the acquisition and application of skills:
    - Positive impact: Trait makes skill easier to learn/apply
    - Negative impact: Trait makes skill more challenging to learn/apply
    """
    class ImpactType(models.IntegerChoices):
        STRONGLY_HINDERING = -3, 'Strongly Hindering'
        MODERATELY_HINDERING = -2, 'Moderately Hindering'
        SLIGHTLY_HINDERING = -1, 'Slightly Hindering'
        NEUTRAL = 0, 'Neutral'
        SLIGHTLY_BENEFICIAL = 1, 'Slightly Beneficial'
        MODERATELY_BENEFICIAL = 2, 'Moderately Beneficial'
        STRONGLY_BENEFICIAL = 3, 'Strongly Beneficial'
    
    generic_skill = models.ForeignKey(
        'user.GenericSkill',
        on_delete=models.CASCADE,
        related_name='trait_relationships'
    )
    generic_trait = models.ForeignKey(
        'user.GenericTrait',
        on_delete=models.CASCADE,
        related_name='skill_relationships'
    )
    impact = ValidatedRangeField(
        choices=ImpactType.choices,
        default=ImpactType.NEUTRAL,
        help_text="How this trait affects learning/applying this skill"
    )
    notes = models.TextField(
        blank=True,
        help_text="Explanation of how/why this trait affects this skill"
    )
    
    class Meta:
        unique_together = ['generic_skill', 'generic_trait']
        verbose_name = "Skill-Trait Relationship"
        verbose_name_plural = "Skill-Trait Relationships"

    def __str__(self):
        return f"{self.generic_trait.name} → {self.generic_skill.name}: {self.get_impact_display()}"

class GenericSkill(models.Model):
    """
    Generic entity for skills.

    A model that represents a generic skill, providing a description of the skill and allowing it to be used 
    as a reference for specific user skills.

    Attributes:
        id(AutoField): 
            Example: 1
        code(CharField):
            Example: "tech_coding_python"  # Unique code following the [area]_[skill]_[level] format
        description(TextField): 
            Example: "Proficiency in Python programming."
    """
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=50, unique=True, help_text="Unique, human-readable code for this skill (e.g., 'tech_coding_python')")
    description = models.TextField(help_text="Brief description of the generic skill.")
    domains = models.ManyToManyField(
        'activity.GenericDomain', # Renamed target model
        related_name="related_skills",
        through='SkillDomainRelationship',
        help_text="Activity domains this skill applies to"
    )
    base_difficulty = ValidatedRangeField(
        default=50,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        help_text="Baseline difficulty to acquire this skill (1-100)"
    )
    development_timeframe = models.CharField(
        max_length=30,
        choices=[
            ('immediate', 'Immediate (hours/days)'),
            ('short', 'Short-term (weeks)'),
            ('medium', 'Medium-term (months)'),
            ('long', 'Long-term (years)'),
            ('lifetime', 'Lifetime (ongoing)')
        ],
        default='medium',
        help_text="Typical timeframe to develop this skill to a proficient level"
    )
    decay_rate = ValidatedRangeField(
        default=30,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="How quickly this skill deteriorates without practice (0=very slow, 100=very fast)"
    )
    prerequisites = models.ManyToManyField(
        'self',
        symmetrical=False,
        related_name='enables',
        blank=True,
        help_text="Skills that typically precede this skill"
    )
    parent_skill = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sub_skills',
        help_text="Parent skill if this is a sub-skill/specialization"
    )
    related_traits = models.ManyToManyField(
        'user.GenericTrait',
        through='SkillTraitRelationship',
        related_name='related_skills',
        help_text="Personality traits that influence skill acquisition"
    )
    
    class Meta:
        indexes = [
            models.Index(fields=['code'])
        ]

    def __str__(self):
        return self.name or self.description[:30]


class Skill(models.Model):
    """
    Represents a specific learned ability within a broader capability.

    A model representing a specific skill within a broader capability, including proficiency level, awareness, 
    enjoyment, and associated details.

    Attributes:
        user_profile(ForeignKey to UserProfile): 
            Example: "Link to a UserProfile instance"
        generic_skill(ForeignKey to GenericSkill): 
            Example: "Link to a GenericSkill instance"
        description(TextField): 
            Example: "Advanced knowledge of Python with Django experience."
        level(IntegerField): 
            Example: 90
        user_awareness(IntegerField): 
            Example: 85
        user_enjoyment(IntegerField): 
            Example: 95
        note(TextField): 
            Example: "Demonstrates expertise and passion for coding."
    """
    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name="skills")
    generic_skill = models.ForeignKey(GenericSkill, on_delete=models.CASCADE, related_name="skills")
    description = models.TextField(help_text="Brief description of the skill.")
    level = ValidatedRangeField(help_text="Proficiency level (0-100).")
    user_awareness = ValidatedRangeField(help_text="User awareness (0-100).")
    user_enjoyment = ValidatedRangeField(help_text="Enjoyment level (0-100).")
    note = models.TextField(help_text="Additional details.")
    acquisition_date = models.DateField(
        null=True, 
        blank=True,
        help_text="When the user first began developing this skill"
    )
    last_practiced = models.DateField(
        null=True,
        blank=True,
        help_text="When the user last practiced/used this skill"
    )
    practice_frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly'),
            ('quarterly', 'Quarterly'),
            ('yearly', 'Yearly'),
            ('rarely', 'Rarely'),
            ('never', 'Never')
        ],
        default='rarely',
        help_text="How often the user practices this skill"
    )
    formal_training = models.BooleanField(
        default=False,
        help_text="Whether the user received formal training in this skill"
    )
    training_details = models.TextField(
        blank=True,
        help_text="Details about training, certifications, or validation"
    )
    contexts = models.JSONField(
        default=dict,
        help_text="Contexts in which this skill is applied (e.g., {'work': 70, 'hobby': 30})"
    )
    growth_goal = ValidatedRangeField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        help_text="Target level user aims to achieve (1-100, null if no specific goal)"
    )
    stagnation_point = ValidatedRangeField(
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Level at which progress has stalled (0-100, null if actively progressing)"
    )
    
    required_by_activity = GenericRelation(
        'activity.ActivityUserRequirement',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='skill'
    )

    class Meta:
        ordering = ['user_profile', 'level']
        constraints = [
            models.UniqueConstraint(
                fields=['user_profile', 'generic_skill'],
                name='unique_user_skill'
            )
        ]

    def __str__(self):
        return f"{self.user_profile.profile_name}'s {self.generic_skill.name or self.generic_skill.description[:20]} ({self.level}/100)"
        
    def calculated_current_level(self):
        """
        Calculate effective current level accounting for skill decay since last practice.
        This provides a more realistic assessment than the stored level value.
        """
        if not self.last_practiced or not self.generic_skill.decay_rate:
            return self.level
            
        import datetime
        days_since_practice = (datetime.date.today() - self.last_practiced).days
        
        # Determine practice frequency factor
        frequency_factors = {
            'daily': 1,
            'weekly': 7,
            'monthly': 30,
            'quarterly': 90,
            'yearly': 365,
            'rarely': 180,
            'never': 365
        }
        practice_factor = frequency_factors.get(self.practice_frequency, 180)
        
        # Calculate decay based on generic skill's decay rate and time since last practice
        # Higher decay_rate = faster skill deterioration
        decay_amount = (self.generic_skill.decay_rate / 100) * (days_since_practice / practice_factor) * self.level
        
        # Limit decay to maintain at least 10% of original skill
        return max(self.level * 0.1, self.level - decay_amount)


class GenericUserLimitation(models.Model):
    """
    Defines common properties for user limitations.

    A model that defines common properties of limitations that apply to users, 
    such as types of limitations (e.g., physical, cognitive) and their descriptions.

    Attributes:
        id(AutoField): 
            Example: 1
        code(CharField):
            Example: "phys_mobility_arms"  # Unique code following the [type]_[constraint]_[scope] format
        description(TextField): 
            Example: "Limited physical endurance for prolonged exercise."
        limitation_type(CharField): 
            Example: "PHYSICAL"
    """
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=50, unique=True, help_text="Unique, human-readable code for this limitation (e.g., 'phys_mobility_arms')")
    description = models.TextField(help_text="Description of the limitation.")
    limitation_type = models.CharField(max_length=50, help_text="Type/category of the limitation (e.g., 'PHYSICAL').")

    class Meta:
        ordering = ['limitation_type']
        indexes = [
            models.Index(fields=['code']),  # Add index for faster lookups by code
        ]

    def __str__(self):
        return self.limitation_type


class UserLimitation(TemporalRecord):
    """
    Represents constraints or challenges the user faces.

    A model that tracks limitations or constraints faced by the user, such as physical or cognitive limitations, 
    including severity, review dates, and awareness of the limitation.

    Attributes:
        user_profile(ForeignKey to UserProfile): 
            Example: "Link to a UserProfile instance"
        generic_limitation(ForeignKey to GenericUserLimitation): 
            Example: "Link to a GenericUserLimitation instance"
        severity(IntegerField): 
            Example: 75
        valid_until(DateField): 
            Example: "2025-05-01"
        is_unlimited(BooleanField): 
            Example: False
        user_awareness(IntegerField): 
            Example: 80
    """
    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name="limitations")
    generic_limitation = models.ForeignKey(GenericUserLimitation, on_delete=models.CASCADE, related_name="user_limitations")
    severity = ValidatedRangeField(help_text="Impact measure (0-100).")
    valid_until = models.DateField(help_text="Expiry or review date.")
    is_unlimited = models.BooleanField(default=False, help_text="Indicates if the limitation is absolute.")
    user_awareness = ValidatedRangeField(help_text="Awareness level (0-100).")
    required_by_activity = GenericRelation(
        'activity.ActivityUserRequirement',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='user_limitation'
    )

    class Meta:
        ordering = ['user_profile']

    def __str__(self):
        return f"Limitation: {self.generic_limitation.description[:30]}"


###############################################################################
# Skill Composition System
###############################################################################

from django.db import models
# ArrayField import was removed as it's incompatible with SQLite tests, but we use postgres now
# from django.contrib.postgres.fields import ArrayField 
from django.utils.text import slugify
from apps.common.fields import ValidatedRangeField


class SkillAttribute(models.Model):
    """
    Fundamental component that can be combined to form skills.
    """
    code = models.CharField(
        max_length=50, 
        unique=True, 
        help_text="Unique identifier for this attribute (e.g., 'spatial_reasoning')"
    )
    name = models.CharField(
        max_length=100, 
        help_text="Human-readable name for this attribute"
    )
    description = models.TextField(
        help_text="Detailed explanation of this attribute and how it manifests"
    )
    
    # Core properties
    base_decay_rate = ValidatedRangeField(
        default=30,
        help_text="Base rate at which this attribute deteriorates without practice (0-100)"
    )
    development_difficulty = ValidatedRangeField(
        default=50,
        help_text="Inherent difficulty in developing this attribute (1-100)"
    )
    development_timeframe = models.CharField(
        max_length=30,
        choices=[
            ('immediate', 'Immediate (hours/days)'),
            ('short', 'Short-term (weeks)'),
            ('medium', 'Medium-term (months)'),
            ('long', 'Long-term (years)'),
            ('lifetime', 'Lifetime (ongoing)')
        ],
        default='medium',
        help_text="Typical timeframe to develop this attribute to a proficient level"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['development_difficulty']),
        ]
        verbose_name = "Skill Attribute"
        verbose_name_plural = "Skill Attributes"
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.code:
            self.code = slugify(self.name)
        super().save(*args, **kwargs)


class AttributeTraitInfluence(models.Model):
    """
    Defines how a personality trait influences a skill attribute.
    """
    class ImpactType(models.IntegerChoices):
        STRONGLY_HINDERING = -3, 'Strongly Hindering'
        MODERATELY_HINDERING = -2, 'Moderately Hindering'
        SLIGHTLY_HINDERING = -1, 'Slightly Hindering'
        NEUTRAL = 0, 'Neutral'
        SLIGHTLY_BENEFICIAL = 1, 'Slightly Beneficial'
        MODERATELY_BENEFICIAL = 2, 'Moderately Beneficial'
        STRONGLY_BENEFICIAL = 3, 'Strongly Beneficial'
    
    attribute = models.ForeignKey(
        SkillAttribute, 
        on_delete=models.CASCADE,
        related_name='trait_influences'
    )
    generic_trait = models.ForeignKey(
        'user.GenericTrait',
        on_delete=models.CASCADE,
        related_name='attribute_influences'
    )
    impact = models.IntegerField(
        choices=ImpactType.choices,
        default=ImpactType.NEUTRAL,
        help_text="How this trait affects learning/applying this attribute"
    )
    notes = models.TextField(
        blank=True,
        help_text="Explanation of why/how this trait influences this attribute"
    )
    
    class Meta:
        unique_together = ['attribute', 'generic_trait']
        verbose_name = "Attribute-Trait Influence"
        verbose_name_plural = "Attribute-Trait Influences"
    
    def __str__(self):
        return f"{self.generic_trait.name} → {self.attribute.name}: {self.get_impact_display()}"


class SkillDefinition(models.Model):
    """
    A skill defined as a composition of attributes with domain applications.
    """
    code = models.CharField(
        max_length=50, 
        unique=True, 
        help_text="Unique identifier for this skill"
    )
    name = models.CharField(
        max_length=100, 
        help_text="Human-readable name for this skill"
    )
    description = models.TextField(
        help_text="Detailed explanation of this skill and its applications"
    )
    
    # Tags for flexible categorization
    # Changed from ArrayField to JSONField for SQLite compatibility during tests.
    # NOTE: Production likely uses ArrayField with PostgreSQL. Test DB should eventually match.
    tags = models.JSONField(
        default=list, 
        blank=True, 
        help_text="Flexible tags (stored as JSON list for SQLite tests)"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['code']),
            # Removed index on 'tags' (JSONField) as it likely causes SQLite incompatibility during test migrations
            # models.Index(fields=['tags'], name='skill_tags_idx'),
        ]
        verbose_name = "Skill Definition"
        verbose_name_plural = "Skill Definitions"
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.code:
            self.code = slugify(self.name)
        super().save(*args, **kwargs)
    
    @property
    def primary_attributes(self):
        """Returns the top 3 attributes by weight that compose this skill"""
        return [comp.attribute for comp in 
                self.attribute_compositions.order_by('-weight')[:3]]


class SkillAttributeComposition(models.Model):
    """
    Defines how attributes combine to form a skill with specific weights.
    """
    skill = models.ForeignKey(
        SkillDefinition,
        on_delete=models.CASCADE,
        related_name='attribute_compositions'
    )
    attribute = models.ForeignKey(
        SkillAttribute,
        on_delete=models.CASCADE,
        related_name='skill_usages'
    )
    weight = models.FloatField(
        default=1.0,
        validators= [MinValueValidator(0.1), MaxValueValidator(10.0)],
        help_text="Relative importance of this attribute in the skill (higher = more important)"
    )
    
    class Meta:
        unique_together = ['skill', 'attribute']
        verbose_name = "Skill-Attribute Composition"
        verbose_name_plural = "Skill-Attribute Compositions"
    
    def __str__(self):
        return f"{self.skill.name}: {self.attribute.name} ({self.weight})"


class SkillDomainApplication(models.Model):
    """
    Defines how a skill applies to a specific domain with contextual parameters.
    """
    class RelevanceLevel(models.IntegerChoices):
        PERIPHERAL = 10, 'Peripheral'
        SUPPORTIVE = 30, 'Supportive'
        IMPORTANT = 70, 'Important'
        CRITICAL = 100, 'Critical'
    
    skill = models.ForeignKey(
        SkillDefinition,
        on_delete=models.CASCADE,
        related_name='domain_applications'
    )
    domain = models.ForeignKey(
        'activity.GenericDomain', # Renamed target model
        on_delete=models.CASCADE,
        related_name='skill_applications'
    )
    relevance = models.IntegerField(
        choices=RelevanceLevel.choices,
        default=RelevanceLevel.IMPORTANT,
        help_text="How relevant this skill is to activities in this domain"
    )
    transfer_coefficient = models.FloatField(
        default=1.0,
        validators=[MinValueValidator(0.1), MaxValueValidator(2.0)],
        help_text="Effectiveness multiplier in this domain context"
    )
    domain_specific_properties = models.JSONField(
        default=dict,
        blank=True,
        help_text="Domain-specific parameters like decay rates, context modifiers, etc."
    )
    
    class Meta:
        unique_together = ['skill', 'domain']
        verbose_name = "Skill-Domain Application"
        verbose_name_plural = "Skill-Domain Applications"
    
    def __str__(self):
        return f"{self.skill.name} in {self.domain.name}: {self.get_relevance_display()}"


class UserAttributeProficiency(models.Model):
    """
    Tracks a user's proficiency in specific skill attributes.
    """
    user_profile = models.ForeignKey(
        'user.UserProfile',
        on_delete=models.CASCADE,
        related_name='attribute_proficiencies'
    )
    attribute = models.ForeignKey(
        SkillAttribute,
        on_delete=models.CASCADE,
        related_name='user_proficiencies'
    )
    level = ValidatedRangeField(
        default=0,
        help_text="User's proficiency level with this attribute (0-100)"
    )
    user_awareness = ValidatedRangeField(
        default=50,
        help_text="User's awareness of their proficiency in this attribute (0-100)"
    )
    last_practiced = models.DateField(
        null=True,
        blank=True,
        help_text="When the user last practiced this attribute"
    )
    practice_frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly'),
            ('quarterly', 'Quarterly'),
            ('yearly', 'Yearly'),
            ('rarely', 'Rarely'),
            ('never', 'Never')
        ],
        default='rarely',
        help_text="How often the user practices this attribute"
    )
    formal_training = models.BooleanField(
        default=False,
        help_text="Whether the user has received formal training for this attribute"
    )
    notes = models.TextField(
        blank=True,
        help_text="Additional notes on the user's experience with this attribute"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user_profile', 'attribute']
        verbose_name = "User Attribute Proficiency"
        verbose_name_plural = "User Attribute Proficiencies"
    
    def __str__(self):
        return f"{self.user_profile.profile_name}'s {self.attribute.name}: {self.level}"
    
    @property
    def effective_level(self):
        """
        Calculate effective level accounting for decay since last practice.
        Simplified calculation for display purposes.
        """
        from apps.user.services.skill_service import SkillService
        return SkillService.calculate_effective_attribute_level(self)
