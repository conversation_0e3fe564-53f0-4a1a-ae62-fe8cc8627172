# Generated by Django 5.2 on 2025-04-17 19:31

import apps.common.fields
import django.core.validators
import django.db.models.deletion
import djmoney.models.fields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('activity', '0001_initial'),
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserGoal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='A short descriptive title for the goal.', max_length=255)),
                ('description', models.TextField(help_text='Detailed explanation of the goal.')),
                ('importance_according_user', apps.common.fields.ValidatedRangeField(help_text='Importance as perceived by the user (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('importance_according_system', apps.common.fields.ValidatedRangeField(help_text="System's evaluation of importance (0-100).", validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('strength', apps.common.fields.ValidatedRangeField(help_text='Current momentum (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Belief',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(help_text='Core statement or proposition.')),
                ('last_updated', models.DateField(help_text='Date of the most recent update.')),
                ('user_confidence', apps.common.fields.ValidatedRangeField(help_text="User's personal conviction (0-100).", validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('system_confidence', apps.common.fields.ValidatedRangeField(help_text="System's evaluation (0-100).", validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('emotionality', apps.common.fields.ExtendedRangeField(help_text='Emotional charge (-100 to 100).', validators=[django.core.validators.MinValueValidator(-100), django.core.validators.MaxValueValidator(100)])),
                ('stability', apps.common.fields.ValidatedRangeField(help_text='Resistance to change (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('user_awareness', apps.common.fields.ValidatedRangeField(help_text='Awareness level (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
            ],
            options={
                'ordering': ['-last_updated'],
            },
        ),
        migrations.CreateModel(
            name='GenericBelief',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text="Unique code like 'self_efficacy_general'", max_length=50, unique=True)),
                ('category', models.CharField(choices=[('SELF_WORTH', 'Self-Worth and Fundamental Value'), ('SELF_EFFICACY', 'Personal Capability and Competence'), ('IDENTITY', 'Core Identity and Purpose'), ('POTENTIAL', 'Personal Growth and Potential'), ('SKILL_LEARN', 'Learning and Skill Acquisition'), ('TALENT', 'Innate Talents and Limitations'), ('FEAR', 'Fundamental Fears and Anxieties'), ('AVOIDANCE', 'Coping and Avoidance Mechanisms'), ('TRAUMA', 'Trauma-Informed Belief Patterns'), ('SOCIAL', 'Interpersonal Relationships'), ('BELONGING', 'Sense of Belonging and Acceptance'), ('TRUST', 'Trust in Self and Others'), ('MEANING', 'Life Purpose and Meaning'), ('WORLDVIEW', 'Fundamental Worldview'), ('CHANGE', 'Attitude Towards Change and Uncertainty'), ('SUCCESS', 'Personal Definition of Success'), ('GOAL', 'Goal Attainment and Motivation'), ('EFFORT', 'Beliefs About Effort and Reward'), ('EMOTION_CONTROL', 'Emotional Management'), ('VULNERABILITY', 'Openness to Emotional Vulnerability'), ('RESILIENCE', 'Psychological Resilience')], help_text="Category of belief (e.g., 'SELF_EFFICACY', 'FEAR')", max_length=50)),
                ('name', models.CharField(help_text='Name of the belief pattern', max_length=255)),
                ('description', models.TextField(help_text='Description of this belief type and how it affects behavior')),
                ('typical_stability', apps.common.fields.ValidatedRangeField(default=50, help_text='How resistant this belief type typically is to change', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
            ],
            options={
                'verbose_name': 'Generic Belief',
                'verbose_name_plural': 'Generic Beliefs',
            },
        ),
        migrations.CreateModel(
            name='GenericEnvironment',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text="Unique, human-readable code for this environment (e.g., 'ind_home')", max_length=50, unique=True)),
                ('name', models.CharField(help_text='Name of the archetypal environment.', max_length=255)),
                ('description', models.TextField(help_text='General description of this environment type.')),
                ('is_active', models.BooleanField(default=True, help_text='Flag indicating if this environment type is available for selection.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_indoor', models.BooleanField(default=True, help_text='Whether the environment is typically indoors or outdoors')),
                ('primary_category', models.CharField(choices=[('residential', 'Residential'), ('professional', 'Professional'), ('educational', 'Educational'), ('commercial', 'Commercial'), ('recreational', 'Recreational'), ('natural', 'Natural'), ('transportation', 'Transportation'), ('cultural', 'Cultural'), ('healthcare', 'Healthcare'), ('other', 'Other')], help_text='Primary category of the environment', max_length=20)),
                ('typical_space_size', models.CharField(choices=[('tiny', 'Tiny'), ('small', 'Small'), ('medium', 'Medium'), ('large', 'Large'), ('vast', 'Vast')], default='medium', help_text='Typical size classification of this environment type', max_length=10)),
                ('typical_privacy_level', apps.common.fields.ValidatedRangeField(default=50, help_text='Typical privacy level: Public (0) to extremely private (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('archetype_attributes', models.JSONField(default=dict, help_text='Additional attributes typical of this environment type')),
            ],
            options={
                'verbose_name': 'Generic Environment',
                'verbose_name_plural': 'Generic Environments',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Inspiration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source', models.CharField(help_text="Origin of the inspiration (e.g., 'TED Talk').", max_length=255)),
                ('description', models.TextField(help_text='Detailed description of the inspirational content.')),
                ('strength', apps.common.fields.ValidatedRangeField(help_text='Inspirational impact rating (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('reference_url', models.URLField(help_text='URL pointing to the source material.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['source'],
            },
        ),
        migrations.CreateModel(
            name='Aspiration',
            fields=[
                ('usergoal_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='user.usergoal')),
                ('effective_start', models.DateField(help_text='Start date when the record becomes active.')),
                ('duration_estimate', models.CharField(help_text="Estimated active period (e.g., '30 days').", max_length=50)),
                ('effective_end', models.DateField(help_text='End date when the record ceases to be active.')),
                ('domain', models.CharField(help_text="Field or area of the aspiration (e.g., 'Career').", max_length=255)),
                ('horizon', models.CharField(help_text="Time frame of the aspiration (e.g., 'Long-term').", max_length=255)),
                ('level_of_ambition', models.CharField(help_text="Descriptor indicating intensity (e.g., 'High').", max_length=50)),
            ],
            options={
                'abstract': False,
            },
            bases=('user.usergoal', models.Model),
        ),
        migrations.CreateModel(
            name='Intention',
            fields=[
                ('usergoal_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='user.usergoal')),
                ('effective_start', models.DateField(help_text='Start date when the record becomes active.')),
                ('duration_estimate', models.CharField(help_text="Estimated active period (e.g., '30 days').", max_length=50)),
                ('effective_end', models.DateField(help_text='End date when the record ceases to be active.')),
                ('start_date', models.DateField(help_text='Date when the intention starts.')),
                ('due_date', models.DateField(help_text='Target completion date.')),
                ('is_completed', models.BooleanField(default=False, help_text='Indicates whether the intention is fulfilled.')),
                ('progress_notes', models.TextField(help_text='Ongoing notes or reflections on progress.')),
            ],
            options={
                'abstract': False,
            },
            bases=('user.usergoal', models.Model),
        ),
        migrations.CreateModel(
            name='BeliefEvidence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('evidence_type', models.CharField(help_text="Category of evidence (e.g., 'EXPERIENCE').", max_length=100)),
                ('description', models.TextField(help_text='Detailed description including context.')),
                ('credibility_score', models.FloatField(help_text='Reliability measure of the evidence.')),
                ('source', models.CharField(help_text='Origin of the evidence.', max_length=255)),
                ('belief', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evidences', to='user.belief')),
            ],
            options={
                'ordering': ['belief'],
            },
        ),
        migrations.AddField(
            model_name='belief',
            name='generic_belief',
            field=models.ForeignKey(blank=True, help_text='Reference to the generic belief template this belief is based on', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_beliefs', to='user.genericbelief'),
        ),
        migrations.CreateModel(
            name='GenericBeliefDomainRelationship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('impact', models.IntegerField(choices=[(-3, 'Strongly Limiting'), (-2, 'Moderately Limiting'), (-1, 'Slightly Limiting'), (0, 'Neutral'), (1, 'Slightly Supportive'), (2, 'Moderately Supportive'), (3, 'Strongly Supportive')], default=0, help_text='How this belief affects activities in this domain')),
                ('notes', models.TextField(blank=True, help_text='Explanation of how/why this belief affects activities in this domain')),
                ('domain', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='belief_relationships', to='activity.genericdomain')),
                ('generic_belief', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='domain_relationships_details', to='user.genericbelief')),
            ],
            options={
                'verbose_name': 'Belief-Domain Relationship',
                'verbose_name_plural': 'Belief-Domain Relationships',
                'unique_together': {('generic_belief', 'domain')},
            },
        ),
        migrations.AddField(
            model_name='genericbelief',
            name='domains',
            field=models.ManyToManyField(help_text='Activity domains this belief affects', related_name='related_beliefs', through='user.GenericBeliefDomainRelationship', to='activity.genericdomain'),
        ),
        migrations.CreateModel(
            name='GenericEnvironmentDomainRelationship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('strength', models.SmallIntegerField(choices=[(-100, 'Strongly Discouraged'), (-70, 'Discouraged'), (-30, 'Slightly Discouraged'), (0, 'Neutral'), (10, 'Minimal'), (30, 'Moderate'), (70, 'Significant'), (100, 'Perfect')], default=0, help_text='How this environment affects activities in this domain (-100 to 100). Negative values indicate discouragement.')),
                ('domain', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='environment_relationships', to='activity.genericdomain')),
                ('generic_environment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='domain_relationships_details', to='user.genericenvironment')),
            ],
            options={
                'verbose_name': 'Environment-Domain Relationship',
                'verbose_name_plural': 'Environment-Domain Relationships',
            },
        ),
        migrations.AddField(
            model_name='genericenvironment',
            name='domain_relationships',
            field=models.ManyToManyField(help_text='Associated domains with relationship strength', related_name='related_environments', through='user.GenericEnvironmentDomainRelationship', to='activity.genericdomain'),
        ),
        migrations.CreateModel(
            name='GenericResource',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text="Unique, human-readable code for this resource (e.g., 'equip_yoga_mat')", max_length=50, unique=True)),
                ('resource_type', models.CharField(help_text="Category of the resource (e.g., 'sport equipment').", max_length=255)),
                ('description', models.TextField(help_text='Brief explanation of the resource.')),
                ('op_cost_currency', djmoney.models.fields.CurrencyField(choices=[('XUA', 'ADB Unit of Account'), ('AFN', 'Afghan Afghani'), ('AFA', 'Afghan Afghani (1927–2002)'), ('ALL', 'Albanian Lek'), ('ALK', 'Albanian Lek (1946–1965)'), ('DZD', 'Algerian Dinar'), ('ADP', 'Andorran Peseta'), ('AOA', 'Angolan Kwanza'), ('AOK', 'Angolan Kwanza (1977–1991)'), ('AON', 'Angolan New Kwanza (1990–2000)'), ('AOR', 'Angolan Readjusted Kwanza (1995–1999)'), ('ARA', 'Argentine Austral'), ('ARS', 'Argentine Peso'), ('ARM', 'Argentine Peso (1881–1970)'), ('ARP', 'Argentine Peso (1983–1985)'), ('ARL', 'Argentine Peso Ley (1970–1983)'), ('AMD', 'Armenian Dram'), ('AWG', 'Aruban Florin'), ('AUD', 'Australian Dollar'), ('ATS', 'Austrian Schilling'), ('AZN', 'Azerbaijani Manat'), ('AZM', 'Azerbaijani Manat (1993–2006)'), ('BSD', 'Bahamian Dollar'), ('BHD', 'Bahraini Dinar'), ('BDT', 'Bangladeshi Taka'), ('BBD', 'Barbadian Dollar'), ('BYN', 'Belarusian Ruble'), ('BYB', 'Belarusian Ruble (1994–1999)'), ('BYR', 'Belarusian Ruble (2000–2016)'), ('BEF', 'Belgian Franc'), ('BEC', 'Belgian Franc (convertible)'), ('BEL', 'Belgian Franc (financial)'), ('BZD', 'Belize Dollar'), ('BMD', 'Bermudan Dollar'), ('BTN', 'Bhutanese Ngultrum'), ('BOB', 'Bolivian Boliviano'), ('BOL', 'Bolivian Boliviano (1863–1963)'), ('BOV', 'Bolivian Mvdol'), ('BOP', 'Bolivian Peso'), ('VED', 'Bolívar Soberano'), ('BAM', 'Bosnia-Herzegovina Convertible Mark'), ('BAD', 'Bosnia-Herzegovina Dinar (1992–1994)'), ('BAN', 'Bosnia-Herzegovina New Dinar (1994–1997)'), ('BWP', 'Botswanan Pula'), ('BRC', 'Brazilian Cruzado (1986–1989)'), ('BRZ', 'Brazilian Cruzeiro (1942–1967)'), ('BRE', 'Brazilian Cruzeiro (1990–1993)'), ('BRR', 'Brazilian Cruzeiro (1993–1994)'), ('BRN', 'Brazilian New Cruzado (1989–1990)'), ('BRB', 'Brazilian New Cruzeiro (1967–1986)'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('BND', 'Brunei Dollar'), ('BGL', 'Bulgarian Hard Lev'), ('BGN', 'Bulgarian Lev'), ('BGO', 'Bulgarian Lev (1879–1952)'), ('BGM', 'Bulgarian Socialist Lev'), ('BUK', 'Burmese Kyat'), ('BIF', 'Burundian Franc'), ('XPF', 'CFP Franc'), ('KHR', 'Cambodian Riel'), ('CAD', 'Canadian Dollar'), ('CVE', 'Cape Verdean Escudo'), ('KYD', 'Cayman Islands Dollar'), ('XAF', 'Central African CFA Franc'), ('CLE', 'Chilean Escudo'), ('CLP', 'Chilean Peso'), ('CLF', 'Chilean Unit of Account (UF)'), ('CNX', 'Chinese People’s Bank Dollar'), ('CNY', 'Chinese Yuan'), ('CNH', 'Chinese Yuan (offshore)'), ('COP', 'Colombian Peso'), ('COU', 'Colombian Real Value Unit'), ('KMF', 'Comorian Franc'), ('CDF', 'Congolese Franc'), ('CRC', 'Costa Rican Colón'), ('HRD', 'Croatian Dinar'), ('HRK', 'Croatian Kuna'), ('CUC', 'Cuban Convertible Peso'), ('CUP', 'Cuban Peso'), ('CYP', 'Cypriot Pound'), ('CZK', 'Czech Koruna'), ('CSK', 'Czechoslovak Hard Koruna'), ('DKK', 'Danish Krone'), ('DJF', 'Djiboutian Franc'), ('DOP', 'Dominican Peso'), ('NLG', 'Dutch Guilder'), ('XCD', 'East Caribbean Dollar'), ('DDM', 'East German Mark'), ('ECS', 'Ecuadorian Sucre'), ('ECV', 'Ecuadorian Unit of Constant Value'), ('EGP', 'Egyptian Pound'), ('GQE', 'Equatorial Guinean Ekwele'), ('ERN', 'Eritrean Nakfa'), ('EEK', 'Estonian Kroon'), ('ETB', 'Ethiopian Birr'), ('EUR', 'Euro'), ('XBA', 'European Composite Unit'), ('XEU', 'European Currency Unit'), ('XBB', 'European Monetary Unit'), ('XBC', 'European Unit of Account (XBC)'), ('XBD', 'European Unit of Account (XBD)'), ('FKP', 'Falkland Islands Pound'), ('FJD', 'Fijian Dollar'), ('FIM', 'Finnish Markka'), ('FRF', 'French Franc'), ('XFO', 'French Gold Franc'), ('XFU', 'French UIC-Franc'), ('GMD', 'Gambian Dalasi'), ('GEK', 'Georgian Kupon Larit'), ('GEL', 'Georgian Lari'), ('DEM', 'German Mark'), ('GHS', 'Ghanaian Cedi'), ('GHC', 'Ghanaian Cedi (1979–2007)'), ('GIP', 'Gibraltar Pound'), ('XAU', 'Gold'), ('GRD', 'Greek Drachma'), ('GTQ', 'Guatemalan Quetzal'), ('GWP', 'Guinea-Bissau Peso'), ('GNF', 'Guinean Franc'), ('GNS', 'Guinean Syli'), ('GYD', 'Guyanaese Dollar'), ('HTG', 'Haitian Gourde'), ('HNL', 'Honduran Lempira'), ('HKD', 'Hong Kong Dollar'), ('HUF', 'Hungarian Forint'), ('IMP', 'IMP'), ('ISK', 'Icelandic Króna'), ('ISJ', 'Icelandic Króna (1918–1981)'), ('INR', 'Indian Rupee'), ('IDR', 'Indonesian Rupiah'), ('IRR', 'Iranian Rial'), ('IQD', 'Iraqi Dinar'), ('IEP', 'Irish Pound'), ('ILS', 'Israeli New Shekel'), ('ILP', 'Israeli Pound'), ('ILR', 'Israeli Shekel (1980–1985)'), ('ITL', 'Italian Lira'), ('JMD', 'Jamaican Dollar'), ('JPY', 'Japanese Yen'), ('JOD', 'Jordanian Dinar'), ('KZT', 'Kazakhstani Tenge'), ('KES', 'Kenyan Shilling'), ('KWD', 'Kuwaiti Dinar'), ('KGS', 'Kyrgystani Som'), ('LAK', 'Laotian Kip'), ('LVL', 'Latvian Lats'), ('LVR', 'Latvian Ruble'), ('LBP', 'Lebanese Pound'), ('LSL', 'Lesotho Loti'), ('LRD', 'Liberian Dollar'), ('LYD', 'Libyan Dinar'), ('LTL', 'Lithuanian Litas'), ('LTT', 'Lithuanian Talonas'), ('LUL', 'Luxembourg Financial Franc'), ('LUC', 'Luxembourgian Convertible Franc'), ('LUF', 'Luxembourgian Franc'), ('MOP', 'Macanese Pataca'), ('MKD', 'Macedonian Denar'), ('MKN', 'Macedonian Denar (1992–1993)'), ('MGA', 'Malagasy Ariary'), ('MGF', 'Malagasy Franc'), ('MWK', 'Malawian Kwacha'), ('MYR', 'Malaysian Ringgit'), ('MVR', 'Maldivian Rufiyaa'), ('MVP', 'Maldivian Rupee (1947–1981)'), ('MLF', 'Malian Franc'), ('MTL', 'Maltese Lira'), ('MTP', 'Maltese Pound'), ('MRU', 'Mauritanian Ouguiya'), ('MRO', 'Mauritanian Ouguiya (1973–2017)'), ('MUR', 'Mauritian Rupee'), ('MXV', 'Mexican Investment Unit'), ('MXN', 'Mexican Peso'), ('MXP', 'Mexican Silver Peso (1861–1992)'), ('MDC', 'Moldovan Cupon'), ('MDL', 'Moldovan Leu'), ('MCF', 'Monegasque Franc'), ('MNT', 'Mongolian Tugrik'), ('MAD', 'Moroccan Dirham'), ('MAF', 'Moroccan Franc'), ('MZE', 'Mozambican Escudo'), ('MZN', 'Mozambican Metical'), ('MZM', 'Mozambican Metical (1980–2006)'), ('MMK', 'Myanmar Kyat'), ('NAD', 'Namibian Dollar'), ('NPR', 'Nepalese Rupee'), ('ANG', 'Netherlands Antillean Guilder'), ('TWD', 'New Taiwan Dollar'), ('NZD', 'New Zealand Dollar'), ('NIO', 'Nicaraguan Córdoba'), ('NIC', 'Nicaraguan Córdoba (1988–1991)'), ('NGN', 'Nigerian Naira'), ('KPW', 'North Korean Won'), ('NOK', 'Norwegian Krone'), ('OMR', 'Omani Rial'), ('PKR', 'Pakistani Rupee'), ('XPD', 'Palladium'), ('PAB', 'Panamanian Balboa'), ('PGK', 'Papua New Guinean Kina'), ('PYG', 'Paraguayan Guarani'), ('PEI', 'Peruvian Inti'), ('PEN', 'Peruvian Sol'), ('PES', 'Peruvian Sol (1863–1965)'), ('PHP', 'Philippine Peso'), ('XPT', 'Platinum'), ('PLN', 'Polish Zloty'), ('PLZ', 'Polish Zloty (1950–1995)'), ('PTE', 'Portuguese Escudo'), ('GWE', 'Portuguese Guinea Escudo'), ('QAR', 'Qatari Riyal'), ('XRE', 'RINET Funds'), ('RHD', 'Rhodesian Dollar'), ('RON', 'Romanian Leu'), ('ROL', 'Romanian Leu (1952–2006)'), ('RUB', 'Russian Ruble'), ('RUR', 'Russian Ruble (1991–1998)'), ('RWF', 'Rwandan Franc'), ('SVC', 'Salvadoran Colón'), ('WST', 'Samoan Tala'), ('SAR', 'Saudi Riyal'), ('RSD', 'Serbian Dinar'), ('CSD', 'Serbian Dinar (2002–2006)'), ('SCR', 'Seychellois Rupee'), ('SLE', 'Sierra Leonean Leone'), ('SLL', 'Sierra Leonean Leone (1964—2022)'), ('XAG', 'Silver'), ('SGD', 'Singapore Dollar'), ('SKK', 'Slovak Koruna'), ('SIT', 'Slovenian Tolar'), ('SBD', 'Solomon Islands Dollar'), ('SOS', 'Somali Shilling'), ('ZAR', 'South African Rand'), ('ZAL', 'South African Rand (financial)'), ('KRH', 'South Korean Hwan (1953–1962)'), ('KRW', 'South Korean Won'), ('KRO', 'South Korean Won (1945–1953)'), ('SSP', 'South Sudanese Pound'), ('SUR', 'Soviet Rouble'), ('ESP', 'Spanish Peseta'), ('ESA', 'Spanish Peseta (A account)'), ('ESB', 'Spanish Peseta (convertible account)'), ('XDR', 'Special Drawing Rights'), ('LKR', 'Sri Lankan Rupee'), ('SHP', 'St. Helena Pound'), ('XSU', 'Sucre'), ('SDD', 'Sudanese Dinar (1992–2007)'), ('SDG', 'Sudanese Pound'), ('SDP', 'Sudanese Pound (1957–1998)'), ('SRD', 'Surinamese Dollar'), ('SRG', 'Surinamese Guilder'), ('SZL', 'Swazi Lilangeni'), ('SEK', 'Swedish Krona'), ('CHF', 'Swiss Franc'), ('SYP', 'Syrian Pound'), ('STN', 'São Tomé & Príncipe Dobra'), ('STD', 'São Tomé & Príncipe Dobra (1977–2017)'), ('TVD', 'TVD'), ('TJR', 'Tajikistani Ruble'), ('TJS', 'Tajikistani Somoni'), ('TZS', 'Tanzanian Shilling'), ('XTS', 'Testing Currency Code'), ('THB', 'Thai Baht'), ('TPE', 'Timorese Escudo'), ('TOP', 'Tongan Paʻanga'), ('TTD', 'Trinidad & Tobago Dollar'), ('TND', 'Tunisian Dinar'), ('TRY', 'Turkish Lira'), ('TRL', 'Turkish Lira (1922–2005)'), ('TMT', 'Turkmenistani Manat'), ('TMM', 'Turkmenistani Manat (1993–2009)'), ('USD', 'US Dollar'), ('USN', 'US Dollar (Next day)'), ('USS', 'US Dollar (Same day)'), ('UGX', 'Ugandan Shilling'), ('UGS', 'Ugandan Shilling (1966–1987)'), ('UAH', 'Ukrainian Hryvnia'), ('UAK', 'Ukrainian Karbovanets'), ('AED', 'United Arab Emirates Dirham'), ('UYW', 'Uruguayan Nominal Wage Index Unit'), ('UYU', 'Uruguayan Peso'), ('UYP', 'Uruguayan Peso (1975–1993)'), ('UYI', 'Uruguayan Peso (Indexed Units)'), ('UZS', 'Uzbekistani Som'), ('VUV', 'Vanuatu Vatu'), ('VES', 'Venezuelan Bolívar'), ('VEB', 'Venezuelan Bolívar (1871–2008)'), ('VEF', 'Venezuelan Bolívar (2008–2018)'), ('VND', 'Vietnamese Dong'), ('VNN', 'Vietnamese Dong (1978–1985)'), ('CHE', 'WIR Euro'), ('CHW', 'WIR Franc'), ('XOF', 'West African CFA Franc'), ('YDD', 'Yemeni Dinar'), ('YER', 'Yemeni Rial'), ('YUN', 'Yugoslavian Convertible Dinar (1990–1992)'), ('YUD', 'Yugoslavian Hard Dinar (1966–1990)'), ('YUM', 'Yugoslavian New Dinar (1994–2002)'), ('YUR', 'Yugoslavian Reformed Dinar (1992–1993)'), ('ZWN', 'ZWN'), ('ZRN', 'Zairean New Zaire (1993–1998)'), ('ZRZ', 'Zairean Zaire (1971–1993)'), ('ZMW', 'Zambian Kwacha'), ('ZMK', 'Zambian Kwacha (1968–2012)'), ('ZWD', 'Zimbabwean Dollar (1980–2008)'), ('ZWR', 'Zimbabwean Dollar (2008)'), ('ZWL', 'Zimbabwean Dollar (2009–2024)')], default='USD', editable=False, max_length=3)),
                ('op_cost', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='USD', help_text='Operational cost', max_digits=10)),
                ('acq_cost_currency', djmoney.models.fields.CurrencyField(choices=[('XUA', 'ADB Unit of Account'), ('AFN', 'Afghan Afghani'), ('AFA', 'Afghan Afghani (1927–2002)'), ('ALL', 'Albanian Lek'), ('ALK', 'Albanian Lek (1946–1965)'), ('DZD', 'Algerian Dinar'), ('ADP', 'Andorran Peseta'), ('AOA', 'Angolan Kwanza'), ('AOK', 'Angolan Kwanza (1977–1991)'), ('AON', 'Angolan New Kwanza (1990–2000)'), ('AOR', 'Angolan Readjusted Kwanza (1995–1999)'), ('ARA', 'Argentine Austral'), ('ARS', 'Argentine Peso'), ('ARM', 'Argentine Peso (1881–1970)'), ('ARP', 'Argentine Peso (1983–1985)'), ('ARL', 'Argentine Peso Ley (1970–1983)'), ('AMD', 'Armenian Dram'), ('AWG', 'Aruban Florin'), ('AUD', 'Australian Dollar'), ('ATS', 'Austrian Schilling'), ('AZN', 'Azerbaijani Manat'), ('AZM', 'Azerbaijani Manat (1993–2006)'), ('BSD', 'Bahamian Dollar'), ('BHD', 'Bahraini Dinar'), ('BDT', 'Bangladeshi Taka'), ('BBD', 'Barbadian Dollar'), ('BYN', 'Belarusian Ruble'), ('BYB', 'Belarusian Ruble (1994–1999)'), ('BYR', 'Belarusian Ruble (2000–2016)'), ('BEF', 'Belgian Franc'), ('BEC', 'Belgian Franc (convertible)'), ('BEL', 'Belgian Franc (financial)'), ('BZD', 'Belize Dollar'), ('BMD', 'Bermudan Dollar'), ('BTN', 'Bhutanese Ngultrum'), ('BOB', 'Bolivian Boliviano'), ('BOL', 'Bolivian Boliviano (1863–1963)'), ('BOV', 'Bolivian Mvdol'), ('BOP', 'Bolivian Peso'), ('VED', 'Bolívar Soberano'), ('BAM', 'Bosnia-Herzegovina Convertible Mark'), ('BAD', 'Bosnia-Herzegovina Dinar (1992–1994)'), ('BAN', 'Bosnia-Herzegovina New Dinar (1994–1997)'), ('BWP', 'Botswanan Pula'), ('BRC', 'Brazilian Cruzado (1986–1989)'), ('BRZ', 'Brazilian Cruzeiro (1942–1967)'), ('BRE', 'Brazilian Cruzeiro (1990–1993)'), ('BRR', 'Brazilian Cruzeiro (1993–1994)'), ('BRN', 'Brazilian New Cruzado (1989–1990)'), ('BRB', 'Brazilian New Cruzeiro (1967–1986)'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('BND', 'Brunei Dollar'), ('BGL', 'Bulgarian Hard Lev'), ('BGN', 'Bulgarian Lev'), ('BGO', 'Bulgarian Lev (1879–1952)'), ('BGM', 'Bulgarian Socialist Lev'), ('BUK', 'Burmese Kyat'), ('BIF', 'Burundian Franc'), ('XPF', 'CFP Franc'), ('KHR', 'Cambodian Riel'), ('CAD', 'Canadian Dollar'), ('CVE', 'Cape Verdean Escudo'), ('KYD', 'Cayman Islands Dollar'), ('XAF', 'Central African CFA Franc'), ('CLE', 'Chilean Escudo'), ('CLP', 'Chilean Peso'), ('CLF', 'Chilean Unit of Account (UF)'), ('CNX', 'Chinese People’s Bank Dollar'), ('CNY', 'Chinese Yuan'), ('CNH', 'Chinese Yuan (offshore)'), ('COP', 'Colombian Peso'), ('COU', 'Colombian Real Value Unit'), ('KMF', 'Comorian Franc'), ('CDF', 'Congolese Franc'), ('CRC', 'Costa Rican Colón'), ('HRD', 'Croatian Dinar'), ('HRK', 'Croatian Kuna'), ('CUC', 'Cuban Convertible Peso'), ('CUP', 'Cuban Peso'), ('CYP', 'Cypriot Pound'), ('CZK', 'Czech Koruna'), ('CSK', 'Czechoslovak Hard Koruna'), ('DKK', 'Danish Krone'), ('DJF', 'Djiboutian Franc'), ('DOP', 'Dominican Peso'), ('NLG', 'Dutch Guilder'), ('XCD', 'East Caribbean Dollar'), ('DDM', 'East German Mark'), ('ECS', 'Ecuadorian Sucre'), ('ECV', 'Ecuadorian Unit of Constant Value'), ('EGP', 'Egyptian Pound'), ('GQE', 'Equatorial Guinean Ekwele'), ('ERN', 'Eritrean Nakfa'), ('EEK', 'Estonian Kroon'), ('ETB', 'Ethiopian Birr'), ('EUR', 'Euro'), ('XBA', 'European Composite Unit'), ('XEU', 'European Currency Unit'), ('XBB', 'European Monetary Unit'), ('XBC', 'European Unit of Account (XBC)'), ('XBD', 'European Unit of Account (XBD)'), ('FKP', 'Falkland Islands Pound'), ('FJD', 'Fijian Dollar'), ('FIM', 'Finnish Markka'), ('FRF', 'French Franc'), ('XFO', 'French Gold Franc'), ('XFU', 'French UIC-Franc'), ('GMD', 'Gambian Dalasi'), ('GEK', 'Georgian Kupon Larit'), ('GEL', 'Georgian Lari'), ('DEM', 'German Mark'), ('GHS', 'Ghanaian Cedi'), ('GHC', 'Ghanaian Cedi (1979–2007)'), ('GIP', 'Gibraltar Pound'), ('XAU', 'Gold'), ('GRD', 'Greek Drachma'), ('GTQ', 'Guatemalan Quetzal'), ('GWP', 'Guinea-Bissau Peso'), ('GNF', 'Guinean Franc'), ('GNS', 'Guinean Syli'), ('GYD', 'Guyanaese Dollar'), ('HTG', 'Haitian Gourde'), ('HNL', 'Honduran Lempira'), ('HKD', 'Hong Kong Dollar'), ('HUF', 'Hungarian Forint'), ('IMP', 'IMP'), ('ISK', 'Icelandic Króna'), ('ISJ', 'Icelandic Króna (1918–1981)'), ('INR', 'Indian Rupee'), ('IDR', 'Indonesian Rupiah'), ('IRR', 'Iranian Rial'), ('IQD', 'Iraqi Dinar'), ('IEP', 'Irish Pound'), ('ILS', 'Israeli New Shekel'), ('ILP', 'Israeli Pound'), ('ILR', 'Israeli Shekel (1980–1985)'), ('ITL', 'Italian Lira'), ('JMD', 'Jamaican Dollar'), ('JPY', 'Japanese Yen'), ('JOD', 'Jordanian Dinar'), ('KZT', 'Kazakhstani Tenge'), ('KES', 'Kenyan Shilling'), ('KWD', 'Kuwaiti Dinar'), ('KGS', 'Kyrgystani Som'), ('LAK', 'Laotian Kip'), ('LVL', 'Latvian Lats'), ('LVR', 'Latvian Ruble'), ('LBP', 'Lebanese Pound'), ('LSL', 'Lesotho Loti'), ('LRD', 'Liberian Dollar'), ('LYD', 'Libyan Dinar'), ('LTL', 'Lithuanian Litas'), ('LTT', 'Lithuanian Talonas'), ('LUL', 'Luxembourg Financial Franc'), ('LUC', 'Luxembourgian Convertible Franc'), ('LUF', 'Luxembourgian Franc'), ('MOP', 'Macanese Pataca'), ('MKD', 'Macedonian Denar'), ('MKN', 'Macedonian Denar (1992–1993)'), ('MGA', 'Malagasy Ariary'), ('MGF', 'Malagasy Franc'), ('MWK', 'Malawian Kwacha'), ('MYR', 'Malaysian Ringgit'), ('MVR', 'Maldivian Rufiyaa'), ('MVP', 'Maldivian Rupee (1947–1981)'), ('MLF', 'Malian Franc'), ('MTL', 'Maltese Lira'), ('MTP', 'Maltese Pound'), ('MRU', 'Mauritanian Ouguiya'), ('MRO', 'Mauritanian Ouguiya (1973–2017)'), ('MUR', 'Mauritian Rupee'), ('MXV', 'Mexican Investment Unit'), ('MXN', 'Mexican Peso'), ('MXP', 'Mexican Silver Peso (1861–1992)'), ('MDC', 'Moldovan Cupon'), ('MDL', 'Moldovan Leu'), ('MCF', 'Monegasque Franc'), ('MNT', 'Mongolian Tugrik'), ('MAD', 'Moroccan Dirham'), ('MAF', 'Moroccan Franc'), ('MZE', 'Mozambican Escudo'), ('MZN', 'Mozambican Metical'), ('MZM', 'Mozambican Metical (1980–2006)'), ('MMK', 'Myanmar Kyat'), ('NAD', 'Namibian Dollar'), ('NPR', 'Nepalese Rupee'), ('ANG', 'Netherlands Antillean Guilder'), ('TWD', 'New Taiwan Dollar'), ('NZD', 'New Zealand Dollar'), ('NIO', 'Nicaraguan Córdoba'), ('NIC', 'Nicaraguan Córdoba (1988–1991)'), ('NGN', 'Nigerian Naira'), ('KPW', 'North Korean Won'), ('NOK', 'Norwegian Krone'), ('OMR', 'Omani Rial'), ('PKR', 'Pakistani Rupee'), ('XPD', 'Palladium'), ('PAB', 'Panamanian Balboa'), ('PGK', 'Papua New Guinean Kina'), ('PYG', 'Paraguayan Guarani'), ('PEI', 'Peruvian Inti'), ('PEN', 'Peruvian Sol'), ('PES', 'Peruvian Sol (1863–1965)'), ('PHP', 'Philippine Peso'), ('XPT', 'Platinum'), ('PLN', 'Polish Zloty'), ('PLZ', 'Polish Zloty (1950–1995)'), ('PTE', 'Portuguese Escudo'), ('GWE', 'Portuguese Guinea Escudo'), ('QAR', 'Qatari Riyal'), ('XRE', 'RINET Funds'), ('RHD', 'Rhodesian Dollar'), ('RON', 'Romanian Leu'), ('ROL', 'Romanian Leu (1952–2006)'), ('RUB', 'Russian Ruble'), ('RUR', 'Russian Ruble (1991–1998)'), ('RWF', 'Rwandan Franc'), ('SVC', 'Salvadoran Colón'), ('WST', 'Samoan Tala'), ('SAR', 'Saudi Riyal'), ('RSD', 'Serbian Dinar'), ('CSD', 'Serbian Dinar (2002–2006)'), ('SCR', 'Seychellois Rupee'), ('SLE', 'Sierra Leonean Leone'), ('SLL', 'Sierra Leonean Leone (1964—2022)'), ('XAG', 'Silver'), ('SGD', 'Singapore Dollar'), ('SKK', 'Slovak Koruna'), ('SIT', 'Slovenian Tolar'), ('SBD', 'Solomon Islands Dollar'), ('SOS', 'Somali Shilling'), ('ZAR', 'South African Rand'), ('ZAL', 'South African Rand (financial)'), ('KRH', 'South Korean Hwan (1953–1962)'), ('KRW', 'South Korean Won'), ('KRO', 'South Korean Won (1945–1953)'), ('SSP', 'South Sudanese Pound'), ('SUR', 'Soviet Rouble'), ('ESP', 'Spanish Peseta'), ('ESA', 'Spanish Peseta (A account)'), ('ESB', 'Spanish Peseta (convertible account)'), ('XDR', 'Special Drawing Rights'), ('LKR', 'Sri Lankan Rupee'), ('SHP', 'St. Helena Pound'), ('XSU', 'Sucre'), ('SDD', 'Sudanese Dinar (1992–2007)'), ('SDG', 'Sudanese Pound'), ('SDP', 'Sudanese Pound (1957–1998)'), ('SRD', 'Surinamese Dollar'), ('SRG', 'Surinamese Guilder'), ('SZL', 'Swazi Lilangeni'), ('SEK', 'Swedish Krona'), ('CHF', 'Swiss Franc'), ('SYP', 'Syrian Pound'), ('STN', 'São Tomé & Príncipe Dobra'), ('STD', 'São Tomé & Príncipe Dobra (1977–2017)'), ('TVD', 'TVD'), ('TJR', 'Tajikistani Ruble'), ('TJS', 'Tajikistani Somoni'), ('TZS', 'Tanzanian Shilling'), ('XTS', 'Testing Currency Code'), ('THB', 'Thai Baht'), ('TPE', 'Timorese Escudo'), ('TOP', 'Tongan Paʻanga'), ('TTD', 'Trinidad & Tobago Dollar'), ('TND', 'Tunisian Dinar'), ('TRY', 'Turkish Lira'), ('TRL', 'Turkish Lira (1922–2005)'), ('TMT', 'Turkmenistani Manat'), ('TMM', 'Turkmenistani Manat (1993–2009)'), ('USD', 'US Dollar'), ('USN', 'US Dollar (Next day)'), ('USS', 'US Dollar (Same day)'), ('UGX', 'Ugandan Shilling'), ('UGS', 'Ugandan Shilling (1966–1987)'), ('UAH', 'Ukrainian Hryvnia'), ('UAK', 'Ukrainian Karbovanets'), ('AED', 'United Arab Emirates Dirham'), ('UYW', 'Uruguayan Nominal Wage Index Unit'), ('UYU', 'Uruguayan Peso'), ('UYP', 'Uruguayan Peso (1975–1993)'), ('UYI', 'Uruguayan Peso (Indexed Units)'), ('UZS', 'Uzbekistani Som'), ('VUV', 'Vanuatu Vatu'), ('VES', 'Venezuelan Bolívar'), ('VEB', 'Venezuelan Bolívar (1871–2008)'), ('VEF', 'Venezuelan Bolívar (2008–2018)'), ('VND', 'Vietnamese Dong'), ('VNN', 'Vietnamese Dong (1978–1985)'), ('CHE', 'WIR Euro'), ('CHW', 'WIR Franc'), ('XOF', 'West African CFA Franc'), ('YDD', 'Yemeni Dinar'), ('YER', 'Yemeni Rial'), ('YUN', 'Yugoslavian Convertible Dinar (1990–1992)'), ('YUD', 'Yugoslavian Hard Dinar (1966–1990)'), ('YUM', 'Yugoslavian New Dinar (1994–2002)'), ('YUR', 'Yugoslavian Reformed Dinar (1992–1993)'), ('ZWN', 'ZWN'), ('ZRN', 'Zairean New Zaire (1993–1998)'), ('ZRZ', 'Zairean Zaire (1971–1993)'), ('ZMW', 'Zambian Kwacha'), ('ZMK', 'Zambian Kwacha (1968–2012)'), ('ZWD', 'Zimbabwean Dollar (1980–2008)'), ('ZWR', 'Zimbabwean Dollar (2008)'), ('ZWL', 'Zimbabwean Dollar (2009–2024)')], default='USD', editable=False, max_length=3)),
                ('acq_cost', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='USD', help_text='Acquisition cost', max_digits=10)),
            ],
            options={
                'ordering': ['resource_type'],
                'indexes': [models.Index(fields=['code'], name='user_generi_code_3aab68_idx')],
            },
        ),
        migrations.CreateModel(
            name='GenericSkill',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text="Unique, human-readable code for this skill (e.g., 'tech_coding_python')", max_length=50, unique=True)),
                ('description', models.TextField(help_text='Brief description of the generic skill.')),
                ('base_difficulty', apps.common.fields.ValidatedRangeField(default=50, help_text='Baseline difficulty to acquire this skill (1-100)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(100)])),
                ('development_timeframe', models.CharField(choices=[('immediate', 'Immediate (hours/days)'), ('short', 'Short-term (weeks)'), ('medium', 'Medium-term (months)'), ('long', 'Long-term (years)'), ('lifetime', 'Lifetime (ongoing)')], default='medium', help_text='Typical timeframe to develop this skill to a proficient level', max_length=30)),
                ('decay_rate', apps.common.fields.ValidatedRangeField(default=30, help_text='How quickly this skill deteriorates without practice (0=very slow, 100=very fast)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('parent_skill', models.ForeignKey(blank=True, help_text='Parent skill if this is a sub-skill/specialization', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sub_skills', to='user.genericskill')),
                ('prerequisites', models.ManyToManyField(blank=True, help_text='Skills that typically precede this skill', related_name='enables', to='user.genericskill')),
            ],
        ),
        migrations.CreateModel(
            name='GenericTrait',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text="Unique, human-readable code for this trait (e.g., 'openness_creativity')", max_length=50, unique=True)),
                ('trait_type', models.CharField(choices=[('OPEN', 'Openness'), ('CONS', 'Conscientiousness'), ('EXTR', 'Extraversion'), ('AGRE', 'Agreeableness'), ('EMO', 'Emotionality'), ('HONHUM', 'Honesty-Humility'), ('UND', 'Undefined')], max_length=50)),
                ('name', models.CharField(help_text='Name of the trait.', max_length=255)),
                ('description', models.TextField(help_text='Detailed description of the trait.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
                'indexes': [models.Index(fields=['code'], name='user_generi_code_416de9_idx')],
            },
        ),
        migrations.CreateModel(
            name='GenericUserLimitation',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text="Unique, human-readable code for this limitation (e.g., 'phys_mobility_arms')", max_length=50, unique=True)),
                ('description', models.TextField(help_text='Description of the limitation.')),
                ('limitation_type', models.CharField(help_text="Type/category of the limitation (e.g., 'PHYSICAL').", max_length=50)),
            ],
            options={
                'ordering': ['limitation_type'],
                'indexes': [models.Index(fields=['code'], name='user_generi_code_e701eb_idx')],
            },
        ),
        migrations.CreateModel(
            name='GoalInspiration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField(help_text='Commentary on the relationship.')),
                ('strength', apps.common.fields.ValidatedRangeField(default=50, help_text='How strongly this inspiration influenced the goal (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user_goal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.usergoal')),
                ('inspiration', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.inspiration')),
            ],
        ),
        migrations.AddField(
            model_name='usergoal',
            name='inspirations',
            field=models.ManyToManyField(help_text='Inspirations linked to this goal.', related_name='influenced_goals', through='user.GoalInspiration', to='user.inspiration'),
        ),
        migrations.CreateModel(
            name='SkillAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(help_text="Unique identifier for this attribute (e.g., 'spatial_reasoning')", max_length=50, unique=True)),
                ('name', models.CharField(help_text='Human-readable name for this attribute', max_length=100)),
                ('description', models.TextField(help_text='Detailed explanation of this attribute and how it manifests')),
                ('base_decay_rate', apps.common.fields.ValidatedRangeField(default=30, help_text='Base rate at which this attribute deteriorates without practice (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('development_difficulty', apps.common.fields.ValidatedRangeField(default=50, help_text='Inherent difficulty in developing this attribute (1-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('development_timeframe', models.CharField(choices=[('immediate', 'Immediate (hours/days)'), ('short', 'Short-term (weeks)'), ('medium', 'Medium-term (months)'), ('long', 'Long-term (years)'), ('lifetime', 'Lifetime (ongoing)')], default='medium', help_text='Typical timeframe to develop this attribute to a proficient level', max_length=30)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Skill Attribute',
                'verbose_name_plural': 'Skill Attributes',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['code'], name='user_skilla_code_a3ef94_idx'), models.Index(fields=['development_difficulty'], name='user_skilla_develop_a8c94e_idx')],
            },
        ),
        migrations.CreateModel(
            name='AttributeTraitInfluence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('impact', models.IntegerField(choices=[(-3, 'Strongly Hindering'), (-2, 'Moderately Hindering'), (-1, 'Slightly Hindering'), (0, 'Neutral'), (1, 'Slightly Beneficial'), (2, 'Moderately Beneficial'), (3, 'Strongly Beneficial')], default=0, help_text='How this trait affects learning/applying this attribute')),
                ('notes', models.TextField(blank=True, help_text='Explanation of why/how this trait influences this attribute')),
                ('generic_trait', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attribute_influences', to='user.generictrait')),
                ('attribute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trait_influences', to='user.skillattribute')),
            ],
            options={
                'verbose_name': 'Attribute-Trait Influence',
                'verbose_name_plural': 'Attribute-Trait Influences',
            },
        ),
        migrations.CreateModel(
            name='SkillDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(help_text='Unique identifier for this skill', max_length=50, unique=True)),
                ('name', models.CharField(help_text='Human-readable name for this skill', max_length=100)),
                ('description', models.TextField(help_text='Detailed explanation of this skill and its applications')),
                ('tags', models.JSONField(blank=True, default=list, help_text='Flexible tags (stored as JSON list for SQLite tests)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Skill Definition',
                'verbose_name_plural': 'Skill Definitions',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['code'], name='user_skilld_code_6c81cc_idx')],
            },
        ),
        migrations.CreateModel(
            name='SkillAttributeComposition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weight', models.FloatField(default=1.0, help_text='Relative importance of this attribute in the skill (higher = more important)', validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(10.0)])),
                ('attribute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skill_usages', to='user.skillattribute')),
                ('skill', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attribute_compositions', to='user.skilldefinition')),
            ],
            options={
                'verbose_name': 'Skill-Attribute Composition',
                'verbose_name_plural': 'Skill-Attribute Compositions',
            },
        ),
        migrations.CreateModel(
            name='SkillDomainApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('relevance', models.IntegerField(choices=[(10, 'Peripheral'), (30, 'Supportive'), (70, 'Important'), (100, 'Critical')], default=70, help_text='How relevant this skill is to activities in this domain')),
                ('transfer_coefficient', models.FloatField(default=1.0, help_text='Effectiveness multiplier in this domain context', validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(2.0)])),
                ('domain_specific_properties', models.JSONField(blank=True, default=dict, help_text='Domain-specific parameters like decay rates, context modifiers, etc.')),
                ('domain', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skill_applications', to='activity.genericdomain')),
                ('skill', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='domain_applications', to='user.skilldefinition')),
            ],
            options={
                'verbose_name': 'Skill-Domain Application',
                'verbose_name_plural': 'Skill-Domain Applications',
            },
        ),
        migrations.CreateModel(
            name='SkillDomainRelationship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('strength', models.PositiveSmallIntegerField(choices=[(10, 'Minimal'), (30, 'Moderate'), (70, 'Significant'), (100, 'Primary')], default=30, help_text='How strongly this skill relates to this domain')),
                ('application_context', models.CharField(choices=[('novice', 'Novice Application'), ('intermediate', 'Intermediate Application'), ('advanced', 'Advanced Application'), ('expert', 'Expert Application')], default='intermediate', help_text='At what skill level this relationship becomes most relevant', max_length=20)),
                ('transfer_coefficient', models.FloatField(default=1.0, help_text='Multiplier for how effectively this skill transfers to this domain (1.0 = direct application)', validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(2.0)])),
                ('skill_ceiling', models.IntegerField(default=100, help_text='Maximum effectiveness this skill can provide in this domain (e.g., 80 means this skill alone can only address 80% of domain challenges)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(100)])),
                ('notes', models.TextField(blank=True, help_text='Context-specific information about how this skill applies in this domain')),
                ('complementary_skills', models.ManyToManyField(blank=True, help_text='Other skills that enhance effectiveness when combined with this one in this domain', related_name='complementary_to', to='user.genericskill')),
                ('domain', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skill_relationships', to='activity.genericdomain')),
                ('generic_skill', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='domain_relationships_details', to='user.genericskill')),
            ],
        ),
        migrations.AddField(
            model_name='genericskill',
            name='domains',
            field=models.ManyToManyField(help_text='Activity domains this skill applies to', related_name='related_skills', through='user.SkillDomainRelationship', to='activity.genericdomain'),
        ),
        migrations.CreateModel(
            name='SkillTraitRelationship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('impact', apps.common.fields.ValidatedRangeField(choices=[(-3, 'Strongly Hindering'), (-2, 'Moderately Hindering'), (-1, 'Slightly Hindering'), (0, 'Neutral'), (1, 'Slightly Beneficial'), (2, 'Moderately Beneficial'), (3, 'Strongly Beneficial')], default=0, help_text='How this trait affects learning/applying this skill', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('notes', models.TextField(blank=True, help_text='Explanation of how/why this trait affects this skill')),
                ('generic_skill', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trait_relationships', to='user.genericskill')),
                ('generic_trait', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skill_relationships', to='user.generictrait')),
            ],
            options={
                'verbose_name': 'Skill-Trait Relationship',
                'verbose_name_plural': 'Skill-Trait Relationships',
            },
        ),
        migrations.AddField(
            model_name='genericskill',
            name='related_traits',
            field=models.ManyToManyField(help_text='Personality traits that influence skill acquisition', related_name='related_skills', through='user.SkillTraitRelationship', to='user.generictrait'),
        ),
        migrations.CreateModel(
            name='UserEnvironment',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('environment_name', models.CharField(help_text="User's personal name for this environment", max_length=255)),
                ('environment_description', models.TextField(help_text="User's description of their specific environment")),
                ('effective_start', models.DateField(help_text='When this environment became available to the user')),
                ('effective_end', models.DateField(blank=True, help_text='When this environment will no longer be available', null=True)),
                ('is_current', models.BooleanField(default=True, help_text='Whether this is a current environment for the user')),
                ('environment_details', models.JSONField(default=dict, help_text='Additional environment details specific to this user')),
                ('domains', models.ManyToManyField(blank=True, help_text='Activity domains relevant to this specific user environment', related_name='user_environments', to='activity.genericdomain')),
                ('generic_environment', models.ForeignKey(blank=True, help_text='The archetypal environment this is based on', null=True, on_delete=django.db.models.deletion.SET_NULL, to='user.genericenvironment')),
            ],
            options={
                'verbose_name': 'User Environment',
                'verbose_name_plural': 'User Environments',
                'ordering': ['-is_current', 'environment_name'],
            },
        ),
        migrations.CreateModel(
            name='Inventory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('valid_until', models.DateField(help_text='Date until which the inventory is valid.')),
                ('notes', models.TextField(help_text='Additional remarks.')),
                ('user_environment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventories', to='user.userenvironment')),
            ],
            options={
                'ordering': ['valid_until'],
            },
        ),
        migrations.CreateModel(
            name='UserEnvironmentActivitySupport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('digital_connectivity', apps.common.fields.ValidatedRangeField(help_text='No connectivity (0) to excellent connectivity (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('resource_availability', apps.common.fields.ValidatedRangeField(help_text='No resources (0) to abundant resources (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('time_availability', models.JSONField(default=dict, help_text='JSON describing when this specific environment is available to the user')),
                ('domain_specific_support', models.JSONField(default=dict, help_text="User-specific domain support levels, overriding the generic environment's defaults")),
                ('user_environment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='activity_support', to='user.userenvironment')),
            ],
            options={
                'verbose_name': 'User Environment Activity Support',
                'verbose_name_plural': 'User Environment Activity Supports',
            },
        ),
        migrations.CreateModel(
            name='UserEnvironmentPhysicalProperties',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rurality', apps.common.fields.ValidatedRangeField(help_text='Urban (0) to rural (100) scale', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('noise_level', apps.common.fields.ValidatedRangeField(help_text='Very quiet (0) to extremely loud (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('light_quality', apps.common.fields.ValidatedRangeField(help_text='Very dark (0) to very bright (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('temperature_range', models.CharField(choices=[('cold', 'Cold'), ('cool', 'Cool'), ('moderate', 'Moderate'), ('warm', 'Warm'), ('hot', 'Hot'), ('variable', 'Highly Variable')], default='moderate', help_text='Typical temperature condition of the environment', max_length=15)),
                ('accessibility', apps.common.fields.ValidatedRangeField(help_text='Not accessible (0) to fully accessible (100) for people with mobility limitations', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('air_quality', apps.common.fields.ValidatedRangeField(help_text='Poor (0) to excellent (100) air quality', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('has_natural_elements', models.BooleanField(default=False, help_text='Presence of plants, trees, or natural features')),
                ('surface_type', models.CharField(choices=[('hard', 'Hard (concrete, tile, etc.)'), ('soft', 'Soft (carpet, grass, etc.)'), ('mixed', 'Mixed surfaces'), ('variable', 'Highly variable')], default='mixed', help_text='Primary surface type of the environment', max_length=15)),
                ('water_proximity', apps.common.fields.ValidatedRangeField(default=0, help_text='No water (0) to immediate water access (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('space_size', models.CharField(choices=[('tiny', 'Tiny'), ('small', 'Small'), ('medium', 'Medium'), ('large', 'Large'), ('vast', 'Vast')], default='medium', help_text='Size classification of this specific environment', max_length=10)),
                ('user_environment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='physical_properties', to='user.userenvironment')),
            ],
            options={
                'verbose_name': 'User Environment Physical Properties',
                'verbose_name_plural': 'User Environment Physical Properties',
            },
        ),
        migrations.CreateModel(
            name='UserEnvironmentPsychologicalQualities',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('restorative_quality', apps.common.fields.ValidatedRangeField(help_text='Not restorative (0) to highly restorative (100) for this specific user', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('stimulation_level', apps.common.fields.ValidatedRangeField(help_text='Under-stimulating (0) to over-stimulating (100) for this specific user', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('aesthetic_appeal', apps.common.fields.ValidatedRangeField(help_text='Unappealing (0) to highly appealing (100) for this specific user', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('novelty_level', apps.common.fields.ValidatedRangeField(help_text='Very familiar (0) to completely novel (100) for this specific user', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('comfort_level', apps.common.fields.ValidatedRangeField(help_text='Uncomfortable (0) to very comfortable (100) for this specific user', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('personal_significance', apps.common.fields.ValidatedRangeField(default=0, help_text='How personally significant this environment is to the user', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('emotional_associations', models.JSONField(default=dict, help_text="User's emotional associations with this specific environment")),
                ('user_environment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='psychological_qualities', to='user.userenvironment')),
            ],
            options={
                'verbose_name': 'User Environment Psychological Qualities',
                'verbose_name_plural': 'User Environment Psychological Qualities',
            },
        ),
        migrations.CreateModel(
            name='UserEnvironmentSocialContext',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('privacy_level', apps.common.fields.ValidatedRangeField(help_text='Public (0) to extremely private (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('typical_occupancy', apps.common.fields.ValidatedRangeField(help_text='Empty (0) to extremely crowded (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('social_interaction_level', apps.common.fields.ValidatedRangeField(help_text='No interaction (0) to constant interaction (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('formality_level', apps.common.fields.ValidatedRangeField(help_text='Very casual (0) to very formal (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('safety_level', apps.common.fields.ValidatedRangeField(help_text='Unsafe (0) to very safe (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('supervision_level', models.CharField(choices=[('none', 'None'), ('minimal', 'Minimal'), ('moderate', 'Moderate'), ('high', 'High'), ('constant', 'Constant')], default='none', help_text='Level of supervision or monitoring in the environment', max_length=15)),
                ('cultural_diversity', apps.common.fields.ValidatedRangeField(default=50, help_text='Homogeneous (0) to highly diverse (100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('user_environment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='social_context', to='user.userenvironment')),
            ],
            options={
                'verbose_name': 'Environment Social Context',
                'verbose_name_plural': 'Environment Social Contexts',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('profile_name', models.CharField(help_text="User's display or login name.", max_length=255)),
                ('current_environment', models.ForeignKey(blank=True, help_text='Current environment of the user.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='user.userenvironment')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='profiles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['profile_name'],
            },
        ),
        migrations.CreateModel(
            name='UserLimitation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('effective_start', models.DateField(help_text='Start date when the record becomes active.')),
                ('duration_estimate', models.CharField(help_text="Estimated active period (e.g., '30 days').", max_length=50)),
                ('effective_end', models.DateField(help_text='End date when the record ceases to be active.')),
                ('severity', apps.common.fields.ValidatedRangeField(help_text='Impact measure (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('valid_until', models.DateField(help_text='Expiry or review date.')),
                ('is_unlimited', models.BooleanField(default=False, help_text='Indicates if the limitation is absolute.')),
                ('user_awareness', apps.common.fields.ValidatedRangeField(help_text='Awareness level (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('generic_limitation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_limitations', to='user.genericuserlimitation')),
                ('user_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='limitations', to='user.userprofile')),
            ],
            options={
                'ordering': ['user_profile'],
            },
        ),
        migrations.AddField(
            model_name='usergoal',
            name='user_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_goals', to='user.userprofile'),
        ),
        migrations.AddField(
            model_name='userenvironment',
            name='user_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='environments', to='user.userprofile'),
        ),
        migrations.CreateModel(
            name='UserAttributeProficiency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', apps.common.fields.ValidatedRangeField(default=0, help_text="User's proficiency level with this attribute (0-100)", validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('user_awareness', apps.common.fields.ValidatedRangeField(default=50, help_text="User's awareness of their proficiency in this attribute (0-100)", validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('last_practiced', models.DateField(blank=True, help_text='When the user last practiced this attribute', null=True)),
                ('practice_frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly'), ('rarely', 'Rarely'), ('never', 'Never')], default='rarely', help_text='How often the user practices this attribute', max_length=20)),
                ('formal_training', models.BooleanField(default=False, help_text='Whether the user has received formal training for this attribute')),
                ('notes', models.TextField(blank=True, help_text="Additional notes on the user's experience with this attribute")),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('attribute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_proficiencies', to='user.skillattribute')),
                ('user_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attribute_proficiencies', to='user.userprofile')),
            ],
            options={
                'verbose_name': 'User Attribute Proficiency',
                'verbose_name_plural': 'User Attribute Proficiencies',
            },
        ),
        migrations.CreateModel(
            name='TrustLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', apps.common.fields.ValidatedRangeField(help_text='Trust level score (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('aggregate_type', models.CharField(help_text="Name of the aggregate (e.g., 'Skill').", max_length=255)),
                ('aggregate_id', models.CharField(help_text='Identifier for the aggregate.', max_length=255)),
                ('notes', models.TextField(help_text='Additional context or commentary.')),
                ('user_profile', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='trust_level', to='user.userprofile')),
            ],
            options={
                'ordering': ['user_profile'],
            },
        ),
        migrations.CreateModel(
            name='Skill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(help_text='Brief description of the skill.')),
                ('level', apps.common.fields.ValidatedRangeField(help_text='Proficiency level (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('user_awareness', apps.common.fields.ValidatedRangeField(help_text='User awareness (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('user_enjoyment', apps.common.fields.ValidatedRangeField(help_text='Enjoyment level (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('note', models.TextField(help_text='Additional details.')),
                ('acquisition_date', models.DateField(blank=True, help_text='When the user first began developing this skill', null=True)),
                ('last_practiced', models.DateField(blank=True, help_text='When the user last practiced/used this skill', null=True)),
                ('practice_frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly'), ('rarely', 'Rarely'), ('never', 'Never')], default='rarely', help_text='How often the user practices this skill', max_length=20)),
                ('formal_training', models.BooleanField(default=False, help_text='Whether the user received formal training in this skill')),
                ('training_details', models.TextField(blank=True, help_text='Details about training, certifications, or validation')),
                ('contexts', models.JSONField(default=dict, help_text="Contexts in which this skill is applied (e.g., {'work': 70, 'hobby': 30})")),
                ('growth_goal', apps.common.fields.ValidatedRangeField(blank=True, help_text='Target level user aims to achieve (1-100, null if no specific goal)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(100)])),
                ('stagnation_point', apps.common.fields.ValidatedRangeField(blank=True, help_text='Level at which progress has stalled (0-100, null if actively progressing)', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('generic_skill', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skills', to='user.genericskill')),
                ('user_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skills', to='user.userprofile')),
            ],
            options={
                'ordering': ['user_profile', 'level'],
            },
        ),
        migrations.CreateModel(
            name='Preference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('effective_start', models.DateField(help_text='Start date when the record becomes active.')),
                ('duration_estimate', models.CharField(help_text="Estimated active period (e.g., '30 days').", max_length=50)),
                ('effective_end', models.DateField(help_text='End date when the record ceases to be active.')),
                ('pref_name', models.CharField(help_text="Name of the preference (e.g., 'Morning Workouts').", max_length=255)),
                ('pref_description', models.TextField(help_text='Detailed description of the preference.')),
                ('pref_strength', apps.common.fields.ValidatedRangeField(help_text='Importance rating (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('user_awareness', apps.common.fields.ValidatedRangeField(help_text="User's awareness of this preference (0-100).", validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('environment', models.ForeignKey(blank=True, help_text='Optional associated environment.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='user.userenvironment')),
                ('user_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to='user.userprofile')),
            ],
            options={
                'ordering': ['pref_name'],
            },
        ),
        migrations.AddField(
            model_name='inspiration',
            name='user_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inspirations', to='user.userprofile'),
        ),
        migrations.CreateModel(
            name='Demographics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(help_text="User's full name.", max_length=255)),
                ('age', models.IntegerField(help_text="User's age.")),
                ('gender', models.CharField(help_text="User's gender.", max_length=50)),
                ('location', models.CharField(help_text="User's location or residence.", max_length=255)),
                ('language', models.CharField(help_text='Preferred or native language.', max_length=50)),
                ('occupation', models.CharField(help_text="User's occupation.", max_length=255)),
                ('personal_prefs_json', models.JSONField(help_text='JSON containing personal preferences.')),
                ('user_profile', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='demographics', to='user.userprofile')),
            ],
            options={
                'ordering': ['full_name'],
            },
        ),
        migrations.CreateModel(
            name='CurrentMood',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('effective_start', models.DateField(help_text='Start date when the record becomes active.')),
                ('duration_estimate', models.CharField(help_text="Estimated active period (e.g., '30 days').", max_length=50)),
                ('effective_end', models.DateField(help_text='End date when the record ceases to be active.')),
                ('description', models.TextField(help_text='Description of the current mood.')),
                ('height', apps.common.fields.ValidatedRangeField(help_text='Intensity of the mood (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('user_awareness', apps.common.fields.ValidatedRangeField(help_text='Awareness level (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('processed_at', models.DateTimeField(help_text='Timestamp when the mood was recorded.')),
                ('inferred_from_text', models.BooleanField(default=False, help_text='Was this mood inferred from text analysis?')),
                ('user_profile', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='current_mood', to='user.userprofile')),
            ],
            options={
                'ordering': ['processed_at'],
            },
        ),
        migrations.AddField(
            model_name='belief',
            name='user_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='beliefs', to='user.userprofile'),
        ),
        migrations.CreateModel(
            name='UserResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('specific_name', models.CharField(help_text='Unique name or label for the resource.', max_length=255)),
                ('location_details', models.TextField(help_text='Extra location information for the resource.')),
                ('ownership_details', models.TextField(help_text='Details regarding ownership or usage.')),
                ('contact_info', models.CharField(help_text='Contact information for resource usage requests.', max_length=255)),
                ('notes', models.TextField(help_text='Additional remarks.')),
                ('generic_resource', models.ForeignKey(blank=True, help_text='Based on a generic resource if applicable.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='user.genericresource')),
                ('user_environment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_resources', to='user.userenvironment')),
            ],
            options={
                'ordering': ['specific_name'],
            },
        ),
        migrations.CreateModel(
            name='UserTraitInclination',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('strength', models.FloatField(help_text='Strength of the inclination.')),
                ('awareness', apps.common.fields.ValidatedRangeField(help_text='User awareness (0-100).', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('generic_trait', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inclinations', to='user.generictrait')),
                ('user_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trait_inclinations', to='user.userprofile')),
            ],
            options={
                'ordering': ['user_profile'],
            },
        ),
        migrations.CreateModel(
            name='BeliefInfluence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.UUIDField()),
                ('influence_strength', apps.common.fields.ExtendedRangeField(help_text='Strength of influence (-100 to 100)', validators=[django.core.validators.MinValueValidator(-100), django.core.validators.MaxValueValidator(100)])),
                ('note', models.TextField(help_text='Additional remarks')),
                ('belief', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='influences', to='user.belief')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
            options={
                'ordering': ['belief'],
                'constraints': [models.CheckConstraint(condition=models.Q(('influence_strength__gte', -100), ('influence_strength__lte', 100)), name='belief_influence_strength_range'), models.UniqueConstraint(fields=('belief', 'content_type', 'object_id'), name='unique_belief_influence')],
            },
        ),
        migrations.AddIndex(
            model_name='genericenvironmentdomainrelationship',
            index=models.Index(fields=['generic_environment'], name='user_generi_generic_27617d_idx'),
        ),
        migrations.AddIndex(
            model_name='genericenvironmentdomainrelationship',
            index=models.Index(fields=['domain'], name='user_generi_domain__829ba1_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='genericenvironmentdomainrelationship',
            unique_together={('generic_environment', 'domain')},
        ),
        migrations.AddIndex(
            model_name='genericenvironment',
            index=models.Index(fields=['code'], name='user_generi_code_b84d28_idx'),
        ),
        migrations.AddIndex(
            model_name='genericenvironment',
            index=models.Index(fields=['is_indoor'], name='user_generi_is_indo_1e6681_idx'),
        ),
        migrations.AddIndex(
            model_name='goalinspiration',
            index=models.Index(fields=['user_goal'], name='user_goalin_user_go_3910cf_idx'),
        ),
        migrations.AddIndex(
            model_name='goalinspiration',
            index=models.Index(fields=['inspiration'], name='user_goalin_inspira_6e1839_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='goalinspiration',
            unique_together={('user_goal', 'inspiration')},
        ),
        migrations.AlterUniqueTogether(
            name='attributetraitinfluence',
            unique_together={('attribute', 'generic_trait')},
        ),
        migrations.AlterUniqueTogether(
            name='skillattributecomposition',
            unique_together={('skill', 'attribute')},
        ),
        migrations.AlterUniqueTogether(
            name='skilldomainapplication',
            unique_together={('skill', 'domain')},
        ),
        migrations.AlterUniqueTogether(
            name='skilltraitrelationship',
            unique_together={('generic_skill', 'generic_trait')},
        ),
        migrations.AddIndex(
            model_name='genericskill',
            index=models.Index(fields=['code'], name='user_generi_code_e6d282_idx'),
        ),
        migrations.AddIndex(
            model_name='usergoal',
            index=models.Index(fields=['user_profile'], name='user_usergo_user_pr_07c3f7_idx'),
        ),
        migrations.AddIndex(
            model_name='usergoal',
            index=models.Index(fields=['created_at'], name='user_usergo_created_2114e9_idx'),
        ),
        migrations.AddIndex(
            model_name='userenvironment',
            index=models.Index(fields=['user_profile', 'is_current'], name='user_useren_user_pr_8e9684_idx'),
        ),
        migrations.AddIndex(
            model_name='userenvironment',
            index=models.Index(fields=['generic_environment'], name='user_useren_generic_858b0b_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userattributeproficiency',
            unique_together={('user_profile', 'attribute')},
        ),
        migrations.AddConstraint(
            model_name='skill',
            constraint=models.UniqueConstraint(fields=('user_profile', 'generic_skill'), name='unique_user_skill'),
        ),
        migrations.AddConstraint(
            model_name='usertraitinclination',
            constraint=models.CheckConstraint(condition=models.Q(('strength__gte', 0), ('strength__lte', 100)), name='trait_strength_range'),
        ),
        migrations.AddConstraint(
            model_name='usertraitinclination',
            constraint=models.UniqueConstraint(fields=('user_profile', 'generic_trait'), name='unique_user_trait'),
        ),
    ]
