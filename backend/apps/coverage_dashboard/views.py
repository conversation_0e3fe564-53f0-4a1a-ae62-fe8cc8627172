from django.shortcuts import render
import json
import os
from pathlib import Path
from django.http import JsonResponse, HttpResponse
from django.conf import settings

def coverage_dashboard(request):
    """
    Main view for the coverage dashboard
    Shows the coverage.json data in a user-friendly format
    """
    # Get the path to coverage.json using settings.BASE_DIR
    # settings.BASE_DIR should point to the 'backend' directory
    coverage_path = os.path.join(settings.BASE_DIR, 'coverage.json')

    # Check if the file exists
    if not os.path.exists(coverage_path):
        return render(request, 'coverage_dashboard/no_data.html')
    
    # Load the coverage data
    with open(coverage_path, 'r') as f:
        try:
            coverage_data = json.load(f)
        except json.JSONDecodeError:
            # Handle case where file is empty or invalid JSON
             return render(request, 'coverage_dashboard/no_data.html', {'error': 'Invalid coverage.json format'})

    # --- Process Standard Coverage Data ---
    
    # Metadata (Timestamp and Run Number are added by run_tests command, not standard)
    run_number = coverage_data.get('run_number', 0) # Get from potentially added metadata
    last_updated = coverage_data.get('last_updated', "N/A") # Get from potentially added metadata
    meta = coverage_data.get('meta', {})
    branch_coverage_enabled = meta.get('branch_coverage', False)

    # Overall Totals
    totals = coverage_data.get('totals', {})
    overall_coverage = totals.get('percent_covered', 0)
    total_statements = totals.get('num_statements', 0)
    total_lines_covered = totals.get('covered_lines', 0)
    total_branches = totals.get('num_branches', 0) # If branch coverage enabled
    total_branches_covered = totals.get('covered_branches', 0) # If branch coverage enabled

    # File Summaries
    files_data = coverage_data.get('files', {})
    # Sort files by path for consistent display
    sorted_files = sorted(files_data.items()) 
    
    total_files = len(files_data)

    # Prepare the context for the template
    context = {
        # Pass the raw data for potential detailed display or JS parsing
        'coverage_data_raw': coverage_data, 
        'coverage_data_json': json.dumps(coverage_data), # For Plotly JS

        # Metadata
        'run_number': run_number,
        'last_updated': last_updated,
        'branch_coverage_enabled': branch_coverage_enabled,

        # Overall Stats
        'overall_coverage': round(overall_coverage, 2),
        'total_statements': total_statements,
        'total_lines_covered': total_lines_covered,
        'total_files': total_files,
        'total_branches': total_branches,
        'total_branches_covered': total_branches_covered,

        # File Details (Sorted)
        'files': sorted_files, # Pass the sorted list of (path, summary_dict) tuples

        'ci_enabled': 'GITHUB_ACTIONS' in os.environ,
    }
    
    return render(request, 'coverage_dashboard/dashboard.html', context)

def coverage_json(request):
    """API endpoint to get the raw coverage data as JSON"""
    # Get the path to coverage.json using settings.BASE_DIR
    coverage_path = os.path.join(settings.BASE_DIR, 'coverage.json')

    if not os.path.exists(coverage_path):
        return JsonResponse({'error': 'No coverage data found'}, status=404)
    
    with open(coverage_path, 'r') as f:
        coverage_data = json.load(f)
    
    return JsonResponse(coverage_data)

def coverage_badge(request):
    """Generate a simple coverage badge SVG"""
    # Get the path to coverage.json using settings.BASE_DIR
    coverage_path = os.path.join(settings.BASE_DIR, 'coverage.json')

    if not os.path.exists(coverage_path):
        return JsonResponse({'error': 'No coverage data found'}, status=404)
    
    with open(coverage_path, 'r') as f:
        try:
            data = json.load(f)
        except json.JSONDecodeError:
             # Return a badge indicating an error
             badge_color = '#e05d44' # Red
             badge_text = 'error'
             text_width = 30 # Approximate width for 'error'
             total_width = 58 + text_width # 'coverage' part + text part
             badge_content = f'''<svg xmlns='http://www.w3.org/2000/svg' width='{total_width}' height='20'>
                 <linearGradient id='a' x2='0' y2='100%'><stop offset='0' stop-color='#bbb' stop-opacity='.1'/><stop offset='1' stop-opacity='.1'/></linearGradient>
                 <rect rx='3' width='{total_width}' height='20' fill='#555'/>
                 <rect rx='3' x='58' width='{text_width}' height='20' fill='{badge_color}'/>
                 <path fill='{badge_color}' d='M58 0h4v20h-4z'/>
                 <rect rx='3' width='{total_width}' height='20' fill='url(#a)'/>
                 <g fill='#fff' text-anchor='middle' font-family='DejaVu Sans,Verdana,Geneva,sans-serif' font-size='11'>
                   <text x='29.5' y='15' fill='#010101' fill-opacity='.3'>coverage</text>
                   <text x='29.5' y='14'>coverage</text>
                   <text x='{58 + text_width / 2}' y='15' fill='#010101' fill-opacity='.3'>{badge_text}</text>
                   <text x='{58 + text_width / 2}' y='14'>{badge_text}</text>
                 </g>
               </svg>'''
             return HttpResponse(badge_content, content_type='image/svg+xml')

    # Use overall percentage from standard coverage JSON
    totals = data.get('totals', {})
    coverage_percent = totals.get('percent_covered', 0)
    badge_text = f'{coverage_percent:.1f}%'

    # Determine badge color based on percentage
    if coverage_percent >= 90:
        badge_color = '#4c1' # Bright green
    elif coverage_percent >= 75:
        badge_color = '#97ca00' # Green
    elif coverage_percent >= 50:
        badge_color = '#dfb317' # Yellow
    else:
        badge_color = '#e05d44' # Red

    # Adjust width based on text length (simple approximation)
    text_width = len(badge_text) * 6 + 10 # Adjust multiplier as needed
    total_width = 58 + text_width # 'coverage' part + text part

    badge_content = f'''<svg xmlns='http://www.w3.org/2000/svg' width='{total_width}' height='20'>
      <linearGradient id='a' x2='0' y2='100%'><stop offset='0' stop-color='#bbb' stop-opacity='.1'/><stop offset='1' stop-opacity='.1'/></linearGradient>
      <rect rx='3' width='{total_width}' height='20' fill='#555'/>
      <rect rx='3' x='58' width='{text_width}' height='20' fill='{badge_color}'/>
      <path fill='{badge_color}' d='M58 0h4v20h-4z'/>
      <rect rx='3' width='{total_width}' height='20' fill='url(#a)'/>
      <g fill='#fff' text-anchor='middle' font-family='DejaVu Sans,Verdana,Geneva,sans-serif' font-size='11'>
        <text x='29.5' y='15' fill='#010101' fill-opacity='.3'>coverage</text>
        <text x='29.5' y='14'>coverage</text>
        <text x='{58 + text_width / 2}' y='15' fill='#010101' fill-opacity='.3'>{badge_text}</text>
        <text x='{58 + text_width / 2}' y='14'>{badge_text}</text>
      </g>
    </svg>'''
    
    return HttpResponse(badge_content, content_type='image/svg+xml')
