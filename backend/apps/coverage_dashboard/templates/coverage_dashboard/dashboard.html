{% extends "admin/base_site.html" %}

{% block title %}Coverage Dashboard{% endblock %}

{% load static %}

{% block extrastyle %}
{{ block.super }}
<script src='https://cdn.plot.ly/plotly-latest.min.js'></script>
<style>
  :root {
    --bg-color: #1e1e1e;
    --text-color: #d4d4d4;
    --card-bg: #2a2a2a;
    --border-color: #444;
    --header-color: #569cd6; /* Light blue */
    --table-header-bg: #333;
    --table-row-alt-bg: #252526;
    --link-color: #569cd6;
    --stat-number-color: #c5c5c5;
    --badge-bg: #333;
    --pre-bg: #222;
    --pre-border: #555;
  }

  body {
    background-color: var(--bg-color);
    color: var(--text-color);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  }
  
  #content { /* Target Django admin content area */
    background-color: var(--bg-color);
    padding: 20px;
  }

  .dashboard {
    margin: 0; /* Reset margin if #content provides padding */
  }
  
  h1, h2, h3 {
    color: var(--header-color); 
  }

  h1 {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-top: 0;
  }
  
  .stats-container {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    margin-bottom: 30px;
  }
  
  .stat-card {
    background-color: var(--card-bg); 
    border: 1px solid var(--border-color); 
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    padding: 20px;
    min-width: 220px;
    flex: 1;
    transition: transform 0.2s ease-in-out;
  }

  .stat-card:hover {
      transform: translateY(-3px);
  }
  
  .stat-card h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.1em;
    font-weight: 600;
  }
  
  .stat-number {
    font-size: 2.2em;
    font-weight: bold;
    color: var(--stat-number-color);
  }

  .stat-time {
      font-size: 1em;
      color: #999;
  }
  
  .modules-table, .test-types-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 40px;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    overflow: hidden; /* Ensures border-radius applies to table */
  }
  
  .modules-table th, .modules-table td,
  .test-types-table th, .test-types-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
  }

  .modules-table td:last-child, 
  .test-types-table td:last-child {
      text-align: right;
  }
  
  .modules-table th, .test-types-table th {
    background-color: var(--table-header-bg);
    color: var(--text-color);
    font-weight: 600;
  }
  
  .modules-table tr:nth-child(even),
  .test-types-table tr:nth-child(even) {
    background-color: var(--table-row-alt-bg);
  }

  .modules-table tr:last-child td,
  .test-types-table tr:last-child td {
      border-bottom: none;
  }
  
  .badge-container {
    margin-bottom: 30px;
    background-color: var(--card-bg);
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
  }
  .badge-container img {
      background-color: var(--badge-bg);
      padding: 5px;
      border-radius: 3px;
  }
  
  .ci-info {
    background-color: #3a2d20; /* Darker orange */
    color: #f0ad4e; /* Lighter orange text */
    padding: 15px;
    border: 1px solid #5c432d;
    border-radius: 5px;
    margin-bottom: 20px;
  }

  pre {
      background-color: var(--pre-bg);
      border: 1px solid var(--pre-border);
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
      color: #ccc;
      font-size: 0.9em;
  }

  a {
      color: var(--link-color);
      text-decoration: none;
  }
  a:hover {
      text-decoration: underline;
  }

  .chart-container {
      margin-bottom: 40px;
      padding: 20px;
      background-color: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 5px;
  }
</style>
{% endblock %}

{% block content %}
<div id="content"> {# Ensure content is wrapped if extending admin base #}
<div class="dashboard">
  <h1>Test Coverage Dashboard</h1>
  {% if ci_enabled %}
  <div class="ci-info">
    <strong>CI Mode:</strong> This dashboard might be running in a CI environment. Badge URL may differ.
  </div>
  {% endif %}
  
  <div class="badge-container">
    <h2>Coverage Badge</h2>
    <img src="{% url 'coverage_badge' %}" alt="Coverage Badge">
    <p>Use this badge in your README.md (adjust URL for CI/hosting if needed):</p>
    <pre>![Coverage]({% url 'coverage_badge' %})</pre>
  </div>

  <div class="stats-container">
    <div class="stat-card">
      <h3>Overall Coverage</h3>
      <div class="stat-number">{{ overall_coverage|default:0 }}%</div>
      <small>{{ total_lines_covered|default:0 }} / {{ total_statements|default:0 }} statements</small>
       {% if branch_coverage_enabled %}
         <br><small>{{ total_branches_covered|default:0 }} / {{ total_branches|default:0 }} branches</small>
       {% endif %}
    </div>
    
    <div class="stat-card">
      <h3>Files Covered</h3>
      <div class="stat-number">{{ total_files|default:0 }}</div>
    </div>

    <div class="stat-card">
        <h3>Run Number</h3>
        <div class="stat-number">#{{ run_number|default:0 }}</div>
    </div>
    
    <div class="stat-card">
      <h3>Last Updated</h3>
      <div class="stat-time">{{ last_updated|default:"N/A" }}</div>
    </div>
  </div>

  {# Charts Section - Data comes from JavaScript parsing coverage_data_json #}
  <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 30px;">
      <div class="chart-container" style="flex: 1; min-width: 300px;">
          <h2>Overall Line Coverage</h2> {# More specific title #}
          <div id="overall-coverage-chart"></div> {# Gauge chart placeholder #}
      </div>
      {% if branch_coverage_enabled %}
      <div class="chart-container" style="flex: 1; min-width: 300px;">
          <h2>Overall Branch Coverage</h2> {# More specific title #}
          <div id="branch-coverage-chart"></div> {# Gauge chart placeholder #}
      </div>
      {% endif %}
  </div>

  <div class="chart-container">
      <h2>Coverage by File (< 100%)</h2> {# More specific title #}
      <div id="file-coverage-chart"></div> {# Bar chart placeholder #}
  </div>
  
  {# Detailed File Table - Using standard coverage data #}
  <h2>File Coverage Details</h2>
  <table class="modules-table"> {# Keep class name for styling consistency #}
    <thead>
      <tr>
        <th>File Path</th>
        <th>Stmts</th> {# Shorter header #}
        <th>Miss</th> {# Shorter header #}
        <th>Excl</th> {# Shorter header #}
        <th>Cover (%)</th> {# Shorter header #}
        {% if branch_coverage_enabled %}
        <th>Branches</th>
        <th>Miss Br.</th> {# Shorter header #}
        <th>Br. Cov (%)</th> {# Shorter header #}
        {% endif %}
      </tr>
    </thead>
    <tbody>
      {# Loop through 'files' context variable from the view #}
      {% for path, file_data in files %} 
      <tr>
        <td>{{ path }}</td>
        <td>{{ file_data.summary.num_statements|default:0 }}</td>
        <td>{{ file_data.summary.missing_lines|default:0 }}</td>
        <td>{{ file_data.summary.excluded_lines|default:0 }}</td>
        <td>{{ file_data.summary.percent_covered|floatformat:2 }}%</td>
        {% if branch_coverage_enabled %}
        <td>{{ file_data.summary.num_branches|default:0 }}</td>
        <td>{{ file_data.summary.missing_branches|default:0 }}</td>
        <td>{{ file_data.summary.percent_covered_branches|floatformat:2 }}%</td>
        {% endif %}
      </tr>
       {% empty %}
      <tr><td colspan="{% if branch_coverage_enabled %}8{% else %}5{% endif %}">No file coverage data available.</td></tr>
      {% endfor %}
    </tbody>
  </table>
  
  {# Removed old custom sections (Test Types, Agent Coverage) #}

  <h2>Raw Coverage Data (JSON)</h2>
  <pre id="raw-json-data" style="display: none;">{{ coverage_data_json|default:"{}" }}</pre>
  <details>
      <summary style="cursor: pointer; color: var(--link-color);">Click to view Raw JSON</summary>
      {# Displaying the raw dict passed from view #}
      <pre>{{ coverage_data_raw|safe }}</pre> 
  </details>

</div> {# End dashboard #}
</div> {# End content #}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const rawJsonDataElement = document.getElementById('raw-json-data');
    let coverageData;
    try {
        // Parse the JSON passed from the Django view
        coverageData = JSON.parse(rawJsonDataElement.textContent || '{}');
    } catch (e) {
        console.error("Error parsing coverage data JSON:", e);
        coverageData = {}; // Fallback to empty object
    }

    // Base Plotly layout for dark theme
    const plotlyLayout = {
        paper_bgcolor: 'rgba(0,0,0,0)', // Transparent background
        plot_bgcolor: 'rgba(0,0,0,0)',
        font: { color: '#d4d4d4' },
        margin: { l: 50, r: 50, t: 50, b: 100 }, // Increased bottom margin for labels
        legend: {
            bgcolor: 'rgba(42, 42, 42, 0.8)', 
            bordercolor: '#444',
            borderwidth: 1
        }
    };
    
    const plotlyConfig = { responsive: true };

    // --- Overall Coverage Gauge Chart ---
    const totals = coverageData.totals || {};
    const overallPercent = totals.percent_covered || 0;

    const overallGaugeData = [{
        type: "indicator",
        mode: "gauge+number",
        value: overallPercent,
        title: { text: "Overall Line Coverage", font: { size: 18 } },
        gauge: {
            axis: { range: [null, 100], tickwidth: 1, tickcolor: "#ccc" },
            bar: { color: "#569cd6" }, // Main color
            bgcolor: "#333",
            borderwidth: 1,
            bordercolor: "#444",
            steps: [
                { range: [0, 50], color: "#e05d44" }, // Red
                { range: [50, 80], color: "#dfb317" }, // Yellow
                { range: [80, 100], color: "#6a9955" } // Green
            ],
            threshold: {
                line: { color: "red", width: 4 },
                thickness: 0.75,
                value: 80 // Example threshold line
            }
        }
    }];
    const overallGaugeLayout = { ...plotlyLayout, 
        width: 350, height: 250, 
        margin: { l: 30, r: 30, t: 60, b: 30 } // Adjusted margins for gauge
    };
    Plotly.newPlot('overall-coverage-chart', overallGaugeData, overallGaugeLayout, plotlyConfig);

    // --- Branch Coverage Gauge Chart (Optional) ---
    const branchCoverageEnabled = coverageData.meta?.branch_coverage || false;
    if (branchCoverageEnabled) {
        const branchPercent = totals.percent_covered_branches || 0;
        const branchGaugeData = [{
            type: "indicator",
            mode: "gauge+number",
            value: branchPercent,
            title: { text: "Overall Branch Coverage", font: { size: 18 } },
            gauge: {
                axis: { range: [null, 100], tickwidth: 1, tickcolor: "#ccc" },
                bar: { color: "#569cd6" },
                bgcolor: "#333",
                borderwidth: 1,
                bordercolor: "#444",
                steps: [
                    { range: [0, 50], color: "#e05d44" },
                    { range: [50, 80], color: "#dfb317" },
                    { range: [80, 100], color: "#6a9955" }
                ],
                 threshold: {
                    line: { color: "red", width: 4 },
                    thickness: 0.75,
                    value: 70 // Example threshold
                }
            }
        }];
         const branchGaugeLayout = { ...plotlyLayout, 
            width: 350, height: 250, 
            margin: { l: 30, r: 30, t: 60, b: 30 } 
        };
        Plotly.newPlot('branch-coverage-chart', branchGaugeData, branchGaugeLayout, plotlyConfig);
    }


    // --- File Coverage Chart (Bar) ---
    const files = coverageData.files || {};
    const fileEntries = Object.entries(files)
        // Extract relevant data and simplify path
        .map(([path, data]) => ({ 
            name: path.replace('backend/apps/', ''), // Shorter name for display
            path: path,
            percent: data.summary?.percent_covered || 0 
        }))
        .filter(f => f.percent < 100) // Focus on files needing coverage
        .sort((a, b) => a.percent - b.percent); // Sort ascending by coverage %

    if (fileEntries.length > 0) {
        const fileLabels = fileEntries.map(f => f.name);
        const fileValues = fileEntries.map(f => f.percent);

        const fileData = [{
            x: fileLabels,
            y: fileValues,
            type: 'bar',
            marker: {
                color: fileValues.map(val => val < 50 ? '#e05d44' : (val < 80 ? '#dfb317' : '#6a9955')), // Color based on coverage
            },
            text: fileValues.map(val => `${val.toFixed(1)}%`), // Show percentage on bar
            textposition: 'outside',
        }];
        const fileLayout = { 
            ...plotlyLayout, 
            title: 'Files with Incomplete Coverage (< 100%)',
            yaxis: { title: 'Coverage %', range: [0, 100] },
            xaxis: { tickangle: -45 } // Angle labels if they overlap
        };
        Plotly.newPlot('file-coverage-chart', fileData, fileLayout, plotlyConfig);
    } else {
         document.getElementById('file-coverage-chart').innerHTML = '<p>All files have 100% coverage or no data available.</p>';
    }

    // Remove old chart generation logic if any remains
    const oldChart1 = document.getElementById('test-types-chart');
    if (oldChart1) oldChart1.innerHTML = ''; // Clear old placeholder if needed
    const oldChart2 = document.getElementById('module-coverage-chart');
    if (oldChart2) oldChart2.innerHTML = ''; 
    const oldChart3 = document.getElementById('agent-tests-chart');
    if (oldChart3) oldChart3.innerHTML = ''; 

});
</script>
{% endblock %}
