{% extends "admin/base_site.html" %}

{% block title %}Coverage Dashboard - No Data{% endblock %}

{% block content %}
<div style="margin: 20px;">
  <h1>No Coverage Data Available</h1>
  
  <p>No coverage data has been generated yet. Run the tests to generate coverage data:</p>
  
  <h3>Using Docker:</h3>
  <pre>
  # In the project root directory
  cd backend
  docker-compose exec web python -m pytest
  </pre>
  
  <h3>Using Local Python Environment:</h3>
  <pre>
  # In the project root directory
  cd backend
  python -m pytest
  </pre>
  
  <h3>Using Management Command:</h3>
  <pre>
  # In the project root directory
  cd backend
  python manage.py run_tests
  </pre>
  
  <p>After running the tests, refresh this page to see the coverage dashboard.</p>
</div>
{% endblock %}