# Context Document for Goali Backend Test Stabilization

**Core Problem Diagnosis:**
The backend test suite is unstable due to:
1.  **Async/Sync Conflicts:** Mixing synchronous Django ORM calls with asynchronous test execution (`pytest-asyncio`) leads to `SynchronousOnlyOperation` errors. Standard async wrappers (`@database_sync_to_async`) might be missing or used incorrectly.
2.  **Fragile Database Setup:** The project *does not use Django migrations*. Schema creation relies on a complex mix of seeding commands (`run_seeders.py`, `seed_db_*.py`) and potentially `conftest.py` fixtures, leading to inconsistencies.
3.  **Inconsistent Mocking:** Different mocking strategies are used, and mocking async calls within specific contexts (like management commands) proves unreliable, failing to intercept calls or verify counts (`await_count`).
4.  **Blurred Test Layers:** Lack of clear separation between unit tests (no Django/DB) and integration/workflow tests (requiring DB setup) causes dependency issues.

**Primary Goal:**
Stabilize the test suite by addressing the database setup and async/sync conflicts, standardizing mocking, clarifying test layers, and ensuring tests run reliably and predictably.

**Key Files for Understanding the Problem & Strategy:**
*   `codebase.json`: Contains the full source code.
*   `PLANNING.md`: High-level architecture, constraints, recent fixes, async DB patterns, troubleshooting notes. **Pay special attention to sections on Async Database Access, Refined Async DB Pattern, and Testing Async Management Commands.**
*   `README_TESTING.md`: General testing guide (needs updating per the stabilization plan).
*   `agent_testing.md`: Agent-specific testing guide (needs updating).
*   `agent_benchmarking.md`: Benchmarking system details (relevant for benchmark tests).
*   `TASK_LIST.md`: The specific plan you need to execute.

**Key Files Likely Requiring Modification:**
*   Test Files: `backend/apps/*/tests/**/*.py` (Unit, Integration, Workflow tests)
*   Agent Files: `backend/apps/main/agents/**/*.py` (Review for async DB calls)
*   Service Files: `backend/apps/main/services/**/*.py`, `backend/apps/user/services/**/*.py` (Review for async DB calls)
*   Graph Files: `backend/apps/main/graphs/**/*.py` (Review agent interactions)
*   Management Commands: `backend/apps/main/management/commands/**/*.py` (Seeding, setup)
*   Test Setup/Fixtures: `backend/conftest.py`, `backend/ultimate_test_setup.py`
*   Seeding Orchestrator: `backend/apps/main/management/commands/run_seeders.py`
*   Documentation: `backend/README_TESTING.md`, `backend/agent_testing.md`

**Core Concepts to Understand:**
*   **No Django Migrations:** Database schema is *not* managed by `migrate`. It relies on seeding commands. This is a major source of fragility. The stabilization plan offers two paths: migrate to standard migrations or refine the current system.
*   **Idempotent Seeding:** `run_seeders.py` uses the `AppliedSeedingCommand` model to avoid re-running seeders. Individual seeders also use `get_or_create` or `update_or_create`.
*   **Async DB Access:** The need to wrap *all* synchronous ORM calls within async functions.
    *   **Correct Wrapper:** Use `@database_sync_to_async` imported from `channels.db` (`from channels.db import database_sync_to_async`). Alternatively, `sync_to_async(..., thread_sensitive=True)` can be used.
    *   **Correct Pattern:** The decorator `@database_sync_to_async` should wrap a *synchronous* helper function (`def _my_sync_db_call(): ...`) that contains *only* the ORM operations. The main `async def` function should then `await` this decorated helper function. **Do not** apply the decorator directly to the `async def` function containing the ORM calls.
    *   **Refined Async DB Pattern:** As mentioned in `PLANNING.md`, keep the synchronous DB block minimal and perform subsequent `await` calls *outside* the decorated function.
*   **Test Layers:** Understand the distinction between unit tests (fully mocked, no Django/DB) and integration/workflow tests (using `@pytest.mark.django_db`).
*   **Mocking Strategy:** The need for a consistent approach, especially the documented strategy for testing management commands.
*   **`SynchronousOnlyOperation` Error:** The specific Django error raised when sync DB calls are made from an async context without proper wrapping.

**Current Database Strategy:**
*   No standard Django migrations.
*   Relies on `ultimate_test_setup.py` and `run_seeders.py` for schema creation and data seeding during test setup.
*   `conftest.py` likely tries to coordinate with this.


**Testing Framework:**
*   Pytest (`pytest`)
*   Pytest Django (`pytest-django`) for DB integration.
*   Pytest Asyncio (`pytest-asyncio`) for running `async` tests.
*   `unittest.mock` (`patch`, `MagicMock`, `AsyncMock`) for mocking.
*   Custom test runners (`AgentTestRunner`, `WorkflowTestRunner`) in `apps/main/testing/`.


