#!/usr/bin/env python
"""
Test script to verify API integration with the benchmark fixes.
"""

import os
import sys
import django
import json
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from apps.main.models import BenchmarkRun


def test_api_integration():
    """Test that the API returns properly formatted data."""
    print("Testing API integration with benchmark fixes...")
    
    # Check if we have any benchmark runs
    runs_count = BenchmarkRun.objects.count()
    print(f"Total benchmark runs in database: {runs_count}")
    
    if runs_count == 0:
        print("No benchmark runs found. Cannot test API integration.")
        return
    
    # Test the API endpoint directly
    try:
        print("\n=== Testing API Endpoint ===")
        response = requests.get('http://localhost:8000/admin/benchmarks/api/run/')
        print(f"API Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"API Response Keys: {list(data.keys())}")
            
            if 'runs' in data and data['runs']:
                first_run = data['runs'][0]
                print(f"First Run Keys: {list(first_run.keys())}")
                
                # Test specific fields that the frontend expects
                expected_fields = [
                    'id', 'scenario_name', 'agent_role', 'execution_date',
                    'success_rate', 'semantic_score', 'mean_duration',
                    'trust_level', 'valence', 'arousal', 'stress_level', 'time_pressure',
                    'token_usage', 'cost', 'total_input_tokens', 'total_output_tokens', 'estimated_cost'
                ]
                
                missing_fields = []
                for field in expected_fields:
                    if field not in first_run:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"❌ Missing fields: {missing_fields}")
                else:
                    print("✅ All expected fields present")
                
                # Test data formatting
                print("\n=== Testing Data Formatting ===")
                
                # Token usage should be formatted as "input+output=total" or "N/A"
                token_usage = first_run.get('token_usage', '')
                if token_usage == 'N/A' or '+' in token_usage:
                    print(f"✅ Token usage properly formatted: {token_usage}")
                else:
                    print(f"❌ Token usage not properly formatted: {token_usage}")
                
                # Cost should be formatted as "$X.XXXXXX"
                cost = first_run.get('cost', '')
                if cost.startswith('$') and '.' in cost:
                    print(f"✅ Cost properly formatted: {cost}")
                else:
                    print(f"❌ Cost not properly formatted: {cost}")
                
                # Context variables should not be objects (except when N/A)
                trust_level = first_run.get('trust_level')
                if isinstance(trust_level, (int, float)) or trust_level == 'N/A':
                    print(f"✅ Trust level properly formatted: {trust_level}")
                else:
                    print(f"❌ Trust level is object, should be value: {trust_level}")
                
                # Test that numeric fields are actually numeric
                numeric_fields = ['success_rate', 'semantic_score', 'mean_duration', 'estimated_cost']
                for field in numeric_fields:
                    value = first_run.get(field)
                    if isinstance(value, (int, float)):
                        print(f"✅ {field} is numeric: {value}")
                    else:
                        print(f"❌ {field} is not numeric: {value} (type: {type(value)})")
                
                print("\n=== Sample API Response ===")
                print(json.dumps(first_run, indent=2, default=str))
                
            else:
                print("❌ No runs in API response")
        else:
            print(f"❌ API returned error: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the server. Make sure it's running on localhost:8000")
        return
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return
    
    print("\n=== API Integration Test Complete ===")


if __name__ == '__main__':
    test_api_integration()
