# backend/run_agent_tests.py
"""
Python-based runner for agent tests to ensure proper plugin loading.
This provides more control than the bash script and works across platforms.
"""

import os
import sys
import subprocess
import argparse

def setup_environment():
    """Set up the environment variables for testing"""
    os.environ["TESTING"] = "true"
    os.environ["DISABLE_DJANGO"] = "true"
    
    # Add the current directory to PYTHONPATH
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

def run_agent_tests(test_files=None, verbose=False):
    """Run the agent tests with proper configuration
    
    Args:
        test_files: Optional list of test files to run
        verbose: Whether to run with verbose output
    
    Returns:
        int: Exit code from pytest
    """
    # Set up environment
    setup_environment()
    
    # Change to the agent tests directory
    test_dir = os.path.join('apps', 'main', 'tests', 'test_agents')
    os.chdir(test_dir)
    
    # Build the pytest command
    pytest_args = [sys.executable, "-m", "pytest"]
    
    # Add verbose flag if requested
    if verbose:
        pytest_args.append("-v")
    
    # Add test files if provided
    if test_files:
        pytest_args.extend(test_files)
    
    # Print the command
    print(f"Running: {' '.join(pytest_args)}")
    print("-" * 50)
    
    # Run pytest
    result = subprocess.run(pytest_args)
    
    # Return to original directory
    os.chdir(os.path.join('..', '..', '..', '..'))
    
    return result.returncode

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run agent tests with proper configuration")
    parser.add_argument("test_files", nargs="*", help="Test files to run")
    parser.add_argument("-v", "--verbose", action="store_true", help="Run with verbose output")
    
    args = parser.parse_args()
    
    exit_code = run_agent_tests(args.test_files, args.verbose)
    
    print("-" * 50)
    if exit_code == 0:
        print("✅ Agent tests completed successfully!")
    else:
        print(f"❌ Agent tests failed with exit code {exit_code}")
    
    sys.exit(exit_code)