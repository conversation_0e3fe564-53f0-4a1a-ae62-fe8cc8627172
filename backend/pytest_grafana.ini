[tool:pytest]
# Pytest configuration for Grafana integration tests

# Test discovery
testpaths = apps/main/tests
python_files = test_grafana_*.py
python_classes = Test*
python_functions = test_*

# Test markers for Grafana integration
markers =
    grafana: Grafana integration tests
    grafana_views: Database view tests
    grafana_setup: Setup and configuration tests
    grafana_migration: Migration tests
    grafana_performance: Performance tests
    grafana_integration: End-to-end integration tests
    grafana_provisioning: Dashboard and datasource provisioning tests
    grafana_auth: Authentication and connectivity tests
    grafana_websocket: WebSocket functionality tests
    slow: Slow running tests
    database: Tests that require database access

# Django settings
DJANGO_SETTINGS_MODULE = myproject.settings.test
addopts =
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --reuse-db
    --nomigrations

# Coverage settings for Grafana integration
# Run with: pytest --cov=apps.main.migrations.0007_create_grafana_analytics_views
# Run with: pytest --cov=monitoring/

# Minimum test coverage thresholds
# --cov-fail-under=90

# Test output formatting
console_output_style = progress
junit_family = xunit2

# Timeout for long-running tests
timeout = 300

# Parallel execution (if pytest-xdist is installed)
# -n auto

# Test filtering examples:
# Run only Grafana tests: pytest -m grafana
# Run only view tests: pytest -m grafana_views
# Run only fast tests: pytest -m "not slow"
# Run specific test file: pytest apps/main/tests/test_grafana_integration.py
# Run specific test class: pytest apps/main/tests/test_grafana_integration.py::TestGrafanaAnalyticsViews
# Run specific test method: pytest apps/main/tests/test_grafana_integration.py::TestGrafanaAnalyticsViews::test_grafana_llm_performance_view_exists
