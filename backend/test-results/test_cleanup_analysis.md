# Test Cleanup Analysis

## Overview
After analyzing the test failures, I've identified several categories of issues that need to be addressed. Some tests are obsolete due to architectural changes, while others have legitimate failures that need fixing.

## Categories of Issues

### 1. Database Constraint Violations ✅ PARTIALLY FIXED
**Status**: Fixed for benchmark_history tests
**Root Cause**: Tests creating duplicate usernames/template names
**Solution**: Use `get_or_create` instead of `create` for shared fixtures

**Fixed Files**:
- `apps/admin_tools/benchmark/tests/test_benchmark_history.py` - Fixed admin_user and generic_agent fixtures

**Still Need Fixing**:
- `apps/main/tests/test_contextual_evaluation_templates.py` - Template name conflicts

### 2. Obsolete Tests (To Be Removed)
**Status**: NEEDS INVESTIGATION
**Candidates for Removal**:
- Some old agent tests that don't match current architecture
- Old benchmark validation approaches that have been replaced

**Need to Verify**:
- `test_validate_benchmarks_v3.py` - Initially thought obsolete, but command is still used
- Some ethical agent tests - may be testing deprecated functionality

### 3. Authentication/Authorization Issues (302/403 errors)
**Status**: NEEDS FIXING
**Root Cause**: Admin views not properly authenticated in tests
**Affected**: Multiple admin tool tests

### 4. Missing API Keys
**Status**: NEEDS CONFIGURATION
**Root Cause**: Mistral API key not configured for tests
**Affected**: All LLM integration tests

### 5. Database Connection Issues
**Status**: NEEDS INVESTIGATION
**Root Cause**: Connection closed prematurely
**Affected**: Scenario editing modal tests

## Next Steps

1. **Fix Template Name Conflicts**: Update contextual evaluation template tests
2. **Configure Test Authentication**: Fix admin view authentication
3. **Set Up API Keys**: Configure test environment for LLM tests
4. **Investigate Database Connection Issues**: Fix connection management
5. **Identify Truly Obsolete Tests**: Carefully review which tests can be safely removed

## Progress Tracking

- [x] Fixed benchmark_history test fixtures
- [ ] Fix contextual evaluation template name conflicts
- [ ] Fix admin authentication issues
- [ ] Configure API keys for LLM tests
- [ ] Fix database connection issues
- [ ] Remove obsolete tests (after careful verification)
