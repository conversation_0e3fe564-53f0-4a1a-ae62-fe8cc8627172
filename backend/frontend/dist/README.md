# Frontend Dist Directory

This directory was automatically created by the `fix_static_files` management command.

It exists to prevent Djan<PERSON> from raising errors when looking for static files in the
frontend/dist directory, which is referenced in STATICFILES_DIRS but doesn't actually
exist in the Docker container.

For proper frontend integration, you should:
1. Build the frontend assets
2. Copy them to this directory or configure Django to look elsewhere
