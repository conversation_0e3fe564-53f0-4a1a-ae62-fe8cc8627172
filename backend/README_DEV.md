# Backend Development

Last Updated: 2025-04-24

This document provides a minimal overview. For comprehensive information on setting up the development environment, understanding the architecture, database schema, agent system, and common development tasks, please refer to the main **[Developer Guide](./DEVELOPER_GUIDE.md)**.

## Generating Model Graphs

To generate a visual graph of Django's models (requires `graphviz`):
```bash
python manage.py graph_models user activity main -o models.png
