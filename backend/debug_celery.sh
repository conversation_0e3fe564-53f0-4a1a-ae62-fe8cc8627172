#!/bin/bash

# This script facilitates debugging Celery workers
# It launches Celery with debugpy attached

# Use a dedicated port for Celery debugging
DEBUG_PORT=5680

echo "======================================================================"
echo "Starting Celery worker with debugger attached"
echo "Debug port: $DEBUG_PORT"
echo "======================================================================"
echo "1. Set breakpoints in your Celery task code"
echo "2. Start VS Code debugger with 'Django: Celery Worker' configuration"
echo "3. Wait for the debugger to attach"
echo "======================================================================"

# Launch Celery worker with debugger
python -m debugpy --listen 0.0.0.0:$DEBUG_PORT --wait-for-client -m celery -A config worker --loglevel=info