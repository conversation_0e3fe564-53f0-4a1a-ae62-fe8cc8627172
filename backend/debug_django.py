#!/usr/bin/env python
import os
import sys
import debugpy
import subprocess



def setup_debugger():
    """Set up debugpy for VS Code debugging support"""
    wait_for_client = "--wait-for-debugger" in sys.argv
    if wait_for_client:
        sys.argv.remove("--wait-for-debugger")

    # Only set up the listener in the primary process
    if os.environ.get("RUN_MAIN") != "true":
        debugpy.listen(("0.0.0.0", 5678))
        if wait_for_client:
            print("Waiting for debugger to attach...")
            debugpy.wait_for_client()
            print("Debugger attached! Starting Uvicorn ASGI server...")
        else:
            print("Debugger can be attached (not waiting)...")
    else:
        print("Running in reloader child process; skipping debugpy.listen.")

def run_uvicorn(args):
    """Run Django using Uvicorn ASGI server"""
    # Default settings
    host = "0.0.0.0"
    port = 8000
    workers = 1  # Use 1 worker for debugging to ensure breakpoints work as expected
    reload = True if os.environ.get("DJANGO_DEBUG") == "1" else False
    
    # Extract Django management command if present
    management_command = None
    management_args = []
    
    if len(args) > 1 and args[1] not in ['-h', '--help', '--version']:
        if not args[1].startswith('--'):
            management_command = args[1]
            management_args = args[2:]
    
    # If a management command is provided, run it with Django
    if management_command:
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.dev")
        from django.core.management import execute_from_command_line
        execute_from_command_line([args[0]] + [management_command] + management_args)
        return
    
    # Otherwise, run with Uvicorn
    uvicorn_args = [
        "uvicorn",
        "config.asgi:application",
        f"--host={host}",
        f"--port={port}",
        f"--workers={workers}",
        "--log-level=info",
    ]
    
    if reload:
        uvicorn_args.append("--reload")
    
    print(f"Starting Django with Uvicorn ASGI server: {' '.join(uvicorn_args)}")
    subprocess.run(uvicorn_args)

if __name__ == "__main__":
    # Set up Django settings
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.dev")
    
    # Set up debugger
    setup_debugger()
    
    # Run with Uvicorn
    run_uvicorn(sys.argv)