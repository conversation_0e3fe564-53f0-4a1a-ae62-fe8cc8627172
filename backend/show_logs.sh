#!/bin/bash

# Detect environment
IS_WSL=false
if grep -q Microsoft /proc/version || grep -q microsoft /proc/version; then
    IS_WSL=true
    echo "Running in WSL environment"
else
    echo "Running in native Linux environment"
fi

# Get the script's directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"  # Change to the script's directory

# Explicitly specify the docker-compose.yml file path
if [ -f "docker-compose.yml" ]; then
    COMPOSE_FILE="docker-compose.yml"
elif [ -f "../docker-compose.yml" ]; then
    COMPOSE_FILE="../docker-compose.yml"
elif [ -f "backend/docker-compose.yml" ]; then
    COMPOSE_FILE="backend/docker-compose.yml"
else
    echo "Error: docker-compose.yml file not found in current directory, parent directory, or backend/ directory."
    exit 1
fi

echo "Using Docker Compose file: $COMPOSE_FILE"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed or not in PATH"
    exit 1
fi

# Start Docker Compose in detached mode
echo "Starting Docker Compose services..."
docker compose -f "$COMPOSE_FILE" up -d --build

# Wait for services to start
sleep 3

# Function to check if a package is installed
check_package() {
    if command -v apt-get &> /dev/null; then
        if ! dpkg -l | grep -q "\\b$1\\b"; then
            echo "Package $1 is not installed. Installing..."
            sudo apt-get update && sudo apt-get install -y "$1"
            return $?
        fi
    elif command -v yum &> /dev/null; then
        if ! rpm -qa | grep -q "\\b$1\\b"; then
            echo "Package $1 is not installed. Installing..."
            sudo yum install -y "$1"
            return $?
        fi
    else
        echo "Unknown package manager. Please install $1 manually."
        return 1
    fi
    return 0
}

# Try to use tmux for displaying logs
if ! command -v tmux &> /dev/null; then
    echo "tmux not found. Attempting to install..."
    if check_package tmux; then
        echo "tmux installed successfully."
    else
        echo "Failed to install tmux. Will display logs in current terminal."
        docker compose -f "$COMPOSE_FILE" logs -f
        exit 0
    fi
fi

echo "Using tmux to display logs in split panes..."

# Kill any existing session with the same name
tmux kill-session -t docker_logs 2>/dev/null || true

# Create a new session
tmux new-session -d -s docker_logs "docker compose -f \"$COMPOSE_FILE\" logs -f web"

# Split the window and run the other commands
tmux split-window -h -t docker_logs "docker compose -f \"$COMPOSE_FILE\" logs -f celery"
tmux split-window -v -t docker_logs:0.0 "docker compose -f \"$COMPOSE_FILE\" logs -f db"
tmux split-window -v -t docker_logs:0.1 "docker compose -f \"$COMPOSE_FILE\" logs -f redis"

# Set window title
tmux rename-window -t docker_logs:0 "Docker Logs"

# Attach to the session
echo "Attaching to tmux session. Press Ctrl+B then D to detach."
echo "To reattach later, run: tmux attach-session -t docker_logs"
sleep 1
tmux attach-session -t docker_logs