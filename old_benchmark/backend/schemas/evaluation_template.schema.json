{"$schema": "http://json-schema.org/draft-07/schema#", "$version": "1.0.0", "title": "Evaluation Template <PERSON><PERSON><PERSON>", "description": "Schema for evaluation criteria templates used in benchmark scenarios", "type": "object", "required": ["template_name", "criteria"], "properties": {"template_name": {"type": "string", "description": "Unique name for the evaluation template"}, "description": {"type": "string", "description": "Description of the evaluation template"}, "criteria": {"type": "object", "description": "Evaluation criteria organized by dimensions", "patternProperties": {"^[A-Za-z]+$": {"type": "array", "description": "List of criteria for a specific dimension", "items": {"type": "string"}}}, "additionalProperties": false}, "dimension_weights": {"type": "object", "description": "Optional weights for each dimension (must sum to 1.0)", "patternProperties": {"^[A-Za-z]+$": {"type": "number", "minimum": 0, "maximum": 1}}, "additionalProperties": false}, "scoring_thresholds": {"type": "object", "description": "Optional thresholds for score interpretation", "properties": {"excellent": {"type": "number", "minimum": 0, "maximum": 1}, "good": {"type": "number", "minimum": 0, "maximum": 1}, "acceptable": {"type": "number", "minimum": 0, "maximum": 1}, "poor": {"type": "number", "minimum": 0, "maximum": 1}}, "additionalProperties": false}, "evaluator_models": {"type": "array", "description": "List of LLM models to use for evaluation", "items": {"type": "string"}}}, "additionalProperties": false}