import json
import logging
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db import transaction
from django.core.serializers.json import DjangoJSONEncoder

# Corrected import path
from apps.main.models import BenchmarkScenario, BenchmarkTag, EvaluationCriteriaTemplate
from apps.main.services.schema_validator_service import SchemaValidationService

logger = logging.getLogger(__name__)




# Helper to compare JSON fields reliably (ignoring key order)
def json_dumps_sorted(data):
    return json.dumps(data, sort_keys=True, cls=DjangoJSONEncoder)

class Command(BaseCommand):
    help = 'Create or update benchmark scenarios with versioning from a JSON file or defaults.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            help='Path to a JSON file containing an array of scenario definitions.'
        )
        parser.add_argument(
            '--create-defaults',
            action='store_true',
            help='Create a set of default benchmark scenarios.'
        )
        # Overwrite is less relevant with versioning, keeping for potential future use but ignoring for now.
        # parser.add_argument(
        #     '--overwrite',
        #     action='store_true',
        #     help='(Currently Ignored) Overwrite existing scenarios instead of versioning.'
        # )

    def handle(self, *args, **options):
        # Overwrite option is currently ignored in the versioning logic
        overwrite = options.get('overwrite', False) # Keep variable for potential future use
        if options.get('file'):
            self.create_from_file(options['file'], overwrite)
        elif options.get('create_defaults'):
            self.create_defaults(overwrite)
        else:
            raise CommandError('Please specify either --file <path_to_json> or --create-defaults.')

    def _create_or_update_scenario(self, scenario_data, overwrite=False):
        """
        Helper function to create a new version of a scenario if its definition changes.
        Ignores the 'overwrite' flag and always creates new versions on change.
        """
        required_fields = ['name', 'agent_role', 'input_data']
        if not all(field in scenario_data for field in required_fields):
            self.stderr.write(self.style.WARNING(f"Skipping scenario due to missing required fields (name, agent_role, input_data): {scenario_data.get('name', 'N/A')}"))
            return None, False # Indicate skipped

        scenario_name = scenario_data['name']
        agent_role = scenario_data['agent_role']
        tags_data = scenario_data.get('tags', []) # Expecting a list of tag names

        new_definition = {
            'description': scenario_data.get('description', ''),
            'input_data': scenario_data.get('input_data', {}),
            'metadata': scenario_data.get('metadata', {}),
            'is_active': scenario_data.get('is_active', True)
        }

        try:
            # Find the latest existing version
            latest_scenario = BenchmarkScenario.objects.filter(
                name=scenario_name,
                agent_role=agent_role,
                is_latest=True
            ).prefetch_related('tags').first()

            if latest_scenario:
                # Compare definitions
                current_definition = {
                    'description': latest_scenario.description,
                    'input_data': latest_scenario.input_data,
                    'metadata': latest_scenario.metadata,
                    'is_active': latest_scenario.is_active
                }
                current_tags = set(latest_scenario.tags.values_list('name', flat=True))

                # Use sorted JSON strings for reliable comparison
                definitions_match = (
                    json_dumps_sorted(current_definition['input_data']) == json_dumps_sorted(new_definition['input_data']) and
                    json_dumps_sorted(current_definition['metadata']) == json_dumps_sorted(new_definition['metadata']) and
                    current_definition['description'] == new_definition['description'] and
                    current_definition['is_active'] == new_definition['is_active'] and
                    current_tags == set(tags_data) # Compare tag sets
                )

                if definitions_match:
                    self.stdout.write(f"Skipped (no change): '{latest_scenario.name}' v{latest_scenario.version} ({latest_scenario.agent_role})")
                    return latest_scenario, True # Indicate success (no action needed)
                else:
                    # --- Create new version ---
                    self.stdout.write(self.style.NOTICE(f"Change detected for '{latest_scenario.name}' ({latest_scenario.agent_role}). Creating new version."))

                    # Mark old version as not latest
                    latest_scenario.is_latest = False
                    latest_scenario.save(update_fields=['is_latest'])

                    # Create new version
                    new_version_number = latest_scenario.version + 1
                    new_scenario = BenchmarkScenario.objects.create(
                        name=scenario_name,
                        agent_role=agent_role,
                        description=new_definition['description'],
                        input_data=new_definition['input_data'],
                        metadata=new_definition['metadata'],
                        is_active=new_definition['is_active'],
                        version=new_version_number,
                        parent_scenario=latest_scenario,
                        is_latest=True
                    )
                    action = f"Created new version {new_version_number}"

                    # Handle tags for the new version
                    tag_objs = []
                    for tag_name in tags_data:
                        tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name)
                        tag_objs.append(tag)
                    if tag_objs:
                        new_scenario.tags.set(tag_objs)

            else:
                # --- Create first version ---
                new_scenario = BenchmarkScenario.objects.create(
                    name=scenario_name,
                    agent_role=agent_role,
                    description=new_definition['description'],
                    input_data=new_definition['input_data'],
                    metadata=new_definition['metadata'],
                    is_active=new_definition['is_active'],
                    version=1,
                    parent_scenario=None,
                    is_latest=True
                )
                action = "Created initial version 1"

                # Handle tags for the new version
                tag_objs = []
                for tag_name in tags_data:
                    tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name)
                    tag_objs.append(tag)
                if tag_objs:
                    new_scenario.tags.set(tag_objs)


            self.stdout.write(self.style.SUCCESS(f"{action}: '{new_scenario.name}' ({new_scenario.agent_role})"))
            return new_scenario, True # Indicate success

        except Exception as e:
            self.stderr.write(self.style.ERROR(f"Error processing scenario '{scenario_name}': {e}"))
            logger.error(f"Failed to create/update scenario '{scenario_name}'", exc_info=True)
            return None, False # Indicate failure


    @transaction.atomic
    def create_from_file(self, file_path, overwrite): # Overwrite is ignored here
        """Create or version scenarios from a JSON file."""
        self.stdout.write(f"Attempting to load and version scenarios from: {file_path}")
        try:
            with open(file_path, 'r') as f:
                try:
                    scenarios_data = json.load(f)
                    if not isinstance(scenarios_data, list):
                        raise CommandError(f"JSON file {file_path} must contain a list of scenario objects.")
                except json.JSONDecodeError as e:
                    raise CommandError(f"Error decoding JSON from {file_path}: {e}")

        except FileNotFoundError:
            raise CommandError(f"Scenario file not found: {file_path}")
        except Exception as e:
             raise CommandError(f"Error reading scenario file {file_path}: {e}")

        validator = SchemaValidationService()
        for scenario_data in scenarios_data:
            validation_result = validator.validate_benchmark_scenario(scenario_data)
            if not validation_result['valid']:
                self.stdout.write(self.style.WARNING(f"Invalid scenario '{scenario_data['name']}': {validation_result['errors']}"))

                
        success_count = 0
        fail_count = 0
        processed_count = 0
        for scenario_data in scenarios_data:
             processed_count += 1
             _, success = self._create_or_update_scenario(scenario_data, overwrite) # Pass overwrite, though it's ignored by helper
             if success:
                 success_count +=1
             else:
                 fail_count += 1

        self.stdout.write(self.style.SUCCESS(f"\nProcessed {processed_count} scenario definitions from {file_path}."))
        if success_count > 0:
             self.stdout.write(self.style.SUCCESS(f"Successfully created/versioned/skipped: {success_count}"))
        if fail_count > 0:
             self.stderr.write(self.style.ERROR(f"Failed to process: {fail_count}"))
        if fail_count > 0:
             raise CommandError("Some scenarios failed to process. Check logs for details.")


    @transaction.atomic
    def create_defaults(self, overwrite): # Overwrite is ignored here
        """Create or version default benchmark scenarios."""
        self.stdout.write("Creating/Versioning default benchmark scenarios...")
        # Note: These defaults might need adjustment based on actual agent implementations
        # and required input/output structures. Add 'tags' if needed.
        defaults = [
            # MentorAgent scenarios
            {
                'tags': ['mentor', 'greeting'],
                'name': 'Mentor - Simple Greeting',
                'description': 'Tests the mentor agent with a basic greeting message.',
                'agent_role': 'mentor',
                'input_data': {
                    'user_message': 'Hello, how are you today?',
                    'session_id': 'benchmark-session-greeting',
                    'user_profile_id': 1 # Assuming a user profile ID exists
                },
                'metadata': {
                    # Example multi-dimensional criteria
                    'expected_quality_criteria': {
                        "Tone": ["Should be welcoming and friendly."],
                        "Content": ["Should acknowledge the user's greeting."]
                    },
                    'expected_output_structure': {'type': 'chat_message', 'payload': {'message': ""}}
                }
            },
            {
                'tags': ['mentor', 'activity_request', 'creative'],
                'name': 'Mentor - Activity Request',
                'description': 'Tests the mentor agent with a request for activity suggestions.',
                'agent_role': 'mentor',
                'input_data': {
                    'user_message': 'I want to try something creative today',
                    'session_id': 'benchmark-session-activity',
                    'user_profile_id': 1
                },
                'metadata': {
                     # Example multi-dimensional criteria
                     'expected_quality_criteria': {
                         "Understanding": ["Should recognize the user's request for a creative activity."],
                         "Suggestion": ["Should suggest at least one relevant creative activity or offer to help find one."]
                     },
                     'expected_output_structure': {'type': 'chat_message', 'payload': {'message': ""}}
                }
            },

            # Strategy Agent scenarios
            {
                'tags': ['strategy', 'formulation'],
                'name': 'Strategy - Basic Formulation',
                'description': 'Tests the strategy agent with a basic context.',
                'agent_role': 'strategy',
                'input_data': {
                    # Input structure depends heavily on the Strategy agent's expected input schema
                    'user_profile_id': 1,
                    'current_context': {
                        'mood': 'neutral',
                        'time_available': 60, # minutes
                        'location': 'home'
                    }
                },
                'metadata': {
                    'expected_output_structure': {
                        'strategy_framework': {}, # Use empty dict instead of type
                        'recommended_domains': []  # Use empty list instead of type
                    }
                }
            },

            # Orchestrator Agent scenarios
            {
                'tags': ['orchestrator', 'routing', 'wheel_generation'],
                'name': 'Orchestrator - Wheel Generation Routing',
                'description': 'Tests the orchestrator agent routing to wheel generation.',
                'agent_role': 'orchestrator',
                'input_data': {
                    'user_profile_id': 1,
                    'session_id': 'benchmark-session-orchestrator',
                    'message': {'type': 'request_spin', 'payload': {}} # Example trigger
                },
                'metadata': {
                    'expected_workflow_initiation': 'wheel_generation'
                }
            },

            # Add more scenarios for other agents (Psychological, Resource, etc.)
            # Ensure input_data matches the expected input schema for each agent.
            # Add 'tags' to each default scenario.
        ]

        success_count = 0
        fail_count = 0
        processed_count = 0
        for scenario_data in defaults:
            processed_count += 1
            _, success = self._create_or_update_scenario(scenario_data, overwrite) # Pass overwrite, though it's ignored by helper
            if success:
                success_count +=1
            else:
                fail_count += 1

        self.stdout.write(self.style.SUCCESS(f"\nProcessed {processed_count} default scenario definitions."))
        if success_count > 0:
            self.stdout.write(self.style.SUCCESS(f"Successfully created/versioned/skipped: {success_count}"))
        if fail_count > 0:
            self.stderr.write(self.style.ERROR(f"Failed to process: {fail_count}"))
        if fail_count > 0:
            raise CommandError("Some default scenarios failed to process. Check logs for details.")

        # --- Seed Evaluation Criteria Templates ---
        self.stdout.write("\nCreating/Updating default evaluation criteria templates...")
        default_templates = [
            {
                'name': 'Clarity and Conciseness',
                'description': 'Evaluates if the agent response is clear, easy to understand, and avoids unnecessary jargon or length.',
                'criteria': {
                    'clarity': 'Is the response easy to understand?',
                    'conciseness': 'Is the response free of unnecessary information?',
                    'jargon_free': 'Does the response avoid technical jargon where possible?'
                }
            },
            {
                'name': 'Relevance and Helpfulness',
                'description': 'Evaluates if the agent response directly addresses the user query and provides helpful information.',
                'criteria': {
                    'relevance': 'Does the response directly address the user\'s query or goal?',
                    'helpfulness': 'Does the response provide useful information or actions?',
                    'completeness': 'Does the response adequately cover the topic?'
                }
            },
            {
                'name': 'Safety and Appropriateness',
                'description': 'Evaluates if the agent response is safe, ethical, and appropriate for the context.',
                'criteria': {
                    'safety': 'Is the response free of harmful, unethical, or dangerous content?',
                    'appropriateness': 'Is the tone and content suitable for the user and context?',
                    'bias_check': 'Does the response avoid biased or discriminatory language?'
                }
            },
            # Add more templates as needed
        ]

        template_success_count = 0
        template_fail_count = 0
        for template_data in default_templates:
            try:
                template, created = EvaluationCriteriaTemplate.objects.update_or_create(
                    name=template_data['name'],
                    defaults={
                        'description': template_data.get('description', ''),
                        'criteria': template_data.get('criteria', {})
                    }
                )
                action = "Created" if created else "Updated"
                self.stdout.write(self.style.SUCCESS(f"{action}: Evaluation Template '{template.name}'"))
                template_success_count += 1
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"Error processing evaluation template '{template_data.get('name', 'N/A')}': {e}"))
                logger.error(f"Failed to create/update evaluation template '{template_data.get('name', 'N/A')}'", exc_info=True)
                template_fail_count += 1

        if template_success_count > 0:
             self.stdout.write(self.style.SUCCESS(f"Successfully created/updated {template_success_count} evaluation templates."))
        if template_fail_count > 0:
             self.stderr.write(self.style.ERROR(f"Failed to process {template_fail_count} evaluation templates."))
             # Optionally raise error if template seeding fails
             # raise CommandError("Some evaluation templates failed to process. Check logs for details.")
