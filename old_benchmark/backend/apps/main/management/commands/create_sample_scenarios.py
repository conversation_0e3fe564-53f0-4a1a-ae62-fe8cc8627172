"""
Django management command for creating sample benchmark scenarios.
"""

import os
import json
import logging
from django.core.management.base import BaseCommand
from apps.main.models import BenchmarkScenario, BenchmarkTag

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Creates sample benchmark scenarios for testing'

    def handle(self, *args, **options):
        # Create sample scenarios
        self.stdout.write("Creating sample benchmark scenarios...")
        
        # Create tags
        mentor_tag, _ = BenchmarkTag.objects.get_or_create(name="mentor")
        workflow_tag, _ = BenchmarkTag.objects.get_or_create(name="workflow")
        wheel_gen_tag, _ = BenchmarkTag.objects.get_or_create(name="wheel_generation")
        discussion_tag, _ = BenchmarkTag.objects.get_or_create(name="discussion")
        test_tag, _ = BenchmarkTag.objects.get_or_create(name="test")
        
        # Create mentor wheel generation scenario
        mentor_wheel_scenario, created = BenchmarkScenario.objects.update_or_create(
            name="test_mentor_wheel_generation",
            defaults={
                "description": "Test scenario for mentor in wheel generation workflow",
                "agent_role": "mentor",
                "input_data": {
                    "user_message": "I want to try something new today",
                    "context_packet": {
                        "workflow_type": "wheel_generation",
                        "trust_level": 65
                    }
                },
                "metadata": {
                    "workflow_type": "wheel_generation",
                    "expected_quality_criteria": {
                        "Tone": ["supportive", "friendly", "encouraging"],
                        "Content": ["relevant", "helpful", "personalized"]
                    }
                },
                "is_active": True,
                "version": 1,
                "is_latest": True
            }
        )
        mentor_wheel_scenario.tags.set([mentor_tag, wheel_gen_tag, test_tag])
        
        action = "Created" if created else "Updated"
        self.stdout.write(f"{action} mentor wheel generation scenario")
        
        # Create mentor discussion scenario
        mentor_discussion_scenario, created = BenchmarkScenario.objects.update_or_create(
            name="test_mentor_discussion",
            defaults={
                "description": "Test scenario for mentor in discussion workflow",
                "agent_role": "mentor",
                "input_data": {
                    "user_message": "I'm feeling stuck with my current activities",
                    "context_packet": {
                        "workflow_type": "discussion",
                        "trust_level": 50
                    }
                },
                "metadata": {
                    "workflow_type": "discussion",
                    "expected_quality_criteria": {
                        "Tone": ["empathetic", "understanding", "patient"],
                        "Content": ["insightful", "thought-provoking", "relevant"]
                    }
                },
                "is_active": True,
                "version": 1,
                "is_latest": True
            }
        )
        mentor_discussion_scenario.tags.set([mentor_tag, discussion_tag, test_tag])
        
        action = "Created" if created else "Updated"
        self.stdout.write(f"{action} mentor discussion scenario")
        
        # Create workflow wheel generation scenario
        workflow_wheel_scenario, created = BenchmarkScenario.objects.update_or_create(
            name="test_workflow_wheel_generation",
            defaults={
                "description": "Test scenario for wheel generation workflow",
                "agent_role": "workflow",
                "input_data": {
                    "user_message": "I need some new activities",
                    "context_packet": {
                        "workflow_type": "wheel_generation",
                        "trust_level": 70
                    }
                },
                "metadata": {
                    "workflow_type": "wheel_generation",
                    "expected_quality_criteria": {
                        "Efficiency": ["timely", "streamlined", "responsive"],
                        "Quality": ["accurate", "comprehensive", "personalized"]
                    }
                },
                "is_active": True,
                "version": 1,
                "is_latest": True
            }
        )
        workflow_wheel_scenario.tags.set([workflow_tag, wheel_gen_tag, test_tag])
        
        action = "Created" if created else "Updated"
        self.stdout.write(f"{action} workflow wheel generation scenario")
        
        self.stdout.write(self.style.SUCCESS("Successfully created sample benchmark scenarios"))
