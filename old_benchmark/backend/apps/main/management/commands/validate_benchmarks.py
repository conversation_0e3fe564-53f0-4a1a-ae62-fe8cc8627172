"""
Django management command for validating benchmark scenarios using Pydantic models.
"""

import os
import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, Set
import re

from django.core.management.base import BaseCommand, CommandError

from apps.main.models import BenchmarkScenario, EvaluationCriteriaTemplate
from apps.main.schemas.benchmark import (
    PydanticSchemaValidator, BenchmarkNamingConvention
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Add a handler to output logs to the console
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)


class Command(BaseCommand):
    help = 'Validate benchmark scenarios using Pydantic models with enhanced error reporting'

    def add_arguments(self, parser):
        parser.add_argument(
            '--scenario-id',
            type=int,
            help='Validate a specific scenario by ID'
        )
        parser.add_argument(
            '--agent-role',
            type=str,
            help='Validate scenarios for a specific agent role'
        )
        parser.add_argument(
            '--file',
            type=str,
            help='Validate a specific file'
        )
        parser.add_argument(
            '--directory',
            type=str,
            help='Validate all files in a specific directory'
        )
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix common issues in scenarios'
        )
        parser.add_argument(
            '--rename',
            action='store_true',
            help='Rename files to follow the naming convention'
        )
        parser.add_argument(
            '--export',
            action='store_true',
            help='Export scenario definitions as schema-compliant JSON files'
        )
        parser.add_argument(
            '--output-dir',
            type=str,
            default='validated_scenarios',
            help='Directory to save validated scenario files (defaults to "validated_scenarios")'
        )
        parser.add_argument(
            '--report-format',
            type=str,
            choices=['json', 'text'],
            default='text',
            help='Format for validation reports (defaults to "text")'
        )
        parser.add_argument(
            '--validate-files',
            action='store_true',
            help='Validate scenario files in the benchmark data directory'
        )
        parser.add_argument(
            '--validate-structure',
            action='store_true',
            help='Validate the directory structure'
        )
        parser.add_argument(
            '--validate-names',
            action='store_true',
            help='Validate file names against the naming convention'
        )
        parser.add_argument(
            '--comprehensive',
            action='store_true',
            help='Perform comprehensive validation (structure, files, names, and database)'
        )

    def handle(self, *args, **options):
        # Set up event loop and run async logic
        try:
            asyncio.run(self.async_handle(**options))
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"An unexpected error occurred: {e}"))
            import traceback
            self.stderr.write(traceback.format_exc())
            raise

    # Import the implementation from the v3 version to avoid duplication
    from apps.main.management.commands.validate_benchmarks_v3 import Command as CommandV3

    # Use the implementation from v3
    async_handle = CommandV3.async_handle
    validate_directory_structure = CommandV3.validate_directory_structure
    validate_file = CommandV3.validate_file
    validate_file_name = CommandV3.validate_file_name
    get_existing_ids = CommandV3.get_existing_ids
    validate_directory = CommandV3.validate_directory
    validate_all_files = CommandV3.validate_all_files
    fix_scenario = CommandV3.fix_scenario
    fix_file = CommandV3.fix_file
    export_scenarios = CommandV3.export_scenarios
