# backend/apps/main/tests/test_agents/test_benchmarking.py
import pytest
import asyncio
from unittest.mock import Magic<PERSON><PERSON>, AsyncMock, patch, call, ANY
import time
import json # Added for potential future use if output needs parsing

from apps.main.agents.benchmarking import AgentB<PERSON>chmarkImproved, BenchmarkResult
from apps.main.testing.mock_database_service import MockDatabaseService
from apps.main.testing.mock_tool_registry import MockToolRegistry
# MockLLMService import removed

# Import llm_config fixture
from apps.main.tests.test_agents.conftest import llm_config

# --- Mock Agent Class ---
class MockAgent:
    agent_role = "MockAgentRole"

    # Accept **kwargs to handle unexpected arguments like llm_config
    def __init__(self, user_profile_id, **kwargs):
        self.user_profile_id = user_profile_id
        self.db_service = None # Will be injected by benchmark runner
        self.tool_registry = None # Will be injected by benchmark runner
        self.llm_service = None # Agent might have this, but benchmark runner won't inject mock
        # self.process = AsyncMock() # This is assigned to instance, patch needs it on class

    async def process(self, *args, **kwargs):
        """Mock process method for patching."""
        # This method exists so patch.object can find it on the class.
        # The actual mock behavior is provided by AsyncMock in the test.
        pass

# --- Fixtures ---

@pytest.fixture
def mock_db_service():
    """Fixture for MockDatabaseService."""
    service = MockDatabaseService()
    service.reset_memory_op_count = MagicMock()
    service.get_memory_op_count = MagicMock(return_value=0)
    return service

@pytest.fixture
def mock_tool_registry():
    """Fixture for MockToolRegistry."""
    registry = MockToolRegistry()
    # Ensure the 'calls' attribute exists for aggregation logic
    registry.calls = []
    # Create a mock reset method that also clears the calls list
    original_reset = registry.reset if hasattr(registry, 'reset') else lambda: None
    def mock_reset_and_clear():
        registry.calls.clear()
        original_reset() # Call original reset if it exists
    registry.reset = MagicMock(side_effect=mock_reset_and_clear)

    # Keep the original mocks for specific methods if they are used elsewhere
    registry.reset_call_counts = MagicMock()
    registry.get_call_counts = MagicMock(return_value={})
    return registry

@pytest.fixture
def agent_benchmark(mock_db_service, mock_tool_registry, llm_config):
    """Fixture for AgentBenchmarkImproved instance."""
    # Instantiate the benchmark runner with the *class* and DB/Tool mocks only
    runner = AgentBenchmarkImproved(
        agent_class=MockAgent,
        user_profile_id="test_user_123",
        mock_db_service=mock_db_service,
        mock_tool_registry=mock_tool_registry,
        agent_llm_config=llm_config
        # No mock_llm_service passed
    )
    return runner

# --- Test Class ---

@pytest.mark.asyncio
class TestAgentBenchmarkImproved:

    async def test_run_benchmark_successful(self, agent_benchmark, mock_db_service, mock_tool_registry):
        """Test a successful benchmark run with warmup."""
        # --- Arrange ---
        scenario_name = "SuccessScenario"
        input_data = {"input": "test"}
        initial_state = {"workflow_id": "wf-1"}
        runs = 2
        warmup_runs = 1

        async def process_side_effect(*args, **kwargs):
            call_number = mock_process.call_count # Use mock_process.call_count
            await asyncio.sleep(0.05 + (call_number * 0.01))
            mock_db_service.get_memory_op_count.return_value = call_number
            # Simulate tool call recording for non-warmup runs
            is_warmup_run = call_number <= warmup_runs
            if not is_warmup_run:
                tool_name = f'tool{call_number}'
                mock_tool_registry.calls.append({'tool_code': tool_name, 'params': {}, 'response': 'ok'})
            # Return value doesn't need get_call_counts anymore
            return {"output_data": {"result": f"output_{call_number}"}, "error": None}

        # Patch the process method on the MockAgent class
        with patch.object(MockAgent, 'process', new_callable=AsyncMock) as mock_process:
            mock_process.side_effect = process_side_effect
            mock_process.reset_mock() # Reset the mock before the call

            # --- Act ---
            result = await agent_benchmark.run_benchmark(
            scenario_name=scenario_name,
            input_data=input_data,
            initial_state=initial_state,
            runs=runs,
            warmup_runs=warmup_runs
        )

        # --- Assert ---
        assert isinstance(result, BenchmarkResult)
        assert result.agent_role == "MockAgentRole"
        assert result.scenario_name == scenario_name
        assert result.runs == runs
        assert len(result.durations) == runs
        assert result.success_rate == 1.0
        assert not result.errors
        assert result.last_output_data == {"result": f"output_{warmup_runs + runs}"}
        # assert not hasattr(result, 'llm_calls') # Check llm_calls field is removed

        assert mock_db_service.reset_memory_op_count.call_count == warmup_runs + runs
        # mock_tool_registry doesn't have reset_call_counts, check reset instead
        assert mock_tool_registry.reset.call_count == warmup_runs + runs
        assert mock_process.call_count == warmup_runs + runs

        assert result.memory_operations == sum(range(warmup_runs + 1, warmup_runs + runs + 1))
        # Tool counts are now aggregated within the runner from mock_tool_registry.calls
        # Let's adjust the assertion based on the side effect logic
        expected_tools = {}
        for i in range(warmup_runs + 1, warmup_runs + runs + 1):
             expected_tools[f'tool{i}'] = expected_tools.get(f'tool{i}', 0) + 1
        assert result.tool_call_counts == expected_tools

        assert all(d > 0.05 for d in result.durations)
        assert result.mean_duration > 0.05

        expected_state_base = {
            **initial_state,
            "context_packet": input_data,
            "initial_context_packet": input_data,
            "conversation_history": [],
            "current_stage": "initial_conversation"
        }
        first_actual_run_call_index = warmup_runs
        # Assert on the mock_process object
        call_args, call_kwargs = mock_process.call_args_list[first_actual_run_call_index]
        assert call_args[0] == expected_state_base

    async def test_run_benchmark_with_failures(self, agent_benchmark, mock_db_service, mock_tool_registry):
        """Test a benchmark run where some runs fail."""
        # --- Arrange ---
        scenario_name = "FailureScenario"
        input_data = {"input": "fail_sometimes"}
        runs = 3
        warmup_runs = 0

        async def process_side_effect(*args, **kwargs):
            call_number = agent_benchmark._current_agent_instance.process.call_count
            await asyncio.sleep(0.01)
            if call_number == 2:
                mock_db_service.get_memory_op_count.return_value = 0
                mock_tool_registry.get_call_counts.return_value = {}
                raise ValueError("Simulated processing error")
            mock_db_service.get_memory_op_count.return_value = 1
            mock_tool_registry.get_call_counts.return_value = {'tool_ok': 1} # Keep this for side effect logic
            # Simulate tool calls being recorded by the registry within the side effect
            mock_tool_registry.calls.append({'tool_code': 'tool_ok', 'params': {}, 'response': 'ok'})
            return {"output_data": {"result": f"output_{call_number}"}, "error": None}

        # Patch the process method on the MockAgent class
        with patch.object(MockAgent, 'process', new_callable=AsyncMock) as mock_process:
            mock_process.side_effect = process_side_effect
            mock_process.reset_mock() # Reset the mock before the call
            mock_tool_registry.reset() # Reset the tool registry before the run

            # --- Act ---
            result = await agent_benchmark.run_benchmark(
            scenario_name=scenario_name,
            input_data=input_data,
            runs=runs,
            warmup_runs=warmup_runs
        )

        # --- Assert ---
        assert result.runs == runs
        assert len(result.durations) == runs - 1
        assert result.success_rate == (runs - 1) / runs
        assert len(result.errors) == 1
        assert "Run 2: Simulated processing error" in result.errors[0]
        assert result.last_output_data == {"result": f"output_{runs}"} # Last successful run was run 3

        assert result.memory_operations == 1 * (runs - 1) # Correct, only successful runs count
        # Tool calls are aggregated from successful runs
        assert result.tool_call_counts == {'tool_ok': runs - 1}

    async def test_run_benchmark_all_failures(self, agent_benchmark):
        """Test a benchmark run where all runs fail."""
        # --- Arrange ---
        scenario_name = "TotalFailureScenario"
        input_data = {"input": "always_fail"}
        runs = 2
        warmup_runs = 0

        # Patch the process method on the MockAgent class
        with patch.object(MockAgent, 'process', new_callable=AsyncMock) as mock_process:
            mock_process.side_effect = RuntimeError("Consistent failure")
            mock_process.reset_mock() # Reset the mock before the call

            # --- Act ---
            result = await agent_benchmark.run_benchmark(
            scenario_name=scenario_name,
            input_data=input_data,
            runs=runs,
            warmup_runs=warmup_runs
        )

        # --- Assert ---
        assert result.runs == runs
        assert len(result.durations) == 0
        assert result.success_rate == 0.0
        assert len(result.errors) == runs
        assert "Run 1: Consistent failure" in result.errors[0]
        assert "Run 2: Consistent failure" in result.errors[1]
        assert result.last_output_data is None
        assert result.mean_duration == 0
        assert result.median_duration == 0
        assert result.min_duration == 0
        assert result.max_duration == 0
        assert result.memory_operations == 0
        assert result.tool_call_counts == {}
        # assert not hasattr(result, 'llm_calls')

    async def test_run_benchmark_mock_injection(self, agent_benchmark, mock_db_service, mock_tool_registry):
        """Verify DB and Tool mocks are correctly injected into the agent instance."""
        # --- Arrange ---
        scenario_name = "MockInjectionTest"
        input_data = {"input": "check_mocks"}
        runs = 1
        warmup_runs = 0

        async def check_mocks(state):
            # Access the agent instance via the benchmark runner's internal attribute
            agent_instance = agent_benchmark._current_agent_instance
            assert agent_instance is not None
            assert agent_instance.db_service is mock_db_service # Check DB mock
            assert agent_instance.tool_registry is mock_tool_registry # Check Tool mock
            # LLM service should not be the mock passed to runner (it wasn't)
            # It should be whatever the agent initializes itself with (likely None or RealLLMClient)
            assert not hasattr(agent_instance, 'llm_service') or agent_instance.llm_service is not ANY # Ensure it's not accidentally mocked
            await asyncio.sleep(0.01)
            # Simulate tool call recording
            mock_tool_registry.calls.append({'tool_code': 'check_tool', 'params': {}, 'response': 'checked'})
            return {"output_data": {"result": "mock_checked"}, "error": None}

        # Patch the process method on the MockAgent class
        with patch.object(MockAgent, 'process', new_callable=AsyncMock) as mock_process:
            mock_process.side_effect = check_mocks
            mock_process.reset_mock() # Reset the mock before the call
            mock_db_service.reset_memory_op_count.reset_mock()
            mock_db_service.get_memory_op_count.reset_mock()
            # Remove manual reset here, runner handles it via the mocked reset method
            # mock_tool_registry.reset()
            mock_tool_registry.get_call_counts.reset_mock() # Keep this if used elsewhere

            # --- Act ---
            result = await agent_benchmark.run_benchmark(
            scenario_name=scenario_name,
            input_data=input_data,
            runs=runs,
            warmup_runs=warmup_runs
        )

        # --- Assert ---
        mock_process.assert_called_once() # Assert on the mock object
        assert mock_db_service.reset_memory_op_count.call_count == runs + warmup_runs
        # get_memory_op_count is called inside the side_effect, not directly asserted here
        # assert mock_db_service.get_memory_op_count.call_count == runs
        assert mock_tool_registry.reset.call_count == runs + warmup_runs
        # get_call_counts is called inside the side_effect, not directly asserted here
        # assert mock_tool_registry.get_call_counts.call_count == runs
        # Check the aggregated tool calls in the result
        assert result.tool_call_counts == {'check_tool': 1}


    async def test_run_benchmark_no_mocks_provided(self):
        """Test benchmark runner when no mocks are provided during init."""
        # --- Arrange ---
        with patch.object(MockAgent, 'process', new_callable=AsyncMock) as mock_process:
            mock_process.return_value = {"output_data": {"result": "ok"}, "error": None}

            benchmark_no_mocks = AgentBenchmarkImproved(
                agent_class=MockAgent,
                user_profile_id="user_no_mocks"
            )

            scenario_name = "NoMocksScenario"
            input_data = {"input": "data"}
            runs = 1
            warmup_runs = 0

            # --- Act ---
            result = await benchmark_no_mocks.run_benchmark(
                scenario_name=scenario_name,
                input_data=input_data,
                runs=runs,
                warmup_runs=warmup_runs
            )

            # --- Assert ---
            assert result.success_rate == 1.0
            assert len(result.durations) == 1
            assert result.memory_operations == 0
            assert result.tool_call_counts == {}
            mock_process.assert_called_once()



    async def test_run_benchmark_warmup_exclusion(self, agent_benchmark, mock_db_service, mock_tool_registry):
        """Verify warmup runs are executed but excluded from results."""
        # --- Arrange ---
        scenario_name = "WarmupTest"
        input_data = {"input": "warmup"}
        runs = 1
        warmup_runs = 2

        async def process_side_effect(*args, **kwargs):
            call_number = mock_process.call_count # Use mock_process.call_count
            await asyncio.sleep(0.01 * call_number)
            mock_db_service.get_memory_op_count.return_value = call_number
            # Simulate tool call recording for ALL runs (warmup and actual)
            # The runner logic will filter based on warmup status later
            tool_name = f'tool{call_number}'
            # IMPORTANT: Append to the registry passed to the benchmark, not the fixture directly
            agent_benchmark.mock_tool_registry.calls.append({'tool_code': tool_name, 'params': {}, 'response': 'ok'})
            # Return value doesn't need get_call_counts anymore
            return {"output_data": {"result": f"output_{call_number}"}, "error": None}

        # Patch the process method on the MockAgent class
        with patch.object(MockAgent, 'process', new_callable=AsyncMock) as mock_process:
            mock_process.side_effect = process_side_effect
            mock_process.reset_mock() # Reset the mock before the call
            mock_tool_registry.reset() # Reset the tool registry

            # --- Act ---
            result = await agent_benchmark.run_benchmark(
            scenario_name=scenario_name,
            input_data=input_data,
            runs=runs,
            warmup_runs=warmup_runs
        )

        # --- Assert ---
        assert result.runs == runs
        assert len(result.durations) == runs
        assert mock_process.call_count == warmup_runs + runs # Assert on mock object

        assert result.memory_operations == warmup_runs + runs # Memory ops count starts from 1
        # Tool calls are aggregated only for non-warmup runs
        expected_tools = {}
        for i in range(warmup_runs + 1, warmup_runs + runs + 1):
             expected_tools[f'tool{i}'] = expected_tools.get(f'tool{i}', 0) + 1
        assert result.tool_call_counts == expected_tools
        assert result.last_output_data == {"result": f"output_{warmup_runs + runs}"}

        assert result.mean_duration >= 0.01 * (warmup_runs + runs)
        assert result.mean_duration < 0.05

    async def test_run_benchmark_agent_init_error(self, agent_benchmark, mock_db_service, mock_tool_registry):
        """Test handling of errors during agent instantiation."""
        # --- Arrange ---
        scenario_name = "InitErrorScenario"
        input_data = {"input": "init_fail"}
        runs = 1
        warmup_runs = 0

        with patch.object(MockAgent, '__init__', side_effect=TypeError("Agent init failed")):
            # --- Act ---
            result = await agent_benchmark.run_benchmark(
                scenario_name=scenario_name,
                input_data=input_data,
                runs=runs,
                warmup_runs=warmup_runs
            )

            # --- Assert ---
            assert result.runs == runs
            assert result.success_rate == 0.0
            assert len(result.errors) == runs
            # Updated assertion to match the actual error message from the log
            assert "Run 1: Agent init failed" in result.errors[0]
            # Removed redundant check for TypeError string within the logged message
            assert result.last_output_data is None
            assert len(result.durations) == 0

    # Remove test for LLM mock injection as it's no longer relevant
    # async def test_run_benchmark_llm_mock_injection(...):
    #     pass


# --- Tests for BenchmarkResult ---

def test_benchmark_result_to_dict():
    """Test the to_dict method of BenchmarkResult."""
    result = BenchmarkResult(
        agent_role="TestRole",
        scenario_name="TestScenario",
        runs=5,
        durations=[1.0, 1.1, 1.2, 1.3, 1.4],
        mean_duration=1.2,
        median_duration=1.2,
        min_duration=1.0,
        max_duration=1.4,
        std_dev=0.1581,
        # llm_calls removed
        tool_call_counts={"tool_b": 7, "tool_a": 3},
        memory_operations=5,
        success_rate=0.8,
        errors=["Run 3 failed"],
        last_output_data={"final": "output"}
    )
    expected_dict = {
        'agent_role': 'TestRole',
        'scenario': 'TestScenario',
        'performance': {
            'runs': 5,
            'mean_duration_s': 1.2000,
            'median_duration_s': 1.2000,
            'min_duration_s': 1.0000,
            'max_duration_s': 1.4000,
            'std_dev': 0.1581,
            'success_rate': 0.8,
        },
        'operations': {
            # llm_calls removed
            'tool_calls': {'tool_a': 3, 'tool_b': 7},
            'memory_operations': 5,
            'last_response_length': None, # Added expected key
            'total_input_tokens': 0,      # Added expected key
            'total_output_tokens': 0      # Added expected key
        },
        'errors': ['Run 3 failed'],
        'last_output': {'final': 'output'},
        'stage_timings_raw': {} # Added expected key (empty in this test case)
    }
    assert result.to_dict() == expected_dict

def test_benchmark_result_to_markdown():
    """Test the to_markdown method of BenchmarkResult."""
    result = BenchmarkResult(
        agent_role="MarkdownRole",
        scenario_name="MarkdownScenario",
        runs=3,
        durations=[0.5, 0.6, 0.7],
        mean_duration=0.6,
        median_duration=0.6,
        min_duration=0.5,
        max_duration=0.7,
        std_dev=0.1,
        # llm_calls removed
        tool_call_counts={"query_db": 2, "send_email": 1},
        memory_operations=3,
        success_rate=1.0,
        errors=[],
        last_output_data={"status": "done"}
    )
    markdown = result.to_markdown()

    assert "## Benchmark: MarkdownRole - MarkdownScenario" in markdown
    assert "### Performance Metrics" in markdown
    assert "- **Runs**: 3" in markdown
    assert "- **Mean Duration**: 0.6000s" in markdown
    assert "- **Median Duration**: 0.6000s" in markdown
    assert "- **Min/Max**: 0.5000s / 0.7000s" in markdown
    assert "- **Standard Deviation**: 0.1000s" in markdown
    assert "- **Success Rate**: 100.0%" in markdown
    assert "### Tool Call Analysis" in markdown
    assert "- **query_db**: 2 calls" in markdown
    assert "- **send_email**: 1 calls" in markdown
    assert "### Errors" not in markdown

def test_benchmark_result_to_markdown_with_errors():
    """Test markdown generation when errors are present."""
    result = BenchmarkResult(
        agent_role="ErrorRole",
        scenario_name="ErrorScenario",
        runs=2,
        durations=[0.9],
        mean_duration=0.9,
        median_duration=0.9,
        min_duration=0.9,
        max_duration=0.9,
        std_dev=0.0,
        # llm_calls removed
        tool_call_counts={},
        memory_operations=1,
        success_rate=0.5,
        errors=["Run 1: Timeout"],
        last_output_data=None
    )
    markdown = result.to_markdown()

    assert "## Benchmark: ErrorRole - ErrorScenario" in markdown
    assert "- **Success Rate**: 50.0%" in markdown
    assert "### Tool Call Analysis" not in markdown
    assert "### Errors" in markdown
    assert "- Run 1: Timeout" in markdown

def test_benchmark_result_to_markdown_no_tools():
    """Test markdown generation when no tools were called."""
    result = BenchmarkResult(
        agent_role="NoToolRole",
        scenario_name="NoToolScenario",
        runs=1,
        durations=[0.1],
        mean_duration=0.1,
        median_duration=0.1,
        min_duration=0.1,
        max_duration=0.1,
        std_dev=0.0,
        # llm_calls removed
        tool_call_counts={},
        memory_operations=0,
        success_rate=1.0,
        errors=[],
        last_output_data={"result": "simple"}
    )
    markdown = result.to_markdown()
    assert "### Tool Call Analysis" not in markdown
    assert "### Errors" not in markdown
