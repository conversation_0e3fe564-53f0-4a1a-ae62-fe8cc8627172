"""
Service for migrating benchmark scenarios to the new directory structure.
"""

import os
import json
import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from channels.db import database_sync_to_async

logger = logging.getLogger(__name__)

class BenchmarkMigration:
    """
    Service for migrating benchmark scenarios to the new directory structure.
    Provides methods to extract templates, transform scenarios, and save them
    to the new directory structure.
    """

    def __init__(self, validator=None):
        """
        Initialize the benchmark migration service.

        Args:
            validator: Optional schema validation service to use.
                     If None, imports and creates a new SchemaValidationService.
        """
        self.base_path = 'backend/testing/benchmark_data'

        # Defer import to avoid circular imports
        if validator is None:
            from apps.main.services.schema_validator_service import SchemaValidationService
            self.validator = SchemaValidationService()
        else:
            self.validator = validator

    async def migrate_scenario(self, scenario: Dict, target_path: str) -> Dict[str, Any]:
        """
        Migrate a single scenario to the new structure.

        Args:
            scenario: Scenario dictionary to migrate
            target_path: Path to save the migrated scenario

        Returns:
            Dictionary with migration results
        """
        result = {
            'success': False,
            'errors': [],
            'warnings': [],
            'template_extracted': False,
            'target_path': target_path
        }

        try:
            # Validate scenario
            validation_result = await self._validate_scenario(scenario)
            if not validation_result['valid']:
                result['errors'].append(f"Invalid scenario: {validation_result['errors']}")
                return result

            # Extract and save template if needed
            template = self._extract_evaluation_template(scenario)
            if template:
                template_result = await self._save_evaluation_template(template)
                result['template_extracted'] = template_result['success']
                if not template_result['success']:
                    result['warnings'].append(f"Failed to save template: {template_result['errors']}")

            # Transform scenario
            new_scenario = self._transform_scenario(scenario)

            # Save to new location
            await self._save_scenario(new_scenario, target_path)
            result['success'] = True

        except Exception as e:
            result['errors'].append(f"Migration error: {str(e)}")
            logger.error(f"Error migrating scenario: {e}", exc_info=True)

        return result

    async def migrate_all_scenarios(self) -> Dict[str, Any]:
        """
        Migrate all scenarios from the old structure to the new structure.

        Returns:
            Dictionary with migration results
        """
        result = {
            'success': True,
            'total': 0,
            'succeeded': 0,
            'failed': 0,
            'scenario_results': []
        }

        try:
            # Find all scenario files
            scenario_files = []
            scenarios_dir = os.path.join(self.base_path, 'scenarios')
            if os.path.exists(scenarios_dir):
                for filename in os.listdir(scenarios_dir):
                    if filename.endswith('.json'):
                        scenario_files.append(os.path.join(scenarios_dir, filename))

            # Also check for wheel_workflow_scenarios.json in the base directory
            wheel_workflow_file = os.path.join(self.base_path, 'wheel_workflow_scenarios.json')
            if os.path.exists(wheel_workflow_file):
                scenario_files.append(wheel_workflow_file)

            result['total'] = len(scenario_files)

            # Process each file
            for file_path in scenario_files:
                file_result = await self._migrate_file(file_path)
                result['scenario_results'].append(file_result)

                if file_result['success']:
                    result['succeeded'] += 1
                else:
                    result['failed'] += 1
                    result['success'] = False

        except Exception as e:
            result['success'] = False
            result['error'] = str(e)
            logger.error(f"Error migrating scenarios: {e}", exc_info=True)

        return result

    async def _migrate_file(self, file_path: str) -> Dict[str, Any]:
        """
        Migrate scenarios from a single file.

        Args:
            file_path: Path to the scenario file

        Returns:
            Dictionary with migration results
        """
        result = {
            'file_path': file_path,
            'success': False,
            'total': 0,
            'succeeded': 0,
            'failed': 0,
            'scenario_results': []
        }

        try:
            # Load scenarios from file
            with open(file_path, 'r') as f:
                scenarios_data = json.load(f)

            # Ensure it's a list
            if not isinstance(scenarios_data, list):
                scenarios_data = [scenarios_data]

            result['total'] = len(scenarios_data)

            # Process each scenario
            for scenario in scenarios_data:
                # Determine target path based on scenario properties
                target_path = self._get_target_path(scenario)

                # Migrate the scenario
                scenario_result = await self.migrate_scenario(scenario, target_path)
                result['scenario_results'].append(scenario_result)

                if scenario_result['success']:
                    result['succeeded'] += 1
                else:
                    result['failed'] += 1

            result['success'] = result['failed'] == 0

        except Exception as e:
            result['success'] = False
            result['error'] = str(e)
            logger.error(f"Error migrating file {file_path}: {e}", exc_info=True)

        return result

    def _get_target_path(self, scenario: Dict) -> str:
        """
        Determine the target path for a scenario based on its properties.

        Args:
            scenario: Scenario dictionary

        Returns:
            Target path for the scenario
        """
        agent_role = scenario.get('agent_role', 'unknown')
        name = self._normalize_name(scenario.get('name', 'unnamed'))

        # Determine category and type
        if agent_role == 'workflow':
            category = 'workflows'
            workflow_type = scenario.get('metadata', {}).get('workflow_type', 'unknown')
            return os.path.join(self.base_path, category, workflow_type, f"{name}.json")
        else:
            category = 'agents'
            # Determine workflow type from metadata or context_packet
            workflow_type = None
            metadata = scenario.get('metadata', {})
            if 'workflow_type' in metadata:
                workflow_type = metadata['workflow_type']
            else:
                input_data = scenario.get('input_data', {})
                context_packet = input_data.get('context_packet', {})
                if isinstance(context_packet, dict) and 'workflow_type' in context_packet:
                    workflow_type = context_packet['workflow_type']
                elif isinstance(context_packet, dict) and 'task_type' in context_packet:
                    workflow_type = context_packet['task_type']

            if not workflow_type:
                # Try to infer from filename or tags
                if 'wheelgen' in name.lower() or 'wheel_generation' in name.lower():
                    workflow_type = 'wheel_generation'
                elif 'discussion' in name.lower():
                    workflow_type = 'discussion'
                elif 'feedback' in name.lower() or 'postactfb' in name.lower():
                    workflow_type = 'feedback'
                else:
                    workflow_type = 'unknown'

            return os.path.join(self.base_path, category, agent_role, workflow_type, f"{name}.json")

    def _normalize_name(self, name: str) -> str:
        """
        Normalize a scenario name for use in filenames.

        Args:
            name: Scenario name

        Returns:
            Normalized name
        """
        # Replace spaces with underscores and remove special characters
        normalized = name.lower().replace(' ', '_')
        normalized = ''.join(c for c in normalized if c.isalnum() or c == '_')
        return normalized

    def _extract_evaluation_template(self, scenario: Dict) -> Optional[Dict]:
        """
        Extract reusable evaluation criteria template from a scenario.

        Args:
            scenario: Scenario dictionary

        Returns:
            Evaluation template dictionary or None if no criteria found
        """
        criteria = scenario.get('metadata', {}).get('expected_quality_criteria')
        if not criteria:
            return None

        # Determine template name
        agent_role = scenario.get('agent_role', 'unknown')
        workflow_type = scenario.get('metadata', {}).get('workflow_type')
        if not workflow_type:
            input_data = scenario.get('input_data', {})
            context_packet = input_data.get('context_packet', {})
            if isinstance(context_packet, dict):
                workflow_type = context_packet.get('workflow_type') or context_packet.get('task_type')

        if not workflow_type:
            workflow_type = 'general'

        template_name = f"{agent_role}_{workflow_type}_criteria"

        # Create template
        template = {
            'template_name': template_name,
            'description': f"Evaluation criteria for {agent_role} in {workflow_type} workflow",
            'criteria': criteria
        }

        # Add evaluator models if present
        evaluator_models = scenario.get('metadata', {}).get('evaluator_models')
        if evaluator_models:
            template['evaluator_models'] = evaluator_models

        return template

    async def _save_evaluation_template(self, template: Dict) -> Dict[str, Any]:
        """
        Save an evaluation template to the new directory structure.

        Args:
            template: Evaluation template dictionary

        Returns:
            Dictionary with save results
        """
        result = {
            'success': False,
            'errors': [],
            'warnings': [],
            'template_name': template.get('template_name', 'unnamed')
        }

        try:
            # Validate template using the validator passed to the constructor
            # Handle both direct calls (for tests with mocks) and async calls (for real usage)
            try:
                validation_result = self.validator.validate_evaluation_criteria(template)
                if isinstance(validation_result, tuple):
                    is_valid, errors = validation_result
                else:
                    # For async mock that returns a coroutine
                    is_valid, errors = await database_sync_to_async(self.validator.validate_evaluation_criteria)(template)
            except Exception as e:
                logger.error(f"Error validating template: {e}", exc_info=True)
                is_valid, errors = False, [str(e)]

            if not is_valid:
                result['errors'].append(f"Invalid template: {errors}")
                return result

            # Determine target path
            template_name = self._normalize_name(template['template_name'])
            target_dir = os.path.join(self.base_path, 'templates', 'evaluation_criteria')
            target_path = os.path.join(target_dir, f"{template_name}.json")

            # Create directory if it doesn't exist
            os.makedirs(target_dir, exist_ok=True)

            # Save template
            with open(target_path, 'w') as f:
                json.dump(template, f, indent=2)

            result['success'] = True
            result['target_path'] = target_path

        except Exception as e:
            result['errors'].append(f"Error saving template: {str(e)}")
            logger.error(f"Error saving evaluation template: {e}", exc_info=True)

        return result

    def _transform_scenario(self, scenario: Dict) -> Dict:
        """
        Transform a scenario to the new format.

        Args:
            scenario: Scenario dictionary

        Returns:
            Transformed scenario dictionary
        """
        # Create a copy of the scenario to avoid modifying the original
        new_scenario = scenario.copy()

        # Extract metadata
        metadata = new_scenario.get('metadata', {}).copy()

        # Add template reference if criteria exist
        if 'expected_quality_criteria' in metadata:
            template = self._extract_evaluation_template(scenario)
            if template:
                template_name = template['template_name']
                # Replace criteria with template reference
                metadata['template_reference'] = template_name

        # Update metadata
        new_scenario['metadata'] = metadata

        # Generate tags if not present
        if 'tags' not in new_scenario:
            new_scenario['tags'] = self._generate_tags(scenario)

        return new_scenario

    def _generate_tags(self, scenario: Dict) -> List[str]:
        """
        Generate tags for a scenario based on its properties.

        Args:
            scenario: Scenario dictionary

        Returns:
            List of tags
        """
        tags = []

        # Add agent role
        agent_role = scenario.get('agent_role')
        if agent_role:
            tags.append(agent_role)

        # Add workflow type
        workflow_type = scenario.get('metadata', {}).get('workflow_type')
        if not workflow_type:
            input_data = scenario.get('input_data', {})
            context_packet = input_data.get('context_packet', {})
            if isinstance(context_packet, dict):
                workflow_type = context_packet.get('workflow_type') or context_packet.get('task_type')

        if workflow_type:
            tags.append(workflow_type)

        # Add trust level if present
        trust_level = None
        input_data = scenario.get('input_data', {})
        context_packet = input_data.get('context_packet', {})
        if isinstance(context_packet, dict) and 'trust_level' in context_packet:
            trust_level = context_packet['trust_level']

        if trust_level is not None:
            if trust_level < 40:
                tags.append('foundation')
            elif trust_level < 70:
                tags.append('expansion')
            else:
                tags.append('integration')

        return tags

    async def _save_scenario(self, scenario: Dict, path: str) -> None:
        """
        Save a scenario to the new directory structure.

        Args:
            scenario: Scenario dictionary
            path: Path to save the scenario
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # Save scenario
        with open(path, 'w') as f:
            json.dump(scenario, f, indent=2)

    async def _validate_scenario(self, scenario: Dict) -> Dict[str, Any]:
        """
        Validate a scenario against schema.

        Args:
            scenario: Scenario dictionary

        Returns:
            Dictionary with validation results
        """
        # Validate the scenario
        validation_result = await database_sync_to_async(self.validator.validate_benchmark_scenario)(scenario)

        return {
            'valid': validation_result.get('valid', False),
            'errors': validation_result.get('errors', [])
        }
