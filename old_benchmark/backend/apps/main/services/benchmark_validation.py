"""
Service for validating benchmark scenarios and directory structure.
"""

import os
import json
import logging
import glob
from typing import Dict, List, Optional, Any
from channels.db import database_sync_to_async

logger = logging.getLogger(__name__)

class BenchmarkValidation:
    """
    Service for validating benchmark scenarios and directory structure.
    Provides methods to validate the directory structure, scenarios, and templates.
    """

    def __init__(self, validator=None, registry=None):
        """
        Initialize the benchmark validation service.

        Args:
            validator: Optional schema validation service to use.
                     If None, imports and creates a new SchemaValidationService.
            registry: Optional schema registry to use.
                    If None, imports and creates a new SchemaRegistry.
        """
        self.base_path = 'backend/testing/benchmark_data'

        # Defer imports to avoid circular imports
        if validator is None:
            from apps.main.services.schema_validator_service import SchemaValidationService
            self.validator = SchemaValidationService()
        else:
            self.validator = validator

        if registry is None:
            from apps.main.services.schema_registry import SchemaRegistry
            self.registry = SchemaRegistry()
        else:
            self.registry = registry

    async def validate_directory_structure(self) -> Dict[str, Any]:
        """
        Validate the benchmark directory structure.

        Returns:
            Dictionary with validation results
        """
        logger.warning(f"Validating directory structure starting from {self.base_path}")
        expected_dirs = [
            'agents/mentor/wheel_generation',
            'agents/mentor/discussion',
            'agents/mentor/feedback',
            'agents/orchestrator',
            'agents/strategy',
            'workflows/wheel_generation',
            'workflows/activity_feedback',
            'templates/evaluation_criteria'
        ]

        results = {
            'valid': True,
            'missing_dirs': [],
            'extra_dirs': []
        }

        # Check for missing directories
        logger.warning("Checking for missing expected directories...")
        for dir_path in expected_dirs:
            full_path = os.path.join(self.base_path, dir_path)
            if not os.path.exists(full_path):
                logger.warning(f"Missing expected directory: {dir_path}")
                results['valid'] = False
                results['missing_dirs'].append(dir_path)
            else:
                logger.warning(f"Found expected directory: {dir_path}")

        # Check for unexpected directories
        logger.warning("Checking for unexpected directories...")
        for root, dirs, _ in os.walk(self.base_path):
            rel_path = os.path.relpath(root, self.base_path)
            if rel_path == '.':
                continue

            # Skip the scenarios directory (old structure)
            if rel_path == 'scenarios':
                logger.warning(f"Dismissing old scenarios directory: {rel_path}")
                continue

            # Check if this directory is expected
            if rel_path not in expected_dirs and not any(rel_path.startswith(d + '/') for d in expected_dirs):
                logger.warning(f"Found unexpected directory: {rel_path}")
                results['extra_dirs'].append(rel_path)
            else:
                logger.info(f"Checking expected directory: {rel_path}")

        return results

    async def validate_all_scenarios(self, scenario_files: List[str]) -> Dict[str, Any]:
        """
        Validate all scenarios provided in the list of file paths.

        Args:
            scenario_files: A list of file paths to scenario JSON files.

        Returns:
            Dictionary with validation results
        """
        results = {
            'valid': True,
            'total': len(scenario_files),
            'valid_count': 0,
            'invalid_count': 0,
            'scenario_results': []
        }

        logger.info(f"Received a total of {results['total']} scenario files for validation.")

        # Validate each scenario
        logger.info("Validating each scenario file...")
        for file_path in scenario_files:
            try:
                logger.info(f"Validating scenario file: {file_path}")
                with open(file_path, 'r') as f:
                    scenario = json.load(f)

                # Validate the scenario
                validation_result = await database_sync_to_async(self.validator.validate_benchmark_scenario)(scenario)

                scenario_result = {
                    'file_path': file_path,
                    'valid': validation_result.get('valid', False),
                    'errors': validation_result.get('errors', [])
                }

                results['scenario_results'].append(scenario_result)

                if scenario_result['valid']:
                    results['valid_count'] += 1
                else:
                    results['invalid_count'] += 1
                    results['valid'] = False

            except Exception as e:
                results['scenario_results'].append({
                    'file_path': file_path,
                    'valid': False,
                    'errors': [f"Error loading scenario: {str(e)}"]
                })
                results['invalid_count'] += 1
                results['valid'] = False

        return results

    async def validate_all_templates(self, template_files: List[str]) -> Dict[str, Any]:
        """
        Validate all templates provided in the list of file paths.

        Args:
            template_files: A list of file paths to template JSON files.

        Returns:
            Dictionary with validation results
        """
        results = {
            'valid': True,
            'total': len(template_files),
            'valid_count': 0,
            'invalid_count': 0,
            'template_results': []
        }

        logger.info(f"Received a total of {results['total']} template files for validation.")

        # Validate each template
        logger.info("Validating each template file...")
        for file_path in template_files:
            try:
                logger.info(f"Validating template file: {file_path}")
                with open(file_path, 'r') as f:
                    template = json.load(f)

                # Validate the template using the evaluation_template schema
                is_valid, errors = self.registry.validate("evaluation_template", template)

                template_result = {
                    'file_path': file_path,
                    'valid': is_valid,
                    'errors': errors
                }

                results['template_results'].append(template_result)

                if is_valid:
                    results['valid_count'] += 1
                else:
                    results['invalid_count'] += 1
                    results['valid'] = False

            except Exception as e:
                results['template_results'].append({
                    'file_path': file_path,
                    'valid': False,
                    'errors': [f"Error loading template: {str(e)}"]
                })
                results['invalid_count'] += 1
                results['valid'] = False

        return results

    async def generate_migration_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive migration report.

        Returns:
            Dictionary with migration report
        """
        return {
            'directory_structure': await self.validate_directory_structure(),
            'scenarios': await self.validate_all_scenarios(),
            'templates': await self.validate_all_templates()
        }
