"""
Evaluation Criteria Migration Utility

This module provides utilities for migrating evaluation criteria between schema versions,
particularly for the phase-aware criteria structure.

Key responsibilities:
1. Migrate from flat criteria structure to phase-aware structure
2. Map trust levels to phases
3. Provide default criteria for missing phases
"""

import logging
from typing import Dict, Any, List, Optional, Tuple

# Configure logging
logger = logging.getLogger(__name__)

# Trust level phase boundaries
FOUNDATION_MAX = 39
EXPANSION_MAX = 69
# INTEGRATION_MAX = 100 (implicit)

def map_trust_level_to_phase(trust_level: int) -> str:
    """
    Map a trust level (0-100) to a trust phase.
    
    Args:
        trust_level: Integer trust level between 0 and 100
        
    Returns:
        String phase name: 'foundation', 'expansion', or 'integration'
    """
    if trust_level <= FOUNDATION_MAX:
        return "foundation"
    elif trust_level <= EXPANSION_MAX:
        return "expansion"
    else:
        return "integration"

def migrate_criteria_to_phase_aware(criteria_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migrate flat criteria structure to phase-aware structure.
    
    Args:
        criteria_data: Dictionary containing flat criteria structure
        
    Returns:
        Dictionary with phase-aware criteria structure
    """
    # If already phase-aware, return as is
    if "evaluation_criteria_by_phase" in criteria_data:
        return criteria_data
        
    # If no criteria, return empty phase-aware structure
    if "criteria" not in criteria_data:
        return {
            "evaluation_criteria_by_phase": {
                "foundation": {"criteria": []},
                "expansion": {"criteria": []},
                "integration": {"criteria": []}
            }
        }
        
    # Create phase-aware structure with the same criteria for all phases
    original_criteria = criteria_data.get("criteria", [])
    
    return {
        "evaluation_criteria_by_phase": {
            "foundation": {"criteria": original_criteria},
            "expansion": {"criteria": original_criteria},
            "integration": {"criteria": original_criteria}
        }
    }

def get_criteria_for_trust_level(
    criteria_data: Dict[str, Any], 
    trust_level: int
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get the appropriate criteria for a given trust level.
    
    Args:
        criteria_data: Dictionary containing criteria data (either flat or phase-aware)
        trust_level: Integer trust level between 0 and 100
        
    Returns:
        Dictionary with criteria for the appropriate phase
    """
    # If flat criteria structure, return as is
    if "criteria" in criteria_data:
        return {"criteria": criteria_data["criteria"]}
        
    # If phase-aware, get the appropriate phase
    if "evaluation_criteria_by_phase" in criteria_data:
        phase = map_trust_level_to_phase(trust_level)
        phase_criteria = criteria_data["evaluation_criteria_by_phase"].get(phase, {})
        
        # If phase has no criteria, fall back to foundation phase
        if not phase_criteria or "criteria" not in phase_criteria:
            logger.warning(f"No criteria found for phase '{phase}'. Falling back to foundation phase.")
            phase_criteria = criteria_data["evaluation_criteria_by_phase"].get("foundation", {})
            
        return {"criteria": phase_criteria.get("criteria", [])}
        
    # If neither structure is present, return empty criteria
    logger.warning("No valid criteria structure found. Returning empty criteria.")
    return {"criteria": []}

def register_evaluation_criteria_migrations(version_manager):
    """
    Register migrations for evaluation criteria schema versions.
    
    Args:
        version_manager: SchemaVersionManager instance
    """
    # Register migration from v1.0.0 to v2.0.0
    version_manager.register_migration(
        "evaluation_criteria", 
        "1.0.0", 
        "2.0.0", 
        migrate_criteria_to_phase_aware
    )
    
    logger.info("Registered evaluation criteria migrations")
