# New Benchmark System Progress Report

## Overview

This document outlines the current status of the new benchmark system, which is being developed to replace the existing benchmark system with a more robust, flexible, and maintainable solution.

## Current Status

### 1. Schema Validation System

The new benchmark system uses Pydantic models for schema validation, providing several advantages:

- **Type Safety**: Pydantic provides runtime type checking and validation
- **Clear Error Messages**: Validation errors include detailed information about what went wrong
- **Self-Documenting**: Models serve as both validation and documentation
- **IDE Support**: Better IDE support for code completion and type hints

The schema validation system includes the following components:

#### 1.1 Base Models

- **BenchmarkBaseModel**: Base model for all benchmark schema models
- **VersionedModel**: Base model for versioned models with semver support
- **NamedEntity**: Base model for named entities

#### 1.2 Scenario Models

- **BenchmarkScenario**: Schema for benchmark scenarios
- **BenchmarkScenarioMetadata**: Metadata for benchmark scenarios
- **EvaluationCriteria**: Collection of evaluation criteria
- **PhaseAwareCriteria**: Phase-aware evaluation criteria for different trust phases
- **ToolExpectation**: Expectation for tool calls in benchmark scenarios

#### 1.3 Run Models

- **BenchmarkRun**: Schema for benchmark run results
- **TokenUsage**: Token usage information for benchmark runs
- **StagePerformance**: Performance metrics for workflow stages
- **SemanticEvaluation**: Semantic evaluation results for benchmark runs
- **EvaluationScore**: Evaluation score for a benchmark run

#### 1.4 Naming Convention

- **BenchmarkNamingConvention**: Utility for parsing and generating benchmark scenario names
- **AgentRole**: Standard agent roles in the system
- **WorkflowType**: Standard workflow types in the system
- **ScenarioType**: Standard scenario types in the system
- **ScenarioVariant**: Standard scenario variants in the system

#### 1.5 Validation

- **PydanticSchemaValidator**: Schema validation service using Pydantic models
- **SchemaValidationResult**: Result of schema validation

### 2. Benchmark Validation Command

The new benchmark validation command (`validate_benchmarks_v3.py`) provides enhanced validation for benchmark scenarios, with improved error reporting and automatic fixing of common issues.

#### 2.1 Features

- **Comprehensive Validation**: Validates directory structure, file schemas, file naming conventions, and database scenarios
- **Automatic Fixing**: Attempts to fix common issues in scenarios
- **Renaming**: Renames files to follow the naming convention
- **Exporting**: Exports scenario definitions as schema-compliant JSON files
- **Detailed Reporting**: Provides detailed validation reports

#### 2.2 Usage

```bash
python manage.py validate_benchmarks_v3 [options]
```

Options include:
- `--scenario-id`: Validate a specific scenario by ID
- `--agent-role`: Validate scenarios for a specific agent role
- `--file`: Validate a specific file
- `--directory`: Validate all files in a specific directory
- `--fix`: Attempt to fix common issues in scenarios
- `--rename`: Rename files to follow the naming convention
- `--export`: Export scenario definitions as schema-compliant JSON files
- `--output-dir`: Directory to save validated scenario files
- `--report-format`: Format for validation reports (`json` or `text`)
- `--validate-files`: Validate scenario files in the benchmark data directory
- `--validate-structure`: Validate the directory structure
- `--validate-names`: Validate file names against the naming convention
- `--comprehensive`: Perform comprehensive validation (structure, files, names, and database)

### 3. Directory Structure

The new benchmark system uses a standardized directory structure for benchmark scenarios:

```
backend/testing/benchmark_data/
├── agents/
│   ├── mentor/
│   │   ├── wheel_generation/
│   │   ├── discussion/
│   │   └── feedback/
│   ├── orchestrator/
│   └── strategy/
├── workflows/
│   ├── wheel_generation/
│   └── activity_feedback/
└── templates/
    └── evaluation_criteria/
```

- Agent-specific scenarios go in `agents/{agent_role}/{workflow_type}/`
- Workflow-specific scenarios go in `workflows/{workflow_type}/`
- Evaluation templates go in `templates/evaluation_criteria/`

### 4. File Naming Convention

The new benchmark system uses a standardized file naming convention for benchmark scenarios:

```
{agent_role}_{workflow_type}_{scenario_type}_{variant}_{id}.json
```

For example:
- `mentor_wheel_generation_basic_trust_high_001.json`
- `orchestrator_discussion_complex_002.json`

## Next Steps

### 1. Migration

- Migrate existing benchmark scenarios to the new directory structure and naming convention
- Update existing code to use the new Pydantic models
- Create a migration guide for developers

### 2. Documentation

- Update the benchmark system documentation to reflect the new schema validation system
- Create a user guide for the new benchmark validation command
- Document the new directory structure and naming convention

### 3. Testing

- Create comprehensive tests for the new schema validation system
- Test the new benchmark validation command with various scenarios
- Ensure backward compatibility with existing benchmark scenarios

### 4. Integration

- Integrate the new schema validation system with the existing benchmark system
- Update the benchmark runner to use the new schema validation system
- Update the benchmark admin interface to use the new schema validation system

## Conclusion

The new benchmark system is making good progress, with a robust schema validation system and enhanced validation command. The next steps involve migrating existing scenarios, updating documentation, and integrating with the existing system.
