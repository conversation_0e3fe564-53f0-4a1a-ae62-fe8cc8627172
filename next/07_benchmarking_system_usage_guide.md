# Benchmarking System Usage Guide

This document provides a practical guide for using the Goali benchmarking system to evaluate agent and workflow performance.

## Overview

The Goali benchmarking system is a comprehensive solution for evaluating agent and workflow performance, measuring execution time, token usage, tool calls, and semantic quality. It supports both agent benchmarking (testing individual agent components) and workflow benchmarking (testing complete LangGraph workflows).

## Prerequisites

Before using the benchmarking system, ensure you have:

1. Access to the Goali development environment
2. <PERSON>er and Docker Compose installed
3. Basic understanding of Django and LangGraph
4. Familiarity with JSON Schema

## Getting Started

### Setting Up the Environment

1. Clone the repository and navigate to the project root:
   ```bash
   git clone https://github.com/elgui/chilltech.git
   cd chilltech
   ```

2. Start the development environment:
   ```bash
   docker-compose up -d
   ```

3. Ensure the database is properly set up:
   ```bash
   docker-compose exec web python manage.py migrate
   ```

4. Seed the benchmark schemas and evaluation templates:
   ```bash
   docker-compose exec web python manage.py seed_benchmark_schemas
   ```

5. Set up the benchmark directory structure:
   ```bash
   docker-compose exec web python manage.py setup_benchmark_structure
   ```

### Creating Benchmark Scenarios

Benchmark scenarios are defined as JSON files and loaded into the database. The process involves:

1. **Define Schemas** (if not already done):
   - Ensure core schemas exist in `backend/schemas/`
   - Run `python manage.py seed_benchmark_schemas` if needed
   - Create reusable evaluation templates in `backend/testing/benchmark_data/templates/evaluation_criteria/`

2. **Create Scenario Files**:
   - Create JSON files in the appropriate directories:
     - Agent-specific scenarios: `backend/testing/benchmark_data/agents/<agent_role>/<workflow_type>/`
     - Workflow-specific scenarios: `backend/testing/benchmark_data/workflows/<workflow_type>/`
     - Evaluation templates: `backend/testing/benchmark_data/templates/evaluation_criteria/`

3. **Example Agent Benchmark Scenario**:
   ```json
   {
     "name": "Mentor - Initial Wheel Gen - Foundation",
     "description": "Tests the mentor agent's ability to generate an initial wheel with foundation items",
     "agent_role": "mentor",
     "input_data": {
       "user_message": "I want to improve my work-life balance",
       "context_packet": {
         "workflow_type": "wheel_generation",
         "user_profile": {
           "name": "John",
           "age": 35,
           "occupation": "Software Engineer"
         }
       }
     },
     "metadata": {
       "user_profile_context": {
         "name": "John",
         "age": 35,
         "occupation": "Software Engineer"
       },
       "expected_quality_criteria": {
         "Tone": ["supportive", "friendly", "empathetic"],
         "Content": ["relevant", "helpful", "accurate"]
       },
       "evaluator_models": ["openai/gpt-4o"],
       "mock_tool_responses": {
         "get_user_profile": {
           "name": "John",
           "age": 35,
           "occupation": "Software Engineer"
         }
       }
     }
   }
   ```

4. **Example Workflow Benchmark Scenario**:
   ```json
   {
     "name": "Wheel Generation - Work-Life Balance",
     "description": "Tests the wheel generation workflow with a work-life balance focus",
     "agent_role": "orchestrator",
     "input_data": {
       "user_message": "I want to improve my work-life balance",
       "context_packet": {
         "workflow_type": "wheel_generation",
         "user_profile": {
           "name": "John",
           "age": 35,
           "occupation": "Software Engineer"
         }
       }
     },
     "metadata": {
       "workflow_type": "wheel_generation",
       "user_profile_context": {
         "name": "John",
         "age": 35,
         "occupation": "Software Engineer"
       },
       "expected_quality_criteria": {
         "Tone": ["supportive", "friendly", "empathetic"],
         "Content": ["relevant", "helpful", "accurate"]
       },
       "evaluator_models": ["openai/gpt-4o"],
       "mock_tool_responses": {
         "get_user_profile": {
           "name": "John",
           "age": 35,
           "occupation": "Software Engineer"
         }
       }
     }
   }
   ```

5. **Validate Scenarios**:
   ```bash
   docker-compose exec web python manage.py validate_benchmarks_v2 --validate-structure --validate-files --validate-templates
   ```

6. **Load Scenarios**:
   ```bash
   docker-compose exec web python manage.py create_benchmark_scenarios --directory backend/testing/benchmark_data/agents/mentor/wheel_generation/
   ```

### Running Benchmarks

#### Using the Command Line

1. **Run Agent Benchmarks**:
   ```bash
   # Run a specific scenario by name
   docker-compose exec web python manage.py run_benchmarks --scenario-name "Mentor - Initial Wheel Gen - Foundation"

   # Run all scenarios for a specific agent role
   docker-compose exec web python manage.py run_benchmarks --agent-role mentor

   # Run with specific parameters
   docker-compose exec web python manage.py run_benchmarks --agent-role mentor --params '{"semantic_evaluation": true, "runs": 5, "warmup_runs": 2}'
   ```

2. **Run Workflow Benchmarks**:
   ```bash
   # Run a specific scenario by ID
   docker-compose exec web python manage.py run_workflow_benchmarks --scenario-id 12345678-1234-5678-1234-567812345678

   # Run all scenarios for a specific workflow type
   docker-compose exec web python manage.py run_workflow_benchmarks --workflow-type wheel_generation

   # Run with specific parameters
   docker-compose exec web python manage.py run_workflow_benchmarks --workflow-type wheel_generation --params '{"semantic_evaluation": true, "runs": 5, "warmup_runs": 2}'
   ```

#### Using the Admin Interface

1. **Access the Admin Interface**:
   - Navigate to `/admin/benchmarks/` in your browser
   - Log in with your admin credentials

2. **Run Agent Benchmarks**:
   - Select a scenario from the list
   - Configure parameters (e.g., `{"semantic_evaluation": true, "runs": 5}`)
   - Click "Run Benchmark"
   - Monitor progress in the WebSocket console

3. **View Results**:
   - Navigate to `/admin/benchmarks/history/`
   - Filter results by agent role, scenario name, or date
   - View trends in the chart
   - Examine individual runs in the table
   - Click "View Details" for comprehensive metrics

### Analyzing Results

The benchmarking system provides detailed metrics for each benchmark run:

1. **Performance Metrics**:
   - Mean, median, min, max duration
   - Standard deviation
   - Success rate

2. **Operational Metrics**:
   - Tool calls (total and breakdown by tool)
   - LLM calls
   - Memory operations
   - Token usage (input, output, total)
   - Estimated cost

3. **Semantic Quality Metrics**:
   - Overall semantic score
   - Dimension-specific scores
   - Evaluation reasoning
   - Multi-model evaluation results

4. **Statistical Comparison**:
   - Comparison with previous runs
   - P-value for statistical significance
   - Performance trend analysis

### Best Practices

#### Creating Effective Scenarios

1. **Define Clear Objectives**:
   - What specific capability are you testing?
   - What constitutes success?
   - What metrics are most important?

2. **Use Realistic Data**:
   - Create scenarios that reflect real user interactions
   - Include edge cases and challenging situations
   - Vary complexity across scenarios

3. **Define Comprehensive Evaluation Criteria**:
   - Include both tone and content dimensions
   - Define specific criteria for each dimension
   - Consider phase-aware criteria for different trust levels

4. **Configure Mock Tools Appropriately**:
   - Mock only the necessary tools
   - Provide realistic mock responses
   - Consider conditional responses for complex scenarios

#### Running Effective Benchmarks

1. **Use Sufficient Runs**:
   - Use at least 3 runs for statistical significance
   - Include warmup runs to avoid cold-start effects
   - Balance accuracy with execution time

2. **Enable Semantic Evaluation**:
   - Use semantic evaluation for qualitative assessment
   - Include multiple evaluator models for robustness
   - Define comprehensive evaluation criteria

3. **Monitor Resource Usage**:
   - Track token usage and cost
   - Monitor execution time
   - Identify performance bottlenecks

4. **Compare Against Baselines**:
   - Establish baseline performance
   - Compare new versions against baselines
   - Track performance trends over time

### Troubleshooting

#### Common Issues

1. **Schema Validation Errors**:
   - Ensure scenario JSON follows the expected schema
   - Check for required fields and correct data types
   - Use `validate_benchmarks_v2` command with `--fix-metadata` flag

2. **Agent Output Structure Issues**:
   - Ensure agents return the expected output structure
   - Check for missing fields in agent responses
   - Update extraction logic if needed

3. **Database Connection Issues**:
   - Use proper async/sync patterns for database access
   - Ensure connections are properly closed
   - Add retry logic for transient errors

4. **Token Tracking Issues**:
   - Ensure TokenTracker is properly initialized with run_id
   - Check for proper token counting for all LLM models
   - Validate token usage data for accuracy

#### Getting Help

If you encounter issues not covered in this guide:

1. Check the error logs for detailed error messages
2. Review the documentation in `docs/backend/BENCHMARK_SYSTEM.md`
3. Look for similar issues in the project's issue tracker
4. Reach out to the development team for assistance

## Conclusion

The Goali benchmarking system is a powerful tool for evaluating agent and workflow performance. By following this guide, you can create effective benchmark scenarios, run benchmarks, analyze results, and troubleshoot common issues. With proper use, the benchmarking system can help ensure the quality of agents and workflows in the Goali project.
