# Benchmark Migration Guide

This guide helps you migrate from the old JSON Schema-based benchmark system to the new Pydantic-based benchmark schema system.

## 1. Overview

The new benchmark schema system uses Pydantic models for robust validation and clear error messages. It also includes utilities for naming conventions and directory structure validation.

Key improvements:
- Type safety and validation at runtime
- Clear error messages that pinpoint exactly what's wrong
- Automatic conversion between JSON and Python objects
- Extensibility through inheritance and composition
- Better IDE support with autocompletion and type hints

## 2. Migration Steps

### 2.1 Validate Existing Scenarios

First, validate your existing scenarios using the new validation command:

```bash
python manage.py validate_benchmarks_v3 --comprehensive
```

This will check:
- Directory structure
- File schemas
- File naming conventions
- Database scenarios

### 2.2 Fix Common Issues

If the validation finds issues, you can use the `--fix` option to automatically fix common issues:

```bash
python manage.py validate_benchmarks_v3 --comprehensive --fix
```

This will fix:
- Missing workflow_type
- Missing expected_quality_criteria
- Missing user_profile_context

### 2.3 Rename Files

If your file names don't follow the naming convention, you can use the `--rename` option to automatically rename them:

```bash
python manage.py validate_benchmarks_v3 --validate-names --rename
```

This will rename files to follow the pattern:
```
{agent_role}_{workflow_type}_{scenario_type}_{variant}_{id}.json
```

### 2.4 Update Code

Update your code to use the new Pydantic models:

#### Old Code:
```python
from apps.main.services.schema_validator_service import SchemaValidationService

validator = SchemaValidationService()
result = validator.validate_benchmark_scenario(scenario_data)

if result['valid']:
    print("Scenario is valid!")
else:
    print("Scenario is invalid:")
    for error in result['errors']:
        print(f"- {error}")
```

#### New Code:
```python
from next.benchmark_schema import PydanticSchemaValidator

validator = PydanticSchemaValidator()
result = validator.validate('benchmark_scenario', scenario_data)

if result.is_valid:
    print("Scenario is valid!")
else:
    print("Scenario is invalid:")
    for error in result.errors:
        print(f"- {error}")
```

### 2.5 Create New Scenarios

When creating new scenarios, use the Pydantic models:

```python
from next.benchmark_schema import (
    BenchmarkScenario, BenchmarkScenarioMetadata, UserProfile,
    WorkflowType, TrustPhase
)

# Create a user profile
user_profile = UserProfile(
    trust_phase=TrustPhase.EXPANSION,
    trust_level=50
)

# Create metadata
metadata = BenchmarkScenarioMetadata(
    workflow_type=WorkflowType.WHEEL_GENERATION,
    user_profile_context=user_profile,
    expected_quality_criteria={
        "Content": ["Should be relevant and helpful"],
        "Tone": ["Should be supportive and encouraging"]
    }
)

# Create scenario
scenario = BenchmarkScenario(
    name="Test Scenario",
    description="A test scenario",
    agent_role="mentor",
    input_data={
        "user_message": "Hello",
        "context_packet": {
            "workflow_type": "wheel_generation",
            "trust_level": 50
        }
    },
    metadata=metadata
)

# Convert to dict for storage
scenario_dict = scenario.model_dump()
```

## 3. Common Issues and Solutions

### 3.1 Missing workflow_type

**Issue**: The `workflow_type` field is required in the metadata.

**Solution**: Add the workflow type to the metadata:

```python
scenario.metadata['workflow_type'] = 'wheel_generation'
```

### 3.2 Missing expected_quality_criteria

**Issue**: The `expected_quality_criteria` field is required in the metadata.

**Solution**: Add basic quality criteria:

```python
scenario.metadata['expected_quality_criteria'] = {
    "Content": ["Should be relevant and helpful"],
    "Tone": ["Should be appropriate for the context"]
}
```

### 3.3 Missing user_profile_context

**Issue**: The `user_profile_context` field is required in the metadata.

**Solution**: Add a basic user profile:

```python
scenario.metadata['user_profile_context'] = {
    "trust_phase": "expansion",
    "trust_level": 50
}
```

### 3.4 Inconsistent workflow_type

**Issue**: The `workflow_type` in the metadata doesn't match the `workflow_type` in the context packet.

**Solution**: Make sure they match:

```python
workflow_type = 'wheel_generation'
scenario.metadata['workflow_type'] = workflow_type
scenario.input_data['context_packet']['workflow_type'] = workflow_type
```

### 3.5 Invalid trust_phase

**Issue**: The `trust_phase` in the user profile is not one of the valid values.

**Solution**: Use one of the valid values:

```python
scenario.metadata['user_profile_context']['trust_phase'] = 'expansion'  # Valid values: foundation, expansion, integration
```

## 4. Directory Structure

Make sure your benchmark files are organized according to the expected directory structure:

```
backend/testing/benchmark_data/
├── agents/
│   ├── mentor/
│   │   ├── wheel_generation/
│   │   ├── discussion/
│   │   └── feedback/
│   ├── orchestrator/
│   └── strategy/
├── workflows/
│   ├── wheel_generation/
│   └── activity_feedback/
└── templates/
    └── evaluation_criteria/
```

- Agent-specific scenarios go in `agents/{agent_role}/{workflow_type}/`.
- Workflow-specific scenarios go in `workflows/{workflow_type}/`.
- Evaluation templates go in `templates/evaluation_criteria/`.

## 5. Naming Convention

Make sure your benchmark files follow the naming convention:

```
{agent_role}_{workflow_type}_{scenario_type}_{variant}_{id}.json
```

For example:
- `mentor_wheel_generation_basic_001.json`
- `mentor_discussion_complex_trust_high_002.json`
- `orchestrator_activity_feedback_error_handling_003.json`

You can use the `--rename` option to automatically rename files:

```bash
python manage.py validate_benchmarks_v3 --validate-names --rename
```

## 6. Further Resources

- [Benchmark Schema System Documentation](./BENCHMARK_SCHEMA_SYSTEM.md)
- [Pydantic Documentation](https://docs.pydantic.dev/latest/)
- [JSON Schema Documentation](https://json-schema.org/learn/getting-started-step-by-step.html)
