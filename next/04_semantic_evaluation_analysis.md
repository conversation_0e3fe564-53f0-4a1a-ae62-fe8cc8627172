# Semantic Evaluation Framework Analysis

This document provides a detailed analysis of the semantic evaluation framework in the Goali benchmarking system, focusing on its architecture, implementation status, and usage patterns.

## System Architecture

The semantic evaluation framework is a key component of the benchmarking system, responsible for evaluating the quality of agent responses using multiple LLM models. It supports dimension-based scoring, score normalization, and phase-aware criteria for different trust levels.

### Key Components

1. **SemanticEvaluator** (`backend/apps/main/services/semantic_evaluator.py`)
   - Core class for evaluating agent responses using multiple LLM models
   - Supports dimension-based scoring and score normalization
   - Handles phase-aware criteria for different trust levels
   - Integrates with both agent and workflow benchmarking systems

2. **BenchmarkManager._evaluate_semantic_quality**
   - Method for integrating semantic evaluation with agent benchmarking
   - Extracts agent response from benchmark results
   - Constructs scenario context for evaluation
   - Calls SemanticEvaluator.evaluate_response

3. **AsyncWorkflowManager._evaluate_semantic_quality**
   - Method for integrating semantic evaluation with workflow benchmarking
   - Similar to BenchmarkManager._evaluate_semantic_quality but adapted for workflows
   - Extracts agent response from workflow output
   - Constructs scenario context for evaluation
   - Calls SemanticEvaluator.evaluate_response

4. **PhaseAwareCriteria** (`backend/apps/main/schemas/benchmark/criteria.py`)
   - Pydantic model for phase-aware evaluation criteria
   - Supports different criteria for different trust phases (0-39, 40-69, 70-100)
   - Validates criteria structure and trust phase ranges

5. **EvaluationCriterion** and **EvaluationCriteria** (`backend/apps/main/schemas/benchmark/criteria.py`)
   - Pydantic models for evaluation criteria
   - Support dimension, description, and weight for each criterion
   - Validate criteria structure

## Implementation Status

Based on our investigation, the semantic evaluation framework appears to be well-implemented with most core components in place. The system supports both agent and workflow benchmarking, with phase-aware criteria for different trust levels.

### Completed Features

1. **Core Evaluation**
   - Multi-model evaluator system
   - Dimension-based scoring
   - Evaluation criteria templates
   - Scoring normalization

2. **Phase-Aware Criteria**
   - Support for different criteria based on trust level
   - Validation for trust phase ranges (0-39, 40-69, 70-100)
   - Phase-specific scoring adjustments

3. **Integration with Benchmarking**
   - Integration with agent benchmarking via BenchmarkManager._evaluate_semantic_quality
   - Integration with workflow benchmarking via AsyncWorkflowManager._evaluate_semantic_quality
   - Storage of evaluation results in BenchmarkRun model

4. **Schema Validation**
   - Validation of evaluation criteria against JSON schemas
   - Validation of phase-aware criteria structure
   - Validation of evaluation templates

### In-Progress Features

Based on the TASK.md file, several features are still in progress:

1. **Admin Interface Enhancements**
   - Detailed visualization for semantic evaluation results
   - Drill-down capability for detailed analysis
   - Comparison of semantic evaluation results across runs

2. **Evaluation Criteria Management**
   - UI for creating and editing evaluation criteria templates
   - Import/export of evaluation criteria templates
   - Version control for evaluation criteria templates

## Usage Patterns

The semantic evaluation framework can be used in several ways:

1. **Agent Benchmarking**
   - Enable semantic evaluation by setting `semantic_evaluation: true` in benchmark parameters
   - Specify evaluator models in scenario metadata or benchmark parameters
   - Define evaluation criteria in scenario metadata
   - Example: `python manage.py run_benchmarks --agent-role mentor --params '{"semantic_evaluation": true, "evaluator_models": ["openai/gpt-4o"]}'`

2. **Workflow Benchmarking**
   - Enable semantic evaluation by setting `semantic_evaluation: true` in benchmark parameters
   - Specify evaluator models in scenario metadata or benchmark parameters
   - Define evaluation criteria in scenario metadata
   - Example: `python manage.py run_workflow_benchmarks --scenario-id 12345678-1234-5678-1234-567812345678 --params '{"semantic_evaluation": true, "evaluator_models": ["openai/gpt-4o"]}'`

3. **Direct API**
   - Use the `SemanticEvaluator.evaluate_response` method for programmatic access
   - Provide scenario context, agent response, and evaluation criteria
   - Optionally specify evaluator models and trust level
   - Example: `await evaluator.evaluate_response(scenario_context, agent_response, criteria, evaluator_models, trust_level=50)`

## Evaluation Criteria Definition

Evaluation criteria can be defined in several ways:

1. **Flat Criteria**
   - Define criteria as a dictionary mapping dimension names to lists of criteria
   - Example:
     ```json
     {
       "Tone": ["supportive", "friendly", "empathetic"],
       "Content": ["relevant", "helpful", "accurate"]
     }
     ```

2. **Phase-Aware Criteria**
   - Define different criteria for different trust phases
   - Example:
     ```json
     {
       "low_trust": {
         "Tone": ["respectful", "professional", "clear"],
         "Content": ["factual", "concise", "relevant"]
       },
       "medium_trust": {
         "Tone": ["friendly", "supportive", "encouraging"],
         "Content": ["helpful", "detailed", "personalized"]
       },
       "high_trust": {
         "Tone": ["warm", "empathetic", "conversational"],
         "Content": ["insightful", "tailored", "comprehensive"]
       }
     }
     ```

3. **Evaluation Templates**
   - Create reusable evaluation templates in `backend/testing/benchmark_data/templates/evaluation_criteria/`
   - Reference templates in scenario metadata using `template_reference`
   - Example:
     ```json
     {
       "metadata": {
         "template_reference": "mentor_evaluation_template"
       }
     }
     ```

## Best Practices

Based on our investigation, the following best practices are recommended for using the semantic evaluation framework:

1. **Criteria Definition**
   - Define clear, specific criteria for each dimension
   - Use phase-aware criteria for scenarios with varying trust levels
   - Create reusable evaluation templates for consistent evaluation

2. **Model Selection**
   - Use multiple evaluator models for more robust evaluation
   - Include at least one high-quality model (e.g., GPT-4) for accurate evaluation
   - Consider cost-performance tradeoffs when selecting models

3. **Response Extraction**
   - Ensure the agent's response is correctly extracted from `last_output_data`
   - Update extraction logic in BenchmarkManager or AsyncWorkflowManager if needed
   - Verify that the response contains the expected fields

4. **Scenario Context**
   - Provide comprehensive scenario context for accurate evaluation
   - Include user profile information, situation description, and input data
   - Consider including trust level for phase-aware evaluation

5. **Result Interpretation**
   - Consider both overall score and dimension-specific scores
   - Review evaluation reasoning for insights into strengths and weaknesses
   - Compare results across multiple evaluator models for robustness

## Current Issues and Challenges

Based on our investigation, there are several issues and challenges with the current semantic evaluation framework:

1. **Response Extraction**
   - The extraction of agent responses from benchmark results may not be consistent across different agent types
   - Some agents may not return the expected fields in their output
   - The extraction logic may need to be updated to handle different output formats

2. **Model Availability**
   - The availability of evaluator models may vary depending on the environment
   - Some models may not be available in certain environments or may have usage limits
   - The system should gracefully handle unavailable models

3. **Evaluation Cost**
   - Semantic evaluation can be costly, especially with multiple high-quality models
   - The system should provide cost estimates and allow for budget constraints
   - Cost optimization strategies should be implemented

4. **Criteria Consistency**
   - Ensuring consistent criteria across different scenarios can be challenging
   - The system should provide guidance on creating effective criteria
   - Reusable templates can help maintain consistency

## Next Steps

Based on our investigation, the following next steps are recommended for improving the semantic evaluation framework:

1. **Enhance Response Extraction**
   - Update extraction logic to handle different output formats
   - Add validation for extracted responses
   - Implement fallback mechanisms for missing fields

2. **Improve Model Management**
   - Implement better handling of unavailable models
   - Add support for model fallbacks
   - Provide clearer error messages for model-related issues

3. **Optimize Evaluation Cost**
   - Implement cost estimation for semantic evaluation
   - Add budget constraints for evaluation
   - Develop cost optimization strategies

4. **Enhance Criteria Management**
   - Create UI for managing evaluation criteria templates
   - Implement version control for templates
   - Provide guidance on creating effective criteria

5. **Improve Visualization**
   - Enhance visualization of semantic evaluation results
   - Add drill-down capability for detailed analysis
   - Implement comparison of results across runs

## Conclusion

The semantic evaluation framework is a powerful component of the benchmarking system, providing valuable insights into the quality of agent responses. While most core components are implemented, there are still several issues and in-progress features that need to be addressed to make the system fully usable. The next steps should focus on enhancing response extraction, improving model management, optimizing evaluation cost, enhancing criteria management, and improving visualization.
