# Benchmarking System Overview

This document provides a comprehensive overview of the current state of the benchmarking system in the Goali project, based on our investigation of the codebase.

## System Architecture

The benchmarking system consists of two main components:

1. **Agent Benchmarking System**: For testing individual agent components with controlled inputs
2. **Workflow Benchmarking System**: For testing complete LangGraph workflows with multiple stages

### Key Components

#### Core Models
- **BenchmarkScenario**: Defines a reusable test case for an agent or workflow
- **BenchmarkRun**: Records a specific benchmark execution with detailed metrics
- **BenchmarkTag**: For categorizing scenarios

#### Services
- **BenchmarkManager**: Unified service for managing agent benchmarks
- **AsyncWorkflowManager**: Base class for workflow benchmarking with async-first architecture
- **WheelWorkflowBenchmarkManager**: Concrete implementation for wheel generation workflow
- **SemanticEvaluator**: System for evaluating the semantic quality of agent responses
- **SchemaRegistry**: Central registry for JSON schemas used in the benchmarking system
- **SchemaValidationService**: Service for validating benchmark components against JSON schemas
- **SchemaVersionManager**: Service for managing schema versions and migrations

#### Utilities
- **TokenTracker**: Enhanced system for tracking token usage across workflow execution
- **StageTimer**: Detailed timing collection for workflow stages
- **MockToolRegistry**: Advanced tool mocking with conditional responses and error simulation

## Current Implementation Status

Based on our investigation, the benchmarking system appears to be well-developed with most core components implemented. The system follows an async-first architecture with proper error handling and database access patterns.

### Completed Features

1. **Async-First Foundation**
   - AsyncWorkflowManager base class with proper error handling
   - Enhanced token tracking system
   - Detailed stage timing collection
   - Advanced tool mocking system
   - WebSocket progress reporting
   - Celery task integration

2. **Semantic Evaluation Framework**
   - Multi-model evaluator system
   - Dimension-based scoring
   - Evaluation criteria templates
   - Scoring normalization
   - Phase-aware criteria for different trust levels

3. **Schema Validation System**
   - JSON Schema validation for benchmark components
   - Schema versioning with migration utilities
   - Directory structure validation
   - Template validation

4. **Testing Infrastructure**
   - End-to-end tests for workflow benchmarking
   - Integration tests for compatibility with existing benchmark system
   - WebSocket tests for progress reporting
   - Celery task tests for task creation and result handling
   - Tool mocking tests for complex conditional responses

### In-Progress Features

Based on the TASK.md file, several features are still in progress:

1. **Admin Interface Enhancements**
   - Benchmark comparison view
   - Detailed visualization for semantic evaluation results
   - Regression analysis visualization

2. **Cost Monitoring**
   - Budget configuration for benchmarks
   - Cost alerts
   - Cost optimization recommendations

3. **Resource Tracking**
   - Detailed token usage breakdown by stage
   - Token usage tracking by model
   - Visualization for token usage patterns

## Usage Workflow

The benchmarking system can be used in several ways:

1. **Admin UI**
   - Navigate to `/admin/benchmarks/`
   - Select a scenario
   - Configure parameters
   - Run the benchmark
   - View results in `/admin/benchmarks/history/`

2. **Command Line**
   - Use `python manage.py run_benchmarks` for agent benchmarks
   - Use `python manage.py run_workflow_benchmarks` for workflow benchmarks
   - Filter by scenario name, agent role, or tags
   - Configure parameters like runs, warmup runs, and semantic evaluation

3. **API**
   - Use the benchmark API endpoints for programmatic access
   - `/admin/benchmarks/api/run/` for running benchmarks
   - `/admin/benchmarks/api/run/<uuid:run_id>/` for getting details
   - `/admin/benchmarks/api/run-all/` for running all active benchmarks

## Benchmark Scenario Creation

Benchmark scenarios are defined as JSON files and loaded into the database. The process involves:

1. **Define Schemas**
   - Ensure core schemas exist in `backend/schemas/`
   - Run `python manage.py seed_benchmark_schemas` if needed
   - Create reusable evaluation templates in `backend/testing/benchmark_data/templates/evaluation_criteria/`

2. **Setup Directory Structure**
   - Run `python manage.py setup_benchmark_structure` to create the organized directory structure

3. **Define Scenarios**
   - Create JSON files in the appropriate directories:
     - Agent-specific scenarios: `backend/testing/benchmark_data/agents/<agent_role>/<workflow_type>/`
     - Workflow-specific scenarios: `backend/testing/benchmark_data/workflows/<workflow_type>/`
     - Evaluation templates: `backend/testing/benchmark_data/templates/evaluation_criteria/`

4. **Validate Scenarios**
   - Run `python manage.py validate_benchmarks_v2` to check scenarios against schemas

5. **Load Scenarios**
   - Use `python manage.py create_benchmark_scenarios` to load scenarios into the database

## Current Issues and Challenges

Based on our investigation, there are several issues and challenges with the current benchmarking system:

1. **Token Tracking System**
   - Issues with TokenTracker tests
   - Need for proper token counting for all LLM models
   - Validation for token usage data

2. **Celery Task Management**
   - Issues with result backend configuration
   - Need for proper task retry mechanisms
   - Task cancellation handling

3. **Tool Mocking System**
   - Issues with conditional response handling
   - Need for proper assertion checking
   - Mock usage tracking

4. **Database Connection Stability**
   - Issues with database connections in async code
   - Need for proper connection monitoring and cleanup

## Next Steps

Based on our investigation, the following next steps are recommended:

1. **Fix Core Issues**
   - Address token tracking system issues
   - Fix Celery task management problems
   - Enhance tool mocking capabilities
   - Improve database connection stability

2. **Complete In-Progress Features**
   - Finish admin interface enhancements
   - Complete cost monitoring implementation
   - Enhance resource tracking

3. **Improve Documentation**
   - Create comprehensive user guide for the benchmarking system
   - Document best practices for creating and running benchmarks
   - Provide examples of common benchmark scenarios

4. **Enhance Testing**
   - Improve test coverage for all components
   - Create more robust test fixtures
   - Implement utility functions for creating test scenarios

## Conclusion

The benchmarking system is a comprehensive solution for evaluating agent and workflow performance, with most core components implemented. However, there are still several issues and in-progress features that need to be addressed to make the system fully usable. The next steps should focus on fixing core issues, completing in-progress features, improving documentation, and enhancing testing.
