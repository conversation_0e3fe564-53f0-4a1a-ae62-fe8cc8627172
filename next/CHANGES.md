# Architectural Changes to the Benchmarking System

## Schema Validation Issues and Fixes

### 1. Schema Registry Enhancement

The schema registry was enhanced to properly load and register the evaluation_template schema. This was necessary because the validation command was failing to validate the evaluation templates correctly.

```diff
// apps/main/services/schema_registry.py
    SCHEMA_TYPES = [
        "user_profile",
        "situation",
        "evaluation_criteria",
        "tool_expectation",
        "workflow_benchmark"
+       "evaluation_template"
    ]
```

Also added code to load the evaluation_template schema:

```diff
// apps/main/services/schema_registry.py
            user_profile_schema_path = os.path.join(self.schema_dir, 'user_profile.schema.json')
            situation_schema_path = os.path.join(self.schema_dir, 'situation.schema.json')
            evaluation_criteria_schema_path = os.path.join(self.schema_dir, 'evaluation_criteria.schema.json')
            tool_expectation_schema_path = os.path.join(self.schema_dir, 'tool_expectation.schema.json')
            workflow_benchmark_schema_path = os.path.join(self.schema_dir, 'workflow_benchmark.schema.json')
+           evaluation_template_schema_path = os.path.join(self.schema_dir, 'evaluation_template.schema.json')
```

And added code to register the evaluation_template schema:

```diff
// apps/main/services/schema_registry.py
+           # Load evaluation template schema if it exists
+           if os.path.exists(evaluation_template_schema_path):
+               try:
+                   with open(evaluation_template_schema_path, 'r') as f:
+                       evaluation_template_schema = json.load(f)
+                       schemas_to_save['evaluation_template'] = evaluation_template_schema
+               except Exception as e:
+                   logger.error(f"Error loading evaluation template schema: {e}", exc_info=True)
```

### 2. Benchmark Validation Service Update

The benchmark validation service was updated to use the evaluation_template schema for validating templates instead of the evaluation_criteria schema:

```diff
// apps/main/services/benchmark_validation.py
-               # Validate the template
-               is_valid, errors = self.validator.validate_evaluation_criteria(template)
+               # Validate the template using the evaluation_template schema
+               is_valid, errors = self.registry.validate("evaluation_template", template)
```

### 3. Schema Validator Service Enhancement

The schema validator service was enhanced to handle the situation field in scenario files:

```diff
// apps/main/services/schema_validator_service.py
        # Validate situation from input_data if present
        if 'input_data' in scenario and isinstance(scenario['input_data'], dict) and 'context_packet' in scenario['input_data']:
            is_valid, errors = self.validate_situation(scenario['input_data']['context_packet'])
            result['components']['situation'] = {
                'valid': is_valid,
                'errors': errors
            }
            if not is_valid:
                result['valid'] = False
                result['errors'].extend([f"Situation: {error}" for error in errors])
                
+       # Also validate the situation field if present
+       if 'situation' in scenario and isinstance(scenario['situation'], dict):
+           # If the situation has workflow_type and text, validate it directly
+           if 'workflow_type' in scenario['situation'] and 'text' in scenario['situation']:
+               is_valid, errors = self.validate_situation(scenario['situation'])
+               result['components']['situation_field'] = {
+                   'valid': is_valid,
+                   'errors': errors
+               }
+               if not is_valid:
+                   result['valid'] = False
+                   result['errors'].extend([f"Situation field: {error}" for error in errors])
```

## Inconsistencies in Schema Formats

### 1. Inconsistent Mock Tool Response Formats

We identified inconsistencies in how mock tool responses are formatted across different parts of the codebase:

1. **Workflow Benchmark Schema**: Expects mock_tool_responses to be objects with a 'response' property:
   ```json
   "mock_tool_responses": {
     "get_user_profile": {
       "response": "..."
     }
   }
   ```

2. **Tool Expectation Schema**: Expects mock_responses to be strings:
   ```json
   "mock_responses": {
     "get_user_profile": "..."
   }
   ```

3. **Schema Validator Service**: Uses the mock_tool_responses field as the mock_responses field for the tool_expectation schema, causing validation errors when both formats are used.

### 2. Situation Field Format Inconsistency

The situation field in scenario files has inconsistent formats:

1. Some files have the situation field as a nested object with 'text' and 'context' properties.
2. The schema expects the 'text' and 'workflow_type' properties directly in the situation object.

## Recommendations for Future Improvements

1. **Standardize Mock Tool Response Format**: Adopt a single format for mock tool responses across all schemas to avoid confusion and validation errors.

2. **Enhance Schema Validation**: Improve the schema validation service to handle different formats of the same field, providing better error messages and suggestions for fixing issues.

3. **Documentation**: Create comprehensive documentation for the benchmarking system schema validation, including examples of valid formats for all fields.

4. **Schema Migration**: Implement a schema migration system to handle changes to schema formats over time, ensuring backward compatibility.

5. **Validation Command Enhancement**: Enhance the validate_benchmarks_v2 command to provide more detailed error messages and suggestions for fixing issues.

6. **Test Coverage**: Increase test coverage for schema validation to catch issues earlier in the development process.
