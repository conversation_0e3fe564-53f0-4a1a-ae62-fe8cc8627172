# Benchmark Schema System

This document describes the new benchmark schema system, which uses Pydantic models for robust validation and clear error messages.

## 1. Overview

The benchmark schema system provides a comprehensive set of Pydantic models for validating benchmark scenarios, tool expectations, evaluation criteria, and other components. It also includes utilities for naming conventions and directory structure validation.

## 2. Core Components

### 2.1 Base Models

- **BenchmarkBaseModel**: Base model for all benchmark schema models, providing common configuration and validation.
- **VersionedModel**: Base class for versioned models, providing version tracking and validation.
- **NamedEntity**: Base class for named entities, providing name and description fields.

### 2.2 Scenario Models

- **BenchmarkScenario**: Schema for benchmark scenarios, including metadata, input data, and agent role.
- **BenchmarkScenarioMetadata**: Metadata for benchmark scenarios, including workflow type, user profile, and evaluation criteria.
- **UserProfile**: User profile for benchmark scenarios, including trust phase, trust level, and personality traits.
- **Situation**: Situation for benchmark scenarios, including workflow type, text, and context.
- **EvaluationCriteria**: Collection of evaluation criteria for benchmark scenarios.
- **PhaseAwareCriteria**: Phase-aware evaluation criteria for different trust phases.
- **ToolExpectation**: Expectation for tool calls in benchmark scenarios.

### 2.3 Run Models

- **BenchmarkRun**: Schema for benchmark run results, including performance metrics and evaluation results.
- **TokenUsage**: Token usage information for benchmark runs.
- **StagePerformance**: Performance metrics for workflow stages.
- **SemanticEvaluation**: Semantic evaluation results for benchmark runs.

### 2.4 Validation

- **PydanticSchemaValidator**: Schema validation service using Pydantic models.
- **SchemaValidationResult**: Result of schema validation, including errors and model instance.

### 2.5 Naming Convention

- **BenchmarkNamingConvention**: Utility for parsing and generating benchmark scenario names.
- **AgentRole**: Standard agent roles in the system.
- **WorkflowType**: Standard workflow types in the system.
- **ScenarioType**: Standard scenario types in the system.
- **ScenarioVariant**: Standard scenario variants in the system.

## 3. Naming Convention

The naming convention for benchmark scenarios follows this pattern:

```
{agent_role}_{workflow_type}_{scenario_type}_{variant}_{id}.json
```

For example:
- `mentor_wheel_generation_basic_v1_001.json`
- `mentor_discussion_complex_trust_high_002.json`
- `orchestrator_activity_feedback_error_handling_003.json`

### 3.1 Agent Roles

- `mentor`: The mentor agent that interacts directly with users.
- `orchestrator`: The orchestrator agent that coordinates workflows.
- `strategy`: The strategy agent that generates plans and strategies.
- `evaluator`: The evaluator agent that evaluates outputs.
- `test`: Used for test scenarios.

### 3.2 Workflow Types

- `wheel_generation`: Wheel generation workflow.
- `activity_feedback`: Activity feedback workflow.
- `discussion`: Discussion workflow.
- `onboarding`: Onboarding workflow.
- `goal_setting`: Goal setting workflow.
- `test_workflow`: Used for test workflows.

### 3.3 Scenario Types

- `basic`: Basic scenarios that test core functionality.
- `complex`: Complex scenarios that test advanced functionality.
- `error_handling`: Scenarios that test error handling.
- `edge_case`: Scenarios that test edge cases.
- `performance`: Scenarios that test performance.
- `integration`: Scenarios that test integration with other components.
- `regression`: Scenarios that test regression issues.
- `test`: Used for test scenarios.

### 3.4 Scenario Variants

- `trust_low`: Low trust level (0-39).
- `trust_medium`: Medium trust level (40-69).
- `trust_high`: High trust level (70-100).
- `personality_open`: High openness personality trait.
- `personality_closed`: Low openness personality trait.
- `environment_noisy`: Noisy environment.
- `environment_quiet`: Quiet environment.
- `mood_positive`: Positive mood.
- `mood_negative`: Negative mood.
- `stress_high`: High stress level.
- `stress_low`: Low stress level.
- `time_limited`: Limited time availability.
- `time_unlimited`: Unlimited time availability.

## 4. Directory Structure

The benchmark data directory structure follows this pattern:

```
backend/testing/benchmark_data/
├── agents/
│   ├── mentor/
│   │   ├── wheel_generation/
│   │   ├── discussion/
│   │   └── feedback/
│   ├── orchestrator/
│   └── strategy/
├── workflows/
│   ├── wheel_generation/
│   └── activity_feedback/
└── templates/
    └── evaluation_criteria/
```

- Agent-specific scenarios go in `agents/{agent_role}/{workflow_type}/`.
- Workflow-specific scenarios go in `workflows/{workflow_type}/`.
- Evaluation templates go in `templates/evaluation_criteria/`.

## 5. Validation Command

The new validation command (`validate_benchmarks_v3.py`) provides enhanced validation for benchmark scenarios, with improved error reporting and automatic fixing of common issues.

### 5.1 Usage

```bash
python manage.py validate_benchmarks_v3 [options]
```

### 5.2 Options

- `--scenario-id`: Validate a specific scenario by ID.
- `--agent-role`: Validate scenarios for a specific agent role.
- `--file`: Validate a specific file.
- `--directory`: Validate all files in a specific directory.
- `--fix`: Attempt to fix common issues in scenarios.
- `--rename`: Rename files to follow the naming convention.
- `--export`: Export scenario definitions as schema-compliant JSON files.
- `--output-dir`: Directory to save validated scenario files.
- `--report-format`: Format for validation reports (`json` or `text`).
- `--validate-files`: Validate scenario files in the benchmark data directory.
- `--validate-structure`: Validate the directory structure.
- `--validate-names`: Validate file names against the naming convention.
- `--comprehensive`: Perform comprehensive validation (structure, files, names, and database).

## 6. Examples

### 6.1 Creating a New Benchmark Scenario

```python
from next.benchmark_schema import (
    BenchmarkScenario, BenchmarkScenarioMetadata, UserProfile,
    WorkflowType, TrustPhase
)

# Create a user profile
user_profile = UserProfile(
    trust_phase=TrustPhase.EXPANSION,
    trust_level=50,
    personality_traits={
        "openness": 0.8,
        "conscientiousness": 0.7,
        "extraversion": 0.6,
        "agreeableness": 0.9,
        "neuroticism": 0.3
    }
)

# Create metadata
metadata = BenchmarkScenarioMetadata(
    workflow_type=WorkflowType.WHEEL_GENERATION,
    user_profile_context=user_profile,
    expected_quality_criteria={
        "Content": ["Should be relevant and helpful"],
        "Tone": ["Should be supportive and encouraging"],
        "Personalization": ["Should be tailored to the user"]
    },
    warmup_runs=1,
    benchmark_runs=3
)

# Create scenario
scenario = BenchmarkScenario(
    name="Test Wheel Generation Scenario",
    description="A test scenario for wheel generation",
    agent_role="mentor",
    input_data={
        "user_message": "I want to try something new today",
        "context_packet": {
            "workflow_type": "wheel_generation",
            "trust_level": 50
        }
    },
    metadata=metadata,
    is_active=True
)

# Validate scenario
validation_result = BenchmarkScenario.validate_scenario(scenario.model_dump())
if validation_result.is_valid:
    print("Scenario is valid!")
else:
    print("Scenario is invalid:")
    for error in validation_result.errors:
        print(f"- {error}")
```

### 6.2 Validating a File

```python
from next.benchmark_schema import PydanticSchemaValidator

validator = PydanticSchemaValidator()
result = validator.validate_file("path/to/scenario.json")

if result.is_valid:
    print("File is valid!")
else:
    print("File is invalid:")
    for error in result.errors:
        print(f"- {error}")
```

### 6.3 Using the Naming Convention

```python
from next.benchmark_schema import BenchmarkNamingConvention

# Parse a name
components = BenchmarkNamingConvention.parse_name("mentor_wheel_generation_basic_001.json")
print(components)
# {'agent_role': 'mentor', 'workflow_type': 'wheel_generation', 'scenario_type': 'basic', 'variant': None, 'id': '001'}

# Generate a name
name = BenchmarkNamingConvention.generate_name(
    agent_role="mentor",
    workflow_type="discussion",
    scenario_type="complex",
    id=2,
    variant="trust_high"
)
print(name)
# mentor_discussion_complex_trust_high_002
```
