# Benchmark Admin Frontend Implementation Guide

## Overview

This document provides comprehensive information for implementing a dedicated admin frontend for benchmark backoffice management. The frontend will enable users to create, edit, and manage benchmark scenarios and acceptance criteria through an intuitive interface.

## Current Implementation

The benchmark management code has been extracted from the main admin_tools views.py file to a dedicated benchmark subfolder. This improves code organization and maintainability by:

1. Separating benchmark management code from other admin tools
2. Providing a clear structure for benchmark-related views
3. Making it easier to find and modify benchmark management code

### Structure

```
backend/apps/admin_tools/
├── benchmark/
│   ├── __init__.py
│   ├── urls.py
│   └── views.py
└── views.py
```

### Views

The benchmark management views have been moved to `benchmark/views.py` and include:

- `benchmark_management`: Main view for managing benchmark scenarios and evaluation criteria
- `BenchmarkScenarioView`: API view for CRUD operations on benchmark scenarios
- `WorkflowTypeView`: API view for operations on workflow types
- `EvaluationCriteriaTemplateView`: API view for CRUD operations on evaluation criteria templates
- `BenchmarkValidationView`: API view for validating benchmark scenarios
- `run_all_benchmarks_view`: API endpoint for running all active benchmark scenarios
- `export_scenarios`: View for exporting benchmark scenarios to a JSON file
- `import_scenarios`: View for importing benchmark scenarios from a JSON file

### URL Configuration

The URL configuration in `config/admin.py` has been updated to point to the new benchmark views. The URLs are still accessible under the `/admin/benchmarks/` path with the namespace `game_of_life_admin`.

## Data Models and Relationships

### Core Models

1. **BenchmarkScenario**
   - Primary model for benchmark scenarios
   - Contains metadata, input data, and configuration
   - Fields:
     - `name`: String - Name of the scenario
     - `description`: String - Description of the scenario
     - `agent_role`: String - Role of the agent (mentor, orchestrator, etc.)
     - `input_data`: JSON - Input data for the scenario
     - `metadata`: JSON - Metadata for the scenario
     - `is_active`: Boolean - Whether the scenario is active

2. **BenchmarkScenarioMetadata**
   - Embedded within BenchmarkScenario
   - Contains metadata about the scenario
   - Fields:
     - `workflow_type`: Enum - Type of workflow (wheel_generation, discussion, etc.)
     - `expected_quality_criteria`: Object - Quality criteria for evaluation
     - `user_profile_context`: Object - User profile context
     - `evaluation_models`: Array - Models to use for evaluation

3. **EvaluationCriteriaTemplate**
   - Template for evaluation criteria
   - Fields:
     - `name`: String - Name of the template
     - `criteria`: JSON - Criteria for evaluation
     - `description`: String - Description of the template

### Relationships

- BenchmarkScenario contains BenchmarkScenarioMetadata
- BenchmarkScenarioMetadata references EvaluationCriteriaTemplate
- BenchmarkScenario is associated with specific agent roles and workflow types

## UI Requirements

### Dashboard

1. **Overview Dashboard**
   - Display summary statistics:
     - Total scenarios by agent role
     - Total scenarios by workflow type
     - Active vs. inactive scenarios
     - Recent validation results

2. **Scenario List View**
   - Filterable by:
     - Agent role
     - Workflow type
     - Validation status
     - Active status
   - Sortable columns
   - Batch operations (activate/deactivate, export)

### Scenario Management

1. **Scenario Creation Form**
   - Step-by-step wizard:
     - Basic information (name, description, agent role)
     - Workflow type selection
     - Input data configuration
     - Metadata configuration
     - Evaluation criteria selection

2. **Scenario Editor**
   - JSON editor with schema validation
   - Form-based editor for non-technical users
   - Preview mode to visualize the scenario
   - Validation feedback in real-time

3. **Scenario Validation**
   - Run validation on demand
   - Display validation results
   - Offer automatic fixes for common issues
   - Rename files to follow naming convention

### Evaluation Criteria Management

1. **Criteria Template List**
   - View all available templates
   - Filter by agent role and workflow type
   - Clone existing templates

2. **Criteria Template Editor**
   - Add/edit criteria dimensions
   - Configure weights for each criterion
   - Preview the template
   - Test against sample responses

### User Profile Management

1. **User Profile Overview**
   - Comprehensive management of user profiles for benchmark testing
   - Create and manage mock user data that agents interact with during scenarios
   - Support for different user archetypes and personality traits

2. **Profile Creation and Editing**
   - **Trust Levels**: Define user trust phases (Foundation, Expansion, Integration) and numeric trust levels (0-100)
   - **Personality Traits**: Configure HEXACO personality dimensions for realistic user modeling
   - **Demographics**: Set age, location, occupation, and other demographic information
   - **Preferences**: Define user preferences for activities, communication styles, and interests
   - **Mock Tool Responses**: Configure how tools should respond when this profile is used in benchmarks

3. **Profile Templates and Archetypes**
   - Predefined templates for common user archetypes:
     - New User (Foundation Phase) - Low trust, cautious approach
     - Experienced User (Expansion Phase) - Moderate trust, open to challenges
     - Confident User (Integration Phase) - High trust, collaborative approach
     - Stressed User - Under pressure, needs quick solutions
     - Cautious User - Risk-averse, prefers safe options
     - Adventurous User - Open to new experiences, high trust
   - Template loading and customization
   - Profile preview and validation

4. **Profile Management Features**
   - Filter profiles by trust phase, archetype, and status
   - Batch operations (activate/deactivate, export, duplicate)
   - Profile usage tracking and analytics
   - Import/export profiles as JSON
   - Profile validation and error checking

5. **Integration with Scenarios**
   - Select user profiles when creating/editing scenarios
   - Profile inheritance and customization per scenario
   - Context variable synchronization with selected profiles
   - Mock tool response configuration based on profile data

## Implementation Guidelines

### Technology Stack

1. **Frontend**
   - React with TypeScript
   - Material-UI or Tailwind CSS for UI components
   - React Query for data fetching
   - Formik or React Hook Form for form management
   - Monaco Editor for JSON editing

2. **API Integration**
   - RESTful API endpoints for CRUD operations
   - WebSocket for real-time validation feedback
   - File upload/download for importing/exporting scenarios

### Key Features to Implement

1. **Schema-Driven Forms**
   - Generate forms based on Pydantic schemas
   - Validate input against schemas in real-time
   - Provide contextual help and examples

2. **Intelligent Naming**
   - Implement the BenchmarkNamingConvention logic in the frontend
   - Suggest names based on metadata
   - Validate names against the convention

3. **Validation Pipeline**
   - Integrate with the validate_benchmarks command
   - Display validation results in a user-friendly way
   - Offer one-click fixes for common issues

4. **Template Management**
   - Create and edit evaluation criteria templates
   - Apply templates to scenarios
   - Version control for templates

5. **Batch Operations**
   - Import/export multiple scenarios
   - Validate multiple scenarios at once
   - Apply changes to multiple scenarios

### API Endpoints

1. **Scenario Management**
   - `GET /api/benchmark/scenarios/` - List all scenarios
   - `GET /api/benchmark/scenarios/{id}/` - Get a specific scenario
   - `POST /api/benchmark/scenarios/` - Create a new scenario
   - `PUT /api/benchmark/scenarios/{id}/` - Update a scenario
   - `DELETE /api/benchmark/scenarios/{id}/` - Delete a scenario
   - `POST /api/benchmark/scenarios/{id}/validate/` - Validate a scenario
   - `POST /api/benchmark/scenarios/{id}/fix/` - Fix common issues in a scenario
   - `POST /api/benchmark/scenarios/import/` - Import scenarios from files
   - `GET /api/benchmark/scenarios/export/` - Export scenarios as files

2. **Template Management**
   - `GET /api/benchmark/templates/` - List all templates
   - `GET /api/benchmark/templates/{id}/` - Get a specific template
   - `POST /api/benchmark/templates/` - Create a new template
   - `PUT /api/benchmark/templates/{id}/` - Update a template
   - `DELETE /api/benchmark/templates/{id}/` - Delete a template
   - `POST /api/benchmark/templates/{id}/clone/` - Clone a template

3. **User Profile Management**
   - `GET /api/benchmark/user-profiles/` - List all user profiles
   - `GET /api/benchmark/user-profiles/{id}/` - Get a specific user profile
   - `POST /api/benchmark/user-profiles/` - Create a new user profile
   - `PUT /api/benchmark/user-profiles/{id}/` - Update a user profile
   - `DELETE /api/benchmark/user-profiles/{id}/` - Delete a user profile
   - `POST /api/benchmark/user-profiles/{id}/duplicate/` - Duplicate a user profile
   - `POST /api/benchmark/user-profiles/import/` - Import user profiles from JSON
   - `GET /api/benchmark/user-profiles/export/` - Export user profiles as JSON
   - `GET /api/benchmark/user-profiles/templates/` - Get predefined profile templates
   - `POST /api/benchmark/user-profiles/{id}/validate/` - Validate a user profile

4. **Validation**
   - `POST /api/benchmark/validate/` - Validate multiple scenarios
   - `GET /api/benchmark/validation-results/` - Get validation results
   - `POST /api/benchmark/fix-all/` - Fix issues in multiple scenarios

### Database Schema

The frontend will interact with the existing Django models through the API. The key models are:

```python
class BenchmarkScenario(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    agent_role = models.CharField(max_length=50)
    input_data = models.JSONField(default=dict)
    metadata = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class EvaluationCriteriaTemplate(models.Model):
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True)
    criteria = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

## Implementation Workflow

### Phase 1: Setup and Basic CRUD

1. **Project Setup**
   - Create a new React application with TypeScript
   - Set up routing and state management
   - Configure API client and authentication

2. **Basic CRUD Operations**
   - Implement scenario list view
   - Create basic forms for scenario creation/editing
   - Implement template list view and editor

3. **Schema Integration**
   - Import Pydantic schemas from backend
   - Generate form validation rules from schemas
   - Implement JSON editor with schema validation

### Phase 2: Advanced Features

1. **Validation Pipeline**
   - Integrate with backend validation API
   - Implement real-time validation feedback
   - Create UI for displaying validation results

2. **Template System**
   - Implement template selection in scenario editor
   - Create template management interface
   - Add template versioning and history

3. **Batch Operations**
   - Implement multi-select in list views
   - Create batch validation and fix operations
   - Add import/export functionality

### Phase 3: Refinement and Optimization

1. **User Experience Improvements**
   - Add contextual help and tooltips
   - Implement keyboard shortcuts
   - Create guided tours for new users

2. **Performance Optimization**
   - Implement pagination and lazy loading
   - Add caching for frequently accessed data
   - Optimize API calls and state management

3. **Testing and Deployment**
   - Write unit and integration tests
   - Set up CI/CD pipeline
   - Deploy to production environment

## Best Practices

1. **Code Organization**
   - Use feature-based folder structure
   - Separate UI components from business logic
   - Create reusable components for common patterns

2. **State Management**
   - Use React Query for server state
   - Use Context API or Redux for global state
   - Implement optimistic updates for better UX

3. **Error Handling**
   - Implement comprehensive error boundaries
   - Create user-friendly error messages
   - Log errors to monitoring service

4. **Accessibility**
   - Follow WCAG 2.1 guidelines
   - Implement keyboard navigation
   - Test with screen readers

5. **Security**
   - Implement proper authentication and authorization
   - Validate all user input
   - Protect against common web vulnerabilities

## Recent Updates (2025-05-28)

### Benchmark Runs Display Fixes

**Problem Resolved**: The benchmark runs display in the admin interface was showing incorrect data:
- Trust level column displayed "[object Object]" instead of numeric values
- All context variables (valence, arousal, stress level, time pressure) showed "N/A"
- Token usage and cost columns were hardcoded to "N/A"

### Implementation Details

#### Backend API Enhancements (`apps/admin_tools/views.py`)

1. **Enhanced Context Variable Extraction**:
   - Added proper handling for object-format trust levels with `{value, label, range}` structure
   - Implemented fallback lookup in `evaluation_template_data` when context variables not found
   - Enhanced extraction of mood and environment variables from nested objects

2. **Token Usage and Cost Formatting**:
   - Added proper token usage formatting: `"input+output=total"` format
   - Added cost formatting with 6 decimal places: `"$X.XXXXXX"`
   - Integrated with `BenchmarkRun.token_usage_display` property

#### Frontend JavaScript Updates (`backend/static/admin/js/benchmark_management.js`)

1. **Dynamic Data Display**:
   - Removed hardcoded "N/A" values for token usage and cost
   - Updated `loadBenchmarkRuns` function to consume actual API data
   - Added proper handling for all context variables

### API Response Format

The benchmark runs API now returns comprehensive data:

```json
{
  "runs": [
    {
      "id": 26,
      "scenario_name": "Mentor - Crisis Support - Foundation",
      "agent_role": "mentor",
      "execution_date": "2025-05-28T15:42:06.326299+00:00",
      "success_rate": 1.0,
      "semantic_score": 0.26,
      "mean_duration": 15972.367397000198,
      "trust_level": 20,
      "valence": "N/A",
      "arousal": "N/A",
      "stress_level": "N/A",
      "time_pressure": "N/A",
      "token_usage": "114+68=182",
      "cost": "$0.000032",
      "total_input_tokens": 114,
      "total_output_tokens": 68,
      "estimated_cost": 3.2e-05
    }
  ]
}
```

### Grafana Integration

#### Timing Analysis Dashboard

Created comprehensive Grafana dashboard for fine-grained benchmark timing analysis:

**Location**: `monitoring/grafana/dashboards/benchmark-analytics/benchmark-timing-analysis.json`

**Features**:
- **Total Duration Over Time**: Track benchmark execution times
- **Average Duration by Scenario**: Compare scenario performance
- **Stage Performance Breakdown**: Detailed stage-level metrics
- **Stage Duration Over Time**: Stacked view of stage contributions
- **Token Usage and Cost Over Time**: Resource utilization tracking

**Access**: Available in Grafana under "Benchmark Analytics" folder

### Testing Implementation

#### Comprehensive Test Coverage

1. **Backend API Tests** (`apps/admin_tools/tests/test_benchmark_runs_display.py`):
   - Complete API response format validation
   - Context variable extraction edge cases
   - Token usage and cost formatting
   - Missing data handling

2. **Frontend Integration Tests** (`apps/admin_tools/tests/test_benchmark_frontend_display.py`):
   - Admin page loading verification
   - JavaScript data consumption testing
   - Field presence validation
   - Data type verification

#### Test Execution Commands

```bash
# Run backend API tests
cd backend
docker-compose run --rm web-test python manage.py test apps.admin_tools.tests.test_benchmark_runs_display

# Run frontend integration tests
docker-compose run --rm web-test python manage.py test apps.admin_tools.tests.test_benchmark_frontend_display

# Test API integration manually
docker-compose exec web python test_api_integration.py

# Test benchmark API functionality
docker-compose exec web python manage.py test_benchmark_api
```

### Data Formatting Standards

#### Token Usage Display
- **Format**: `"input+output=total"` (e.g., "114+68=182")
- **Fallback**: `"N/A"` when no token data available
- **Source**: Uses `BenchmarkRun.token_usage_display` property

#### Cost Display
- **Format**: `"$X.XXXXXX"` with 6 decimal places (e.g., "$0.000032")
- **Fallback**: `"$0.000000"` when no cost data available
- **Source**: `BenchmarkRun.estimated_cost` field

#### Context Variables
- **Trust Level**: Numeric value extracted from object format
- **Mood Variables**: `valence`, `arousal` from `mood` object
- **Environment Variables**: `stress_level`, `time_pressure` from `environment` object
- **Fallback**: `"N/A"` when data not available

### Troubleshooting Guide

#### Common Issues and Solutions

1. **"[object Object]" Display**:
   - **Cause**: Context variables returned as objects
   - **Solution**: Backend extracts `value` field from object format

2. **"N/A" for All Values**:
   - **Cause**: Frontend not consuming API data
   - **Solution**: Updated JavaScript to use actual API response

3. **Missing Token/Cost Data**:
   - **Cause**: API not including token/cost fields
   - **Solution**: Added proper field extraction and formatting

#### Debugging Commands

```bash
# Check API response
curl -s "http://localhost:8000/admin/benchmarks/api/run/" | python -m json.tool

# Verify database data
docker-compose exec web python -c "
from apps.main.models import BenchmarkRun
run = BenchmarkRun.objects.first()
print(f'Token usage: {run.token_usage_display}')
print(f'Cost: {run.estimated_cost}')
"

# Test management command
docker-compose exec web python manage.py test_benchmark_api
```
