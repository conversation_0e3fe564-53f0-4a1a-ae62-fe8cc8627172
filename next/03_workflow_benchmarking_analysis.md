# Workflow Benchmarking System Analysis

This document provides a detailed analysis of the workflow benchmarking system in the Goali project, focusing on its architecture, implementation status, and usage patterns.

## System Architecture

The workflow benchmarking system extends the existing agent benchmarking system to evaluate complete LangGraph workflows, providing insights into performance, resource usage, and quality across multi-agent interactions.

### Key Components

1. **AsyncWorkflowManager** (`backend/apps/main/services/async_workflow_manager.py`)
   - Base class for workflow benchmarking with async-first architecture
   - Provides proper error handling and async context management
   - Includes automatic agent_definition handling
   - Implements WebSocket progress reporting
   - Integrates with Celery for background task execution

2. **TokenTracker** (`backend/apps/main/services/async_workflow_manager.py`)
   - Enhanced token tracking system for workflow-level token usage
   - Requires a `run_id` parameter in its constructor
   - Provides async methods for recording token usage and calculating costs
   - Supports tracking by stage and model
   - Provides detailed cost calculation and reporting

3. **StageTimer** (`backend/apps/main/services/async_workflow_manager.py`)
   - Detailed stage timing collection with aggregation across runs
   - Provides async methods for starting and ending stage timings
   - Calculates statistical metrics for stage performance

4. **WheelWorkflowBenchmarkManager** (`backend/apps/main/services/wheel_workflow_benchmark_manager.py`)
   - Concrete implementation for wheel generation workflow
   - Extends AsyncWorkflowManager with workflow-specific logic
   - Implements the `_run_workflow_benchmark` method

5. **MockToolRegistry** (Enhanced version)
   - Advanced tool mocking with conditional responses
   - Supports assertions and error simulation
   - Tracks tool usage statistics

6. **SemanticEvaluator** (`backend/apps/main/services/semantic_evaluator.py`)
   - Evaluates the semantic quality of agent responses using multiple LLM models
   - Supports dimension-based scoring and score normalization
   - Integrates with the workflow benchmarking system

## Implementation Status

Based on our investigation, the workflow benchmarking system appears to be well-implemented with most core components in place. The system follows an async-first architecture with proper error handling and database access patterns.

### Completed Features

1. **Core Infrastructure**
   - AsyncWorkflowManager base class with proper error handling
   - Enhanced token tracking system
   - Detailed stage timing collection
   - Advanced tool mocking system

2. **Async Integration**
   - Proper async context management
   - Database operation safeguards
   - WebSocket progress reporting
   - Async event emission for benchmark stages

3. **Celery Integration**
   - Benchmark task definitions
   - Result backends configuration
   - Progress tracking via Celery signals
   - Task retry mechanisms with proper error handling

4. **Semantic Evaluation Framework**
   - Multi-model evaluator system
   - Dimension-based scoring
   - Evaluation criteria templates
   - Scoring normalization

5. **Testing Infrastructure**
   - End-to-end tests for workflow benchmarking
   - Integration tests for compatibility with existing benchmark system
   - WebSocket tests for progress reporting
   - Celery task tests for task creation and result handling
   - Tool mocking tests for complex conditional responses

### In-Progress Features

Based on the TASK.md file, several features are still in progress:

1. **Token Tracking System**
   - Issues with TokenTracker tests
   - Need for proper token counting for all LLM models
   - Validation for token usage data

2. **Celery Task Management**
   - Issues with result backend configuration
   - Need for proper task retry mechanisms
   - Task cancellation handling

3. **Tool Mocking System**
   - Issues with conditional response handling
   - Need for proper assertion checking
   - Mock usage tracking

## Usage Patterns

The workflow benchmarking system can be used in several ways:

1. **Command Line**
   - Use `python manage.py run_workflow_benchmarks` to run workflow benchmarks
   - Specify scenario ID or workflow type
   - Configure parameters like runs, warmup runs, and semantic evaluation
   - Example: `python manage.py run_workflow_benchmarks --scenario-id 12345678-1234-5678-1234-567812345678 --params '{"semantic_evaluation": true, "runs": 5}'`

2. **Celery Tasks**
   - Use the `run_workflow_benchmark` task for running a single workflow benchmark
   - Use the `run_all_workflow_benchmarks` task for running all active workflow benchmarks
   - Configure parameters like runs, warmup runs, and semantic evaluation
   - Example: `run_workflow_benchmark.apply_async(args=[str(scenario_id)], kwargs={'params': params})`

3. **Direct API**
   - Use the `AsyncWorkflowManager.execute_benchmark` method for programmatic access
   - Configure parameters like runs, warmup runs, and semantic evaluation
   - Example: `await workflow_manager.execute_benchmark(scenario_id=scenario_id, params=params)`

## Benchmark Scenario Creation

Workflow benchmark scenarios are defined as JSON files and loaded into the database. The process involves:

1. **Define Schemas**
   - Ensure core schemas exist in `backend/schemas/`
   - Run `python manage.py seed_benchmark_schemas` if needed
   - Create reusable evaluation templates in `backend/testing/benchmark_data/templates/evaluation_criteria/`

2. **Setup Directory Structure**
   - Run `python manage.py setup_benchmark_structure` to create the organized directory structure

3. **Define Scenarios**
   - Create JSON files in the appropriate directories:
     - Workflow-specific scenarios: `backend/testing/benchmark_data/workflows/<workflow_type>/`
     - Evaluation templates: `backend/testing/benchmark_data/templates/evaluation_criteria/`

4. **Validate Scenarios**
   - Run `python manage.py validate_benchmarks_v2` to check scenarios against schemas

5. **Load Scenarios**
   - Use `python manage.py create_benchmark_scenarios` to load scenarios into the database

## Best Practices

Based on our investigation, the following best practices are recommended for using the workflow benchmarking system:

1. **Async/Sync Patterns**
   - Use `@database_sync_to_async` with `thread_sensitive=True` for all database operations in async code
   - Keep sync blocks minimal and avoid mixing sync/async operations in the same function
   - Use proper cleanup in async contexts to prevent resource leaks

2. **Error Handling**
   - Implement multi-level error capture with proper debug messaging
   - Use `EventService.emit_debug_info` for detailed error reporting
   - Implement proper error handling in async contexts and background tasks

3. **Token Tracking**
   - Always implement token tracking in LLM calls for cost monitoring
   - Use the TokenTracker class for tracking token usage across workflow execution
   - Ensure the TokenTracker is properly initialized with a run_id parameter

4. **Semantic Evaluation**
   - Use semantic evaluation for qualitative metrics with multiple models
   - Ensure the agent's response is correctly extracted from `last_output_data`
   - Verify that `semantic_evaluation: true` parameter is passed and `metadata.expected_quality_criteria` exists

5. **Tool Mocking**
   - Use the MockToolRegistry for mocking tool calls in benchmarks
   - Configure mock tool responses in the scenario metadata
   - Implement proper assertion checking for tool calls

## Current Issues and Challenges

Based on our investigation, there are several issues and challenges with the current workflow benchmarking system:

1. **TokenTracker Issues**
   - The TokenTracker class requires a run_id parameter in its constructor, which may cause issues in tests
   - There may be issues with token counting for different LLM models
   - Validation for token usage data may be incomplete

2. **Celery Task Issues**
   - There may be issues with result backend configuration
   - Task retry mechanisms may not be properly implemented
   - Task cancellation handling may be incomplete

3. **Tool Mocking Issues**
   - Conditional response handling may have issues
   - Assertion checking for tool calls may not be properly implemented
   - Mock usage tracking may be incomplete

4. **Database Connection Issues**
   - There may be issues with database connections in async code
   - Connection monitoring and cleanup may not be properly implemented
   - Transaction management in async code may have issues

## Next Steps

Based on our investigation, the following next steps are recommended for improving the workflow benchmarking system:

1. **Fix TokenTracker Issues**
   - Update TokenTracker to properly handle run_id parameter
   - Implement proper token counting for all LLM models
   - Add validation for token usage data

2. **Fix Celery Task Issues**
   - Update result backend configuration
   - Implement proper task retry mechanisms
   - Fix task cancellation handling

3. **Enhance Tool Mocking**
   - Fix conditional response handling
   - Implement proper assertion checking
   - Add mock usage tracking

4. **Improve Database Connection Stability**
   - Implement proper connection monitoring and cleanup
   - Fix transaction management in async code
   - Add retry logic for database operations

5. **Complete Documentation**
   - Create comprehensive user guide for the workflow benchmarking system
   - Document best practices for creating and running workflow benchmarks
   - Provide examples of common workflow benchmark scenarios

## Conclusion

The workflow benchmarking system is a comprehensive solution for evaluating LangGraph workflows, with most core components implemented. However, there are still several issues and in-progress features that need to be addressed to make the system fully usable. The next steps should focus on fixing core issues, completing in-progress features, improving documentation, and enhancing testing.
