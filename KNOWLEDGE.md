# Goali Benchmarking System Knowledge Base

## Core Concept Definitions (Clarified 2025-01-27)

### 🎯 **Scenario** → **"Generic Situation"**
- **Definition**: A reusable test case template that defines the testing context without being tied to specific user or environmental conditions
- **Examples**: "Life Wheel Creation", "Goal Setting Session", "Progress Review Meeting"
- **Key Principle**: Context-agnostic, focused on the core interaction pattern
- **Implementation**: BenchmarkScenario model with agent_role, workflow_type, and input_data

### 🔄 **Variable Context** → **"Comprehensive Context Variables"**
- **Definition**: Everything about both user profile AND environment that could influence evaluation criteria
- **Components**:
  - **User Profile Variables**: Trust level (0-100), personality traits, preferences, demographics
  - **Environmental Variables**: Stress level (0-100), time pressure (0-100), mood state (valence/arousal -1.0 to 1.0)
  - **Interaction Variables**: Session history, current goals, relationship phase
- **Key Principle**: All factors that should influence how we evaluate the AI's response
- **Implementation**: EvaluationCriteriaTemplate with contextual_criteria and variable_ranges

### 🔗 **Evaluation Criteria** → **"Context-Linked Assessment Framework"**
- **Definition**: Evaluation criteria that are ALWAYS linked with a scenario + ranges of context variables
- **Structure**: `Generic Situation + Context Variable Ranges → Adapted Evaluation Criteria`
- **Examples**:
  - "Life Wheel Creation" + "Low Trust (0-39)" → Simple, clear, reassuring criteria
  - "Goal Setting" + "High Stress (70-100)" → Concise, essential-only criteria
- **Implementation**: Template system with get_criteria_for_context() method

## System Architecture Overview

### **Backend Foundation** ✅
- **Django Models**: BenchmarkScenario, EvaluationCriteriaTemplate, BenchmarkRun
- **Services**: AgentBenchmarker, WorkflowBenchmarker, SemanticEvaluator
- **Task System**: Celery integration for long-running evaluations
- **API**: RESTful endpoints for template management and benchmark execution

### **Frontend Architecture** ✅
- **Modular JavaScript**: 5 specialized modules for different functionality
- **Interactive UI**: Context variable builders, template editors, real-time preview
- **Visual Systems**: Color-coded ranges, mood quadrants, stress indicators
- **Admin Interface**: Comprehensive management with filtering and batch operations

### **Testing Framework** ✅
- **150+ Tests**: Comprehensive coverage of all system components
- **Integration Tests**: End-to-end contextual evaluation testing
- **Performance Tests**: Large dataset validation and optimization
- **CI/CD Integration**: Automated testing and deployment

## Key Technical Insights

### **Context Variable Complexity**
- **Trust Levels**: Foundation (0-39), Expansion (40-69), Integration (70-100)
- **Mood Variables**: Valence/Arousal mapping to quadrants with visual representation
- **Environment**: Stress and time pressure with color-coded indicators
- **Range Parsing**: Supports "0-39", "40-69", "-1.0-0.0" format strings

### **Token Leak Prevention** 🚨
- **Issue**: Multi-range evaluation could generate 100+ combinations causing cost overruns
- **Solution**: Celery task architecture with proper combination selection
- **Status**: Fixed with comprehensive testing and stop functionality

### **Performance Considerations**
- **Database Views**: 4 optimized views for Grafana analytics
- **Caching Strategy**: Template adaptation results cached for repeated contexts
- **Async Processing**: Celery tasks for long-running benchmark evaluations

## User Experience Principles

### **Conceptual Clarity**
- Use "Generic Situations" instead of "Scenarios" for better understanding
- Emphasize context variables as primary interface, profiles as shortcuts
- Always show the linkage between situation, context, and criteria

### **Guided Workflows**
- Step-by-step process: Select Situation → Define Context → Generate Criteria
- Visual feedback at each step with validation and suggestions
- Preset loading for common configurations with customization options

### **Visual Design**
- Color-coded context variables for immediate understanding
- Interactive elements with hover effects and real-time feedback
- Progress indicators and completion status throughout the interface

## Implementation Priorities

### **Phase 1: Terminology & Clarity** (Weeks 1-2) ✅ **COMPLETED**
- [x] Replace "Scenarios" with "Generic Situations" throughout UI
- [x] Restructure "User Profiles" as "Comprehensive Context Variables" with clear sections
- [x] Add interactive concept guide and contextual tooltips
- [x] Update all documentation with new terminology
- [x] Implement conceptual help modal with examples and relationships

### **Phase 2: Context Architecture** (Weeks 3-4)
- Build unified context variable interface with visual builders
- Implement guided workflow for situation-context-criteria linkage
- Create relationship visualization and dependency mapping

### **Phase 3: Enhanced UX** (Weeks 5-6)
- Add smart template suggestions and real-time validation
- Implement batch operations and advanced filtering
- Create analytics dashboard for context performance insights

### **Phase 4: Advanced Features** (Weeks 7-8)
- AI-assisted context generation and optimization
- 3D visualization of context space relationships
- Comprehensive reporting and ROI analysis

## Success Metrics

### **Immediate (Phase 1)** ✅ **ACHIEVED**
- [x] 100% terminology consistency across interface
- [x] Interactive conceptual help system implemented
- [x] Documentation fully updated with new terminology
- [ ] Reduced user confusion about core concepts (pending user testing)
- [ ] Improved onboarding experience (pending deployment)

### **Short-term (Phase 2)**
- 50% reduction in context variable setup time
- 90% successful completion of guided workflows
- Increased usage of contextual evaluation features

### **Long-term (Phase 3-4)**
- 40% reduction in template creation time
- 80% adoption rate of AI-suggested contexts
- Measurable improvement in testing coverage and quality

## Risk Mitigation

### **High Risk: User Confusion**
- **Mitigation**: Comprehensive terminology standardization and interactive help
- **Timeline**: Phase 1 implementation with user testing

### **Medium Risk: Context Complexity**
- **Mitigation**: Visual builders, preset loading, and guided workflows
- **Timeline**: Phase 2 implementation with iterative feedback

### **Low Risk: Performance**
- **Mitigation**: Existing optimization strategies and monitoring
- **Status**: Well-handled by current architecture

## Next Steps

1. **Stakeholder Review**: Approve terminology changes and implementation phases
2. **Resource Allocation**: Assign frontend, backend, UX, and QA resources
3. **User Testing**: Establish feedback loops for iterative development
4. **Documentation**: Update all guides and training materials in parallel

## Recent Implementation Progress (2025-01-27)

### **Completed Work** ✅
1. **Frontend Terminology Standardization**
   - Updated all UI labels from "Scenarios" → "Generic Situations"
   - Renamed "User Profiles" → "Comprehensive Context Variables"
   - Standardized "Evaluation Templates" → "Context-Linked Assessment Framework"
   - Added explanatory text and contextual information throughout interface

2. **Interactive Conceptual Help System**
   - Implemented modal with core concept explanations and examples
   - Added visual relationship diagrams showing data flow
   - Created comparison tables highlighting concept distinctions
   - Integrated help button in main interface with proper styling

3. **Documentation Updates**
   - Updated 7 key documentation files with new terminology
   - Ensured consistency across all technical documentation
   - Revised conceptual explanations and examples
   - Updated admin frontend analysis report with implementation status

4. **Technical Implementation**
   - Added CSS styling for concept help modal
   - Implemented JavaScript event handlers for modal functionality
   - Updated HTML templates with new terminology and help system
   - Maintained backward compatibility with existing functionality

### **Next Phase Ready** 🚀
- Phase 1 (Terminology & Clarity) successfully completed
- Foundation established for Phase 2 (Context Variable Architecture)
- User testing and feedback collection can begin
- Ready to proceed with advanced context variable builder implementation

## Graphiti Tool Organization (2025-01-30)

### **Clean Implementation** ✅ **COMPLETED**
- **Organization**: All Graphiti-related files moved to dedicated `tools/graphiti/` directory
- **Structure**: Analysis modules in `tools/graphiti/analysis/` with virtual environment
- **Documentation**: Comprehensive setup and usage guides created
- **User Preference**: Follows preference for tool-specific directories vs. root-level clutter

### **Enhanced Setup Process**
- **Automated Installation**: `install_dependencies.py` for one-command setup
- **Comprehensive Verification**: `setup_verification.py` validates entire setup
- **Docker Integration**: Clear Neo4j Docker setup instructions with troubleshooting
- **Configuration**: Centralized `.env` file in `tools/graphiti/analysis/`

### **Documentation Structure**
- **Main Setup**: `GRAPHITI_SETUP_README.md` - comprehensive setup guide
- **Usage Guide**: `GRAPHITI_USAGE_GUIDE.md` - detailed usage examples and patterns
- **Tool Docs**: `tools/graphiti/README.md` - tool-specific documentation
- **Cleanup Summary**: `GRAPHITI_CLEANUP_SUMMARY.md` - migration and organization details

### **File Organization**
```
tools/graphiti/
├── analysis/                    # Analysis environment
│   ├── venv/                   # Python virtual environment
│   ├── .env                    # Configuration file
│   ├── code_scanner.py         # Code structure extraction
│   ├── graphiti_analyzer.py    # Graphiti integration
│   └── code_entities.py        # Custom entity definitions
├── analyze_codebase.py         # Full codebase analysis
├── analyze_small_sample.py     # Sample analysis script
├── visualize_graph.py          # Interactive visualizations
├── install_dependencies.py     # Dependency installation
├── setup_verification.py       # Comprehensive setup verification
├── quick_test.py               # Quick setup verification
└── test_setup.py               # Complete setup testing
```

## Mock Server for Frontend Development (2025-01-27)

### **Complete Implementation** ✅ **COMPLETED**
- **Purpose**: Provide realistic backend simulation for frontend development using benchmarking system as source of truth
- **Location**: `tools/mock_server/` - follows user preference for tool-specific directories
- **Architecture**: Pydantic-based contracts with WebSocket communication compatibility

### **Pydantic Contract System**
- **Base Contracts** (`contracts/base.py`): Core workflow structures, user profiles, context packets
- **Discussion Workflow** (`contracts/discussion.py`): Chat-based interaction models with stages
- **Wheel Generation** (`contracts/wheel_generation.py`): Activity wheel creation with metadata
- **WebSocket Messages** (`contracts/websocket.py`): Complete message type definitions for frontend compatibility

### **Workflow Implementations**
- **Discussion Workflow** (`workflows/discussion.py`):
  - Emotional tone detection and personalized responses
  - Trust-level adaptation (Foundation/Expansion/Integration phases)
  - Contextual insights and recommendations generation
- **Wheel Generation** (`workflows/wheel_generation.py`):
  - Multi-stage processing simulation (8 stages: orchestrator → wheel construction)
  - Activity domain selection based on user input
  - Personalized activity templates with challenge level adjustment
  - Value proposition generation for each activity

### **Server Features**
- **WebSocket Compatibility**: Matches existing frontend WebSocket manager (`ws://localhost:8765`)
- **Realistic Timing**: Configurable processing delays to simulate real backend behavior
- **Message Types**: Complete support for chat_message, spin_result, processing_status, wheel_data, etc.
- **Error Handling**: Comprehensive error responses and connection management
- **Testing**: Automated test client with validation of all workflow types

### **Usage & Integration**
```bash
# Quick start
cd tools/mock_server
python3 run.py --fast

# Frontend integration
# Update WebSocket URL to: ws://localhost:8765
# All existing message formats supported
```

### **Docker Deployment Ready** 🐳
- **Production Container**: Dockerfile with security best practices (non-root user, health checks)
- **Docker Compose**: Orchestrated deployment with monitoring UI and persistent logging
- **Environment Configuration**: Full environment variable support for all settings
- **Health Monitoring**: Built-in health checks and web-based monitoring interface
- **Documentation**: Complete deployment guide with troubleshooting and production setup

### **Technical Benefits**
- **Source of Truth**: Uses benchmarking system patterns for contract definitions
- **Type Safety**: Full Pydantic validation for all input/output
- **Development Speed**: Frontend can develop without backend dependencies
- **Realistic Behavior**: Trust-level personalization and multi-stage processing
- **Testing**: Comprehensive validation ensures frontend compatibility
- **Production Ready**: Docker container with complete deployment documentation

---

*Last Updated: 2025-01-27*
*Status: Phase 1 complete, Graphiti tools organized, Mock server with Docker deployment ready, Phase 2 ready to begin*
