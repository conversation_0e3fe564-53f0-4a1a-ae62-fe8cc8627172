# Setting Up AI Coding Assistant for Game of Life

This guide walks you through the process of configuring an AI coding assistant (like <PERSON>line, GitHub Copilot, or VS Code's AI features) with custom instructions tailored to our Game of Life project.

## Why Custom Instructions Matter

Custom instructions help the AI understand:
- Our specific project architecture and patterns
- The technologies and frameworks we use
- Our coding conventions and best practices
- Project-specific features like WebSocket communication

This results in more accurate, relevant code suggestions that integrate well with our existing codebase.

## Step 1: Choose Your AI Coding Assistant

Several options are available:

1. **Cline**: A VS Code extension that integrates with various AI models
2. **GitHub Copilot**: Microsoft's AI pair programmer
3. **VS Code's built-in AI features**: Depending on your VS Code version

This guide focuses on configuring Cline, but similar principles apply to other tools.

## Step 2: Install the AI Assistant Extension

For Cline:

1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X or Cmd+Shift+X on Mac)
3. Search for "Cline"
4. Click "Install"

## Step 3: Configure Global Rules

1. Open Cline's configuration panel (usually through the Command Palette with Ctrl+Shift+P, then search for "Cline: Configure")

2. Find the "Global Rules" or "Project Rules" section

3. Copy the contents from our `AI_CODING_INSTRUCTIONS.md` file

4. Paste the instructions into the configuration field

5. Save the configuration

## Step 4: Test the Configuration

To test if the AI assistant has properly absorbed our project-specific instructions:

1. Create a new file (e.g., `test_websocket.tsx`)

2. Start typing a related prompt, for example:
   ```
   // Create a component that connects to the WebSocket and displays chat messages
   ```

3. Invoke the AI assistant (usually with Alt+/ or Ctrl+Enter, depending on the tool)

4. Verify that the generated code:
   - Follows our WebSocket API contract
   - Uses the correct message formats
   - Implements proper error handling
   - Follows our TypeScript and React patterns

## Step 5: Use MCP for Enhanced Capabilities

If the AI coding assistant supports the Model Context Protocol (MCP):

1. Install an MCP server compatible with your assistant
   ```bash
   npm install -g @mcp/server
   ```

2. Configure your AI assistant to use the MCP server:
   - Find the MCP configuration in your assistant's settings
   - Point it to your running MCP server (typically `http://localhost:8080`)

3. This allows the AI to:
   - Read and edit multiple files
   - Access the file system
   - Perform Git operations
   - Search documentation

## Troubleshooting

If the AI generates code that doesn't align with our project:

1. **Refresh the context**: Start a new conversation
2. **Be more specific**: Provide explicit requirements and reference existing patterns
3. **Update the instructions**: If you notice consistent issues, update our global rules

## Feedback and Improvements

As you work with the AI assistant, note any areas where the instructions could be improved. Share these insights with the team so we can refine our AI configuration over time.

---

By following this guide, you'll have an AI coding assistant that's specifically tuned to our Game of Life project, helping you write code that integrates seamlessly with our architecture and follows our established patterns.
