Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF831350000 ntdll.dll
7FF82F8F0000 KERNEL32.DLL
7FF82EAE0000 KERNELBASE.dll
7FF82F580000 USER32.dll
7FF82EA30000 win32u.dll
7FF82FF20000 GDI32.dll
7FF82E670000 gdi32full.dll
7FF82EF40000 msvcp_win.dll
7FF82E7A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF82F9C0000 advapi32.dll
7FF82F350000 msvcrt.dll
7FF830D90000 sechost.dll
7FF82E440000 bcrypt.dll
7FF830120000 RPCRT4.dll
7FF82CC90000 CRYPTBASE.DLL
7FF82EEC0000 bcryptPrimitives.dll
7FF82F120000 IMM32.DLL
