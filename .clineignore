# Dependencies
**/node_modules/
**/__pycache__/
*.pyc
*.pyo

# Build outputs & Artifacts
backend/_build/
backend/coverage_html/
backend/staticfiles/
backend/docs/build/
frontend/public/ # Often contains build artifacts
frontend/dist/ # Common build output directory

# Testing & Benchmarking
backend/test-results/
backend/coverage_metadata.json
benchmark_results/
*.coverage


# Databases
*.sqlite3

# Generated files
backend/models.png

# Git directory (usually ignored by default, but explicit doesn't hurt)
.git/

# OS specific files
.DS_Store
Thumbs.db
