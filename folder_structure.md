# Folder Structure for `.`

- **repo/**
    - .clineignore
    - .codecov.yml
    - .dockerignore
    - .flake8
    - .gitignore
    - ADK-documentation.md
    - AI_CODING_INSTRUCTIONS.md
    - AI_SETUP_GUIDE.md
    - CONTRIBUTING.md
    - DEBUG.md
    - K<PERSON>OWLEDGE_CICD_INFRA.md
    - LICENSE
    - PLANNING_old.md
    - README.md
    - TO_INVESTIGATE_new.md
    - codemcp.toml
    - folder_structure.md
    - pyproject.toml
    - welcome_poem.txt
- **.git/**
- **out/**
    - **coach/**
        - **docs/**
            - **model/**
                - **global/**
                - **user/**
                - **activity/**
            - **user_stories/**
                - **philipp_22.SD/**
    - **docs/**
        - **agents/**
            - **agent_scenarios/**
                - **generated_graphs/**
                    - **wheel_generation/**
                        - wheel_generation.png
                        - wheel_generation.svg
        - **diagrams/**
            - **dispatcher_workflow_v3/**
                - dispatcher_workflow_v3.svg
            - **dispatcher_workflow_simpleBW/**
                - dispatcher_workflow_simpleBW.svg
        - **messy/**
            - **diagram_dispatcher_workflow_v3/**
                - diagram_dispatcher_workflow_v3.svg
            - **roadmap_ux_marketing/**
                - roadmap_ux_marketing.svg
- **backend/**
    - .coveragerc
    - CONTEXT_FOR_LLM.md
    - DEVELOPER_GUIDE.md
    - Dockerfile
    - PLANNING.md
    - README_ADMIN.md
    - README_DEV.md
    - SCHEMA_VERSIONING_SUMMARY.md
    - TASK.md
    - TASK_ARCHIVE.md
    - TO_INVESTIGATE.md
    - conftest.py
    - coverage_metadata.json
    - debug_celery.sh
    - debug_diagnostic.py
    - debug_django.py
    - docker-compose.yml
    - entrypoint.sh
    - manage.py
    - models.png
    - pytest.ini
    - requirements.in
    - requirements.txt
    - run_agent_tests.py
    - set_dev_env.ps1
    - ultimate_test_setup.py
    - **config/**
        - __init__.py
        - admin.py
        - asgi.py
        - celery.py
        - db.sqlite3
        - middleware.py
        - middleware_static.py
        - urls.py
        - wsgi.py
        - **settings/**
            - __init__.py
            - base.py
            - dev.py
            - prod.py
            - test.py
            - **__pycache__/**
        - **__pycache__/**
    - **apps/**
        - .gitkeep
        - __init__.py
        - **activity/**
            - __init__.py
            - admin.py
            - apps.py
            - models.py
            - signals.py
            - tests.py
            - views.py
            - **services/**
                - __init__.py
                - challengingness.py
                - **__pycache__/**
            - **__pycache__/**
            - **migrations/**
        - **main/**
            - __init__.py
            - admin.py
            - apps.py
            - celery_results.py
            - consumers.py
            - models.py
            - resources.py
            - routing.py
            - tasks.py
            - tests.py
            - urls.py
            - views.py
            - **management/**
                - __init__.py
                - **commands/**
                    - __init__.py
                    - cmd_register_tools.py
                    - cmd_tool_connect.py
                    - collect_static.py
                    - create_benchmark_scenarios.py
                    - create_benchmark_scenarios_v2.py
                    - create_sample_scenarios.py
                    - create_workflow_benchmark_scenarios.py
                    - generate_model_docs.py
                    - migrate_benchmark_scenarios.py
                    - migrate_to_phase_aware_criteria.py
                    - run_benchmarks.py
                    - run_seeders.py
                    - run_tests.py
                    - run_workflow_benchmarks.py
                    - seed_benchmark_scenarios.py
                    - seed_benchmark_schemas.py
                    - seed_db_10_hexacos.py
                    - seed_db_20_limitations.py
                    - seed_db_30_domains.py
                    - seed_db_40_envs.py
                    - seed_db_50_skill_system.py
                    - seed_db_60_beliefs.py
                    - seed_db_70_activities.py
                    - seed_db_80_agents.py
                    - seed_db_phiphi.py
                    - seed_llm_configs.py
                    - setup_benchmark_structure.py
                    - validate_benchmarks.py
                    - validate_benchmarks_v2.py
                    - **seeding_prompts/**
                        - generic activities.md
                    - **__pycache__/**
                - **__pycache__/**
            - **tasks/**
                - __init__.py
                - agent_tasks.py
                - benchmark_tasks.py
                - onboarding_tasks.py
                - wheel_generation_tasks.py
                - **__pycache__/**
            - **agents/**
                - __init__.py
                - base_agent.py
                - benchmarking.py
                - engagement_agent.py
                - error_handler.py
                - ethical_agent.py
                - exceptions.py
                - mentor_agent.py
                - orchestrator_agent.py
                - psy_agent.py
                - resource_agent.py
                - strategy_agent.py
                - wheel_activity_agent.py
                - **tools/**
                    - __init__.py
                    - dispatcher_tools.py
                    - evaluate_message_sentiment_tool.py
                    - example_tool.py
                    - extra_tools.py
                    - get_user_profile_tool.py
                    - mentor_tools.py
                    - tools.py
                    - tools_util.py
                    - update_current_mood_tool.py
                    - user_state_analysis_tool.py
                    - **__pycache__/**
                - **__pycache__/**
            - **llm/**
                - client.py
                - executor.py
                - response.py
                - service.py
                - tools_formatter.py
                - **__pycache__/**
            - **services/**
                - async_workflow_manager.py
                - benchmark_manager.py
                - benchmark_migration.py
                - benchmark_schema_integrator.py
                - benchmark_service.py
                - benchmark_validation.py
                - conversation_dispatcher.py
                - database_service.py
                - evaluation_criteria_migration.py
                - event_service.py
                - schema_migration_utility.py
                - schema_registry.py
                - schema_validator_service.py
                - schema_version_manager.py
                - semantic_evaluator.py
                - statistical_analysis.py
                - wheel_item_analyzer.py
                - wheel_workflow_benchmark_manager.py
                - workflow_result_handler.py
                - workflow_schemas.py
                - **__pycache__/**
            - **tests/**
                - __init__.py
                - assertions.py
                - conftest.py
                - dataflow_tests.md
                - extended_models.py
                - fixtures.py
                - models.py
                - plugin.py
                - test_celery_results.py
                - test_conversation_dispatcher.py
                - test_llm_config.py
                - test_logging.py
                - test_models.py
                - test_pydantic_factories.py
                - test_schema_migration_example.py
                - test_schema_version_manager.py
                - test_simple.py
                - test_utility_functions.py
                - test_websocket_consumer.py
                - test_workflow_result_handler.py
                - utils.py
                - workflow_utils.py
                - **test_agents/**
                    - __init__.py
                    - base.py
                    - conftest.py
                    - pytest.ini
                    - test_agent_config_loading.py
                    - test_basic_setup.py
                    - test_benchmarking.py
                    - test_engagement_agent.py
                    - test_engagement_agent_fixed.py
                    - test_engagement_pattern_agent.py
                    - test_env.py
                    - test_error_handler_agent.py
                    - test_ethical_agent.py
                    - test_llm_calling_tool.py
                    - test_mentor_agent.py
                    - test_orchestrator_agent.py
                    - test_psy_agent.py
                    - test_resource_agent.py
                    - test_stage_timer.py
                    - test_strategy_agent.py
                    - test_wheel_activity_agent.py
                    - **.pytest_cache/**
                        - .gitignore
                        - CACHEDIR.TAG
                        - README.md
                        - **v/**
                            - **cache/**
                                - lastfailed
                                - nodeids
                                - stepwise
                    - **__pycache__/**
                    - **test_tools/**
                        - test_get_user_profile_tool.py
                        - **__pycache__/**
                - **test_data/**
                - **test_flows/**
                    - test_discussion.py
                    - test_workflow_transitions.py
                    - test_workflow_transitions_integration.py
                    - **__pycache__/**
                - **test_tools/**
                    - README.md
                    - conftest.py
                    - test_discussion_tools.py
                    - test_dispatcher_tools.py
                    - test_evaluate_message_sentiment_tool.py
                    - test_tool_update_current_mood.py
                    - test_tool_update_current_mood_simple.py
                    - **__pycache__/**
                - **dispatcher/**
                    - test_dispatcher_actions.py
                    - test_dispatcher_classification.py
                    - test_dispatcher_context.py
                    - test_dispatcher_error_handling.py
                    - test_dispatcher_profile.py
                    - test_dispatcher_workflow_launch.py
                    - **__pycache__/**
                - **test_integration/**
                    - __init__.py
                    - test_discussion_integration.py
                    - test_workflow_benchmarking.py
                    - test_workflow_benchmarking_enhanced.py
                    - test_workflow_benchmarking_enhanced_fixed.py
                    - test_workflow_benchmarking_fixed.py
                    - **__pycache__/**
                - **__pycache__/**
                - **test_services/**
                    - test_benchmark_manager.py
                    - test_benchmark_migration.py
                    - test_benchmark_scenario.py
                    - test_benchmark_scenario_validation.py
                    - test_cost_calculation.py
                    - test_enhanced_token_tracking.py
                    - test_evaluation_criteria.py
                    - test_mock_tool_responses.py
                    - test_mock_workflow.py
                    - test_phase_aware_criteria.py
                    - test_schema_validation_edge_cases.py
                    - test_schema_validator_service.py
                    - test_semantic_evaluation_error_handling.py
                    - test_semantic_evaluator.py
                    - test_situation.py
                    - test_statistical_analysis.py
                    - test_token_tracking.py
                    - test_tool_mocking_conditional_responses.py
                    - test_wheel_item_analyzer.py
                    - test_workflow_benchmark.py
                    - test_workflow_benchmark_manager.py
                    - test_workflow_benchmark_validation.py
                    - test_workflow_schema_validation.py
                    - test_workflow_semantic_evaluation.py
                    - test_workflow_utils.py
                    - **__pycache__/**
                - **test_management/**
                    - test_create_benchmark_scenarios.py
                    - test_run_benchmarks.py
                    - **__pycache__/**
                - **utils/**
                    - __init__.py
                    - benchmark_debug_helper.py
                    - phase_aware_test_utils.py
                    - test_utils.py
                    - utils.py
                    - **__pycache__/**
                - **factories/**
                    - __init__.py
                    - pydantic_factories.py
                - **schemas/**
                    - test_schema_registry_updates.py
                - **test_commands/**
                    - test_create_benchmark_scenarios_v2.py
                    - test_validate_benchmarks_v2.py
                - **test_schemas/**
                    - test_conversion.py
                - **test_utils/**
                    - test_db_connection_monitor.py
                    - test_db_connection_utility.py
                    - test_task_monitoring.py
            - **testing/**
                - __init__.py
                - agent_test_helpers.py
                - agent_test_runner.py
                - assertions.py
                - definition_extractors.py
                - interfaces.py
                - mock_agent_test_runner.py
                - mock_agents.py
                - mock_database_service.py
                - mock_llm_service.py
                - mock_tool_registry.py
                - mock_utils.py
                - mock_workflow.py
                - mocks.py
                - test_orchestrator_agent.py
                - test_resource_agent.py
                - workflow_test_runner.py
                - **__pycache__/**
            - **graphs/**
                - discussion_graph.py
                - onboarding_graph.py
                - state_models.py
                - wheel_generation_graph.py
                - **__pycache__/**
            - **__pycache__/**
            - **utils/**
                - benchmark_reporting.py
                - db_connection_monitor.py
                - db_connection_utility.py
                - **__pycache__/**
            - **migrations/**
            - **schemas/**
                - __init__.py
                - base.py
                - conversion.py
                - migration.py
                - registry_integration.py
                - registry_updates.py
                - version.py
                - **agent/**
                    - __init__.py
                    - messages.py
                    - outputs.py
                    - **__pycache__/**
                - **__pycache__/**
                - **benchmark/**
                    - __init__.py
                    - runs.py
                    - scenarios.py
                - **migrations/**
                    - __init__.py
                    - situation_v1_to_v2.py
        - **user/**
            - __init__.py
            - admin.py
            - apps.py
            - models.py
            - signals.py
            - tests.py
            - urls.py
            - views.py
            - **services/**
                - __init__.py
                - skill_service.py
                - **__pycache__/**
            - **__pycache__/**
            - **migrations/**
        - **utils/**
            - __init__.py
            - activity_compatibility.py
            - activity_skill_integration.py
            - migration_config.py
            - reset_db.py
            - **__pycache__/**
            - **migrations_activity/**
                - __init__.py
            - **migrations_user/**
                - __init__.py
        - **common/**
            - __init__.py
            - fields.py
            - models.py
            - **__pycache__/**
        - **__pycache__/**
        - **coverage_dashboard/**
            - urls.py
            - views.py
            - **__pycache__/**
            - **templates/**
                - **coverage_dashboard/**
                    - dashboard.html
                    - no_data.html
        - **admin_tools/**
            - __init__.py
            - admin.py
            - apps.py
            - consumers.py
            - models.py
            - routing.py
            - tests.py
            - urls.py
            - views.py
            - **templates/**
                - **admin_tools/**
                    - websocket_tester.html
            - **__pycache__/**
            - **tests/**
                - __init__.py
                - conftest.py
                - test_benchmark_runs_detail_api.py
                - test_benchmark_scenario_import_export_views.py
    - **test-results/**
        - error_report.txt
    - **backend/**
        - **schemas/**
            - **situation/**
                - **versions/**
    - **tests/**
        - factories.py
    - **_build/**
    - **_templates/**
    - **_static/**
    - **docs/**
        - **build/**
        - **backend/**
        - **testing/**
            - TESTING_GUIDE.md
    - **templates/**
        - **admin/**
            - base_site.html
            - index.html
            - **activity/**
                - **genericactivity/**
                    - change_form.html
                - **activitytailored/**
                    - change_form.html
            - **main/**
                - **benchmarkrun/**
                    - change_form.html
        - **admin_tools/**
            - benchmark_dashboard.html
            - benchmark_history.html
    - **static/**
        - **admin/**
            - **js/**
                - jsoneditor.js
            - **css/**
                - jsoneditor.css
    - **.pytest_cache/**
        - .gitignore
        - CACHEDIR.TAG
        - README.md
        - **v/**
            - **cache/**
                - lastfailed
                - nodeids
                - stepwise
    - **__pycache__/**
    - **scripts/**
        - export_codebase.py
        - github_setup.sh
        - run_benchmarks.py
    - **coverage_html/**
        - .gitignore
        - class_index.html
        - coverage_html_cb_497bf287.js
        - favicon_32_cb_58284776.png
        - function_index.html
        - index.html
        - keybd_closed_cb_ce680311.png
        - status.json
        - style_cb_718ce007.css
        - z_007b7fdc33db20e8___init___py.html
        - z_007b7fdc33db20e8_agent_tasks_py.html
        - z_007b7fdc33db20e8_benchmark_tasks_py.html
        - z_007b7fdc33db20e8_onboarding_tasks_py.html
        - z_007b7fdc33db20e8_wheel_generation_tasks_py.html
        - z_0cf45020bb92c3b2___init___py.html
        - z_0cf45020bb92c3b2_challengingness_py.html
        - z_0fc6071870bccaa6_0001_initial_py.html
        - z_0fc6071870bccaa6___init___py.html
        - z_1206e32f80c2a022___init___py.html
        - z_1206e32f80c2a022_base_agent_py.html
        - z_1206e32f80c2a022_benchmarking_py.html
        - z_1206e32f80c2a022_engagement_agent_py.html
        - z_1206e32f80c2a022_error_handler_py.html
        - z_1206e32f80c2a022_ethical_agent_py.html
        - z_1206e32f80c2a022_mentor_agent_py.html
        - z_1206e32f80c2a022_orchestrator_agent_py.html
        - z_1206e32f80c2a022_psy_agent_py.html
        - z_1206e32f80c2a022_resource_agent_py.html
        - z_1206e32f80c2a022_strategy_agent_py.html
        - z_1206e32f80c2a022_wheel_activity_agent_py.html
        - z_170f963750b62d5c___init___py.html
        - z_170f963750b62d5c_test_discussion_integration_py.html
        - z_2c90f3fff3b4e0c9___init___py.html
        - z_2c90f3fff3b4e0c9_dispatcher_tools_py.html
        - z_2c90f3fff3b4e0c9_evaluate_message_sentiment_tool_py.html
        - z_2c90f3fff3b4e0c9_example_tool_py.html
        - z_2c90f3fff3b4e0c9_extra_tools_py.html
        - z_2c90f3fff3b4e0c9_get_user_profile_tool_py.html
        - z_2c90f3fff3b4e0c9_mentor_tools_py.html
        - z_2c90f3fff3b4e0c9_tools_py.html
        - z_2c90f3fff3b4e0c9_tools_util_py.html
        - z_2c90f3fff3b4e0c9_update_current_mood_tool_py.html
        - z_2c90f3fff3b4e0c9_user_state_analysis_tool_py.html
        - z_2c965f0e94ef83aa___init___py.html
        - z_2c965f0e94ef83aa_agent_test_runner_py.html
        - z_2c965f0e94ef83aa_assertions_py.html
        - z_2c965f0e94ef83aa_benchmarking_py.html
        - z_2c965f0e94ef83aa_definition_extractors_py.html
        - z_2c965f0e94ef83aa_framework_py.html
        - z_2c965f0e94ef83aa_interfaces_py.html
        - z_2c965f0e94ef83aa_mock_database_service_py.html
        - z_2c965f0e94ef83aa_mock_llm_service_py.html
        - z_2c965f0e94ef83aa_mock_tool_registry_py.html
        - z_2c965f0e94ef83aa_mock_utils_py.html
        - z_2c965f0e94ef83aa_mocks_py.html
        - z_2c965f0e94ef83aa_workflow_test_runner_py.html
        - z_2dd50e8a661d457c_benchmark_reporting_py.html
        - z_2e23235e0aed6c68___init___py.html
        - z_2e23235e0aed6c68_cmd_register_tools_py.html
        - z_2e23235e0aed6c68_cmd_tool_connect_py.html
        - z_2e23235e0aed6c68_collect_static_py.html
        - z_2e23235e0aed6c68_create_benchmark_scenarios_py.html
        - z_2e23235e0aed6c68_generate_model_docs_py.html
        - z_2e23235e0aed6c68_run_benchmarks_py.html
        - z_2e23235e0aed6c68_run_seeders_py.html
        - z_2e23235e0aed6c68_run_tests_py.html
        - z_2e23235e0aed6c68_seed_benchmark_schemas_py.html
        - z_2e23235e0aed6c68_seed_db_10_hexacos_py.html
        - z_2e23235e0aed6c68_seed_db_20_limitations_py.html
        - z_2e23235e0aed6c68_seed_db_30_domains_py.html
        - z_2e23235e0aed6c68_seed_db_40_envs_py.html
        - z_2e23235e0aed6c68_seed_db_50_skill_system_py.html
        - z_2e23235e0aed6c68_seed_db_60_beliefs_py.html
        - z_2e23235e0aed6c68_seed_db_70_activities_py.html
        - z_2e23235e0aed6c68_seed_db_80_agents_py.html
        - z_2e23235e0aed6c68_seed_db_phiphi_py.html
        - z_2e23235e0aed6c68_seed_llm_configs_py.html
        - z_2e23235e0aed6c68_validate_benchmarks_py.html
        - z_372ab193023461c9___init___py.html
        - z_372ab193023461c9_activity_compatibility_py.html
        - z_372ab193023461c9_activity_skill_integration_py.html
        - z_372ab193023461c9_migration_config_py.html
        - z_372ab193023461c9_reset_db_py.html
        - z_3aa692924f7ce655_test_dispatcher_actions_py.html
        - z_3aa692924f7ce655_test_dispatcher_classification_py.html
        - z_3aa692924f7ce655_test_dispatcher_context_py.html
        - z_3aa692924f7ce655_test_dispatcher_error_handling_py.html
        - z_3aa692924f7ce655_test_dispatcher_profile_py.html
        - z_3aa692924f7ce655_test_dispatcher_workflow_launch_py.html
        - z_41a4a7fc8ef6eadd___init___py.html
        - z_41a4a7fc8ef6eadd_admin_py.html
        - z_41a4a7fc8ef6eadd_apps_py.html
        - z_41a4a7fc8ef6eadd_celery_results_py.html
        - z_41a4a7fc8ef6eadd_consumers_py.html
        - z_41a4a7fc8ef6eadd_models_py.html
        - z_41a4a7fc8ef6eadd_routing_py.html
        - z_41a4a7fc8ef6eadd_tests_py.html
        - z_41a4a7fc8ef6eadd_urls_py.html
        - z_41a4a7fc8ef6eadd_views_py.html
        - z_4adcd2bafdcb235d___init___py.html
        - z_4adcd2bafdcb235d_skill_service_py.html
        - z_58e50c35f2edd4f0_discussion_graph_py.html
        - z_58e50c35f2edd4f0_onboarding_graph_py.html
        - z_58e50c35f2edd4f0_state_models_py.html
        - z_58e50c35f2edd4f0_wheel_generation_graph_py.html
        - z_62241e6653396f97___init___py.html
        - z_6bf723f89d1e0333_client_py.html
        - z_6bf723f89d1e0333_response_py.html
        - z_6bf723f89d1e0333_service_py.html
        - z_6c2f15f5c9cc56d9_benchmark_manager_py.html
        - z_6c2f15f5c9cc56d9_conversation_dispatcher_py.html
        - z_6c2f15f5c9cc56d9_database_service_py.html
        - z_6c2f15f5c9cc56d9_event_service_py.html
        - z_6c2f15f5c9cc56d9_schema_registry_py.html
        - z_6c2f15f5c9cc56d9_schema_validator_service_py.html
        - z_6c2f15f5c9cc56d9_workflow_result_handler_py.html
        - z_6dfa6db260f51ed6___init___py.html
        - z_6dfa6db260f51ed6_admin_py.html
        - z_6dfa6db260f51ed6_apps_py.html
        - z_6dfa6db260f51ed6_models_py.html
        - z_6dfa6db260f51ed6_signals_py.html
        - z_6dfa6db260f51ed6_tests_py.html
        - z_6dfa6db260f51ed6_views_py.html
        - z_78af73f4699d4ec5___init___py.html
        - z_78af73f4699d4ec5_fields_py.html
        - z_78af73f4699d4ec5_models_py.html
        - z_7c3314aaae14ec5e___init___py.html
        - z_85c5e678027c7413___init___py.html
        - z_86f42a2be76498a2_test_create_benchmark_scenarios_py.html
        - z_86f42a2be76498a2_test_run_benchmarks_py.html
        - z_87e61c99ba697d43___init___py.html
        - z_87e61c99ba697d43_admin_py.html
        - z_87e61c99ba697d43_apps_py.html
        - z_87e61c99ba697d43_models_py.html
        - z_87e61c99ba697d43_signals_py.html
        - z_87e61c99ba697d43_tests_py.html
        - z_87e61c99ba697d43_urls_py.html
        - z_87e61c99ba697d43_views_py.html
        - z_8a229bccb15c3ec1_test_discussion_py.html
        - z_8a229bccb15c3ec1_test_workflow_transitions_integration_py.html
        - z_8a229bccb15c3ec1_test_workflow_transitions_py.html
        - z_8f869532c6436057_0001_initial_py.html
        - z_8f869532c6436057_0002_initial_py.html
        - z_8f869532c6436057___init___py.html
        - z_9049f71b23343394___init___py.html
        - z_968fdc2146cb8dce___init___py.html
        - z_968fdc2146cb8dce_admin_py.html
        - z_968fdc2146cb8dce_apps_py.html
        - z_968fdc2146cb8dce_consumers_py.html
        - z_968fdc2146cb8dce_models_py.html
        - z_968fdc2146cb8dce_routing_py.html
        - z_968fdc2146cb8dce_tests_py.html
        - z_968fdc2146cb8dce_urls_py.html
        - z_968fdc2146cb8dce_views_py.html
        - z_9be9392ab9fe7a73_0001_initial_py.html
        - z_9be9392ab9fe7a73_0002_initial_py.html
        - z_9be9392ab9fe7a73_0003_alter_genericagent_role_py.html
        - z_9be9392ab9fe7a73_0003_benchmarkscenario_benchmarkrun_py.html
        - z_9be9392ab9fe7a73_0004_remove_benchmarkrun_semantic_evaluation_and_more_py.html
        - z_9be9392ab9fe7a73_0005_agenttool_definition_hash_and_more_py.html
        - z_9be9392ab9fe7a73_0006_benchmarktag_benchmarkscenario_tags_py.html
        - z_9be9392ab9fe7a73___init___py.html
        - z_b830ba75e317d239___init___py.html
        - z_b830ba75e317d239_assertions_py.html
        - z_b830ba75e317d239_conftest_py.html
        - z_b830ba75e317d239_factories_py.html
        - z_b830ba75e317d239_plugin_py.html
        - z_b830ba75e317d239_test_agent_framework_py.html
        - z_b830ba75e317d239_test_celery_results_py.html
        - z_b830ba75e317d239_test_conversation_dispatcher_py.html
        - z_b830ba75e317d239_test_models_py.html
        - z_b830ba75e317d239_test_websocket_consumer_py.html
        - z_b830ba75e317d239_test_workflow_result_handler_py.html
        - z_dec45144ee18435a___init___py.html
        - z_e9609776b223d505___init___py.html
        - z_e9609776b223d505_base_py.html
        - z_e9609776b223d505_conftest_py.html
        - z_e9609776b223d505_test_basic_setup_py.html
        - z_e9609776b223d505_test_benchmarking_py.html
        - z_e9609776b223d505_test_engagement_agent_py.html
        - z_e9609776b223d505_test_env_py.html
        - z_e9609776b223d505_test_error_handler_agent_py.html
        - z_e9609776b223d505_test_ethical_agent_py.html
        - z_e9609776b223d505_test_llm_calling_tool_py.html
        - z_e9609776b223d505_test_mentor_agent_py.html
        - z_e9609776b223d505_test_orchestrator_agent_py.html
        - z_e9609776b223d505_test_psy_agent_py.html
        - z_e9609776b223d505_test_resource_agent_py.html
        - z_e9609776b223d505_test_stage_timer_py.html
        - z_e9609776b223d505_test_strategy_agent_py.html
        - z_e9609776b223d505_test_wheel_activity_agent_py.html
        - z_ec8c2d5defd51597_conftest_py.html
        - z_ec8c2d5defd51597_test_discussion_tools_py.html
        - z_ec8c2d5defd51597_test_dispatcher_tools_py.html
        - z_ec8c2d5defd51597_test_evaluate_message_sentiment_tool_py.html
        - z_ec8c2d5defd51597_test_tool_update_current_mood_py.html
        - z_f72e312f8bb98afd_test_get_user_profile_tool_py.html
        - z_f9b6ac1745dae61d_test_benchmark_manager_py.html
    - **coverage_dashboard/**
        - __init__.py
        - admin.py
        - apps.py
        - models.py
        - tests.py
        - views.py
        - **migrations/**
            - __init__.py
        - **__pycache__/**
    - **staticfiles/**
    - **testing/**
        - coverage_plugin.py
        - error_reporter_plugin.py
        - **__pycache__/**
        - **benchmark_data/**
            - valid_scenarios.json
            - wheel_workflow_scenarios.json
            - **scenarios/**
                - mentor_discussion_25.json
                - mentor_discussion_65.json
                - mentor_discussion_85.json
                - mentor_postactfb_40.json
                - mentor_postactfb_65.json
                - mentor_wheelgen_35.json
                - mentor_wheelgen_65.json
                - mentor_wheelgen_85.json
            - **agents/**
                - **mentor/**
                    - **discussion/**
                        - test_mentor_discussion.json
                    - **wheel_generation/**
                        - test_mentor_wheel_generation.json
            - **examples/**
                - phase_aware_scenario_example.json
            - **templates/**
                - **evaluation_criteria/**
                    - mentor_discussion_criteria.json
                    - mentor_wheel_generation_criteria.json
                    - workflow_wheel_generation_criteria.json
            - **workflows/**
                - **wheel_generation/**
                    - test_workflow_wheel_generation.json
        - **seed_data/**
            - llm_configs.json
    - **benchmark_results/**
    - **evaluation_templates/**
        - mentor_response_quality.json
    - **schemas/**
        - evaluation_criteria.schema.json
        - evaluation_template.schema.json
        - situation.schema.json
        - tool_expectation.schema.json
        - user_profile.schema.json
        - workflow_benchmark.schema.json
        - **situation/**
            - **versions/**
                - v2.0.0.schema.json
- **scripts/**
    - generate_docs.py
    - generate_folder_content_list.py
    - markdown_processor.py
    - merge_global.py
    - run_docker_tests.sh
- **docs/**
    - DOCUMENTATION_MAP.md
    - documentation_guide.md
    - index.md
    - **architecture/**
        - **diagrams/**
        - **model/**
            - **data/**
        - **models/**
            - activity.md
            - activity.puml
            - belief-model.md
            - belief-model.puml
            - global.puml
            - slack_connections.puml
            - user.md
            - user.puml
            - **data/**
                - character_traits.puml
        - **workflows/**
            - data_flow.md
            - mentor_workflow_transition.md
    - **api/**
        - ApiContract.md
    - **frontend/**
    - **backend/**
        - AGENT_TESTING_HELPERS.md
        - BENCHMARKING_GUIDE.md
        - BENCHMARK_REORGANIZATION.md
        - BENCHMARK_SYSTEM.md
        - ENHANCED_CONTEXT_SCHEMA.md
        - EVALUATION_SYSTEM_ENHANCEMENT.md
        - PHASE_AWARE_CRITERIA.md
        - PYDANTIC_TESTING.md
        - TDD.md
        - TROUBLESHOOTING.md
        - WORKFLOW_ASYNC_PATTERNS.md
        - WORKFLOW_ERROR_HANDLING.md
        - WORKFLOW_SCHEMA_MANAGEMENT.md
        - WORKFLOW_SCHEMA_VALIDATION.md
        - WORKFLOW_TESTING_PATTERNS.md
        - WORKFLOW_TOKEN_TRACKING.md
        - WORKFLOW_TOOL_MOCKING.md
        - agent_benchmarking.md
        - agent_tools.md
        - benchmark_schema_inspection.md
        - wheel_generation.puml
        - **agents/**
            - AGENT_REFERENCE.md
            - README_AGENTS.md
            - agents_description.md
            - **benchmarks/**
            - **diagrams/**
                - **flow_sequence_diagrams/**
            - **flow_simulation/**
                - WG_scenario_02.md
                - WG_scenario_03_philipp.md
                - WG_scenario_schemas_01.md
                - onboarding.puml
                - wheel_generation_simulation_template.txt
            - **flows/**
                - discussion_flow.md
                - onboarding_flow.md
                - onboarding_flow.puml
                - onboarding_flow_graph.puml
                - post_activity_FLOW.md
                - post_spin_FLOW.md
                - pre_spin_FLOW.md
                - wheel_generation_FLOW.md
                - workflow_analysis.md
            - **generation_prompts/**
    - **governance/**
    - **development/**
        - pytest_collection_error_fix.md
        - **global/**
        - **dynamic/**
            - **AI_prompts/**
                - **story_and_model/**
                - **US sequence diagram/**
                - **model and its docs/**
                - **UX_and_agents/**
                - **agents and model/**
                - **model and globaldescription/**
                - **flow sequence diagram/**
    - **user/**
        - **users/**
            - **diagrams/**
            - **profiles/**
                - **archetype/**
                - **questionaire/**
            - **stories/**
    - **global/**
        - 1_introduction.md
        - 2_ethical_framework.md
        - 3_user_experience.md
        - 4_system_strategy.md
        - 5_technical_architecture.md
        - 6_implementation_roadmap.md
        - 7_success_framework.md
        - 8_resource_management.md
        - 9_global_glossary.md
        - **future/**
            - FUTURE_ROADMAP.md
    - **users/**
        - README_USERS.md
        - **diagrams/**
            - philipp_22.SD.puml
        - **profiles/**
            - **archetype/**
                - analytical_perfectionist.md
                - creative_explorer.md
                - emotional_empath.md
                - reflective_observer.md
                - template.md
                - value_proposition.md
        - **stories/**
            - philipp_22.md
            - simon_27.md
            - wilhelm_78.md
    - **messy/**
        - architecture_models_merge_all.py
        - diagram_dispatcher_workflow_simpleBW.puml
        - global_challengingness.md
        - governance_ideal_structure.md
        - roadmap_ux_marketing.puml
        - **agents_benchmarks/**
            - questionaire_mapping.md
            - user_profile.md
        - **development_docs/**
            - generating docs.md
        - **dynamic_docs/**
            - .gitkeep
        - **users_profiles_questionaire/**
            - mapping_userprofile.md
            - template.md
    - **prompts/**
        - backend_tools_gen.md
        - governance_first_agreement_prompt.md
        - governance_first_draft_agreement_governance_gen.md
        - **agents_generation_prompts/**
            - agent-story-puml-template.txt
            - agent-story-to-puml.py
            - agent_story_wheel_generation.txt
            - gen_prompt_new.py
            - input_output_schema_template.txt
            - prompt_template.txt
            - template_scenario_puml.txt
            - workflow_template.md
        - **dynamic_AI_prompts/**
            - add_example_to_user_model.txt
            - agent benchmarks
            - basic template.txt
            - **US sequence diagram/**
                - gen_prompt.py
                - template.txt
            - **UX_and_agents/**
                - gen_prompt.py
                - template.txt
            - **agents and model/**
                - gen_prompt.py
                - template.txt
            - **model and globaldescription/**
                - gen_prompt.py
                - template.txt
            - **model and its docs/**
                - gen_prompt.py
                - template.txt
            - **story_and_model/**
                - gen_prompt.py
                - template.txt
        - **tests/**
            - cline on agent tests.txt
            - cline on tool tests.txt
    - **administrative/**
        - companion_document.md
        - partnership_agreement.md
    - **ux&marketing/**
        - strategy_analysis_report.md
    - **ci-testing/**
        - pytest_config.md
    - **testing/**
        - AGENT_TESTING_GUIDE.md
        - BENCHMARK_REORGANIZATION_GUIDE.md
        - CI_CD_GUIDE.md
        - TESTING_GUIDE.md
        - TEST_STABILITY_IMPROVEMENTS.md
- **monitoring/**
    - **prometheus/**
        - prometheus.yml
- **.vscode/**
    - launch.json
    - settings.json
    - tasks.json
- **.pytest_cache/**
    - .gitignore
    - CACHEDIR.TAG
    - README.md
    - **v/**
        - **cache/**
            - lastfailed
            - nodeids
            - stepwise
- **frontend/**
    - .eslintrc.js
    - README.md
    - README_WEBSOCKET_IMPLEMENTATION.md
    - cypress.config.js
    - instructions.md
    - jest.config.js
    - package-lock.json
    - package.json
    - reinstall.sh
    - tsconfig.json
    - tsconfig.test.json
    - webpack.config.js
    - **docs/**
        - README.md
        - WebSocketArchitecture.md
        - WebSocketManager.md
        - WebSocketMocking.md
    - **src/**
        - App.tsx
        - index.tsx
        - **contexts/**
            - DebugContext.tsx
            - UserContext.tsx
            - WebSocketContext.tsx
        - **hooks/**
            - useActivity.ts
            - useChatMessages.ts
            - useWheel.ts
        - **services/**
            - WebSocketManager.ts
        - **types/**
            - api.ts
            - models.ts
        - **utils/**
            - Logger.ts
            - MockWebSocket.ts
            - MockWebSocketProvider.tsx
            - README_MOCK_USAGE.md
            - WebSocketProviderSelector.tsx
            - apiUtils.ts
            - dateUtils.ts
            - mockWebSocketProvider.ts
        - **components/**
            - **activities/**
                - ActivitiesPanel.tsx
                - ActivityItem.tsx
            - **chat/**
                - ChatContainer.tsx
                - ChatInterface.css
                - ChatInterface.tsx
                - ChatMessage.tsx
                - TypingIndicator.tsx
            - **layout/**
                - ConnectionStatus.tsx
                - GameContainer.tsx
                - UserHeader.tsx
            - **wheel/**
                - WheelContainer.tsx
                - WheelSegment.tsx
            - **debug/**
                - DebugConsole.css
                - DebugConsole.tsx
        - **styles/**
            - global.css
        - **config/**
            - constants.ts
            - environment.ts
            - settings.ts
        - **tests/**
            - App.test.tsx
    - **tests/**
        - MockConsumerTest.md
        - QuickStartGuide.md
        - README.md
        - WebSocketTESTREADme.md
        - quick_start.md
        - **end2end/**
            - CYPRESS_SETUP_GUIDE.md
            - README_WEBSOCKET_TESTING.md
            - sample_cypress.test
        - **integration/**
            - DJANGO_INTEGRATION.md
            - MockConsumerTest.test.tsx
            - WebSocketContext.test.tsx
            - WebSocketDjangoIntegration.test.ts
            - WebSocketIntegration.test.ts
            - test_Websocket.py
            - **__pycache__/**
        - **mocks/**
            - EnhancedWebSocketMock.ts
            - websocketMock.ts
        - **unit/**
            - **services/**
                - WebSocketManager.test.ts
    - **public/**
        - favicon.ico
        - index.html
    - **node_modules/**
- **.langgraph_api/**
- **goali-governance/**
    - README.md
    - custom_instructions.md
    - **constitution/**
        - benchmark-specifications.md
        - business-standards.md
        - values-constitution.md
    - **decisions/**
        - decision-log.md
        - transcription0404_analysis.md
    - **status/**
        - daily-planning.md
        - project-status.md
        - readiness_assessment_report_20250405.md
        - technical-project-status.md
        - timeline.md
    - **tasks/**
        - backlog.md
        - completed-tasks.md
        - current-tasks.md
        - guillaume-tasks.md
        - philipp-tasks.md
        - shared-tasks.md
        - test-task.md
    - **templates/**
        - assessment-template.md
        - daily-plan-template.md
        - decision-request.md
    - **domains/**
        - **creative/**
            - brand-strategy-framework.md
            - deep_research_1.md
            - deep_research_2.md
            - deep_research_3.md
            - enhanced-deep-research-prompt.md
            - networking-engagement-guide.md
            - startup-marketing-playbook.md
            - ux-best-practices.md
            - ux-deliverables-standards.md
            - ux-marketing-roadmap.md
            - weekly-progress-template.md
            - work-assessment-framework.md
            - **strategy/**
                - cognitive-dissonance-opportunity.md
    - **questionaires/**
        - answers_questionaire_philipp.md
        - questionaire
        - questionnaire.md
        - questionnaire_answered_guillaume.md
    - **meetings/**
        - meeting-2023-10-17.md
        - meeting-2025-04-09.md
    - **partnership/**
        - partnership-agreement.md
    - **user-stories/**
        - MVP_user_story.md
        - langgraph_draft.puml
- **benchmark_results/**
- **ux/**
    - **marketing/**
        - blue_ocean_market_analysis.md
        - brand_identity.md
        - deep_research_prompt_market_launch.md
        - go_to_market_strategy_(draft).md
- **.VSCodeCounter/**
- **.github/**
    - **workflows/**
        - benchmarks.yml
        - ci.yml
        - deploy.yml
        - docker-build.yml
        - restructure-plan.md
        - test-frontend.yml
- **.clinerules/**
- **frontend_enhanced/**
    - PLANNING.md
    - README.md
    - bun.lockb
    - components.json
    - eslint.config.js
    - index.html
    - package-lock.json
    - package.json
    - postcss.config.js
    - tailwind.config.ts
    - tsconfig.app.json
    - tsconfig.json
    - tsconfig.node.json
    - vite.config.ts
    - **public/**
        - apple-touch-icon.png
        - favicon.ico
        - maskable-icon.png
        - placeholder.svg
        - pwa-192x192.png
        - pwa-512x512.png
        - robots.txt
    - **src/**
        - App.css
        - App.tsx
        - index.css
        - main.tsx
        - vite-env.d.ts
        - **components/**
            - ActivityCard.tsx
            - ChatInterface.tsx
            - ConnectionStatus.tsx
            - InstallPrompt.tsx
            - MobileLayout.tsx
            - OnboardingProgress.tsx
            - PWAUpdater.tsx
            - SplitScreen.tsx
            - Wheel.tsx
            - **ui/**
                - accordion.tsx
                - alert-dialog.tsx
                - alert.tsx
                - aspect-ratio.tsx
                - avatar.tsx
                - badge.tsx
                - breadcrumb.tsx
                - button.tsx
                - calendar.tsx
                - card.tsx
                - carousel.tsx
                - chart.tsx
                - checkbox.tsx
                - collapsible.tsx
                - command.tsx
                - context-menu.tsx
                - dialog.tsx
                - drawer.tsx
                - dropdown-menu.tsx
                - form.tsx
                - hover-card.tsx
                - input-otp.tsx
                - input.tsx
                - label.tsx
                - menubar.tsx
                - navigation-menu.tsx
                - pagination.tsx
                - popover.tsx
                - progress.tsx
                - radio-group.tsx
                - resizable.tsx
                - scroll-area.tsx
                - select.tsx
                - separator.tsx
                - sheet.tsx
                - sidebar.tsx
                - skeleton.tsx
                - slider.tsx
                - sonner.tsx
                - switch.tsx
                - table.tsx
                - tabs.tsx
                - textarea.tsx
                - toast.tsx
                - toaster.tsx
                - toggle-group.tsx
                - toggle.tsx
                - tooltip.tsx
                - use-toast.ts
        - **contexts/**
            - MockWebSocketContext.tsx
            - UserContext.tsx
            - WebSocketContext.tsx
            - WebSocketProviderSelector.tsx
            - WheelContext.tsx
        - **hooks/**
            - use-mobile.tsx
            - use-toast.ts
            - useActivity.tsx
            - useChatMessages.tsx
            - useSimulatedIntelligence.tsx
            - useWheel.tsx
        - **lib/**
            - utils.ts
        - **pages/**
            - ActivityDetail.tsx
            - ActivityHistory.tsx
            - ActivityReview.tsx
            - Home.tsx
            - Index.tsx
            - NotFound.tsx
            - Offline.tsx
            - Onboarding.tsx
            - Profile.tsx
            - WheelSpin.tsx
        - **services/**
            - MockWebSocket.ts
            - WebSocketManager.ts
        - **types/**
            - api.ts
- **public/**
    - favicon.ico
    - placeholder.svg
    - robots.txt
- **apps/**
    - **main/**
        - **testing/**
            - agent_test_helpers.py
        - **tests/**
            - **schemas/**
                - __init__.py
                - test_schema_registry_updates.py
            - **test_agents/**
                - conftest.py
- **myenv/**