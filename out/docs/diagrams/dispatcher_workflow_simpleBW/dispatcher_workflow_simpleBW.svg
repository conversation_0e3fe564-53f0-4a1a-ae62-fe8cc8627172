<?xml version="1.0" encoding="us-ascii" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="1574px" preserveAspectRatio="none" style="width:2733px;height:1574px;background:#FFFFFF;" version="1.1" viewBox="0 0 2733 1574" width="2733px" zoomAndPan="magnify"><defs/><g><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacing" textLength="371" x="1179.875" y="34.9659">Backend Message Processing Flow (Dispatcher Focus)</text><ellipse cx="984.5" cy="60.0679" fill="#222222" rx="10" ry="10" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#FFFFFF" height="402.3411" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:1.5;" width="1450" x="11" y="80.0679"/><path d="M172,80.0679 L172,92.1358 L162,102.1358 L11,102.1358 " fill="none" style="stroke:#000000;stroke-width:1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="151" x="14" y="96.0339">UserSessionConsumer</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="182" x="893.5" y="119.1358"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="162" x="903.5" y="141.9639">Receive WebSocket Message</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="83" x="943" y="175.4799"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="63" x="953" y="198.308">Parse JSON</text><polygon fill="#FFFFFF" points="895,231.8241,1074,231.8241,1086,243.8241,1074,255.8241,895,255.8241,883,243.8241,895,231.8241" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="179" x="895" y="248.092">Validate Message Type &amp; Content?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="26" x="857" y="240.6011">Valid</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="36" x="1086" y="240.6011">Invalid</text><polygon fill="#FFFFFF" points="71.5,290.8241,149.5,290.8241,161.5,302.8241,149.5,314.8241,71.5,314.8241,59.5,302.8241,71.5,290.8241" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="72" x="114.5" y="326.583">chat_message</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="78" x="71.5" y="307.092">Message Type?</text><path d="M218,354.125 L218,363.978 L198,367.978 L218,371.978 L218,381.831 A0,0 0 0 0 218,381.831 L385,381.831 A0,0 0 0 0 385,381.831 L385,364.125 L375,354.125 L218,354.125 A0,0 0 0 0 218,354.125 " fill="#FFFFFF" style="stroke:#FFFFFF;stroke-width:1.5;"/><path d="M375,354.125 L375,364.125 L385,364.125 L375,354.125 " fill="#FFFFFF" style="stroke:#FFFFFF;stroke-width:1.5;"/><text fill="#FFFFFF" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="146" x="224" y="373.022">Passes message content</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="175" x="23" y="349.806"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="155" x="33" y="372.634">Call handle_chat_message()</text><polygon fill="#FFFFFF" points="454,290.8241,511,290.8241,523,302.8241,511,314.8241,454,314.8241,442,302.8241,454,290.8241" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="57" x="454" y="307.092">spin_result</text><path d="M580,334.8241 L580,353.5301 L560,357.5301 L580,361.5301 L580,380.2361 A0,0 0 0 0 580,380.2361 L761,380.2361 A0,0 0 0 0 761,380.2361 L761,344.8241 L751,334.8241 L580,334.8241 A0,0 0 0 0 580,334.8241 " fill="#FFFFFF" style="stroke:#FFFFFF;stroke-width:1.5;"/><path d="M751,334.8241 L751,344.8241 L761,344.8241 L751,334.8241 " fill="#FFFFFF" style="stroke:#FFFFFF;stroke-width:1.5;"/><text fill="#FFFFFF" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="146" x="586" y="353.7211">Passes message content</text><text fill="#FFFFFF" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="160" x="586" y="371.4271">(incl. spin_result metadata)</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="155" x="405" y="339.358"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="135" x="415" y="362.1861">Call handle_spin_result()</text><polygon fill="#FFFFFF" points="831.5,290.8241,962.5,290.8241,974.5,302.8241,962.5,314.8241,831.5,314.8241,819.5,302.8241,831.5,290.8241" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="131" x="831.5" y="307.092">workflow_status_request</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="79" x="974.5" y="299.6011">Unknown Type</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="232" x="781" y="334.8241"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="212" x="791" y="357.6522">Call handle_workflow_status_request()</text><ellipse cx="897" cy="417.1501" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="897" cy="417.1501" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="194" x="1033" y="337.8241"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="174" x="1043" y="360.6522">Send Error Response (to Client)</text><ellipse cx="1130" cy="420.1501" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="1130" cy="420.1501" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="194" x="1247" y="265.8241"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="174" x="1257" y="288.6522">Send Error Response (to Client)</text><ellipse cx="1344" cy="348.1682" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="1344" cy="348.1682" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#FFFFFF" height="1039.4143" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:1.5;" width="2596" x="126.75" y="492.409"/><path d="M419.75,492.409 L419.75,504.4769 L409.75,514.4769 L126.75,514.4769 " fill="none" style="stroke:#000000;stroke-width:1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="283" x="129.75" y="508.3749">ConversationDispatcher.process_message</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="113" x="928" y="531.4769"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="93" x="938" y="554.305">Dispatcher Entry</text><polygon fill="#FFFFFF" points="894,587.821,1075,587.821,1087,602.8029,1075,617.7848,894,617.7848,882,602.8029,894,587.821" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="100" x="894" y="599.5799">1. Check Metadata?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="181" x="894" y="614.5618">(requested_workflow / spin_result)</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="99" x="783" y="599.5799">Yes - Explicit Intent</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="15" x="1087" y="599.5799">No</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="250" x="624.8125" y="627.7848"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="230" x="634.8125" y="650.6129">4a. Determine Workflow (from Metadata)</text><polygon fill="#FFFFFF" points="1176.1875,627.7848,1262.1875,627.7848,1274.1875,642.7667,1262.1875,657.7486,1176.1875,657.7486,1164.1875,642.7667,1176.1875,627.7848" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="86" x="1176.1875" y="639.5437">2. Check Profile?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="84" x="1176.1875" y="654.5256">(&lt;50% complete)</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="86" x="1078.1875" y="639.5437">Yes - Incomplete</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="75" x="1274.1875" y="639.5437">No - Complete</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="260" x="904.8125" y="667.7486"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="240" x="914.8125" y="690.5766">4b. Determine Workflow (user_onboarding)</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="199" x="1304.0625" y="667.7486"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="179" x="1314.0625" y="690.5766">3. Workflow History Log Request</text><polygon fill="#FFFFFF" points="1338.0625,724.0927,1469.0625,724.0927,1481.0625,736.0927,1469.0625,748.0927,1338.0625,748.0927,1326.0625,736.0927,1338.0625,724.0927" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="131" x="1338.0625" y="740.3606">3a. Try LLM Classification</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="39" x="1287.0625" y="732.8697">Success</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="107" x="1481.0625" y="732.8697">Failure / Unavailable</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="218" x="1194.8125" y="758.0927"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="198" x="1204.8125" y="780.9208">4c. Determine Workflow (from LLM)</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="141" x="1432.8125" y="758.0927"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="121" x="1442.8125" y="780.9208">4d. Rule Classification</text><polygon fill="#FFFFFF" points="1403.5625,800.4368,1415.5625,812.4368,1403.5625,824.4368,1391.5625,812.4368,1403.5625,800.4368" style="stroke:#000000;stroke-width:0.5;"/><polygon fill="#FFFFFF" points="1219.1875,830.4368,1231.1875,842.4368,1219.1875,854.4368,1207.1875,842.4368,1219.1875,830.4368" style="stroke:#000000;stroke-width:0.5;"/><polygon fill="#FFFFFF" points="984.5,860.4368,996.5,872.4368,984.5,884.4368,972.5,872.4368,984.5,860.4368" style="stroke:#000000;stroke-width:0.5;"/><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="147" x="911" y="904.4368"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="127" x="921" y="927.2649">5. Build Context Packet</text><polygon fill="#FFFFFF" points="934,960.7809,1035,960.7809,1047,975.7628,1035,990.7447,934,990.7447,922,975.7628,934,960.7809" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="101" x="934" y="972.5398">6. Action Required?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="95" x="934" y="987.5217">(e.g., missing info)</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="17" x="905" y="972.5398">Yes</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="197" x="1047" y="972.5398">No - Proceed with classified workflow</text><path d="M413.75,1000.7447 L413.75,1019.4507 L393.75,1023.4507 L413.75,1027.4507 L413.75,1046.1567 A0,0 0 0 0 413.75,1046.1567 L541.75,1046.1567 A0,0 0 0 0 541.75,1046.1567 L541.75,1010.7447 L531.75,1000.7447 L413.75,1000.7447 A0,0 0 0 0 413.75,1000.7447 " fill="#FFFFFF" style="stroke:#FFFFFF;stroke-width:1.5;"/><path d="M531.75,1000.7447 L531.75,1010.7447 L541.75,1010.7447 L531.75,1000.7447 " fill="#FFFFFF" style="stroke:#FFFFFF;stroke-width:1.5;"/><text fill="#FFFFFF" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="107" x="419.75" y="1019.6417">Target: discussion</text><text fill="#FFFFFF" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="88" x="419.75" y="1037.3477">(to collect info)</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="224" x="169.75" y="1005.2786"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="204" x="179.75" y="1028.1067">7a. Determine Workflow (discussion)</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="247" x="158.25" y="1066.1567"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="227" x="168.25" y="1088.9848">7b. Launch Async Workflow (Celery Task)</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="270" x="146.75" y="1137.5008"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="250" x="156.75" y="1160.3289">Trigger execute_graph_workflow (discussion)</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="166" x="198.75" y="1208.8449"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="146" x="208.75" y="1231.673">Record Workflow Initiation</text><path d="M423.75,1280.1891 L423.75,1298.8951 L403.75,1302.8951 L423.75,1306.8951 L423.75,1325.6011 A0,0 0 0 0 423.75,1325.6011 L623.75,1325.6011 A0,0 0 0 0 623.75,1325.6011 L623.75,1290.1891 L613.75,1280.1891 L423.75,1280.1891 A0,0 0 0 0 423.75,1280.1891 " fill="#FFFFFF" style="stroke:#FFFFFF;stroke-width:1.5;"/><path d="M613.75,1280.1891 L613.75,1290.1891 L623.75,1290.1891 L613.75,1280.1891 " fill="#FFFFFF" style="stroke:#FFFFFF;stroke-width:1.5;"/><text fill="#FFFFFF" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="179" x="429.75" y="1299.0861">Discussion workflow launched</text><text fill="#FFFFFF" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="98" x="429.75" y="1316.7921">asynchronously.</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="244" x="159.75" y="1284.723"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="224" x="169.75" y="1307.5511">8. Return Initial Response (to Consumer)</text><ellipse cx="281.75" cy="1365.9342" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="281.75" cy="1365.9342" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="247" x="1563.75" y="1000.7447"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="227" x="1573.75" y="1023.5728">7b. Launch Async Workflow (Celery Task)</text><polygon fill="#FFFFFF" points="700.75,1082.0888,897.75,1082.0888,909.75,1094.0888,897.75,1106.0888,700.75,1106.0888,688.75,1094.0888,700.75,1082.0888" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="94" x="803.25" y="1117.8477">wheel_generation</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="197" x="700.75" y="1098.3568">Workflow Type Determined in Step 4?</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="311" x="643.75" y="1141.0707"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="291" x="653.75" y="1163.8988">Trigger execute_graph_workflow (wheel_generation)</text><polygon fill="#FFFFFF" points="1081.75,1082.0888,1131.75,1082.0888,1143.75,1094.0888,1131.75,1106.0888,1081.75,1106.0888,1069.75,1094.0888,1081.75,1082.0888" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="50" x="1081.75" y="1098.3568">post_spin</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="264" x="974.75" y="1126.0888"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="244" x="984.75" y="1148.9169">Trigger execute_graph_workflow (post_spin)</text><polygon fill="#FFFFFF" points="1366.25,1082.0888,1457.25,1082.0888,1469.25,1094.0888,1457.25,1106.0888,1366.25,1106.0888,1354.25,1094.0888,1366.25,1082.0888" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="91" x="1366.25" y="1098.3568">user_onboarding</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="306" x="1258.75" y="1126.0888"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="286" x="1268.75" y="1148.9169">Trigger execute_graph_workflow (user_onboarding)</text><polygon fill="#FFFFFF" points="1693.75,1082.0888,1782.75,1082.0888,1794.75,1094.0888,1782.75,1106.0888,1693.75,1106.0888,1681.75,1094.0888,1693.75,1082.0888" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="89" x="1693.75" y="1098.3568">activity_feedback</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="307" x="1584.75" y="1126.0888"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="287" x="1594.75" y="1148.9169">Trigger execute_graph_workflow (activity_feedback)</text><polygon fill="#FFFFFF" points="2020.75,1082.0888,2117.75,1082.0888,2129.75,1094.0888,2117.75,1106.0888,2020.75,1106.0888,2008.75,1094.0888,2020.75,1082.0888" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="97" x="2020.75" y="1098.3568">pre_spin_feedback</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="315" x="1911.75" y="1126.0888"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="295" x="1921.75" y="1148.9169">Trigger execute_graph_workflow (pre_spin_feedback)</text><polygon fill="#FFFFFF" points="2344.75,1082.0888,2398.75,1082.0888,2410.75,1094.0888,2398.75,1106.0888,2344.75,1106.0888,2332.75,1094.0888,2344.75,1082.0888" style="stroke:#000000;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="54" x="2344.75" y="1098.3568">discussion</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="83" x="2410.75" y="1090.8658">Unknown/Error</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="270" x="2236.75" y="1126.0888"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="250" x="2246.75" y="1148.9169">Trigger execute_graph_workflow (discussion)</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="72" x="2577.75" y="1121.8343"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="52" x="2587.75" y="1144.6624">Log Error</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="194" x="2516.75" y="1193.1784"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="174" x="2526.75" y="1216.0065">Send Error Response (to Client)</text><ellipse cx="2613.75" cy="1275.5225" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="2613.75" cy="1275.5225" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="166" x="1604.25" y="1380.6011"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="146" x="1614.25" y="1403.4292">Record Workflow Initiation</text><path d="M1829.25,1432.4112 L1829.25,1451.1173 L1809.25,1455.1173 L1829.25,1459.1173 L1829.25,1477.8233 A0,0 0 0 0 1829.25,1477.8233 L2005.25,1477.8233 A0,0 0 0 0 2005.25,1477.8233 L2005.25,1442.4112 L1995.25,1432.4112 L1829.25,1432.4112 A0,0 0 0 0 1829.25,1432.4112 " fill="#FFFFFF" style="stroke:#FFFFFF;stroke-width:1.5;"/><path d="M1995.25,1432.4112 L1995.25,1442.4112 L2005.25,1442.4112 L1995.25,1432.4112 " fill="#FFFFFF" style="stroke:#FFFFFF;stroke-width:1.5;"/><text fill="#FFFFFF" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="155" x="1835.25" y="1451.3083">Target workflow launched</text><text fill="#FFFFFF" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="98" x="1835.25" y="1469.0143">asynchronously.</text><rect fill="#FFFFFF" height="36.3441" rx="3.5" ry="3.5" style="stroke:#000000;stroke-width:0.5;" width="244" x="1565.25" y="1436.9452"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="224" x="1575.25" y="1459.7733">8. Return Initial Response (to Consumer)</text><ellipse cx="1687.25" cy="1508.8233" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="1687.25" cy="1508.8233" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="984.5" cy="1552.8233" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="984.5" cy="1552.8233" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="984.5" x2="984.5" y1="155.4799" y2="175.4799"/><polygon fill="#000000" points="980.5,165.4799,984.5,175.4799,988.5,165.4799,984.5,169.4799" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="897" x2="897" y1="371.1682" y2="406.1501"/><polygon fill="#000000" points="893,396.1501,897,406.1501,901,396.1501,897,400.1501" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1130" x2="1130" y1="374.1682" y2="409.1501"/><polygon fill="#000000" points="1126,399.1501,1130,409.1501,1134,399.1501,1130,403.1501" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="110.5" x2="110.5" y1="314.8241" y2="349.806"/><polygon fill="#000000" points="106.5,339.806,110.5,349.806,114.5,339.806,110.5,343.806" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="110.5" x2="110.5" y1="386.1501" y2="470.409"/><polygon fill="#000000" points="106.5,460.409,110.5,470.409,114.5,460.409,110.5,464.409" style="stroke:#000000;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="87" x="114.5" y="448.909">Dispatcher Entry</text><line style="stroke:#000000;stroke-width:1.0;" x1="482.5" x2="482.5" y1="314.8241" y2="339.358"/><polygon fill="#000000" points="478.5,329.358,482.5,339.358,486.5,329.358,482.5,333.358" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="482.5" x2="482.5" y1="375.7021" y2="470.409"/><polygon fill="#000000" points="478.5,460.409,482.5,470.409,486.5,460.409,482.5,464.409" style="stroke:#000000;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="87" x="486.5" y="443.685">Dispatcher Entry</text><line style="stroke:#000000;stroke-width:1.0;" x1="897" x2="897" y1="314.8241" y2="334.8241"/><polygon fill="#000000" points="893,324.8241,897,334.8241,901,324.8241,897,328.8241" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="161.5" x2="442" y1="302.8241" y2="302.8241"/><polygon fill="#000000" points="432,298.8241,442,302.8241,432,306.8241,436,302.8241" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="523" x2="819.5" y1="302.8241" y2="302.8241"/><polygon fill="#000000" points="809.5,298.8241,819.5,302.8241,809.5,306.8241,813.5,302.8241" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="883" x2="110.5" y1="243.8241" y2="243.8241"/><line style="stroke:#000000;stroke-width:1.0;" x1="110.5" x2="110.5" y1="243.8241" y2="290.8241"/><polygon fill="#000000" points="106.5,280.8241,110.5,290.8241,114.5,280.8241,110.5,284.8241" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="974.5" x2="1130" y1="302.8241" y2="302.8241"/><line style="stroke:#000000;stroke-width:1.0;" x1="1130" x2="1130" y1="302.8241" y2="337.8241"/><polygon fill="#000000" points="1126,327.8241,1130,337.8241,1134,327.8241,1130,331.8241" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="110.5" x2="625" y1="470.409" y2="470.409"/><line style="stroke:#000000;stroke-width:1.0;" x1="1344" x2="1344" y1="302.1682" y2="337.1682"/><polygon fill="#000000" points="1340,327.1682,1344,337.1682,1348,327.1682,1344,331.1682" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1086" x2="1344" y1="243.8241" y2="243.8241"/><line style="stroke:#000000;stroke-width:1.0;" x1="1344" x2="1344" y1="243.8241" y2="265.8241"/><polygon fill="#000000" points="1340,255.8241,1344,265.8241,1348,255.8241,1344,259.8241" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="625" x2="625" y1="470.409" y2="475.409"/><line style="stroke:#000000;stroke-width:1.0;" x1="625" x2="984.5" y1="475.409" y2="475.409"/><line style="stroke:#000000;stroke-width:1.0;" x1="984.5" x2="984.5" y1="475.409" y2="531.4769"/><polygon fill="#000000" points="980.5,521.4769,984.5,531.4769,988.5,521.4769,984.5,525.4769" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="984.5" x2="984.5" y1="211.8241" y2="231.8241"/><polygon fill="#000000" points="980.5,221.8241,984.5,231.8241,988.5,221.8241,984.5,225.8241" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="984.5" x2="984.5" y1="70.0679" y2="119.1358"/><polygon fill="#000000" points="980.5,109.1358,984.5,119.1358,988.5,109.1358,984.5,113.1358" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1326.0625" x2="1303.8125" y1="736.0927" y2="736.0927"/><line style="stroke:#000000;stroke-width:1.0;" x1="1303.8125" x2="1303.8125" y1="736.0927" y2="758.0927"/><polygon fill="#000000" points="1299.8125,748.0927,1303.8125,758.0927,1307.8125,748.0927,1303.8125,752.0927" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1481.0625" x2="1503.3125" y1="736.0927" y2="736.0927"/><line style="stroke:#000000;stroke-width:1.0;" x1="1503.3125" x2="1503.3125" y1="736.0927" y2="758.0927"/><polygon fill="#000000" points="1499.3125,748.0927,1503.3125,758.0927,1507.3125,748.0927,1503.3125,752.0927" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1303.8125" x2="1303.8125" y1="794.4368" y2="812.4368"/><line style="stroke:#000000;stroke-width:1.0;" x1="1303.8125" x2="1391.5625" y1="812.4368" y2="812.4368"/><polygon fill="#000000" points="1381.5625,808.4368,1391.5625,812.4368,1381.5625,816.4368,1385.5625,812.4368" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1503.3125" x2="1503.3125" y1="794.4368" y2="812.4368"/><line style="stroke:#000000;stroke-width:1.0;" x1="1503.3125" x2="1415.5625" y1="812.4368" y2="812.4368"/><polygon fill="#000000" points="1425.5625,808.4368,1415.5625,812.4368,1425.5625,816.4368,1421.5625,812.4368" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1403.5625" x2="1403.5625" y1="704.0927" y2="724.0927"/><polygon fill="#000000" points="1399.5625,714.0927,1403.5625,724.0927,1407.5625,714.0927,1403.5625,718.0927" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1164.1875" x2="1034.8125" y1="642.7667" y2="642.7667"/><line style="stroke:#000000;stroke-width:1.0;" x1="1034.8125" x2="1034.8125" y1="642.7667" y2="667.7486"/><polygon fill="#000000" points="1030.8125,657.7486,1034.8125,667.7486,1038.8125,657.7486,1034.8125,661.7486" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1274.1875" x2="1403.5625" y1="642.7667" y2="642.7667"/><line style="stroke:#000000;stroke-width:1.0;" x1="1403.5625" x2="1403.5625" y1="642.7667" y2="667.7486"/><polygon fill="#000000" points="1399.5625,657.7486,1403.5625,667.7486,1407.5625,657.7486,1403.5625,661.7486" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1034.8125" x2="1034.8125" y1="704.0927" y2="842.4368"/><line style="stroke:#000000;stroke-width:1.0;" x1="1034.8125" x2="1207.1875" y1="842.4368" y2="842.4368"/><polygon fill="#000000" points="1197.1875,838.4368,1207.1875,842.4368,1197.1875,846.4368,1201.1875,842.4368" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1403.5625" x2="1403.5625" y1="824.4368" y2="842.4368"/><line style="stroke:#000000;stroke-width:1.0;" x1="1403.5625" x2="1231.1875" y1="842.4368" y2="842.4368"/><polygon fill="#000000" points="1241.1875,838.4368,1231.1875,842.4368,1241.1875,846.4368,1237.1875,842.4368" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="882" x2="749.8125" y1="602.8029" y2="602.8029"/><line style="stroke:#000000;stroke-width:1.0;" x1="749.8125" x2="749.8125" y1="602.8029" y2="627.7848"/><polygon fill="#000000" points="745.8125,617.7848,749.8125,627.7848,753.8125,617.7848,749.8125,621.7848" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1087" x2="1219.1875" y1="602.8029" y2="602.8029"/><line style="stroke:#000000;stroke-width:1.0;" x1="1219.1875" x2="1219.1875" y1="602.8029" y2="627.7848"/><polygon fill="#000000" points="1215.1875,617.7848,1219.1875,627.7848,1223.1875,617.7848,1219.1875,621.7848" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="749.8125" x2="749.8125" y1="664.1289" y2="872.4368"/><line style="stroke:#000000;stroke-width:1.0;" x1="749.8125" x2="972.5" y1="872.4368" y2="872.4368"/><polygon fill="#000000" points="962.5,868.4368,972.5,872.4368,962.5,876.4368,966.5,872.4368" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1219.1875" x2="1219.1875" y1="854.4368" y2="872.4368"/><line style="stroke:#000000;stroke-width:1.0;" x1="1219.1875" x2="996.5" y1="872.4368" y2="872.4368"/><polygon fill="#000000" points="1006.5,868.4368,996.5,872.4368,1006.5,876.4368,1002.5,872.4368" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="984.5" x2="984.5" y1="567.821" y2="587.821"/><polygon fill="#000000" points="980.5,577.821,984.5,587.821,988.5,577.821,984.5,581.821" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="984.5" x2="984.5" y1="884.4368" y2="904.4368"/><polygon fill="#000000" points="980.5,894.4368,984.5,904.4368,988.5,894.4368,984.5,898.4368" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="281.75" x2="281.75" y1="1041.6228" y2="1066.1567"/><polygon fill="#000000" points="277.75,1056.1567,281.75,1066.1567,285.75,1056.1567,281.75,1060.1567" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="281.75" x2="281.75" y1="1102.5008" y2="1137.5008"/><polygon fill="#000000" points="277.75,1127.5008,281.75,1137.5008,285.75,1127.5008,281.75,1131.5008" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="281.75" x2="281.75" y1="1173.8449" y2="1208.8449"/><polygon fill="#000000" points="277.75,1198.8449,281.75,1208.8449,285.75,1198.8449,281.75,1202.8449" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="281.75" x2="281.75" y1="1245.1891" y2="1284.723"/><polygon fill="#000000" points="277.75,1274.723,281.75,1284.723,285.75,1274.723,281.75,1278.723" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="281.75" x2="281.75" y1="1321.0671" y2="1354.9342"/><polygon fill="#000000" points="277.75,1344.9342,281.75,1354.9342,285.75,1344.9342,281.75,1348.9342" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="2613.75" x2="2613.75" y1="1158.1784" y2="1193.1784"/><polygon fill="#000000" points="2609.75,1183.1784,2613.75,1193.1784,2617.75,1183.1784,2613.75,1187.1784" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="2613.75" x2="2613.75" y1="1229.5225" y2="1264.5225"/><polygon fill="#000000" points="2609.75,1254.5225,2613.75,1264.5225,2617.75,1254.5225,2613.75,1258.5225" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="799.25" x2="799.25" y1="1106.0888" y2="1141.0707"/><polygon fill="#000000" points="795.25,1131.0707,799.25,1141.0707,803.25,1131.0707,799.25,1135.0707" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="799.25" x2="799.25" y1="1177.4148" y2="1345.6011"/><polygon fill="#000000" points="795.25,1335.6011,799.25,1345.6011,803.25,1335.6011,799.25,1339.6011" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1106.75" x2="1106.75" y1="1106.0888" y2="1126.0888"/><polygon fill="#000000" points="1102.75,1116.0888,1106.75,1126.0888,1110.75,1116.0888,1106.75,1120.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1106.75" x2="1106.75" y1="1162.4329" y2="1345.6011"/><polygon fill="#000000" points="1102.75,1335.6011,1106.75,1345.6011,1110.75,1335.6011,1106.75,1339.6011" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1411.75" x2="1411.75" y1="1106.0888" y2="1126.0888"/><polygon fill="#000000" points="1407.75,1116.0888,1411.75,1126.0888,1415.75,1116.0888,1411.75,1120.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1411.75" x2="1411.75" y1="1162.4329" y2="1345.6011"/><polygon fill="#000000" points="1407.75,1335.6011,1411.75,1345.6011,1415.75,1335.6011,1411.75,1339.6011" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1738.25" x2="1738.25" y1="1106.0888" y2="1126.0888"/><polygon fill="#000000" points="1734.25,1116.0888,1738.25,1126.0888,1742.25,1116.0888,1738.25,1120.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1738.25" x2="1738.25" y1="1162.4329" y2="1345.6011"/><polygon fill="#000000" points="1734.25,1335.6011,1738.25,1345.6011,1742.25,1335.6011,1738.25,1339.6011" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="2069.25" x2="2069.25" y1="1106.0888" y2="1126.0888"/><polygon fill="#000000" points="2065.25,1116.0888,2069.25,1126.0888,2073.25,1116.0888,2069.25,1120.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="2069.25" x2="2069.25" y1="1162.4329" y2="1345.6011"/><polygon fill="#000000" points="2065.25,1335.6011,2069.25,1345.6011,2073.25,1335.6011,2069.25,1339.6011" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="2371.75" x2="2371.75" y1="1106.0888" y2="1126.0888"/><polygon fill="#000000" points="2367.75,1116.0888,2371.75,1126.0888,2375.75,1116.0888,2371.75,1120.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="2371.75" x2="2371.75" y1="1162.4329" y2="1345.6011"/><polygon fill="#000000" points="2367.75,1335.6011,2371.75,1345.6011,2375.75,1335.6011,2371.75,1339.6011" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="909.75" x2="1069.75" y1="1094.0888" y2="1094.0888"/><polygon fill="#000000" points="1059.75,1090.0888,1069.75,1094.0888,1059.75,1098.0888,1063.75,1094.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1143.75" x2="1354.25" y1="1094.0888" y2="1094.0888"/><polygon fill="#000000" points="1344.25,1090.0888,1354.25,1094.0888,1344.25,1098.0888,1348.25,1094.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1469.25" x2="1681.75" y1="1094.0888" y2="1094.0888"/><polygon fill="#000000" points="1671.75,1090.0888,1681.75,1094.0888,1671.75,1098.0888,1675.75,1094.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1794.75" x2="2008.75" y1="1094.0888" y2="1094.0888"/><polygon fill="#000000" points="1998.75,1090.0888,2008.75,1094.0888,1998.75,1098.0888,2002.75,1094.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="2129.75" x2="2332.75" y1="1094.0888" y2="1094.0888"/><polygon fill="#000000" points="2322.75,1090.0888,2332.75,1094.0888,2322.75,1098.0888,2326.75,1094.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1687.25" x2="1687.25" y1="1037.0888" y2="1057.0888"/><line style="stroke:#000000;stroke-width:1.0;" x1="1687.25" x2="799.25" y1="1057.0888" y2="1057.0888"/><line style="stroke:#000000;stroke-width:1.0;" x1="799.25" x2="799.25" y1="1057.0888" y2="1082.0888"/><polygon fill="#000000" points="795.25,1072.0888,799.25,1082.0888,803.25,1072.0888,799.25,1076.0888" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="2410.75" x2="2613.75" y1="1094.0888" y2="1094.0888"/><line style="stroke:#000000;stroke-width:1.0;" x1="2613.75" x2="2613.75" y1="1094.0888" y2="1121.8343"/><polygon fill="#000000" points="2609.75,1111.8343,2613.75,1121.8343,2617.75,1111.8343,2613.75,1115.8343" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="799.25" x2="2371.75" y1="1345.6011" y2="1345.6011"/><line style="stroke:#000000;stroke-width:1.0;" x1="1687.25" x2="1687.25" y1="1345.6011" y2="1380.6011"/><polygon fill="#000000" points="1683.25,1370.6011,1687.25,1380.6011,1691.25,1370.6011,1687.25,1374.6011" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1687.25" x2="1687.25" y1="1416.9452" y2="1436.9452"/><polygon fill="#000000" points="1683.25,1426.9452,1687.25,1436.9452,1691.25,1426.9452,1687.25,1430.9452" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1687.25" x2="1687.25" y1="1473.2893" y2="1497.8233"/><polygon fill="#000000" points="1683.25,1487.8233,1687.25,1497.8233,1691.25,1487.8233,1687.25,1491.8233" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="922" x2="281.75" y1="975.7628" y2="975.7628"/><line style="stroke:#000000;stroke-width:1.0;" x1="281.75" x2="281.75" y1="975.7628" y2="1005.2786"/><polygon fill="#000000" points="277.75,995.2786,281.75,1005.2786,285.75,995.2786,281.75,999.2786" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="1047" x2="1687.25" y1="975.7628" y2="975.7628"/><line style="stroke:#000000;stroke-width:1.0;" x1="1687.25" x2="1687.25" y1="975.7628" y2="1000.7447"/><polygon fill="#000000" points="1683.25,990.7447,1687.25,1000.7447,1691.25,990.7447,1687.25,994.7447" style="stroke:#000000;stroke-width:1.0;"/><line style="stroke:#000000;stroke-width:1.0;" x1="984.5" x2="984.5" y1="940.7809" y2="960.7809"/><polygon fill="#000000" points="980.5,950.7809,984.5,960.7809,988.5,950.7809,984.5,954.7809" style="stroke:#000000;stroke-width:1.0;"/><!--SRC=[nLPjRoCr4FwUNp4S0YKIjpsu7g0MSUgbhIZgbQf9GKWdHOvtahNYs7ksDsc4xhypjZVxbhIGAnBxeSrwny-Cvtbcnb-u39S8Ap4rJBcUNGYLCyEMmBWJA-4sy5SFw7d7-69kTA7IePRQm9UNuOdVj4dHbEklmXFMpupHw_RofuO7-kk44rfzfeicLYhOKglq6S5UQkKUnrVQuQ7WZVKkE67twEmI0nfIuE_HMZP7k3MQqq-Xvd0fzHhwvyBcpF4C3Q7mmWvwFUieSxrUdRyNznRDo6_JQgYLBPPeNfJXdjmXHx52-0Ed8qtEtDRNQVn-oun5-7NqsqrS43Fe_ywaI1aTUHlMU9CZV0q4xb2vjmCWsQZIRX2sRRSsTrHsF6DkienVQlFWViYaX8ofLEAaQTOVdBOC0mD6p3Dt0hVCMhHGMWAFKRNCN_uCZSnTA6SsDHvAYpvMcmirCMWBwPw8gc5rU50VL5yeBXDee92DOvHUDZWyvBKsYna9Oq8YS8Kbn8y5sgV2VsH7zoZMwRpb3VhtQg7qMWKgEmv6NhCNLDy6xj3cf3coTng6Kj2XduHMgPZrQYTNQjKMqR_7hd0ZvgTMIP1KLsWiyszrMfCyrjTMPLMLxC-yp_Yt2Gmpv0kgXSZQM-AqJ2AcLNhXg4dnOAlyFqaIB-7Y8PU22mTNGHIDmnwp1CxHeLaAHJMw1UlFZ5vM7iipnuJTw7R1VRUDZrh7J4ZquVtqvjLNfC1bBWcw4ykLgZwqEJsUxW-beEOoqMggcKcfBmrEEpAvqOGwt8_vEgc1VX54cDd0jPuJiI5_RPtuqxocV8ozqVLx8fnAIim43oHkJp4gk6UmxIWUW3-HImBio396Vyc4B0mIVVUAhUY5JINk0qyJk2keOxUZwa1Mukv8FVoAIdkJmBj2o3GsqmT7ZSDtvLfmtoTmneE8VPQ4mTHJYiay-GQM8iu6eMQwoMj3KJyyegXKM1eVVaGqyj1iO6DcvaZ_QqDoItN7jPJ8NVJPy4DokMQ5uXcSsOtY3LT3b4WSZfbTD7oTZCdHd0eC7v0N3YTpm_AiBg3z4VhvHLErHhzILE6Tt9_yIEIWAumgFyjEwoY7ujwJjnWyhsA0AZ0PJebfov3vCsT6ArrOkKcQf0ViFLsghfOmvJ65jN0Py59G_hrq-PmqUmbLDc5kLsf8YKCOESpXk1hDwmnHJkQeqCGoQnVmFt6sitrtPENQkZ143uIkz-rYTdlIOSXFTpIFlxtzJcP4qvGAz40Fk_ltvCMKTulzFg929SvyQKPtHE1qK-abDg0FYh0ChWIkVMC8V1oOvvquTcDjbEPd1qaQbiZCKHIB17_REopIlGsYTLSv2lU63bb-u8JrqsTSOXwxnVmFFIeswl- -FsrlKM7bRm00]--></g></svg>