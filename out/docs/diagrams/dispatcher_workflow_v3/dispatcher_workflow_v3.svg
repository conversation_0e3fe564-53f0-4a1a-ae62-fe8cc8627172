<?xml version="1.0" encoding="us-ascii" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="2119px" preserveAspectRatio="none" style="width:2742px;height:2119px;background:#FFFFFF;" version="1.1" viewBox="0 0 2742 2119" width="2742px" zoomAndPan="magnify"><defs/><g><text fill="#000000" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacing" textLength="371" x="1184.375" y="34.9659">Backend Message Processing Flow (Dispatcher Focus)</text><ellipse cx="984.5" cy="60.0679" fill="#222222" rx="10" ry="10" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#E8DAEF" height="402.3411" rx="3.5" ry="3.5" style="stroke:#6C3483;stroke-width:1.5;" width="1450" x="11" y="80.0679"/><path d="M172,80.0679 L172,92.1358 L162,102.1358 L11,102.1358 " fill="none" style="stroke:#6C3483;stroke-width:1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="151" x="14" y="96.0339">UserSessionConsumer</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="182" x="893.5" y="119.1358"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="162" x="903.5" y="141.9639">Receive WebSocket Message</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="83" x="943" y="175.4799"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="63" x="953" y="198.308">Parse JSON</text><polygon fill="#F8F9FA" points="895,231.8241,1074,231.8241,1086,243.8241,1074,255.8241,895,255.8241,883,243.8241,895,231.8241" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="179" x="895" y="248.092">Validate Message Type &amp; Content?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="26" x="857" y="240.6011">Valid</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="36" x="1086" y="240.6011">Invalid</text><polygon fill="#F8F9FA" points="71.5,290.8241,149.5,290.8241,161.5,302.8241,149.5,314.8241,71.5,314.8241,59.5,302.8241,71.5,290.8241" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="72" x="114.5" y="326.583">chat_message</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="78" x="71.5" y="307.092">Message Type?</text><path d="M218,354.125 L218,363.978 L198,367.978 L218,371.978 L218,381.831 A0,0 0 0 0 218,381.831 L385,381.831 A0,0 0 0 0 385,381.831 L385,364.125 L375,354.125 L218,354.125 A0,0 0 0 0 218,354.125 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><path d="M375,354.125 L375,364.125 L385,364.125 L375,354.125 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="146" x="224" y="373.022">Passes message content</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="175" x="23" y="349.806"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="155" x="33" y="372.634">Call handle_chat_message()</text><polygon fill="#F8F9FA" points="454,290.8241,511,290.8241,523,302.8241,511,314.8241,454,314.8241,442,302.8241,454,290.8241" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="57" x="454" y="307.092">spin_result</text><path d="M580,334.8241 L580,353.5301 L560,357.5301 L580,361.5301 L580,380.2361 A0,0 0 0 0 580,380.2361 L761,380.2361 A0,0 0 0 0 761,380.2361 L761,344.8241 L751,334.8241 L580,334.8241 A0,0 0 0 0 580,334.8241 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><path d="M751,334.8241 L751,344.8241 L761,344.8241 L751,334.8241 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="146" x="586" y="353.7211">Passes message content</text><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="160" x="586" y="371.4271">(incl. spin_result metadata)</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="155" x="405" y="339.358"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="135" x="415" y="362.1861">Call handle_spin_result()</text><polygon fill="#F8F9FA" points="831.5,290.8241,962.5,290.8241,974.5,302.8241,962.5,314.8241,831.5,314.8241,819.5,302.8241,831.5,290.8241" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="131" x="831.5" y="307.092">workflow_status_request</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="79" x="974.5" y="299.6011">Unknown Type</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="232" x="781" y="334.8241"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="212" x="791" y="357.6522">Call handle_workflow_status_request()</text><ellipse cx="897" cy="417.1501" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="897" cy="417.1501" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="194" x="1033" y="337.8241"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="174" x="1043" y="360.6522">Send Error Response (to Client)</text><ellipse cx="1130" cy="420.1501" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="1130" cy="420.1501" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="194" x="1247" y="265.8241"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="174" x="1257" y="288.6522">Send Error Response (to Client)</text><ellipse cx="1344" cy="348.1682" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="1344" cy="348.1682" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#E8DAEF" height="1050.282" rx="3.5" ry="3.5" style="stroke:#6C3483;stroke-width:1.5;" width="2614" x="117.75" y="492.409"/><path d="M410.75,492.409 L410.75,504.4769 L400.75,514.4769 L117.75,514.4769 " fill="none" style="stroke:#6C3483;stroke-width:1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="283" x="120.75" y="508.3749">ConversationDispatcher.process_message</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="113" x="928" y="531.4769"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="93" x="938" y="554.305">Dispatcher Entry</text><polygon fill="#F8F9FA" points="894,587.821,1075,587.821,1087,602.8029,1075,617.7848,894,617.7848,882,602.8029,894,587.821" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="100" x="894" y="599.5799">1. Check Metadata?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="181" x="894" y="614.5618">(requested_workflow / spin_result)</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="99" x="783" y="599.5799">Yes - Explicit Intent</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="15" x="1087" y="599.5799">No</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="250" x="624.8125" y="627.7848"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="230" x="634.8125" y="650.6129">4a. Determine Workflow (from Metadata)</text><polygon fill="#F8F9FA" points="1176.1875,627.7848,1262.1875,627.7848,1274.1875,642.7667,1262.1875,657.7486,1176.1875,657.7486,1164.1875,642.7667,1176.1875,627.7848" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="86" x="1176.1875" y="639.5437">2. Check Profile?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="84" x="1176.1875" y="654.5256">(&lt;50% complete)</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="86" x="1078.1875" y="639.5437">Yes - Incomplete</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="75" x="1274.1875" y="639.5437">No - Complete</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="260" x="904.8125" y="667.7486"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="240" x="914.8125" y="690.5766">4b. Determine Workflow (user_onboarding)</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="199" x="1304.0625" y="667.7486"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="179" x="1314.0625" y="690.5766">3. Workflow History Log Request</text><polygon fill="#F8F9FA" points="1338.0625,724.0927,1469.0625,724.0927,1481.0625,736.0927,1469.0625,748.0927,1338.0625,748.0927,1326.0625,736.0927,1338.0625,724.0927" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="131" x="1338.0625" y="740.3606">3a. Try LLM Classification</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="39" x="1287.0625" y="732.8697">Success</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="107" x="1481.0625" y="732.8697">Failure / Unavailable</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="218" x="1194.8125" y="758.0927"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="198" x="1204.8125" y="780.9208">4c. Determine Workflow (from LLM)</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="141" x="1432.8125" y="758.0927"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="121" x="1442.8125" y="780.9208">4d. Rule Classification</text><polygon fill="#F8F9FA" points="1403.5625,800.4368,1415.5625,812.4368,1403.5625,824.4368,1391.5625,812.4368,1403.5625,800.4368" style="stroke:#495057;stroke-width:0.5;"/><polygon fill="#F8F9FA" points="1219.1875,830.4368,1231.1875,842.4368,1219.1875,854.4368,1207.1875,842.4368,1219.1875,830.4368" style="stroke:#495057;stroke-width:0.5;"/><polygon fill="#F8F9FA" points="984.5,860.4368,996.5,872.4368,984.5,884.4368,972.5,872.4368,984.5,860.4368" style="stroke:#495057;stroke-width:0.5;"/><path d="M1078,908.7558 L1078,918.6088 L1058,922.6088 L1078,926.6088 L1078,936.4619 A0,0 0 0 0 1078,936.4619 L1489,936.4619 A0,0 0 0 0 1489,936.4619 L1489,918.7558 L1479,908.7558 L1078,908.7558 A0,0 0 0 0 1078,908.7558 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><path d="M1479,908.7558 L1479,918.7558 L1489,918.7558 L1479,908.7558 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="390" x="1084" y="927.6528">Context includes user message, history summary, profile info, etc.</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="147" x="911" y="904.4368"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="127" x="921" y="927.2649">5. Build Context Packet</text><polygon fill="#F8F9FA" points="883.5,960.7809,1085.5,960.7809,1097.5,975.7628,1085.5,990.7447,883.5,990.7447,871.5,975.7628,883.5,960.7809" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="101" x="883.5" y="972.5398">6. Action Required?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="202" x="883.5" y="987.5217">(e.g., missing info for target workflow)</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="17" x="854.5" y="972.5398">Yes</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="197" x="1097.5" y="972.5398">No - Proceed with classified workflow</text><path d="M404.75,1000.7447 L404.75,1019.4507 L384.75,1023.4507 L404.75,1027.4507 L404.75,1046.1567 A0,0 0 0 0 404.75,1046.1567 L631.75,1046.1567 A0,0 0 0 0 631.75,1046.1567 L631.75,1010.7447 L621.75,1000.7447 L404.75,1000.7447 A0,0 0 0 0 404.75,1000.7447 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><path d="M621.75,1000.7447 L621.75,1010.7447 L631.75,1010.7447 L621.75,1000.7447 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="172" x="410.75" y="1019.6417">Target workflow = discussion</text><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="206" x="410.75" y="1037.3477">Context includes missing_field info</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="224" x="160.75" y="1005.2786"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="204" x="170.75" y="1028.1067">7a. Determine Workflow (discussion)</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="247" x="149.25" y="1066.1567"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="227" x="159.25" y="1088.9848">7b. Launch Async Workflow (Celery Task)</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="270" x="137.75" y="1137.5008"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="250" x="147.75" y="1160.3289">Trigger execute_graph_workflow (discussion)</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="166" x="189.75" y="1208.8449"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="146" x="199.75" y="1231.673">Record Workflow Initiation</text><path d="M414.75,1280.1891 L414.75,1307.7481 L394.75,1311.7481 L414.75,1315.7481 L414.75,1343.3071 A0,0 0 0 0 414.75,1343.3071 L632.75,1343.3071 A0,0 0 0 0 632.75,1343.3071 L632.75,1290.1891 L622.75,1280.1891 L414.75,1280.1891 A0,0 0 0 0 414.75,1280.1891 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><path d="M622.75,1280.1891 L622.75,1290.1891 L632.75,1290.1891 L622.75,1280.1891 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="179" x="420.75" y="1299.0861">Discussion workflow launched</text><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="181" x="420.75" y="1316.7921">asynchronously to collect info.</text><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="197" x="420.75" y="1334.4981">Dispatcher processing ends here.</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="244" x="150.75" y="1293.576"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="224" x="160.75" y="1316.4041">8. Return Initial Response (to Consumer)</text><ellipse cx="272.75" cy="1382.268" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="272.75" cy="1382.268" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="247" x="1572.75" y="1000.7447"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="227" x="1582.75" y="1023.5728">7b. Launch Async Workflow (Celery Task)</text><polygon fill="#F8F9FA" points="709.75,1082.0888,906.75,1082.0888,918.75,1094.0888,906.75,1106.0888,709.75,1106.0888,697.75,1094.0888,709.75,1082.0888" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="94" x="812.25" y="1117.8477">wheel_generation</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="197" x="709.75" y="1098.3568">Workflow Type Determined in Step 4?</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="311" x="652.75" y="1141.0707"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="291" x="662.75" y="1163.8988">Trigger execute_graph_workflow (wheel_generation)</text><polygon fill="#F8F9FA" points="1090.75,1082.0888,1140.75,1082.0888,1152.75,1094.0888,1140.75,1106.0888,1090.75,1106.0888,1078.75,1094.0888,1090.75,1082.0888" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="50" x="1090.75" y="1098.3568">post_spin</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="264" x="983.75" y="1126.0888"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="244" x="993.75" y="1148.9169">Trigger execute_graph_workflow (post_spin)</text><polygon fill="#F8F9FA" points="1375.25,1082.0888,1466.25,1082.0888,1478.25,1094.0888,1466.25,1106.0888,1375.25,1106.0888,1363.25,1094.0888,1375.25,1082.0888" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="91" x="1375.25" y="1098.3568">user_onboarding</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="306" x="1267.75" y="1126.0888"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="286" x="1277.75" y="1148.9169">Trigger execute_graph_workflow (user_onboarding)</text><polygon fill="#F8F9FA" points="1702.75,1082.0888,1791.75,1082.0888,1803.75,1094.0888,1791.75,1106.0888,1702.75,1106.0888,1690.75,1094.0888,1702.75,1082.0888" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="89" x="1702.75" y="1098.3568">activity_feedback</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="307" x="1593.75" y="1126.0888"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="287" x="1603.75" y="1148.9169">Trigger execute_graph_workflow (activity_feedback)</text><polygon fill="#F8F9FA" points="2029.75,1082.0888,2126.75,1082.0888,2138.75,1094.0888,2126.75,1106.0888,2029.75,1106.0888,2017.75,1094.0888,2029.75,1082.0888" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="97" x="2029.75" y="1098.3568">pre_spin_feedback</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="315" x="1920.75" y="1126.0888"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="295" x="1930.75" y="1148.9169">Trigger execute_graph_workflow (pre_spin_feedback)</text><polygon fill="#F8F9FA" points="2353.75,1082.0888,2407.75,1082.0888,2419.75,1094.0888,2407.75,1106.0888,2353.75,1106.0888,2341.75,1094.0888,2353.75,1082.0888" style="stroke:#495057;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="54" x="2353.75" y="1098.3568">discussion</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="83" x="2419.75" y="1090.8658">Unknown/Error</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="270" x="2245.75" y="1126.0888"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="250" x="2255.75" y="1148.9169">Trigger execute_graph_workflow (discussion)</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="72" x="2586.75" y="1121.8343"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="52" x="2596.75" y="1144.6624">Log Error</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="194" x="2525.75" y="1193.1784"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="174" x="2535.75" y="1216.0065">Send Error Response (to Client)</text><ellipse cx="2622.75" cy="1275.5225" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="2622.75" cy="1275.5225" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="166" x="1613.25" y="1379.2288"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="146" x="1623.25" y="1402.0569">Record Workflow Initiation</text><path d="M1838.25,1425.5729 L1838.25,1453.132 L1818.25,1457.132 L1838.25,1461.132 L1838.25,1488.691 A0,0 0 0 0 1838.25,1488.691 L2056.25,1488.691 A0,0 0 0 0 2056.25,1488.691 L2056.25,1435.5729 L2046.25,1425.5729 L1838.25,1425.5729 A0,0 0 0 0 1838.25,1425.5729 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><path d="M2046.25,1425.5729 L2046.25,1435.5729 L2056.25,1435.5729 L2046.25,1425.5729 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="155" x="1844.25" y="1444.47">Target workflow launched</text><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="98" x="1844.25" y="1462.176">asynchronously.</text><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="197" x="1844.25" y="1479.882">Dispatcher processing ends here.</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="244" x="1574.25" y="1438.9599"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="224" x="1584.25" y="1461.788">8. Return Initial Response (to Consumer)</text><ellipse cx="1696.25" cy="1519.691" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="1696.25" cy="1519.691" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#E8DAEF" height="514.0257" rx="3.5" ry="3.5" style="stroke:#6C3483;stroke-width:1.5;" width="817.5" x="494" y="1552.691"/><path d="M804,1552.691 L804,1564.7589 L794,1574.7589 L494,1574.7589 " fill="none" style="stroke:#6C3483;stroke-width:1.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="300" x="497" y="1568.6569">Async Workflow Execution &amp; Result Handling</text><path d="M514,1801.6433 L514,1847.0553 L770,1847.0553 L770,1811.6433 L760,1801.6433 L514,1801.6433 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><path d="M760,1801.6433 L760,1811.6433 L770,1811.6433 L760,1801.6433 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="233" x="520" y="1820.5403">This partition executes asynchronously</text><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="235" x="520" y="1838.2463">after being triggered by the Dispatcher.</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="222" x="873.5" y="1584.7589"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="202" x="883.5" y="1607.587">execute_graph_workflow Task Starts</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="236" x="866.5" y="1641.103"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="216" x="876.5" y="1663.9311">Runs appropriate LangGraph Workflow</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="180" x="894.5" y="1697.4471"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="160" x="904.5" y="1720.2752">Task Completes Successfully</text><path d="M1114.5,1782.3152 L1114.5,1792.1682 L1094.5,1796.1682 L1114.5,1800.1682 L1114.5,1810.0212 A0,0 0 0 0 1114.5,1810.0212 L1301.5,1810.0212 A0,0 0 0 0 1301.5,1810.0212 L1301.5,1792.3152 L1291.5,1782.3152 L1114.5,1782.3152 A0,0 0 0 0 1114.5,1782.3152 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><path d="M1291.5,1782.3152 L1291.5,1792.3152 L1301.5,1792.3152 L1291.5,1782.3152 " fill="#FFF9C4" style="stroke:#FBC02D;stroke-width:1.5;"/><text fill="#333333" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="166" x="1120.5" y="1801.2122">Intercepts Celery task result</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="220" x="874.5" y="1777.9961"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="200" x="884.5" y="1800.8242">handle_task_success Signal Handler</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="192" x="888.5" y="1849.3402"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="172" x="898.5" y="1872.1683">Extracts workflow result &amp; type</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="219" x="875" y="1905.6843"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="199" x="885" y="1928.5124">Write Workflow Result to HistoryLog</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="235" x="867" y="1962.0285"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="215" x="877" y="1984.8565">Formats result based on workflow type</text><rect fill="#F8F9FA" height="36.3441" rx="3.5" ry="3.5" style="stroke:#495057;stroke-width:0.5;" width="409" x="780" y="2018.3726"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="389" x="790" y="2041.2007">Channel Layer Group Send (Sends formatted result(s) to specific user)</text><ellipse cx="984.5" cy="2097.7167" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="984.5" cy="2097.7167" fill="#222222" rx="6" ry="6" style="stroke:#222222;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="155.4799" y2="175.4799"/><polygon fill="#6C3483" points="980.5,165.4799,984.5,175.4799,988.5,165.4799,984.5,169.4799" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="897" x2="897" y1="371.1682" y2="406.1501"/><polygon fill="#6C3483" points="893,396.1501,897,406.1501,901,396.1501,897,400.1501" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1130" x2="1130" y1="374.1682" y2="409.1501"/><polygon fill="#6C3483" points="1126,399.1501,1130,409.1501,1134,399.1501,1130,403.1501" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="110.5" x2="110.5" y1="314.8241" y2="349.806"/><polygon fill="#6C3483" points="106.5,339.806,110.5,349.806,114.5,339.806,110.5,343.806" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="110.5" x2="110.5" y1="386.1501" y2="470.409"/><polygon fill="#6C3483" points="106.5,460.409,110.5,470.409,114.5,460.409,110.5,464.409" style="stroke:#6C3483;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="87" x="114.5" y="448.909">Dispatcher Entry</text><line style="stroke:#6C3483;stroke-width:1.0;" x1="482.5" x2="482.5" y1="314.8241" y2="339.358"/><polygon fill="#6C3483" points="478.5,329.358,482.5,339.358,486.5,329.358,482.5,333.358" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="482.5" x2="482.5" y1="375.7021" y2="470.409"/><polygon fill="#6C3483" points="478.5,460.409,482.5,470.409,486.5,460.409,482.5,464.409" style="stroke:#6C3483;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="87" x="486.5" y="443.685">Dispatcher Entry</text><line style="stroke:#6C3483;stroke-width:1.0;" x1="897" x2="897" y1="314.8241" y2="334.8241"/><polygon fill="#6C3483" points="893,324.8241,897,334.8241,901,324.8241,897,328.8241" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="161.5" x2="442" y1="302.8241" y2="302.8241"/><polygon fill="#6C3483" points="432,298.8241,442,302.8241,432,306.8241,436,302.8241" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="523" x2="819.5" y1="302.8241" y2="302.8241"/><polygon fill="#6C3483" points="809.5,298.8241,819.5,302.8241,809.5,306.8241,813.5,302.8241" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="883" x2="110.5" y1="243.8241" y2="243.8241"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="110.5" x2="110.5" y1="243.8241" y2="290.8241"/><polygon fill="#6C3483" points="106.5,280.8241,110.5,290.8241,114.5,280.8241,110.5,284.8241" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="974.5" x2="1130" y1="302.8241" y2="302.8241"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1130" x2="1130" y1="302.8241" y2="337.8241"/><polygon fill="#6C3483" points="1126,327.8241,1130,337.8241,1134,327.8241,1130,331.8241" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="110.5" x2="625" y1="470.409" y2="470.409"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1344" x2="1344" y1="302.1682" y2="337.1682"/><polygon fill="#6C3483" points="1340,327.1682,1344,337.1682,1348,327.1682,1344,331.1682" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1086" x2="1344" y1="243.8241" y2="243.8241"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1344" x2="1344" y1="243.8241" y2="265.8241"/><polygon fill="#6C3483" points="1340,255.8241,1344,265.8241,1348,255.8241,1344,259.8241" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="625" x2="625" y1="470.409" y2="475.409"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="625" x2="984.5" y1="475.409" y2="475.409"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="475.409" y2="531.4769"/><polygon fill="#6C3483" points="980.5,521.4769,984.5,531.4769,988.5,521.4769,984.5,525.4769" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="211.8241" y2="231.8241"/><polygon fill="#6C3483" points="980.5,221.8241,984.5,231.8241,988.5,221.8241,984.5,225.8241" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="70.0679" y2="119.1358"/><polygon fill="#6C3483" points="980.5,109.1358,984.5,119.1358,988.5,109.1358,984.5,113.1358" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1326.0625" x2="1303.8125" y1="736.0927" y2="736.0927"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1303.8125" x2="1303.8125" y1="736.0927" y2="758.0927"/><polygon fill="#6C3483" points="1299.8125,748.0927,1303.8125,758.0927,1307.8125,748.0927,1303.8125,752.0927" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1481.0625" x2="1503.3125" y1="736.0927" y2="736.0927"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1503.3125" x2="1503.3125" y1="736.0927" y2="758.0927"/><polygon fill="#6C3483" points="1499.3125,748.0927,1503.3125,758.0927,1507.3125,748.0927,1503.3125,752.0927" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1303.8125" x2="1303.8125" y1="794.4368" y2="812.4368"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1303.8125" x2="1391.5625" y1="812.4368" y2="812.4368"/><polygon fill="#6C3483" points="1381.5625,808.4368,1391.5625,812.4368,1381.5625,816.4368,1385.5625,812.4368" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1503.3125" x2="1503.3125" y1="794.4368" y2="812.4368"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1503.3125" x2="1415.5625" y1="812.4368" y2="812.4368"/><polygon fill="#6C3483" points="1425.5625,808.4368,1415.5625,812.4368,1425.5625,816.4368,1421.5625,812.4368" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1403.5625" x2="1403.5625" y1="704.0927" y2="724.0927"/><polygon fill="#6C3483" points="1399.5625,714.0927,1403.5625,724.0927,1407.5625,714.0927,1403.5625,718.0927" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1164.1875" x2="1034.8125" y1="642.7667" y2="642.7667"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1034.8125" x2="1034.8125" y1="642.7667" y2="667.7486"/><polygon fill="#6C3483" points="1030.8125,657.7486,1034.8125,667.7486,1038.8125,657.7486,1034.8125,661.7486" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1274.1875" x2="1403.5625" y1="642.7667" y2="642.7667"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1403.5625" x2="1403.5625" y1="642.7667" y2="667.7486"/><polygon fill="#6C3483" points="1399.5625,657.7486,1403.5625,667.7486,1407.5625,657.7486,1403.5625,661.7486" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1034.8125" x2="1034.8125" y1="704.0927" y2="842.4368"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1034.8125" x2="1207.1875" y1="842.4368" y2="842.4368"/><polygon fill="#6C3483" points="1197.1875,838.4368,1207.1875,842.4368,1197.1875,846.4368,1201.1875,842.4368" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1403.5625" x2="1403.5625" y1="824.4368" y2="842.4368"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1403.5625" x2="1231.1875" y1="842.4368" y2="842.4368"/><polygon fill="#6C3483" points="1241.1875,838.4368,1231.1875,842.4368,1241.1875,846.4368,1237.1875,842.4368" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="882" x2="749.8125" y1="602.8029" y2="602.8029"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="749.8125" x2="749.8125" y1="602.8029" y2="627.7848"/><polygon fill="#6C3483" points="745.8125,617.7848,749.8125,627.7848,753.8125,617.7848,749.8125,621.7848" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1087" x2="1219.1875" y1="602.8029" y2="602.8029"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1219.1875" x2="1219.1875" y1="602.8029" y2="627.7848"/><polygon fill="#6C3483" points="1215.1875,617.7848,1219.1875,627.7848,1223.1875,617.7848,1219.1875,621.7848" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="749.8125" x2="749.8125" y1="664.1289" y2="872.4368"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="749.8125" x2="972.5" y1="872.4368" y2="872.4368"/><polygon fill="#6C3483" points="962.5,868.4368,972.5,872.4368,962.5,876.4368,966.5,872.4368" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1219.1875" x2="1219.1875" y1="854.4368" y2="872.4368"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1219.1875" x2="996.5" y1="872.4368" y2="872.4368"/><polygon fill="#6C3483" points="1006.5,868.4368,996.5,872.4368,1006.5,876.4368,1002.5,872.4368" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="567.821" y2="587.821"/><polygon fill="#6C3483" points="980.5,577.821,984.5,587.821,988.5,577.821,984.5,581.821" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="884.4368" y2="904.4368"/><polygon fill="#6C3483" points="980.5,894.4368,984.5,904.4368,988.5,894.4368,984.5,898.4368" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="272.75" x2="272.75" y1="1041.6228" y2="1066.1567"/><polygon fill="#6C3483" points="268.75,1056.1567,272.75,1066.1567,276.75,1056.1567,272.75,1060.1567" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="272.75" x2="272.75" y1="1102.5008" y2="1137.5008"/><polygon fill="#6C3483" points="268.75,1127.5008,272.75,1137.5008,276.75,1127.5008,272.75,1131.5008" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="272.75" x2="272.75" y1="1173.8449" y2="1208.8449"/><polygon fill="#6C3483" points="268.75,1198.8449,272.75,1208.8449,276.75,1198.8449,272.75,1202.8449" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="272.75" x2="272.75" y1="1245.1891" y2="1293.576"/><polygon fill="#6C3483" points="268.75,1283.576,272.75,1293.576,276.75,1283.576,272.75,1287.576" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="272.75" x2="272.75" y1="1329.9201" y2="1371.268"/><polygon fill="#6C3483" points="268.75,1361.268,272.75,1371.268,276.75,1361.268,272.75,1365.268" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="2622.75" x2="2622.75" y1="1158.1784" y2="1193.1784"/><polygon fill="#6C3483" points="2618.75,1183.1784,2622.75,1193.1784,2626.75,1183.1784,2622.75,1187.1784" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="2622.75" x2="2622.75" y1="1229.5225" y2="1264.5225"/><polygon fill="#6C3483" points="2618.75,1254.5225,2622.75,1264.5225,2626.75,1254.5225,2622.75,1258.5225" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="808.25" x2="808.25" y1="1106.0888" y2="1141.0707"/><polygon fill="#6C3483" points="804.25,1131.0707,808.25,1141.0707,812.25,1131.0707,808.25,1135.0707" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="808.25" x2="808.25" y1="1177.4148" y2="1351.268"/><polygon fill="#6C3483" points="804.25,1341.268,808.25,1351.268,812.25,1341.268,808.25,1345.268" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1115.75" x2="1115.75" y1="1106.0888" y2="1126.0888"/><polygon fill="#6C3483" points="1111.75,1116.0888,1115.75,1126.0888,1119.75,1116.0888,1115.75,1120.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1115.75" x2="1115.75" y1="1162.4329" y2="1351.268"/><polygon fill="#6C3483" points="1111.75,1341.268,1115.75,1351.268,1119.75,1341.268,1115.75,1345.268" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1420.75" x2="1420.75" y1="1106.0888" y2="1126.0888"/><polygon fill="#6C3483" points="1416.75,1116.0888,1420.75,1126.0888,1424.75,1116.0888,1420.75,1120.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1420.75" x2="1420.75" y1="1162.4329" y2="1351.268"/><polygon fill="#6C3483" points="1416.75,1341.268,1420.75,1351.268,1424.75,1341.268,1420.75,1345.268" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1747.25" x2="1747.25" y1="1106.0888" y2="1126.0888"/><polygon fill="#6C3483" points="1743.25,1116.0888,1747.25,1126.0888,1751.25,1116.0888,1747.25,1120.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1747.25" x2="1747.25" y1="1162.4329" y2="1351.268"/><polygon fill="#6C3483" points="1743.25,1341.268,1747.25,1351.268,1751.25,1341.268,1747.25,1345.268" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="2078.25" x2="2078.25" y1="1106.0888" y2="1126.0888"/><polygon fill="#6C3483" points="2074.25,1116.0888,2078.25,1126.0888,2082.25,1116.0888,2078.25,1120.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="2078.25" x2="2078.25" y1="1162.4329" y2="1351.268"/><polygon fill="#6C3483" points="2074.25,1341.268,2078.25,1351.268,2082.25,1341.268,2078.25,1345.268" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="2380.75" x2="2380.75" y1="1106.0888" y2="1126.0888"/><polygon fill="#6C3483" points="2376.75,1116.0888,2380.75,1126.0888,2384.75,1116.0888,2380.75,1120.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="2380.75" x2="2380.75" y1="1162.4329" y2="1351.268"/><polygon fill="#6C3483" points="2376.75,1341.268,2380.75,1351.268,2384.75,1341.268,2380.75,1345.268" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="918.75" x2="1078.75" y1="1094.0888" y2="1094.0888"/><polygon fill="#6C3483" points="1068.75,1090.0888,1078.75,1094.0888,1068.75,1098.0888,1072.75,1094.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1152.75" x2="1363.25" y1="1094.0888" y2="1094.0888"/><polygon fill="#6C3483" points="1353.25,1090.0888,1363.25,1094.0888,1353.25,1098.0888,1357.25,1094.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1478.25" x2="1690.75" y1="1094.0888" y2="1094.0888"/><polygon fill="#6C3483" points="1680.75,1090.0888,1690.75,1094.0888,1680.75,1098.0888,1684.75,1094.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1803.75" x2="2017.75" y1="1094.0888" y2="1094.0888"/><polygon fill="#6C3483" points="2007.75,1090.0888,2017.75,1094.0888,2007.75,1098.0888,2011.75,1094.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="2138.75" x2="2341.75" y1="1094.0888" y2="1094.0888"/><polygon fill="#6C3483" points="2331.75,1090.0888,2341.75,1094.0888,2331.75,1098.0888,2335.75,1094.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1696.25" x2="1696.25" y1="1037.0888" y2="1057.0888"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1696.25" x2="808.25" y1="1057.0888" y2="1057.0888"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="808.25" x2="808.25" y1="1057.0888" y2="1082.0888"/><polygon fill="#6C3483" points="804.25,1072.0888,808.25,1082.0888,812.25,1072.0888,808.25,1076.0888" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="2419.75" x2="2622.75" y1="1094.0888" y2="1094.0888"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="2622.75" x2="2622.75" y1="1094.0888" y2="1121.8343"/><polygon fill="#6C3483" points="2618.75,1111.8343,2622.75,1121.8343,2626.75,1111.8343,2622.75,1115.8343" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="808.25" x2="2380.75" y1="1351.268" y2="1351.268"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1696.25" x2="1696.25" y1="1351.268" y2="1379.2288"/><polygon fill="#6C3483" points="1692.25,1369.2288,1696.25,1379.2288,1700.25,1369.2288,1696.25,1373.2288" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1696.25" x2="1696.25" y1="1415.5729" y2="1438.9599"/><polygon fill="#6C3483" points="1692.25,1428.9599,1696.25,1438.9599,1700.25,1428.9599,1696.25,1432.9599" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1696.25" x2="1696.25" y1="1475.304" y2="1508.691"/><polygon fill="#6C3483" points="1692.25,1498.691,1696.25,1508.691,1700.25,1498.691,1696.25,1502.691" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="871.5" x2="272.75" y1="975.7628" y2="975.7628"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="272.75" x2="272.75" y1="975.7628" y2="1005.2786"/><polygon fill="#6C3483" points="268.75,995.2786,272.75,1005.2786,276.75,995.2786,272.75,999.2786" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1097.5" x2="1696.25" y1="975.7628" y2="975.7628"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="1696.25" x2="1696.25" y1="975.7628" y2="1000.7447"/><polygon fill="#6C3483" points="1692.25,990.7447,1696.25,1000.7447,1700.25,990.7447,1696.25,994.7447" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="940.7809" y2="960.7809"/><polygon fill="#6C3483" points="980.5,950.7809,984.5,960.7809,988.5,950.7809,984.5,954.7809" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="1621.103" y2="1641.103"/><polygon fill="#6C3483" points="980.5,1631.103,984.5,1641.103,988.5,1631.103,984.5,1635.103" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="1677.4471" y2="1697.4471"/><polygon fill="#6C3483" points="980.5,1687.4471,984.5,1697.4471,988.5,1687.4471,984.5,1691.4471" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="1733.7912" y2="1777.9961"/><polygon fill="#6C3483" points="980.5,1767.9961,984.5,1777.9961,988.5,1767.9961,984.5,1771.9961" style="stroke:#6C3483;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="185" x="988.5" y="1757.2731">handle_task_success Signal Handler</text><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="1814.3402" y2="1849.3402"/><polygon fill="#6C3483" points="980.5,1839.3402,984.5,1849.3402,988.5,1839.3402,984.5,1843.3402" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="1885.6843" y2="1905.6843"/><polygon fill="#6C3483" points="980.5,1895.6843,984.5,1905.6843,988.5,1895.6843,984.5,1899.6843" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="1942.0285" y2="1962.0285"/><polygon fill="#6C3483" points="980.5,1952.0285,984.5,1962.0285,988.5,1952.0285,984.5,1956.0285" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="1998.3726" y2="2018.3726"/><polygon fill="#6C3483" points="980.5,2008.3726,984.5,2018.3726,988.5,2008.3726,984.5,2012.3726" style="stroke:#6C3483;stroke-width:1.0;"/><line style="stroke:#6C3483;stroke-width:1.0;" x1="984.5" x2="984.5" y1="2054.7167" y2="2086.7167"/><polygon fill="#6C3483" points="980.5,2076.7167,984.5,2086.7167,988.5,2076.7167,984.5,2080.7167" style="stroke:#6C3483;stroke-width:1.0;"/><!--SRC=[nLTjRzis4FxkNt7hiC86KhLhd3PDjXMfOxSPqgw8qmq32XYqTBQ9qAH6KdQCeVzzHufwjPtLw83bGv18n-VUddju-iukS8cmubFDfEsOEovJfjaIM6pvYjiD_Dq1-dd3uhkvLfbC1aee3T-FJaUlHXVvEwKJrE5v_zN9ivEN_lc5rceT7hyO7FTFZpjVQXxej-MMAxdFnV3qyc8usdQn0olXRAbaiZVOay7byCKsqkcmtx-uzCz7IjhmzDZ_DF2bihWNV3HwDUXlWu_U39uztm_UeUG5UZYaoD-ZCMoEy56hcFxaSWuZeTRGlUGcPJPUe2QOE3EzJiTOAbsdKnNmqIU3UkoEAJbGqcHBr8z2l6St62DV8Vo1qx4YPxRmTPw__yYqGVXr_Dk7_06VGVTt9dZ2AESYhDjDYl0O2DoYjAzxGBoHmQxdZnL7woTAktZ1x6IPlwdClVi14m8MJ2O29tMpRk-yOUXReFbyOS_W8pC63GHBYFEe6kPFVe5QvORIwas5XyAWYzMaN4uqcapO1wAgMHqUr6VPvJ8M4THGoCOoAY_h7HxoMkcx6H5ZGYImcI74lp8q3uM_vqGx5MDLsl06tK_oJggrzAriEHWxpWvfm3NSe4c9SsHl5Gm4fwGVX9O9dtKg9rTorIJHrsENk3dcbyP845LNg0rp_rLbZT9ylWgMbLEok_AkuZz6C5XWV4Upa7VjDVKq516JihpmjDxYNi7yFuaIJs1udmeUSmjNdXIrPFiiWakqg9TSqemMODsPLilIOyWvBzW7rHouvqLy91qpBj25zzF9in-8WSjK47GhbYjPlcZsj3_T7Kf6uZ9HSggOJaYNUkSjcdnGX3hOZNaSLK3lE3LCR-1QpQcnldvDdhXiZgaUjyxe-Zqrd4QAptZicrXaCSvYry6cepo1-85Q4c2Bbddq8yP5ff7Qzqco5Vt3fW9tWISHt6IasCseMf0bkLjKzt_bJ3k9u4t6HPABwRqbuN2gd0FL1QLux_GZIwXthX65lXp18fIJr7x9zEO8qhp_P3zJHu0sZaeEluZW8lPpuGhFDIQE9HZDeoDOylowSSTWHaD7jykSRec2s7Nwr8ZxSWzn4sxebd8tKQtSzRHkc_3mCrH7Fikjf4Duanb7gfaBiXO3CVQQPJ9Um8NPoBWMnW05Kd5kcRchnN5sIq7CgOfuZt5cSJBNB5rKCxmxUdU5qirUeLz94fbM-yzEYHzeCot3Qz5Iht0lxwnAWqINPGnLaOJF4fE68NCvBxIIAZDY0-GaLa9WR7sPeZPeeMzfjM8GBmtGCupgfFM1xL3PQjhzbe89hBbTG1m6mlrVSEORUkJuMjhulQEacUi_Z2scq2zNY_K2KKpcA57dCj4Ke7zh-DRnxIitLSRw9U10wEhSDcPRKmz3VbYH7NwnlazcrAOf2So17hRFxwYB3hlHRX-UtG9dRkHpRzI_wQQaIwwVdoL1UM7Z1ZIkEAuTCNrR3YptLZZR8TV6kwdm1mJB_HNkmYLI2sJwQSuU0Mvz9Lbb-GJvJoWb_Rxb5FV03knKeiPUzjJlI2rOTxdwv-VViB3jszZ-1p5itnPV8OJ_lV3jM2zRMZRqNNHl7hjCtPB_pgtVvAjOCKkAbGvkwGglVGO79fXMEgKvcv4CmXHT_BPEKXB2MffHPokRipqaSyfBUaeHcF0nU9D9yfzIfLBDtGVWDPFpj-vKcMkmz6UBhSz0sCfccH1XTwRFcV05OibqOd836FEv93huwY2nD4Vx6ils8k5sQHrZQWs4cyGTXtm1ps67zrRJ69gAF-4Bx35Okbb2AdzeRcihJEWVqJHih3HJmNAaz99PKw1CcQ4ErE_f6kg0Sf8eg88RQjjRhR8K_1HsnvvzCuz5dn01hMjwpgT9CNQ2uqMHvkEB-zud8lu3]--></g></svg>