# PLANNING.md - Goali Project

This document outlines the high-level vision, architecture, constraints, and technology stack for the Goali project. It serves as a central reference for AI coding assistants and human developers working on this project.

**AI Assistant Instructions:** Always read this document and `README.md` at the start of a new conversation to understand the project's context. Refer to `TASK.md` for specific tasks.

## 1. Project Vision & Goals

- **Core Concept:** Develop an interactive web application, "Goali", that uses a multi-agent AI system to provide personalized guidance and activities based on user interactions and psychological profiles.
- **User Experience:** Create an engaging and supportive environment where users interact with AI agents (e.g., Mentor, Strategist) through chat and activity suggestions (like the activity wheel).
- **Psychological Framework:** Integrate the HEXACO personality model to tailor interactions and agent behavior.
- **Ethical Framework:** Adhere strictly to principles of benevolence, fairness, and transparency in all system design and AI interactions.

## 2. System Architecture

The system employs a client-server model with clear separation of concerns between the frontend (React), backend (Django), and the AI multi-agent system (LangGraph).

- **Frontend (`frontend/src`):**
    - **Framework:** React (v19+) with TypeScript.
    - **Structure:** Organized by feature (`activities`, `chat`, `layout`, `wheel`) and type (`components`, `config`, `contexts`, `hooks`, `services`, `styles`, `types`, `utils`).
    - **Components:** Modular, single-responsibility components within feature directories.
    - **State Management:** Primarily uses React Context (`UserContext`, `WebSocketContext`). Custom hooks (`useActivity`, `useChatMessages`, `useWheel`) encapsulate feature logic.
    - **Backend Communication:** Exclusively via WebSockets, managed by `services/WebSocketManager.ts`. Adheres strictly to `docs/ApiContract.md`.
    - **Styling:** Modular CSS (`styles/global.css` and potentially component-specific styles).
    - **Testing:** Utilizes utilities like `utils/MockWebSocket.ts` for testing WebSocket interactions. The use of the mock vs. real WebSocket is controlled via the `USE_MOCK_WS` flag in `frontend/src/config/environment.ts`, which typically reads from the `REACT_APP_USE_MOCK_WS` environment variable. Frontend tests (`frontend/src/tests/`) verify component logic and conditional rendering (e.g., `App.test.tsx` for the Debug Console).
    - **Debugging:** Includes a staff-only `DebugConsole` component (`components/debug/DebugConsole.tsx`) that displays internal backend messages (`debug_info` type). It uses `DebugContext` (`contexts/DebugContext.tsx`) to receive messages from `WebSocketManager`. Rendering is controlled conditionally in `App.tsx` based on the `is_staff` flag obtained from `UserContext`. See Backend -> Debugging section for details on error propagation.

- **Backend (`backend/`):**
    - **Framework:** Django (Python).
    - **Structure:** Modular design using Django Apps:
        - `main`: Core application logic, including WebSocket consumers (`consumers.py`, `routing.py`), the central `ConversationDispatcher` service (`services/conversation_dispatcher.py`), a centralized `EventService` (`services/event_service.py` for emitting events like `debug_info`), agent definitions (`agents/`), LangGraph workflows (`graphs/`), LLM interactions (`llm/`), background tasks (`tasks/`), and other core services.
        - `activity`: Manages activity data models, services (e.g., `challengingness.py`), and related logic.
        - `user`: Manages user profiles (`UserProfile` model, which has its own ID separate from the standard Django `User` ID), authentication, skill models (`services/skill_service.py`), etc. **Note:** System components like WebSocket consumers typically use the `UserProfile.id` for identification and group naming.
        - `admin_tools`: Provides administrative utilities (views, templates, consumers) such as the WebSocket Tester and Benchmark Dashboard. **Note:** These tools are integrated directly into the main custom Django admin site (`config/admin.py:GameOfLifeAdminSite`) accessible at `/admin/`, rather than being served under a separate URL prefix. The WebSocket tester still uses its specific consumer (`AdminTesterConsumer`) and view logic for functionality.
        - `common`: Shared utilities, base models, or fields.
        - `utils`: Standalone utility scripts.
    - **Database:** Django ORM with a relational database. **Note:** The `docker-compose.yml` configuration sets a `DATABASE_URL` environment variable, typically pointing to a PostgreSQL service. This overrides the default SQLite setting in `settings/dev.py`. Therefore, management commands (`migrate`, `shell`, custom commands) **must** be executed within the appropriate Docker container (e.g., `docker-compose exec web python manage.py <command>`) to interact with the correct database.
        - **Schema Management (Current Strategy - April 15, 2025):** The project currently **does not use Django migrations**. The database schema is defined in `models.py` but managed primarily through **idempotent seeding commands** (`management/commands/seed_db_*.py` and others like `create_benchmark_scenarios.py`). These commands handle table creation implicitly via the ORM and populate initial data. The `main/migrations/` directory is intentionally kept empty. Standard Django migration commands (`makemigrations`, `migrate`) will not work correctly and may produce unexpected results due to the lack of migration history. See `docs/backend/benchmark_schema_inspection.md` for details. A future transition to migrations is possible but would require careful planning and cleanup.
    - **WebSocket Communication:** Handled by Django Channels (`main/consumers.py`, `main/routing.py`), receiving messages and interacting with the `ConversationDispatcher`. Uses `UserProfile.id` for user-specific group names (e.g., `user_session_group_{id}`). The `UserSessionConsumer` checks the connected user's `is_staff` status and forwards `debug_info` messages (received via the channel layer, typically emitted by `EventService`) only to staff users' connections. **Error Handling:** If the `ConversationDispatcher` returns an error status (from `process_message`), the consumer sends a minimal user-facing chat message and relies on the `debug_info` message emitted by the dispatcher (via `EventService`) for details. Exceptions caught directly within the consumer also trigger both a structured `error` message and a detailed `debug_info` message emitted via `EventService`. Errors during dispatcher initialization (e.g., LLM client setup) also now trigger `debug_info` messages via `EventService`.
        - **Asynchronous Response Flow (Debugging Note - April 12, 2025):** Debugging revealed that the initial response from the `ConversationDispatcher` only confirms workflow initiation. The final agent response (e.g., a `chat_message`) is generated within an asynchronous Celery task (e.g., `execute_graph_workflow` calling `run_discussion_workflow`). This final response **must** be explicitly sent back to the user's WebSocket session from within the workflow/task logic, typically using `EventService.emit_event` targeting the specific `session_id`. The `UserSessionConsumer` should *not* send placeholder messages based on the initial dispatcher response, as this can interfere with the display of the final asynchronous message. The consumer's role after dispatch is primarily to handle status updates and forward events received via the channel layer (like the final agent message sent via `EventService`).
    - **Async Operations:** Uses Celery (`config/celery.py`, `main/tasks/`) as the primary mechanism for executing agent workflows (LangGraph graphs). The `ConversationDispatcher` initiates these workflows as asynchronous Celery tasks (`main/tasks/agent_tasks.py`). Crucially, the completed Celery task (or the graph logic itself, like `run_discussion_workflow`) is responsible for sending the final result back to the user via the Channels layer (using `EventService`). Employs `@database_sync_to_async` for safe ORM access from async contexts.
    - **Debugging Environment:**
        - **Containerized:** All backend debugging (Django web server, Celery workers, tests, management commands) occurs *inside* Docker containers using the `debugpy` library (installed via `backend/Dockerfile`).
        - **Services & Ports:**
            - `web` (Django): Debugger listens on port `5678`. Can optionally wait for attachment via `WAIT_FOR_DEBUGGER=true` env var (managed by VS Code tasks).
            - `celery`: Debugger listens on port `5680`.
            - `web-test` (Pytest): Debugger listens on port `5681` and *waits* for client attachment before running tests.
            - Management Commands (via VS Code task): Debugger listens on port `5679` and *waits* for client attachment.
        - **VS Code Integration:** `.vscode/tasks.json` provides tasks to manage the `WAIT_FOR_DEBUGGER` flag and to run/debug tests and management commands within their respective containers. `.vscode/launch.json` (not read, but assumed) likely contains configurations to attach to these ports.
        - **Debugging Observability:** Internal state, errors, and decisions from agents (`LangGraphAgent`) and services (`ConversationDispatcher`) can be captured and sent via the `debug_info` WebSocket message type exclusively to staff users for observability. This is centralized via `EventService.emit_debug_info`. Components like `ConversationDispatcher`, `LangGraphAgent` (in `base_agent.py`), and `UserSessionConsumer` (in its error handling) call this service to emit debug events. The `UserSessionConsumer` then receives these events from the channel layer and forwards them to connected staff clients.
            - **Agent Lifecycle:** The `LangGraphAgent` base class now emits enriched `debug_info` messages at the start and end of its `__call__` method. These messages include contextual details like `workflow_id`, `run_id`, `current_stage`, and a summary of `output_data`, providing better visibility into agent execution steps within the frontend `DebugConsole`.
            - **Error Reporting:** Detailed tracebacks are included in `debug_info` messages for errors caught during dispatcher initialization, message processing, within the consumer, or during agent execution (`LangGraphAgent.__call__`), improving error visibility on the frontend debug console.

- **Multi-Agent System (within `backend/apps/main/`):**
    - **Framework:** LangGraph integrated within the Django `main` app.
    - **Structure:**
        - Agent Logic: Defined in `agents/` (e.g., `mentor_agent.py`, `orchestrator_agent.py`). Agent base class is `LangGraphAgent` in `agents/base_agent.py`.
        - Workflows: Defined in `graphs/` (e.g., `onboarding_graph.py`, `wheel_generation_graph.py`).
    - **Tools:** Agent capabilities defined in `agents/tools/` with clear schemas and registration (`management/commands/cmd_register_tools.py`).
    - **Integration:** WebSocket messages are processed by `main/consumers.py`, which passes the relevant content to the `main/services/conversation_dispatcher.py`. The dispatcher classifies the user's intent using a prioritized strategy (checking message metadata like `type='spin_result'` or `metadata.requested_workflow`, then user profile completion, then LLM/tool/rule-based classification) and then initiates the appropriate workflow as an asynchronous Celery task (`main/tasks/agent_tasks.py`).
    - **LLM Interaction:** Managed via `main/llm/` service layer, often invoked within agent tools or graph nodes.

- **WebSocket API:**
    - **Contract:** The single source of truth is `docs/ApiContract.md`. All communication MUST adhere to this. (Updated April 2, 2025 to reflect async flow).
    - **Frontend Implementation:** `frontend/src/services/WebSocketManager.ts`.
    - **Backend Implementation:** `backend/apps/main/consumers.py` (handles connection, passes messages to dispatcher).
    - **Reliability & Flow:** Frontend includes reconnection logic. Backend uses the `ConversationDispatcher` to classify intent and initiate workflows asynchronously via Celery. The initial response confirms initiation; the final result (e.g., `wheel_data`, agent `chat_message`) is sent later by the completed Celery task via the Channels layer.

- **Agent Benchmarking System:**
    - **Purpose:** Provides a framework for evaluating agent performance, quality, and resource usage under controlled conditions. Helps track improvements and regressions over time.
    - **Core Components:**
        - **Models (`main/models.py`):**
            - `BenchmarkTag`: Allows categorization of scenarios (e.g., by feature, phase).
            - `BenchmarkScenario`: Defines reusable test cases with specific agent roles, input data, `tags` (ManyToManyField to `BenchmarkTag`), and optional `metadata`. Metadata can include:
                - `expected_quality_criteria`: Dictionary mapping dimension names to lists of criteria strings for semantic evaluation (used if `evaluation_template_name` is not specified or found).
                - `evaluation_template_name`: Optional string referencing the `name` of an `EvaluationCriteriaTemplate` to use for semantic evaluation.
                - `mock_tool_responses`: Dictionary for configuring mock tool behavior (see Tool Mocking below).
                - `reference_answers`: Optional list of strings representing ideal/correct answers for comparison during semantic evaluation.
            - `BenchmarkRun`: Records the results of executing a scenario, capturing performance metrics (duration, success rate), operational metrics (tool calls), and detailed semantic evaluation results (potentially multi-dimensional and multi-model).
            - `EvaluationCriteriaTemplate`: Stores reusable templates for semantic evaluation criteria (name, description, criteria JSON). Seeded via `create_benchmark_scenarios` command.
        - **Service (`main/services/benchmark_manager.py`):**
            - `BenchmarkManager`: Orchestrates benchmark execution. Retrieves scenarios, dynamically imports the target agent class, prepares mocks (including processing `mock_tool_responses` from scenario metadata), runs the benchmark using `AgentBenchmarkImproved`, performs semantic evaluation (loading criteria from `EvaluationCriteriaTemplate` if referenced in scenario metadata, otherwise using direct `expected_quality_criteria`), performs statistical comparison with previous runs, and stores findings in `BenchmarkRun`.
            - `SimulatedToolException`: Custom exception raised by the mocking system when a scenario is configured to simulate a tool error.
        - **Benchmark Runner (`main/agents/benchmarking.py`):**
            - `AgentBenchmarkImproved`: Executes the agent multiple times for a scenario, injecting pre-configured mocks (`MockDatabaseService`, `MockToolRegistry`). Catches `SimulatedToolException` and records it as a run error.
        - **Mocking (`main/testing/mock_tool_registry.py`):**
            - `MockToolRegistry`: Mock implementation that accepts configuration via its `config` parameter. The `BenchmarkManager` provides this config, potentially including dynamically generated response functions based on scenario metadata. Logs tool calls and inputs. Catches and re-raises `SimulatedToolException` from configured response functions.
        - **Admin Interface (Integrated into `/admin/`):**
            - **Models:** `BenchmarkTag`, `BenchmarkScenario` (with tag filtering/display), and `EvaluationCriteriaTemplate` are registered.
            - **Views (`admin_tools/views.py`):**
                - `benchmark_dashboard`: Allows running scenarios (with tag filtering) and viewing recent results.
                - `benchmark_history`: Renders the initial history page with filters.
                - `BenchmarkRunView` (API): Provides JSON data for fetching run details (for modal) and fetching filtered run lists and corresponding chart data (used by the history page's dynamic JavaScript filtering). Accepts query parameters (`agent_role`, `tag`, `start_date`, `end_date`, `include_chart_data`).
            - **Templates (`admin_tools/templates/admin_tools/`):**
                - `benchmark_dashboard.html`: UI for running benchmarks.
                - `benchmark_history.html`: UI for viewing history. Includes filter controls (Agent Role, Scenario, Tag, Start Date, End Date) and uses Chart.js. JavaScript fetches data dynamically from `BenchmarkRunView` API based on filters, updating the chart and table without page reload. **Crucially, if a specific scenario is selected via the filter, the chart displays the trend of individual semantic dimensions (e.g., Clarity, Tone) *and* the Mean Duration for that scenario over time, using separate Y-axes. The hover tooltip for this view shows all dimension values, the duration, the LLM model, and the agent version for the specific run.** Otherwise (no specific scenario selected), it displays the aggregated metrics (Mean Duration, Success Rate, Overall Semantic Score). The scenario filter dropdown now also displays the count of existing runs for each scenario (e.g., "Scenario Name (v1) (5 runs)"). Displays overall semantic scores in the table and provides access to full details (including multi-dimensional evaluations) via modal.
            - **URLs:** Integrated into the custom admin site (`config/admin.py`) under `/admin/benchmarks/`.
        - **Management Commands (`main/management/commands/`):**
            - `create_benchmark_scenarios`: Populates `BenchmarkScenario` (with versioning) and `EvaluationCriteriaTemplate` from JSON files or defaults.
            - `run_benchmarks`: Runs benchmarks via CLI, supporting filtering by scenario ID/name, agent role, and tags (`--tags` argument). Also includes a `--generate-report <path>` option to create an HTML summary report.
    - **Tool Mocking Enhancements (April 14, 2025):**
        - **Configurable Responses:** Scenarios can define mock tool responses in `metadata["mock_tool_responses"]`. This maps `tool_code` to:
            - An f-string template representing JSON (e.g., `"{'result': 'ok'}"`). Accesses `tool_input` variable.
            - A list of condition/response pairs (`[{ "condition": "...", "response": "..." }, ...]`). Conditions are Python expressions accessing `tool_input`. The first matching condition's response template is used.
        - **Exception Simulation:** A response template string like `"{'$raise_exception': 'Error message'}"` will cause the mock tool call to raise `SimulatedToolException`.
        - **Input Logging:** `MockToolRegistry` now logs the input parameters for each tool call at the DEBUG level.
    - **Workflow:**
        1. Define scenarios (including optional `mock_tool_responses` and `tags`) using the `create_benchmark_scenarios` command or the Django admin. Manage tags via the admin.
        2. Trigger benchmark runs via the admin dashboard (filtering by tag is available) or the `run_benchmarks` command (using `--tags` filter).
        3. `BenchmarkManager` prepares mocks based on scenario metadata and executes the scenario using `AgentBenchmarkImproved`.
        4. Results are stored in `BenchmarkRun`.
        5. View results and history via the admin dashboard/history pages, the output JSON file (if requested via `--output`), or the generated HTML report (if requested via `--generate-report`).
    - **CI/CD Integration & Reporting (April 15, 2025):**
        - **GitHub Actions Workflow:** A dedicated workflow (`.github/workflows/run-benchmarks.yml`) allows manual triggering of benchmark runs via the GitHub UI.
        - **Manual Triggering:** Supports inputs for filtering by tags (`tags`), scenario IDs (`scenario_ids`), specifying a JSON output path (`output_file`), and specifying an HTML report path (`report_path`).
        - **Execution:** Runs the `run_benchmarks` command within the `web` Docker container using `docker-compose exec`.
        - **HTML Reporting:** If `report_path` is provided, the workflow calls the command with `--generate-report`. The report is generated using `apps.main.utils.benchmark_reporting.generate_html_report` and includes performance and semantic metrics (currently *excluding* token counts - see `TASK.md`).
        - **Artifacts:** The generated HTML report is copied from the container and uploaded as a workflow artifact for easy access.
    - **Configuration Storage (April 15, 2025):**
        - **Scenario Definitions:** Benchmark configurations are stored in Git by committing either:
            - The JSON definition files used with the `create_benchmark_scenarios --file` command.
            - The `create_benchmark_scenarios.py` command itself, which contains the hardcoded default scenario definitions used with `--create-defaults`.
        - **Run Parameters:** Specific parameters for a benchmark execution (like number of runs, LLM model used) are typically passed as arguments to the `run_benchmarks` command or via the GitHub Action inputs, rather than being stored within the scenario definition itself.
    - **Recent Fixes (April 15, 2025):**
        - Resolved an `IntegrityError` where `BenchmarkRun.llm_calls` was null. Ensured this field is populated correctly in `BenchmarkManager._store_results_sync`.
        - Resolved a `ValueError` during statistical comparison save (`BenchmarkManager._perform_statistical_comparison`). The issue was caused by attempting to save a `numpy.bool_` value directly to a `BooleanField` using `queryset.update()`, which failed Django's internal validation. The fix involved explicitly casting the `numpy.bool_` to a standard Python `bool` before the update operation.
    - **Troubleshooting Notes (Benchmarking):**
        - **NumPy Types & `queryset.update()`:** Be cautious when using `queryset.update()` with values derived from NumPy operations. While `FloatField` might handle `numpy.float64` correctly, `BooleanField` can raise a `ValueError` if given a `numpy.bool_`. Explicitly cast NumPy types (especially `numpy.bool_` to Python `bool`) before using them in `update()` calls for nullable `BooleanField`s to avoid validation errors (`value in self.empty_values`).
    - **Future Considerations:**
        - **Testing:** Add comprehensive tests for the `BenchmarkManager`, admin views, management commands, and reporting utilities.
        - **Integration:** Verify and refine the integration between `BenchmarkManager` and the actual agent execution/benchmarking logic (e.g., `AgentBenchmarkImproved`).
        - **Semantic Evaluation:** Implement a more robust semantic evaluation mechanism, potentially using an LLM evaluator.
        - **CI/CD Automation:** Fully integrate benchmark runs into the CI/CD pipeline (e.g., automated runs on PRs or schedules) beyond the current manual trigger.
        - **Token Tracking:** Implement token usage tracking within the benchmark runner and reporting (see `TASK.md`).

## 3. Technology Stack

- **Backend:** Python 3.x, Django, Django Channels, LangGraph, Celery, Pytest.
- **Frontend:** TypeScript, React 19+, Webpack, Jest/React Testing Library, Cypress.
- **Database:** PostgreSQL (production), SQLite (development/testing).
- **AI:** LangChain/LangGraph library, specific LLM providers configured via `backend/apps/main/llm/`.
- **Infrastructure:** Docker, Docker Compose (`backend/docker-compose.yml`, `backend/Dockerfile`).
- **Documentation:** Markdown, PlantUML (for diagrams in `docs/model/`).

## 4. Development Principles & Constraints

- **Modularity:** Keep files under 500 lines. Refactor aggressively into smaller modules or helper functions/classes.
- **Testing:**
    - **Backend:** Mandatory Pytest unit tests for all new features (expected use, edge case, failure case). Mock external dependencies (DB, LLMs). Tests reside in corresponding `/tests` directories.
    - **Frontend:** Unit tests for logic, component tests, and WebSocket integration tests (using mocks).
    - **Updates:** Ensure existing tests in `backend/apps/*/tests/` and `frontend/tests/` are updated when related logic changes.
    - **Running Tests (Docker):**
        - **Command Structure:** To run backend tests within the Docker environment, use the following pattern from the *project root* directory (`d:/The Game/repo`):
          ```bash
          cd backend && docker-compose exec -T -w /usr/src/app web pytest <path_to_test_file_or_dir>
          ```
        - **Working Directory:** The working directory inside the `web` container is `/usr/src/app`.
        - **Test Paths:** The `<path_to_test_file_or_dir>` must be relative to the container's working directory (`/usr/src/app`). For example, to run the benchmark tests: `apps/main/tests/test_management/test_run_benchmarks.py`.
        - **Test Service:** Note that there is also a `web-test` service defined in `docker-compose.yml` specifically for debugging tests, which uses a different entry point and environment variables. Standard test execution typically uses the `web` service as shown above.
    - **Testing Async Management Commands:** Testing async management commands (like `run_benchmarks.py`) that involve awaiting other async methods (especially those from mocked dependencies) presents challenges with standard mocking techniques within the `pytest-asyncio` environment.
        - **Problem:** Directly asserting `await_count` or `call_count` on `AsyncMock` objects (created via `mocker.patch`, `mocker.patch.object`, or attached to mock instances) often fails (`count` remains 0) when the command's core async method is `await`ed directly within the test. This suggests the patch is not reliably intercepting the call within the command's async execution context. Standard strategies like patching the class vs. the instance, or patching where imported vs. where defined, do not consistently resolve this. Using `django.core.management.call_command` is also generally unsuitable for testing the internal async flow.
        - **Recommended Strategy (April 14, 2025):**
            1.  **Focus Command Tests:** Command-level tests (`tests/test_management/`) should primarily verify the command's own logic: argument parsing, database filtering (if applicable), instantiation of services/managers, handling of return values or exceptions from dependencies, and generation of correct stdout/stderr/file output.
            2.  **Mock Dependencies:** Patch the command's direct dependencies (e.g., service classes like `BenchmarkManager`) where they are *imported* in the command module (e.g., `apps.main.management.commands.run_benchmarks.BenchmarkManager`). Use `return_value` to provide a `MagicMock` instance.
            3.  **Configure Mock Behavior:** Configure the behavior of methods on the *mock instance* (e.g., set `side_effect` or `return_value` for `mock_manager_instance.run_benchmark`). Use `AsyncMock` for async methods on the mock instance if needed for configuration, but avoid asserting its counts.
            4.  **Instantiate & Run:** Instantiate the command class directly in the test and `await` its core async method (e.g., `await command.run_benchmarks(...)`).
            5.  **Assert Command Logic:** Assert that the dependency class was instantiated (`mock_dependency_cls.assert_called_once()`). Assert that the relevant method *on the mock instance* was called with the expected arguments (`mock_instance.method.assert_called_once_with(...)` or `assert_has_calls(...)`). **Crucially, do not assert `call_count > 1` or `await_count` on the mocked async method within this test context.** Verify the command's output (stdout, stderr, file content) based on the configured return values/side effects of the mocks.
            6.  **Defer Async Internal Testing:** Defer the testing of the *internal* async behavior of the dependency (e.g., ensuring `BenchmarkManager.run_benchmark` correctly `await`s its own internal calls) to dedicated service-level tests (e.g., `tests/test_services/test_benchmark_manager.py`), where mocking interactions are generally more reliable.
        - **Database Interaction:** For async tests involving database fixtures, ensure proper transaction handling by adding `transaction=True` to the `@pytest.mark.django_db` decorator (e.g., `@pytest.mark.django_db(transaction=True)`). This helps ensure data created by fixtures is visible within the async test's execution context.
- **Serialization:**
    - **JSON:** Standard `json.dump`/`json.dumps` cannot serialize non-standard types like `uuid.UUID` or `datetime` objects directly. Explicitly convert these to strings (e.g., `str(my_uuid)`, `my_datetime.isoformat()`) before attempting serialization.
    - **Mocking File Writes:** When mocking `builtins.open` and testing code that writes JSON with indentation (`json.dump(..., indent=...)`), the mock file handle's `write` method may be called multiple times. To verify the complete written content, capture `mock_file_handle.write.call_args_list` and join the string arguments from all calls before attempting `json.loads`.
- **Documentation:**
    - **Docstrings:** Google style for Python functions/classes. JSDoc/TSDoc for frontend.
    - **Inline Comments:** Use `# Reason:` for complex Python logic, `// Reason:` for TS/JS.
    - **Core Docs:** Maintain `README.md`, `PLANNING.md`, `TASK.md`, `AI_CODING_INSTRUCTIONS.md`.
    - **API Contract:** `docs/ApiContract.md` is critical and must be kept up-to-date.
    - **Diagrams:** Use PlantUML for data models (`docs/model/*.puml`) and potentially agent flows.
    - **Component/Tool Docs:** Document new React components, agent tools (`backend/apps/main/agents/tools/`), and WebSocket message handlers.
- **Task Management:** Use `TASK.md` for granular task tracking. Update status promptly. Add discovered tasks under a dedicated section.
- **Data Seeding:** Utilize management commands (`backend/apps/main/management/commands/seed_db_*.py`) for setting up development/testing data.
- **Style Conventions:**
    - **Python:** PEP8, type hints.
    - **TypeScript:** Strict typing, interfaces, avoid `any`.
    - **React:** Functional components, hooks, error boundaries.
- **AI Assistant Interaction:**
    - Start fresh conversations often.
    - One task per message.
    - Be specific, provide context and examples.
    - Verify file paths and imports.
    - Do not assume context; ask clarifying questions.
    - Never hallucinate libraries or functions.
    - Do not delete/overwrite code unless instructed or part of a `TASK.md` item.
- **Security:** Validate all inputs (user, agent, LLM), handle sensitive data securely, follow secure reconnection patterns.
    - **Environment Variables:** Manage secrets and environment variables manually; do not rely on the AI assistant for this.
    - **Container Startup & Initialization (Updated April 15, 2025):**
        - **`entrypoint.sh` Order:** The `backend/entrypoint.sh` script defines the container startup sequence. It's crucial that migrations are generated and applied *before* any seeding or setup commands that rely on the database schema.
        - **Migration Handling (`reset_db.py` Workflow):** The project supports a workflow where `backend/apps/utils/reset_db.py` is used to clear the database and migration files for a fresh start (without committing migration files). To support this:
            1.  `entrypoint.sh` runs `python manage.py makemigrations --noinput` (without specifying an app) first. This detects model changes across *all* apps and generates the necessary migration files (e.g., `0001_initial.py` for each app after a reset).
            2.  `entrypoint.sh` then runs `python manage.py migrate --noinput`. This applies the newly generated migrations in the correct order based on dependencies (e.g., ensuring `user` app tables exist before `main` app tables that reference them).
            3.  This sequence prevents `ProgrammingError` (table does not exist) during subsequent steps like seeding.
            - **Idempotent Seeding/Setup (`run_seeders`):**
            - **Problem:** Running all seeding and setup commands on every container start is slow and can cause errors (e.g., unique constraint violations).
            - **Solution:** A dedicated management command `backend/apps/main/management/commands/run_seeders.py` orchestrates seeding and setup idempotently using the `main.AppliedSeedingCommand` model to track completed commands. This command employs a **double-level idempotency pattern**:
              - **Top-Level Idempotency:** The `run_seeders.py` command checks `AppliedSeedingCommand` before calling an individual seeder. If a record exists, it skips the `call_command` entirely. If it runs the command successfully, it creates the `AppliedSeedingCommand` record.
              - **Individual Seeder Idempotency:** Individual `seed_db_*.py` commands keep their initial check (if `AppliedSeedingCommand.objects.filter(...).exists(): return`). This allows `run_seeders` to skip them if they've already been fully completed once. Their internal logic (e.g., `seed_hexaco_traits`, `create_or_update_environment`) still uses methods like `get_or_create`, `update_or_create`, or `filter().exists()` to avoid duplicating individual items (traits, domains, environments, etc.) if the command happens to be run multiple times for some reason (e.g., manual execution, or if the top-level record was deleted). **This pattern is used in `seed_db_phiphi.py`.**
            - **Implementation:** `entrypoint.sh` calls `python manage.py run_seeders` after migrations are applied. The `run_seeders` command iterates through a predefined list (`SEEDING_COMMANDS_WITH_ARGS`) containing commands like `seed_db_*`. It checks `AppliedSeedingCommand` before running each command (using the full command string with args as the key) and records success within a transaction.
            - **Benefits:** Ensures seeding/setup commands run only once successfully, speeds up subsequent container startups, makes the process more robust.
            - **Other Startup Commands (April 15, 2025):** Commands like `cmd_register_tools`, `cmd_tool_connect --reset`, and `create_benchmark_scenarios` are executed *directly* by `entrypoint.sh` after `run_seeders`. This is intentional. While `run_seeders` handles initial data seeding idempotently, these other commands are responsible for synchronizing database state with the current code definitions or external files on *every* startup. They have their own internal idempotency mechanisms (hash comparison, reset+add, versioning) and should not be moved into `run_seeders`, as that could prevent necessary updates after code or configuration changes.
    - **Async Database Access:** When agents or other components running in an async context (e.g., Channels consumers, Celery tasks) need to interact with the synchronous Django ORM:
        - **Direct Calls:** Wrap direct synchronous ORM calls (e.g., `Model.objects.get()`, `instance.save()`) using `asgiref.sync.sync_to_async(..., thread_sensitive=True)` or `channels.db.database_sync_to_async`.
    - **Related Fields/Lazy Loading:** Accessing related fields (e.g., `instance.related_set.all()`) can trigger lazy database queries. To prevent `SynchronousOnlyOperation` errors in these cases:
        - **Eager Loading:** Use `select_related()` or `prefetch_related()` in the initial *synchronous* query (within the `sync_to_async` block) if possible.
        - **Async Helper Methods:** If eager loading isn't feasible or efficient, create dedicated `async` methods (e.g., in a service layer like `DatabaseService`) that internally use `database_sync_to_async` to fetch and process the related data, returning the final result. Agents should then `await` these async helper methods. **Important:** Ensure the methods decorated with `@database_sync_to_async` contain *all* the necessary synchronous ORM operations. Calling other synchronous methods (even other service methods) from within the decorated async method can still lead to `SynchronousOnlyOperation` if those called methods perform DB access without their own async wrapper. (Resolved in `RealDatabaseService` for `start_run`, `complete_run`, etc.).
    - **Agent Initialization:** Avoid synchronous database calls in agent `__init__` methods if the agent might be instantiated in an async context. Instead, load necessary data (like agent definitions or tools) lazily within an `async` method (e.g., `_ensure_loaded`) called at the beginning of the agent's main `async process` method. This `_ensure_loaded` method should use the async patterns described above for its database calls. (See `MentorAgent` for an example implementation).
    - **Error Handling & Reporting:** Exceptions occurring within async workflows (like agent execution in Celery tasks) might be caught at different levels. To ensure detailed errors reach the frontend debug console:
        - Agent-level exception handlers (e.g., in `LangGraphAgent.__call__`) should capture tracebacks and emit detailed `debug_info` events via `EventService`.
        - Higher-level handlers (e.g., in Celery tasks like `execute_graph_workflow` in `agent_tasks.py`) should *also* capture tracebacks and emit `debug_info` events using `EventService.emit_event_sync` (since tasks run synchronously) to report errors that occur before or outside the agent's specific handler. This provides comprehensive error visibility.
    - **Refined Async DB Pattern (April 13, 2025):** Avoid calling `async_to_sync` from within a function already decorated with `@database_sync_to_async`. This can lead to deadlocks. Instead:
        - Keep `@database_sync_to_async` blocks focused on the necessary synchronous ORM operations.
        - If an async operation (like another service call or `set_memory`) needs to happen after a sync DB update within the same logical step, perform the sync update first using `await some_sync_db_call(...)`, then perform the subsequent async operations using `await some_async_call(...)` directly in the main async context. (See `RealDatabaseService.complete_run` refactoring for an example).

- **Troubleshooting:**
    - **Custom Admin Index `app_label` Error:** If you encounter a `VariableDoesNotExist: Failed lookup for key [app_label]` error on the main `/admin/` page, it's often due to how the custom `AdminSite.index` method (`config/admin.py`) interacts with the template rendering (`templates/admin/base_site.html`, `templates/admin/index.html`). The fix usually involves ensuring the parent `super().index()` is called *first* to establish the base context (including `app_list`), and *then* updating the response context with custom variables. Directly passing a merged context to `super().index()` can sometimes fail. See commit history around April 12, 2025 for the fix pattern.
    - **Custom Admin `NoReverseMatch` Errors (Namespaces & Templates):** When encountering `NoReverseMatch` errors within custom admin views or templates (e.g., for `admin_tools` pages like `/admin/benchmarks/history/`):
        1.  **Verify Namespace:** Check the custom `AdminSite` instantiation in `config/admin.py`. If it uses a `name` (e.g., `GameOfLifeAdminSite(name='game_of_life_admin')`), ensure *all* `{% url %}` tags within the admin site's templates (including base templates and custom view templates) use this specific namespace (e.g., `{% url 'game_of_life_admin:my_view_name' %}`).
        2.  **Check Template Inheritance:** Ensure custom admin templates (e.g., `admin_tools/benchmark_history.html`) correctly extend the intended base template, typically `admin/base_site.html`, not just `admin/base.html`.
        3.  **Avoid Overriding Core Blocks:** Be cautious about overriding fundamental blocks like `breadcrumbs` in custom templates. If overridden, ensure the logic correctly handles context variables (like `opts`) and uses the proper admin namespace for any generated URLs. It's often better to let the corrected base template handle these blocks.
        4.  **Context Variables (`opts`):** For custom admin views that don't correspond to a specific model, ensure the `opts` context variable is explicitly set to `None` in the view function. The base templates (like `admin/base_site.html`) should handle this `None` case gracefully, often skipping parts of the breadcrumb generation that rely on `opts`.
        5.  **URL Tag `silent` Keyword:** While `{% url ... silent %}` can suppress errors for URLs that don't exist (like `app_list` for apps without registered models), it might not prevent `NoReverseMatch` if the fundamental issue is an incorrect namespace or context problem during the resolution attempt itself. Focus on fixing the namespace and template inheritance first.

## 5. Key Project Files & Documentation (Non-Exhaustive)

- **Root:** `README.md`, `PLANNING.md` (this file), `TASK.md`, `AI_CODING_INSTRUCTIONS.md`, `docker-compose.yml`, `Dockerfile` (backend).
- **Documentation:**
    - `docs/ApiContract.md`: **Crucial** WebSocket definition.
    - `docs/agents/`: Agent descriptions, flows, etc.
    - `docs/model/`: Data model diagrams and descriptions.
    - `docs/global/`: High-level project concepts.
    - `goali-governance/`: Contains the project's governance system documents (values, standards, decision logs, operational files).
- **Backend Core (`backend/apps/main/`):**
    - `consumers.py`: WebSocket connection handler.
    - `agents/`: Agent class definitions (including `base_agent.py`).
    - `agents/tools/`: Agent tool definitions.
    - `graphs/`: LangGraph workflow definitions.
    - `services/`: Business logic services (dispatcher, DB access).
    - `tasks/`: Celery task definitions.
    - `tests/`: Backend unit/integration tests.
- **Frontend Core (`frontend/src/`):**
    - `App.tsx`: Main application component.
    - `index.tsx`: Application entry point.
    - `components/`: Reusable UI components, organized by feature.
    - `contexts/`: React Context providers (WebSocket, User).
    - `hooks/`: Custom React hooks.
    - `services/WebSocketManager.ts`: WebSocket communication handler.
    - `types/api.ts`: TypeScript types matching the API contract.
    - `tests/`: Frontend unit/integration tests.


## 6. Admin Tools URL Integration and Naming Consistency (Added April 15, 2025)
- The `admin_tools` app URLs are integrated directly into the custom Django admin site via the `get_urls()` method in `backend/config/admin.py` (`GameOfLifeAdminSite` class).
- This integration means the URLs are accessible under the `/admin/` path with the namespace `game_of_life_admin`.
- URL pattern names must be consistent with references in templates and tests to avoid `NoReverseMatch` errors.
- A recent issue was caused by a mismatch between the URL pattern names (`benchmark_run_xxx` vs. `benchmark_runs_xxx`) and their references in templates and tests.
- The fix involved renaming the URL patterns in `backend/config/admin.py` to use plural forms (`benchmark_runs_api` and `benchmark_runs_detail_api`) and updating all template references accordingly.
- This ensures that reverse URL lookups succeed and the admin pages display correctly without errors.


This document provides the foundational plan. Specific implementation details are found within the codebase, associated READMEs (e.g., `backend/README_DEV.md`), and tracked granularly in `TASK.md`.
