# KNOWLEDGE_INFRA.md - Infrastructure Quick Reference

## Core Architecture
- **Backend**: Django 5.2+ (Python), Celery, Channels (WebSockets), LangGraph
- **Frontend**: React with TypeScript
- **Database**: PostgreSQL (dev/prod/test)
- **Container**: Docker/Docker Compose (development and CI)
- **CI/CD**: GitHub Actions workflows

## Development Environment (Docker)
- **Key Services**:
  - `web`: Django application server (debug port: 5678)
  - `db`: PostgreSQL database
  - `redis`: For Celery/Channels
  - `celery`: Celery worker (debug port: 5680)
  - `web-test`: Test runner service
  - `debug-tests`: Test service with debugger (port: 5681)

## Key Configuration Files
- `backend/docker-compose.yml`: Service definitions
- `backend/config/settings/`: Django settings (`base.py`, `dev.py`, `test.py`)
- `backend/requirements.txt`: Python dependencies
- `pyproject.toml`: pytest configuration
- `.vscode/launch.json` & `.vscode/tasks.json`: VSCode integration
- `.github/workflows/`: CI workflow definitions
- `backend/entrypoint.sh`: Container startup script
- `backend/conftest.py`: Pytest fixtures and configuration
- `backend/ultimate_test_setup.py`: Test DB initialization

## Database Schema Management (Critical!)
- Project does NOT use standard Django migrations
- Schema defined in `models.py` files
- Managed through idempotent seeding commands (`seed_db_*.py`)
- `AppliedSeedingCommand` model tracks applied seeders
- `setup_test_env.sh` or `ultimate_test_setup.py` for test DB initialization
- `backend/apps/utils/reset_db.py` for dev environment reset

## Testing Infrastructure
- **Framework**: pytest with Django integration
- **Database**: PostgreSQL
- **Key Files**:
  - `ultimate_test_setup.py`: Consolidated test environment setup
  - `conftest.py`: Pytest fixtures and configuration
  - `config/settings/test.py`: Django settings for test environment

## Test Execution
- **Docker** (recommended): 
  ```bash
  docker compose run --rm web-test
  # Specific tests
  docker compose run --rm web-test python -m pytest path/to/test_file.py
  # With markers
  docker compose run --rm web-test python -m pytest -m "tool"
  ```
- **Debug Tests**: 
  ```bash
  # Docker + specific tests + debugger
  PYTEST_ARGS="apps/main/tests/file.py" docker compose run --rm --service-ports debug-tests
  # Then attach via VSCode's "Django: Debug Test" configuration
  ```
- **Local** (non-Docker):
  ```bash
  cd backend
  python ultimate_test_setup.py
  python -m pytest --reuse-db
  ```
- **VSCode Integration**: Pre-configured launch configurations

## Test Organization
```
backend/apps/
  └── main/
      └── tests/
          ├── test_agents/       # Tests for agent functionality
          ├── test_tools/        # Tests for agent tools
          ├── test_flows/        # Tests for workflow flows
          ├── test_integration/  # Integration tests
          └── test_services/     # Tests for services
```

## Key Testing Features
- **Markers**: `asyncio`, `django_db`, `tool`, `test_type("unit|integration|workflow")`
- **Database Fixtures**: `db`, `transactional_db`, plus custom fixtures
- **Test Data**: Minimal seeded data via direct imports, not commands

## Key Environment Variables
- `DJANGO_SETTINGS_MODULE`: Selects settings profile
- `TESTING`: Enables test mode
- `USE_REAL_LLM`: Enables real LLM API calls in tests
- `WAIT_FOR_DEBUGGER`: Django container waits for debugger attachment
- `CELERY_BROKER_URL`: Redis connection for Celery
- `TEST_DB_INITIALIZED`: Set by setup scripts to coordinate with pytest
- `PYTEST_ARGS`: Pass specific test paths to debug-tests service

## GitHub Actions Workflows
- `.github/workflows/test-and-coverage.yml`: Main test workflow
- `.github/workflows/test-backend.yml`: Backend-specific tests
- `.github/workflows/test-frontend.yml`: Frontend tests
- `.github/workflows/run-benchmarks.yml`: Agent benchmarking
- `.github/workflows/deploy.yml`: Deployment workflow
- `.github/workflows/docker-build.yml`: Docker image building

## Common Issues & Solutions
- **"No such table" Errors**:
  - Run `python ultimate_test_setup.py` for proper DB initialization
  - Ensure test uses `db` fixture
  - Use `--create-db` flag for fresh DB
- **Tool Registration Problems**:
  - Use direct Python imports, not management commands
  - Verify module paths in test environment
  - Run diagnostic checks: `python diagnostic_tools.py`
- **WebSocket Testing**:
  - Use `channels.testing.WebsocketCommunicator` for consumer tests
  - Mock `channel_layer` for async event testing
- **Docker Volume Permissions**:
  - Check `.env` file permissions
  - Use consistent UID/GID in Docker config