codecov:
  require_ci_to_pass: true

coverage:
  precision: 2
  round: down
  range: "70...100"
  status:
    project:
      default:
        target: auto  # auto compares coverage to the previous base commit
        threshold: 1% # allow coverage to drop by 1% without failing
    patch:
      default:
        target: 80%  # require coverage of new code to be at least 80%

parsers:
  gcov:
    branch_detection:
      conditional: yes
      loop: yes
      method: no
      macro: no

comment:
  layout: "reach,diff,flags,files,footer"
  behavior: default
  require_changes: false

ignore:
  - "frontend/tests/**/*"
  - "backend/apps/*/migrations/*"