# Frontend Development Specifications: Wheel Component and Chat Area

## 1. Introduction

This document outlines the technical specifications for developing the "Wheel Component" and "Chat Area" of the Game of Life AI system's frontend. The target developer is assumed to have limited experience, so clarity, detailed data models, and best practices are emphasized. The components must be flexible, maintainable, and include comprehensive unit tests. The chosen technology stack is Next.js, ensuring compatibility across web browsers and mobile devices.

## 2. Architecture Overview

The Game of Life AI system utilizes a multi-agent architecture with real-time communication, primarily via WebSockets. The frontend acts as a WebSocket client, sending user inputs and receiving responses.

**Key Data Flow (Frontend-Relevant):**

1.  **Frontend WebSocket Client**:
    *   Establishes and maintains WebSocket connection.
    *   Sends user messages (`chat_message`, `spin_result`, `workflow_status_request`).
    *   Processes and displays responses (`chat_message`, `system_message`, `processing_status`, `wheel_data`, `error`, `workflow_status`, `activity_details`).
    *   Renders UI elements (wheel, chat messages, activity details).

2.  **Backend (Simplified View)**:
    *   `UserSessionConsumer`: Manages WebSocket connections, routes messages, formats responses.
    *   `ConversationDispatcher`: Analyzes user input, classifies workflows, initiates Celery tasks.
    *   `Agent Workflow System`: Processes requests, generates responses.
    *   `WorkflowResultHandler`: Processes completed workflow results and delivers to clients.

## 3. Frontend Components

### 3.1. Chat Area

**Responsibilities:**
*   Display incoming and outgoing chat messages.
*   Allow users to input text messages.
*   Display system notifications and processing status updates.
*   Handle scrolling for new messages.

**UI/UX Considerations:**
*   Clear distinction between user, assistant, and system messages.
*   Input field for text messages, with a send button.
*   Automatic scrolling to the bottom for new messages.
*   Visual indicators for message processing (e.g., "typing..." or loading spinner).

**Data Structures (Input/Output Messages):**

*   **Client → Server (`chat_message`)**:
    ```typescript
    interface ClientChatMessage {
      type: "chat_message";
      message: string; // User's text message
      user_profile_id: string; // UUID of the user profile
    }
    ```

*   **Server → Client (`chat_message`)**:
    ```typescript
    interface ServerChatMessage {
      type: "chat_message";
      content: string; // Text response from agent
      is_user: boolean; // True if the message originated from the user (echoed back), false for agent response
    }
    ```

*   **Server → Client (`system_message`)**:
    ```typescript
    interface SystemMessage {
      type: "system_message";
      content: string; // System notification (e.g., "Connecting...", "Workflow started")
    }
    ```

*   **Server → Client (`processing_status`)**:
    ```typescript
    interface ProcessingStatus {
      type: "processing_status";
      status: string; // Current processing state (e.g., "thinking", "fetching_data", "generating_response")
    }
    ```

*   **Server → Client (`error`)**:
    ```typescript
    interface ErrorMessage {
      type: "error";
      content: string; // Error message
    }
    ```

**Communication Protocol:**
*   Uses WebSockets for real-time communication.
*   Sends `ClientChatMessage` when the user submits a message.
*   Listens for `ServerChatMessage`, `SystemMessage`, `ProcessingStatus`, and `ErrorMessage` from the WebSocket.

### 3.2. Wheel Component

**Responsibilities:**
*   Display the interactive activity wheel with various segments.
*   Allow users to "spin" the wheel.
*   Visually indicate the selected activity after a spin.
*   Display detailed information about the selected activity.

**UI/UX Considerations:**
*   Visually appealing wheel with distinct segments.
*   Clear "Spin" action/button.
*   Animation for spinning and landing on a segment.
*   Display area for `ActivityDetails` (e.g., a modal or a dedicated section).

**Data Structures (Input/Output Messages):**

*   **Client → Server (`spin_result`)**:
    ```typescript
    interface SpinResult {
      type: "spin_result";
      activity_tailored_id: string; // UUID of the selected tailored activity
      name: string; // Name of the selected activity
    }
    ```

*   **Server → Client (`wheel_data`)**:
    ```typescript
    interface WheelData {
      type: "wheel_data";
      wheel: Wheel; // See Shared Data Models for Wheel structure
    }
    ```

*   **Server → Client (`activity_details`)**:
    ```typescript
    interface ActivityDetailsMessage {
      type: "activity_details";
      details: ActivityTailored; // See Shared Data Models for ActivityTailored structure
    }
    ```

**Communication Protocol:**
*   Receives `wheel_data` from the WebSocket to populate the wheel.
*   Sends `spin_result` to the WebSocket when the user selects an activity.
*   Receives `activity_details` from the WebSocket to display information about the selected activity.

## 4. Shared Data Models

These models represent the core data structures exchanged between the frontend and backend, derived from Django models.

### 4.1. UserProfile

Represents the core user profile.

```typescript
interface UserProfile {
  id: string; // UUID of the user profile
  profile_name: string; // User's display name
  // Other relevant fields from Django's UserProfile model can be added as needed
}
```

### 4.2. ActivityTailored

Represents a personalized activity tailored for a specific user. This is the detailed object sent for `activity_details`.

```typescript
interface ActivityTailored {
  id: string; // UUID of the tailored activity
  user_profile: UserProfile; // The user this activity is tailored for
  generic_activity_id: string; // UUID of the base generic activity
  name: string; // Name of the tailored activity (e.g., "Farm Boundary Exploration Walk")
  description: string; // Detailed description
  instructions: string; // Step-by-step instructions
  base_challenge_rating: number; // Overall difficulty (0-100)
  challengingness: { // JSON mapping challenge levels across personality dimensions
    openness: number;
    conscientiousness: number;
    extraversion: number;
    agreeableness: number;
    neuroticism: number;
  };
  version: number; // Sequential version number
  tailorization_level: number; // How customized it is (0-100)
  // Relationships (simplified for frontend consumption)
  domain_relationships: Array<{
    domain_code: string;
    strength: number; // 0-100
  }>;
  tags: string[]; // List of tag names
  // Other fields from ActivityTailored as needed
}
```

### 4.3. Wheel

Represents the overall wheel configuration.

```typescript
interface Wheel {
  name: string; // Name of the wheel (e.g., "Philipp's Daily Challenge Wheel - Foundation Phase")
  created_by: string; // Identifier for the agent that generated it
  created_at: string; // Date when the wheel was created (ISO 8601 string)
  items: WheelItem[]; // Array of segments on the wheel
}
```

### 4.4. WheelItem

Represents a single segment on the wheel, linked to a tailored activity.

```typescript
interface WheelItem {
  id: string; // Unique identifier for this segment
  percentage: number; // Probability weight (0.0-100.0)
  activity_tailored_id: string; // UUID of the associated ActivityTailored
  // Potentially include a simplified version of ActivityTailored here if full details aren't always needed
  // e.g., activity_name: string; activity_description: string;
}
```

## 5. Non-Functional Requirements

### 5.1. Technology Stack

*   **Framework**: Next.js (for React-based development, SSR/SSG capabilities, and API routes if needed).
*   **Language**: TypeScript (for strong typing, improved maintainability, and reduced bugs).
*   **Styling**: Choose a modern CSS-in-JS library (e.g., Styled Components, Emotion) or a utility-first framework (e.g., Tailwind CSS) for flexible and maintainable styling.
*   **State Management**: React Context API or a lightweight library like Zustand/Jotai for local component state. For global state, consider Redux Toolkit if complexity demands, but start simple.
*   **WebSocket Client**: Use a robust WebSocket library (e.g., `ws` for Node.js environments, or native `WebSocket` API in browsers, potentially wrapped in a custom hook).

### 5.2. Development Principles

*   **Modularity and Reusability**: Components should be small, focused, and reusable. Separate UI logic from business logic.
*   **Maintainability**: Write clean, well-commented, and self-documenting code. Follow consistent coding styles (e.g., ESLint, Prettier).
*   **Flexibility**: Design components to be easily adaptable to future changes in data models or UI requirements. Avoid hardcoding values where possible.
*   **Performance**: Optimize for fast loading times and smooth user interactions. Implement lazy loading for components/data where appropriate.
*   **Error Handling**: Implement robust error handling for all API calls and WebSocket communication. Provide clear, user-friendly error messages. Log errors effectively for debugging.

### 5.3. Testing

*   **Unit Tests**: All components and utility functions must have comprehensive unit tests.
    *   Use a testing framework like Jest or Vitest with React Testing Library.
    *   Aim for high code coverage (e.g., >80%).
    *   Test component rendering, user interactions, and data transformations.
*   **Integration Tests**: Test the interaction between components and the WebSocket communication.
*   **End-to-End Tests**: Consider Playwright or Cypress for critical user flows.

### 5.4. Mobile Responsiveness

*   The Next.js application must be fully responsive and provide an optimal user experience on both desktop web browsers and mobile devices.
*   Utilize responsive design techniques (e.g., CSS media queries, flexible layouts).

### 5.5. Documentation

*   Maintain clear and concise documentation for components, props, and data flows.
*   Update `SPECS.md` as the project evolves.
*   Use JSDoc or TypeScript comments for inline code documentation.
