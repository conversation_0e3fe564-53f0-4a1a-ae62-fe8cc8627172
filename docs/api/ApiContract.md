# WebSocket API Contract

## Overview

This document defines the WebSocket API contract between the Frontend WebSocket Manager and Backend UserSessionConsumer for the Game of Life coaching system. It provides technical specifications for all message types, formats, and behaviors required for the MVP implementation.

## Connection Details

### Connection Endpoint

```
ws://[hostname]:[port]/ws/game/
```

In deployment, <PERSON><PERSON><PERSON> uses <PERSON><PERSON>orn as the ASGI server to handle WebSocket connections. The connection endpoint remains consistent regardless of the server implementation.

### Connection Lifecycle

1. **Connection Establishment**:
   ```
   C<PERSON> initiates WebSocket connection → Server accepts → Server adds client to session group → Server sends welcome message
   ```

2. **Session Identification**:
   - Sessions are managed via a unique session identifier: `client_session_[uuid]`
   - User identity is established via `user_profile_id` included in message payloads

3. **Disconnection Handling**:
   - Client should implement reconnection with exponential backoff (starting at 2s)
   - Maximum reconnection attempts: 5
   - Server cleans up session resources on disconnect

## Message Format

All WebSocket messages conform to the following JSON structure:

```json
{
  "type": "message_type",
  "content": {
    // Type-specific payload
  }
}
```

Additional root-level properties may be included for certain message types.

## Message Types

### Client → Server Messages

#### 1. Chat Message

User sends a text message for processing by the agent system.

```json
{
  "type": "chat_message",
  "content": {
    "message": "string",
    "user_profile_id": "string", // Unique identifier for the user
    "timestamp": "string", // Optional: ISO-8601 formatted timestamp
    "metadata": { // Optional: Additional context or routing hints
      "requested_workflow"?: "wheel_generation" | "pre_spin_feedback" | "activity_feedback" | "discussion"
      // Other potential metadata keys TBD
    }
  }
}
```

**Required Fields:**
- `message`: The user's message text.
- `user_profile_id`: Unique identifier for the user.

**Optional Fields:**
- `timestamp`: ISO-8601 formatted timestamp.
- `metadata`: Additional context information (object).
  - `requested_workflow`: (Optional) Explicitly requests a specific backend workflow, bypassing complex classification. Useful for UI elements directly triggering a workflow. Allowed values:
    - `"wheel_generation"`: Request a new activity wheel.
    - `"pre_spin_feedback"`: Provide feedback on the currently displayed wheel *before* spinning.
    - `"activity_feedback"`: Provide feedback on a *previously completed* activity.
    - `"discussion"`: Intend to have a general chat.
    - `"onboarding"`: User onboarding workflow.
    - `"post_spin"`: Post-activity selection workflow.
    - `"post_activity"`: Post-activity completion workflow.

#### 2. Spin Result

User selects an activity from the activity wheel.

```json
{
  "type": "spin_result",
  "content": {
    "activity_tailored_id": "string", // Renamed from activity_id
    "name": "string",
    "description": "string", // Optional, but often included
    "user_profile_id": "string"
  }
}
```

**Required Fields:**
- `activity_tailored_id`: Unique identifier for the selected `ActivityTailored` object
- `name`: Display name of the activity
- `user_profile_id`: Unique identifier for the user

**Optional Fields:**
- `description`: Activity description (if available)

#### 3. Workflow Status Request

Request the current status of a specific workflow.

```json
{
  "type": "workflow_status_request",
  "content": {
    "workflow_id": "string"
  }
}
```

**Required Fields:**
- `workflow_id`: Unique identifier for the workflow to check

### Server → Client Messages

#### 1. System Message

System notifications and status updates.

```json
{
  "type": "system_message",
  "content": "string"
}
```

**Fields:**
- `content`: System notification text

#### 2. Chat Message

Messages from the agent, system, or echoed user messages.

```json
{
  "type": "chat_message",
  "content": "string",
  "is_user": true | false
}
```

**Fields:**
- `content`: Message content
- `is_user`: Boolean indicating the origin. `true` if it's an echo of the user's message, `false` otherwise (agent/system).

#### 3. Processing Status

Indicates that the system is processing a request.

```json
{
  "type": "processing_status",
  "status": "processing|completed|error"
}
```

**Fields:**
- `status`: Current processing status
  - `processing`: Request is being processed
  - `completed`: Processing has completed
  - `error`: An error occurred during processing

#### 4. Wheel Data

Provides configuration data for the activity wheel, including activities.

```json
{
  "type": "wheel_data",
  "wheel": {
    "name": "string",
    "items": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "percentage": 15.5,
        "color": "#66BB6A",
        "domain": "string",
        "base_challenge_rating": 35,
        "activity_tailored_id": "string" // Renamed from activity_id
      }
    ]
  }
}
```

**Fields:**
- `wheel`: Object containing wheel configuration
  - `name`: Name of the wheel (e.g., "Daily Challenge Wheel")
  - `items`: Array of wheel items
    - `id`: Unique identifier for the wheel item
    - `name`: Display name for the activity
    - `description`: Activity description text
    - `percentage`: Probability weight for selection (0-100)
    - `color`: HEX color code for visual representation
    - `domain`: Primary domain code for the activity (e.g., "creative")
    - `base_challenge_rating`: Difficulty level of the activity (0-100)
    - `activity_tailored_id`: Reference to the underlying ActivityTailored object

#### 5. Error

Error notifications for client-side handling.

```json
{
  "type": "error",
  "content": "string"
}
```

**Fields:**
- `content`: Error description

#### 6. Workflow Status

Updates on the status of an ongoing workflow.

```json
{
  "type": "workflow_status",
  "workflow_id": "string",
  "status": "initiated|processing|completed|failed|unknown"
}
```

**Fields:**
- `workflow_id`: Unique identifier for the workflow
- `status`: Current status of the workflow

#### 7. Debug Info (Staff Only)

Provides internal debugging information about backend processes (e.g., agent state transitions, tool calls, errors) specifically for staff users. This message type should *only* be sent to users authenticated as staff.

```json
{
  "type": "debug_info",
  "content": {
    "timestamp": "string", // ISO-8601 formatted timestamp
    "source": "string", // e.g., "Dispatcher", "MentorAgent", "WheelGenerationGraph"
    "level": "info|warning|error|debug", // Log level
    "message": "string", // The debug message
    "details": {} // Optional: Additional structured data (e.g., agent state, tool input/output)
  }
}
```

**Fields:**
- `content`: Object containing debug details
  - `timestamp`: ISO-8601 formatted timestamp of the event.
  - `source`: The component or process generating the message.
  - `level`: Severity level of the debug message.
  - `message`: The primary debug text.
  - `details`: (Optional) A JSON object containing more structured context relevant to the debug message.

#### 8. Tool Argument Error

Indicates an error occurred because a tool was called with incorrect or missing arguments. This helps the frontend display specific issues related to backend tool execution.

```json
{
  "type": "tool_argument_error",
  "content": {
    "tool_name": "string", // Name of the tool that failed
    "error_message": "string", // Description of the argument error
    "source": "string" // e.g., "Dispatcher", "MentorAgent"
  }
}
```

**Fields:**
- `content`: Object containing error details
  - `tool_name`: The name of the tool that encountered the argument error.
  - `error_message`: A user-friendly message explaining the missing or incorrect arguments.
  - `source`: The backend component where the error occurred.

## Sequence Flows

### Chat Message Flow (Asynchronous)

This describes the typical flow when a user sends a standard chat message:

1.  **Frontend -> Consumer:** Sends `chat_message` (containing `message`, `user_profile_id`, and optional `metadata`).
2.  **Consumer -> Frontend:** Sends `chat_message` (echo back to user).
3.  **Consumer -> Dispatcher:** Passes message content to `process_message`.
4.  **Dispatcher -> Consumer:** Determines appropriate workflow (e.g., `discussion`), triggers an asynchronous background task (Celery), and immediately returns an initial response (e.g., `workflow_status` with status `initiated` and a `workflow_id`).
5.  **Consumer -> Frontend:** Forwards the initial `workflow_status` response.
6.  **(Later) Async Task -> Consumer:** The background task executes the workflow (e.g., `discussion_graph`), completes, and sends the final result (e.g., agent's `chat_message` response) back to the Consumer (targeting the specific user via Channels).
7.  **Consumer -> Frontend:** Forwards the final agent `chat_message` response.

*Key Point: The final agent response arrives asynchronously, potentially much later than the initial status update.*

### Wheel Generation, Feedback, and Spin Flow (Asynchronous)

This describes the multi-stage process involving the activity wheel:

**1. Wheel Generation Phase:**
*   **Frontend -> Consumer:** Sends `chat_message` requesting a wheel (potentially with `metadata.requested_workflow = "wheel_generation"`).
*   **Consumer -> Frontend:** Sends `chat_message` (echo).
*   **Consumer -> Dispatcher:** Passes message to `process_message`.
*   **Dispatcher -> Consumer:** Triggers async `wheel_generation` task (Celery), returns initial `workflow_status` (initiated).
*   **Consumer -> Frontend:** Forwards initial `workflow_status`.
*   **(Later) Async Task -> Consumer:** `wheel_generation` task completes, sends final `wheel_data` result.
*   **Consumer -> Frontend:** Forwards `wheel_data`. Frontend displays the wheel.

**2. Optional Pre-Spin Feedback Phase:**
*   *(Condition: User sends a chat message while viewing the wheel, before spinning)*
*   **Frontend -> Consumer:** Sends `chat_message` (feedback text).
*   **Consumer -> Frontend:** Sends `chat_message` (echo).
*   **Consumer -> Dispatcher:** Passes message to `process_message`.
*   **Dispatcher -> Consumer:** Classifies intent as `pre_spin_feedback` (based on context), triggers async task, returns initial `workflow_status` (initiated).
*   **Consumer -> Frontend:** Forwards initial `workflow_status`.
*   **(Later) Async Task -> Consumer:** `pre_spin_feedback` task completes (e.g., modifies wheel), sends final updated `wheel_data` result.
*   **Consumer -> Frontend:** Forwards updated `wheel_data`.

**3. Post-Spin Phase:**
*   *(Condition: User interacts with the wheel UI and selects an activity)*
*   **Frontend -> Consumer:** Sends `spin_result` message (containing `activity_tailored_id`).
*   **Consumer -> Dispatcher:** Passes message to `process_message`.
*   **Dispatcher -> Consumer:** Deterministically routes to `post_spin` based on message type, triggers async task, returns initial `workflow_status` (initiated).
*   **Consumer -> Frontend:** Forwards initial `workflow_status`.
*   **(Later) Async Task -> Consumer:** `post_spin` task completes, sends final result (e.g., `chat_message` confirming activity selection).
*   **Consumer -> Frontend:** Forwards final confirmation `chat_message`.

*Key Point: Each phase involving backend processing (generation, feedback, post-spin confirmation) follows the asynchronous pattern: an initial status update followed by the final result arriving later from a background task.*

## Authentication

For the MVP, the authentication is handled simply via the `user_profile_id` field included in message payloads. The backend validates this ID before processing messages.

Future versions should implement:
- Token-based authentication
- WebSocket handshake validation
- Session management

## Error Handling

### Client-Side Responsibilities

1. **Reconnection Logic**:
   - Implement exponential backoff (starting at 2s)
   - Limit maximum reconnection attempts (recommended: 5)
   - Notify user of connection status

2. **Message Validation**:
   - Validate outgoing message format before sending
   - Handle incoming error messages appropriately

### Server-Side Behaviors

The server sends error messages in the following format:

```json
{
  "type": "error",
  "content": "Error message details"
}
```

Common error scenarios:
- Missing required fields
- Invalid message format
- Missing user_profile_id
- Unknown message type

## Implementation Notes

### Frontend WebSocket Manager

The WebSocket Manager should:

1. Maintain a persistent connection
2. Implement reconnection logic
3. Serialize/deserialize JSON messages
4. Provide a clean API for components to send/receive messages
5. Handle connection state changes

```javascript
// Core methods
connect()
disconnect()
send(messageType, content)
registerHandler(messageType, handlerFunction)

// Helper methods
sendChatMessage(message)
sendSpinResult(activityId, name, description)
```

### Message Processing Flow

1. **Incoming messages**:
   - Parse JSON message
   - Determine message type
   - Route to appropriate handler
   - Update application state
   - Trigger UI updates

2. **Outgoing messages**:
   - Format data as JSON
   - Send through WebSocket
   - Handle send failures

## Testing

1. **Connection Testing**:
   - Establish connection
   - Handle disconnection and reconnection
   - Test connection timeouts

2. **Message Format Testing**:
   - Validate correct message formatting
   - Test handling of malformed messages
   - Test with various message types

3. **Functional Testing**:
   - Test chat messaging
   - Test wheel data reception
   - Test activity selection
   - Test workflow status requests
   - Test handling error responses

### Asynchronous Workflow Execution

It's important to note that the backend `ConversationDispatcher` typically initiates workflows (like wheel generation or discussion handling) as asynchronous background tasks (using Celery).

- The **initial response** from the backend after receiving a `chat_message` or `spin_result` (e.g., a `workflow_status` message) usually confirms that the task has been *started*.
- The **final result** of the workflow (e.g., the `wheel_data` or the agent's `chat_message` response) arrives in a **separate, later message** sent from the completed background task.
- The frontend needs to handle this asynchronous flow, potentially showing loading indicators based on the initial `workflow_status` and updating the UI when the final result message arrives. The sequence diagrams above illustrate this.
