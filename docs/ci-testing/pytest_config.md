# Comprehensive Pytest Reporting Guide

## Official Documentation
- [Pytest Official Documentation](https://docs.pytest.org/en/stable/)
- [Pytest Output and Reporting](https://docs.pytest.org/en/stable/usage.html#detailed-summary-report)
- [Command Line Options](https://docs.pytest.org/en/stable/reference.html#command-line-flags)

## Output Control and Reporting Options

### Verbosity and Detail Levels

#### Basic Verbosity Flags
```bash
pytest -v        # Verbose mode (shows more details)
pytest -q        # Quiet mode (minimal output)
pytest -s        # Show print statements and captured output
```

### Traceback Configurations

#### Traceback Style Options
```bash
pytest --tb=auto     # Default: long traceback for first and last entry
pytest --tb=long     # Full, detailed traceback
pytest --tb=short    # Shorter, more concise traceback
pytest --tb=line     # One-line traceback
pytest --tb=no       # No traceback at all
```

### Warning Management

#### Warning Configuration
```bash
# Filtering warnings
pytest -W ignore    # Ignore all warnings
pytest -W error     # Treat warnings as errors
pytest -W default   # Default warning behavior

# Specific warning filters in pyproject.toml
[tool.pytest.ini_options]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::RuntimeWarning",
    "error::UserWarning"  # Treat user warnings as errors
]
```

### Output and Capture Modes

#### Capture Modes
```bash
pytest -s                # Disable output capture, show all output
pytest --capture=no      # Same as -s
pytest --capture=fd      # Capture to file descriptors
pytest --capture=tee-sys # Capture and print simultaneously
```

### Summary and Result Reporting

#### Summary Options
```bash
pytest --report-log=log.json       # Generate a JSON log file
pytest --junitxml=results.xml      # Generate JUnit XML report
pytest --html=report.html          # Generate HTML report (requires pytest-html)
pytest --no-summary                # Suppress summary information
```

### Detailed Reporting Configurations

#### Comprehensive Reporting Setup
```toml
[tool.pytest.ini_options]
# Logging configuration
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

# Additional reporting options
addopts = [
    "-v",
    "--tb=short",
    "--disable-pytest-warnings",
    "--junitxml=test-results/junit.xml"
]
```

## Advanced Reporting Techniques

### Selective Test Reporting

#### Running and Reporting Specific Tests
```bash
# Run tests matching a pattern
pytest -k "test_important"

# Run tests with specific markers
pytest -m "critical"

# Show captured output for failed tests
pytest --show-capture=failed
```

### Performance and Coverage Reporting

#### Coverage and Performance Metrics
```bash
# Generate coverage report
pytest --cov=.               # Cover current directory
pytest --cov-report=html     # HTML coverage report
pytest --cov-report=xml      # XML coverage report

# Show performance bottlenecks
pytest --durations=10        # Show 10 slowest test durations
```

## Recommended Plugins for Enhanced Reporting

1. **pytest-html**: Generates HTML test reports
   ```bash
   pip install pytest-html
   pytest --html=report.html
   ```

2. **pytest-sugar**: Improves test output formatting
   ```bash
   pip install pytest-sugar
   ```

3. **pytest-cov**: Comprehensive code coverage reporting
   ```bash
   pip install pytest-cov
   pytest --cov=. --cov-report=html
   ```

## Best Practices for Reporting

1. **Consistent Configuration**
   - Use `pyproject.toml` for global pytest settings
   - Create environment-specific reporting configurations

2. **Selective Reporting**
   - Use markers to categorize and filter tests
   - Utilize `-k` and `-m` flags for targeted reporting

3. **CI/CD Integration**
   - Generate machine-readable reports (XML, JSON)
   - Use coverage and performance reports for quality gates

## Troubleshooting Reporting Issues

### Common Reporting Challenges
- Overly verbose output
- Missing critical information
- Inconsistent reporting across environments

### Diagnostic Commands
```bash
# Understand pytest configuration
pytest --fixtures
pytest --markers
pytest --help  # Show all available options
```

## Example Comprehensive Configuration

```toml
[tool.pytest.ini_options]
# Basic configuration
testpaths = ["tests"]
python_files = "test_*.py"

# Reporting and output
addopts = [
    "-v",
    "--tb=short",
    "--disable-pytest-warnings",
    "--junitxml=test-results/junit.xml",
    "--cov=.",
    "--cov-report=html",
    "--cov-report=xml"
]

# Logging
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)"

# Warning filters
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::RuntimeWarning"
]

# Markers for test categorization
markers = [
    "unit: unit tests",
    "integration: integration tests",
    "slow: slow tests"
]
```

## Conclusion

Effective pytest reporting is about finding the right balance between detail and clarity. Experiment with different configurations to find what works best for your project's specific needs.