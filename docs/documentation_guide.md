# Documentation Navigation and Maintenance Guide

This guide provides instructions on how to effectively navigate and contribute to the project documentation located within the `/docs` directory. Maintaining clear, consistent, and up-to-date documentation is crucial for project understanding, onboarding new team members, and ensuring long-term maintainability.

## Navigating the Documentation

The documentation is structured into logical folders based on the area of the project they cover.

*   **`/docs/index.md`:** The main entry point. Start here to get an overview and find links to major sections.
*   **`/docs/global/`:** High-level concepts, strategy, architecture overview, and glossary applicable to the entire project.
*   **`/docs/architecture/`:** Detailed architectural diagrams, data models, and workflow descriptions.
*   **`/docs/backend/`:** Specifics about the Django backend, including agent details, tools, and testing strategies.
*   **`/docs/frontend/`:** Documentation related to the React frontend. (Note: More detailed component/hook documentation might reside within `frontend/docs/` or inline as JSDoc/TSDoc).
*   **`/docs/api/`:** Contains the `ApiContract.md` defining the WebSocket communication protocol.
*   **`/docs/users/`:** User-centric documentation like personas, user stories, and guides.
*   **`/docs/governance/`:** Project governance structure, constitution, and status reports.
*   **`/docs/prompts/`:** Examples and templates for prompts used in AI generation tasks.
*   **`/docs/messy/`:** A temporary holding area for documents that need refinement or reorganization. Avoid adding new permanent documentation here.

**Tips for Navigation:**

*   Use the links provided in `docs/index.md` as a starting point.
*   Utilize relative links within documents to jump between related topics.
*   Use your IDE's search functionality (e.g., Ctrl+Shift+F in VS Code) to find specific keywords across the documentation.

## Maintaining the Documentation

Keeping the documentation accurate and relevant is a shared responsibility. Follow these best practices when updating or adding new documentation:

### Best Practices

1.  **Keep it Updated:** Documentation should reflect the current state of the codebase and project decisions. Update relevant documents whenever you make changes to features, architecture, or processes.
2.  **Be Clear and Concise:** Write in simple, unambiguous language. Use headings, bullet points, and code blocks to structure information effectively. Avoid jargon where possible, or explain it in the [Global Glossary](./global/9_global_glossary.md).
3.  **Use Markdown:** All documentation should be written in Markdown (`.md`). Familiarize yourself with Markdown syntax for formatting text, links, images, code blocks, and tables.
4.  **Relative Linking:** ALWAYS use relative links (e.g., `[Link Text](./relative/path/to/file.md)`) to refer to other documents within the `/docs` structure. This ensures links remain valid regardless of where the project is cloned or hosted. Avoid absolute paths or URLs for internal links.
5.  **Diagrams:** Use tools like PlantUML (`.puml`) or Mermaid (within Markdown code blocks) for diagrams. Store diagram source files alongside the documentation that references them (e.g., in a `diagrams/` subdirectory or directly if closely related). Embed diagrams or link to the source file. Ensure diagrams are kept up-to-date with architectural changes.
6.  **Consistency:** Follow the established structure and naming conventions. Look at existing documents for examples of formatting and tone.
7.  **Single Source of Truth:** Avoid duplicating information. Link to existing documents instead of copying content. If information needs to be referenced in multiple places, consider creating a central document and linking to it.
8.  **Review:** When adding significant documentation or making major changes, ask a team member to review it for clarity, accuracy, and completeness.
9.  **`docs/messy/` Folder:** Use this folder *only* for temporary drafts or documents pending reorganization. Regularly review its contents and integrate useful information into the main structure or delete obsolete files. Do not treat it as a permanent archive.

### Adding New Documentation

1.  **Identify the Right Location:** Determine the most logical folder within the `/docs` structure for your new document based on its content (e.g., backend-specific details go in `/docs/backend/`).
2.  **Create the File:** Use a descriptive filename (e.g., `new_feature_details.md`).
3.  **Write Content:** Follow the best practices outlined above.
4.  **Add Links:** Link *to* your new document from relevant existing documents (e.g., add a link in `docs/index.md` or a more specific index file if appropriate). Also, add relative links *from* your new document to other relevant documentation.

### Updating Existing Documentation

1.  **Locate the Document:** Find the document(s) that need updating.
2.  **Make Changes:** Edit the content to reflect the current state accurately.
3.  **Verify Links:** Ensure all relative links within the document are still correct.
4.  **Review:** If the changes are substantial, request a review.

By following these guidelines, we can maintain a valuable and reliable documentation resource for The Game of Life project.
