# Comprehensive Testing Guide

This document combines all testing documentation into a single comprehensive guide for the Goali backend application.

## Table of Contents
- [Test Environment Setup](#test-environment-setup)
- [Running Tests](#running-tests)
- [Debugging Tests](#debugging-tests)
- [Writing Tests](#writing-tests)
- [Agent Testing](#agent-testing)
- [Mocking Strategies](#mocking-strategies)
- [CI/CD Integration](#cicd-integration)
- [Benchmarking Tests](#benchmarking-tests)
- [Common Testing Issues](#common-testing-issues)

## Test Environment Setup

Our testing infrastructure is built on pytest with Django integration. The test environment uses Postgres as the database backend for compatibility with the production environment.

### Prerequisites

- Python 3.12+
- All dependencies installed (`pip install -r backend/requirements.txt`)
- Docker and Docker Compose (for containerized testing)

### Key Files

- `backend/ultimate_test_setup.py`: Sets up the test database and environment (handles schema creation, applies Django migrations, and seeds test data).
- `backend/conftest.py`: Contains pytest fixtures and configuration.
- `backend/config/settings/test.py`: Django settings for the test environment.
- `backend/scripts/setup_test_env.sh`: Legacy Bash wrapper for test setup (prefer `ultimate_test_setup.py`).

### Docker Compose Services for Testing

The project uses Docker Compose (`backend/docker-compose.yml`) to manage the local development and testing environment:

- **`web-test`**: Runs the full test suite using `.env.test` and `config.settings.test`.
- **`debug-tests`**: Extends `web-test` but starts with debugpy for debugging on port 5681.
- **`redis`**: Required for Celery and Channels tests.
- **`test-db`**: A dedicated Postgres database service used for testing.

### Test Database Management

The project uses a dedicated Postgres database for testing. The database is created and destroyed automatically when running tests.

The test database is managed through our test setup script:

**Automatic Setup:**
The `backend/ultimate_test_setup.py` script:
1. Creates/resets the test database (Postgres for production-like testing).
2. Applies Django migrations (the project uses standard Django migrations for database schema management).
3. Creates tables for models without migrations using Django's schema editor.
4. Seeds minimal test data required for tests to run.
5. Registers tools directly in the database.

**Coordination with Pytest:**
Our setup coordinates with pytest's database handling through:
- The `TEST_DB_INITIALIZED` environment variable.
- The custom `django_db_setup` fixture in `backend/conftest.py`.
This ensures the database is properly set up regardless of how tests are run.

**Important Note:** Do not use SQLite for testing or any other purpose in the project. Always use Postgres for testing to ensure compatibility with the production environment.

## Running Tests

### Using Docker (Recommended)

The simplest way to run tests is using Docker Compose. Ensure the `redis` service is running.

```bash
# Start redis service in detached mode (if not already running)
docker-compose -f backend/docker-compose.yml up -d redis

# Run all tests
docker-compose -f backend/docker-compose.yml run --rm web-test

# Run specific tests (replace path/to/test_file.py)
docker-compose -f backend/docker-compose.yml run --rm web-test python -m pytest backend/apps/main/tests/test_agents/test_mentor_agent.py

# Run tests with specific markers (e.g., "tool")
docker-compose -f backend/docker-compose.yml run --rm web-test python -m pytest -m "tool"

# Run tests matching a keyword (e.g., "discussion_flow")
docker-compose -f backend/docker-compose.yml run --rm web-test python -m pytest -k "discussion_flow"
```

### Django Tests

Django tests can be run using the following command:

```bash
cd backend
docker-compose run --rm web-test python -m pytest [test_path] -v
```

For example, to run all tests in the `apps/main/tests` directory:

```bash
cd backend
docker-compose run --rm web-test python -m pytest apps/main/tests -v
```

To run a specific test file:

```bash
cd backend
docker-compose run --rm web-test python -m pytest apps/main/tests/test_models.py -v
```

To run a specific test class or function:

```bash
cd backend
docker-compose run --rm web-test python -m pytest apps/main/tests/test_models.py::TestBenchmarkScenario -v
docker-compose run --rm web-test python -m pytest apps/main/tests/test_models.py::TestBenchmarkScenario::test_create_scenario -v
```

### With VS Code Test Explorer

Configure your workspace settings (`.vscode/settings.json`) to run tests inside the Docker container:

```json
{
    "python.testing.pytestEnabled": true,
    "python.testing.pytestPath": "docker-compose",
    "python.testing.pytestArgs": [
        "-f",
        "backend/docker-compose.yml", // Specify compose file if not default
        "exec",
        "-T", // Disable pseudo-TTY allocation
        "-w", // Set working directory inside container
        "/usr/src/app",
        "web", // Service name
        "python", // Use python executable
        "-Xfrozen_modules=off", // Recommended for pytest
        "-m",
        "pytest",
        "--import-mode=importlib", // Important for resolving imports
        "backend/apps" // Target test directory
    ],
    "python.testing.cwd": "${workspaceFolder}", // Run docker-compose from root
    "python.testing.autoTestDiscoverOnSaveEnabled": true,
    "python.envFile": "${workspaceFolder}/backend/.env.test" // Use test environment variables
}
```
Reload VS Code, and the Test Explorer should discover and run tests within the `web` container.

### Locally (Without Docker)

You can also run tests directly on your local machine if the environment is set up:

```bash
# Setup the test environment (run from project root)
cd backend
python ultimate_test_setup.py
cd ..

# Run tests (run from project root)
python -m pytest backend/apps --reuse-db

# Run specific tests
python -m pytest backend/apps/main/tests/test_agents/test_mentor_agent.py --reuse-db
```

### Important Pytest Flags

- `--reuse-db`: Reuse the test database if it exists (faster).
- `--create-db`: Force recreation of the test database.
- `-v`: Verbose output.
- `-k "expression"`: Run tests matching expression.
- `-m "marker"`: Run tests with specific marker (see Metadata Tagging section).
- `--import-mode=importlib`: Helps prevent import issues, especially with complex project structures.

### Test Timeouts

The project includes a timeout mechanism to prevent tests from hanging indefinitely, which can cause CI/CD pipeline failures and developer productivity loss.

#### Global Timeout Fixture

A global timeout fixture is implemented in `backend/conftest.py` that applies a default timeout of 60 seconds to all tests:

```python
@pytest.fixture
def test_timeout():
    """
    Fixture that provides a timeout value for tests.

    Returns:
        int: The timeout value in seconds (default: 60)
    """
    return 60  # Default timeout of 60 seconds
```

#### Timeout Marker

Tests can specify a custom timeout using the `@pytest.mark.timeout` marker:

```python
@pytest.mark.timeout(120)  # Set explicit timeout of 120 seconds
def test_something(test_timeout):
    # Test code here
```

#### Explicit Timeout Context

For more fine-grained control, tests can use the `asyncio.timeout` context manager with the `test_timeout` fixture:

```python
import asyncio

async def test_async_function(test_timeout):
    async with asyncio.timeout(test_timeout):
        # Async code that might hang
        result = await potentially_hanging_function()
```

#### Timeout Cleanup

When a test times out:
1. For synchronous tests, a `TimeoutError` is raised and caught by the fixture
2. For asynchronous tests, pending tasks are automatically cancelled and cleaned up
3. Detailed logging is provided to help diagnose the cause of the timeout

#### Problematic Tests

Tests that have been observed to hang should be updated with explicit timeouts:
- Agent tests that interact with real LLMs
- Tests with complex async patterns
- Tests that involve multiple async components

### Task Monitoring

The project includes a task monitoring system to track asyncio tasks during test execution, identify orphaned tasks, and provide detailed reporting.

#### Task Monitor Fixture

A task monitoring fixture is implemented in `backend/conftest.py` that tracks all asyncio tasks at the start of a test, compares them with tasks at the end, and logs and cancels any orphaned tasks:

```python
@pytest.fixture
def task_monitor():
    """
    Fixture that monitors asyncio tasks during test execution.

    This fixture tracks all asyncio tasks at the start of a test,
    compares them with tasks at the end, and logs and cancels any orphaned tasks.

    Usage:
        def test_something(task_monitor):
            # Test code here

    Returns:
        dict: A dictionary with task monitoring functions:
            - get_task_snapshot(): Get a snapshot of current tasks
            - compare_snapshots(before, after): Compare two task snapshots
            - cleanup_orphaned_tasks(before, after): Cancel orphaned tasks
    """
    # Implementation details...
```

#### Using the Task Monitor

The task monitor fixture provides several functions for monitoring and managing asyncio tasks:

```python
@pytest.mark.asyncio
async def test_with_task_monitoring(task_monitor, event_loop):
    # Get a snapshot of tasks before the test
    before = task_monitor["get_task_snapshot"]()

    # Run the test code
    await some_function_that_creates_tasks()

    # Get a snapshot of tasks after the test
    after = task_monitor["get_task_snapshot"]()

    # Compare snapshots to find new, completed, and ongoing tasks
    new_tasks, completed_tasks, ongoing_tasks = task_monitor["compare_snapshots"](before, after)

    # Clean up any orphaned tasks
    await task_monitor["cleanup_orphaned_tasks"](before, after)
```

#### Improved Task Cleanup (May 30, 2025)

The task cleanup function has been enhanced to handle task cancellation more gracefully and prevent tests from hanging:

```python
async def cleanup_orphaned_tasks(before, after):
    """
    Cleanup orphaned tasks that were created during the test.

    Args:
        before (set): Set of tasks before the test
        after (set): Set of tasks after the test
    """
    orphaned = after - before
    if orphaned:
        # Cancel tasks that are not done
        pending_tasks = []
        for task in orphaned:
            if not task.done():
                task.cancel()
                pending_tasks.append(task)

        # If there are pending tasks, wait for them to be cancelled
        if pending_tasks:
            try:
                # Use a short timeout to avoid hanging
                await asyncio.wait(pending_tasks, timeout=2.0)
            except asyncio.CancelledError:
                # Ignore cancelled errors
                pass
            except Exception as e:
                # Log other exceptions but don't fail the test
                logger = logging.getLogger(__name__)
                logger.warning(f"Error during task cleanup: {e}")
```

This improved implementation:

1. Separates the cancellation and waiting steps to avoid waiting for tasks that are already done
2. Uses a timeout when waiting for tasks to be cancelled to prevent tests from hanging
3. Handles cancellation errors gracefully to prevent test failures due to task cleanup issues
4. Logs exceptions during cleanup for debugging purposes without failing the test

This approach is particularly useful for agent tests that create multiple tasks that need to be properly cleaned up.

#### Event Loop Fixture

When using asyncio functionality in tests, it's important to ensure that an event loop is available. The recommended approach is to create a dedicated event loop fixture in your test file:

```python
@pytest.fixture(scope="function")
def event_loop():
    """
    Create an instance of the default event loop for each test case.
    This ensures that each test has its own event loop.
    """
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()
    asyncio.set_event_loop(None)
```

Then, mark your tests with `@pytest.mark.asyncio` and include the event_loop fixture:

```python
@pytest.mark.asyncio
async def test_async_functionality(event_loop):
    # Your test code here
    assert asyncio.get_event_loop() is event_loop
```

This approach ensures that:
1. Each test has its own isolated event loop
2. The event loop is properly cleaned up after the test
3. The test can use asyncio functionality without "no running event loop" errors

#### Automatic Task Reporting

The task monitor automatically logs task statistics at the end of each test:

```
Task monitor summary: 3 new, 2 completed, 5 ongoing
```

If orphaned tasks are found, detailed information is logged:

```
Orphaned task: Task-123456 (ID: 140123456789)
Stack trace: ['file.py:123 in function_name', 'other_file.py:456 in other_function']
```

#### Runtime Debugging with Signal Handlers

For debugging running tests, the project includes a signal handler for SIGUSR1 that dumps stack traces of all threads and asyncio tasks:

```bash
# Find the process ID of the running test
ps aux | grep pytest

# Send SIGUSR1 signal to dump stack traces
kill -SIGUSR1 <pid>
```

The stack trace dump includes:
- Thread stacks with thread IDs and names
- Asyncio task stacks with task names, IDs, and status
- Detailed stack frames for each thread and task

This is particularly useful for diagnosing hanging tests or deadlocks in CI environments.

### Database Connection Monitoring

The project includes a database connection monitoring system to track database connections during test execution, identify connection leaks, and provide detailed reporting.

#### Database Connection Monitor Fixture

A database connection monitoring system is implemented with a utility class in `backend/apps/main/utils/db_connection_monitor.py` and a fixture in `backend/conftest.py`. This system tracks all database connections at the start of a test, compares them with connections at the end, and logs and closes any leaked connections:

```python
# backend/apps/main/utils/db_connection_monitor.py
class DBConnectionMonitor:
    """
    Utility class for monitoring database connections.

    This class provides methods to track, compare, and clean up database connections,
    helping to identify and fix connection leaks in tests and production code.
    """

    @staticmethod
    def get_connection_snapshot() -> Dict[str, Dict[str, Any]]:
        """Get a snapshot of all current database connections."""
        # Implementation details...

    @staticmethod
    def compare_snapshots(before, after) -> Tuple[Dict, Dict, Dict]:
        """Compare two connection snapshots and return new, closed, and leaked connections."""
        # Implementation details...

    # Other methods...

# backend/conftest.py
@pytest.fixture
def db_connection_monitor():
    """
    Fixture that monitors database connections during test execution.

    This fixture tracks all database connections at the start of a test,
    compares them with connections at the end, and logs and closes any leaked connections.

    Usage:
        def test_something(db_connection_monitor):
            # Test code here

    Returns:
        dict: A dictionary with connection monitoring functions:
            - get_connection_snapshot(): Get a snapshot of current connections
            - compare_snapshots(before, after): Compare two connection snapshots
            - cleanup_leaked_connections(before, after): Close leaked connections
            - get_connection_source(connection): Get source information for a connection
            - add_timeout_to_connections(): Add timeout parameters to all database connections
    """
    # Implementation details...
```

#### Using the Database Connection Monitor

The database connection monitor fixture provides several functions for monitoring and managing database connections:

```python
@pytest.mark.django_db(transaction=True)
def test_with_connection_monitoring(db_connection_monitor):
    # Get a snapshot of connections before the test
    before = db_connection_monitor["get_connection_snapshot"]()

    # Run the test code
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")

    # Get a snapshot of connections after the test
    after = db_connection_monitor["get_connection_snapshot"]()

    # Compare snapshots to find new, closed, and leaked connections
    new_connections, closed_connections, leaked_connections = db_connection_monitor["compare_snapshots"](before, after)

    # Clean up any leaked connections
    db_connection_monitor["cleanup_leaked_connections"](before, after)

    # Add timeout parameters to all database connections
    db_connection_monitor["add_timeout_to_connections"]()
```

#### Connection Source Tracking

The database connection monitor tracks the source of each connection, including the file, line number, and function where the connection was created:

```python
# Get source information for a connection
source = db_connection_monitor["get_connection_source"](connection)
print(f"Connection created at {source['file']}:{source['line']} in {source['function']}")
```

#### Automatic Connection Cleanup

The database connection monitor automatically logs connection statistics at the end of each test:

```
DB connection monitor summary: 2 new, 1 closed, 1 leaked, 2 total open
```

If leaked connections are found, detailed information is logged:

```
Found 1 leaked database connections
Leaked connection: default from /path/to/file.py:123 in function_name
Closed 1 leaked connections
```

#### Connection Timeouts

The database connection monitor adds timeout parameters to all database connections to prevent hanging queries:

```python
# Add timeout parameters to all database connections
db_connection_monitor["add_timeout_to_connections"]()
```

For PostgreSQL connections, this sets the `statement_timeout` parameter to 30 seconds (30000 ms), which prevents queries from running indefinitely.

#### Handling Database Connection Stability Issues

When running tests, you may encounter database connection stability issues, especially in workflow tests that involve multiple async operations. Common errors include:

```
psycopg2.OperationalError: server closed the connection unexpectedly
django.db.utils.OperationalError: connection to server at "test-db" failed: FATAL: the database system is shutting down
```

The project provides a dedicated `DatabaseConnectionUtility` class in `backend/apps/main/utils/db_connection_utility.py` to address these issues. This utility class offers:

1. **Connection Pooling**: Reuse connections from Django's connection pool
2. **Retry Logic**: Automatically retry database operations that fail due to transient issues
3. **Safe Transaction Management**: Properly manage database transactions
4. **Automatic Connection Cleanup**: Ensure connections are properly closed
5. **Integration with db_connection_monitor**: Better diagnostics for connection issues

### Using DatabaseConnectionUtility

#### Basic Usage

```python
from apps.main.utils.db_connection_utility import DatabaseConnectionUtility

# Execute a SQL query with retry logic
result = DatabaseConnectionUtility.execute_with_retry(
    "SELECT * FROM my_table WHERE id = %s",
    params=[1],
    max_retries=3,
    retry_delay=0.5
)

# Use a safe connection context manager
with DatabaseConnectionUtility.safe_connection() as conn:
    # Use the connection
    with conn.cursor() as cursor:
        cursor.execute("SELECT 1")

# Use a safe cursor context manager
with DatabaseConnectionUtility.safe_cursor() as cursor:
    cursor.execute("SELECT 1")
    result = cursor.fetchone()
```

#### Decorators for Retry Logic

```python
from apps.main.utils.db_connection_utility import DatabaseConnectionUtility

# Add retry logic to a function
@DatabaseConnectionUtility.with_retry(max_retries=3, retry_delay=0.5)
def get_user_by_id(user_id):
    with connection.cursor() as cursor:
        cursor.execute("SELECT * FROM auth_user WHERE id = %s", [user_id])
        return cursor.fetchone()

# Add retry logic to an async function
@DatabaseConnectionUtility.async_with_retry(max_retries=3, retry_delay=0.5)
async def get_user_by_id_async(user_id):
    # Use database_sync_to_async to run the database operation
    return await database_sync_to_async(get_user_by_id)(user_id)
```

#### Transaction Management

```python
from apps.main.utils.db_connection_utility import DatabaseConnectionUtility

# Add transaction management to a function
@DatabaseConnectionUtility.safe_transaction()
def create_user(username, email):
    # This function will run in a transaction
    with connection.cursor() as cursor:
        cursor.execute(
            "INSERT INTO auth_user (username, email) VALUES (%s, %s)",
            [username, email]
        )
        return cursor.lastrowid

# Add transaction management to an async function
@DatabaseConnectionUtility.async_safe_transaction()
async def create_user_async(username, email):
    # This function will run in a transaction
    return await database_sync_to_async(create_user)(username, email)
```

#### Connection Monitoring

```python
from apps.main.utils.db_connection_utility import DatabaseConnectionUtility

# Add connection monitoring to a function
@DatabaseConnectionUtility.add_connection_monitoring()
def function_with_db_operations():
    # This function will be monitored for connection leaks
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")

# Add connection monitoring to an async function
@DatabaseConnectionUtility.async_add_connection_monitoring()
async def async_function_with_db_operations():
    # This function will be monitored for connection leaks
    result = await database_sync_to_async(lambda: connection.cursor().execute("SELECT 1"))()
    return result
```

#### Integration with db_connection_monitor Fixture

```python
@pytest.mark.django_db(transaction=True)
def test_something(db_connection_monitor):
    # Get initial connection snapshot
    initial_snapshot = db_connection_monitor["get_connection_snapshot"]()

    # Add timeout to connections to prevent hanging queries
    db_connection_monitor["add_timeout_to_connections"]()

    # Use the DatabaseConnectionUtility with the db_connection_monitor
    @DatabaseConnectionUtility.add_connection_monitoring(db_connection_monitor)
    def function_with_db_operations():
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")

    # Call the function
    function_with_db_operations()

    # Get final snapshot and check for leaked connections
    final_snapshot = db_connection_monitor["get_connection_snapshot"]()
    _, _, leaked_connections = db_connection_monitor["compare_snapshots"](
        initial_snapshot, final_snapshot
    )

    if leaked_connections:
        logger.warning(f"Found {len(leaked_connections)} leaked connections")
        # Close leaked connections
        db_connection_monitor["cleanup_leaked_connections"](
            initial_snapshot, final_snapshot
        )
```

#### Limiting Concurrent Database Operations

```python
from apps.main.utils.db_connection_utility import DatabaseConnectionUtility

# Limit concurrent database operations
@DatabaseConnectionUtility.limit_concurrent_db_operations(max_concurrent=5)
async def db_operation():
    # This function will be limited to 5 concurrent executions
    result = await database_sync_to_async(lambda: connection.cursor().execute("SELECT 1"))()
    return result
```

#### Best Practices for Database Connections in Tests

1. **Use DatabaseConnectionUtility**: Always use the utility class for database operations in tests:
   ```python
   from apps.main.utils.db_connection_utility import DatabaseConnectionUtility

   # Use the utility's safe_cursor context manager
   with DatabaseConnectionUtility.safe_cursor() as cursor:
       cursor.execute("SELECT 1")
   ```

2. **Add Connection Monitoring**: Use the `async_add_connection_monitoring` decorator for async tests:
   ```python
   @DatabaseConnectionUtility.async_add_connection_monitoring()
   async def test_async_function():
       # Test code here that uses database connections
   ```

3. **Use Retry Logic**: Add retry logic to database operations that may fail due to transient issues:
   ```python
   @DatabaseConnectionUtility.with_retry(max_retries=3, retry_delay=0.5)
   def get_data_from_db():
       # Database operations here
   ```

4. **Limit Concurrent Operations**: Use the `limit_concurrent_db_operations` decorator for async tests:
   ```python
   @DatabaseConnectionUtility.limit_concurrent_db_operations(max_concurrent=5)
   async def async_db_operation():
       # Database operations here
   ```

5. **Clean Up Connections**: Ensure connections are properly closed in the `finally` block:
   ```python
   @pytest.mark.django_db(transaction=True)
   def test_something(db_connection_monitor):
       initial_snapshot = db_connection_monitor["get_connection_snapshot"]()
       try:
           # Test code here
       finally:
           final_snapshot = db_connection_monitor["get_connection_snapshot"]()
           db_connection_monitor["cleanup_leaked_connections"](initial_snapshot, final_snapshot)
   ```

6. **Use the db_connection_monitor Fixture**: Include the fixture in tests that use database connections:
   ```python
   @pytest.mark.django_db(transaction=True)
   def test_something(db_connection_monitor):
       # Test code here
   ```

7. **Add Timeout Parameters**: Use `db_connection_monitor["add_timeout_to_connections"]()` to prevent hanging queries:
   ```python
   @pytest.mark.django_db(transaction=True)
   def test_something(db_connection_monitor):
       # Add timeout to connections
       db_connection_monitor["add_timeout_to_connections"]()
       # Test code here
   ```

8. **Use Safe Transaction Management**: Use the `safe_transaction` decorator for functions that need transaction management:
   ```python
   @DatabaseConnectionUtility.safe_transaction()
   def function_that_needs_transaction():
       # Database operations here
   ```

9. **Handle Connection Errors Gracefully**: Use try-except blocks to handle connection errors:
   ```python
   try:
       # Database operations here
   except Exception as e:
       logger.error(f"Database error: {e}")
       # Handle the error appropriately
   ```

10. **Use Connection Pooling**: Reuse connections from Django's connection pool:
    ```python
    # Use the safe_connection context manager
    with DatabaseConnectionUtility.safe_connection() as conn:
        # Use the connection
    ```

## Test Infrastructure Issues

### AppRegistryNotReady and No Running Event Loop Errors (June 6, 2025)

**Problem:** Tests frequently fail with `django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet` or `RuntimeError: no running event loop` errors, particularly in fixtures that use asyncio or Django models.

**Current Solution:**
1. For AppRegistryNotReady:
   - Initialize Django in test files with `os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')` and `django.setup()`
   - Use string references for model and agent class names to avoid early imports
   - Ensure Django is initialized before any imports that use Django models

2. For No Running Event Loop:
   - Wrap asyncio calls in try-except blocks to handle the case where there is no running event loop
   - Use `asyncio.get_event_loop()` to get the current event loop before calling asyncio functions
   - Provide graceful fallbacks when no event loop is available

**Root Cause:**
- AppRegistryNotReady occurs when Django models are imported or accessed before Django's application registry is fully initialized
- No Running Event Loop occurs when asyncio functions like `asyncio.all_tasks()` are called in a synchronous context where no event loop is running

**Implementation Details:**
- Task monitoring fixture in `conftest.py` now safely handles the case where there is no running event loop
- Signal handlers for runtime debugging also handle the case where there is no running event loop
- Test files that use Django models now explicitly initialize Django before imports

**Related Files:**
- backend/conftest.py
- backend/apps/main/tests/test_utils/test_task_monitoring.py
- backend/apps/main/tests/test_agents/test_ethical_agent.py

**Documentation:**
- This issue is documented in PLANNING.md (June 6, 2025 update)
- Detailed information about task monitoring and asyncio patterns is added to TESTING_GUIDE.md and AGENT_TESTING_GUIDE.md


## Debugging Tests

### Debugging with VSCode

The project is configured for debugging tests running inside Docker containers with VSCode.

**Steps:**
1.  **Ensure Docker is Running:** Start Docker Desktop.
2.  **Open the Run and Debug Panel:** (Ctrl+Shift+D or Cmd+Shift+D).
3.  **Select a Launch Configuration:** (Defined in `.vscode/launch.json`)
    *   **`Django: Debug Test`:** Runs the entire test suite with the debugger attached.
    *   **`Django: Debug Test (Select)`:** Prompts for a specific test path or pattern to debug.
4.  **Set Breakpoints:** Place breakpoints in your test files or application code.
5.  **Start Debugging:** Click the green play button or press F5.

**What Happens:**
- VSCode executes the appropriate pre-launch task (defined in `.vscode/tasks.json`).
- This starts the `debug-tests` service in Docker Compose.
- The container runs `python -m debugpy --wait-for-client --listen 0.0.0.0:5681 -m pytest [args]`.
- VSCode attaches its debugger to port 5681.
- Test execution begins, pausing at your breakpoints.

### Using the `debug-tests` Service Manually

You can also manually start the debug service and attach:

```bash
# Start the debug-tests container, optionally specifying tests
PYTEST_ARGS="backend/apps/main/tests/test_specific_file.py" docker-compose -f backend/docker-compose.yml run --rm --service-ports debug-tests

# Then attach your debugger (e.g., in VS Code, use the "Python: Remote Attach" configuration targeting localhost:5681)
```

## Writing Tests

### Test Organization

Tests are organized by app and functionality within the `backend/apps/` directory:

```
backend/apps/
  └── [app_name]/
      └── tests/
          ├── test_agents/       # Tests for agent functionality
          ├── test_tools/        # Tests for agent tools
          ├── test_flows/        # Tests for workflow flows (LangGraph)
          ├── test_integration/  # Integration tests spanning multiple components
          ├── test_services/     # Tests for services
          ├── test_models.py     # Tests for model logic (if any)
          └── test_views.py      # Tests for Django views/APIs
```

### Test Utilities

The project provides several utility functions for testing:

- `create_test_scenario` and `create_test_scenario_async` in `apps/main/tests/utils.py` for creating test scenarios
- `generate_unique_scenario_name` in `apps/main/tests/utils.py` for generating unique scenario names to avoid name conflicts in tests

When testing models that have a foreign key to `LLMConfig`, you can use the `ExtendedTestBenchmarkRun` model from `apps/main/tests/extended_models.py`. This model extends `TestBenchmarkRun` and provides a custom manager that handles `TestLLMConfig` instances.

Example:

```python
from apps.main.tests.extended_models import ExtendedTestBenchmarkRun
from apps.main.tests.test_agents.conftest import TestLLMConfig

# Create a TestLLMConfig instance
test_llm_config = TestLLMConfig(
    name="test_llm",
    model_name="test_model",
    temperature=0.7,
    input_token_price=0.0001,
    output_token_price=0.0002
)

# Create an ExtendedTestBenchmarkRun instance with the TestLLMConfig
benchmark_run = ExtendedTestBenchmarkRun.objects.create(
    scenario=scenario_instance,
    agent_definition=agent_instance,
    agent_version="1.0",
    llm_config=test_llm_config,  # This will be converted to a real LLMConfig instance
    parameters={"runs": 3},
    runs_count=3,
    mean_duration=150.5,
    median_duration=145.0,
    min_duration=100.0,
    max_duration=200.0,
    std_dev=25.0,
    success_rate=1.0,
    llm_calls=5,
    tool_calls=2,
    tool_breakdown={"tool1": 2},
    memory_operations=0,
    semantic_score=0.85,
    semantic_evaluation_details={"reasoning": "Looks good"},
    semantic_evaluations={},
    execution_date=timezone.now() - timezone.timedelta(days=1)
)
```

**Agent Unit Tests (`test_agents/`):**
Organize tests for each agent into categories:
1.  **Basic Functionality**: Input processing, output structure, state management.
2.  **Decision Logic**: Core algorithms, classification, prioritization.
3.  **Error Handling**: Invalid inputs, dependency failures.
4.  **Memory and State**: Read/write operations, state transformation.

**Integration Tests (`test_integration/`, `test_flows/`):**
Organize by workflow or feature:
1.  **Setup Phase**: Create realistic user profiles, initial states, dependencies.
2.  **Workflow Execution**: Trigger entry points, monitor state transitions.
3.  **Verification Phase**: Validate end state, intermediate states, side effects.

### Test Layers and Mocking Strategy

To ensure clarity and consistency, tests are categorized into layers, each with a default mocking approach. **A clear mocking strategy is crucial.**

*   **Unit Tests (Agents/Tools/Services/Utils):**
    *   **Goal:** Test isolated logic within a single function or class.
    *   **Setup:** Generally avoid database access (`@pytest.mark.django_db` should *not* be used unless testing a DB-specific utility). Mock *all* external dependencies (other services, LLM clients, database interactions if absolutely necessary for the logic). Use `unittest.mock.patch`, `mocker` fixture (from `pytest-mock`), or dependency injection. For agents, mocks in `backend/apps/main/testing/mock_*.py` or `test_env.py` might be suitable.
    *   **Markers:** `@pytest.mark.test_type("unit")`, `@pytest.mark.component("path.to.module")`

*   **Integration/Workflow Tests:**
    *   **Goal:** Test the interaction between multiple components (e.g., agent calling tools, service using database, full LangGraph flow).
    *   **Setup:** Use the real test database (`@pytest.mark.django_db(transaction=True)`). Mock external *system* boundaries like LLM clients (use `MockLLMClient` via `AgentTestRunner` or fixtures like `configure_agent`), Celery task execution (`patch`), and external APIs. Avoid mocking internal methods or services unless strictly necessary to isolate a specific interaction point. Use `AgentTestRunner` or `WorkflowTestRunner` where applicable.
    -   **Markers:** `@pytest.mark.test_type("integration")` or `@pytest.mark.test_type("workflow")`, `@pytest.mark.component("path.to.main_component")`, potentially `@pytest.mark.agent("AgentName")` or `@pytest.mark.workflow("WorkflowName")`.

*   **LLM Tests:**
    *   **Goal:** Test the interaction with a real Large Language Model. These tests are often slower and may incur costs.
    *   **Setup:** Use the real `RealLLMClient` (controlled by the `USE_REAL_LLM` environment variable). Mock database interactions (`@pytest.mark.django_db(transaction=True)` might still be needed for setup/teardown, but core logic might use mocked DB data) and agent tools to isolate the LLM interaction.
    *   **Markers:** `@pytest.mark.llm`, `@pytest.mark.test_type("llm")`, `@pytest.mark.component("path.to.llm_dependent_component")`.

*   **Component Tests:** (Can overlap with Integration)
    *   **Goal:** Test a larger piece of functionality, potentially spanning multiple services or apps, but still within the backend system.
    *   **Setup:** Similar to Integration tests, using the real database and mocking external system boundaries (e.g., external APIs, frontend interactions if simulated).
    *   **Markers:** `@pytest.mark.test_type("component")`, `@pytest.mark.component("path.to.primary_component")`.

*Benefit:* This layered approach improves test clarity, reduces confusion about what to mock, and makes the test suite more maintainable and reliable.

### Test Coverage

The project uses `pytest-cov` to measure test coverage. To run tests with coverage:

```bash
cd backend
docker-compose run --rm web-test python -m pytest --cov=apps [test_path]
```

To generate a coverage report:

```bash
cd backend
docker-compose run --rm web-test python -m pytest --cov=apps --cov-report=html [test_path]
```

### Metadata Tagging for Enhanced Coverage

Use pytest markers to categorize tests and improve coverage analysis. Consistent tagging is crucial.

**Available Markers:**
- **`@pytest.mark.asyncio`**: For async test functions.
- **`@pytest.mark.django_db`**: For tests needing database access (use `transaction=True` for integration tests).
- **`@pytest.mark.test_type("type")`**: Test category (`unit`, `integration`, `workflow`, `llm`, `component`).
- **`@pytest.mark.component("component_path")`**: Primary component being tested (dot notation, e.g., `main.agents.mentor`).
- **`@pytest.mark.workflow("workflow_name")`**: For tests covering specific LangGraph workflows.
- **`@pytest.mark.agent("AgentClassName")`**: For tests focused on a specific agent.
- **`@pytest.mark.tool("tool_name")`**: For tests focused on a specific agent tool.
- **`@pytest.mark.llm`**: Indicates this test interacts with a real LLM (should be skipped unless `USE_REAL_LLM=true`).

**Example Usage:**
```python
import pytest
from apps.main.agents.mentor_agent import MentorAgent

@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
@pytest.mark.test_type("integration")
@pytest.mark.component("main.agents.mentor")
@pytest.mark.agent("MentorAgent")
async def test_mentor_agent_integration(configure_agent):
    # Test code...
```

### Database Fixtures

Standard pytest-django fixtures:
- `db`: Provides access to the test database (function scope).
- `transactional_db`: Provides database access within a transaction (function scope).

Custom fixtures are defined in `backend/conftest.py` and app-specific `conftest.py` files (e.g., `backend/apps/main/tests/conftest.py`). Examples:
```python
# Example fixtures from conftest.py
@pytest.fixture
def test_user(db):
    # Creates a User and UserProfile instance
    ...
    return user_profile

@pytest.fixture
def test_generic_agent(db):
    # Creates a GenericAgent instance
    ...
    return agent

# Factories (using pytest-factoryboy) are often defined in conftest.py too
@pytest.fixture
def user_profile_factory(db):
    # Returns a factory function to create UserProfile instances
    ...
    return _user_profile_factory
```

### Testing Examples

Refer to `docs/ci-testing/README_TESTING.md` (now archived, content merged here) for detailed examples of testing:
- Agent Nodes
- Tools
- WebSocket Consumers
- Async Celery Tasks

### Testing Admin Views

The admin views for the benchmark system are tested using Django's test client and pytest-asyncio for async views. The tests are organized into separate files for better maintainability:

- `backend/apps/admin_tools/tests/test_benchmark_dashboard_view.py`: Tests for the benchmark dashboard view
- `backend/apps/admin_tools/tests/test_benchmark_history_view.py`: Tests for the benchmark history view
- `backend/apps/admin_tools/tests/test_benchmark_run_view.py`: Tests for the benchmark run API list view
- `backend/apps/admin_tools/tests/test_benchmark_runs_detail_api.py`: Tests for the benchmark run detail API view
- `backend/apps/admin_tools/tests/test_benchmark_scenario_import_export_views.py`: Tests for scenario import/export functionality

**Key Testing Considerations:**

1. **Database Isolation**: Use `@pytest.mark.django_db(transaction=True)` to ensure proper database isolation between tests.
   ```python
   @pytest.mark.django_db(transaction=True)
   @pytest.mark.asyncio
   class TestBenchmarkRunView:
       # Test methods...
   ```

2. **Async Testing**: Test async views using `@pytest.mark.asyncio` and `AsyncClient`.
   ```python
   @pytest.mark.asyncio
   async def test_get_list_staff_user(self, async_client, staff_user, benchmark_run_mentor_yesterday):
       # Test code...
   ```

3. **Fixture Cleanup**: Clean up the database before creating test fixtures to avoid test interdependence.
   ```python
   @pytest_asyncio.fixture(scope="function")
   async def benchmark_scenario_active(db):
       # Clean up existing data
       await sync_to_async(BenchmarkScenario.objects.all().delete)()

       # Create fresh test data
       scenario = await create_test_scenario_async(
           name="Active Scenario",
           description="An active scenario.",
           # Other parameters...
       )
       return scenario
   ```

4. **Robust Assertions**: Verify structure and filtering logic rather than exact counts, making tests more resilient to changes in the database state.
   ```python
   # Instead of asserting exact counts:
   # assert len(data['runs']) == 2  # Brittle

   # Assert structure and filtering logic:
   assert all(run['agent_role'] == AgentRole.MENTOR.value for run in data['runs'])
   assert run_ids == {str(run_orchestrator_today.id), str(run_mentor_today.id)}
   ```

5. **Mocking**: Mock external services like `BenchmarkManager` to isolate the view logic from the underlying services.
   ```python
   @patch('apps.admin_tools.views.BenchmarkManager')
   async def test_post_run_benchmark_staff_user_success(self, mock_benchmark_manager_cls, async_client, staff_user):
       # Configure the mock
       mock_manager_instance = mock_benchmark_manager_cls.return_value
       mock_run_result = MagicMock(spec=BenchmarkRun)
       mock_run_result.id = uuid.uuid4()
       mock_manager_instance.run_benchmark = AsyncMock(return_value=mock_run_result)

       # Test code...
   ```

6. **Permission Testing**: Verify that views enforce proper permissions (anonymous, regular user, staff user).
   ```python
   async def test_get_list_anonymous(self, async_client):
       """Test GET list access for anonymous users."""
       url = reverse('admin:benchmark_runs_api')
       response = await async_client.get(url)
       assert response.status_code == 403
   ```

7. **Error Handling**: Verify that views handle errors gracefully and return appropriate error messages.
   ```python
   @patch('apps.admin_tools.views.BenchmarkManager')
   async def test_post_run_benchmark_manager_value_error(self, mock_benchmark_manager_cls, async_client, staff_user):
       # Configure the mock to raise an error
       mock_manager_instance = mock_benchmark_manager_cls.return_value
       mock_manager_instance.run_benchmark = AsyncMock(side_effect=ValueError("Scenario not found!"))

       # Test code...
       assert response.status_code == 400
       assert 'Scenario not found!' in response.json()['error']
   ```

8. **Response Structure**: Verify that API responses have the expected structure and content.
   ```python
   async def test_get_detail_staff_user(self, async_client, staff_user, benchmark_run_mentor_yesterday):
       # Test code...
       assert data['id'] == str(run.id)
       assert data['scenario'] == run.scenario.name
       assert data['agent_role'] == run.agent_definition.role
       assert data['mean_duration'] == run.mean_duration
       assert data['success_rate'] == run.success_rate
       assert 'raw_results' in data  # Check presence
   ```

For more detailed information about admin view testing, see the [BENCHMARK_SYSTEM.md](../backend/BENCHMARK_SYSTEM.md) document.

### Testing WebSocket Communication

The application uses WebSockets for real-time communication between the server and clients. Testing WebSocket communication requires special considerations.

#### WebSocket Test Setup

```python
import pytest
from channels.testing import WebsocketCommunicator
from django.contrib.auth.models import User
from channels.db import database_sync_to_async
from apps.main.routing import application

@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_websocket_consumer_connection():
    # Create a WebSocket communicator
    communicator = WebsocketCommunicator(application, "/ws/chat/")

    # Connect to the WebSocket
    connected, _ = await communicator.connect()

    try:
        # Test that the connection was accepted
        assert connected

        # Perform test actions...

    finally:
        # Always disconnect when done
        await communicator.disconnect()
```

#### Testing Message Types

The WebSocket communication system supports several message types:

1. **Chat Messages**:
```python
# Send a chat message
await communicator.send_json_to({
    "type": "chat_message",
    "content": "Hello, how can I help you today?",
    "is_user": True
})

# Receive a response
response = await communicator.receive_json_from(timeout=2)
assert response["type"] == "chat_message"
assert "content" in response
```

2. **Processing Status Messages**:
```python
# Send a processing status message
await communicator.send_json_to({
    "type": "processing_status",
    "status": "processing"
})

# Receive a response
response = await communicator.receive_json_from(timeout=2)
assert response["type"] == "processing_status"
assert response["status"] == "processing"
```

3. **Error Messages**:
```python
# Send an error message
await communicator.send_json_to({
    "type": "error",
    "content": "Error message describing what went wrong"
})

# Receive a response
response = await communicator.receive_json_from(timeout=2)
assert response["type"] == "error"
assert "content" in response
```

#### Testing Staff-Only Features

Some WebSocket features, like debug information, are only available to staff users:

```python
@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_debug_info_sent_to_staff_user():
    # Create a staff user
    user = await database_sync_to_async(User.objects.create)(
        username="staff_user",
        is_staff=True
    )

    # Create a WebSocket communicator with the staff user
    communicator = WebsocketCommunicator(application, "/ws/chat/")
    communicator.scope["user"] = user

    connected, _ = await communicator.connect()

    try:
        assert connected

        # Send a message that triggers debug info
        await communicator.send_json_to({
            "type": "chat_message",
            "content": "Agent message triggering debug",
            "is_user": False
        })

        # Receive the debug info message
        response = await communicator.receive_json_from(timeout=2)
        assert response["type"] == "debug_info"
        assert "content" in response

    finally:
        await communicator.disconnect()
```

#### Testing Non-Staff Users

Verify that non-staff users don't receive debug information:

```python
@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_debug_info_not_sent_to_non_staff_user():
    # Create a non-staff user
    user = await database_sync_to_async(User.objects.create)(
        username="regular_user",
        is_staff=False
    )

    # Create a WebSocket communicator with the non-staff user
    communicator = WebsocketCommunicator(application, "/ws/chat/")
    communicator.scope["user"] = user

    connected, _ = await communicator.connect()

    try:
        assert connected

        # Send a message that would trigger debug info for staff
        await communicator.send_json_to({
            "type": "chat_message",
            "content": "Agent message potentially triggering debug",
            "is_user": False
        })

        # Verify no debug info is received
        # Use receive_nothing to assert no message is received within the timeout
        await communicator.receive_nothing(timeout=0.5)

    finally:
        await communicator.disconnect()
```

#### Common WebSocket Testing Issues

1. **Timeout Errors**: When a test is waiting for a message that is never sent
   - Increase the timeout value in `receive_json_from(timeout=X)`
   - Check that the message is actually being sent by the consumer

2. **Assertion Errors**: When the test expects a different message format
   - Verify the message format in the consumer code
   - Update the test to match the current message format

3. **Missing Required Fields**: When a message is missing required fields
   - Check the message construction in the consumer
   - Ensure all required fields are included in the message

4. **WebSocket Connection Cleanup**: Ensure connections are properly cleaned up after tests
   - Always use `try...finally` to disconnect the communicator
   - Check for resource leaks if tests are failing intermittently

For more detailed information about WebSocket message formats and testing, see the [BENCHMARK_SYSTEM.md](../backend/BENCHMARK_SYSTEM.md) and [BENCHMARKING_GUIDE.md](../backend/BENCHMARKING_GUIDE.md) documents.

## Agent Testing

This section focuses on best practices specifically for testing AI agents. For a more comprehensive guide on agent testing, see [AGENT_TESTING_GUIDE.md](./AGENT_TESTING_GUIDE.md), which includes detailed information on handling AppRegistryNotReady errors, making test assertions resilient to different error messages, and example tests.

### Agent Testing Framework (`configure_agent`, `AgentTestRunner`)

The framework provides standardized ways to test agents, handling common setup and mocking. Key fixtures/helpers are often found in `backend/apps/main/testing/` or `backend/apps/main/tests/conftest.py`.

#### Enhanced Agent Test Runner Cleanup

The `AgentTestRunner` class includes an enhanced cleanup method that provides robust error handling, database connection cleanup, task cancellation, and cleanup verification. This helps prevent resource leaks and ensures tests don't interfere with each other.

**Using the Enhanced Cleanup Method:**

```python
@pytest.mark.asyncio
async def test_agent_with_enhanced_cleanup(agent_runner):
    # Create the agent runner
    runner = agent_runner("MentorAgent")

    # Set up test state
    state = create_agent_test_state('mentor')

    try:
        # Run the test
        state_updates = await runner.run_test(state=state)

        # Make assertions
        assert state_updates is not None
    finally:
        # Explicitly call cleanup with a custom timeout
        cleanup_report = await runner.cleanup(timeout_seconds=15)

        # Check cleanup status
        if not cleanup_report["success"]:
            logger.warning(f"Cleanup completed with issues: {cleanup_report}")
```

**Cleanup Report:**

The cleanup method returns a detailed report of the cleanup operations:

```python
cleanup_report = {
    "patches_stopped": 2,       # Number of patches stopped
    "connections_closed": 1,     # Number of database connections closed
    "tasks_cancelled": 3,        # Number of tasks cancelled
    "errors": [],                # List of errors encountered during cleanup
    "success": True,             # Whether cleanup was successful
    "timed_out": False,          # Whether cleanup timed out
    "unclosed_resources": []     # List of resources that couldn't be closed
}
```

**Cleanup Timeout:**

The cleanup method includes a timeout to prevent tests from hanging:

```python
# Set a custom timeout (default is 10 seconds)
cleanup_report = await runner.cleanup(timeout_seconds=30)

# Check if cleanup timed out
if cleanup_report["timed_out"]:
    logger.warning("Cleanup operation timed out")
```

**Synchronous Fallback:**

For tests that don't use async/await, the cleanup method falls back to a synchronous version:

```python
def test_agent_sync(agent_runner):
    # Create the agent runner
    runner = agent_runner("MentorAgent")

    # Clean up resources
    runner.teardown()  # Synchronous version of cleanup
```

**Basic Agent Test Structure (using `configure_agent` fixture):**
```python
import pytest
from apps.main.agents.mentor_agent import MentorAgent # Example

# Assume configure_agent is defined in a conftest.py
# It likely sets up mocks for LLM, tools, memory based on parameters

@pytest.mark.asyncio
async def test_agent_basic_interaction(configure_agent):
    # Configure the agent with mocked LLM and tools
    # The fixture returns the agent instance and mocks
    agent, tool_mock, llm_mock, patches = await configure_agent(
        MentorAgent,
        # Optional: Provide custom responses for mocks
        llm_responses={"default": {"response": "Mocked LLM output"}},
        custom_tool_responses={"get_user_profile": {"name": "Mock User"}}
    )

    try:
        # Prepare input data and initial state
        input_data = {"user_message": "Hello there"}
        initial_state = {"user_profile_id": "test_user_123", "current_stage": "Greeting"}

        # Run the agent (might be .run() or .process() depending on base class)
        output_data, updated_state = await agent.process(initial_state) # Example using .process()

        # Make assertions on output and state
        assert "response" in output_data # Check agent's output structure
        assert updated_state["current_stage"] == "ResponseSent" # Check state transition
        # Verify mocks were called as expected
        llm_mock.invoke.assert_called_once()
        tool_mock.call_tool.assert_called_with("get_user_profile", ...) # Verify tool calls

    finally:
        # Clean up patches applied by the fixture
        for p in patches:
            p.stop()
```

**Agent Testing Best Practices:**
1.  **Use Standard Fixtures:** Leverage `configure_agent`, `AgentTestRunner`, etc., for consistent setup.
2.  **Clean up Patches:** Always use `try...finally` or context managers (`with patch(...)`) to ensure mocks/patches are stopped.
3.  **Isolate Tests:** Each test should be independent. Use factories or fixtures to create fresh data.
4.  **Test Edge Cases:** Cover errors, missing data, unexpected inputs, resource limits, trust boundaries.
5.  **Mark Real LLM Tests:** Use `@pytest.mark.llm` and skip appropriately based on `USE_REAL_LLM`.
6.  **Verify Mock Calls:** Assert that mocks (LLM, tools) were called with the expected arguments.
7.  **Test State Transitions:** Verify that the agent updates the state dictionary correctly.
8.  **Validate Output Structure:** Check that the agent's return value conforms to the expected format.

### Test Data Management

**Standardizing Test Scenarios:**
- Define reusable scenarios in dedicated modules or files (e.g., `scenarios.py`).
- Import scenarios into tests for consistency.

**Test Data Helper Functions/Factories:**
- Use helper functions or factories (like `pytest-factoryboy`) for creating complex test data objects (e.g., `create_test_psychological_assessment`, `user_profile_factory`).

### Assertions and Verification

**Custom Assertions:**
- Consider creating custom assertion helpers for domain-specific checks (e.g., `assert_valid_strategy_framework`).

**Common Assertion Patterns:**
- Asserting state transitions.
- Asserting output structure and content.
- Asserting mock calls (`assert_called_once_with`, `await_count`).

### Testing for Reliability

**Edge Case Scenarios:**
- Test resource constraints, trust boundaries, empty inputs, maximum complexity.

**Error Injection:**
- Deliberately inject errors (e.g., make mocks raise exceptions) to verify agent error handling.
  ```python
  # Example: Make a mocked tool raise an exception
  tool_mock.call_tool.side_effect = Exception("Simulated tool failure")

  # Test that the agent handles the error gracefully
  output_data, updated_state = await agent.process(initial_state)
  assert "error" in output_data # Or check for specific error handling state
  ```

### Testing the Error Handler Agent

The error handler agent requires special testing considerations due to its role in handling errors within the workflow:

**Test Categories for Error Handler Agent:**
1. **Recoverable Errors:** Test that the agent correctly identifies and handles recoverable errors.
   ```python
   # Example: Test recoverable error handling
   @pytest.mark.asyncio
   @pytest.mark.test_type("integration")
   @pytest.mark.component("main.agents.error_handler")
   @pytest.mark.agent("ErrorHandlerAgent")
   async def test_error_handler_recoverable_error(agent_runner):
       # Create test runner for the ErrorHandlerAgent
       runner = agent_runner(ErrorHandlerAgent)

       # Set up state with a recoverable error
       state = State()
       state.error = "Temporary resource unavailable: database connection timeout"

       # Run the test
       state_updates = await runner.run_test(state=state)
       output_data = state_updates['output_data']

       # Verify error was handled correctly
       assert output_data["error_handled"] == True, "Error should be marked as handled"
       assert output_data["next_agent"] != "end", "Should not route to end for recoverable error"
       assert "recovery_plan" in output_data, "Missing recovery plan in output"
   ```

2. **Unrecoverable Errors:** Test that the agent correctly identifies and handles unrecoverable errors.
   ```python
   # Example: Test unrecoverable error handling
   @pytest.mark.asyncio
   @pytest.mark.test_type("integration")
   @pytest.mark.component("main.agents.error_handler")
   @pytest.mark.agent("ErrorHandlerAgent")
   async def test_error_handler_unrecoverable_error(agent_runner):
       # Create test runner for the ErrorHandlerAgent
       runner = agent_runner(ErrorHandlerAgent)

       # Set up state with an unrecoverable error
       state = State()
       state.error = "Critical error: Failed to load profile data"

       # Run the test
       state_updates = await runner.run_test(state=state)
       output_data = state_updates['output_data']

       # Verify error handling for unrecoverable error
       assert output_data["error_handled"] == False, "Unrecoverable error should be marked as not handled"
       assert output_data["next_agent"] == "end", "Should route to end for unrecoverable error"
       assert "user_response" in output_data, "Missing user_response in output"
       assert "completed" in state_updates, "Missing completed flag in state updates"
       assert state_updates["completed"] == True, "Workflow should be marked as completed with error"
   ```

3. **Meta-Errors:** Test that the agent correctly handles errors that occur during its own execution.
   ```python
   # Example: Test meta-error handling
   @pytest.mark.asyncio
   @pytest.mark.test_type("integration")
   @pytest.mark.component("main.agents.error_handler")
   @pytest.mark.agent("ErrorHandlerAgent")
   async def test_error_handler_meta_error(agent_runner):
       # Create test runner for the ErrorHandlerAgent
       runner = agent_runner(ErrorHandlerAgent)

       # Set up the agent instance
       agent, _, _, _ = await runner.setup(user_profile_id="test-user-id")

       # Mock the agent's internal method to raise an exception
       async def mock_analyze_error_exception(*args, **kwargs):
           raise Exception("Internal analysis failed")
       agent._analyze_error = mock_analyze_error_exception

       # Set up state with an error
       state = State()
       state.error = "Original error in activity agent"

       # Run the test and expect MockRunFailedError
       try:
           await runner.run_test(state=state)
           pytest.fail("Expected MockRunFailedError but no exception was raised.")
       except MockRunFailedError as e:
           # Verify the exception message contains the meta-error details
           error_str = str(e)
           assert "internal analysis failed" in error_str.lower()

           # Verify the agent's final state in the mock database
           run_id = list(runner.db_service.runs.keys())[-1]
           run_data = runner.db_service.runs.get(run_id)
           final_output_data = run_data.get('output_data', {})

           # Check for meta-error field
           assert 'meta_error' in final_output_data
           assert "Internal analysis failed" in final_output_data['meta_error']

           # Verify routing to end
           assert final_output_data["next_agent"] == "end"
   ```

4. **Retry Limit:** Test that the agent correctly handles retry limits.
   ```python
   # Example: Test retry limit handling
   @pytest.mark.asyncio
   @pytest.mark.test_type("integration")
   @pytest.mark.component("main.agents.error_handler")
   @pytest.mark.agent("ErrorHandlerAgent")
   async def test_error_handler_retry_limit(agent_runner):
       # Create test runner for the ErrorHandlerAgent
       runner = agent_runner(ErrorHandlerAgent)

       # Set up state with multiple retries already attempted
       state = State()
       state.error = "Temporary resource unavailable: database connection timeout"
       state.retry_count = 3  # Already tried 3 times

       # Run the test
       state_updates = await runner.run_test(state=state)
       output_data = state_updates['output_data']

       # Verify retry limit handling
       assert output_data["error_handled"] == False, "Error after retry limit should be marked as not handled"
       assert output_data["next_agent"] == "end", "Should route to end after retry limit"
       assert "user_response" in output_data, "Missing user_response in output"
       assert "completed" in state_updates, "Missing completed flag in state updates"
       assert state_updates["completed"] == True, "Workflow should be marked as completed after retry limit"
   ```

**Output Structure Considerations:**
- The error handler agent has a unique output structure that depends on whether the error is handled or not:
  - For handled errors: `error_handled=True`, `next_agent` (not "end"), `recovery_plan`, `user_message`
  - For unhandled errors: `error_handled=False`, `next_agent="end"`, `user_response`, `completed=True`
  - For meta-errors: `meta_error`, `next_agent="end"`, `error_handled=False`, `user_response`

**Testing Tools:**
- Use the `agent_test_helpers.ensure_agent_output_structure` function to ensure the error handler agent's output has the required structure.
- Use the `agent_runner` fixture to create a test runner for the error handler agent.
- Use the `MockRunFailedError` exception to test meta-errors in the error handler agent.

## Mocking Strategies

Effective mocking is critical for stable and fast tests, especially unit tests.

### General Principles

- **Targeted Mocking:** Mock only the direct dependencies of the unit under test. Avoid over-mocking.
- **Use `unittest.mock`:** Leverage `patch`, `MagicMock`, and `AsyncMock`.
- **`pytest-mock` Fixture:** Use the `mocker` fixture for convenience (`mocker.patch`, `mocker.MagicMock`).
- **Dependency Injection:** Consider designing classes to accept dependencies (like clients or services) in their constructor for easier mocking during tests.
- **Async Mocking:** Use `AsyncMock` for mocking `async` functions or methods. Verify calls with `await_count` and `assert_awaited_with`.

### Mocking LLM and External Services

- **Unit Tests:** Mock the LLM client completely (e.g., `MagicMock` or `AsyncMock`).
- **Integration Tests:** Use `MockLLMClient` (often provided by fixtures like `configure_agent`) or patch the specific client class used.
- **LLM Tests:** Use the `RealLLMClient` (enabled by `USE_REAL_LLM=true`).

```python
# Example using mocker fixture
@pytest.fixture
def mock_llm_client(mocker):
    """Mock the LLM client for testing"""
    mock_client = mocker.AsyncMock(spec=RealLLMClient) # Use spec for better type hinting
    # Configure return values for methods like chat_completion or invoke
    mock_response = LLMResponse(content="Mocked response", input_tokens=10, output_tokens=20)
    mock_client.invoke.return_value = mock_response
    # Patch the location where the client is instantiated or imported
    mocker.patch("apps.main.llm.get_llm_client", return_value=mock_client)
    return mock_client
```

### Mocking Database Objects (for Unit Tests)

*   **When:** Needed for unit tests of functions/methods operating on Django models/querysets *without* hitting the database (for speed or isolation). **Avoid this in integration tests.**
*   **How:** Use `unittest.mock.MagicMock` or `mocker.MagicMock` to create objects mimicking model instances or querysets. Use `spec=YourModel` for better type checking.
*   **Example:**
    ```python
    from apps.user.models import UserProfile # Import the actual model for spec

    def process_user_profile(user_profile: UserProfile):
        # Function that takes a UserProfile model instance
        if user_profile.is_active:
            return user_profile.display_name.upper() # Use a field from the model
        return None

    def test_process_active_user(mocker):
        mock_user = mocker.MagicMock(spec=UserProfile)
        mock_user.is_active = True
        mock_user.display_name = "Test User"
        # Mock other fields/methods as needed by the function under test
        result = process_user_profile(mock_user)
        assert result == "TEST USER"
    ```
*   **Important:** For tests requiring *real* database interaction (integration/workflow), use `@pytest.mark.django_db` and the test database, not mocked objects.

### Mocking Agent Tools (Benchmarking Context)

- The benchmark system (`BenchmarkManager`, `AgentBenchmarkImproved`) specifically mocks *only* tool calls using a `MockToolRegistry`.
- This allows measuring tool usage while letting the agent interact with the real LLM and database.
- **Configurable Responses:** Tool responses can be configured in `BenchmarkScenario.metadata.mock_tool_responses` using:
    - Simple f-string templates (accessing `tool_input`, `call_count`).
    - Conditional logic based on `tool_input` and `call_count`.
    - Simulation of exceptions using `"$raise_exception"`.
- See `docs/backend/agent_benchmarking.md` (archived, content merged here) for detailed examples.

## CI/CD Integration

Backend tests automatically run on GitHub Actions.

### Workflow Configuration

- **Main Workflow:** `.github/workflows/test-and-coverage.yml`
- **Triggers:** Push/PR to main branches (`main`, `master`, `develop`), manual dispatch.
- **Runner:** Ubuntu latest.

### CI Process for Tests (`test-and-coverage.yml`)

1.  **Setup:** Checkout code, set up Python 3.12, install dependencies, start Redis.
2.  **Environment:** Set necessary environment variables (`DJANGO_SETTINGS_MODULE=config.settings.test`, `TESTING=true`, etc.).
3.  **Database Initialization:** Run `python backend/ultimate_test_setup.py` to create schema and seed data.
4.  **Test Execution:** Run `python -m pytest --import-mode=importlib ...` with coverage collection.
5.  **Reporting:** Upload coverage data to Codecov.

### Coverage Reporting

- **Generation:** `pytest-cov` generates `coverage.json`.
- **Configuration:** Settings in `pyproject.toml` and `backend/.coveragerc`.
- **Upload:** GitHub Action uploads reports to Codecov.io.
- **Analysis:** Codecov compares against thresholds and previous commits.
- **Viewing:** Codecov dashboard, PR comments, README badge.

### Best Practices

- Write tests for all new code
- Keep tests small and focused
- Use fixtures to set up test data
- Use mocks to isolate tests from external dependencies
- Use assertions to verify expected behavior
- Use descriptive test names
- Use comments to explain complex test logic
- Use test utilities to reduce duplication
- Use test coverage to identify untested code
- Tests that use real LLM integration should be maintained and fixed rather than skipped
- Preserve real LLM calls in tests when they're relevant rather than always mocking them

## Benchmarking Tests

While benchmarks primarily measure performance and quality, running them is part of the overall testing strategy.

### Running Benchmarks

- Benchmarks are defined as `BenchmarkScenario` records in the database, loaded from JSON files (e.g., `backend/testing/benchmark_data/`).
- They are run using the `BenchmarkManager` service.
- **Execution:**
    - Via Admin UI (`/admin/benchmarks/`).
    - Via CLI: `python manage.py run_benchmarks --agent-role <role> --params '{"semantic_evaluation": true, ...}'`
    - Via GitHub Actions (`.github/workflows/run-benchmarks.yml`, manual trigger).
- **Key Features:**
    - Measures performance (duration), operational metrics (tool calls, tokens), and semantic quality (LLM eval).
    - Mocks *only* tool calls, uses real LLM/DB.
    - Supports configurable mock tool responses.
    - Performs statistical comparison against previous runs.
    - Includes stage-level performance profiling.

### Benchmark Best Practices (Testing Perspective)

- **Isolate Factors:** Design benchmarks to test specific agent responsibilities.
- **Realistic Scenarios:** Base scenarios on production patterns and edge cases.
- **Track Metrics:** Monitor execution time, success rate, tool calls, semantic scores.
- **Continuous Testing:** Run benchmarks regularly (e.g., manually via Actions on relevant PRs) to catch regressions.
- **Performance Budgets:** Define acceptable performance limits.

### Testing the Benchmark Manager

When writing tests for the `BenchmarkManager` class, follow these best practices:

- **Mock LLM Configurations:** Don't check for exact LLM config instances in assertions. The actual implementation might use different LLM configs than what's passed in the test. Instead, check that an LLM config is present without asserting its exact identity.

```python
# Good practice - flexible assertion
assert call_kwargs.get('llm_config') is not None  # Just check it exists

# Avoid - brittle assertion that might break with implementation changes
assert call_kwargs.get('llm_config') == specific_llm_config_instance
```

- **Handle Optional Fields:** Some fields like `evaluator_llm_model` might not be set in all cases. Make your tests resilient to these variations.

```python
# Flexible approach - don't assume field is always present
if 'evaluator_llm_model' in call_kwargs:
    assert call_kwargs['evaluator_llm_model'] is not None
```

- **Use Context Managers for Mocks:** When using mocks with `patch`, ensure assertions are made within the same context:

```python
# Good practice - assertions inside the context manager
with patch('module.Class.method') as mock_method:
    # Call the code that uses the mocked method
    result = await function_under_test()

    # Make assertions while the mock is still active
    mock_method.assert_called_once()
    assert result == expected_value
```

- **Flexible Raw Results Handling:** The structure of `raw_results` might change as the implementation evolves. Avoid asserting on its exact structure unless that's critical to the test.

- **Use Appropriate Mocking Strategy:** For benchmark manager tests, you typically need to mock:
  - The agent class and instance
  - The LLM config lookup
  - The benchmark runner class and instance
  - Database operations like `BenchmarkRun.objects.create`

### Using Real LLM Calls in Standard Tests (Non-Benchmark)

Mark standard integration tests (`@pytest.mark.django_db`) that *require* actual LLM services with `@pytest.mark.llm`:

```python
import pytest
import os
from apps.main.agents.mentor_agent import MentorAgent

@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
@pytest.mark.llm  # Mark test for real LLM runs
async def test_agent_with_real_llm(configure_agent):
    if os.environ.get('USE_REAL_LLM', 'false').lower() != 'true':
        pytest.skip("Test requires USE_REAL_LLM=true")

    # configure_agent should use RealLLMClient when USE_REAL_LLM=true
    # Ensure tools are appropriately mocked if needed for isolation
    agent, tool_mock, llm_client, patches = await configure_agent(
        MentorAgent,
        # Provide necessary tool mocks even with real LLM
        custom_tool_responses={"get_user_profile": {"name": "Real LLM Test User"}}
    )

    try:
        # Test code interacting with the real LLM...
        pass
    finally:
        for p in patches:
            p.stop()
```
Run these tests with the environment variable set:
```bash
docker-compose -f backend/docker-compose.yml run --rm -e USE_REAL_LLM=true web-test python -m pytest -m llm
```

## Common Testing Issues

### 1. Django AppRegistryNotReady Exception

When running tests that import Django models, you may encounter the `django.core.exceptions.AppRegistryNotReady: Apps aren't loaded yet.` exception. This happens when Django models are imported before the Django app registry is initialized.

**Solutions:**
1. **Use Django's test runner**: Ensure you're using Django's test runner, which properly initializes the app registry before running tests.
   ```bash
   # Use Django's test runner
   docker-compose run --rm web-test python manage.py test apps.main.tests.test_agents
   ```

2. **Use pytest-django**: If using pytest, ensure you have pytest-django properly configured.
   ```bash
   # In pytest.ini
   [pytest]
   DJANGO_SETTINGS_MODULE = config.settings.test
   django_find_project = true
   ```

3. **Import Django models inside test functions**: Avoid importing Django models at module level in test files.
   ```python
   # Instead of this at module level
   from apps.main.models import SomeModel

   # Do this inside test functions
   def test_something():
       from apps.main.models import SomeModel
       # Test code here
   ```

4. **Use django_db fixture**: Ensure you're using the django_db fixture or marker correctly.
   ```python
   @pytest.mark.django_db
   def test_something():
       # Test code here
   ```

5. **Initialize Django manually**: In some cases, you may need to initialize Django manually.
   ```python
   import django
   django.setup()
   ```

6. **Use agent name strings instead of class references**: When using the agent_runner fixture, use the agent name as a string instead of importing the agent class directly.
   ```python
   # Instead of this
   from apps.main.agents.error_handler import ErrorHandlerAgent
   runner = agent_runner(ErrorHandlerAgent)

   # Do this
   runner = agent_runner("ErrorHandlerAgent")
   ```

7. **Create mock implementations for problematic agents**: For agents that import Django models at module level (like the ErrorHandlerAgent), create mock implementations that don't import Django models.
   ```python
   # In apps/main/testing/mock_agents.py
   class MockErrorHandlerAgent:
       """Mock implementation of ErrorHandlerAgent for testing."""

       def __init__(self, user_profile_id, db_service=None, llm_client=None, llm_config=None):
           self.user_profile_id = user_profile_id
           self.agent_role = "error_handler"
           # Initialize other attributes without importing Django models

       async def process(self, state):
           """Handle errors and attempt recovery."""
           # Implement the process method with the same interface
           # but without importing Django models
   ```

8. **Create a mock version of the AgentTestRunner**: If the AgentTestRunner imports Django models, create a mock version that doesn't import Django models.
   ```python
   # In apps/main/testing/mock_agent_test_runner.py
   class MockAgentTestRunner:
       """Mock implementation of AgentTestRunner for testing."""

       def __init__(self, agent_class, use_real_llm=False, llm_config=None):
           self.agent_class = agent_class
           self.use_real_llm = use_real_llm
           # Initialize other attributes without importing Django models

       async def setup(self, user_profile_id="test-user-id", mock_llm_responses=None,
                      mock_tool_responses=None, mock_memory=None, mock_tools=True):
           # Implement setup method with the same interface
           # but without importing Django models

       async def run_test(self, state, mock_llm_responses=None, mock_tool_responses=None,
                         mock_memory=None, mock_tools=True, patch_process=True):
           # Implement run_test method with the same interface
           # but without importing Django models
   ```

9. **Update the agent_runner fixture to use mock implementations**: Modify the agent_runner fixture to use the mock implementations for problematic agents.
   ```python
   @pytest.fixture
   def agent_runner():
       """Factory fixture for creating agent test runners."""
       # Import mock implementations
       from apps.main.testing.mock_agents import MockErrorHandlerAgent
       from apps.main.testing.mock_agent_test_runner import MockAgentTestRunner

       def _create_runner(agent_class_or_name, use_real_llm=False):
           # Special case for ErrorHandlerAgent
           if agent_class_or_name == "ErrorHandlerAgent" or agent_class_or_name == "error_handler":
               return MockAgentTestRunner(MockErrorHandlerAgent, use_real_llm=use_real_llm)

           # Handle other agent types...

           # Always use MockAgentTestRunner to avoid AppRegistryNotReady errors
           return MockAgentTestRunner(agent_class, use_real_llm=use_real_llm)

       return _create_runner
   ```

### 2. Async/Sync Conflicts (`SynchronousOnlyOperation` Error)

- **Cause:** Calling synchronous Django ORM methods (e.g., `User.objects.get()`) directly within an `async def` function without proper wrapping.
- **Solution:** Use the `@database_sync_to_async` decorator correctly.
    - Import: `from channels.db import database_sync_to_async`
    - Pattern:
      ```python
      @database_sync_to_async
      def _get_user_sync(user_id):
          # ONLY sync ORM calls here
          try:
              return User.objects.get(id=user_id)
          except User.DoesNotExist:
              return None

      async def my_async_function(user_id):
          user = await _get_user_sync(user_id)
          if user:
              # Other async operations or logic
              await some_other_async_call(user)
          return user
      ```
    - **Avoid:** Decorating the `async def` function directly. Keep the sync block minimal.

### 2. Database Setup Failures ("No such table", Constraint Errors)

- **Cause:** The project uses standard Django migrations for database schema management. Setup relies on `ultimate_test_setup.py` and seeding commands. Errors occur if this process fails or is inconsistent.
- **Solution:**
    - **Always** run `python backend/ultimate_test_setup.py` before running tests locally or ensure the CI step executes it correctly.
    - Use `pytest --create-db` if you suspect the database schema is stale or corrupt.
    - Ensure tests requiring DB access use `@pytest.mark.django_db`.
    - Make sure the Postgres test database is properly configured and accessible.

### 3. Inconsistent or Failing Mocks

- **Cause:** Incorrect patching targets, issues mocking async functions/methods, problems mocking within specific contexts (like management commands). `await_count` might not work as expected if the mock setup is wrong.
- **Solution:**
    - **Patch Target:** Ensure you patch where the object is *looked up*, not where it's defined (e.g., `patch('apps.main.services.some_module.ExternalServiceClient')` instead of `patch('apps.utils.external_client.ExternalServiceClient')`).
    - **Async Mocking:** Use `AsyncMock` for async functions. Use `await_count` and `assert_awaited_with`.
    - **Management Commands:** Mocking dependencies called *within* management commands can be tricky. Patching the dependency *before* the command's `handle` method is called is often necessary. Refer to patterns in `PLANNING.md`.
    - **Standardize:** Follow the mocking strategy outlined in the "Test Layers" section.

### 4. Tool Registration Issues in Tests

- **Cause:** Tests might rely on tools being registered, but the test setup might not do this correctly or consistently. Relying on management commands for registration in tests is fragile.
- **Solution:**
    - `ultimate_test_setup.py` should handle tool registration directly in the database during setup.
    - Avoid calling `register_tools` management command within tests.
    - Ensure necessary `GenericAgent` and `Tool` instances are created by fixtures or the setup script.

### 5. Circular Model Dependencies / Import Errors

- **Cause:** Complex relationships between models in different apps can cause import loops, especially during test discovery or setup.
- **Solution:**
    - The test settings (`config/settings/test.py`) might have specific configurations (like `MIGRATION_MODULES`) to handle known circular dependencies during testing.
    - Use `pytest --import-mode=importlib` to mitigate some import issues.
    - Refactor models to reduce circular dependencies if possible (long-term fix).

### 6. WebSocket Testing Issues

- **Cause:** Testing Channels consumers requires careful setup and mocking of the `channel_layer`.
- **Solution:** Use `channels.testing.WebsocketCommunicator`. Mock the `channel_layer` (e.g., `communicator.instance.channel_layer`) to verify `group_send` or `send` calls. Handle async operations correctly.

### 7. Celery Task Testing Issues

- **Cause:** Testing the *logic* of a Celery task vs. testing its execution via the broker.
- **Solution:** For unit/integration tests of the task's logic, import the task function and call its `.run()` or `()` method directly. Mock its dependencies as needed. Avoid involving the Celery worker/broker unless specifically testing the queuing mechanism.
```python
# Example: Testing task logic directly
from apps.main.tasks import my_celery_task

@pytest.mark.asyncio
async def test_my_celery_task_logic(mocker):
    mock_dependency = mocker.patch('apps.main.tasks.some_dependency')
    # Call the task's underlying function
    result = await my_celery_task.run(arg1="test", arg2=123)
    assert result == "expected outcome"
    mock_dependency.assert_called_once()
```

### 8. Conflicting Test Models

- **Cause:** Multiple test files defining model classes with the same `app_label` and `db_table` in their `Meta` class. This causes Django to raise a `RuntimeError` about conflicting models during test discovery.
- **Solution:**
  - Ensure each test model has a unique name, even across different test files.
  - Use different `app_label` or `db_table` values in the `Meta` class for test models.
  - Add prefixes to related_name attributes to avoid conflicts.
  - If you need to create test models that mimic real models, consider using a naming convention like `{App}Test{ModelName}` (e.g., `MainTestBenchmarkRun` instead of `TestBenchmarkRun`).

```python
# Example: Properly named test model with unique related_name attributes
class MainTestBenchmarkRun(models.Model):
    """A test version of BenchmarkRun for the main app tests."""
    scenario = models.ForeignKey(BenchmarkScenario, on_delete=models.CASCADE,
                                related_name="main_test_runs")  # Note the prefix
    # ... other fields ...

    class Meta:
        app_label = 'main'  # Same app_label is fine if model names are unique
        db_table = 'main_benchmarkrun'  # Can reuse the same table name if needed
```

### 9. Test Isolation Issues

- **Cause:** Tests that create database objects without proper cleanup can cause subsequent tests to fail or behave unexpectedly. This is especially problematic in tests that create objects with fixed names or unique constraints.
- **Solution:**
  - Use the `@pytest.mark.django_db(transaction=True)` decorator to ensure each test runs in a transaction that is rolled back after the test.
  - Add explicit setup and teardown methods to clean the database before and after each test.
  - Use helper methods to create test objects with unique names to prevent name collisions.
  - Add `try/finally` blocks to ensure cleanup happens even if tests fail.
  - Use utility functions like `generate_unique_scenario_name` from `apps.main.tests.utils` to create unique names for test objects.

```python
# Example: Test class with proper isolation
@pytest.mark.django_db(transaction=True)
class TestBenchmarkScenarioImportExportViews:

    @pytest.fixture(autouse=True)
    def setup_and_teardown(self, db):
        """Setup and teardown for each test."""
        # Setup - clean database state before test
        self.clean_database()

        # Run the test
        yield

        # Teardown - clean database state after test
        self.clean_database()

    def clean_database(self):
        """Clean up the database by removing all test objects."""
        try:
            # Delete all test objects
            BenchmarkScenario.objects.all().delete()
            BenchmarkTag.objects.all().delete()
        except Exception as e:
            logger.error(f"Error cleaning database: {str(e)}")

    def create_test_object(self, name_prefix="Test"):
        """Create a test object with a unique name."""
        unique_name = f"{name_prefix}_{uuid.uuid4().hex[:8]}"
        # Create and return the object
        return SomeModel.objects.create(name=unique_name)

    def test_something(self):
        """Test with proper isolation."""
        try:
            # Create test objects with unique names
            test_object = self.create_test_object()

            # Run the test
            # ...

            # Make assertions
            # ...
        finally:
            # Ensure cleanup happens even if test fails
            self.clean_database()
```

**Best Practices for Test Isolation:**

1. **Use Transactions:** Add `@pytest.mark.django_db(transaction=True)` to ensure each test runs in a transaction that is rolled back after the test.

2. **Clean Before and After:** Add explicit cleanup in both setup and teardown to ensure a clean state for each test.

3. **Use Unique Names:** Generate unique names for test objects to prevent name collisions.

4. **Add Try/Finally Blocks:** Ensure cleanup happens even if tests fail by using `try/finally` blocks.

5. **Reset Mocks:** Reset mocks in `finally` blocks to avoid affecting other tests.

6. **Use Helper Methods:** Create helper methods for common test operations like creating test objects and cleaning up.

7. **Verify Cleanup:** Add logging to verify that cleanup operations completed successfully.

8. **Handle Errors Gracefully:** Add error handling in cleanup methods to ensure tests complete properly even if cleanup operations fail.

9. **Use Autouse Fixtures:** Add `autouse=True` to fixtures that should run for every test in a class.

10. **Isolate Test Data:** Each test should create its own data and not rely on data created by other tests.
