# Test Stability Improvements

This document outlines the test stability improvements implemented to address issues with hanging tests, orphaned tasks, and database connection leaks.

## Overview

The following test stability improvements have been implemented:

| Improvement | Status | Description |
|-------------|--------|-------------|
| **Timeout Mechanism** | ✅ Implemented | Global timeout mechanism for all tests to prevent hanging. Configurable via `@pytest.mark.timeout` marker. |
| **Task Monitoring** | ✅ Implemented | Tracks all asyncio tasks created during tests, detects and cleans up orphaned tasks. Provides detailed reporting. |
| **Database Connection Monitoring** | ✅ Implemented | Tracks database connections during tests, detects and cleans up leaked connections. Adds timeout parameters to prevent hanging queries. |
| **Event Loop Management** | ✅ Implemented | Ensures proper event loop setup and teardown in tests to prevent "no running event loop" errors. |

These improvements have significantly enhanced test stability and reliability, particularly for agent tests with complex async patterns and database interactions.

## Timeout Mechanism

A global timeout mechanism has been implemented in `backend/conftest.py` that applies a default timeout of 60 seconds to all tests. This prevents tests from hanging indefinitely, which can block the entire test suite.

### Features

- Default timeout of 60 seconds for all tests
- Configurable via `@pytest.mark.timeout` marker
- Proper cleanup of timed-out tests with detailed logging
- Handles both synchronous and asynchronous tests
- Compatible with existing test infrastructure

### Usage

```python
# Use default timeout (60 seconds)
@pytest.mark.asyncio
async def test_my_function(test_timeout):
    # Test code here

# Use custom timeout (120 seconds)
@pytest.mark.timeout(120)
@pytest.mark.asyncio
async def test_complex_function(test_timeout):
    # Test code here

# Use explicit timeout context
@pytest.mark.asyncio
async def test_with_explicit_timeout(test_timeout):
    # Setup code here
    async with asyncio.timeout(test_timeout):
        # Code that might hang
    # Assertions here
```

## Task Monitoring

The task monitoring system tracks all asyncio tasks created during tests, compares them with tasks at the end, and logs and cancels any orphaned tasks. This prevents resource leaks and test failures due to orphaned tasks.

### Features

- Tracks all asyncio tasks at test start and compares with tasks at test end
- Logs and cancels orphaned tasks with detailed reporting
- Provides API for manual task management in tests
- Signal handler for runtime debugging (SIGUSR1 dumps stack traces)
- Compatible with existing test infrastructure

### Usage

```python
@pytest.mark.asyncio
async def test_with_task_monitoring(task_monitor, event_loop):
    # Get a snapshot of tasks before the test
    before = task_monitor["get_task_snapshot"]()

    # Run test code
    await my_async_function()

    # Get a snapshot of tasks after the test
    after = task_monitor["get_task_snapshot"]()

    # Clean up any orphaned tasks
    await task_monitor["cleanup_orphaned_tasks"](before, after)
```

## Database Connection Monitoring

The database connection monitoring system tracks all database connections during tests, compares them with connections at the end, and logs and closes any leaked connections. This prevents connection leaks and test failures due to exhausted connection pools.

### Features

- Tracks database connections at test start and compares with connections at test end
- Logs and closes leaked connections with detailed reporting
- Provides API for manual connection management in tests
- Adds timeout parameters to database connections to prevent hanging queries
- Tracks connection sources (file, line number) for debugging
- Compatible with existing test infrastructure

### Usage

```python
@pytest.mark.django_db(transaction=True)
def test_with_db_connection_monitoring(db_connection_monitor):
    # Get a snapshot of connections before the test
    before = db_connection_monitor["get_connection_snapshot"]()

    # Run test code
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")

    # Get a snapshot of connections after the test
    after = db_connection_monitor["get_connection_snapshot"]()

    # Clean up any leaked connections
    db_connection_monitor["cleanup_leaked_connections"](before, after)
```

## Event Loop Management

The event loop management system ensures proper event loop setup and teardown in tests to prevent "no running event loop" errors. This is particularly important for agent tests with complex async patterns.

### Features

- Creates a dedicated event loop for each test
- Ensures proper event loop cleanup in test teardown
- Prevents "no running event loop" errors
- Compatible with existing test infrastructure

### Usage

```python
@pytest.fixture(scope="function")
def event_loop():
    """
    Create an instance of the default event loop for each test case.
    This ensures that each test has its own event loop.
    """
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()
    asyncio.set_event_loop(None)

@pytest.mark.asyncio
async def test_with_event_loop(event_loop):
    # Test code here
    assert asyncio.get_event_loop() is event_loop
```

## Best Practices

### Database Connection Best Practices

1. **Use Context Managers**: Always use context managers for database connections and cursors:
   ```python
   with connection.cursor() as cursor:
       cursor.execute("SELECT 1")
   ```

2. **Use Transaction Management**: Use Django's transaction management to ensure proper cleanup:
   ```python
   from django.db import transaction
   
   with transaction.atomic():
       # Database operations here
   ```

3. **Add Timeout Parameters**: Add timeout parameters to prevent hanging queries:
   ```python
   with connection.cursor() as cursor:
       cursor.execute("SET statement_timeout = 30000")  # 30 seconds
       cursor.execute("SELECT 1")
   ```

4. **Use Async Database Access**: When testing async agents, use `database_sync_to_async` for database operations:
   ```python
   from channels.db import database_sync_to_async
   
   @database_sync_to_async
   def get_agent_from_db(agent_id):
       return Agent.objects.get(id=agent_id)
   
   # In async test
   agent = await get_agent_from_db(1)
   ```

### Task Management Best Practices

1. **Use Task Monitor**: Include the `task_monitor` fixture in tests that create asyncio tasks:
   ```python
   @pytest.mark.asyncio
   async def test_something(task_monitor):
       # Test code here
   ```

2. **Clean Up Tasks**: Explicitly clean up tasks at the end of tests:
   ```python
   # Get a snapshot of tasks before the test
   before = task_monitor["get_task_snapshot"]()
   
   # Run test code
   await my_async_function()
   
   # Clean up any orphaned tasks
   await task_monitor["cleanup_orphaned_tasks"](before, task_monitor["get_task_snapshot"]())
   ```

3. **Use Event Loop Fixture**: Include the `event_loop` fixture in tests that use asyncio:
   ```python
   @pytest.mark.asyncio
   async def test_something(event_loop):
       # Test code here
   ```

4. **Set Timeouts**: Use the `test_timeout` fixture to prevent hanging tests:
   ```python
   @pytest.mark.asyncio
   async def test_something(test_timeout):
       # Test code here
   ```

## Conclusion

These test stability improvements have significantly enhanced the reliability of the test suite, particularly for agent tests with complex async patterns and database interactions. By implementing these improvements, we have addressed the critical issues that were causing tests to hang, fail, or produce inconsistent results.

For more detailed information, see:
- [TESTING_GUIDE.md](TESTING_GUIDE.md)
- [AGENT_TESTING_GUIDE.md](AGENT_TESTING_GUIDE.md)
