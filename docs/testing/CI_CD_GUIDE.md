# CI/CD Pipeline Documentation

This document describes the Continuous Integration and Continuous Deployment (CI/CD) pipeline for the Goali project.

## Table of Contents
- [Overview](#overview)
- [Workflow Architecture](#workflow-architecture)
- [Test Environment Configuration](#test-environment-configuration)
- [Docker Integration](#docker-integration)
- [Automated Testing](#automated-testing)
- [Coverage Reporting](#coverage-reporting)
- [Benchmark Testing](#benchmark-testing)
- [Environment Configuration](#environment-configuration)
- [Deployment Process](#deployment-process)
- [Artifacts](#artifacts)
- [Best Practices](#best-practices)
- [Troubleshooting CI Issues](#troubleshooting-ci-issues)

## Overview

The CI/CD pipeline automates testing, building, and deploying the application to ensure code quality and streamline the delivery process. It is implemented using GitHub Actions and orchestrated through workflow files in the `.github/workflows/` directory.

The pipeline is triggered on:
- Push to main, master, or develop branches
- Pull requests targeting these branches
- Manual trigger via GitHub UI (`workflow_dispatch`)

## Workflow Architecture

The project implements several specialized workflows that handle different aspects of the CI/CD process:

### 1. Main Testing Workflow (`test-and-coverage.yml`)

**Purpose**: Primary workflow for running backend tests and reporting coverage.

**Triggers**:
- Push to `main`, `master`, or `develop` branches
- Pull requests targeting these branches
- Manual trigger via GitHub UI (`workflow_dispatch`)

**Key Steps**:
1. Checkout code
2. Set up Python 3.12 with pip caching
3. Install dependencies from `backend/requirements.txt`
4. Start Redis service container
5. Set environment variables:
   ```
   DJANGO_SETTINGS_MODULE=config.settings.test
   TESTING=true
   PYTEST_DJANGO_AUTODISCOVER=0
   DJANGO_SKIP_CHECKS=1
   DJANGO_ALLOW_ASYNC_UNSAFE=true
   PYTHONPATH=$GITHUB_WORKSPACE/backend
   MISTRAL_API_KEY=${{secrets.MISTRAL_API_KEY}}
   CELERY_BROKER_URL=redis://localhost:6379/0
   ```
6. Initialize test database:
   - Execute `python ultimate_test_setup.py`
   - Runs migrations with `--fake-initial`
   - Executes seeders for baseline data
   - Registers and connects tools
7. Run tests with pytest
8. Upload coverage report to Codecov

### 2. Backend-Specific Tests (`test-backend.yml`)

**Purpose**: Focused on backend testing with additional configuration options.

**Triggers**:
- Push to `main` affecting backend files
- Pull requests affecting backend files

**Key Steps**:
- Similar to main testing workflow but with backend-specific settings
- Generates a separate coverage badge

### 3. Frontend Tests (`test-frontend.yml`)

**Purpose**: Tests the React/TypeScript frontend.

**Triggers**:
- Push to `main` affecting frontend files
- Pull requests affecting frontend files

**Key Steps**:
1. Checkout code
2. Set up Node.js environment
3. Install npm dependencies
4. Run unit tests with Jest
5. (Comment indicates Cypress tests will be added in the future)

### 4. Agent Benchmarks (`run-benchmarks.yml`)

**Purpose**: Runs performance benchmarks on AI agents.

**Triggers**:
- Manual trigger only with input parameters

**Parameters**:
- `tags`: Filter scenarios by tag (e.g., "core,mentor")
- `scenario_ids`: Specific scenario IDs to run
- `output_file`: Path for JSON output
- `report_path`: Path for HTML report

**Key Steps**:
1. Set up Docker Compose
2. Build the web service
3. Run benchmark command with user parameters
4. Extract and upload report artifact

### 5. Docker Build (`docker-build.yml`)

**Purpose**: Builds and validates Docker images.

**Triggers**:
- Push to `main` affecting Docker configuration
- Pull requests affecting Docker configuration

**Key Steps**:
1. Set up Docker Buildx
2. Create environment files
3. Build backend image without pushing
4. Test Docker Compose configuration

### 6. Deployment (`deploy.yml`)

**Purpose**: Deploys application to production environment.

**Triggers**:
- Push to `main` (excluding documentation)
- Manual trigger

**Key Steps**:
1. Set up Docker Buildx
2. Login to GitHub Container Registry
3. Build and push backend image
4. Deploy to hosting service (placeholder implementation)

### 7. Integration Tests (`integration-tests.yml`)

**Purpose**: Runs integration tests using Docker environment.

**Triggers**:
- Push to `main`
- Pull requests to `main`
- Manual trigger

**Key Steps**:
1. Set up Docker environment
2. Create test environment files
3. Start Redis and DB services
4. Run tests using the web-test service
5. Upload coverage artifacts

### 8. Complete CI/CD Pipeline (`ci-cd-pipeline.yml`)

**Purpose**: Combined pipeline covering test, build, and deploy stages.

**Triggers**:
- Push to `main`, `master`, `develop`
- Pull requests to these branches
- Manual trigger

**Key Stages**:
1. **Test**: Complete test suite with coverage
2. **Docker Build**: Builds Docker images
3. **Deploy**: Conditionally deploys to production (main branch only)

## Test Environment Configuration

The test environment is designed to be fast, reliable, and isolated from other environments.

### Key Configuration Files

- `backend/config/settings/test.py`: Django settings for test environment
- `backend/ultimate_test_setup.py`: Script to set up test database and environment
- `backend/conftest.py`: Pytest configuration and fixtures

### Environment Variables

Key environment variables for the test environment:
- `DJANGO_SETTINGS_MODULE=config.settings.test`: Use test-specific Django settings
- `TESTING=true`: Indicates test environment
- `PYTEST_DJANGO_AUTODISCOVER=0`: Custom test discovery
- `DJANGO_SKIP_CHECKS=1`: Skip certain Django checks for speed
- `DJANGO_ALLOW_ASYNC_UNSAFE=true`: Allow async operations in synchronous contexts

## Docker Integration

Docker is used to ensure consistent environments for development, testing, and production.

### Test Container Configuration

The test container is defined in `docker-compose.yml`:

```yaml
web-test:
  build: .
  env_file:
    - .env.test
  volumes:
    - .:/usr/src/app
  depends_on:
    redis:
      condition: service_started
  environment:
    - DJANGO_SETTINGS_MODULE=config.settings.test
    # Additional environment variables...
  command: >
    bash -c "echo 'Setting up test environment...' &&
            python ultimate_test_setup.py &&
            echo 'Starting test runner...' &&
            python -Xfrozen_modules=off -m pytest --reuse-db"
```

## Automated Testing

The automated testing process consists of several key components:

### 1. Database Setup

The test database is initialized using `ultimate_test_setup.py`, which:
1. Sets up the Django environment
2. Creates/resets the SQLite database
3. Applies migrations
4. Creates tables for models without migrations
5. Properly handles SQLite foreign key constraints
6. Seeds minimal test data
7. Registers agent tools

### 2. Pytest Configuration

Pytest is configured through:
- `pyproject.toml`: General pytest configuration
- `conftest.py`: Custom fixtures and setup

Important pytest flags:
- `--reuse-db`: Reuse existing test database
- `--import-mode=importlib`: Use importlib for imports

### 3. Test Execution

In CI, tests are executed with:
```bash
python -m pytest --import-mode=importlib -v --reuse-db
```

## Coverage Reporting

Code coverage is tracked and reported using:
- pytest-cov: Generates coverage data during test execution
- coverage-badge: Creates visual coverage badge
- CodeCov integration: Provides detailed coverage reports and visualization

Coverage configuration:
- Coverage reports are generated in multiple formats:
  - JSON (for dashboard)
  - HTML (for detailed reports)
  - LCOV (for badges)
- Reports are uploaded to CodeCov after successful test runs

## Benchmark Testing

Performance benchmarks help track and optimize agent performance:

- Benchmarks are defined in JSON files
- Each benchmark measures agent performance metrics
- Benchmarks can be run manually via GitHub Actions
- Results are stored and visualized for comparison

## Environment Configuration

The CI environment uses the following key configurations:

- **Database**: In-memory SQLite for testing
- **Cache**: Python dependencies are cached via `actions/setup-python@v5`
- **Docker Registry**: GitHub Container Registry (ghcr.io)
- **Secrets**:
  - `MISTRAL_API_KEY`: For tests using LLM services
  - `GITHUB_TOKEN`: For Docker image pushing
  - `DEPLOY_TOKEN`: For deployment service authentication

## Deployment Process

The deployment process involves:

1. **Build**: Docker images are built and tagged
2. **Push**: Images are pushed to GitHub Container Registry
3. **Deploy**: Images are deployed to the production environment

Security considerations:
- Secrets are stored in GitHub Secrets
- Production credentials are never exposed in CI logs

## Artifacts

The workflows produce several artifacts:

- Test coverage reports
- Coverage badges
- Docker images (tagged by branch)
- Benchmark reports (when manually run)

## Best Practices

### Testing Best Practices

- **Database Management**: Use `--reuse-db` for faster tests, `--create-db` when models change
- **Test Isolation**: Each test should be independent and not rely on side effects from other tests
- **Mocking**: Use mocks for external services, particularly for LLM calls
- **Test Coverage**: Aim for comprehensive test coverage of critical paths
- **Debugging**: Use the debug-tests service for troubleshooting failing tests

### CI/CD Best Practices

- **Fast Feedback**: CI should provide quick feedback on code changes
- **Reliable Tests**: Tests should be deterministic and not flaky
- **Security**: No secrets in code or logs
- **Documentation**: Keep CI/CD documentation updated
- **Monitoring**: Monitor CI pipeline health and performance

## Troubleshooting CI Issues

Common CI issues and solutions:

1. **Database Initialization Failures**:
   - Use the Python-based setup script instead of management commands
   - Ensure proper foreign key constraint handling

2. **Import Errors**:
   - Verify Python path and module structure
   - Use `--import-mode=importlib` to prevent import issues

3. **Tool Registration Issues**:
   - Use direct Python imports instead of management commands
   - Verify module paths and imports

4. **SQLite Constraints Errors**:
   - Disable foreign key constraints before table creation
   - Re-enable constraints after table creation
   - Avoid nested transactions with SQLite