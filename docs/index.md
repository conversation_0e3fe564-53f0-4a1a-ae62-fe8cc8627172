# Welcome to the Goali Documentation

This documentation provides a comprehensive overview of the Goali project, covering its architecture, components, processes, and guidelines.

## Key Documentation Areas

Navigate through the different sections to find the information you need:

*   **[Documentation Map](./DOCUMENTATION_MAP.md):** Overview of all project documentation.
*   **[Global Concepts](./global/1_introduction.md):** Start here for a high-level introduction, ethical framework, user experience, system strategy, technical architecture, roadmap, success framework, resource management, and glossary.
    *   [Glossary](./global/9_global_glossary.md)
*   **[Architecture](./architecture/):** Understand the system's design.
    *   [Models](./architecture/models/): Detailed data models (User, Activity, Beliefs).
    *   [Workflows](./architecture/workflows/): Key process flows like data flow and agent interactions.
*   **[Backend](./backend/):** Dive into the Django backend implementation.
    *   [Agent Reference Guide](./backend/agents/AGENT_REFERENCE.md): Learn about the different AI agents and their workflows.
    *   [Agent Tools](./backend/agent_tools.md): Tools available to agents.
    *   [Benchmark System](./backend/BENCHMARK_SYSTEM.md): Details on the agent benchmarking system.
*   **[Frontend](./frontend/):** Explore the React frontend application. (Note: More detailed frontend docs might be within the `frontend/docs` directory itself).
*   **[API Contract](./api/ApiContract.md):** Specification for WebSocket communication between frontend and backend.
*   **[User Documentation](./users/README_USERS.md):** Information relevant to end-users, including profiles and stories.
*   **[Governance](./governance/):** Project governance structure and status.
*   **[Prompts](./prompts/):** Collection of prompts used for AI generation tasks.
*   **[Documentation Guide](./documentation_guide.md):** Learn how to navigate and maintain this documentation.

## Contribution

Please refer to the [Documentation Guide](./documentation_guide.md) for instructions on how to contribute to and maintain this documentation.
