**5. Technical Architecture**

**5.1 Core Data Structure**

- **Object Categories:**
    - **History and Event Logging:**
         - Comprehensive event tracking via both _HistoryLog_ and _HistoryEvent_ objects that capture user interactions, agent actions, and system events with timestamps, event types, and contextual metadata.
         - Event tracking system supports both generic events and content-type-specific tracking via Django's ContentType framework.
    - **User Models:**
         - _UserProfile_ serves as the central entity connecting all user-related data, incorporating HEXACO personality dimensions instead of Big Five.
         - Sophisticated _Demographics_ model captures detailed user context including occupation, life stage, and environmental factors.
    - **Resource and Environment Models:**
         - Hierarchical environment system with _GenericEnvironment_ representing common settings (e.g., home, office) and _UserEnvironment_ for personalized instances.
         - _EnvironmentDomainSupportMixin_ provides consistent methods for assessing how specific environments support different activity domains.
         - Resource system includes _Inventory_, _ResourceBase_, and _PersonalResource_ with robust relational mapping.
    - **Psychological & Belief Models:**
         - Rich psychological representation through _TrustLevel_, _Belief_, _CharacterTrait_ (with HEXACO alignment), and _CurrentMood_ models.
         - _UserGoal_ framework with specialized subtypes (_Intention_, _Aspiration_) to model both short and long-term objectives.
         - _Belief_ model now tracks emotional charge, awareness level, and stability metrics to inform activity selection.
    - **Activity Models:**
         - Comprehensive _GenericDomain_ categorization system with hierarchical relationships.
         - _GenericActivity_ catalog with extensive metadata and tagging capabilities.
         - _ActivityTailored_ instances incorporate detailed user-specific customization, challenge level calibration, and environmental constraints.
         - Sophisticated tagging system through _Tag_ and _TaggedItem_ models that can be applied to any model in the system.
    - **Game Mechanics – Wheel:**
         - _Wheel_ objects represent user-specific activity collections with probability distributions.
         - _WheelItem_ objects link to _ActivityTailored_ instances and include percentage weighting, visual attributes, and domain information.
    - **Agent System Models:**
         - _GenericAgent_ and _CustomAgent_ models define agent capabilities and behaviors.
         - _AgentRun_, _AgentMetric_, and _AgentMemory_ models track agent execution and performance.
         - _AgentTool_ model defines available tools with input/output schemas and execution rules.

- **Integration & Relationships:**
    - Enhanced integration via Django's ContentType framework for flexible polymorphic relationships.
    - Activity challenge tailoring based on gap analysis between user psychological traits and activity requirements.
    - Environment support scores influence activity selection and wheel probability distribution.
    - Trust levels determine activity challenge calibration in a dynamic feedback loop.

---

**5.2 Agent Ecosystem**

- **LangGraph Integration:**
    - The system leverages LangGraph for defining and orchestrating agent workflows through a declarative graph structure.
    - Each agent is encapsulated as a modular node within the workflow graph with standardized interfaces.
    - _AgentNodeBase_ class provides common functionality for all agent implementations, including run tracking, memory management, and tool execution.

- **Core Agent Roles:**
    - **Orchestrator Agent:**
        - Controls workflow orchestration with stage-based progression (_resource_assessment_ → _engagement_analysis_ → _psychological_assessment_ → _strategy_formulation_ → _activity_selection_ → _ethical_validation_ → _presentation_preparation_).
        - Manages context and state transitions between agent executions, tracking workflow progress in the database.
        - Implements error recovery and state management strategies.
    
    - **Mentor Agent:**
        - Acts as the primary user interface, synthesizing outputs from specialized agents.
        - Provides personalized guidance, reflective feedback, and motivational messaging.
        - Formats responses based on user trust phase and psychological profile.
    
    - **Resource Agent:**
        - Evaluates user environment, available resources, and time constraints.
        - Creates a resource context that informs activity feasibility and tailoring.
        - Flags potential resource limitations that might impact activity recommendations.
    
    - **Psychological Monitoring Agent:**
        - Analyzes user traits, beliefs, and current psychological state.
        - Tracks trust level progression and determines appropriate challenge calibration.
        - Identifies personality traits requiring development and opportunities for growth.
    
    - **Engagement and Pattern Analytics Agent:**
        - Examines historical user interaction patterns and preferences.
        - Identifies trends in activity engagement, refusals, and completions.
        - Provides temporal insights for activity domain balancing and rotation.
    
    - **Strategy Agent:**
        - Combines insights from other agents to formulate a comprehensive activity strategy.
        - Performs gap analysis between user's current traits and activity requirements.
        - Prioritizes domains and challenge levels based on growth opportunities.
    
    - **Wheel and Activity Agent:**
        - Generates personalized activity suggestions based on strategy framework.
        - Creates the wheel with appropriate probability distributions.
        - Ensures activities are properly calibrated for user traits and context.
    
    - **Ethical Agent:**
        - Validates all activity recommendations against ethical principles.
        - Ensures activities respect user boundaries and promote wellbeing.
        - Modifies or rejects potentially harmful suggestions.

- **Conversation Dispatcher:**
    - The _ConversationDispatcher_ serves as the entry point for user interactions, determining the appropriate workflow based on message content.
    - Implements classification logic to route messages to specialized workflows (wheel_generation, activity_feedback, user_onboarding, etc.).
    - Creates standardized context packets containing session metadata, user context, and extracted insights.
    - Launches asynchronous workflows and provides immediate feedback to users while processing continues.

- **Tool System:**
    - Modular tool system allows agents to interact with external systems, databases, and APIs.
    - Tools are registered with input/output schemas and description metadata.
    - Agents dynamically select appropriate tools based on task requirements.
    - Tool execution is tracked and logged for performance optimization and debugging.

---

**5.3 Technology Stack**

- **Frontend:**
    - **Core Framework:**
        - Modern React application with TypeScript for type safety and improved developer experience.
        - Component-based architecture with clear separation of concerns (activities, chat, wheel, layout).
        - Context-based state management via React Context API with optimized re-rendering.
    
    - **Real-time Communication:**
        - WebSocket-based communication for bidirectional, real-time updates.
        - Robust WebSocketManager with comprehensive type definitions, message formatting, and event routing.
        - Context provider (WebSocketContext) for global access to WebSocket functionality throughout the app.
        - Automatic reconnection logic with exponential backoff for resilience against network interruptions.
    
    - **Testing Framework:**
        - Comprehensive testing strategy with unit, integration, and end-to-end tests.
        - Mock WebSocket server for testing real-time communication without backend dependencies.
        - Cypress for browser-based end-to-end testing of the entire application flow.
    
    - **Developer Experience:**
        - Hot module replacement for rapid development iteration.
        - Extensive type definitions for API contracts and data models.
        - Custom hooks for component-specific functionality (useChatMessages, useWheel, useActivity).

- **Backend:**
    - **Core Framework:**
        - Django provides the foundation for the backend with robust ORM, authentication, and admin capabilities.
        - Django Channels extends Django with WebSocket support for real-time communication.
        - Comprehensive model system with migrations, validation, and secure query capabilities.
    
    - **Database:**
        - PostgreSQL serves as the primary relational database for structured data storage.
        - Django's ORM provides a clean abstraction layer for database operations.
        - Models utilize Django's ContentType framework for flexible polymorphic relationships.
    
    - **Asynchronous Processing:**
        - Celery handles background task processing for agent execution and long-running operations.
        - Redis serves as both message broker and result backend for Celery tasks.
        - Task tracking and monitoring via Celery's built-in tools and custom result handlers.
    
    - **Agent Framework:**
        - LangGraph orchestrates the multi-agent system, defining workflow graphs and agent interactions.
        - Modular agent nodes with standardized interfaces for easy extension and customization.
        - Clear separation between agent definitions and execution environment.
    
    - **LLM Integration:**
        - Abstracted LLM client interface supporting multiple providers.
        - AgentLLMExecutor handles agent-specific prompt generation and response parsing.
        - Tool execution framework for integrating external capabilities into LLM workflows.
    
    - **WebSocket Infrastructure:**
        - Django Channels provides the foundation for WebSocket handling via ASGI.
        - UserSessionConsumer implements the WebSocket API contract, handling connections, messages, and events.
        - Channel Groups enable efficient message distribution to connected clients.
    
    - **Monitoring & Logging:**
        - Comprehensive logging throughout the system with configurable verbosity.
        - Prometheus metrics collection for system performance monitoring.
        - Grafana dashboards for visual monitoring of system health and performance.

- **Development & Operations:**
    - **Container Infrastructure:**
        - Docker-based development and deployment environments.
        - Docker Compose for local development with services for Django, PostgreSQL, Redis, and Celery.
        - Environment-specific configuration via settings modules.
    
    - **Testing & Quality Assurance:**
        - Pytest-based backend testing with mocking capabilities for external dependencies.
        - Jest and Cypress for frontend testing across unit, integration, and end-to-end levels.
        - Mock WebSocket infrastructure for testing real-time communications in isolation.
    
    - **Security:**
        - Authentication and authorization via Django's security infrastructure.
        - Secure WebSocket connections with proper authentication and session management.
        - Input validation and sanitation throughout the system.

See [Future Plans](../future/FUTURE_ROADMAP.md#future-technical-architecture) for upcoming enhancements

---
