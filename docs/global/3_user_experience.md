# 3. User Experience

## 3.1 User Profiling & Onboarding

### 3.1.1 Conversational Data Collection
- **Onboarding Approach:** A straightforward data collection process focusing on gathering essential personal information, initial exploration of personal potential
  - introduce the concept of controlled randomness, its purpose and the philosophy of the game of life briefly
- **Data Captured:**
  - Foundational demographics (name, age, occupation, living context)
  - Contextual factors (life stage transitions, daily routines, hobbies)
  - Comprehensive questionnaire to establish initial user profile
- **Collection Method:** Direct intentional questioning with clear, concise prompts to efficiently capture foundational user information and users self perception

### 3.1.2 Psychological Insight Gathering
- **Personality Assessment:** 
  - Evaluation individual psychological dimensions by means of the scientific HEXACO personality trait model/ framework
  - HEXACO assesses Honesty-Humility, Emotionality, eXtraversion, Agreeableness, Conscientiousness, and Openness to Experience
  - scientifically validated personality trait model to ensure nuanced understanding of user's psychological profile
  - Core traits assessment without disclosing technical details so as not to overwhelm the user with technological complexity
- **Belief and Motivation Capture:**
  - Reflective prompts to uncover core beliefs and motivational drivers
  - Focus on understanding user's psychological baseline and potential growth areas framed as self discovery journey
  - Integrated approach to assess beliefs through assessment responses and interaction patterns

### 3.1.3 Immediate Personalized Feedback
- **Feedback Mechanism:** 
  - Clear, text-based summaries of user inputs
  - Emphasis on reinforcing self-awareness and reinforcing self perception
  - Highlighting dominant strengths and unexplored potential growth areas
- **Communication Approach:** 
  - Voice-based communication of tailored activity suggestions
  - Simple Framing of recommendations aligned with user profile
  - familiarize the user with controlled randomness

## 3.2 User Journey & Growth

### 3.2.1 Transformation Phases
#### Foundation Phase
- **Objective:** Establish system credibility and psychological safety
- **Characteristics:**
  - Build user-confidence through low-risk, high-success activities aligned with known user strengths and goals
  - Clear explanations of activities to demonstrate the system's benevolent intent
  - Focus on building initial trust and relationship
- **Example Activity:** A gentle, low-pressure task that ensures user comfort and success

#### Expansion Phase
- **Objective:** Gradually expand user's comfort zone and self-imposed limitations
- **Characteristics:**
  - Leverage User Trust into moderately challenging activities that balance and explore users personality traits
  - Clear explanations of activity benefits
  - Incorporated reflective prompts to capture progress and relationship to uncertainity and disruption of their habitual decision-making patterns
  - Controlled increase in challenge levels

## 3.3 Interface, Engagement & Gamification

### 3.3.1 Core Interface Elements
- **Spin the Wheel Experience:**
  - Simple, voice-interface and wheel presenting personalized activities
  - Clear, plain language describing activities and their purpose
  - Intention to transform Anxiety into Anticipation
  - Communication in motivational language tailored to the Users communication patterns and preferences
  - System discloses any uncertainty in Activity Recommendation to reinforce fairness
- **Daily Check-Ins:**
  - Morning prompts to set daily tone and open possibilities
  - End-of-day reflective questions that reframe completed activities into learning opportunities
- **Progress Tracking:**
  - Basic progress map showing and celebrating user achievements

### 3.3.2 User Interaction Flow
- **Navigation:** 
  - Intuitive flow between onboarding, daily reflections, and activities
  - Simple, consistent visual cues
  - Voice-based primary interaction method
  - Commitment Check before Wheel Spinning
    - Before Spinning the Wheel users review a summary of the recommended activities—displayed with transparent probability distributions and reasons
    - User's are asked to provide explicit confirmation of their willingness to proceed and commit to the any outcome of the Wheel Spin
    - This step validates User Intention and serves as a feedback to adjust activities before spinning the wheel

### 3.3.3 Personalization & Adaptive Feedback
- **Activity Suggestions:**
  - Dynamically tailored based on user profile and dynamic environemental and psychological context
  - respecting individual psychological cboundaries and emerging personality traits
  - Agent Team responable for initial sophisticated personalization in the back-end
- **Challenge Intensity:**
  - Basic adaptive mechanism to adjust and select activities challenge level and dimension
  - Maintain psychological safety through gentle progression

### 3.3.4 Gamification & Motivational Elements
- **Reward Mechanism:**
  - Basic badge system acknowledging small victories
  - Rewards tied to accumulated achievements in specific skill domains and goals (aligned with HEXACO dimensions and beliefs)
  - Recognition of courage in facing uncertainty
- **Narrative Approach:**
  - Simple and smart framing of activities to connect with user's personal context
- Focus on making challenges feel relevant and meaningful (even activities that feel uncomfortable for the user)

See [Future Plans](../future/FUTURE_ROADMAP.md#future-user-experience) for upcoming enhancements
