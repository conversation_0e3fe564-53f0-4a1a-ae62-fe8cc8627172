
## 9. Global Glossary

### 9.1 Glossary Terms

- **Activity**
    - **Definition:** A unit of action prompting user engagement for personal growth through adaptive coaching and controlled randomness.
    - **Related Sections:** Overview, User Experience, The Wheel System & Activity Dimensions.
    - **Data Model Reference:** Abstract class `Activity` (with subclasses `ActivityBase` and `ActivityTailored`).
    - **Notes:** Encompasses fluid dimensions (e.g., creativity, absurdity) and fixed properties (e.g., duration, resources).

- **ActivityBase**
    - **Definition:** The generic template in the activity catalog from which personalized versions are generated.
    - **Related Sections:** The Wheel System & Activity Dimensions.
    - **Data Model Reference:** Class `ActivityBase` (extends `Activity`).
    - **Notes:** Serves as the foundational pool for generating tailored experiences.

- **ActivityTailored**
    - **Definition:** A customized activity modified to align with an individual user's psychological profile and contextual needs.
    - **Related Sections:** User Experience, The Wheel System & Activity Dimensions.
    - **Data Model Reference:** Class `ActivityTailored`.
    - **Notes:** Includes versioning and personalization metadata, such as personal challengingness levels.

- **Wheel**
    - **Definition:** The core game mechanic that uses randomness and weighted probabilities to select a set of activities for the user.
    - **Related Sections:** Interface & Engagement, The Wheel System & Activity Dimensions.
    - **Data Model Reference:** Class `Wheel`.
    - **Notes:** Introduces spontaneity and dynamic engagement into the user experience.

- **WheelItem**
    - **Definition:** An individual segment or component of the Wheel that points to a specific ActivityBase.
    - **Related Sections:** The Wheel System & Activity Dimensions.
    - **Data Model Reference:** Class `WheelItem`.
    - **Notes:** Carries a percentage value determining its probability of selection.

- **User**
    - **Definition:** An individual interacting with the Game of Life, whose demographic and psychological profiles inform personalized system behavior.
    - **Related Sections:** Introduction, User Experience.
    - **Data Model Reference:** Class `User`.
    - **Notes:** Central to all personalized activity and coaching strategies.

- **UserArchetype**
    - **Definition:** A categorization based on personality traits (derived from the Big Five), used to tailor activities and coaching interventions.
    - **Related Sections:** User Profiling, Ethical Framework (Psychological Foundations).
    - **Data Model Reference:** Class `UserArchetype`.
    - **Notes:** Each archetype has a dominance score that influences system calibration.

- **TrustLevel**
    - **Definition:** A dynamic metric indicating the trust a user or archetype has in the system, based on interactions and feedback.
    - **Related Sections:** Adaptive System Strategy, Success Framework.
    - **Data Model Reference:** Class `TrustLevel`.
    - **Notes:** Integral to activity selection, difficulty adjustment, and adaptive coaching.

- **Capacity**
    - **Definition:** A representation of a user's abilities (mental, physical, social, financial) that determines the feasibility of engaging with an activity.
    - **Related Sections:** Technical Architecture (Resource Models).
    - **Data Model Reference:** Class `Capacity`.
    - **Notes:** Used to match activity demands with user skills.

- **Limitation**
    - **Definition:** Constraints that restrict a user's performance or engagement, influencing activity calibration and safety protocols.
    - **Related Sections:** Technical Architecture (Resource Models).
    - **Data Model Reference:** Class `Limitation`.
    - **Notes:** Helps ensure activities are within a user's safe operational bounds.

- **HistoryLog**
    - **Definition:** A generic logging mechanism that records system events, including user interactions and feedback.
    - **Related Sections:** Technical Architecture, Success Framework.
    - **Data Model Reference:** Abstract class `HistoryLog`.
    - **Notes:** Extended by logs such as `UserFeedbackLog` and `UserPsyAgentLog`.

- **UserFeedbackLog**
    - **Definition:** A specialized log capturing user feedback on activities and system interactions, used for performance measurement.
    - **Related Sections:** Success Framework (Performance Metrics).
    - **Data Model Reference:** Class `UserFeedbackLog` (extends `HistoryLog`).
    - **Notes:** Essential for evaluating engagement depth and decision latency.

- **PersonalChallenge**
    - **Definition:** A personalized objective or task designed to promote individual growth by addressing specific psychological needs.
    - **Related Sections:** User Profiling, The Wheel System & Activity Dimensions.
    - **Data Model Reference:** Class `PersonalChallenge`.
    - **Notes:** Links directly with tailored activity challengingness for precise calibration.

- **Agent**
    - **Definition:** An autonomous system component that delivers adaptive coaching, manages activities, and interacts with the user.
    - **Related Sections:** Multi-Agent AI System.
    - **Data Model Reference:** Entity `Agent`.
    - **Notes:** Includes specialized roles such as Mentor Agent, Gamification Agent, etc.

- **AgentMemory**
    - **Definition:** The stored historical data of an agent's interactions and decisions, which informs future actions.
    - **Related Sections:** Multi-Agent AI System.
    - **Data Model Reference:** Entity `AgentMemory`.
    - **Notes:** Connected to the HistoryLog for tracking agent evolution.

- **Onboarding**
    - **Definition:** The initial process of capturing user data, preferences, and intentions to facilitate personalized system adaptation.
    - **Related Sections:** User Profiling, The Role of Intentions & User Onboarding.
    - **Data Model Reference:** Reflected in entities like `Demographics` and `UserGoal`.
    - **Notes:** Critical for establishing trust and guiding adaptive coaching.

- **Adaptive Coaching**    
    - **Definition:** The dynamic method of providing personalized feedback, interventions, and support tailored to user progress and context.
    - **Related Sections:** Adaptive System Strategy, Success Framework.
    - **Data Model Reference:** Implemented via agent interactions and updates to user profiles.
    - **Notes:** Includes various intervention strategies such as supportive, growth, and reflective approaches.

- **UML Diagrams / Knowledge Graphs**
    - **Definition:** Visual representations that depict the system's data models, architecture, and inter-agent relationships.
    - **Related Sections:** Resource Management, Technical Architecture.
    - **Data Model Reference:** Provided through PlantUML diagrams in the model files.
    - **Notes:** Crucial for ensuring clear communication among development teams and stakeholders.