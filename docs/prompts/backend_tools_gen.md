# Tools Documentation

## get_user_profile: Retrieves comprehensive user profile information including demographics, traits, goals, and current context

Retrieves comprehensive user profile information including demographics, traits, goals, and current context.



Input:

    user_profile_id: UUID of the user profile

    include_beliefs: Whether to include user beliefs (default: True)

    include_goals: Whether to include user goals (default: True)

    include_traits: Whether to include personality traits (default: True)

    include_environment: Whether to include current environment details (default: True)

    

Output:

    user_profile: Dictionary with user profile details

    id: UUID of the user profile

    profile_name: Display name of the user

    demographics: Basic demographic information

    traits: User personality trait inclinations (if requested)

    goals: User goals and aspirations (if requested)

    beliefs: User belief system information (if requested)

    current_environment: Current environment context (if requested)

    current_mood: Current mood information (if available)

    trust_level: Trust metrics and phase information

**Function Path:** `apps.main.agents.tools.tools.get_user_profile`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'include_beliefs': {'type': 'string', 'description': 'Whether to include user beliefs (default: True)'}, 'include_goals': {'type': 'string', 'description': 'Whether to include user goals (default: True)'}, 'include_traits': {'type': 'string', 'description': 'Whether to include personality traits (default: True)'}, 'include_environment': {'type': 'string', 'description': 'Whether to include current environment details (default: True)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'user_profile': {'type': 'object', 'description': 'Dictionary with user profile details'}, 'id': {'type': 'string', 'description': 'UUID of the user profile'}, 'profile_name': {'type': 'string', 'description': 'Display name of the user'}, 'demographics': {'type': 'string', 'description': 'Basic demographic information'}, 'traits': {'type': 'string', 'description': 'User personality trait inclinations (if requested)'}, 'goals': {'type': 'string', 'description': 'User goals and aspirations (if requested)'}, 'beliefs': {'type': 'string', 'description': 'User belief system information (if requested)'}, 'current_environment': {'type': 'string', 'description': 'Current environment context (if requested)'}, 'current_mood': {'type': 'string', 'description': 'Current mood information (if available)'}, 'trust_level': {'type': 'string', 'description': 'Trust metrics and phase information'}}}
```

---

## get_activity_details: Retrieves comprehensive details about a tailored activity for presentation to the user

Retrieves comprehensive details about a tailored activity for presentation to the user.



Input:

    activity_id: UUID of the tailored activity

    include_influences: Whether to include influence information (default: True)

    include_domains: Whether to include domain information (default: True)

    include_requirements: Whether to include requirement details (default: True)

    

Output:

    activity: Dictionary with activity details

        id: UUID of the activity

        name: Name of the activity

        description: Detailed description of the activity

        instructions: Step-by-step instructions for completing the activity

        challenge_rating: Overall challenge rating of the activity

        challenge_breakdown: Challenge levels across different personality dimensions

        duration_range: Expected time required for the activity

        domains: Activity domains and their strengths (if requested)

        influences: Factors that influenced activity selection (if requested)

        requirements: Activity requirements details (if requested)

        value_proposition: The benefits and growth opportunities of the activity

**Function Path:** `apps.main.agents.tools.tools.get_activity_details`

**Input Schema:**
```json
{'type': 'object', 'properties': {'activity_id': {'type': 'string', 'description': 'UUID of the tailored activity'}, 'include_influences': {'type': 'string', 'description': 'Whether to include influence information (default: True)'}, 'include_domains': {'type': 'string', 'description': 'Whether to include domain information (default: True)'}, 'include_requirements': {'type': 'string', 'description': 'Whether to include requirement details (default: True)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'activity': {'type': 'object', 'description': 'Dictionary with activity details'}, 'id': {'type': 'string', 'description': 'UUID of the activity'}, 'name': {'type': 'string', 'description': 'Name of the activity'}, 'description': {'type': 'string', 'description': 'Detailed description of the activity'}, 'instructions': {'type': 'string', 'description': 'Step-by-step instructions for completing the activity'}, 'challenge_rating': {'type': 'string', 'description': 'Overall challenge rating of the activity'}, 'challenge_breakdown': {'type': 'string', 'description': 'Challenge levels across different personality dimensions'}, 'duration_range': {'type': 'string', 'description': 'Expected time required for the activity'}, 'domains': {'type': 'string', 'description': 'Activity domains and their strengths (if requested)'}, 'influences': {'type': 'string', 'description': 'Factors that influenced activity selection (if requested)'}, 'requirements': {'type': 'string', 'description': 'Activity requirements details (if requested)'}, 'value_proposition': {'type': 'string', 'description': 'The benefits and growth opportunities of the activity'}}}
```

---

## record_user_feedback: Records and processes user feedback on activities, interactions, or the system

Records and processes user feedback on activities, interactions, or the system.



Input:

    user_profile_id: UUID of the user profile

    feedback_type: Type of feedback (pre_spin, post_spin, activity_completion, system)

    content_type: Type of the entity the feedback is about (optional)

    object_id: ID of the entity the feedback is about (optional)

    user_comment: User's feedback text

    rating: Numerical rating if applicable (0-100)

    sentiment: Analyzed sentiment of the feedback (positive, neutral, negative)

    context_data: Additional contextual data for the feedback

    criticality: Importance level of the feedback (1-5)

    

Output:

    feedback: Dictionary with feedback details

        id: ID of the created feedback record

        status: Status of the feedback recording

        action_taken: Any immediate actions taken based on feedback

**Function Path:** `apps.main.agents.tools.tools.record_user_feedback`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'feedback_type': {'type': 'string', 'description': 'Type of feedback (pre_spin, post_spin, activity_completion, system)'}, 'content_type': {'type': 'string', 'description': 'Type of the entity the feedback is about (optional)'}, 'object_id': {'type': 'string', 'description': 'ID of the entity the feedback is about (optional)'}, 'user_comment': {'type': 'string', 'description': "User's feedback text"}, 'rating': {'type': 'string', 'description': 'Numerical rating if applicable (0-100)'}, 'sentiment': {'type': 'string', 'description': 'Analyzed sentiment of the feedback (positive, neutral, negative)'}, 'context_data': {'type': 'string', 'description': 'Additional contextual data for the feedback'}, 'criticality': {'type': 'string', 'description': 'Importance level of the feedback (1-5)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'feedback': {'type': 'object', 'description': 'Dictionary with feedback details'}, 'id': {'type': 'string', 'description': 'ID of the created feedback record'}, 'status': {'type': 'string', 'description': 'Status of the feedback recording'}, 'action_taken': {'type': 'string', 'description': 'Any immediate actions taken based on feedback'}}}
```

---

## get_recent_interactions: Retrieves recent user interactions and history to maintain context in conversations

Retrieves recent user interactions and history to maintain context in conversations.



Input:

    user_profile_id: UUID of the user profile

    max_items: Maximum number of history items to retrieve (default: 10)

    event_types: List of event types to filter by (optional)

    since_timestamp: Only retrieve events after this timestamp (optional)

    include_feedback: Whether to include feedback events (default: True)

    

Output:

    interactions: List of recent interactions in chronological order

        id: ID of the history event

        timestamp: When the event occurred

        event_type: Type of the event

        details: Additional details about the event

        related_entity: Information about the related entity if applicable

**Function Path:** `apps.main.agents.tools.tools.get_recent_interactions`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'max_items': {'type': 'string', 'description': 'Maximum number of history items to retrieve (default: 10)'}, 'event_types': {'type': 'string', 'description': 'List of event types to filter by (optional)'}, 'since_timestamp': {'type': 'string', 'description': 'Only retrieve events after this timestamp (optional)'}, 'include_feedback': {'type': 'string', 'description': 'Whether to include feedback events (default: True)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'interactions': {'type': 'array', 'description': 'List of recent interactions in chronological order'}, 'id': {'type': 'string', 'description': 'ID of the history event'}, 'timestamp': {'type': 'string', 'description': 'When the event occurred'}, 'event_type': {'type': 'string', 'description': 'Type of the event'}, 'details': {'type': 'string', 'description': 'Additional details about the event'}, 'related_entity': {'type': 'string', 'description': 'Information about the related entity if applicable'}}}
```

---

## update_current_mood: Updates or creates the current mood record for a user based on interaction analysis

Updates or creates the current mood record for a user based on interaction analysis.



Input:

    user_profile_id: UUID of the user profile

    description: Text description of the mood

    height: Intensity level of the mood (0-100)

    user_awareness: Level of user's awareness of their mood (0-100)

    inferred_from_text: Whether the mood was inferred from user text (vs explicitly stated)

    source_text: Optional text from which the mood was inferred

    

Output:

    mood: Dictionary with the updated mood information

        id: ID of the mood record

        description: Text description of the mood

        height: Intensity level of the mood

        user_awareness: Level of user's awareness

        processed_at: Timestamp when the mood was processed

        previous_mood: Previous mood information if available

**Function Path:** `apps.main.agents.tools.tools.update_current_mood`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'description': {'type': 'string', 'description': 'Text description of the mood'}, 'height': {'type': 'string', 'description': 'Intensity level of the mood (0-100)'}, 'user_awareness': {'type': 'string', 'description': "Level of user's awareness of their mood (0-100)"}, 'inferred_from_text': {'type': 'string', 'description': 'Whether the mood was inferred from user text (vs explicitly stated)'}, 'source_text': {'type': 'string', 'description': 'Optional text from which the mood was inferred'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'mood': {'type': 'object', 'description': 'Dictionary with the updated mood information'}, 'id': {'type': 'string', 'description': 'ID of the mood record'}, 'description': {'type': 'string', 'description': 'Text description of the mood'}, 'height': {'type': 'string', 'description': 'Intensity level of the mood'}, 'user_awareness': {'type': 'string', 'description': "Level of user's awareness"}, 'processed_at': {'type': 'string', 'description': 'Timestamp when the mood was processed'}, 'previous_mood': {'type': 'string', 'description': 'Previous mood information if available'}}}
```

---

## generate_reflection_prompt: Generates personalized reflection prompts for users based on their activities and growth journey

Generates personalized reflection prompts for users based on their activities and growth journey.



Input:

    user_profile_id: UUID of the user profile

    activity_id: UUID of the activity to reflect on (optional)

    prompt_type: Type of reflection prompt (post_activity, daily_check_in, weekly_review)

    focus_areas: List of specific areas to focus on in the reflection (optional)

    trust_phase: Current trust phase (foundation/expansion) if known (optional)

    

Output:

    prompt: Dictionary with reflection prompt details

        text: The reflection prompt text

        questions: List of specific reflection questions

        custom_framing: Any custom framing for the reflection based on user context

        suggested_focus: Suggested areas to focus reflection on

**Function Path:** `apps.main.agents.tools.tools.generate_reflection_prompt`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'activity_id': {'type': 'string', 'description': 'UUID of the activity to reflect on (optional)'}, 'prompt_type': {'type': 'string', 'description': 'Type of reflection prompt (post_activity, daily_check_in, weekly_review)'}, 'focus_areas': {'type': 'string', 'description': 'List of specific areas to focus on in the reflection (optional)'}, 'trust_phase': {'type': 'string', 'description': 'Current trust phase (foundation/expansion) if known (optional)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'prompt': {'type': 'object', 'description': 'Dictionary with reflection prompt details'}, 'text': {'type': 'string', 'description': 'The reflection prompt text'}, 'questions': {'type': 'array', 'description': 'List of specific reflection questions'}, 'custom_framing': {'type': 'string', 'description': 'Any custom framing for the reflection based on user context'}, 'suggested_focus': {'type': 'string', 'description': 'Suggested areas to focus reflection on'}}}
```

---

## get_wheel_details: Retrieves details about a wheel and its current state for presentation to the user

Retrieves details about a wheel and its current state for presentation to the user.



Input:

    wheel_id: ID of the wheel to retrieve

    include_activities: Whether to include detailed activity information (default: True)

    

Output:

    wheel: Dictionary with wheel details

        id: ID of the wheel

        name: Name of the wheel

        created_at: When the wheel was created

        items: List of wheel items with probability percentages and activity summaries

**Function Path:** `apps.main.agents.tools.tools.get_wheel_details`

**Input Schema:**
```json
{'type': 'object', 'properties': {'wheel_id': {'type': 'string', 'description': 'ID of the wheel to retrieve'}, 'include_activities': {'type': 'string', 'description': 'Whether to include detailed activity information (default: True)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'wheel': {'type': 'object', 'description': 'Dictionary with wheel details'}, 'id': {'type': 'string', 'description': 'ID of the wheel'}, 'name': {'type': 'string', 'description': 'Name of the wheel'}, 'created_at': {'type': 'string', 'description': 'When the wheel was created'}, 'items': {'type': 'array', 'description': 'List of wheel items with probability percentages and activity summaries'}}}
```

---

## present_activity_options: Generates user-friendly presentation of activity options from a wheel or set of activities

Generates user-friendly presentation of activity options from a wheel or set of activities.



Input:

    user_profile_id: UUID of the user profile

    wheel_id: ID of the wheel containing activities (optional if activities provided)

    activities: List of activity IDs to present (optional if wheel_id provided)

    trust_phase: Current trust phase (foundation/expansion) if known

    presentation_style: Preferred presentation style (brief, standard, detailed)

    

Output:

    presentation: Dictionary with presentation details

        activity_summaries: List of activity summaries for presentation

        framing: Suggested framing for presenting the options

        focus_message: Message highlighting the focus or theme of these options

**Function Path:** `apps.main.agents.tools.tools.present_activity_options`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'wheel_id': {'type': 'string', 'description': 'ID of the wheel containing activities (optional if activities provided)'}, 'activities': {'type': 'string', 'description': 'List of activity IDs to present (optional if wheel_id provided)'}, 'trust_phase': {'type': 'string', 'description': 'Current trust phase (foundation/expansion) if known'}, 'presentation_style': {'type': 'string', 'description': 'Preferred presentation style (brief, standard, detailed)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'presentation': {'type': 'object', 'description': 'Dictionary with presentation details'}, 'activity_summaries': {'type': 'array', 'description': 'List of activity summaries for presentation'}, 'framing': {'type': 'string', 'description': 'Suggested framing for presenting the options'}, 'focus_message': {'type': 'string', 'description': 'Message highlighting the focus or theme of these options'}}}
```

---

## get_communication_guidelines: Retrieves personalized communication guidelines for interacting with a specific user

Retrieves personalized communication guidelines for interacting with a specific user.



Input:

    user_profile_id: UUID of the user profile

    

Output:

    guidelines: Dictionary with communication guidelines

        tone: Preferred communication tone

        detail_level: Preferred level of detail in explanations

        metaphor_types: Types of metaphors that resonate with the user

        pacing: Recommended pacing of information delivery

        formatting: Preferred formatting styles

        customizations: Any user-specific customizations

**Function Path:** `apps.main.agents.tools.tools.get_communication_guidelines`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'guidelines': {'type': 'object', 'description': 'Dictionary with communication guidelines'}, 'tone': {'type': 'string', 'description': 'Preferred communication tone'}, 'detail_level': {'type': 'string', 'description': 'Preferred level of detail in explanations'}, 'metaphor_types': {'type': 'string', 'description': 'Types of metaphors that resonate with the user'}, 'pacing': {'type': 'string', 'description': 'Recommended pacing of information delivery'}, 'formatting': {'type': 'string', 'description': 'Preferred formatting styles'}, 'customizations': {'type': 'string', 'description': 'Any user-specific customizations'}}}
```

---

## explain_activity_selection: Generates a personalized explanation of why an activity was selected for a user

Generates a personalized explanation of why an activity was selected for a user.



Input:

    user_profile_id: UUID of the user profile

    activity_id: UUID of the activity to explain

    detail_level: Desired level of detail (brief, standard, detailed)

    include_context: Whether to include contextual factors in the explanation

    

Output:

    explanation: Dictionary with explanation details

        summary: Brief summary of why the activity was selected

        alignment_points: List of ways the activity aligns with user needs/goals

        growth_opportunities: Growth opportunities this activity provides

        tailoring_aspects: How the activity was tailored to the user

        contextual_factors: How contextual factors influenced selection (if requested)

**Function Path:** `apps.main.agents.tools.tools.explain_activity_selection`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'activity_id': {'type': 'string', 'description': 'UUID of the activity to explain'}, 'detail_level': {'type': 'string', 'description': 'Desired level of detail (brief, standard, detailed)'}, 'include_context': {'type': 'string', 'description': 'Whether to include contextual factors in the explanation'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'explanation': {'type': 'object', 'description': 'Dictionary with explanation details'}, 'summary': {'type': 'string', 'description': 'Brief summary of why the activity was selected'}, 'alignment_points': {'type': 'array', 'description': 'List of ways the activity aligns with user needs/goals'}, 'growth_opportunities': {'type': 'string', 'description': 'Growth opportunities this activity provides'}, 'tailoring_aspects': {'type': 'string', 'description': 'How the activity was tailored to the user'}, 'contextual_factors': {'type': 'string', 'description': 'How contextual factors influenced selection (if requested)'}}}
```

---

## calculate_trust_level: Calculates the current trust level and phase for a user based on interaction history and feedback

Calculates the current trust level and phase for a user based on interaction history and feedback.



Input:

    user_profile_id: UUID of the user profile

    include_history: Whether to include trust history data (default: False)

    time_period: Time period for recent trust analysis (last_week, last_month, all_time)

    

Output:

    trust_data: Dictionary with trust metrics

        unified_score: Overall trust score (0-100)

        phase: Current trust phase (foundation/expansion)

        dimensions: Detailed metrics for different trust dimensions

        critical_factors: Key factors influencing current trust level

        history: Trust development over time (if requested)

        phase_transition: Information about recent or upcoming phase transitions

**Function Path:** `apps.main.agents.tools.tools.calculate_trust_level`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'include_history': {'type': 'string', 'description': 'Whether to include trust history data (default: False)'}, 'time_period': {'type': 'string', 'description': 'Time period for recent trust analysis (last_week, last_month, all_time)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'trust_data': {'type': 'object', 'description': 'Dictionary with trust metrics'}, 'unified_score': {'type': 'string', 'description': 'Overall trust score (0-100)'}, 'phase': {'type': 'string', 'description': 'Current trust phase (foundation/expansion)'}, 'dimensions': {'type': 'string', 'description': 'Detailed metrics for different trust dimensions'}, 'critical_factors': {'type': 'string', 'description': 'Key factors influencing current trust level'}, 'history': {'type': 'string', 'description': 'Trust development over time (if requested)'}, 'phase_transition': {'type': 'string', 'description': 'Information about recent or upcoming phase transitions'}}}
```

---

## generate_wheel: Creates a new activity wheel with tailored activities based on the strategy framework

Creates a new activity wheel with tailored activities based on the strategy framework.



Input:

    user_profile_id: UUID of the user profile

    strategy_framework: Dictionary with the selection strategy parameters

        domain_distribution: Dictionary with domain weights

        challenge_calibration: Dictionary with challenge parameters

        selection_constraints: Dictionary with constraints and filters

        trust_phase: Current trust phase (foundation/expansion)

    activity_count: Number of activities to include in the wheel (default: 8)

    

Output:

    wheel: Dictionary with wheel details

        id: ID of the created wheel

        name: Name of the wheel

        items: List of wheel items with probability percentages and activity summaries

        activities: List of tailored activities created for this wheel

        domain_coverage: Actual domain distribution achieved

        challenge_metrics: Challenge metrics for the selected activities

**Function Path:** `apps.main.agents.tools.tools.generate_wheel`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'strategy_framework': {'type': 'string', 'description': 'Dictionary with the selection strategy parameters'}, 'domain_distribution': {'type': 'string', 'description': 'Dictionary with domain weights'}, 'challenge_calibration': {'type': 'string', 'description': 'Dictionary with challenge parameters'}, 'selection_constraints': {'type': 'number', 'description': 'Dictionary with constraints and filters'}, 'trust_phase': {'type': 'string', 'description': 'Current trust phase (foundation/expansion)'}, 'activity_count': {'type': 'number', 'description': 'Number of activities to include in the wheel (default: 8)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'wheel': {'type': 'object', 'description': 'Dictionary with wheel details'}, 'id': {'type': 'string', 'description': 'ID of the created wheel'}, 'name': {'type': 'string', 'description': 'Name of the wheel'}, 'items': {'type': 'array', 'description': 'List of wheel items with probability percentages and activity summaries'}, 'activities': {'type': 'array', 'description': 'List of tailored activities created for this wheel'}, 'domain_coverage': {'type': 'string', 'description': 'Actual domain distribution achieved'}, 'challenge_metrics': {'type': 'string', 'description': 'Challenge metrics for the selected activities'}}}
```

---

## validate_activities: Performs ethical validation of activities and wheel composition for alignment with system principles

Performs ethical validation of activities and wheel composition for alignment with system principles.



Input:

    user_profile_id: UUID of the user profile

    activity_ids: List of activity IDs to validate, or None to validate all activities in a wheel

    wheel_id: ID of the wheel to validate (required if activity_ids not provided)

    trust_phase: Current trust phase (foundation/expansion)

    validation_level: Depth of validation (standard, comprehensive)

    

Output:

    validation: Dictionary with validation results

        approved: Whether all activities are approved

        activities: List of activity validation results

            id: Activity ID

            name: Activity name

            approved: Whether this activity is approved

            concerns: List of ethical concerns if any

            recommendations: List of modification recommendations if not approved

        wheel_assessment: Wheel-level validation results (if wheel_id provided)

            approved: Whether the wheel composition is approved

            concerns: List of wheel-level ethical concerns if any

            recommendations: List of wheel-level recommendations

        ethical_rationale: Overall ethical assessment rationale

**Function Path:** `apps.main.agents.tools.tools.validate_activities`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'activity_ids': {'type': 'string', 'description': 'List of activity IDs to validate, or None to validate all activities in a wheel'}, 'wheel_id': {'type': 'string', 'description': 'ID of the wheel to validate (required if activity_ids not provided)'}, 'trust_phase': {'type': 'string', 'description': 'Current trust phase (foundation/expansion)'}, 'validation_level': {'type': 'string', 'description': 'Depth of validation (standard, comprehensive)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'validation': {'type': 'object', 'description': 'Dictionary with validation results'}, 'approved': {'type': 'string', 'description': 'Whether the wheel composition is approved'}, 'activities': {'type': 'array', 'description': 'List of activity validation results'}, 'id': {'type': 'string', 'description': 'Activity ID'}, 'name': {'type': 'string', 'description': 'Activity name'}, 'concerns': {'type': 'array', 'description': 'List of wheel-level ethical concerns if any'}, 'recommendations': {'type': 'array', 'description': 'List of wheel-level recommendations'}, 'wheel_assessment': {'type': 'string', 'description': 'Wheel-level validation results (if wheel_id provided)'}, 'ethical_rationale': {'type': 'string', 'description': 'Overall ethical assessment rationale'}}}
```

---

## analyze_trait_gap: Analyzes the gap between user traits and activity requirements to calculate challenge levels

Analyzes the gap between user traits and activity requirements to calculate challenge levels.



Input:

    user_profile_id: UUID of the user profile

    activity_id: UUID of the activity to analyze (optional)

    trait_codes: List of specific trait codes to analyze (optional)

    include_growth_opportunities: Whether to include detailed growth information

    

Output:

    analysis: Dictionary with trait gap analysis

        overall_gap: Overall trait gap score (0-100)

        trait_gaps: Detailed breakdown of gaps by trait

        challenge_recommendations: Challenge level recommendations by trait dimension

        growth_opportunities: Identified growth areas based on gaps (if requested)

        strategy_implications: Implications for activity selection strategy

**Function Path:** `apps.main.agents.tools.tools.analyze_trait_gap`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'activity_id': {'type': 'string', 'description': 'UUID of the activity to analyze (optional)'}, 'trait_codes': {'type': 'string', 'description': 'List of specific trait codes to analyze (optional)'}, 'include_growth_opportunities': {'type': 'string', 'description': 'Whether to include detailed growth information'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'analysis': {'type': 'object', 'description': 'Dictionary with trait gap analysis'}, 'overall_gap': {'type': 'string', 'description': 'Overall trait gap score (0-100)'}, 'trait_gaps': {'type': 'string', 'description': 'Detailed breakdown of gaps by trait'}, 'challenge_recommendations': {'type': 'string', 'description': 'Challenge level recommendations by trait dimension'}, 'growth_opportunities': {'type': 'string', 'description': 'Identified growth areas based on gaps (if requested)'}, 'strategy_implications': {'type': 'string', 'description': 'Implications for activity selection strategy'}}}
```

---

## formulate_strategy: Formulates a comprehensive activity selection strategy based on multi-agent inputs

Formulates a comprehensive activity selection strategy based on multi-agent inputs.



Input:

    user_profile_id: UUID of the user profile

    resource_context: Resource analysis from Resource & Capacity Agent

    engagement_patterns: Engagement analysis from Engagement & Pattern Agent

    psychological_assessment: Psychological state from Psychological Agent

    trait_gap_analysis: Gap analysis from Gap Analysis tool

    trust_phase: Current trust phase (foundation/expansion)

    strategy_focus: Emphasis area for strategy (growth, engagement, balance)

    

Output:

    strategy: Dictionary with comprehensive strategy framework

        domain_distribution: Recommended distribution across domains

        challenge_calibration: Challenge parameters across personality dimensions

        selection_constraints: Filtering constraints for activity selection

        value_emphasis: Areas to emphasize in activity value propositions

        rationale: Detailed explanation of strategy decisions

**Function Path:** `apps.main.agents.tools.tools.formulate_strategy`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'resource_context': {'type': 'string', 'description': 'Resource analysis from Resource & Capacity Agent'}, 'engagement_patterns': {'type': 'string', 'description': 'Engagement analysis from Engagement & Pattern Agent'}, 'psychological_assessment': {'type': 'string', 'description': 'Psychological state from Psychological Agent'}, 'trait_gap_analysis': {'type': 'string', 'description': 'Gap analysis from Gap Analysis tool'}, 'trust_phase': {'type': 'string', 'description': 'Current trust phase (foundation/expansion)'}, 'strategy_focus': {'type': 'string', 'description': 'Emphasis area for strategy (growth, engagement, balance)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'strategy': {'type': 'object', 'description': 'Dictionary with comprehensive strategy framework'}, 'domain_distribution': {'type': 'string', 'description': 'Recommended distribution across domains'}, 'challenge_calibration': {'type': 'string', 'description': 'Challenge parameters across personality dimensions'}, 'selection_constraints': {'type': 'string', 'description': 'Filtering constraints for activity selection'}, 'value_emphasis': {'type': 'string', 'description': 'Areas to emphasize in activity value propositions'}, 'rationale': {'type': 'string', 'description': 'Detailed explanation of strategy decisions'}}}
```

---

## analyze_environment_compatibility: Analyzes the compatibility between user's environment and activities for resource-aware recommendations

Analyzes the compatibility between user's environment and activities for resource-aware recommendations.



Input:

    user_profile_id: UUID of the user profile

    environment_id: UUID of the specific environment to analyze (optional, uses current if not provided)

    activity_id: UUID of a specific activity to analyze compatibility for (optional)

    include_domain_analysis: Whether to include detailed domain support analysis (default: True)

    include_resource_analysis: Whether to include resource availability analysis (default: True)

    

Output:

    analysis: Dictionary with environment compatibility analysis

        current_environment: Information about the analyzed environment

        domain_support: Ratings for how well the environment supports different activity domains

        resource_availability: Analysis of resources available in this environment

        activity_compatibility: Compatibility ratings for specific activities (if requested)

        environmental_constraints: Constraints that should inform activity selection

        recommended_adaptations: Suggested adaptations to improve compatibility

**Function Path:** `apps.main.agents.tools.tools.analyze_environment_compatibility`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'environment_id': {'type': 'string', 'description': 'UUID of the specific environment to analyze (optional, uses current if not provided)'}, 'activity_id': {'type': 'string', 'description': 'UUID of a specific activity to analyze compatibility for (optional)'}, 'include_domain_analysis': {'type': 'string', 'description': 'Whether to include detailed domain support analysis (default: True)'}, 'include_resource_analysis': {'type': 'string', 'description': 'Whether to include resource availability analysis (default: True)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'analysis': {'type': 'object', 'description': 'Dictionary with environment compatibility analysis'}, 'current_environment': {'type': 'string', 'description': 'Information about the analyzed environment'}, 'domain_support': {'type': 'string', 'description': 'Ratings for how well the environment supports different activity domains'}, 'resource_availability': {'type': 'string', 'description': 'Analysis of resources available in this environment'}, 'activity_compatibility': {'type': 'string', 'description': 'Compatibility ratings for specific activities (if requested)'}, 'environmental_constraints': {'type': 'string', 'description': 'Constraints that should inform activity selection'}, 'recommended_adaptations': {'type': 'string', 'description': 'Suggested adaptations to improve compatibility'}}}
```

---

## handle_activity_refusal: Processes user refusal of an activity and provides alternative suggestions

Processes user refusal of an activity and provides alternative suggestions.



Input:

    user_profile_id: UUID of the user profile

    activity_id: UUID of the refused activity

    refusal_reason: Reason for refusal (time_constraint, difficulty, interest, etc.)

    refusal_comment: User's comment explaining the refusal

    request_alternative: Whether to request an alternative activity

    

Output:

    result: Dictionary with refusal handling results

        recorded: Whether the refusal was successfully recorded

        understanding_message: Supportive response acknowledging the refusal

        alternative_suggestion: Alternative activity suggestion if requested

        trust_impact: Any information about trust impact

**Function Path:** `apps.main.agents.tools.extra_tools.handle_activity_refusal`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'activity_id': {'type': 'string', 'description': 'UUID of the refused activity'}, 'refusal_reason': {'type': 'string', 'description': 'Reason for refusal (time_constraint, difficulty, interest, etc.)'}, 'refusal_comment': {'type': 'string', 'description': "User's comment explaining the refusal"}, 'request_alternative': {'type': 'string', 'description': 'Whether to request an alternative activity'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'result': {'type': 'object', 'description': 'Dictionary with refusal handling results'}, 'recorded': {'type': 'string', 'description': 'Whether the refusal was successfully recorded'}, 'understanding_message': {'type': 'string', 'description': 'Supportive response acknowledging the refusal'}, 'alternative_suggestion': {'type': 'string', 'description': 'Alternative activity suggestion if requested'}, 'trust_impact': {'type': 'string', 'description': 'Any information about trust impact'}}}
```

---

## format_activity_instructions: Formats activity instructions in a user-friendly, engaging way for presentation

Formats activity instructions in a user-friendly, engaging way for presentation.



Input:

    activity_id: UUID of the activity

    user_profile_id: UUID of the user profile

    format_style: Desired formatting style (simple, step_by_step, detailed)

    include_preparation: Whether to include preparation steps

    include_reflection: Whether to include reflection prompts

    

Output:

    formatted_instructions: Dictionary with formatted instructions

        introduction: Introduction to the activity

        preparation: Preparation steps (if requested)

        main_steps: List of main activity steps

        reflection_prompts: Reflection prompts (if requested)

        time_estimates: Time estimates for each phase

        tips: Helpful tips for successful completion

**Function Path:** `apps.main.agents.tools.extra_tools.format_activity_instructions`

**Input Schema:**
```json
{'type': 'object', 'properties': {'activity_id': {'type': 'string', 'description': 'UUID of the activity'}, 'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'format_style': {'type': 'string', 'description': 'Desired formatting style (simple, step_by_step, detailed)'}, 'include_preparation': {'type': 'string', 'description': 'Whether to include preparation steps'}, 'include_reflection': {'type': 'string', 'description': 'Whether to include reflection prompts'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'formatted_instructions': {'type': 'object', 'description': 'Dictionary with formatted instructions'}, 'introduction': {'type': 'string', 'description': 'Introduction to the activity'}, 'preparation': {'type': 'string', 'description': 'Preparation steps (if requested)'}, 'main_steps': {'type': 'array', 'description': 'List of main activity steps'}, 'reflection_prompts': {'type': 'string', 'description': 'Reflection prompts (if requested)'}, 'time_estimates': {'type': 'string', 'description': 'Time estimates for each phase'}, 'tips': {'type': 'string', 'description': 'Helpful tips for successful completion'}}}
```

---

## get_user_history_analysis: Analyzes a user's activity history to identify patterns, preferences, and growth trajectory

Analyzes a user's activity history to identify patterns, preferences, and growth trajectory.



Input:

    user_profile_id: UUID of the user profile

    time_period: Time period to analyze (last_week, last_month, all_time)

    analysis_focus: Specific aspect to focus on (patterns, preferences, growth, engagement)

    include_feedback: Whether to include feedback analysis (default: True)

    

Output:

    analysis: Dictionary with history analysis results

        activity_patterns: Patterns in activity engagement and completion

        domain_preferences: Domain preferences based on engagement

        growth_trajectory: Growth and challenge progression over time

        engagement_metrics: Engagement and participation metrics

        feedback_themes: Common themes from user feedback (if requested)

        recommendations: Personalized recommendations based on analysis

**Function Path:** `apps.main.agents.tools.extra_tools.get_user_history_analysis`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'time_period': {'type': 'string', 'description': 'Time period to analyze (last_week, last_month, all_time)'}, 'analysis_focus': {'type': 'string', 'description': 'Specific aspect to focus on (patterns, preferences, growth, engagement)'}, 'include_feedback': {'type': 'string', 'description': 'Whether to include feedback analysis (default: True)'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'analysis': {'type': 'object', 'description': 'Dictionary with history analysis results'}, 'activity_patterns': {'type': 'string', 'description': 'Patterns in activity engagement and completion'}, 'domain_preferences': {'type': 'string', 'description': 'Domain preferences based on engagement'}, 'growth_trajectory': {'type': 'string', 'description': 'Growth and challenge progression over time'}, 'engagement_metrics': {'type': 'string', 'description': 'Engagement and participation metrics'}, 'feedback_themes': {'type': 'string', 'description': 'Common themes from user feedback (if requested)'}, 'recommendations': {'type': 'string', 'description': 'Personalized recommendations based on analysis'}}}
```

---

## extract_message_context: Extract contextual elements from a user message with high precision

Extract contextual elements from a user message with high precision.



This tool focuses exclusively on extracting context elements such as mood,

environment, time availability, and focus from user messages, with optional

historical context integration.



Input:

    message: The user's message text to analyze

    user_profile_id: UUID of the user profile

    include_historical_context: Whether to incorporate historical context 

    extraction_level: Detail level ("basic", "standard", "comprehensive")

    

Output:

    extracted_context: Dictionary with all extracted context elements

        mood: User's current emotional state

        environment: Where the user is or their surroundings

        time_availability: How much time the user has available

        focus: What the user is focused on or thinking about

        satisfaction: Whether the user is satisfied with previous experiences

        extraction_confidence: Confidence score for the extraction (0.0-1.0)

        extracted_entities: List of identified entities in the message

    historical_context: Previous context patterns if requested

**Function Path:** `apps.main.agents.tools.dispatcher_tools.extract_message_context`

**Input Schema:**
```json
{'type': 'object', 'properties': {'message': {'type': 'string', 'description': "The user's message text to analyze"}, 'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'include_historical_context': {'type': 'string', 'description': 'Whether to incorporate historical context'}, 'extraction_level': {'type': 'string', 'description': 'Detail level ("basic", "standard", "comprehensive")'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'extracted_context': {'type': 'object', 'description': 'Dictionary with all extracted context elements'}, 'mood': {'type': 'string', 'description': "User's current emotional state"}, 'environment': {'type': 'string', 'description': 'Where the user is or their surroundings'}, 'time_availability': {'type': 'string', 'description': 'How much time the user has available'}, 'focus': {'type': 'string', 'description': 'What the user is focused on or thinking about'}, 'satisfaction': {'type': 'string', 'description': 'Whether the user is satisfied with previous experiences'}, 'extraction_confidence': {'type': 'string', 'description': 'Confidence score for the extraction (0.0-1.0)'}, 'extracted_entities': {'type': 'array', 'description': 'List of identified entities in the message'}, 'historical_context': {'type': 'string', 'description': 'Previous context patterns if requested'}}}
```

---

## classify_message_intent: Classify user message intent with high precision for workflow routing

Classify user message intent with high precision for workflow routing.



This tool specializes in classifying user intents and determining the appropriate

workflow for processing, with ability to detect multiple intents in complex messages.



Input:

    message: The user's message text to classify

    context: Extracted context from the message (mood, environment, etc.)

    profile_completion: Numeric value (0.0-1.0) indicating user profile completion

    include_multi_intent_detection: Whether to detect multiple intents

    

Output:

    classification: Primary classification result

        workflow_type: Detected workflow type (wheel_generation, activity_feedback, etc.)

        confidence: Confidence score for classification (0.0-1.0)

        reason: Explanation for classification decision

        keywords_matched: List of keywords that influenced classification

    secondary_intents: List of secondary intents detected (if requested)

    action_required: Action requirement data if additional information is needed

**Function Path:** `apps.main.agents.tools.dispatcher_tools.classify_message_intent`

**Input Schema:**
```json
{'type': 'object', 'properties': {'message': {'type': 'string', 'description': "The user's message text to classify"}, 'context': {'type': 'string', 'description': 'Extracted context from the message (mood, environment, etc.)'}, 'profile_completion': {'type': 'string', 'description': 'Numeric value (0.0-1.0) indicating user profile completion'}, 'include_multi_intent_detection': {'type': 'number', 'description': 'Whether to detect multiple intents'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'classification': {'type': 'string', 'description': 'Primary classification result'}, 'workflow_type': {'type': 'string', 'description': 'Detected workflow type (wheel_generation, activity_feedback, etc.)'}, 'confidence': {'type': 'string', 'description': 'Confidence score for classification (0.0-1.0)'}, 'reason': {'type': 'string', 'description': 'Explanation for classification decision'}, 'keywords_matched': {'type': 'array', 'description': 'List of keywords that influenced classification'}, 'secondary_intents': {'type': 'array', 'description': 'List of secondary intents detected (if requested)'}, 'action_required': {'type': 'string', 'description': 'Action requirement data if additional information is needed'}}}
```

---

## create_session_packet: Creates a standardized session context packet with metadata for downstream processing

Creates a standardized session context packet with metadata for downstream processing.



This tool consolidates various data elements into a standardized context packet format

that can be used consistently by all agents in the workflow.



Input:

    user_profile_id: UUID of the user profile

    extracted_context: Dictionary with extracted contextual elements

    workflow_type: Type of workflow being initiated

    session_data: Additional session-specific data to include

    include_history: Whether to include recent session history

    

Output:

    context_packet: Standardized context packet with all required fields

        user_id: UUID of the user profile

        session_id: Unique identifier for this session

        session_timestamp: ISO formatted timestamp for the session

        reported_mood: User's reported mood from context

        reported_environment: User's reported environment from context

        reported_time_availability: User's reported time availability

        reported_focus: User's reported focus or interest area

        reported_satisfaction: User's reported satisfaction level

        workflow_type: Type of workflow being initiated

        session_data: Additional session-specific data

        session_history: Recent session data if requested

**Function Path:** `apps.main.agents.tools.dispatcher_tools.create_session_packet`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'extracted_context': {'type': 'string', 'description': 'Dictionary with extracted contextual elements'}, 'workflow_type': {'type': 'string', 'description': 'Type of workflow being initiated'}, 'session_data': {'type': 'string', 'description': 'Additional session-specific data to include'}, 'include_history': {'type': 'string', 'description': 'Whether to include recent session history'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'context_packet': {'type': 'string', 'description': 'Standardized context packet with all required fields'}, 'user_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'session_id': {'type': 'string', 'description': 'Unique identifier for this session'}, 'session_timestamp': {'type': 'string', 'description': 'ISO formatted timestamp for the session'}, 'reported_mood': {'type': 'string', 'description': "User's reported mood from context"}, 'reported_environment': {'type': 'string', 'description': "User's reported environment from context"}, 'reported_time_availability': {'type': 'string', 'description': "User's reported time availability"}, 'reported_focus': {'type': 'string', 'description': "User's reported focus or interest area"}, 'reported_satisfaction': {'type': 'string', 'description': "User's reported satisfaction level"}, 'workflow_type': {'type': 'string', 'description': 'Type of workflow being initiated'}, 'session_data': {'type': 'string', 'description': 'Additional session-specific data'}, 'session_history': {'type': 'string', 'description': 'Recent session data if requested'}}}
```

---

## validate_session_requirements: Validates whether a context packet meets all requirements for a specific workflow

Validates whether a context packet meets all requirements for a specific workflow.



This tool checks if all necessary information is present for a given workflow type

and identifies any missing critical fields.



Input:

    context_packet: The context packet to validate

    workflow_type: The workflow type to validate against

    

Output:

    validation_result: Result of the validation

        is_valid: Boolean indicating if the packet is valid

        missing_fields: List of required fields that are missing

        critical_fields: List of critical missing fields that must be addressed

        field_requirements: Dictionary of field requirements for this workflow

        action_required: Action requirement data if critical fields are missing

**Function Path:** `apps.main.agents.tools.dispatcher_tools.validate_session_requirements`

**Input Schema:**
```json
{'type': 'object', 'properties': {'context_packet': {'type': 'string', 'description': 'The context packet to validate'}, 'workflow_type': {'type': 'string', 'description': 'The workflow type to validate against'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'validation_result': {'type': 'string', 'description': 'Result of the validation'}, 'is_valid': {'type': 'string', 'description': 'Boolean indicating if the packet is valid'}, 'missing_fields': {'type': 'array', 'description': 'List of required fields that are missing'}, 'critical_fields': {'type': 'array', 'description': 'List of critical missing fields that must be addressed'}, 'field_requirements': {'type': 'object', 'description': 'Dictionary of field requirements for this workflow'}, 'action_required': {'type': 'string', 'description': 'Action requirement data if critical fields are missing'}}}
```

---

## merge_session_contexts: Merges multiple context packets into a consolidated context

Merges multiple context packets into a consolidated context.



This tool combines contexts from multiple related messages or sessions,

with configurable priority for resolving conflicts.



Input:

    primary_context: The main context packet with higher priority

    secondary_context: Secondary context to merge in

    override_primary: Whether secondary context should override primary

    

Output:

    merged_context: The consolidated context packet

        conflicts: List of fields that had conflicting values

        resolution_source: Which context was used for resolution

**Function Path:** `apps.main.agents.tools.dispatcher_tools.merge_session_contexts`

**Input Schema:**
```json
{'type': 'object', 'properties': {'primary_context': {'type': 'string', 'description': 'The main context packet with higher priority'}, 'secondary_context': {'type': 'string', 'description': 'Secondary context to merge in'}, 'override_primary': {'type': 'string', 'description': 'Whether secondary context should override primary'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'merged_context': {'type': 'string', 'description': 'The consolidated context packet'}, 'conflicts': {'type': 'array', 'description': 'List of fields that had conflicting values'}, 'resolution_source': {'type': 'string', 'description': 'Which context was used for resolution'}}}
```

---

## predict_workflow_success: Predicts likelihood of workflow completion success based on context and history

Predicts likelihood of workflow completion success based on context and history.



This tool analyzes current user context, historical completion patterns, and

context completeness to predict the probability of workflow success.



Input:

    user_profile_id: UUID of the user profile

    workflow_type: The type of workflow being initiated

    context_packet: The current context packet with all fields

    include_recommendations: Whether to include improvement recommendations

    

Output:

    prediction: Dictionary with success prediction details

        success_probability: Numerical probability of success (0.0-1.0)

        risk_factors: List of identified risk factors

        success_factors: List of identified success factors

        historical_completion_rate: Success rate of similar workflows

        recommendations: Suggestions to improve success probability (if requested)

**Function Path:** `apps.main.agents.tools.dispatcher_tools.predict_workflow_success`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'workflow_type': {'type': 'string', 'description': 'The type of workflow being initiated'}, 'context_packet': {'type': 'string', 'description': 'The current context packet with all fields'}, 'include_recommendations': {'type': 'string', 'description': 'Whether to include improvement recommendations'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'prediction': {'type': 'object', 'description': 'Dictionary with success prediction details'}, 'success_probability': {'type': 'string', 'description': 'Numerical probability of success (0.0-1.0)'}, 'risk_factors': {'type': 'array', 'description': 'List of identified risk factors'}, 'success_factors': {'type': 'array', 'description': 'List of identified success factors'}, 'historical_completion_rate': {'type': 'string', 'description': 'Success rate of similar workflows'}, 'recommendations': {'type': 'string', 'description': 'Suggestions to improve success probability (if requested)'}}}
```

---

## estimate_workflow_duration: Provides accurate time estimates for workflow completion

Provides accurate time estimates for workflow completion.



This tool calculates expected duration for different workflow types,

with adjustments based on complexity and historical patterns.



Input:

    workflow_type: The type of workflow being initiated

    context_complexity: Complexity level ("low", "medium", "high")

    user_profile_id: Optional user profile ID for personalized estimates

    

Output:

    duration: Dictionary with duration estimates

        estimated_seconds: Estimated duration in seconds

        min_seconds: Minimum likely duration

        max_seconds: Maximum likely duration

        human_readable: Human-readable duration estimate

        factors: Factors that influenced the estimate

**Function Path:** `apps.main.agents.tools.dispatcher_tools.estimate_workflow_duration`

**Input Schema:**
```json
{'type': 'object', 'properties': {'workflow_type': {'type': 'string', 'description': 'The type of workflow being initiated'}, 'context_complexity': {'type': 'string', 'description': 'Complexity level ("low", "medium", "high")'}, 'user_profile_id': {'type': 'string', 'description': 'Optional user profile ID for personalized estimates'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'duration': {'type': 'object', 'description': 'Dictionary with duration estimates'}, 'estimated_seconds': {'type': 'string', 'description': 'Estimated duration in seconds'}, 'min_seconds': {'type': 'string', 'description': 'Minimum likely duration'}, 'max_seconds': {'type': 'string', 'description': 'Maximum likely duration'}, 'human_readable': {'type': 'string', 'description': 'Human-readable duration estimate'}, 'factors': {'type': 'string', 'description': 'Factors that influenced the estimate'}}}
```

---

## identify_optimal_agent_path: Determines the ideal sequence of agents for a given workflow

Determines the ideal sequence of agents for a given workflow.



This tool analyzes the workflow type and context to provide an optimized

agent execution path with conditional branches based on context.



Input:

    workflow_type: The type of workflow being initiated

    context_packet: The current context packet

    user_profile_id: UUID of the user profile

    

Output:

    agent_path: Dictionary with optimized agent path

        primary_path: Ordered list of primary agent roles

        conditional_branches: Conditions that may alter the path

        estimated_steps: Expected number of agent steps

        abbreviated_path: Flag indicating if path was shortened

        trust_phase_adaptations: How the path adapts to trust phase

**Function Path:** `apps.main.agents.tools.dispatcher_tools.identify_optimal_agent_path`

**Input Schema:**
```json
{'type': 'object', 'properties': {'workflow_type': {'type': 'string', 'description': 'The type of workflow being initiated'}, 'context_packet': {'type': 'string', 'description': 'The current context packet'}, 'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'agent_path': {'type': 'object', 'description': 'Dictionary with optimized agent path'}, 'primary_path': {'type': 'array', 'description': 'Ordered list of primary agent roles'}, 'conditional_branches': {'type': 'string', 'description': 'Conditions that may alter the path'}, 'estimated_steps': {'type': 'string', 'description': 'Expected number of agent steps'}, 'abbreviated_path': {'type': 'string', 'description': 'Flag indicating if path was shortened'}, 'trust_phase_adaptations': {'type': 'string', 'description': 'How the path adapts to trust phase'}}}
```

---

## get_user_engagement_status: Analyzes current user engagement metrics to understand user state

Analyzes current user engagement metrics to understand user state.



This tool examines recent user interactions, activity completions, and

feedback patterns to assess user engagement and satisfaction levels.



Input:

    user_profile_id: UUID of the user profile

    timeframe: Time period to analyze ("recent", "weekly", "monthly", "all_time")

    include_trend_analysis: Whether to include trend analysis over time

    

Output:

    engagement_status: Dictionary with user engagement analysis

        current_engagement_level: Overall engagement score (0.0-1.0)

        activity_completion_rate: Rate of activity completion

        average_activity_satisfaction: User satisfaction with activities

        interaction_patterns: Analysis of interaction patterns

        recent_mood_trends: Analysis of mood trends

        engagement_trends: Engagement trends over time (if requested)

        suggestions: Engagement improvement suggestions

**Function Path:** `apps.main.agents.tools.user_state_analysis_tool.get_user_engagement_status`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'timeframe': {'type': 'string', 'description': 'Time period to analyze ("recent", "weekly", "monthly", "all_time")'}, 'include_trend_analysis': {'type': 'string', 'description': 'Whether to include trend analysis over time'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'engagement_status': {'type': 'object', 'description': 'Dictionary with user engagement analysis'}, 'current_engagement_level': {'type': 'string', 'description': 'Overall engagement score (0.0-1.0)'}, 'activity_completion_rate': {'type': 'string', 'description': 'Rate of activity completion'}, 'average_activity_satisfaction': {'type': 'string', 'description': 'User satisfaction with activities'}, 'interaction_patterns': {'type': 'string', 'description': 'Analysis of interaction patterns'}, 'recent_mood_trends': {'type': 'string', 'description': 'Analysis of mood trends'}, 'engagement_trends': {'type': 'string', 'description': 'Engagement trends over time (if requested)'}, 'suggestions': {'type': 'string', 'description': 'Engagement improvement suggestions'}}}
```

---

## detect_emotional_state_shifts: Recognizes significant changes in user emotional state

Recognizes significant changes in user emotional state.



This tool analyzes current message content against historical mood data

to identify emotional shifts requiring special handling.



Input:

    user_profile_id: UUID of the user profile

    current_message: The user's current message text

    context_packet: Current context with mood information

    lookback_period: Days to look back for mood history

    

Output:

    emotional_analysis: Dictionary with emotional shift analysis

        current_emotional_state: Current detected emotional state

        previous_emotional_state: Previous recorded emotional state

        detected_shift: Whether a significant shift was detected

        shift_magnitude: Magnitude of the emotional shift (0.0-1.0)

        shift_direction: Direction of shift (positive/negative/neutral)

        recommended_approach: Handling approach for detected shift

        confidence: Confidence in the shift detection

**Function Path:** `apps.main.agents.tools.user_state_analysis_tool.detect_emotional_state_shifts`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'current_message': {'type': 'string', 'description': "The user's current message text"}, 'context_packet': {'type': 'string', 'description': 'Current context with mood information'}, 'lookback_period': {'type': 'string', 'description': 'Days to look back for mood history'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'emotional_analysis': {'type': 'object', 'description': 'Dictionary with emotional shift analysis'}, 'current_emotional_state': {'type': 'string', 'description': 'Current detected emotional state'}, 'previous_emotional_state': {'type': 'string', 'description': 'Previous recorded emotional state'}, 'detected_shift': {'type': 'string', 'description': 'Whether a significant shift was detected'}, 'shift_magnitude': {'type': 'string', 'description': 'Magnitude of the emotional shift (0.0-1.0)'}, 'shift_direction': {'type': 'string', 'description': 'Direction of shift (positive/negative/neutral)'}, 'recommended_approach': {'type': 'string', 'description': 'Handling approach for detected shift'}, 'confidence': {'type': 'string', 'description': 'Confidence in the shift detection'}}}
```

---

## check_workflow_history: Examines recent workflow patterns for a user

Examines recent workflow patterns for a user.



This tool analyzes the user's recent workflow history to identify patterns,

preferences, and potential issues in workflow execution.



Input:

    user_profile_id: UUID of the user profile

    workflow_type: Optional specific workflow type to analyze

    timeframe: Days to look back for workflow history

    include_failures: Whether to include failed workflows in analysis

    

Output:

    workflow_analysis: Dictionary with workflow history analysis

        recent_workflows: Count of recent workflows

        completion_rate: Rate of workflow completions

        preferred_workflows: Most frequently initiated workflows

        common_failure_points: Common points of workflow failure

        workflow_efficiency: Efficiency metrics for workflows

        patterns: Identified patterns in workflow usage

        recommendations: Workflow handling recommendations

**Function Path:** `apps.main.agents.tools.user_state_analysis_tool.check_workflow_history`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'workflow_type': {'type': 'string', 'description': 'Optional specific workflow type to analyze'}, 'timeframe': {'type': 'string', 'description': 'Days to look back for workflow history'}, 'include_failures': {'type': 'string', 'description': 'Whether to include failed workflows in analysis'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'workflow_analysis': {'type': 'object', 'description': 'Dictionary with workflow history analysis'}, 'recent_workflows': {'type': 'string', 'description': 'Count of recent workflows'}, 'completion_rate': {'type': 'string', 'description': 'Rate of workflow completions'}, 'preferred_workflows': {'type': 'string', 'description': 'Most frequently initiated workflows'}, 'common_failure_points': {'type': 'string', 'description': 'Common points of workflow failure'}, 'workflow_efficiency': {'type': 'string', 'description': 'Efficiency metrics for workflows'}, 'patterns': {'type': 'string', 'description': 'Identified patterns in workflow usage'}, 'recommendations': {'type': 'string', 'description': 'Workflow handling recommendations'}}}
```

---

## analyze_mood_progression: Analyzes mood progression and patterns over time

Analyzes mood progression and patterns over time.



This tool examines the user's mood history to identify trends,

recurring patterns, and potential correlations with activities.



Input:

    user_profile_id: UUID of the user profile

    timeframe_days: Number of days to look back

    

Output:

    mood_analysis: Dictionary with mood progression analysis

        overall_trend: The general trend direction of mood

        volatility: Measurement of mood stability/changes

        common_moods: Most frequently reported moods

        mood_activity_correlations: Activities correlated with mood changes

        recommendations: Suggested approaches based on mood patterns

**Function Path:** `apps.main.agents.tools.user_state_analysis_tool.analyze_mood_progression`

**Input Schema:**
```json
{'type': 'object', 'properties': {'user_profile_id': {'type': 'string', 'description': 'UUID of the user profile'}, 'timeframe_days': {'type': 'string', 'description': 'Number of days to look back'}}}
```

**Output Schema:**
```json
{'type': 'object', 'properties': {'mood_analysis': {'type': 'object', 'description': 'Dictionary with mood progression analysis'}, 'overall_trend': {'type': 'string', 'description': 'The general trend direction of mood'}, 'volatility': {'type': 'string', 'description': 'Measurement of mood stability/changes'}, 'common_moods': {'type': 'string', 'description': 'Most frequently reported moods'}, 'mood_activity_correlations': {'type': 'string', 'description': 'Activities correlated with mood changes'}, 'recommendations': {'type': 'string', 'description': 'Suggested approaches based on mood patterns'}}}
```

---
