Here is the detailed UX of "The Game" (ignore the "(FUTURE)" points, as they are not part of the MVP) :



# 3. User Experience



## 3.1. User Profiling



### Foundational Profile



- **What to Capture:**

- Demographics (name, age, occupation, housing context) stored in the _Demographics_ entity

- Life stage transitions and environmental factors impacting routines (aspects of _Demographics_ and contextual fields)

- Daily routines, hobbies, and energy cycles to optimize timing for interventions

- **Purpose:** Establish a baseline to tailor activity selection and schedule recommendations.



### Psychological Profile



- **Core Constructs:** Mapped to the Big Five personality model and represented as User Archetype Presets:

- **Analytical Archetype:** High in Conscientiousness and Openness, lower in Agreeableness

- **Emotional Archetype:** High in Neuroticism and Agreeableness

- **Creative Archetype:** High in Openness, moderate Extraversion

- **Reflective Archetype:** Balanced Conscientiousness and Openness, lower Extraversion

- **Implementation:**

- Character traits are initially categorized using Big Five dimensions (CharacterTraitCategory)

- The system synthesizes these traits into dominant archetypes (UserArchetype)

- Each UserArchetype has a dominance score reflecting its relative strength

- **Purpose:** Inform activity challenge calibration and adaptive interventions based on inherent personality traits.



### Onboarding



- **In-Depth Intake:**  

    Use a structured yet flexible questionnaire to capture the user's dreams, fears, routines, and living context. Store responses in _Goal_, _PersonalChallenge_, and _Demographics_ entities.

- **Dynamic Question Flow:**  

    Begin with open-ended inquiries about hopes and expectations in relation to the game of life and in general, then steer toward uncovering practical needs. Dominant Archetype is identified early in onboarding to access archetypal value proposition blueprints. This early Expectation Assessment allows the system to adapt the value proposition approach to the user's dominant archetype.

- **Trust Building:**  

    Gradually introduce more challenging questions while acknowledging resistance with empathy. Establish a conservative baseline in the _TrustLevel_ entity to guide low-risk (Foundation) versus higher-risk (Expansion) activities.

- **Three-Phase Structure:**

    - **Phase 1 to Phase 2 Transition:** Triggered when basic demographic information and dominant archetype identification is complete

    - **Phase 2 to Phase 3 Transition:** Requires completion of basic psychological profiling questions and at least one dominant archetype must be confirmed

    - **Phase 3 to Regular Use Transition:** Aspirational goals must be defined and user has completed at least one starter activity

- **Tailored Value Propositions:**  

    Conclude each phase with reflective feedback—using the user's language and metaphors—and a forward-looking question to spark momentum.



### Current Mood Tracking



- **Implementation:**

- Links between _CharacterTraitCategory_ and _CurrentMood_ ensure a consistent taxonomy of emotional states.

- Energy, receptivity, and immediate reaction data (via _UserFeedbackLog_ and _CurrentMood_) inform real-time activity selection.

- **Purpose:** Enable rapid adaptation of _ActivityTailored_ recommendations based on current psychological state.



## 3.2. User Journey & Growth



### Transformation Phases



- **Mapping to Trust Levels:**

- **Foundation Phase:** TrustLevel values 0–25; focuses on establishing safety with low-risk activities.

- **Expansion Phase:** TrustLevel values 26–50; introduces more challenging activities.

- **Integration Phase:** TrustLevel values 51–75; begins harmonizing multiple archetypal strengths.

- **Mastery Phase:** TrustLevel values 76–100; supports self-initiated transformative growth.

- **Data Flow:** TrustLevel changes, supported by _UserPsyAgentLog_ entries, indicate transitions between phases.

- **Purpose:** Clearly delineate developmental milestones and adaptive challenge calibration.



### Success Framework



- **Components:**

- **Goal & Intention Tracking:** Personal goals are captured as _Goal_ entities with dual importance ratings (user and system).

- **Quantitative & Qualitative Metrics:** Trust levels, engagement rates, decision latencies (from _TrustLevel_ and _UserFeedbackLog_), plus qualitative reflections.

- **Progress Mapping:** Direct relationships between _ActivityTailored_ completions and goal progression.

- **On-the-Fly Adjustments:**

- Each completed activity updates progress maps and capacity metrics (through _Capacity_ and _TrustLevel_ entities).

- **Purpose:** Provide a unified, transparent narrative of user growth and motivation.



### Protective Mechanisms



- **MVP Implementation:**

- **Vulnerability Identification:** Monitored via _Limitation_ entities.

- **Safety Protocols & Adaptive Feedback:** Triggered by patterns in _UserFeedbackLog_ and _CurrentMood_.

- **Structured Interventions:** Adjustments via alternative _ActivityTailored_ selections when challenges exceed current capacity.

- **Purpose:** Maintain psychological safety by ensuring that every adaptive intervention is data-driven and responsive.



## 3.3. Interface & Engagement



### The Wheel Experience

- A core randomization process is implemented using the _Wheel_ and _WheelItem_ entities.

- Pre-spin and post-spin reflections are recorded in _UserFeedbackLog_.

- **Purpose:** Disrupt habitual thought patterns and gauge user responses to uncertainty.



### Visualization System & Gamification

- **Progress Maps:** Visualizations based on TrustLevel and Capacity data.

- **Achievement Badges:** Multi-tiered rewards (e.g., "Brave Confessor" to "Master Confessor") reflect improvements in users capacity metrics

- badges serve as both motivational rewards and tangible indicators of skill/achievement progression

- **Scorecards & Visual Cues:** Progress bars and tier colors indicate proximity to milestones.

- **Purpose:** Enhance motivation and provide tangible feedback on user progress.



### Daily Rhythm Integration

- **Data Sources:**

- Scheduling and energy state adjustments based on _Demographics_ and _CurrentMood_.

- Daily reflections (captured in _UserFeedbackLog_) adjust upcoming challenges.

- **Mechanisms:**

- **Daily Reflection:** Short check-ins to record emotional state and progress.

- **Weekly Review:** Summaries that update goals and TrustLevel assessments.

- **Purpose:** Align system interventions with natural user rhythms for optimized engagement.



## 3.4. Future Feature Roadmap



### Social Connection (Future)

- **Note:** Social and community features (e.g., peer accountability, group challenges) are planned for future development and are not part of the MVP.

- **Purpose:** They will eventually support holistic development but are presently outside current capacities.



### Circadian Rhythm Integration (Future)

- **Capabilities:**

- Detect energy patterns via time-stamped interaction data.

- Dynamic scheduling and notification recommendations based on sleep and activity cycles.



### onboarding (future)

- **Adaptive Question Paths:**  

    Clearly state that future iterations will integrate adaptive question pathways that respond to individual archetype identification and evolving response patterns.

- **Archetype-Specific Language Libraries:**  

    Mention the planned development of language libraries to better align system communication with the user's intrinsic language and metaphors.



### Reward System Enhancement (Future)

- **Capabilities:**

- A digital token economy (coins), unlockable content, and virtual marketplace options.

- Streak bonuses and premium rewards for long-term engagement.



### Advanced Social Features (Future)

- **Capabilities:**

- Peer accountability, mentor matching, and community-driven challenges.

- Enhanced social integration based on user archetype dynamics.





# 4. Adaptive System Strategy



## 4.1 WHAT: Core System Components



### Trust Framework

- **Measurement & Storage:**

  - Trust is measured across archetypal dimensions using the _TrustLevel_ entity.

  - Each UserArchetype has an associated TrustLevel value (0-100).

  - Trust metrics are updated after each interaction based on UserFeedbackLog entries.

- **Building Strategy:**

  - Trust develops through consistent, positive interactions that demonstrate system competence

  - Each successfully completed activity builds incremental trust within its archetype domain

  - Higher trust enables access to more personal growth opportunities

- **System Strategy Determination:**

  - Trust thresholds determine appropriate challenge levels and intervention types

  - Low trust (Foundation Phase): Focus on validation, safety, and quick wins

  - Building trust (Expansion Phase): Gradually introduce more challenging activities

  - High trust (Integration/Mastery Phases): Enable deeper transformation work

- **Recalibration Triggers:**

  - After activity completion (standard adjustment)

  - After activity refusal (downward adjustment)

  - During weekly reviews (comprehensive reassessment)

  - When significant mood shifts are detected



### Activity Selection Logic

- **Dynamic Content Curation:**

  - Weighted probability distributions within the _Wheel_ configuration balance user aspirations with system goals.

  - The _ActivityBase_ catalog is curated and diversified to ensure balanced challenge gradients.

  - Progressive difficulty scaling is applied in _ActivityTailored_ based on transformation phases.

- **Refusal Flow:**

  - Every challenge refusal is recorded in the _UserFeedbackLog_.

  - Overwhelm detection through direct feedback (recorded in _UserFeedbackLog_).

  - Gentle alternatives provided by selecting simplified less-challenging _ActivityTailored_ variants.

  - Refusals affect the _TrustLevel_ (temporary drop leading to challenge-level recalibration)

  - Supportive messaging ensures users feel encouraged rather than penalized.

  - Gradual reintroduction of higher-intensity tasks as TrustLevel improves.



### Adaptive Coaching Toolkit

- **Supportive Interventions (MVP):**

  - Validating user emotions and experiences

  - Normalizing challenges as part of growth

  - Providing perspective on difficult emotions

- **Growth Interventions (MVP):**

  - Gentle challenging of limiting beliefs

  - Reframing obstacles as opportunities

  - Encouraging incremental stretching of comfort zones

- **Reflective Interventions (MVP):**

  - Prompting insight through targeted questions

  - Highlighting patterns across situations

  - Connecting current challenges to stated goals

- **Overwhelm Responses (MVP):**

  - Recognition of signs of distress

  - Immediate scaling back of challenge level

  - Offering grounding activities as alternatives



## 4.2 HOW: Technical Implementation



### Psychological/Archetype Analysis

- Interpret current dominant archetype

- Evaluate archetype-specific trust levels

- Assess inter-archetype tensions



### Context Assessment

- Current time of day and environmental factors

- Recent activity history and patterns

- Energy state and receptivity indicators



### Challenge Calibration

- Match activity difficulty to trust level

- Balance between growth and psychological safety

- Cross-reference with identified limitations



### Response Formulation

- Select appropriate communication style

- Determine optimal challenge framing

- Prepare supportive and fallback messaging



### Adaptive Response Framework

- **Real-Time Factors (Immediate Context):**

  - Current mood and energy state (CurrentMood)

  - Time of day and environmental constraints

  - Recent activity completion or refusal patterns

- **Medium-Term Factors (User Evolution):**

  - Trust level trends over days/weeks

  - Capacity and limitation developments

  - Archetype dominance shifts

- **Adaptation Mechanisms:**

  - Challenge Level: Adjusts difficulty based on trust levels and recent successes/failures

  - Content Selection: Filters activities based on mood, preferences, and context

  - Timing Optimization: Schedules interventions to align with energy patterns

  - Communication Style: Adapts language to match dominant archetype



### Pattern Recognition Framework

- **Behavioral Patterns:**

  - Activity engagement cycles (time of day, day of week)

  - Challenge approach tendencies (avoidance, procrastination, diving in)

  - Feedback style patterns (detailed vs. brief, emotional vs. analytical)

- **Growth Patterns:**

  - Trust development rates across archetypes

  - Skill acquisition trajectories

  - Challenge tolerance evolution

- **Interaction Patterns:**

  - Communication preferences and responsiveness

  - Linguistic markers and metaphor affinity

  - Motivational trigger consistency

- **Implementation:**

  - Basic pattern detection through statistical analysis of UserFeedbackLog

  - Correlations between moods, activities, and outcomes

  - Trend analysis of trust and capacity metrics



## 4.3 WHY: Strategic Objectives



### User Growth & Transformation

- Activities build progressive capacity for psychological flexibility

- Balanced development across archetypes creates resilience

- Trust-based progression enables deeper personal insights over time

- Personalized challenge calibration optimizes growth without overwhelm



### Engagement & Retention

- Varied activity selection prevents habituation and boredom

- Wheel mechanics add novelty and reduce decision fatigue

- Achievement recognition reinforces continued participation

- Adapting to user rhythms increases likelihood of sustained engagement



### Psychological Safety

- Conservative trust baselines prevent premature exposure to challenging content

- Continuous feedback monitoring identifies signs of distress

- Alternative activity paths maintain momentum during difficult periods

- Clear boundaries between psychological growth and therapy ensure ethical usage



## 4.4 Future Enhancements



### Advanced Pattern Recognition (Future)

- Deep pattern analysis for archetypal events and multi-dimensional behavioral clustering

- Predictive modeling of user transformation trajectories and early warnings for disengagement



### Autonomous Agent System (Future)

- Self-evolving activity generation and dynamic agent specialization

- Automated calibration adjustments with minimal human oversight, supported by cross-user insights (privacy preserved)



### Adaptive Coaching (Future)

- More advanced escalation and re-escalation strategies will be introduced as AI capabilities mature

- Adaptation to user-specific linguistic patterns and emotional tone matching

- Personalized metaphor generation and evolving communication styles aligned with transformation phases

------------

Following is the last version of the user's data model :

@startuml model
!include history.puml
' -------------------------------
' USER MODELS
' -------------------------------
package "User" #E9D8FD {
  class User {
    +userId: UUID
    +userName: String
    +email: String
  }

  class UserArchetype {
    +archetypeId: UUID
    +archetypeName: String
    +archetypeDescription: String
    +dominance: int
    +isTheUser: boolean
  }

  User "1" -- "1..*" UserArchetype : "has"

  ' -------------------------------
  ' RESOURCE MODELS
  ' -------------------------------
  package "Resources"#97ffb9 {
    ' Inventory linking User <-> Resource
    ' for a specific location or moment in time
    class Inventory {
      +inventoryId: UUID
      +location:String
      +validUntil:Date
      +notes: String // e.g. "repair needed"
    }

    class ResourceBase {
        +id: String
        +resourceType: String // e.g. "sport equipment", "place"
        +description: String
        +priciness: int          // scale 0-100
        +accessibility: int      // scale 0-100
        +notes: String
    }

    class PersonalResource {
      +specificName: String
      +location: String
      +ownershipDetails: String  // e.g. "until 06-2025", "starting 05-2025"
      +contactInfo: String
      +notes: String
    }

    class Capacity {
      +skillId: UUID
      +validUntil: Date
      +isUnlimited: boolean
      +skillType: ENUM("MENTAL", "PHYSICAL", "SOCIAL", "FINANCIAL")
      +isUserAware: int
    }


    class Limitation {
      +limitationId: UUID
      +validUntil: Date
      +isUnlimited: boolean
      +limitationType: ENUM("MENTAL", "PHYSICAL", "SOCIAL", "FINANCIAL")
      +isUserAware: int
    }

    UserArchetype "1" -- "0..*" Limitation : "limited by"
    UserArchetype "1" -- "0..*" Capacity : "helped by"
    User "1" -- "1..*" Inventory : "has"
    Inventory "1..*" -- "0..*" PersonalResource : "contains"

  }

  ' -------------------------------
  ' USER PROFILE MODELS
  ' -------------------------------
  package "User Psyche profile" #D7CEC7 {
    class Demographics {
      +fullName: String
      +age: int
      +gender: String
      +location: String
      +language: String
      +personalPrefsJSON: String
    }

    abstract class UserGoal {
      +id: UUID
      --
      +user_id: INT
      +title: VARCHAR
      +description: TEXT
      +importance_according_user: int
      +importance_according_system: int
      +strength: int
      +created_at: DATETIME
      +updated_at: DATETIME
    }

    class Intention {
      +start_date: DATE
      +due_date: DATE
      +is_completed: BOOLEAN
      +progress_notes: TEXT
    }

    class Aspiration {
      +domain: VARCHAR
      +horizon: VARCHAR
      +level_of_ambition: VARCHAR
    }

    UserGoal <|-- Intention
    UserGoal <|-- Aspiration

    class Inspiration {
      +inspiration_id: UUID
      --
      +user_id: INT
      +source: VARCHAR
      +description: TEXT
      +strength: INT
      +reference_url: VARCHAR
      +created_at: DATETIME
      +updated_at: DATETIME
    }

    class GoalInspiration {
      +goal_id: UUID
      +inspiration_id: INT
      --
      +note: TEXT
    }

    UserGoal}o--o{GoalInspiration : "influenced by"
    Inspiration}o--o{GoalInspiration : "influences"

    UserArchetype "1" -- "0..*" UserGoal : "owns"
    UserArchetype "1" -- "0..*" Inspiration : "has"

    class TrustLevel {
      +trustLevelId: UUID
      --
      +value: int
      +aggregate: String
      +aggregateId: String
      +notes: String
    }

    class Preference {
      +prefId: UUID
      --
      +prefName: String
      +prefDescription: String
      +prefStrength: int
      +userIsAware: int
    }

    class Belief {
      +beliefId: UUID
      --
      +beliefLabel: String
      +beliefDescription: String
      +beliefStrength: int
      +userIsAware: int
      +discoveredAt: Date
    }

    class CharacterTrait {
      +traitId: UUID
      --
      +traitLabel: String
      +description: String
      +traitStrength: int
      +userIsAware: int
    }
    
    class CurrentMood {
      +moodId: UUID
      --
      +label: String
      +description: String
      +strength: int
      +userIsAware: int
    }

    class PersonalChallenge {
      +challengeId: UUID
      --
      +description: String
      +strength: int
    }

    UserArchetype "1" -- "1" Demographics
    UserArchetype "1" -- "0..*" Preference
    UserArchetype "1" -- "0..*" Intention : "wants"
    UserArchetype "1" -- "0..*" Belief
    UserArchetype "1" -- "0..*" CharacterTrait
    UserArchetype "1" -- "0..*" CurrentMood
    UserArchetype "1" -- "0..*" PersonalChallenge

    
    
  }
  UserArchetype <.. TrustLevel : "trust"
  User <.. TrustLevel : "trust"


  package "User psyche categories"#6b72fc {
    class ChallengeCategory {
      +catId: UUID
      +catName: String
      +catDescription: String
    }

    class BeliefCategory {
      +catId: UUID
      +catName: String
      +catDescription: String
    }

    class CharacterTraitCategory {
      +catId: UUID
      +catName: String 
      +catDescription: String
    }

    PersonalChallenge "0..*" -- "1" ChallengeCategory
    Belief "0..*" --- "1" BeliefCategory
    CharacterTrait "0..*" -- "1" CharacterTraitCategory
    CurrentMood "0..*" -- "1" CharacterTraitCategory
  }
}

package "History Log" {
  class UserFeedbackLog {
    +feedbackType: String
    +feedbackContent: String
    +positiveness: int
    +criticality: int
  }
  UserFeedbackLog --|> HistoryLog

  class UserPsyAgentLog {
    +actionType: String
    +feedbackContent: String
    +positiveness: int
    +criticality: int
  }
  UserPsyAgentLog --|> HistoryLog
  'UserPsyAgentLog ..> Limitation: creates, updates
  'UserPsyAgentLog ..> Capacity: creates, updates
  'UserPsyAgentLog ..> TrustLevel: updates
  'UserPsyAgentLog ..> Aspiration: creates, updates

  class UserLog {
    +userId: UUID
    +eventType: String
    +details: String
  }
  UserLog --|> HistoryLog
}


abstract class TemporalRecord {
  +effectiveStart: Date
  +durationEstimate: String
  +effectiveEnd: Date
}
CharacterTrait -[hidden]-|> TemporalRecord
CurrentMood -[hidden]-|> TemporalRecord
Preference -[hidden]-|> TemporalRecord
Intention -[hidden]-|> TemporalRecord
Aspiration -[hidden]-|> TemporalRecord
Limitation -[hidden]-|> TemporalRecord
Capacity -[hidden]-|> TemporalRecord

' -------------------------------
' EXTERNAL CONNECTIONS
' -------------------------------
PersonalResource --|> ResourceBase : "inspired by"

@enduml
------------------


Now make a deep analysis of the model and our agents, in the light of our UX needs, and add to the model (in plantUML) a note like this :
note bottom
<i>description of the element</i>
example value for 1st property
example value for 2nd property
etc.
end note