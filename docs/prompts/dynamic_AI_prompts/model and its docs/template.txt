Goal:

Gain a deep and comprehensive understanding of the relationships and consistency between the model definitions and their corresponding documentation files. Your task is to analyze the provided plantUML and markdown content, identify discrepancies, and provide relevant updates to the documentation if it does not match the model.

— — — — — — — — — — — — — — — — —

Output Structure:

Provide a detailed report that includes:
- An analysis of the entities defined in the model files.
- A list of identified inconsistencies.
- Code blocks containing the suggested updates of the documentation

— — — — — — — — — — — — — — — — —

Things to Avoid:

*Do not ask confirmation for trivial mistakes with somehow obvious answers
*Do not alter the model files

— — — — — — — — — — — — — — — — —

Context for this Request

here is the content of "user.puml", defining entities around a user :

{MODEL_USER}

and here is the content of "user.md", documenting "user.puml" :

{MODEL_USER_DOC}

here is the content of "activity.puml", defining entities around a user :

{MODEL_ACTIVITY}

and here is the content of "activity.md", documenting "activity.puml" :

{MODEL_ACTIVITY_DOC}

— — — — — — — — — — — — — — — — —