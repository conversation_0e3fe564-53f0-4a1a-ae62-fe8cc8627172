Here is a user's story :

{USER_STORY}

------------

Following is the last version of the data model :

{MODEL}
------------------
And finally here is the list of the AI agents we have at our service:

{AGENTS}

-----------------

Make a deep analysis of how our agents interact with the model.
Then translate in the most accurate way possible the user's story into a Sequence Diagram (in plantUML).
The Sequence Diagram should be well structured, with visual separation between :
- the user
- the AI agents
- the model
Each steps in the diagram are verbose enough so that it's easy to follow, but also containing high-level technical details of each background processes (exact data trnasmission)