Goal:

Make a deep analysis of the provided data model, and group entities according to their role and nature (less than 10 groups). Create a high-level comprehensive description of each group, their entities and the key relationships.

— — — — — — — — — — — — — — — — —

Output Structure:

A markdown code block respecting this structure (including spaces) :
```
**Object Categories:**  
            - **[group name]:**  
                _[high level description of that group]_
				  * __[key entity]__:[high level description of that entity]
```
```
**Key Relationships:**  
            - **[relationship group name]:**  
                _[high level description of those relationships]_
				  * __[key relationship]__:[high level description of that relationship]
```
— — — — — — — — — — — — — — — — —

Context for this Request:



Our data model :
```
{MODEL}
```
The documentation of our model :
{MODEL_USER_DOC}

{MODEL_ACTIVITY_DOC}