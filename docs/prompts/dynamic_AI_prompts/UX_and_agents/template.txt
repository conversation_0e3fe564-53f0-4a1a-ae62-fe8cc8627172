Goal:

Perform a comprehensive analysis of our system's detailed UX, current AI agents team and data model for "The Game". Evaluate each agent by determining:
* The relevance of its instructions in addressing our UX needs.
* The relevance of its Input and Output schema according to the model
* Whether something is missing in the model for the agent to perform its mission

If you identify any gaps, make suggestion for a new agent.

Clarifications:
– Each agent will be backed up by a reasoning model, which allows for broad agent scopes.

— — — — — — — — — — — — — — — — —

Output Structure:

Provide a detailed report that includes:
1. A section for each agent from that clearly answers:
   • Is this agent relevant to the current UX needs?
   • Should its scope be expanded?
   • Is there a good reason for splitting that agent or merging it with another one ?
2. A markdown code block containing the updated agent description based on your analysis, respecting the structure of the provided agent description :
```
### 8. [name of the agent]

**Scope:**  
[scope]

- **Generic Instructions:**  
  [instructions]

- **Expected Input Schema (JSON):**  
  ```json
  [json input schema]
  ```
  ```

- **Produced Output Schema (JSON):**  
  ```json
  [json output schema]
  ```
```
— — — — — — — — — — — — — — — — —

Things to Avoid:

Do not alter the structure of the markdown code output

— — — — — — — — — — — — — — — — —


Context for this Request :

Here is the detailed UX of "The Game":
```
{UX}

{UXS}
```
---
Here is the list of the AI agents we currently consider:
```
{AGENTS}
```
---
And here is our current data model :
```
{MODEL_USER_DOC}
```
```
{MODEL_ACTIVITY_DOC}
```
```
{MODEL}
```


— — — — — — — — — — — — — — — — —
