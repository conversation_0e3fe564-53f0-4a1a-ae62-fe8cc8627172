from pathlib import Path
import subprocess
import logging
import chardet  # Requires `pip install chardet`

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(levelname)s - %(message)s",
)

script_directory = Path(__file__).parent
working_directory = Path.cwd()

# List of external scripts to execute (relative to PWD)
external_scripts = [
    #working_directory / "./coach/docs/model/merge_all.py"
]

# Define mappings between placeholders and file paths
placeholder_mapping = {
    '{MODEL_USER}': working_directory / './coach/docs/model/user.puml',
    '{MODEL_USER_DOC}': working_directory / './coach/docs/model/user.md',
    '{MODEL_ACTIVITY}': working_directory / './coach/docs/model/activity.puml',
    '{MODEL_ACTIVITY_DOC}': working_directory / './coach/docs/model/activity.md',
}

# Function to execute scripts sequentially
# Function to execute scripts sequentially
def execute_scripts(script_paths):
    for script_path in script_paths:
        if script_path.exists():
            logging.info(f"Executing external script: {script_path}")
            try:
                result = subprocess.run(
                    ["python", str(script_path)],
                    check=True,  # Raises an error if the script fails
                    text=True,  # Ensures output is handled as text
                    stdin=None,  # Allows interactive input
                )
                logging.info(f"Successfully executed {script_path}")
            except subprocess.CalledProcessError as e:
                logging.error(f"Error while executing {script_path}: {e}")
                exit(1)  # Stop execution if any script fails
        else:
            logging.error(f"External script not found: {script_path}")
            exit(1)

# Execute all external scripts before proceeding
execute_scripts(external_scripts)
# Read the template file
template_path = script_directory / "template.txt"

if not template_path.exists():
    logging.error(f"Template file not found: {template_path}")
    exit(1)

try:
    template_content = template_path.read_text(encoding="utf-8")
    logging.info("Successfully loaded template file.")
except Exception as e:
    logging.error(f"Failed to read template file: {e}")
    exit(1)

# Function to detect encoding and read file safely
def read_file_safely(file_path):
    """ Reads a file with detected encoding, ensuring robust error handling. """
    if not file_path.exists():
        logging.error(f"\nFile not found: {file_path}\n")
        return f"[Error: File {file_path} not found]"
    
    try:
        # Detect encoding
        with file_path.open("rb") as f:
            raw_data = f.read()
            detected = chardet.detect(raw_data)
            encoding = detected["encoding"] if detected["confidence"] > 0.5 else "utf-8"
        
        content = raw_data.decode(encoding)
        logging.info(f"Successfully read file: {file_path} (Encoding: {encoding})")
        return content
    except UnicodeDecodeError:
        logging.error(f"UnicodeDecodeError in file: {file_path}. Using fallback decoding.")
        return raw_data.decode("utf-8", errors="ignore")
    except Exception as e:
        logging.error(f"Unexpected error reading file {file_path}: {e}")
        return f"[Error reading {file_path}]"

# Replace placeholders with file contents
for placeholder, file_path in placeholder_mapping.items():
    logging.debug(f"Processing placeholder: {placeholder} -> {file_path}")
    file_content = read_file_safely(file_path)
    if placeholder in template_content:
        template_content = template_content.replace(placeholder, file_content)
        logging.info(f"Replaced placeholder: {placeholder}")
    else:
        logging.warning(f"Placeholder {placeholder} not found in template.")

# Output the result
output_path = script_directory / "output_generated.txt"
try:
    output_path.write_text(template_content, encoding="utf-8")
    logging.info(f"Template processing complete. Output saved to {output_path}")
except Exception as e:
    logging.error(f"Failed to write output file: {e}")