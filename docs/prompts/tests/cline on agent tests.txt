in "backend\apps\main\tests\test_flows\test_discussion.py", the test "test_discussion_flow_with_real_llm" fails.

I want you to :
- understand the purpose of the test
- run it by yourself
- fix the situation in a smart and relevant way

extra instructions :
- Respect the original purpose, scope, and meaningfullness of that test
- Respect the current testing framework
- get inspiration fromn the successful approach of the test "test_discussion_flow_collect_missing_information"
- if you identify a fix that can serve other tests in the same file, apply it everywhere it's relevant
- use TASK.md to :
  * decompose complex problems into several several subtasks
  * note descrepencies, technical debt and other valuable analysis as subtasks
  
once you are done :
- confirm that you haven't add more mocking
- generate a concise and meaninfull "git commit"'s message

extra context:
Agent's missions are described in "docs\backend\agents\agents_description.md".
The test will ultimately serve "backend\apps\main\graphs\discussion_graph.py"

Ask me if there is something you are not sure about