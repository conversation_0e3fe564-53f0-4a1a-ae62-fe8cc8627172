import logging
import chardet
from pathlib import Path
import re
import sys

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(levelname)s - %(message)s",
)

# Use script_directory to ensure relative paths are based on the script location
script_directory = Path(__file__).parent

# Mapping between placeholders and the actual file paths (wrapped as Path objects)
placeholder_mapping = {
    # Agent Story placeholders
    '{AGENT_STORY_WHEEL_GENERATION}': Path("/home/<USER>/Documents/chilltech/docs/agents/agent_scenarios/wheelgeneration.md"),
    '{AGENT_STORY_POST_SPIN}': Path("/home/<USER>/Documents/chilltech/docs/agents/agent_scenarios/postspin.md"),
    '{AGENT_STORY_POST_ACTIVITY}': Path("/home/<USER>/Documents/chilltech/docs/agents/agent_scenarios/postactivity.md"),
}

def read_file_safely(file_path):
    """Reads a file with detected encoding, ensuring robust error handling."""
    if not file_path.exists():
        logging.error(f"File not found: {file_path}")
        return f"[Error: File {file_path} not found]"
    
    try:
        with file_path.open("rb") as f:
            raw_data = f.read()
            detected = chardet.detect(raw_data)
            encoding = detected["encoding"] if detected["confidence"] > 0.5 else "utf-8"
        content = raw_data.decode(encoding)
        logging.info(f"Successfully read file: {file_path} (Encoding: {encoding})")
        return content
    except UnicodeDecodeError:
        logging.error(f"UnicodeDecodeError in file: {file_path}. Using fallback decoding.")
        return raw_data.decode("utf-8", errors="ignore")
    except Exception as e:
        logging.error(f"Unexpected error reading file {file_path}: {e}")
        return f"[Error reading {file_path}]"

def select_agent_story():
    """Allows user to select an agent story from available options."""
    options = list(placeholder_mapping.keys())
    
    print("\nAvailable Agent Stories:")
    for i, option in enumerate(options, 1):
        # Extract story name from placeholder
        story_name = option.strip('{}').replace('AGENT_STORY_', '')
        print(f"{i}. {story_name}")
    
    while True:
        try:
            choice = int(input("\nSelect an agent story (enter number): "))
            if 1 <= choice <= len(options):
                selected_key = options[choice-1]
                return selected_key, placeholder_mapping[selected_key]
            else:
                print(f"Please enter a number between 1 and {len(options)}")
        except ValueError:
            print("Please enter a valid number")

def generate_prompt_from_template(agent_story_key, agent_story_path):
    """Generates the prompt using the template file and the selected agent story."""
    story_content = read_file_safely(agent_story_path)
    story_name = agent_story_key.strip('{}').replace('AGENT_STORY_', '').lower()
    
    # Read the template file
    template_path = script_directory / "agent-story-puml-template.txt"
    if not template_path.exists():
        logging.error(f"Template file not found: {template_path}")
        print(f"Error: Template file 'agent-story-puml-template.txt' not found in {script_directory}")
        sys.exit(1)
    
    template_content = read_file_safely(template_path)
    
    # Replace the placeholder with the actual story content
    prompt_content = template_content.replace('{AGENT_STORY_CONTENT}', story_content)
    
    return prompt_content, story_name

def main():
    print("Agent Story to PlantUML Converter")
    print("=================================")
    
    # Allow user to select an agent story
    selected_key, selected_path = select_agent_story()
    story_name = selected_key.strip('{}').replace('AGENT_STORY_', '').lower()
    logging.info(f"Selected agent story: {selected_key} at {selected_path}")
    
    # Generate prompt using template
    prompt_content, story_name = generate_prompt_from_template(selected_key, selected_path)
    
    # Create output filename with .txt extension and agent story name
    output_filename = f"agent_story_{story_name}.txt"
    prompt_output_path = script_directory / output_filename
    
    try:
        prompt_output_path.write_text(prompt_content, encoding="utf-8")
        logging.info(f"Prompt saved to {prompt_output_path}")
        print(f"\nPrompt has been generated and saved to: {prompt_output_path}")
        print("\nUse this prompt with GPT to generate a PlantUML diagram of the agent story.")
        print(f"After receiving the PlantUML code from GPT, save it to a .puml file to view it.")
    except Exception as e:
        logging.error(f"Failed to write prompt: {e}")
        print(f"Error: Failed to write prompt: {e}")

if __name__ == "__main__":
    main()