import logging
import chardet  # Requires `pip install chardet`
from pathlib import Path

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(levelname)s - %(message)s",
)

# Use script_directory to ensure relative paths are based on the script location
script_directory = Path(__file__).parent

# Mapping between placeholders and the actual file paths (wrapped as Path objects)
placeholder_mapping = {
    # Agent Story placeholders
    '{AGENT_STORY_WHEEL_GENERATION}': Path("/home/<USER>/Documents/chilltech/docs/agents/agent_scenarios/wheelgeneration.md"),
    '{AGENT_STORY_POST_SPIN}': Path("/home/<USER>/Documents/chilltech/docs/agents/agent_scenarios/postspin.md"),
    '{AGENT_STORY_POST_ACTIVITY}': Path("/home/<USER>/Documents/chilltech/docs/agents/agent_scenarios/postactivity.md"),
    # Data Model placeholders: code
    '{MODEL_ACTIVITY}': Path("/home/<USER>/Documents/chilltech/backend/apps/activity/models.py"),
    '{MODEL_MAIN}': Path("/home/<USER>/Documents/chilltech/backend/apps/main/models.py"),
    '{MODEL_USER}': Path("/home/<USER>/Documents/chilltech/backend/apps/user/models.py"),
}

# Marker indicating where to append context information.
CONTEXT_MARKER = "## 4. Context for this Request"

def read_file_safely(file_path):
    """Reads a file with detected encoding, ensuring robust error handling."""
    if not file_path.exists():
        logging.error(f"File not found: {file_path}")
        return f"[Error: File {file_path} not found]"
    
    try:
        with file_path.open("rb") as f:
            raw_data = f.read()
            detected = chardet.detect(raw_data)
            encoding = detected["encoding"] if detected["confidence"] > 0.5 else "utf-8"
        content = raw_data.decode(encoding)
        logging.info(f"Successfully read file: {file_path} (Encoding: {encoding})")
        return content
    except UnicodeDecodeError:
        logging.error(f"UnicodeDecodeError in file: {file_path}. Using fallback decoding.")
        return raw_data.decode("utf-8", errors="ignore")
    except Exception as e:
        logging.error(f"Unexpected error reading file {file_path}: {e}")
        return f"[Error reading {file_path}]"

def main():
    # Load the prompt template
    template_path = script_directory / "prompt_template.txt"
    if not template_path.exists():
        logging.error(f"Template file not found: {template_path}")
        return
    
    try:
        full_template = template_path.read_text(encoding="utf-8")
        logging.info("Successfully loaded prompt template file.")
    except Exception as e:
        logging.error(f"Failed to read template file: {e}")
        return

    # Split the template into main content and context (if marker exists)
    if CONTEXT_MARKER in full_template:
        main_content, _ = full_template.split(CONTEXT_MARKER, 1)
    else:
        main_content = full_template
        logging.warning("Context marker not found in template; context will be appended at the end.")

    # Replace placeholders in the main content
    for placeholder, file_path in placeholder_mapping.items():
        logging.debug(f"Processing placeholder: {placeholder} -> {file_path}")
        file_content = read_file_safely(file_path)
        if placeholder in main_content:
            main_content = main_content.replace(placeholder, file_content)
            logging.info(f"Replaced placeholder: {placeholder}")
        else:
            logging.info(f"Placeholder {placeholder} not found in main content; will be added in context.")

    # Build the context section from all file contents
    context_lines = [CONTEXT_MARKER, ""]
    for placeholder, file_path in placeholder_mapping.items():
        context_lines.append(f"### {placeholder.strip('{}')}")
        context_lines.append(read_file_safely(file_path))
        context_lines.append("")  # Blank line between entries
    context_section = "\n".join(context_lines)

    # Final output: main content + context section
    final_output = main_content.strip() + "\n\n" + context_section.strip() + "\n"

    # Write the result to output_generated.txt in the same folder as the script
    output_path = script_directory / "output_generated.txt"
    try:
        output_path.write_text(final_output, encoding="utf-8")
        logging.info(f"Template processing complete. Output saved to {output_path}")
    except Exception as e:
        logging.error(f"Failed to write output file: {e}")

if __name__ == "__main__":
    main()
