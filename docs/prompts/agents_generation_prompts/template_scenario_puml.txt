# Agent Story to PlantUML Conversion

## 1. Instructions
Please convert the following agent story into a PlantUML graph diagram that shows:

1. All agents mentioned in the story with proper color coding:
   - Mentor Agent: #FFCCE5
   - Orchestrator Agent: #CCE5FF
   - Utility Agents (Resource & Capacity, Engagement & Pattern Analytics, Psychological Monitoring): #E5FFCC
   - Strategy and Wheel/Activity Agents: #FFE5CC
   - Ethical Oversight Agent: #E5CCFF

2. All interactions between agents (use arrows to show data/instruction flow)

3. All database objects that agents interact with (use different shape for database objects)

4. Proper labeling of all connections between agents and between agents and database objects

## 2. Agent Story
{AGENT_STORY_PLACEHOLDER}

## 3. Expected Output Format
Please respond with a complete PlantUML diagram code using standard PlantUML syntax. Start with @startuml and end with @enduml. Include all necessary styling and coloring to make the diagram clear and visually appealing.

## 4. Context for this Request
