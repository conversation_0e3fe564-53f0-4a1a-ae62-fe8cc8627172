# Streamlined Agent Workflow Template

## Purpose and Usage Instructions

This template provides a standardized format for documenting each agent's role in the Game of Life workflow. Use this template to:

1. **Define clear responsibility boundaries** between agents in the system
2. **Establish consistent interfaces** for data exchange between agents
3. **Document integrated decision-making logic** within core responsibilities
4. **Set benchmarks** for expected output quality and format
5. **Maintain traceability** between agent activities and system objectives

### How to Use This Template

1. **Fill out all sections completely** for each agent in the workflow
2. **Maintain consistency** in terminology across agent definitions
3. **Be specific about data access patterns** - clearly distinguish between read, write, and recommend operations
4. **Integrate decision criteria into core responsibilities** as needed - formal IF-THEN-ELSE structures are optional
5. **Provide processing logic steps** that transform inputs to outputs
6. **Include benchmark output descriptions** that establish quality expectations
7. **Document state transitions** to maintain workflow continuity
8. **Keep documentation concise** - total template should not exceed 500 lines

### What to Avoid

- **Redundant sections** - avoid separating decision logic from core responsibilities
- **Implementation details** - focus on what transformations occur, not how they're coded
- **Contradictory instructions** between agents - ensure logical consistency
- **Overly creative embellishments** - maintain focus on functional requirements
- **Incomplete transitions** - every agent should have clear next states defined
- **Circular references** - avoid creating loops without exit conditions
- **Inventing features** - stick to what's documented in the project requirements
- **Excessive precision** - avoid made-up numeric thresholds without clear justification

## Template Structure

```
### [Agent Name]

**Entry Point & Input:**
- Trigger: [Event that initiates this agent's activity]
- Input: [Detailed description of incoming data structure and key components]

**Data Access:**
- Read: [Data model elements accessed with specific purpose]
- Write: [Data model elements directly modified]
- Recommend: [Suggested updates requiring validation, or "None" with explanation]
- Memory Access: 
  - Read: [Persistent memory elements accessed, or "None (stateless for MVP)"]
  - Write: [Memory updates performed]

**Core Responsibilities:**
- [Primary responsibility 1 with specific details]
  - [Include decision criteria as appropriate - formal IF-THEN-ELSE structures optional]
- [Primary responsibility 2 with specific details]
  - [Include decision criteria as appropriate]
- [Additional responsibilities as needed, each with specific details and any relevant decision logic]

**Processing Logic:**
- [Processing step 1]:
  - [Sub-step A with specific transformation]
  - [Sub-step B with specific transformation]
- [Processing step 2]:
  - [Similar structure with specific data manipulations]

**Expected Output Benchmark:**
- [Output component 1] containing:
  - [Section A with specific content requirements]
  - [Section B with specific content requirements]
  - [Additional sections as needed]
- [Output component 2] containing:
  - [Similar structure with specific content requirements]
- All outputs must meet these quality standards:
  - [Quality standard 1]
  - [Quality standard 2]
  - [Additional quality standards as needed]

**Output & Next Step:**
- To [Receiving Agent]: [Specific output package with key components]
```

## Section Definitions

### Agent Name
The official agent name from the system architecture (e.g., "Psychological Monitoring Agent"). Be consistent with naming conventions used in documentation.

### Entry Point & Input

Combine the trigger event with a detailed description of the input data:

- **Trigger:** The specific event that activates this agent (system event, message receipt, user action, scheduled event)
- **Input:** Detailed breakdown of the incoming data structure, including:
  - Major data components with their specific contents
  - Format expectations for each component
  - Any workflow metadata or identifiers included

Example:
```
**Entry Point & Input:**
- Trigger: Receipt of context packet from Mentor Agent
- Input: Structured context packet containing:
  - Emotional state (mood, energy level, emotional indicators)
  - Environmental context (location, resources, constraints)
  - Time availability (free periods, duration preferences)
  - Cognitive focus (thoughts, concerns, interests)
  - Trust and satisfaction information (metrics with evidence)
  - Workflow identifiers and metadata
```

### Data Access

**Read:** List all data models the agent accesses but doesn't modify:
- Specify model names from the data architecture (e.g., "UserTraitInclination, TrustLevel, Belief")
- Include purpose of access (e.g., "for trait analysis")
- Indicate scope limitations if applicable (e.g., "limited to 30-day history")

**Write:** Specify data models the agent directly updates:
- List models with specific fields when possible (e.g., "CurrentMood parameters")
- If the agent doesn't perform direct writes, explain briefly

**Recommend:** List suggested updates requiring later validation:
- Models that should be updated by other agents (e.g., "TrustLevel adjustments")
- Include explicit note if agent makes no recommendations: "None (this agent does not make recommendation updates in MVP)"
- For agents that do make recommendations, specify:
  - Which data models are targeted
  - What types of updates are recommended
  - What evidence supports these recommendations

**Memory Access:** For stateful agents only:
- **Read:** Memory elements retrieved (e.g., "Trait expression patterns, trust development history")
- **Write:** Memory updates performed (e.g., "Updated psychological patterns")
- For stateless agents: "None (stateless for MVP)"

### Core Responsibilities

List the primary functions of the agent with specific details and decision criteria as appropriate:
- Focus on WHAT the agent does, including how decisions are made within each responsibility
- Include scope boundaries for each responsibility
- Decision criteria can be described in natural language; formal IF-THEN-ELSE structures are optional
- Structure as 5-7 clear bullet points with sub-bullets for details
- Include memory updates if the agent is stateful

Example:
```
**Core Responsibilities:**
- Assess current psychological state:
  - Evaluate mood against historical patterns
  - Determine energy levels and cognitive capacity
  - Identify emotional indicators relevant to activity receptiveness
  - When mood shows negative deviation from baseline, flag for reduced challenge level
- Determine trust phase and calibrate challenges accordingly:
  - Analyze trust metrics and activity completion history
  - Evaluate readiness for challenge progression
  - Assign appropriate trust phase based on metrics and history
  - Set challenge parameters according to trust phase
- [Additional responsibilities with relevant decision criteria...]
```

### Processing Logic

Detail the steps that transform inputs into outputs:
- Organize as sequential processing operations
- Document key transformations and analyses performed
- Focus on WHAT is processed, not implementation details
- Include data synthesis and integration approaches
- Structure as numbered steps with bullet sub-steps

Example:
```
**Processing Logic:**
1. Current State Assessment:
   - Extract mood indicators from context packet
   - Compare with historical patterns from memory
   - Update CurrentMood parameters
   - Identify mood-based activity constraints

2. Trust Evaluation:
   - Analyze direct trust feedback from context
   - Retrieve trust development history from memory
   - Apply trust phase determination criteria
   - Assess readiness for challenge progression

3. [Additional processing steps...]
```

### Expected Output Benchmark

Describe the expected format, content, and quality standards for the agent's output in detail:
- Structure as main output components with nested sections
- Specify exactly what each section should contain
- Include quality standards that all outputs must meet
- Avoid providing literal examples (use descriptions instead)
- Focus on content requirements, not formatting specifics

Example:
```
**Expected Output Benchmark:**
- Psychological report containing:
  - Current state section with mood assessment, energy level, and cognitive state
  - Trust phase section identifying foundation, transition, or expansion phase with supporting evidence
  - Trait profile section analyzing all HEXACO dimensions with current values and growth indicators
  - Belief system section highlighting relevant limitations and supports
  - Growth recommendation section prioritizing development areas with goal connections
  - Challenge calibration section with parameters for different trait dimensions
- All outputs must meet these quality standards:
  - Trust phase determination must include supporting evidence
  - Trait analyses must connect to both patterns and goals
  - Growth recommendations must link to stated aspirations
  - Challenge calibrations must include clear rationales
  - Vulnerability flags must be included where relevant
```

### Output & Next Step

Define what is output and to which agent it flows:
- Specify the receiving agent(s)
- Detail the key components of the output package
- For conditional branches, specify criteria for each path
- Include error handling paths where appropriate

Example:
```
**Output & Next Step:**
- To Orchestrator Agent: Psychological report with current state assessment, trust phase determination, trait analysis, belief insights, and challenge recommendations
```

## Memory Implementation Guidelines

For MVP, agent memory should complement the data model by storing derived insights rather than duplicating stored data. Only four agents require memory for MVP, with each focusing on specific high-value patterns that enhance performance.

### Mentor Agent Memory

**Purpose:** Optimize communication effectiveness without requiring repeated user preference learning

**Essential Memory Elements:**
- **Communication preferences**: Effective tone, detail level, and metaphor types that resonate with this specific user
- **Conversation tracking**: Recently discussed topics and successful approaches to prevent repetition and maintain continuity

**Relationship to Data Model:** 
- While user traits and feedback are stored in the data model, the derived communication patterns represent synthesized insights about effective engagement approaches

### Psychological Monitoring Agent Memory

**Purpose:** Track psychological patterns that require longitudinal analysis

**Essential Memory Elements:**
- **Trait expression patterns**: How consistently traits are expressed across contexts
- **Trust development history**: Response to challenges and recovery patterns after setbacks
- **Growth area indicators**: Areas where user shows resistance but would benefit from development based on goals

**Relationship to Data Model:**
- Complements UserTraitInclination and Belief records by tracking dynamic patterns rather than point-in-time measurements
- Enables identification of growth opportunities in areas where users show resistance but would benefit from development to reach their goals

### Engagement & Pattern Analytics Agent Memory

**Purpose:** Store pre-computed pattern analysis to improve performance

**Essential Memory Elements:**
- **Domain engagement metrics**: Pre-calculated metrics on user's engagement with different activity domains
- **Pattern confidence scores**: Statistical reliability of identified patterns based on sample size
- **Activity response patterns**: Which activity types consistently engage or disengage the user

**Relationship to Data Model:**
- Transforms raw HistoryLog and UserFeedbackLog data into actionable engagement insights
- Reduces repeated computation of stable patterns

### Strategy Agent Memory

**Purpose:** Maintain strategic continuity between sessions

**Essential Memory Elements:**
- **Baseline strategy**: Core domain distribution and challenge calibration parameters
- **Trust-based adaptations**: How strategy shifts between foundation phase (comfort-oriented) and expansion phase (growth-oriented)
- **Goal-trait alignments**: Which traits support user's stated goals and aspirations

**Relationship to Data Model:**
- Uses the UserGoal and UserTraitInclination gap analysis to inform strategic decisions
- Adapts based on trust phases to balance familiar activities with appropriate challenges