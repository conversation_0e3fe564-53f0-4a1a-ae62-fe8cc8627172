## 1. Goal
Analyze and refine the consistency, data alignment, and overall coherence of the three Game of Life agent stories.

## 2. Output Structure

1. **Executive Summary**
   - Briefly summarize major findings or inconsistencies.
   - Highlight any critical issues that block MVP completion.

2. **Agent Story Analysis**
   - **Wheel Generation:** Check internal logic, references to the data model, and synergy with MVP scope.
   - **Post-Spin:** Assess how it handles acceptance/refusal, logs feedback, and references the data model.
   - **Post-Activity:** Evaluate feedback capture, user model updates, and trust metric recalibration.

3. **Data Model Alignment**
   - Identify mismatches between the agent stories and the provided data model documentation.
   - Propose specific corrections or clarifications.

4. **Cross-Story Consistency**
   - Verify that terminology, trust levels, user feedback loops, and environment references are consistent.
   - Suggest any needed standardization.

5. **MVP Scope Review**
   - Flag features or logic that exceed the MVP’s complexity threshold.
   - Recommend any phased approaches for advanced features.

6. **Implementation Recommendations**
   - Provide step-by-step or bullet-pointed suggestions to address identified issues.
   - Include any final clarifications or next steps.

---

## 3. Things to Avoid
- Introducing new features or data structures not mentioned in the existing context.
- Overly broad suggestions that lack specific references to the agent stories or data model.
- Changing essential ethical or trust-related logic beyond what is required for MVP coherence.
- Making assumptions about external integrations (e.g., third-party APIs) that aren’t in scope.

---

## 4. Context for this Request

### Agent Stories
1. **Wheel Generation:**  
   {AGENT_STORY_WHEEL_GENERATION}

2. **Post-Spin:**  
   {AGENT_STORY_POST_SPIN}

3. **Post-Activity:**  
   {AGENT_STORY_POST_ACTIVITY}

### Data Model Code & Documentation
- **Activity App:**  
  **Code:** {MODEL_ACTIVITY}  

- **Main App:**  
  **Code:** {MODEL_MAIN}  

- **User App:**  
  **Code:** {MODEL_USER}  
