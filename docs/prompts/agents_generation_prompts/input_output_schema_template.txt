
# Enhanced Agent Interaction Schema Generator Template

## GOAL
Create comprehensive, bullet-point schemas representing the complete interaction workflow for each agent in the Game of Life scenario. These schemas will:
- Mirror the exact behavior of a production-ready system
- Document each information flow with no logical gaps
- Serve as a foundation for distilling generic input/output schemas and agent instructions
- Function as benchmarks for validating agent implementations

---

## OUTPUT STRUCTURE
The output will consist of a detailed, sequential bullet-point workflow covering:

1. **Scenario Context**
   - Specific user context derived from the user story
   - Current state of the system when the workflow begins
   - Environmental and temporal context that affects the interaction

2. **Agent Flow Overview**
   - Sequential list of agents activated in the workflow
   - Primary purpose of each agent in this specific scenario
   - How the flow connects to preceding and subsequent workflows

3. **For Each Agent in Sequence:**
   - **Input Data Structure**
     - Complete context packet received
     - Data model fields accessed and their specific values
     - User context information with actual values from user story
     - Request parameters and queries
     - Information source ("receivedFrom")

   - **Processing Steps**
     - Detailed algorithm with decision branches
     - Specific criteria evaluated at each decision point
     - Pattern recognition and analysis approaches
     - Connections to historical data and reasoning

   - **Output Data Structure**
     - Complete result package produced
     - Recommendations with confidence levels
     - Decision justifications with alternatives considered
     - Data model updates (direct vs. recommended)
     - Destination ("forwardTo")

4. **Data Flow Visualization**
   - Step-by-step information movement between agents
   - Transformation of data at each stage
   - Aggregation points and decision branches

5. **Final System State**
   - Updated data model values
   - Persistent changes vs. ephemeral recommendations
   - State preparation for the next workflow

---

## THINGS TO PRIORITIZE
- **Concrete Realism:** Use specific, plausible values rather than placeholders
- **Logical Completeness:** Ensure no steps are skipped or abstracted
- **Data Model Alignment:** Reference actual Django model fields with realistic values
- **Workflow Connection:** Show how this scenario connects to preceding and subsequent workflows
- **Decision Explicitness:** Detail exactly how and why agents make specific choices
- **Trust Development Integration:** Incorporate how the interaction affects user trust metrics
- **User Story Fidelity:** Maintain consistency with the personality, goals, and context from the user story

---

## THINGS TO AVOID
- Actual JSON syntax (use bullet points instead)
- Sequence diagrams or PUML notation
- Features beyond MVP scope
- Inventing capabilities not in documentation
- Abstract placeholders instead of concrete examples
- Skipping steps in the information flow
- Overlooking how data transformations occur
- Missing connections between agents

---

## SCHEMA GENERATION PROCESS

### 1. User Context Establishment
- Extract concrete details from the user story (name, age, location, goals, preferences)
- Document the specific psychological state (current mood, trust level, challenges)
- Capture environmental context (time of day, physical location, available resources)
- Note recent history that affects current session (previous activities, feedback)

### 2. Map Complete Agent Interaction Flow
- Identify initiating event that triggers the workflow
- List every agent that activates in order
- Document all Orchestrator interactions between specialized agents
- Track every data transformation from raw input to final output
- Ensure complete coverage of the scenario from beginning to end

### 3. Define Real-World Agent Schemas

For each agent involved, create detailed schemas following this structure:

#### Input Schema
- **agent:** [Full agent name]
- **purpose:** [Detailed description of function and goals]
- **inputData:**
  - **contextPacket:**
    - [Detailed user context relevant to this agent]
    - [System state information]
    - [Historical data references]
  - **systemData:**
    - [Specific data models accessed with actual field names]
    - [Realistic values that would exist in production]
  - **specificRequest:**
    - [Exact query or task parameters]
    - [Timing and urgency information]
- **receivedFrom:** [Source agent]
- **accessedDataModels:**
  - [List each Django model accessed with specific fields]
  - [Include exact queries or filters applied]
- **process:**
  - [Step-by-step algorithm the agent follows]
  - [Decision points with explicit criteria]
  - [Data analysis approaches]
  - [Pattern recognition techniques]

#### Output Schema
- **agent:** [Full agent name]
- **outputData:**
  - **primaryOutput:**
    - [Core results produced]
    - [References to actual Django model fields]
  - **supportingData:**
    - [Contextual information explaining the output]
    - [Evidence supporting decisions made]
  - **recommendations:**
    - [Specific suggested updates to data models]
    - [Future action recommendations]
  - **confidenceMetrics:**
    - [Quantified confidence levels for each output element]
    - [Uncertainty factors identified]
  - **decisionPoints:**
    - [List of all choice points encountered]
    - [Criteria used at each decision]
    - [Rationale for the path taken]
    - [Alternatives considered with reasons for rejection]
- **forwardTo:** [Destination agent]
- **updatedDataModels:**
  - [Direct updates made to specific fields]
  - [Recommended updates passed to Orchestrator]

### 4. Create Real-World Data Examples
- Develop a continuous story-like example that follows a single user session
- Use the user story characters (Philipp or Wilhelm) as the foundation
- Create specific, plausible values that might appear in production:
  - **User Context Example:**
    - mood: "Anxious but motivated to try something new"
    - current_environment: "Farm near Lyon, overcast weather"
    - available_time: "4 hours before dinner with friend"
    - recent_activities: "Fixed bicycle yesterday, completed farm chore wheel spin two days ago"
    - trust_level: 68 (growing but still developing)
    - user_trait_inclinations: [Extraversion: 42, Openness: 76, Conscientiousness: 39...]

### 5. Document Progression Logic
- For each decision point, specify:
  - Exact criteria evaluated (e.g., "trust_level > 65 && available_time > 2 hours")
  - Full rationale ("Higher trust allows exploration activities that require more time commitment")
  - How the decision connects to the user's psychological profile
  - Alternative paths that would be taken under different conditions

### 6. Connect to Trust Development Framework
- Explicitly show how each interaction:
  - Impacts the trust metric
  - Relates to Foundation vs. Expansion phase
  - Accommodates user psychological state
  - Balances challenge and support

### 7. Demonstrate Data Model Updates
- List exactly which model fields change and how:
  - Direct updates (immediate changes to the database)
  - Recommended updates (suggestions passed to Post-Activity workflow)
  - Temporary/session values vs. persistent profile changes

### 8. Validate Against MVP Scope
- Cross-check all functionality against the documented MVP capabilities
- Ensure alignment with the three core workflows (Wheel Generation, Post-Spin, Post-Activity)
- Focus on the priority features as defined in the implementation roadmap

### 9. Show Integration Points
- Document how this workflow:
  - Receives inputs from previous workflows
  - Produces outputs for subsequent workflows
  - Maintains continuity through the HistoryLog
  - Preserves context for future user sessions

---

## SCENARIO-SPECIFIC CONSIDERATIONS

### For Wheel Generation Scenario
- Focus on how agent team dynamically adapts to user's current context
- Show challenge calibration based on trust level and psychological state
- Demonstrate dynamic wheel item selection and probability weighting
- Showcase ethical validation of all selected activities

### For Post-Spin Scenario
- Emphasize handling of both primary acceptance and emergency refusal paths
- Demonstrate trust metric impact from commitment follow-through
- Show preparation for activity handoff to Post-Activity workflow
- Highlight exceptional path handling for emergency situations

### For Post-Activity Scenario
- Focus on comprehensive user experience data capture
- Show integration of feedback into user model updates
- Demonstrate trust level evaluation and adjustment
- Highlight preparation for next Wheel Generation cycle

---

## EXAMPLE FORMAT (PARTIAL)

### Wheel Generation Scenario - Philipp's Morning Session

**1. Initial Context**
- **User Profile:**
  - user_profile.profile_name: "Philipp"
  - demographics.age: 22
  - demographics.occupation: "Currently unemployed, former student"
  - current_environment: "Friend's farm near Lyon"
  - [Additional relevant profile fields]

- **Psychological State:**
  - current_mood: "Motivated but anxious"
  - trust_level.value: 68
  - trust_phase: "Early Expansion"
  - dominant_traits: [Openness: 76, Agreeableness: 62, Extraversion: 42, Conscientiousness: 39, Neuroticism: 57]
  - [Other relevant psychological state fields]

- **Environmental Context:**
  - time_of_day: "09:30 AM"
  - weather: "Partly cloudy"
  - available_time: "4 hours"
  - available_resources: ["Basic farm tools", "Bicycle", "Art supplies", "Internet connection"]
  - location_context: "Isolated farm, 5km from nearest village"

**2. Agent Flow Overview**
1. Mentor Agent (Initial user interaction)
2. Orchestrator Agent (Task distribution)
3. Resource & Capacity Agent (Resource analysis)
4. Engagement & Pattern Analytics Agent (Historical analysis)
5. Psychological Monitoring Agent (Psychological assessment)
6. Strategy Agent (Challenge calibration)
7. Wheel/Activity Agent (Activity selection)
8. Ethical Oversight Agent (Validation)
9. Mentor Agent (Final presentation)

**3. Agent Interaction Schemas**

#### Mentor Agent - Initial Interaction
- **agent:** "Mentor Agent"
- **purpose:** "Engage with user to gather current context and state for wheel generation"
- **inputData:** null (Initial activation point)
- **process:**
  - Greet user with personalized message: "Good morning, Philipp! How are you feeling today?"
  - Retrieve recent HistoryLog entries to avoid repetitive questions
  - Present structured dialogue to gather:
    - Current mood and energy level
    - Available time today
    - Environmental factors
    - Any specific intentions or concerns
  - Process natural language responses to extract:
    - Emotional state indicators
    - Time availability parameters
    - Environmental constraints
    - Current goals or aspirations
- **outputData:**
  - **contextPacket:**
    - user_id: "philipp_user_profile_id"
    - session_timestamp: "2025-03-12T09:30:00Z"
    - reported_mood: "Feeling motivated but a bit anxious about starting something new"
    - reported_environment: "At the farm, weather is okay, my friend is away for the day"
    - reported_time_availability: "I have about 4 hours before I need to prepare dinner"
    - reported_focus: "I've been thinking about trying something creative but I'm also feeling like I should do something useful around the farm"
    - reported_trust: "The last activity was good, I'm curious what comes next"
  - **forwardTo:** "Orchestrator Agent"
  - **updatedDataModels:** None (Context gathering only)

#### Orchestrator Agent - Initial Coordination
- **agent:** "Orchestrator Agent"
- **purpose:** "Determine task sequence and distribute to specialized agents"
- **receivedFrom:** "Mentor Agent"
- **inputData:**
  - **contextPacket:** [Full packet from Mentor Agent]
- **process:**
  - Identify requirement: "Generate new activity wheel"
  - Determine agent activation sequence:
    - Resource & Capacity + Engagement & Pattern Analytics (parallel)
    - Psychological Monitoring
    - Strategy
    - Wheel/Activity
    - Ethical Oversight
  - Create specialized agent requests with appropriate context subsets

[Continue with detailed schemas for each agent in sequence]

---

## BENCHMARKING APPLICATION
These bullet-point schemas will serve as:
- Reference implementations for agent behavior
- Validation benchmarks for real implementation
- Sources for distilling generic input/output schemas
- Templates for converting to JSON schemas later
- Documentation for cross-team alignment
- Debugging references for system integration

---

## USAGE INSTRUCTIONS

1. Select the specific scenario to model (Wheel Generation, Post-Spin, or Post-Activity)
2. Choose which user story to use as context (Philipp or Wilhelm)
3. Generate a complete end-to-end workflow with no logical gaps
4. Use the output to derive generic agent schemas and behavior specifications
5. Iterate on edge cases and alternative paths as needed
6. Refine until the workflow represents a production-ready system