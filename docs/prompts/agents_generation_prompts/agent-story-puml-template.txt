# Agent Story to PlantUML Conversion

## Goal
Create a clean, structured PlantUML diagram that represents agent interactions with clear organization, proper arrow routing, and effective data model integration while maintaining flexibility for different agent stories.

## Core Structure Guidelines
- **Balanced Hierarchy**: User → Mentor Agent → Orchestrator Agent → Specialized Agents in a single row
- **Optimized Arrow Routing**: Ensure arrows don't cross objects and minimize overlaps
- **Enhanced Data Model Integration**: Clear connections between agents and data sources
- **Flexible Database Positioning**: Strategic placement of databases to maintain clarity
- **Minimal Text**: Keep connection labels concise but meaningful
- **Readable Layout**: Ensure adequate spacing and clear organization

## PlantUML Base Configuration
```
@startuml
' Essential layout directives
!pragma layout smetana
top to bottom direction

' Basic styling
skinparam padding 2
skinparam backgroundColor transparent
skinparam roundCorner 15
skinparam ArrowColor black
skinparam linetype ortho
skinparam nodesep 100
skinparam ranksep 80
skinparam DefaultTextAlignment center
skinparam wrapWidth 120
skinparam maxMessageSize 50

' Core actor and agents
actor "User" as User
rectangle "Mentor Agent" as MentorAgent #FFCCE5
rectangle "Orchestrator Agent" as OrchestratorAgent #CCE5FF

' Orchestrator note - customize for each story
note right of OrchestratorAgent
  Coordinates specialized agents
  based on user context
end note

' Position specialized agents in a single row while maintaining flexibility
together {
  rectangle "Strategy Agent" as StrategyAgent #FFE5CC
  rectangle "Wheel/Activity Agent" as WheelAgent #FFE5CC
  rectangle "Ethical Oversight Agent" as EthicsAgent #E5CCFF
  ' Add other specialized agents based on the story
}

' Subtle vertical structure guidance without overconstraining
User -[hidden]d-> MentorAgent
MentorAgent -[hidden]d-> OrchestratorAgent
OrchestratorAgent -[hidden]d-> StrategyAgent

' Keep specialized agents on same level with some spacing
StrategyAgent -[hidden]r-> WheelAgent
WheelAgent -[hidden]r-> EthicsAgent

' Core vertical relationships with meaningful labels
User -d-> MentorAgent: input
MentorAgent -d-> OrchestratorAgent: context

' Connect Orchestrator to specialized agents with proper routing
OrchestratorAgent -d-> StrategyAgent: request
OrchestratorAgent -d-> WheelAgent: request
OrchestratorAgent -d-> EthicsAgent: review

' Return connections with clean routing
StrategyAgent -u-> OrchestratorAgent: strategy
WheelAgent -u-> OrchestratorAgent: activities
EthicsAgent -u-> OrchestratorAgent: validation

' Database objects with strategic positioning
database "UserDB" as UserDB #F0F0F0
database "StrategyDB" as StrategyDB #F0F0F0
database "ActivityDB" as ActivityDB #F0F0F0

' Position databases strategically for best data flow visualization
UserDB -[hidden]r-> MentorAgent
StrategyDB -[hidden]r-> StrategyAgent
ActivityDB -[hidden]r-> WheelAgent

' Database connections with meaningful labels
UserDB --> MentorAgent: profile
StrategyDB --> StrategyAgent: frameworks
ActivityDB --> WheelAgent: options

' Add story-specific elements and connections below
```

## Flexible Adaptation Guidelines

### Agent Structure
1. **Core Agents**: Always include User, Mentor Agent, and Orchestrator Agent
2. **Specialized Agents**:
   - Add, remove, or modify based on the specific agent story
   - Keep all specialized agents in a single hierarchical row when possible
   - Use the `together {}` block to ensure alignment

### Arrow Routing Optimization
1. **Clean Pathways**: Ensure arrows don't cross objects or other arrows when possible
2. **Consistent Direction**: Maintain downward flow for requests, upward for responses
3. **Strategic Layout**: Position elements to minimize crossing connections
4. **Connection Types**:
   - Use solid arrows for direct interactions
   - Consider using different arrow styles for different types of data flow if needed

### Database Implementation
1. **Strategic Positioning**:
   - Place databases near their primary interacting agents
   - Position to minimize crossing connections
   - Use hidden connections to maintain proper spacing
2. **Connection Logic**: Connect databases to relevant agents with clear, labeled arrows
3. **Visual Integration**: Ensure database connections clearly show how data flows through the system

### Connection Guidelines
1. **Primary Flow**: Maintain the core hierarchical flow
2. **Label Style**: Use short, descriptive phrases that convey meaning
3. **Optional Inter-Agent Communication**:
   - Allow direct connections between specialized agents when explicitly stated in the story
   - Route these connections carefully to avoid crossing other elements

## Common PlantUML Issues to Avoid
1. **Error: Same diagram description contains errors**
   - Ensure all named elements have unique identifiers
   - Check for syntax errors in element definitions
   - Verify all connections reference valid elements

2. **Layout Problems**
   - Use `[hidden]` connections strategically but sparingly
   - Allow PlantUML to handle basic positioning
   - Use layout directives to guide rather than force positioning

3. **Readability Issues**
   - Avoid overlapping elements and connections
   - Don't overcrowd the diagram with too many elements
   - Use colors consistently to aid visual understanding

## Agent Story Integration Approach
1. Read the entire agent story first to understand the overall flow
2. Identify all agents and their primary responsibilities
3. Map out key interactions between agents
4. Identify databases and other data objects mentioned
5. Adapt the base template with these story-specific elements
6. Focus on capturing the essential relationships rather than every detail

## Agent Story
{AGENT_STORY_CONTENT}

## Final Verification Checklist
Before finalizing, verify:
1. ✓ User → Mentor → Orchestrator → Specialized Agents hierarchy is clear
2. ✓ Specialized agents are organized in a logical row
3. ✓ Arrows are routed cleanly without crossing objects when possible
4. ✓ Databases are positioned strategically to show clear data flow
5. ✓ All connections have concise, meaningful labels
6. ✓ The Orchestrator has a brief note describing its role
7. ✓ The diagram maintains visual clarity and readability

## Request for Clarification
If any part of the agent story is unclear or if you need additional information about the following, please ask specific questions before creating the diagram:
1. How certain agents interact with each other or with database objects
2. The specific role or purpose of any agent in the story
3. The nature of data flowing between components
4. Any hierarchical relationships that might not be clear
5. The expected output format or style preferences

This will ensure the PlantUML diagram accurately represents the agent orchestration framework with optimized arrows and clear data model integration.