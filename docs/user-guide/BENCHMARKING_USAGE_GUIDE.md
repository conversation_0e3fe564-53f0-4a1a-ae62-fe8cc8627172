# Benchmarking System Usage Guide

This guide provides practical, step-by-step instructions for using the Goali benchmarking system. It's designed for developers, QA engineers, and anyone who needs to evaluate AI agent performance.

## Table of Contents

- [Quick Start](#quick-start)
- [Creating Your First Benchmark](#creating-your-first-benchmark)
- [Running Benchmarks](#running-benchmarks)
- [Understanding Results](#understanding-results)
- [Advanced Scenarios](#advanced-scenarios)
- [Best Practices](#best-practices)
- [Common Workflows](#common-workflows)

## Quick Start

### 1. Access the Benchmark Dashboard

Navigate to `/admin/benchmarks/` in your browser. You'll see:
- Recent benchmark runs
- Quick run interface
- Links to history and management

### 2. Run an Existing Benchmark

1. Select a scenario from the dropdown
2. Leave default parameters or adjust as needed
3. Click "Run Benchmark"
4. Watch the progress indicator
5. View results when complete

### 3. View Results

Click "View Full Benchmark History" to see:
- Performance trends over time
- Detailed metrics for each run
- Comparison between different configurations

## Creating Your First Benchmark

### Step 1: Plan Your Test

Before creating a benchmark, define:
- **What are you testing?** (specific agent behavior)
- **What input will you provide?** (user message, context)
- **What constitutes success?** (quality criteria)
- **What tools might be called?** (for mocking)

### Step 2: Create the Scenario File

Create a JSON file in the appropriate directory:

**For agent testing**: `backend/testing/benchmark_data/agents/{agent_role}/{workflow_type}/`
**For workflow testing**: `backend/testing/benchmark_data/workflows/{workflow_type}/`

**Example**: `backend/testing/benchmark_data/agents/mentor/wheel_generation/my_test.json`

```json
{
  "name": "Mentor - Wheel Creation Help",
  "description": "Test mentor's ability to help with life wheel creation",
  "agent_role": "mentor",
  "input_data": {
    "context_packet": {
      "user_text": "I'm feeling overwhelmed and need help organizing my life. Can you help me create a life wheel?",
      "trust_level": 30,
      "mood": {
        "valence": -0.3,
        "arousal": 0.7
      }
    }
  },
  "metadata": {
    "expected_quality_criteria": {
      "Empathy": [
        "Acknowledges the user's feeling of being overwhelmed",
        "Shows understanding and compassion"
      ],
      "Guidance": [
        "Provides clear steps for creating a life wheel",
        "Explains the purpose and benefits"
      ],
      "Tone": [
        "Supportive and encouraging",
        "Not overwhelming or pushy"
      ]
    },
    "user_profile_context": {
      "trust_phase": "Foundation",
      "communication_style": "supportive"
    }
  },
  "is_active": true
}
```

### Step 3: Validate the Scenario

```bash
python manage.py validate_benchmarks_v3 --directory backend/testing/benchmark_data/agents/mentor/wheel_generation/
```

Fix any validation errors before proceeding.

### Step 4: Load into Database

```bash
python manage.py create_benchmark_scenarios_v3 --directory backend/testing/benchmark_data/agents/mentor/wheel_generation/
```

### Step 5: Test Your Scenario

Run the benchmark to ensure it works:

```bash
python manage.py run_benchmarks --scenario-name "Mentor - Wheel Creation Help" --params '{"runs": 1, "semantic_evaluation": true}'
```

## Running Benchmarks

### Via Admin Interface

#### Dashboard Method
1. Go to `/admin/benchmarks/`
2. Select your scenario
3. Configure parameters:
   - **Runs**: Number of times to execute (1-10)
   - **Semantic Evaluation**: Enable quality assessment
   - **LLM Model**: Choose the model for the agent
   - **Temperature**: Control randomness (0.0-1.0)
4. Click "Run Benchmark"

#### Management Interface
1. Go to `/admin/benchmarks/manage/`
2. Find your scenario in the list
3. Click "Run" button
4. Configure parameters in the modal
5. Start execution

### Via Command Line

#### Basic Execution
```bash
# Run all active scenarios for an agent
python manage.py run_benchmarks --agent-role mentor

# Run specific scenario
python manage.py run_benchmarks --scenario-name "Your Scenario Name"

# Run with custom parameters
python manage.py run_benchmarks --agent-role mentor --params '{
  "runs": 3,
  "semantic_evaluation": true,
  "agent_llm_model_name": "openai/gpt-4o",
  "llm_temperature": 0.7
}'
```

#### Advanced Parameters
```bash
python manage.py run_benchmarks --agent-role mentor --params '{
  "runs": 5,
  "warmup_runs": 2,
  "semantic_evaluation": true,
  "validate_schema": true,
  "agent_llm_model_name": "openai/gpt-4o",
  "llm_temperature": 0.5,
  "llm_input_token_price": 0.000005,
  "llm_output_token_price": 0.000015
}'
```

### Monitoring Progress

- **Admin Interface**: Progress bar and status updates
- **Command Line**: Log output with timing information
- **Background Tasks**: Check `/admin/benchmarks/history/` for completion

## Understanding Results

### Performance Metrics

#### Duration Metrics
- **Mean Duration**: Average execution time across runs
- **Median Duration**: Middle value (less affected by outliers)
- **Min/Max Duration**: Fastest and slowest runs
- **Standard Deviation**: Consistency of performance

#### Success Metrics
- **Success Rate**: Percentage of successful runs (0-100%)
- **Runs Count**: Total number of executions

### Operational Metrics

#### Resource Usage
- **Tool Calls**: Number of tool invocations
- **Tool Breakdown**: Calls per tool type
- **Memory Operations**: Database interactions

#### Cost Tracking
- **Input Tokens**: Tokens sent to LLM
- **Output Tokens**: Tokens generated by LLM
- **Estimated Cost**: Calculated based on token prices

### Quality Metrics

#### Semantic Evaluation
- **Overall Score**: Aggregate quality score (0-1)
- **Dimensional Scores**: Scores per evaluation dimension
- **Reasoning**: Detailed explanation from evaluator LLM

#### Comparison Analysis
- **Statistical Significance**: Whether performance changes are meaningful
- **P-Value**: Statistical confidence in differences
- **Trend Analysis**: Performance over time

### Interpreting Results

#### Good Performance Indicators
- **Consistent Duration**: Low standard deviation
- **High Success Rate**: >95% for production scenarios
- **Good Semantic Scores**: >0.7 for most dimensions
- **Reasonable Costs**: Within expected token usage

#### Warning Signs
- **High Variability**: Large standard deviation
- **Low Success Rate**: <90% indicates reliability issues
- **Poor Quality Scores**: <0.5 suggests output problems
- **Unexpected Costs**: Much higher than baseline

## Advanced Scenarios

### Tool Mocking

When your agent calls external tools, you can mock their responses:

```json
{
  "metadata": {
    "mock_tool_responses": {
      "get_user_profile": "{'name': 'Test User', 'trust_level': 50}",
      "search_knowledge": [
        {
          "condition": "tool_input.get('query') == 'life wheel'",
          "response": "{'results': ['Life wheel is a visualization tool...']}"
        },
        {
          "condition": "True",
          "response": "{'results': ['General information']}"
        }
      ],
      "save_progress": "{'success': true, 'saved_at': '2024-01-01T12:00:00Z'}"
    }
  }
}
```

### Contextual Evaluation

Adapt evaluation criteria based on user context:

```json
{
  "metadata": {
    "evaluation_criteria_by_phase": {
      "foundation": {
        "Clarity": ["Use simple, clear language"],
        "Support": ["Provide basic encouragement"]
      },
      "expansion": {
        "Clarity": ["Balance clarity with depth"],
        "Support": ["Offer detailed guidance"]
      },
      "integration": {
        "Clarity": ["Use sophisticated communication"],
        "Support": ["Provide philosophical insights"]
      }
    }
  }
}
```

### Multi-Model Evaluation

Use multiple LLMs to evaluate quality:

```json
{
  "metadata": {
    "evaluator_models": [
      "openai/gpt-4o",
      "anthropic/claude-3-haiku-20240307",
      "mistralai/Mistral-7B-Instruct-v0.1"
    ]
  }
}
```

## Best Practices

### Scenario Design

1. **Start Simple**: Begin with basic scenarios before adding complexity
2. **Clear Objectives**: Define exactly what you're testing
3. **Realistic Input**: Use real-world user messages and contexts
4. **Comprehensive Criteria**: Cover all important quality dimensions

### Test Organization

1. **Logical Grouping**: Organize scenarios by agent role and workflow
2. **Descriptive Names**: Use clear, searchable scenario names
3. **Version Control**: Keep scenario files in git
4. **Documentation**: Comment complex scenarios

### Execution Strategy

1. **Regular Testing**: Run benchmarks on code changes
2. **Baseline Establishment**: Create performance baselines
3. **Trend Monitoring**: Watch for performance degradation
4. **Cost Awareness**: Monitor token usage and costs

### Result Analysis

1. **Statistical Significance**: Don't over-interpret small changes
2. **Multiple Runs**: Use enough runs for reliable statistics
3. **Context Consideration**: Consider external factors affecting performance
4. **Actionable Insights**: Focus on results that guide improvements

## Common Workflows

### Development Workflow

1. **Create Feature Branch**
2. **Modify Agent Code**
3. **Run Relevant Benchmarks**
4. **Compare with Baseline**
5. **Fix Issues if Performance Degrades**
6. **Merge When Benchmarks Pass**

### QA Workflow

1. **Create Test Scenarios** for new features
2. **Validate Scenarios** against schemas
3. **Run Comprehensive Test Suite**
4. **Document Results** and any issues
5. **Report Performance** to development team

### Monitoring Workflow

1. **Schedule Regular Benchmarks** (daily/weekly)
2. **Set Up Alerts** for performance degradation
3. **Review Trends** in weekly reports
4. **Investigate Anomalies** promptly
5. **Update Baselines** as system improves

### Optimization Workflow

1. **Identify Performance Bottlenecks** from benchmark data
2. **Create Targeted Test Scenarios** for optimization
3. **Implement Improvements**
4. **Measure Impact** with before/after benchmarks
5. **Document Optimizations** for future reference

---

*For technical details and advanced configuration, see the [Technical Documentation](../backend/BENCHMARKING_SYSTEM.md).*
