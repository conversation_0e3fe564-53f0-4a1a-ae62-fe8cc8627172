# MODIFIED PARTNERSHIP AGREEMENT FOR GAME OF LIFE PROJECT

## BETWEEN THE UNDERSIGNED:

**Mr. <PERSON>*, of French nationality, residing in Roanne, France, born on [DATE OF BIRTH], 
hereinafter referred to as "<PERSON>",

AND

**Mr. <PERSON>*, of German nationality, residing in [ADDRESS], born on [DATE OF BIRTH],
hereinafter referred to as "<PERSON>",

Both parties collectively referred to as "the Founders" or "the Parties".

HAVE AGREED AS FOLLOWS:

## PREAMBLE: FUNDAMENTAL PRINCIPLES

The Game of Life project ("Goali") is founded upon these non-negotiable principles, which may only be modified by a 90% supermajority vote:

1. **Benevolence & Fairness**: We prioritize genuine user well-being over engagement metrics or short-term gain. Our system must demonstrably improve users' lives through honest, ethical interactions.

2. **Transparency & Self-Agency**: Users maintain control and understanding of how our system works. We reject manipulative or exploitative engagement mechanisms.

3. **Decentralization & Multi-Agent Systems**: We embrace distributed decision-making through specialized entities contributing to governance according to their domains of expertise.

4. **Efficiency & Pragmatism**: We value practical solutions that achieve our mission without unnecessary complexity. We pursue elegant simplicity in both technical and organizational systems.

5. **Capped Personal Wealth**: We commit to directing surplus wealth beyond a mutually-determined threshold toward projects aligned with our ethical principles through our purpose-driven entity.

6. **Service-Oriented Leadership**: We embrace leadership as service rather than control, applying domain expertise to advance our shared mission rather than personal interests.

7. **Psychological Safety**: We maintain an environment where honest feedback is valued, ideas can be shared without fear, and disagreement is addressed with respect rather than hostility.

## 1. ENTITY FORMATION & STRUCTURE

1.1 **Legal Entity**: The parties hereby establish a French SARL (Société à responsabilité limitée) with its principal place of business in France.

1.2 **Name**: The company shall be known as "Goali SARL".

1.3 **Initial Capital**: The initial capital shall be €1, with future capital increases according to the phase implementation timeline.

1.4 **Purpose**: The company's purpose is to develop and commercialize the "Game of Life" application and related technologies while advancing ethical AI governance.

1.5 **Values Foundation**: The company embraces the Fundamental Principles outlined in the Preamble, which shall guide all decision-making and operations.

1.6 **Phase Implementation**: The company shall evolve through four phases as outlined in the Phase Implementation Timeline, with specific triggers for each transition.

## 2. EQUITY ALLOCATION & OWNERSHIP

2.1 **Initial Ownership**: Based on the parties' respective contributions, expertise, and market value of services:
   - Guillaume Berthet: 67.5% ownership
   - Philipp Brandes: 32.5% ownership

2.2 **Vesting Schedule**: Both founders' shares shall be subject to a 4-year vesting schedule with a 1-year cliff, as follows:
   - 25% vests after 1 year from agreement date
   - Remaining 75% vests in equal monthly installments over the following 36 months
   - Full acceleration upon company acquisition or agreed exit event

2.3 **Contribution Valuation Methodology**: Contributions shall be valued according to:
   - Market rate equivalent of time invested (€110K/year for Guillaume's technical work, €45K/year for Philipp's creative/UX work)
   - Intellectual property created and its centrality to the product, including both technical implementations and conceptual frameworks
   - Special skills and expertise provided, including technical architecture, AI systems design, psychological research, strategic development, and user profiling methodology
   - Future value creation potential
   
2.3.1 **Adaptive Contribution Tracking**:
   - Both founders shall maintain weekly time records using a mutually agreed lightweight tracking system
   - Time tracking shall focus on actual value-adding contributions rather than hours in seat
   - Record shall include both quantitative (hours) and qualitative (accomplishment) data
   - Records will be reviewed monthly to ensure mutual awareness of contribution patterns
   - Historical contribution records shall inform future compensation decisions once revenue enables founder salaries
   - Periods of extraordinary contribution shall be documented for future compensation balancing
   - This tracking shall not affect the established equity structure, which reflects founding contributions and domain expertise

2.4 **Capital Increases**: Future capital increases shall be conducted according to the Phase Implementation Timeline, with both founders maintaining pro-rata rights.

## 3. DOMAIN SEPARATION & DECISION AUTHORITY

3.1 **Technical Domain** (Guillaume as Captain):
   - Technical architecture and implementation
   - Data models and database structures
   - AI systems and agent frameworks
   - Infrastructure and deployment
   - Testing frameworks and quality assurance
   - Technical skill development framework

3.2 **Creative and Strategic Domain** (Philipp as Captain):
   - User experience and creative direction
   - Marketing, public relations, and user communications
   - Psychological framework research and implementation
   - System strategy development and refinement
   - User story development and documentation
   - Brand identity and visual language
   - Onboarding experience and trust development
   - User profiling methodology
   - Community building initiatives
   - Creative direction for product evolution

3.3 **Shared Domains** (Joint Decision Making):
   - Core values and principles definition
   - Strategic direction and vision
   - Market positioning and business model
   - Financial planning and resource allocation
   - External partnerships and fundraising
   - Team building and cultural development

3.4 **Domain Authority Protocols**:
   - Each domain captain has primary decision authority within their designated domain
   - Domain captains shall solicit input from other founders before making significant decisions
   - The non-captain may challenge decisions that:
     a) Contradict Fundamental Principles
     b) Significantly impact other domains
     c) Require resources beyond agreed budgets
   - AI governance shall be consulted for domain-crossing decisions and conflicts, with recommendations treated as binding by mutual agreement of the founders even prior to legal formalization

## 4. COMMUNICATION PROTOCOLS

4.1 **Daily Standups**: Daily 10:30 CET meetings with:
   - 5-minute uninterrupted presentation by each founder
   - Discussion of alignment and technical/creative progress
   - Documentation of key decisions and action items

4.2 **Structured Channel System**:
   - Dedicated channels for different communication types
   - Mono-directional channels for uninterrupted expression
   - Bi-directional channels for active discussion
   - Separation of personal from professional communication

4.3 **Feedback Framework**:
   - Direct communication with specific examples
   - Focus on work product rather than personal attributes
   - Balanced perspective acknowledging both strengths and areas for improvement
   - Solutions-oriented discussion about alternatives
   - Opportunity for uninterrupted response
   - Option for written feedback when sensitive topics make verbal communication challenging
   - Dedicated time for expressing concerns in a structured format
   - AI-facilitated periodic team assessments for objective perspective

4.4 **Documentation Requirements**:
   - Key decisions documented in shared repository
   - Meeting summaries with action items
   - Critical path documentation
   - Periodic review of documentation

## 5. TECHNICAL MENTORSHIP FRAMEWORK

5.1 **Knowledge Transfer Protocol**:
   - Weekly dedicated technical sessions (2 hours)
   - Progression from observation to supervised implementation to autonomy
   - Clear objectives and skill development milestones
   - Definition of "why" over prescriptive "how"

5.2 **Skill Development Focus Areas**:
   - Frontend implementation
   - Testing methodologies
   - Version control and development workflow
   - Database design basics
   - AI framework understanding

5.3 **Benchmarking and Evaluation**:
   - Industry standards as benchmarks
   - Measured against time required by confirmed professionals
   - Balance between learning and immediate contribution
   - Progressive responsibility matching growing capabilities

## 6. FINANCIAL ARRANGEMENTS

6.1 **Initial Funding**:
   - Self-funded through founder contributions
   - Philipp commits €3,000 of personal capital to be contributed as soon as he receives the funds from the sale of his boat, or at the time of formal company registration, whichever comes later
   - Guillaume commits equivalent value in technical services

6.2 **Founder Compensation**:
   - No founder compensation until company achieves monthly revenue of €5,000
   - Initial compensation structured as contractors until Phase 2
   - Compensation adjusted to reflect different market values of expertise
   - Compensation increases with company revenue according to agreed framework

6.3 **Resource Allocation**:
   - Technical infrastructure: 40% of available capital
   - Marketing and user acquisition: 30% of available capital
   - Legal and administrative: 10% of available capital
   - Reserve fund: 20% of available capital

6.4 **Personal Wealth Cap**:
   - Maximum annual compensation for founders capped at €150,000 each during Phase 1-3
   - Surplus profits directed to purpose association according to framework
   - Cap revisited at Phase 4 with input from values foundation

## 7. INTELLECTUAL PROPERTY

7.1 **IP Ownership**:
   - All intellectual property created by founders for the project belongs to the company
   - Both founders assign all relevant pre-existing work to the company
   - Company grants founders limited license to use concepts for personal (non-commercial) projects

7.2 **Protected Components**:
   - Core algorithms for challenge calibration and user profiling
   - Multi-agent architecture and coordination mechanisms
   - Database schemas and data relationships
   - Specific implementation of the wheel mechanic
   - Integration patterns between AI services and user-facing components
   - Brand elements and distinctive user experience patterns

7.3 **Documentation Requirements**:
   - Technical documentation maintained by Guillaume
   - UX and psychological framework documentation maintained by Philipp
   - Documentation of AI governance as proprietary IP

## 8. AI GOVERNANCE IMPLEMENTATION

8.1 **Prototype Development**:
   - Implementation as Claude project for MVP
   - Values Constitution developed jointly (50/50 approach)
   - Initial benchmark specifications with evaluation criteria
   - Standardized decision request format

8.2 **Decision Domains**:
   - Value alignment verification
   - Dispute resolution assistance (treated as binding by mutual agreement even prior to legal formalization)
   - Ethics evaluation for features and marketing
   - User privacy and data usage policies
   - Resource allocation recommendations

8.3 **Evolution Framework**:
   - Phase 1: Advisor with recommendations treated as binding by mutual agreement of the founders for conflict resolution
   - Phase 2: Formal decision tracking with partial authority
   - Phase 3: Official advisory board position
   - Phase 4: Full AI governance with binding authority in specific domains

## 9. DISPUTE RESOLUTION PROTOCOL

9.1 **Tiered Resolution Process**:
   1. Structured debate with formal presentation of alternatives
   2. Cooling-off period (48-72 hours) for reflection
   3. Second-round discussion with focus on identifying common ground
   4. Consultation with AI governance system (with recommendations treated as binding by mutual agreement)
   5. Consultation with trusted neutral advisor if still unresolved
   6. Formal mediation if necessary
   7. Decision authority defaulting to domain-appropriate founder

9.2 **Domain-Specific Resolution**:
   - Technical disputes: Guillaume has final decision authority after considering Philipp's input
   - Creative/UX disputes: Philipp has final decision authority after considering Guillaume's input
   - Strategy disputes: Requires mutual agreement or AI governance recommendation

9.3 **Emergency Decision Protocol**:
   - For time-sensitive decisions requiring immediate action
   - Domain captain makes provisional decision
   - Documents rationale and communicates immediately
   - Subject to review within 48 hours

9.4 **Deadlock-Breaking Mechanism**:
   - If no resolution through previous steps, founders agree to:
     a) Implement AI governance recommendation, or
     b) Flip a coin to determine which proposal to implement (limited to non-critical decisions)

9.5 **Enhanced Psychological Safety Measures**:
   - Option for written feedback when verbal communication is challenging
   - Dedicated time for expressing concerns in a structured format
   - AI-facilitated periodic team assessments for objective perspective
   - Structured communication protocols for addressing sensitive topics
   - Documentation of communication preferences to improve understanding

## 10. EXIT FRAMEWORK

10.1 **Voluntary Departure Protocol**:
   - 60 days minimum notice for significant reduction in involvement
   - 90 days notice for complete withdrawal
   - Written transition plan with knowledge transfer requirements
   - Continued consulting availability for 6 months (10 hours/month maximum)

10.2 **Share Valuation Methodology**:
   - Fair market value determined by independent valuation
   - If no external valuation available, formula based on:
     a) 2x trailing twelve months revenue, or
     b) 5x EBITDA
     whichever is greater
   - Adjustment for development stage and founder contributions

10.3 **Buyout Terms**:
   - Remaining founder(s) have right of first refusal
   - Payment schedule: 20% upfront, remainder over 24 months
   - Acceleration option with 15% discount for full cash payment

10.4 **Non-Compete Provisions**:
   - Guillaume: 18-month non-compete for AI coaching apps or multi-agent systems
   - Philipp: 12-month non-compete for randomized activity apps
   - Geographic limitation: EU markets only
   - Exclusion for open-source or academic contributions
   - Different standards for voluntary vs. involuntary departure

10.5 **Specific IP Protections**:
   - Permanent protection of trade secrets and algorithms
   - 24-month prohibition on using proprietary design patterns
   - Different restrictions based on reason for departure

## 11. AMENDMENT PROCESS

11.1 **Regular Review Schedule**:
   - Initial 90-day review after formal agreement
   - Scheduled quarterly reviews during Phase 1
   - Semi-annual reviews during Phase 2
   - Annual reviews thereafter
   - Event-triggered reviews for significant milestones

11.2 **Amendment Requirements**:
   - Fundamental Principles require 90% supermajority
   - Operating procedures require simple majority
   - Domain authority changes require mutual agreement
   - All amendments must be documented in writing

11.3 **AI Governance Role**:
   - AI governance system consulted on all proposed amendments
   - Assessment of alignment with Fundamental Principles
   - Non-binding in Phase 1, advisory in Phase 2-3, integrated authority in Phase 4

Done in Roanne, France on April 10th, 2025 in two (2) original copies.

____________________           ____________________
Guillaume Berthet               Philipp Brandes