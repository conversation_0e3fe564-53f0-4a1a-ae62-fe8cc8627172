# Fixing Pytest Collection Errors Related to Django/Channels Mocking

## Problem Description

Running `pytest -v` resulted in an `INTERNALERROR` during the test collection phase, specifically:

```
INTERNALERROR> ModuleNotFoundError: No module named 'django.core.handlers'; 'django.core' is not a package
```

This error occurred before any tests were actually run, indicating an issue with pytest's setup or module discovery process, particularly related to how `pytest-django` initializes the Django test environment. The error suggested that Python's import system was encountering a module named `django.core` that wasn't the expected Django package directory, likely due to a namespace conflict or improper mocking. A similar conflict involving the `channels` package and `langgraph.channels` was also suspected.

## Investigation Steps

1.  **Checked for Naming Conflicts:** Searched the project for files or directories named `django.core` or `channels` that might shadow the installed packages. None were found in relevant locations.
2.  **Reviewed Pytest Configuration (`pyproject.toml`):** Confirmed standard configuration, including `pythonpath = ["./backend"]`.
3.  **Examined `conftest.py` and `__init__.py` Files:** Checked various test configuration files (`backend/conftest.py`, `backend/apps/main/tests/conftest.py`, `backend/apps/main/tests/__init__.py`, etc.) for manual `sys.path` manipulations or problematic import-time code. Removed several instances of manual `sys.path.insert(0, ...)` which can interfere with pytest's path handling.
4.  **Analyzed Import Timing:** Investigated potential issues where modules importing `langgraph.channels` (like Celery tasks or graph definitions) might be loaded before `pytest-django` initialized the environment for the `channels` app. Delayed imports in `agent_tasks.py` as a precaution.
5.  **Isolated Mocking Environment:** Focused on `backend/apps/main/tests/test_agents/test_env.py`, which contained extensive logic to mock a Django environment for isolated agent unit tests. This file was imported and its setup function was initially called automatically via `test_agents/conftest.py`.

## Root Cause Identification

The investigation revealed that the direct manipulation of `sys.modules` within `backend/apps/main/tests/test_agents/test_env.py` was the root cause:

```python
# In setup_django_mocks() within test_env.py:
sys.modules['django.core'] = django_core # Problematic Line
sys.modules['django.core.exceptions'] = django_core.exceptions # Problematic Line

# In create_app_models() within test_env.py:
sys.modules['channels'] = MagicMock() # Problematic Line
sys.modules['channels.layers'] = MagicMock() # Problematic Line
```

Replacing core modules like `django.core` and `channels` globally in `sys.modules` fundamentally conflicted with how `pytest-django` sets up the test environment during its collection phase. Even when the execution of the setup function (`setup_agent_test_env`) was moved into a fixture, the mere presence and potential inspection of this mocking code during collection seemed to trigger the error. `pytest-django` requires the real modules to be present during its initial setup.

## Solution Implemented

1.  **Removed `sys.path` Manipulation:** Ensured all manual modifications to `sys.path` were removed from test configuration files (`__init__.py`, `conftest.py`), relying solely on `pyproject.toml`'s `pythonpath` setting.
2.  **Commented Out Global Mocks:** The problematic lines manipulating `sys.modules` for `django.core` and `channels` within `backend/apps/main/tests/test_agents/test_env.py` were commented out.
3.  **Scoped Mock Environment Setup:** The `conftest.py` file within `backend/apps/main/tests/test_agents/` was modified to use an `autouse=True` fixture (`agent_test_environment_fixture`) that explicitly calls the `setup_agent_test_env()` function (from `test_env.py`) before each test in that directory and calls a reset function afterwards. This ensures the remaining (non-problematic) mocks in `test_env.py` are applied only when needed for those specific tests.
4.  **Corrected Mock Attribute:** Added `django_db.DEFAULT_DB_ALIAS = 'default'` to the `django_db` mock in `test_env.py` to resolve subsequent `ImproperlyConfigured` errors during test execution setup for tests requiring database access.

This approach resolved the test collection error by preventing the global `sys.modules` override while still allowing the agent unit tests to utilize their necessary (partial) mock environment via the fixture.

## Required Cleanup (Technical Debt)

The agent unit tests in `backend/apps/main/tests/test_agents/` now likely require refactoring. Since the global mocking of `django.core` and `channels` was removed from `test_env.py`, these tests need to implement mocking for these components locally using standard techniques:

-   **`unittest.mock.patch`:** Use as a decorator or context manager to mock specific objects (like `django.core.exceptions` or `channels.layers.get_channel_layer`) within the scope of a test function or class.
-   **`pytest.MonkeyPatch`:** Use the `monkeypatch` fixture provided by pytest to modify classes, methods, or dictionaries temporarily for a test.

This refactoring will make the tests more robust, easier to understand, and less prone to causing conflicts with the overall pytest environment setup. Refer to `TASK.md` for the specific task tracking this refactoring effort.
