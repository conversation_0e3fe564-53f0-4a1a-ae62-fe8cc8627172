# Game of Life User Model

This document defines the User Model for the Game of Life. The model is organized into several packages:
- **User & Resources:** Core user identification and their physical or conceptual environments and resources.
- **User Psyche Profile:** Details capturing demographics, goals, beliefs, traits, and moods.
- **History Log:** Records events and feedback related to user interactions.
- **TemporalRecord:** An abstract structure to standardize time-bound entities.

Below, each entity is described along with its documented fields.

---

### User
*A player in "The Game", capturing the core identification details required for linking to psychological profiles, resource inventories, and event logs.*

| Field              | Type   | Description                                                |
|--------------------|--------|------------------------------------------------------------|
| userName           | String | The user's display or login name.                        |
| email              | Email  | Contact email address.                                     |
| currentEnvironment | UUID   | Identifier for the current environment of the user.      |

---

### UserEnvironment
*Models a physical or conceptual environment for the user. Inherits temporal properties from TemporalRecord.*

| Field                  | Type   | Description                                               |
|------------------------|--------|-----------------------------------------------------------|
| environmentId          | UUID   | Unique identifier for the environment.                  |
| environmentName        | String | Name of the environment (e.g., "Home", "Vacation").       |
| environmentDescription | String | Description of the environment.                           |
| environmentDetails     | JSON   | Additional details and metadata about the environment.    |
| effectiveStart         | Date   | (Inherited) Start date when the record becomes active.    |
| durationEstimate       | String | (Inherited) Estimated active period (e.g., "30 days").      |
| effectiveEnd           | Date   | (Inherited) End date when the record ceases to be active.   |


---

### Inventory
*Represents a snapshot of the user’s available resources for a specific location or time period. Contextualizes items for challenges and activities.*

| Field       | Type   | Description                                                          |
|-------------|--------|----------------------------------------------------------------------|
| environmentId | UUID | Identifier linking the inventory to a specific UserEnvironment.     |
| validUntil  | Date   | Date until which the inventory is valid.                           |
| notes       | String | Additional remarks (e.g., "need to retrieve the key").               |

*Relationships:*
- Linked to exactly one UserEnvironment
- Contains zero or more PersonalResource items

---

### GenericResource
*Defines baseline properties for any resource used in the game, providing a standardized structure.*

| Field        | Type    | Description                                                                         |
|--------------|---------|-------------------------------------------------------------------------------------|
| resourceType | String  | Category of the resource (e.g., "sport equipment", "cinema").                       |
| description  | String  | A brief explanation (e.g., "A mountain bike").                                      |
| opCost       | Integer | Generic evaluation of the operational cost (scale 0-100).                           |
| acqCost      | Integer | Generic evaluation of the acquisition cost (scale 0-100).                           |

*Relationships:*
- Extended by PersonalResource

---

### PersonalResource
*A specific resource a user has access to. Extends GenericResource by adding personalized attributes such as a unique name, location details, and ownership information.*

| Field            | Type   | Description                                                                                          |
|------------------|--------|------------------------------------------------------------------------------------------------------|
| specificName     | String | The unique name or label for the resource (e.g., "My Mountain Bike").                                |
| environmentId    | UUID   | Identifier linking the resource to a specific UserEnvironment.                                      |
| locationDetails  | String | Extra information to help locate the resource (e.g., "in the garage, top-left shelf").                |
| ownershipDetails | String | Details regarding ownership or usage (e.g., "user is cautious due to past damage").                  |
| contactInfo      | String | Contact information for resource usage requests (e.g., "call Roger at +33645785214").                 |
| notes            | String | Additional remarks (e.g., "Needs tire replacement").                                                |

*Relationships:*
- Contained within Inventory
- Extends GenericResource

*Note:* **PersonalResource** extends **GenericResource** to inherit standardized properties.

---

### GenericSkill
*Generic entity for skills; specific skills link to this base.*

| Field       | Type   | Description                                |
|-------------|--------|--------------------------------------------|
| id          | UUID   | Unique identifier for the generic skill.      |
| description | String | Brief description of the generic skill.       |

---

### Skill
*Represents a specific learned ability.*

| Field         | Type   | Description                                                                       |
|---------------|--------|-----------------------------------------------------------------------------------|
| description   | String | Brief description of the skill (e.g., "Storytelling").                            |
| level         | Integer| Mastery or proficiency level (scale 0-100).                                         |
| userAwareness | Integer| Indicates how much the user recognizes this skill (scale 0-100).                    |
| userEnjoyment | Integer| Indicates the enjoyment level when using this skill (scale 0-100).                  |
| note          | String | Additional details (e.g., "exhausting after 10min").                              |

*Relationships:*
- Linked to User
- Extends GenericSkill

---

### GenericUserLimitation
*Defines the common properties for user limitations.*

| Field           | Type   | Description                                         |
|-----------------|--------|-----------------------------------------------------|
| id              | UUID   | Unique identifier for the generic limitation.         |
| description     | String | Description of the limitation.                     |
| limitationType  | String | Type/category of the limitation (e.g., "PHYSICAL").  |
---

### UserLimitation
*Represents constraints or challenges the user may face, helping the system adjust difficulty or offer supportive interventions.*

| Field          | Type    | Description                                                                                                  |
|----------------|---------|--------------------------------------------------------------------------------------------------------------|
| description    | String  | Description of the limitation (e.g., "broken leg").                                                          |
| severity       | Integer | Measure of the limitation's impact (scale 0-100).                                                             |
| validUntil     | Date    | Expiry or review date for the limitation.                                                                    |
| isUnlimited    | Boolean | Flag indicating if the limitation is absolute.                                                               |
| limitationType | String  | Domain of the limitation (ENUM: "COGNITIVE", "PHYSICAL", "SOCIAL", "EMOTIONAL", "CREATIVE", "SPIRITUAL", "TACTICAL"). |
| userAwareness  | Integer | How much the user is conscious of this limitation (scale 0-100).                                               |
| effectiveStart | Date    | Start date when the record becomes active.                                                                 |
| durationEstimate| String | Rough estimation of the record's active period (e.g., "30 days").                                          |
| effectiveEnd   | Date    | End date when the record ceases to be active.                                                              |

*Relationships:*
- Linked to User
- Extends GenericUserLimitation
- Extends TemporalRecord

---

### Demographics
*Captures the basic demographic details of the user.*

| Field             | Type    | Description                                   |
|-------------------|---------|-----------------------------------------------|
| fullName          | String  | User's full name.                             |
| age               | Integer | User's age.                                   |
| gender            | String  | User's gender.                                |
| location          | String  | Geographic location or residence.           |
| language          | String  | Preferred or native language.               |
| occupation        | String  | User's occupation.                            |
| personalPrefsJSON | JSON    | JSON containing personal preferences.       |


*Relationships:*
- Linked to User

---

### UserTraitInclination
*Represents individual trait inclinations of the user.*

| Field    | Type    | Description                                       |
|----------|---------|---------------------------------------------------|
| user_id  | Integer | Identifier linking to the user.                   |
| trait_id | Integer | Identifier linking to a specific trait.           |
| strength | Float   | Strength of the inclination.                      |
| awareness| Integer | Degree of user awareness of this inclination (0-100).|


*Relationships:*
- Linked to User
- Linked to Trait

---

### UserGoal
*Serves as the base entity for all user-defined goals, aspirations, and intentions that drive personalized content and adaptive interventions.*

| Field                      | Type     | Description                                                              |
|----------------------------|----------|--------------------------------------------------------------------------|
| user_id                    | Integer  | Identifier linking the goal to a specific user.                          |
| title                      | String   | A short, descriptive title for the goal (e.g., "Improve Public Speaking"). |
| description                | Text     | Detailed explanation of the goal.                                        |
| importance_according_user  | Integer  | Importance of the goal as perceived by the user (scale 0-100).             |
| importance_according_system| Integer  | System's evaluation of the goal's importance (scale 0-100).                |
| strength                   | Integer  | Current momentum or drive behind the goal (scale 0-100).                   |
| created_at                 | Datetime| Timestamp when the goal was created.                                     |
| updated_at                 | Datetime| Timestamp when the goal was last updated.                                |

*Relationships:*
- Extended by Intention and Aspiration
- Related to Inspiration through GoalInspiration

---

### Intention
*An actionable, time-bound goal that extends UserGoal for short-term behavioral change.*

| Field         | Type     | Description                                                              |
|---------------|----------|--------------------------------------------------------------------------|
| start_date    | Date     | Date when the intention starts.                                          |
| due_date      | Date     | Target completion date.                                                  |
| is_completed  | Boolean  | Flag indicating whether the intention has been fulfilled.                |
| progress_notes| Text     | Ongoing notes or reflections on progress.                                |

*Relationships:*
- Extends UserGoal

*Note:* **Intention** inherits all fields from **UserGoal**.

---

### Aspiration
*Captures long-term, visionary goals reflecting the user’s broader ambitions. Extends UserGoal.*

| Field             | Type   | Description                                                       |
|-------------------|--------|-------------------------------------------------------------------|
| domain            | String | The field or area of the aspiration (e.g., "Career").             |
| horizon           | String | Time frame of the aspiration (e.g., "Long-term").                  |
| level_of_ambition | String | Descriptor indicating the intensity of the ambition (e.g., "High"). |

*Relationships:*
- Extends UserGoal

*Note:* **Aspiration** inherits all fields from **UserGoal**.

---

### Inspiration
*Records external or internal influences that motivate the user’s goals.*

| Field          | Type     | Description                                                                                   |
|----------------|----------|-----------------------------------------------------------------------------------------------|
| user_id        | Integer  | Identifier linking the inspiration to a specific user.                                         |
| source         | String   | Origin of the inspiration (e.g., "TED Talk").                                                  |
| description    | Text     | Detailed description of the inspirational content.                                             |
| strength       | Integer  | A rating of the inspirational impact (scale 0-100).                                              |
| reference_url  | URL      | URL pointing to the source material.                                                           |
| created_at     | Datetime | Timestamp when the inspiration was recorded.                                                   |
| updated_at     | Datetime | Timestamp when the inspiration was last updated.                                               |

*Relationships:*
- Related to UserGoal through GoalInspiration

---

### GoalInspiration
*Establishes a relationship between a goal and its sources of inspiration, highlighting external influences.*

| Field          | Type | Description                                                                  |
|----------------|------|------------------------------------------------------------------------------|
| goal_id        | UUID | Identifier for the associated goal.                                          |
| inspiration_id | UUID | Identifier for the linked inspiration.                                       |
| note           | Text | Commentary on how the inspiration relates to the goal.                     |

*Relationships:*
- Links UserGoal and Inspiration

*Relationships:*  
- **UserGoal** and **Inspiration** are linked through **GoalInspiration**.

---

### TrustLevel
*Quantifies the level of trust a user has in the system across different archetypal domains, guiding challenge selection and pacing of interventions.*

| Field         | Type   | Description                                                                          |
|---------------|--------|--------------------------------------------------------------------------------------|
| value         | Integer| A score from 0 to 100 indicating the trust level.                                    |
| aggregateType     | String | Name of the aggregate (e.g., Skill).                                      |
| aggregateId   | String | Identifier for the aggregate.                                                 |
| notes         | String | Additional context or commentary about the trust measurement.                        |

---

### Preference
*Reflects individual likes, habits, or inclinations that influence the delivery of personalized content.*

| Field           | Type   | Description                                                                               |
|-----------------|--------|-------------------------------------------------------------------------------------------|
| prefName        | String | The name of the preference (e.g., "Morning Workouts").                                      |
| prefDescription | String | Detailed description (e.g., "User prefers exercising in the early morning.").               |
| prefStrength    | Integer| A rating indicating the importance of the preference (scale 0-100).                          |
| userAwareness   | Integer| Indicates if the user is consciously aware of this preference (scale 0-100).                 |

---

### Belief
*Encapsulates a cognitive proposition held by the user. Beliefs are dynamically updated based on supporting or contradicting evidence and can influence goals, capabilities, and limitations.*

| Field             | Type                        | Description                                                                                   |
|-------------------|-----------------------------|-----------------------------------------------------------------------------------------------|
| content           | String                      | Core statement or proposition in natural language.   |
| lastUpdated       | Date                        | Timestamp of the most recent update or evaluation.           |
| userConfidence    | Integer                     | Quantitative measure of the user's personal conviction (scale 0-100).|
| systemConfidence  | Integer                     | System’s evaluation of the belief's validity (scale 0-100).|
| emotionality      | Integer                     | Emotional charge on a signed scale (-100 to +100).|
| stability         | Integer                     | Resistance to change (scale 0-100); higher values indicate more stability.|
| userAwareness     | Integer                     | Degree of conscious recognition of the belief (scale 0-100).|

---

### BeliefEvidence
*Represents individual data points or observations that support or contradict a belief. Each piece is evaluated for credibility.*

| Field           | Type                | Description                                                                                  |
|-----------------|---------------------|----------------------------------------------------------------------------------------------|
| type            | BeliefEvidenceType  | Category of evidence (e.g., EXPERIENCE, AUTHORITY, SOCIAL_CONSENSUS, INTUITION).              |
| description     | String              | Detailed description, including context.                                                     |
| credibilityScore| Float               | Quantitative measure of the reliability or trustworthiness of the evidence.                  |
| source          | String              | Origin of the evidence (e.g., empirical data, expert opinion).                               |

---

### BeliefInfluence
*Represents the influence exerted by a belief on a target entity (such as goals, capabilities, skills, or limitations).*

| Field               | Type   | Description                                                                          |
|---------------------|--------|--------------------------------------------------------------------------------------|
| belief_id           | UUID   | Identifier of the influencing belief.                                                |
| target_entity_id    | UUID   | Identifier of the target entity affected by the belief.                             |
| target_entity_type  | String | Type of the target entity (e.g., Goal, Capability, Skill, UserLimitation).           |
| influence_strength  | Integer| Strength of the influence (scale -100 to +100).                                      |
| note                | String | Additional remarks regarding the influence.                                        |

---

### CurrentMood
*Tracks the user’s immediate emotional state for real-time adaptive activity selection and interventions.*

| Field         | Type     | Description                                                                          |
|---------------|----------|--------------------------------------------------------------------------------------|
| description   | String   | Detailed description of the current mood (e.g., "feeling elated after sunshine").       |
| height        | Integer  | Intensity of the mood on a scale from 0 (low) to 100 (high).                           |
| userAwareness | Integer  | Degree to which the user is aware of their current mood (scale 0-100).                   |
| processed_at  | Datetime | Timestamp when the mood was processed or recorded.                                   |

---

### Preference
*Represents user likes or habits influencing content delivery. Inherits temporal attributes from TemporalRecord.*

| Field           | Type    | Description                                                   |
|-----------------|---------|---------------------------------------------------------------|
| prefName        | String  | Name of the preference (e.g., "Morning Workouts").            |
| prefDescription | String  | Detailed description of the preference.                     |
| prefStrength    | Integer | Importance rating of the preference (0-100).                  |
| userAwareness   | Integer | User’s awareness of this preference (0-100).                  |
| effectiveStart  | Date    | (Inherited) Start date of the preference record.              |
| durationEstimate| String  | (Inherited) Estimated duration (e.g., "30 days").             |
| effectiveEnd    | Date    | (Inherited) End date of the preference record.                |


---

### TemporalRecord (Abstract)
*Provides a common structure for time-bound elements, ensuring each record has an effective time span.*

| Field           | Type   | Description                                                            |
|-----------------|--------|------------------------------------------------------------------------|
| effectiveStart  | Date   | Start date when the record becomes active.                             |
| durationEstimate| String | Rough estimation of the record's active period (e.g., "30 days").        |
| effectiveEnd    | Date   | End date when the record ceases to be active.                          |

*Note:* Several entities (e.g., UserEnvironment, CurrentMood, Preference, Intention, Aspiration, UserLimitation, Capability) extend **TemporalRecord**.

---
### Catalog Entities

#### GenericTrait
*Provides metadata for traits referenced in user inclinations.*

| Field      | Type    | Description                                           |
|------------|---------|-------------------------------------------------------|
| id         | UUID    | Unique identifier for the trait record.             |
| code       | String  | Unique code for the trait (max 50 characters).      |
| name       | String  | Name of the trait.                                    |
| description| String  | Detailed description of the trait.                  |
| createdAt  | Timestamp | Record creation timestamp.                        |
| updatedAt  | Timestamp | Last update timestamp.                            |

---

#### GenericEnvironment 
*Catalog of defined environments available for user selection.*

| Field      | Type    | Description                                          |
|------------|---------|------------------------------------------------------|
| id         | UUID    | Unique identifier for the generic environment entry.|
| code       | String  | Unique code for the environment (max 50 characters).|
| name       | String  | Name of the environment.                             |
| description| String  | Description of the environment.                      |
| isActive   | Boolean | Flag indicating if the environment is active.        |
| createdAt  | Timestamp | Creation timestamp.                              |
| updatedAt  | Timestamp | Last update timestamp.                           |

---



### History Log Entities

#### UserFeedbackLog
*Records feedback provided by the user, capturing the type, content, and evaluative scores.*

| Field           | Type    | Description                                             |
|-----------------|---------|---------------------------------------------------------|
| feedbackType    | String  | Type or category of the feedback.                     |
| feedbackContent | String  | The content of the feedback.                           |
| positiveness    | Integer | Score representing positive sentiment (scale 0-100).   |
| criticality     | Integer | Score representing criticality (scale 0-100).          |

#### UserPsyAgentLog
*Logs actions and feedback from the user psychology agent for interventions and analysis.*

| Field           | Type    | Description                                             |
|-----------------|---------|---------------------------------------------------------|
| actionType      | String  | Type of action performed by the psychology agent.     |
| feedbackContent | String  | Content of the feedback associated with the action.   |
| positiveness    | Integer | Positiveness score of the feedback (scale 0-100).       |
| criticality     | Integer | Criticality score of the feedback (scale 0-100).        |

#### UserLog
*Records general user events for historical tracking and analysis.*

| Field    | Type   | Description                                      |
|----------|--------|--------------------------------------------------|
| userId   | UUID   | Identifier for the user associated with the event. |
| eventType| String | Type of event that occurred.                      |
| details  | String | Detailed description of the event.               |

---

### Relationships Summary

- **User** defines one or more **UserEnvironment** entries and has many associated resources (Inventory, PersonalResource, Capability, Skill, and UserLimitation).  
- **Inventory** is linked to **UserEnvironment** and contains multiple **PersonalResource** items.  
- In the **User Psyche Profile**, **UserGoal** (and its subtypes **Intention** and **Aspiration**) relate to **Inspiration** via **GoalInspiration**.  
- **Belief** entities influence other entities through **BeliefInfluence**.  
- **Trait** and **UserInclination** establish the basis for personalized challenge calibration.  
- Several entities extend **TemporalRecord** to include effective time spans.

---