class CharacterTraitCategory {
}

class Openness {
    +imagination: Float
    +artisticInterests: Float
    +emotionality: Float
    +adventurousness: Float
    +intellect: Float
    +liberalism: Float
}

class Conscientiousness {
    +selfEfficacy: Float
    +orderliness: Float
    +dutifulness: Float
    +achievementStriving: Float
    +selfDiscipline: Float
    +cautiousness: Float
}

class Extraversion {
    +friendliness: Float
    +gregariousness: Float
    +assertiveness: Float
    +activityLevel: Float
    +excitementSeeking: Float
    +cheerfulness: Float
}

class Agreeableness {
    +trust: Float
    +morality: Float
    +altruism: Float
    +cooperation: Float
    +modesty: Float
    +sympathy: Float
}

class Neuroticism {
    +anxiety: Float
    +anger: Float
    +depression: Float
    +selfConsciousness: Float
    +immoderation: Float
    +vulnerability: Float
}

CharacterTraitCategory --|> Openness
CharacterTraitCategory --|> Conscientiousness
CharacterTraitCategory --|> Extraversion
CharacterTraitCategory --|> Agreeableness
CharacterTraitCategory --|> Neuroticism