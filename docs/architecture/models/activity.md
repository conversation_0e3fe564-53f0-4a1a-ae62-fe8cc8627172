### GenericActivity
*Represents a generic activity available in the catalog.*

| Field      | Type   | Description                                                      |
|------------|--------|------------------------------------------------------------------|
| id         | uuid   | Unique identifier for the generic activity. (Previously noted as genericActivityId) |
| name       | string | The name of the activity. Example: "Yoga"                        |
| description| string | A brief summary of what the activity entails. Example: "A relaxing practice." |
| createdOn  | Date   | Date the activity was created.                                   |
| durationRange | string | Generic time range for completing the activity. Example: "30-45 minutes" |
| instructions  | string | Directions to perform the activity. Example: "Lay out your mat in a quiet space." |
| physicalRequirements | json | JSON listing physical requirements. Example: `{"strength":70,"endurance":80}` |
| socialRequirements   | json | JSON listing social requirements. Example: `{"min_participant":2,"max_participant":4}` |
| domain     | json   | JSON detailing the activity category. Example: `{"type":"physical"}` |


#### Relationships
| Relationship | Description |
|--------------|-------------|
| `synergyActivities` | Many-to-many relationship with other GenericActivity entities indicating complementary activities |
| `GenericActivityResourceRequirement` | Many-to-many relationship with resource requirements |
| `GenericUserLimitation` | Many-to-many relationship indicating incompatibilities with certain limitations |
| `GenericSkill` | Many-to-many relationship indicating required skills |

---

### ActivityTailored
*Represents a customized activity for a specific user, based on a generic activity.*

| Field      | Type   | Description                                                       |
|------------|--------|-------------------------------------------------------------------|
| id         | uuid   | Unique identifier for the tailored activity. |
| userId     | uuid   | Identifier of the user for whom the activity is tailored.         |
| name       | string | The name of the tailored activity. Example: "Morning Yoga"          |
| description| string | A summary of the tailored activity.                               |
| createdOn  | Date   | Creation date.                                                    |
| durationRange | string | Expected time range for completion. Example: "30-45 minutes"      |
| instructions  | string | Specific directions for the tailored activity.                  |
| baseChallengeRating | int | Base difficulty rating of the activity.                    |
| physicalRequirements | json | JSON listing physical requirements.                        |
| socialRequirements   | json | JSON listing social requirements.                          |
| challengingness      | json | JSON describing challenge aspects.                         |
| domain     | json   | JSON detailing the activity category.                           |
| version    | int    | Version number of the tailored activity.                         |
| tailorizationLevel | int | Degree of customization relative to the generic activity.         |


#### Relationships
| Relationship | Description |
|--------------|-------------|
| `GenericActivity` | Links to the generic activity this is tailored from |
| `ActivityInfluencedBy` | Many-to-one relationship with influencing entities (goals, inspirations, etc) |
| `ActivityUserRequirement` | Many-to-many relationship with required skills, traits, and limitations |
| `ActivityTailoredResourceRequirement` | Many-to-many relationship with custom resource requirements |

---

### GenericActivityResourceRequirement
Associates an GenericActivity with the necessary resource requirements.

#### Fields
| Field             | Type   | Description |
|-------------------|--------|-------------|
| `genericActivityId`  | `UUID` | Identifier linking to the generic activity. Example: "act-base-001" |
| `resourceBaseId`  | `UUID` | Identifier for the required resource. Example: "res-101" |
| `quantityRequired`| `int`  | Amount of the resource needed. Example: 2 |
| `optional`        | `boolean` | Specifies if the resource is optional. Example: false |

#### Relationships
| Relationship | Description |
|--------------|-------------|
| `GenericActivity` | Many-to-one relationship with the generic activity |
| `GenericResource` | Many-to-one relationship with the required resource |

---

### GenericActivityTraitRequirement
*Associates an GenericActivity with required trait levels.*

| Field         | Type   | Description                                     |
|---------------|--------|-------------------------------------------------|
| genericActivityId| UUID   | Identifier of the generic activity.                |
| traitId       | UUID   | Identifier of the required trait.               |
| levelRequired | int    | Minimum trait level required.                   |
| optional      | boolean| Indicates if the trait requirement is optional. |

---

### GenericActivityEnvRequirement
*Defines environmental requirements for an GenericActivity.*

| Field         | Type   | Description                                     |
|---------------|--------|-------------------------------------------------|
| genericActivityId| UUID   | Identifier of the generic activity.                |
| traitId       | UUID   | (As defined in the model) Identifier used to match environment-related traits. |
| optional      | boolean| Indicates if the environment requirement is optional. |


---

### ActivityTailoredResourceRequirement
*Captures resource requirements specific to a tailored activity.*

| Field             | Type   | Description                                                      |
|-------------------|--------|------------------------------------------------------------------|
| id                | uuid   | Unique identifier for this requirement record.                 |
| activityTailoredId| UUID   | Identifier of the associated tailored activity.                  |
| personalResourceId| UUID   | Identifier for the required personal resource.                   |
| quantityRequired  | int    | Required quantity of the resource.                              |
| optional          | boolean| Flag indicating if the resource is optional.                     |


#### Relationships
| Relationship | Description |
|--------------|-------------|
| `ActivityTailored` | Many-to-one relationship with the tailored activity |
| `PersonalResource` | Many-to-one relationship with the required resource |

---

### Wheel
*Represents the spinning mechanism for activity selection.*

| Field    | Type   | Description                                               |
|----------|--------|-----------------------------------------------------------|
| id       | string | Unique identifier for the wheel. (Previously noted as wheelId) |
| name     | string | The name of the wheel. Example: "Daily Challenge Wheel".  |
| createdBy| string | Identifier for the creator.                               |
| createdAt| Date   | Timestamp when the wheel was created.                     |

#### Relationships
| Relationship | Description |
|--------------|-------------|
| `WheelItem` | One-to-many relationship with wheel items |

---

### WheelItem
*Represents a segment within the wheel corresponding to a tailored activity.*

| Field    | Type   | Description                                               |
|----------|--------|-----------------------------------------------------------|
| id       | string | Unique identifier for the wheel item. (Previously noted as itemId) |
| percentage| float | The probability weight (0.0–100.0) for selecting this item. |


#### Relationships
| Relationship | Description |
|--------------|-------------|
| `Wheel` | Many-to-one relationship with the parent wheel |
| `GenericActivity` | Many-to-one relationship with the generic activity |

---

### ActivityInfluencedBy
Represents the influence of various entities (goals, inspirations, etc.) on tailored activities.

#### Fields
| Field                  | Type   | Description |
|------------------------|--------|-------------|
| `id`                   | `uuid` | Unique identifier for the influence record |
| `activity_tailored_id` | `uuid` | Identifier of the tailored activity being influenced |
| `influencing_entity_id`| `uuid` | Identifier of the influencing entity (goal, inspiration, etc.) |
| `influencing_entity_type`| `string` | Type of the influencing entity (e.g., "Goal", "Inspiration") |
| `influence_strength`   | `int`  | Strength of influence (0-100) |
| `note`                 | `string` | Optional notes about the influence |

#### Relationships
| Relationship | Description |
|--------------|-------------|
| `ActivityTailored` | Many-to-one relationship with the influenced activity |

### ActivityUserTraitRequirement
*Connects tailored activities with required user traits, skills, and limitations.*

| Field           | Type   | Description                                                  |
|-----------------|--------|--------------------------------------------------------------|
| id              | uuid   | Unique identifier for the requirement record.              |
| resource_type   | string | Type of resource (e.g., "Skill", "Trait", "UserLimitation"). |
| resource_id     | uuid   | Identifier of the specific resource required.              |
| required_level  | int    | Required proficiency level (e.g., scale -100 to +100).       |
| quantityRequired| int    | Quantity needed of the resource.                           |
| optional        | boolean| Whether the requirement is optional.                      |


#### Relationships
| Relationship | Description |
|--------------|-------------|
| `ActivityTailored` | Many-to-one relationship with the tailored activity |
| `Skill` | Many-to-one relationship with required skills |
| `Trait` | Many-to-one relationship with required traits |
| `UserLimitation` | Many-to-one relationship with user limitations |

### ActivityTailoredLog
Logs events related to tailored activities, such as creation, updates, and deletions.

#### Fields
| Field             | Type   | Description |
|-------------------|--------|-------------|
| `activityTailoredId`| `string` | Identifier of the tailored activity. Example: "act-tail-001" |
| `activityVersion` | `string` | The version of the activity at the time of the event. Example: "v2" |
| `eventType`       | `string` | Type of event ("Created", "Updated", "Deleted"). Example: "Created" |
| `userId`          | `string` | Identifier of the user who triggered the event. Example: "user-123" |
| `reason`          | `string` | Optional explanation for the event (used for "Updated" and "Deleted"). Example: "User requested modification" |

---

### WheelLog
Logs events related to the wheel, capturing changes in configuration or usage.

#### Fields
| Field       | Type   | Description |
|-------------|--------|-------------|
| `wheelId`   | `string` | Unique identifier for the wheel. Example: "wheel-01" |
| `wheelVersion`| `string` | Version identifier for the wheel. Example: "v1" |
| `eventType` | `string` | Describes the event type ("Created", "Updated", "Deleted"). Example: "Updated" |
| `userId`    | `string` | Identifier for the user who made the change. Example: "admin" |
| `reason`    | `string` | Optional explanation for the event. Example: "Adjusted probabilities for fairness" |

---
