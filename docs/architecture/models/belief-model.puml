@startuml
' ==========================================================
' Game of Life Belief System - Refined MVP Model
' ----------------------------------------------------------
' This model represents a dynamic belief system wherein beliefs are
' continuously updated and interact with evidence, goals, capacities,
' and limitations. The following commentary explains the technical
' purpose of each property in the Belief class with precision.
' ===========================================================

' ---------------------------
' Updated Technical Commentary on Belief Properties
' ---------------------------
'
' Belief Properties:
'
' - beliefId:
'   * A unique identifier (e.g., UUID) that distinguishes each belief.
'
' - content:
'   * The core statement or proposition, articulated in natural language.
'
' - evidence:
'   * A collection of supporting or contradicting data points.
'   * The first entry may represent the initial trigger or origin of the belief.
'
' - userConfidence:
'   * A quantitative measure (scale 0 to 100) reflecting how strongly
'     the user personally endorses the belief.
'
' - systemConfidence:
'   * An evaluation metric provided by the system that assesses the truthfulness
'     or validity of the belief based on aggregated evidence (scale 0 to 100).
'
'  - emotionality:
'   * A numerical value measured on a signed scale (-100 to +100) representing both the intensity and valence of the emotional charge
'     linked to the belief.
'     Negative values indicate a negative emotional charge, while positive values indicate a positive charge.
'
' - stability:
'   * Indicates the resistance of the belief to change (scale 0 to 100).
'   * Higher values suggest that the belief remains stable despite new inputs.
'
' - userAwareness:
'   * Reflects the level of conscious recognition the user has regarding
'     the belief, distinguishing explicit from implicit convictions (scale 0 to 100).
'
' - lastUpdated:
'   * A timestamp marking the most recent evaluation or update, ensuring
'     that the belief reflects the latest available information.
'
' ---------------------------
' 1. Enumerations (Optional)
' ---------------------------
' (Optional enumerations such as BeliefEvidenceType can be defined here)

' ---------------------------
' 2. Core Classes
' ---------------------------
' Belief class encapsulates a proposition held by the user, measured across several dimensions
' that allow for precise quantification and dynamic updating based on new evidence and experience.
class Belief {
  + beliefId: String              ' Unique identifier for the belief (e.g., UUID)
  + content: String               ' Core statement or proposition represented in natural language
  + lastUpdated: Date             ' Timestamp of the most recent recalibration or evaluation
  + evidence: List<BeliefEvidence>      ' Collection of evidence items; the first may serve as the belief’s origin
  --
  + userConfidence: int           ' Quantitative measure of personal conviction (scale 0 to 100)
  + systemConfidence: int         ' System evaluation of the belief's truthfulness (scale 0 to 100)
  + emotionality: int             ' Emotional charge measured on a signed scale (-100 to +100); negative for negative emotions, positive for positive emotions
  + stability: int                ' Resistance to modification (scale 0 to 100; higher values indicate more stability)
  + userAwareness: int            ' Level of conscious recognition of the belief by the user (scale 0 to 100)
}

'---------------------------------------------------------------------
' Class: BeliefEvidence
'---------------------------------------------------------------------
' Represents a data point supporting or contradicting a belief. The type field,
' based on the BeliefEvidenceType enum, indicates the category of evidence (e.g., EXPERIENCE, AUTHORITY, SOCIAL_CONSENSUS, INTUITION).
class BeliefEvidence {
  + evidenceId: String            ' Unique identifier for the evidence entry
  + type: BeliefEvidenceType      ' Type of evidence (e.g., EXPERIENCE, AUTHORITY, SOCIAL_CONSENSUS, INTUITION)
  + description: String           ' Detailed description of the evidence, including context
  + credibilityScore: Float       ' Quantitative measure of reliability or trustworthiness
  + source: String                ' Cites the origin of the evidence (e.g., empirical data, expert opinion)
}

' Goal class represents personal objectives influenced by or interacting with beliefs.
class Goal {
  + goalId: String                ' Unique identifier for the goal
  + title: String                 ' Short, descriptive title for the objective
  + description: String           ' Detailed explanation of the goal
  + priority: Float               ' Priority level for goal attainment (e.g., scale 0 to 1 or 0 to 100)
}

' Capacity class represents a user's ability or strength, which may be modulated by the beliefs held.
class Capacity {
  + capacityId: String            ' Unique identifier for the capacity record
  + description: String           ' Detailed explanation of the user's ability or strength
  + level: Float                  ' Quantitative measure of the capacity’s magnitude
}

' UserLimitation class models constraints or weaknesses that affect user performance.
class UserLimitation {
  + limitationId: String          ' Unique identifier for the limitation
  + description: String           ' Explanation of the constraint or weakness
  + severity: Float               ' Quantitative measure of the limitation’s impact (e.g., scale 0 to 1)
  + isOvercomable: Boolean        ' Flag indicating whether the limitation can be mitigated through intervention
}

' ---------------------------
' 3. Relationships
' ---------------------------
' Beliefs may be supported or contradicted by multiple pieces of evidence,
' allowing aggregation of validation or refutation inputs.
Belief "1" -- "0..*" BeliefEvidence : "supportedBy"

' Beliefs interact bidirectionally with Goals in a many-to-many relationship.
' This interaction reflects both how beliefs shape goal formation and how goal pursuits can modify beliefs.
Belief "0..*" -- "0..*" Goal : "shapes or is shaped by"

' Beliefs interact with Capacities in a many-to-many relationship,
' indicating that beliefs may boost or reduce inherent abilities, thereby affecting performance.
Belief "0..*" -- "0..*" Capacity : "can boost or reduce"

' Beliefs also interact with UserLimitations in a many-to-many manner,
' capturing how beliefs can create constraints or mitigate existing limitations,
' thus influencing the potential for overcoming obstacles.
Belief "0..*" -- "0..*" UserLimitation : "can create or mitigate"

@enduml
