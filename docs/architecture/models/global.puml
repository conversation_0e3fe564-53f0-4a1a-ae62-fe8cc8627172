@startuml global model
!include user.puml
!include activity.puml

'================================================================
' Global Skin Parameters
'================================================================
skinparam packageTitleFontColor White
skinparam packageTitleBackgroundColor #1B4965
skinparam packageBorderColor #1B4965
skinparam packageBackgroundColor #CAE9FF
skinparam classAttributeIconSize 0
package "app 'main'"#d4ff97 {
  package "agents" #f99 {

    '---------------------------------------------------------------------
    ' Entity: GenericAgent
    '---------------------------------------------------------------------
    ' It's a "fixed" list of generic agents that will be used to create the custom agents.
    '
    ' Fields:
    '   * agent_id: Unique identifier for the agent.
    '     Example: "123e4567-e89b-12d3-a456-************"
    '   * name: The name of the agent.
    '     Example: "Mentor Agent"
    '   * description: Detailed description of the agent's responsibilities.
    '     Example: "Acts as the primary user interface, synthesizing data from all other agents."
    '   * input_schema: json structure representing the expected input.
    '     Example: "{ 'userId': 'string', 'activityFeedback': 'string' }"
    '   * output_schema: json structure representing the expected output.
    '     Example: "{ 'message': 'string', 'actionRecommendation': 'string' }"
    entity GenericAgent {
      * agent_id : AutoField
      --
      name : string
      description : text

      generic_instructions : text
      input_schema: json
      output_schema: json
    }

    '---------------------------------------------------------------------
    ' Entity: CustomAgent
    '---------------------------------------------------------------------
    ' Represents an AI agent in the system.
    '
    ' Fields:
    '   * agent_id: Unique identifier for the agent.
    '     Example: "123e4567-e89b-12d3-a456-************"
    '   * name: The name of the agent.
    '     Example: "Mentor Agent"
    '   * description: Detailed description of the agent's responsibilities.
    '     Example: "Acts as the primary user interface, synthesizing data from all other agents."
    entity CustomAgent {
      * agent_id : uuid
      --
      name : string
      description : text
      instruction : text
    }

    GenericAgent "1" -- "0..*" CustomAgent : based on


    '---------------------------------------------------------------------
    ' Entity: AgentMemory
    '---------------------------------------------------------------------
    ' Personal memory of an agent, used to store information about interactions with the user or other agents.
    '
    ' Fields:
    '   * id: Unique identifier for the memory entry.
    '     Example: "mem-001"
    '   * content: The content of the memory log.
    '     Example: "User completed activity X successfully."
    '   * timestamp: The date and time when the memory was recorded.
    '     Example: "2023-08-15T14:30:00Z"
    '   * history_log_id: Identifier linking this memory to a history log.
    '     Example: "hist-123"
    entity AgentMemory {
      * id : BigAutoField
      --
      content : json
      timestamp : datetime
      history_log_id: uuid
    }

    '---------------------------------------------------------------------
    ' Entity: AgentGoal
    '---------------------------------------------------------------------
    ' Represents goals that an agent is pursuing for a user.
    '
    ' Fields:
    '   * goal_id: Unique identifier for the goal.
    '     Example: "goal-001"
    '   * description: Description of the goal.
    '     Example: "Increase user engagement by 20%"
    '   * priority: The importance of the goal.
    '     Example: 1
    '   * user_goal_id: Identifier linking this goal to the user's goal.
    '     Example: "usergoal-456"
    entity AgentGoal {
      * goal_id : BigAutoField
      --
      description : text
      priority : Integer
      user_goal_id : uuid
    }

    '---------------------------------------------------------------------
    ' Entity: AgentTool
    '---------------------------------------------------------------------
    ' Represents a tool that an agent can utilize to perform specific functions.
    '
    ' Fields:
    '   * tool_id: Unique identifier for the tool.
    '     Example: "tool-001"
    '   * name: The name of the tool.
    '     Example: "Data Analyzer"
    '   * function: Description of the tool's function.
    '     Example: "Processes and analyzes user interaction data."
    entity AgentTool {
      * id : AutoField
      --
      name : string
      function : text
    }

    ' Define relationships between the entities
    CustomAgent "1" -- "0..*" AgentMemory : has
    CustomAgent "1" -- "0..*" AgentGoal : pursues
    CustomAgent "1" -- "0..*" AgentTool : utilizes
  }

  package "Memory" #ffff97 {
    '================================================================
    ' History Log
    '================================================================
    ' Centralized log table for all relevant events happening in the system
    ' *aggregateType : the type of entity that has triggered the event
    ' *aggregateId : the id of entity that has triggered the event
    ' *aggregateVersion : the version (if any) of that entity that has triggered the event
    ' *eventDetails will contains things like :
    '  -'event_type' : most likely an Enum to categorize the event
    '  -'event_trigger' : what has cause this log to be created
    '  -'event_data' : any relevant data to the event (feedback_content, positiveness, criticallity, etc.)

    entity HistoryLog {
      *id: UUID
      + timestamp: Date
      + aggregateType: String
      + aggregateId: String
      + aggregateVersion: int
      + eventDetails: json
    }

    entity UserFeedback {
      * id : uuid
      + feedback_type : string "e.g., Accepted, Declined, Comment"
      + aggregateType: String
      + aggregateId: String
      + created_on : datetime
      + user_comment : text
      + criticallity : int
      + context_data : json
      + slack_payload : json
    }
  }
  package "Game Mechanics" #FBE7C6 {

    entity Wheel {
      * id : uuid
      + name : string
      + created_by : string
      + created_at : date
    }

    entity WheelItem {
      * id : uuid
      + percentage : float
    }

    Wheel "1" -- "1..*" WheelItem : contains

    WheelItem "1" -- "1" ActivityTailored : refers_to
  }
}

@enduml