package "app 'user'" #E9D8FD {

  entity UserProfile {
    * id : BigAutoField
    + profile_name : string
    + current_environment : uuid
  }



  package "User Psyche profile" #D7CEC7 {

    entity Demographics {
      + full_name : string
      + age : int
      + gender : string
      + location : string
      + language : string
      + occupation : string
      + personal_prefs_json : json
    }

    entity UserGoal {
      * id : uuid
      * user_id : uuid
      + title : string
      + description : text
      + importance_according_user : int
      + importance_according_system : int
      + strength : int
      + created_at : datetime
      + updated_at : datetime
    }

    entity Intention extends UserGoal {
      * id : uuid
      + start_date : date
      + due_date : date
      + is_completed : boolean
      + progress_notes : text
    }

    entity Aspiration extends UserGoal {
      * id : uuid
      + domain : string
      + horizon : string
      + level_of_ambition : string
    }

    entity Inspiration {
      * id : uuid
      * user_id : uuid
      + source : string
      + description : text
      + strength : int
      + reference_url : url
      + created_at : datetime
      + updated_at : datetime
    }

    entity GoalInspiration {
      * id : uuid
      * goal_id : uuid
      * inspiration_id : uuid
      + note : text
    }

    ' Associations between UserGoal, Inspiration and GoalInspiration
    UserGoal "1" -- "0..*" GoalInspiration : influenced_by
    Inspiration "1" -- "0..*" GoalInspiration : influences

    entity TrustLevel {
      * id : uuid
      + value : int
      + aggregate_type : string
      + aggregate_id : string
      + notes : string
    }

    entity Preference {
      * id : uuid
      + pref_name : string
      + pref_description : string
      + pref_strength : int
      + user_awareness : int
      + environment_id : int <<optional>>
    }

    entity Belief {
      * id : uuid
      + content : string
      + last_updated : date
      + user_confidence : int
      + system_confidence : int
      + emotionality : int
      + stability : int
      + user_awareness : int
    }

    entity BeliefEvidence {
      * id : uuid
      + type : BeliefEvidenceType
      + description : string
      + credibility_score : float
      + source : string
    }

    entity BeliefInfluence {
      * id : uuid
      + belief_id : uuid
      + target_entity_id : uuid
      + target_entity_type : string
      + influence_strength : int
      + note : string
    }

    Belief "1" -- "0..*" BeliefInfluence : influences

    entity CurrentMood {
      * id : uuid
      + description : string
      + height : int
      + user_awareness : int
      + processed_at : datetime
    }

    ' Relationships in UserProfile Psyche profile
    UserProfile "1" -- "1" Demographics
    UserProfile "1" -- "0..*" Preference
    UserProfile "1" -- "0..*" Intention
    UserProfile "1" -- "0..*" Aspiration
    UserProfile "1" -- "0..*" Inspiration
    UserProfile "1" -- "0..*" Belief
    UserProfile "1" -- "0..*" UserGoal
    UserProfile "1" -- "1" CurrentMood
    Belief "1" -- "0..*" BeliefEvidence : has

    UserProfile "1" -- "1" TrustLevel : trust


  }
  package "Shared with activities" #97ffb9 {

    entity GenericTrait #99f{
      * id : AutoField
      * code : varchar(50) <<Unique>>
      --
      name : string
      description : string
      created_at : timestamp
      updated_at : timestamp
    }

    entity UserTraitInclination {
      * id : uuid
      * user_id : int
      * trait_id : int
      + strength : float
      + awareness : int
    }

    UserTraitInclination "0..*" -- "1" GenericTrait : based on
    UserProfile "1" -- "1..*" UserTraitInclination : has_inclinations


    entity GenericEnvironment #99f {
      * id : AutoField
      * code : varchar(50) <<Unique>>
      --
      name : string
      description : json
      is_active : boolean
      created_at : timestamp
      updated_at : timestamp
    }

    entity UserEnvironment {
      * environment_id : BigAutoField
      + environment_name : string
      + environment_description : string
      + environment_details : json
    }



    entity Inventory {
      * id : uuid
      + environment_id : uuid
      + valid_until : date
      + notes : string
    }

    UserEnvironment "1" -- "0..*" Inventory : has
    UserEnvironment "1" -- "0..*" Preference : influences

    UserProfile "1" -- "0..*" UserEnvironment : can_define

    entity GenericResource #99f {
      * id : AutoField
      + resource_type : string
      + description : string
      + op_cost : int
      + acq_cost : int
    }

    entity UserResource {
      * id : uuid
      + specific_name : string
      + environment_id : uuid
      + location_details : string
      + ownership_details : string
      + contact_info : string
      + notes : string
    }

    entity GenericSkill #99f {
      * id : AutoField
      + description : string
    }

    entity Skill {
      * id : uuid
      + description : string
      + level : int
      + user_awareness : int
      + user_enjoyment : int
      + note : string
    }

    entity GenericUserLimitation #99f {
      * id : AutoField
      + description : string
      + limitation_type : string
    }

    entity UserLimitation {
      * id : uuid
      + severity : int
      + valid_until : date
      + is_unlimited : boolean
      + user_awareness : int
    }

    UserProfile "1" -- "0..*" Skill : has
    UserProfile "1" -- "0..*" UserLimitation : limited_by
    UserProfile "1" -- "1..*" Inventory : has

    Inventory "1..*" -- "0..*" UserResource : contains

    Skill "0..*" -- "1" GenericSkill : based on
    UserLimitation "0..*" -- "1" GenericUserLimitation : based on
    UserEnvironment "0..*" -- "1" GenericEnvironment : based on
    UserResource "0..*" -- "0..1" GenericResource : based on

  }
  ' Abstract TemporalRecord is defined once, with a note listing its inheritors to avoid diagram overload.
  entity TemporalRecord <<abstract>> {
    + effective_start : date
    + duration_estimate : string
    + effective_end : date
  }

  note right of TemporalRecord
    Inherited by:
    UserEnvironment,
    CurrentMood,
    Preference,
    Intention,
    Aspiration,
    UserLimitation
  end note
}



DjangoUser "1" -- "1" UserProfile