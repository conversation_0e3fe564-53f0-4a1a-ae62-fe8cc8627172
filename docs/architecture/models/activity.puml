package "app 'activity'" #D4F1F4 {

  entity GenericActivity #99f {
    * id : AutoField
    + name : string
    + description : string
    + created_on : date
    + duration_range : string
    + instructions : string
    + physical_requirements : json
    + social_requirements : json
    + domain : json
  }

  entity ActivityTailored {
    * id : uuid
    * user_id : uuid
    + name : string
    + description : string
    + created_on : date
    + duration_range : string
    + instructions : string
    + base_challenge_rating : int
    + physical_requirements : json
    + social_requirements : json
    + challengingness : json
    + domain : json
    + version : int
    + tailorization_level : int
  }

  ActivityTailored "1" -- "1" GenericActivity : based_on

  entity ActivityInfluencedBy {
    * id : uuid
    * activity_tailored_id : uuid
    * influencing_entity_id : uuid
    + influencing_entity_type : string
    + influence_strength : int
    + note : string
  }

  ActivityTailored "1" -- "0..*" ActivityInfluencedBy : influenced_by

  entity GenericActivityResourceRequirement {
    * gen_activity_id : int
    * resource_base_id : int
    + quantity_required : int
    + optional : boolean
  }

  entity GenericActivityTraitRequirement {
    * gen_activity_id : int
    * trait_id : int
    + level_required : int
    + optional : boolean
  }

  entity GenericActivityEnvRequirement {
    * gen_activity_id : int
    * env_id : int
    + optional : boolean
  }

  entity ActivityUserRequirement {
    * id : uuid
    + resource_type : string
    + resource_id : uuid
    + required_level : int
    + quantity_required : int
    + optional : boolean
  }

  entity ActivityEnvRequirement {
    * id : uuid
    + env_type : string
    + env_detail : json
    + required_level : int
    + optional : boolean
  }

  entity ActivityTailoredResourceRequirement {
    * id : uuid
    * activity_tailored_id : uuid
    * personal_resource_id : uuid
    + quantity_required : int
    + optional : boolean
  }

  
}

GenericActivity "1" -- "0..*" GenericActivityResourceRequirement : requires
GenericActivity "1" -- "0..*" GenericUserLimitation : incompatible_with
GenericActivity "1" -- "0..*" GenericSkill : requires
GenericActivity "1" -- "0..*" GenericActivityTraitRequirement : requires
GenericActivity "1" -- "0..*" GenericActivityEnvRequirement : requires

GenericActivityTraitRequirement "1" -- "0..*" GenericTrait : requires
GenericActivityEnvRequirement "1" -- "0..*" GenericEnvironment : requires
GenericActivityResourceRequirement "1" -- "0..*" GenericResource : requires

ActivityTailoredResourceRequirement "1" -- "0..*" UserResource : requires
ActivityTailored "1" -- "0..*" ActivityUserRequirement : requires
ActivityTailored "1" -- "0..*" ActivityTailoredResourceRequirement : requires
ActivityTailored "1" -- "0..*" ActivityEnvRequirement : requires


UserTraitInclination "1" -- "0..*" ActivityUserRequirement : requires
Skill "1" -- "0..*" ActivityUserRequirement : requires
UserLimitation "1" -- "0..*" ActivityUserRequirement : limited_by



UserEnvironment "1" -- "0..*" ActivityEnvRequirement : matching