# Workflow Transition Implementation Guide

This document explains how the MentorAgent can trigger transitions between different workflows, specifically from the `discussion_graph` to other workflow types like `wheel_generation`.

## Overview

During a conversation within the discussion workflow, the user might express a need that would be better served by a different workflow type. For example, while having a general discussion, the user might say "I'd like some activity suggestions" - which is better handled by the wheel generation workflow.

This implementation allows the MentorAgent to:
1. Detect when a workflow transition is appropriate
2. Request the transition with proper context
3. Communicate the transition clearly to the user
4. Seamlessly initiate the new workflow while terminating the current one

## Components

The implementation spans several components that work together:

1. **MentorAgent**: Enhanced to detect workflow transition needs and request transitions
2. **DiscussionState**: Extended with transition_request field to track transition requests 
3. **Discussion Graph**: Modified to handle transition requests in the routing logic
4. **ConversationDispatcher**: Enhanced to respect requested_workflow in metadata
5. **WorkflowResultHandler**: Already capable of processing transition results

## Implementation Details

### 1. MentorAgent Enhancements

The MentorAgent has been enhanced with:

- **Workflow Transition Detection**: Using the `_detect_workflow_transition` method, it analyzes user messages within context to determine if a transition is needed
- **Transition Response Formatting**: The `_format_transition_response` method creates a user-friendly message explaining the transition
- **Transition Request Handling**: The main `process` method checks for transition requests and formats appropriate state updates

```python
# Key method for detecting transitions
async def _detect_workflow_transition(self, message, context_packet, current_stage, conversation_history):
    # Use LLM to determine if transition is needed
    # Return transition request dict or None
```

### 2. DiscussionState Changes

Added a new field to track transition requests:

```python
class DiscussionState(BaseModel):
    # ... existing fields ...
    
    # Workflow transition information - Added for transition handling
    transition_request: Optional[Dict[str, Any]] = None
```

### 3. Discussion Graph Routing Logic

Modified the routing function to check for transition requests:

```python
def route_from_mentor(state: DiscussionState):
    # Check for workflow transition requests
    if state.transition_request:
        state.current_stage = "workflow_transition"
        # End this workflow to allow the new one to start
        return END
    
    # ... existing routing logic ...
```

Also added a new handler function to process the transition:

```python
async def handle_workflow_transition(user_profile_id, transition_request, user_ws_session_name):
    # Create conversation dispatcher
    # Process the message with requested_workflow in metadata
    # Return transition results
```

### 4. ConversationDispatcher Integration

Enhanced to recognize and respect the `requested_workflow` in metadata:

```python
async def _classify_message(self, user_message, context, profile_status):
    # Check metadata for explicit workflow requests (highest priority)
    if "requested_workflow" in metadata:
        return {
            "workflow_type": metadata["requested_workflow"],
            "confidence": 1.0,
            "reason": "Explicitly requested in metadata"
        }
    
    # ... existing classification logic ...
```

## Workflow Transition Process

The complete transition process works as follows:

1. User sends a message in the discussion workflow
2. MentorAgent processes the message and detects transition need
3. MentorAgent includes transition_request in output and state updates
4. Discussion graph routing function sees transition request and ends current workflow
5. handle_workflow_transition creates ConversationDispatcher and processes message
6. ConversationDispatcher respects requested_workflow in metadata
7. New workflow is initiated with context from previous workflow
8. WorkflowResultHandler processes results from both workflows
9. WebSocket consumer delivers all appropriate messages to client

## Transition Request Format

```json
{
  "workflow_type": "wheel_generation",  // The target workflow
  "confidence": 0.85,                  // Confidence in transition need
  "message": "I want activities",      // Original user message
  "context": {                         // Context to pass to new workflow
    "mood": "excited",
    "focus": "activities"
  },
  "reason": "User is asking for activity suggestions"
}
```

## Metadata Format for ConversationDispatcher

```json
{
  "requested_workflow": "wheel_generation",  // Explicit workflow request
  "transition_context": {},                  // Additional context
  "from_workflow": "discussion"              // Source workflow for tracking
}
```

## Testing

The implementation includes:

1. **Unit Tests**: Testing individual components in isolation
2. **Integration Tests**: Testing the complete flow between components
3. **End-to-End Tests**: Testing the entire process including WebSocket communication

## Usage Example

```python
# MentorAgent detecting transition need
transition_request = await mentor._detect_workflow_transition(
    message="I want to try some fun activities today",
    context_packet={"mood": "energetic"},
    current_stage="context_deepening",
    conversation_history=conversation_history
)

# If transition needed, include in output
if transition_request:
    output_data["transition_request"] = transition_request
    state_updates["transition_request"] = transition_request
```

## Benefits

This implementation offers several advantages:

1. **Seamless User Experience**: The transition appears natural to the user
2. **Context Preservation**: Important context is carried over to the new workflow
3. **Appropriate Specialization**: Each workflow can focus on what it does best
4. **Minimal Changes**: The implementation builds on existing architecture
5. **Testability**: The design is fully testable at all levels

## Future Enhancements

Potential improvements for the future:

1. **Transition History**: Track workflow transitions for analytics
2. **Bidirectional Transitions**: Allow transitions back to discussion from other workflows
3. **Partial Transitions**: Execute another workflow while keeping discussion active
4. **Multi-Workflow Orchestration**: Coordinate multiple workflows simultaneously