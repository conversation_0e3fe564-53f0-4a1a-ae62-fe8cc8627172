# Goali Documentation

This directory contains comprehensive documentation for the Goali project, organized by audience and purpose.

## Quick Navigation

### 🚀 Getting Started
- **[Benchmarking Usage Guide](user-guide/BENCHMARKING_USAGE_GUIDE.md)** - Step-by-step instructions for using the benchmarking system
- **[Testing Guide](testing/TESTING_GUIDE.md)** - Comprehensive testing documentation

### 🔧 Technical Reference
- **[Benchmarking System](backend/BENCHMARKING_SYSTEM.md)** - Complete technical documentation for the benchmarking system
- **[Backend Documentation](backend/)** - Technical documentation for backend systems
- **[Quality Documentation](backend/quality/)** - Quality assurance and testing documentation

### 📚 Historical Context
- **[Benchmarking Archive](../BENCHMARKING_ARCHIVE.md)** - Preserved ideas and historical context from benchmarking system development

## Documentation Structure

### User Guides (`user-guide/`)
Practical, step-by-step instructions for end users:
- **Benchmarking Usage Guide** - How to create, run, and analyze benchmarks

### Backend Documentation (`backend/`)
Technical reference for developers:
- **Main Systems** - Core system documentation
- **Quality** - Quality assurance and testing patterns
- **Architecture** - System architecture and design decisions

### Testing Documentation (`testing/`)
Comprehensive testing guidance:
- **Testing Guide** - General testing practices and patterns
- **Agent Testing** - Specific guidance for testing AI agents

## Documentation Principles

### Audience-Focused Organization
- **Users**: Practical guides with step-by-step instructions
- **Developers**: Technical reference with implementation details
- **Maintainers**: Architecture decisions and historical context

### Up-to-Date Information
- All documentation reflects the current system state
- Deprecated files are clearly marked with redirects
- Historical context is preserved in dedicated archive files

### Clear Navigation
- Quick navigation sections in each document
- Cross-references between related documentation
- Consistent linking structure throughout

## Recent Documentation Refresh

The documentation has been recently refreshed to:

✅ **Eliminate Obsolete Information**: Removed outdated content that no longer reflects the current system

✅ **Preserve Good Ideas**: Moved valuable historical context and future ideas to dedicated archive files

✅ **Improve Organization**: Separated user guides from technical reference documentation

✅ **Ensure Accuracy**: Updated all information to reflect the current system state

✅ **Enhance Usability**: Added clear navigation and practical examples

## Contributing to Documentation

### When to Update Documentation
- Adding new features or systems
- Changing existing functionality
- Discovering outdated information
- Adding new best practices or patterns

### Documentation Standards
- Use clear, concise language
- Include practical examples
- Provide navigation aids
- Link to related documentation
- Mark deprecated content clearly

### File Organization
- Place user-focused content in `user-guide/`
- Place technical reference in `backend/`
- Place testing guidance in `testing/`
- Preserve historical context in archive files

## Getting Help

### For Users
Start with the [Benchmarking Usage Guide](user-guide/BENCHMARKING_USAGE_GUIDE.md) for practical instructions.

### For Developers
Refer to the [Benchmarking System](backend/BENCHMARKING_SYSTEM.md) technical documentation.

### For Historical Context
Check the [Benchmarking Archive](../BENCHMARKING_ARCHIVE.md) for preserved ideas and investigation results.

### For Testing
See the [Testing Guide](testing/TESTING_GUIDE.md) for comprehensive testing documentation.

---

*This documentation is actively maintained. For questions or suggestions, please refer to the project's contribution guidelines.*
