# Archetypal User Persona Template

## 1. Executive Summary
**Archetype Essence:** [One-paragraph distillation of this user type's defining characteristics]
**Key Pattern:** [The core psychological pattern that defines this archetype's approach to life and activities]
**Trust & Growth Trajectory:** [Brief overview of how this archetype typically develops trust and expands their comfort zone]
**Lifecycle Phase:** [Onboarding/Foundation/Expansion/Mastery - current stage in user journey]

## 2. Psychological Framework
**HEXACO Profile:**
- Honesty-Humility: [Low/Moderate/High] - [Key manifestation]
- Emotionality: [Low/Moderate/High] - [Key manifestation]
- eXtraversion: [Low/Moderate/High] - [Key manifestation]
- Agreeableness: [Low/Moderate/High] - [Key manifestation]
- Conscientiousness: [Low/Moderate/High] - [Key manifestation]
- Openness to Experience: [Low/Moderate/High] - [Key manifestation]

**Key Data Model Elements:**
- Primary GenericTrait Inclinations: [List of 3-5 key traits with strength values]
- Core Beliefs: [2-3 central beliefs with stability and emotionality values]
- Key UserGoals: [1-2 Intentions and 1-2 Aspirations]
- TrustLevel: [Initial value and aggregate_type]
- Critical Preferences: [Key preferences with strength values]

**Core Psychological Dynamics:**
- Primary Driver: [What fundamentally motivates this archetype]
- Avoidance Pattern: [What this archetype typically avoids or resists]
- Coping Mechanism: [How this archetype typically handles stress or challenges]
- Growth Edge: [The frontier where this archetype has the most potential for development]

## 3. Life Context Snapshot
**Environmental Setting:** [Brief description of typical environment and resources]

**Activity Engagement Profile:**
- High Engagement Domains: [Where they naturally invest energy]
- Resistance Domains: [Where they typically disengage]
- Temporal Patterns: [When they're most receptive to engagement]

**Life Task Approach:**
- Work/Contribution: [How they approach work-related tasks]
- Social Connection: [How they navigate relationships]
- Self-Development: [How they pursue personal growth]

## 4. System Interaction Model
**Trust Development:**
- Initial Trust Phase: [Foundation/Transition/Expansion]
- Previous Phase Transition: [Brief description of how they moved from previous phase]
- Next Phase Triggers: [Events or changes that would signal readiness for next phase]
- Trust Building Keys: [What builds trust most effectively]
- Trust Erosion Risks: [What would undermine trust]

**Challenge Calibration Strategy:**
- Optimal Challenge Zone: [General range and approach]
- Domain-Specific Considerations: [Where challenge levels should vary]
- Progressive Challenge Path: [How to gradually increase challenge]

**Activity Wheel Testing Parameters:**
- Optimal Domain Distribution: [Percentages across domains]
- Challenge Tolerance: [Parameters for initial challenge calibration]
- Activity Type Preferences: [Formats and structures that resonate]
- Refusal Risk Factors: [Activities or framing likely to trigger refusals]
- Post-Spin Response Profile: [Typical response to wheel outcomes]

**Communication Framework:**
- Effective Framing: [How to present activities and feedback]
- Language Patterns: [Communication style that resonates]
- Resistance Handling: [How to address avoidance patterns]

## 5. Agent Strategy Blueprint
**Orchestrator Agent Focus:** [Key considerations for workflow coordination]

**Resource & Capacity Agent:** [Important resource and capacity factors]

**Engagement & Pattern Agent:** 
- Key Patterns to Monitor: [Behavioral patterns to track]
- Response to Refusals: [How to handle activity rejections]
- Engagement Prediction Factors: [What predicts successful engagement]

**Psychological Monitoring Agent:** 
- Key Psychological Dynamics to Track: [Critical emotional and behavioral patterns]
- Belief-Behavior Connections: [How beliefs influence actions]
- Vulnerability Indicators: [Signs of psychological discomfort]

**Strategy Agent Approach:** [Overall strategic framework]

**Wheel/Activity Agent:** [Activity selection and tailoring principles]

**Ethical Oversight Considerations:** 
- Potential Vulnerability Areas: [Psychological sensitivities to monitor]
- Privacy Boundaries: [Topics or data requiring special handling]
- Challenge Ethics: [Ethical considerations for challenge calibration]
- Value Alignment Factors: [How system values may conflict or align with user values]

**Cross-Agent Coordination Requirements:**
- Key Information Sharing Points: [Critical data that must be passed between agents]
- Sequential vs. Parallel Processing: [Agent workflow optimization for this archetype]
- Conflict Resolution Approach: [How to handle conflicting agent recommendations]

## 6. Memory Formation Priorities
**Engagement Pattern Memory:**
- Key Patterns to Record: [Behavioral patterns the system should recognize]
- Critical Context Factors: [Environmental or situational factors that significantly affect engagement]

**Psychological Pattern Memory:**
- Trait Expression Consistency: [How consistently traits are expressed across contexts]
- Psychological Growth Indicators: [Signs of development to track]
- Resistance Pattern Recognition: [How to identify and respond to resistance]

**Communication Memory:**
- Effective Tone and Style: [Communication approaches that resonate]
- Metaphors and Examples: [Explanatory devices that connect]
- Resistance Language Patterns: [Verbal indicators of disengagement]

**Strategy Memory:**
- Challenge Response History: [How the user responds to different challenge levels]
- Domain Engagement Evolution: [Changes in domain preferences over time]
- Milestone Recognition: [How to frame achievements for this archetype]

## 7. Profile Confidence Assessment
- High Confidence Elements: [Aspects where the system can be highly confident]
- Low Confidence Elements: [Aspects requiring additional validation]
- Progressive Profiling Needs: [Data to gather over time]
- Contradiction Reconciliation: [How to handle inconsistent user data]

## 8. User Journey Narrative
**Entry Point Story:** [Brief narrative of how this archetype typically enters the system]

**Key Transformative Moments:** [Critical moments in their development journey]

**Expected Evolution:** [How this archetype typically transforms through system interaction]

**Success Indicators:** [Observable signs that the system is working effectively]

## 9. Testing & Validation
**Representative Questionnaire Responses:** [3-5 characteristic responses to key onboarding questions]

**Critical Test Scenarios:**
- Onboarding Flow Test: [Specific test for initial profile creation]
- Wheel Generation Test: [Specific test for activity selection]
- Post-Spin Test: [Specific test for handling spin outcomes]
- Post-Activity Test: [Specific test for processing activity feedback]

**Edge Cases to Monitor:** [Potential unique challenges this archetype might present]

**Simulated User Actions:**
- Likely Refusals: [Activities or contexts likely to trigger refusals]
- Trust Breakpoints: [Scenarios that might break trust]
- Engagement Accelerators: [Scenarios that might significantly increase engagement]