@startuml
' ======== Participants ========
actor "User" as U

box "AI Agents" #eef
  participant "Mentor Agent" as MA
  participant "Psychological Monitoring Agent" as PMA
  participant "Activity & Wheel Specialist Agent" as AWSA
  participant "System Monitoring Agent" as SMA
  participant "Engagement & Pattern Analytics Agent" as EPA
  participant "Contextual Scheduling & Notification Agent" as CSNA
  participant "Resource & Capacity Management Agent" as RCMA
end box

box "Model" #efe
  participant "User Model\n(User, UserArchetype)" as UM
  participant "Activity Model\n(ActivityBase, ActivityTailored, Wheel)" as AM
  participant "History Log" as HL
end box

' ======== Sequence of Interactions ========
== App Launch and Initial Feedback ==
U -> MA: Launch "The Game of Life" app\nEnter daily journal (e.g., "How's your energy today?")
MA -> UM: Query user profile\n[GET /user/{userId}]\nPayload: {userId, userName, archetype, demographics}
UM --> MA: Return JSON with user profile data

== Psychological Data Processing ==
MA -> PMA: Forward user feedback & mood update
note right of MA
  Payload JSON: {
    "userFeedback": "Feeling overwhelmed yet hopeful",
    "currentMood": {"label": "anxious-but-determined", "strength": 60},
    "traitScores": {"openness": 70, "conscientiousness": 40},
    "historicalData": [...]
  }
end note
PMA -> HL: Log feedback into UserFeedbackLog with timestamp
PMA --> MA: Return updated psychological profile
note right of PMA
  JSON: {
    "updatedPsychProfile": {"archetype": "Explorer", "traitScores": {"openness": 70, "conscientiousness": 45}},
    "moodAnalysis": {"currentMood": "anxious-but-determined", "trend": "stable"},
    "alerts": []
  }
end note

== Activity Recommendation via Wheel Spin ==
U -> MA: Initiate wheel spin for challenge selection
MA -> AWSA: Request tailored activity recommendation
note right of MA
  Payload JSON: {
    "activityCatalog": [ ... ],
    "userProfile": {"userId": "UUID", "psychProfile": "Explorer"},
    "capacityData": {"physical": 70, "mental": 65},
    "wheelConfig": {"items": [{ "activityBaseId": "AB12", "percentage": 20 }, ...]}
  }
end note
AWSA -> AM: Query Activity Catalog and Wheel items\n[GET /activities/catalog]
AM --> AWSA: Return available activities and challenge details
note right of AM
  JSON: { "activities": [ {...}, {...} ] }
end note
AWSA -> RCMA: Request resource & capacity assessment
note right of AWSA
  Payload JSON: {
    "inventoryData": [ ... ],
    "capacityMetrics": {"physical": 70, "mental": 65, "social": 50, "financial": 40},
    "limitations": [ ... ]
  }
end note
RCMA -> UM: Retrieve resource and capacity info for user
UM --> RCMA: Return resource details and limitations
RCMA --> AWSA: Return resource assessment & adjustments
note right of RCMA
  JSON: { 
    "resourceAssessment": { ... }, 
    "adjustedActivityParameters": {"maxDifficulty": 85} 
  }
end note
AWSA --> MA: Return tailored activity recommendation
note right of AWSA
  Payload JSON: {
    "tailoredActivity": {"activityTailoredId": "AT123", "description": "Build a mini greenhouse", "adjustedDifficulty": 80},
    "wheelSelectionResult": {"selectedItem": "WheelItem45", "rationale": "Balanced challenge and relevance"}
  }
end note

== Progress and Engagement Analysis ==
MA -> EPA: Request progress report
note right of MA
  Payload JSON: {
    "trustLevels": { ... },
    "activityCompletions": [ ... ],
    "goalData": [ ... ],
    "feedbackEntries": [ ... ]
  }
end note
EPA -> HL: Query historical logs\n[GET /history/logs?userId=UUID]
HL --> EPA: Return aggregated logs and metrics
EPA --> MA: Return progress report
note right of EPA
  JSON: {
    "progressReport": {"milestonesAchieved": ["First Builder", "Brave Confessor"], "currentEngagementScore": 75},
    "trendAnalysis": {"trendSummary": "steady growth", "growthRate": 5, "declineRate": 1}
  }
end note

== Scheduling Notifications ==
MA -> CSNA: Request optimal notification timing
note right of MA
  Payload JSON: {
    "userDemographics": {"userId": "UUID", "location": "Lyon", "age": 22},
    "currentMood": {"label": "motivated", "energyLevel": 80},
    "timePreferences": {"preferredStart": "08:00", "preferredEnd": "22:00"}
  }
end note
CSNA -> UM: Retrieve scheduling preferences and demographics
UM --> CSNA: Return user demographics and time preferences
CSNA --> MA: Return scheduled notifications and optimal intervention time
note right of CSNA
  JSON: {
    "scheduledNotifications": [ { 
      "notificationId": "N001", 
      "scheduledTime": "2025-03-01T09:00:00Z", 
      "content": "Time for your daily challenge!" 
    } ],
    "optimalInterventionTime": "2025-03-01T09:00:00Z"
  }
end note

== System Health Check ==
MA -> SMA: Send system logs and trust metrics
note right of MA
  Payload JSON: {
    "userFeedbackLogs": [ ... ],
    "systemLogs": [ ... ],
    "trustMetrics": {"currentTrustLevel": 85}
  }
end note
SMA -> HL: Query system logs and trust data
HL --> SMA: Return system status and log details
SMA --> MA: Return system health report
note right of SMA
  JSON: {
    "systemHealthStatus": {"status": "Healthy", "uptime": 99.9},
    "recommendedActions": []
  }
end note

== Mentor Agent Synthesis and User Response ==
MA -> U: Deliver composite message with guidance
note right of MA
  Payload JSON: {
    "message": "Today, take on the challenge: Build a mini greenhouse.",
    "actionRecommendation": "Spin the wheel and commit to the task.",
    "insightSummary": {"keyPoints": ["Breaking the cycle of abandoned projects", "Embrace small wins"]},
    "followUpQuestions": ["How do you feel about starting this challenge?"]
  }
end note
U -> MA: Acknowledge and proceed with activity selection

== Activity Execution and Logging ==
U -> MA: Confirm activity start and update journal entry
MA -> AWSA: Confirm activity selection and update progress
note right of MA
  Payload JSON: {
    "activityTailoredId": "AT123",
    "status": "In Progress",
    "timestamp": "2025-03-01T09:30:00Z"
  }
end note
AWSA -> HL: Log activity update (ActivityTailoredLog)
HL --> AWSA: Acknowledge log entry

@enduml