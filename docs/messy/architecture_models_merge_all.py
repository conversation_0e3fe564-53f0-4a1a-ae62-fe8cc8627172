import os
import re

os.chdir("./docs/model")

def resolve_includes(file_path, base_dir, included_files=None, show_hidden=False):
    """Recursively resolves !include directives in a PlantUML file while optionally removing [hidden] links."""
    if included_files is None:
        included_files = set()

    resolved_lines = []
    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            stripped_line = line.strip()

            # Remove any duplicate @startuml or @enduml from included files
            if stripped_line.lower().startswith("@startuml") or stripped_line.lower().startswith("@enduml"):
                continue

            # Match !include directives
            match = re.match(r"^\s*!include\s+(.+)", stripped_line)
            if match:
                include_path = match.group(1)
                include_file = os.path.normpath(os.path.join(base_dir, include_path))

                if include_file in included_files:
                    continue  # Avoid duplicate includes

                if os.path.exists(include_file):
                    included_files.add(include_file)
                    resolved_lines.append(f"' >>> Included from {include_file}")
                    resolved_lines.extend(resolve_includes(include_file, base_dir, included_files, show_hidden))
                    resolved_lines.append(f"' <<< End of {include_file}")
                else:
                    resolved_lines.append(f"' !!! Warning: Could not find {include_file}")
                    raise
            else:
                # Remove [hidden] from relationships if the user wants hidden links to be shown
                if show_hidden:
                    stripped_line = re.sub(r"\[hidden\]", "", stripped_line)

                resolved_lines.append(stripped_line)

    return resolved_lines

def generate_expanded_plantuml(input_file, output_file, show_hidden):
    """Generates a fully expanded PlantUML file while keeping only one @startuml/@enduml pair."""
    base_dir = os.path.dirname(os.path.abspath(input_file))
    resolved_content = resolve_includes(input_file, base_dir, show_hidden=show_hidden)

    # Ensure the original @startuml and @enduml are preserved only from the main file
    with open(input_file, "r", encoding="utf-8") as file:
        main_content = file.readlines()

    startuml = enduml = ""
    cleaned_main_content = []

    for line in main_content:
        stripped_line = line.strip()
        if stripped_line.lower().startswith("@startuml"):
            startuml = stripped_line  # Preserve the @startuml
        elif stripped_line.lower().startswith("@enduml"):
            enduml = stripped_line  # Preserve the @enduml
        else:
            cleaned_main_content.append(stripped_line)

    # Combine everything
    final_content = [startuml] + resolved_content + [enduml]

    with open(output_file, "w", encoding="utf-8") as file:
        file.write("\n".join(final_content))

    print(f"Expanded PlantUML file saved to: {output_file}")

if __name__ == "__main__":
    input_puml = "global.puml"  # Change this to your main PlantUML file
    output_puml = "entire_model_generated.puml"  # Output file

    # Ask user if they want to remove [hidden] from links
    #show_hidden = input("Do you want to show hidden links? (y/n): ").strip().lower() == "y"
    show_hidden = True
    generate_expanded_plantuml(input_puml, output_puml, show_hidden)
