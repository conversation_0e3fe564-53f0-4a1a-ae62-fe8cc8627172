@startuml

!theme vibrant
skinparam activity {
    BackgroundColor #F8F9FA
    BorderColor #495057
    ArrowColor #6C3483
    FontSize 12
}
skinparam partition {
    BackgroundColor #E8DAEF
    BorderColor #6C3483
    FontSize 14
    FontColor #333
}
skinparam diamond {
    BackgroundColor #FFFFF
    BorderColor #8E44AD
    FontColor #333333
    FontSize 12
}
skinparam note {
    BackgroundColor #FFF9C4
    BorderColor #FBC02D
    FontColor #333333
    FontSize 11
}

title Goali Beta Launch: Synthesized 8-Week Roadmap Activity Flow

start

partition "Phase 1: Inspiration, Definition & Prototyping (Weeks 1-3)" {
    :Begin Roadmap Initialization;
    note left
        **Focus:** Deep Dive & Concept Generation
        **Method:** Inspiration-Synthesis-Creation
        **AI Role:** Brainstorming Partner, Research Synthesis
    end note
    :Explore Inspirations (Brands, Philosophy, Art);
    :Define Core Emotional Targets;
    note right: Delightful Tension, Nurturing, Goali Joys, Companion Voice
    :Generate Initial Concepts (Wheel, Visuals, Joys, Voice);
    :Define MVP Scope & Exclusions;
    note left: Crucial for maintaining focus on core feelings
    :Develop Lo-Fi Prototypes;
    note right: Wheel Animation Concepts (Video/Interactive)\nNurturing Visual States (Static Mockups)\nJoy Card Templates\nCore Voice Copy Drafts
    :Internal Review & Alignment;
    note right: Founder + Trusted Advisors\nFocus on *feeling* and philosophical alignment
    :Check Technical Feasibility w/ Guillaume;
    note right: Especially for Nurturing Visuals & Wheel interactions
    :Refine Prototypes & Concepts based on Internal Feedback;
    :Develop Playful Hypothesis Validation Plan;
    note left: Target: Students\nFraming: Co-exploration, not testing
}

partition "Phase 2: Hypothesis Validation (Week 4)" {
    note right
        **Focus:** Validating Core Feelings & Concepts
        **Method:** Playful User Validation Sessions
        **AI Role:** Transcription, Qualitative Analysis Support
    end note
    :Recruit Target Users (Students, Early Contacts);
    :Conduct Moderated Validation Sessions (Remote);
    note left: Tasks focus ONLY on core experiences:\n- Onboarding (Voice)\n- First Wheel Spin (Tension)\n- Nurturing Visual Mockups Reaction\n- Goali Joy Term Resonance
    :Collect Qualitative Feedback;
    note right: Emphasize asking "How did that *feel*?"
    :Analyze Feedback for Emotional Resonance;
    note left: Look for patterns in language, validate/refute hypotheses\nAI assists summary, human confirms nuance
    if (Core Hypotheses Validated?) then (Yes)
        :Proceed to Iteration & Asset Dev;
    else (No / Partially)
        note right: Identify specific concepts needing rework
        :Feedback Loop: Return to Refinement in Phase 1/2;
        -> Phase 1 Partition;
        end
    endif
}

partition "Phase 3: Iteration & Asset Development (Weeks 5-6)" {
    note left
        **Focus:** Refining Core Experience & Building Assets
        **Method:** Synthesis & Creation based on Validation
        **AI Role:** Drafting, Variant Generation, Style Exploration
    end note
    :Synthesize Validation Findings;
    :Finalize Core Experience Specifications;
    note right: **Wheel Interaction:** Locked animation/sound/haptics\n**Nurturing Visuals:** Defined MVP scope & states\n**Companion Voice:** Finalized guide & core copy\n**Goali Joys:** Refined first batch, plan next
    :Synthesize & Define Go-Forward Brand Elements;
    note left: Likely Mischievous Companion focus,\ninfused with Goali Joys & visual style
    :Develop Hi-Fi Mockups (Core Beta Screens);
    :Draft V1 Style Guide;
    note right: Colors, Typography, Components, Voice Snippets\nEmphasis on "Simple Greatness"
    :Develop "Hidden Gem" Landing Page w/ Waitlist;
    :Produce Initial Marketing Content;
    note right: Focus on Goali Joys introduction\n(Shareable Cards, Blog/Substack Post)
}

partition "Phase 4: Community Building & Pre-Launch Prep (Weeks 6-7)" {
     note right
        **Focus:** Seeding Community & Preparing Launch Materials
        **Method:** Co-creation Framing (Goali Joys)
        **AI Role:** Content Generation, Comms Drafting
    end note
    :Launch & Seed Pre-Beta Community Platform;
    note left: Discord/Substack etc.\nFrame as co-creating the Dictionary of Goali Joys
    :Engage Early Community;
    note right: Share progress, ask philosophical questions,\npost initial Joy terms for discussion
    :Finalize Launch Communications Kit;
    note right: Emails (Waitlist, Beta Welcome)\nApp Store Descriptions (Companion Voice!)\nLaunch Announcement (Blog/Email)
    :Plan Beta Invitation Strategy;
    note left: "Hidden Gem" controlled rollout plan (Initial 100)
    :Plan Launch Week Community Activities;
    note right: Centered around Goali Joys & first experiences
}

partition "Phase 5: Final QA & Launch Readiness (Week 8)" {
     note left
        **Focus:** Ensuring Core Experience Quality & Building Buzz
        **Method:** Rigorous QA, Subtle Teaser Campaign
        **AI Role:** QA Support (e.g., reviewing copy consistency)
    end note
    :Prepare Final UX Assets for Handoff;
    :Support Technical Implementation (Guillaume);
    :Conduct Rigorous Core Experience QA;
    note right: Test Focus:\n1. Wheel Interaction FEELING\n2. Nurturing Visual Trigger/Transition\n3. Voice Consistency\n4. Onboarding Clarity
    :Log & Prioritize Bugs (Focus on Core Experience);
    :Execute Pre-Launch Teaser Campaign;
    note left: Subtle hints, daily Goali Joy reveals?\nTargeted community engagement
    :Manage Waitlist & Prepare Invites;
    :Final Founders' Go/No-Go Review;
    note right: Assess QA status, technical readiness,\ncore experience feeling against goals
    if (Ready for Beta Launch?) then (Yes)
      :Beta Launch Readiness Achieved!;
      stop
    else (No)
      :Identify Blocking Issues;
      :Feedback Loop: Return to QA / Dev Support;
      -> Phase 5 Partition;
       end
    endif
}

stop

@enduml