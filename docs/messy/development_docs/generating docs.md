### code docstrings
that's the structure to respect in order to generate clean docs :
```python
class HistoryLog(models.Model):
    """
    Stores all recorded events related to system interactions.

    Attributes:
        id (UUIDField): Primary key, automatically generated.
        aggregateType (CharField): Type of entity triggering the event.
            Example: "UserAction"
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    aggregateType = models.CharField(max_length=255, help_text="Type of entity that triggered the event.")
```

### generating model documentation
```python
python manage.py generate_model_docs --apps main user activity
```
and you'll get the result in /docs/models.md