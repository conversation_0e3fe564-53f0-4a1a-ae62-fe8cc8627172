# Game of Life Onboarding Template

## Purpose and Overview

This template provides a structured framework for collecting comprehensive user data during the onboarding process. It defines the essential information required to build a complete user profile that can drive personalized activity recommendations in the Game of Life application.

## Template Structure and Data Model Mapping

### 1. Environmental Context and Resources
**Data Models:** `UserEnvironment`, `UserEnvironmentPhysicalProperties`, `UserEnvironmentSocialContext`, `UserEnvironmentActivitySupport`, `UserEnvironmentPsychologicalQualities`, `UserResource`, `Inventory`  
**Processing Agent:** Resource & Capacity Agent

#### 1.1 Living Environment
- Physical location description (urban/rural, type of dwelling)
- Environmental characteristics (noise, light, temperature, space)
- Privacy level and social density
- Daily accessibility to different environments
- Environmental constraints and limitations

#### 1.2 Available Resources
- Physical resources and equipment
- Digital tools and connectivity
- Time availability patterns (weekdays, weekends, specific times)
- Financial resource constraints
- Access to community resources

#### 1.3 Environmental Support for Activities
- Spaces available for different activity types
- Natural elements present (plants, water, outdoor areas)
- Activity timing constraints based on environment
- Domains well-supported by current environment
- Domains poorly supported by current environment

#### 1.4 Environmental Psychological Impact
- User's emotional response to their environment
- Restorative qualities of different spaces
- Comfort level in different environments
- Personal significance of environmental elements
- Environmental factors affecting motivation or focus

### 2. Skills and Capabilities
**Data Models:** `Skill`, `GenericSkill`, `UserLimitation` (physical)  
**Processing Agent:** Resource & Capacity Agent

#### 2.1 Existing Skills
- Technical skills and proficiency levels
- Creative skills and experience
- Physical/athletic abilities
- Social and communication skills
- Organizational and planning skills
- Language and cognitive skills

#### 2.2 Skill Enjoyment and Awareness
- Skills user enjoys using
- Skills user is aware of but underutilizes
- Skills user wants to develop further
- Self-assessment accuracy for different skill domains

#### 2.3 Physical Capabilities and Limitations
- Physical energy patterns throughout day
- Physical endurance for different activities
- Mobility or dexterity constraints
- Health-related activity limitations
- Sensory processing considerations

### 3. Engagement Patterns and Preferences
**Data Models:** `Preference`, Agent Memory (engagement patterns)  
**Processing Agent:** Engagement & Pattern Analytics Agent

#### 3.1 Domain Preferences
- Activity domains user naturally gravitates toward
- Activity domains user consistently avoids
- Historical engagement with different domains
- Preference strength across domains
- Domain-specific engagement patterns

#### 3.2 Temporal Patterns
- Optimal times of day for different activities
- Energy flow throughout typical day
- Weekly rhythm and engagement patterns
- Duration preferences for different activities
- Sequential preferences (activity ordering)

#### 3.3 Activity Format Preferences
- Structure preferences (guided vs. open-ended)
- Social context preferences (solo vs. collaborative)
- Complexity preferences (simple vs. multifaceted)
- Creative vs. analytical engagement preferences
- Physical vs. digital modality preferences

#### 3.4 Activity Completion Patterns
- Historical follow-through on commitments
- Abandonment patterns and typical timing
- Completion enablers (what helps user finish)
- Obstacles that typically prevent completion
- Recovery patterns after interruptions

#### 3.5 Reward and Motivation Patterns
- Activities that function as intrinsic rewards
- External motivators that drive engagement
- Feedback preferences and response patterns
- Achievement recognition preferences
- Intrinsic vs. extrinsic motivation balance

### 4. Psychological Profile
**Data Models:** `Demographics`, `UserTraitInclination`, `CurrentMood`  
**Processing Agent:** Psychological Monitoring Agent

#### 4.1 Demographics
- Basic information (age, gender, location)
- Occupational context
- Living arrangements
- Cultural background
- Language preferences

#### 4.2 HEXACO Trait Assessment
**Honesty-Humility:**
- Sincerity (genuineness in self-expression)
- Fairness (tendency to avoid exploiting others)
- Greed Avoidance (interest in status/luxury)
- Modesty (humility about achievements)

**Emotionality:**
- Fearfulness (response to potential dangers)
- Anxiety (worry in various contexts)
- Dependence (need for emotional support)
- Sentimentality (emotional bonding tendencies)

**eXtraversion:**
- Social Self-Esteem (social confidence)
- Social Boldness (comfort in social situations)
- Sociability (enjoyment of social gatherings)
- Liveliness (energy in social contexts)

**Agreeableness:**
- Forgiveness (willingness to trust after harm)
- Gentleness (mildness in interactions)
- Flexibility (willingness to compromise)
- Patience (tendency to remain calm vs. anger)

**Conscientiousness:**
- Organization (seeking order and structure)
- Diligence (work ethic and persistence)
- Perfectionism (concern with details/accuracy)
- Prudence (deliberate decision-making)

**Openness to Experience:**
- Aesthetic Appreciation (enjoyment of beauty)
- Inquisitiveness (exploring new ideas)
- Creativity (preference for innovation)
- Unconventionality (challenge to tradition)

#### 4.3 Current Psychological State
- Present mood and emotional state
- Energy levels (mental, emotional, physical)
- Stress and anxiety levels
- Current focus of attention
- Recent emotional experiences

### 5. Belief System and Self-Concept
**Data Models:** `Belief`, `BeliefEvidence`, `BeliefInfluence`  
**Processing Agent:** Psychological Monitoring Agent

#### 5.1 Core Beliefs
- Self-beliefs (identity, capabilities, worth)
- World-beliefs (how the world functions)
- Value-beliefs (what matters most)
- Relationship-beliefs (how relationships work)
- Growth-beliefs (potential for change)

#### 5.2 Belief Evidence
- Experiences supporting key beliefs
- Counterexamples challenging key beliefs
- Sources of influential beliefs
- Confidence level in different beliefs
- Emotional charge of different beliefs

#### 5.3 Limiting Beliefs
- Self-doubt patterns
- Vulnerability areas
- Avoidance justifications
- Internal contradictions
- Areas of cognitive dissonance

#### 5.4 Empowering Beliefs
- Sources of confidence
- Resilience-building beliefs
- Success attributions
- Positive identity elements
- Growth mindset indicators

### 6. Goals and Aspirations
**Data Models:** `UserGoal`, `Aspiration`, `Intention`, `Inspiration`, `GoalInspiration`  
**Processing Agent:** Psychological Monitoring Agent & Strategy Agent

#### 6.1 Long-term Aspirations
- Life vision elements
- Career/professional aspirations
- Personal development aspirations
- Relationship aspirations
- Lifestyle aspirations

#### 6.2 Short-term Intentions
- Current active goals
- Projects in progress
- Habits being developed
- Skills being learned
- Near-term desired outcomes

#### 6.3 Goal Importance
- Self-rated importance of different goals
- Motivation behind different goals
- External vs. internal goal origins
- Time horizon for different goals
- Resource investment in different goals

#### 6.4 Inspirational Sources
- People who inspire the user
- Media that provides motivation
- Experiences that sparked goals
- Values driving aspirations
- Vision of future self

#### 6.5 Goal-Trait Alignment
- Traits supporting goal achievement
- Traits potentially hindering goals
- Skills needed for goal progress
- Environmental factors affecting goals
- Belief support/interference with goals

### 7. Trust and Psychological Safety
**Data Models:** `TrustLevel`, `UserLimitation` (psychological)  
**Processing Agent:** Psychological Monitoring Agent

#### 7.1 Trust Indicators
- Comfort with uncertainty
- Historical trust in guidance systems
- Self-disclosure patterns
- Follow-through on commitments
- Reaction to structure vs. freedom

#### 7.2 Psychological Limitations
- Anxiety-related constraints
- Trauma-related sensitivities
- Fear-based limitations
- Cognitive processing constraints
- Emotional regulation challenges

#### 7.3 Safety Requirements
- Communication preferences
- Challenge tolerance thresholds
- Feedback sensitivity areas
- Support requirements
- Recovery mechanisms

## HEXACO Trait Assessment Methodology

### Deriving HEXACO Traits from User Responses

#### 1. Direct Question Mapping
Each HEXACO subfacet can be assessed through multiple question types:

- **Behavioral scenarios**: "When faced with X situation, what would you typically do?"
- **Self-descriptions**: "How would you describe your tendency to..."
- **Past behavior examples**: "Tell me about a time when you..."
- **Preference patterns**: "How do you feel about situations where..."

#### 2. Response Analysis Framework
For each subfacet, analyze responses across three dimensions:

1. **Frequency**: How often user exhibits trait-related behaviors
2. **Intensity**: How strongly trait is expressed when present
3. **Consistency**: How reliable trait expression is across contexts

#### 3. Scoring Methodology
Convert qualitative responses to quantitative scores (0-100):

- **Low (0-30)**: Trait rarely expressed, low intensity, inconsistent
- **Moderate (31-70)**: Trait variably expressed, context-dependent
- **High (71-100)**: Trait frequently expressed, high intensity, consistent

#### 4. Behavioral Indicators by Trait

**Honesty-Humility:**
- Sincerity: Look for transparency about motives, authenticity in self-description
- Fairness: Assess ethics in described past behaviors, hypothetical scenarios
- Greed Avoidance: Note attitudes toward wealth, status, luxury items
- Modesty: Observe self-presentation style, comfort with recognition

**Emotionality:**
- Fearfulness: Note described responses to risks, changes, uncertainties
- Anxiety: Look for worry patterns, contingency planning, overthinking
- Dependence: Assess described reliance on others for emotional support
- Sentimentality: Note emotional responses to others' experiences, attachment descriptions

**eXtraversion:**
- Social Self-Esteem: Observe confidence in social self-descriptions
- Social Boldness: Note comfort with leadership, speaking up, social initiative
- Sociability: Assess preference for social vs. solitary activities
- Liveliness: Look for energy descriptions in social contexts

**Agreeableness:**
- Forgiveness: Note described responses to slights, betrayals, disagreements
- Gentleness: Assess tone in critiques of others, judgment patterns
- Flexibility: Note compromise willingness in conflict descriptions
- Patience: Look for frustration tolerance, anger response patterns

**Conscientiousness:**
- Organization: Note described tidiness, scheduling, and planning behaviors
- Diligence: Assess work ethic descriptions, effort persistence
- Perfectionism: Look for attention to detail, quality standards
- Prudence: Note impulse control, deliberation before actions

**Openness to Experience:**
- Aesthetic Appreciation: Assess described enjoyment of arts, nature, beauty
- Inquisitiveness: Note curiosity patterns, information-seeking behaviors
- Creativity: Look for innovation preferences, unconventional thinking
- Unconventionality: Assess tradition adherence vs. norm-challenging

#### 5. Evidence Triangulation
Strengthen assessment validity by triangulating evidence:

1. **Self-reported tendencies**: What user says about themselves
2. **Behavioral examples**: What user has actually done
3. **Preference patterns**: What user enjoys or avoids
4. **Engagement history**: Past activity completion patterns

#### 6. Confidence Rating Calculation
For each trait assessment, calculate confidence (0-100):

- +20: Multiple consistent behavioral examples
- +15: Explicit self-description matching behaviors
- +15: Consistent preference patterns
- +10: High specificity in descriptions
- +10: Low contradictions across contexts
- -15: Significant contradiction between statements and behaviors
- -10: Vague or generic descriptions
- -10: Limited examples or context
- -5: Socially desirable responding patterns

## User Profile Quality Benchmarks

### Minimum Viable Profile (Foundation Phase)

1. **Environmental Context:** Complete `UserEnvironment` with basic properties
2. **Resource Assessment:** At least 3 `UserResource` records
3. **Skill Documentation:** At least 3 `Skill` records across different domains
4. **Preference Mapping:** At least 3 domain and 2 format `Preference` records
5. **Trait Assessment:** All 6 HEXACO domains with at least 50% confidence
6. **Belief Documentation:** At least 3 core `Belief` records with evidence
7. **Goal Documentation:** At least 1 `Aspiration` and 2 `Intention` records
8. **Trust Establishment:** Initial `TrustLevel` with foundation phase determination

### Comprehensive Profile (Expansion Phase)

1. **Environmental Context:** Detailed environment with psychological qualities
2. **Resource Assessment:** 5+ resources with detailed inventory
3. **Skill Documentation:** 5+ skills with proficiency and enjoyment ratings
4. **Preference Mapping:** Comprehensive preferences across all assessment areas
5. **Trait Assessment:** All 24 HEXACO subfacets with 70%+ confidence
6. **Belief Documentation:** 5+ beliefs with multiple evidence connections
7. **Goal Documentation:** 3+ aspirations and 5+ intentions with inspiration links
8. **Trust Establishment:** Trust level with component metrics and phase determination
9. **Limitation Documentation:** Both physical and psychological limitations documented

## Application Guidance for User Engagement

### Engagement Principles

1. **Progressive Disclosure**
   - Begin with lighter, less personal questions
   - Gradually introduce deeper questions after establishing rapport
   - Alternate between easy and more reflective questions

2. **Narrative Elicitation**
   - Ask for stories rather than direct self-assessment
   - Use "tell me about a time when..." rather than "are you..."
   - Focus on specific examples rather than generalizations

3. **Contextual Inference**
   - Derive traits and beliefs from behavior descriptions
   - Infer preferences from engagement patterns
   - Extract limitations from obstacle descriptions

4. **Multimodal Assessment**
   - Combine direct questions with behavioral scenarios
   - Incorporate interactive elements (like sorting exercises)
   - Allow for both structured responses and free expression

### Questionnaire Design Guidelines

1. **Framing Questions Indirectly**
   - **Instead of:** "How conscientious are you?"
   - **Try:** "Describe your approach to planning a complex project."

2. **Using Behavioral Scenarios**
   - **Instead of:** "Do you enjoy social activities?"
   - **Try:** "When you have free time on a weekend, what kinds of activities do you typically choose?"

3. **Leveraging Past Experience**
   - **Instead of:** "Are you persistent?"
   - **Try:** "Tell me about a time when you faced a significant obstacle while pursuing a goal. What happened?"

4. **Exploring Patterns Over Time**
   - **Instead of:** "Do you procrastinate?"
   - **Try:** "Think about the last few projects you started. How did the beginning, middle, and end phases typically go?"

5. **Using Relative Comparisons**
   - **Instead of:** "How much do you like creative activities?"
   - **Try:** "If you had to choose between a creative project, a physical activity, or a social gathering, which would you typically prefer and why?"

### Sequencing and Pacing Recommendations

1. **Initial Focus:** Environment, basic demographics, and preferences
2. **Middle-stage:** Skills, experiences, and behavioral patterns
3. **Later Stage:** Goals, beliefs, and psychological insights
4. **Final Elements:** Limitations, challenges, and deep motivations

### Adapting for Different Trust Phases

1. **Early Foundation Phase:**
   - Prioritize simple, low-disclosure questions
   - Focus heavily on preference mapping
   - Use more structured, less open-ended formats

2. **Late Foundation Phase:**
   - Introduce moderate self-reflection questions
   - Begin goal and aspiration exploration
   - Incorporate narrative elements

3. **Expansion Phase:**
   - Explore contradictions and nuances
   - Delve into belief systems and limitations
   - Encourage deeper self-reflection

### Continuous Profile Enhancement

1. **Post-Activity Insights:** Update profile based on activity completion patterns
2. **Progressive Profiling:** Spread data collection across multiple sessions
3. **Contextual Questions:** Time specific questions based on user's recent experiences
4. **Feedback Integration:** Incorporate explicit feedback on profile accuracy