# User Profile Benchmark Structure

Based on our extensive development work on the agent workflow and onboarding process, I recommend the following structure for user profile benchmarks to establish clear standards for profile quality and completeness.

## 1. Core Component Benchmarks with Weighted Significance

### Foundational Profile (15%)
- **Basic Demographic Data** (100% required)
  * Name, age, location, occupation
  * Language preferences and cultural context
  * Complete demographic records

- **Communication Framework** (85% minimum)
  * Preferred communication styles
  * Response to different tones and approaches
  * Metaphor and example preferences
  * Record of effective engagement patterns

- **Personal Narrative Context** (70% minimum)
  * Life stage and key transitions
  * Formative experiences
  * Current chapter framing
  * Consistent storyline verification

### Resource & Capacity Assessment (15%)
- **Environmental Context** (90% minimum)
  * Primary living/working environments
  * Environmental characteristics and constraints
  * Access to spaces by domain (creative, social, etc.)
  * Environmental preference indicators

- **Resource Inventory** (80% minimum)
  * Available tools and equipment
  * Digital resources and access
  * Financial resource parameters
  * Verification of resource reliability

- **Temporal Availability** (85% minimum)
  * Daily rhythm and energy patterns
  * Weekly availability structure
  * Season-specific variations
  * Recovery and processing time needs

- **Capability Profile** (75% minimum)
  * Skill inventory with proficiency ratings
  * Physical capability parameters
  * Limitation documentation with adaptations
  * Skill development priorities

### Psychological Framework (20%)
- **HEXACO Trait Profile** (90% minimum)
  * Complete assessment of all six dimensions
  * Facet-level trait measurement
  * Trait stability indicators
  * Trait interaction patterns
  * Evidence validation for each trait

- **Belief System Mapping** (80% minimum)
  * Core belief inventory with strength ratings
  * Evidence documentation for key beliefs
  * Belief influence mapping
  * Belief modification history
  * Limiting belief identification

- **Emotional Pattern Recognition** (75% minimum)
  * Emotional response patterns by context
  * Trigger identification and mapping
  * Processing and regulation strategies
  * Emotional awareness assessment

- **Growth Edge Documentation** (70% minimum)
  * Comfort zone mapping by domain
  * Challenge response history
  * Growth opportunity prioritization
  * Evidence of previous growth responses

### Engagement & Behavioral Patterns (15%)
- **Domain Engagement History** (85% minimum)
  * Completion rates by activity domain
  * Engagement pattern stability
  * Domain preference strength
  * Domain avoidance documentation

- **Flow State Facilitators** (70% minimum)
  * Documented flow-producing activities
  * Optimal challenge parameters for flow
  * Focus duration capabilities
  * Flow-to-frustration boundaries

- **Resistance Pattern Documentation** (75% minimum)
  * Avoidance behavior mapping
  * Justification pattern recognition
  * Resistance triggers by domain
  * Evidence quality for pattern identification

- **Novelty-Familiarity Balance** (70% minimum)
  * Novelty tolerance assessment
  * Familiarity needs by context
  * Optimal exploration parameters
  * Domain-specific novelty preferences

### Goal Framework & Aspirations (15%)
- **Aspiration Documentation** (90% minimum)
  * Long-term aspiration inventory
  * Aspiration intensity and priority
  * Aspiration stability assessment
  * Aspiration-belief alignment

- **Intention Mapping** (85% minimum)
  * Short-term intention inventory
  * Intention commitment strength
  * Implementation planning quality
  * Historical follow-through assessment

- **Goal-Trait Alignment** (80% minimum)
  * Goal-supporting trait mapping
  * Goal-hindering trait identification
  * Trait development priorities
  * Goal-trait gap analysis

- **Value System Coherence** (75% minimum)
  * Value hierarchy documentation
  * Value-goal consistency
  * Value conflict identification
  * Evidence of value expression

### Trust & Challenge Calibration (10%)
- **Trust Phase Determination** (95% minimum)
  * Current trust phase with evidence
  * Trust development trajectory
  * Trust recovery patterns
  * Phase transition indicators

- **Challenge Response History** (80% minimum)
  * Challenge response by domain
  * Optimal challenge parameters
  * Recovery pattern documentation
  * Challenge progression readiness

- **Resilience Assessment** (75% minimum)
  * Resilience indicators by challenge type
  * Recovery timeline patterns
  * Support system utilization
  * Resilience development priorities

### Ethical Alignment (10%)
- **Autonomy Protection Framework** (95% minimum)
  * Consent parameters and boundaries
  * Autonomy-supportive approach documentation
  * Manipulation vulnerability assessment
  * Decision ownership preferences

- **Well-being Safeguards** (90% minimum)
  * Vulnerability indicators with thresholds
  * Recovery need documentation
  * Challenge safety parameters
  * Support system mapping

- **Value Alignment Verification** (85% minimum)
  * Ethical boundary documentation
  * Value expression preferences
  * Activity alignment filters
  * Potential conflict flagging

## 2. Quality Dimension Benchmarks

### Completeness Metrics
- **Fundamental Completeness** (90% minimum)
  * Critical data fields present across all components
  * Minimum thresholds met for each component
  * No critical gaps in core dimensions

- **Comprehensive Coverage** (75% minimum)
  * Complete profile across all dimensions
  * Granular detail in priority areas
  * Appropriate depth based on trust phase

### Coherence Assessment
- **Internal Consistency** (85% minimum)
  * Alignment between related data points
  * Consistency between stated and observed preferences
  * Coherent narrative across dimensions
  * Goal-trait-belief alignment

- **Temporal Stability** (70% minimum)
  * Consistent data across time periods
  * Pattern stability in core traits
  * Documented evolution of changing elements
  * Flagging of high-variability components

### Evidence Quality
- **Direct Evidence Validation** (85% minimum)
  * Clear documentation of direct user statements
  * Verification through multiple inputs
  * Context preservation for quotes
  * Recency of direct evidence

- **Synthetic Deduction Confidence** (70% minimum)
  * Confidence ratings for all inferences
  * Multiple evidence points for key deductions
  * Transparent inference logic
  * User validation of critical synthetics

### Implementation Readiness
- **Activity Generation Support** (85% minimum)
  * Sufficient detail for activity tailoring
  * Clear challenge calibration parameters
  * Domain distribution guidance
  * Resource-activity matching capability

- **Growth Strategy Enablement** (80% minimum)
  * Clear development priorities
  * Challenge progression pathways
  * Goal achievement scaffolding
  * Belief-activity influence mapping
