@startuml

!theme vibrant
skinparam activity {
    BackgroundColor #FFFFFF
    BorderColor #000000
    ArrowColor #000000
}
skinparam partition {
    BackgroundColor #FFFFFF
    BorderColor #000000
}
skinparam diamond {
    BackgroundColor #FFFFFF
    BorderColor #000000
    FontColor #000000
}
skinparam note {
    BackgroundColor #FFFFFF
    BorderColor #FFFFFF
    FontColor #00000
}

title Backend Message Processing Flow (Dispatcher Focus)

start

partition "UserSessionConsumer" {
    :Receive WebSocket Message;
    :Parse JSON;
    if (Validate Message Type & Content?) then (Valid)
        if (Message Type?) then (chat_message)
            :Call handle_chat_message();
            note right: Passes message content
            -> Dispatcher Entry;
        elseif (spin_result)
            :Call handle_spin_result();
            note right: Passes message content\n(incl. spin_result metadata)
            -> Dispatcher Entry;
        elseif (workflow_status_request)
            :Call handle_workflow_status_request();
            stop
        else (Unknown Type)
            :Send Error Response (to Client);
            stop
        endif
    else (Invalid)
        :Send Error Response (to <PERSON>lient);
        stop
    endif
}

partition "ConversationDispatcher.process_message" {
    :Dispatcher Entry;
    if (1. Check Metadata?\n(requested_workflow / spin_result)) then (Yes - Explicit Intent)
        :4a. Determine Workflow (from Metadata);
    else (No)
        if (2. Check Profile?\n(<50% complete)) then (Yes - Incomplete)
            :4b. Determine Workflow (user_onboarding);
        else (No - Complete)
            :3. Workflow History Log Request;
            if (3a. Try LLM Classification) then (Success)
                :4c. Determine Workflow (from LLM);
            else (Failure / Unavailable)
                :4d. Rule Classification;
            endif
        endif
    endif

    :5. Build Context Packet;
    if (6. Action Required?\n(e.g., missing info)) then (Yes)
        :7a. Determine Workflow (discussion);
        note right: Target: discussion\n(to collect info)
        :7b. Launch Async Workflow (Celery Task);
        :Trigger execute_graph_workflow (discussion);
        :Record Workflow Initiation;
        :8. Return Initial Response (to Consumer);
        note right
            Discussion workflow launched
            asynchronously.
        end note
        stop
    else (No - Proceed with classified workflow)
        :7b. Launch Async Workflow (Celery Task);
        if (Workflow Type Determined in Step 4?) then (wheel_generation)
            :Trigger execute_graph_workflow (wheel_generation);
        elseif (post_spin)
            :Trigger execute_graph_workflow (post_spin);
        elseif (user_onboarding)
            :Trigger execute_graph_workflow (user_onboarding);
        elseif (activity_feedback)
            :Trigger execute_graph_workflow (activity_feedback);
        elseif (pre_spin_feedback)
             note left: Triggered by classification\nif reviewing wheel
            :Trigger execute_graph_workflow (pre_spin_feedback);
        elseif (discussion)
             note left: Triggered by classification\nif unclear/general chat
            :Trigger execute_graph_workflow (discussion);
        else (Unknown/Error)
            :Log Error;
            :Send Error Response (to Client);
             stop
        endif
        :Record Workflow Initiation;
        :8. Return Initial Response (to Consumer);
        note right
            Target workflow launched
            asynchronously.
        end note
        stop
    endif
}

stop

@enduml
