@startuml
' Essential layout directives - using dot engine with forced vertical layout
!pragma layout dot
top to bottom direction
' Basic styling
skinparam padding 2
skinparam backgroundColor transparent
skinparam roundCorner 15
skinparam ArrowColor black
skinparam linetype polyline
skinparam ranksep 80
skinparam nodesep 40
skinparam DefaultTextAlignment center
skinparam wrapWidth 120
skinparam maxMessageSize 60

' Core actor and agents
actor "<PERSON>" as User
rectangle "Mentor Agent" as Mentor #FFCCE5
rectangle "Orchestrator Agent" as Orchestrator #CCE5FF

' Position specialized agents in a single row
together {
  rectangle "Resource & Capacity\nAgent" as Resource #FFE5CC
  rectangle "Engagement & Pattern\nAnalytics Agent" as Engagement #FFE5CC
  rectangle "Psychological\nMonitoring Agent" as Psych #E5CCFF
  rectangle "Strategy Agent" as Strategy #FFE5CC
  rectangle "Wheel/Activity\nAgent" as Wheel #FFE5CC
  rectangle "Ethical Oversight\nAgent" as Ethics #E5CCFF
}

' Vertical structure guidance for agents
User -[hidden]d-> Mentor
Mentor -[hidden]d-> Orchestrator
Orchestrator -[hidden]d-> Resource

' Keep specialized agents on same level with tighter spacing
Resource -[hidden]r-> Engagement
Engagement -[hidden]r-> Psych
Psych -[hidden]r-> Strategy
Strategy -[hidden]r-> Wheel
Wheel -[hidden]r-> Ethics

' Core vertical relationships for agents
User -d-> Mentor: "provides context"
Mentor -d-> Orchestrator: "2. context packet"

' Clear orchestration flow
Orchestrator -d-> Resource: ""
Orchestrator -d-> Engagement: ""
Orchestrator -d-> Psych: ""
Orchestrator -d-> Strategy: ""
Orchestrator -d-> Wheel: ""
Orchestrator -d-> Ethics: ""

' Return connections to Orchestrator
Resource -u-> Orchestrator: "resource context"
Engagement -u-> Orchestrator: "engagement patterns"
Psych -u-> Orchestrator: "psychological insights"
Strategy -u-> Orchestrator: "strategy framework"
Wheel -u-> Orchestrator: "wheel object"
Ethics -u-> Orchestrator: "ethical approval"

' Final output
Orchestrator -u-> Mentor: "8. final wheel object"
Mentor -u-> User: "9. presents wheel"

' Essential notes
note right of Orchestrator
  1. First Analysis
  2. Synthesis + Decision
  3. Generation + Validation
  4. Presentation
end note

' Grouping psychology data above
rectangle "Psychology & Goals Data" as PsychGroup #FFFFFF33 {
  together {
    database "Traits & Beliefs" as TraitsBelief #F0F0F0
    database "Goals, Aspirations & Inspirations" as GoalsAsp #F0F0F0
  }
}

' History data above
together {
  database "HistoryLog" as HistoryLog #F0F0F0
  database "UserFeedbackLog" as FeedbackLog #F0F0F0
}

' Activity data on right side
together {
  database "GenericActivity" as GenActivity #F0F0F0
  database "ActivityTailored" as TailoredActivity #F0F0F0
}

' Grouping resource data below agents
rectangle "Environment & Resources Data" as ResGroup #FFFFFF33 {
  together {
    database "Environment Context & Inventory" as EnvContext #F0F0F0
    database "Resources & Capabilities" as ResCapabilities #F0F0F0
  }
}

' Forcing vertical positioning
Resource -[hidden]d-> ResGroup
PsychGroup -[hidden]d-> Orchestrator

' Data connections to agents based on agent story
HistoryLog --> Mentor: "provides history"
FeedbackLog --> Mentor: "user feedback"
HistoryLog --> Engagement: "analyzes patterns"
FeedbackLog --> Engagement: "analyzes feedback"

EnvContext --> Resource: "environmental + resource analysis"
ResCapabilities --> Resource: "capability assessment"

TraitsBelief --> Psych: "trait assessment"
GoalsAsp --> Psych: "goal analysis"
GoalsAsp --> Strategy: "goal integration"

GenActivity --> Wheel: "activity selection"
Wheel --> TailoredActivity: "creates tailored activities"
Ethics --> HistoryLog: "logs concerns"

' Key data flow to final output
TailoredActivity -u-> Wheel: "combined into wheel"

' Additional logic flows
TraitsBelief --> Strategy: "challenge calibration"
FeedbackLog --> Psych: "trust assessment"
TailoredActivity --> Ethics: "reviews activities"

@enduml