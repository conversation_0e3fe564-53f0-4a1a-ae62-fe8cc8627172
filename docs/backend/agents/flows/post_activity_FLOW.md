# Post-Activity Agent Workflow

*Last Updated: 2025-04-24*

## Mentor Agent

**Entry Point & Input:**
- Trigger: User returns to app after previously accepting a wheel activity
- Input: ActivityTailored object, user interaction indicators, timestamp data

**Data Access:**
- Read: HistoryLog, UserFeedbackLog, User<PERSON>ro<PERSON>le, Aspirations
- Write: None (creates feedback report only)
- Recommend: None (collection agent only)
- Memory Access:
  - Read: Communication preferences, conversation tracking
  - Write: Updated conversation topics, effective approaches

**Core Responsibilities:**
- Collect comprehensive user experience data:
  - Gather narrative description of activity experience
  - Record obstacles encountered during activity
  - Document perceived value and satisfaction level
  - Capture perceived challenge level of activity
- Assess future engagement context:
  - Collect explicit feedback on system recommendation quality
  - Gather information on current interests and goals
  - Directly measure current trust in the system
  - Identify shifts in aspirations or self-perception
- Provide validation and acknowledgment:
  - Connect progress to stated aspirations
  - Frame challenges as learning opportunities
  - Inform user that feedback will improve future recommendations
  - Use trust-enhancing communication patterns

**Processing Logic:**
1. Pre-inquiry preparation:
   - Retrieve recent HistoryLog entries
   - Review UserFeedbackLog to avoid repetitive questioning
   - Determine appropriate communication approach

2. Experience data collection:
   - Structure questions to gather qualitative experience narrative
   - Assess satisfaction and perceived value
   - Evaluate challenge level appropriateness

3. Context gathering:
   - Capture shifts in interests, trust, and aspirations
   - Identify unexpected insights or realizations

4. Feedback packaging:
   - Structure all collected information into standardized format
   - Prepare comprehensive feedback report for Orchestrator

**Expected Output Benchmark:**
- Structured feedback report containing:
  - Experience section with narrative, obstacles, and perceived value
  - Satisfaction metrics with both qualitative and quantitative indicators
  - Challenge assessment with appropriateness evaluation
  - Trust measurement with current level and change indicators
  - Aspiration update section with shifts in goals or self-perception
  - System feedback section with recommendation quality assessment
- All outputs must meet these quality standards:
  - Clear separation between user statements and agent interpretations
  - Comprehensive coverage of all required feedback dimensions
  - Objective recording without premature analysis
  - Proper formatting for downstream agent processing

**Output & Next Step:**
- To Orchestrator Agent: Complete structured feedback report with all collected user experience data

## Orchestrator Agent

**Entry Point & Input:**
- Trigger: Receipt of structured feedback report from Mentor Agent
- Input: Feedback report containing experience narrative, satisfaction metrics, challenge assessment, trust measurements, aspiration updates

**Data Access:**
- Read: HistoryLog, UserFeedbackLog, pending Update Recommendations
- Write: None (coordinates only)
- Recommend: None (integration agent)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Analyze feedback nature and determine appropriate workflow:
  - Distinguish between standard follow-up and significant psychological change
  - Identify simple updates versus deeper analysis requirements
- Design sequential agent workflow based on feedback complexity:
  - Determine logical agent dependencies based on feedback type
  - Create processing sequence with appropriate data handoffs
- Integrate feedback with historical context:
  - Incorporate relevant historical data for context
  - Add pending recommendations from previous scenarios
- Coordinate specialized agent analysis sequence:
  - Manage agent activation order based on analysis needs
  - Consolidate agent recommendations into coherent update plan
- Resolve conflicts between agent recommendations:
  - Apply resolution priorities when recommendations conflict
  - Implement evidence weighting for contradictory updates

**Processing Logic:**
1. Feedback classification:
   - Categorize feedback complexity level
   - Determine analysis depth requirements

2. Context integration:
   - Retrieve relevant historical data points
   - Incorporate pending recommendations

3. Workflow design:
   - Structure agent activation sequence
   - Create complete context package for specialized agents

4. Recommendation processing:
   - Collect all specialized agent recommendations
   - Identify conflicts between recommendations
   - Apply resolution framework to conflicts
   - Create finalized update instructions

**Expected Output Benchmark:**
- Sequential workflow package containing:
  - Agent activation sequence with dependencies
  - Comprehensive context data with historical references
  - Feedback categorization with analysis requirements
- Consolidated update plan containing:
  - Resolved recommendations for all data model elements
  - Conflict resolution explanations where applicable
  - Implementation priorities with justifications
- All outputs must meet these quality standards:
  - Logical agent sequencing that respects dependencies
  - Complete context package for downstream agents
  - Clear resolution rationale for conflicting recommendations

**Output & Next Step:**
- To Resource & Capacity Agent: Complete context package with feedback report
- After all agent analyses: Integrated update plan to data model

## Resource & Capacity Agent

**Entry Point & Input:**
- Trigger: Receipt of context package from Orchestrator Agent
- Input: Feedback report, historical context, pending recommendations

**Data Access:**
- Read: UserResource, UserProfile, HistoryLog
- Write: None
- Recommend: UserResource updates, UserProfile capability adjustments
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Evaluate activity completion impact on user resources:
  - Identify changes in available time, energy, or environmental resources
  - Assess shifts in resource allocation patterns
- Detect new capabilities or limitations:
  - Identify skill development from completed activities
  - Recognize resource constraints that affected activity experience
- Update resource availability understanding:
  - Refine time and resource allocation models based on activity evidence
  - Adjust capability assessments based on performance indicators

**Processing Logic:**
1. Resource impact assessment:
   - Analyze activity resource requirements
   - Compare expected versus actual resource utilization
   - Identify resource-related obstacles

2. Capability evaluation:
   - Extract skill utilization indicators from feedback
   - Assess performance against capability expectations
   - Identify capability gains or limitations

3. Recommendation formulation:
   - Create structured resource update recommendations
   - Specify capability adjustment recommendations
   - Provide supporting evidence for each recommendation

**Expected Output Benchmark:**
- Resource & Capacity Update Recommendation containing:
  - Resource modification section with specific parameter changes
  - Capability adjustment section with skill development indicators
  - Resource pattern section with allocation trend analysis
  - Supporting evidence section linking recommendations to feedback
- All outputs must meet these quality standards:
  - Clear connection between feedback and recommendations
  - Specific parameter adjustment values
  - Evidence-based justification for all recommendations

**Output & Next Step:**
- To Engagement & Pattern Analytics Agent: Context package with Resource & Capacity Update Recommendation

## Engagement & Pattern Analytics Agent

**Entry Point & Input:**
- Trigger: Receipt of context package from Resource & Capacity Agent
- Input: Feedback report, historical context, pending recommendations, Resource & Capacity Update Recommendation

**Data Access:**
- Read: HistoryLog, UserFeedbackLog, Preference
- Write: None
- Recommend: Preference updates, engagement pattern modifications
- Memory Access:
  - Read: Domain engagement metrics, pattern confidence scores, activity response patterns
  - Write: Updated engagement metrics and pattern confidence

**Core Responsibilities:**
- Compare new activity data with historical patterns:
  - Assess consistency with previous engagement patterns
  - Identify deviations from established preferences
- Validate feedback coherence:
  - Flag inconsistencies or contradictions in user reports
  - Identify potentially unreliable feedback
  - Determine if feedback represents genuine preference shift
- Update domain engagement and activity type preferences:
  - Refine understanding of domain preferences
  - Update activity type engagement patterns

**Processing Logic:**
1. Pattern comparison:
   - Retrieve historical engagement patterns
   - Compare activity outcomes to pattern predictions
   - Calculate consistency scores

2. Coherence validation:
   - Analyze internal consistency of feedback
   - Cross-reference with historical preferences
   - Flag pattern deviations with confidence ratings

3. Preference update:
   - Formulate domain engagement adjustments
   - Create activity type preference modifications
   - Provide confidence ratings for each update

**Expected Output Benchmark:**
- Engagement Pattern Update Recommendation containing:
  - Pattern validation section with consistency metrics
  - Preference adjustment section with specific parameter changes
  - Coherence assessment with reliability indicators
  - Evidence section linking recommendations to both current feedback and historical patterns
- All outputs must meet these quality standards:
  - Statistical validity measures for pattern assessments
  - Clear distinction between temporary deviations and preference shifts
  - Confidence ratings for all preference updates

**Output & Next Step:**
- To Psychological Monitoring Agent: Context package with Engagement Pattern Update Recommendation

## Psychological Monitoring Agent

**Entry Point & Input:**
- Trigger: Receipt of context package from Engagement & Pattern Analytics Agent
- Input: Feedback report, historical context, pending recommendations, multiple Update Recommendations

**Data Access:**
- Read: UserTraitInclination, Aspiration, TrustLevel, UserProfile, Belief
- Write: None
- Recommend: UserTraitInclination updates, Aspiration modifications, TrustLevel adjustments, Belief additions
- Memory Access:
  - Read: Trait expression patterns, trust development history, growth area indicators
  - Write: Updated psychological patterns and indicators

**Core Responsibilities:**
- Analyze user trait expressions and changes:
  - Evaluate personality trait expression shifts based on activity experience
  - Identify emerging or shifting traits with supporting evidence
  - Track trait stability or volatility over time
- Assess aspiration and self-perception updates:
  - Analyze shifts in stated goals and aspirations
  - Identify changes in self-concept with evidence
  - Reconcile aspirations with observed behaviors
- Evaluate trust level changes:
  - Process direct user feedback about system trust
  - Calculate unified trust metric adjustments
  - Implement Trust Alarm Protocol for concerning drops
- Conduct psychological safety analysis:
  - Identify comfort/discomfort indicators
  - Flag potential risk areas for future activities
  - Provide safety-based boundary recommendations

**Processing Logic:**
1. Trait analysis:
   - Extract trait expression indicators from feedback
   - Compare with historical trait patterns
   - Identify significant trait shifts or confirmations

2. Aspiration assessment:
   - Analyze goal-related statements in feedback
   - Compare with stated aspirations
   - Identify self-concept modifications

3. Trust evaluation:
   - Calculate trust metric changes based on feedback
   - Assess trust level trajectory
   - Determine if Trust Alarm Protocol activation is needed

4. Safety analysis:
   - Identify psychological comfort indicators
   - Assess risk boundaries for future activities
   - Formulate safety recommendations

**Expected Output Benchmark:**
- Psychological Update Recommendation containing:
  - Trait adjustment section with evidence and confidence levels
  - Aspiration modification section with goal alignment analysis
  - Trust evaluation section with metric adjustments and protocol recommendations
  - Safety assessment with boundary recommendations
  - Communication approach guidelines based on psychological state
- All outputs must meet these quality standards:
  - Evidence-based trait modifications with confidence ratings
  - Clear connection between feedback and aspiration updates
  - Precise trust metric calculations with trajectory analysis
  - Well-defined safety boundaries with justifications

**Output & Next Step:**
- To Strategy Agent: Context package with Psychological Update Recommendation

## Strategy Agent

**Entry Point & Input:**
- Trigger: Receipt of context package from Psychological Monitoring Agent
- Input: Feedback report, historical context, pending recommendations, multiple Update Recommendations

**Data Access:**
- Read: All recommendations, UserProfile, TrustLevel
- Write: None
- Recommend: Challenge level adjustments, domain distribution parameters, activity selection criteria
- Memory Access:
  - Read: Baseline strategy, trust-based adaptations, goal-trait alignments
  - Write: Updated strategic parameters

**Core Responsibilities:**
- Evaluate impact of user model updates on recommendation strategy:
  - Assess how psychological and preference changes affect optimal activity selection
  - Determine appropriate strategic adjustments based on trust level
  - Calibrate challenge parameters based on performance indicators
- Optimize domain distribution parameters:
  - Adjust domain emphasis based on engagement patterns
  - Balance between preference reinforcement and healthy variety
  - Incorporate aspiration alignment in domain weighting
- Refine activity selection criteria:
  - Update challenge level calibration based on performance
  - Modify novelty versus familiarity balance based on trust
  - Adjust activity type distribution based on engagement

**Processing Logic:**
1. Strategy impact assessment:
   - Analyze all update recommendations
   - Evaluate strategic implications
   - Identify necessary parameter adjustments

2. Challenge calibration:
   - Calculate optimal challenge levels based on performance
   - Adjust based on trust level and psychological state
   - Create challenge progression plan

3. Domain optimization:
   - Formulate domain distribution adjustments
   - Balance preference reinforcement with healthy variety
   - Align with updated aspirations and goals

**Expected Output Benchmark:**
- Strategy Update Recommendation containing:
  - Challenge calibration section with specific parameter adjustments
  - Domain distribution section with weighting modifications
  - Selection criteria section with parameter updates
  - Strategic rationale connecting recommendations to user model updates
- All outputs must meet these quality standards:
  - Clear parameter adjustment specifications
  - Balance between comfort and growth opportunities
  - Alignment with trust level and psychological state
  - Evidence-based justification for strategic changes

**Output & Next Step:**
- To Ethical Oversight Agent: Context package with Strategy Update Recommendation

## Ethical Oversight Agent

**Entry Point & Input:**
- Trigger: Receipt of context package from Strategy Agent
- Input: Feedback report, historical context, all Update Recommendations

**Data Access:**
- Read: All recommendations, UserProfile, Belief, boundary specifications
- Write: None
- Recommend: Ethical validation or correction recommendations
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Review all proposed data model updates for ethical alignment:
  - Validate that model changes respect expressed boundaries
  - Ensure recommendations preserve user autonomy
  - Verify psychological safety in all proposed changes
- Assess Belief updates for potential biases:
  - Check for unwarranted assumptions in belief evidence
  - Evaluate neutrality in belief formation processes
  - Ensure diversity of evidence sources
- Validate trust level adjustments:
  - Ensure fair attribution in trust calculations
  - Verify proportionality in trust adjustments
  - Validate alarm protocols against overreaction risks

**Processing Logic:**
1. Ethical review:
   - Analyze all update recommendations against ethical guidelines
   - Identify potential boundary issues
   - Flag autonomy concerns

2. Belief validation:
   - Assess belief update evidence quality
   - Evaluate neutrality in belief formation
   - Check for bias indicators

3. Trust validation:
   - Review trust adjustment calculations
   - Evaluate proportionality and fairness
   - Assess alarm protocol appropriateness

**Expected Output Benchmark:**
- Ethical Validation or Correction Recommendations containing:
  - Validation section for ethically sound recommendations
  - Correction section for problematic recommendations
  - Boundary assessment with specific concerns
  - Autonomy evaluation with specific guidance
  - Modified parameter recommendations where needed
- All outputs must meet these quality standards:
  - Clear connection to ethical principles
  - Specific correction recommendations when needed
  - Preservation of user agency and autonomy
  - Absence of system-beneficial bias

**Output & Next Step:**
- To Orchestrator Agent: Ethical Validation or Correction Recommendations

## Final Integration Process

**Entry Point & Input:**
- Trigger: Receipt of Ethical Validation from Ethical Oversight Agent
- Input: All Update Recommendations with ethical validation

**Data Access:**
- Read: All recommendations, current data model state
- Write: UserProfile, UserTraitInclination, Aspiration, TrustLevel, Preference, UserResource, Inspiration, GoalInspiration, Belief, HistoryLog
- Recommend: None (final implementation)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Implement all validated updates to data model:
  - Apply UserProfile adjustments
  - Update trust level based on direct feedback
  - Modify User Beliefs with new evidence
  - Adjust activity preferences and trait inclinations
  - Update aspiration and inspiration records
- Record complete update process in HistoryLog:
  - Document all changes with justifications
  - Create comprehensive record for future reference
  - Maintain audit trail for system decisions
- Trigger appropriate user communication:
  - Instruct Mentor Agent on communication approach
  - Provide guidance on trust maintenance or recovery
  - Define follow-up action parameters

**Processing Logic:**
1. Update implementation:
   - Process all validated recommendations
   - Apply changes to appropriate data models
   - Ensure transactional integrity across updates

2. History logging:
   - Create comprehensive update record
   - Document evidence and justifications
   - Maintain decision audit trail

3. Communication preparation:
   - Generate Mentor Agent instructions
   - Specify trust-appropriate communication approach
   - Define follow-up parameters

**Expected Output Benchmark:**
- Updated data model with:
  - Coherent and consistent changes across all elements
  - Complete evidence trail for all updates
  - Comprehensive history record of changes
- Mentor Agent instructions containing:
  - Communication approach specifications
  - Session summary parameters
  - Trust maintenance or recovery guidance
  - Follow-up action recommendations
- All outputs must meet these quality standards:
  - Data consistency across all model elements
  - Clear evidence connection for all changes
  - Appropriate trust-based communication guidance

**Output & Next Step:**
- To Mentor Agent: Communication instructions with session summary
- To System: Updated data model for future interactions
