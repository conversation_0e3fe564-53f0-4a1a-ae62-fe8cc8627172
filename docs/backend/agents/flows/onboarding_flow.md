# Game of Life Onboarding Flow: MVP Implementation

*Last Updated: 2025-04-24*

## 1. Overview and Purpose

This document defines the sequential workflow for transforming completed questionnaire responses into a comprehensive user profile that guides all subsequent Game of Life experiences. The flow assumes all user responses have already been collected, and focuses on the systematic processing and analysis of this data to build a cohesive user model.

The onboarding flow generates profile components including:
- HEXACO personality trait assessments
- Environmental and resource context
- Domain preferences and engagement patterns
- Goals, aspirations, and inspirations
- Beliefs and limitations
- Skills and capabilities
- Trust baseline and vulnerability boundaries

## 2. Onboarding Workflow Sequence

### Step 1: Orchestrator Agent - Initial Workflow Setup

**Entry Point:**
- Trigger: Receipt of completed questionnaire responses
- Input: Complete questionnaire response package with all user responses

**Data Access:**
- Read: Complete user profile with all components
- Write: HistoryLog (validation record)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Review complete user profile for ethical concerns
- Validate balanced and fair representation of user
- Ensure proper handling of sensitive information
- Verify psychological safety boundaries are respected
- Confirm appropriate challenge calibration approach
- Provide final validation or modification recommendations

**Processing Logic:**
1. Profile Balance Assessment:
   - Review trait assessments for balanced representation:
     - Verify both strengths and growth areas are acknowledged
     - Check for overly negative or limiting characterizations
     - Ensure evidence-based assessments rather than assumptions
   - Examine belief documentation for fairness:
     - Confirm beliefs are documented without judgment
     - Verify supporting evidence is objective and sufficient
     - Check for balanced representation of positive and limiting beliefs
   - Review limitation documentation:
     - Verify limitations are described without stigmatization
     - Ensure proper differentiation between limitations and preferences
     - Check for appropriate confidence levels in limitation assessments

2. Psychological Safety Validation:
   - Review strategy approach for safety considerations:
     - Verify challenge levels respect current trust phase
     - Check that resistance areas are approached gradually
     - Ensure potential vulnerability triggers are properly acknowledged
   - Validate developmental approach:
     - Confirm development pathways are psychologically sound
     - Verify growth expectations align with capabilities
     - Ensure sufficient scaffolding for challenging areas
   - Assess communication approach:
     - Verify framing respects user's self-concept
     - Check that terminology is non-stigmatizing
     - Ensure validation is authentic and appropriate

3. Privacy and Sensitivity Review:
   - Evaluate handling of sensitive information:
     - Verify appropriate detail level in trauma-related content
     - Check that personal information is relevant to system function
     - Ensure private information is handled with respect
   - Review boundary recognition:
     - Confirm system respects indicated boundaries
     - Verify sensitive topics are approached appropriately
     - Ensure user autonomy is preserved in strategy

4. Ethical Recommendation Development:
   - For any identified concerns:
     - Document specific issue with component reference
     - Provide clear rationale for the concern
     - Recommend specific modifications to address the issue
   - For approved components:
     - Document validation with any enhancement suggestions
     - Note particularly sensitive areas for ongoing monitoring
     - Recommend additional supports where appropriate

**Output & Next Step:**
- To Orchestrator: Ethical validation report containing:
  - Complete assessment of profile ethical considerations
  - Specific modification recommendations for any concerns
  - Validation confirmation for approved components
  - Guidance for ongoing ethical monitoring:**
- Read: None (workflow initiation)
- Write: UserProfile (create initial record), HistoryLog (workflow events)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Create base UserProfile record with user identifier and minimal metadata
- Analyze questionnaire responses to identify relevant sections for each agent
- Prepare data packages for sequential processing by specialized agents
- Establish processing sequence and dependencies
- Track workflow progression and completion status

**Processing Logic:**
1. Create UserProfile record with basic identifier and timestamp
2. Organize questionnaire responses into logical sections:
   - Environment and demographic sections for Resource & Capacity Agent
   - Preference and behavior sections for Engagement & Pattern Analytics Agent
   - Psychological assessment sections for Psychological Monitoring Agent
3. Determine processing sequence based on data dependencies:
   - Resource & Capacity Agent first (environmental context required by other agents)
   - Engagement & Pattern Analytics Agent second (preference patterns needed for psychological assessment)
   - Psychological Monitoring Agent third (leverages resource context and engagement patterns)
   - Strategy Agent fourth (synthesizes outputs from all previous agents)
4. Create initial workflow tracking record in HistoryLog

**Output & Next Step:**
- To Resource & Capacity Agent: Environmental and demographic data package with processing instructions
- Workflow state maintained for subsequent orchestration steps

### Step 2: Resource & Capacity Agent - Environmental Context Establishment

**Entry Point:**
- Trigger: Receipt of environmental data package from Orchestrator
- Input: Questionnaire sections about living situation, resources, daily routines, and environmental context

**Data Access:**
- Read: GenericEnvironment templates, GenericResource catalog, GenericSkill catalog, GenericUserLimitation catalog
- Write: UserEnvironment, UserEnvironmentPhysicalProperties, UserEnvironmentSocialContext, UserEnvironmentActivitySupport, UserResource, Inventory, Skill, UserLimitation (physical/resource limitations only)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Create comprehensive UserEnvironment record based on user's described context
- Establish appropriate GenericEnvironment template relationship
- Populate physical and social environment property models with detailed characteristics
- Identify available resources and create corresponding UserResource records
- Create initial Inventory record documenting resource availability
- Identify and document user skills through Skill records
- Identify physical/resource limitations and create corresponding UserLimitation records
- Determine environmental constraints and domain support capabilities

**Processing Logic:**
1. Environment Assessment:
   - Analyze living situation descriptions to identify environment type
   - Query GenericEnvironment catalog to find closest matching template
   - Create UserEnvironment record linked to appropriate GenericEnvironment
   - Set effective_start date to current date
   - Populate environment_name and environment_description from user responses

2. Environment Properties Population:
   - Create UserEnvironmentPhysicalProperties record with:
     - Rurality assessment based on location description
     - Noise level inference from described surroundings
     - Light quality assessment from environmental descriptions
     - Temperature range categorization
     - Accessibility evaluation
     - Space size classification
     - Surface type identification
     - Natural elements presence determination
   - Create UserEnvironmentSocialContext record with:
     - Privacy level assessment based on living situation
     - Typical occupancy estimation from household descriptions
     - Social interaction level inference from daily routine
     - Formality level assessment
     - Safety level evaluation
     - Supervision level categorization
     - Cultural diversity estimation
   - Create UserEnvironmentActivitySupport record with:
     - Digital connectivity assessment based on technology mentions
     - Resource availability rating based on described possessions
     - Time availability mapping from daily routine descriptions
     - Domain support levels copied from GenericEnvironment with adjustments based on specific circumstances

3. Resource Identification:
   - Extract explicit and implicit resource mentions from questionnaire responses
   - For each identified resource:
     - Query GenericResource catalog for matching resource types
     - Create UserResource record with:
       - specific_name from user description
       - link to appropriate generic_resource if match found
       - location_details from context
       - ownership_details from descriptions
       - contact_info if applicable
       - notes with additional context
   - Create initial Inventory record linked to UserEnvironment with:
     - valid_until set to 3 months from current date
     - notes summarizing key resources

4. Skill Assessment:
   - Extract explicit and implicit skill mentions from questionnaire responses
   - For each identified skill:
     - Query GenericSkill catalog for matching skill types
     - Create Skill record with:
       - link to appropriate generic_skill if match found
       - description from user context
       - level based on proficiency indications (0-100)
       - user_awareness based on explicit mentions
       - user_enjoyment based on enthusiasm indicators
       - note with supporting context
   - Ensure skills across multiple domains are represented:
     - Technical skills (software, tools, equipment operation)
     - Creative skills (artistic, writing, design)
     - Social skills (communication, leadership, teamwork)
     - Physical skills (sports, manual dexterity, coordination)
     - Cognitive skills (analysis, language, problem-solving)

5. Physical Limitation Assessment:
   - Identify physical or resource-based limitations from questionnaire responses
   - For each identified limitation:
     - Query GenericUserLimitation catalog for matching limitation types
     - Create UserLimitation record for physical limitations with:
       - link to appropriate generic_limitation if match found
       - severity based on impact indicators (0-100)
       - valid_until based on permanence indicators
       - is_unlimited flag for persistent limitations
       - user_awareness based on explicit acknowledgment (0-100)
   - Focus specifically on:
     - Physical health limitations (mobility, energy, chronic conditions)
     - Environmental limitations (space constraints, location restrictions)
     - Resource limitations (financial, equipment, access)
     - Time limitations (schedule constraints, competing commitments)
     - Skill-based limitations (technical knowledge gaps, experience deficits)

6. Confidence Assessment:
   - For each created record, assign confidence rating (0-100) based on:
     - Specificity of user descriptions (more specific = higher confidence)
     - Consistency across multiple mentions (more consistent = higher confidence)
     - Clarity of mapping to generic templates (clearer mapping = higher confidence)
   - Document confidence ratings in processing notes for Orchestrator review

**Output & Next Step:**
- To Orchestrator: Resource context report containing:
  - Complete UserEnvironment with physical and social property records
  - UserResource records with confidence ratings
  - Inventory summary
  - Skill assessment with confidence ratings
  - Physical/resource UserLimitation records
  - Identified constraints and opportunities
  - Confidence metrics for each assessment
  - Recommendations for additional data collection where confidence is low

### Step 3: Orchestrator Agent - First Transition

**Entry Point:**
- Trigger: Receipt of resource context report from Resource & Capacity Agent
- Input: Complete resource context with environment, resources, skills, and physical limitations

**Data Access:**
- Read: Resource context report
- Write: HistoryLog (workflow progression)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Verify completion of Resource & Capacity Agent processing
- Update workflow state in HistoryLog
- Prepare data package for Engagement & Pattern Analytics Agent
- Forward processing to next agent in sequence

**Processing Logic:**
1. Verify resource context report meets minimum completeness requirements:
   - UserEnvironment record with physical and social property models populated
   - At least 3 UserResource records created
   - At least 3 Skill records documented
   - Confidence ratings for all assessments
2. Extract relevant data to enhance engagement analysis package:
   - Environmental factors that may influence engagement patterns
   - Skills that indicate activity preferences
   - Resources that enable certain activities
   - Physical limitations that constrain engagement options
3. Update workflow state in HistoryLog
4. Prepare engagement data package with:
   - Relevant questionnaire sections
   - Resource context elements that inform engagement analysis
   - Instructions for preference and pattern assessment

**Output & Next Step:**
- To Engagement & Pattern Analytics Agent: Engagement data package with questionnaire responses and relevant resource context

### Step 4: Engagement & Pattern Analytics Agent - Preference and Pattern Analysis

**Entry Point:**
- Trigger: Receipt of engagement data package from Orchestrator
- Input: Questionnaire sections related to preferences, activities, and engagement patterns, plus resource context

**Data Access:**
- Read: Resource context (environment, skills, limitations)
- Write: Preference records
- Memory Access: 
  - Write: Domain engagement patterns, behavioral tendency insights, activity completion predictors, engagement strategy guidance

**Core Responsibilities:**
- Analyze stated preferences and behavioral patterns
- Identify domain engagement tendencies across activity types
- Detect temporal patterns in energy and engagement
- Assess optimal activity structures and formats
- Document preference patterns through Preference records
- Build pattern memory for ongoing engagement optimization
- Identify potential resistance patterns and abandonment triggers

**Processing Logic:**
1. Domain Preference Analysis:
   - Extract explicit preference statements from questionnaire responses
   - Analyze implicit preferences from behavior descriptions
   - Map preferences to activity domains:
     - Creative (arts, crafts, writing, music)
     - Physical (exercise, sports, outdoor activities)
     - Social (group interactions, conversations, community)
     - Intellectual (learning, problem-solving, analysis)
     - Reflective (meditation, journaling, contemplation)
     - Practical (organizing, building, fixing)
   - For each identified domain preference:
     - Create Preference record with:
       - pref_name describing domain (e.g., "Creative Activities")
       - pref_description with detailed context
       - pref_strength based on enthusiasm indicators (0-100)
       - user_awareness based on explicit acknowledgment (0-100)
       - environment link if context-specific
       - effective_start set to current date
       - duration_estimate set to "6 months"
       - effective_end calculated accordingly

2. Temporal Pattern Analysis:
   - Identify daily rhythm patterns from questionnaire responses
   - Analyze energy flow descriptions throughout the day
   - Detect optimal times for different activity types
   - For each temporal pattern:
     - Create Preference record with:
       - pref_name describing pattern (e.g., "Morning Productivity")
       - pref_description with detailed context
       - pref_strength based on consistency indicators (0-100)
       - user_awareness based on explicit acknowledgment (0-100)
       - effective_start set to current date
       - duration_estimate set to "3 months"
       - effective_end calculated accordingly

3. Format Preference Analysis:
   - Identify preferences for activity structure and format:
     - Solo vs. group activities
     - Structured vs. open-ended activities
     - Physical vs. digital engagement
     - Short vs. long duration
     - Goal-oriented vs. process-oriented
   - For each format preference:
     - Create Preference record with:
       - pref_name describing format (e.g., "Structured Activities")
       - pref_description with detailed context
       - pref_strength based on consistency indicators (0-100)
       - user_awareness based on explicit acknowledgment (0-100)
       - effective_start set to current date
       - duration_estimate set to "6 months"
       - effective_end calculated accordingly

4. Specialized Preference Analysis:
   - Identify additional preference categories:
     - Coping preferences (how user deals with stress)
     - Reward preferences (small pleasures that motivate)
     - Feedback preferences (how user prefers to receive guidance)
     - Recharge preferences (how user restores energy)
     - Learning preferences (how user best absorbs information)
   - For each specialized preference:
     - Create Preference record with appropriate fields
     - Ensure detailed documentation of context and evidence

5. Engagement Pattern Analysis:
   - Analyze descriptions of past activities and engagement
   - Identify factors in both completed and abandoned activities
   - Detect recurring patterns in user's engagement history
   - Document patterns across multiple dimensions:
     - Resistance patterns (where user consistently disengages)
     - Completion patterns (where user successfully follows through)
     - Avoidance patterns (what user systematically avoids)
     - Enthusiasm patterns (what generates sustained interest)
     - Difficulty response patterns (how user handles challenges)

6. Trust Indicator Analysis:
   - Extract behavioral indicators relevant to trust assessment:
     - Engagement trust indicators (willingness to interact with systems)
     - Action trust indicators (follow-through on commitments)
     - Disclosure trust indicators (comfort sharing personal information)
   - Analyze patterns indicating trust phase:
     - Comfort with uncertainty and unpredictability
     - Response to structured vs. open-ended activities
     - Follow-through patterns on past commitments
     - History of engagement with similar guidance systems

7. Memory Formation:
   - Document domain engagement patterns:
     - Relative engagement across domains with completion likelihood
     - Example: "Creative activities show highest engagement (80% completion rate), particularly in evening hours (90% completion when scheduled after 7pm vs. 40% in mornings). Visual creative activities (drawing, design) show stronger completion patterns than written creative activities."
     - Usage: Informs optimal domain distribution in activity selection
   
   - Record behavioral tendency insights:
     - Consistent behavioral patterns with underlying drivers
     - Example: "Consistent pattern of starting projects enthusiastically but abandoning at 30-40% completion point. Pattern strongest when projects require sustained solo effort without external accountability. Projects with collaborative elements or structured milestone check-ins show 3x higher completion rate."
     - Usage: Guides activity structure and challenge calibration
   
   - Capture activity completion predictors:
     - Factors that reliably predict successful completion
     - Example: "Strong completion predictor: Duration under 30 minutes (90% completion rate). Secondary predictor: Morning scheduling for practical tasks (85% completion rate). Activities combining both factors show 95% completion rate."
     - Usage: Informs activity selection and scheduling recommendations
   
   - Formulate engagement strategy guidance:
     - Strategic approaches to enhance engagement based on patterns
     - Example: "For initial trust building: Short-duration creative activities in evening hours with clear completion criteria. For handling resistance around social activities: Begin with digital social interaction with defined purpose, gradually transitioning to in-person contexts."
     - Usage: Guides Strategy Agent in developing effective engagement approaches

8. Confidence Assessment:
   - For each preference and pattern, assign confidence rating (0-100) based on:
     - Consistency of indicators across questionnaire responses
     - Specificity of supporting examples
     - Presence of contradictory information
   - Document confidence ratings with supporting rationale
   - Flag patterns with low confidence (<50%) for future validation

**Output & Next Step:**
- To Orchestrator: Engagement pattern report containing:
  - Complete set of Preference records with confidence ratings
  - Domain engagement assessment
  - Temporal pattern analysis
  - Format preference evaluation
  - Resistance and completion pattern documentation
  - Trust indicator analysis
  - Memory formation content
  - Confidence metrics for each assessment
  - Recommendations for pattern validation through early activities

### Step 5: Orchestrator Agent - Second Transition

**Entry Point:**
- Trigger: Receipt of engagement pattern report from Engagement & Pattern Analytics Agent
- Input: Complete preference and pattern analysis with trust indicators

**Data Access:**
- Read: Engagement pattern report, Resource context report
- Write: HistoryLog (workflow progression)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Verify completion of Engagement & Pattern Analytics Agent processing
- Update workflow state in HistoryLog
- Prepare data package for Psychological Monitoring Agent
- Forward processing to next agent in sequence

**Processing Logic:**
1. Verify engagement pattern report meets minimum completeness requirements:
   - At least 3 domain preference records
   - At least 2 temporal preference records
   - At least 3 format preference records
   - Documented engagement patterns with confidence ratings
   - Trust indicator analysis
2. Compile comprehensive data package for Psychological Monitoring Agent:
   - Resource context from Resource & Capacity Agent
   - Engagement patterns from Engagement & Pattern Analytics Agent
   - Relevant questionnaire sections for psychological assessment
   - Instructions for psychological profile creation
3. Update workflow state in HistoryLog

**Output & Next Step:**
- To Psychological Monitoring Agent: Psychological data package with questionnaire responses, resource context, and engagement patterns

### Step 6: Psychological Monitoring Agent - Comprehensive Psychological Assessment

**Entry Point:**
- Trigger: Receipt of psychological data package from Orchestrator
- Input: Questionnaire sections related to personality, emotional patterns, beliefs, goals, psychological factors, plus resource context and engagement patterns

**Data Access:**
- Read: GenericTrait reference data, Resource context (environment, skills, physical limitations), Engagement patterns (preferences, behavioral tendencies)
- Write: Demographics, UserTraitInclination, Belief, BeliefEvidence, UserGoal (both Intention and Aspiration), TrustLevel, CurrentMood, Inspiration, GoalInspiration, UserLimitation (psychological limitations), BeliefInfluence, UserEnvironmentPsychologicalQualities
- Memory Access: 
  - Write: Psychological growth trajectories, trait-belief-behavior connections, developmental readiness insights, intervention approach guidelines

**Core Responsibilities:**
- Create Demographics record with user's personal information
- Perform comprehensive HEXACO trait assessment and create UserTraitInclination records
- Identify core beliefs and create Belief records with supporting BeliefEvidence
- Extract aspirations and intentions to create UserGoal records
- Establish inspirational sources with Inspiration records and GoalInspiration connections
- Identify belief influences and create BeliefInfluence records
- Document psychological environment qualities in UserEnvironmentPsychologicalQualities
- Identify psychological limitations and create UserLimitation records
- Assess trust metrics and create TrustLevel record
- Document current emotional state in CurrentMood record
- Build psychological memory for long-term pattern tracking

**Processing Logic:**
1. Demographic Analysis:
   - Extract explicit demographic information from questionnaire responses
   - Create Demographics record with:
     - full_name, age, gender from direct responses
     - location, language from context
     - occupation from career information
     - personal_prefs_json with extracted preference indicators

2. Environmental Psychological Assessment:
   - Analyze psychological aspects of user's environment
   - Create UserEnvironmentPsychologicalQualities record with:
     - restorative_quality based on described feelings
     - stimulation_level based on environmental complexity
     - aesthetic_appeal based on described appreciation
     - novelty_level based on familiarity indicators
     - comfort_level based on described feelings
     - personal_significance based on attachment indications
     - emotional_associations based on described feelings

3. HEXACO Trait Assessment:
   - For each HEXACO trait facet:
     - Extract relevant responses from personality-related questions
     - Integrate behavioral patterns from Engagement Agent analysis
     - Convert qualitative responses to quantitative scores (0-100)
     - Assess awareness level based on explicit self-knowledge
     - Query GenericTrait catalog to find corresponding trait record
     - Create UserTraitInclination record with:
       - link to appropriate generic_trait
       - strength value (0-100) based on assessment
       - awareness value (0-100) based on self-knowledge indicators

4. Belief System Analysis:
   - Identify recurring themes in user's worldview and self-conception
   - Extract explicit belief statements from responses
   - Infer implicit beliefs from behavioral patterns reported by Engagement Agent
   - For each identified belief:
     - Create Belief record with:
       - content containing the core proposition
       - last_updated set to current date
       - user_confidence based on conviction indicators (0-100)
       - system_confidence based on evidence strength (0-100)
       - emotionality based on emotional charge indicators (-100 to 100)
       - stability based on consistency indicators (0-100)
       - user_awareness based on explicit acknowledgment (0-100)
     - For each piece of supporting evidence:
       - Create BeliefEvidence record with:
         - evidence_type categorized as "EXPERIENCE", "OBSERVATION", etc.
         - description with detailed context
         - credibility_score based on evidence quality (0.0-1.0)
         - source attribution from user's responses
   - For each belief influence identified:
     - Create BeliefInfluence record with:
       - belief linked to the influencing belief
       - content_type and object_id identifying the target entity
       - influence_strength indicating impact magnitude (-100 to 100)
       - note explaining influence mechanism
   - Categorize beliefs into:
     - Self-beliefs (about user's identity and capabilities)
     - World-beliefs (about how the world works)
     - Value-beliefs (about what matters and priorities)
     - Relationship-beliefs (about how user relates to others)

5. Goal Extraction:
   - Analyze responses related to aspirations, intentions, and desired outcomes
   - Distinguish between short-term intentions and long-term aspirations
   - For each identified goal:
     - Categorize as either Intention (short-term) or Aspiration (long-term)
     - Create appropriate UserGoal record (Intention or Aspiration) with:
       - title from concise goal statement
       - description with detailed explanation
       - importance_according_user based on stated priority (0-100)
       - importance_according_system based on goal alignment with values (0-100)
       - strength based on commitment indicators (0-100)
       - For Intention records, add:
         - start_date set to current date
         - due_date based on timeframe indicators
         - is_completed set to False
         - progress_notes with initial context
       - For Aspiration records, add:
         - domain categorization (e.g., "Career", "Creativity")
         - horizon classification (e.g., "Long-term")
         - level_of_ambition indicator (e.g., "High")
   - Ensure balanced representation across life domains:
     - Personal development goals
     - Relationship goals
     - Career/work goals
     - Health/wellness goals
     - Creative/hobby goals

6. Inspiration Analysis:
   - Identify sources of motivation and inspiration from responses
   - For each inspirational source:
     - Create Inspiration record with:
       - source describing origin (e.g., "Book", "Person", "Experience")
       - description with detailed explanation
       - strength indicating impact level (0-100)
       - reference_url if applicable
   - Connect inspirations to relevant goals:
     - For each appropriate inspiration-goal pair:
       - Create GoalInspiration record with:
         - links to respective user_goal and inspiration
         - note explaining connection
         - strength indicating relationship power (0-100)

7. Psychological Limitation Identification:
   - Analyze responses for psychological limitations, fears, and vulnerabilities
   - Distinguish carefully between psychological limitations and beliefs:
     - Psychological limitation: Specific psychological constraint that directly impedes activities
     - Belief: Cognitive proposition that may indirectly influence behavior
   - For each identified psychological limitation:
     - Query GenericUserLimitation catalog for matching limitation types
     - Create UserLimitation record with:
       - link to appropriate generic_limitation if match found
       - severity based on impact indicators (0-100)
       - valid_until based on permanence indicators
       - is_unlimited flag for persistent limitations
       - user_awareness based on explicit acknowledgment (0-100)
   - Categorize psychological limitations into:
     - Anxiety-based limitations (social anxiety, performance anxiety)
     - Trauma-based limitations (past negative experiences)
     - Fear-based limitations (specific phobias or concerns)
     - Cognitive limitations (attention, focus, processing)
     - Emotional limitations (regulation, expression, awareness)

8. Trust Assessment:
   - Integrate trust indicators from Engagement Agent analysis
   - Evaluate psychological factors affecting trust readiness
   - Calculate dimensional trust scores:
     - Engagement Trust: Willingness to interact with systems (0-100)
     - Action Trust: Likelihood of following through on commitments (0-100)
     - Disclosure Trust: Comfort with sharing personal information (0-100)
   - Determine trust phase based on unified score:
     - 0-40%: Early Foundation Phase
     - 41-60%: Late Foundation Phase
     - 61-75%: Early Expansion Phase
     - 76-100%: Established Expansion Phase
   - Create TrustLevel record with:
     - value set to the unified trust score (0-100)
     - aggregate_type set to "Initial"
     - aggregate_id generated for reference
     - notes documenting trust assessment rationale

9. Current Mood Establishment:
   - Analyze emotional indicators from recent responses
   - Create CurrentMood record with basic attributes including description, height, and user_awareness

10. Memory Formation:
    - Document psychological growth trajectories:
      - Example: "User shows high development potential in conscientiousness when activities connect to creative aspirations. Growth sequence: Begin with short creative assignments with clear endpoints, gradually extend duration while maintaining creative engagement, then introduce planning elements for larger creative projects."
      - Usage: Guides developmentally appropriate challenge sequencing
    
    - Map trait-belief-behavior connections:
      - Example: "Low conscientiousness (trait) connects to 'I'm not good at following through' (belief), manifesting as project abandonment at 30-40% completion (behavior). Belief reinforced by selective attention to failures and discounting of successes. Intervention opportunity: Highlight small completions and create success experiences with properly sized challenges."
      - Usage: Identifies leverage points for psychological development
    
    - Capture developmental readiness insights:
      - Example: "User shows readiness for extraversion development in digital contexts but resistance in physical social settings. Digital social activities serve as appropriate bridge for gradually building comfort with in-person social engagement. Current optimal challenge level: text-based interaction with 1-2 known individuals around shared interests."
      - Usage: Informs appropriate challenge calibration
    
    - Formulate intervention approach guidelines:
      - Example: "Communication strategy: Frame challenges as explorations rather than achievements to bypass perfectionism. Success validation strategy: Acknowledge effort and process engagement rather than outcomes. Resistance handling strategy: When social anxiety emerges, temporarily shift to creative domain then reintroduce social elements gradually within creative context."
      - Usage: Guides effective psychological approach across interactions

11. Confidence Assessment:
    - For each component of the psychological profile, assign confidence rating (0-100) based on:
      - Consistency of evidence across multiple responses
      - Clarity of indicators from self-reports
      - Alignment with behavioral patterns from Engagement Agent
      - Presence of contradictory information
    - Document confidence ratings with supporting rationale
    - Flag areas with low confidence (<50%) for further exploration

**Output & Next Step:**
- To Orchestrator: Comprehensive psychological profile containing:
  - Demographics record
  - UserEnvironmentPsychologicalQualities record
  - Complete set of UserTraitInclination records with confidence ratings
  - Belief records with BeliefEvidence and BeliefInfluence connections
  - UserGoal records (both Intentions and Aspirations)
  - Inspiration records with GoalInspiration connections
  - Psychological UserLimitation records
  - TrustLevel assessment
  - CurrentMood record
  - Memory formation content
  - Confidence metrics for each assessment
  - Recommendations for additional data collection where confidence is low

### Step 7: Orchestrator Agent - Third Transition

**Entry Point:**
- Trigger: Receipt of psychological profile from Psychological Monitoring Agent
- Input: Complete psychological profile with traits, beliefs, goals, and trust metrics

**Data Access:**
- Read: Psychological profile report, Engagement pattern report, Resource context report
- Write: HistoryLog (workflow progression)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Verify completion of Psychological Monitoring Agent processing
- Update workflow state in HistoryLog
- Prepare data package for Strategy Agent
- Forward processing to next agent in sequence

**Processing Logic:**
1. Verify psychological profile meets minimum completeness requirements:
   - UserTraitInclination records covering all HEXACO dimensions
   - At least 3 Belief records with supporting evidence
   - At least 2 Aspiration and 3 Intention records
   - TrustLevel record with phase determination
   - CurrentMood record with baseline assessment
2. Compile comprehensive data package for Strategy Agent:
   - Complete resource context from Resource & Capacity Agent
   - Complete engagement patterns from Engagement & Pattern Analytics Agent
   - Complete psychological profile from Psychological Monitoring Agent
   - Instructions for strategy formation
3. Update workflow state in HistoryLog

**Output & Next Step:**
- To Strategy Agent: Comprehensive data package with all previous agent outputs

### Step 8: Strategy Agent - Strategy Formation

**Entry Point:**
- Trigger: Receipt of comprehensive data package from Orchestrator
- Input: Complete user profile with resource context, engagement patterns, and psychological profile

**Data Access:**
- Read: All previous agent outputs (resource context, engagement patterns, psychological profile)
- Write: None (strategy stored in memory only)
- Memory Access: 
  - Write: Goal-trait-skill mapping, challenge calibration rationale, domain engagement strategy, growth facilitation approach, trust development pathway

**Core Responsibilities:**
- Analyze relationships between goals, traits, and skills
- Identify development opportunities based on trait-goal alignment
- Determine appropriate challenge levels based on trust metrics
- Calculate optimal domain distribution for activities
- Create comprehensive strategy framework for wheel generation
- Build strategy memory for ongoing optimization

**Processing Logic:**
1. Goal-Trait-Skill Alignment Analysis:
   - For each UserGoal (both Intentions and Aspirations):
     - Identify the HEXACO traits that most support achievement
     - Identify skills that directly contribute to goal progress
     - Evaluate current skill and trait levels against goal requirements
     - Determine development priorities based on:
       - Goal importance (user and system evaluated)
       - Current trait/skill levels
       - Development difficulty
       - Trust phase readiness
   - Create goal achievement pathways that identify:
     - Trait developments that would facilitate goal achievement
     - Skill developments that would support goal progress 
     - Environmental factors that enable or constrain progress
     - Psychological factors that support or limit advancement

2. Development Opportunity Identification:
   - Analyze where trait gaps affect multiple important goals
   - Identify skills with high leverage across multiple objectives
   - Assess development readiness based on:
     - Current trust phase (Foundation vs. Expansion)
     - Engagement patterns from preference analysis
     - Resistance areas identified by Engagement Agent
     - Growth trajectory insights from Psychological Agent
   - Prioritize development opportunities by:
     - Impact on multiple important goals
     - Alignment with user's current interests
     - Feasibility given current trust level
     - Potential for early success experiences

3. Challenge Calibration Strategy:
   - Determine appropriate challenge parameters based on trust phase:
     - For Foundation Phase: Emphasize success probability and clear structure
     - For Expansion Phase: Introduce moderate challenge and ambiguity
   - Create domain-specific challenge calibration approach:
     - For high-engagement domains: Slightly higher challenge levels
     - For resistance domains: Lower initial challenge with step-based progression
   - Establish challenge dimensions for activity selection:
     - Novelty level: Familiarity vs. new experiences
     - Difficulty level: Skill requirement vs. current ability
     - Ambiguity level: Clear guidelines vs. open-ended exploration
     - Duration: Time commitment and sustained attention required
   - Develop calibration adjustment guidelines based on:
     - Activity completion patterns
     - Reflection sentiment analysis
     - Explicit feedback on challenge levels
     - Engagement pattern changes over time

4. Domain Engagement Strategy:
   - Analyze domain distribution requirements based on:
     - User's stated preferences (from Engagement Agent)
     - Goal-supporting domains (from goal analysis)
     - Development opportunity domains (from trait-goal analysis)
     - Environment-supported domains (from Resource Agent)
   - Create trust-phase appropriate distribution strategy:
     - Foundation Phase: Preference-heavy with strategic growth elements
     - Expansion Phase: Growth-focused with preference anchoring
   - Establish domain balance principles:
     - Minimum representation for all essential domains
     - Strategic sequencing of preferred and growth domains
     - Contextual adaptation based on environmental factors
     - Progressive exposure to resistance domains
   - Develop reward integration approach:
     - Identify activities that function as intrinsic rewards based on preferences
     - Determine optimal placement of high-enjoyment activities
     - Create principles for balancing reward and challenge activities
     - Establish reinforcement patterns that build habit strength

5. Growth Facilitation Strategy:
   - Develop approaches to handle resistance areas:
     - Identify resistance domains from engagement patterns
     - Create gradual exposure strategies for resistance areas
     - Design scaffolding approaches for challenging activities
     - Establish recovery mechanisms for setbacks
   - Create trait development pathways:
     - Define observable indicators of trait development
     - Establish progression sequences for key traits
     - Identify activities that specifically target trait development
     - Create milestone recognition approach for growth validation
   - Design skill building approach:
     - Map skill development sequences for priority skills
     - Identify activities that build targeted skills
     - Create skill application opportunities that support goals
     - Establish skill assessment mechanisms

6. Memory Formation:
   - Document goal-trait-skill mapping:
     - Example: "Goal 'Complete a creative project' requires: Conscientiousness (trait, current level 40%, target 60%), Project Organization (skill, current level 30%, target 50%), Creative Ideation (existing strength, current level 85%). Primary development focus: short-duration creative activities with clear completion criteria to build conscientiousness while leveraging existing creative strengths."
     - Usage: Guides activity selection to support specific goal achievement
   
   - Capture challenge calibration rationale:
     - Example: "Current trust level (48%) indicates Late Foundation Phase, requiring success probability of 75%+ for initial activities. Existing completion patterns show highest success rates with creative activities under 30 minutes. Challenge escalation path: maintain 20-minute duration while gradually introducing planning elements, then extend duration incrementally while maintaining creative engagement."
     - Usage: Informs appropriate challenge levels for different domains and contexts
   
   - Record domain engagement strategy:
     - Example: "Initial domain distribution should emphasize creative activities (40-50%) with morning practical activities (20-30%) as secondary focus. Creative domain provides engagement anchor while practical domain builds conscientiousness. Social activities should start with minimal 10% representation through digital interaction only. Reward activities (identified preferences: sketching, listening to specific music genres) should follow challenging activities to build positive association."
     - Usage: Guides activity domain distribution and sequencing
   
   - Formulate growth facilitation approach:
     - Example: "Primary growth lever: Connecting conscientiousness development to creative goals provides motivation lacking in previous attempts. Resistance handling strategy: Frame activities as creative exploration rather than tasks to bypass resistance to structure. Support approach: Break larger projects into explicit creative micro-sessions with clear outputs. Milestone recognition strategy: Highlight accumulation of completed creative components."
     - Usage: Guides psychological approach to facilitating development
   
   - Map trust development pathway:
     - Example: "Trust development sequence: Begin with high-success creative activities that establish system credibility, gradually introduce planning elements while maintaining creative focus, then expand to brief social activities with defined parameters. Initial trust indicators to monitor: Completion rates, reflection depth, explicit feedback on recommendations. Phase transition indicators: Consistent completion of planned activities, unprompted mentions of growth mindset, requests for more challenging activities."
     - Usage: Guides progressive trust building and phase transition management


     # Game of Life Onboarding Flow: Final Steps


## Step 9: Orchestrator Agent - Final Integration Preparation

**Entry Point:**
- Trigger: Receipt of strategy framework from Strategy Agent
- Input: Complete strategy framework with all components

**Data Access:**
- Read: Strategy framework, all previous agent outputs
- Write: HistoryLog (workflow progression)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Verify completion of Strategy Agent processing
- Compile complete user profile for final validation
- Update workflow state in HistoryLog
- Prepare data package for Ethical Oversight Agent
- Forward processing to final validation

**Processing Logic:**
1. Verify strategy framework meets minimum completeness requirements:
   - Complete goal-trait-skill mapping
   - Challenge calibration approach with clear parameters
   - Domain engagement strategy with preference integration
   - Growth facilitation approach for development areas
   - Trust development pathway
   - Strategy reassessment guidelines
   
2. Compile comprehensive user profile with all components:
   - Resource context (environment, resources, skills, physical limitations)
   - Psychological profile (traits, beliefs, goals, psychological limitations, trust)
   - Engagement patterns (preferences, behavioral tendencies)
   - Strategy framework (development pathways, challenge parameters, engagement approach)
   
3. Update workflow state in HistoryLog

4. Prepare validation package for Ethical Oversight Agent

**Output & Next Step:**
- To Ethical Oversight Agent: Complete user profile with all components for validation

## Step 10: Ethical Oversight Agent - Profile Validation

**Entry Point:**
- Trigger: Receipt of complete user profile from Orchestrator
- Input: Comprehensive user profile with all components from all previous agents

**Data Access:**
- Read: Complete user profile with all components
- Write: HistoryLog (validation record)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Review complete user profile for ethical concerns
- Validate balanced and fair representation of user
- Ensure proper handling of sensitive information
- Verify psychological safety boundaries are respected
- Confirm appropriate challenge calibration approach
- Provide final validation or modification recommendations

**Processing Logic:**
1. Profile Balance Assessment:
   - Review trait assessments for balanced representation:
     - Verify both strengths and growth areas are acknowledged
     - Check for overly negative or limiting characterizations
     - Ensure evidence-based assessments rather than assumptions
   - Examine belief documentation for fairness:
     - Confirm beliefs are documented without judgment
     - Verify supporting evidence is objective and sufficient
     - Check for balanced representation of positive and limiting beliefs
   - Review limitation documentation:
     - Verify limitations are described without stigmatization
     - Ensure proper differentiation between limitations and preferences
     - Check for appropriate confidence levels in limitation assessments

2. Psychological Safety Validation:
   - Review strategy approach for safety considerations:
     - Verify challenge levels respect current trust phase
     - Check that resistance areas are approached gradually
     - Ensure potential vulnerability triggers are properly acknowledged
   - Validate developmental approach:
     - Confirm development pathways are psychologically sound
     - Verify growth expectations align with capabilities
     - Ensure sufficient scaffolding for challenging areas
   - Assess communication approach:
     - Verify framing respects user's self-concept
     - Check that terminology is non-stigmatizing
     - Ensure validation is authentic and appropriate

3. Privacy and Sensitivity Review:
   - Evaluate handling of sensitive information:
     - Verify appropriate detail level in trauma-related content
     - Check that personal information is relevant to system function
     - Ensure private information is handled with respect
   - Review boundary recognition:
     - Confirm system respects indicated boundaries
     - Verify sensitive topics are approached appropriately
     - Ensure user autonomy is preserved in strategy

4. Ethical Recommendation Development:
   - For any identified concerns:
     - Document specific issue with component reference
     - Provide clear rationale for the concern
     - Recommend specific modifications to address the issue
   - For approved components:
     - Document validation with any enhancement suggestions
     - Note particularly sensitive areas for ongoing monitoring
     - Recommend additional supports where appropriate

**Output & Next Step:**
- To Orchestrator: Ethical validation report containing:
  - Complete assessment of profile ethical considerations
  - Specific modification recommendations for any concerns
  - Validation confirmation for approved components
  - Guidance for ongoing ethical monitoring

## Step 11: Orchestrator Agent - Final Integration

**Entry Point:**
- Trigger: Receipt of ethical validation report from Ethical Oversight Agent
- Input: Ethical validation with modification recommendations or approval

**Data Access:**
- Read: Ethical validation report, all previous agent outputs
- Write: HistoryLog (workflow completion)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Review ethical validation and apply recommended modifications
- Identify and resolve contradictions in user profile
- Document information gaps for future exploration
- Prepare guidance for Mentor Agent on follow-up topics
- Finalize the user profile with all components
- Complete the onboarding workflow

**Processing Logic:**
1. Ethical Modification Implementation:
   - For each modification recommended by Ethical Oversight Agent:
     - Identify affected profile component
     - Apply recommended changes
     - Document modification in HistoryLog
   - If significant modifications are required:
     - Return to appropriate agent for revision
     - Update affected components
     - Resubmit for ethical validation

2. Contradiction Identification and Resolution:
   - Identify contradictions across agent assessments:
     - Trait assessments that conflict with behavioral evidence
     - Stated preferences that contradict observed patterns
     - Goals that conflict with expressed limitations
     - Beliefs that contradict reported behaviors
   - For each identified contradiction:
     - Apply resolution framework:
       1. User's explicit statements (highest priority)
       2. Consistent behavioral evidence
       3. Psychological interpretation with strong evidence
       4. Resource or environmental constraints
     - Determine resolution approach:
       - If resolvable with high confidence: Apply resolution and document rationale
       - If ambiguous: Flag for Mentor Agent follow-up and document specific questions
   - Document all contradictions and resolutions in HistoryLog

3. Information Gap Analysis:
   - Identify profile areas with low confidence ratings (<70%):
     - Trait assessments with limited supporting evidence
     - Domains with insufficient preference data
     - Goals with unclear motivations or importance
     - Beliefs with limited supporting evidence
   - For each identified gap:
     - Formulate specific questions to address the gap
     - Determine appropriate context for gathering information
     - Assign priority based on impact on strategy formation
   - Compile comprehensive information gap report

4. Mentor Agent Guidance Preparation:
   - Create structured guidance package for Mentor Agent:
     - Communication approach based on psychological profile
     - Prioritized list of contradictions requiring clarification
     - Information gaps to address in future interactions
     - Specific questions for each gap with suggested timing
     - Indicators for when to gather additional information
   - Include triggers for when to collect specific types of feedback:
     - Activity reaction patterns that inform trait assessment
     - Completion patterns that validate preference assessments
     - Response patterns that clarify goal importance

5. Profile Finalization:
   - Verify all required profile components are complete
   - Ensure proper relationships between all objects
   - Validate overall profile coherence and consistency
   - Set appropriate confidence ratings for all components
   - Add metadata about information gaps and contradictions

6. Workflow Completion:
   - Record workflow completion in HistoryLog
   - Document overall process metrics and outcomes
   - Prepare transition to Wheel Generation workflow
   - Finalize all data model objects and relationships

**Output & Next Step:**
- To Mentor Agent: Profile presentation package containing:
  - Complete finalized user profile
  - Key insights for initial sharing
  - Communication guidance based on profile
  - Contradiction resolution guidance
  - Information gap questions for future interactions
  - Transition to Wheel Generation guidance

## Step 12: Mentor Agent - Profile Presentation and Progressive Profiling

**Entry Point:**
- Trigger: Receipt of profile presentation package from Orchestrator
- Input: Complete user profile with presentation guidance, contradiction resolution needs, and information gap questions

**Data Access:**
- Read: Complete user profile, presentation guidance, contradiction list, information gap questions
- Write: HistoryLog (presentation completed)
- Memory Access:
  - Write: Communication preferences, effective approaches, contradiction contexts, information gap priorities, progressive profiling needs

**Core Responsibilities:**
- Present key profile insights to the user in an engaging way
- Explain how the profile will inform their Game of Life experience
- Set appropriate expectations for next steps
- Capture user feedback on the initial profile
- Document communication approaches in memory
- Record contradiction contexts and information gap priorities for future interactions
- Transition to Wheel Generation workflow

**Processing Logic:**
1. Communication Approach Selection:
   - Analyze trust level and psychological profile to determine appropriate tone
   - Select communication style aligned with user preferences
   - Choose appropriate metaphors and framing for explanation
   - Adapt presentation depth based on user characteristics

2. Profile Insight Presentation:
   - Share selected key insights from the profile:
     - Strengths and interests identified
     - Goals and aspirations documented
     - Preferences and patterns noted
   - Frame insights in positive, growth-oriented language
   - Connect insights to user's stated objectives
   - Maintain appropriate depth based on trust phase
   - Note user responses to specific insights for memory

3. Initial Contradiction Exploration:
   - For high-priority contradictions flagged by Orchestrator:
     - Identify natural conversation opportunities to explore the contradiction
     - Frame exploration as clarification rather than challenge
     - Document user responses that help resolve the contradiction
     - Note context that may explain apparent contradictions
   - Reserve complex contradictions for future interactions based on trust level

4. System Explanation:
   - Explain how the profile will inform their experience:
     - How activities are selected based on profile
     - How challenge levels are calibrated to trust level
     - How the system adapts based on engagement
   - Frame explanation according to trust phase:
     - Foundation Phase: Emphasize safety and alignment
     - Expansion Phase: Highlight growth and exploration

5. Next Steps Introduction:
   - Explain the Wheel Generation process:
     - How the wheel presents personalized activities
     - The philosophy of controlled randomness
     - The value of commitment to the process
   - Set appropriate expectations:
     - What user can expect in their first wheel
     - How feedback helps refine the experience
     - The journey of trust development

6. Feedback Collection:
   - Gather initial reactions to the profile insights
   - Note areas of strong agreement or disagreement
   - Identify areas where user provides additional context
   - Document feedback for profile refinement

7. Memory Formation:
   - Document effective communication approaches:
     - Which metaphors resonated with the user
     - What tone and style received positive response
     - Which insights generated strongest engagement
   - Record contradiction contexts for future resolution:
     - Which contradictions seemed most significant
     - User responses that help clarify contradictions
     - Environmental or contextual factors affecting inconsistencies
     - Priority order for addressing remaining contradictions
   - Map information gap exploration strategy:
     - Which gaps are most critical to strategy formation
     - Appropriate contexts for gathering specific information
     - Natural conversation opportunities for exploration
     - Estimated timeline for progressive profiling
   - Note topics that generated strong engagement:
     - Areas where user provided detailed responses
     - Topics that triggered emotional reactions
     - Subjects user seemed eager to explore further

8. Workflow Transition:
   - Confirm user readiness to proceed to wheel generation
   - Address any remaining questions about the process
   - Create smooth handoff to Wheel Generation workflow
   - Complete onboarding process in HistoryLog

**Output & Next Step:**
- To System: Transition to Wheel Generation workflow
- To HistoryLog: Complete onboarding record with feedback
- To Memory: Updated communication preferences, contradiction contexts, and information gap priorities
