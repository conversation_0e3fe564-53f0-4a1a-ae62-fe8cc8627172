@startuml gr
skinparam backgroundColor white
skinparam nodeSep 50
skinparam rankSep 70

title Game of Life Onboarding Flow - Langgraph Implementation

' Define nodes (agents)
rectangle "OrchestratorInitNode" as orchestratorInit #LightBlue
rectangle "ResourceCapacityNode" as resourceCapacity #LightGreen
rectangle "OrchestratorTransition1Node" as orchestratorTrans1 #LightBlue
rectangle "EngagementPatternNode" as engagementPattern #LightYellow
rectangle "OrchestratorTransition2Node" as orchestratorTrans2 #LightBlue
rectangle "PsychologicalMonitoringNode" as psychMonitoring #LightPink
rectangle "OrchestratorTransition3Node" as orchestratorTrans3 #LightBlue
rectangle "StrategyFormationNode" as strategyFormation #LightCyan
rectangle "EthicalOversightNode" as ethicalOversight #LightGray
rectangle "OrchestratorFinalNode" as orchestratorFinal #LightBlue
rectangle "ErrorHandlerNode" as errorHandler #Pink

' Define the conditional routers
diamond "CheckResourceCompletion" as checkResource #LightBlue
diamond "CheckEngagementCompletion" as checkEngagement #LightBlue
diamond "CheckPsychologicalCompletion" as checkPsych #LightBlue
diamond "CheckStrategyCompletion" as checkStrategy #LightBlue
diamond "CheckEthicalIssues" as checkEthical #LightBlue

' Define flow
[*] --> orchestratorInit
orchestratorInit --> resourceCapacity : state
resourceCapacity --> checkResource : state
checkResource --> orchestratorTrans1 : complete
checkResource --> resourceCapacity : incomplete
orchestratorTrans1 --> engagementPattern : state
engagementPattern --> checkEngagement : state
checkEngagement --> orchestratorTrans2 : complete
checkEngagement --> engagementPattern : incomplete
orchestratorTrans2 --> psychMonitoring : state
psychMonitoring --> checkPsych : state
checkPsych --> orchestratorTrans3 : complete
checkPsych --> psychMonitoring : incomplete
orchestratorTrans3 --> strategyFormation : state
strategyFormation --> checkStrategy : state
checkStrategy --> ethicalOversight : complete
checkStrategy --> strategyFormation : incomplete
ethicalOversight --> checkEthical : state
checkEthical --> orchestratorFinal : approved
checkEthical --> strategyFormation : major_issues
checkEthical --> psychMonitoring : psych_issues
orchestratorFinal --> [*] : complete

' Error paths
orchestratorInit -[#red]-> errorHandler : error
resourceCapacity -[#red]-> errorHandler : error
engagementPattern -[#red]-> errorHandler : error
psychMonitoring -[#red]-> errorHandler : error
strategyFormation -[#red]-> errorHandler : error
ethicalOversight -[#red]-> errorHandler : error
orchestratorFinal -[#red]-> errorHandler : error

' Add technical details as notes
note bottom of orchestratorInit
  Tools: get_user_profile, record_user_feedback
  Models: Creates UserProfile, HistoryLog
  State: Initializes workflow_id, organizes questionnaire data
endnote

note bottom of resourceCapacity
  Tools: get_user_profile
  Models: Reads GenericEnvironment, GenericResource, GenericSkill
  Creates: UserEnvironment, UserResource, Inventory, Skill, UserLimitation
  Output: resource_context with confidence scores
endnote

note bottom of engagementPattern
  Tools: get_user_profile, get_recent_interactions
  Models: Creates Preference records
  Memory: Domain engagement patterns
  Output: engagement_patterns with confidence scores
endnote

note bottom of psychMonitoring
  Tools: get_user_profile, update_current_mood
  Models: Creates Demographics, UserTraitInclination,
  Belief, UserGoal, Inspiration, TrustLevel, CurrentMood
  Output: psychological_profile with confidence scores
endnote

note bottom of strategyFormation
  Tools: get_user_profile, get_user_history_analysis
  Memory: Creates goal-trait-skill mappings
  Output: strategy_framework with challenge calibration
endnote

note bottom of ethicalOversight
  Tools: get_user_profile
  Models: Creates HistoryLog validation entries
  Output: ethics_validation with recommendations
endnote

note bottom of orchestratorFinal
  Tools: get_user_profile, get_communication_guidelines
  Models: Updates UserProfile.onboarding_status
  Output: Profile Presentation Package
endnote

note right of errorHandler
  Error Recovery logic:
  - Logs detailed error information
  - Determines fallback actions
  - Can retry with modified parameters
  - Escalation pathways to human review
endnote

' Define state structure in a note
note left of orchestratorInit
<b>Onboarding State Structure</b>
{
  "user_profile_id": "uuid",
  "workflow_id": "uuid",
  "status": "in_progress",
  "current_phase": "resource_analysis",
  "agents_completed": ["orchestrator_init"],
  "error_state": null,
  "artifacts": {},
  "confidence_scores": {},
  "resource_context": {},
  "engagement_patterns": {},
  "psychological_profile": {},
  "strategy_framework": {},
  "ethics_validation": {},
  "questionnaire_responses": {}
}
endnote

@enduml