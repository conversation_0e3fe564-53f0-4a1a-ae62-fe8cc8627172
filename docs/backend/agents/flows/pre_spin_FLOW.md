# Pre-Spin Agent Workflow Documentation

*Last Updated: 2025-04-24*

## Purpose and Usage

This document provides a standardized format for all agents involved in the Pre-Spin workflow within the Game of Life system. It defines clear responsibility boundaries, establishes consistent interfaces, documents integrated decision-making logic, sets benchmarks for expected output quality, and maintains traceability between agent activities and system objectives.

## Agent Workflows

### Mentor Agent (Pre-Spin Initiator)

**Entry Point & Input:**
- Trigger: Completion of Wheel Generation scenario
- Input: Structured wheel package containing:
  - Wheel object with probability distributions
  - WheelItems (segments linked to ActivityTailored objects)
  - ActivityTailored objects with personalized content
  - User context information (resources, environment, time availability)

**Data Access:**
- Read: Wheel, WheelItems, ActivityTailored, UserEnvironment, Inventory, Capability, UserGoal, UserTraitInclination
- Write: HistoryLog, UserFeedbackLog
- Recommend: None
- Memory Access:
  - Read: Communication preferences, conversation tracking
  - Write: Updated communication patterns

**Core Responsibilities:**
- Present philosophical framing of pre-commitment value:
  - Explain transformative value of commitment before randomness
  - Emphasize how pre-commitment transforms anxiety into anticipation
  - Set clear expectations about follow-through once wheel is spun
- Conduct comprehensive activity review presentation:
  - Present summary of all wheel activities with probability distributions
  - Provide descriptions of resource requirements, time commitments, and challenge levels
  - Explain selection rationale connecting to goals, growth areas, and challenge calibration
- Facilitate structured feedback collection for any refused activities:
  - Gather resource/capacity concerns (equipment, time, environment, physical capability)
  - Collect psychological/preference concerns (challenge level, domain preferences, emotional readiness)
  - Document general feedback on probability distributions and alternative suggestions
- Secure explicit commitment to accept any resulting activity:
  - Request clear confirmation of willingness to commit to any outcome
  - Reinforce game philosophy of controlled randomness

**Processing Logic:**
1. Activity Review Preparation:
   - Extract wheel data and activity details
   - Format activities for presentation with clear descriptions
   - Connect activities to user goals and growth areas
   - Structure presentation using established communication preferences

2. Feedback Collection:
   - Present all activities and request review
   - Identify refused activities through direct questioning
   - Gather structured data on refusal reasons
   - Document feedback in appropriate data structures
   - Classify feedback by refusal type (capability, preference, probability)

3. Feedback Processing:
   - Package all feedback into structured format
   - Include metadata for downstream processing
   - Forward to Orchestrator for analysis and adaptation

**Expected Output Benchmark:**
- Activity review presentation containing:
  - Clear philosophical framing emphasizing commitment value
  - Comprehensive activity summaries with resource requirements and rationales
  - Structured prompts for feedback on specific activities
  - Explicit commitment request language
- Feedback collection package containing:
  - Structured refusal data with categorized reasons
  - Probability distribution concerns
  - Alternative suggestions
  - Direct quotes supporting each refusal category
- All outputs must meet these quality standards:
  - Non-judgmental language that encourages honest feedback
  - Clear connection between activities and user goals
  - Transparent explanation of challenge calibration
  - Explicit respect for user autonomy while emphasizing commitment value

**Output & Next Step:**
- To Orchestrator Agent: Structured feedback packet containing all refusals, concerns, and suggestions

### Orchestrator Agent (Refusal Processing)

**Entry Point & Input:**
- Trigger: Receipt of feedback packet from Mentor Agent
- Input: Structured feedback packet containing:
  - Activity refusals with categorized reasons
  - Probability distribution concerns
  - Alternative suggestions
  - Commitment level indicators

**Data Access:**
- Read: HistoryLog, UserFeedbackLog, UserTraitInclination, Trust
- Write: None
- Recommend: None
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Coordinate pattern analysis of refusal feedback:
  - Forward feedback packet to Engagement & Pattern Analytics Agent
  - Request historical pattern analysis for context
  - Incorporate pattern insights into processing decisions
- Classify and process individual activity refusals:
  - Categorize each refusal as capability-based, preference-based, or probability-based
  - Create processing queue for specialized agents
  - Determine appropriate processing path (adjustment vs. regeneration)
- Delegate specialized processing to appropriate agents:
  - Route capability concerns to Resource & Capacity Agent
  - Direct preference concerns to Psychological Monitoring Agent
  - Send probability adjustment requests to both agents for balanced assessment
- Integrate adaptation recommendations from specialized agents:
  - Combine outputs from all processing agents
  - Resolve conflicts using established priority hierarchy
  - Formulate comprehensive adaptation strategy
  - Select appropriate execution path (adjustment or regeneration)

**Processing Logic:**
1. Pattern Analysis Coordination:
   - Extract feedback metadata
   - Forward to Engagement & Pattern Analytics Agent
   - Await analysis completion

2. Refusal Classification:
   - For each refused activity:
     - Identify primary refusal category
     - Classify as capability, preference, or probability issue
     - Add to appropriate processing queue

3. Workflow Determination:
   - Assess overall refusal pattern
   - Determine if targeted adjustments are sufficient (adjustment path)
   - Identify if fundamental misalignment exists (regeneration path)
   - Select appropriate processing route

4. Specialized Processing Delegation:
   - Route capability issues to Resource & Capacity Agent
   - Direct preference issues to Psychological Monitoring Agent
   - Channel probability concerns to both agents
   - Await specialized processing completion

5. Adaptation Integration:
   - Combine all specialized agent outputs
   - Resolve conflicts using priority hierarchy
   - Create comprehensive adaptation plan
   - Forward to Wheel/Activity Agent for execution

**Expected Output Benchmark:**
- Adaptation plan containing:
  - Detailed specifications for each activity requiring adjustment
  - Challenge calibration parameters for replacements
  - Probability distribution modifications
  - Execution path selection with rationale
- All outputs must meet these quality standards:
  - Clear traceability between user concerns and adaptation recommendations
  - Balanced consideration of physical constraints and psychological needs
  - Consistent application of priority hierarchy for conflict resolution
  - Appropriate selection of execution path based on refusal patterns

**Output & Next Step:**
- To Wheel/Activity Agent: Comprehensive adaptation plan with activity replacement specifications and probability adjustments

### Engagement & Pattern Analytics Agent

**Entry Point & Input:**
- Trigger: Pattern analysis request from Orchestrator Agent
- Input: Structured feedback packet with refusal data and historical context request

**Data Access:**
- Read: HistoryLog, UserFeedbackLog, Preference, UserTraitInclination
- Write: None
- Recommend: None
- Memory Access:
  - Read: Domain engagement metrics, pattern confidence scores, activity response patterns
  - Write: Updated pattern insights

**Core Responsibilities:**
- Identify historical refusal patterns:
  - Examine HistoryLog for recurring activity refusal patterns
  - Identify domain-specific avoidance behaviors
  - Note consistent resource limitation claims
  - Distinguish between one-time and recurring justifications
- Analyze modification request trends:
  - Map recurring probability distribution concerns
  - Identify consistently adjusted activity types
  - Detect domain preference patterns
- Validate consistency with historical data:
  - Compare current feedback with historical preferences
  - Identify contradictions between past acceptances and current refusals
  - Evaluate refusal credibility based on participation history
- Generate pattern analysis report with recommendations:
  - Provide structured analysis of identified patterns
  - Flag inconsistencies requiring additional attention
  - Recommend approach to handling current refusals

**Processing Logic:**
1. Historical Data Retrieval:
   - Access HistoryLog entries for previous refusals
   - Extract UserFeedbackLog patterns
   - Retrieve stored pattern metrics from memory

2. Pattern Identification:
   - Map domain-specific refusal frequency
   - Identify recurring justification types
   - Calculate consistency metrics across sessions
   - Compare against baseline engagement patterns

3. Consistency Validation:
   - Cross-reference current and historical refusals
   - Identify contradictions and pattern breaks
   - Calculate credibility scores for current refusals
   - Flag suspicious patterns for special attention

4. Report Generation:
   - Compile structured pattern analysis
   - Provide evidence-based recommendations
   - Include confidence metrics for each pattern
   - Format for downstream processing

**Expected Output Benchmark:**
- Pattern analysis report containing:
  - Domain-specific refusal metrics with historical context
  - Justification consistency analysis across sessions
  - Contradiction flags with supporting evidence
  - Credibility assessment for current refusals
  - Recommended handling approach based on patterns
- All outputs must meet these quality standards:
  - Quantitative support for pattern identification
  - Clear distinction between one-time and recurring patterns
  - Evidence-based credibility assessment
  - Non-judgmental language focused on observable patterns

**Output & Next Step:**
- To Orchestrator Agent: Pattern analysis report with historical context and handling recommendations

### Resource & Capacity Management Agent

**Entry Point & Input:**
- Trigger: Receipt of capability-based refusal queue from Orchestrator
- Input: Structured capability concerns including:
  - Time availability limitations
  - Resource constraints
  - Environmental incompatibilities
  - Physical capability limitations
  - Skill requirement gaps

**Data Access:**
- Read: UserEnvironment, Inventory, Capability, Limitation, ResourceBase
- Write: UserEnvironment, Inventory, ResourceBase
- Recommend: Capability, Limitation
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Analyze specific constraint claims in detail:
  - Map time availability limitations against activity requirements
  - Validate resource constraints against inventory records
  - Cross-reference environmental needs with current context
  - Assess physical capability claims against established metrics
  - Evaluate skill requirement gaps against documented proficiency
- Validate resource context against known data:
  - Cross-check user-reported constraints with system records
  - Identify discrepancies between claims and documented resources
  - Update resource records when new information is provided
- Generate concrete adaptation specifications:
  - Modify duration parameters to fit time constraints
  - Identify alternative resource options from available inventory
  - Suggest environmental adjustments based on current context
  - Calibrate difficulty to accommodate physical capabilities
  - Adjust skill requirements based on user proficiency
- Make data model updates and recommendations:
  - Update objective resource records directly when appropriate
  - Create recommendation records for interpretive updates

**Processing Logic:**
1. Constraint Mapping:
   - Extract specific constraint claims from refusals
   - Categorize by constraint type (time, resource, environment, capability)
   - Structure for systematic validation

2. Resource Validation:
   - Cross-reference each constraint against system records
   - Identify validation status (confirmed, contradicted, new information)
   - Update objective resource records when new information exists
   - Flag discrepancies for further investigation

3. Adaptation Specification:
   - For each validated constraint:
     - Determine required parameter adjustments
     - Identify available alternatives from system records
     - Calculate feasibility metrics for each adjustment
     - Select optimal adaptation approach

4. Recommendation Generation:
   - Create direct updates for objective resource data
   - Formulate recommendation records for interpretive assessments
   - Package adaptation specifications with evidence
   - Format for integration by Orchestrator

**Expected Output Benchmark:**
- Constraint analysis document containing:
  - Validation status for each constraint claim
  - Evidence supporting or contradicting each claim
  - Direct data model updates for objective resource information
  - Recommendation records for interpretive assessments
- Adaptation specifications containing:
  - Modified duration parameters with justification
  - Alternative resource options from inventory
  - Environmental adjustment suggestions
  - Difficulty modification parameters
  - Skill requirement adaptations
- All outputs must meet these quality standards:
  - Evidence-based validation of all constraint claims
  - Clear distinction between objective and interpretive updates
  - Practical adaptation specifications with concrete parameters
  - Transparent reasoning for all recommendations

**Output & Next Step:**
- To Orchestrator Agent: Constraint analysis with adaptation specifications and data model updates

### Psychological Monitoring Agent

**Entry Point & Input:**
- Trigger: Receipt of preference-based refusal queue from Orchestrator
- Input: Structured preference concerns including:
  - Challenge level objections
  - Domain preferences
  - Emotional readiness issues
  - Goal alignment concerns

**Data Access:**
- Read: UserTraitInclination, Belief, UserGoal, Trust, Preference
- Write: None
- Recommend: UserTraitInclination, Belief, Trust
- Memory Access:
  - Read: Trait expression patterns, trust development history, growth area indicators
  - Write: Updated psychological patterns

**Core Responsibilities:**
- Conduct deep psychological assessment of refusals:
  - Analyze relationship between refusals and belief system
  - Connect refusal patterns to trait inclinations
  - Evaluate impact on trust level and challenge readiness
  - Assess alignment with stated goals and aspirations
- Distinguish between surface and deep motivations:
  - Identify surface-level objections expressed directly
  - Uncover deeper psychological needs driving refusals
  - Recognize growth opportunities despite resistance
  - Balance respect for boundaries with growth potential
- Evaluate probability distribution feedback:
  - Assess domain balance against psychological profile
  - Balance user preferences with growth needs
  - Ensure appropriate challenge calibration for trust level
  - Maintain variety across domains for holistic development
- Generate alternative activity recommendations:
  - Suggest alternatives addressing psychological concerns
  - Maintain appropriate challenge calibration
  - Support growth while respecting boundaries
  - Align with user goals and aspirations

**Processing Logic:**
1. Psychological Context Analysis:
   - Retrieve trait expression patterns
   - Access belief system records
   - Extract trust development history
   - Map refusals to psychological dimensions

2. Motivation Assessment:
   - Analyze stated reasons for psychological cues
   - Identify potential unstated concerns
   - Map to known resistance patterns
   - Distinguish legitimate boundaries from growth resistance

3. Probability Evaluation:
   - Assess domain distribution against psychological profile
   - Calculate ideal challenge calibration for current trust level
   - Analyze variety metrics across domains
   - Determine optimal probability adjustments

4. Alternative Generation:
   - For each refused activity:
     - Identify core psychological concerns
     - Generate alternatives addressing concerns
     - Calculate growth potential and boundary respect
     - Select optimal replacement candidates

**Expected Output Benchmark:**
- Psychological alignment report containing:
  - Analysis of refusal patterns against psychological profile
  - Surface vs. deep motivation assessment
  - Trust impact evaluation of proposed changes
  - Growth opportunity identification within boundaries
- Alternative activity recommendations containing:
  - Specific replacement suggestions with rationales
  - Challenge calibration parameters
  - Goal alignment evidence
  - Boundary respect assurances
- Probability adjustment recommendations containing:
  - Domain-specific adjustment parameters
  - Challenge calibration implications
  - Psychological impact assessment
- All outputs must meet these quality standards:
  - Respectful language acknowledging user autonomy
  - Clear distinction between boundaries and growth resistance
  - Evidence-based connections to psychological profile
  - Concrete parameters for adjustments and replacements

**Output & Next Step:**
- To Orchestrator Agent: Psychological alignment report with alternative recommendations and probability adjustments

### Wheel/Activity Agent

**Entry Point & Input:**
- Trigger: Receipt of adaptation plan from Orchestrator
- Input: Comprehensive adaptation plan containing:
  - Activity replacement specifications
  - Challenge calibration parameters
  - Probability distribution modifications

**Data Access:**
- Read: GenericActivity, ActivityTailored, Wheel, WheelItems, UserGoal, UserTraitInclination
- Write: ActivityTailored, Wheel, WheelItems
- Recommend: None
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Execute activity replacement process:
  - Access GenericActivity database for replacement candidates
  - Select appropriate replacements based on specifications
  - Tailor activities to user context and needs
  - Create new ActivityTailored objects with required parameters
- Adjust wheel probability distributions:
  - Modify probabilities based on approved adjustment requests
  - Ensure balanced representation across domains
  - Maintain overall challenge calibration aligned with trust level
  - Preserve appropriate variety for engagement
- Enhance value proposition for each activity:
  - Provide clear explanation addressing user concerns
  - Connect to goals and growth areas
  - Justify challenge level appropriateness
  - Highlight specific benefits and outcomes

**Processing Logic:**
1. Replacement Selection:
   - Query GenericActivity database with specifications
   - Apply filtering criteria from adaptation plan
   - Rank candidates by suitability metrics
   - Select optimal replacements

2. Activity Tailoring:
   - For each selected replacement:
     - Apply user-specific modifications
     - Adjust resource requirements
     - Calibrate challenge parameters
     - Connect to specific goals
     - Generate clear descriptions

3. Wheel Modification:
   - Update WheelItems with new ActivityTailored objects
   - Apply probability distribution adjustments
   - Verify overall balance and calibration
   - Ensure complete coverage of all segments

4. Value Enhancement:
   - For each new activity:
     - Generate clear rationale addressing concerns
     - Create goal connection statements
     - Develop challenge calibration explanation
     - Format for transparent presentation

**Expected Output Benchmark:**
- Modified wheel package containing:
  - Updated Wheel object with revised probability distributions
  - New WheelItems linked to replacement ActivityTailored objects
  - Complete activity descriptions with enhanced value propositions
  - Domain distribution metrics showing balanced representation
- All outputs must meet these quality standards:
  - Clear responsiveness to user concerns
  - Transparent connection to goals and growth areas
  - Appropriate challenge calibration with justification
  - Complete coverage of all wheel segments

**Output & Next Step:**
- To Ethical Oversight Agent: Modified wheel package for validation and review

### Ethical Oversight Agent

**Entry Point & Input:**
- Trigger: Receipt of modified wheel package from Wheel/Activity Agent
- Input: Modified wheel package containing updated activities and probability distributions

**Data Access:**
- Read: Wheel, WheelItems, ActivityTailored, UserTraitInclination, Belief, Trust, Ethical
- Write: None
- Recommend: Ethical
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Conduct ethical assessment of activity replacements:
  - Evaluate alignment with ethical principles
  - Verify respect for user autonomy and boundaries
  - Assess appropriate challenge calibration
  - Ensure growth potential without undue stress
- Validate overall wheel balance:
  - Verify appropriate domain distribution
  - Confirm challenge level variety
  - Assess alignment with user's growth trajectory
  - Ensure fair representation based on goals
- Review transparency and communication clarity:
  - Verify clear resource and time commitment information
  - Confirm transparent explanation of challenge nature
  - Assess clarity of growth connection and purpose
  - Ensure language respects user agency

**Processing Logic:**
1. Ethical Compliance Check:
   - Apply ethical principles to each activity
   - Verify boundary respect parameters
   - Assess challenge appropriateness
   - Evaluate growth-stress balance

2. Wheel Balance Validation:
   - Calculate domain distribution metrics
   - Verify challenge variety parameters
   - Map to growth trajectory alignment
   - Confirm fair representation

3. Transparency Verification:
   - Review activity descriptions for clarity
   - Assess resource requirement transparency
   - Validate challenge level explanations
   - Verify growth connection statements

4. Approval Decision:
   - Generate validation report
   - Apply necessary modifications
   - Make final approval determination
   - Format for orchestrator handoff

**Expected Output Benchmark:**
- Ethical validation report containing:
  - Compliance assessment for each activity
  - Wheel balance metrics with evaluations
  - Transparency verification results
  - Modification recommendations if needed
- Approved wheel package with:
  - Final adjustments addressing any ethical concerns
  - Certification of ethical compliance
  - Readiness for user presentation
- All outputs must meet these quality standards:
  - Evidence-based ethical assessments
  - Clear rationale for all approvals or modifications
  - Transparent application of ethical principles
  - User-centered language and framing

**Output & Next Step:**
- To Mentor Agent: Ethically validated wheel package for final user presentation

### Final Commitment (Mentor Agent)

**Entry Point & Input:**
- Trigger: Receipt of validated wheel package from Ethical Oversight
- Input: Finalized wheel package with ethical validation

**Data Access:**
- Read: Wheel, WheelItems, ActivityTailored, HistoryLog, UserFeedbackLog
- Write: HistoryLog, Commitment
- Recommend: Trust
- Memory Access:
  - Read: Communication preferences
  - Write: Updated communication patterns

**Core Responsibilities:**
- Present adjusted wheel with transparent explanations:
  - Highlight changes made in response to feedback
  - Explain how adjustments address expressed concerns
  - Provide clear rationale for probability distributions
  - Demonstrate responsiveness to user input
- Secure final explicit commitment:
  - Request clear confirmation of commitment to any outcome
  - Reinforce game philosophy of controlled randomness
  - Emphasize transformative value of surrendering direct choice
  - Obtain unambiguous user agreement before proceeding
- Initiate wheel spin and workflow transition:
  - Enable wheel spinning functionality
  - Record commitment in HistoryLog with context
  - Prepare handoff to Post-Spin workflow
  - Set appropriate expectations for post-spin experience

**Processing Logic:**
1. Adjustment Presentation:
   - Extract changes from original to final wheel
   - Generate clear explanations for each change
   - Format using established communication preferences
   - Structure for maximum transparency

2. Commitment Facilitation:
   - Present final commitment request
   - Reinforce philosophical framing
   - Process user response
   - Document commitment level

3. Transition Preparation:
   - Record final commitment in HistoryLog
   - Update Trust recommendations based on commitment quality
   - Prepare handoff package for Post-Spin workflow
   - Initiate wheel spin functionality

**Expected Output Benchmark:**
- Adjusted wheel presentation containing:
  - Clear explanation of all changes made
  - Transparent rationale for each adjustment
  - Evidence of responsiveness to feedback
  - Appropriate philosophical framing
- Commitment request containing:
  - Explicit language about acceptance of any outcome
  - Reinforcement of game philosophy
  - Clear emphasis on transformative value
  - Unambiguous request for confirmation
- All outputs must meet these quality standards:
  - Non-manipulative language respecting autonomy
  - Clear connection between user concerns and adjustments
  - Transparent explanation of randomness philosophy
  - Precise documentation of commitment for trust metrics

**Output & Next Step:**
- To Post-Spin Workflow: Handoff package with spin result, commitment context, and user expectations
