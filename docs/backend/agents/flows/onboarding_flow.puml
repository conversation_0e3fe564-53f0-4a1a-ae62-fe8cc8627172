@startuml
skinparam responseMessageBelowArrow true
skinparam sequenceMessageAlign center
skinparam maxMessageSize 250
skinparam wrapWidth 300
skinparam lifelineStrategy solid

title Game of Life Onboarding Flow Sequence Diagram

actor "User" as user
participant "Orchestrator Agent" as orchestrator #LightBlue
participant "Resource & Capacity Agent" as resource #LightGreen
participant "Engagement & Pattern Agent" as engagement #LightYellow
participant "Psychological Agent" as psych #LightPink
participant "Strategy Agent" as strategy #LightCyan
participant "Ethical Oversight Agent" as ethics #LightGray
database "Database" as db #WhiteSmoke

note over orchestrator, ethics: Step 1: Orchestrator Agent - Initial Workflow Setup

user -> orchestrator: Submit Questionnaire Responses

activate orchestrator
orchestrator -> db: Create UserProfile record
note right: Write: UserProfile, HistoryLog

orchestrator -> db: Create HistoryLog for workflow start

orchestrator -> resource: Send Environmental & Demographic Data
note right: Data Package: Environment, Demographics, Resources, Skills

deactivate orchestrator

note over orchestrator, ethics: Step 2: Resource & Capacity Agent - Environmental Context Establishment

activate resource
resource -> db: Access GenericEnvironment templates
note right: Read: GenericEnvironment templates

resource -> db: Access GenericResource catalog
note right: Read: GenericResource catalog

resource -> db: Access GenericSkill catalog
note right: Read: GenericSkill catalog

resource -> db: Create UserEnvironment record
note right: Write: UserEnvironment

resource -> db: Create UserEnvironmentPhysicalProperties
note right: Write: UserEnvironmentPhysicalProperties

resource -> db: Create UserEnvironmentSocialContext
note right: Write: UserEnvironmentSocialContext

resource -> db: Create UserEnvironmentActivitySupport
note right: Write: UserEnvironmentActivitySupport

resource -> db: Create UserResource records
note right: Write: UserResource

resource -> db: Create Inventory record
note right: Write: Inventory

resource -> db: Create Skill records
note right: Write: Skill

resource -> db: Create UserLimitation records (physical)
note right: Write: UserLimitation (physical)

resource --> orchestrator: Return Resource Context Report
note left: Confidence Ratings for Environment, Resources, Skills, Limitations

deactivate resource

note over orchestrator, ethics: Step 3: Orchestrator Agent - First Transition

activate orchestrator
orchestrator -> db: Update HistoryLog (workflow progression)
note right: Write: HistoryLog

orchestrator -> engagement: Send Engagement Data Package
note right: Data Package: Preferences, Activities, Patterns

deactivate orchestrator

note over orchestrator, ethics: Step 4: Engagement & Pattern Analytics Agent - Preference and Pattern Analysis

activate engagement
engagement -> db: Access Resource Context (read-only)
note right: Read: UserEnvironment, Skills, Limitations

engagement -> db: Create Preference records for domain preferences
note right: Write: Preference (domain preferences)

engagement -> db: Create Preference records for temporal patterns
note right: Write: Preference (temporal patterns)

engagement -> db: Create Preference records for format preferences
note right: Write: Preference (format preferences)

engagement -> engagement: Form memory for domain engagement patterns
note right: Memory: Domain engagement patterns

engagement -> engagement: Form memory for behavioral tendencies
note right: Memory: Behavioral tendencies

engagement -> engagement: Form memory for activity completion predictors
note right: Memory: Activity completion predictors

engagement --> orchestrator: Return Engagement Pattern Report
note left: Confidence Ratings for Preferences, Patterns, Tendencies

deactivate engagement

note over orchestrator, ethics: Step 5: Orchestrator Agent - Second Transition

activate orchestrator
orchestrator -> db: Update HistoryLog (workflow progression)
note right: Write: HistoryLog

orchestrator -> psych: Send Psychological Data Package
note right: Data Package: Personality, Beliefs, Goals, Context

deactivate orchestrator

note over orchestrator, ethics: Step 6: Psychological Monitoring Agent - Comprehensive Psychological Assessment

activate psych
psych -> db: Access GenericTrait data
note right: Read: GenericTrait

psych -> db: Access Resource Context (read-only)
note right: Read: Resource Context

psych -> db: Access Engagement patterns (read-only)
note right: Read: Engagement patterns

psych -> db: Create Demographics record
note right: Write: Demographics

psych -> db: Create UserTraitInclination records
note right: Write: UserTraitInclination

psych -> db: Create Belief records
note right: Write: Belief

psych -> db: Create BeliefEvidence records
note right: Write: BeliefEvidence

psych -> db: Create UserGoal records (Intention/Aspiration)
note right: Write: UserGoal (Intention/Aspiration)

psych -> db: Create Inspiration records
note right: Write: Inspiration

psych -> db: Create GoalInspiration connections
note right: Write: GoalInspiration

psych -> db: Create UserLimitation records (psychological)
note right: Write: UserLimitation (psychological)

psych -> db: Create TrustLevel record
note right: Write: TrustLevel

psych -> db: Create CurrentMood record
note right: Write: CurrentMood

psych -> psych: Form memory for psychological growth trajectories
note right: Memory: Psychological growth trajectories

psych -> psych: Form memory for trait-belief-behavior connections
note right: Memory: Trait-belief-behavior connections

psych --> orchestrator: Return Comprehensive Psychological Profile
note left: Confidence Ratings for Traits, Beliefs, Goals, Trust

deactivate psych

note over orchestrator, ethics: Step 7: Orchestrator Agent - Third Transition

activate orchestrator
orchestrator -> db: Update HistoryLog (workflow progression)
note right: Write: HistoryLog

orchestrator -> strategy: Send Comprehensive Data Package
note right: Data Package: All previous agent outputs

deactivate orchestrator

note over orchestrator, ethics: Step 8: Strategy Agent - Strategy Formation

activate strategy
strategy -> strategy: Analyze goal-trait-skill alignment
note right: Strategy analysis based on all inputs

strategy -> strategy: Identify development opportunities
note right: Based on trait-goal alignment

strategy -> strategy: Calibrate challenge strategy
note right: Based on trust phase

strategy -> strategy: Calculate domain engagement strategy
note right: Based on preferences and needs

strategy -> strategy: Form memory for goal-trait-skill mapping
note right: Memory: Goal-trait-skill mapping

strategy -> strategy: Form memory for challenge calibration rationale
note right: Memory: Challenge calibration

strategy -> strategy: Form memory for domain engagement strategy
note right: Memory: Domain engagement strategy

strategy --> orchestrator: Return Strategy Framework
note left: Strategy for wheel generation

deactivate strategy

note over orchestrator, ethics: Step 9: Ethical Oversight Agent - Profile Validation

activate orchestrator
orchestrator -> ethics: Send Complete User Profile
note right: Data Package: All components

deactivate orchestrator

activate ethics
ethics -> ethics: Review profile for ethical concerns
note right: Balance assessment

ethics -> ethics: Verify psychological safety boundaries
note right: Safety validation

ethics -> ethics: Evaluate handling of sensitive information
note right: Privacy review

ethics -> db: Create HistoryLog validation record
note right: Write: HistoryLog (validation)

ethics --> orchestrator: Return Ethical Validation Report
note left: Recommendations or approval

deactivate ethics

note over orchestrator, ethics: Step 10: Orchestrator Agent - Final Integration

activate orchestrator
orchestrator -> orchestrator: Apply ethical modifications if needed

orchestrator -> orchestrator: Resolve profile contradictions

orchestrator -> orchestrator: Identify information gaps

orchestrator -> db: Update HistoryLog (workflow completion)
note right: Write: HistoryLog

orchestrator -> db: Finalize onboarding status in UserProfile
note right: Update: UserProfile.onboarding_status

orchestrator -> user: Send Profile Presentation Package
note left: Complete onboarding workflow
deactivate orchestrator

@enduml