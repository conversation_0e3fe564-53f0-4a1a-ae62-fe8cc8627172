# Discussion Agent Workflow Documentation

*Last Updated: 2025-04-24*

## 1. Overview and Purpose

This document outlines the agent workflow designed for handling conversational interactions that fall outside the scope of other specific workflows (like wheel generation or activity feedback). Its primary purposes include:

1.  **General Conversation & Clarification:** Engaging the user when their input doesn't map clearly to a defined workflow (as determined by the `ConversationDispatcher`), when they explicitly request to chat (e.g., via `metadata.requested_workflow='discussion'`), or when handling queries that require clarification before proceeding. This serves as the default conversational fallback.
    *   *(Note: While the Mentor might gently probe for user goals or readiness for activities during general chat, its primary function here is to maintain an engaging, supportive conversation aligned with the user's immediate input, rather than forcing a specific outcome.)*
2.  **Targeted Information Collection:** Triggered by the `ConversationDispatcher` when essential information (e.g., time availability for wheel generation, activity ID for feedback) is missing to initiate another *specific* workflow. The Discussion Flow collects this information conversationally before terminating, allowing the *next* user interaction to potentially trigger the target workflow.
3.  **Error Handling Communication:** Communicating certain types of system errors or ambiguities to the user in a conversational manner.


This flow leverages the `MentorAgent` as the primary conversational interface, ensuring consistency in the user's interaction with the system persona.

## 2. Workflow Architecture

### Conversation Flow

1.  **Trigger:**
    *   **Dispatcher Action Required:** The `ConversationDispatcher` classifies an intent but identifies missing essential information (`action_required`). It routes to this workflow, passing the `missing_field` and potentially the `target_workflow` name (for context, not direct transition) to collect the information.
    *   **Dispatcher Fallback/Ambiguity:** The `ConversationDispatcher` cannot confidently classify the user's intent into a specific workflow, identifies a direct question requiring clarification, or classifies the intent as general `discussion`.
    *   **Explicit User Request:** User sends a `chat_message` with `metadata.requested_workflow = "discussion"`.

2.  **Initiation:** The `ConversationDispatcher` initiates the LangGraph Discussion Workflow via a Celery task, providing an initial state packet containing the trigger type, user message context, user profile ID, session name, and potentially `missing_field`, `target_workflow` (as context), and `collection_goal`. The dispatcher immediately sends a `workflow_status: initiated` message back to the client.

3.  **LangGraph Discussion Workflow:**
    *   The asynchronous Celery task executes the workflow.
    *   Processes the initial state through a sequence of agents, primarily involving the `MentorAgent`.
    *   Manages conversational state within the LangGraph execution, tracks the collection goal (if applicable), and gathers necessary information or provides clarification.
    *   Determines completion criteria (e.g., information collected, clarification provided, or conversation turn limit reached).
    *   **Termination:** Upon completion, the workflow sends its final message(s) (e.g., Mentor's response) back to the client via the Channels layer (using the session name from the initial state) and then **terminates**.This workflow **does not** transition directly back to a `target_workflow`. It follows the standard asynchronous pattern outlined in `ApiContract.md` and `workflow_analysis.md`. The workflow completes its task (chat or info collection) and ends. The *next* user message will be processed independently by the `ConversationDispatcher`, which can then use any newly available context (e.g., collected info stored in `HistoryLog`) to potentially launch the originally intended `target_workflow`.*
    *   **Looping Mechanism:** Utilizes LangGraph's state and conditional edges to loop back to the `MentorAgent` node until the `collection_goal` or clarification objective is met, or the conversation naturally concludes.

4.  **Output:** Sends conversational responses from the `MentorAgent` to the WebSocket consumer for delivery to the client during its execution. The final step is termination after sending the last message.

## 3. Agent Nodes and Responsibilities

*(Note: This section follows the structure of other flow documents)*

### 3.1. Orchestrator Agent (Entry & Transition Management)

*   **Entry Point & Input:**
    *   Trigger: Workflow initiated by `ConversationDispatcher` or internal routing.
    *   Input: Initial state packet containing `trigger_type`, `user_message`, `user_profile_id`, `session_metadata`, and potentially `missing_field`, `target_workflow`, `collection_goal`.
*   **Data Access:**
    *   Read: `HistoryLog`, `UserProfile`, `TrustLevel`.
    *   Write: `HistoryLog` (workflow events, state transitions).
    *   Recommend: None.
    *   Memory Access: Reads the transient LangGraph state packet during execution. *(Persistent memory like communication patterns is typically associated with the Mentor Agent, not the Orchestrator).*


*   **Core Responsibilities:**
    *   Validates the incoming request and context from the initial state packet.
    *   Determines the initial goal (e.g., collect `missing_field`, clarify query, or engage in general chat).
    *   Routes the conversation to the `MentorAgent` with the appropriate context and goal.
    *   Receives completion signals from the `MentorAgent` (e.g., information collected, clarification provided, conversation ended).
    *   Determines the workflow should terminate.
    *   Manages overall workflow state within the LangGraph execution and handles errors.
*   **Processing Logic:**
    *   Parses the initial state packet to understand the discussion's purpose.
    *   Sets up the initial conversational goal for the `MentorAgent`.
    *   Monitors conversation progress based on `MentorAgent` updates.
    *   Evaluates if the collection/clarification goal has been met based on Mentor signals.
    *   Signals the workflow's termination point.
*   **Expected Output Benchmark:**
    *   Clear routing instructions for the `MentorAgent`.
    *   Accurate state management reflecting the discussion goal within the LangGraph execution.
    *   Correct signaling for workflow termination upon goal completion or conversation end.
*   **Output & Transition:**
    *   To `MentorAgent`: Context packet with conversational goal.
    *   Upon completion: Signals workflow end within the LangGraph execution. (No direct transition to another workflow).

### 3.2. Mentor Agent (Core Conversational Interaction)

*   **Entry Point & Input:**
    *   Trigger: Routing from `OrchestratorAgent`.
    *   Input: Context packet including the conversational goal (e.g., collect specific information, clarify a topic), user message history, user profile insights.
*   **Data Access:**
    *   Read: `UserProfile`, `TrustLevel`, `Beliefs`, `Goals`, `HistoryLog` (for context).
    *   Write: `HistoryLog` (conversation turns), `UserFeedbackLog` (implicit feedback).
    *   Recommend: Potential updates to `TrustLevel` based on interaction quality.
    *   Memory Access:
    *   Read: Persistent memory regarding communication preferences, effective interaction patterns for this user. Reads transient LangGraph state (including conversation history within the current flow).
    *   Write: Updates transient LangGraph state (e.g., collected info, `is_goal_met` flag). May contribute to updating persistent memory stores post-workflow via separate mechanisms if designed.
*   **Core Responsibilities:**
    *   Engage the user in natural, empathetic conversation based on the defined goal (information collection, clarification, or general chat).
    *   If collecting information: Ask clarifying questions, guide the user to provide the `missing_field` data, potentially perform basic validation.
    *   If clarifying: Explain concepts, answer questions, ensure user understanding.
    *   If general chat: Maintain engaging conversation aligned with the system persona, potentially gently probing for user needs or goals without being pushy.
    *   Adapt communication style based on user profile, `TrustLevel`, and persistent memory insights.
    *   Identify when the conversational goal (collection, clarification, or natural end of chat) is met.
    *   Handle conversational dead-ends or user confusion gracefully.
    *   Updates the LangGraph state with collected information or confirmation of clarification.
*   **Processing Logic:**
    *   Analyzes the conversational goal and user context provided in the LangGraph state.
    *   Formulates appropriate questions or statements using LLM capabilities guided by persona and goal.
    *   Parses user responses to extract relevant information or gauge understanding.
    *   Updates the transient LangGraph state (e.g., adding collected info, setting `is_goal_met` flag).
    *   Uses persistent memory insights (if available) and conversation history within the current flow state to maintain coherence.
    *   Evaluates if the `collection_goal` is met or if the general chat has reached a natural pause/conclusion.
    *   Sets completion status flags in the LangGraph state for the Orchestrator/conditional edges.
*   **Expected Output Benchmark:**
    *   Conversational turns that are natural, on-topic, and goal-oriented (or appropriately open-ended for general chat).
    *   Accurate updating of the LangGraph state with collected information.
    *   Clear signaling within the state whether the conversational goal was achieved.
    *   Responses aligned with the established Mentor persona and ethical guidelines.
*   **Output & Transition:**
    *   To User (via WebSocket/Channels layer): Conversational messages.
    *   Updates LangGraph State: Modifies the state packet with status updates, collected information, and completion signals for conditional routing or Orchestrator processing.



### 4.2. Conversational Loop Implementation

The core conversational loop, especially for information collection, is implemented using LangGraph's conditional routing:

1.  **Mentor Node Execution:** The `MentorAgent` node processes the current state, generates a response/question, analyzes the latest user input (from the updated state), and sets the `is_goal_met` flag in the state.
2.  **Conditional Edge:** After the `MentorAgent` node runs, a conditional edge checks `state.is_goal_met`:
    *   If `state.is_goal_met == False`, the edge routes execution back to the `MentorAgent` node, continuing the conversation.
    *   If `state.is_goal_met == True`, the edge routes execution to the `OrchestratorAgent` (or a dedicated finalization node) to handle the transition out of the loop.

This allows the conversation to continue iteratively until the specific goal (e.g., collecting `missing_field`) is achieved.

## 5. Workflow Termination (Replaces Transition Logic)

This workflow adheres to the standard asynchronous pattern used throughout the system:

1.  **Goal Completion:** The `MentorAgent` determines the conversational goal is met (information collected, clarification provided, or general chat concludes). It updates the LangGraph state accordingly (e.g., sets `is_goal_met = True`).
2.  **Loop Termination:** The conditional edge following the `MentorAgent` node routes execution to the `OrchestratorAgent` (or a finalization node) based on the `is_goal_met` flag.
3.  **Final Message & Termination:** The workflow sends its final message(s) to the user via the Channels layer. The Orchestrator node signals the end of the LangGraph execution. The Celery task completes.
4.  **Awaiting Next Interaction:** The system now waits for the *next* message from the user.
5.  **Dispatcher Reprocessing:** When the user sends a new message, the `ConversationDispatcher` processes it from scratch.
    *   If the previous Discussion Flow collected necessary information (which should now be available in the conversation history or potentially passed in metadata by the frontend), the Dispatcher can now successfully classify and initiate the originally intended `target_workflow` (e.g., `wheel_generation`).
    *   If the previous flow was general chat, the Dispatcher classifies the new message based on its content.

*   **Fallback:** If the `MentorAgent` cannot meet a specific collection goal after a reasonable number of turns (determined by graph logic/timeouts), the workflow should still terminate gracefully, potentially sending a message indicating difficulty and inviting the user to try again or rephrase. The Orchestrator manages this termination.

## 6. Key Data Model References

*   `HistoryLog`: Records conversation turns and workflow state changes.
*   `UserProfile`: Provides context about the user for the `MentorAgent`.
*   `TrustLevel`: Influences communication style.
*   Models related to the `missing_field` being collected (e.g., `Activity` if collecting `activity_id`).

## 7. Open Questions / Future Considerations

*   How to handle multi-turn information collection robustly?
*   Maximum number of turns before escalating or failing?
*   Specific validation logic for different types of collected information?
*   Integration with tool use within the discussion (if Mentor needs to look something up)?
*   Handling user digressions during information collection.
