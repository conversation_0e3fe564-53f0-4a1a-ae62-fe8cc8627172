# Wheel Generation Workflow

*Last Updated: 2025-04-24*

## Overview
This document outlines the agent workflow for wheel generation in the Goali MVP, implemented using LangGraph. It defines agent responsibilities, interactions, data flow patterns, and state transitions to guide implementation.

## Workflow Architecture

### Conversation Flow

1. User sends a message via WebSocket
2. **ConversationDispatcher** (system component, not a LangGraph node):
   - Extracts context from user message
   - Classifies the message intent and workflow type
   - Creates a context packet with standardized format
   - Routes to appropriate workflow (wheel_generation in this case)
   - Initiates the LangGraph workflow with initial state

3. **LangGraph Wheel Generation Workflow**:
   - Processes the context packet through the agent sequence
   - Maintains workflow state throughout the process
   - Returns results to WebSocket consumer for delivery to client

## Agent Nodes and Responsibilities

### 1. Orchestrator Agent (Entry Point)

**Entry Point & Input:**
- Trigger: Workflow initiated by ConversationDispatcher
- Input: Context packet containing:
  - User's current mood and emotional state
  - Environmental context and available resources
  - Time availability and constraints
  - Current thoughts and concerns
  - User profile ID and session metadata

**Data Access:**
- Read: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>mendation, TrustLevel
- Write: HistoryLog (workflow events), AgentRun
- Recommend: None (orchestrates other agents)

**Core Responsibilities:**
- Determine agent sequence based on workflow type
- Route to appropriate specialized agents
- Distribute relevant context to specialized agents
- Manage workflow state transitions
- Integrate outputs from multiple agents
- Resolve conflicts using priority framework
- Handle errors and implement recovery mechanisms

**Processing Logic:**
- Parse context packet to identify task requirements
- Determine optimal agent sequence based on task and context
- Extract relevant context subsets for specialized agents
- Track agent activation and workflow state
- Integrate specialized agent outputs to maintain consistency
- Apply priority hierarchy to resolve conflicting recommendations

**Expected Output Benchmark:**
- Structured routing decisions with:
  - Next agent designation
  - Workflow stage transitions
  - Integration of prior agent outputs
  - Error handling directives if needed

**Output & Transition:**
- Routing decision to next agent in sequence
- Next state: Resource Agent for initial wheel generation

### 2. Resource & Capacity Management Agent

**Entry Point & Input:**
- Trigger: Receipt of orchestration routing
- Input: Context packet containing:
  - Environmental context description
  - Time availability parameters
  - Physical and mental state indicators
  - User profile ID for resource lookups

**Data Access:**
- Read: UserEnvironment, Inventory, UserResource, Capability
- Write: Dynamic resource availability records
- Recommend: Resource capacity updates

**Core Responsibilities:**
- Analyze user's available resources and environment
- Evaluate time availability and constraints
- Assess physical and mental capability levels
- Identify resource limitations and opportunities

**Processing Logic:**
- Compare environmental context with UserEnvironment records
- Match available resources against Inventory
- Calculate time availability parameters
- Assess capability constraints

**Expected Output Benchmark:**
- Resource context report containing:
  - Environmental assessment with activity compatibility ratings
  - Resource inventory with availability classifications
  - Time availability with recommended activity durations
  - Capability assessment with physical and mental challenge boundaries
  - Confidence ratings for uncertain elements

**Output & Transition:**
- Resource context report to state
- Next state: Engagement & Pattern Analytics Agent

### 3. Engagement & Pattern Analytics Agent

**Entry Point & Input:**
- Trigger: Receipt of state with resource context
- Input: Context packet and resource context

**Data Access:**
- Read: HistoryLog, UserFeedbackLog, Preference
- Write: None
- Recommend: Pattern recognition updates, domain preferences
- Memory: Engagement patterns, activity response history

**Core Responsibilities:**
- Analyze historical activity engagement
- Identify domain preferences and aversions
- Detect completion vs. abandonment patterns
- Recommend domain distribution for the wheel

**Processing Logic:**
- Calculate completion rates by activity domain
- Identify time-of-day patterns
- Compare stated preferences with actual behavior
- Generate domain distribution recommendations

**Expected Output Benchmark:**
- Pattern analysis report containing:
  - Domain engagement metrics with completion rates
  - Trend analysis showing significant changes
  - Time-of-day pattern section identifying optimal timing
  - Activity type preference showing completion rates by format
  - Recommended domain distribution with percentage allocations

**Output & Transition:**
- Engagement analysis added to state
- Next state: Psychological Monitoring Agent

### 4. Psychological Monitoring Agent

**Entry Point & Input:**
- Trigger: Receipt of state with resource and engagement data
- Input: Context packet, resource context, and engagement analysis

**Data Access:**
- Read: UserTraitInclination, TrustLevel, Belief, Aspiration, Inspiration, GoalInspiration
- Write: CurrentMood parameters
- Recommend: Trust level adjustments, psychological baseline updates
- Memory: Trait patterns, trust development, growth areas

**Core Responsibilities:**
- Assess current psychological state
- Determine trust phase (foundation or expansion)
- Analyze trait expressions and growth opportunities
- Identify belief-based limitations or supports
- Recommend appropriate challenge calibration

**Processing Logic:**
- Evaluate current mood against historical patterns
- Determine trust phase based on metrics and history
- Compare trait expressions with activity requirements
- Identify traits needed for goal achievement
- Map beliefs to potential limitations or opportunities

**Expected Output Benchmark:**
- Psychological report containing:
  - Current state with mood assessment and cognitive state evaluation
  - Trust phase identification with supporting evidence
  - Trait profile analyzing HEXACO dimensions with growth indicators
  - Belief system section highlighting supportive or limiting beliefs
  - Growth recommendation section prioritizing development areas
  - Challenge calibration section with specific parameters

**Output & Transition:**
- Psychological assessment added to state
- Next state: Strategy Agent

### 5. Strategy Agent

**Entry Point & Input:**
- Trigger: Receipt of all specialized reports via state
- Input: Resource report, pattern analysis report, and psychological report

**Data Access:**
- Read: UserGoal records
- Write: None
- Recommend: None
- Memory: Baseline strategy, trust-based adaptations

**Core Responsibilities:**
- Synthesize multi-agent inputs into coherent strategy
- Calibrate challenge levels based on trust phase
- Determine domain distribution for activities
- Define activity selection criteria
- Balance comfort and growth based on user readiness

**Processing Logic:**
- Integrate insights from all agent reports
- Resolve conflicts using priority framework
- Apply trust phase modifiers to challenge levels
- Calculate optimal domain distribution
- Define activity selection parameters

**Expected Output Benchmark:**
- Strategy framework containing:
  - Challenge calibration with specific parameters for each trait dimension
  - Domain distribution with specific probability allocations for each domain
  - Resource constraint section specifying maximum duration and requirements
  - Activity selection criteria defining filtering rules and priorities
  - Growth connection section linking strategy to user goals
  - Strategic rationale section explaining key decisions

**Output & Transition:**
- Strategy framework added to state
- Next state: Wheel/Activity Agent

### 6. Wheel/Activity Agent

**Entry Point & Input:**
- Trigger: Receipt of strategy framework via state
- Input: Strategy framework with selection criteria and parameters

**Data Access:**
- Read: GenericActivity catalog, UserProfile, HistoryLog
- Write: Wheel, WheelItem, ActivityTailored objects
- Recommend: None

**Core Responsibilities:**
- Select appropriate activities from GenericActivity catalog
- Create personalized ActivityTailored objects
- Assign probability weights to wheel segments
- Ensure wheel meets strategic requirements
- Develop value propositions for activities

**Processing Logic:**
- Query GenericActivity catalog using strategy parameters
- Filter activities based on selection criteria
- Create tailored versions of selected activities
- Customize instructions for user context
- Assign probability weights according to strategy

**Expected Output Benchmark:**
- Complete wheel package containing:
  - Wheel object with metadata
  - 6-8 WheelItem objects with specific probability weights
  - ActivityTailored objects for each wheel item
  - Value propositions for each activity
  - Wheel construction report explaining selection rationales

**Output & Transition:**
- Wheel package added to state
- Next state: Ethical Oversight Agent

### 7. Ethical Oversight Agent

**Entry Point & Input:**
- Trigger: Receipt of complete wheel for validation
- Input: Complete wheel with activities and psychological vulnerability flags

**Data Access:**
- Read: System ethical principles, TrustLevel data
- Write: None
- Recommend: Safety modifications

**Core Responsibilities:**
- Review activities for ethical alignment
- Ensure psychological safety based on user vulnerabilities
- Validate appropriate challenge levels
- Verify transparency in activity descriptions
- Assess overall wheel balance

**Processing Logic:**
- Evaluate each activity against ethical principles
- Check challenge levels against psychological state
- Review resource requirements for safety
- Validate overall wheel composition
- Provide approval decisions with rationales

**Expected Output Benchmark:**
- Ethical validation report containing:
  - Activity approval section with status for each activity
  - Modification recommendation section with specific changes
  - Wheel-level assessment evaluating overall balance
  - Ethical rationale section connecting decisions to core principles
  - Safety consideration section highlighting potential vulnerabilities

**Output & Transition:**
- Ethical validation added to state
- Next state: Orchestrator Agent (Final Integration)

### 8. Orchestrator Agent (Final Integration)

**Entry Point & Input:**
- Trigger: Receipt of ethical validation via state
- Input: Ethical validation report with approval statuses and recommendations

**Data Access:**
- Read: All previous agent outputs in state
- Write: HistoryLog (workflow completion)
- Recommend: None

**Core Responsibilities:**
- Review ethical validation results
- Implement or request necessary modifications
- Finalize wheel for presentation
- Prepare presentation context for WebSocket delivery
- Document workflow completion

**Processing Logic:**
- Determine appropriate integration approach
- Implement simple modifications if needed
- Request revisions from appropriate agents if required
- Finalize wheel with complete metadata
- Prepare presentation guidelines

**Expected Output Benchmark:**
- Complete, validated wheel package containing:
  - Finalized Wheel object with metadata
  - Approved WheelItem objects with normalized probability weights
  - Validated ActivityTailored objects
  - Presentation guidelines for WebSocket delivery
  - Workflow summary with key insights

**Output & Transition:**
- Final wheel package and user response
- Next state: Final delivery to WebSocket via WorkflowResultHandler

## Memory Implementation

For MVP, the following key memory elements should be maintained:

### Psychological Monitoring Agent Memory
- Trait expression patterns and stability
- Trust development and phase transitions
- Growth areas based on goals and resistance patterns

### Engagement & Pattern Analytics Agent Memory
- Domain engagement metrics and confidence
- Activity response patterns (completion/abandonment)

### Strategy Agent Memory
- Baseline strategy parameters
- Trust-based adaptation patterns

Memory should be implemented as simple key-value stores with confidence scores, timestamps, and references to supporting evidence in the data model.

## Workflow Dependencies and Transitions

1. **Sequential Dependencies**:
   - Resource & Capacity Agent → Engagement & Pattern Agent → Psychological Monitoring Agent → Strategy Agent → Wheel/Activity Agent → Ethical Oversight Agent → Orchestrator Agent (Final)

2. **Conditional Paths**:
   - If ethical issues are found, Orchestrator may request modifications
   - Significant ethical concerns may require returning to Strategy Agent

3. **Error Handling**:
   - Agent timeouts trigger fallback to default parameters
   - Missing data causes conservative default assumptions
   - Workflow tracking ensures no steps are skipped

## LangGraph Implementation Details

The workflow is implemented using LangGraph's `StateGraph` with a defined state model:

```python
class WheelGenerationState(BaseModel):
    """
    State model for the wheel generation workflow.
    Tracks data flow between agents and current workflow stage.
    """
    # Workflow identification
    workflow_id: str
    user_profile_id: str
    user_ws_session_name: Optional[str] = None
    
    # Input/context data
    context_packet: Dict[str, Any]
    
    # Agent outputs
    resource_context: Optional[Dict[str, Any]] = None
    engagement_analysis: Optional[Dict[str, Any]] = None
    psychological_assessment: Optional[Dict[str, Any]] = None
    strategy_framework: Optional[Dict[str, Any]] = None
    wheel: Optional[Dict[str, Any]] = None
    ethical_validation: Optional[Dict[str, Any]] = None
    
    # Routing information
    next_agent: Optional[str] = None
    
    # Workflow state tracking
    current_stage: WorkflowStage = "orchestration_initial"
    last_agent: Optional[str] = None
    error: Optional[str] = None
    error_context: Optional[Dict[str, Any]] = None
    completed: bool = False
```

Each agent implementation follows the `LangGraphAgent` base class pattern:

```python
class AgentName(LangGraphAgent):
    async def process(self, state: WheelGenerationState) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        # Extract state information
        
        # Process based on agent's responsibilities
        
        # Return output_data and state_updates as a tuple
        # Add output_data to state updates
            state_updates["output_data"] = output_data
return state_updates
```

The routing logic in the graph is implemented with conditional edges that examine the current state to determine the next agent in the workflow.

## Key Data Model References

- **HistoryLog**: Central record of all system events and user interactions
- **UserFeedbackLog**: Structured storage of user feedback
- **UserProfile**: Core user identity and demographic information
- **UserEnvironment**: Current user environment details
- **Inventory**: Available resources and tools
- **UserTraitInclination**: Expression of personality traits
- **TrustLevel**: User's trust in system recommendations
- **Belief**: User's core beliefs affecting activity receptiveness
- **Aspiration/UserGoal**: User's stated goals and intentions
- **GenericActivity**: Template activities in the system catalog
- **ActivityTailored**: Personalized activities for specific users
- **Wheel/WheelItem**: The wheel mechanism and its segments
