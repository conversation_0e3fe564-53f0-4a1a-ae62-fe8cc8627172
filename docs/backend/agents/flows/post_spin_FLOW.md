# Post-Spin Agent Workflow Documentation

*Last Updated: 2025-04-24*

## Mentor Agent

**Entry Point & Input:**
- Trigger: User spin completion with selected activity
- Input: Selected ActivityTailored object containing:
  - Activity details (instructions, requirements, duration)
  - User context information
  - Reference to Pre-Spin Commitment record
  - Timestamps and activity metadata

**Data Access:**
- Read: ActivityTailored, UserTraitInclination, Pre-Spin Commitment record, TrustLevel
- Write: HistoryLog, UserFeedbackLog, CurrentActivity
- Recommend: Trust Metric adjustments based on acceptance/refusal patterns
- Memory Access:
  - Read: Communication preferences, conversation tracking
  - Write: Updated communication effectiveness insights

**Core Responsibilities:**
- Deliver initial response to wheel spin outcome:
  - Present selected activity with appropriate tone and enthusiasm
  - Frame activity in relation to user's goals and growth areas
  - Highlight benefits and expected outcomes
- Provide detailed activity instructions:
  - Deliver clear step-by-step guidance
  - Include resource requirements and preparation steps
  - Offer tips for successful completion
- Process emergency refusals when they occur:
  - Collect structured feedback on emergency circumstances
  - Gather trust and system feedback
  - Document alternative preferences and constraints
- Set Post-Activity expectations:
  - Explain importance of reflection after completion
  - Outline how feedback improves future recommendations
  - Emphasize value of experience documentation

**Processing Logic:**
1. Response Formulation:
   - Determine appropriate response type (acceptance or emergency refusal)
   - Select communication tone based on memory of effective engagement
   - Connect activity to user goals using Pre-Spin Commitment context
   - Structure response with clear sections for instructions and next steps

2. Emergency Response (if applicable):
   - Implement structured questioning to obtain detailed feedback
   - Document emergency context with timestamps
   - Package feedback with original commitment context
   - Organize data for pattern analysis

3. Documentation:
   - Record interaction outcomes in HistoryLog
   - Update CurrentActivity status
   - Package relevant data for Orchestrator Agent

**Expected Output Benchmark:**
- For Acceptance Path:
  - Congratulatory message with enthusiasm appropriate to user preferences
  - Activity instructions with clear step-by-step guidance
  - Goal connection section relating activity to user aspirations
  - Post-activity expectations with specific return instructions
- For Emergency Refusal Path:
  - Acknowledgment message showing understanding without judgment
  - Structured feedback collection with specific questions
  - Alternative options appropriate to emergency constraints
  - Session closure information if needed
- All outputs must meet these quality standards:
  - Personalized tone matching user communication preferences
  - Clear actionable instructions without ambiguity
  - Consistent reinforcement of commitment principle
  - Appropriate emotional calibration based on situation

**Output & Next Step:**
- To Orchestrator Agent: Complete interaction package with:
  - Selected response type (acceptance/refusal)
  - Collected user feedback (for refusals)
  - Activity context information
  - Interaction metadata

## Orchestrator Agent

**Entry Point & Input:**
- Trigger: Receipt of interaction package from Mentor Agent
- Input: Complete interaction package containing:
  - Response type (acceptance/refusal)
  - User feedback data (for refusals)
  - Activity context and metadata
  - Pre-Spin Commitment references

**Data Access:**
- Read: HistoryLog, UserFeedbackLog, ActivityTailored, Pre-Spin Commitment
- Write: HistoryLog, CurrentActivity status
- Recommend: None (delegates to specialized agents)
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Determine appropriate workflow path:
  - Route acceptances to activity preparation
  - Direct emergency refusals to pattern analysis
- Prepare activity context for Post-Activity workflow:
  - Package selected ActivityTailored object with instructions
  - Include context information about user state and environment
  - Reference Pre-Spin Commitment record for follow-through
  - Document anticipated completion parameters
- Coordinate emergency response planning:
  - Evaluate pattern analysis results
  - Select appropriate response path (alternative activity or session closure)
  - Delegate to appropriate agents based on selected path
- Ensure data continuity across workflow:
  - Maintain consistent context between workflow stages
  - Record state transitions in HistoryLog
  - Create appropriate handoffs to next workflow stage

**Processing Logic:**
1. Path Determination:
   - Analyze response type from Mentor Agent package
   - For acceptance: proceed to activity preparation
   - For emergency refusal: activate emergency protocol
   
2. Activity Preparation (Acceptance Path):
   - Record acceptance in data model
   - Structure activity context for Post-Activity handoff
   - Include commitment reference for follow-through tracking
   - Return prepared context to Mentor Agent

3. Emergency Handling (Refusal Path):
   - Forward structured feedback to Pattern Analytics Agent
   - Await pattern analysis results
   - Determine appropriate emergency response path
   - Coordinate agent delegation based on response type

**Expected Output Benchmark:**
- For Acceptance Path:
  - Post-Activity handoff package containing:
    - Complete activity instructions
    - User context information
    - Commitment reference data
    - Expected completion parameters
    - Trust metric implications
- For Emergency Refusal Path:
  - Emergency response package containing:
    - Pattern analysis results
    - Selected response path
    - Delegation instructions for specialized agents
    - Session status updates
- All outputs must meet these quality standards:
  - Complete context information without data loss
  - Clear path designation with appropriate metadata
  - Consistent references to original commitments
  - Proper structured formatting for receiving agents

**Output & Next Step:**
- For Acceptance Path:
  - To Mentor Agent: Activity preparation package
  - To Post-Activity Workflow: Activity context handoff (after Mentor Agent final communication)
- For Emergency Refusal Path:
  - To Pattern Analytics Agent: Structured feedback package
  - To Psychological Monitoring Agent or Session Management: Delegation instructions based on analysis results

## Engagement & Pattern Analytics Agent

**Entry Point & Input:**
- Trigger: Receipt of emergency feedback package from Orchestrator Agent
- Input: Structured feedback package containing:
  - Emergency context details
  - User response data
  - Historical references
  - Pre-Spin Commitment context

**Data Access:**
- Read: HistoryLog, UserFeedbackLog, Pre-Spin Commitment records
- Write: None (analytics only)
- Recommend: Trust Metric adjustments, UserTraitInclination insights
- Memory Access:
  - Read: Domain engagement metrics, pattern confidence scores, activity response patterns
  - Write: Updated pattern insights based on new data

**Core Responsibilities:**
- Analyze historical patterns related to emergencies:
  - Examine previous emergency refusals and contexts
  - Evaluate consistency of justifications across time
  - Assess completion rates for similar activities
- Classify emergency type based on evidence:
  - Categorize as genuinely unpredictable, potentially foreseeable, or preference-masked
  - Apply confidence scoring to classification
  - Identify patterns that may require system adjustments
- Assess trust impact implications:
  - Evaluate effect on established trust metrics
  - Consider pattern of commitment follow-through
  - Identify potential trust recovery approaches
- Provide pattern-based recommendations:
  - Suggest appropriate response approaches
  - Recommend future adjustments to activity selection
  - Identify potential user trait inclination updates

**Processing Logic:**
1. Historical Pattern Evaluation:
   - Query HistoryLog for previous emergency refusals
   - Analyze contextual similarities with current situation
   - Calculate consistency metrics across refusal instances
   - Identify timing patterns relative to commitment points

2. Emergency Classification:
   - Apply classification criteria to emergency context
   - Calculate confidence score for classification
   - Document supporting evidence for classification
   - Determine appropriate handling approach

3. Trust Analysis:
   - Calculate potential impact on trust metrics
   - Evaluate within context of historical follow-through
   - Identify appropriate trust maintenance strategies
   - Document for Post-Activity evaluation

**Expected Output Benchmark:**
- Emergency pattern analysis report containing:
  - Historical context section with pattern identification
  - Classification section with supporting evidence
  - Confidence assessment with statistical backing
  - Similar incident documentation with outcomes
- Trust impact assessment containing:
  - Metric impact projections
  - Recovery opportunity identification
  - Historical context for similar situations
  - Recommendation justification
- All outputs must meet these quality standards:
  - Evidence-based classifications with confidence scores
  - Clear pattern documentation with specific examples
  - Actionable recommendations with supporting rationale
  - Appropriate sensitivity to user context and history

**Output & Next Step:**
- To Orchestrator Agent: Complete pattern analysis package with:
  - Emergency classification with confidence score
  - Trust impact assessment
  - Recommended handling approach
  - Supporting evidence and pattern documentation

## Psychological Monitoring Agent

**Entry Point & Input:**
- Trigger: Delegation from Orchestrator for emergency alternative analysis
- Input: Emergency response package containing:
  - Pattern analysis results
  - Emergency classification and context
  - User psychological state indicators
  - Trust impact assessment

**Data Access:**
- Read: UserTraitInclination, Belief, TrustLevel, HistoryLog
- Write: None (analysis only)
- Recommend: Alternative activity parameters, trust recovery strategies
- Memory Access:
  - Read: Trait expression patterns, trust development history, growth area indicators
  - Write: Updated psychological patterns

**Core Responsibilities:**
- Assess user's current psychological state:
  - Analyze emotional response to emergency situation
  - Evaluate psychological impact of commitment interruption
  - Identify appropriate support framing needed
- Determine appropriate alternative activity parameters:
  - Consider emergency constraints and limitations
  - Evaluate psychological receptiveness to activity types
  - Identify feasible activities that support original objectives
- Develop trust recovery communication strategy:
  - Create approach to acknowledge genuine emergency
  - Maintain commitment principle appropriately
  - Support trust maintenance despite exception
- Provide comprehensive psychological context:
  - Connect emergency response to overall psychological profile
  - Document insights for future pattern recognition
  - Support appropriate challenge calibration

**Processing Logic:**
1. Psychological State Assessment:
   - Analyze emotional indicators from emergency feedback
   - Compare to historical pattern memory
   - Evaluate cognitive state during emergency
   - Determine appropriate emotional support approach

2. Alternative Activity Identification:
   - Extract constraint parameters from emergency context
   - Match constraints against activity requirements
   - Filter for psychological appropriateness
   - Prioritize activities supporting original objectives

3. Trust Recovery Strategy Development:
   - Analyze trust impact assessment
   - Reference memory for effective recovery approaches
   - Develop communication framework
   - Document approach for Mentor Agent implementation

**Expected Output Benchmark:**
- Emergency psychological assessment containing:
  - Current state evaluation with emotional indicators
  - Constraint analysis with activity implications
  - Trust impact evaluation with recovery potential
  - Support approach recommendations
- Alternative activity parameters containing:
  - Specific constraint accommodations
  - Connection to original growth objectives
  - Appropriate challenge calibration
  - Psychological receptiveness indicators
- Trust recovery communication strategy containing:
  - Key messaging components
  - Framing recommendations
  - Language suggestions
  - Potential pitfalls to avoid
- All outputs must meet these quality standards:
  - Evidence-based assessments with clear rationales
  - Practical recommendations considering all constraints
  - Balanced approach to commitment and flexibility
  - Appropriate sensitivity to user's psychological state

**Output & Next Step:**
- To Orchestrator Agent: Complete psychological analysis package with:
  - Emergency psychological assessment
  - Alternative activity parameters
  - Trust recovery communication strategy
  - Implementation recommendations

## Wheel/Activity Agent

**Entry Point & Input:**
- Trigger: Request from Orchestrator for emergency alternative generation
- Input: Alternative activity parameters containing:
  - Constraint specifications
  - Psychological receptiveness indicators
  - Connection to original objectives
  - Challenge calibration requirements

**Data Access:**
- Read: GenericActivity database, ActivityTailored history
- Write: None (generation only)
- Recommend: New ActivityTailored object
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Generate appropriate alternative activities:
  - Access GenericActivity database for candidates
  - Apply emergency constraint filters
  - Maintain connection to original growth objectives
  - Match current psychological state requirements
- Customize selected alternatives:
  - Adapt to specific emergency limitations
  - Maintain appropriate challenge level
  - Preserve original growth intent where possible
  - Include clear, detailed instructions
- Ensure quality and appropriateness:
  - Verify all constraints are respected
  - Confirm psychological suitability
  - Validate connection to growth objectives
  - Check for feasibility under emergency conditions

**Processing Logic:**
1. Alternative Selection:
   - Query GenericActivity database with constraint parameters
   - Apply psychological receptiveness filters
   - Rank by objective alignment
   - Select highest-ranked feasible activity

2. Activity Customization:
   - Apply specific constraint accommodations
   - Adjust challenge level based on parameters
   - Maintain connection to original objectives
   - Generate detailed, adapted instructions

3. Validation:
   - Verify constraint compliance
   - Confirm psychological appropriateness
   - Validate growth objective connection
   - Ensure complete instruction clarity

**Expected Output Benchmark:**
- Emergency alternative ActivityTailored object containing:
  - Complete activity definition
  - Detailed constraints accommodations
  - Step-by-step instructions
  - Required resources and environment
  - Expected outcomes and benefits
  - Connection to original growth objectives
- All outputs must meet these quality standards:
  - Full constraint compliance with explicit documentation
  - Clear, actionable instructions without ambiguity
  - Appropriate challenge level with justification
  - Explicit connection to user goals and growth areas

**Output & Next Step:**
- To Ethical Oversight Agent: Emergency alternative ActivityTailored object for validation

## Ethical Oversight Agent

**Entry Point & Input:**
- Trigger: Receipt of emergency alternative from Wheel/Activity Agent
- Input: Emergency alternative ActivityTailored object and communication strategy

**Data Access:**
- Read: UserTraitInclination, Belief, TrustLevel, HistoryLog
- Write: None (validation only)
- Recommend: Validated emergency alternative, approved communication strategy
- Memory Access: None (stateless for MVP)

**Core Responsibilities:**
- Validate ethical appropriateness of alternative:
  - Review for alignment with user well-being
  - Assess respect for user's current limitations
  - Verify maintenance of growth focus
  - Ensure psychological safety
- Review communication strategy:
  - Evaluate for blame or judgment elements
  - Verify balance of commitment and flexibility
  - Assess support for psychological safety
  - Check for appropriate framing
- Provide final validation:
  - Approve or request modifications
  - Document validation reasoning
  - Include any implementation guidance
  - Ensure complete ethical alignment

**Processing Logic:**
1. Ethical Assessment:
   - Review alternative against ethical criteria
   - Evaluate constraint accommodation appropriateness
   - Assess psychological impact potential
   - Verify well-being and growth orientation

2. Communication Review:
   - Analyze language for judgment elements
   - Evaluate commitment principle maintenance
   - Assess psychological safety support
   - Review framing appropriateness

3. Validation Determination:
   - Consolidate assessment results
   - Determine approval status
   - Document validation reasoning
   - Prepare validation package

**Expected Output Benchmark:**
- Validation assessment containing:
  - Ethical appropriateness evaluation
  - Constraint accommodation verification
  - Psychological safety confirmation
  - Growth orientation validation
- Communication review containing:
  - Judgment-free verification
  - Commitment principle balance assessment
  - Psychological safety support confirmation
  - Framing appropriateness evaluation
- All outputs must meet these quality standards:
  - Clear approval status with specific justification
  - Comprehensive coverage of all ethical considerations
  - Explicit reasoning for all validation components
  - Implementation guidance where appropriate

**Output & Next Step:**
- To Orchestrator Agent: Validation package with:
  - Approved emergency alternative
  - Validated communication strategy
  - Implementation guidance
  - Approval documentation
