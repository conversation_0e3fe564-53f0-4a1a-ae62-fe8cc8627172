# Workflow Switching & Data Flow Analysis (Refined)

*Last Updated: 2025-04-24*

**Version:** 3.0 (April 2, 2025)

**Goal:** To provide a detailed analysis of the `ConversationDispatcher` logic, clarify workflow triggering mechanisms (including metadata usage), explain the handling of ambiguous messages, and detail the asynchronous result handling process.

## Summary of Findings

The `ConversationDispatcher` acts as the central routing point for incoming user messages (`chat_message`, `spin_result`). It employs a prioritized classification strategy:
1.  **Explicit Metadata Check:** Checks for `metadata.requested_workflow` or `metadata.type='spin_result'`. This provides deterministic routing and bypasses further checks, optimizing performance by avoiding unnecessary history lookups or LLM calls.
2.  **Profile Completion Check:** If no metadata matches, checks user profile completion for onboarding.
3.  **History & Classification:** Only if the above checks fail (e.g., for a standard `chat_message`), it reads the workflow history and uses LLM/tool/rule-based classification to determine intent. This classification must consider the current conversation state (e.g., if a wheel was just presented) to differentiate between general discussion, feedback on a past activity (`activity_feedback`), or feedback on the current wheel (`pre_spin`).

Crucially, the dispatcher *initiates* workflows asynchronously via Celery tasks and does **not** handle the final results itself; the results are sent back directly from the completed Celery task via WebSockets. The overall "Pre-Spin" phase involves the `wheel_generation` workflow followed by an optional `pre_spin` feedback/modification workflow if triggered by the user.

**(See `docs/diagrams/dispatcher_workflow_v3.puml` or `dispatcher_workflow_v3.mermaid` for a visual representation)**

## Core Backend Workflows & Dispatcher Logic

Based on the implementation in `backend/apps/main/services/conversation_dispatcher.py`, the system primarily revolves around **five core workflows**, triggered and managed as follows. An additional optional workflow (`pre_spin_feedback`) can be triggered under specific contextual conditions.

1.  **`onboarding`:**
    *   **Purpose:** Guides new users through initial profile setup.
    *   **Trigger:** `ConversationDispatcher` checks profile completion via `_check_profile_completion` tool. If `< 50%`, this workflow is selected with high priority (after metadata checks).
    *   **Implementation:** Launched via Celery task, likely running `backend/apps/main/graphs/onboarding_graph.py`.
    *   **Analogy:** The "New Account Setup Wizard".

2.  **`wheel_generation`:**
    *   **Purpose:** Analyzes user context, profile, history, and psychological state to generate the *initial* personalized activity wheel. This is the first part of the backend process for the "Pre-Spin" phase.
    *   **Trigger:**
        *   **Explicit:** `metadata.requested_workflow == "wheel_generation"` in an incoming `chat_message`.
        *   **Implicit:** Classified from a `chat_message` text via LLM/tool/rules *after* metadata and profile checks fail, when the user is not currently reviewing a wheel. This is often the default/fallback workflow to get things started.
    *   **Implementation:** Launched via Celery task, running `backend/apps/main/graphs/wheel_generation_graph.py`. This involves an extensive multi-agent workflow (Orchestrator, Resource, Engagement, Psychological, Strategy, Wheel/Activity, Ethical Oversight) as detailed in `docs/agents/flows/wheel_generation_FLOW.md`. The result is sent to the frontend, presenting the wheel.
    *   **Analogy:** The "Personalized Recommendation Engine" involving multiple specialist advisors preparing the initial proposal. The result (`wheel_data`) puts the frontend into the "Pre-Spin" state.

3.  **`post_spin`:**
    *   **Purpose:** Handles the user *committing* to an activity from the wheel (either the initial one from `wheel_generation` or a modified one after potential `pre_spin_feedback`). Presents the selected activity details and prepares for the user to perform it.
    *   **Trigger:** Deterministic receipt of a `spin_result` message type (sent from the frontend when the user interacts with the wheel UI). This is checked with high priority in the `ConversationDispatcher` via the metadata check, bypassing classification.
    *   **Implementation:** Launched via Celery task, likely running `backend/apps/main/graphs/post_spin_graph.py` (verify graph name). Involves fetching activity details and potentially interacting with other agents.
    *   **Analogy:** The "Order Confirmation & Instructions" step after *finalizing* the choice from the menu.

4.  **`activity_feedback`:**
    *   **Purpose:** Processes user reflections and feedback *after* they have completed (or attempted) a previously assigned activity (initiated via `post_spin`). Updates user profile and system memory.
    *   **Trigger:**
        *   **Explicit:** `metadata.requested_workflow == "activity_feedback"` in the incoming message.
        *   **Implicit:** Classified from `chat_message` text (e.g., "I finished...") via LLM/tool/rules.
    *   **Implementation:** Launched via Celery task. The task likely routes to specific agents/tools for processing feedback and updating the user profile.
    *   **Analogy:** The "Post-Meal Survey" or "Experience Debrief".

5.  **`discussion`:**
    *   **Purpose:** Handles general chat, questions, or when user intent is unclear or doesn't fit other core workflows (`onboarding`, `wheel_generation`, `activity_feedback`).
    *   **Trigger:** Classified from a `chat_message` text via LLM/tool/rules *after* metadata and profile checks fail, and when the context does *not* indicate the user is providing feedback on a currently displayed wheel (which would trigger `pre_spin_feedback` instead). The LLM prompt explicitly mentions defaulting to `discussion` if unsure.
    *   **Implementation:** Launched via Celery task, likely running `backend/apps/main/graphs/discussion_graph.py`.
    *   **Analogy:** The "Information Desk" or "General Chat Room".

---

**Optional Contextual Workflow:**

*   **`pre_spin_feedback`:**
    *   **Purpose:** Handles user feedback or modification requests *specifically* about the currently presented activity wheel, *before* the user commits via `spin_result`. Allows for adjustments based on immediate reactions.
    *   **Trigger:** This workflow is optional and context-dependent. After the `wheel_generation` workflow sends `wheel_data` to the frontend:
        *   If the user types a message (frontend sends `chat_message`), the dispatcher's classification logic (LLM/tool/rules) may identify the intent as `pre_spin_feedback` based on the message content and the context that a wheel is currently displayed.
        *   If the user interacts directly with the wheel UI (frontend sends `spin_result`), this workflow is bypassed entirely, and the `post_spin` workflow is triggered deterministically.
    *   **Implementation:** Launched via Celery task. Runs a workflow to analyze feedback and potentially modify the wheel (details in `docs/agents/flows/pre_spin_FLOW.md`). The result is typically an updated `wheel_data` message sent back to the frontend, keeping the user in the "Pre-Spin" state.
    *   **Analogy:** The optional "Revision Round" on a proposal *if* the user provides feedback via chat instead of immediately accepting/spinning via the UI.

*(Note: The dispatcher code also mentions `progress_review` as a potential classification target, suggesting it might be another planned or partially implemented workflow.)*

## Dispatcher Classification Logic & Metadata Handling

The `ConversationDispatcher._classify_message` method implements a clear priority order:

1.  **Explicit Metadata Check:**
    *   `metadata.requested_workflow`: If present in a `chat_message` (e.g., `"wheel_generation"`, `"activity_feedback"` sent from a UI button click), this directly determines the workflow (Confidence: 1.0). **Reason:** Provides deterministic routing for unambiguous UI actions, bypassing history lookups and classification.
    *   `metadata.type == "spin_result"`: If the message type is `spin_result`, this deterministically triggers the `post_spin` workflow (Confidence: 1.0). **Reason:** Handles the specific event of the user selecting an activity from the wheel, bypassing history lookups and classification.
2.  **Profile Completion Check:** If no explicit metadata matches, it checks profile status (`_check_profile_completion` tool). If `< 50%`, it triggers `user_onboarding` (Confidence: 0.95). **Reason:** Prioritizes guiding new users.
3.  **Intent Classification (Conditional on Context):** Only if none of the above apply (i.e., for a standard `chat_message` with no specific metadata and a complete profile), the dispatcher attempts to classify the intent based on message content *and* conversation context (potentially requiring a history lookup or state check):
    *   **Context is Key:** The classification must differentiate based on whether the user is currently reviewing a wheel (frontend "Pre-Spin" state) or not.
    *   **Possible Targets:**
        *   If reviewing a wheel: `pre_spin_feedback` (primary target for ambiguous chat in this state).
        *   If *not* reviewing a wheel: `discussion`, `wheel_generation` (requesting a new one), `activity_feedback` (discussing a past one), `progress_review`.
    *   **Process:**
        *   **LLM Classification (`_classify_with_llm`):** Used first if available, likely needs context/history.
        *   **Tool-Based Classification:** Fallback using `classify_workflow` tool.
        *   **Rule-Based Refinement (`_apply_classification_rules`):** Keyword matching and heuristics, crucial for distinguishing `pre_spin_feedback` from `discussion` based on context/state.
    *   **Default:** If confidence remains low, the default is likely `discussion` (if not in pre-spin state) or potentially `pre_spin_feedback` (if in pre-spin state).
    **Reason for this multi-step process:** To handle the ambiguity of free-form text input when no explicit intent is provided, leveraging context, LLMs, and rules to route correctly, especially distinguishing feedback on the current wheel (`pre_spin_feedback`) from other chat. History/state checks are deferred until this classification step is necessary.

## Asynchronous Workflow Execution & Result Handling

A critical aspect clarified by the code is that the `ConversationDispatcher` **does not execute the workflow itself nor handle its final result.**

1.  **Initiation:** After classifying the intent and building a context packet, the dispatcher calls `_launch_workflow`.
2.  **Asynchronous Task:** `_launch_workflow` triggers a Celery background task (`execute_graph_workflow.delay`). This task receives the `workflow_id`, `user_profile_id`, and the `initial_input` (which includes the context packet and the `user_ws_session_name`).
3.  **Dispatcher Response:** The dispatcher immediately returns a response to the WebSocket consumer, indicating the workflow has been *initiated* (providing the `workflow_id`, `workflow_type`, estimated time, etc.). This allows the frontend to show a loading state.
4.  **Workflow Execution:** The Celery worker picks up the task and executes the corresponding LangGraph workflow (e.g., `wheel_generation_graph.py`).
5.  **Result Sending:** Once the LangGraph workflow completes, the **Celery task** (or the graph logic itself) is responsible for:
    *   Formatting the final result (e.g., wheel data, chat message).
    *   Using the `user_ws_session_name` (passed in the initial context) to target the specific user's WebSocket connection.
    *   Sending the result back to the frontend using the Django Channels layer (e.g., `channel_layer.group_send`).
6.  **Frontend Receives Result:** The frontend's `WebSocketManager` receives the message asynchronously and updates the UI accordingly.

**Implication:** The dispatcher's role is purely classification and initiation. The actual work and result delivery happen in a separate, asynchronous process.

## Required Document & Code Updates (Confirmation & Refinement)

Based on the dispatcher code:

1.  **`docs/workflow_analysis.md`:** This document (updated to Version 3.0).
2.  **`docs/ApiContract.md`:** Confirm/add optional `metadata.requested_workflow` (string enum) to `chat_message` content. Define allowed values (`wheel_generation`, `activity_feedback`, `progress_review`, etc.). Ensure `spin_result` message type is clearly defined.
3.  **`PLANNING.md`:** Update architecture description to reflect the dispatcher's classification priority, the use of `metadata.requested_workflow`, and the asynchronous Celery-based execution/result handling.
4.  **`backend/apps/main/services/conversation_dispatcher.py`:** The current logic already prioritizes `metadata.requested_workflow` and `spin_result`. Ensure LLM prompts and classification rules align with desired behavior.
5.  **Frontend Code:**
    *   `frontend/src/services/WebSocketManager.ts` (or equivalent): Ensure it can send `chat_message` with `metadata.requested_workflow` when needed.
    *   UI Components: Implement sending the correct `metadata.requested_workflow` from relevant buttons/actions.
6.  **Agent/Graph Code (`backend/apps/main/agents/`, `backend/apps/main/graphs/`, `backend/apps/main/tasks/agent_tasks.py`):**
    *   Verify the implementation details for `pre_spin_feedback`, `post_spin`, `activity_feedback`, `progress_review` workflows within the Celery task execution logic.
    *   Ensure the Celery task correctly uses the `user_ws_session_name` to send results back via the Channels layer.
7.  **Flow Documentation (`docs/agents/flows/`):** Update all relevant flow documents (`wheel_generation_FLOW.md`, `pre_spin_FLOW.md`, `post_spin_FLOW.md`, `activity_feedback_FLOW.md` [renamed from `post_activity_FLOW.md`]) to accurately reflect the dispatcher's triggering logic (metadata bypass, conditional classification considering context), the optional nature of the `pre_spin_feedback` loop, and the asynchronous execution/result handling. Ensure clear transitions between these flows are documented. Add notes about `progress_review` based on implementation status.

## Final Recommendations (Refined based on Code)

1.  **Standardize Workflow Names:** Ensure consistency between dispatcher classification targets (`user_onboarding`, `wheel_generation`, `post_spin`, `activity_feedback`, `discussion`, `pre_spin_feedback`, `progress_review`), graph implementations (`graphs/`), and documentation. Focus on the 5 core + optional `pre_spin_feedback`.
2.  **Leverage `metadata.requested_workflow`:** Encourage frontend development to use this metadata field for all UI-driven actions that map directly to a core workflow, improving reliability.
3.  **Verify Async Result Handling:** Thoroughly test the Celery task (`execute_graph_workflow`) and associated graph logic to ensure results are consistently and correctly sent back to the originating user's WebSocket session.
4.  **Refine Contextual Classification:** Continue tuning the LLM prompt in `_classify_with_llm` and the rules in `_apply_classification_rules`, ensuring they effectively use conversation context/state to distinguish `pre_spin_feedback` intent from `discussion` or other workflows when classification *is* needed.
5.  **Document Asynchronous Flow & Optional `pre_spin_feedback`:** Clearly document the asynchronous handoff from the dispatcher to Celery for all workflows and the separate result-sending mechanism. Explicitly illustrate the optional `pre_spin_feedback` loop (triggered during the frontend "Pre-Spin" state) in relevant architecture diagrams and descriptions.
