# Agent Tools Documentation

This document explains the Game of Life agent tools system, which allows agents to directly interact with Django's ORM through Python functions rather than API endpoints.

## Overview

The agent tools system provides a standardized way for AI agents to access system data and functionality through registered Python functions. Each tool:

1. Is registered with a unique code identifier
2. Has a well-defined input and output schema
3. Is associated with specific agent roles that can use it
4. Directly interacts with Django's ORM and other system components
5. Stores performance metrics automatically

## Tool Registration

### Register a New Tool

Tools are registered using the `@register_tool` decorator in `apps.main.agents.tools`:

```python
from apps.main.agents.tools import register_tool

@register_tool('my_custom_tool')
async def my_custom_tool_handler(input_data):
    """
    Brief description of what the tool does.
    
    Input:
        param1: Description of first parameter
        param2: Description of second parameter
        
    Output:
        result: Description of the output
    """
    # Implementation
    # Directly use Django ORM here
    from apps.your_app.models import YourModel
    
    # Process input
    user_id = input_data.get('user_id')
    
    # Perform database operations
    results = YourModel.objects.filter(user_id=user_id)
    
    # Return structured output
    return {
        "result": [
            {"id": item.id, "name": item.name}
            for item in results
        ]
    }
```

### Important Notes:

1. Tool handlers **must** be async functions
2. Document input/output formats in the docstring following the convention shown above
3. Organize related tools in thematic modules for better management
4. Use Django ORM directly within the function body
5. Handle exceptions properly and return structured error messages when needed

## Sync Tool Registry with Database

After adding or modifying tools, sync them with the database:

```bash
python manage.py register_tools
```

This command:
1. Scans the tools module for all registered tools
2. Creates or updates tool records in the database
3. Updates input/output schemas based on docstrings
4. Preserves existing access control settings

## Using Tools in Agent Code

In your agent implementation, use the provided utility function to call tools:

```python
from apps.main.agents.tools_util import execute_tool

async def my_agent_function():
    # Call a tool
    try:
        result = await execute_tool(
            'my_custom_tool',
            {'user_id': 'some-user-id'},
            run_id='optional-agent-run-id'
        )
        # Process result
        return result
    except ValueError as e:
        # Handle error
        return {"error": str(e)}
```

For agent nodes inheriting from `AgentNodeBase`, use the `_call_tool` method:

```python
async def process_data(self, input_data):
    # Call a tool registered with this agent
    result = await self._call_tool('my_custom_tool', {'param': 'value'})
    # Process result
    return result
```

## Tool Access Control

Tools have a permissions system to control which agents can use them:

1. Each tool defines `allowed_agent_roles` in the database
2. The agent base class verifies permission before executing a tool
3. Permissions are managed through the Django admin interface

## Performance Monitoring

All tool executions are automatically tracked:

1. Response time is measured and averaged
2. Error rates are calculated
3. Usage counts are maintained

View these metrics in the Django admin interface under "Agent Tools".

## Writing Effective Tools

### Do:

1. Make each tool focused on a specific task
2. Use descriptive names and document inputs/outputs clearly
3. Add appropriate error handling with meaningful messages
4. Include validation for input parameters
5. Use Django ORM efficiently (select_related, prefetch_related, etc.)
6. Keep tools stateless whenever possible

### Don't:

1. Create overly broad tools that do too many things
2. Bypass the permission system
3. Include sensitive credentials in tool code
4. Create tools with undocumented side effects
5. Perform long-running operations without proper handling

## Tool System Architecture

The tool system consists of several key components:

1. **Tool Registration**: The `@register_tool` decorator in `tools_registration.py`
2. **Tool Storage**: The `AgentTool` model in the database
3. **Tool Execution**: The `execute_tool` utility in `tools_util.py`
4. **Access Control**: Handled by `AgentNodeBase._call_tool`
5. **Metrics Collection**: Automatically tracked during execution

This architecture provides a clean separation of concerns while maintaining a unified interface for tools across the system.

## Common Issues and Solutions

### Tool Not Found

If a tool is not found when called:

1. Ensure it's properly registered with `@register_tool`
2. Run `python manage.py cmd_register_tools` to sync with database
3. Check that the tool is marked as active in the admin interface

### Permission Denied

If an agent can't access a tool:

1. Check that the agent's role is included in the tool's `allowed_agent_roles`
2. Verify the agent is loading the correct role in its initialization

### Tool Execution Error

If a tool fails during execution:

1. Check the error message returned from `execute_tool`
2. Review the agent run logs for details about the failure
3. Test the tool function directly with sample inputs

## Extending the Tool System

To extend the tool system:

1. Add new tool handlers in thematic modules
2. Use the registration decorator to make them available
3. Sync them to the database with the management command
4. Update agent permissions as needed