# Game of Life Multi-Agent System Documentation

## System Architecture Overview

The Game of Life system uses a multi-agent architecture with two primary components:

1. **Outside the LangGraph Workflow**:
   - **ConversationDispatcher**: The system's entry point that processes initial user messages, extracts context, and routes to appropriate agent workflows

2. **Inside the LangGraph Workflow**:
   - **Orchestrator Agent**: Coordinates the flow between specialized agents
   - **Resource & Capacity Agent**: Analyzes resource availability and constraints
   - **Engagement & Pattern Agent**: Analyzes historical engagement patterns
   - **Psychological Monitoring Agent**: Assesses psychological state and trust
   - **Strategy Agent**: Formulates activity selection strategy
   - **Wheel/Activity Agent**: Constructs the wheel with tailored activities
   - **Ethical Oversight Agent**: Validates ethical alignment and safety

## 1. ConversationDispatcher

**Role/Purpose:**
- Top-level service that receives initial user messages from WebSocket handlers
- Analyzes and classifies user messages to determine appropriate workflows
- Extracts contextual information for downstream agent processing
- Launches the appropriate agent workflow based on message classification
- Acts as the bridge between the WebSocket layer and the agent workflow system

**Data Access:**
- Read: UserProfile for profile completion status
- Write: HistoryEvent for workflow initiation
- Execute: Tools for context extraction, message classification, and workflow selection
- Memory Access: Only through tool execution

**Processing Logic:**
1. Message Classification:
   - Extract context from user message (mood, environment, time availability, etc.)
   - Classify message into appropriate workflow type (wheel_generation, activity_feedback, etc.)
   - Determine if additional action is required before proceeding
   - Generate workflow ID and prepare tracking information

2. Context Packet Creation:
   - Build standardized context packet with user ID, session data, and extracted context
   - Format temporal information and metadata for downstream processing
   - Include WebSocket session information for result routing

3. Workflow Execution:
   - Launch the appropriate agent workflow with the context packet
   - Record workflow initiation in the database
   - Return workflow status and estimated completion time to client

4. Error Management:
   - Handle errors in classification or context extraction
   - Provide fallback behavior when exact classification is uncertain
   - Return meaningful error responses that maintain user experience

**Outputs:**
- **To WebSocket Consumer:**
  - Workflow initiation confirmation with tracking information
  - Estimated completion time for client-side progress indication
- **To Agent Workflow System:**
  - Workflow type determination (wheel_generation, activity_feedback, etc.)
  - Structured context packet with extracted information
  - WebSocket session information for result routing
- **To Database:**
  - HistoryEvent records of workflow initiation

**This architecture ensures:**
- Clear separation of concerns between routing and workflow execution
- Standardized context extraction independent of workflow processing
- Consistent workflow classification with fallbacks and error handling
- Efficient transition from user input to specialized agent processing

---

## 1. Mentor Agent

**Role/Instructions:**
- Serves as the primary user-facing conversational interface within workflow graphs
- Builds trust and relationship with the user through consistent, supportive interactions
- Processes conversational context passed from the ConversationDispatcher
- Presents activities, explanations, and feedback in user-friendly language
- Maintains the philosophical framing of the game throughout all interactions
- Adjusts tone and framing based on user-specific communication guidelines

**Relationship with ConversationDispatcher:**
- Works in conjunction with the ConversationDispatcher, which handles initial message classification and routing
- Receives pre-processed context packets prepared by the ConversationDispatcher
- Focuses on providing personalized, conversational responses based on the workflow results
- Returns user-friendly responses that are delivered through the workflow result handler

**Data Access:**
- Read: UserProfile, HistoryEvent, UserFeedback, ActivityTailored, Wheel, WheelItem, TrustLevel
- Write: HistoryEvent, UserFeedback, CurrentMood
- Recommend: TrustLevel adjustments based on interaction quality
- Memory Access:
  - Read: Communication preferences, conversation context, effective approaches
  - Write: Updated communication effectiveness, user response patterns

**Inputs:**
- **From ConversationDispatcher (via workflow):**
  - Structured context packet containing:
    - Extracted user mood, environment, time availability and focus
    - Workflow type classification (wheel_generation, activity_feedback, etc.)
    - User WebSocket session information for result routing
    - User profile information and metadata
  - Workflow ID and session tracking information
- **From Orchestrator Agent:**
  - Integrated results from specialized agent processing
  - Communication guidelines and presentation directives
- **From Data Model:**
  - Current wheel with activity options and spin outcomes
  - User interaction memory to avoid repetitive questions

**Processing Logic:**
1. Context Enrichment:
   - Process the pre-classified context packet from the dispatcher
   - Enhance context with user-specific communication preferences
   - Retrieve relevant historical context for continuity
   - Prepare contextualized message for user response generation

2. Communication Preparation:
   - Select appropriate tone based on trust phase and preferences
   - Determine detail level based on user history
   - Choose metaphors and examples that resonate with user

3. Response Formulation:
   - Structure natural language responses with clear sections
   - Balance philosophical framing with practical instructions
   - Format responses for optimal clarity and engagement
   - Include appropriate emotion and enthusiasm calibration

4. Workflow Integration:
   - Format outputs for downstream agents in the workflow
   - Generate structural metadata for the workflow result handler
   - Provide context for the next agent in the workflow sequence

**Expected Output Benchmark:**
- Conversational dialogue containing:
  - Natural, personalized language matching user preferences
  - Clear activity explanations with goal connections
  - Structured feedback collection with specific questions
  - Philosophical framing appropriate to trust phase
- All outputs must meet these quality standards:
  - Non-judgmental, supportive tone throughout
  - Appropriate detail level based on user preferences
  - Clear actionable information without ambiguity
  - Consistent philosophical alignment with game framework

**Outputs:**
- **To Orchestrator Agent / Workflow State:**
  - User response text (for delivery to the client via the result handler).
  - Enhanced context packet with user interaction insights.
  - Process state updates for workflow tracking.
  - **Optional:** `transition_request` dictionary if a workflow change is detected. This signals the graph router to terminate the current workflow and initiate the requested new workflow via the `ConversationDispatcher`, passing along relevant context. (See `docs/architecture/workflows/mentor_workflow_transition.md` for details).
- **To Data Model:**
  - `HistoryEvent` entries documenting user interactions.
  - `UserFeedback` entries capturing structured feedback.

**Error Handling:**
- User ambiguity: Implements clarification protocol before proceeding
- Timeout response: Provides appropriate transition if user is inactive
- Emotional escalation: De-escalation protocol for user frustration or anxiety

**Must Avoid:**
- Directly querying or modifying the data model beyond access permissions
- Making substantive decisions about challenges or psychological assessments
- Implementing complex reasoning about user's state (defer to specialized agents)
- Overwhelming the user with technical explanations or system mechanics
- Judgmental language or pressure when users refuse activities

## 1.1 Complementary System: ConversationDispatcher

**Role/Purpose:**
- Top-level service that receives initial user messages from WebSocket handlers
- Analyzes and classifies user messages to determine appropriate workflows
- Extracts contextual information for downstream agent processing
- Launches the appropriate agent workflow based on message classification
- Acts as the bridge between the WebSocket layer and the agent workflow system

**Data Access:**
- Read: UserProfile for profile completion status
- Write: HistoryEvent for workflow initiation
- Execute: Tools for context extraction, message classification, and workflow selection
- Memory Access: Only through tool execution

**Processing Logic:**
1. Message Classification:
   - Extract context from user message (mood, environment, time availability, etc.)
   - Classify message into appropriate workflow type (wheel_generation, activity_feedback, etc.)
   - Determine if additional action is required before proceeding
   - Generate workflow ID and prepare tracking information

2. Context Packet Creation:
   - Build standardized context packet with user ID, session data, and extracted context
   - Format temporal information and metadata for downstream processing
   - Include WebSocket session information for result routing

3. Workflow Execution:
   - Launch the appropriate agent workflow with the context packet
   - Record workflow initiation in the database
   - Return workflow status and estimated completion time to client

4. Error Management:
   - Handle errors in classification or context extraction
   - Provide fallback behavior when exact classification is uncertain
   - Return meaningful error responses that maintain user experience

**Outputs:**
- **To WebSocket Consumer:**
  - Workflow initiation confirmation with tracking information
  - Estimated completion time for client-side progress indication
- **To Agent Workflow System:**
  - Workflow type determination (wheel_generation, activity_feedback, etc.)
  - Structured context packet with extracted information
  - WebSocket session information for result routing
- **To Database:**
  - HistoryEvent records of workflow initiation

**Relationship to Mentor Agent:**
- ConversationDispatcher handles initial message processing and workflow selection
- Mentor Agent focuses on conversational interface within the selected workflow
- Together they form the complete communication path between user and system

**This complementary architecture ensures:**
- Clear separation of concerns between routing and conversation
- Standardized context extraction independent of conversation style
- Consistent workflow classification with fallbacks and error handling
- Efficient transition from user input to specialized agent processing

---

## 2. Orchestrator Agent

**Role:**
- Coordinates the flow of information between all specialized agents
- Determines which agents to invoke and in what sequence based on current task
- Evaluates and makes decisions between specialized agent recommendations
- Implements a priority framework for decision-making
- Manages error handling and recovery for the entire agent system

**Data Access:**
- Read: All data models (for coordination purposes)
- Write: HistoryEvent (workflow events), AgentActivation records
- Recommend: None (delegates to specialized agents)
- Memory Access: None (stateless for MVP implementation)

**Inputs:**
- **From Mentor Agent:**
  - Structured context packets containing user feedback and state data
  - Task type identifiers (wheel generation, activity processing, etc.)
- **From Specialized Agents:**
  - Output reports and recommendations
  - Analysis results and decision parameters
- **From Data Model:**
  - Environmental and temporal context
  - System state and workflow stage indicators
  - HistoryEvent references for context

**Processing Logic:**
1. Task Analysis:
   - Identify specific workflow type (onboarding, wheel generation, etc.)
   - Determine required agent sequence based on task type
   - Establish dependencies between agent activations
   - Define success criteria for workflow completion

2. Context Distribution:
   - Extract relevant subsets of context for each agent
   - Format data according to agent input requirements
   - Include only necessary information to minimize data transfer
   - Add workflow identifiers and tracking metadata

3. Workflow Management:
   - Track agent activation status and completion
   - Handle parallel processing for independent agents
   - Manage sequential dependencies for dependent agents
   - Document workflow state transitions in HistoryEvent

4. Decision Integration:
   - Apply priority framework to resolve conflicting recommendations
   - Integrate multiple agent outputs into coherent decisions
   - Ensure data consistency across agent boundaries
   - Create comprehensive integration packages for downstream agents

**Expected Output Benchmark:**
- Agent activation instructions containing:
  - Specific data subsets relevant to each agent
  - Clear processing requests with parameters
  - Workflow identifiers and state information
  - Expected output formats and requirements
- Integrated decision packages containing:
  - Aggregated specialized agent outputs
  - Resolved conflicts with applied priority rules
  - Complete context for downstream processing
  - Traceability to source recommendations
- All outputs must meet these quality standards:
  - Complete workflow documentation for audit
  - Correct dependency management between agents
  - Efficient data distribution (minimal necessary information)
  - Clear decision rationales for conflict resolution

**Outputs:**
- **To Specialized Agents:**
  - Sequential agent activation instructions
  - Specific data subsets relevant to each agent's function
  - Processing requests with contextual parameters
- **To Mentor Agent:**
  - Aggregated decision documents
  - Final activity selections and wheel constructions
  - Conflict resolution explanations
- **To Database/Data Model:**
  - System-level transaction logs for HistoryEvent
  - Workflow state transitions

**Key Decision Points:**
1. Agent Sequence Determination:
   - Criteria: Task type, data dependencies, parallel processing opportunities
   - Impact: Workflow efficiency, data consistency, output quality

2. Conflict Resolution:
   - Priority hierarchy: Safety, ethical alignment, psychological fit, resource feasibility
   - Impact: Final recommendations, challenge calibration, wheel composition

**Must Avoid:**
- Implementing domain-specific logic that belongs in specialized agents
- Passing complete user models to agents that only need specific aspects
- Making subjective assessments of user psychology (defer to Psychological Monitoring Agent)
- Bypassing ethical validation for efficiency
- Processing natural language directly (rely on Mentor Agent's structured output)

---

## 3. Resource & Capacity Management Agent

**Role:**
- Analyzes user's available physical resources, time, and environmental context
- Identifies constraints and limitations that affect activity feasibility
- Maps resource requirements to user's current inventory and capabilities
- Validates environmental compatibility with activities
- Provides feasibility assessments for potential activities

**Data Access:**
- Read: UserEnvironment, Inventory, UserResource, Skill, UserLimitation
- Write: Inventory (direct updates for objective resource changes)
- Recommend: Capability and UserLimitation updates based on new information
- Memory Access: None (stateless for MVP implementation)

**Inputs:**
- **From Orchestrator Agent:**
  - User's current environmental context parameters
  - Time availability specifications
  - Activity evaluation requests
- **From Data Model:**
  - UserEnvironment records
  - Inventory items and availability
  - UserResource and Skill records
  - Physical and cognitive capability metrics
  - ActivityTailored resource requirements

**Processing Logic:**
1. Environmental Analysis:
   - Compare current environmental context with UserEnvironment records
   - Identify environmental factors that enable or constrain activities
   - Calculate environmental support metrics for different activity domains
   - Document any new environmental information for updates

2. Resource Inventory:
   - Match available resources against activity requirements
   - Identify resource gaps or limitations
   - Calculate resource feasibility metrics
   - Document new resource information

3. Time Availability:
   - Analyze time constraints against activity duration requirements
   - Calculate optimal activity duration parameters
   - Identify scheduling opportunities and limitations
   - Document temporal feasibility metrics

4. Capability Assessment:
   - Evaluate physical and cognitive capability requirements
   - Compare against user's documented capabilities
   - Calculate capability-based feasibility scores
   - Identify potential capability limitations

**Expected Output Benchmark:**
- Resource context document containing:
  - Environmental assessment with domain support ratings (0-100)
  - Resource inventory with availability classifications
  - Time availability parameters with scheduling recommendations
  - Capability metrics with feasibility ratings (0-100)
  - Limitation impact assessments with constraint specifications
- Each section must include:
  - Specific measurement metrics with confidence scores
  - Explicit activity implications with justifications
  - Clear constraint boundaries with parameters
  - Recommendations for addressing limitations

**Outputs:**
- **To Orchestrator Agent:**
  - Comprehensive resource context document
  - Feasibility ratings for activities (numeric scale)
  - Constraint specifications with alternatives
  - Resource requirement adjustment recommendations
- **To Database/Data Model:**
  - Direct updates to objective resource records (Inventory, UserResource)
  - Recommendations for interpretive capability updates

**Error Handling:**
- Missing resource data: Conservative default assumptions
- Capability uncertainty: Apply safety margins to estimates
- Conflicting environmental data: Prioritize user-reported current state

**Must Avoid:**
- Making psychological assessments outside resource domain
- Modifying core user psychological profile data
- Directly adjusting activity challenge levels (provide data for Strategy Agent)
- Overriding ethical constraints for resource optimization
- Assuming resource availability without validation

---

## 4. Engagement & Pattern Analytics Agent

**Role:**
- Integrates quantitative engagement metrics with qualitative user feedback
- Analyzes historical interaction patterns alongside real-time sentiment
- Identifies recurring behavioral and emotional themes to guide system improvements
- Provides data-driven view of user engagement and satisfaction
- Supports targeted adjustments to activities based on pattern insights

**Data Access:**
- Read: HistoryEvent, UserFeedback, Preference, ActivityTailored
- Write: None (analysis only for MVP)
- Recommend: Preference updates, engagement pattern insights
- Memory Access:
  - Read: Domain engagement metrics, pattern confidence scores
  - Write: Updated engagement patterns, behavioral insights

**Inputs:**
- **From Orchestrator Agent:**
  - Current feedback context and analysis requests
  - Specific pattern evaluation queries
  - Time period parameters for historical analysis
- **From Database/Data Model:**
  - HistoryEvent entries (filtered for relevant interactions)
  - UserFeedback entries and sentiment data
  - Activity completion and abandonment records
  - Domain preference indicators and historical engagement metrics

**Processing Logic:**
1. Historical Pattern Analysis:
   - Calculate completion rates by activity domain
   - Identify temporal patterns (time-of-day, day-of-week)
   - Map engagement trends over time periods
   - Document pattern consistency and confidence

2. Preference Extraction:
   - Analyze explicit preference statements from feedback
   - Compare stated preferences with behavioral patterns
   - Identify preference contradictions or evolutions
   - Calculate preference strength and confidence metrics

3. Feedback Integration:
   - Correlate quantitative metrics with qualitative feedback
   - Identify sentiment patterns across domains and activities
   - Extract insight themes from narrative feedback
   - Connect feedback patterns to activity characteristics

4. Pattern Memory Management:
   - Update domain engagement metrics with new data
   - Adjust confidence scores based on consistency
   - Document new behavioral insights with evidence
   - Update pattern predictions for future activities

**Expected Output Benchmark:**
- Engagement profile containing:
  - Domain preference metrics with completion rates (%)
  - Temporal pattern analysis with optimal timing recommendations
  - Activity format preferences with engagement correlations
  - Behavioral pattern identification with consistency metrics
  - Contradiction analysis for stated vs. observed preferences
- Each component must include:
  - Statistical support with confidence intervals
  - Historical context with trend indicators
  - Supporting evidence from user history
  - Actionable implications for activity selection

**Outputs:**
- **To Orchestrator Agent:**
  - Comprehensive reports blending engagement patterns with sentiment analysis
  - Domain preference rankings with confidence scores
  - Pattern identification reports (e.g., refusal patterns, completion trends)
  - Consistency validation between stated preferences and actual behavior
- **To Database/Data Model:**
  - Recommendation records for Preference updates
  - Pattern recognition metrics for future reference

**Key Decision Points:**
1. Pattern Significance Determination:
   - Criteria: Statistical significance, consistency over time, alignment with feedback
   - Impact: Influences domain distribution, activity selection, timing recommendations

2. Preference-Behavior Inconsistency Handling:
   - Criteria: Consistency of behavioral evidence vs. explicit statements
   - Impact: Determines which preferences are emphasized in recommendations

**Must Avoid:**
- Relying exclusively on recent feedback without considering historical trends
- Making direct psychological assessments beyond the scope of aggregated engagement data
- Filtering or biasing negative feedback—ensure all sentiment is considered objectively
- Overemphasizing either quantitative or qualitative data at the expense of a balanced view

---

## 5. Psychological Monitoring Agent

**Role:**
- Assesses user's current psychological state and trust level
- Analyzes relationship between activities and user's belief system
- Evaluates appropriate challenge levels based on user traits
- Identifies growth opportunities and psychological readiness
- Monitors emotional vulnerabilities and safety boundaries

**Data Access:**
- Read: UserTraitInclination, Belief, TrustLevel, UserGoal, Inspiration, CurrentMood
- Write: CurrentMood (direct updates for current state)
- Recommend: TrustLevel updates, Belief adjustments, UserTraitInclination refinements
- Memory Access:
  - Read: Trait expression patterns, trust development history
  - Write: Updated psychological insights, growth opportunity indicators

**Inputs:**
- **From Orchestrator Agent:**
  - Current mood data and psychological assessment requests
  - Trust feedback and psychological context
  - Resource and engagement data for integrated assessment
- **From Database/Data Model:**
  - UserTraitInclination records (HEXACO personality dimensions)
  - Belief and Aspiration models
  - TrustLevel metrics and historical trend data
  - Inspiration and GoalInspiration connections
  - UserLimitation records (psychological limitations)

**Processing Logic:**
1. Psychological State Assessment:
   - Analyze current mood indicators against baseline
   - Evaluate emotional state through linguistic markers
   - Compare current state with historical patterns
   - Document confidence levels for state assessment

2. Trust Phase Determination:
   - Calculate unified trust score from dimensional metrics
   - Apply phase transition criteria to current metrics
   - Determine appropriate trust phase (Foundation/Expansion)
   - Document evidence supporting phase determination

3. Trait-Belief-Goal Alignment:
   - Map HEXACO traits to current goals and aspirations
   - Identify traits that support or hinder goal achievement
   - Analyze belief impact on trait expression
   - Document growth opportunities with supporting evidence

4. Challenge Calibration:
   - Calculate appropriate challenge levels based on trust phase
   - Determine domain-specific challenge adjustments
   - Identify psychological safety boundaries
   - Document challenge recommendations with rationales

**Expected Output Benchmark:**
- Psychological state assessment containing:
  - Current mood evaluation with confidence metrics
  - Trust phase determination with supporting evidence
  - Trait analysis with goal alignment connections
  - Growth opportunity identification with priority rankings
  - Safety boundary identification with parameters
- Each component must include:
  - Evidence-based assessments with confidence ratings
  - Clear connection to user goals and aspirations
  - Explicit challenge implications with parameters
  - Appropriate safety considerations with boundaries

**Outputs:**
- **To Orchestrator Agent:**
  - Comprehensive psychological state assessment
  - Trust phase determination (foundation/expansion)
  - Challenge calibration recommendations (numeric values per trait)
  - Growth opportunity identification with priority rankings
  - Emotional vulnerability flags and safety parameters
  - Alternative activity suggestions with psychological rationales
- **To Database/Data Model:**
  - Updates to current mood parameters
  - Recommendation records for Trust metrics, Belief updates, and trait adjustments

**Key Decision Points:**
1. Trust Phase Determination:
   - Foundation Phase criteria: Trust score <60, limited completion history, high vulnerability
   - Expansion Phase criteria: Trust score ≥60, consistent completion pattern, moderate resilience
   - Impact: Controls challenge calibration parameters and activity selection approach

2. Vulnerability Assessment:
   - Criteria: Emotional intensity, belief stability, historical sensitivity
   - Impact: Establishes safety boundaries, determines appropriate challenge levels

**Must Avoid:**
- Making resource feasibility assessments outside psychological domain
- Implementing complex activity generation logic (defer to Activity Agent)
- Bypassing ethical oversight for growth potential
- Pushing beyond appropriate challenge levels based on trust metrics
- Treating correlation as causation in psychological patterns

---

## 6. Strategy Agent

**Role:**
- Formulates comprehensive activity selection strategy based on multi-agent inputs
- Performs gap analysis between user traits and activity requirements
- Balances challenge calibration with domain distribution
- Aligns activity strategy with user's growth trajectory
- Establishes boundary conditions and constraints for activity selection

**Data Access:**
- Read: UserGoal, UserTraitInclination, TrustLevel, Preference, Belief
- Write: None (strategy formulation only for MVP)
- Recommend: None (outputs functional strategy without direct recommendations)
- Memory Access:
  - Read: Baseline strategy parameters, trust-based adaptation patterns
  - Write: Updated strategy parameters, challenge calibration rationales

**Inputs:**
- **From Orchestrator Agent:**
  - Combined specialized agent reports and strategy request
  - Resource context from Resource Agent
  - Engagement patterns from Engagement Agent
  - Psychological assessment from Psychological Agent
- **From Database/Data Model:**
  - UserGoal records and aspirations
  - Current challenge settings and trajectory data

**Processing Logic:**
1. Multi-Agent Integration:
   - Synthesize inputs from all specialized agents
   - Resolve conflicts using priority framework
   - Identify key constraints and requirements
   - Create unified context for strategy development

2. Gap Analysis:
   - Compare user traits with activity requirements
   - Identify development opportunities by trait
   - Calculate optimal challenge levels for growth
   - Document gap analysis with evidence

3. Domain Distribution Planning:
   - Balance user preferences with growth needs
   - Allocate domain percentages based on multiple factors
   - Ensure appropriate variety and exploration
   - Document distribution rationale with justifications

4. Challenge Calibration Strategy:
   - Apply trust phase modifiers to base challenge levels
   - Create domain-specific challenge adjustments
   - Establish progression pathways for growth
   - Document calibration approach with parameters

**Expected Output Benchmark:**
- Strategy framework containing:
  - Challenge calibration section with specific parameters for each trait dimension
  - Domain distribution section with percentage allocations and rationales
  - Activity selection criteria with detailed filtering parameters
  - Growth alignment section connecting strategy to goals
  - Constraint boundaries with clear limitations
  - Trust-phase appropriate adaptations
- Each component must include:
  - Specific numerical parameters for implementation
  - Clear rationales connected to user data
  - Evidence from specialized agent reports
  - Appropriate trust-phase considerations

**Outputs:**
- **To Orchestrator Agent:**
  - Comprehensive strategy framework for wheel construction
  - Domain distribution specifications with numeric weights
  - Challenge calibration parameters across personality dimensions
  - Activity selection constraint guidelines and boundary conditions
  - Probability distribution recommendations with justifications

**Key Decision Points:**
1. Challenge-Trust Balance:
   - Criteria: Trust phase, goal importance, trait gap significance
   - Impact: Determines appropriate challenge levels for growth without overwhelming

2. Domain Balance Optimization:
   - Criteria: Preference strength, growth needs, goal alignment, variety requirements
   - Impact: Controls domain distribution for engagement and development

**Must Avoid:**
- Selecting specific activities (defer to Wheel/Activity Agent)
- Making direct psychological assessments (rely on Psychological Agent)
- Evaluating specific resource feasibility (rely on Resource Agent)
- Implementing ethical judgments (defer to Ethical Oversight Agent)
- Optimizing for engagement at the expense of growth or well-being

---

## 7. Wheel/Activity Agent

**Role:**
- Selects concrete activities based on strategy framework
- Tailors generic activities to user's specific context
- Constructs the wheel with appropriate probability distributions
- Customizes activity instructions and resource requirements
- Provides clear value propositions for selected activities

**Data Access:**
- Read: GenericActivity, UserProfile, UserEnvironment, Inventory, UserTraitInclination, UserGoal
- Write: Wheel, WheelItem, ActivityTailored
- Recommend: None (direct creation of wheel and activities)
- Memory Access: None (stateless for MVP implementation)

**Inputs:**
- **From Orchestrator Agent:**
  - Strategy framework with selection parameters
  - Activity creation or replacement requests
  - Domain distribution specifications
  - Challenge calibration parameters
- **From Database/Data Model:**
  - GenericActivity catalog for activity selection
  - UserProfile and UserEnvironment context
  - Previous activity history and feedback references
  - Activity requirement specifications and constraints

**Processing Logic:**
1. Activity Selection:
   - Query GenericActivity catalog with strategy parameters
   - Apply domain distribution requirements
   - Filter by resource availability and constraints
   - Match challenge levels to calibration parameters
   - Select appropriate variety and balance

2. Activity Tailoring:
   - Customize instructions for user's specific context
   - Adapt resource requirements to available inventory
   - Adjust challenge levels to match calibration parameters
   - Connect to user goals and growth areas
   - Create personalized value propositions

3. Wheel Construction:
   - Create Wheel object with metadata
   - Generate WheelItem objects for each activity
   - Assign probability weights according to strategy
   - Ensure complete wheel coverage and balance
   - Verify normalization of probability distribution

4. Value Enhancement:
   - Create specific value propositions for each activity
   - Connect to user's stated goals and aspirations
   - Highlight growth opportunities and benefits
   - Frame appropriately for trust phase
   - Document selection rationale for transparency

**Expected Output Benchmark:**
- Complete wheel package containing:
  - Wheel object with metadata (creation time, user reference)
  - 6-8 WheelItem objects with probability weights
  - ActivityTailored objects with detailed specifications
  - Value propositions for each activity
  - Selection rationales with strategy alignment evidence
- Each ActivityTailored object must include:
  - Completely customized instructions for user context
  - Appropriate challenge level with justification
  - Clear connection to user goals and growth areas
  - Resource requirements matched to availability
  - Step-by-step guidance for completion

**Outputs:**
- **To Orchestrator Agent:**
  - Complete Wheel object with probability-weighted WheelItems
  - ActivityTailored objects with customized details and instructions
  - Value propositions and personalization justifications
- **To Database/Data Model:**
  - Creates and stores new Wheel, WheelItem, and ActivityTailored objects
  - Links activities to appropriate resources and requirements

**Error Handling:**
- Insufficient activities: Implement fallback selection criteria
- Resource conflicts: Apply substitution rules or adjust requirements
- Challenge mismatch: Apply normalization to maintain relative calibration

**Must Avoid:**
- Overriding strategy framework parameters
- Selecting activities that violate resource constraints
- Including activities with inappropriate challenge levels
- Bypassing ethical validation
- Generating generic activities without personalization
- Creating wheel with insufficient variety or domain balance

---

## 8. Ethical Oversight Agent

**Role:**
- Reviews all proposed activities for ethical alignment
- Ensures respect for user autonomy and well-being
- Validates appropriate challenge calibration
- Verifies transparency in activity descriptions
- Implements the ethical framework of benevolence, fairness, and transparency

**Data Access:**
- Read: Wheel, WheelItem, ActivityTailored, UserTraitInclination, Belief, TrustLevel
- Write: None (validation only for MVP)
- Recommend: Ethical modifications to activities, wheel composition adjustments
- Memory Access: None (stateless for MVP implementation)

**Inputs:**
- **From Orchestrator Agent:**
  - Activity validation requests
  - Wheel composition review requests
  - Psychological vulnerability flags
- **From Database/Data Model:**
  - ActivityTailored objects for review
  - Complete Wheel construction
  - User psychological vulnerability flags
  - System ethical principles and guidelines
  - TrustLevel data and phase information

**Processing Logic:**
1. Activity Evaluation:
   - Review each activity against ethical principles
   - Verify psychological safety based on trust phase
   - Validate resource requirement appropriateness
   - Assess challenge level calibration
   - Verify transparency and clarity of instructions

2. Wheel Composition Review:
   - Evaluate overall balance and variety
   - Verify appropriate distribution across domains
   - Assess alignment with user's goals and aspirations
   - Validate probability weighting fairness
   - Review for potential manipulation or bias

3. Ethical Validation:
   - Apply binary approval system to each activity
   - Provide specific modification recommendations if needed
   - Document validation rationale with principles referenced
   - Flag potential concerns for monitoring
   - Prepare final validation report

4. Safety Assessment:
   - Review for psychological vulnerability triggers
   - Verify appropriate challenge boundaries
   - Validate resource safety considerations
   - Assess potential unintended consequences
   - Document safety enhancements if needed

**Expected Output Benchmark:**
- Ethical validation report containing:
  - Activity-level assessments with approval status
  - Modification recommendations with specific changes
  - Wheel-level evaluation with balance assessment
  - Ethical rationale connecting to principles
  - Safety considerations with vulnerability mitigations
- Each assessment must include:
  - Clear approval status (Approved/Not Approved)
  - Specific principle references for decisions
  - Concrete modification suggestions when needed
  - Evidence-based reasoning for concerns
  - Implementation guidance for improvements

**Outputs:**
- **To Orchestrator Agent:**
  - Ethical validation report
  - Binary approval decisions for each activity
  - Safety enhancement recommendations
  - Transparency improvement suggestions
  - Validated wheel ready for presentation
  - Ethical rationales for any rejections

**Key Decision Points:**
1. Activity Approval Determination:
   - Criteria: Benevolence, fairness, transparency, autonomy, safety
   - Impact: Determines whether activities proceed to user or require modification

2. Safety Boundary Assessment:
   - Criteria: Trust phase, vulnerability flags, challenge calibration, psychological context
   - Impact: Establishes appropriate safety margins for activities

**Must Avoid:**
- Implementing domain-specific logic outside ethical scope
- Replacing rejected activities (defer to Activity Agent)
- Making subjective judgments about activity value
- Prioritizing engagement or growth over well-being
- Applying overly rigid interpretations of ethical guidelines

## 9. Error Handler Agent

**Role:**
- Provides graceful error recovery when other agents encounter issues
- Maintains user experience continuity despite system failures
- Analyzes error types and determines appropriate recovery strategies
- Creates appropriate fallback responses when recovery isn't possible
- Implements the system's resilience and fault tolerance layer

**Data Access:**
- Read: AgentRun, HistoryEvent for error context analysis
- Write: HistoryEvent (error events), AgentRun (status updates)
- Recommend: None (recovery operations only)
- Memory Access: None (stateless for MVP implementation)

**Inputs:**
- **From Orchestrator Agent:**
  - Error information and stack traces
  - Context of the failed operation
  - Current workflow state before failure
  - Previous agent's output if available
- **From Any Agent (via error propagation):**
  - Agent-specific error details
  - Current stage of processing when error occurred
  - Partial results if available
- **From Data Model:**
  - Workflow status and history
  - Error history for recurring issues
  - Current recovery attempt count

**Processing Logic:**
1. Error Analysis:
   - Classify error type (transient/persistent, infrastructure/data/logic)
   - Determine severity and impact on user experience
   - Assess recovery potential based on error characteristics
   - Identify appropriate recovery path if possible

2. Recovery Planning:
   - Formulate strategy for workflow continuation where possible
   - Create minimal state modifications required for recovery
   - Set up retry parameters with appropriate backoff
   - Determine target agent for recovery attempt

3. Fallback Response Creation:
   - Generate user-friendly error messages that maintain trust
   - Create appropriate explanations without technical details
   - Format responses to match workflow context
   - Maintain philosophical framing in fallback messaging

4. Transaction Management:
   - Ensure database consistency despite errors
   - Document error and recovery attempts
   - Mark appropriate workflow completion status
   - Preserve valuable partial results where possible

**Expected Output Benchmark:**
- Recovery plan containing:
  - Error classification with type and severity
  - Recovery path specification with target agent
  - State modifications needed for retry
  - Retry count and backoff parameters
- Fallback response containing:
  - User-friendly explanation maintaining trust
  - Next steps recommendation for the user
  - Appropriate emotional tone based on error severity
  - Clear closure without technical jargon
- All outputs must meet these quality standards:
  - Complete error documentation for monitoring
  - Appropriate recovery strategies for error type
  - User-friendly messaging preserving experience
  - Proper transaction management preserving data

**Outputs:**
- **To Orchestrator Agent (if recovery possible):**
  - Recovery path specification with target agent
  - State modifications for recovery attempt
  - Retry parameters and instructions
- **To Workflow Result Handler (if recovery not possible):**
  - Fallback user response text
  - Error documentation and classification
  - Workflow completion status
- **To Database/Data Model:**
  - Error event records

**Key Decision Points:**
1. Recovery Potential Assessment:
   - Criteria: Error type, system state, retry history, error patterns
   - Impact: Determines whether to attempt recovery or provide fallback

2. Recovery Path Selection:
   - Criteria: Stage in workflow, dependencies, data consistency requirements
   - Impact: Identifies optimal agent to retry execution from

**Must Avoid:**
- Exposing technical error details to users
- Attempting recovery from critical data integrity errors
- Excessive retry attempts that could compound errors
- Promising specific timeframes for issue resolution
- Breaking the conversational and philosophical framing of the system
- Degrading user experience quality in fallback responses
- Creating feedback loops in recovery attempts
