Below are the complete JSON schema definitions for every agent in the flow. Each schema includes realistic sample data values, key decision points (with criteria and rationale), and explicit links to the Django data models where applicable. Note that all specialized agents communicate only with the Orchestrator; the Orchestrator collects their outputs and then passes an aggregated result back to the Mentor Agent.

---

### 1. Mentor Agent

**Input Schema (Mentor Agent receives user input and historical context):**

```json
{
  "agent": "Mentor Agent",
  "purpose": "Collect initial context and state from <PERSON> via natural language inquiry; retrieve prior logs to avoid redundancy.",
  "inputData": {
    "contextPacket": {
      "userInquiry": "What do you dream about building in this life?",
      "userResponse": {
        "mood": "anxious but hopeful",
        "environmentDescription": "run-down farm near Lyon with limited modern tools",
        "timeAvailability": "3 hours",
        "cognitiveFocus": "overwhelmed by options"
      },
      "trustAndSatisfaction": {
        "currentTrust": "neutral",
        "satisfactionLevel": "slightly positive"
      }
    },
    "systemData": {
      "HistoryLog": {
        "lastSessionSummary": "Past attempts show a pattern of abandoning projects after initial enthusiasm."
      },
      "UserFeedbackLog": {
        "recentFeedback": "User appreciated gentle nudges but felt overwhelmed by long tasks."
      }
    },
    "specificRequest": {
      "action": "Initialize session context for wheel generation",
      "timestamp": "2025-03-12T08:00:00Z"
    }
  },
  "accessedDataModels": [
    "HistoryLog",
    "UserFeedback"
  ]
}
```

**Output Schema (Mentor Agent produces a structured context packet for the Orchestrator):**

```json
{
  "agent": "Mentor Agent",
  "outputData": {
    "primaryOutput": {
      "contextPacket": {
        "mood": "anxious but hopeful",
        "environment": "run-down farm near Lyon",
        "timeAvailability": "3 hours",
        "cognitiveFocus": "overwhelmed by options",
        "trustAndSatisfaction": "neutral with slight trust",
        "decisionPoints": [
          {
            "criterion": "Time availability less than 4 hours",
            "rationale": "Select shorter, manageable activities to prevent overcommitment."
          }
        ]
      }
    },
    "supportingData": {
      "userHistorySummary": "Derived from HistoryLog and UserFeedback records indicating a tendency to abandon projects."
    },
    "recommendations": {},
    "confidenceMetrics": {
      "overallConfidence": 0.85
    }
  },
  "nextAgent": "Orchestrator Agent",
  "updatedDataModels": []
}
```

---

### 2. Orchestrator Agent

**Input Schema (Orchestrator Agent receives the context packet from Mentor Agent plus system data):**

```json
{
  "agent": "Orchestrator Agent",
  "purpose": "Aggregate the context packet from the Mentor Agent and delegate tasks to specialized agents for wheel generation.",
  "inputData": {
    "contextPacket": {
      "mood": "anxious but hopeful",
      "environment": "run-down farm near Lyon",
      "timeAvailability": "3 hours",
      "cognitiveFocus": "overwhelmed by options",
      "trustAndSatisfaction": "neutral with slight trust"
    },
    "systemData": {
      "UserProfile": {
        "profile_name": "Philipp",
        "location": "Lyon region"
      },
      "DjangoModels": [
        "HistoryLog",
        "UserFeedback",
        "Inventory",
        "UserResource"
      ]
    },
    "specificRequest": {
      "task": "Generate a tailored daily activity wheel",
      "target": "Final activity wheel with ethically-approved tasks"
    }
  },
  "accessedDataModels": [
    "UserProfile",
    "HistoryLog",
    "UserFeedback",
    "Inventory",
    "UserResource"
  ]
}
```

**Output Schema (Orchestrator Agent aggregates specialized outputs into a wheel generation plan):**

```json
{
  "agent": "Orchestrator Agent",
  "outputData": {
    "primaryOutput": {
      "collectedAgentOutputs": {
        "resourceAnalysis": "Data from Resource & Capacity Management Agent",
        "engagementAnalysis": "Data from Engagement & Pattern Analytics Agent",
        "psychologicalAssessment": "Data from Psychological Monitoring Agent",
        "strategyFramework": "Data from Strategy Agent",
        "wheelActivities": "Data from Wheel/Activity Agent",
        "ethicalReview": "Data from Ethical Oversight Agent"
      },
      "aggregatedWheelPlan": {
        "wheelID": "wheel_20250312_001",
        "description": "Daily challenge wheel tailored for Philipp with balanced domains and moderate challenge levels."
      }
    },
    "supportingData": {
      "rationale": "Integrated decisions from all specialized agents based on resources, engagement trends, and psychological state."
    },
    "recommendations": {},
    "confidenceMetrics": {
      "overallConfidence": 0.90
    }
  },
  "nextAgent": "Mentor Agent",
  "updatedDataModels": [
    "Wheel",
    "WheelItem"
  ]
}
```

---

### 3. Resource & Capacity Management Agent

**Input Schema (Receives context from Orchestrator plus relevant system data):**

```json
{
  "agent": "Resource & Capacity Management Agent",
  "purpose": "Assess Philipp's available time, physical resources, and limitations based on the current environment and Inventory data.",
  "inputData": {
    "contextPacket": {
      "timeAvailability": "3 hours",
      "environment": "run-down farm near Lyon"
    },
    "systemData": {
      "Inventory": {
        "tools": "old farm tools, some wood scraps",
        "notes": "Limited modern equipment"
      },
      "UserResource": {
        "physicalCapability": "moderate",
        "limitations": "No heavy machinery available"
      }
    },
    "specificRequest": {
      "action": "Perform resource and capacity analysis",
      "timestamp": "2025-03-12T08:05:00Z"
    }
  },
  "accessedDataModels": [
    "Inventory",
    "UserResource"
  ]
}
```

**Output Schema (Produces a resource context report):**

```json
{
  "agent": "Resource & Capacity Management Agent",
  "outputData": {
    "primaryOutput": {
      "resourceContext": {
        "availableTime": "2.5 hours (accounting for daily routines)",
        "physicalResources": "Old farm tools, available wood scraps",
        "limitations": "Limited modern equipment; heavy tasks not recommended",
        "decisionPoints": [
          {
            "criterion": "Time available is under 3 hours",
            "rationale": "Select activities with shorter durations to fit available time."
          }
        ]
      }
    },
    "supportingData": {
      "inventorySummary": "Matches Inventory Django model records."
    },
    "recommendations": {},
    "confidenceMetrics": {
      "resourceAssessmentConfidence": 0.80
    }
  },
  "nextAgent": "Orchestrator Agent",
  "updatedDataModels": [
    "UserResource"
  ]
}
```

---

### 4. Engagement & Pattern Analytics Agent

**Input Schema (Receives context and system data from Orchestrator):**

```json
{
  "agent": "Engagement & Pattern Analytics Agent",
  "purpose": "Analyze Philipp's historical activity logs and feedback to determine domain preferences and engagement trends.",
  "inputData": {
    "contextPacket": {
      "userHistorySummary": "Pattern of high initial enthusiasm with frequent project abandonment."
    },
    "systemData": {
      "HistoryLog": {
        "completedActivities": ["creative_drawing", "casual phone calls"],
        "abandonedActivities": ["physical exercises", "complex DIY projects"]
      },
      "UserFeedbackLog": {
        "feedbackRatings": {
          "creative": 0.75,
          "physical": 0.20,
          "social": 0.65
        }
      }
    },
    "specificRequest": {
      "action": "Analyze engagement patterns",
      "timestamp": "2025-03-12T08:10:00Z"
    }
  },
  "accessedDataModels": [
    "HistoryLog",
    "UserFeedback"
  ]
}
```

**Output Schema (Produces an engagement profile):**

```json
{
  "agent": "Engagement & Pattern Analytics Agent",
  "outputData": {
    "primaryOutput": {
      "engagementProfile": {
        "preferredDomains": ["creative", "social"],
        "completionRates": {
          "creative": 0.75,
          "physical": 0.20,
          "social": 0.65
        },
        "decisionPoints": [
          {
            "criterion": "Low completion rate in physical activities",
            "rationale": "Reduce the weight of physical tasks in the final wheel."
          }
        ]
      }
    },
    "supportingData": {
      "trendAnalysis": "Derived from HistoryLog and UserFeedback metrics."
    },
    "recommendations": {},
    "confidenceMetrics": {
      "engagementAnalysisConfidence": 0.85
    }
  },
  "nextAgent": "Orchestrator Agent",
  "updatedDataModels": []
}
```

---

### 5. Psychological Monitoring Agent

**Input Schema (Receives context from Orchestrator with direct user sentiment and system traits):**

```json
{
  "agent": "Psychological Monitoring Agent",
  "purpose": "Assess Philipp's current psychological state, challenge tolerance, and identify growth opportunities.",
  "inputData": {
    "contextPacket": {
      "mood": "anxious but hopeful",
      "trustAndSatisfaction": "neutral with slight trust"
    },
    "systemData": {
      "UserTraitInclination": {
        "traits": {
          "openness": 70,
          "conscientiousness": 50
        }
      },
      "UserFeedback": {
        "recentSentiment": "prefers gentle nudges"
      }
    },
    "specificRequest": {
      "action": "Evaluate current psychological readiness",
      "timestamp": "2025-03-12T08:15:00Z"
    }
  },
  "accessedDataModels": [
    "UserTraitInclination",
    "UserFeedback"
  ]
}
```

**Output Schema (Produces a psychological profile):**

```json
{
  "agent": "Psychological Monitoring Agent",
  "outputData": {
    "primaryOutput": {
      "psychologicalProfile": {
        "currentMood": "anxious",
        "challengeTolerance": "moderate",
        "growthOpportunities": ["social", "creative"],
        "decisionPoints": [
          {
            "criterion": "User exhibits mild anxiety",
            "rationale": "Avoid overly strenuous or high-risk tasks to maintain emotional safety."
          }
        ]
      }
    },
    "supportingData": {
      "moodAnalysis": "Based on direct feedback and trait inclination data."
    },
    "recommendations": {},
    "confidenceMetrics": {
      "psychologicalAssessmentConfidence": 0.80
    }
  },
  "nextAgent": "Orchestrator Agent",
  "updatedDataModels": [
    "UserTraitInclination"
  ]
}
```

---

### 6. Strategy Agent

**Input Schema (Receives aggregated outputs from Resource, Engagement, and Psychological agents via the Orchestrator):**

```json
{
  "agent": "Strategy Agent",
  "purpose": "Integrate multi-agent insights to formulate a tailored challenge level and domain framework for the activity wheel.",
  "inputData": {
    "contextPacket": {
      "resourceAnalysis": "Available time: 2.5 hours; limited physical resources",
      "engagementAnalysis": "High preference for creative and social activities",
      "psychologicalAssessment": "Moderate challenge tolerance; avoid high-stress tasks"
    },
    "systemData": {},
    "specificRequest": {
      "action": "Generate strategy framework for activity selection",
      "timestamp": "2025-03-12T08:20:00Z"
    }
  },
  "accessedDataModels": [],
  "note": "This agent synthesizes the inputs from the previous specialized agents."
}
```

**Output Schema (Produces a strategy framework for activity selection):**

```json
{
  "agent": "Strategy Agent",
  "outputData": {
    "primaryOutput": {
      "strategyFramework": {
        "challengeLevel": "moderate",
        "domainDistribution": {
          "creative": 40,
          "social": 35,
          "physical": 15,
          "intellectual": 10
        },
        "decisionPoints": [
          {
            "criterion": "Balance required between engagement patterns and physical limitations",
            "rationale": "Ensure activities are challenging yet within the user’s capacity."
          }
        ]
      }
    },
    "supportingData": {
      "synthesizedInputs": "Data aggregated from resource, engagement, and psychological analyses."
    },
    "recommendations": {},
    "confidenceMetrics": {
      "strategyConfidence": 0.90
    }
  },
  "nextAgent": "Orchestrator Agent",
  "updatedDataModels": []
}
```

---

### 7. Wheel/Activity Agent

**Input Schema (Receives strategy framework and system data from the Orchestrator):**

```json
{
  "agent": "Wheel/Activity Agent",
  "purpose": "Select and tailor concrete activities from the GenericActivity catalog based on the strategy framework and available system resources.",
  "inputData": {
    "contextPacket": {
      "strategyFramework": {
        "challengeLevel": "moderate",
        "domainDistribution": {
          "creative": 40,
          "social": 35,
          "physical": 15,
          "intellectual": 10
        }
      }
    },
    "systemData": {
      "GenericActivityCatalog": [
        {
          "id": "generic_001",
          "name": "Build a small wooden crate",
          "description": "Construct a basic crate using available wood scraps.",
          "duration_range": "15-20 minutes",
          "instructions": "Collect wood scraps and assemble into a crate.",
          "physical_requirements": { "strength": "low" },
          "domain": "physical/creative"
        },
        {
          "id": "generic_002",
          "name": "Call an old friend",
          "description": "Reconnect by sharing a personal insight over the phone.",
          "duration_range": "10-15 minutes",
          "instructions": "Call a friend from Frankfurt and share a recent insight.",
          "social_requirements": { "communication": "moderate" },
          "domain": "social"
        }
      ],
      "Inventory": {
        "availableResources": ["wood scraps", "old tools"]
      }
    },
    "specificRequest": {
      "action": "Generate a tailored activity wheel",
      "timestamp": "2025-03-12T08:25:00Z"
    }
  },
  "accessedDataModels": [
    "GenericActivity",
    "ActivityTailored",
    "Inventory"
  ]
}
```

**Output Schema (Produces the final activity wheel with tailored activities):**

```json
{
  "agent": "Wheel/Activity Agent",
  "outputData": {
    "primaryOutput": {
      "activityWheel": {
        "wheelItems": [
          {
            "activityTailored": {
              "id": "activity_001",
              "name": "Build a Small Wooden Crate",
              "description": "Using available wood scraps, construct a crate.",
              "duration_range": "15-20 minutes",
              "instructions": "Collect wood scraps from the barn and assemble a crate.",
              "tailorization_level": 2,
              "base_challenge_rating": 30,
              "linkedDjangoModel": {
                "GenericActivity": "generic_001",
                "ActivityTailored": "activity_001"
              }
            },
            "probabilityWeight": 50.0,
            "decisionPoints": [
              {
                "criterion": "Resource availability and moderate physical challenge",
                "rationale": "Fits within available time and current physical capacity."
              }
            ]
          },
          {
            "activityTailored": {
              "id": "activity_002",
              "name": "Call an Old Friend",
              "description": "Reach out to a friend from Frankfurt to share a personal insight.",
              "duration_range": "10-15 minutes",
              "instructions": "Call and share a new insight about your current life.",
              "tailorization_level": 1,
              "base_challenge_rating": 20,
              "linkedDjangoModel": {
                "GenericActivity": "generic_002",
                "ActivityTailored": "activity_002"
              }
            },
            "probabilityWeight": 50.0,
            "decisionPoints": [
              {
                "criterion": "High historical engagement in social activities",
                "rationale": "Encourages meaningful social connection without being overly demanding."
              }
            ]
          }
        ],
        "decisionPoints": [
          {
            "criterion": "Minimum wheel size requirement",
            "rationale": "At least two tailored activities are required for a valid wheel."
          }
        ]
      }
    },
    "supportingData": {
      "catalogReference": "Selected activities from GenericActivityCatalog based on strategy."
    },
    "recommendations": {},
    "confidenceMetrics": {
      "wheelConstructionConfidence": 0.92
    }
  },
  "nextAgent": "Orchestrator Agent",
  "updatedDataModels": [
    "ActivityTailored",
    "Wheel",
    "WheelItem"
  ]
}
```

---

### 8. Ethical Oversight Agent

**Input Schema (Receives the Wheel/Activity Agent’s output and psychological profile via the Orchestrator):**

```json
{
  "agent": "Ethical Oversight Agent",
  "purpose": "Validate each tailored activity against ethical guidelines and user safety, ensuring no task exceeds Philipp's psychological or physical limits.",
  "inputData": {
    "contextPacket": {
      "activityWheel": {
        "wheelItems": [
          {
            "activityTailored": {
              "id": "activity_001",
              "name": "Build a Small Wooden Crate"
            },
            "probabilityWeight": 50.0
          },
          {
            "activityTailored": {
              "id": "activity_002",
              "name": "Call an Old Friend"
            },
            "probabilityWeight": 50.0
          }
        ]
      },
      "psychologicalProfile": {
        "currentMood": "anxious",
        "challengeTolerance": "moderate"
      }
    },
    "systemData": {
      "UserFeedback": {
        "safetyFlags": "No immediate red flags detected."
      }
    },
    "specificRequest": {
      "action": "Perform ethical review of each tailored activity",
      "timestamp": "2025-03-12T08:30:00Z"
    }
  },
  "accessedDataModels": [
    "ActivityTailored",
    "UserFeedback"
  ]
}
```

**Output Schema (Produces the ethical review of the wheel):**

```json
{
  "agent": "Ethical Oversight Agent",
  "outputData": {
    "primaryOutput": {
      "ethicalReview": {
        "approvedActivities": [
          "activity_001",
          "activity_002"
        ],
        "rejectedActivities": [],
        "decisionPoints": [
          {
            "criterion": "Cross-check with current psychological state and physical limitations",
            "rationale": "Both activities fall within safe and acceptable challenge levels."
          }
        ]
      }
    },
    "supportingData": {
      "reviewSummary": "All tailored activities meet ethical and safety standards based on user profile."
    },
    "recommendations": {},
    "confidenceMetrics": {
      "ethicalReviewConfidence": 0.95
    }
  },
  "nextAgent": "Orchestrator Agent",
  "updatedDataModels": []
}
```

---

### Final Process Flow Schema

This final schema ties together the full flow—from the Mentor Agent’s initial context to the Orchestrator’s final aggregation of ethically-approved activities (the final wheel) that will be presented back to the Mentor Agent.

```json
{
  "ProcessFlow": {
    "MentorAgent": {
      "input": "User inquiry and historical context (see Mentor Agent Input Schema)",
      "output": "Structured contextPacket with mood, environment, timeAvailability, etc. (see Mentor Agent Output Schema)"
    },
    "OrchestratorAgent": {
      "input": "Receives contextPacket from Mentor Agent plus system data (see Orchestrator Agent Input Schema)",
      "delegatedAgents": [
        "Resource & Capacity Management Agent",
        "Engagement & Pattern Analytics Agent",
        "Psychological Monitoring Agent",
        "Strategy Agent",
        "Wheel/Activity Agent",
        "Ethical Oversight Agent"
      ],
      "output": "Aggregated wheel generation plan and collected specialized outputs (see Orchestrator Agent Output Schema)"
    },
    "SpecializedAgents": {
      "ResourceAnalysis": "Output from Resource & Capacity Management Agent",
      "EngagementAnalysis": "Output from Engagement & Pattern Analytics Agent",
      "PsychologicalAssessment": "Output from Psychological Monitoring Agent",
      "StrategyFramework": "Output from Strategy Agent",
      "WheelActivities": "Output from Wheel/Activity Agent",
      "EthicalReview": "Output from Ethical Oversight Agent"
    },
    "FinalOutput": {
      "finalWheel": {
        "wheelID": "wheel_20250312_001",
        "description": "Daily challenge wheel tailored for Philipp",
        "wheelItems": [
          {
            "activityTailored": {
              "id": "activity_001",
              "name": "Build a Small Wooden Crate"
            },
            "probabilityWeight": 50.0
          },
          {
            "activityTailored": {
              "id": "activity_002",
              "name": "Call an Old Friend"
            },
            "probabilityWeight": 50.0
          }
        ]
      },
      "presentationMessage": "Philipp, your daily activity wheel has been generated based on your current state and previous feedback. Enjoy your balanced mix of creative and social tasks!"
    }
  }
}
```

---


{
  "agent": "Wheel/Activity Agent",
  "schemaVersion": "2.1.0",
  "purpose": "Select and tailor concrete activities from the GenericActivity catalog based on the strategy framework and available system resources.",
  
  "inputSchema": {
    "metaData": {
      "sessionID": "sess_20250312_Philipp_001",
      "requestID": "req_2025031208250001",
      "timestamp": "2025-03-12T08:25:00.223Z",
      "schemaVersion": "2.1.0"
    },
    "contextPacket": {
      "strategyFramework": {
        "challengeLevel": {
          "overall": "moderate",
          "dimensions": {
            "cognitive": 0.4,
            "physical": 0.3,
            "emotional": 0.5,
            "social": 0.6
          },
          "calibrationFactors": {
            "userTrust": 0.65,
            "recentCompletionRate": 0.72,
            "psychologicalReadiness": 0.68
          }
        },
        "domainDistribution": {
          "creative": {
            "weight": 40,
            "preferredTimeOfDay": "morning",
            "subdomains": {
              "visual": 0.6,
              "constructive": 0.3,
              "expressive": 0.1
            }
          },
          "social": {
            "weight": 35,
            "preferredTimeOfDay": "afternoon",
            "subdomains": {
              "oneonone": 0.7,
              "smallGroup": 0.2,
              "digital": 0.1
            }
          },
          "physical": {
            "weight": 15,
            "preferredTimeOfDay": "morning",
            "subdomains": {
              "endurance": 0.2,
              "strength": 0.3,
              "dexterity": 0.5
            }
          },
          "intellectual": {
            "weight": 10,
            "preferredTimeOfDay": "evening",
            "subdomains": {
              "problem-solving": 0.4,
              "learning": 0.4,
              "planning": 0.2
            }
          }
        },
        "adaptationParameters": {
          "noveltyLevel": 0.35,
          "consistencyFactor": 0.65,
          "structurePreference": 0.58
        },
        "userGoalAlignment": {
          "shortTerm": ["improve_farm_infrastructure", "reduce_daily_stress"],
          "longTerm": ["build_self_sufficiency", "develop_creative_skills"]
        }
      },
      "resourceConstraints": {
        "timeAvailable": {
          "total": 150,  // in minutes
          "contiguous": true,
          "bestStartTime": "09:30"
        },
        "physicalResources": {
          "woodScraps": {
            "quantity": "moderate",
            "quality": "variable",
            "location": "barn"
          },
          "tools": {
            "quantity": "limited",
            "variety": "basic farm tools",
            "condition": "aged but functional"
          }
        },
        "weatherConditions": {
          "current": "partly cloudy",
          "temperature": 18,  // celsius
          "precipitation": 0.1,  // probability
          "wind": "light",
          "outdoorSuitability": 0.8
        }
      },
      "psychologicalReadiness": {
        "anxietyLevel": 0.45,
        "currentEnergy": 0.65,
        "focusCapacity": 0.70,
        "socialReadiness": 0.60,
        "creativityReadiness": 0.75
      },
      "previousEngagement": {
        "recentlyCompletedDomains": ["creative", "intellectual"],
        "recentlyAbandonedDomains": ["physical"],
        "highestCompletionRateDomain": "creative",
        "lowestCompletionRateDomain": "physical"
      }
    },
    "systemData": {
      "GenericActivityCatalog": [
        {
          "id": "generic_001",
          "name": "Build a small wooden crate",
          "description": "Construct a basic crate using available wood scraps.",
          "durationRange": {
            "min": 15,
            "max": 30,
            "optimal": 20
          },
          "instructions": {
            "setup": "Collect wood scraps and basic tools.",
            "steps": [
              "Measure and cut 4 boards for the base (approximately 30cm x 30cm)",
              "Cut 4 vertical corner pieces (approximately 30cm tall)",
              "Assemble the base by nailing boards in a square pattern",
              "Attach corner pieces to each corner of the base",
              "Add side panels by nailing boards between corner pieces"
            ],
            "completion": "Sand any rough edges for safety"
          },
          "requirements": {
            "physical": {
              "strength": 0.2,
              "dexterity": 0.5,
              "endurance": 0.1
            },
            "cognitive": {
              "focus": 0.3,
              "problemSolving": 0.4,
              "creativity": 0.5
            },
            "resources": {
              "wood": {
                "type": "scraps",
                "minimumAmount": "4-6 boards",
                "alternatives": ["old pallets", "reclaimed lumber"]
              },
              "tools": {
                "required": ["hammer", "saw", "measuring tape"],
                "optional": ["sandpaper", "level"]
              },
              "environment": {
                "workspace": "minimum 2m²",
                "weatherDependence": 0.3
              }
            }
          },
          "attributes": {
            "primaryDomain": "creative",
            "secondaryDomain": "physical",
            "challengeRating": 0.35,
            "satisfactionPotential": 0.75,
            "completionRate": 0.68,
            "adaptability": 0.8
          },
          "benefits": {
            "skills": ["woodworking", "spatial reasoning", "tool usage"],
            "psychological": ["sense of accomplishment", "stress reduction"],
            "practical": ["storage solution", "farm organization"]
          },
          "variations": [
            {
              "id": "var_001a",
              "name": "Simple tool rack",
              "difficultyAdjustment": 0.1,
              "durationAdjustment": 5
            },
            {
              "id": "var_001b",
              "name": "Decorative planter box",
              "difficultyAdjustment": 0.2,
              "durationAdjustment": 10
            }
          ]
        },
        {
          "id": "generic_002",
          "name": "Call an old friend",
          "description": "Reconnect by sharing a personal insight over the phone.",
          "durationRange": {
            "min": 10,
            "max": 30,
            "optimal": 15
          },
          "instructions": {
            "setup": "Find a quiet, comfortable place where you won't be interrupted.",
            "steps": [
              "Choose a friend you haven't spoken with recently",
              "Prepare 1-2 meaningful topics or insights to share",
              "Make the call at an appropriate time",
              "Begin with warm greetings and brief catch-up",
              "Share your prepared insight and listen to their response"
            ],
            "completion": "End with appreciation and perhaps plans to talk again"
          },
          "requirements": {
            "physical": {
              "strength": 0.0,
              "dexterity": 0.1,
              "endurance": 0.0
            },
            "cognitive": {
              "focus": 0.5,
              "problemSolving": 0.1,
              "creativity": 0.3
            },
            "social": {
              "expressiveness": 0.6,
              "empathy": 0.7,
              "adaptability": 0.5
            },
            "resources": {
              "technology": {
                "type": "phone",
                "alternatives": ["video call", "voice message"]
              },
              "environment": {
                "quietSpace": true,
                "privacyLevel": "moderate"
              }
            }
          },
          "attributes": {
            "primaryDomain": "social",
            "secondaryDomain": "emotional",
            "challengeRating": 0.4,
            "satisfactionPotential": 0.8,
            "completionRate": 0.75,
            "adaptability": 0.9
          },
          "benefits": {
            "skills": ["communication", "active listening", "empathy"],
            "psychological": ["connection", "reduced isolation", "perspective"],
            "practical": ["maintain social networks", "potential support system"]
          },
          "variations": [
            {
              "id": "var_002a",
              "name": "Scheduled video coffee break",
              "difficultyAdjustment": 0.1,
              "durationAdjustment": 10
            },
            {
              "id": "var_002b",
              "name": "Send a thoughtful voice message",
              "difficultyAdjustment": -0.1,
              "durationAdjustment": -5
            }
          ]
        }
      ],
      "UserHistory": {
        "previousActivities": [
          {
            "activityId": "activity_previous_001",
            "baseGenericId": "generic_001",
            "completionStatus": "completed",
            "satisfactionRating": 0.75,
            "challengeRating": 0.4,
            "completionDate": "2025-03-05T14:12:23Z",
            "userFeedback": "Enjoyed having something tangible at the end"
          },
          {
            "activityId": "activity_previous_002",
            "baseGenericId": "generic_037",
            "completionStatus": "abandoned",
            "abandonmentReason": "Too physically demanding",
            "completionDate": "2025-03-07T09:45:11Z",
            "userFeedback": "Started well but got too exhausting"
          }
        ],
        "activityPreferences": {
          "preferredDuration": 15,
          "preferredChallengeLevel": 0.35,
          "preferredCompletionIndicators": ["tangible result", "clear endpoint"]
        }
      },
      "Inventory": {
        "availableResources": {
          "wood": {
            "scraps": {
              "quantity": "moderate",
              "location": "barn",
              "lastUpdated": "2025-03-10T16:30:00Z"
            }
          },
          "tools": {
            "hammer": {
              "quantity": 2,
              "condition": "good",
              "location": "toolshed"
            },
            "saw": {
              "quantity": 1,
              "condition": "fair",
              "location": "toolshed"
            },
            "nails": {
              "quantity": "plentiful",
              "variety": "assorted sizes",
              "location": "toolshed"
            }
          },
          "technology": {
            "phone": {
              "available": true,
              "internetConnectivity": "moderate",
              "batteryStatus": "good"
            }
          }
        }
      }
    },
    "specificRequest": {
      "action": "Generate a tailored activity wheel",
      "timestamp": "2025-03-12T08:25:00.223Z",
      "parameters": {
        "wheelSize": {
          "minimum": 2,
          "maximum": 4,
          "optimal": 3
        },
        "activityOverlap": "minimal",
        "diversityRequirement": "moderate",
        "noveltyFactor": 0.4
      }
    },
    "errorHandling": {
      "timeoutSeconds": 30,
      "fallbackStrategy": "use_cached_activities",
      "retryAttempts": 2,
      "requiredFields": ["strategyFramework", "specificRequest"]
    }
  },
  
  "outputSchema": {
    "metaData": {
      "responseID": "resp_2025031208253012",
      "sessionID": "sess_20250312_Philipp_001",
      "processingTime": 2.34,  // seconds
      "timestamp": "2025-03-12T08:25:03.563Z",
      "status": {
        "code": 200,
        "message": "Success"
      }
    },
    "primaryOutput": {
      "activityWheel": {
        "wheelID": "wheel_20250312_001",
        "sessionID": "sess_20250312_Philipp_001",
        "creationTimestamp": "2025-03-12T08:25:03.123Z",
        "description": "A balanced wheel focused on creative and social activities with moderate challenge levels, tailored for farm resources.",
        "wheelItems": [
          {
            "activityTailored": {
              "id": "activity_001",
              "name": "Build a Small Wooden Crate for Farm Tools",
              "baseGenericActivityId": "generic_001",
              "description": "Using the wood scraps from your barn, construct a practical crate to organize your smaller farm tools.",
              "duration": {
                "estimated": 20,
                "range": "15-25 minutes",
                "pacing": "relaxed"
              },
              "instructions": {
                "contextualized": true,
                "setup": "Collect the wood scraps from the barn's east corner and gather your hammer and saw from the toolshed.",
                "steps": [
                  "Measure and cut 4 boards for the base (approximately 30cm x 30cm) using the old measuring tape from the kitchen drawer",
                  "Cut 4 vertical corner pieces (approximately 30cm tall)",
                  "Assemble the base by nailing boards in a square pattern - the slightly curved nails in the coffee tin work fine for this",
                  "Attach corner pieces to each corner of the base",
                  "Add side panels by nailing boards between corner pieces - leave one side partially open for easy access"
                ],
                "completion": "Sand any rough edges for safety using the small piece of sandpaper by the workbench",
                "adaptations": [
                  "If wood is too tough to cut, the old chisel can be used to score deep lines first",
                  "In case of limited wood, a smaller crate (20cm x 20cm) is still functional"
                ]
              },
              "requirements": {
                "adjusted": {
                  "physical": {
                    "strength": 0.15,  // reduced from original
                    "dexterity": 0.4,  // reduced slightly
                    "endurance": 0.1
                  },
                  "cognitive": {
                    "focus": 0.3,
                    "problemSolving": 0.3,  // reduced slightly
                    "creativity": 0.5
                  }
                },
                "resources": {
                  "verifiedAvailable": true,
                  "wood": {
                    "location": "barn's east corner",
                    "substitutes": ["The old palette behind the chicken coop"]
                  },
                  "tools": {
                    "verifiedAvailable": true,
                    "locations": {
                      "hammer": "toolshed hanging rack",
                      "saw": "toolshed bottom shelf",
                      "measuring tape": "kitchen drawer"
                    }
                  }
                }
              },
              "valueProposition": {
                "alignmentWithGoals": {
                  "shortTerm": {
                    "goal": "improve_farm_infrastructure",
                    "contribution": "Creates organization for small tools, reducing daily frustration"
                  },
                  "longTerm": {
                    "goal": "develop_creative_skills",
                    "contribution": "Practices basic woodworking in a low-pressure project"
                  }
                },
                "psychologicalBenefits": [
                  "Provides tangible results which you've found satisfying in the past",
                  "Combines creativity with practical utility, matching your preference pattern",
                  "Short duration with clear completion criteria helps maintain focus"
                ],
                "learningValue": "Develops basic construction skills transferable to larger farm projects",
                "evidenceBase": "Your previous wooden projects have a 75% completion rate with high satisfaction"
              },
              "tailorizationLevel": 3,
              "baseChallengeRating": 0.3,
              "adjustedChallengeRating": 0.28,
              "linkedDjangoModel": {
                "GenericActivity": "generic_001",
                "ActivityTailored": "activity_001"
              },
              "adaptationDetails": {
                "personalizedElements": [
                  "Location-specific resource references",
                  "Tool availability adjustments",
                  "Challenge level reduction based on reported anxiety",
                  "Added practical farm application to increase relevance"
                ],
                "modifiedSteps": [
                  "Added tool location specificity",
                  "Included alternative approaches for resource limitations",
                  "Simplified measurement requirements"
                ]
              }
            },
            "probabilityWeight": 55.0,
            "wheelPosition": 1,
            "tags": ["creative", "practical", "organizational", "woodworking"],
            "decisionPoints": [
              {
                "ruleId": "resource_match_001",
                "criterion": "Available resources closely match requirements",
                "confidenceLevel": 0.9,
                "rationale": "Verified inventory shows all necessary materials are readily accessible in the barn and toolshed."
              },
              {
                "ruleId": "psychological_fit_002",
                "criterion": "Challenge level appropriate for current anxiety",
                "confidenceLevel": 0.85,
                "rationale": "Reduced physical requirements match with current moderate anxiety and preference for creative activities."
              },
              {
                "ruleId": "goal_alignment_004",
                "criterion": "Directly supports farm infrastructure improvement goal",
                "confidenceLevel": 0.92,
                "rationale": "Activity creates functional storage for organizing farm tools, addressing stated short-term goal."
              }
            ]
          },
          {
            "activityTailored": {
              "id": "activity_002",
              "name": "Call Your Friend Thomas from Frankfurt",
              "baseGenericActivityId": "generic_002",
              "description": "Take a short break to reconnect with Thomas and share your recent insight about sustainable farming.",
              "duration": {
                "estimated": 15,
                "range": "10-20 minutes",
                "pacing": "natural conversation"
              },
              "instructions": {
                "contextualized": true,
                "setup": "Find a comfortable spot on the porch overlooking your fields for a sense of perspective during the call.",
                "steps": [
                  "Take 2 minutes to jot down your recent realization about crop rotation on your small notepad",
                  "Text Thomas first to ensure it's a good time to call",
                  "Begin with catching up briefly on his urban gardening project from last month",
                  "Share your insight about how you've adapted traditional crop rotation for your small farm plots",
                  "Listen to his perspective and experiences with his balcony garden"
                ],
                "completion": "End with appreciation for his friendship despite the distance between farm and city life",
                "adaptations": [
                  "If Thomas is busy, leave a detailed voice message sharing your farming insight instead",
                  "If you feel anxious, focus the conversation on specific farming questions to give it structure"
                ]
              },
              "requirements": {
                "adjusted": {
                  "cognitive": {
                    "focus": 0.4,
                    "creativity": 0.3
                  },
                  "social": {
                    "expressiveness": 0.5,  // reduced slightly
                    "empathy": 0.7,
                    "adaptability": 0.5
                  }
                },
                "resources": {
                  "verifiedAvailable": true,
                  "phone": {
                    "contacts": ["Thomas (Frankfurt)"],
                    "signal": "Strongest on the east-facing porch"
                  },
                  "environment": {
                    "recommendations": "The wooden chair on the porch provides both comfort and privacy"
                  }
                }
              },
              "valueProposition": {
                "alignmentWithGoals": {
                  "shortTerm": {
                    "goal": "reduce_daily_stress",
                    "contribution": "Social connection provides perspective and emotional support"
                  },
                  "longTerm": {
                    "goal": "build_self_sufficiency",
                    "contribution": "Exchanging ideas about farming techniques with an outside perspective"
                  }
                },
                "psychologicalBenefits": [
                  "Brief social connection reduces isolation of farm life",
                  "Sharing your progress validates your efforts",
                  "Structured conversation topic reduces social anxiety"
                ],
                "learningValue": "Exchange of practical gardening knowledge applicable to farm development",
                "evidenceBase": "Your previous calls with friends show 75% completion rate with high satisfaction"
              },
              "tailorizationLevel": 3,
              "baseChallengeRating": 0.4,
              "adjustedChallengeRating": 0.35,
              "linkedDjangoModel": {
                "GenericActivity": "generic_002",
                "ActivityTailored": "activity_002"
              },
              "adaptationDetails": {
                "personalizedElements": [
                  "Specific friend selection based on shared interest",
                  "Farming-related conversation topic",
                  "Location-specific call setting",
                  "Structured conversation framework to reduce anxiety"
                ],
                "modifiedSteps": [
                  "Added text message pre-check to reduce call anxiety",
                  "Included specific conversation points relevant to farm context",
                  "Added fallback for asynchronous communication"
                ]
              }
            },
            "probabilityWeight": 45.0,
            "wheelPosition": 2,
            "tags": ["social", "connection", "knowledge-sharing", "perspective"],
            "decisionPoints": [
              {
                "ruleId": "engagement_pattern_003",
                "criterion": "High historical engagement in social activities",
                "confidenceLevel": 0.82,
                "rationale": "Previous social connections show high completion rate when topic is structured around shared interests."
              },
              {
                "ruleId": "psychological_fit_006",
                "criterion": "Social