
## 1. Mentor Agent Step

### Input
- **User Context**
  - Mood: Anxious, feeling directionless
  - Current Location: Run-down farm near Lyon
  - Time of Day: Morning (09:30)
  - Available Time: Approximately 3 hours
  - Recent Emotional State: Overwhelmed by lack of structure
  - Spoken Language: German
  - Age: 22
  - Immediate Environment: Farm with limited resources

### Process
- Retrieve user's historical interaction logs
- Analyze previous feedback and activity patterns
- Formulate a gentle, non-threatening inquiry approach
- Create a context-aware communication strategy
- Prepare open-ended questions to gather additional insights
- Use natural language processing to extract nuanced emotional cues
- Cross-reference with user's profile and previous interactions

### Output
- **Structured Context Packet**
  - Mood Assessment: "Anxious but with potential for engagement"
  - Key Focus Areas:
    * Provide structure
    * Build confidence
    * Introduce gentle challenges
  - Recommended Interaction Tone: Supportive, understanding
  - Preliminary Trust Level: Neutral (slight positive potential)
  - Communication Recommendations:
    * Use clear, simple language
    * Offer clear rationale for activities
    * Emphasize user's agency and choice

## 2. Orchestrator Agent Step

### Input
- **Comprehensive Context Packet**
  - Mentor Agent's mood and focus analysis
  - User Profile Data:
    * Name: Philipp
    * Age: 22
    * Location: Farm near Lyon
    * Primary Goals: Break cycle of abandoned projects
  - Historical Activity Logs:
    * Pattern of starting but not completing projects
    * Previous interests: AI, sailing, various university studies
  - Current Environmental Context:
    * Farm setting
    * Limited modern tools
    * Seasonal considerations (early spring)

### Process
- Aggregate and synthesize input data
- Identify potential activity domains
- Map user's psychological state to activity selection strategy
- Determine delegation priorities for specialized agents
- Create a preliminary framework for activity selection
- Analyze potential challenges and mitigation strategies
- Prepare detailed briefing for each specialized agent

### Output
- **Agent Delegation Plan**
  - Priority Agents to Consult:
    1. Resource & Capacity Management
    2. Engagement & Pattern Analytics
    3. Psychological Monitoring
  - Consolidated Context Summary
  - Initial Hypothesis:
    * Focus on short, achievable activities
    * Balance creative and practical domains
    * Provide clear, structured challenges
  - Recommended Initial Approach:
    * 2-3 activities maximum
    * Duration: 15-30 minutes each
    * Low to moderate challenge level

## 3. Resource & Capacity Management Agent

### Input
- **Resource Assessment Data**
  - Physical Environment:
    * Farm location
    * Available tools: Old farm tools
    * Materials: Wood scraps, limited modern equipment
  - Time Constraints:
    * Available time: 3 hours
    * Preferred activity window: Morning to early afternoon
  - Physical Capabilities:
    * Basic strength
    * Moderate dexterity
    * Limited experience with complex tasks

### Process
- Inventory comprehensive resource availability
- Analyze tool condition and usability
- Map available resources to potential activity types
- Assess physical limitations and capabilities
- Identify potential activity constraints
- Calculate realistic time allocation
- Evaluate environmental suitability for activities

### Output
- **Resource Utilization Report**
  - Available Tools:
    * Hammer (2, in good condition)
    * Saw (1, fair condition)
    * Miscellaneous farm tools
  - Material Availability:
    * Wood scraps (moderate quantity)
    * Reclaimed materials possible
  - Time Allocation Recommendation:
    * Primary activity window: 09:30 - 12:30
    * Suggested activity duration: 20-30 minutes
  - Physical Capability Constraints:
    * Avoid heavy lifting
    * Focus on precision-based tasks
    * Emphasize achievable, tangible outcomes

## 4. Engagement & Pattern Analytics Agent

### Input
- **Historical Engagement Data**
  - Completed Activities:
    * Creative drawing
    * Casual conversations
  - Abandoned Projects:
    * Physical exercises
    * Complex DIY projects
    * Multiple university study attempts
  - Interaction Patterns:
    * High initial enthusiasm
    * Quick disengagement
    * Preference for creative, low-commitment tasks

### Process
- Analyze completion rates across different activity domains
- Identify patterns of engagement and disengagement
- Calculate domain preference weightings
- Assess psychological triggers for project abandonment
- Evaluate potential motivational strategies
- Create engagement probability matrix

### Output
- **Engagement Profile**
  - Preferred Domains:
    1. Creative (75% engagement)
    2. Social (65% engagement)
    3. Physical (20% engagement)
  - Recommended Domain Distribution:
    * Creative: 40%
    * Social: 35%
    * Physical: 15%
    * Intellectual: 10%
  - Engagement Optimization Strategies:
    * Short, defined activities
    * Clear outcome expectations
    * Minimal administrative overhead
    * Immediate sense of achievement

## 5. Psychological Monitoring Agent

### Input
- **Psychological Context**
  - Current Mood: Anxious, hopeful
  - Personality Trait Indicators:
    * Openness: High
    * Conscientiousness: Moderate
    * Extraversion: Moderate
  - Trust Level: Emerging, cautious
  - Psychological Barriers:
    * Fear of failure
    * Overwhelm from too many options
    * Desire for meaningful progress

### Process
- Analyze mood and emotional state
- Assess challenge tolerance
- Evaluate psychological readiness for new activities
- Identify potential growth areas
- Determine appropriate challenge calibration
- Consider psychological safety margins

### Output
- **Psychological Readiness Assessment**
  - Challenge Tolerance: Low to Moderate
  - Recommended Approach:
    * Gentle, supportive activities
    * Clear, achievable goals
    * Minimal performance pressure
  - Growth Focus Areas:
    * Building consistent completion habits
    * Developing self-confidence
    * Exploring creative problem-solving
  - Emotional Support Strategies:
    * Provide clear activity rationales
    * Emphasize learning over perfection
    * Create safe exploration environment

## 6. Strategy Agent

### Input
- **Integrated Agent Insights**
  - Resource Constraints:
    * Limited farm tools
    * 3 hours available time
    * Wood scraps as primary material
  - Engagement Patterns:
    * High creative domain preference
    * Low physical activity completion
    * Moderate social interaction
  - Psychological Profile:
    * Anxious but hopeful
    * Moderate challenge tolerance
    * Desire to break project abandonment cycle
  - User Goals:
    * Improve farm infrastructure
    * Develop consistent action habits
    * Reduce daily stress
    * Build self-sufficiency

### Process
- Synthesize insights from specialized agent reports
- Align activity recommendations with user's psychological state
- Balance challenge levels with user's current capabilities
- Create a holistic strategy framework
- Identify potential synergies between different activity domains
- Develop a nuanced approach to user growth
- Ensure activities support multiple goal dimensions

### Output
- **Comprehensive Strategy Framework**
  - Challenge Calibration:
    * Overall Challenge Level: Low to Moderate
    * Psychological Safety Margin: High
  - Domain Distribution Strategy:
    * Creative: 40% (Primary Growth Domain)
    * Social: 35% (Secondary Engagement)
    * Physical: 15% (Minimal Challenge)
    * Intellectual: 10% (Supplementary Learning)
  - Activity Selection Criteria:
    * Maximum duration: 30 minutes
    * Tangible outcome requirement
    * Low resource intensity
    * Clear, achievable objectives
  - Growth Alignment Principles:
    * Build confidence through completion
    * Provide immediate sense of achievement
    * Minimize performance anxiety
    * Create low-stakes learning opportunities

## 7. Wheel/Activity Agent

### Input
- **Activity Generation Context**
  - Strategy Framework Details:
    * Challenge Level: Low to Moderate
    * Domain Preferences Identified
  - Available Resource Catalog:
    * Farm environment
    * Wood scraps
    * Basic farm tools
  - User Profile Constraints:
    * Short attention span
    * Need for tangible results
    * Limited physical endurance
  - Generic Activity Options:
    * Construction-based activities
    * Social interaction tasks
    * Creative problem-solving challenges

### Process
- Scan generic activity catalog
- Match activities to strategy framework
- Personalize activities to user's specific context
- Adjust difficulty and duration
- Create tailored activity descriptions
- Ensure resource availability
- Build probabilistic wheel selection mechanism
- Incorporate personal growth elements

### Output
- **Personalized Activity Wheel**
  - Activity 1: Wooden Tool Organizer
    * Domain: Creative/Physical
    * Duration: 20-25 minutes
    * Resource Requirement: Wood scraps, basic tools
    * Challenge Level: Low
    * Outcome: Tangible farm infrastructure improvement
    * Personal Growth Elements:
      - Develop basic construction skills
      - Create practical farm solution
      - Build confidence through completion
    * Probability Weight: 55%

  - Activity 2: Reconnection Call with Frankfurt Friend
    * Domain: Social/Emotional
    * Duration: 15-20 minutes
    * Resource Requirement: Phone, quiet space
    * Challenge Level: Low
    * Outcome: Social connection, perspective sharing
    * Personal Growth Elements:
      - Practice structured communication
      - Reduce social isolation
      - Share recent life insights
    * Probability Weight: 45%

  - Wheel Composition:
    * Total Activities: 2
    * Balanced Domain Representation
    * Complementary Skill Development

## 8. Ethical Oversight Agent

### Input
- **Activity and User Context**
  - Generated Activity Wheel Details
    * Two proposed activities
    * Low challenge levels
    * Personal growth focus
  - Psychological Profile:
    * Anxiety-prone
    * Seeking structure
    * Desire for meaningful progress
  - Ethical Considerations:
    * User autonomy
    * Psychological safety
    * Transparency
    * Genuine personal development

### Process
- Review each proposed activity
- Assess potential psychological risks
- Validate alignment with user's well-being
- Check for inappropriate challenge levels
- Ensure activities respect user boundaries
- Verify transparent communication approach
- Evaluate potential unintended consequences

### Output
- **Ethical Review and Validation**
  - Activity Approval Status:
    * Activity 1 (Wooden Tool Organizer): Approved
    * Activity 2 (Reconnection Call): Approved
  - Ethical Validation Criteria:
    * Maintains user psychological safety
    * Provides clear, achievable challenges
    * Supports user's stated goals
    * Minimizes potential for negative emotional impact
  - Recommended Communication Approach:
    * Explain activity rationale
    * Highlight potential personal growth
    * Offer clear, supportive instructions
    * Emphasize user's choice and agency
  - Risk Mitigation Strategies:
    * Provide optional activity variations
    * Create easy exit/modification paths
    * Ensure low-pressure engagement model

## Final Wheel Generation Outcome

- **Comprehensive User Experience Design**
  - Carefully curated activities
  - Personalized to Philipp's specific context
  - Balanced between challenge and psychological safety
  - Clear path for personal growth and skill development
  - Minimal risk of triggering anxiety or abandonment
