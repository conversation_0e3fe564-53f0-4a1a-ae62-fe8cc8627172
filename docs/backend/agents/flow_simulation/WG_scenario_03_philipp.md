# Wheel Generation Agent Workflow - <PERSON>'s Morning Session

## 1. Scenario Context

### User Profile
- **user_profile.id**: "550e8400-e29b-41d4-a716-446655440000"
- **user_profile.profile_name**: "<PERSON>"
- **demographics.full_name**: "<PERSON>"
- **demographics.age**: 22
- **demographics.gender**: "Male"
- **demographics.location**: "Farm near Lyon, France"
- **demographics.occupation**: "Currently unemployed, former student"
- **user_profile.current_environment**: [GenericEnvironment object with code="rural_farm"]

### Psychological State
- **current_mood**: "Motivated but somewhat anxious"
- **trust_level.value**: 68 (Moderate-high, representing growing trust with the system)
- **trust_phase**: "Early Expansion Phase" (Has completed several successful activities)
- **UserTraitInclinations**: 
  - **Openness**: 78 (High - drawn to new experiences)
  - **Conscientiousness**: 42 (Medium-low - struggles with follow-through)
  - **Extraversion**: 56 (Medium - socially capable but sometimes avoidant)
  - **Agreeableness**: 64 (Medium-high - generally cooperative)
  - **Neuroticism**: 61 (Medium-high - experiences anxiety about decisions)

### User Goals & Beliefs
- **Primary UserGoal**: "Break cycle of abandoned projects" (importance_according_user: 90)
- **Secondary UserGoal**: "Find meaningful direction" (importance_according_user: 85)
- **Key Belief**: "I start projects with enthusiasm but always abandon them" (stability: 75)
- **Key Belief**: "I need external structure to follow through" (stability: 82)
- **Inspiration**: "Create something tangible on the farm" (strength: 74)

### Environmental Context
- **time_of_day**: "09:15 AM" (Morning, typically higher energy)
- **day_of_week**: "Wednesday" (Weekday, typical routine day)
- **weather**: "Partly cloudy, mild temperature"
- **location_details**: "Isolated farm, 5km from nearest village"
- **available_time**: "4 hours before friend returns"
- **available_resources**: 
  - Farm tools (rakes, shovels, wheelbarrow)
  - Art supplies (sketchbook, basic paints)
  - Bicycle
  - Internet connection
  - Kitchen supplies

### Recent History
- Completed a successful creative activity 2 days ago (drawing the farmhouse)
- Refused a physically demanding activity yesterday (claimed too tired)
- Has shown increasing engagement with reflective activities
- Morning journal entries indicate growing confidence but lingering anxiety about commitment

## 2. Agent Flow Overview

1. **Mentor Agent** - Initial user interaction to gather context
2. **Orchestrator Agent** - Task distribution and workflow management
3. **Resource & Capacity Agent** - Analysis of available resources and time
4. **Engagement & Pattern Analytics Agent** - Historical pattern analysis
5. **Psychological Monitoring Agent** - Assessment of current psychological state
6. **Strategy Agent** - Framework creation for challenge calibration
7. **Wheel/Activity Agent** - Activity selection and wheel construction
8. **Ethical Oversight Agent** - Ethical validation of selected activities
9. **Mentor Agent** - Final wheel presentation to user

## 3. Agent Interaction Schemas

### Mentor Agent - Initial Interaction

#### Input Data Structure
- **agent**: "Mentor Agent"
- **purpose**: "Engage with user to gather current context and state for wheel generation"
- **inputData**: null (Initial activation point)
- **accessedDataModels**:
  - **HistoryLog**: filtered by user_profile.id="550e8400-e29b-41d4-a716-446655440000", recent entries only
  - **UserFeedbackLog**: filtered by recent entries to avoid repetitive questioning

#### Processing Steps
- Greet user with personalized message: "Good morning, Philipp! How are you feeling today as you start your day on the farm?"
- Check recent HistoryLog entries to contextualize questions:
  - Note recent activity completion (creative drawing task)
  - Note recent activity refusal (physical farm task)
- Present structured dialogue to gather contextual information:
  - "How's your energy level this morning?"
  - "What's on your mind today?"
  - "How much time do you have available before other commitments?"
  - "Is there anything specific about your environment or situation today I should know?"
  - "How did you feel about your drawing activity from two days ago?"
- Process natural language responses to extract structured data:
  - Identify emotional keywords ("motivated", "anxious", "curious")
  - Extract time availability information ("about 4 hours")
  - Note environmental factors ("friend is away", "weather is okay")
  - Capture any specific intentions ("thinking about doing something creative")

#### Output Data Structure
- **agent**: "Mentor Agent"
- **outputData**:
  - **contextPacket**:
    - **user_id**: "550e8400-e29b-41d4-a716-446655440000"
    - **session_timestamp**: "2025-03-12T09:15:00Z"
    - **reported_mood**: "I'm feeling motivated to try something new today, but a bit anxious about starting something I might not finish"
    - **reported_environment**: "I'm at the farm, my friend is away until evening, weather is nice enough to be outside"
    - **reported_time_availability**: "I have about 4 hours free before I need to start preparing dinner"
    - **reported_focus**: "I've been thinking about doing something creative, but also feel I should be productive around the farm"
    - **reported_satisfaction**: "I enjoyed the drawing activity from before, it felt good to complete something"
  - **forwardTo**: "Orchestrator Agent"
- **updatedDataModels**: None (Context gathering only)

### Orchestrator Agent - Task Distribution

#### Input Data Structure
- **agent**: "Orchestrator Agent"
- **purpose**: "Analyze context, determine task sequence, and coordinate specialized agents"
- **receivedFrom**: "Mentor Agent"
- **inputData**:
  - **contextPacket**: [Full packet from Mentor Agent]
- **accessedDataModels**: None (Routing and coordination only)

#### Processing Steps
- Identify the task type: "Generate new activity wheel"
- Parse critical context elements:
  - User is in early Expansion Phase (trust_level.value: 68)
  - Available time is approximately 4 hours
  - User has multiple environment options (indoor/outdoor)
  - User has expressed both creative and productive desires
- Determine appropriate agent activation sequence:
  - First tier (parallel): Resource & Capacity Agent, Engagement & Pattern Analytics Agent
  - Second tier: Psychological Monitoring Agent
  - Third tier: Strategy Agent
  - Fourth tier: Wheel/Activity Agent
  - Final validation: Ethical Oversight Agent
- Create specialized agent requests with appropriate context subsets

#### Output Data Structure
- **agent**: "Orchestrator Agent"
- **outputData**:
  - **resourceCapacityRequest**:
    - **contextSubset**: [Relevant environment and time information]
    - **specificQuery**: "Analyze available resources and time constraints for today's activities"
  - **engagementPatternRequest**:
    - **contextSubset**: [Relevant historical data references]
    - **specificQuery**: "Analyze historical engagement patterns and identify domain preferences"
  - **forwardTo**: "Resource & Capacity Agent" and "Engagement & Pattern Analytics Agent" (parallel)
- **updatedDataModels**: None (Coordination only)

### Resource & Capacity Management Agent - Resource Analysis

#### Input Data Structure
- **agent**: "Resource & Capacity Management Agent"
- **purpose**: "Analyze available resources, time constraints, and environmental conditions"
- **receivedFrom**: "Orchestrator Agent"
- **inputData**:
  - **contextPacket**: [Subset focused on resources and environment]
  - **specificQuery**: "Analyze available resources and time constraints for today's activities"
- **accessedDataModels**:
  - **UserEnvironment**: filtered by user_profile.id
  - **Inventory**: associated with current environment
  - **UserResource**: available resources for current environment
  - **HistoryLog**: recent resource utilization patterns

#### Processing Steps
- Analyze environment specifications:
  - Location: Rural farm near Lyon
  - Weather: Partly cloudy, suitable for outdoor activities
  - Time of day: Morning (optimal for both physical and creative tasks)
- Evaluate available resources against inventory:
  - Farm tools: Available and in working condition
  - Art supplies: Limited but sufficient for basic projects
  - Technology: Internet access available, computer functional
  - Transportation: Bicycle available, limited public transport
- Assess time constraints:
  - Available window: 4 hours (adequate for medium-complexity activities)
  - Time of day: Morning (aligned with higher energy levels)
  - Urgency: No imminent deadlines reported
- Identify any capacity limitations:
  - Recent refusal of physical activity suggests potential physical fatigue
  - No reported skill limitations for creative activities
- Create resource context document detailing all available tools, spaces, and time considerations

#### Output Data Structure
- **agent**: "Resource & Capacity Management Agent"
- **outputData**:
  - **resourceContext**:
    - **availableTimeWindow**: 240 minutes
    - **optimalActivityDuration**: 30-120 minutes (to allow completion satisfaction)
    - **environmentOptions**: ["outdoor_farm", "indoor_living_space", "nearby_nature"]
    - **weatherConstraints**: ["suitable_for_outdoor_activities", "no_weather_limitations"]
    - **availableResources**: [Detailed list of farm tools, art supplies, technology, etc.]
    - **resourceLimitations**: ["limited_art_supplies", "basic_farm_tools_only"]
    - **physicalCapacity**: "Moderate - caution with extended physical activities"
  - **confidenceLevel**: 85
  - **forwardTo**: "Orchestrator Agent"
- **updatedDataModels**: None (Analysis only)

### Engagement & Pattern Analytics Agent - Historical Analysis

#### Input Data Structure
- **agent**: "Engagement & Pattern Analytics Agent"
- **purpose**: "Analyze historical engagement patterns and identify domain preferences"
- **receivedFrom**: "Orchestrator Agent"
- **inputData**:
  - **contextPacket**: [Subset focused on historical data]
  - **specificQuery**: "Analyze historical engagement patterns and identify domain preferences"
- **accessedDataModels**:
  - **HistoryLog**: filtered by user_profile.id, activity completions and refusals
  - **UserFeedbackLog**: qualitative feedback on previous activities
  - **Preference**: existing preference records

#### Processing Steps
- Analyze activity completion patterns:
  - Creative activities: 80% completion rate (High engagement)
  - Physical farm activities: 40% completion rate (Lower engagement)
  - Reflective activities: 75% completion rate (Good engagement)
  - Social activities: 60% completion rate (Moderate engagement)
- Identify time-of-day patterns:
  - Morning activities: 70% completion rate (Preferred time)
  - Afternoon activities: 50% completion rate (Less optimal)
- Analyze feedback sentiments:
  - Positive sentiment toward activities with:
    - Creative expression components
    - Clear completion criteria
    - Visible results/outcomes
  - Negative sentiment toward activities with:
    - Extended physical exertion
    - Ambiguous endpoints
    - No tangible outcome
- Identify domain preference trends:
  - Increasing engagement with creative activities (upward trend)
  - Stable engagement with reflective activities
  - Decreasing engagement with pure physical labor
- Calculate completion probability estimates by domain and activity type

#### Output Data Structure
- **agent**: "Engagement & Pattern Analytics Agent"
- **outputData**:
  - **domainPreferences**:
    - **creative**: { preference: 85, trend: "increasing", completion_probability: 0.8 }
    - **physical**: { preference: 45, trend: "decreasing", completion_probability: 0.4 }
    - **reflective**: { preference: 75, trend: "stable", completion_probability: 0.75 }
    - **social**: { preference: 60, trend: "stable", completion_probability: 0.6 }
  - **timePatterns**:
    - **optimal_time_windows**: ["morning", "early_evening"]
    - **sub_optimal_windows**: ["afternoon", "late_night"]
  - **engagementFactors**:
    - **positive_correlations**: ["creative_elements", "clear_completion_criteria", "visible_results"]
    - **negative_correlations**: ["extended_physical_exertion", "ambiguous_endpoints"]
  - **confidenceLevel**: 80
  - **forwardTo**: "Orchestrator Agent"
- **updatedDataModels**: None (Analysis only)

### Orchestrator Agent - Intermediate Coordination

#### Input Data Structure
- **agent**: "Orchestrator Agent"
- **purpose**: "Integrate first-tier agent outputs and forward to Psychological Monitoring Agent"
- **receivedFrom**: "Resource & Capacity Agent" and "Engagement & Pattern Analytics Agent"
- **inputData**:
  - **resourceContext**: [Complete output from Resource & Capacity Agent]
  - **engagementAnalysis**: [Complete output from Engagement & Pattern Analytics Agent]
- **accessedDataModels**: None (Integration and forwarding only)

#### Processing Steps
- Integrate resource and engagement data into a unified context
- Verify data consistency between the two analyses
- Prepare comprehensive packet for psychological assessment
- Forward integrated data to Psychological Monitoring Agent

#### Output Data Structure
- **agent**: "Orchestrator Agent"
- **outputData**:
  - **integratedContext**:
    - **resourceContext**: [Complete resource analysis]
    - **engagementAnalysis**: [Complete engagement analysis]
    - **originalUserContext**: [Original context from Mentor Agent]
  - **specificQuery**: "Assess current psychological state and readiness for challenge"
  - **forwardTo**: "Psychological Monitoring Agent"
- **updatedDataModels**: None (Integration only)

### Psychological Monitoring Agent - Psychological Assessment

#### Input Data Structure
- **agent**: "Psychological Monitoring Agent"
- **purpose**: "Assess current psychological state and readiness for challenge"
- **receivedFrom**: "Orchestrator Agent"
- **inputData**:
  - **integratedContext**: [Combined resource and engagement data]
  - **specificQuery**: "Assess current psychological state and readiness for challenge"
- **accessedDataModels**:
  - **UserTraitInclination**: current personality trait values
  - **Belief**: current belief system records
  - **TrustLevel**: current trust metrics
  - **UserGoal**: goals and aspirations
  - **Inspiration**: motivational factors

#### Processing Steps
- Update dynamic psychological variables:
  - Current mood: "Motivated but anxious" (from user report)
  - Energy level: "Moderate to high" (morning, reported as motivated)
- Evaluate trust metrics and phase determination:
  - Trust level: 68 (Moderate-high)
  - Phase assessment: Early Expansion Phase
    - Justification: Successfully completed multiple activities
    - Trust is sufficient to introduce moderate challenges
- Analyze trait expression and challenge readiness:
  - Openness (78): High readiness for novel experiences
  - Conscientiousness (42): Moderate risk of non-completion
  - Extraversion (56): Moderate comfort with social elements
  - Agreeableness (64): Good candidacy for cooperative activities
  - Neuroticism (61): Potential anxiety with ambiguous outcomes
- Connect with belief system:
  - Key belief: "I abandon projects" conflicts with goal of completion
  - Key belief: "I need external structure" aligns with wheel mechanism
- Evaluate goal alignment opportunities:
  - Primary goal: "Break cycle of abandoned projects"
    - Suggests activities with clear completion criteria
    - Benefits from activities that build incremental success
  - Secondary goal: "Find meaningful direction"
    - Suggests exploration across varied domains
    - Benefits from activities with reflection components
- Assess inspiration leverage points:
  - Key inspiration: "Create something tangible"
    - Suggests physical creation activities
    - Connects to visible results engagement factor
- Determine psychological readiness profile and challenge calibration:
  - Overall readiness: Moderate-High
  - Challenge tolerance: Medium (limited by anxiety and conscientiousness)
  - Novelty tolerance: High (supported by openness)
  - Growth areas: Conscientiousness, commitment follow-through

#### Output Data Structure
- **agent**: "Psychological Monitoring Agent"
- **outputData**:
  - **psychologicalState**:
    - **currentMood**: "Motivated but anxious about commitment"
    - **energyLevel**: "Moderate to high (morning)"
    - **anxietyTriggers**: ["ambiguous endpoints", "extended commitment", "perceived failure"]
    - **motivators**: ["visible results", "creative expression", "clear completion criteria"]
  - **trustAssessment**:
    - **trustLevel**: 68
    - **trustPhase**: "Early Expansion"
    - **challengeReadiness**: "Moderate - can introduce novelty with support"
  - **traitAnalysis**:
    - [Detailed analysis of all five traits and their implications]
  - **recommendedGrowthAreas**:
    - **primary**: {
      trait: "Conscientiousness",
      current_level: 42,
      target_range: "45-50",
      approach: "Activities with clear steps and visible progress"
    }
    - **secondary**: {
      trait: "Neuroticism",
      current_level: 61,
      target_range: "55-60",
      approach: "Activities with manageable uncertainty and positive reinforcement"
    }
  - **confidenceLevel**: 85
  - **forwardTo**: "Orchestrator Agent"
- **updatedDataModels**:
  - **CurrentMood**: updated based on user report (direct update)

### Orchestrator Agent - Second Intermediate Coordination

#### Input Data Structure
- **agent**: "Orchestrator Agent"
- **purpose**: "Integrate psychological assessment with previous analyses"
- **receivedFrom**: "Psychological Monitoring Agent"
- **inputData**:
  - **psychologicalAssessment**: [Complete output from Psychological Monitoring Agent]
- **accessedDataModels**: None (Integration and forwarding only)

#### Processing Steps
- Integrate psychological assessment with resource and engagement data
- Verify alignment and resolve any inconsistencies
- Prepare comprehensive data package for strategy development
- Forward integrated data to Strategy Agent

#### Output Data Structure
- **agent**: "Orchestrator Agent"
- **outputData**:
  - **completeContext**:
    - **resourceContext**: [Resource analysis]
    - **engagementAnalysis**: [Engagement analysis]
    - **psychologicalAssessment**: [Psychological assessment]
    - **originalUserContext**: [Original context]
  - **specificQuery**: "Develop challenge and domain strategy framework"
  - **forwardTo**: "Strategy Agent"
- **updatedDataModels**: None (Integration only)

### Strategy Agent - Challenge Framework Development

#### Input Data Structure
- **agent**: "Strategy Agent"
- **purpose**: "Formulate strategy for challenge calibration and domain distribution"
- **receivedFrom**: "Orchestrator Agent"
- **inputData**:
  - **completeContext**: [Integrated data from all previous analyses]
  - **specificQuery**: "Develop challenge and domain strategy framework"
- **accessedDataModels**:
  - **UserGoal**: to verify alignment with strategy

#### Processing Steps
- Synthesize insights from resource, engagement, and psychological analyses
- Conduct gap analysis between current traits and activity requirements:
  - Identify trait development opportunities (Conscientiousness: 42 → 45-50)
  - Calculate ideal "stretch zone" based on trust level (68 = moderate challenge appropriate)
- Formulate domain distribution strategy:
  - Allocate higher probability to domains with proven engagement (creative)
  - Include moderate probability for growth areas (moderately challenging physical)
  - Ensure diversity across domains (include social and reflective elements)
- Develop challenge calibration framework:
  - Base challenge level: Moderate (aligned with Early Expansion Phase)
  - Domain-specific adjustments:
    - Creative: Moderate challenge (high engagement allows for stretching)
    - Physical: Lower-moderate challenge (engagement declining, needs reinforcement)
    - Reflective: Moderate challenge (stable engagement supports growth)
    - Social: Moderate challenge (stable engagement supports growth)
- Define constraint and limitation guidelines:
  - Maximum activity duration: 90-120 minutes (based on 4-hour window)
  - Required resources: Must be available in current inventory
  - Physical intensity: Moderate maximum (based on recent refusal)
- Create clear rationales for strategic decisions that can be communicated to user

#### Output Data Structure
- **agent**: "Strategy Agent"
- **outputData**:
  - **domainDistribution**:
    - **creative**: 40% (primary focus based on engagement and preference)
    - **physical**: 20% (included for balance and growth)
    - **reflective**: 25% (supports goal awareness and direction-finding)
    - **social**: 15% (smaller component to maintain diversity)
  - **challengeCalibration**:
    - **overallLevel**: "Moderate" (trust score: 68)
    - **domainSpecific**:
      - **creative**: { base_level: "Moderate", adjustment: +5, final: "Moderate-High" }
      - **physical**: { base_level: "Moderate", adjustment: -10, final: "Low-Moderate" }
      - **reflective**: { base_level: "Moderate", adjustment: 0, final: "Moderate" }
      - **social**: { base_level: "Moderate", adjustment: 0, final: "Moderate" }
  - **constraintGuidelines**:
    - **durationRange**: "30-90 minutes" (optimized for completion)
    - **resourceRequirements**: "Must use available inventory only"
    - **physicalIntensity**: "Low to moderate only"
    - **complexityLevel**: "Clear steps with defined outcome"
  - **goalAlignmentStrategy**:
    - **primary**: {
      goal: "Break cycle of abandoned projects",
      strategy: "Activities with clear completion criteria and incremental success"
    }
    - **secondary**: {
      goal: "Find meaningful direction",
      strategy: "Diverse activities with reflection components"
    }
  - **confidenceLevel**: 80
  - **forwardTo**: "Orchestrator Agent"
- **updatedDataModels**: None (Strategy development only)

### Orchestrator Agent - Third Intermediate Coordination

#### Input Data Structure
- **agent**: "Orchestrator Agent"
- **purpose**: "Forward strategy framework to Wheel/Activity Agent"
- **receivedFrom**: "Strategy Agent"
- **inputData**:
  - **strategyFramework**: [Complete output from Strategy Agent]
- **accessedDataModels**: None (Forwarding only)

#### Processing Steps
- Validate strategy framework against user context
- Prepare complete package for activity selection
- Forward to Wheel/Activity Agent

#### Output Data Structure
- **agent**: "Orchestrator Agent"
- **outputData**:
  - **completePackage**:
    - **strategyFramework**: [Strategy framework]
    - **resourceContext**: [Resource context]
    - **psychologicalAssessment**: [Psychological assessment]
    - **originalUserContext**: [Original context]
  - **specificQuery**: "Select and customize activities for wheel generation"
  - **forwardTo**: "Wheel/Activity Agent"
- **updatedDataModels**: None (Forwarding only)

### Wheel/Activity Agent - Activity Selection and Wheel Construction

#### Input Data Structure
- **agent**: "Wheel/Activity Agent"
- **purpose**: "Select and customize activities, construct weighted wheel"
- **receivedFrom**: "Orchestrator Agent"
- **inputData**:
  - **completePackage**: [Integrated data package]
  - **specificQuery**: "Select and customize activities for wheel generation"
- **accessedDataModels**:
  - **GenericActivity**: to select base activities
  - **UserEnvironment**: to verify environmental compatibility
  - **Inventory**: to check resource availability
  - **UserTraitInclination**: to match activities to user traits

#### Processing Steps
- Search GenericActivity database for appropriate activities:
  - Filter by domain distribution parameters
  - Filter by duration constraints (30-90 minutes)
  - Filter by resource availability
  - Filter by challenge level appropriateness
- Select candidate activities for each domain:
  - Creative domain (40%):
    - "Create a small sketch journal of the farm environment"
    - "Build a simple birdhouse from scrap wood"
    - "Design a logo for the farm"
    - "Create a photo essay of interesting farm details"
  - Physical domain (20%):
    - "Clear and organize one small section of the garden"
    - "Take a 30-minute bike ride to the nearby village"
    - "Learn 3 basic stretching exercises and practice them"
  - Reflective domain (25%):
    - "Write a letter to yourself about what you'd like to accomplish this month"
    - "Create a mind map of potential projects and their first steps"
    - "Record a voice note about your ideal day on the farm"
  - Social domain (15%):
    - "Call a friend from Frankfurt and share one positive development"
    - "Write an email to a family member asking them about their current projects"
- Customize each selected activity:
  - Adjust instructions for current environment
  - Specify appropriate duration
  - Tailor difficulty to challenge calibration
  - Add personalized elements based on user context
- Create ActivityTailored objects for each customized activity:
  - Set name, description, instructions, requirements
  - Add challenge ratings and domain classifications
  - Connect to user goals and growth areas
- Create value propositions for each activity:
  - Clear explanation of selection rationale
  - Connection to user goals
  - Anticipated benefits and outcomes
- Assign probability weights based on domain distribution strategy
- Construct Wheel object with weighted WheelItems

#### Output Data Structure
- **agent**: "Wheel/Activity Agent"
- **outputData**:
  - **wheelObject**:
    - **id**: [Generated UUID]
    - **name**: "Philipp's Morning Wheel - March 12"
    - **created_by**: "Wheel/Activity Agent"
    - **created_at**: "2025-03-12"
  - **wheelItems**: [
    {
      **id**: [Generated UUID],
      **wheel**: [Reference to wheelObject],
      **percentage**: 15,
      **activity_tailored**: {
        **id**: [Generated UUID],
        **user_profile**: [Reference to Philipp's profile],
        **generic_activity**: [Reference to sketch journal activity],
        **name**: "Create a Nature Sketch Journal of Farm Life",
        **description**: "Explore the farm environment through drawing, creating 4-5 simple sketches of interesting details you notice",
        **instructions**: "Grab your sketchbook and pencils, and find 4-5 interesting objects or scenes around the farm. Spend about 10-15 minutes on each sketch. Focus on capturing the essence rather than perfect details. When finished, add a short caption to each sketch describing why you chose it.",
        **duration_range**: "50-75 minutes",
        **base_challenge_rating**: 65,
        **domain**: { "primary": "creative", "secondary": "reflective" },
        **challengingness**: {
          "openness": 70,
          "conscientiousness": 50,
          "extraversion": 30,
          "agreeableness": 40,
          "neuroticism": 45
        }
      }
    },
    {
      **id**: [Generated UUID],
      **wheel**: [Reference to wheelObject],
      **percentage**: 10,
      **activity_tailored**: {
        **id**: [Generated UUID],
        **user_profile**: [Reference to Philipp's profile],
        **generic_activity**: [Reference to birdhouse activity],
        **name**: "Build a Simple Birdhouse from Farm Scraps",
        **description**: "Create a functional birdhouse using scrap wood and basic tools available on the farm",
        **instructions**: "Gather scrap wood pieces from behind the shed. Use the hand saw and hammer to create a simple birdhouse following the diagram provided. Focus on functionality rather than perfect aesthetics. When complete, find a suitable tree to hang it from.",
        **duration_range**: "60-90 minutes",
        **base_challenge_rating**: 70,
        **domain**: { "primary": "creative", "secondary": "physical" },
        **challengingness**: {
          "openness": 65,
          "conscientiousness": 75,
          "extraversion": 30,
          "agreeableness": 40,
          "neuroticism": 60
        }
      }
    },
    // Additional wheel items for all selected activities with appropriate weights
  ]
  - **activityValuePropositions**: [
    {
      **activity_id**: [Reference to sketch journal activity],
      **selection_rationale**: "Selected based on your high engagement with creative activities and interest in exploring your surroundings",
      **goal_connection**: "This activity has clear completion criteria (4-5 sketches) to support your goal of completing projects",
      **growth_area**: "Helps develop conscientiousness through a structured creative process"
    },
    // Value propositions for all activities
  ]
  - **confidenceLevel**: 85
  - **forwardTo**: "Ethical Oversight Agent"
- **updatedDataModels**:
  - **ActivityTailored**: Created new objects for all selected activities
  - **Wheel**: Created new wheel object
  - **WheelItem**: Created new wheel item objects

### Ethical Oversight Agent - Ethical Validation

#### Input Data Structure
- **agent**: "Ethical Oversight Agent"
- **purpose**: "Validate wheel and activities for ethical alignment"
- **receivedFrom**: "Wheel/Activity Agent"
- **inputData**:
  - **wheelObject**: [Complete wheel data structure]
  - **wheelItems**: [All wheel item objects with activities]
  - **activityValuePropositions**: [Value propositions for all activities]
- **accessedDataModels**:
  - **UserFeedbackLog**: to check for previously reported concerns
  - **HistoryLog**: to verify activity patterns

#### Processing Steps
- Review each ActivityTailored object for ethical alignment:
  - Verify benevolence (promotes well-being)
  - Check fairness (appropriate challenge level)
  - Ensure transparency (clear purpose and instructions)
- Evaluate psychological safety for each activity:
  - Assess anxiety triggers against user profile
  - Check for potential emotional harm
  - Verify appropriate challenge calibration
- Review wheel composition as a whole:
  - Verify domain balance
  - Check variety of challenge levels
  - Ensure no unconscious bias in selections
- Apply binary approval system to each activity:
  - Approved: Meets all ethical guidelines
  - Not Approved: Poses potential risks or concerns
- For any non-approved activities, document specific concerns

#### Output Data Structure
- **agent**: "Ethical Oversight Agent"
- **outputData**:
  - **ethicalValidation**: {
    **overallApproval**: true,
    **activityAssessments**: [
      {
        **activity_id**: [Reference to sketch journal activity],
        **status**: "Approved",
        **notes**: "Appropriate challenge level, clear instructions, promotes well-being"
      },
      {
        **activity_id**: [Reference to birdhouse activity],
        **status**: "Approved",
        **notes**: "Tool safety instructions added, otherwise appropriate"
      },
      // Assessments for all activities
    ]
  }
  - **wheelBalanceAssessment**: {
    **domain_balance**: "Appropriate distribution across domains",
    **challenge_calibration**: "Well-aligned with user's trust phase and capabilities",
    **variety**: "Sufficient variety to support exploration while maintaining focus"
  }
  - **confidenceLevel**: 90
  - **forwardTo**: "Orchestrator Agent"
- **updatedDataModels**: None (Validation only)

### Orchestrator Agent - Final Integration

#### Input Data Structure
- **agent**: "Orchestrator Agent"
- **purpose**: "Integrate ethical validation and prepare final wheel for presentation"
- **receivedFrom**: "Ethical Oversight Agent"
- **inputData**:
  - **ethicalValidation**: [Complete ethical validation]
- **accessedDataModels**: None (Integration only)

#### Processing Steps
- Verify all activities received ethical approval
- Prepare final wheel package for presentation
- Forward complete package to Mentor Agent

#### Output Data Structure
- **agent**: "Orchestrator Agent"
- **outputData**:
  - **finalWheel**: [Complete wheel object with items]
  - **activityValuePropositions**: [Value propositions]
  - **presentationContext**: {
    **userMood**: "Motivated but anxious",
    **trustPhase**: "Early Expansion",
    **focusAreas**: ["Completion achievement", "Creative expression", "Direction finding"]
  }
  - **forwardTo**: "Mentor Agent"
- **updatedDataModels**: None (Integration only)

###


### Mentor Agent - Final Wheel Presentation

#### Input Data Structure
- **agent**: "Mentor Agent"
- **purpose**: "Present finalized wheel to user with clear explanations and engage commitment"
- **receivedFrom**: "Orchestrator Agent"
- **inputData**:
  - **finalWheel**: [Complete wheel object with items]
  - **activityValuePropositions**: [Value propositions for all activities]
  - **presentationContext**: [User mood, trust phase, focus areas]
- **accessedDataModels**: None (Presentation only)

#### Processing Steps
- Prepare engaging introduction to wheel presentation:
  - Acknowledge user's current mood and context
  - Frame wheel as opportunity for both creativity and productivity
  - Emphasize connection to breaking the cycle of abandoned projects
- Present overview of the wheel composition:
  - Explain domain distribution rationale
  - Highlight variety of activities
  - Connect to user's stated goals and interests
- Provide preview of potential activities with transparent probability weighting:
  - Present each activity with brief description
  - Explain why each activity was selected
  - Note the relative probability of each (via wheel segment size)
- Emphasize the transformative value of commitment:
  - Explain the importance of committing before spinning
  - Highlight how commitment transforms anxiety into anticipation
  - Reinforce the philosophy of controlled randomness
- Request explicit commitment to the randomized outcome:
  - "Are you willing to commit to whichever activity the wheel selects?"
  - Capture clear affirmative response before enabling spin
- Present the wheel interface for user interaction

#### Output Data Structure
- **agent**: "Mentor Agent"
- **outputData**:
  - **presentationDialogue**: "Good morning Philipp! Based on our conversation, I've created a wheel with activities that balance creativity and productivity - perfect for your available time today. I've included more creative options since you've been enjoying those, but also some manageable farm tasks and reflective activities that might help with finding direction.

    Before you spin, take a look at the possible activities. Each segment's size shows its likelihood of being selected. I've included activities like creating a farm sketch journal, building a simple birdhouse, organizing a garden section, and writing a reflection letter - all activities you can complete within your available time.
    
    Remember, the magic of the wheel is in committing to whichever activity it selects. This transforms the anxiety of choosing into the anticipation of discovery. Are you ready to commit to whichever activity the wheel selects?"
  - **userInterface**: {
    **wheelDisplay**: true,
    **activityPreviews**: [Array of activity summaries with probabilities],
    **commitButton**: "I commit to whatever the wheel selects",
    **spinEnabled**: false until commitment
  }
- **forwardTo**: User interface for interaction
- **updatedDataModels**:
  - **HistoryLog**: Record of wheel presentation and activity options

## 4. Data Flow Visualization

1. **Initial Activation**:
   - User opens app → Mentor Agent activates
   - Mentor Agent gathers context → Orchestrator Agent

2. **First-Tier Analysis (Parallel)**:
   - Orchestrator Agent → Resource & Capacity Agent
   - Orchestrator Agent → Engagement & Pattern Analytics Agent
   - Both agents return analyses → Orchestrator Agent

3. **Second-Tier Analysis**:
   - Orchestrator Agent (with combined analyses) → Psychological Monitoring Agent
   - Psychological Monitoring Agent returns assessment → Orchestrator Agent

4. **Strategy Development**:
   - Orchestrator Agent (with psychological assessment) → Strategy Agent
   - Strategy Agent returns framework → Orchestrator Agent

5. **Activity Selection**:
   - Orchestrator Agent (with strategy framework) → Wheel/Activity Agent
   - Wheel/Activity Agent returns wheel and activities → Ethical Oversight Agent

6. **Ethical Validation**:
   - Ethical Oversight Agent returns validation → Orchestrator Agent

7. **Final Presentation**:
   - Orchestrator Agent (with validated wheel) → Mentor Agent
   - Mentor Agent presents to user

## 5. Final System State

### Updated Data Model Values
- **Wheel**: New wheel object created for Philipp's session
  - **id**: [Generated UUID]
  - **name**: "Philipp's Morning Wheel - March 12"
  - **created_at**: "2025-03-12"

- **WheelItems**: Multiple new wheel item objects created
  - Each linked to respective ActivityTailored objects
  - Each assigned appropriate probability weight

- **ActivityTailored**: Multiple new tailored activity objects created
  - Each customized to Philipp's current context
  - Each with appropriate challenge ratings and instructions

- **HistoryLog**: New entries documenting wheel generation process
  - Initial context gathering
  - Wheel presentation

- **CurrentMood**: Updated based on user report
  - "Motivated but anxious about commitment"

### Ephemeral vs. Persistent Changes
- **Persistent Changes**:
  - Wheel, WheelItems, and ActivityTailored objects (stored in database)
  - HistoryLog entries (permanent record)
  - CurrentMood update (relatively short-term)

- **Ephemeral/Session Values**:
  - Context packet (temporary for this workflow)
  - Agent analyses and recommendations (not directly persisted)
  - Strategy framework (used for wheel construction but not stored)

### State Preparation for Next Workflow
- Wheel is ready for user to commit and spin (transition to Post-Spin workflow)
- Complete activity options are available for reference during Post-Spin
- Context has been established for interpreting user's response to wheel
- HistoryLog entries provide continuity for future sessions
- Trust metrics are prepared for potential updates based on user commitment

This wheel generation workflow has successfully transformed Philipp's morning context (motivated but anxious, available time, mixed desires for creativity and productivity) into a personalized activity wheel. The system has balanced his preferences for creative activities with his need for structure and completion, providing appropriate challenge calibration aligned with his trust level and psychological state. The wheel is now ready for Philipp to commit and spin, transitioning to the Post-Spin workflow where his commitment will be processed and the selected activity prepared for execution.