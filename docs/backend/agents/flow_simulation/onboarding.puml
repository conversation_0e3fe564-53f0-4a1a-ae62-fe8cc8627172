@startuml gr
skinparam sequenceMessageAlign center
skinparam responseMessageBelowArrow true
skinparam maxMessageSize 300
skinparam WrapWidth 300
skinparam style strictuml
skinparam dpi 300

title Game of Life Onboarding Flow: Emma's Journey

actor "Emma\n(User)" as User
participant "Onboarding\nInterface" as Interface
participant "Orchestrator\nAgent" as Orchestrator
participant "Resource &\nCapacity Agent" as ResourceAgent
participant "Engagement &\nPattern Agent" as EngagementAgent 
participant "Psychological\nMonitoring Agent" as PsychAgent
participant "Strategy\nAgent" as StrategyAgent
participant "Ethical Oversight\nAgent" as EthicalAgent
database "Database" as DB

== Initial Questionnaire ==

User -> Interface: Completes onboarding questionnaire
note right
  Demographics: Emma, 32, Marketing Professional
  Living Situation: Small urban apartment
  Schedule: Busy weekdays, freer weekends
  Interests: Creative writing, photography, mindfulness
  Challenges: Project completion, consistency
end note

Interface -> Orchestrator: Submit completed questionnaire responses
activate Orchestrator

== Step 1: Orchestrator Agent - Initial Workflow Setup ==

Orchestrator -> DB: Create UserProfile record
note right
  profile_name: "<PERSON>"
  id: "550e8400-e29b-41d4-a716-************"
end note

Orchestrator -> DB: Create HistoryEvent (workflow_started)
note right
  event_type: "onboarding_workflow_started"
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  details: {
    "workflow_id": "ONB-2025-03-22-001",
    "status": "initiated"
  }
end note

Orchestrator -> Orchestrator: Organize questionnaire data into packages
note right
  Environment/Demographics Package
  Preference/Behavior Package
  Psychological Assessment Package
end note

Orchestrator -> ResourceAgent: Send environmental data package
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  living_situation: "Small urban apartment"
  resource_mentions: ["laptop", "smartphone", "small desk"]
  time_constraints: {
    "weekdays": "limited evenings only",
    "weekends": "mornings and afternoons"
  }
end note

deactivate Orchestrator
activate ResourceAgent

== Step 2: Resource & Capacity Agent - Environmental Context Establishment ==

ResourceAgent -> DB: Query GenericEnvironment templates
ResourceAgent -> DB: Create UserEnvironment record
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  environment_name: "Emma's Apartment"
  generic_environment: "urban_apartment_small"
  is_current: true
end note

ResourceAgent -> DB: Populate UserEnvironmentPhysicalProperties
note right
  user_environment: "Emma's Apartment"
  space_size: "small"
  noise_level: 40
  privacy_level: 85
  digital_connectivity: 95
end note

ResourceAgent -> DB: Create UserEnvironmentSocialContext
note right
  user_environment: "Emma's Apartment"
  typical_occupancy: 15
  privacy_level: 90
  social_interaction_level: 20
end note

ResourceAgent -> DB: Create UserEnvironmentActivitySupport
note right
  user_environment: "Emma's Apartment"
  digital_connectivity: 95
  resource_availability: 60
  time_availability: {
    "weekdays": ["evening"],
    "weekends": ["morning", "afternoon"]
  }
end note

ResourceAgent -> DB: Create Demographics record
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  full_name: "Emma Johnson"
  age: 32
  occupation: "Marketing Professional"
  location: "Urban City"
end note

ResourceAgent -> DB: Create UserResource records
note left
  For each identified resource:
  - laptop (digital device)
  - smartphone (mobile device)
  - small desk (work surface)
end note

ResourceAgent -> DB: Create Skill records
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  skills: [
    {
      "generic_skill": "digital_marketing",
      "level": 85,
      "user_enjoyment": 70
    },
    {
      "generic_skill": "creative_writing",
      "level": 60,
      "user_enjoyment": 90
    },
    {
      "generic_skill": "photography_basic",
      "level": 55,
      "user_enjoyment": 80
    }
  ]
end note

ResourceAgent -> DB: Create UserLimitation records
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  limitation: {
    "generic_limitation": "space_constraint_indoor",
    "severity": 70,
    "is_unlimited": true
  }
end note

ResourceAgent -> Orchestrator: Resource context report
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  environment_id: "ENV-550e8401"
  resources_available: ["laptop", "smartphone", "small desk"]
  limitations: ["space_constraint_indoor"]
  skills_identified: ["digital_marketing", "creative_writing", "photography_basic"]
  time_availability: {
    "weekdays": ["evening"],
    "weekends": ["morning", "afternoon"]
  }
  confidence_score: 0.85
end note

deactivate ResourceAgent
activate Orchestrator

== Step 3: Orchestrator Agent - First Transition ==

Orchestrator -> DB: Update HistoryEvent (resource context complete)
note right
  event_type: "onboarding_resource_context_complete"
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  details: {
    "workflow_id": "ONB-2025-03-22-001",
    "completed_step": "resource_context"
  }
end note

Orchestrator -> EngagementAgent: Send engagement data package
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  questionnaire_responses: {
    "activity_preferences": ["creative writing", "photography", "mindfulness"],
    "completion_tendencies": "struggles with following through",
    "schedule_preferences": "prefers morning activities on weekends",
    "engagement_history": "abandoned several projects"
  }
  resource_context: {/* Resource context from previous step */}
end note

deactivate Orchestrator
activate EngagementAgent

== Step 4: Engagement & Pattern Analytics Agent - Preference and Pattern Analysis ==

EngagementAgent -> DB: Create Preference records
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  preferences: [
    {
      "pref_name": "Morning Weekend Activities",
      "pref_strength": 80,
      "pref_description": "Prefers engaging in activities during weekend mornings"
    },
    {
      "pref_name": "Creative Domain Activities",
      "pref_strength": 85,
      "pref_description": "Shows strong preference for creative activities"
    },
    {
      "pref_name": "Short Duration Activities",
      "pref_strength": 75,
      "pref_description": "Preference for activities that can be completed in one session"
    }
  ]
end note

EngagementAgent -> EngagementAgent: Analyze temporal patterns
note right
  Identified:
  - Strong morning energy on weekends
  - Declining energy late evenings
  - Project abandonment typically occurs after 2-3 weeks
end note

EngagementAgent -> EngagementAgent: Analyze domain preferences
note right
  Domain Preferences:
  - Creative: High (85)
  - Mindfulness: Medium-High (70)
  - Physical: Low-Medium (40)
  - Social: Medium (50)
end note

EngagementAgent -> EngagementAgent: Analyze engagement patterns
note right
  Engagement Patterns:
  - Strong initial motivation
  - Enthusiasm decline after novelty wears off
  - Struggles with regular practice
  - Responds well to visible progress markers
end note

EngagementAgent -> Orchestrator: Engagement pattern report
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  domain_preferences: {
    "creative": 85,
    "mindfulness": 70,
    "physical": 40,
    "social": 50
  },
  temporal_patterns: {
    "high_energy_periods": ["weekend_mornings"],
    "low_energy_periods": ["weekday_evenings"]
  },
  engagement_pattern: {
    "initial_motivation": "high",
    "consistency_challenge": "significant",
    "abandonment_trigger": "when_novelty_decreases",
    "completion_predictors": ["visible_progress", "short_duration"]
  },
  trust_indicators: {
    "current_phase": "foundation",
    "resistance_areas": ["long_term_commitment", "routine_activities"]
  },
  confidence_score: 0.80
end note

deactivate EngagementAgent
activate Orchestrator

== Step 5: Orchestrator Agent - Second Transition ==

Orchestrator -> DB: Update HistoryEvent (engagement patterns complete)
note right
  event_type: "onboarding_engagement_patterns_complete"
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  details: {
    "workflow_id": "ONB-2025-03-22-001",
    "completed_step": "engagement_patterns"
  }
end note

Orchestrator -> PsychAgent: Send psychological data package
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  questionnaire_responses: {
    "personality_indicators": [...],
    "belief_statements": [
      "I'm creative but struggle with discipline",
      "I need external validation to stay motivated",
      "I can't stick with routine activities for long"
    ],
    "goal_statements": [
      "I want to build better habits and consistency",
      "I want to complete creative projects I start",
      "I'd like to explore mindfulness practice"
    ]
  },
  resource_context: {...},
  engagement_patterns: {...}
end note

deactivate Orchestrator
activate PsychAgent

== Step 6: Psychological Monitoring Agent - Comprehensive Psychological Assessment ==

PsychAgent -> DB: Query GenericTrait data
PsychAgent -> DB: Create UserTraitInclination records
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  trait_inclinations: [
    {
      "generic_trait": "openness_creativity",
      "strength": 85,
      "awareness": 90
    },
    {
      "generic_trait": "conscientiousness_discipline",
      "strength": 40,
      "awareness": 85
    },
    {
      "generic_trait": "extraversion_sociability",
      "strength": 60,
      "awareness": 75
    },
    {
      "generic_trait": "agreeableness_cooperation",
      "strength": 75,
      "awareness": 70
    },
    {
      "generic_trait": "emotionality_sensitivity",
      "strength": 70,
      "awareness": 65
    },
    {
      "generic_trait": "honesty_humility_authenticity",
      "strength": 80,
      "awareness": 70
    }
  ]
end note

PsychAgent -> DB: Create Belief records
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  beliefs: [
    {
      "content": "I need external validation to stay motivated",
      "generic_belief": "SELF_EFFICACY",
      "stability": 70,
      "emotionality": -40,
      "user_confidence": 75
    },
    {
      "content": "I can't stick with routine activities for long",
      "generic_belief": "SKILL_LEARN",
      "stability": 80,
      "emotionality": -50,
      "user_confidence": 85
    },
    {
      "content": "I'm creative but lack discipline",
      "generic_belief": "TALENT_INNATE",
      "stability": 75,
      "emotionality": -30,
      "user_confidence": 80
    }
  ]
end note

PsychAgent -> DB: Create UserGoal records (Intention subclass)
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  intentions: [
    {
      "title": "Complete a short story within two weeks",
      "description": "Write a complete short story (1500-3000 words) with beginning, middle, and end",
      "importance_according_user": 80,
      "importance_according_system": 75,
      "strength": 70,
      "start_date": "2025-03-22",
      "due_date": "2025-04-05"
    }
  ]
end note

PsychAgent -> DB: Create UserGoal records (Aspiration subclass)
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  aspirations: [
    {
      "title": "Develop consistent creative practice",
      "description": "Build a sustainable creative routine that leads to completed projects",
      "importance_according_user": 90,
      "importance_according_system": 85,
      "strength": 75,
      "domain": "creative",
      "horizon": "long-term"
    },
    {
      "title": "Establish mindfulness practice",
      "description": "Develop a regular mindfulness practice to reduce stress and improve focus",
      "importance_according_user": 75,
      "importance_according_system": 80,
      "strength": 60,
      "domain": "mindfulness",
      "horizon": "medium-term"
    }
  ]
end note

PsychAgent -> DB: Create TrustLevel record
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  trust_level: {
    "value": 65,
    "notes": "Initial trust baseline. User shows openness to growth but caution around consistency challenges. Currently in Foundation phase."
  }
end note

PsychAgent -> DB: Create CurrentMood record
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  mood: {
    "description": "Motivated but slightly anxious about starting something new",
    "height": 65,
    "user_awareness": 70
  }
end note

PsychAgent -> Orchestrator: Psychological profile report
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  trait_assessment: {
    "dominant_traits": ["openness_creativity", "honesty_humility_authenticity"],
    "development_areas": ["conscientiousness_discipline"]
  },
  belief_system: {
    "limiting_beliefs": [
      "External validation dependency",
      "Perceived inability to maintain routines"
    ],
    "supportive_beliefs": [
      "Creative self-identity"
    ]
  },
  goals_analysis: {
    "intentions": 1,
    "aspirations": 2,
    "primary_focus": "creative consistency"
  },
  trust_assessment: {
    "current_phase": "foundation",
    "trust_score": 65,
    "recommendation": "focus on building completion confidence"
  },
  mood_baseline: "motivated but slightly anxious",
  confidence_score: 0.85
end note

deactivate PsychAgent
activate Orchestrator

== Step 7: Orchestrator Agent - Third Transition ==

Orchestrator -> DB: Update HistoryEvent (psychological profile complete)
note right
  event_type: "onboarding_psychological_profile_complete"
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  details: {
    "workflow_id": "ONB-2025-03-22-001",
    "completed_step": "psychological_profile"
  }
end note

Orchestrator -> StrategyAgent: Send comprehensive data package
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  resource_context: {...},
  engagement_patterns: {...},
  psychological_profile: {...}
end note

deactivate Orchestrator
activate StrategyAgent

== Step 8: Strategy Agent - Strategy Formation ==

StrategyAgent -> StrategyAgent: Analyze goal-trait-skill mappings
note right
  Goal "Complete a short story":
  - Supported by: openness_creativity (85)
  - Hindered by: conscientiousness_discipline (40)
  - Related skills: creative_writing (60)
  
  Goal "Develop consistent creative practice":
  - Supported by: openness_creativity (85)
  - Hindered by: conscientiousness_discipline (40)
  - Related skills: creative_writing (60)
  
  Goal "Establish mindfulness practice":
  - Supported by: emotionality_sensitivity (70)
  - Hindered by: conscientiousness_discipline (40)
  - No directly related skills identified
end note

StrategyAgent -> StrategyAgent: Identify development opportunities
note right
  Primary development opportunities:
  1. Conscientiousness gap affecting multiple goals
  2. Skill development in mindfulness practices
  3. Project completion confidence building
  
  Current trust phase: Foundation
  
  Approach: Build incremental successes in creative domain
  before expanding to new mindfulness domain
end note

StrategyAgent -> StrategyAgent: Create challenge calibration strategy
note right
  Challenge calibration:
  - Current trust score: 65 (Foundation phase)
  - Creative domain: Start with 30-40 challenge level
  - Mindfulness domain: Start with 20-30 challenge level
  - Physical domain: Keep minimal (10-20 level)
  
  Weekly increase rate: maximum 5-10 points
  based on successful completions
end note

StrategyAgent -> StrategyAgent: Calculate domain distribution
note right
  Recommended domain distribution:
  - Creative: 60% (matches strengths and primary goals)
  - Mindfulness: 30% (supports aspirations, remains accessible)
  - Physical: 5% (minimal but present for balance)
  - Social: 5% (minimal but present for balance)
  
  Activity duration preferences:
  - Short activities (15-30 minutes): 70%
  - Medium activities (30-60 minutes): 30%
  - Long activities (60+ minutes): 0% during Foundation
end note

StrategyAgent -> Orchestrator: Strategy framework
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  trust_phase: "foundation",
  challenge_calibration: {
    "creative": {"range": [30, 40], "weekly_increase": 5},
    "mindfulness": {"range": [20, 30], "weekly_increase": 5},
    "physical": {"range": [10, 20], "weekly_increase": 5},
    "social": {"range": [15, 25], "weekly_increase": 5}
  },
  domain_distribution: {
    "creative": 0.60,
    "mindfulness": 0.30,
    "physical": 0.05,
    "social": 0.05
  },
  duration_preferences: {
    "short": 0.70,
    "medium": 0.30,
    "long": 0.0
  },
  key_development_focus: "project_completion_confidence",
  confidence_score: 0.90
end note

deactivate StrategyAgent
activate Orchestrator

Orchestrator -> EthicalAgent: Send profile for validation
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  resource_context: {...},
  engagement_patterns: {...},
  psychological_profile: {...},
  strategy_framework: {...}
end note

deactivate Orchestrator
activate EthicalAgent

== Step 9: Ethical Oversight Agent - Profile Validation ==

EthicalAgent -> EthicalAgent: Review profile for ethical concerns
note right
  Profile balance assessment:
  - Balanced representation of strengths and weaknesses ✓
  - Fair documentation of beliefs and limitations ✓
  - No inappropriate assumptions about user capabilities ✓
  
  Psychological safety:
  - Challenge levels respect trust phase ✓
  - Resistance areas approached gradually ✓
  - Activity domain distribution supports confidence building ✓
  
  Privacy concerns:
  - Sensitive information properly handled ✓
  - System respects user boundaries ✓
  
  Ethical recommendations:
  - Emphasize small wins for completion confidence
  - Include light creative activities with clear endpoints
  - Be transparent about challenge calibration approach
end note

EthicalAgent -> Orchestrator: Ethical validation report
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  validation_result: "approved",
  ethical_assessment: {
    "profile_balance": "appropriate",
    "psychological_safety": "maintained",
    "privacy_handling": "compliant",
    "potential_concerns": []
  },
  recommendations: [
    "Emphasize small wins for completion confidence",
    "Include light creative activities with clear endpoints",
    "Be transparent about challenge calibration approach"
  ],
  confidence_score: 0.95
end note

deactivate EthicalAgent
activate Orchestrator

== Step 10: Orchestrator Agent - Final Integration ==

Orchestrator -> Orchestrator: Apply ethical recommendations
note right
  Added recommendations to strategy framework:
  - Modified challenge levels to emphasize small wins
  - Added preference for activities with clear endpoints
  - Updated framing guidance to emphasize transparency
end note

Orchestrator -> Orchestrator: Prepare guidance for Mentor Agent
note right
  Mentor Agent guidance:
  - Communication focus on creative confidence building
  - Frame challenges as small, achievable steps
  - Emphasize completion rather than perfection
  - Acknowledge user's creative strengths
  - Be transparent about the calibrated approach
end note

Orchestrator -> DB: Update HistoryEvent (onboarding complete)
note right
  event_type: "onboarding_complete"
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  details: {
    "workflow_id": "ONB-2025-03-22-001",
    "status": "completed",
    "completion_timestamp": "2025-03-22T15:45:00Z",
    "next_steps": "wheel_generation"
  }
end note

Orchestrator -> Interface: Complete onboarding workflow
note right
  user_profile_id: "550e8400-e29b-41d4-a716-************"
  profile_creation_complete: true,
  initial_strategy_established: true,
  ready_for_wheel_generation: true,
  mentor_guidance: {...}
end note

deactivate Orchestrator

Interface -> User: Present welcome and next steps
note right
  "Welcome to Game of Life, Emma! 
   We've created your profile based on your responses.
   Your first activity wheel is being prepared with a focus
   on creative activities that build confidence through completion.
   Let's start your journey!"
end note

@enduml