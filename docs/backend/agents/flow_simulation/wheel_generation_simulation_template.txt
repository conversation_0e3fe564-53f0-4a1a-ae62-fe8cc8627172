Goal:

Simulate a realistic wheel generation workflow for <PERSON> ("<PERSON><PERSON><PERSON>") using his complete user profile. Step through how each agent in the agent team would process information, make decisions, and contribute to creating a personalized activity wheel. After the simulation, evaluate the process to identify strengths, limitations, and practical challenges in the current data model and agent workflow.

— — — — — — — — — — — — — — — — —

Output Structure:

1. INITIAL CONTEXT SETUP
   - Summarize the current state of <PERSON>'s profile (psychological state, environment, goals, etc.)
   - Identify which stage of trust development he's in (Foundation Phase or Expansion Phase)
   - Note his current context (time, location, resources available)

2. DETAILED AGENT WORKFLOW SIMULATION
   For each agent in the wheel generation workflow:
   - Mentor Agent: Show conversation with <PERSON> to gather context
   - Orchestrator Agent: Outline coordination strategy and agent delegation
   - Resource & Capacity Agent: Detail analysis of available resources and constraints
   - Engagement & Pattern Analytics Agent: Show pattern analysis and engagement metrics
   - Psychological Monitoring Agent: Present psychological assessment and challenge calibration
   - Strategy Agent: Demonstrate strategic framework development
   - Wheel/Activity Agent: Detail activity selection and wheel construction
   - Ethical Oversight Agent: Present ethical validation
   - Final integration: Show the completed wheel with rationales
   
   For each agent section, include:
   - Data inputs analyzed
   - Key considerations and decision points
   - Outputs produced
   - How these outputs feed into the next agent's work

3. SAMPLE WHEEL PRESENTATION
   - Show how the Mentor Agent would present the final wheel to <PERSON>
   - Include 6-8 tailored activities with their rationales and connections to Philipp's goals
   - Demonstrate how the presentation would address Philipp's specific psychological profile

4. EVALUATION AND INSIGHTS
   - Strengths: What worked well in the workflow? Which aspects of the data model provided valuable insights?
   - Limitations: Where were there gaps or difficulties? Which parts of the process seemed artificial or impractical?
   - Implementation Challenges: What practical obstacles might arise in a real implementation?
   - Recommendations: Specific suggestions for improving the data model, agent workflows, or overall approach

5. SPECIFIC RECOMMENDATIONS FOR MODEL ENHANCEMENT
   - Data model refinements needed
   - Agent workflow optimizations
   - Interface considerations for real-world implementation

— — — — — — — — — — — — — — — — —

Things to Avoid:

- Don't focus only on happy path scenarios - include realistic challenges and decision points
- Avoid oversimplifying agent reasoning - show the complex considerations each agent would process
- Don't gloss over potential ethical concerns or trade-offs
- Avoid proposing activities that are unrealistic given Philipp's current context, resources, and limitations
- Don't overlook the connection between activities and Philipp's stated goals and aspirations
- Avoid proposing model changes that would require a complete restructuring - focus on incremental improvements

— — — — — — — — — — — — — — — — —

Context for this Request:

[INSERT PHIPHI'S FULL USER PROFILE DATA]

[INSERT WHEEL GENERATION AGENT WORKFLOW DOCUMENTATION]

[INSERT RELEVANT DATA MODEL DEFINITIONS]

[INSERT WHEEL GENERATION FLOW DOCUMENTATION]

— — — — — — — — — — — — — — — — —

Please ask me any clarifying questions about my request before you start.