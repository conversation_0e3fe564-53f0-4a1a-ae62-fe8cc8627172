# Tool Mocking System for Workflow Benchmarking

This document provides guidance on the tool mocking system used in the workflow benchmarking system, focusing on creating and configuring mock tools, using conditional responses, simulating errors and delays, and validating tool calls against expectations.

## Table of Contents
- [Tool Mocking Overview](#tool-mocking-overview)
- [MockToolRegistry](#mocktoolregistry)
- [Creating and Configuring Mock Tools](#creating-and-configuring-mock-tools)
- [Using Conditional Responses](#using-conditional-responses)
- [Simulating Errors and Delays](#simulating-errors-and-delays)
- [Validating Tool Calls Against Expectations](#validating-tool-calls-against-expectations)
- [Tool Call Tracking](#tool-call-tracking)
- [Common Tool Mocking Patterns](#common-tool-mocking-patterns)
- [Best Practices](#best-practices)

## Tool Mocking Overview

The workflow benchmarking system uses a tool mocking system to simulate tool calls during benchmark execution. This allows for consistent, reproducible benchmarks without relying on external services or resources. The tool mocking system consists of several key components:

1. **MockToolRegistry**: Central registry for mock tools.
2. **Conditional Responses**: Support for dynamic responses based on input parameters.
3. **Error Simulation**: Support for simulating errors and exceptions.
4. **Delay Simulation**: Support for simulating realistic delays.
5. **Tool Call Validation**: Support for validating tool calls against expectations.
6. **Tool Call Tracking**: Support for tracking tool calls and usage statistics.

These components work together to provide a flexible, powerful tool mocking system for workflow benchmarking.

## MockToolRegistry

The `MockToolRegistry` is the central component of the tool mocking system. It provides methods for registering, configuring, and calling mock tools.

### Key Features

- **Tool Registration**: Registers mock tools with the registry.
- **Tool Configuration**: Configures mock tools with responses, assertions, and delays.
- **Tool Call Handling**: Handles tool calls and returns mock responses.
- **Tool Call Tracking**: Tracks tool calls and usage statistics.
- **Tool Call Validation**: Validates tool calls against expectations.
- **Error Simulation**: Simulates errors and exceptions.
- **Delay Simulation**: Simulates realistic delays.

### Example Usage

```python
from apps.main.testing.mock_tool_registry import MockToolRegistry

# Create a mock tool registry
registry = MockToolRegistry()

# Configure the registry with mock responses
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"},
        "assertions": [
            {"type": "param_check", "param": "user_id", "should_exist": True}
        ],
        "delay": 0.1
    },
    "search_database": [
        {
            "condition": "get(tool_input, 'query') == 'test'",
            "response": {"results": ["result1", "result2"]},
            "assertions": [
                {"type": "call_count", "expected": 1}
            ]
        },
        {
            "condition": "True",
            "response": {"results": []},
            "delay": 0.2
        }
    ]
})

# Call a mock tool
result = await registry.call_tool("get_user_profile", user_id="test-user")
# Result: {"id": "test-user", "name": "Test User"}

# Get call counts
call_counts = registry.get_call_counts()
# Result: {"get_user_profile": 1}
```

## Creating and Configuring Mock Tools

### Basic Configuration

The simplest way to configure a mock tool is to provide a static response:

```python
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"}
    }
})
```

### Configuration with Assertions

You can add assertions to validate tool calls:

```python
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"},
        "assertions": [
            {"type": "param_check", "param": "user_id", "should_exist": True},
            {"type": "param_value", "param": "user_id", "expected": "test-user"}
        ]
    }
})
```

### Configuration with Delay

You can add a delay to simulate realistic tool execution times:

```python
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"},
        "delay": 0.1  # 100ms delay
    }
})
```

### Configuration with Error Simulation

You can configure a tool to raise an exception:

```python
registry.configure({
    "get_user_profile": {
        "response": {"$raise_exception": "User not found"},
        "delay": 0.1
    }
})
```

### Configuration with Conditional Responses

You can configure a tool to return different responses based on input parameters:

```python
registry.configure({
    "search_database": [
        {
            "condition": "get(tool_input, 'query') == 'test'",
            "response": {"results": ["result1", "result2"]}
        },
        {
            "condition": "True",
            "response": {"results": []}
        }
    ]
})
```

### Configuration from Scenario Metadata

The `MockToolRegistry` can be configured from a benchmark scenario's metadata:

```python
# Scenario metadata
metadata = {
    "mock_tool_responses": {
        "get_user_profile": {
            "response": {"id": "test-user", "name": "Test User"},
            "assertions": [
                {"type": "param_check", "param": "user_id", "should_exist": True}
            ],
            "delay": 0.1
        },
        "search_database": [
            {
                "condition": "get(tool_input, 'query') == 'test'",
                "response": {"results": ["result1", "result2"]},
                "assertions": [
                    {"type": "call_count", "expected": 1}
                ]
            },
            {
                "condition": "True",
                "response": {"results": []},
                "delay": 0.2
            }
        ]
    }
}

# Configure the registry from scenario metadata
registry.configure(metadata["mock_tool_responses"])
```

## Using Conditional Responses

Conditional responses allow you to return different responses based on input parameters. This is useful for simulating complex tool behavior.

### Basic Conditional Response

```python
registry.configure({
    "search_database": [
        {
            "condition": "get(tool_input, 'query') == 'test'",
            "response": {"results": ["result1", "result2"]}
        },
        {
            "condition": "True",
            "response": {"results": []}
        }
    ]
})
```

### Conditional Response with Call Count

You can use the `call_count` variable in conditions to return different responses based on the number of times a tool has been called:

```python
registry.configure({
    "get_random_number": [
        {
            "condition": "call_count == 0",
            "response": {"number": 42}
        },
        {
            "condition": "call_count == 1",
            "response": {"number": 7}
        },
        {
            "condition": "True",
            "response": {"number": 0}
        }
    ]
})
```

### Conditional Response with Multiple Parameters

You can use multiple parameters in conditions:

```python
registry.configure({
    "search_database": [
        {
            "condition": "get(tool_input, 'query') == 'test' and get(tool_input, 'limit') == 10",
            "response": {"results": ["result1", "result2", "result3", "result4", "result5", "result6", "result7", "result8", "result9", "result10"]}
        },
        {
            "condition": "get(tool_input, 'query') == 'test'",
            "response": {"results": ["result1", "result2"]}
        },
        {
            "condition": "True",
            "response": {"results": []}
        }
    ]
})
```

### Conditional Response with Complex Logic

You can use complex logic in conditions:

```python
registry.configure({
    "search_database": [
        {
            "condition": "get(tool_input, 'query', '').startswith('test') and get(tool_input, 'limit', 0) > 5",
            "response": {"results": ["result1", "result2", "result3", "result4", "result5", "result6"]}
        },
        {
            "condition": "get(tool_input, 'query', '').startswith('test')",
            "response": {"results": ["result1", "result2"]}
        },
        {
            "condition": "True",
            "response": {"results": []}
        }
    ]
})
```

### Conditional Response with Dynamic Response

You can use f-strings in responses to create dynamic responses:

```python
registry.configure({
    "get_user_profile": [
        {
            "condition": "True",
            "response": {"id": "{tool_input.get('user_id', 'unknown')}", "name": "User {tool_input.get('user_id', 'Unknown')}"}
        }
    ]
})
```

## Simulating Errors and Delays

### Simulating Errors

You can simulate errors by configuring a tool to raise an exception:

```python
registry.configure({
    "get_user_profile": {
        "response": {"$raise_exception": "User not found"}
    }
})
```

### Simulating Specific Exceptions

You can simulate specific exceptions by providing an exception class:

```python
registry.configure({
    "get_user_profile": {
        "response": {"$raise_exception": {"type": "ValueError", "message": "Invalid user ID"}}
    }
})
```

### Simulating Conditional Errors

You can simulate errors conditionally:

```python
registry.configure({
    "get_user_profile": [
        {
            "condition": "get(tool_input, 'user_id') == 'invalid'",
            "response": {"$raise_exception": "User not found"}
        },
        {
            "condition": "True",
            "response": {"id": "test-user", "name": "Test User"}
        }
    ]
})
```

### Simulating Delays

You can simulate delays to make tool calls more realistic:

```python
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"},
        "delay": 0.1  # 100ms delay
    }
})
```

### Simulating Variable Delays

You can simulate variable delays:

```python
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"},
        "delay": {"min": 0.05, "max": 0.2}  # Random delay between 50ms and 200ms
    }
})
```

### Simulating Conditional Delays

You can simulate delays conditionally:

```python
registry.configure({
    "search_database": [
        {
            "condition": "get(tool_input, 'query') == 'slow'",
            "response": {"results": ["result1", "result2"]},
            "delay": 0.5  # 500ms delay for slow queries
        },
        {
            "condition": "True",
            "response": {"results": []},
            "delay": 0.1  # 100ms delay for other queries
        }
    ]
})
```

## Validating Tool Calls Against Expectations

### Parameter Existence Assertion

You can assert that a parameter exists:

```python
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"},
        "assertions": [
            {"type": "param_check", "param": "user_id", "should_exist": True}
        ]
    }
})
```

### Parameter Value Assertion

You can assert that a parameter has a specific value:

```python
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"},
        "assertions": [
            {"type": "param_value", "param": "user_id", "expected": "test-user"}
        ]
    }
})
```

### Call Count Assertion

You can assert that a tool is called a specific number of times:

```python
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"},
        "assertions": [
            {"type": "call_count", "expected": 1}
        ]
    }
})
```

### Parameter Type Assertion

You can assert that a parameter has a specific type:

```python
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"},
        "assertions": [
            {"type": "param_type", "param": "user_id", "expected_type": "str"}
        ]
    }
})
```

### Multiple Assertions

You can combine multiple assertions:

```python
registry.configure({
    "get_user_profile": {
        "response": {"id": "test-user", "name": "Test User"},
        "assertions": [
            {"type": "param_check", "param": "user_id", "should_exist": True},
            {"type": "param_value", "param": "user_id", "expected": "test-user"},
            {"type": "call_count", "expected": 1}
        ]
    }
})
```

### Conditional Assertions

You can use conditional assertions:

```python
registry.configure({
    "search_database": [
        {
            "condition": "get(tool_input, 'query') == 'test'",
            "response": {"results": ["result1", "result2"]},
            "assertions": [
                {"type": "param_check", "param": "limit", "should_exist": True},
                {"type": "param_value", "param": "limit", "expected": 10}
            ]
        },
        {
            "condition": "True",
            "response": {"results": []},
            "assertions": [
                {"type": "call_count", "expected": 1}
            ]
        }
    ]
})
```

## Tool Call Tracking

### Getting Call Counts

You can get the number of times each tool has been called:

```python
call_counts = registry.get_call_counts()
# Result: {"get_user_profile": 1, "search_database": 2}
```

### Getting Call History

You can get the history of tool calls:

```python
call_history = registry.get_call_history()
# Result: [
#     {"tool": "get_user_profile", "input": {"user_id": "test-user"}, "output": {"id": "test-user", "name": "Test User"}},
#     {"tool": "search_database", "input": {"query": "test"}, "output": {"results": ["result1", "result2"]}}
# ]
```

### Getting Tool-Specific Call History

You can get the history of calls to a specific tool:

```python
tool_history = registry.get_tool_history("search_database")
# Result: [
#     {"input": {"query": "test"}, "output": {"results": ["result1", "result2"]}}
# ]
```

### Getting Call Statistics

You can get statistics about tool calls:

```python
call_stats = registry.get_call_statistics()
# Result: {
#     "total_calls": 3,
#     "unique_tools": 2,
#     "calls_by_tool": {"get_user_profile": 1, "search_database": 2},
#     "average_delay": 0.133,
#     "total_delay": 0.4
# }
```

## Common Tool Mocking Patterns

### Pattern: User Profile Tool

```python
registry.configure({
    "get_user_profile": {
        "response": {
            "id": "test-user",
            "name": "Test User",
            "email": "<EMAIL>",
            "preferences": {
                "language": "en",
                "theme": "light"
            },
            "traits": {
                "openness": 0.7,
                "conscientiousness": 0.8,
                "extraversion": 0.5,
                "agreeableness": 0.9,
                "neuroticism": 0.3
            }
        },
        "assertions": [
            {"type": "param_check", "param": "user_id", "should_exist": True}
        ],
        "delay": 0.1
    }
})
```

### Pattern: Database Search Tool

```python
registry.configure({
    "search_database": [
        {
            "condition": "get(tool_input, 'query') == 'test'",
            "response": {
                "results": [
                    {"id": "result1", "title": "Test Result 1", "content": "This is test result 1"},
                    {"id": "result2", "title": "Test Result 2", "content": "This is test result 2"}
                ],
                "total": 2,
                "page": 1,
                "page_size": 10
            },
            "assertions": [
                {"type": "param_check", "param": "query", "should_exist": True},
                {"type": "param_check", "param": "limit", "should_exist": True}
            ],
            "delay": 0.2
        },
        {
            "condition": "True",
            "response": {
                "results": [],
                "total": 0,
                "page": 1,
                "page_size": 10
            },
            "delay": 0.1
        }
    ]
})
```

### Pattern: External API Tool

```python
registry.configure({
    "call_external_api": [
        {
            "condition": "get(tool_input, 'endpoint') == 'users' and get(tool_input, 'method') == 'GET'",
            "response": {
                "status": "success",
                "data": [
                    {"id": "user1", "name": "User 1"},
                    {"id": "user2", "name": "User 2"}
                ]
            },
            "assertions": [
                {"type": "param_check", "param": "endpoint", "should_exist": True},
                {"type": "param_check", "param": "method", "should_exist": True}
            ],
            "delay": 0.3
        },
        {
            "condition": "get(tool_input, 'endpoint') == 'users' and get(tool_input, 'method') == 'POST'",
            "response": {
                "status": "success",
                "data": {"id": "user3", "name": "User 3"}
            },
            "assertions": [
                {"type": "param_check", "param": "endpoint", "should_exist": True},
                {"type": "param_check", "param": "method", "should_exist": True},
                {"type": "param_check", "param": "data", "should_exist": True}
            ],
            "delay": 0.3
        },
        {
            "condition": "True",
            "response": {
                "status": "error",
                "message": "Invalid endpoint or method"
            },
            "delay": 0.1
        }
    ]
})
```

### Pattern: Error Handling Tool

```python
registry.configure({
    "get_user_profile": [
        {
            "condition": "get(tool_input, 'user_id') == 'invalid'",
            "response": {"$raise_exception": "User not found"},
            "delay": 0.1
        },
        {
            "condition": "get(tool_input, 'user_id') == 'error'",
            "response": {"$raise_exception": {"type": "ValueError", "message": "Invalid user ID"}},
            "delay": 0.1
        },
        {
            "condition": "get(tool_input, 'user_id') == 'timeout'",
            "response": {"$raise_exception": "Request timed out"},
            "delay": 2.0
        },
        {
            "condition": "True",
            "response": {"id": "test-user", "name": "Test User"},
            "delay": 0.1
        }
    ]
})
```

### Pattern: Stateful Tool

```python
# Create a stateful tool using a closure
def create_stateful_tool_config():
    """Create a stateful tool configuration."""
    state = {"counter": 0, "items": []}
    
    def get_counter_response(tool_input):
        """Get the counter response."""
        return {"counter": state["counter"]}
    
    def increment_counter_response(tool_input):
        """Increment the counter and return the new value."""
        state["counter"] += 1
        return {"counter": state["counter"]}
    
    def add_item_response(tool_input):
        """Add an item to the list and return the updated list."""
        item = tool_input.get("item")
        if item:
            state["items"].append(item)
        return {"items": state["items"]}
    
    def get_items_response(tool_input):
        """Get the list of items."""
        return {"items": state["items"]}
    
    return {
        "get_counter": {
            "response": get_counter_response,
            "delay": 0.1
        },
        "increment_counter": {
            "response": increment_counter_response,
            "delay": 0.1
        },
        "add_item": {
            "response": add_item_response,
            "assertions": [
                {"type": "param_check", "param": "item", "should_exist": True}
            ],
            "delay": 0.1
        },
        "get_items": {
            "response": get_items_response,
            "delay": 0.1
        }
    }

# Configure the registry with the stateful tool
registry.configure(create_stateful_tool_config())
```

## Best Practices

1. **Use Realistic Responses**: Make mock responses as realistic as possible to ensure accurate benchmarks.

2. **Add Assertions**: Add assertions to validate tool calls and ensure they are used correctly.

3. **Simulate Delays**: Add realistic delays to simulate the actual performance of tools.

4. **Use Conditional Responses**: Use conditional responses to simulate complex tool behavior.

5. **Simulate Errors**: Simulate errors to test error handling in workflows.

6. **Track Tool Calls**: Track tool calls to analyze tool usage patterns.

7. **Use Schema Validation**: Validate tool inputs and outputs against schemas.

8. **Document Mock Tools**: Document mock tool configurations for future reference.

9. **Test Mock Tools**: Test mock tools to ensure they behave as expected.

10. **Use Realistic Data**: Use realistic data in mock responses to ensure accurate benchmarks.

11. **Handle Edge Cases**: Handle edge cases in mock tools, such as missing parameters or unexpected values.

12. **Use Consistent Naming**: Use consistent naming for mock tools and parameters.

13. **Keep Mock Tools Simple**: Keep mock tools simple and focused on a single responsibility.

14. **Use Factory Functions**: Use factory functions to create complex mock tool configurations.

15. **Use Stateful Tools**: Use stateful tools to simulate tools that maintain state between calls.

16. **Use Dynamic Responses**: Use dynamic responses to create realistic, context-aware mock tools.

17. **Test Error Handling**: Test error handling in workflows by simulating errors in mock tools.

18. **Use Realistic Delays**: Use realistic delays to simulate the actual performance of tools.

19. **Track Tool Usage**: Track tool usage to analyze tool usage patterns and optimize workflows.

20. **Document Tool Expectations**: Document tool expectations to ensure tools are used correctly.
