# Comprehensive Context Variables Schema

This document describes the comprehensive context variables schema (v2.0.0) for the workflow benchmarking system. The comprehensive context variables schema encompasses all factors that influence evaluation criteria, including user profile variables, environmental variables, and interaction variables.

## Overview

The comprehensive context variables schema extends the original situation schema (v1.0.0) with comprehensive context variables organized into three main categories:

- **User Profile Variables**: Enhanced user state information including trust level, personality traits, preferences, and demographics
- **Environmental Variables**: Device capabilities, temporal context, physical environment, and external conditions
- **Interaction Variables**: Session history, current goals, relationship phase, and communication preferences

## Schema Structure

### User State

The `user_state` object provides enhanced information about the user's current state:

```json
"user_state": {
  "trust_level": 75,
  "mood": "happy",
  "environment": "home"
}
```

| Field | Type | Description |
|-------|------|-------------|
| `trust_level` | number | Numeric trust level (0-100) |
| `mood` | string | Current emotional state of the user |
| `environment` | string | Current physical environment of the user |

### Device Capabilities

The `device_capabilities` object provides information about the user's device:

```json
"device_capabilities": {
  "screen_size": "large",
  "input_method": "keyboard",
  "accessibility": {
    "vision_impaired": false,
    "hearing_impaired": false,
    "motor_impaired": false,
    "preferred_modality": "visual"
  }
}
```

| Field | Type | Description |
|-------|------|-------------|
| `screen_size` | string | Size of the user's screen (e.g., 'small', 'medium', 'large') |
| `input_method` | string | Primary input method used by the user (touch, keyboard, voice, mixed) |
| `accessibility` | object | Accessibility settings and requirements |

### Time Context

The `time_context` object provides temporal context information:

```json
"time_context": {
  "available_time": 30,
  "time_of_day": "morning",
  "time_zone": "UTC"
}
```

| Field | Type | Description |
|-------|------|-------------|
| `available_time` | number | Amount of time available in minutes |
| `time_of_day` | string | Current time of day (morning, afternoon, evening, night) |
| `time_zone` | string | User's time zone (e.g., 'UTC', 'America/New_York') |

### User Preferences

The `user_preferences` object provides information about the user's preferences:

```json
"user_preferences": {
  "learning_style": "visual",
  "communication_preferences": {
    "verbosity": "moderate",
    "formality": "neutral",
    "feedback_frequency": "moderate"
  }
}
```

| Field | Type | Description |
|-------|------|-------------|
| `learning_style` | string | User's preferred learning style (visual, auditory, reading, kinesthetic) |
| `communication_preferences` | object | Communication preferences |

## Migration

When migrating from v1.0.0 to v2.0.0, the following mappings are applied:

- `trust_level` → `user_state.trust_level`
- `psychological_state.mood` → `user_state.mood`
- `environment.location` → `user_state.environment`
- `time_availability` → `time_context.available_time`

Other fields are populated with default values if not present in the v1.0.0 data.

## Usage in Context-Linked Assessment Framework

The comprehensive context variables schema provides detailed information for the Context-Linked Assessment Framework, allowing for contextually appropriate evaluation of agent performance. The context variables are used to:

1. **Adapt Evaluation Criteria**: Dynamically adjust evaluation criteria based on comprehensive context variable ranges
2. **Test Contextual Appropriateness**: Evaluate agent adaptability to different user profile, environmental, and interaction contexts
3. **Ensure Consistent Linkage**: Maintain explicit connections between Generic Situations, Context Variable ranges, and adapted evaluation criteria
4. **Support Context Presets**: Enable reusable context variable combinations for common testing scenarios

## Example

Here's an example of a complete generic situation with comprehensive context variables:

```json
{
  "workflow_type": "wheel_generation",
  "text": "I need an activity suggestion",
  "trust_level": 75,
  "time_availability": 30,
  "environment": {
    "location": "home",
    "noise_level": "low"
  },
  "psychological_state": {
    "mood": "happy",
    "energy_level": "high"
  },
  "user_state": {
    "trust_level": 75,
    "mood": "happy",
    "environment": "home"
  },
  "device_capabilities": {
    "screen_size": "large",
    "input_method": "keyboard",
    "accessibility": {
      "vision_impaired": false,
      "hearing_impaired": false,
      "motor_impaired": false,
      "preferred_modality": "visual"
    }
  },
  "time_context": {
    "available_time": 30,
    "time_of_day": "morning",
    "time_zone": "UTC"
  },
  "user_preferences": {
    "learning_style": "visual",
    "communication_preferences": {
      "verbosity": "moderate",
      "formality": "neutral",
      "feedback_frequency": "moderate"
    }
  }
}
```
