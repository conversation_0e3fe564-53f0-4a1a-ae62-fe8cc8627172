# Error Handling Patterns for Workflow Benchmarking

This document provides guidance on error handling patterns used in the workflow benchmarking system, focusing on multi-level error capture, detailed error reporting, and error handling in async contexts.

## Table of Contents
- [Error Handling Philosophy](#error-handling-philosophy)
- [Multi-Level Error Capture](#multi-level-error-capture)
- [Using EventService for Error Reporting](#using-eventservice-for-error-reporting)
- [Error Handling in Async Contexts](#error-handling-in-async-contexts)
- [Error Handling in Background Tasks](#error-handling-in-background-tasks)
- [Common Error Patterns](#common-error-patterns)
- [Best Practices](#best-practices)

## Error Handling Philosophy

The workflow benchmarking system follows these principles for error handling:

1. **Fail Gracefully**: Errors should not crash the entire system. Catch and handle errors at appropriate levels.
2. **Detailed Reporting**: Provide detailed error information for debugging, including context, traceback, and error type.
3. **User-Friendly Messages**: Present user-friendly error messages while logging detailed information for developers.
4. **Consistent Patterns**: Use consistent error handling patterns throughout the codebase.
5. **Proper Propagation**: Propagate errors to the appropriate level for handling.

## Multi-Level Error Capture

The workflow benchmarking system uses a multi-level approach to error capture, ensuring that errors are caught and handled at the appropriate level.

### Level 1: Function-Level Error Handling

```python
async def get_scenario_async(scenario_id):
    """Get a benchmark scenario by ID."""
    try:
        return await sync_to_async(
            _get_scenario_sync,
            thread_sensitive=True
        )(scenario_id)
    except Exception as e:
        logger.error(f"Error getting scenario {scenario_id}: {str(e)}", exc_info=True)
        # Re-raise with more context
        raise ValueError(f"Failed to retrieve scenario {scenario_id}: {str(e)}") from e
```

### Level 2: Component-Level Error Handling

```python
async def execute_benchmark(scenario_id, params=None):
    """Execute a benchmark with component-level error handling."""
    try:
        # Load scenario
        scenario = await get_scenario_async(scenario_id)
        if not scenario:
            raise ValueError(f"Scenario {scenario_id} not found")
            
        # Prepare mock tools
        try:
            mock_tools = await prepare_mock_tools(scenario)
        except Exception as e:
            logger.error(f"Error preparing mock tools: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to prepare mock tools: {str(e)}") from e
            
        # Run benchmark
        try:
            result = await run_workflow_benchmark(scenario, mock_tools)
        except Exception as e:
            logger.error(f"Error running workflow benchmark: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to run workflow benchmark: {str(e)}") from e
            
        return result
    except Exception as e:
        # Component-level error handling
        logger.error(f"Error executing benchmark for scenario {scenario_id}: {str(e)}", exc_info=True)
        # Report error via EventService
        await EventService.emit_debug_info(
            event_type="benchmark_error",
            data={
                "error": str(e),
                "scenario_id": scenario_id,
                "params": params
            }
        )
        # Re-raise for higher-level handling
        raise
```

### Level 3: System-Level Error Handling

```python
async def handle_benchmark_request(request):
    """Handle a benchmark request with system-level error handling."""
    try:
        # Parse request
        scenario_id = request.data.get('scenario_id')
        params = request.data.get('params', {})
        
        # Execute benchmark
        result = await execute_benchmark(scenario_id, params)
        
        # Return success response
        return {
            'status': 'success',
            'result': result
        }
    except ValueError as e:
        # Handle expected errors
        logger.warning(f"Invalid benchmark request: {str(e)}")
        return {
            'status': 'error',
            'error': str(e),
            'error_type': 'validation_error'
        }
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Unexpected error handling benchmark request: {str(e)}", exc_info=True)
        # Report error via EventService
        await EventService.emit_debug_info(
            event_type="system_error",
            data={
                "error": str(e),
                "traceback": traceback.format_exc(),
                "request_data": {
                    "scenario_id": request.data.get('scenario_id'),
                    "params": request.data.get('params', {})
                }
            }
        )
        return {
            'status': 'error',
            'error': "An unexpected error occurred. Please try again later.",
            'error_type': 'system_error',
            'error_id': str(uuid.uuid4())  # Generate a unique ID for tracking
        }
```

## Using EventService for Error Reporting

The `EventService` is a central component for reporting errors and events in the workflow benchmarking system. It provides methods for emitting events to WebSocket clients and logging detailed error information.

### Emitting Debug Information

```python
async def execute_benchmark(scenario_id, user_profile_id=None):
    """Execute a benchmark with error reporting via EventService."""
    try:
        # Execute benchmark logic
        result = await run_benchmark(scenario_id)
        return result
    except Exception as e:
        # Log the error
        logger.error(f"Error executing benchmark: {str(e)}", exc_info=True)
        
        # Report error via EventService
        await EventService.emit_debug_info(
            event_type="benchmark_error",
            data={
                "error": str(e),
                "traceback": traceback.format_exc(),
                "scenario_id": scenario_id,
                "component": "workflow_benchmark_manager",
                "method": "execute_benchmark",
                "timestamp": datetime.now().isoformat()
            },
            user_profile_id=user_profile_id  # Target the event to a specific user if available
        )
        
        # Re-raise the error for higher-level handling
        raise
```

### Emitting User-Facing Errors

```python
async def execute_benchmark(scenario_id, user_profile_id=None):
    """Execute a benchmark with user-facing error reporting."""
    try:
        # Execute benchmark logic
        result = await run_benchmark(scenario_id)
        
        # Report success
        await EventService.emit_event(
            event_type="benchmark_complete",
            data={"result": result},
            user_profile_id=user_profile_id
        )
        
        return result
    except ValueError as e:
        # Handle expected errors
        logger.warning(f"Validation error: {str(e)}")
        
        # Report user-facing error
        await EventService.emit_event(
            event_type="benchmark_error",
            data={
                "error": str(e),
                "error_type": "validation_error"
            },
            user_profile_id=user_profile_id
        )
        
        # Re-raise the error for higher-level handling
        raise
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        
        # Report detailed error to debug channel
        await EventService.emit_debug_info(
            event_type="system_error",
            data={
                "error": str(e),
                "traceback": traceback.format_exc(),
                "scenario_id": scenario_id,
                "component": "workflow_benchmark_manager",
                "method": "execute_benchmark",
                "timestamp": datetime.now().isoformat()
            },
            user_profile_id=user_profile_id
        )
        
        # Report user-facing error
        await EventService.emit_event(
            event_type="benchmark_error",
            data={
                "error": "An unexpected error occurred. Please try again later.",
                "error_type": "system_error",
                "error_id": str(uuid.uuid4())
            },
            user_profile_id=user_profile_id
        )
        
        # Re-raise the error for higher-level handling
        raise
```

## Error Handling in Async Contexts

Async code requires special error handling patterns to ensure that errors are properly caught and reported.

### Using try/except in Async Functions

```python
async def execute_async_operation():
    """Execute an async operation with error handling."""
    try:
        # Execute async operation
        result = await async_operation()
        return result
    except Exception as e:
        # Handle the error
        logger.error(f"Error executing async operation: {str(e)}", exc_info=True)
        # Re-raise the error
        raise
```

### Using asyncio.gather with return_exceptions

```python
async def execute_multiple_operations(operations):
    """Execute multiple async operations with error handling."""
    # Execute operations concurrently and collect results/exceptions
    results = await asyncio.gather(*operations, return_exceptions=True)
    
    # Process results and handle exceptions
    processed_results = []
    errors = []
    
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            # Handle the exception
            logger.error(f"Error executing operation {i}: {str(result)}", exc_info=True)
            errors.append({
                "operation_index": i,
                "error": str(result),
                "error_type": type(result).__name__
            })
        else:
            # Process the result
            processed_results.append(result)
    
    return {
        "results": processed_results,
        "errors": errors
    }
```

### Using asyncio.shield for Critical Operations

```python
async def execute_critical_operation():
    """Execute a critical operation that should not be cancelled."""
    try:
        # Shield the operation from cancellation
        result = await asyncio.shield(critical_operation())
        return result
    except asyncio.CancelledError:
        # Handle cancellation
        logger.warning("Operation was cancelled, but critical operation continued")
        raise
    except Exception as e:
        # Handle other exceptions
        logger.error(f"Error executing critical operation: {str(e)}", exc_info=True)
        raise
```

## Error Handling in Background Tasks

Background tasks, such as Celery tasks, require special error handling patterns to ensure that errors are properly reported and logged.

### Using EventService.emit_event_sync in Celery Tasks

```python
@app.task
def run_benchmark_task(scenario_id, user_profile_id=None):
    """Run a benchmark as a Celery task with error handling."""
    try:
        # Set up asyncio event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Execute the benchmark
            result = loop.run_until_complete(execute_benchmark(scenario_id))
            
            # Report success
            EventService.emit_event_sync(
                event_type="benchmark_complete",
                data={"result": result},
                user_profile_id=user_profile_id
            )
            
            return result
        finally:
            # Clean up the event loop
            loop.close()
    except Exception as e:
        # Handle the error
        logger.error(f"Error executing benchmark task: {str(e)}", exc_info=True)
        
        # Report error via EventService (sync version)
        EventService.emit_event_sync(
            event_type="benchmark_error",
            data={
                "error": str(e),
                "error_type": type(e).__name__,
                "scenario_id": scenario_id
            },
            user_profile_id=user_profile_id
        )
        
        # Report detailed error to debug channel
        EventService.emit_debug_info_sync(
            event_type="system_error",
            data={
                "error": str(e),
                "traceback": traceback.format_exc(),
                "scenario_id": scenario_id,
                "component": "benchmark_task",
                "method": "run_benchmark_task",
                "timestamp": datetime.now().isoformat()
            },
            user_profile_id=user_profile_id
        )
        
        # Re-raise the error to mark the task as failed
        raise
```

### Using Task Retry Mechanisms

```python
@app.task(bind=True, max_retries=3)
def run_benchmark_task(self, scenario_id, user_profile_id=None):
    """Run a benchmark as a Celery task with retry mechanism."""
    try:
        # Set up asyncio event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Execute the benchmark
            result = loop.run_until_complete(execute_benchmark(scenario_id))
            
            # Report success
            EventService.emit_event_sync(
                event_type="benchmark_complete",
                data={"result": result},
                user_profile_id=user_profile_id
            )
            
            return result
        finally:
            # Clean up the event loop
            loop.close()
    except (TemporaryError, ConnectionError) as e:
        # Handle temporary errors with retry
        logger.warning(f"Temporary error executing benchmark task: {str(e)}")
        
        # Report retry via EventService
        EventService.emit_event_sync(
            event_type="benchmark_retry",
            data={
                "error": str(e),
                "error_type": type(e).__name__,
                "scenario_id": scenario_id,
                "retry_count": self.request.retries,
                "max_retries": self.max_retries
            },
            user_profile_id=user_profile_id
        )
        
        # Retry the task with exponential backoff
        retry_delay = 60 * (2 ** self.request.retries)  # 60s, 120s, 240s
        raise self.retry(exc=e, countdown=retry_delay)
    except Exception as e:
        # Handle other errors
        logger.error(f"Error executing benchmark task: {str(e)}", exc_info=True)
        
        # Report error via EventService
        EventService.emit_event_sync(
            event_type="benchmark_error",
            data={
                "error": str(e),
                "error_type": type(e).__name__,
                "scenario_id": scenario_id
            },
            user_profile_id=user_profile_id
        )
        
        # Report detailed error to debug channel
        EventService.emit_debug_info_sync(
            event_type="system_error",
            data={
                "error": str(e),
                "traceback": traceback.format_exc(),
                "scenario_id": scenario_id,
                "component": "benchmark_task",
                "method": "run_benchmark_task",
                "timestamp": datetime.now().isoformat()
            },
            user_profile_id=user_profile_id
        )
        
        # Re-raise the error to mark the task as failed
        raise
```

## Common Error Patterns

### Pattern: Error Classification

```python
class BenchmarkError(Exception):
    """Base class for benchmark errors."""
    pass

class ValidationError(BenchmarkError):
    """Error raised when validation fails."""
    pass

class ResourceError(BenchmarkError):
    """Error raised when a resource is not available."""
    pass

class TimeoutError(BenchmarkError):
    """Error raised when an operation times out."""
    pass

async def execute_benchmark(scenario_id):
    """Execute a benchmark with error classification."""
    try:
        # Validate scenario
        scenario = await get_scenario_async(scenario_id)
        if not scenario:
            raise ValidationError(f"Scenario {scenario_id} not found")
            
        # Check resources
        if not await check_resources():
            raise ResourceError("Insufficient resources to execute benchmark")
            
        # Execute benchmark with timeout
        try:
            result = await asyncio.wait_for(run_benchmark(scenario), timeout=300)
        except asyncio.TimeoutError:
            raise TimeoutError("Benchmark execution timed out after 300 seconds")
            
        return result
    except BenchmarkError as e:
        # Handle benchmark-specific errors
        logger.warning(f"Benchmark error: {str(e)}")
        # Re-raise for higher-level handling
        raise
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        # Re-raise as a BenchmarkError
        raise BenchmarkError(f"Unexpected error: {str(e)}") from e
```

### Pattern: Error Context

```python
class ErrorContext:
    """Context for error reporting."""
    
    def __init__(self, component, method, scenario_id=None, user_profile_id=None):
        """Initialize the error context."""
        self.component = component
        self.method = method
        self.scenario_id = scenario_id
        self.user_profile_id = user_profile_id
        self.timestamp = datetime.now().isoformat()
        
    def to_dict(self):
        """Convert the context to a dictionary."""
        return {
            "component": self.component,
            "method": self.method,
            "scenario_id": self.scenario_id,
            "user_profile_id": self.user_profile_id,
            "timestamp": self.timestamp
        }

async def execute_benchmark(scenario_id, user_profile_id=None):
    """Execute a benchmark with error context."""
    # Create error context
    error_context = ErrorContext(
        component="workflow_benchmark_manager",
        method="execute_benchmark",
        scenario_id=scenario_id,
        user_profile_id=user_profile_id
    )
    
    try:
        # Execute benchmark logic
        result = await run_benchmark(scenario_id)
        return result
    except Exception as e:
        # Log the error with context
        logger.error(f"Error executing benchmark: {str(e)}", exc_info=True, extra=error_context.to_dict())
        
        # Report error via EventService with context
        await EventService.emit_debug_info(
            event_type="benchmark_error",
            data={
                "error": str(e),
                "traceback": traceback.format_exc(),
                **error_context.to_dict()
            },
            user_profile_id=user_profile_id
        )
        
        # Re-raise the error for higher-level handling
        raise
```

### Pattern: Error Recovery

```python
async def execute_benchmark_with_recovery(scenario_id):
    """Execute a benchmark with error recovery."""
    # Define recovery strategies
    recovery_strategies = {
        ConnectionError: retry_with_backoff,
        TimeoutError: retry_with_increased_timeout,
        ResourceError: wait_for_resources
    }
    
    # Maximum number of recovery attempts
    max_recovery_attempts = 3
    recovery_attempts = 0
    
    while recovery_attempts <= max_recovery_attempts:
        try:
            # Execute benchmark logic
            result = await run_benchmark(scenario_id)
            return result
        except Exception as e:
            # Check if we have a recovery strategy for this error
            recovery_strategy = recovery_strategies.get(type(e))
            
            if recovery_strategy and recovery_attempts < max_recovery_attempts:
                # Increment recovery attempts
                recovery_attempts += 1
                
                # Log recovery attempt
                logger.warning(f"Recovery attempt {recovery_attempts}/{max_recovery_attempts} for error: {str(e)}")
                
                # Execute recovery strategy
                await recovery_strategy(e, recovery_attempts)
            else:
                # No recovery strategy or max attempts reached
                logger.error(f"Error executing benchmark: {str(e)}", exc_info=True)
                raise
```

## Best Practices

1. **Use Specific Exception Types**: Define and use specific exception types for different error categories.

2. **Include Context in Error Messages**: Error messages should include relevant context (e.g., scenario ID, operation being performed).

3. **Log at Appropriate Levels**: Use appropriate logging levels (DEBUG, INFO, WARNING, ERROR, CRITICAL) based on the severity of the error.

4. **Handle Expected Errors Gracefully**: Catch and handle expected errors at the appropriate level, providing user-friendly messages.

5. **Report Unexpected Errors**: Log and report unexpected errors with detailed information for debugging.

6. **Use Multi-Level Error Handling**: Implement error handling at multiple levels (function, component, system) to ensure errors are properly handled.

7. **Provide Recovery Mechanisms**: Where appropriate, implement recovery mechanisms for transient errors.

8. **Use EventService for Reporting**: Use EventService to report errors to both users and developers.

9. **Include Tracebacks for Debugging**: Include tracebacks in error reports for debugging purposes.

10. **Clean Up Resources**: Ensure resources are properly cleaned up when errors occur, especially in async contexts.

11. **Test Error Handling**: Write tests specifically for error handling to ensure it works as expected.

12. **Document Error Handling Patterns**: Document error handling patterns and expectations for different components.
