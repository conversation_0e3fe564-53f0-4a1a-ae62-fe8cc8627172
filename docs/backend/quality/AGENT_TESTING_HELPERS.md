# Agent Testing Helpers

This document describes the helper functions and utilities available for testing agents with consistent output structure.

## Overview

The agent testing helpers provide a set of utilities to ensure consistent agent output structure during testing. These helpers make it easier to write robust tests for agents by automatically handling common issues like missing fields in agent responses.

## Key Components

### Helper Functions

- **`ensure_agent_output_structure(output_data, agent_role)`**: Ensures agent output has required fields based on the agent role.
  - Adds `context_packet` if missing
  - Adds `user_response` if missing
  - Adds `next_agent` if missing (with role-specific defaults)
  - Adds role-specific fields like `psychological_assessment`, `ethical_validation`, etc.

- **`patch_agent_process_method(agent_class)`**: Patches an agent class's process method to ensure consistent output structure.
  - Returns the original process method for later restoration
  - Automatically applies `ensure_agent_output_structure` to the agent's output

### Enhanced AgentTestRunner

The `AgentTestRunner` class has been enhanced to use these helpers:

- **Improved profiler initialization**: Ensures agents have a valid profiler from the start
- **Output structure patching**: Automatically adds missing fields to agent output
- **Consistent cleanup**: Properly restores original methods after tests

## Usage Examples

### Basic Usage

```python
from apps.main.testing.agent_test_helpers import ensure_agent_output_structure, patch_agent_process_method

# Ensure agent output has required fields
output_data = ensure_agent_output_structure(
    output_data=agent_response,
    agent_role="mentor"
)

# Patch an agent class to ensure consistent output structure
original_process = patch_agent_process_method(MentorAgent)
try:
    # Run tests with patched agent
    result = await agent.process(state)
    # Output will have all required fields
finally:
    # Restore original method
    MentorAgent.process = original_process
```

### Integration with AgentTestRunner

```python
from apps.main.testing.agent_test_runner import AgentTestRunner

# Create a test runner with automatic output structure patching
runner = AgentTestRunner(MentorAgent)

# Run a test with patched process method
result = await runner.run_test(
    state=test_state,
    mock_llm_responses=mock_responses,
    patch_process=True  # Enable output structure patching
)
```

### Testing with Mocked Profiler

```python
from unittest.mock import AsyncMock, MagicMock
from apps.main.testing.agent_test_runner import AgentTestRunner

# Create a mock profiler
mock_profiler = MagicMock()
mock_profiler.record_usage = AsyncMock()

# Create a test runner with the mock profiler
runner = AgentTestRunner(MentorAgent, profiler=mock_profiler)

# Run a test with the mock profiler
result = await runner.run_test(
    state=test_state,
    mock_llm_responses=mock_responses
)

# Verify profiler was called
mock_profiler.record_usage.assert_called()
```

## Default Field Values by Agent Role

The `ensure_agent_output_structure` function adds different default fields based on the agent role:

### Mentor Agent
- `next_agent`: "mentor"
- `context_packet`: {}
- `user_response`: "I'm here to help you on your journey."

### Psychological Agent
- `next_agent`: "strategy"
- `psychological_assessment`: {
  "hexaco_traits": {},
  "communication_preferences": {},
  "trust_level": 50
}

### Ethical Agent
- `next_agent`: "resource"
- `ethical_validation`: {
  "is_appropriate": true,
  "reasoning": "No ethical concerns detected."
}

### Resource Agent
- `next_agent`: "strategy"
- `resource_context`: {
  "relevant_resources": [],
  "knowledge_gaps": []
}

### Strategy Agent
- `next_agent`: "engagement"
- `strategy_framework`: {
  "approach": "balanced",
  "focus_areas": [],
  "constraint_boundaries": {}
}

### Error Handler Agent
- `next_agent`: "mentor"
- `error_handling_result`: {
  "error_handled": true,
  "recovery_action": "restart"
}

### Orchestrator Agent
- `next_agent`: "mentor"
- `routing_decision`: {
  "selected_agent": "mentor",
  "reasoning": "Initial routing to mentor agent."
}

## Best Practices

1. **Always restore original methods**: When using `patch_agent_process_method`, always restore the original method in a finally block.

2. **Use with AgentTestRunner**: The `AgentTestRunner` class handles patching and restoration automatically when `patch_process=True`.

3. **Test both with and without patching**: Test your agents both with and without patching to ensure they work correctly in both scenarios.

4. **Add custom fields when needed**: You can add custom fields to the output data before or after calling `ensure_agent_output_structure`.

5. **Mock the profiler when needed**: If you need to test profiler interactions, create a mock profiler and pass it to the `AgentTestRunner`.

## Troubleshooting

### Common Issues

1. **Missing fields in tests**: If your tests are failing due to missing fields in agent output, use `ensure_agent_output_structure` or set `patch_process=True` in `AgentTestRunner.run_test()`.

2. **Method not restored**: If you're seeing strange behavior after tests, make sure you're properly restoring the original process method.

3. **Profiler errors**: If you're seeing errors related to the profiler, make sure you're properly initializing it or mocking it in your tests.

### Debugging Tips

1. **Print output structure**: Print the output structure before and after calling `ensure_agent_output_structure` to see what fields are being added.

2. **Check agent role**: Make sure you're using the correct agent role when calling `ensure_agent_output_structure`.

3. **Inspect patched method**: You can inspect the patched method by printing `agent_class.process.__name__` before and after patching.
