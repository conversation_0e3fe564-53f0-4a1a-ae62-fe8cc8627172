# Context-Linked Assessment Framework

## Overview

The Context-Linked Assessment Framework extends the benchmark evaluation framework to support evaluation criteria that are ALWAYS linked with Generic Situations and Comprehensive Context Variable ranges. This allows for more nuanced and appropriate evaluation of agent responses based on the complete contextual picture including user profile, environmental, and interaction factors.

**Objective**: Implement a context-linked assessment framework that ensures evaluation criteria are ALWAYS linked with Generic Situations and Comprehensive Context Variable ranges.

**Impact**:
- Evaluation criteria now adapt dynamically based on comprehensive context variables including user profile, environmental, and interaction factors.
- Supports personalized evaluation for different trust levels, emotional states, and environmental conditions.
- Enforces the principle that evaluation criteria must be contextually appropriate and consistently linked.
- Maintains backward compatibility with existing static templates and generic situations.
- Provides a foundation for more sophisticated AI evaluation systems that consider the complete contextual picture.

## Integration Status

- ✅ **Template Management**: Complete
- ✅ **Contextual Criteria Definition**: Complete
- ✅ **Range-based Adaptation**: Complete
- ✅ **Multi-variable Support**: Complete
- ✅ **Testing Framework**: Complete
- ✅ **Benchmark Integration**: Complete
- ✅ **Admin Interface**: Complete
- ✅ **Benchmark Runs Display**: Complete (NEW)
- ✅ **Multi-Range Combinations Visualization**: Complete (NEW)

## Key Features

- **Dynamic Criteria Adaptation**: Evaluation criteria automatically adjust based on contextual variables.
- **Multi-Variable Support**: Supports trust level, mood (valence/arousal), and environment (stress/time pressure).
- **Range-Based Configuration**: Define criteria for specific ranges of contextual variables.
- **Backward Compatibility**: Works alongside existing static evaluation templates.
- **Enhanced Database Model**: Extended `EvaluationCriteriaTemplate` with fields for workflow type, category, contextual criteria, variable ranges, and activation status.
- **Comprehensive Testing**: Includes dedicated test suites for template functionality and integration with benchmark execution.

## Supported Comprehensive Context Variables

### Trust Level (0-100)
- **0-39 (Foundation)**: Basic trust, requires simple, clear, reassuring criteria
- **40-69 (Expansion)**: Moderate trust, allows for encouraging and supportive criteria
- **70-100 (Integration)**: High trust, enables collaborative and empowering criteria

### Mood
- **Valence (-1.0 to 1.0)**: Emotional positivity/negativity
  - Negative values: Gentle, understanding, patient criteria
  - Positive values: Enthusiastic, energetic, positive criteria
- **Arousal (-1.0 to 1.0)**: Emotional activation level
  - Low values: Calming, relaxation-focused criteria
  - High values: Stimulating, dynamic criteria

### Environment
- **Stress Level (0-100)**: Current environmental stress
  - Low stress: Detailed, comprehensive criteria
  - High stress: Concise, essential-only criteria
- **Time Pressure (0-100)**: Urgency in current situation
  - Low pressure: Long-term, detailed criteria
  - High pressure: Quick, immediate criteria

## Database Model

The `EvaluationCriteriaTemplate` model has been enhanced with:

```python
class EvaluationCriteriaTemplate(models.Model):
    # ... existing fields ...

    workflow_type = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Specific workflow type this template targets"
    )
    category = models.CharField(
        max_length=50,
        default="custom",
        help_text="Template categorization (semantic, quality, phase, contextual, custom)"
    )
    contextual_criteria = models.JSONField(
        default=dict,
        help_text="Contextual evaluation criteria that vary based on variables"
    )
    variable_ranges = models.JSONField(
        default=dict,
        help_text="Defines the ranges for contextual variables this template supports"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Template activation status"
    )
    # Added database indexes for performance
    # Created migration: 0004_add_contextual_evaluation_fields
```

## Template Structure

### Basic Template
```json
{
  "name": "Basic Quality Assessment",
  "category": "quality",
  "criteria": {
    "Content": ["Relevance", "Accuracy"],
    "Tone": ["Appropriate", "Supportive"]
  }
}
```

### Contextual Template
```json
{
  "name": "Contextual Evaluation Template",
  "category": "contextual",
  "criteria": {
    "Content": ["Relevance", "Personalization"],
    "Tone": ["Appropriate", "Supportive"]
  },
  "contextual_criteria": {
    "trust_level": {
      "0-39": {
        "Tone": ["Simple", "Clear", "Reassuring"],
        "Content": ["Safe options", "Low-risk activities"]
      },
      "40-69": {
        "Tone": ["Encouraging", "Supportive"],
        "Content": ["Balanced options", "Some stretch goals"]
      },
      "70-100": {
        "Tone": ["Collaborative", "Empowering"],
        "Content": ["Ambitious goals", "Creative challenges"]
      }
    },
    "mood": {
      "valence": {
        "-1.0-0.0": {
          "Tone": ["Gentle", "Understanding", "Patient"]
        },
        "0.0-1.0": {
          "Tone": ["Enthusiastic", "Energetic", "Positive"]
        }
      }
    },
    "environment": {
      "stress_level": {
        "0-30": {
          "Approach": ["Detailed", "Comprehensive"]
        },
        "71-100": {
          "Approach": ["Concise", "Essential-only"]
        }
      }
    }
  },
  "variable_ranges": {
    "trust_level": {"min": 0, "max": 100},
    "mood": {
      "valence": {"min": -1.0, "max": 1.0},
      "arousal": {"min": -1.0, "max": 1.0}
    },
    "environment": {
      "stress_level": {"min": 0, "max": 100},
      "time_pressure": {"min": 0, "max": 100}
    }
  }
}
```

## Usage

### Getting Adapted Criteria

```python
from apps.main.models import EvaluationCriteriaTemplate

# Load template
template = EvaluationCriteriaTemplate.objects.get(name="Contextual Template")

# Define context
context = {
    "trust_level": 75,
    "mood": {"valence": 0.5, "arousal": -0.2},
    "environment": {"stress_level": 20, "time_pressure": 60}
}

# Get adapted criteria
adapted_criteria = template.get_criteria_for_context(context)
```

The system implements `get_criteria_for_context()` method to adapt criteria based on context. It includes range parsing for different variable types: Trust level (0-100, foundation/expansion/integration phases), Mood valence (-1.0 to 1.0, negative to positive), Mood arousal (-1.0 to 1.0, calm to excited), Environment stress (0-100, low to high stress), and Environment time pressure (0-100, relaxed to urgent). Criteria merging logic combines base and contextual criteria.

### Admin Interface

- @backend\static\admin\js\benchmark_management.js
- @backend\templates\admin_tools\benchmark_management.html

The enhanced admin interface now provides a comprehensive template management experience:

#### Template Modal Features
- **Tabbed Interface**: Organized into 5 intuitive tabs:
  - **Basic Info**: Template name, description, workflow type, category, and status
  - **Base Criteria**: Standard evaluation criteria that apply in all contexts
  - **Contextual Criteria**: Interactive builder for context-dependent criteria
  - **Variable Ranges**: Visual editor for defining supported variable ranges
  - **Preview**: Real-time testing and preview of contextual adaptations

#### Admin Interface Files and Integration

The Contextual Evaluation System's admin interface leverages several key files for its functionality, including those responsible for the integrated Contextual Help System.

- **`backend/static/admin/js/evaluation_template_modal.js`** (MODIFIED)
  - Main modal class handling high-level UI interactions, tab navigation, and modal lifecycle for the evaluation template. Integrates with the help system for tab-specific help refresh and help icon initialization.
- **`backend/static/admin/js/template_modal_utils.js`** (MODIFIED)
  - Provides business logic utilities, including API calls, form data handling, validation, and sample template generation. Also contains the comprehensive help content registry.
- **`backend/static/admin/js/help_system.js`** (NEW)
  - Core help system functionality, including help modal creation and management, click-outside-to-close behavior, and dynamic positioning relative to trigger elements.
- **`backend/static/admin/css/benchmark_management.css`** (MODIFIED)
  - Provides styling for the help modals, help icon positioning, hover effects, and responsive design for mobile devices, ensuring a consistent look and feel across the benchmark management interface.
- **`backend/templates/admin_tools/benchmark_management.html`** (MODIFIED)
  - Includes the `help_system.js` script and ensures proper loading order for dependencies, integrating the help system into the benchmark management page.

##### Help System Class Structure and Integration Points

The `HelpSystem` class is central to providing in-modal help within the evaluation template modal. Its structure and integration points are as follows:

```javascript
class HelpSystem {
    constructor()           // Initialize modal and event listeners
    createHelpModal()       // Create help modal DOM structure
    setupEventListeners()   // Handle clicks and keyboard events
    showHelp(helpId)        // Display help content for specific ID
    hideHelp()              // Close help modal with animation
    positionModal()         // Smart positioning relative to trigger
    addHelpIcon()           // Add help icon to specific element
    initializeHelpIcons()   // Initialize all help icons
    addContextualHelpIcons() // Add help icons for contextual sections
    addVariableSectionHelp() // Add variable-specific help icons
}
```

**Integration Points:**
1.  **Modal Initialization**: The help system is initialized when the evaluation template modal opens.
2.  **Tab Switching**: Help icons are refreshed when switching between tabs within the modal, ensuring context-aware help.
3.  **Content Registry**: Help content is stored and managed within `TemplateModalUtils`, allowing for dynamic loading based on context.
4.  **Event Handling**: The system manages click and keyboard events for displaying and dismissing help modals.

#### Enhanced Contextual Criteria Builder

**New Advanced Features:**
- **Extended Template System**: 7 comprehensive pre-built templates:
  - **Trust Level (Basic/Advanced)**: Simple and comprehensive trust-based adaptations
  - **Mood-Aware Support**: Emotional state-based adaptations for supportive interactions
  - **Stress-Adaptive**: Environment-based adaptations for different stress levels
  - **Coaching-Focused**: Professional coaching adaptations based on client readiness
  - **Therapeutic Support**: Mental health and wellness-focused adaptations
  - **Comprehensive**: Full-featured template covering all contextual variables
- **Template Preview System**: Hover preview with detailed descriptions, statistics, and variable counts before loading
- **Smart Suggestions Engine**: Context-aware suggestions for criteria based on variable types, ranges, and common patterns
- **Enhanced Guidance System**: Expandable help sections with detailed guidelines, examples, and best practices for each variable type
- **Real-time Coverage Analysis**: Visual progress bars with percentages, completion status, and intelligent recommendations
- **Interactive Range Visualization**: Clickable range segments with highlighting, tooltips, and automatic scrolling to corresponding criteria sections

**Enhanced User Experience:**
- **Visual Interface**: Interactive forms with enhanced color-coded range indicators, mood quadrants, and environment bars
- **Trust Level Ranges**: Enhanced visual gradient bars with interactive segments, hover effects, and phase indicators for Foundation (0-39), Expansion (40-69), Integration (70-100)
- **Mood Variables**: Advanced interactive mood space visualization with coordinate labels, quadrant highlighting, and click-to-select functionality
- **Environment Variables**: Enhanced visual indicators with interactive bar segments, stress level visualization, and time pressure indicators
- **Tag-based Input**: Improved visual criteria tags with removal buttons, character counting, and dimension-specific auto-suggestions
- **Coverage Analysis**: Enhanced real-time progress bars with color coding, percentage display, and overall completion summary
- **Range Actions**: Extended actions including add/copy/suggest/clear with visual feedback, confirmation dialogs, and undo support
- **Real-time Validation**: Advanced validation with detailed error messages, success indicators, and actionable suggestions
- **Clear All Function**: One-click clearing with confirmation dialog and undo capability
- **Enhanced Feedback**: Real-time character counting, criteria counting, completion indicators, and visual hierarchy improvements

#### Enhanced Variable Ranges Editor

**Recent Fixes (Latest Update):**
- **Fixed Tab Visibility Issue**: Resolved CSS and JavaScript conflicts that prevented the Variable Ranges tab from displaying properly
- **Improved Tab Switching Logic**: Enhanced the tab navigation system to properly handle dynamically created containers
- **Enhanced Container Management**: Fixed container creation and visibility management for better user experience
- **Real-time Validation**: Added immediate visual feedback for input validation with green/red border indicators
- **Debugging Support**: Added console logging for troubleshooting tab switching and container creation issues

**New Advanced Features:**
- **Extended Preset System**: 5 comprehensive configuration presets:
  - **Standard Ranges**: Balanced configuration suitable for most applications
  - **Extended Ranges**: Comprehensive configuration with detailed descriptions for research applications
  - **Minimal Setup**: Basic configuration for simple applications
  - **Research Configuration**: High-precision ranges for academic and research applications with normalized values
  - **Clinical Settings**: Configuration optimized for healthcare and therapeutic applications with clinical scales
- **Preset Preview System**: Detailed preview with variable counts, range statistics, and configuration descriptions before loading
- **Interactive Testing Interface**: Built-in testing functionality for validating range configurations with sample values and real-time feedback
- **Auto-Configuration System**: One-click optimal configuration for each variable type with intelligent defaults
- **Advanced Validation Engine**: Real-time validation with detailed error messages, suggestions, and conflict detection
- **Range Conflict Detection**: Automatic detection of overlapping, problematic, or invalid ranges with actionable recommendations

**Enhanced Visualizations:**
- **Trust Level**: Interactive gradient bar with test value input, phase calculation, and range highlighting
- **Mood Variables**: Enhanced circumplex model visualization with coordinate display, quadrant testing, and interactive mood space
- **Environment Variables**: Visual indicator bars with segment highlighting, interactive testing, and stress/time pressure visualization
- **Range Statistics**: Comprehensive summary with configuration coverage, validation status, and completion metrics

**Enhanced User Experience:**
- **Visual Range Displays**: Enhanced gradient bars for trust levels and advanced interactive mood space visualization
- **Interactive Inputs**: Advanced real-time validation with green/red border feedback, character counters, and input suggestions
- **Preset Configurations**: Extended preset options with detailed descriptions and one-click loading
- **Range Summary**: Dynamic overview grid with enhanced statistics, validation status, and configuration completeness
- **Input Validation**: Advanced real-time min/max validation with visual indicators, detailed help text, and error prevention
- **Nested Variables**: Enhanced support for complex variables like mood (valence/arousal) and environment with visual organization
- **Contextual Help**: Expandable guidance sections with detailed configuration guidelines, examples, and best practices
- **Character Counters**: Real-time character counting for descriptions with limits and visual feedback
- **Suggestion System**: Quick-apply buttons for common range configurations (percentage, scale, normalized) with preview
- **Export/Import Ready**: Structured configuration for easy sharing, backup, and template distribution

#### Enhanced Context Preview & Testing
- **Interactive Context Simulator**: Real-time sliders for all contextual variables with live value displays
- **Real-time Adaptation**: Live preview of how criteria adapt to context changes with visual feedback
- **Preset User Scenarios**: Six predefined scenarios with one-click loading:
  - New User (low trust, cautious)
  - Stressed User (high stress, negative mood)
  - Confident User (high trust, positive mood)
  - Low Mood User (negative valence, low arousal)
  - Time Pressured (high urgency, focused)
  - Overwhelmed (high stress, low trust)
- **Context Analysis**: Automatic categorization showing trust phase, mood quadrant, stress level, and urgency
- **Visual Context Summary**: Color-coded display of current variable values and their interpretations
- **Enhanced Criteria Display**: Visual representation with criteria tags, counts, and color-coded dimensions

#### Advanced Features

**Technical Enhancements:**
- **Modular Architecture**: Split into focused JavaScript modules for better maintainability, testing, and code organization
- **JSON Synchronization**: Automatic bidirectional sync between visual builders and JSON fields with validation
- **Advanced Validation**: Real-time validation with detailed error messages, success indicators, and actionable suggestions
- **Visual Feedback Systems**: Enhanced progress bars, color coding, status messages, and completion indicators throughout the interface
- **Responsive Design**: Optimized layouts for various screen sizes with mobile-friendly controls and touch interactions
- **Accessibility**: Enhanced keyboard navigation, screen reader support, ARIA labels, and high contrast color schemes

**Recent Technical Fixes:**
- **Tab Container Management**: Fixed dynamic container creation and visibility handling in `evaluation_template_modal.js`
- **CSS Display Logic**: Resolved conflicts between form groups and custom tab content containers
- **Container Initialization**: Enhanced the variable ranges builder initialization to ensure proper DOM integration
- **Event Handling**: Improved event listener setup and cleanup for better performance and reliability
- **Error Prevention**: Added defensive programming patterns to handle missing DOM elements gracefully

**User Experience Improvements:**
- **Interactive Elements**: Clickable range segments, mood quadrants, and environment indicators with visual feedback
- **Smart Defaults**: Intelligent default values and suggestions based on variable types and common patterns
- **Drag-and-Drop Support**: Copy criteria between ranges with visual feedback and confirmation
- **Undo/Redo Capability**: Support for undoing actions and reverting changes with confirmation dialogs
- **Real-time Feedback**: Immediate visual feedback for all user interactions with status updates
- **Performance Optimization**: Efficient rendering for large datasets and optimized event handling

**Enhanced Validation System:**
- **Range Validation**: Comprehensive validation for overlapping ranges, invalid values, and configuration conflicts
- **Criteria Validation**: Real-time validation of criteria completeness, format, and consistency
- **Template Validation**: End-to-end validation of complete templates with detailed error reporting
- **Context Validation**: Validation of context variables and their compatibility with defined ranges
- **Export Validation**: Validation of configurations before export or sharing

#### JavaScript Architecture

The admin interface has been refactored into a modular architecture for better maintainability and functionality:

**Core Modules:**
- **`evaluation_template_modal.js`**: Main modal class handling high-level UI interactions, tab navigation, and modal lifecycle
- **`template_modal_utils.js`**: Business logic utilities including API calls, form data handling, validation, and sample template generation
- **`contextual_criteria_builder.js`**: Enhanced contextual criteria interface with visual builders, preset loading, and coverage analysis
- **`variable_ranges_builder.js`**: Enhanced variable ranges configuration with visual feedback and validation
- **`context_preview.js`**: Interactive preview and testing functionality with real-time context simulation

**Key Benefits:**
- **Separation of Concerns**: Each module has a focused responsibility
- **Reusability**: Components can be used independently or combined
- **Maintainability**: Easier to debug, test, and extend individual features
- **Performance**: Better code organization and potential for lazy loading
- **Testing**: Individual modules can be unit tested in isolation

**Module Dependencies:**
```
evaluation_template_modal.js (main)
├── template_modal_utils.js (utilities)
├── contextual_criteria_builder.js (contextual tab)
├── variable_ranges_builder.js (ranges tab)
└── context_preview.js (preview tab)
```

The admin interface improvements include new form fields for contextual criteria and variable ranges, enhanced template modal with JSON validation, and added "contextual" category option. It now integrates real API calls instead of mocked data.

## API Endpoints

- `GET /api/benchmark/templates/` - List all templates with filtering
- `GET /api/benchmark/templates/{id}/` - Get specific template details
- `POST /api/benchmark/templates/` - Create new template
- `PUT /api/benchmark/templates/{id}/` - Update existing template
- `DELETE /api/benchmark/templates/{id}/` - Delete template

The `EvaluationCriteriaTemplateView` has been updated to support new fields. The GET endpoint is enhanced with filtering by workflow_type, category, and status. POST/PUT endpoints now handle contextual criteria.

## Range Format

Ranges are specified as strings in the format:
- `"0-39"` for integer ranges
- `"0.0-1.0"` for float ranges
- `"-1.0-0.0"` for negative ranges

## Integration with Benchmark System

Contextual templates integrate seamlessly with the existing benchmark system:

1. **Scenario Definition**: Include context variables in scenario metadata
2. **Template Selection**: Choose contextual templates for evaluation
3. **Runtime Adaptation**: Criteria automatically adapt based on context
4. **Evaluation**: Standard evaluation process with adapted criteria

### Benchmark Scenario Integration

The `BenchmarkScenarioMetadata` schema now supports contextual evaluation:

```python
class BenchmarkScenarioMetadata(BenchmarkBaseModel):
    # ... existing fields ...

    # Contextual evaluation fields
    evaluation_template_id: Optional[int] = Field(
        None,
        description="ID of the EvaluationCriteriaTemplate to use for contextual evaluation"
    )
    context: Optional[Dict[str, Any]] = Field(
        None,
        description="Contextual variables for evaluation adaptation (trust_level, mood, environment)"
    )
```

### Benchmark Manager Integration

Both `AgentBenchmarker` and `WorkflowBenchmarker` now support contextual evaluation:

- **Template Loading**: Support for loading templates by ID or name
- **Context Processing**: Automatic adaptation of criteria based on scenario context
- **Fallback Support**: Graceful fallback to base criteria when context is unavailable
- **Backward Compatibility**: Existing scenarios continue to work without modification

The `AgentBenchmarker` and `WorkflowBenchmarker` have been enhanced with comprehensive contextual criteria adaptation methods: `_adapt_criteria_for_context` (main adaptation logic), `_get_range_adaptation` (finds criteria for value ranges), `_value_in_range` (handles range parsing), and `_merge_criteria` (merges base with contextual adaptations). Evaluation criteria loading prioritizes contextual templates, and support for trust level, mood (valence/arousal), and environment (stress/time pressure) adaptations is included.

## Template Testing Integration

The system now supports real-time testing of evaluation templates through the admin interface:

### Live Template Testing Features

1. **Direct Template Testing**: Templates can be tested without saving to the database by passing `evaluation_template_data` in benchmark parameters
2. **Context Variable Integration**: Test templates with specific context variables (trust level, mood, environment)
3. **Real Benchmark Execution**: Tests run actual benchmarks and store results in the database
4. **Comprehensive Results**: Returns detailed metrics including success rates, semantic scores, execution times, token usage, and costs

### Implementation Details

The `AgentBenchmarker.run_benchmark` method has been enhanced to support:

```python
# Template testing with direct data
benchmark_params = {
    'evaluation_template_data': {
        'name': 'Test Template',
        'criteria': {'Tone': ['supportive', 'clear']},
        'contextual_criteria': {...},
        'context_variables': {...}
    },
    'context_variables': {
        'trust_level': 75,
        'mood': {'valence': 0.5, 'arousal': 0.3},
        'environment': {'stress_level': 20, 'time_pressure': 30}
    },
    'semantic_evaluation': True,
    'runs': 3
}
```

### Key Findings

1. **Agent Role Constraints**: The `GenericAgent` model has strict constraints:
   - `role` field is unique and limited to predefined `AgentRole` choices
   - Maximum length of 20 characters for role field
   - Template testing uses existing agents matching the scenario's role

2. **BenchmarkRun Field Mappings**: The model uses different field names than expected:
   - No `success` field - calculated from `success_rate >= 0.5`
   - `execution_time` → `mean_duration` (converted from ms to seconds)
   - `cost` → `estimated_cost` (DecimalField)
   - `token_usage` is an integer, not a detailed object

3. **Context Variable Priority**: Context variables are loaded from:
   - `benchmark_params.get('context_variables')` (highest priority)
   - `scenario.metadata.get('context')` (fallback)

4. **Template Data Structure**: Evaluation template data supports both saved templates (by ID/name) and direct template data for testing unsaved configurations.

**Technical Implementation Details**:
- **Range Format Support**: Handles "0-39", "40-69", "70-100" for integers and "-1.0-0.0", "0.0-1.0" for floats.
- **Context Variables**: `trust_level` (0-100), `mood.valence` (-1.0 to 1.0), `mood.arousal` (-1.0 to 1.0), `environment.stress_level` (0-100), `environment.time_pressure` (0-100).
- **Criteria Merging**: Extends existing dimensions and adds new ones from contextual adaptations.
- **Error Handling**: Graceful fallback to base criteria when context or adaptations are unavailable.

## Recent Enhancements (Latest Update)

### Benchmark Runs Display with Evaluation Variables

**Overview**: Enhanced the benchmark runs table to display evaluation variables as individual columns, providing immediate visibility into the contextual parameters used for each benchmark execution.

**Implementation Files**:
- `backend/templates/admin_tools/benchmark_management.html` - Added 5 new table columns
- `backend/apps/admin_tools/views.py` - Enhanced API to extract evaluation variables
- `backend/static/admin/js/benchmark_management.js` - Updated table rendering logic

**Features**:
- **Individual Variable Columns**: Dedicated columns for Trust Level, Valence, Arousal, Stress Level, and Time Pressure
- **Smart Data Extraction**: Automatically extracts variables from `parameters.context_variables` field
- **Proper Formatting**: Displays numeric values with appropriate precision (e.g., valence/arousal to 1 decimal place)
- **Fallback Handling**: Shows "N/A" for runs without context variables
- **Backward Compatibility**: Works with existing benchmark runs that don't have contextual data

**Data Flow**:
1. **Storage**: Context variables stored in `BenchmarkRun.parameters.context_variables`
2. **API Extraction**: `BenchmarkRunView.get()` extracts variables from nested JSON structure
3. **Frontend Display**: JavaScript renders variables in dedicated table columns
4. **User Experience**: Immediate visibility of evaluation context for each run

### Multi-Range Combinations Visualization

**Overview**: Enhanced the context preview system to display all possible combinations when Multi-Range Contextual Evaluation is enabled, using a color-coded table format for better visualization and analysis.

**Implementation Files**:
- `backend/static/admin/js/context_preview.js` - Enhanced combinations rendering
- `backend/static/admin/css/benchmark_management.css` - Added color-coded styling

**Features**:
- **Comprehensive Display**: Shows ALL combinations without limiting to first 20
- **Table Format**: Clean, organized table with one column per variable
- **Color-Coded Values**: Meaningful colors for different variable ranges:
  - **Trust Level**: Foundation (yellow), Expansion (blue), Integration (green)
  - **Valence**: Negative (red), Positive (green)
  - **Arousal**: Calm (gray), Excited (orange/pink)
  - **Stress Level**: Low (green), Medium (yellow), High (red)
  - **Time Pressure**: Relaxed (green), Moderate (yellow), Urgent (red)
- **Interactive Elements**: Hover effects and tooltips showing range descriptions
- **Sticky Headers**: Table headers remain visible during scrolling
- **Responsive Design**: Adapts to different screen sizes

**Color Coding System**:
```css
/* Trust Level Colors */
.trust-foundation    /* Yellow - Building basic trust */
.trust-expansion     /* Blue - Growing confidence */
.trust-integration   /* Green - Full collaboration */

/* Mood Colors */
.mood-negative       /* Red - Negative emotional state */
.mood-positive       /* Green - Positive emotional state */
.arousal-calm        /* Gray - Low energy state */
.arousal-excited     /* Orange/Pink - High energy state */

/* Environment Colors */
.stress-low          /* Green - Relaxed environment */
.stress-medium       /* Yellow - Moderate pressure */
.stress-high         /* Red - High stress situation */
.pressure-relaxed    /* Green - No time constraints */
.pressure-moderate   /* Yellow - Some urgency */
.pressure-urgent     /* Red - High time pressure */
```

**User Experience Improvements**:
- **Visual Clarity**: Immediate understanding of variable combinations through color coding
- **Complete Overview**: See all possible test scenarios at a glance
- **Efficient Analysis**: Quickly identify patterns and coverage gaps
- **Scrollable Container**: Handle large numbers of combinations without overwhelming the UI
- **Contextual Tooltips**: Hover to see detailed range descriptions

**Technical Implementation**:
- **Dynamic Generation**: Combinations generated based on template variable ranges
- **Standard Range Mapping**: Consistent range definitions across all variable types
- **Efficient Rendering**: Optimized table rendering for large datasets
- **Memory Management**: Efficient handling of combination calculations
- **Real-time Updates**: Automatically refreshes when template data changes

### Integration Benefits

**Enhanced Workflow Analysis**:
- **Historical Context**: View evaluation variables for past benchmark runs
- **Pattern Recognition**: Identify trends and correlations in contextual testing
- **Coverage Analysis**: Ensure comprehensive testing across all variable combinations
- **Quality Assurance**: Verify that contextual adaptations are working as expected

**Improved User Experience**:
- **Visual Feedback**: Immediate understanding of test scenarios through color coding
- **Complete Transparency**: Full visibility into evaluation context and combinations
- **Efficient Navigation**: Quick identification of specific test conditions
- **Data-Driven Decisions**: Better insights for template optimization and testing strategy

**Development and Testing Benefits**:
- **Debugging Support**: Easy identification of problematic variable combinations
- **Template Validation**: Visual confirmation that all ranges are properly covered
- **Performance Monitoring**: Track evaluation performance across different contexts
- **Documentation**: Visual representation serves as documentation for test coverage

## Usage Examples

### Creating a New Contextual Template

1. **Open Template Modal**: Click "Create Template" in the benchmark management interface
2. **Basic Info Tab**:
   - Enter template name: "Adaptive Wheel Generation"
   - Set description: "Context-aware evaluation for wheel generation workflows"
   - Select workflow type: "wheel_generation"
   - Choose category: "contextual"
3. **Base Criteria Tab**: Define standard criteria that apply in all contexts
4. **Contextual Criteria Tab**:
   - Use the visual builder to define trust level adaptations
   - Click "Load Sample" for quick setup
   - Customize criteria for each range
5. **Variable Ranges Tab**: Configure supported variable ranges
6. **Preview Tab**: Test with different scenarios using preset buttons
7. **Save**: Template is automatically validated and saved

### Testing Template Adaptations

1. **Open Existing Template**: Click "Edit" on any contextual template
2. **Navigate to Preview Tab**: Use the context simulator
3. **Adjust Variables**: Move sliders to test different contexts
4. **Use Presets**: Click preset scenario buttons for quick testing
5. **Review Results**: Check adapted criteria display for expected behavior

### Integrating with Benchmarks

```python
# In benchmark scenario metadata
{
    "evaluation_template_id": 123,
    "context": {
        "trust_level": 65,
        "mood": {"valence": 0.3, "arousal": -0.1},
        "environment": {"stress_level": 40, "time_pressure": 30}
    }
}
```

## Best Practices

### Template Design
1. **Start Simple**: Begin with basic contextual adaptations before adding complexity
2. **User-Centric**: Design adaptations based on user research and behavioral patterns
3. **Consistent Ranges**: Use standard ranges across templates for consistency
4. **Clear Descriptions**: Provide meaningful descriptions for all variables

### Criteria Definition
1. **Gradual Transitions**: Ensure smooth transitions between ranges
2. **Complementary Criteria**: Design criteria that work well together
3. **Avoid Conflicts**: Ensure criteria don't contradict each other
4. **Measurable Outcomes**: Define criteria that can be objectively evaluated

### Testing and Validation
1. **Use Preview Feature**: Always test templates with the preview simulator
2. **Test Edge Cases**: Check behavior at range boundaries
3. **Validate with Real Data**: Test with actual user scenarios
4. **Regular Review**: Periodically review and update templates based on results

### Performance Optimization
1. **Minimal Complexity**: Keep contextual logic as simple as possible
2. **Cache Results**: Consider caching adapted criteria for repeated contexts
3. **Efficient Ranges**: Use ranges that align with natural user behavior patterns
4. **Monitor Usage**: Track which adaptations are most frequently used

## Troubleshooting

### Variable Ranges Tab Not Showing

If the Variable Ranges tab is not visible or not functioning properly:

1. **Check Browser Console**: Open browser developer tools and check for JavaScript errors
2. **Verify Module Loading**: Ensure all JavaScript modules are loaded in the correct order:
   - `template_modal_utils.js`
   - `contextual_criteria_builder.js`
   - `variable_ranges_builder.js`
   - `context_preview.js`
   - `evaluation_template_modal.js`
3. **Clear Browser Cache**: Hard refresh the page (Ctrl+F5) to ensure latest JavaScript is loaded
4. **Check Container Creation**: Look for console messages about container creation and tab switching
5. **Verify CSS**: Ensure the CSS file `benchmark_management.css` is loaded and contains the latest styles

### Common Issues and Solutions

- **Tab Content Not Switching**: Check that the `switchTab` method is properly handling custom containers
- **Container Not Created**: Verify that the `VariableRangesBuilder` constructor is being called
- **Styling Issues**: Ensure CSS classes are properly applied and not overridden by other styles
- **Event Handlers Not Working**: Check that event listeners are properly attached after container creation

## Testing

Comprehensive tests are available in `apps/main/tests/test_contextual_evaluation_templates.py` and `test_contextual_evaluation_integration.py`.

```bash
docker exec backend-web-1 python manage.py test apps.main.tests.test_contextual_evaluation_templates
```

### `test_contextual_evaluation_integration.py`

This test module focuses on the end-to-end integration of the contextual evaluation templates with the benchmark execution system. It ensures that evaluation criteria are correctly adapted based on contextual variables during actual benchmark runs.

**Key Tests and Functionality:**

- **`contextual_template` fixture**: Asynchronously creates a `Test Contextual Template` in the database, pre-populating it with various contextual criteria for trust level, mood (valence), and environment (stress level). This template serves as the basis for integration tests.
- **`test_criteria_adaptation_high_trust`**: Verifies that when a high trust context (e.g., `trust_level: 85`) is provided, the `AgentBenchmarker` correctly adapts the criteria to include "Collaborative", "Empowering", "Ambitious goals", and "Creative challenges" for Tone and Content, along with positive mood and low stress adaptations.
- **`test_criteria_adaptation_low_trust`**: Confirms that for a low trust context (e.g., `trust_level: 25`), the criteria are adapted to include "Simple", "Clear", "Reassuring", "Safe options", and "Low-risk activities" for Tone and Content, along with negative mood and high stress adaptations.
- **`test_range_parsing`**: A utility test that validates the `_value_in_range` method of the `AgentBenchmarker`, ensuring it correctly parses and evaluates both integer and float ranges, including edge cases.
- **`test_criteria_merging`**: Verifies the `_merge_criteria` logic, ensuring that base criteria are correctly extended with adaptations and new dimensions are added as expected.

This module is crucial for confirming the functional correctness of the contextual evaluation system within the broader benchmark framework.

## Documentation

### 📚 **Comprehensive Learning Guide**
**[Mastering Contextual AI Evaluation: A Complete Guide to Intelligent Benchmarking](../user-guide/CONTEXTUAL_EVALUATION_MASTERY_GUIDE.md)**

*The definitive learning resource for mastering contextual evaluation from beginner to expert level.*

**What's Included:**
- **Foundation & Psychology**: Understanding user context and AI interaction patterns
- **Hands-On Tutorials**: Step-by-step creation of sophisticated evaluation templates
- **AI-Assisted Workflows**: Leveraging AI to generate meaningful benchmark scenarios
- **Real-World Case Studies**: Mental health, education, and corporate coaching applications
- **Advanced Patterns**: Complex evaluation architectures and scaling strategies
- **Integration & Future-Proofing**: Production deployment and evolution strategies

**Perfect For:**
- First-time users wanting comprehensive understanding
- AI researchers and evaluation specialists
- Product managers implementing contextual evaluation
- Anyone seeking to create truly intelligent benchmarking systems

### 🔧 **Technical References**
- **Getting Started**: Basic setup and first template creation (this document)
- **Advanced Patterns**: Complex evaluation scenarios and best practices
- **Integration Guide**: Connecting with existing systems
- **API Reference**: Complete technical documentation

## User Profile Integration

The Contextual Evaluation System now integrates with a comprehensive User Profile Management system that provides realistic user data for benchmark testing:

### User Profile Features

1. **Profile Management Interface**: Dedicated tab in the benchmark management interface for creating and managing user profiles
2. **Profile Templates**: Predefined templates for common user archetypes:
   - **New User (Foundation Phase)**: Low trust (25), cautious approach, needs simple guidance
   - **Experienced User (Expansion Phase)**: Moderate trust (65), open to challenges, collaborative
   - **Confident User (Integration Phase)**: High trust (85), ready for complex tasks, empowering
   - **Stressed User**: Under pressure, needs quick solutions, concise communication

3. **Profile Components**:
   - **Trust Levels**: Numeric trust levels (0-100) with automatic phase calculation
   - **HEXACO Personality Traits**: Six major personality dimensions for realistic modeling
   - **Demographics**: Age, gender, location, occupation for context
   - **Preferences**: Activity types, communication styles, interests, risk tolerance
   - **Mock Tool Responses**: Configurable responses for tools like `get_user_profile`

### Integration with Contextual Evaluation

1. **Automatic Context Population**: User profiles automatically populate context variables for evaluation
2. **Scenario Integration**: Select user profiles when creating benchmark scenarios
3. **Dynamic Evaluation Adaptation**: Evaluation criteria adapt based on selected user profile characteristics

## Future Enhancements

- **Add machine learning to optimize criteria adaptations**
- **Implement real-time context detection**
- **Extend to additional contextual variables**
- **Dynamic Range Adjustment**: Automatically adjust ranges based on usage patterns
- **Multi-Template Composition**: Combine multiple contextual templates
- **Performance Optimization**: Add caching for criteria adaptation, optimize database queries for template retrieval, monitor performance impact of contextual logic.
- **Profile Analytics**: Track profile usage patterns and effectiveness in different scenarios
- **Adaptive Profiles**: Profiles that evolve based on interaction history and feedback

## Critical Bug Fix: Token Leak in Multi-Range Evaluation (December 2024)

### Issue Discovered

A critical token leak was discovered in the contextual evaluation system that caused massive token consumption and cost overruns:

**Problem**: When users selected specific combinations for testing (e.g., 3 out of 108 available combinations), the backend ignored the selection and tested ALL combinations, leading to:
- 36x more token usage than expected (108 vs 3 combinations)
- Significant cost overruns for users
- System becoming unresponsive due to excessive LLM calls

**Root Cause**: The `_handle_template_test` method in `BenchmarkRunView` was not processing the `selected_combinations` parameter sent by the frontend.

### Fix Implementation

**Backend Changes** (`backend/apps/admin_tools/views.py`):
```python
# Extract selected combinations from request
selected_combinations = params.get('selected_combinations', [])

# Use selected combinations if provided, otherwise generate all
if selected_combinations:
    context_combinations = selected_combinations
    logger.info(f"Multi-range evaluation enabled: using {len(context_combinations)} selected combinations")
else:
    context_combinations = manager._generate_context_combinations(template_data)
    logger.info(f"Multi-range evaluation enabled: generating {len(context_combinations)} context combinations")
```

**Frontend Enhancement** (`backend/static/admin/js/context_preview.js`):
- Added stop button functionality to terminate running benchmarks
- Improved task tracking with `currentTaskId` property
- Enhanced UI feedback during benchmark execution

**Stop Button Implementation**:
- New `BenchmarkRunStopView` class for stopping Celery tasks
- URL endpoint: `/admin/benchmarks/api/run/<task_id>/stop/`
- Uses Celery's `control.revoke(task_id, terminate=True)` to stop tasks

### Testing and Verification

Created comprehensive test suite (`backend/test_token_leak_fix.py`) that verifies:
1. Selected combinations are properly respected (✅ PASSED)
2. Only selected combinations are tested, not all combinations (✅ PASSED)
3. Stop button functionality works correctly (✅ PASSED)

**Test Results**:
```
🎉 ALL TESTS PASSED!
✅ Token leak fix is working correctly
✅ Stop button functionality is implemented
✅ Backend now respects selected combinations
```

### Impact and Prevention

**Before Fix**:
- Testing 3 selected combinations → 108 combinations tested
- ~$10-50 in unexpected costs per test session
- Users had to kill Docker containers to stop runaway processes

**After Fix**:
- Testing 3 selected combinations → exactly 3 combinations tested
- Predictable costs matching user expectations
- Stop button provides graceful termination

**Prevention Measures**:
- Added comprehensive logging for combination selection
- Implemented proper parameter validation
- Added stop functionality for emergency termination
- Created automated tests to prevent regression

### Final Solution: Celery Task Architecture

**Root Cause Analysis**:
The token leak occurred in **two places**:
1. **Template Testing View** (`BenchmarkRunView._handle_template_test`) - ignored selected combinations
2. **Benchmark Manager** (`AgentBenchmarker.run_benchmark`) - generated all combinations during semantic evaluation

**Complete Fix Implementation**:

1. **Replaced Synchronous Execution with Celery Tasks**:
   - Created new `run_template_test` Celery task in `backend/apps/main/tasks/benchmark_tasks.py`
   - Template testing now runs asynchronously, preventing UI blocking and enabling proper cancellation
   - Added task progress tracking with real-time updates

2. **Fixed Both Token Leak Sources**:
   - **Template Test**: Now uses Celery task that respects selected combinations
   - **Benchmark Manager**: Modified to use `selected_combinations` parameter during semantic evaluation

3. **Enhanced Stop Functionality**:
   - Added `BenchmarkRunStopView` for terminating Celery tasks
   - Added `BenchmarkTaskStatusView` for polling task status
   - Frontend now properly tracks `currentTaskId` and can stop running tests

4. **Improved Frontend Experience**:
   - Real-time progress updates from Celery task metadata
   - Proper error handling and user feedback
   - Stop button appears during test execution

**Technical Implementation Details**:

```python
# NEW: Celery Task for Template Testing
@shared_task(bind=True, name='apps.main.tasks.benchmark_tasks.run_template_test')
def run_template_test(self, scenario_id: str, template_data: Dict[str, Any], params: Dict[str, Any] = None):
    # Respects selected_combinations parameter
    selected_combinations = params.get('selected_combinations', [])
    if multi_range_evaluation and selected_combinations:
        # Only test selected combinations, not all combinations
        for context_combo in selected_combinations:
            # Run benchmark for this specific combination only
```

```python
# FIXED: Benchmark Manager Semantic Evaluation
if multi_range_evaluation and evaluation_template and evaluation_template.get('contextual_criteria'):
    # Check if selected combinations are provided in benchmark params
    selected_combinations = benchmark_params.get('selected_combinations', [])
    if selected_combinations:
        context_combinations = selected_combinations  # Use selected only
    else:
        context_combinations = self._generate_context_combinations(evaluation_template)  # Generate all
```

**API Endpoints Added**:
- `POST /admin/benchmarks/api/run/<task_id>/stop/` - Stop running task
- `GET /admin/benchmarks/api/task/<task_id>/status/` - Check task status

**Test Results**:
```
🎉 ALL TESTS PASSED!
✅ Template test now uses Celery tasks (no more token leaks)
✅ Stop button functionality is implemented
✅ Backend properly handles selected combinations in Celery task
```

**Impact Verification**:
- **Before**: 3 selected combinations → 108+ combinations tested → $10-50 cost
- **After**: 3 selected combinations → exactly 3 combinations tested → predictable cost
- **Stop Button**: Graceful task termination without killing Docker containers
