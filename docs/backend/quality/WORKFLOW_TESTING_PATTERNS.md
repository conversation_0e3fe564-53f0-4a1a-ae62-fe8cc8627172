# Testing Patterns for Workflow Benchmarking

This document provides guidance on testing patterns for the workflow benchmarking system, focusing on async testing, mocking, and best practices.

## Table of Contents
- [Testing Philosophy](#testing-philosophy)
- [Testing Async Code](#testing-async-code)
- [Mocking Async Methods](#mocking-async-methods)
- [Testing Database Operations](#testing-database-operations)
- [Testing WebSocket Communication](#testing-websocket-communication)
- [Testing Workflow Benchmarks](#testing-workflow-benchmarks)
- [Common Testing Patterns](#common-testing-patterns)
- [Best Practices](#best-practices)

## Testing Philosophy

The workflow benchmarking system follows these principles for testing:

1. **Comprehensive Coverage**: Test all critical components and edge cases.
2. **Isolation**: Tests should be isolated from each other and from external dependencies.
3. **Realistic Scenarios**: Use realistic test scenarios that mimic production usage.
4. **Performance Awareness**: Be mindful of test performance, especially for async tests.
5. **Maintainability**: Write clear, maintainable tests that are easy to understand and update.

## Testing Async Code

Testing async code requires special patterns to ensure that async functions are properly executed and tested.

### Using pytest.mark.asyncio

```python
import pytest

@pytest.mark.asyncio
async def test_async_function():
    """Test an async function."""
    # Arrange
    input_data = {"key": "value"}
    
    # Act
    result = await async_function(input_data)
    
    # Assert
    assert result["status"] == "success"
    assert "data" in result
```

### Using asyncio.run for Sync Test Functions

```python
import asyncio
import pytest

def test_async_function_from_sync():
    """Test an async function from a sync test function."""
    # Arrange
    input_data = {"key": "value"}
    
    # Act
    result = asyncio.run(async_function(input_data))
    
    # Assert
    assert result["status"] == "success"
    assert "data" in result
```

### Testing Async Context Managers

```python
import pytest

@pytest.mark.asyncio
async def test_async_context_manager():
    """Test an async context manager."""
    # Arrange
    input_data = {"key": "value"}
    
    # Act
    async with AsyncContextManager() as manager:
        result = await manager.process(input_data)
    
    # Assert
    assert result["status"] == "success"
    assert "data" in result
```

### Testing Async Iterators

```python
import pytest

@pytest.mark.asyncio
async def test_async_iterator():
    """Test an async iterator."""
    # Arrange
    iterator = AsyncIterator(items=["item1", "item2", "item3"])
    results = []
    
    # Act
    async for item in iterator:
        results.append(item)
    
    # Assert
    assert len(results) == 3
    assert results == ["item1", "item2", "item3"]
```

## Mocking Async Methods

Mocking async methods requires special patterns to ensure that the mocks behave like real async functions.

### Using AsyncMock

```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.asyncio
async def test_function_with_async_dependency():
    """Test a function that depends on an async function."""
    # Arrange
    mock_dependency = AsyncMock()
    mock_dependency.return_value = {"status": "success", "data": "mocked_data"}
    
    # Act
    with patch("module.async_dependency", mock_dependency):
        result = await function_under_test()
    
    # Assert
    assert result["status"] == "success"
    assert result["data"] == "mocked_data"
    mock_dependency.assert_called_once()
```

### Verifying Async Method Calls

```python
import pytest
from unittest.mock import AsyncMock

@pytest.mark.asyncio
async def test_async_method_calls():
    """Test that async methods are called correctly."""
    # Arrange
    mock_client = AsyncMock()
    mock_client.fetch.return_value = {"status": "success", "data": "mocked_data"}
    
    # Act
    result = await function_under_test(client=mock_client)
    
    # Assert
    assert result["status"] == "success"
    mock_client.fetch.assert_called_once_with("endpoint", {"param": "value"})
    assert mock_client.fetch.await_count == 1  # Verify the method was awaited
    mock_client.fetch.assert_awaited_once_with("endpoint", {"param": "value"})  # Alternative syntax
```

### Mocking Async Methods with Side Effects

```python
import pytest
from unittest.mock import AsyncMock

@pytest.mark.asyncio
async def test_async_method_with_side_effect():
    """Test an async method with a side effect."""
    # Arrange
    async def side_effect(arg):
        if arg == "valid":
            return {"status": "success", "data": "valid_data"}
        else:
            raise ValueError("Invalid argument")
    
    mock_client = AsyncMock()
    mock_client.fetch.side_effect = side_effect
    
    # Act & Assert - Success case
    result = await function_under_test(client=mock_client, arg="valid")
    assert result["status"] == "success"
    
    # Act & Assert - Error case
    with pytest.raises(ValueError, match="Invalid argument"):
        await function_under_test(client=mock_client, arg="invalid")
```

### Mocking Async Context Managers

```python
import pytest
from unittest.mock import AsyncMock

class MockAsyncContextManager:
    """Mock async context manager for testing."""
    
    def __init__(self, return_value=None):
        """Initialize the mock context manager."""
        self.return_value = return_value
        self.enter_called = False
        self.exit_called = False
    
    async def __aenter__(self):
        """Enter the context manager."""
        self.enter_called = True
        return self.return_value
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager."""
        self.exit_called = True
        return False  # Don't suppress exceptions

@pytest.mark.asyncio
async def test_async_context_manager_usage():
    """Test a function that uses an async context manager."""
    # Arrange
    mock_context_manager = MockAsyncContextManager(return_value={"status": "success"})
    
    # Act
    async with mock_context_manager as context:
        result = context
    
    # Assert
    assert result["status"] == "success"
    assert mock_context_manager.enter_called
    assert mock_context_manager.exit_called
```

## Testing Database Operations

Testing database operations in async code requires special patterns to ensure that the database is properly set up and cleaned up.

### Using pytest.mark.django_db

```python
import pytest
from apps.main.models import BenchmarkScenario

@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_database_operation():
    """Test a database operation in async code."""
    # Arrange
    scenario = await sync_to_async(BenchmarkScenario.objects.create)(
        name="Test Scenario",
        description="Test scenario for database operation",
        agent_role="test",
        input_data={"key": "value"},
        metadata={"workflow_type": "test_workflow"}
    )
    
    # Act
    result = await get_scenario_async(scenario.id)
    
    # Assert
    assert result is not None
    assert result.name == "Test Scenario"
    assert result.metadata["workflow_type"] == "test_workflow"
```

### Mocking Database Operations

```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.asyncio
async def test_function_with_database_dependency():
    """Test a function that depends on a database operation."""
    # Arrange
    mock_get_scenario = AsyncMock()
    mock_get_scenario.return_value = {
        "id": "test-id",
        "name": "Test Scenario",
        "metadata": {"workflow_type": "test_workflow"}
    }
    
    # Act
    with patch("module.get_scenario_async", mock_get_scenario):
        result = await function_under_test("test-id")
    
    # Assert
    assert result["status"] == "success"
    mock_get_scenario.assert_called_once_with("test-id")
```

### Using Fixtures for Database Setup

```python
import pytest
from apps.main.models import BenchmarkScenario

@pytest.fixture
async def test_scenario():
    """Fixture to create a test scenario."""
    scenario = await sync_to_async(BenchmarkScenario.objects.create)(
        name="Test Scenario",
        description="Test scenario for database operation",
        agent_role="test",
        input_data={"key": "value"},
        metadata={"workflow_type": "test_workflow"}
    )
    yield scenario
    # Clean up
    await sync_to_async(scenario.delete)()

@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_database_operation_with_fixture(test_scenario):
    """Test a database operation with a fixture."""
    # Act
    result = await get_scenario_async(test_scenario.id)
    
    # Assert
    assert result is not None
    assert result.name == "Test Scenario"
    assert result.metadata["workflow_type"] == "test_workflow"
```

## Testing WebSocket Communication

Testing WebSocket communication requires special patterns to ensure that WebSocket messages are properly sent and received.

### Using channels.testing.WebsocketCommunicator

```python
import pytest
from channels.testing import WebsocketCommunicator
from apps.main.consumers import UserSessionConsumer

@pytest.mark.asyncio
async def test_websocket_communication():
    """Test WebSocket communication."""
    # Arrange
    communicator = WebsocketCommunicator(UserSessionConsumer.as_asgi(), "/ws/user/test-user/")
    connected, _ = await communicator.connect()
    assert connected
    
    # Act
    await communicator.send_json_to({"type": "benchmark_request", "scenario_id": "test-id"})
    
    # Assert
    response = await communicator.receive_json_from()
    assert response["type"] == "benchmark_progress"
    assert response["data"]["status"] == "started"
    
    # Clean up
    await communicator.disconnect()
```

### Mocking WebSocket Communication

```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.asyncio
async def test_function_with_websocket_dependency():
    """Test a function that depends on WebSocket communication."""
    # Arrange
    mock_emit_event = AsyncMock()
    
    # Act
    with patch("apps.main.services.event_service.EventService.emit_event", mock_emit_event):
        await function_under_test("test-id", "test-user")
    
    # Assert
    mock_emit_event.assert_called_once()
    args, kwargs = mock_emit_event.call_args
    assert kwargs["event_type"] == "benchmark_progress"
    assert kwargs["data"]["status"] == "started"
    assert kwargs["user_profile_id"] == "test-user"
```

### Testing Progress Reporting

```python
import pytest
from unittest.mock import AsyncMock

@pytest.mark.asyncio
async def test_progress_reporting():
    """Test progress reporting via WebSocket."""
    # Arrange
    progress_callback = AsyncMock()
    
    # Act
    await execute_benchmark("test-id", progress_callback=progress_callback)
    
    # Assert
    assert progress_callback.call_count >= 3  # At least 3 progress updates
    
    # Verify initial progress
    args, kwargs = progress_callback.call_args_list[0]
    assert kwargs["state"] == "PROGRESS"
    assert kwargs["meta"]["current"] == 0
    assert kwargs["meta"]["total"] == 100
    
    # Verify final progress
    args, kwargs = progress_callback.call_args_list[-1]
    assert kwargs["state"] == "SUCCESS"
    assert kwargs["meta"]["current"] == 100
    assert kwargs["meta"]["total"] == 100
```

## Testing Workflow Benchmarks

Testing workflow benchmarks requires special patterns to ensure that the benchmarks are properly executed and evaluated.

### Using MockWorkflow for Testing

```python
import pytest
from apps.main.testing.mock_workflow import MockWorkflow

@pytest.mark.asyncio
async def test_workflow_benchmark():
    """Test a workflow benchmark using MockWorkflow."""
    # Arrange
    workflow = MockWorkflow(
        workflow_type="test_workflow",
        stages=["init", "process", "complete"],
        stage_durations={"init": 0.1, "process": 0.5, "complete": 0.1},
        tool_calls={"get_user_profile": 1, "search_database": 2},
        input_tokens=100,
        output_tokens=50,
        success_rate=1.0,
        error_stages=[],
        output_data={"response": "This is a mock response."}
    )
    
    # Act
    result = await workflow.execute_benchmark(
        scenario_id="test-id",
        params={"semantic_evaluation": True},
        progress_callback=None,
        user_profile_id=None
    )
    
    # Assert
    assert result is not None
    assert result.mean_duration_ms > 0
    assert result.success_rate == 1.0
    assert result.tool_calls == 3  # Total tool calls
```

### Testing Semantic Evaluation

```python
import pytest
from unittest.mock import AsyncMock, patch
from apps.main.services.semantic_evaluator import SemanticEvaluator

@pytest.mark.asyncio
async def test_semantic_evaluation():
    """Test semantic evaluation in a workflow benchmark."""
    # Arrange
    mock_evaluator = AsyncMock(spec=SemanticEvaluator)
    mock_evaluator.evaluate_response.return_value = {
        "openai/gpt-3.5-turbo": {
            "dimensions": {
                "Clarity": {"score": 0.8, "reasoning": "Clear response"},
                "Helpfulness": {"score": 0.9, "reasoning": "Very helpful"}
            },
            "overall_score": 0.85,
            "overall_reasoning": "Good response overall"
        },
        "_meta": {
            "models_used": ["openai/gpt-3.5-turbo"],
            "primary_model": "openai/gpt-3.5-turbo",
            "errors": [],
            "criteria_dimensions": ["Clarity", "Helpfulness"]
        }
    }
    
    # Act
    with patch("apps.main.services.async_workflow_manager.SemanticEvaluator", return_value=mock_evaluator):
        workflow = MockWorkflow(
            workflow_type="test_workflow",
            output_data={"response": "This is a mock response."}
        )
        result = await workflow.execute_benchmark(
            scenario_id="test-id",
            params={"semantic_evaluation": True},
            progress_callback=None,
            user_profile_id=None
        )
    
    # Assert
    assert result is not None
    assert result.semantic_score == 0.85
    assert "Good response overall" in result.semantic_evaluation_details
    mock_evaluator.evaluate_response.assert_called_once()
```

### Testing Token Tracking

```python
import pytest
from unittest.mock import AsyncMock, patch
from apps.main.services.async_workflow_manager import TokenTracker

@pytest.mark.asyncio
async def test_token_tracking():
    """Test token tracking in a workflow benchmark."""
    # Arrange
    mock_token_tracker = AsyncMock(spec=TokenTracker)
    mock_token_tracker.input_tokens = 100
    mock_token_tracker.output_tokens = 50
    mock_token_tracker.get_token_usage_report.return_value = {
        "total": {"input": 100, "output": 50, "total": 150},
        "by_stage": {
            "init": {"input": 20, "output": 10, "total": 30},
            "process": {"input": 60, "output": 30, "total": 90},
            "complete": {"input": 20, "output": 10, "total": 30}
        },
        "by_model": {
            "openai/gpt-3.5-turbo": {"input": 100, "output": 50, "total": 150}
        }
    }
    mock_token_tracker.calculate_cost.return_value = 0.25
    mock_token_tracker.calculate_cost_by_model.return_value = {
        "openai/gpt-3.5-turbo": 0.25
    }
    mock_token_tracker.calculate_cost_by_stage.return_value = {
        "init": 0.05,
        "process": 0.15,
        "complete": 0.05
    }
    
    # Act
    with patch("apps.main.services.async_workflow_manager.TokenTracker", return_value=mock_token_tracker):
        workflow = MockWorkflow(
            workflow_type="test_workflow",
            output_data={"response": "This is a mock response."}
        )
        result = await workflow.execute_benchmark(
            scenario_id="test-id",
            params={
                "model_prices": {
                    "default": {"input": 0.001, "output": 0.002},
                    "openai/gpt-3.5-turbo": {"input": 0.001, "output": 0.002}
                }
            },
            progress_callback=None,
            user_profile_id=None
        )
    
    # Assert
    assert result is not None
    assert result.total_input_tokens == 100
    assert result.total_output_tokens == 50
    assert "token_usage_report" in result.raw_results
    assert "cost_report" in result.raw_results
    mock_token_tracker.record_usage.assert_called()
    mock_token_tracker.calculate_cost.assert_called_once()
    mock_token_tracker.calculate_cost_by_model.assert_called_once()
    mock_token_tracker.calculate_cost_by_stage.assert_called_once()
```

### Testing Tool Mocking

```python
import pytest
from unittest.mock import AsyncMock, patch
from apps.main.testing.mock_tool_registry import MockToolRegistry

@pytest.mark.asyncio
async def test_tool_mocking():
    """Test tool mocking in a workflow benchmark."""
    # Arrange
    mock_tool_registry = AsyncMock(spec=MockToolRegistry)
    mock_tool_registry.call_tool.side_effect = lambda tool_name, **kwargs: {
        "get_user_profile": {"id": "test-user", "name": "Test User"},
        "search_database": {"results": ["result1", "result2"]}
    }.get(tool_name, {"error": "Unknown tool"})
    mock_tool_registry.get_call_counts.return_value = {
        "get_user_profile": 1,
        "search_database": 2
    }
    
    # Act
    with patch("apps.main.services.async_workflow_manager.MockToolRegistry", return_value=mock_tool_registry):
        workflow = MockWorkflow(
            workflow_type="test_workflow",
            tool_calls={"get_user_profile": 1, "search_database": 2},
            output_data={"response": "This is a mock response."}
        )
        result = await workflow.execute_benchmark(
            scenario_id="test-id",
            params={},
            progress_callback=None,
            user_profile_id=None
        )
    
    # Assert
    assert result is not None
    assert result.tool_call_counts == {"get_user_profile": 1, "search_database": 2}
    assert result.tool_calls == 3  # Total tool calls
    mock_tool_registry.configure.assert_called_once()
    mock_tool_registry.get_call_counts.assert_called_once()
```

## Common Testing Patterns

### Pattern: Test Fixture Factory

```python
import pytest
import uuid
from apps.main.models import BenchmarkScenario, BenchmarkRun

@pytest.fixture
def create_test_scenario():
    """Factory fixture to create test scenarios."""
    scenarios = []
    
    def _create_scenario(name=None, workflow_type="test_workflow", **kwargs):
        """Create a test scenario with the given parameters."""
        name = name or f"Test Scenario {uuid.uuid4()}"
        scenario = BenchmarkScenario.objects.create(
            name=name,
            description=f"Test scenario for {workflow_type}",
            agent_role="workflow",
            input_data=kwargs.pop("input_data", {"key": "value"}),
            metadata={
                "workflow_type": workflow_type,
                "situation": {
                    "workflow_type": workflow_type,
                    "text": f"Test situation for {workflow_type}"
                },
                "expected_quality_criteria": {
                    "Clarity": ["Is the response clear?"],
                    "Helpfulness": ["Is the response helpful?"]
                },
                **kwargs
            },
            is_active=True
        )
        scenarios.append(scenario)
        return scenario
    
    yield _create_scenario
    
    # Clean up
    for scenario in scenarios:
        scenario.delete()
```

### Pattern: Test Data Generator

```python
import uuid
import random

def generate_test_data(workflow_type="test_workflow", num_stages=3, num_tools=2):
    """Generate test data for workflow benchmarking."""
    stages = [f"stage_{i}" for i in range(num_stages)]
    tools = [f"tool_{i}" for i in range(num_tools)]
    
    stage_durations = {stage: random.uniform(0.1, 1.0) for stage in stages}
    tool_calls = {tool: random.randint(1, 5) for tool in tools}
    
    return {
        "workflow_type": workflow_type,
        "stages": stages,
        "stage_durations": stage_durations,
        "tool_calls": tool_calls,
        "input_tokens": random.randint(50, 200),
        "output_tokens": random.randint(25, 100),
        "success_rate": random.uniform(0.8, 1.0),
        "error_stages": random.sample(stages, random.randint(0, min(1, num_stages - 1))),
        "output_data": {
            "response": f"This is a mock response for {workflow_type}.",
            "metadata": {
                "workflow_type": workflow_type,
                "stages": stages,
                "tools": tools
            }
        }
    }
```

### Pattern: Test Context Manager

```python
import contextlib

@contextlib.asynccontextmanager
async def mock_workflow_context(workflow_type="test_workflow", **kwargs):
    """Context manager for testing with a mock workflow."""
    # Set up
    workflow = MockWorkflow(workflow_type=workflow_type, **kwargs)
    
    try:
        # Yield the workflow
        yield workflow
    finally:
        # Clean up
        await workflow.cleanup()
```

### Pattern: Test Parametrization

```python
import pytest

@pytest.mark.parametrize("workflow_type,expected_stages", [
    ("wheel_generation", ["init", "generate_wheel", "complete"]),
    ("discussion", ["init", "process_message", "generate_response", "complete"]),
    ("activity", ["init", "select_activity", "complete"])
])
@pytest.mark.asyncio
async def test_workflow_stages(workflow_type, expected_stages):
    """Test that workflows have the expected stages."""
    # Arrange
    workflow = MockWorkflow(workflow_type=workflow_type)
    
    # Act
    result = await workflow.execute_benchmark(
        scenario_id="test-id",
        params={},
        progress_callback=None,
        user_profile_id=None
    )
    
    # Assert
    assert result is not None
    assert set(result.stage_timings.keys()) == set(expected_stages)
```

## Best Practices

1. **Use Async Test Functions**: Use `@pytest.mark.asyncio` for testing async functions.

2. **Mock Async Dependencies**: Use `AsyncMock` for mocking async dependencies.

3. **Verify Async Method Calls**: Use `assert_awaited_once_with` and `await_count` to verify async method calls.

4. **Use Fixtures for Setup and Teardown**: Use fixtures for setting up and tearing down test data.

5. **Test Edge Cases**: Test error handling, edge cases, and boundary conditions.

6. **Use Realistic Test Data**: Use realistic test data that mimics production usage.

7. **Test Performance Metrics**: Verify that performance metrics are correctly calculated and reported.

8. **Test Resource Cleanup**: Verify that resources are properly cleaned up, especially in async contexts.

9. **Use Parameterized Tests**: Use `@pytest.mark.parametrize` for testing multiple scenarios.

10. **Test Progress Reporting**: Verify that progress is correctly reported via WebSocket.

11. **Test Error Handling**: Verify that errors are properly caught, reported, and handled.

12. **Test Database Operations**: Use `@pytest.mark.django_db(transaction=True)` for testing database operations.

13. **Test WebSocket Communication**: Use `WebsocketCommunicator` for testing WebSocket communication.

14. **Test Token Tracking**: Verify that token usage is correctly tracked and reported.

15. **Test Semantic Evaluation**: Verify that semantic evaluation is correctly performed and reported.

16. **Test Tool Mocking**: Verify that tools are correctly mocked and tool calls are tracked.

17. **Test Workflow Execution**: Verify that workflows are correctly executed and results are properly stored.

18. **Test Result Aggregation**: Verify that results are correctly aggregated and statistics are calculated.

19. **Test Schema Validation**: Verify that schemas are correctly validated.

20. **Test Integration with Existing Systems**: Verify that the workflow benchmarking system integrates correctly with existing systems.
