# Async Patterns for Workflow Benchmarking

This document provides guidance on async patterns used in the workflow benchmarking system, focusing on database access, error handling, and best practices.

## Table of Contents
- [Database Access Patterns](#database-access-patterns)
- [Transaction Management](#transaction-management)
- [Avoiding Sync/Async Conflicts](#avoiding-syncasync-conflicts)
- [Context Management](#context-management)
- [Common Patterns](#common-patterns)
- [Troubleshooting](#troubleshooting)

## Database Access Patterns

The workflow benchmarking system uses Django's ORM for database access, which is synchronous by nature. To use Django's ORM in an async context, we use the `database_sync_to_async` decorator from Django Channels.

### Basic Pattern

```python
from asgiref.sync import sync_to_async
from django.db import transaction
from apps.main.models import BenchmarkScenario, BenchmarkRun

# Correct pattern: Define a separate sync function for database operations
async def get_scenario_async(scenario_id):
    """Get a benchmark scenario by ID."""
    return await sync_to_async(
        _get_scenario_sync,  # Pass the function, don't call it
        thread_sensitive=True  # Important for preventing race conditions
    )(scenario_id)  # Pass arguments to the sync function

def _get_scenario_sync(scenario_id):
    """Synchronous function to get a benchmark scenario."""
    try:
        return BenchmarkScenario.objects.get(id=scenario_id)
    except BenchmarkScenario.DoesNotExist:
        return None
```

### Important Rules

1. **Always use `thread_sensitive=True`**: This ensures that the database operation runs in the same thread, preventing race conditions and connection issues.

2. **Keep sync blocks minimal**: Only include necessary ORM operations in the sync function. Process the data in the async function.

3. **Use eager loading**: Use `select_related` and `prefetch_related` to minimize database queries.

4. **Pass the function, don't call it**: Pass the sync function to `sync_to_async`, not the result of calling it.

5. **Handle exceptions in the sync function**: Catch and handle database-specific exceptions in the sync function.

### Batch Operations

For operations involving multiple database queries, use a single sync function to avoid multiple sync-to-async transitions:

```python
async def get_related_data_async(scenario_id):
    """Get a scenario and its related runs."""
    return await sync_to_async(
        _get_related_data_sync,
        thread_sensitive=True
    )(scenario_id)

def _get_related_data_sync(scenario_id):
    """Get a scenario and its related runs in a single sync function."""
    try:
        scenario = BenchmarkScenario.objects.get(id=scenario_id)
        runs = BenchmarkRun.objects.filter(scenario=scenario).order_by('-execution_date')[:5]
        return {
            'scenario': scenario,
            'runs': list(runs)  # Convert QuerySet to list to avoid lazy loading issues
        }
    except BenchmarkScenario.DoesNotExist:
        return {'scenario': None, 'runs': []}
```

## Transaction Management

Transactions in async code require special handling to ensure atomicity and consistency.

### Using Transactions in Async Code

```python
async def create_benchmark_run_async(scenario, result):
    """Create a benchmark run with transaction support."""
    return await sync_to_async(
        _create_benchmark_run_sync,
        thread_sensitive=True
    )(scenario, result)

def _create_benchmark_run_sync(scenario, result):
    """Create a benchmark run in a transaction."""
    with transaction.atomic():
        # All operations in this block are part of a single transaction
        benchmark_run = BenchmarkRun.objects.create(
            scenario=scenario,
            mean_duration_ms=result.mean_duration * 1000,
            # ... other fields
        )
        
        # Additional operations that should be part of the same transaction
        # For example, creating related records
        
        return benchmark_run
```

### Important Rules for Transactions

1. **Keep the entire transaction in the sync function**: Don't split transaction operations across multiple sync functions.

2. **Use `with transaction.atomic()`**: This ensures that all operations in the block are part of a single transaction.

3. **Handle exceptions properly**: Catch and handle exceptions that might occur during the transaction.

4. **Be aware of savepoints**: Nested transactions create savepoints, which might not behave as expected in all cases.

## Avoiding Sync/Async Conflicts

Mixing sync and async code can lead to deadlocks, race conditions, and other issues. Here are some patterns to avoid these problems:

### Anti-Pattern: Calling Sync Code from Async

```python
# DON'T DO THIS
async def bad_example():
    # This will raise SynchronousOnlyOperation
    scenario = BenchmarkScenario.objects.get(id=scenario_id)
    return scenario
```

### Anti-Pattern: Calling Async Code from Sync

```python
# DON'T DO THIS
def another_bad_example():
    # This will block the thread and might cause deadlocks
    import asyncio
    scenario = asyncio.run(get_scenario_async(scenario_id))
    return scenario
```

### Anti-Pattern: Mixing Sync and Async in the Same Function

```python
# DON'T DO THIS
async def yet_another_bad_example(scenario_id):
    # This mixes sync and async operations in the same function
    scenario = await get_scenario_async(scenario_id)
    # This will raise SynchronousOnlyOperation
    runs = BenchmarkRun.objects.filter(scenario=scenario)
    return runs
```

### Correct Pattern: Separate Sync and Async Code

```python
# DO THIS
async def get_scenario_with_runs_async(scenario_id):
    """Get a scenario and its runs asynchronously."""
    return await sync_to_async(
        _get_scenario_with_runs_sync,
        thread_sensitive=True
    )(scenario_id)

def _get_scenario_with_runs_sync(scenario_id):
    """Get a scenario and its runs synchronously."""
    try:
        scenario = BenchmarkScenario.objects.get(id=scenario_id)
        runs = list(BenchmarkRun.objects.filter(scenario=scenario))
        return {'scenario': scenario, 'runs': runs}
    except BenchmarkScenario.DoesNotExist:
        return {'scenario': None, 'runs': []}
```

## Context Management

Proper context management is essential for async code to ensure resources are properly cleaned up.

### Using Async Context Managers

```python
async def execute_benchmark(scenario_id):
    """Execute a benchmark using async context managers."""
    async with AsyncLLMClient() as llm_client:
        # Use the LLM client within this context
        result = await llm_client.generate(prompt)
        
    # The client is automatically closed when exiting the context
    return result
```

### Creating Async Context Managers

```python
class AsyncResourceManager:
    """Example of an async context manager."""
    
    async def __aenter__(self):
        """Set up the resource asynchronously."""
        await self.setup_resource()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Clean up the resource asynchronously."""
        await self.cleanup_resource()
        
    async def setup_resource(self):
        """Set up the resource."""
        # Implementation
        
    async def cleanup_resource(self):
        """Clean up the resource."""
        # Implementation
```

## Common Patterns

### Pattern: Async Factory Method

```python
class WorkflowBenchmarkManager:
    """Workflow benchmark manager with async factory method."""
    
    def __init__(self, run_id):
        """Initialize the manager."""
        self.run_id = run_id
        self.token_tracker = None
        
    @classmethod
    async def create(cls, scenario_id):
        """Async factory method to create a manager instance."""
        # Get the scenario asynchronously
        scenario = await get_scenario_async(scenario_id)
        if not scenario:
            raise ValueError(f"Scenario {scenario_id} not found")
            
        # Create a benchmark run asynchronously
        run = await create_benchmark_run_async(scenario)
        
        # Create and initialize the manager
        manager = cls(run.id)
        await manager.initialize()
        
        return manager
        
    async def initialize(self):
        """Initialize the manager asynchronously."""
        self.token_tracker = TokenTracker(run_id=self.run_id)
        # Other async initialization
```

### Pattern: Async Iterator

```python
class AsyncBenchmarkIterator:
    """Async iterator for benchmark runs."""
    
    def __init__(self, scenario_id, num_runs):
        """Initialize the iterator."""
        self.scenario_id = scenario_id
        self.num_runs = num_runs
        self.current_run = 0
        
    def __aiter__(self):
        """Return the iterator."""
        return self
        
    async def __anext__(self):
        """Get the next benchmark run."""
        if self.current_run >= self.num_runs:
            raise StopAsyncIteration
            
        self.current_run += 1
        return await self.execute_run()
        
    async def execute_run(self):
        """Execute a single benchmark run."""
        # Implementation
```

### Pattern: Async Event Emitter

```python
class AsyncEventEmitter:
    """Async event emitter for benchmark progress."""
    
    def __init__(self):
        """Initialize the emitter."""
        self.listeners = {}
        
    async def emit(self, event, data):
        """Emit an event asynchronously."""
        if event in self.listeners:
            for listener in self.listeners[event]:
                await listener(data)
                
    def on(self, event, listener):
        """Register a listener for an event."""
        if event not in self.listeners:
            self.listeners[event] = []
        self.listeners[event].append(listener)
        
    def off(self, event, listener):
        """Remove a listener for an event."""
        if event in self.listeners and listener in self.listeners[event]:
            self.listeners[event].remove(listener)
```

## Troubleshooting

### Common Issues and Solutions

1. **`SynchronousOnlyOperation` Error**:
   - **Cause**: Calling synchronous Django ORM methods directly in an async function.
   - **Solution**: Use `sync_to_async` with `thread_sensitive=True`.

2. **Deadlocks**:
   - **Cause**: Mixing sync and async code, especially when using transactions.
   - **Solution**: Keep all database operations in sync functions, and use `sync_to_async` to call them from async functions.

3. **Connection Pool Exhaustion**:
   - **Cause**: Not properly closing database connections or creating too many connections.
   - **Solution**: Use context managers and ensure connections are properly closed.

4. **Race Conditions**:
   - **Cause**: Multiple async functions accessing the same database records concurrently.
   - **Solution**: Use transactions and locks to ensure atomicity and consistency.

5. **Memory Leaks**:
   - **Cause**: Not properly cleaning up resources in async code.
   - **Solution**: Use async context managers and ensure resources are properly cleaned up.

### Debugging Async Code

1. **Use `asyncio.create_task` with Names**:
   ```python
   task = asyncio.create_task(coro(), name="my_task")
   ```

2. **Use `asyncio.current_task()` to Identify Tasks**:
   ```python
   current_task = asyncio.current_task()
   print(f"Current task: {current_task.get_name()}")
   ```

3. **Use `asyncio.get_event_loop().get_debug()` to Enable Debug Mode**:
   ```python
   loop = asyncio.get_event_loop()
   loop.set_debug(True)
   ```

4. **Use `asyncio.get_event_loop().set_exception_handler()` to Handle Exceptions**:
   ```python
   def exception_handler(loop, context):
       print(f"Exception in async code: {context}")
       
   loop = asyncio.get_event_loop()
   loop.set_exception_handler(exception_handler)
   ```

5. **Use `await asyncio.sleep(0)` to Yield Control**:
   ```python
   async def long_running_task():
       for i in range(1000):
           # Do some work
           await asyncio.sleep(0)  # Yield control to other tasks
   ```
