# Schema Management for Generic Situation Benchmarking

This document provides guidance on schema management for the generic situation benchmarking system, focusing on schema validation, versioning, and migration.

## Table of Contents
- [Schema Management Overview](#schema-management-overview)
- [Schema Registry](#schema-registry)
- [Schema Validation](#schema-validation)
- [Schema Versioning](#schema-versioning)
- [Schema Migration](#schema-migration)
- [Creating and Registering Schemas](#creating-and-registering-schemas)
- [Validating Data Against Schemas](#validating-data-against-schemas)
- [Managing Schema Versions](#managing-schema-versions)
- [Migrating Data Between Schema Versions](#migrating-data-between-schema-versions)
- [Common Schema Management Patterns](#common-schema-management-patterns)
- [Best Practices](#best-practices)

## Schema Management Overview

The generic situation benchmarking system uses JSON Schema (Draft-07) to define and validate the structure of benchmark components. The schema management system consists of several key components:

1. **SchemaRegistry**: Central registry for JSON schemas loaded from `schemas/`.
2. **SchemaValidationService**: Service for validating benchmark components against JSON schemas.
3. **SchemaVersionManager**: Service for managing schema versions and migrations.
4. **SchemaMigrationUtility**: Utility for migrating data between schema versions.

These components work together to ensure that benchmark components adhere to standardized formats and can be migrated between schema versions as the system evolves.

## Schema Registry

The `SchemaRegistry` is the central component for managing JSON schemas. It loads schemas from the `schemas/` directory and provides methods for registering, retrieving, and validating against schemas.

### Key Features

- **Schema Loading**: Loads schemas from the `schemas/` directory.
- **Schema Registration**: Registers schemas with the registry.
- **Schema Retrieval**: Retrieves schemas by type and version.
- **Schema Validation**: Validates data against registered schemas.
- **Version Management**: Manages schema versions and selects the appropriate version for validation.

### Example Usage

```python
from apps.main.services.schema_registry import SchemaRegistry

# Create a schema registry
registry = SchemaRegistry()

# Load schemas from directory
registry.load_schemas_from_directory("schemas/")

# Register a schema
registry.register_schema("workflow_benchmark", workflow_benchmark_schema)

# Get a schema
schema = registry.get_schema("workflow_benchmark")

# Validate data against a schema
is_valid, errors = registry.validate("workflow_benchmark", data)
```

## Schema Validation

The `SchemaValidationService` provides high-level methods for validating benchmark components against JSON schemas. It uses the `SchemaRegistry` to retrieve schemas and validate data.

### Key Features

- **Component Validation**: Validates specific components like user profiles, situations, evaluation criteria, and tool expectations.
- **Scenario Validation**: Validates complete benchmark scenarios including all components.
- **Error Reporting**: Provides detailed error messages for validation failures.
- **Schema Retrieval**: Retrieves schemas for inspection and documentation.

### Example Usage

```python
from apps.main.services.schema_validator_service import SchemaValidationService

# Create a schema validation service
validator = SchemaValidationService()

# Validate a user profile
is_valid, errors = validator.validate_user_profile(user_profile)

# Validate a situation
is_valid, errors = validator.validate_situation(situation)

# Validate evaluation criteria
is_valid, errors = validator.validate_evaluation_criteria(evaluation_criteria)

# Validate tool expectations
is_valid, errors = validator.validate_tool_expectation(tool_expectation)

# Validate a workflow benchmark
is_valid, errors = validator.validate_workflow_benchmark(workflow_benchmark)

# Validate a complete benchmark scenario
validation_result = validator.validate_benchmark_scenario(scenario)
```

## Schema Versioning

The `SchemaVersionManager` provides support for managing schema versions and migrations between versions. It uses semantic versioning (semver) to track schema evolution.

### Key Features

- **Semantic Versioning**: Uses semver for schema versioning (major.minor.patch).
- **Version Extraction**: Extracts version information from schemas.
- **Migration Registration**: Registers migration functions between versions.
- **Data Migration**: Migrates data between schema versions.
- **Compatibility Checking**: Checks if data is compatible with a specific schema version.
- **Default Version Management**: Automatically selects the latest version as default.

### Example Usage

```python
from apps.main.services.schema_version_manager import SchemaVersionManager

# Create a schema version manager
version_manager = SchemaVersionManager()

# Register schema versions
version_manager.register_schema_version("workflow_benchmark", "1.0.0", schema_v1)
version_manager.register_schema_version("workflow_benchmark", "2.0.0", schema_v2)

# Register a migration function
def migrate_v1_to_v2(data):
    """Migrate data from v1 to v2."""
    result = data.copy()
    # Perform migration
    return result

version_manager.register_migration("workflow_benchmark", "1.0.0", "2.0.0", migrate_v1_to_v2)

# Validate data against a specific version
is_valid, errors = version_manager.validate_with_version("workflow_benchmark", data, "1.0.0")

# Migrate data between versions
migrated_data = version_manager.migrate_data("workflow_benchmark", data, "1.0.0", "2.0.0")

# Check compatibility
is_compatible, errors = version_manager.is_compatible("workflow_benchmark", data, "1.0.0", "2.0.0")
```

## Schema Migration

The `SchemaMigrationUtility` provides utilities for migrating data between schema versions. It includes methods for field manipulation, object merging, enum value updates, and array item updates.

### Key Features

- **Field Manipulation**: Add, remove, rename, and transform fields.
- **Object Merging**: Merge objects within data.
- **Enum Value Updates**: Update enum values in data.
- **Array Item Updates**: Update items in array fields.
- **Migration Chains**: Create chains of migrations for complex transformations.

### Example Usage

```python
from apps.main.services.schema_migration_utility import SchemaMigrationUtility

# Create a schema migration utility
migration_utility = SchemaMigrationUtility()

# Add a field
data = {"name": "Test"}
data = migration_utility.add_field(data, "description", "Test description")
# Result: {"name": "Test", "description": "Test description"}

# Remove a field
data = migration_utility.remove_field(data, "description")
# Result: {"name": "Test"}

# Rename a field
data = migration_utility.rename_field(data, "name", "title")
# Result: {"title": "Test"}

# Transform a field
data = migration_utility.transform_field(data, "title", lambda x: x.upper())
# Result: {"title": "TEST"}

# Merge objects
data = migration_utility.merge_objects(data, "", {"description": "Test description"})
# Result: {"title": "TEST", "description": "Test description"}

# Update enum values
data = {"status": "active"}
data = migration_utility.update_enum_values(data, "status", {"active": "enabled"})
# Result: {"status": "enabled"}

# Update array items
data = {"items": [{"id": 1}, {"id": 2}]}
data = migration_utility.update_array_items(data, "items", lambda x: {**x, "active": True})
# Result: {"items": [{"id": 1, "active": True}, {"id": 2, "active": True}]}

# Create a migration chain
def add_description(data):
    return migration_utility.add_field(data, "description", "Test description")

def capitalize_title(data):
    return migration_utility.transform_field(data, "title", lambda x: x.upper())

migration_chain = migration_utility.create_migration_chain([add_description, capitalize_title])
data = {"title": "Test"}
data = migration_chain(data)
# Result: {"title": "TEST", "description": "Test description"}
```

## Creating and Registering Schemas

### Creating a New Schema

Schemas are defined using JSON Schema (Draft-07) and stored in the `schemas/` directory. Each schema should include a `$schema` property, a `title`, a `description`, and a `$version` property.

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "$version": "1.0.0",
  "title": "Workflow Benchmark Schema",
  "description": "Schema for workflow benchmark scenarios",
  "type": "object",
  "required": ["workflow_type"],
  "properties": {
    "workflow_type": {
      "type": "string",
      "description": "Type of workflow being benchmarked",
      "enum": ["wheel_generation", "discussion", "activity"]
    },
    "warmup_runs": {
      "type": "integer",
      "description": "Number of warmup runs to perform",
      "default": 1,
      "minimum": 0
    },
    "benchmark_runs": {
      "type": "integer",
      "description": "Number of benchmark runs to perform",
      "default": 3,
      "minimum": 1
    },
    "mock_tool_responses": {
      "type": "object",
      "description": "Mock responses for tools",
      "additionalProperties": true
    },
    "mock_tool_validation": {
      "type": "object",
      "description": "Validation settings for mock tools",
      "properties": {
        "validate_schemas": {
          "type": "boolean",
          "description": "Whether to validate tool inputs and outputs against schemas",
          "default": false
        },
        "validate_params": {
          "type": "boolean",
          "description": "Whether to validate tool parameters",
          "default": false
        },
        "strict_mode": {
          "type": "boolean",
          "description": "Whether to fail on validation errors",
          "default": false
        }
      }
    },
    "expected_quality_criteria": {
      "type": "object",
      "description": "Criteria for evaluating the quality of the workflow output",
      "additionalProperties": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "evaluator_models": {
      "type": "array",
      "description": "LLM models to use for semantic evaluation",
      "items": {
        "type": "string"
      }
    },
    "expected_output": {
      "type": "object",
      "description": "Expected output from the workflow",
      "additionalProperties": true
    },
    "expected_stages": {
      "type": "array",
      "description": "Expected stages in the workflow",
      "items": {
        "type": "string"
      }
    },
    "expected_tool_calls": {
      "type": "object",
      "description": "Expected tool calls during workflow execution",
      "additionalProperties": {
        "type": "integer",
        "minimum": 0
      }
    },
    "timeout_seconds": {
      "type": "number",
      "description": "Timeout for workflow execution in seconds",
      "default": 300,
      "minimum": 0
    }
  }
}
```

### Registering a Schema

Schemas can be registered programmatically using the `SchemaRegistry` or loaded from the `schemas/` directory.

```python
from apps.main.services.schema_registry import SchemaRegistry

# Create a schema registry
registry = SchemaRegistry()

# Register a schema programmatically
registry.register_schema("workflow_benchmark", workflow_benchmark_schema, version="1.0.0")

# Load schemas from directory
registry.load_schemas_from_directory("schemas/")
```

### Using the Schema Seeding Command

The `seed_benchmark_schemas` management command can be used to initialize schema definitions and evaluation templates.

```bash
python manage.py seed_benchmark_schemas
```

This command:
1. Loads schemas from the `schemas/` directory.
2. Registers them with the `SchemaRegistry`.
3. Creates `EvaluationCriteriaTemplate` records from JSON files in the `evaluation_templates/` directory.

## Validating Data Against Schemas

### Using SchemaValidationService

The `SchemaValidationService` provides high-level methods for validating benchmark components against JSON schemas.

```python
from apps.main.services.schema_validator_service import SchemaValidationService

# Create a schema validation service
validator = SchemaValidationService()

# Validate a workflow benchmark
is_valid, errors = validator.validate_workflow_benchmark({
    "workflow_type": "wheel_generation",
    "warmup_runs": 1,
    "benchmark_runs": 3,
    "mock_tool_responses": {
        "get_user_profile": {"response": {"id": "test-user", "name": "Test User"}},
        "search_database": {"response": {"results": ["result1", "result2"]}}
    },
    "expected_quality_criteria": {
        "Clarity": ["Is the response clear?"],
        "Helpfulness": ["Is the response helpful?"]
    }
})

if not is_valid:
    print("Validation failed:", errors)
else:
    print("Validation succeeded")
```

### Validating a Complete Benchmark Scenario

The `validate_benchmark_scenario` method validates a complete benchmark scenario including all components.

```python
from apps.main.services.schema_validator_service import SchemaValidationService

# Create a schema validation service
validator = SchemaValidationService()

# Validate a complete benchmark scenario
validation_result = validator.validate_benchmark_scenario({
    "name": "Test Scenario",
    "description": "Test scenario for workflow benchmarking",
    "agent_role": "workflow",
    "input_data": {
        "user_profile_id": "test-user",
        "context_packet": {
            "task_type": "wheel_generation",
            "user_message": "Generate a wheel for me"
        }
    },
    "metadata": {
        "workflow_type": "wheel_generation",
        "situation": {
            "workflow_type": "wheel_generation",
            "text": "User requests a wheel generation",
            "context": "Testing wheel generation workflow"
        },
        "expected_quality_criteria": {
            "Clarity": ["Is the response clear?"],
            "Helpfulness": ["Is the response helpful?"]
        },
        "mock_tool_responses": {
            "get_user_profile": {"response": {"id": "test-user", "name": "Test User"}},
            "search_database": {"response": {"results": ["result1", "result2"]}}
        },
        "warmup_runs": 1,
        "benchmark_runs": 3
    }
})

if validation_result["valid"]:
    print("Validation succeeded")
else:
    print("Validation failed:", validation_result["errors"])

    # Check component-specific errors
    for component, result in validation_result["components"].items():
        if not result["valid"]:
            print(f"Errors in {component}:", result["errors"])
```

### Using the Validation Command

The `validate_benchmarks` management command can be used to validate all benchmark scenarios in the database.

```bash
python manage.py validate_benchmarks
```

This command:
1. Loads all benchmark scenarios from the database.
2. Validates each scenario against the registered schemas.
3. Reports validation errors.
4. Optionally fixes metadata issues (`--fix-metadata`).
5. Optionally exports validated scenarios to JSON (`--export`).

## Managing Schema Versions

### Registering Schema Versions

Schema versions can be registered using the `SchemaVersionManager`.

```python
from apps.main.services.schema_version_manager import SchemaVersionManager

# Create a schema version manager
version_manager = SchemaVersionManager()

# Register schema versions
version_manager.register_schema_version("workflow_benchmark", "1.0.0", schema_v1)
version_manager.register_schema_version("workflow_benchmark", "2.0.0", schema_v2)
```

### Validating Against Specific Versions

The `validate_with_version` method validates data against a specific schema version.

```python
from apps.main.services.schema_version_manager import SchemaVersionManager

# Create a schema version manager
version_manager = SchemaVersionManager()

# Validate data against a specific version
is_valid, errors = version_manager.validate_with_version("workflow_benchmark", data, "1.0.0")
```

### Checking Compatibility

The `is_compatible` method checks if data is compatible with a specific schema version.

```python
from apps.main.services.schema_version_manager import SchemaVersionManager

# Create a schema version manager
version_manager = SchemaVersionManager()

# Check compatibility
is_compatible, errors = version_manager.is_compatible("workflow_benchmark", data, "1.0.0", "2.0.0")
```

## Migrating Data Between Schema Versions

### Registering Migration Functions

Migration functions can be registered using the `SchemaVersionManager`.

```python
from apps.main.services.schema_version_manager import SchemaVersionManager

# Create a schema version manager
version_manager = SchemaVersionManager()

# Define a migration function
def migrate_v1_to_v2(data):
    """Migrate data from v1 to v2."""
    result = data.copy()
    # Add new required field
    if "new_field" not in result:
        result["new_field"] = "default_value"
    # Rename field
    if "old_field" in result:
        result["new_field_name"] = result.pop("old_field")
    return result

# Register the migration function
version_manager.register_migration("workflow_benchmark", "1.0.0", "2.0.0", migrate_v1_to_v2)
```

### Migrating Data

The `migrate_data` method migrates data from one schema version to another.

```python
from apps.main.services.schema_version_manager import SchemaVersionManager

# Create a schema version manager
version_manager = SchemaVersionManager()

# Migrate data
migrated_data = version_manager.migrate_data("workflow_benchmark", data, "1.0.0", "2.0.0")
```

### Using Migration Utilities

The `SchemaMigrationUtility` provides utilities for common migration operations.

```python
from apps.main.services.schema_migration_utility import SchemaMigrationUtility

# Create a schema migration utility
migration_utility = SchemaMigrationUtility()

# Define a migration function using utilities
def migrate_v1_to_v2(data):
    """Migrate data from v1 to v2."""
    # Add new required field
    data = migration_utility.add_field(data, "new_field", "default_value")
    # Rename field
    data = migration_utility.rename_field(data, "old_field", "new_field_name")
    # Update enum values
    data = migration_utility.update_enum_values(data, "status", {"active": "enabled", "inactive": "disabled"})
    return data
```

### Creating Migration Chains

The `create_migration_chain` method creates a chain of migration functions for complex transformations.

```python
from apps.main.services.schema_migration_utility import SchemaMigrationUtility

# Create a schema migration utility
migration_utility = SchemaMigrationUtility()

# Define migration functions
def add_new_field(data):
    """Add a new field."""
    return migration_utility.add_field(data, "new_field", "default_value")

def rename_field(data):
    """Rename a field."""
    return migration_utility.rename_field(data, "old_field", "new_field_name")

def update_enum_values(data):
    """Update enum values."""
    return migration_utility.update_enum_values(data, "status", {"active": "enabled", "inactive": "disabled"})

# Create a migration chain
migration_chain = migration_utility.create_migration_chain([
    add_new_field,
    rename_field,
    update_enum_values
])

# Migrate data
migrated_data = migration_chain(data)
```

## Common Schema Management Patterns

### Pattern: Schema Evolution

```python
from apps.main.services.schema_version_manager import SchemaVersionManager
from apps.main.services.schema_migration_utility import SchemaMigrationUtility

# Create a schema version manager
version_manager = SchemaVersionManager()

# Create a schema migration utility
migration_utility = SchemaMigrationUtility()

# Register schema versions
version_manager.register_schema_version("workflow_benchmark", "1.0.0", schema_v1)
version_manager.register_schema_version("workflow_benchmark", "2.0.0", schema_v2)

# Define migration functions
def migrate_v1_to_v2(data):
    """Migrate data from v1 to v2."""
    # Add new required field
    data = migration_utility.add_field(data, "new_field", "default_value")
    # Rename field
    data = migration_utility.rename_field(data, "old_field", "new_field_name")
    return data

# Register migration functions
version_manager.register_migration("workflow_benchmark", "1.0.0", "2.0.0", migrate_v1_to_v2)

# Migrate all scenarios in the database
scenarios = BenchmarkScenario.objects.filter(metadata__workflow_type__isnull=False)
for scenario in scenarios:
    # Check if the scenario needs migration
    if "workflow_type" in scenario.metadata and "version" in scenario.metadata:
        current_version = scenario.metadata["version"]
        if current_version == "1.0.0":
            # Migrate the scenario
            migrated_metadata = version_manager.migrate_data(
                "workflow_benchmark",
                scenario.metadata,
                "1.0.0",
                "2.0.0"
            )
            # Update the scenario
            scenario.metadata = migrated_metadata
            scenario.save()
```

### Pattern: Schema Validation in Workflow Execution

```python
async def execute_benchmark(scenario_id, params=None):
    """Execute a benchmark with schema validation."""
    # Default parameters
    params = params or {}

    # Load the scenario
    scenario = await get_scenario_async(scenario_id)
    if not scenario:
        raise ValueError(f"Scenario {scenario_id} not found")

    # Validate the scenario if requested
    if params.get("validate_schema", False):
        validator = SchemaValidationService()
        validation_result = validator.validate_benchmark_scenario({
            "name": scenario.name,
            "description": scenario.description,
            "agent_role": scenario.agent_role,
            "input_data": scenario.input_data,
            "metadata": scenario.metadata
        })

        if not validation_result["valid"]:
            raise ValueError(f"Schema validation failed: {validation_result['errors']}")

    # Execute the benchmark
    result = await run_benchmark(scenario, params)

    return result
```

### Pattern: Schema Migration in API

```python
from rest_framework.decorators import api_view
from rest_framework.response import Response
from apps.main.services.schema_version_manager import SchemaVersionManager

@api_view(['POST'])
def migrate_scenario(request):
    """API endpoint for migrating a scenario to a new schema version."""
    # Get parameters
    scenario_id = request.data.get("scenario_id")
    target_version = request.data.get("target_version")

    # Validate parameters
    if not scenario_id:
        return Response({"error": "scenario_id is required"}, status=400)
    if not target_version:
        return Response({"error": "target_version is required"}, status=400)

    try:
        # Get the scenario
        scenario = BenchmarkScenario.objects.get(id=scenario_id)

        # Get the current version
        current_version = scenario.metadata.get("version")
        if not current_version:
            return Response({"error": "Scenario does not have a version"}, status=400)

        # Create a schema version manager
        version_manager = SchemaVersionManager()

        # Check if migration is possible
        if not version_manager.can_migrate("workflow_benchmark", current_version, target_version):
            return Response({
                "error": f"Cannot migrate from version {current_version} to {target_version}"
            }, status=400)

        # Migrate the scenario
        migrated_metadata = version_manager.migrate_data(
            "workflow_benchmark",
            scenario.metadata,
            current_version,
            target_version
        )

        # Update the scenario
        scenario.metadata = migrated_metadata
        scenario.save()

        return Response({
            "status": "success",
            "message": f"Migrated scenario from version {current_version} to {target_version}"
        })
    except BenchmarkScenario.DoesNotExist:
        return Response({"error": f"Scenario {scenario_id} not found"}, status=404)
    except Exception as e:
        return Response({"error": str(e)}, status=500)
```

### Pattern: Schema Validation in Admin Interface

```python
from django.contrib import admin
from django.contrib import messages
from apps.main.models import BenchmarkScenario
from apps.main.services.schema_validator_service import SchemaValidationService

class BenchmarkScenarioAdmin(admin.ModelAdmin):
    """Admin interface for BenchmarkScenario."""

    actions = ["validate_schemas"]

    def validate_schemas(self, request, queryset):
        """Validate schemas for selected scenarios."""
        validator = SchemaValidationService()
        valid_count = 0
        invalid_count = 0

        for scenario in queryset:
            validation_result = validator.validate_benchmark_scenario({
                "name": scenario.name,
                "description": scenario.description,
                "agent_role": scenario.agent_role,
                "input_data": scenario.input_data,
                "metadata": scenario.metadata
            })

            if validation_result["valid"]:
                valid_count += 1
            else:
                invalid_count += 1
                self.message_user(
                    request,
                    f"Validation failed for scenario {scenario.name}: {validation_result['errors']}",
                    messages.ERROR
                )

        if valid_count > 0:
            self.message_user(
                request,
                f"{valid_count} scenario(s) passed validation.",
                messages.SUCCESS
            )

        if invalid_count > 0:
            self.message_user(
                request,
                f"{invalid_count} scenario(s) failed validation.",
                messages.WARNING
            )

    validate_schemas.short_description = "Validate schemas for selected scenarios"
```

## Best Practices

1. **Use Semantic Versioning**: Use semantic versioning (major.minor.patch) for schema versions.

2. **Include Version in Schema**: Include a `$version` property in the schema definition.

3. **Document Schema Changes**: Document schema changes in the schema definition and in the migration functions.

4. **Test Migrations**: Write tests for migration functions to ensure they work correctly.

5. **Validate Before Migration**: Validate data against the source schema before migration.

6. **Validate After Migration**: Validate migrated data against the target schema after migration.

7. **Use Migration Utilities**: Use the `SchemaMigrationUtility` for common migration operations.

8. **Create Migration Chains**: Use migration chains for complex transformations.

9. **Handle Edge Cases**: Handle edge cases in migration functions, such as missing fields or unexpected values.

10. **Provide Default Values**: Provide default values for new required fields in migration functions.

11. **Keep Migrations Simple**: Keep migration functions simple and focused on a single transformation.

12. **Test with Real Data**: Test migrations with real data to ensure they work correctly.

13. **Document Migration Paths**: Document migration paths between schema versions.

14. **Validate in CI/CD**: Validate schemas in CI/CD pipelines to catch issues early.

15. **Use Schema Validation in APIs**: Validate data against schemas in API endpoints.

16. **Provide Migration APIs**: Provide APIs for migrating data between schema versions.

17. **Handle Backward Compatibility**: Ensure backward compatibility when possible.

18. **Use Schema Registry**: Use the `SchemaRegistry` to manage schemas centrally.

19. **Use Schema Validation Service**: Use the `SchemaValidationService` for high-level validation.

20. **Use Schema Version Manager**: Use the `SchemaVersionManager` for managing schema versions and migrations.
