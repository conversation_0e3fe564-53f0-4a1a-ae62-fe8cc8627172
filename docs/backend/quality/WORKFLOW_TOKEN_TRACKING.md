# Token Tracking and Cost Monitoring for Workflow Benchmarks

This document provides a comprehensive overview of the token tracking and cost monitoring system for workflow benchmarks.

## Table of Contents
- [Overview](#overview)
- [TokenTracker Implementation](#tokentracker-implementation)
- [Cost Calculation](#cost-calculation)
- [Budget Configuration](#budget-configuration)
- [Cost Optimization](#cost-optimization)
- [Integration with Benchmark System](#integration-with-benchmark-system)
- [Testing Token Tracking](#testing-token-tracking)
- [Best Practices](#best-practices)
- [Future Improvements](#future-improvements)

## Overview

The token tracking and cost monitoring system provides a way to track token usage across workflow execution, calculate costs based on token usage, and monitor budget utilization. This system is an essential part of the workflow benchmarking system, providing insights into resource usage and cost efficiency.

Key features:
- Token usage tracking by stage, model, and operation type
- Cost calculation based on input and output tokens
- Budget configuration and alerts
- Cost optimization recommendations

## TokenTracker Implementation

The `TokenTracker` class is the core component of the token tracking system. It tracks token usage across workflow execution and provides methods for recording token usage and calculating costs.

### Key Components

```python
@dataclass
class TokenTracker:
    """
    Tracks token usage across workflow execution.

    Attributes:
        run_id: UUID of the benchmark run
        input_tokens: Count of input tokens used
        output_tokens: Count of output tokens used
        stage_tokens: Dictionary mapping stage names to token counts
        model_tokens: Dictionary mapping model names to token counts
    """
    run_id: uuid.UUID
    input_tokens: int = 0
    output_tokens: int = 0
    stage_tokens: Dict[str, Dict[str, int]] = field(default_factory=lambda: defaultdict(lambda: {"input": 0, "output": 0}))
    model_tokens: Dict[str, Dict[str, int]] = field(default_factory=lambda: defaultdict(lambda: {"input": 0, "output": 0}))

    async def record_usage(self, input_tokens: int, output_tokens: int, stage: Optional[str] = None, model: Optional[str] = None):
        """
        Record token usage and update the benchmark run record.

        Args:
            input_tokens: Number of input tokens used
            output_tokens: Number of output tokens used
            stage: Optional stage name for tracking tokens by stage
            model: Optional model name for tracking tokens by model
        """
        self.input_tokens += input_tokens
        self.output_tokens += output_tokens

        # Track tokens by stage if provided
        if stage:
            self.stage_tokens[stage]["input"] += input_tokens
            self.stage_tokens[stage]["output"] += output_tokens

        # Track tokens by model if provided
        if model:
            self.model_tokens[model]["input"] += input_tokens
            self.model_tokens[model]["output"] += output_tokens

        # Update the benchmark run record in the database
        await sync_to_async(
            BenchmarkRun.objects.filter(id=self.run_id).update,
            thread_sensitive=True
        )(
            total_input_tokens=F('total_input_tokens') + input_tokens,
            total_output_tokens=F('total_output_tokens') + output_tokens,
            llm_calls=F('llm_calls') + 1
        )

    async def calculate_cost(self, input_price: float, output_price: float) -> float:
        """
        Calculate the cost of token usage.

        Args:
            input_price: Price per input token
            output_price: Price per output token

        Returns:
            float: Total cost
        """
        return (self.input_tokens * input_price) + (self.output_tokens * output_price)

    async def calculate_cost_by_model(self, model_prices: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """
        Calculate the cost of token usage by model.

        Args:
            model_prices: Dictionary mapping model names to price dictionaries
                          with "input" and "output" keys

        Returns:
            Dict[str, float]: Dictionary mapping model names to costs
        """
        costs = {}
        for model, tokens in self.model_tokens.items():
            if model in model_prices:
                input_price = model_prices[model].get("input", 0)
                output_price = model_prices[model].get("output", 0)
                costs[model] = (tokens["input"] * input_price) + (tokens["output"] * output_price)
        return costs

    async def calculate_cost_by_stage(self, input_price: float, output_price: float) -> Dict[str, float]:
        """
        Calculate the cost of token usage by stage.

        Args:
            input_price: Price per input token
            output_price: Price per output token

        Returns:
            Dict[str, float]: Dictionary mapping stage names to costs
        """
        costs = {}
        for stage, tokens in self.stage_tokens.items():
            costs[stage] = (tokens["input"] * input_price) + (tokens["output"] * output_price)
        return costs

    def get_token_usage_report(self) -> Dict[str, Any]:
        """
        Generate a detailed token usage report.

        Returns:
            Dict[str, Any]: Dictionary containing token usage information
        """
        return {
            "total": {
                "input": self.input_tokens,
                "output": self.output_tokens,
                "total": self.input_tokens + self.output_tokens
            },
            "by_stage": {
                stage: {
                    "input": tokens["input"],
                    "output": tokens["output"],
                    "total": tokens["input"] + tokens["output"]
                }
                for stage, tokens in self.stage_tokens.items()
            },
            "by_model": {
                model: {
                    "input": tokens["input"],
                    "output": tokens["output"],
                    "total": tokens["input"] + tokens["output"]
                }
                for model, tokens in self.model_tokens.items()
            }
        }
```

### Usage

To use the `TokenTracker` class:

1. Create a `TokenTracker` instance with a benchmark run ID:
   ```python
   token_tracker = TokenTracker(run_id=benchmark_run.id)
   ```

2. Record token usage:
   ```python
   await token_tracker.record_usage(
       input_tokens=100,
       output_tokens=50,
       stage="process_input",
       model="openai/gpt-4"
   )
   ```

3. Calculate costs:
   ```python
   # Calculate total cost
   total_cost = await token_tracker.calculate_cost(
       input_price=0.001,  # $0.001 per input token
       output_price=0.002  # $0.002 per output token
   )

   # Calculate cost by model
   model_costs = await token_tracker.calculate_cost_by_model({
       "openai/gpt-4": {"input": 0.001, "output": 0.002},
       "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.001}
   })

   # Calculate cost by stage
   stage_costs = await token_tracker.calculate_cost_by_stage(
       input_price=0.001,
       output_price=0.002
   )
   ```

4. Generate a token usage report:
   ```python
   token_usage_report = token_tracker.get_token_usage_report()
   ```

## Cost Calculation

The cost calculation system provides a way to calculate costs based on token usage. It supports different pricing tiers for different models and provides detailed cost breakdowns.

### Model-Specific Pricing

Different LLM models have different pricing tiers. The cost calculation system supports model-specific pricing through the `calculate_cost_by_model` method:

```python
model_costs = await token_tracker.calculate_cost_by_model({
    "openai/gpt-4": {"input": 0.001, "output": 0.002},
    "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.001},
    "anthropic/claude-3-opus": {"input": 0.0015, "output": 0.0025}
})
```

### Cost Breakdown

The cost calculation system provides detailed cost breakdowns by stage and model:

```python
# Calculate cost by stage
stage_costs = await token_tracker.calculate_cost_by_stage(
    input_price=0.001,
    output_price=0.002
)

# Calculate cost by model
model_costs = await token_tracker.calculate_cost_by_model({
    "openai/gpt-4": {"input": 0.001, "output": 0.002},
    "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.001}
})
```

## Budget Configuration

The budget configuration system provides a way to set budget limits for benchmark runs and receive alerts when limits are exceeded.

### Budget Limits

Budget limits can be set for benchmark runs through the `budget_limits` parameter:

```python
await workflow_manager.execute_benchmark(
    scenario_id=scenario.id,
    params={
        "budget_limits": {
            "total": 10.0,  # $10.00 total budget
            "per_run": 1.0,  # $1.00 per run budget
            "per_model": {
                "openai/gpt-4": 5.0,  # $5.00 budget for GPT-4
                "openai/gpt-3.5-turbo": 2.0  # $2.00 budget for GPT-3.5 Turbo
            }
        }
    },
    progress_callback=progress_callback,
    user_profile_id=user_profile_id
)
```

### Budget Alerts

Budget alerts are triggered when budget limits are exceeded:

```python
# Budget alert handler
async def budget_alert_handler(alert_type, alert_data):
    """
    Handle budget alerts.

    Args:
        alert_type: Type of alert (e.g., "budget_exceeded")
        alert_data: Alert data (e.g., {"model": "openai/gpt-4", "limit": 5.0, "current": 5.5})
    """
    if alert_type == "budget_exceeded":
        logger.warning(f"Budget exceeded: {alert_data}")
        # Send email notification
        await send_budget_alert_email(alert_data)

# Register budget alert handler
workflow_manager.register_budget_alert_handler(budget_alert_handler)
```

## Cost Optimization

The cost optimization system provides recommendations for optimizing token usage and reducing costs.

### Token Usage Analysis

The token usage analysis system analyzes token usage patterns and identifies opportunities for optimization:

```python
# Get token usage report
token_usage_report = token_tracker.get_token_usage_report()

# Analyze token usage patterns
token_usage_analysis = analyze_token_usage(token_usage_report)

# Get optimization recommendations
optimization_recommendations = get_optimization_recommendations(token_usage_analysis)
```

### Cost Comparison

The cost comparison system compares costs between different models and approaches:

```python
# Compare costs between different models
model_cost_comparison = compare_model_costs({
    "openai/gpt-4": {"input": 0.001, "output": 0.002, "tokens": {"input": 1000, "output": 500}},
    "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.001, "tokens": {"input": 1200, "output": 600}}
})

# Compare costs between different approaches
approach_cost_comparison = compare_approach_costs({
    "approach1": {"tokens": {"input": 1000, "output": 500}, "cost": 2.0},
    "approach2": {"tokens": {"input": 1200, "output": 600}, "cost": 1.8}
})
```

## Integration with Benchmark System

The token tracking and cost monitoring system integrates with the workflow benchmarking system:

```python
# Execute a workflow benchmark with token tracking and cost monitoring
result = await workflow_manager.execute_benchmark(
    scenario_id=scenario.id,
    params={
        "token_tracking": True,
        "cost_monitoring": True,
        "budget_limits": {
            "total": 10.0,
            "per_run": 1.0,
            "per_model": {
                "openai/gpt-4": 5.0,
                "openai/gpt-3.5-turbo": 2.0
            }
        },
        "model_prices": {
            "openai/gpt-4": {"input": 0.001, "output": 0.002},
            "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.001}
        }
    },
    progress_callback=progress_callback,
    user_profile_id=user_profile_id
)

# Get token usage report from benchmark result
token_usage_report = result.token_usage_report

# Get cost report from benchmark result
cost_report = result.cost_report
```

## Testing Token Tracking

The token tracking system includes comprehensive testing utilities:

```python
@pytest.fixture
def token_tracker():
    """Fixture to provide a TokenTracker instance."""
    # Create a mock run_id
    run_id = uuid.uuid4()
    # Mock the record_usage method to avoid database operations
    with patch.object(TokenTracker, 'record_usage', AsyncMock()):
        return TokenTracker(run_id=run_id)

@pytest.mark.asyncio
async def test_record_usage(token_tracker):
    """Test that record_usage works correctly."""
    # Record token usage
    await token_tracker.record_usage(100, 50, stage="process_input", model="openai/gpt-4")

    # Verify the counts
    assert token_tracker.input_tokens == 100
    assert token_tracker.output_tokens == 50
    assert token_tracker.stage_tokens["process_input"]["input"] == 100
    assert token_tracker.stage_tokens["process_input"]["output"] == 50
    assert token_tracker.model_tokens["openai/gpt-4"]["input"] == 100
    assert token_tracker.model_tokens["openai/gpt-4"]["output"] == 50

@pytest.mark.asyncio
async def test_calculate_cost(token_tracker):
    """Test that calculate_cost works correctly."""
    # Record token usage
    await token_tracker.record_usage(100, 50, stage="process_input", model="openai/gpt-4")

    # Calculate cost
    cost = await token_tracker.calculate_cost(0.001, 0.002)

    # Verify the cost
    expected_cost = (100 * 0.001) + (50 * 0.002)
    assert cost == pytest.approx(expected_cost)

@pytest.mark.asyncio
async def test_calculate_cost_by_model(token_tracker):
    """Test that calculate_cost_by_model works correctly."""
    # Record token usage
    await token_tracker.record_usage(100, 50, stage="process_input", model="openai/gpt-4")
    await token_tracker.record_usage(200, 100, stage="generate_response", model="openai/gpt-3.5-turbo")

    # Calculate cost by model
    model_costs = await token_tracker.calculate_cost_by_model({
        "openai/gpt-4": {"input": 0.001, "output": 0.002},
        "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.001}
    })

    # Verify the costs
    expected_gpt4_cost = (100 * 0.001) + (50 * 0.002)
    expected_gpt35_cost = (200 * 0.0005) + (100 * 0.001)
    assert model_costs["openai/gpt-4"] == pytest.approx(expected_gpt4_cost)
    assert model_costs["openai/gpt-3.5-turbo"] == pytest.approx(expected_gpt35_cost)
```

## Best Practices

### Token Tracking

1. **Always Track Tokens by Stage**: Track tokens by stage to identify which parts of the workflow are using the most tokens.
2. **Track Tokens by Model**: Track tokens by model to identify which models are using the most tokens.
3. **Use Consistent Model Names**: Use consistent model names across the codebase to ensure accurate token tracking.
4. **Record Token Usage Immediately**: Record token usage immediately after receiving a response from the LLM to ensure accurate tracking.

### Cost Monitoring

1. **Set Realistic Budget Limits**: Set realistic budget limits based on expected token usage and model prices.
2. **Monitor Budget Utilization**: Regularly monitor budget utilization to identify potential issues.
3. **Use Model-Specific Pricing**: Use model-specific pricing to accurately calculate costs.
4. **Implement Budget Alerts**: Implement budget alerts to be notified when budget limits are exceeded.

### Cost Optimization

1. **Analyze Token Usage Patterns**: Regularly analyze token usage patterns to identify opportunities for optimization.
2. **Compare Costs Between Models**: Compare costs between different models to identify the most cost-effective options.
3. **Optimize Prompt Design**: Optimize prompt design to reduce token usage.
4. **Use Efficient Models**: Use more efficient models for tasks that don't require the capabilities of more expensive models.

## Future Improvements

1. **Real-Time Cost Monitoring**: Implement real-time cost monitoring with a dashboard for visualizing costs.
2. **Advanced Budget Alerts**: Enhance budget alerts with more detailed information and customizable thresholds.
3. **Cost Prediction**: Implement cost prediction based on historical token usage patterns.
4. **Integration with Billing Systems**: Integrate with billing systems for more accurate cost tracking.
5. **Cost Optimization Recommendations**: Enhance cost optimization recommendations with more detailed suggestions.
