# Pydantic Testing Guide

This guide explains how to use the Pydantic model factories for testing in the Goali project.

## Overview

The Goali project uses Pydantic models for data validation and serialization. To facilitate testing these models, we've created a set of factory classes that make it easy to create test instances of Pydantic models with random or specified data.

## Factory Classes

The factory classes are located in `backend/apps/main/tests/factories/pydantic_factories.py`. The main factory classes are:

- `BaseFactory`: Base factory class with common functionality for all Pydantic model factories.
- `GoaliBaseModelFactory`: Factory for creating test instances of `GoaliBaseModel`.
- `VersionedModelFactory`: Factory for creating test instances of `VersionedModel`.
- `BenchmarkScenarioFactory`: Factory for creating test instances of `BenchmarkScenario`.
- `BenchmarkScenarioMetadataFactory`: Factory for creating test instances of `BenchmarkScenarioMetadata`.
- `EvaluationCriterionFactory`: Factory for creating test instances of `EvaluationCriterion`.
- `EvaluationCriteriaFactory`: Factory for creating test instances of `EvaluationCriteria`.
- `PhaseAwareCriteriaFactory`: Factory for creating test instances of `PhaseAwareCriteria`.
- `ToolExpectationFactory`: Factory for creating test instances of `ToolExpectation`.
- `BenchmarkRunFactory`: Factory for creating test instances of `BenchmarkRun`.
- `TokenUsageFactory`: Factory for creating test instances of `TokenUsage`.
- `StagePerformanceFactory`: Factory for creating test instances of `StagePerformance`.
- `SemanticEvaluationFactory`: Factory for creating test instances of `SemanticEvaluation`.

## Helper Functions

The module also provides helper functions for creating test instances of Pydantic models:

- `create_benchmark_scenario`: Create a test instance of `BenchmarkScenario`.
- `create_benchmark_run`: Create a test instance of `BenchmarkRun`.
- `create_evaluation_criteria`: Create a test instance of `EvaluationCriteria`.
- `create_phase_aware_criteria`: Create a test instance of `PhaseAwareCriteria`.
- `create_tool_expectation`: Create a test instance of `ToolExpectation`.
- `create_token_usage`: Create a test instance of `TokenUsage`.
- `create_stage_performance`: Create a test instance of `StagePerformance`.
- `create_semantic_evaluation`: Create a test instance of `SemanticEvaluation`.
- `create_invalid_model_data`: Create data for an invalid model instance.

## Basic Usage

### Creating a Valid Model Instance

To create a valid model instance, use the `build` method of the factory class:

```python
from apps.main.tests.factories import BenchmarkScenarioFactory

# Create a benchmark scenario with default values
scenario = BenchmarkScenarioFactory.build()

# Create a benchmark scenario with custom values
scenario = BenchmarkScenarioFactory.build(
    name="Custom Scenario",
    description="Custom description",
    agent_role="custom_role",
    is_active=False
)
```

### Creating Multiple Model Instances

To create multiple model instances, use the `build_batch` method:

```python
from apps.main.tests.factories import BenchmarkScenarioFactory

# Create 5 benchmark scenarios with default values
scenarios = BenchmarkScenarioFactory.build_batch(size=5)

# Create 5 benchmark scenarios with custom values
scenarios = BenchmarkScenarioFactory.build_batch(
    size=5,
    agent_role="custom_role",
    is_active=False
)
```

### Creating Invalid Model Data

To create data for an invalid model instance, use the `create_invalid_model_data` function:

```python
from apps.main.tests.factories import create_invalid_model_data, BenchmarkScenarioFactory
from pydantic import ValidationError

# Create data for an invalid benchmark scenario
data = create_invalid_model_data(
    BenchmarkScenarioFactory,
    field_name="agent_role",
    invalid_value=123  # agent_role should be a string
)

# Verify that the data is invalid
with pytest.raises(ValidationError):
    BenchmarkScenario(**data)
```

## Advanced Usage

### Customizing Factory Classes

You can customize the factory classes by overriding the `get_default_values` method:

```python
from apps.main.tests.factories import BenchmarkScenarioFactory

class CustomBenchmarkScenarioFactory(BenchmarkScenarioFactory):
    @classmethod
    def get_default_values(cls):
        values = super().get_default_values()
        values.update({
            "name": "Custom Scenario",
            "description": "Custom description",
            "agent_role": "custom_role",
            "is_active": False
        })
        return values
```

### Testing Validation Errors

You can test validation errors by creating invalid model data and verifying that validation fails:

```python
from apps.main.tests.factories import create_invalid_model_data, BenchmarkScenarioFactory
from apps.main.schemas.benchmark.scenarios import BenchmarkScenario
from pydantic import ValidationError

def test_benchmark_scenario_validation():
    # Create data for an invalid benchmark scenario
    data = create_invalid_model_data(
        BenchmarkScenarioFactory,
        field_name="agent_role",
        invalid_value=123  # agent_role should be a string
    )

    # Verify that validation fails
    with pytest.raises(ValidationError) as excinfo:
        BenchmarkScenario(**data)

    # Verify the error message
    assert "agent_role" in str(excinfo.value)
    assert "str type expected" in str(excinfo.value)
```

### Testing Logging

You can test logging by using the `LogCapture` class from `backend/apps/main/tests/test_logging.py`:

```python
from apps.main.tests.test_logging import LogCapture
from apps.main.tests.factories import BenchmarkScenarioFactory
import logging

def test_benchmark_scenario_logging():
    with LogCapture() as log:
        # Create a valid benchmark scenario
        scenario = BenchmarkScenarioFactory.build()

        # Verify that a debug log message was emitted
        assert log.assert_debug_log("Successfully initialized BenchmarkScenario")

    with LogCapture() as log:
        # Create an invalid benchmark scenario
        with pytest.raises(ValidationError):
            data = create_invalid_model_data(
                BenchmarkScenarioFactory,
                field_name="agent_role",
                invalid_value=123
            )
            BenchmarkScenario(**data)

        # Verify that an error log message was emitted
        assert log.assert_error_log("Failed to initialize BenchmarkScenario")
```

## Best Practices

1. **Use Factory Classes**: Use the factory classes to create test instances of Pydantic models instead of creating them manually.

2. **Test Validation**: Test both valid and invalid model instances to ensure that validation works correctly.

3. **Test Logging**: Test that validation failures are logged at ERROR level and successful validations are logged at DEBUG level.

4. **Test Edge Cases**: Test models with minimum and maximum values, empty or null fields, and invalid field types.

5. **Use Helper Functions**: Use the helper functions to create test instances of Pydantic models when appropriate.

6. **Customize Factory Classes**: Customize the factory classes when you need to create model instances with specific values.

7. **Test Model Methods**: Test any methods defined on the model classes, such as `record_completion` on `BenchmarkRun`.

8. **Test Model Relationships**: Test relationships between models, such as creating a `BenchmarkScenario` with a `BenchmarkScenarioMetadata` instance.

9. **Test Model Serialization**: Test that models can be serialized to and deserialized from JSON correctly.

10. **Test Model Validation**: Test that models validate their data correctly, including any custom validators.

## Conclusion

The Pydantic model factories provide a convenient way to create test instances of Pydantic models with random or specified data. By using these factories, you can write more concise and maintainable tests for your Pydantic models.
