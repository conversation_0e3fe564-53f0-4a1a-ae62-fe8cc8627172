# Benchmark System Troubleshooting Guide

This document provides guidance for troubleshooting common issues with the benchmark system.

## Table of Contents
- [Agent Configuration Issues](#agent-configuration-issues)
- [Tool Mocking Issues](#tool-mocking-issues)
- [Schema Validation Issues](#schema-validation-issues)
- [Database Issues](#database-issues)
- [Performance Issues](#performance-issues)
- [WebSocket Issues](#websocket-issues)
- [Semantic Evaluation Issues](#semantic-evaluation-issues)

## Agent Configuration Issues

### "Failed to load agent configuration" Errors

**Symptoms:**
- Tests fail with "Failed to load agent configuration" errors
- Agent initialization fails in the test environment
- Missing agent definitions in the test database

**Causes:**
1. Agent definitions not properly seeded in the test database
2. Missing or incorrect `langgraph_node_class` in agent definitions
3. Missing or incorrect `llm_config` in agent definitions
4. Errors in agent definition loading process

**Solutions:**
1. **Check Agent Seeding:**
   - Ensure `conftest.py` properly seeds all required agent definitions
   - Verify that agent roles match those expected by tests
   - Check that class paths are correct for all agent roles

2. **Verify MockDatabaseService:**
   - Ensure `MockDatabaseService.load_agent_definition` provides proper defaults
   - Check that fallback mechanisms are working correctly
   - Verify that default class paths are provided for all agent roles

3. **Add Error Handling:**
   - Implement proper error handling in agent initialization
   - Add detailed logging for agent configuration loading failures
   - Ensure standardized error structure in agent responses

4. **Update Tests:**
   - Add tests specifically for agent configuration loading failures
   - Ensure tests properly initialize agent configurations
   - Add tests for missing output_data handling

### Missing or Incorrect Agent Output Structure

**Symptoms:**
- Tests fail with "Missing 'output_data' key" errors
- Agent responses have inconsistent structure
- Missing required fields in agent output

**Causes:**
1. Agent process method not returning required output structure
2. Missing validation for required output fields
3. Inconsistent output structure across agent types

**Solutions:**
1. **Standardize Output Structure:**
   - Ensure all agents return a consistent output structure
   - Add validation for required output fields
   - Implement fallback mechanisms for missing output fields

2. **Add Error Handling:**
   - Implement proper error handling for missing output fields
   - Add detailed logging for output structure validation failures
   - Ensure standardized error structure in agent responses

3. **Update Tests:**
   - Add tests specifically for output structure validation
   - Ensure tests verify presence of required fields
   - Add tests for fallback mechanisms

## Tool Mocking Issues

### Tool Mocking Configuration Errors

**Symptoms:**
- Tests fail with "Tool not found" errors
- Unexpected tool responses in tests
- Missing tool call tracking in benchmark results

**Causes:**
1. Tool mocking configuration not properly set up
2. Missing or incorrect tool code in mock configuration
3. Errors in tool response template evaluation
4. Conditional responses not matching expected conditions

**Solutions:**
1. **Check Tool Mocking Configuration:**
   - Ensure tool codes match those used by agents
   - Verify that tool response templates are properly formatted
   - Check that conditional responses have correct conditions

2. **Verify Template Evaluation:**
   - Ensure placeholders in templates are properly replaced
   - Check that JSON templates are properly parsed
   - Verify that nested conditions are correctly matched

3. **Add Error Handling:**
   - Implement proper error handling for tool mocking failures
   - Add detailed logging for tool call tracking
   - Ensure standardized error structure in tool responses

## Schema Validation Issues

### Schema Validation Failures

**Symptoms:**
- Tests fail with schema validation errors
- Benchmark scenarios fail to load
- Missing required fields in benchmark components

**Causes:**
1. Missing required fields in benchmark scenarios
2. Incorrect schema structure in benchmark components
3. Schema validation not properly integrated with benchmark system
4. Schema versioning issues

**Solutions:**
1. **Check Benchmark Scenarios:**
   - Ensure all required fields are present in scenarios
   - Verify that schema structure matches expectations
   - Check that evaluation criteria follow the correct schema

2. **Verify Schema Validation Integration:**
   - Ensure schema validation is properly integrated with benchmark system
   - Check that validation errors are properly reported
   - Verify that schema versioning is correctly handled

3. **Add Error Handling:**
   - Implement proper error handling for schema validation failures
   - Add detailed logging for validation errors
   - Ensure standardized error structure in validation responses

## Database Issues

### Database Schema vs. Model Definition Discrepancies

**Symptoms:**
- Tests fail with database integrity errors
- Missing fields in database tables
- Inconsistent field types between models and database

**Causes:**
1. Model fields not matching database schema
2. Missing migrations for model changes
3. Inconsistent field naming between models and database

**Solutions:**
1. **Check Model Definitions:**
   - Ensure model fields match database schema
   - Verify that field types are consistent
   - Check that field names match database column names

2. **Create Migrations:**
   - Create migrations for model changes
   - Apply migrations to test database
   - Verify that migrations are properly applied

3. **Update Tests:**
   - Update tests to use correct field names
   - Ensure tests properly handle database schema changes
   - Add tests for database integrity

## Performance Issues

### Slow Benchmark Execution

**Symptoms:**
- Benchmark execution takes too long
- Tests timeout
- Performance metrics show unexpected slowdowns

**Causes:**
1. Inefficient agent implementation
2. Slow tool calls
3. Database access bottlenecks
4. LLM service delays

**Solutions:**
1. **Profile Agent Implementation:**
   - Use stage timing to identify slow stages
   - Optimize critical paths
   - Reduce unnecessary operations

2. **Optimize Tool Calls:**
   - Implement caching for expensive tool calls
   - Reduce number of tool calls
   - Optimize tool implementation

3. **Improve Database Access:**
   - Use efficient queries
   - Implement caching where appropriate
   - Reduce database round trips

## WebSocket Issues

### WebSocket Communication Failures

**Symptoms:**
- Tests fail with WebSocket connection errors
- Missing progress updates during benchmark execution
- WebSocket connections timeout or close unexpectedly

**Causes:**
1. Incorrect WebSocket routing configuration
2. Missing or incorrect WebSocket consumer registration
3. Errors in WebSocket message handling
4. Async context management issues

**Solutions:**
1. **Check WebSocket Configuration:**
   - Ensure WebSocket routing is correctly configured
   - Verify that WebSocket consumer is properly registered
   - Check that WebSocket paths match expectations

2. **Improve Error Handling:**
   - Implement proper error handling for WebSocket communication
   - Add detailed logging for WebSocket errors
   - Ensure standardized error structure in WebSocket messages

3. **Fix Async Context Management:**
   - Ensure proper async context management in WebSocket tests
   - Implement proper cleanup for WebSocket connections
   - Add timeout handling for WebSocket operations

## Semantic Evaluation Issues

### Semantic Evaluation Failures

**Symptoms:**
- Tests fail with semantic evaluation errors
- Missing or incorrect semantic scores in benchmark results
- Inconsistent evaluation results across runs

**Causes:**
1. Missing or incorrect evaluation criteria
2. LLM service errors during evaluation
3. Parsing errors in evaluation results
4. Inconsistent evaluation prompts

**Solutions:**
1. **Check Evaluation Criteria:**
   - Ensure evaluation criteria are properly defined
   - Verify that criteria dimensions match expectations
   - Check that criteria are appropriate for the agent role

2. **Improve LLM Service Integration:**
   - Implement proper error handling for LLM service failures
   - Add retries for transient errors
   - Ensure consistent LLM configuration across evaluations

3. **Enhance Result Parsing:**
   - Implement robust parsing for evaluation results
   - Add validation for parsed results
   - Ensure standardized result structure

## General Troubleshooting Tips

1. **Enable Detailed Logging:**
   - Set logging level to DEBUG for detailed information
   - Add specific log messages for critical operations
   - Use structured logging for machine-readable logs

2. **Use Test Fixtures:**
   - Create reusable test fixtures for common setup
   - Ensure fixtures properly clean up after tests
   - Use fixtures to isolate test dependencies

3. **Check Database State:**
   - Verify that database is properly seeded before tests
   - Ensure database is cleaned up after tests
   - Check for database locking issues in async tests

4. **Inspect Event Emissions:**
   - Monitor events emitted during benchmark execution
   - Check for error events that might indicate issues
   - Verify that progress events are properly emitted

5. **Review Test Environment:**
   - Ensure test environment matches production environment
   - Check for environment-specific issues
   - Verify that all dependencies are properly installed

6. **Use Debugging Tools:**
   - Use debugger to step through code execution
   - Add breakpoints at critical points
   - Inspect variable values during execution

7. **Check for Race Conditions:**
   - Look for async/sync conflicts
   - Ensure proper locking for shared resources
   - Verify that operations are properly sequenced

8. **Review Recent Changes:**
   - Check for recent code changes that might have introduced issues
   - Review pull request history
   - Look for changes to dependencies or configuration

## Reporting Issues

When reporting issues with the benchmark system, please include:

1. **Detailed Description:**
   - What were you trying to do?
   - What happened instead?
   - What did you expect to happen?

2. **Environment Information:**
   - Operating system
   - Python version
   - Django version
   - Database type and version
   - LLM service configuration

3. **Reproduction Steps:**
   - Step-by-step instructions to reproduce the issue
   - Sample code or configuration if applicable
   - Test case that demonstrates the issue

4. **Logs and Error Messages:**
   - Full error message and stack trace
   - Relevant log output
   - Database queries if applicable

5. **Screenshots or Videos:**
   - If the issue involves the UI, include screenshots or videos
   - Annotate screenshots to highlight the issue

6. **Workarounds:**
   - If you found a workaround, include it
   - Note any limitations or side effects of the workaround

## Common Test Failures and Root Causes

This section documents common test failures encountered in the benchmark system and their likely root causes. This information is intended to help developers quickly identify and fix issues.

### State Model Compatibility Issues

**Symptoms:**
- Tests fail with `NameError: name 'State' is not defined`
- Tests fail with `AttributeError` when accessing state attributes
- Tests fail with `TypeError` when setting state attributes

**Root Causes:**
1. **Missing State Class Definition**: Some tests reference a `State` class that is not defined or imported in the test file.
2. **Inconsistent State Models**: Different tests use different state models with incompatible attributes.
3. **Pydantic Model Compatibility**: Tests using older Pydantic v1 patterns with newer Pydantic v2 models.

**Solutions:**
1. **Define or Import State Class**: Ensure the `State` class is properly defined or imported in the test file.
2. **Standardize State Models**: Use a consistent state model across all tests, or ensure compatibility between different models.
3. **Update Pydantic Usage**: Update code to use Pydantic v2 patterns (e.g., `model_dump` instead of `dict`).

### Agent Output Structure Issues

**Symptoms:**
- Tests fail with assertions like `Missing 'psychological_assessment' in output`
- Tests fail with assertions like `Missing 'resource_context' in output`
- Tests fail with `KeyError` when accessing expected fields in agent output

**Root Causes:**
1. **Inconsistent Output Structure**: Different agents return different output structures.
2. **Missing Default Values**: Agents don't provide default values for expected fields when there are errors.
3. **Error Handling Differences**: Error handling code doesn't maintain the expected output structure.

**Solutions:**
1. **Standardize Output Structure**: Ensure all agents return a consistent output structure with all expected fields.
2. **Add Default Values**: Provide default values for all expected fields, even in error cases.
3. **Update Error Handling**: Ensure error handling code maintains the expected output structure.

### Error Handling Issues

**Symptoms:**
- Tests fail with assertions like `Error should be marked as handled`
- Tests fail with `Expected MockRunFailedError but no exception was raised`
- Tests fail with assertions about error messages or error handling behavior

**Root Causes:**
1. **Changed Error Handling Logic**: Recent changes to error handling code modified how errors are handled.
2. **Inconsistent Error Structure**: Different components use different error structures.
3. **Missing Error Propagation**: Errors are not properly propagated through the system.

**Solutions:**
1. **Standardize Error Handling**: Ensure all components use a consistent error handling approach.
2. **Update Error Structure**: Use a standardized error structure across all components.
3. **Improve Error Propagation**: Ensure errors are properly propagated through the system.

### Memory Handling Issues

**Symptoms:**
- Tests fail with `TypeError: argument of type 'NoneType' is not iterable`
- Tests fail with errors related to memory operations
- Tests fail with assertions about memory content

**Root Causes:**
1. **Missing Memory Initialization**: Memory is not properly initialized before use.
2. **Incompatible Memory Structure**: Memory structure doesn't match expected format.
3. **Async/Sync Conflicts**: Memory operations mix async and sync code incorrectly.

**Solutions:**
1. **Initialize Memory**: Ensure memory is properly initialized before use.
2. **Standardize Memory Structure**: Use a consistent memory structure across all components.
3. **Fix Async/Sync Issues**: Ensure memory operations use consistent async/sync patterns.

### Tool Mocking Issues

**Symptoms:**
- Tests fail with `AssertionError: Expected emit_error to have been awaited once. Awaited 0 times`
- Tests fail with unexpected tool behavior
- Tests fail with assertions about tool call counts or arguments

**Root Causes:**
1. **Changed Tool Interface**: Recent changes to tool interfaces broke existing mocks.
2. **Missing Mock Configuration**: Tool mocks are not properly configured for all test cases.
3. **Async/Sync Conflicts**: Tool mocking code mixes async and sync code incorrectly.

**Solutions:**
1. **Update Tool Mocks**: Ensure tool mocks match the current tool interfaces.
2. **Complete Mock Configuration**: Configure tool mocks for all test cases.
3. **Fix Async/Sync Issues**: Ensure tool mocking code uses consistent async/sync patterns.
