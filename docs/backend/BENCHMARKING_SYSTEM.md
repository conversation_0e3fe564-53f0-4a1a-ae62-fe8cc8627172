# File: docs\backend\BENCHMARKING_SYSTEM.md
# Benchmarking System Documentation

This document provides comprehensive, up-to-date documentation for the Goali benchmarking system. It covers both technical reference and practical usage guidance.

## Table of Contents

- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Getting Started](#getting-started)
- [Usage Guide](#usage-guide)
- [Technical Reference](#technical-reference)
- [Troubleshooting](#troubleshooting)
- [Related Documentation](#related-documentation)

## Overview

The Goali benchmarking system provides comprehensive evaluation capabilities for AI agents and workflows. It measures performance, operational metrics, and semantic quality to ensure reliable and high-quality AI behavior.

### Key Features

- **Agent Benchmarking**: Performance testing for individual agent components
- **Workflow Benchmarking**: End-to-end evaluation of LangGraph workflows
- **Semantic Evaluation**: LLM-based quality assessment with contextual adaptation
- **Tool Mocking**: Isolated testing with sophisticated mock tool responses
- **Token Tracking**: Comprehensive LLM usage and cost monitoring
- **Admin Interface**: Web-based management and visualization tools
- **Schema Validation**: Robust validation using JSON schemas and Pydantic models

### Clear System Differentiation

#### Agent Evaluation vs Workflow Evaluation
- **Agent Evaluation**: Tests individual agent components with controlled inputs
  - Focus on agent-specific behavior and responses
  - Uses `agent_role` to specify which agent to test
  - Simpler input/output patterns
  - Direct agent invocation

- **Workflow Evaluation**: Tests complete LangGraph workflows with multiple stages
  - End-to-end evaluation of multi-agent interactions
  - Uses `workflow_type` to specify which workflow to execute
  - Complex multi-stage execution patterns
  - Comprehensive stage timing and token tracking

#### Generic Situations, Comprehensive Context Variables, and Context-Linked Assessment Framework

**Generic Situations**: Reusable test case templates that define:
- Core interaction patterns without specific user/environmental conditions
- Input data structure for the test
- Expected behavior and outcomes
- Configuration parameters
- Mock tool responses

**Comprehensive Context Variables**: All factors that influence evaluation criteria:
- **User Profile Variables**: Trust level (0-100), personality traits, preferences, demographics
- **Environmental Variables**: Stress level (0-100), time pressure (0-100), mood state (valence/arousal -1.0 to 1.0)
- **Interaction Variables**: Session history, current goals, relationship phase

**Context Presets**: Reusable context variable combinations that:
- Auto-populate comprehensive context variable ranges
- Provide consistent testing scenarios
- Include mock tool responses for realistic simulation
- Serve as shortcuts for common testing contexts

**Context-Linked Assessment Framework**: Evaluation criteria that are ALWAYS linked with Generic Situations and Context Variable ranges:
- **Structure**: `Generic Situation + Context Variable Ranges → Adapted Evaluation Criteria`
- **Contextual Templates**: Reusable templates with contextual adaptation (recommended)
- **Expected Quality Criteria**: Simple dimension-based criteria
- **Phase-Aware Criteria**: Trust-level specific evaluation rules

### System Benefits

- **Quality Assurance**: Ensure consistent agent behavior across updates
- **Performance Monitoring**: Track performance trends and identify regressions
- **Cost Management**: Monitor and optimize LLM usage costs
- **Development Feedback**: Get detailed insights for agent improvement

## System Architecture

### Core Components

#### Models (`backend/apps/main/models.py`)
- **`BenchmarkScenario`**: Defines reusable generic situations for agents
- **`BenchmarkRun`**: Records execution results with comprehensive metrics
- **`BenchmarkTag`**: Categorizes generic situations for organization
- **`EvaluationCriteriaTemplate`**: Stores reusable context-linked assessment frameworks

#### Services (`backend/apps/main/services/`)
- **`AgentBenchmarker`**: Core service for agent benchmarking
- **`WorkflowBenchmarker`**: Base class for workflow benchmarking
- **`SemanticEvaluator`**: LLM-based quality assessment
- **`SchemaRegistry`**: Schema management and validation

#### Admin Interface (`backend/apps/admin_tools/`)
- **Dashboard**: `/admin/benchmarks/` - Run benchmarks and view recent results
- **History**: `/admin/benchmarks/history/` - Comprehensive result analysis
- **Management**: `/admin/benchmarks/manage/` - Generic situation and context-linked assessment framework management

#### Management Commands
- **`run_benchmarks`**: Execute benchmarks from command line
- **`create_benchmark_scenarios_v3`**: Load generic situations from JSON files
- **`validate_benchmarks_v3`**: Validate generic situations against schemas
- **`setup_benchmark_structure`**: Initialize directory structure

### Data Flow

1. **Generic Situation Definition**: Create JSON generic situation files with test parameters
2. **Validation**: Validate generic situations against JSON schemas
3. **Loading**: Import generic situations into database using management commands
4. **Execution**: Run benchmarks via admin interface or CLI
5. **Analysis**: View results through admin interface or export data

## Getting Started

### Prerequisites

- Django backend running with admin access
- Benchmark data directory structure set up
- LLM configurations defined in the system

### Quick Start

1. **Set up directory structure**:
   ```bash
   python manage.py setup_benchmark_structure
   ```

2. **Create a simple scenario** (`backend/testing/benchmark_data/agents/mentor/wheel_generation/simple_test.json`):
   ```json
   {
     "name": "Mentor - Simple Test",
     "description": "Basic mentor agent test",
     "agent_role": "mentor",
     "input_data": {
       "context_packet": {
         "user_text": "I need help creating a life wheel",
         "trust_level": 50
       }
     },
     "metadata": {
       "expected_quality_criteria": {
         "Helpfulness": ["Provides clear guidance"],
         "Tone": ["Supportive and encouraging"]
       }
     },
     "is_active": true
   }
   ```

3. **Load the scenario**:
   ```bash
   python manage.py create_benchmark_scenarios_v3 --directory backend/testing/benchmark_data/agents/mentor/wheel_generation/
   ```

4. **Run the benchmark**:
   - Via Admin UI: Navigate to `/admin/benchmarks/`, select scenario, click "Run Benchmark"
   - Via CLI: `python manage.py run_benchmarks --agent-role mentor --params '{"semantic_evaluation": true}'`

5. **View results**: Navigate to `/admin/benchmarks/history/` to see detailed results

### Directory Structure

```
backend/testing/benchmark_data/
├── agents/
│   ├── mentor/
│   │   ├── wheel_generation/
│   │   ├── discussion/
│   │   └── feedback/
│   ├── orchestrator/
│   └── strategy/
├── workflows/
│   ├── wheel_generation/
│   └── activity_feedback/
├── templates/
│   └── evaluation_criteria/
└── contextual_templates/
```

## Usage Guide

### Creating Benchmark Scenarios

#### Basic Scenario Structure

Every benchmark scenario requires:

```json
{
  "name": "Unique scenario name",
  "description": "What this scenario tests",
  "agent_role": "mentor|orchestrator|strategy",
  "input_data": {
    "context_packet": {
      "user_text": "User input text",
      "trust_level": 50
    }
  },
  "metadata": {
    "expected_quality_criteria": {
      "Dimension1": ["Criterion 1", "Criterion 2"],
      "Dimension2": ["Criterion 3"]
    }
  },
  "is_active": true
}
```

#### Advanced Features

**Tool Mocking**:
```json
"metadata": {
  "mock_tool_responses": {
    "get_user_profile": "{'name': 'Test User', 'trust_level': {tool_input.get('trust_level', 50)}}",
    "search_knowledge": [
      {
        "condition": "tool_input.get('query') == 'specific_topic'",
        "response": "{'results': ['Detailed info on specific_topic']}"
      },
      {
        "condition": "True",
        "response": "{'results': ['General information']}"
      }
    ]
  }
}
```

**Phase-Aware Evaluation**:
```json
"metadata": {
  "evaluation_criteria_by_phase": {
    "foundation": {
      "Clarity": ["Simple, clear language"],
      "Support": ["Basic encouragement"]
    },
    "expansion": {
      "Clarity": ["Clear with some complexity"],
      "Support": ["Detailed guidance"]
    },
    "integration": {
      "Clarity": ["Sophisticated communication"],
      "Support": ["Deep, philosophical support"]
    }
  }
}
```

### Running Benchmarks

#### Via Admin Interface

1. **Dashboard** (`/admin/benchmarks/`):
   - Select scenario from dropdown
   - Configure parameters (runs, semantic evaluation, LLM settings)
   - Click "Run Benchmark"
   - Monitor progress and view results

2. **Management** (`/admin/benchmarks/manage/`):
   - Create and edit scenarios
   - Manage evaluation templates
   - Import/export scenario data

#### Via Command Line

```bash
# Run specific agent role
python manage.py run_benchmarks --agent-role mentor --params '{"semantic_evaluation": true}'

# Run specific scenario
python manage.py run_benchmarks --scenario-name "Mentor - Simple Test"

# Custom configuration
python manage.py run_benchmarks --agent-role mentor --params '{
  "runs": 5,
  "semantic_evaluation": true,
  "agent_llm_model_name": "openai/gpt-4o",
  "llm_temperature": 0.7
}'
```

### Analyzing Results

#### History View (`/admin/benchmarks/history/`)

- **Filtering**: Filter by agent role, tags, date range
- **Visualization**: Charts showing performance trends
- **Detailed Analysis**: Click "View Details" for comprehensive metrics

#### Key Metrics

- **Performance**: Mean/median duration, success rate, standard deviation
- **Operational**: Tool calls, token usage, memory operations
- **Quality**: Semantic scores across multiple dimensions
- **Cost**: Estimated LLM usage costs

### Schema Validation

#### Validate Scenarios
```bash
# Validate all scenarios
python manage.py validate_benchmarks_v3 --validate-structure --validate-files

# Validate specific directory
python manage.py validate_benchmarks_v3 --directory backend/testing/benchmark_data/agents/mentor/

# Auto-fix common issues
python manage.py validate_benchmarks_v3 --fix-metadata

# Export validated scenarios
python manage.py validate_benchmarks_v3 --export
```

#### Schema Types

- **User Profile**: Validates user context in scenarios
- **Situation**: Validates input data structure
- **Evaluation Criteria**: Validates quality assessment criteria
- **Tool Expectation**: Validates mock tool configurations

## Technical Reference

### API Endpoints

#### Benchmark Execution
- `POST /admin/benchmarks/api/run/` - Execute benchmark
- `GET /admin/benchmarks/api/run/<run_id>/` - Get run details
- `POST /admin/benchmarks/api/run-all/` - Run all active scenarios

#### Scenario Management
- `GET/POST /admin/benchmarks/api/scenarios/` - List/create scenarios
- `GET/PUT/DELETE /admin/benchmarks/api/scenarios/<id>/` - Manage specific scenario

#### Template Management
- `GET/POST /admin/benchmarks/api/templates/` - List/create templates
- `GET/PUT/DELETE /admin/benchmarks/api/templates/<id>/` - Manage specific template

### Configuration Parameters

#### Benchmark Parameters
```json
{
  "runs": 3,                    // Number of benchmark runs
  "warmup_runs": 1,            // Number of warmup runs
  "semantic_evaluation": true,  // Enable quality evaluation
  "validate_schema": true,     // Validate before running
  "agent_llm_model_name": "openai/gpt-4o",
  "llm_temperature": 0.7,
  "llm_input_token_price": 0.000005,
  "llm_output_token_price": 0.000015
}
```

#### Tool Mocking Configuration
```json
{
  "mock_tool_responses": {
    "tool_code": "simple_response_template",
    "conditional_tool": [
      {
        "condition": "tool_input.get('param') == 'value'",
        "response": "conditional_response"
      }
    ]
  }
}
```

### Pydantic Models

The system uses comprehensive Pydantic models for validation:

- **`BenchmarkScenario`**: Scenario definition with metadata
- **`BenchmarkRun`**: Run results with performance metrics
- **`EvaluationCriteria`**: Quality assessment criteria
- **`TokenUsage`**: LLM token consumption tracking
- **`StagePerformance`**: Workflow stage timing analysis

## Troubleshooting

### Common Issues

#### Scenario Loading Fails
- **Check JSON syntax**: Validate JSON format
- **Verify schema compliance**: Run `validate_benchmarks_v3`
- **Check file permissions**: Ensure files are readable

#### Benchmark Execution Errors
- **Agent not found**: Verify agent role exists in database
- **LLM configuration**: Check LLM config is properly set up
- **Tool mocking**: Verify mock tool responses are valid

#### Semantic Evaluation Skipped
- **Missing criteria**: Ensure `expected_quality_criteria` is defined
- **Agent response**: Verify agent returns proper response format
- **LLM access**: Check evaluator LLM is accessible

### Debug Commands

```bash
# Validate specific scenario
python manage.py validate_benchmarks_v3 --scenario-id <id>

# Run with detailed logging
python manage.py run_benchmarks --agent-role mentor --params '{"runs": 1}' --verbosity 2

# Check database state
python manage.py shell
>>> from apps.main.models import BenchmarkScenario
>>> BenchmarkScenario.objects.filter(is_active=True).count()
```

### Log Analysis

Key log locations:
- Benchmark execution: `apps.main.services.benchmark_manager`
- Schema validation: `apps.main.services.schema_registry`
- Admin interface: `apps.admin_tools.benchmark.views`

## Related Documentation

- [Contextual Evaluation System](quality/CONTEXTUAL_EVALUATION_SYSTEM.md)
- [Workflow Schema Validation](quality/WORKFLOW_SCHEMA_VALIDATION.md)
- [Testing Guide](../testing/TESTING_GUIDE.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)
- [Benchmarking Archive](../../BENCHMARKING_ARCHIVE.md) - Historical context and preserved ideas

---

*This documentation is actively maintained. For the latest updates, see the git history of this file.*
