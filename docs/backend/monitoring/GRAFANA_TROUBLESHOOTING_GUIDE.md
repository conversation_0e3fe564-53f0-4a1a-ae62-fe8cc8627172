# Grafana Integration Troubleshooting Guide

This guide provides comprehensive troubleshooting steps for the Grafana integration with the benchmark system.

## Quick Start

If you're experiencing issues with Grafana dashboards, run the setup script:

```bash
bash scripts/setup_grafana.sh
```

This script will:
- Set up the complete Grafana environment
- Fix common configuration issues
- Create sample data if none exists
- Verify all components are working

## Common Issues and Solutions

### 1. Dashboards Show "No Data" or Can't Find Datasource

**Symptoms:**
- Dashboards are visible but show no data
- Error: "Data source not found" or similar
- Panels show "No data" even with data in database

**Solution:**
```bash
# 1. Check if datasource UID matches dashboard expectations
cd backend
docker-compose exec db psql -U postgres -d mydb -c "SELECT COUNT(*) FROM grafana_llm_performance;"

# 2. Restart Grafana to reload provisioning
docker-compose restart grafana

# 3. Check Grafana logs for errors
docker-compose logs grafana --tail=50
```

**Root Cause:** The datasource UID in the provisioning configuration must match the UID expected by the dashboards (`postgres-benchmarks`).

### 2. Grafana Shows "Failed to Load" or 500 Errors

**Symptoms:**
- Grafana web interface shows errors
- Cannot access dashboards
- 500 Internal Server Error

**Solution:**
```bash
# 1. Check Grafana container status
cd backend
docker-compose ps grafana

# 2. Check for provisioning errors
docker-compose logs grafana | grep -i error

# 3. Restart with clean state
docker-compose stop grafana
docker volume rm backend_grafana_data
docker-compose up -d grafana
```

### 3. Volume Mount Issues (Windows/Docker Desktop)

**Symptoms:**
- Provisioning files not found
- Empty `/etc/grafana/provisioning/` directory in container
- Dashboards not loading

**Solution:**
The Docker Compose configuration uses absolute paths for Windows compatibility:

```yaml
volumes:
  - D:/goali/repo/monitoring/grafana/provisioning:/etc/grafana/provisioning
  - D:/goali/repo/monitoring/grafana/dashboards:/var/lib/grafana/dashboards
```

If you're on a different system, update the paths in `backend/docker-compose.yml`:

```bash
# For Linux/Mac, use relative paths:
volumes:
  - ../monitoring/grafana/provisioning:/etc/grafana/provisioning
  - ../monitoring/grafana/dashboards:/var/lib/grafana/dashboards
```

### 4. Database Views Are Empty

**Symptoms:**
- Grafana datasource connects successfully
- Database has benchmark runs but views show no data
- Dashboards show "No data"

**Solution:**
```bash
# 1. Check if benchmark runs exist
cd backend
docker-compose exec db psql -U postgres -d mydb -c "SELECT COUNT(*) FROM main_benchmarkrun;"

# 2. Check if views have data
docker-compose exec db psql -U postgres -d mydb -c "SELECT COUNT(*) FROM grafana_llm_performance;"

# 3. If views are empty but runs exist, check view definition
docker-compose exec db psql -U postgres -d mydb -c "\d+ grafana_llm_performance"

# 4. Create sample data if needed
docker cp scripts/create_minimal_sample_data.sql backend-db-1:/tmp/sample_data.sql
docker-compose exec db psql -U postgres -d mydb -f /tmp/sample_data.sql
```

### 5. Authentication Issues

**Symptoms:**
- Cannot log in to Grafana
- "Invalid username or password" errors
- WebSocket authentication errors in logs

**Solution:**
```bash
# Default credentials
Username: admin
Password: admin

# If login fails, reset Grafana data
cd backend
docker-compose stop grafana
docker volume rm backend_grafana_data
docker-compose up -d grafana
```

**Note:** WebSocket authentication errors like "user token not found" are normal for unauthenticated connections and don't affect functionality.

## Verification Steps

### 1. Check All Components

```bash
cd backend

# 1. Verify services are running
docker-compose ps

# 2. Check Grafana health
curl -s http://localhost:3000/api/health

# 3. Verify database connection
docker-compose exec db psql -U postgres -d mydb -c "SELECT version();"

# 4. Check provisioning files exist
ls -la ../monitoring/grafana/provisioning/datasources/
ls -la ../monitoring/grafana/provisioning/dashboards/

# 5. Verify sample data
docker-compose exec db psql -U postgres -d mydb -c "SELECT COUNT(*) FROM grafana_llm_performance;"
```

### 2. Test Dashboard Functionality

1. Open Grafana: http://localhost:3000
2. Login with admin/admin
3. Navigate to Dashboards
4. Check folders: Benchmark Analytics, LLM Performance, etc.
5. Open any dashboard and verify data is displayed

### 3. Validate Data Flow

```bash
# Check the complete data pipeline
cd backend

# 1. Raw benchmark data
docker-compose exec db psql -U postgres -d mydb -c "SELECT id, llm_config_id, success_rate, execution_date FROM main_benchmarkrun LIMIT 3;"

# 2. Processed view data
docker-compose exec db psql -U postgres -d mydb -c "SELECT llm_model, success_rate, estimated_cost FROM grafana_llm_performance LIMIT 3;"

# 3. Cost analytics view
docker-compose exec db psql -U postgres -d mydb -c "SELECT llm_model, total_cost, total_tokens FROM grafana_cost_analytics LIMIT 3;"
```

## Advanced Troubleshooting

### Debug Grafana Provisioning

```bash
# 1. Check provisioning logs
cd backend
docker-compose logs grafana | grep -i provision

# 2. Verify file permissions and content
docker-compose exec grafana ls -la /etc/grafana/provisioning/
docker-compose exec grafana cat /etc/grafana/provisioning/datasources/datasources.yaml

# 3. Test datasource connection manually
docker-compose exec grafana curl -X GET http://localhost:3000/api/datasources
```

### Database Schema Issues

```bash
# 1. Check if required views exist
cd backend
docker-compose exec db psql -U postgres -d mydb -c "\dv grafana_*"

# 2. Verify view definitions match dashboard queries
docker-compose exec db psql -U postgres -d mydb -c "\d+ grafana_llm_performance"

# 3. Check for missing migrations
docker-compose run --rm web python manage.py showmigrations main
```

### Performance Issues

```bash
# 1. Check database performance
cd backend
docker-compose exec db psql -U postgres -d mydb -c "EXPLAIN ANALYZE SELECT * FROM grafana_llm_performance LIMIT 10;"

# 2. Monitor resource usage
docker stats backend-grafana-1 backend-db-1

# 3. Check Grafana query performance
# Navigate to Grafana > Explore > Query Inspector
```

## Maintenance

### Regular Maintenance Tasks

1. **Clean up old data** (if needed):
```bash
cd backend
docker-compose exec db psql -U postgres -d mydb -c "DELETE FROM main_benchmarkrun WHERE execution_date < NOW() - INTERVAL '30 days';"
```

2. **Update dashboards**:
- Export modified dashboards from Grafana UI
- Save to `monitoring/grafana/dashboards/` directory
- Commit to version control

3. **Backup Grafana configuration**:
```bash
# Export datasources and dashboards
docker-compose exec grafana grafana-cli admin export-dashboard
```

### Testing Changes

Before making changes to the Grafana configuration:

1. Run the test suite:
```bash
cd backend
python -m pytest apps/main/tests/test_grafana_config_validation.py -v
```

2. Test with sample data:
```bash
bash scripts/setup_grafana.sh
```

3. Verify dashboards manually in the web interface

## Getting Help

If you continue to experience issues:

1. Check the logs for specific error messages
2. Verify your environment matches the requirements
3. Run the setup script with verbose output
4. Consult the main integration documentation: `docs/backend/GRAFANA_INTEGRATION.md`

## Quick Reference

- **Grafana URL**: http://localhost:3000
- **Default Login**: admin/admin
- **Setup Script**: `bash scripts/setup_grafana.sh`
- **Test Command**: `python -m pytest apps/main/tests/test_grafana_config_validation.py -v`
- **Sample Data**: `scripts/create_minimal_sample_data.sql`
