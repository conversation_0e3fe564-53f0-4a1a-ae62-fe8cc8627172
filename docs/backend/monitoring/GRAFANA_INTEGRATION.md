# Grafana Integration for Benchmark Visualization

## Overview

This document describes the comprehensive Grafana integration for visualizing benchmark results, LLM performance metrics, and contextual evaluation insights. The integration provides powerful dashboards to easily identify strengths and weaknesses of LLM configurations and prompt versions.

## ✅ Current Status

**All components are working correctly:**
- ✅ Grafana service running on port 3000
- ✅ PostgreSQL datasource configured with UID `postgres-benchmarks`
- ✅ Dashboard provisioning working
- ✅ Database views populated with sample data
- ✅ All dashboard folders created and accessible
- ✅ Volume mounts configured correctly
- ✅ Authentication working (admin/admin)
- ✅ Comprehensive test suite passing
- ✅ Automated setup script available
- ✅ **FIXED**: Datasource configuration issues resolved
- ✅ **FIXED**: Dashboard connectivity working properly

**Quick Access:**
- **Grafana URL**: http://localhost:3000
- **Setup Command**: `bash scripts/setup_grafana.sh`
- **Test Command**: `bash scripts/test_monitoring_system.sh`
- **Troubleshooting**: See troubleshooting section below

## Architecture

### Components

1. **Grafana Service**: Main visualization platform
2. **PostgreSQL Data Source**: Direct connection to benchmark database
3. **Optimized Database Views**: Pre-computed analytics views for performance
4. **Provisioned Dashboards**: Automatically deployed dashboard configurations
5. **Prometheus Integration**: Optional system metrics collection

### Data Flow

```
Benchmark Runs → PostgreSQL → Analytics Views → Grafana → Dashboards
```

## Database Views

The integration includes four optimized database views designed for Grafana analytics:

### 1. `grafana_llm_performance`
Comprehensive LLM performance metrics including:
- Success rates and semantic scores
- Token usage and costs
- Context variables (trust level, mood, stress)
- Performance categorization
- Cost per token calculations

### 2. `grafana_prompt_analytics`
Prompt engineering effectiveness metrics:
- Prompt effectiveness scores
- Version-to-version comparisons
- Agent role performance
- Semantic quality trends

### 3. `grafana_contextual_evaluation`
Contextual evaluation insights:
- Trust phase categorization (Foundation/Expansion/Integration)
- Mood quadrant analysis
- Stress and time pressure categories
- Context-aware performance metrics

### 4. `grafana_cost_analytics`
Cost and resource optimization:
- Hourly and daily cost aggregations
- Token efficiency metrics
- Cost per success calculations
- Model cost comparisons

## Dashboard Categories

### 1. LLM Performance Dashboards
**Location**: `/monitoring/grafana/dashboards/llm-performance/`

- **LLM Performance Overview**: Success rates and response times by model
- Real-time performance monitoring
- Model comparison visualizations
- Performance trend analysis

### 2. Prompt Engineering Dashboards
**Location**: `/monitoring/grafana/dashboards/prompt-engineering/`

- **Prompt Effectiveness Dashboard**: Version comparison and optimization insights
- A/B testing visualization
- Prompt iteration tracking
- Agent role performance analysis

### 3. Contextual Evaluation Dashboards
**Location**: `/monitoring/grafana/dashboards/contextual-evaluation/`

- **Contextual Insights Dashboard**: Trust phase and mood analysis
- Context variable distribution
- Performance by psychological state
- Adaptive evaluation metrics

### 4. Cost Analytics Dashboards
**Location**: `/monitoring/grafana/dashboards/benchmark-analytics/`

- **Cost Analytics Dashboard**: Resource optimization and cost tracking
- Token usage patterns
- Cost efficiency by model
- Budget monitoring and forecasting

## Quick Start Guide

### 🚀 **Option 1: Automated Setup (Recommended)**
```bash
# Run the comprehensive setup script
./scripts/setup_grafana.sh

# Validate the monitoring system
./scripts/test_monitoring_system.sh
```

The setup script will:
- ✅ Start all required services (database, Grafana, Prometheus)
- ✅ Apply database migrations to create analytics views
- ✅ Verify service health and connectivity
- ✅ Check for sample data and provide guidance
- ✅ Display access information and next steps

The test script validates:
- ✅ All services running properly
- ✅ Database connectivity and views
- ✅ Grafana datasource configuration
- ✅ Dashboard provisioning and queries

### 🔧 **Option 2: Manual Setup**

#### 1. Start Services
```bash
cd backend
docker-compose up -d grafana prometheus
```

#### 2. Apply Database Migrations
```bash
docker-compose run --rm web python manage.py migrate
```

#### 3. Access Grafana
- **URL**: http://localhost:3000
- **Username**: admin
- **Password**: admin (change on first login)

#### 4. Verify Data Sources
The PostgreSQL data source should be automatically provisioned. Verify connection in:
- Configuration → Data Sources → PostgreSQL-Benchmarks

### 🎯 **First-Time Usage**

#### Step 1: Change Default Password
1. Login with admin/admin
2. Grafana will prompt to change password
3. Set a secure password for production use

#### Step 2: Explore Dashboards
Navigate to **Dashboards** → **Browse** to find:
- 📊 **LLM Performance Overview**
- 🧠 **Contextual Evaluation Insights**
- 💰 **Cost Analytics Dashboard**
- 📝 **Prompt Effectiveness Dashboard**
- 🔍 **Advanced Analytics Dashboard**

#### Step 3: Populate with Data
If dashboards are empty:
```bash
# Run sample benchmarks to populate data
cd backend
docker-compose run --rm web python manage.py run_benchmarks
```

#### Step 4: Customize Time Ranges
- Use the time picker (top right) to adjust data ranges
- Start with "Last 24 hours" or "Last 7 days"
- Expand as you accumulate more benchmark data

## Dashboard Usage

### Key Metrics to Monitor

1. **LLM Strengths & Weaknesses**:
   - Success rate trends by model
   - Response time consistency
   - Cost efficiency ratios
   - Context-specific performance

2. **Prompt Optimization**:
   - Effectiveness score improvements
   - Version comparison metrics
   - Agent role specialization
   - Semantic quality trends

3. **Contextual Insights**:
   - Trust phase performance patterns
   - Mood-dependent effectiveness
   - Stress impact analysis
   - Environmental adaptation

### Advanced Analytics

#### Time-Series Analysis
- Trend identification over time
- Seasonal performance patterns
- Regression detection
- Forecasting capabilities

#### Comparative Analysis
- Model-to-model comparisons
- Prompt version A/B testing
- Context variable impact assessment
- Cost-benefit analysis

#### Anomaly Detection
- Performance outlier identification
- Cost spike detection
- Quality degradation alerts
- Context-specific anomalies

## Query Examples

### LLM Performance Comparison
```sql
SELECT
  execution_date as time,
  AVG(success_rate * 100) as "Success Rate %",
  llm_model
FROM grafana_llm_performance
WHERE $__timeFilter(execution_date)
GROUP BY execution_date, llm_model
ORDER BY execution_date
```

### Contextual Performance Analysis
```sql
SELECT
  trust_phase,
  ROUND(AVG(success_rate)::numeric, 3) as avg_success_rate,
  ROUND(AVG(semantic_score)::numeric, 2) as avg_semantic_score,
  COUNT(*) as total_runs
FROM grafana_contextual_evaluation
WHERE $__timeFilter(execution_date)
GROUP BY trust_phase
ORDER BY avg_success_rate DESC
```

### Cost Efficiency Tracking
```sql
SELECT
  execution_date_hour as time,
  SUM(estimated_cost) as "Total Cost",
  llm_model
FROM grafana_cost_analytics
WHERE $__timeFilter(execution_date)
GROUP BY execution_date_hour, llm_model
ORDER BY execution_date_hour
```

## Best Practices

### Dashboard Design
1. **Use appropriate time ranges** for different analysis types
2. **Implement proper filtering** by model, agent, or context
3. **Combine multiple visualizations** for comprehensive insights
4. **Set up alerting** for critical performance thresholds

### Performance Optimization
1. **Leverage database views** for complex calculations
2. **Use time-based indexing** for large datasets
3. **Implement data retention policies** for historical data
4. **Monitor query performance** and optimize as needed

### Security Considerations
1. **Restrict database access** to read-only for Grafana
2. **Use environment variables** for sensitive configuration
3. **Implement proper authentication** for Grafana access
4. **Regular security updates** for all components

## 🔧 Recent Fixes Applied

### **Issue Resolution Summary**

The following critical issues were identified and resolved:

#### **1. Datasource Configuration Issues**
- **Problem**: Dashboards expecting UID `postgres-benchmarks` but datasource had different UID
- **Root Cause**: Incorrect datasource type (`postgres` instead of `grafana-postgresql-datasource`)
- **Solution**:
  - Fixed datasource type in `monitoring/grafana/provisioning/datasources/datasources.yaml`
  - Updated configuration with proper PostgreSQL plugin settings
  - Removed conflicting manually created datasources

#### **2. Authentication Failures**
- **Problem**: `pq: password authentication failed for user "postgres"`
- **Root Cause**: Conflicting datasource configurations
- **Solution**:
  - Standardized on single provisioned datasource
  - Added `secureJsonData` for password handling
  - Set proper connection parameters

#### **3. Dashboard Connectivity**
- **Problem**: "Datasource postgres-benchmarks was not found" errors
- **Root Cause**: UID mismatch between provisioned and actual datasources
- **Solution**:
  - Ensured consistent UID `postgres-benchmarks` across all configurations
  - Verified dashboard JSON files reference correct datasource UID

#### **4. System Validation**
- **Enhancement**: Added comprehensive test script
- **Benefit**: Validates entire monitoring stack automatically
- **Location**: `scripts/test_monitoring_system.sh`

### **Architecture Decision: PostgreSQL vs Prometheus**

**Decision**: Use **direct PostgreSQL connection** for dashboard data

**Rationale**:
- ✅ Benchmark data already in PostgreSQL with optimized views
- ✅ Direct SQL queries provide more flexibility for complex analytics
- ✅ Real-time data access without additional data pipeline complexity
- ✅ Better performance for time-series analysis of benchmark results
- ✅ Prometheus remains available for optional system metrics

## Troubleshooting

### 🚨 **Common Issues & Solutions**

#### 1. **Grafana Not Starting**
```bash
# Check service status
docker-compose ps grafana

# View detailed logs
docker-compose logs grafana

# Common fixes:
docker-compose restart grafana
# or
docker-compose down && docker-compose up -d grafana
```

**Possible causes:**
- Port 3000 already in use
- Insufficient memory/disk space
- Configuration file errors

#### 2. **Data Source Connection Failed**
```bash
# Verify PostgreSQL is running
docker-compose ps db

# Test database connectivity
docker-compose exec db pg_isready -U postgres

# Check database exists
docker-compose exec db psql -U postgres -l
```

**Solutions:**
- Ensure database service is healthy
- Verify credentials in `monitoring/grafana/provisioning/datasources/postgres.yml`
- Check network connectivity between containers

#### 3. **Empty Dashboards / No Data**
```bash
# Check if analytics views exist
docker-compose exec db psql -U postgres -d mydb -c "\dv grafana_*"

# Check for benchmark data
docker-compose exec db psql -U postgres -d mydb -c "SELECT COUNT(*) FROM main_benchmarkrun;"

# Verify view data
docker-compose exec db psql -U postgres -d mydb -c "SELECT COUNT(*) FROM grafana_llm_performance;"
```

**Solutions:**
- Run migrations: `docker-compose run --rm web python manage.py migrate`
- Generate sample data: `docker-compose run --rm web python manage.py run_benchmarks`
- Adjust time range in dashboards (top-right time picker)
- Check that time zone settings match your data

#### 4. **Dashboard Import Errors**
```bash
# Check dashboard files exist
ls -la monitoring/grafana/dashboards/*/

# Verify JSON syntax
python -m json.tool monitoring/grafana/dashboards/llm-performance/llm-performance-overview.json
```

**Solutions:**
- Restart Grafana to reload provisioning: `docker-compose restart grafana`
- Check dashboard JSON syntax for errors
- Verify data source UID matches in dashboards (`postgres-benchmarks`)
- Manually import dashboards via Grafana UI if auto-provisioning fails

#### 5. **Performance Issues / Slow Queries**
```bash
# Check query performance in database
docker-compose exec db psql -U postgres -d mydb -c "EXPLAIN ANALYZE SELECT * FROM grafana_llm_performance LIMIT 100;"
```

**Solutions:**
- Use smaller time ranges for large datasets
- Add database indexes on frequently queried columns
- Optimize dashboard queries using Grafana query inspector
- Consider data retention policies for historical data

### 🔍 **Debugging Tools**

#### Grafana Query Inspector
1. Open any dashboard panel
2. Click panel title → **Inspect** → **Query**
3. View:
   - Generated SQL queries
   - Query execution times
   - Raw data returned
   - Error messages

#### Database Query Testing
```sql
-- Test analytics views directly
SELECT * FROM grafana_llm_performance LIMIT 5;
SELECT * FROM grafana_contextual_evaluation LIMIT 5;
SELECT * FROM grafana_cost_analytics LIMIT 5;
SELECT * FROM grafana_prompt_analytics LIMIT 5;

-- Check data freshness
SELECT MAX(execution_date) FROM grafana_llm_performance;

-- Verify context variable extraction
SELECT DISTINCT trust_phase FROM grafana_contextual_evaluation;
SELECT DISTINCT mood_quadrant FROM grafana_contextual_evaluation;
```

#### Service Health Checks
```bash
# Check all services
docker-compose ps

# Check service logs
docker-compose logs grafana
docker-compose logs prometheus
docker-compose logs db

# Check resource usage
docker stats
```

### 🛠️ **Advanced Troubleshooting**

#### Reset Grafana Configuration
```bash
# Stop services
docker-compose down

# Remove Grafana data (WARNING: loses custom dashboards)
docker volume rm backend_grafana-storage

# Restart with fresh configuration
docker-compose up -d grafana
```

#### Database Connection Testing
```bash
# Test from Grafana container
docker-compose exec grafana nc -zv db 5432

# Test database queries
docker-compose exec grafana psql -h db -U postgres -d mydb -c "SELECT 1;"
```

#### Manual Dashboard Import
If auto-provisioning fails:
1. Access Grafana UI at http://localhost:3000
2. Go to **Dashboards** → **Import**
3. Upload JSON files from `monitoring/grafana/dashboards/*/`
4. Select **PostgreSQL-Benchmarks** as data source

### 📞 **Getting Help**

#### Log Collection
```bash
# Collect all relevant logs
docker-compose logs grafana > grafana.log
docker-compose logs db > database.log
docker-compose logs prometheus > prometheus.log

# Check system resources
docker system df
docker system events --since 1h
```

#### Configuration Validation
```bash
# Validate YAML files
python -c "import yaml; yaml.safe_load(open('monitoring/grafana/provisioning/datasources/postgres.yml'))"

# Validate JSON dashboards
find monitoring/grafana/dashboards -name "*.json" -exec python -m json.tool {} \; > /dev/null
```

For persistent issues:
1. Check the troubleshooting section above
2. Review service logs for specific error messages
3. Verify all configuration files are properly formatted
4. Consult Grafana documentation for advanced configuration options

## Future Enhancements

### Planned Features
1. **Machine Learning Integration**: Predictive analytics and trend forecasting
2. **Advanced Alerting**: Context-aware performance alerts
3. **Custom Metrics**: User-defined KPIs and calculations
4. **Export Capabilities**: Automated report generation
5. **Real-time Streaming**: Live benchmark result updates

### Integration Opportunities
1. **Slack/Teams Notifications**: Alert integration
2. **CI/CD Pipeline Integration**: Automated performance reporting
3. **A/B Testing Framework**: Statistical significance testing
4. **Cost Optimization Recommendations**: Automated suggestions

## Support

For issues or questions regarding the Grafana integration:
1. Check the troubleshooting section above
2. Review Grafana logs: `docker-compose logs grafana`
3. Verify database connectivity and view definitions
4. Consult the Grafana documentation for advanced configuration
