# Prompt Content & Conversation Analysis - Quick Reference

## Overview

The Grafana dashboards now provide **complete visibility** into prompt content and agent communications, addressing the critical need to see:
- **Actual prompt text** used in each benchmark run
- **Raw agent communications** and conversation flow
- **Performance correlation** with prompt and conversation characteristics

## New Dashboards

### 1. Prompt Content Analysis Dashboard
**URL**: `http://localhost:3000/d/prompt-content-analysis`

**What it shows**:
- Full prompt text for each benchmark run
- Prompt effectiveness scores
- Prompt length and style analysis
- Performance metrics correlation

**Use cases**:
- Review exact prompts used in high/low performing runs
- Identify optimal prompt patterns
- Analyze prompt length vs effectiveness
- Compare prompt styles across scenarios

### 2. Conversation Flow Analysis Dashboard
**URL**: `http://localhost:3000/d/conversation-flow-analysis`

**What it shows**:
- Raw conversation data from agent communications
- Semantic evaluation details
- Stage performance breakdown
- Communication quality assessment

**Use cases**:
- See exactly how agents communicated
- Analyze conversation patterns
- Understand semantic evaluation reasoning
- Identify communication bottlenecks

### 3. Enhanced Advanced Analytics Dashboard
**New Panel**: "Detailed Analysis - Prompt Content & Conversation Data"

**What it shows**:
- Combined prompt and conversation data
- Complete benchmark run context
- Performance optimization insights

## Key Database Views

### `grafana_prompt_content`
- Full prompt text (`full_prompt_content`)
- Prompt style categorization
- Effectiveness scoring
- Performance metrics

### `grafana_conversation_analysis`
- Raw conversation data (`conversation_data`)
- Semantic evaluation details
- Tool usage breakdown
- Communication quality indicators

### `grafana_detailed_benchmark_analysis`
- Combined prompt and conversation data
- Complete benchmark context
- Efficiency metrics

## Quick Usage Guide

### To Analyze Poor Performance:
1. Go to any dashboard and filter by low success rate or semantic score
2. Click on **Prompt Content Analysis** to see the exact prompt used
3. Click on **Conversation Flow Analysis** to see how agents communicated
4. Identify patterns in failed conversations

### To Replicate Success:
1. Filter dashboards by high performance metrics
2. Extract successful prompt patterns from **Prompt Content Analysis**
3. Analyze effective conversation flows in **Conversation Flow Analysis**
4. Apply insights to new scenarios

### To Optimize Prompts:
1. Use **Prompt Content Analysis** to compare prompt lengths and styles
2. Correlate prompt characteristics with performance metrics
3. Identify optimal prompt patterns
4. Test variations based on successful examples

### To Debug Agent Communications:
1. Use **Conversation Flow Analysis** to see raw communication data
2. Analyze semantic evaluation reasoning
3. Identify communication bottlenecks
4. Understand stage-by-stage performance issues

## Dashboard Features

### Content Display
- **Large cell heights** for readability
- **Text wrapping** for prompt content
- **JSON view** for conversation data
- **Expandable content** areas

### Filtering Options
- Agent role
- LLM model
- Time range
- Performance metrics

### Performance Metrics
- Success rate with gradient gauges
- Semantic scores with visual indicators
- Cost efficiency analysis
- Token usage breakdown

## Technical Notes

### Data Sources
- All views are based on `main_benchmarkrun` table
- Joined with agent, scenario, and LLM configuration data
- Real-time updates from live benchmark runs

### Performance Optimization
- Limited result sets for large content display
- Optimized queries for content retrieval
- Efficient JSON formatting
- Responsive design

### Access Requirements
- Grafana admin credentials (admin/admin by default)
- Database connection to benchmark data
- Proper dashboard provisioning

## Troubleshooting

### If dashboards don't show data:
1. Check if benchmark runs exist in database
2. Verify Grafana database connection
3. Ensure migrations are applied (especially 0009)
4. Restart Grafana service if needed

### If content is not displaying properly:
1. Check browser compatibility with JSON view
2. Verify cell height settings
3. Ensure text wrapping is enabled
4. Check for data truncation limits

### If performance is slow:
1. Reduce time range filter
2. Limit result sets further
3. Check database query performance
4. Consider data archiving for old runs

## Next Steps

### Recommended Workflow:
1. **Start with Advanced Analytics** for overview
2. **Drill down to Prompt Content** for prompt analysis
3. **Examine Conversation Flow** for communication details
4. **Iterate and optimize** based on insights

### Best Practices:
- Regular review of prompt performance patterns
- Documentation of successful prompt templates
- Systematic testing of prompt variations
- Continuous monitoring of conversation quality

### Future Enhancements:
- Automated prompt pattern detection
- Conversation flow visualization
- A/B testing capabilities
- Performance prediction models

## Summary

The enhanced dashboards provide **complete transparency** into:
- What prompts are actually being used
- How agents are communicating
- Why certain runs succeed or fail
- How to optimize for better performance

This transforms the monitoring system into a **comprehensive optimization platform** that enables data-driven improvements to both prompt engineering and model selection.
