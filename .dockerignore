# Git
.git
.gitignore

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.pytest_cache/
.env*
*.log
*.sqlite3
coverage_html/
benchmark_results/
backend/coverage_html/
backend/coverage.json
backend/coverage_metadata.json
backend/config/db.sqlite3

# Node.js
node_modules/
frontend/node_modules/
frontend/build/
frontend/dist/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE/Editor specific
.vscode/
.idea/

# OS specific
.DS_Store
Thumbs.db
