## Debugging Workflow

### For Django Application Debugging

1. Set breakpoints in your Django application code (views, models, etc.)
2. In VS Code, select the "Django: Remote Attach" configuration (port 5678)
3. Start the debugger
4. Access your Django application through the browser to trigger breakpoints


### For Celery Worker Debugging (Optional)

1. Set breakpoints in your Celery task code
2. In VS Code, select the "Django: Celery Worker" configuration (port 5680)
3. Start the debugger
4. Start the Celery worker with debugging:
   ```bash
   docker-compose exec web ./debug_celery.sh
   ```

## Technical Explanation

This solution follows the industry standard for debugging multiple processes in a Docker environment:

1. **Port Separation**: Each debuggable process uses a distinct debug port to avoid conflicts
2. **Configuration Alignment**: The VS Code configuration is updated to match these port assignments
3. **Container Configuration**: Docker Compose exposes all necessary ports to the host
4. **Standard Tooling**: Leverages standard debugpy patterns for Django/Python debugging