// frontend/src/types/api.ts

/**
 * Defines the structure for WebSocket messages based on docs/api/ApiContract.md
 */

// ==================================
// Base Message Structure
// ==================================
export interface WebSocketMessage<T extends string, C> {
  type: T;
  content: C;
  is_user?: boolean; // Only for chat_message type from server
  status?: 'processing' | 'completed' | 'error'; // Only for processing_status type
  wheel?: WheelDataContent; // Only for wheel_data type
  workflow_id?: string; // Only for workflow_status type
}

// ==================================
// Client -> Server Message Contents
// ==================================
export interface ChatMessageToServerContent {
  message: string;
  user_profile_id: string;
  timestamp?: string; // ISO-8601
  metadata?: {
    requested_workflow?: 'wheel_generation' | 'pre_spin_feedback' | 'activity_feedback' | 'discussion';
    // Other potential metadata keys TBD
  };
}

export interface SpinResultToServerContent {
  activity_tailored_id: string;
  name: string;
  description?: string;
  user_profile_id: string;
}

export interface WorkflowStatusRequestToServerContent {
  workflow_id: string;
}

// ==================================
// Server -> Client Message Contents
// ==================================
export interface SystemMessageFromServerContent {
  content: string;
}

export interface ChatMessageFromServerContent {
  content: string;
  is_user: boolean;
}

export interface ProcessingStatusFromServerContent {
  status: 'processing' | 'completed' | 'error';
}

export interface WheelItem {
  id: string;
  name: string;
  description: string;
  percentage: number;
  color: string;
  domain: string;
  base_challenge_rating: number;
  activity_tailored_id: string;
}

export interface WheelDataContent {
  name: string;
  items: WheelItem[];
}

export interface WheelDataFromServerContent {
  wheel: WheelDataContent;
}

export interface ErrorFromServerContent {
  content: string;
}

export interface WorkflowStatusFromServerContent {
  workflow_id: string;
  status: 'initiated' | 'processing' | 'completed' | 'failed' | 'unknown';
}

// --- NEW ---
export interface DebugInfoFromServerContent {
  timestamp: string; // ISO-8601
  source: string; // e.g., "Dispatcher", "Agent:MentorAgent"
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  details?: Record<string, any>; // Optional structured data
}
// --- END NEW ---

// ==================================
// Specific Message Types (Union Type)
// ==================================

// Client -> Server
export type ClientToServerMessage =
  | WebSocketMessage<'chat_message', ChatMessageToServerContent>
  | WebSocketMessage<'spin_result', SpinResultToServerContent>
  | WebSocketMessage<'workflow_status_request', WorkflowStatusRequestToServerContent>;

// Server -> Client
export type ServerToClientMessage =
  | WebSocketMessage<'system_message', SystemMessageFromServerContent>
  | WebSocketMessage<'chat_message', ChatMessageFromServerContent>
  | WebSocketMessage<'processing_status', ProcessingStatusFromServerContent>
  | WebSocketMessage<'wheel_data', WheelDataFromServerContent>
  | WebSocketMessage<'error', ErrorFromServerContent>
  | WebSocketMessage<'workflow_status', WorkflowStatusFromServerContent>
  | WebSocketMessage<'debug_info', DebugInfoFromServerContent>; // Added new type
