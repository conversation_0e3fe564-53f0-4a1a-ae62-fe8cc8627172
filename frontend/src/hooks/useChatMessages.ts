/**
 * useChatMessages.ts
 *
 * A custom hook for managing chat messages with the WebSocket connection.
 * This hook transforms the raw WebSocket data into a component-friendly interface,
 * handling chat-specific behaviors like message formatting, typing indicators,
 * and scroll management.
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useWebSocket, StoredMessage } from '../contexts/WebSocketContext';
import { ChatMessageData } from '../services/WebSocketManager';

// Message type enum for UI rendering
export enum MessageType {
  User = 'user',
  Assistant = 'assistant',
  System = 'system'
}

// Enhanced chat message interface for UI consumption
export interface ChatMessage {
  id: string;
  content: string;
  type: MessageType;
  timestamp: string;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'error';
}

export interface UseChatMessagesResult {
  // Message data
  messages: ChatMessage[];
  isTyping: boolean;
  
  // Message actions
  sendMessage: (content: string, metadata?: Record<string, any>) => boolean;
  clearMessages: () => void;
  
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  
  // Scroll management
  scrollToBottom: () => void;
  scrollToMessage: (messageId: string) => void;
}

export const useChatMessages = (): UseChatMessagesResult => {
  // Get data and methods from the WebSocket context
  const { 
    chatMessages,
    systemMessages,
    isTyping,
    isConnected,
    isConnecting,
    connectionError,
    sendChatMessage,
    clearChatHistory
  } = useWebSocket();
  
  // Refs for scroll management
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  
  // Convert stored messages to UI-friendly format
  const messages = useMemo(() => {
    console.log('[useChatMessages] Processing messages - chat count:', chatMessages.length, 
                'system count:', systemMessages.length);
    
    // Combine chat and system messages
    const combinedMessages: StoredMessage[] = [
      ...chatMessages,
      ...systemMessages
    ];
    
    // Sort by timestamp
    const sortedMessages = [...combinedMessages].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    
    console.log('[useChatMessages] Combined and sorted messages count:', sortedMessages.length);
    
    // Transform to ChatMessage format
    const formattedMessages = sortedMessages.map(message => {
      const data = message.data;
      
      // Determine message type
      let type = MessageType.System;
      if (data.type === 'chat_message') {
        type = data.is_user ? MessageType.User : MessageType.Assistant;
      }
      
      const content = data.content || data.message || '';
      
      console.log(`[useChatMessages] Processing message: type=${data.type}, id=${message.id}, content="${content.substring(0, 30)}${content.length > 30 ? '...' : ''}"`);
      
      // Create UI message
      return {
        id: message.id,
        content,
        type,
        timestamp: message.timestamp,
        status: 'delivered' as 'delivered' // Type assertion to match the expected union type
      };
    });
    
    console.log('[useChatMessages] Returning', formattedMessages.length, 'formatted messages');
    return formattedMessages;
  }, [chatMessages, systemMessages]);
  
  // Send a chat message
  const sendMessage = useCallback((content: string, metadata: Record<string, any> = {}) => {
    return sendChatMessage(content, metadata);
  }, [sendChatMessage]);
  
  // Clear all messages
  const clearMessages = useCallback(() => {
    clearChatHistory();
  }, [clearChatHistory]);
  
  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);
  
  // Scroll to specific message
  const scrollToMessage = useCallback((messageId: string) => {
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, []);
  
  // Auto-scroll when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages.length, scrollToBottom]);
  
  return {
    messages,
    isTyping,
    sendMessage,
    clearMessages,
    isConnected,
    isConnecting,
    connectionError,
    scrollToBottom,
    scrollToMessage
  };
};
