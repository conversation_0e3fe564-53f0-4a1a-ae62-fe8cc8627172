/**
 * useActivity.ts
 * 
 * A custom hook for managing activity data and interactions.
 * This hook abstracts the WebSocket operations related to activity selection, 
 * feedback, and interactions.
 */

import { useState, useEffect, useCallback } from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';

// Activity status types
type ActivityStatus = 'pending' | 'accepted' | 'completed' | 'rejected';

// Activity data structure
interface ActivityData {
  id: string;
  name: string;
  description?: string;
  domain?: string;
  challengeRating?: number;
}

// Activity state interface
interface ActivityState {
  // Current activity data
  currentActivity: ActivityData | null;
  
  // Activity status
  status: ActivityStatus;
  
  // Loading and error states
  isLoading: boolean;
  error: string | null;
}

// Activity actions interface
interface ActivityActions {
  // Accept the current activity
  acceptActivity: () => boolean;
  
  // Reject the current activity
  rejectActivity: (reason?: string) => boolean;
  
  // Mark the current activity as completed
  completeActivity: (feedback?: string) => boolean;
  
  // Set a new current activity
  setActivity: (activityData: ActivityData) => void;
  
  // Reset the activity state
  resetActivity: () => void;
}

/**
 * Hook for interacting with activities
 * 
 * @returns Activity state and actions
 */
export const useActivity = (): [ActivityState, ActivityActions] => {
  // Access the WebSocket context
  const { 
    sendChatMessage,
    isProcessing,
    connectionError
  } = useWebSocket();
  
  // Local state management
  const [state, setState] = useState<ActivityState>({
    currentActivity: null,
    status: 'pending',
    isLoading: false,
    error: null
  });
  
  // Update loading state based on processing status
  useEffect(() => {
    setState(prevState => ({
      ...prevState,
      isLoading: isProcessing
    }));
  }, [isProcessing]);
  
  // Update error state from connection errors
  useEffect(() => {
    if (connectionError) {
      setState(prevState => ({
        ...prevState,
        error: connectionError
      }));
    }
  }, [connectionError]);
  
  // Accept the current activity
  const acceptActivity = useCallback(() => {
    if (!state.currentActivity) {
      setState(prevState => ({
        ...prevState,
        error: 'No current activity to accept'
      }));
      return false;
    }
    
    // Send message to backend
    const success = sendChatMessage('accept_activity', {
      type: 'activity_feedback',
      activity_id: state.currentActivity.id,
      action: 'accept'
    });
    
    if (success) {
      setState(prevState => ({
        ...prevState,
        status: 'accepted',
        error: null
      }));
    } else {
      setState(prevState => ({
        ...prevState,
        error: 'Failed to accept activity'
      }));
    }
    
    return success;
  }, [state.currentActivity, sendChatMessage]);
  
  // Reject the current activity
  const rejectActivity = useCallback((reason?: string) => {
    if (!state.currentActivity) {
      setState(prevState => ({
        ...prevState,
        error: 'No current activity to reject'
      }));
      return false;
    }
    
    // Send message to backend
    const success = sendChatMessage('reject_activity', {
      type: 'activity_feedback',
      activity_id: state.currentActivity.id,
      action: 'reject',
      reason: reason || 'No reason provided'
    });
    
    if (success) {
      setState(prevState => ({
        ...prevState,
        status: 'rejected',
        error: null
      }));
    } else {
      setState(prevState => ({
        ...prevState,
        error: 'Failed to reject activity'
      }));
    }
    
    return success;
  }, [state.currentActivity, sendChatMessage]);
  
  // Mark the current activity as completed
  const completeActivity = useCallback((feedback?: string) => {
    if (!state.currentActivity) {
      setState(prevState => ({
        ...prevState,
        error: 'No current activity to complete'
      }));
      return false;
    }
    
    // Send message to backend
    const success = sendChatMessage('complete_activity', {
      type: 'activity_feedback',
      activity_id: state.currentActivity.id,
      action: 'complete',
      feedback: feedback || ''
    });
    
    if (success) {
      setState(prevState => ({
        ...prevState,
        status: 'completed',
        error: null
      }));
    } else {
      setState(prevState => ({
        ...prevState,
        error: 'Failed to complete activity'
      }));
    }
    
    return success;
  }, [state.currentActivity, sendChatMessage]);
  
  // Set a new current activity
  const setActivity = useCallback((activityData: ActivityData) => {
    setState(prevState => ({
      ...prevState,
      currentActivity: activityData,
      status: 'pending',
      error: null
    }));
  }, []);
  
  // Reset the activity state
  const resetActivity = useCallback(() => {
    setState({
      currentActivity: null,
      status: 'pending',
      isLoading: false,
      error: null
    });
  }, []);
  
  // Actions object
  const actions: ActivityActions = {
    acceptActivity,
    rejectActivity,
    completeActivity,
    setActivity,
    resetActivity
  };
  
  return [state, actions];
};