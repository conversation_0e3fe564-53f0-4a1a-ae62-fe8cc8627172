/**
 * useWheel.ts
 *
 * A custom hook for managing wheel data and interactions.
 * This hook abstracts the WebSocket operations related to the activity wheel,
 * providing a clean interface for components to interact with the wheel.
 * 
 * It handles:
 * - Wheel data loading and state management
 * - Spinning animation coordination
 * - Random activity selection based on weighted probabilities
 * - Workflow state awareness to enable/disable wheel appropriately
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useWebSocket, WorkflowType } from '../contexts/WebSocketContext';
import { Wheel, WheelItem } from '../services/WebSocketManager';

// Animation states for the wheel
export enum WheelAnimationState {
  Idle = 'idle',
  Spinning = 'spinning',
  Completed = 'completed'
}

// Animation settings
interface AnimationSettings {
  duration: number;
  minRotations: number;
  easing: string;
}

export interface UseWheelState {
  // The current wheel data
  wheel: Wheel | null;
  
  // Selected wheel item (if any)
  selectedItem: WheelItem | null;
  
  // Wheel states
  animationState: WheelAnimationState;
  isLoading: boolean;
  isSpinEnabled: boolean;
  
  // Animation properties
  currentRotation: number; // Current rotation in degrees
  targetRotation: number; // Target rotation in degrees
  
  // Error state
  error: string | null;
}

export interface UseWheelActions {
  // Initiate a spin operation
  spin: () => void;
  
  // Animation control
  animateToPosition: (finalRotation: number, onComplete?: (selectedItem?: WheelItem) => void) => void;
  
  // Report selected activity to backend
  selectActivity: (activityId: string, name: string, description?: string) => boolean;
  
  // Request new wheel data (e.g., after a user updates preferences)
  refreshWheel: () => void;
  
  // Reset wheel state
  resetSelection: () => void;
  
  // DOM references
  setWheelRef: (ref: HTMLElement | null) => void;
}

/**
 * Hook for interacting with the activity wheel
 *
 * @returns Wheel state and actions
 */
export const useWheel = (): [UseWheelState, UseWheelActions] => {
  // Access the WebSocket context
  const {
    lastWheelData,
    sendSpinResult,
    isProcessing,
    sendChatMessage,
    connectionError,
    isConnected,
    workflowState
  } = useWebSocket();
  
  // DOM references
  const wheelRef = useRef<HTMLElement | null>(null);
  
  // Local state management
  const [state, setState] = useState<UseWheelState>({
    wheel: null,
    selectedItem: null,
    animationState: WheelAnimationState.Idle,
    isLoading: false,
    isSpinEnabled: false,
    currentRotation: 0,
    targetRotation: 0,
    error: null
  });
  
  // Animation settings
  const animationSettings: AnimationSettings = {
    duration: 4000, // 4 seconds
    minRotations: 4, // At least 4 full rotations
    easing: 'cubic-bezier(0.2, 0.1, 0.25, 1)' // Nice easing curve
  };

  // Debug logging for current state
  const logDebugState = useCallback(() => {
    console.log('[useWheel] Current state:', {
      animationState: state.animationState,
      currentRotation: state.currentRotation,
      isSpinEnabled: state.isSpinEnabled,
      selectedItem: state.selectedItem ? state.selectedItem.name : 'none'
    });
  }, [state]);

  // Update wheel data when new data is received from WebSocket
  useEffect(() => {
    if (lastWheelData && lastWheelData.wheel) {
      setState(prevState => ({
        ...prevState,
        wheel: lastWheelData.wheel,
        isLoading: false,
        error: null
      }));
    }
  }, [lastWheelData]);

  // Update loading state based on processing status
  useEffect(() => {
    setState(prevState => ({
      ...prevState,
      isLoading: isProcessing
    }));
  }, [isProcessing]);

  // Update error state from connection errors
  useEffect(() => {
    if (connectionError) {
      setState(prevState => ({
        ...prevState,
        error: connectionError
      }));
    }
  }, [connectionError]);
  
  // Determine if wheel spinning should be enabled based on workflow state
  useEffect(() => {
    // Debug logging
    console.log('[useWheel] Workflow state update:', {
      currentWorkflow: workflowState.currentWorkflow,
      status: workflowState.status,
      lastUpdated: workflowState.lastUpdated
    });
    
    // Only enable wheel in specific workflow states
    const spinningEnabled = 
      isConnected && 
      workflowState.currentWorkflow === 'pre_spin' && 
      workflowState.status === 'completed';
    
    console.log('[useWheel] Spin enabled:', spinningEnabled);
    
    setState(prevState => ({
      ...prevState,
      isSpinEnabled: spinningEnabled
    }));
  }, [isConnected, workflowState]);

  // Set wheel reference
  const setWheelRef = useCallback((ref: HTMLElement | null) => {
    wheelRef.current = ref;
  }, []);
  
  // Calculate position for an item
  const calculatePositionForItem = useCallback((item: WheelItem): number => {
    if (!state.wheel) return 0;
    
    let cumulativePercentage = 0;
    
    // Find the item's position in the wheel
    for (const wheelItem of state.wheel.items) {
      // Calculate the middle point of this segment
      const segmentStart = cumulativePercentage;
      cumulativePercentage += wheelItem.percentage;
      const segmentEnd = cumulativePercentage;
      
      if (wheelItem.id === item.id) {
        // Return the middle angle of this segment in degrees
        const middlePercentage = (segmentStart + segmentEnd) / 2;
        // Need to offset by 180 degrees to align with pointer at top
        return 360 - (middlePercentage * 3.6); // Convert to degrees (360/100)
      }
    }
    
    // Fallback if item not found - random position
    return Math.floor(Math.random() * 360);
  }, [state.wheel]);
  
  /**
   * Determines which wheel item is at a given angle (pointer position)
   * 
   * The pointer is fixed at the top (0 degrees), so we need to determine
   * which segment of the wheel is under the pointer when the wheel is rotated
   * to the given angle.
   * 
   * @param angle The wheel's rotation angle (in degrees)
   * @returns The wheel item at the pointer position, or null if none found
   */
  const getItemAtAngle = useCallback((angle: number): WheelItem | null => {
    if (!state.wheel || state.wheel.items.length === 0) return null;
    
    // First normalize the angle to determine which segment is at the top (pointer position)
    // The wheel rotates clockwise, but the pointer is fixed at the top
    // So we need to find which segment is at the top (0 degrees) when the wheel is at 'angle'
    
    // Normalize to 0-360 range and invert (since wheel rotates clockwise)
    const normalizedAngle = (360 - (angle % 360)) % 360;
    
    console.log('[useWheel] Checking pointer position at wheel angle:', angle);
    console.log('[useWheel] Normalized angle for segment calculation:', normalizedAngle);
    
    // Now determine which segment contains this angle
    let cumulativePercentage = 0;
    
    for (const item of state.wheel.items) {
      const startPercentage = cumulativePercentage;
      cumulativePercentage += item.percentage;
      
      // Convert percentages to angles (0-360)
      const startAngle = (startPercentage * 3.6); // 3.6 = 360/100
      const endAngle = (cumulativePercentage * 3.6);
      
      console.log(`[useWheel] Item ${item.name}: ${startAngle}° - ${endAngle}°`);
      
      if (normalizedAngle >= startAngle && normalizedAngle < endAngle) {
        console.log(`[useWheel] Found item at pointer position: ${item.name}`);
        return item;
      }
    }
    
    // Edge case: might be exactly at 360 degrees, which would be the first segment's start
    if (normalizedAngle === 360 && state.wheel.items.length > 0) {
      return state.wheel.items[0];
    }
    
    console.log('[useWheel] No item found at pointer position, using first item as fallback');
    return state.wheel.items[0]; // Fallback
  }, [state.wheel]);

  // Animate the wheel to a position
  const animateToPosition = useCallback((finalRotation: number, onComplete?: (selectedItem?: WheelItem) => void) => {
    if (!wheelRef.current) return;
    
    console.log(`[useWheel] Starting animation to ${finalRotation}deg`);
    
    // Set animation state
    setState(prevState => ({
      ...prevState,
      animationState: WheelAnimationState.Spinning,
      targetRotation: finalRotation
    }));
    
    // Apply the rotation with a nice easing
    wheelRef.current.style.transition = `transform ${animationSettings.duration / 1000}s ${animationSettings.easing}`;
    wheelRef.current.style.transform = `rotate(${finalRotation}deg)`;
    
    // Handle animation completion (called by transitionend or timeout)
    const handleAnimationComplete = () => {
      console.log('[useWheel] Animation completed');
      
      // Remove event listener
      wheelRef.current?.removeEventListener('transitionend', handleTransitionEnd);
      
      // Update state with current rotation and disable spinning
      setState(prevState => ({
        ...prevState,
        animationState: WheelAnimationState.Completed,
        currentRotation: finalRotation,
        isSpinEnabled: false // Explicitly disable spinning after animation completes
      }));
      
      console.log('[useWheel] Spin disabled after completion');
      
      // Get actual item at final position
      const actualSelectedItem = getItemAtAngle(finalRotation);
      
      if (actualSelectedItem) {
        console.log(`[useWheel] Selected item after spin: ${actualSelectedItem.name} (id: ${actualSelectedItem.id})`);
        
        // Update selected item
        setState(prevState => ({
          ...prevState,
          selectedItem: actualSelectedItem
        }));
        
        // Execute callback if provided - pass the actual selected item
        if (onComplete) {
          console.log('[useWheel] Executing post-spin callback with selected item:', actualSelectedItem.name);
          onComplete(actualSelectedItem);
        }
      } else {
        console.log('[useWheel] No item selected at final position');
        // Call callback with undefined if no item found
        if (onComplete) {
          console.log('[useWheel] Executing post-spin callback with no selected item');
          onComplete(undefined);
        }
      }
    };
    
    // Transitionend handler
    const handleTransitionEnd = () => {
      // Clear timeout when transition ends naturally
      clearTimeout(timeoutId);
      handleAnimationComplete();
    };
    
    // Timeout fallback in case transitionend doesn't fire
    const timeoutId = setTimeout(() => {
      console.log('[useWheel] Animation timeout fallback triggered');
      handleAnimationComplete();
    }, animationSettings.duration + 500); // Add 500ms buffer
    
    // Add event listener
    wheelRef.current.addEventListener('transitionend', handleTransitionEnd, { once: true });
  }, [animationSettings, getItemAtAngle]);

  // Spin the wheel with full animation and selection logic
  const spin = useCallback(() => {
    console.log('[useWheel] Spin initiated');
    logDebugState();
    
    // Don't spin if disabled
    if (!state.isSpinEnabled || !state.wheel || state.wheel.items.length === 0) {
      const errorMsg = !state.isSpinEnabled 
        ? 'Wheel spinning is currently disabled' 
        : 'No wheel items available';
      
      console.log(`[useWheel] Spin error: ${errorMsg}`);
      
      setState(prevState => ({
        ...prevState,
        error: errorMsg
      }));
      return;
    }
    
    // Update state to spinning
    setState(prevState => ({
      ...prevState,
      animationState: WheelAnimationState.Spinning,
      selectedItem: null
    }));
    
    // Select a random item
    const selectedItem = selectRandomWeightedItem(state.wheel.items);
    console.log(`[useWheel] Random item selected for animation: ${selectedItem.name}`);
    
    // Calculate target position for this item
    const targetPosition = calculatePositionForItem(selectedItem);
    
    // Calculate final rotation - ensure at least minimum rotations plus target position
    const currentFullRotations = Math.floor(state.currentRotation / 360);
    const minRotation = (currentFullRotations * 360) + 
      (animationSettings.minRotations * 360) + 
      targetPosition;
    
    // Ensure we go at least one full rotation farther
    const finalRotation = state.currentRotation >= minRotation 
      ? minRotation + 360 
      : minRotation;
    
    console.log(`[useWheel] Starting spin to ${finalRotation}deg`);
    
    // Animate the wheel - define a callback that will receive the actual selected item
    animateToPosition(finalRotation, (actualSelectedItem) => {
      // The actualSelectedItem is passed directly from handleAnimationComplete
      console.log(`[useWheel] Spin completed, selected: ${actualSelectedItem?.name}`);
      
      // Verify we have a valid item before sending to backend
      if (actualSelectedItem) {
        console.log(`[useWheel] Sending result to backend with data:`, {
          id: actualSelectedItem.activity_tailored_id,
          name: actualSelectedItem.name,
          description: actualSelectedItem.description || 'No description'
        });
        
        // Send the result to the backend using the actual item passed to the callback
        sendSpinResult(
          actualSelectedItem.activity_tailored_id, 
          actualSelectedItem.name, 
          actualSelectedItem.description
        );
      } else {
        console.error('[useWheel] No item selected, cannot send spin result');
      }
    });
  }, [
    state.isSpinEnabled, 
    state.wheel, 
    state.currentRotation, 
    animationSettings.minRotations, 
    calculatePositionForItem, 
    animateToPosition, 
    sendSpinResult,
    logDebugState
  ]);

  // Report the selected activity to the backend (can be called manually if needed)
  const selectActivity = useCallback((activityId: string, name: string, description?: string) => {
    // Send the result to the backend
    const success = sendSpinResult(activityId, name, description);

    // If this failed, update the error state
    if (!success) {
      setState(prevState => ({
        ...prevState,
        error: 'Failed to send activity selection'
      }));
    }

    return success;
  }, [sendSpinResult]);

  // Request new wheel data
  const refreshWheel = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      isLoading: true,
      error: null
    }));

    // Send a special message to request new wheel data
    sendChatMessage('refresh_wheel', { type: 'wheel_request' });
  }, [sendChatMessage]);

  // Reset the wheel selection
  const resetSelection = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      selectedItem: null,
      animationState: WheelAnimationState.Idle
    }));
  }, []);

  // Actions object
  const actions: UseWheelActions = {
    spin,
    animateToPosition,
    selectActivity,
    refreshWheel,
    resetSelection,
    setWheelRef
  };

  return [state, actions];
};

/**
 * Selects a random item from the wheel items based on their percentage weights
 *
 * @param items - The wheel items to select from
 * @returns The selected wheel item
 */
function selectRandomWeightedItem(items: WheelItem[]): WheelItem {
  const totalPercentage = items.reduce((sum, item) => sum + item.percentage, 0);
  const normalizedItems = items.map(item => ({
    ...item,
    normalizedPercentage: totalPercentage > 0 ? item.percentage / totalPercentage : 1 / items.length
  }));

  // Generate a random number between 0 and 1
  const random = Math.random();

  // Find the item that corresponds to this random number
  let cumulativePercentage = 0;
  for (const item of normalizedItems) {
    cumulativePercentage += item.normalizedPercentage;
    if (random <= cumulativePercentage) {
      return item;
    }
  }

  // Fallback to first item (should never reach here unless there's a rounding error)
  return items[0];
}
