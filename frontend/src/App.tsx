/**
 * App.tsx
 *
 * The main application component for the Game of Life frontend.
 * This serves as the root component and provides the application layout.
 */

import React, { type FC } from 'react'; // Import React
import { WebSocketProviderSelector } from './utils/WebSocketProviderSelector'; // Keep this import
import { useWebSocket } from './contexts/WebSocketContext'; // Corrected import path for the hook
import GameContainer from './components/layout/GameContainer';
import { WS_URL } from './config/environment';
import { DebugProvider } from './contexts/DebugContext'; // Import DebugProvider
import { UserProvider, useUser } from './contexts/UserContext'; // Import UserProvider and useUser
import DebugConsole from './components/debug/DebugConsole'; // Import DebugConsole

// For testing purposes - in production this would come from authentication
const USER_ID = '2'; // Use the correct user profile ID

/**
 * Inner component to access WebSocket context
 */
const AppContent: FC = () => {
  const { webSocketManager } = useWebSocket(); // Get manager from context
  const { user } = useUser(); // Get user info from UserContext
  const isStaff = user?.is_staff ?? false; // Determine if user is staff

  return (
    <DebugProvider webSocketManager={webSocketManager}>
      <GameContainer />
      {/* Conditionally render DebugConsole based on isStaff */}
      {isStaff && <DebugConsole />}
    </DebugProvider>
  );
};

/**
 * Root application component
 */
const App: FC = () => {
  console.log('[App] Rendering App component with:', {
    WS_URL,
    USER_ID
  });

  return (
    <WebSocketProviderSelector url={WS_URL} userProfileId={USER_ID}>
      <UserProvider> {/* Wrap AppContent with UserProvider */}
        <AppContent /> {/* Render the inner component */}
      </UserProvider>
    </WebSocketProviderSelector>
  );
};

export default App;
