import React, { createContext, useState, useContext, useEffect, ReactNode, useCallback } from 'react';
import { DebugInfoFromServerContent } from '../types/api'; // Import the specific type
import { WebSocketManager, DebugInfoData } from '../services/WebSocketManager'; // Import manager and specific data type

// Define the shape of a debug message entry
export interface DebugMessageEntry extends DebugInfoFromServerContent {
  id: string; // Add a unique ID for list rendering
}

// Define the context shape
interface DebugContextType {
  debugMessages: DebugMessageEntry[];
  clearDebugMessages: () => void;
}

// Create the context with a default value
const DebugContext = createContext<DebugContextType | undefined>(undefined);

// Define the props for the provider
interface DebugProviderProps {
  children: ReactNode;
  webSocketManager: WebSocketManager | null; // Pass the manager instance
}

// Create the provider component
export const DebugProvider: React.FC<DebugProviderProps> = ({ children, webSocketManager }) => {
  const [debugMessages, setDebugMessages] = useState<DebugMessageEntry[]>([]);

  // Handler for incoming debug messages
  // Wrapped with useCallback to stabilize the function reference
  const handleDebugInfo = useCallback((data: DebugInfoData) => {
    // Ensure we are handling the correct type (though subscribe should guarantee this)
    if (data.type === 'debug_info') {
      const newEntry: DebugMessageEntry = {
        ...data.content,
        id: `${data.content.timestamp}-${Math.random().toString(36).substring(2, 9)}`, // Simple unique ID
      };
      // Add new message, sort by timestamp, and keep only the latest N messages
      setDebugMessages(prevMessages => {
        const updatedMessages = [...prevMessages, newEntry];
        // Sort messages by timestamp (ascending)
        updatedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        // Keep only the latest 100 messages
        return updatedMessages.slice(-100);
      });
    }
  }, []); // Empty dependency array: the function doesn't depend on props or state

  // Subscribe/unsubscribe to WebSocket messages
  useEffect(() => {
    if (webSocketManager) {
      console.log('[DebugProvider] Subscribing to debug_info messages.');
      // Corrected: Pass only the specific data type as the generic argument
      webSocketManager.subscribe<DebugInfoData>('debug_info', handleDebugInfo);

      // Cleanup function to unsubscribe
      return () => {
        console.log('[DebugProvider] Unsubscribing from debug_info messages.');
        // Corrected: Pass only the specific data type as the generic argument
        webSocketManager.unsubscribe<DebugInfoData>('debug_info', handleDebugInfo);
      };
    }
  }, [webSocketManager, handleDebugInfo]);

  // Function to clear messages
  const clearDebugMessages = useCallback(() => {
    setDebugMessages([]);
  }, []);

  // Provide the context value
  const contextValue: DebugContextType = {
    debugMessages,
    clearDebugMessages,
  };

  return (
    <DebugContext.Provider value={contextValue}>
      {children}
    </DebugContext.Provider>
  );
};

// Custom hook to use the DebugContext
export const useDebug = (): DebugContextType => {
  const context = useContext(DebugContext);
  if (context === undefined) {
    throw new Error('useDebug must be used within a DebugProvider');
  }
  return context;
};
