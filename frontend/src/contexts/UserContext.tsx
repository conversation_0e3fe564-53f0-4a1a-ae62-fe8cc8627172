import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  type ReactNode,
  type FC
} from 'react';

// Define the shape of the user object
// TODO: Replace with the actual UserProfile type from api.ts or similar
interface UserProfile {
  id: string;
  username: string;
  is_staff: boolean;
  // Add other relevant user fields
}

// Define the context shape
interface UserContextType {
  user: UserProfile | null;
  loading: boolean;
  error: Error | null;
}

// Create the context with a default value
export const UserContext = createContext<UserContextType | undefined>(undefined); // Added export

// Define the props for the provider
interface UserProviderProps {
  children: ReactNode;
}

// Create the provider component
export const UserProvider: FC<UserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Placeholder for fetching user data
    // In a real app, you'd fetch this from an API or auth service
    const fetchUser = async (): Promise<void> => {
      setLoading(true);
      setError(null);
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate network delay
        // TODO: Replace with actual user fetching logic
        // For now, using hardcoded staff user for testing DebugConsole
        const mockUser: UserProfile = {
          id: '2', // Matches USER_ID in App.tsx
          username: 'staff_user',
          is_staff: true // Set to true to test DebugConsole visibility
        };
        setUser(mockUser);
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error('Failed to fetch user data')
        );
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    void fetchUser();
  }, []); // Empty dependency array means this runs once on mount

  const value = { user, loading, error };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

// Create a custom hook for using the context
export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
