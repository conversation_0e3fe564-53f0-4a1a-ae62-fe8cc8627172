/**
 * WebSocketContext.tsx
 *
 * This file provides a React Context for the WebSocketManager,
 * allowing components to access WebSocket functionality throughout the application.
 * 
 * Key responsibilities:
 * - Manage WebSocket connection state
 * - Maintain message histories for different message types
 * - Provide access to WebSocket communication methods
 * - Track workflow state for UI coordination
 */

import React, { 
  createContext, 
  useContext, 
  useEffect, 
  useRef, 
  useState, 
  type ReactNode, 
  useCallback, 
  useMemo, 
  type Dispatch, 
  type SetStateAction 
} from 'react';
import {
  WebSocketManager,
  ChatMessageData,
  WheelData,
  ProcessingStatusData,
  ErrorData,
  SystemMessageData,
  WorkflowStatusData
} from '../services/WebSocketManager';

// Message storage with ID and timestamp
export interface StoredMessage {
  id: string;
  timestamp: string;
  data: any;
}

// Define connection state
interface ConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
}

// Define workflow types from backend
export type WorkflowType = 
  'wheel_generation' | 
  'pre_spin' | 
  'post_spin' | 
  'activity_feedback' | 
  'user_onboarding' | 
  'progress_review';

// Define workflow state
export interface WorkflowState {
  currentWorkflow: WorkflowType | null;
  status: 'initiated' | 'processing' | 'completed' | 'failed' | 'unknown';
  workflowId: string | null;
  lastUpdated: string;
}

// Define context state type
interface WebSocketContextState {
  // WebSocket instance
  webSocketManager: WebSocketManager;

  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;

  // Processing state
  isProcessing: boolean;

  // Methods
  sendChatMessage: (message: string, metadata?: Record<string, any>) => boolean;
  sendSpinResult: (activityId: string, name: string, description?: string) => boolean;
  checkWorkflowStatus: (workflowId: string) => boolean;
  reconnect: () => void;
  clearChatHistory: () => void;

  // Message histories
  chatMessages: StoredMessage[]; // History of chat messages
  systemMessages: StoredMessage[]; // History of system messages
  
  // Workflow state
  workflowState: WorkflowState;
  
  // Last received data (for specific hook subscriptions)
  lastChatMessage: ChatMessageData | null;
  lastWheelData: WheelData | null;
  lastError: ErrorData | null;
  lastWorkflowStatus: WorkflowStatusData | null;
  
  // UI state indicators
  isTyping: boolean; // Indicates if the assistant is "typing"
  
  // Timestamp of last state update for optimizations
  lastStateUpdate: number;
}

// Maximum number of messages to keep in history
const MAX_CHAT_HISTORY = 100;

// Create context with default values
export const WebSocketContext = createContext<WebSocketContextState | null>(null);

// Define props for provider
interface WebSocketProviderProps {
  children: ReactNode;
  url: string;
  userProfileId?: string;
}

// Provider component
export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({
  children,
  url,
  userProfileId
}) => {
  console.log('[WebSocketProvider] Initializing real WebSocketProvider with:', {
    url,
    userProfileId
  });
  
  // Create WebSocketManager instance (persists across renders)
  const webSocketManagerRef = useRef<WebSocketManager | null>(null);

  // State for connection and message data
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    isConnected: false,
    isConnecting: false,
    error: null
  });
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [isTyping, setIsTyping] = useState<boolean>(false);
  
  // Message histories
  const [chatMessages, setChatMessages] = useState<StoredMessage[]>([]);
  const [systemMessages, setSystemMessages] = useState<StoredMessage[]>([]);
  
  // Last message of each type (for hook subscriptions)
  const [lastChatMessage, setLastChatMessage] = useState<ChatMessageData | null>(null);
  const [lastWheelData, setLastWheelData] = useState<WheelData | null>(null);
  const [lastError, setLastError] = useState<ErrorData | null>(null);
  const [lastWorkflowStatus, setLastWorkflowStatus] = useState<WorkflowStatusData | null>(null);
  
  // Workflow state
  const [workflowState, setWorkflowState] = useState<WorkflowState>({
    currentWorkflow: null,
    status: 'unknown',
    workflowId: null,
    lastUpdated: new Date().toISOString()
  });
  
  const [lastStateUpdate, setLastStateUpdate] = useState<number>(Date.now());

  // Force a reconnection (exposed in context)
  const reconnect = useCallback(() => {
    if (webSocketManagerRef.current) {
      setConnectionState(prev => ({ ...prev, isConnecting: true, error: null }));
      webSocketManagerRef.current.disconnect();
      webSocketManagerRef.current.connect();
    }
  }, []);
  
  // Clear chat history
  const clearChatHistory = useCallback(() => {
    setChatMessages([]);
  }, []);

  // Helper to add a message to history with proper ID
  const addMessageToHistory = useCallback((
    messageData: any, 
    setter: Dispatch<SetStateAction<StoredMessage[]>>
  ) => {
    const storedMessage: StoredMessage = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      data: messageData
    };
    
    setter(prev => {
      // Add new message while ensuring we don't exceed max history
      const updatedMessages = [...prev, storedMessage];
      return updatedMessages.slice(-MAX_CHAT_HISTORY);
    });
    
    return storedMessage;
  }, []);

  // Set up event handlers for WebSocketManager
  const setupEventHandlers = useCallback(() => {
    if (!webSocketManagerRef.current) return;
    
    const wsm = webSocketManagerRef.current;
    
    // Handle connection state changes
    wsm.subscribeToConnection(({ connected }) => {
      setConnectionState(prev => ({ 
        ...prev, 
        isConnected: connected, 
        isConnecting: false,
        // Reset error when connected
        error: connected ? null : prev.error
      }));
      
      // Update the last state change timestamp
      setLastStateUpdate(Date.now());
    });
    
    // Handle chat messages
    wsm.subscribe<ChatMessageData>('chat_message', (data) => {
      // Update last chat message
      setLastChatMessage(data);

      // Only add non-user messages from the server to history
      // User messages are added optimistically in sendChatMessage
      if (!data.is_user) {
        addMessageToHistory(data, setChatMessages);
        // Set typing indicator off if this is an assistant message
        setIsTyping(false);
      }
      
      setLastStateUpdate(Date.now());
    });
    
    // Handle wheel data
    wsm.subscribe<WheelData>('wheel_data', (data) => {
      setLastWheelData(data);
      setLastStateUpdate(Date.now());
    });
    
    // Handle processing status
    wsm.subscribe<ProcessingStatusData>('processing_status', (data) => {
      setIsProcessing(data.status === 'processing');
      
      // Set typing indicator if processing (assistant is "thinking")
      if (data.status === 'processing') {
        setIsTyping(true);
      }
      
      setLastStateUpdate(Date.now());
    });
    
    // Handle errors
    wsm.subscribe<ErrorData>('error', (data) => {
      setLastError(data);
      
      // Add to system messages
      addMessageToHistory(data, setSystemMessages);
      
      // Update connection state if it's a connection error
      if (data.content.includes('connection') || data.content.includes('connect')) {
        setConnectionState(prev => ({ 
          ...prev, 
          error: data.content,
          isConnecting: false 
        }));
      }
      
      setLastStateUpdate(Date.now());
    });
    
    // Handle workflow status
    wsm.subscribe<WorkflowStatusData>('workflow_status', (data) => {
      setLastWorkflowStatus(data);
      
      // Update workflow state
      setWorkflowState(prev => ({
        ...prev,
        workflowId: data.workflow_id,
        status: data.status,
        // Set currentWorkflow if workflow_type is provided
        currentWorkflow: data.workflow_type as WorkflowType || prev.currentWorkflow,
        lastUpdated: new Date().toISOString()
      }));
      
      console.log('[WebSocketContext] Workflow state updated:', {
        workflowId: data.workflow_id,
        status: data.status,
        workflowType: data.workflow_type || 'not provided'
      });
      
      setLastStateUpdate(Date.now());
    });
    
    // Handle system messages
    wsm.subscribe<SystemMessageData>('system_message', (data) => {
      console.log('System message:', data.content);
      
      // Add to system messages history
      addMessageToHistory(data, setSystemMessages);
      
      setLastStateUpdate(Date.now());
    });
  }, [addMessageToHistory]);

  // Initialize WebSocketManager
  useEffect(() => {
    // Create WebSocketManager if it doesn't exist
    if (!webSocketManagerRef.current) {
      webSocketManagerRef.current = new WebSocketManager(url, userProfileId);
      
      // Set up event handlers
      setupEventHandlers();
      
      // Start connection process
      setConnectionState(prev => ({ ...prev, isConnecting: true }));
      webSocketManagerRef.current.connect();
    } else if (url !== webSocketManagerRef.current.getUrl()) {
      // URL changed, need to reconnect
      webSocketManagerRef.current.disconnect();
      webSocketManagerRef.current = new WebSocketManager(url, userProfileId);
      setupEventHandlers();
      setConnectionState(prev => ({ ...prev, isConnecting: true }));
      webSocketManagerRef.current.connect();
    }
    
    return () => {
      // Clean up on unmount
      if (webSocketManagerRef.current) {
        webSocketManagerRef.current.disconnect();
      }
    };
  }, [url, userProfileId, setupEventHandlers]);
  
  // Update userProfileId if it changes
  useEffect(() => {
    if (webSocketManagerRef.current && userProfileId) {
      webSocketManagerRef.current.setUserProfileId(userProfileId);
    }
  }, [userProfileId]);

  // Wrapper methods for WebSocketManager functions
  const sendChatMessage = useCallback((message: string, metadata: Record<string, any> = {}) => {
    // Create a fake message for immediate display
    const fakeMessage: ChatMessageData = {
      type: 'chat_message',
      content: message,
      is_user: true
    };
    
    // Add to chat history immediately
    addMessageToHistory(fakeMessage, setChatMessages);
    
    // Send via WebSocket
    const success = webSocketManagerRef.current?.sendChatMessage(message, metadata) || false;
    
    return success;
  }, [addMessageToHistory]);
  
  const sendSpinResult = useCallback((activityId: string, name: string, description?: string) => {
    if (!webSocketManagerRef.current) {
      console.log('[WebSocketContext] Cannot send spin result - WebSocketManager is not initialized');
      return false;
    }

    console.log('[WebSocketContext] Sending spin result with data:', {
      activity_id: activityId,
      activity_name: name,
      description: description || 'None'
    });
    
    // This will update the workflow state which will then disable the spin button
    const success = webSocketManagerRef.current.sendSpinResult(activityId, name, description);
    
    if (success) {
      console.log('[WebSocketContext] Spin result sent successfully via WebSocketManager');
      
      // Explicitly update workflow state to prevent double spinning
      setWorkflowState(prev => ({
        ...prev,
        status: 'processing', // Mark as processing right away
        lastUpdated: new Date().toISOString()
      }));
      
      // Log this state update
      console.log('[WebSocketContext] Updated workflow state to processing to prevent double spinning');
      
      // Add a system message for debugging
      const systemMessageData: SystemMessageData = {
        type: 'system_message',
        content: `Spin result sent: ${name}`
      };
      
      // Add to system messages history
      addMessageToHistory(systemMessageData, setSystemMessages);
      console.log('[WebSocketContext] Added system message about spin result');
    } else {
      console.error('[WebSocketContext] Failed to send spin result through WebSocketManager');
    }
    
    return success;
  }, [addMessageToHistory]);
  
  const checkWorkflowStatus = useCallback((workflowId: string) => {
    return webSocketManagerRef.current?.checkWorkflowStatus(workflowId) || false;
  }, []);

  // Create context value with useMemo for performance optimization
  const contextValue = useMemo<WebSocketContextState>(() => ({
    webSocketManager: webSocketManagerRef.current!,
    isConnected: connectionState.isConnected,
    isConnecting: connectionState.isConnecting,
    connectionError: connectionState.error,
    isProcessing,
    isTyping,
    sendChatMessage,
    sendSpinResult,
    checkWorkflowStatus,
    reconnect,
    clearChatHistory,
    chatMessages,
    systemMessages,
    workflowState,
    lastChatMessage,
    lastWheelData,
    lastError,
    lastWorkflowStatus,
    lastStateUpdate
  }), [
    connectionState.isConnected,
    connectionState.isConnecting,
    connectionState.error,
    isProcessing,
    isTyping,
    sendChatMessage,
    sendSpinResult,
    checkWorkflowStatus,
    reconnect,
    clearChatHistory,
    chatMessages,
    systemMessages,
    workflowState,
    lastChatMessage,
    lastWheelData,
    lastError,
    lastWorkflowStatus,
    lastStateUpdate
  ]);

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

// Custom hook for using the WebSocket context
export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  
  return context;
};
