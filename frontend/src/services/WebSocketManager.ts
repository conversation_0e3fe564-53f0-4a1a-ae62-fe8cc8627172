/**
 * WebSocketManager.ts
 *
 * This class provides a typed interface for WebSocket communication with the Game of Life backend.
 * It handles connection management, message formatting, reconnection logic, and event routing.
 */

import { createWebSocket } from '../utils/MockWebSocket';
import { ENV } from '../config/environment';

// Type definitions for API messages
// --------------------------------

// Outgoing Message Types (Client -> Server)

export interface ChatMessageContent {
    message: string;
    user_profile_id: string;
    timestamp?: string;
    metadata?: Record<string, any>;
  }

  export interface SpinResultContent {
    activity_id: string;
    name: string;
    description?: string;
    user_profile_id: string;
  }

  export interface WorkflowStatusRequestContent {
    workflow_id: string;
  }

  // Incoming Message Types (Server -> Client)

  export interface SystemMessageData {
    type: 'system_message';
    content: string;
  }

  export interface ChatMessageData {
    type: 'chat_message';
    content: string;
    is_user: boolean;
  }

  export interface ProcessingStatusData {
    type: 'processing_status';
    status: 'processing' | 'completed' | 'error';
  }

  export interface WheelItem {
    id: string;
    name: string;
    description: string;
    percentage: number;
    color: string;
    domain: string;
    base_challenge_rating: number;
    activity_tailored_id: string;
  }

  export interface Wheel {
    name: string;
    items: WheelItem[];
  }

  export interface WheelData {
    type: 'wheel_data';
    wheel: Wheel;
  }

  export interface ErrorData {
    type: 'error';
    content: string;
  }

  export interface WorkflowStatusData {
    type: 'workflow_status';
    workflow_id: string;
    status: 'initiated' | 'processing' | 'completed' | 'failed' | 'unknown';
    workflow_type?: string; // Optional workflow type
  }

  // TODO: Refactor to import types directly from ../types/api.ts instead of redefining here.
  // --- NEW ---
  export interface DebugInfoData {
    type: 'debug_info';
    content: {
      timestamp: string; // ISO-8601
      source: string;
      level: 'info' | 'warning' | 'error' | 'debug';
      message: string;
      details?: Record<string, any>;
    };
  }
  // --- END NEW ---

  // Union types for all message types
  export type OutgoingMessageType = 'chat_message' | 'spin_result' | 'workflow_status_request';
  export type IncomingMessageType =
    'system_message' | 'chat_message' | 'processing_status' |
    'wheel_data' | 'error' | 'workflow_status' | 'debug_info'; // Added debug_info

  export type OutgoingMessageContent =
    ChatMessageContent | SpinResultContent | WorkflowStatusRequestContent;

  export type IncomingMessageData =
    SystemMessageData | ChatMessageData | ProcessingStatusData |
    WheelData | ErrorData | WorkflowStatusData | DebugInfoData; // Added DebugInfoData

  // Event handler types
  export type MessageHandler<T> = (data: T) => void;
  export type ConnectionStateHandler = (state: { connected: boolean }) => void;

  // WebSocketManager Class
  // --------------------------------

  export class WebSocketManager {
    private url: string;
    private socket: WebSocket | null = null;
    private connected: boolean = false;
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 5;
    private reconnectDelay: number = 2000; // Start with 2 seconds
    private activeWorkflows: Map<string, { status: string, lastUpdate?: string }> = new Map();
    private useMockWebSocket: boolean;

    /**
     * Gets the current WebSocket URL
     *
     * @returns The WebSocket URL
     */
    public getUrl(): string {
      return this.url;
    }

    // Event handlers
    private messageHandlers: Map<string, Set<MessageHandler<any>>> = new Map();
    private connectionHandlers: Set<ConnectionStateHandler> = new Set();

    // Queue for messages when offline
    private messageQueue: Array<{ type: OutgoingMessageType, content: OutgoingMessageContent }> = [];

    // User Profile ID Storage
    private userProfileId: string | null = null;

    /**
     * Creates a new WebSocketManager instance
     *
     * @param url - The WebSocket endpoint URL
     * @param userProfileId - Optional initial user profile ID
     */
    constructor(url: string, userProfileId?: string) {
      console.log('[WebSocketManager] Creating new instance with URL:', url);
      this.url = url;
      this.useMockWebSocket = ENV.USE_MOCK_WS;

      if (userProfileId) {
        this.userProfileId = userProfileId;
      }
    }

    /**
     * Sets or updates the current user profile ID
     *
     * @param id - The user profile ID to set
     */
    public setUserProfileId(id: string): void {
      console.log('[WebSocketManager] Setting user profile ID:', id);
      this.userProfileId = id;
    }

    /**
     * Establishes a WebSocket connection
     */
    public connect(): void {
      console.log(`[WebSocketManager] Connecting to WebSocket at ${this.url}, useMock: ${this.useMockWebSocket}`);

      try {
        // Use the createWebSocket factory function to get either a real or mock WebSocket
        this.socket = createWebSocket(this.url, this.useMockWebSocket);
        
        // Add enhanced logging if in development mode
        if (process.env.NODE_ENV === 'development' && !this.useMockWebSocket) {
          enableDetailedLogging(this.socket);
        }

        this.socket.onopen = this.handleOpen;
        this.socket.onclose = this.handleClose;
        this.socket.onerror = this.handleError;
        this.socket.onmessage = this.handleMessage;
      } catch (error) {
        console.error('[WebSocketManager] Error creating WebSocket connection:', error);
        this.notifyConnectionState(false);
        this.scheduleReconnect();
      }
    }

    /**
     * Closes the WebSocket connection
     */
    public disconnect(): void {
      console.log('[WebSocketManager] Disconnecting WebSocket');
      if (this.socket && this.connected) {
        this.socket.close();
        this.connected = false;
        this.notifyConnectionState(false);
      }
    }

    /**
     * Sends a message through the WebSocket
     *
     * @param type - The message type
     * @param content - The message content
     * @returns boolean indicating if the message was sent successfully
     */
    public send(type: OutgoingMessageType, content: OutgoingMessageContent): boolean {
      console.log(`[WebSocketManager] Sending message of type: ${type}`, content);
      
      if (!this.connected) {
        console.warn('[WebSocketManager] Cannot send message: WebSocket not connected');
        // Queue the message to send when reconnected
        this.messageQueue.push({ type, content });
        return false;
      }

      try {
        const message = { type, content };
        this.socket?.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('[WebSocketManager] Error sending WebSocket message:', error);
        return false;
      }
    }

    /**
     * Sends a chat message through the WebSocket
     * 
     * @param message - The message text to send
     * @param metadata - Optional metadata to include with the message 
     * @returns boolean indicating if the message was sent successfully
     */
    public sendChatMessage(message: string, metadata: Record<string, any> = {}): boolean {
      console.log('[WebSocketManager] Sending chat message:', message, metadata);
      
      if (!this.userProfileId) {
        console.error('[WebSocketManager] Cannot send chat message: No user profile ID set');
        return false;
      }

      return this.send('chat_message', {
        message,
        user_profile_id: this.userProfileId,
        timestamp: new Date().toISOString(),
        metadata
      });
    }

    /**
     * Sends a spin result through the WebSocket
     *
     * @param activityId - The ID of the selected activity
     * @param name - The name of the selected activity
     * @param description - Optional description of the activity
     * @returns boolean indicating if the message was sent successfully
     */
    public sendSpinResult(activityId: string, name: string, description?: string): boolean {
      if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
        console.error('[WebSocketManager] Cannot send spin result: WebSocket not open');
        return false;
      }
      console.log('[WebSocketManager] Sending spin result:', { activityId, name, description });
      
      if (!this.userProfileId) {
        console.error('[WebSocketManager] Cannot send spin result: No user profile ID set');
        return false;
      }

      return this.send('spin_result', {
        activity_id: activityId,
        name,
        description,
        user_profile_id: this.userProfileId
      });
    }

    /**
     * Sends a workflow status request through the WebSocket
     *
     * @param workflowId - The ID of the workflow to check
     * @returns boolean indicating if the request was sent successfully
     */
    public checkWorkflowStatus(workflowId: string): boolean {
      console.log('[WebSocketManager] Checking workflow status:', workflowId);
      return this.send('workflow_status_request', { workflow_id: workflowId });
    }

    /**
     * Subscribes a handler to a specific message type
     *
     * @param type - The message type to subscribe to
     * @param handler - The handler function to call
     */
    public subscribe<T extends IncomingMessageData>(
      type: T['type'],
      handler: MessageHandler<T>
    ): void {
      console.log(`[WebSocketManager] Subscribing to message type: ${type}`);
      
      if (!this.messageHandlers.has(type)) {
        this.messageHandlers.set(type, new Set());
      }

      this.messageHandlers.get(type)?.add(handler as MessageHandler<any>);
    }

    /**
     * Unsubscribes a handler from a specific message type
     *
     * @param type - The message type to unsubscribe from
     * @param handler - The handler function to remove
     */
    public unsubscribe<T extends IncomingMessageData>(
      type: T['type'],
      handler: MessageHandler<T>
    ): void {
      console.log(`[WebSocketManager] Unsubscribing from message type: ${type}`);
      
      const handlers = this.messageHandlers.get(type);
      if (handlers) {
        handlers.delete(handler as MessageHandler<any>);
      }
    }

    /**
     * Subscribes a handler to connection state changes
     *
     * @param handler - The handler function to call
     */
    public subscribeToConnection(handler: ConnectionStateHandler): void {
      console.log('[WebSocketManager] Subscribing to connection state changes');
      
      this.connectionHandlers.add(handler);

      // Immediately notify of current state
      handler({ connected: this.connected });
    }

    /**
     * Unsubscribes a handler from connection state changes
     *
     * @param handler - The handler function to remove
     */
    public unsubscribeFromConnection(handler: ConnectionStateHandler): void {
      console.log('[WebSocketManager] Unsubscribing from connection state changes');
      
      this.connectionHandlers.delete(handler);
    }

    /**
     * Gets the current connection state
     *
     * @returns boolean indicating if connected
     */
    public isConnected(): boolean {
      return this.connected;
    }

    /**
     * Gets information about an active workflow
     *
     * @param workflowId - The ID of the workflow to check
     * @returns Workflow information or null if not found
     */
    public getWorkflowInfo(workflowId: string): { status: string, lastUpdate?: string } | null {
      if (this.activeWorkflows.has(workflowId)) {
        return this.activeWorkflows.get(workflowId) || null;
      }
      return null;
    }

    // Private methods
    // --------------------------------

    /**
     * Handles WebSocket open event
     */
    private handleOpen = (): void => {
      console.log('[WebSocketManager] WebSocket connection established');
      this.connected = true;
      this.reconnectAttempts = 0;
      this.reconnectDelay = 2000; // Reset delay

      this.notifyConnectionState(true);

      // Send any queued messages
      this.processMessageQueue();
    };

    /**
     * Handles WebSocket close event
     */
    private handleClose = (event: CloseEvent): void => {
      console.log(`[WebSocketManager] WebSocket connection closed: ${event.code}`);
      this.connected = false;

      this.notifyConnectionState(false);

      this.scheduleReconnect();
    };

    /**
     * Handles WebSocket error event
     */
    private handleError = (event: Event): void => {
      console.error('[WebSocketManager] WebSocket error:', event);

      // Notify all error handlers
      this.notifyHandlers('error', {
        type: 'error',
        content: 'Connection error occurred'
      });
    };

    /**
     * Handles WebSocket message event
     */
    private handleMessage = (event: MessageEvent): void => {
      try {
        const data = JSON.parse(event.data) as IncomingMessageData;
        console.log('[WebSocketManager] WebSocket message received:', data.type, data);

        // Route message to handlers
        this.notifyHandlers(data.type, data);

        // Handle special cases
        this.handleSpecialCases(data);
      } catch (error) {
        console.error('[WebSocketManager] Error parsing WebSocket message:', error);
      }
    };

    /**
     * Handles special message processing cases
     */
    private handleSpecialCases(data: IncomingMessageData): void {
      switch (data.type) {
        case 'workflow_status':
          const workflowData = data as WorkflowStatusData;
          console.log('[WebSocketManager] Handling workflow status:', workflowData);
          
          // Update workflow tracking
          this.activeWorkflows.set(workflowData.workflow_id, {
            status: workflowData.status,
            lastUpdate: new Date().toISOString()
          });
          break;

        case 'wheel_data':
          const wheelData = data as WheelData;
          console.log('[WebSocketManager] Handling wheel data:', wheelData);
          
          // Mark wheel generation workflow as completed if it exists
          if (this.activeWorkflows.has('wheel_generation')) {
            this.activeWorkflows.set('wheel_generation', {
              status: 'completed',
              lastUpdate: new Date().toISOString()
            });
          }
          break;
      }
    }

    /**
     * Notifies all handlers for a specific message type
     */
    private notifyHandlers(type: string, data: any): void {
      console.log(`[WebSocketManager] Notifying handlers for message type: ${type}`);
      
      const handlers = this.messageHandlers.get(type);
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(data);
          } catch (error) {
            console.error(`[WebSocketManager] Error in message handler for ${type}:`, error);
          }
        });
      }
    }

    /**
     * Notifies all connection state handlers
     */
    private notifyConnectionState(connected: boolean): void {
      console.log(`[WebSocketManager] Notifying connection state handlers: connected=${connected}`);
      
      this.connectionHandlers.forEach(handler => {
        try {
          handler({ connected });
        } catch (error) {
          console.error('[WebSocketManager] Error in connection state handler:', error);
        }
      });
    }

    /**
     * Schedules a reconnection attempt
     */
    private scheduleReconnect(): void {
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        console.log(`[WebSocketManager] Scheduling reconnect attempt ${this.reconnectAttempts + 1} in ${this.reconnectDelay}ms`);

        setTimeout(() => {
          console.log(`[WebSocketManager] Attempting to reconnect (attempt ${this.reconnectAttempts + 1})...`);
          this.connect();
        }, this.reconnectDelay);

        // Exponential backoff with jitter
        this.reconnectDelay = Math.min(
          30000, // Cap at 30 seconds
          this.reconnectDelay * 1.5 + Math.random() * 1000
        );
        this.reconnectAttempts++;
      } else {
        console.log('[WebSocketManager] Maximum reconnection attempts reached');

        // Notify all error handlers
        this.notifyHandlers('error', {
          type: 'error',
          content: 'Failed to reconnect after maximum attempts'
        });
      }
    }

    /**
     * Processes any queued messages after reconnection
     */
    private processMessageQueue(): void {
      if (this.messageQueue.length > 0) {
        console.log(`[WebSocketManager] Processing ${this.messageQueue.length} queued messages`);

        // Create a copy to avoid issues if new messages are queued during processing
        const queueCopy = [...this.messageQueue];
        this.messageQueue = [];

        // Send all queued messages
        queueCopy.forEach(({ type, content }) => {
          this.send(type, content);
        });
      }
    }
  }

/**
 * Enhanced WebSocket logging
 * 
 * This function enhances a WebSocket instance with detailed logging capabilities,
 * providing insight into all messages sent and received, as well as connection events.
 * 
 * @param webSocket - The WebSocket instance to enhance with logging
 * @returns The enhanced WebSocket instance
 */
export const enableDetailedLogging = (webSocket: WebSocket): WebSocket => {
  const originalSend = webSocket.send;

  // Override the send method to log outgoing messages
  webSocket.send = function(data: any) {
    console.group('[WebSocketManager] WebSocket Outgoing Message');
    console.log('Raw:', data);
    try {
      console.log('Parsed:', JSON.parse(data.toString()));
    } catch (e) {
      console.log('(Not JSON data)');
    }
    console.groupEnd();
    return originalSend.call(this, data);
  };

  // Log incoming messages
  webSocket.addEventListener('message', (event) => {
    console.group('[WebSocketManager] WebSocket Incoming Message');
    console.log('Raw:', event.data);
    try {
      console.log('Parsed:', JSON.parse(event.data));
    } catch (e) {
      console.log('(Not JSON data)');
    }
    console.groupEnd();
  });

  // Log other connection events
  webSocket.addEventListener('open', () => console.log('[WebSocketManager] 🟢 WebSocket Connected'));
  webSocket.addEventListener('close', (e) => console.log(`[WebSocketManager] 🔴 WebSocket Closed: ${e.code} ${e.reason}`));
  webSocket.addEventListener('error', (e) => console.error('[WebSocketManager] ⚠️ WebSocket Error:', e));
  
  // Return the enhanced WebSocket for chaining
  return webSocket;
};
