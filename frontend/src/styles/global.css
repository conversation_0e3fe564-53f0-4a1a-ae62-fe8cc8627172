/**
 * Global CSS styles for the Game of Life application
 */

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

/* Game layout styles */
.game-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.user-header {
  background-color: #4a90e2;
  color: white;
  padding: 15px;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 20px;
  padding: 20px;
  background: white;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-grow: 1;
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

/* Wheel styles */
.wheel-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.wheel {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  position: relative;
  transform-origin: center;
  background: conic-gradient(from 0deg, red, blue, green, yellow, purple, orange);
  margin: 40px auto;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

/* Animation for spinning wheel */
.wheel.spinning {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
  /* No animation here - the transform property handles the actual rotation */
  /* We'll add a visual effect with a pseudo-element */
}

/* Create a visual effect for the spinning state using a pseudo-element */
.wheel.spinning::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  border: 2px dashed rgba(255,255,255,0.3);
  animation: spin-glow 0.5s infinite alternate;
  pointer-events: none;
}

@keyframes spin-glow {
  from { opacity: 0.5; transform: scale(0.98); }
  to { opacity: 0.8; transform: scale(1.02); }
}

.pointer {
  position: absolute;
  top: 0; /* Position at the wheel's top border */
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 30px solid #000; /* Black pointer as requested */
  z-index: 100; /* Ensure it's always on top */
  filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
  margin-top: -15px; /* This makes half the pointer outside the wheel and half inside */
}

/* Active state for pointer during spinning */
.pointer.active {
  border-top-color: #222;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.7));
  animation: pointer-pulse 0.5s infinite alternate;
}

@keyframes pointer-pulse {
  from { transform: translateX(-50%) scaleY(1); }
  to { transform: translateX(-50%) scaleY(1.2); }
}

.spin-button {
  margin-top: 20px;
  padding: 10px 30px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.spin-button:hover {
  background-color: #3a80d2;
  transform: scale(1.05);
}

.spin-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
}

/* Activities list styles */
.activities-panel {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.activities-list {
  list-style: none;
  margin-top: 10px;
}

.activities-list li {
  padding: 8px 10px;
  margin-bottom: 5px;
  border-radius: 4px;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.activities-list li.selected {
  background-color: #e6f7ff;
  border-left: 3px solid #4a90e2;
}

.activity-color {
  display: inline-block;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin-right: 10px;
}

/* Chat styles */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-messages {
  flex-grow: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.message {
  padding: 10px 15px;
  border-radius: 18px;
  margin-bottom: 10px;
  max-width: 80%;
  word-wrap: break-word;
}

.message.user {
  background-color: #dcf8c6;
  align-self: flex-end;
  margin-left: auto;
}

.message.assistant {
  background-color: #f0f0f0;
  align-self: flex-start;
}

.message.system {
  background-color: #e6f7ff;
  color: #0066cc;
  text-align: center;
  max-width: 100%;
  font-style: italic;
  font-size: 0.9em;
}

.chat-input {
  display: flex;
  padding: 10px;
  border-top: 1px solid #e5e5e5;
  background-color: white;
}

.chat-input input {
  flex-grow: 1;
  padding: 10px 15px;
  border: 1px solid #e5e5e5;
  border-radius: 30px;
  font-size: 14px;
  outline: none;
}

.chat-input button {
  margin-left: 10px;
  padding: 0 20px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 30px;
  font-weight: bold;
  cursor: pointer;
}

.typing-indicator {
  display: flex;
  padding: 10px 15px;
  background-color: #f0f0f0;
  border-radius: 18px;
  width: fit-content;
  margin-bottom: 10px;
  align-self: flex-start;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: #999;
  display: inline-block;
  margin-right: 4px;
  animation: typing 1.3s infinite;
}

.typing-indicator span:nth-child(1) { animation-delay: 0s; }
.typing-indicator span:nth-child(2) { animation-delay: 0.3s; }
.typing-indicator span:nth-child(3) { animation-delay: 0.6s; }

@keyframes typing {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-5px); }
}

/* Connection status indicator */
.connection-status {
  position: fixed;
  bottom: 10px;
  right: 10px;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 12px;
  color: white;
  z-index: 1000;
}

.connection-status.connected {
  background-color: #4caf50;
}

.connection-status.connecting {
  background-color: #ff9800;
}

.connection-status.disconnected {
  background-color: #f44336;
}
