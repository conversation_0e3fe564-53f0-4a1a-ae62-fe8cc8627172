import React from 'react';
import { render, screen, queryByTestId } from '@testing-library/react';
import '@testing-library/jest-dom'; // Import jest-dom matchers
import App from '../App'; // Adjust the import path as necessary
import { UserContext } from '../contexts/UserContext'; // Import the actual context
import { WebSocketContext } from '../contexts/WebSocketContext'; // Import the actual context
import { WebSocketManager } from '../services/WebSocketManager'; // Use named import

// Mock the WebSocketManager class
jest.mock('../services/WebSocketManager');
const MockWebSocketManager = WebSocketManager as jest.MockedClass<
  typeof WebSocketManager
>;

// Mock the DebugConsole component to simplify testing App's rendering logic
jest.mock('../components/debug/DebugConsole', () => () => (
  <div data-testid="debug-console">Mock Debug Console</div>
));
// Mock GameContainer as well
jest.mock('../components/layout/GameContainer', () => () => (
  <div data-testid="game-container">Mock Game Container</div>
));

describe('<App />', () => {
  let mockWebSocketManagerInstance: jest.Mocked<WebSocketManager>;

  beforeEach(() => {
    // Create a fresh mock instance for each test with a dummy URL
    mockWebSocketManagerInstance = new MockWebSocketManager(
      'ws://dummy-url'
    ) as jest.Mocked<WebSocketManager>;
    // Provide mock implementations for methods used by DebugProvider if any
    // e.g., mockWebSocketManagerInstance.addDebugMessageHandler = jest.fn();
    // e.g., mockWebSocketManagerInstance.removeDebugMessageHandler = jest.fn();
  });

  // Adjust error type to allow Error objects
  const renderAppWithUser = (user: any, loading = false, error: Error | null = null) => {
    // Provide a complete mock state for WebSocketContext
    const mockWebSocketContextValue = {
      webSocketManager: mockWebSocketManagerInstance,
      isConnected: true,
      isConnecting: false,
      connectionError: null,
      isProcessing: false,
      isTyping: false,
      sendChatMessage: jest.fn(() => true),
      sendSpinResult: jest.fn(() => true),
      checkWorkflowStatus: jest.fn(() => true),
      reconnect: jest.fn(),
      clearChatHistory: jest.fn(),
      chatMessages: [],
      systemMessages: [],
      workflowState: {
        currentWorkflow: null,
        status: 'unknown' as const, // Use 'as const' for literal type
        workflowId: null,
        lastUpdated: new Date().toISOString()
      },
      lastChatMessage: null,
      lastWheelData: null,
      lastError: null,
      lastWorkflowStatus: null,
      lastStateUpdate: Date.now()
    };

    return render(
      <WebSocketContext.Provider value={mockWebSocketContextValue}>
        <UserContext.Provider value={{ user, loading, error }}>
          {/* We need to render the actual App structure here,
              but since App wraps content in UserProvider, we provide it directly */}
          <App />
        </UserContext.Provider>
      </WebSocketContext.Provider>
    );
  };

  test('renders GameContainer for all users', () => {
    const nonStaffUser = { id: '1', username: 'testuser', is_staff: false };
    renderAppWithUser(nonStaffUser);
    expect(screen.getByTestId('game-container')).toBeInTheDocument();
  });

  test('renders DebugConsole for staff users', () => {
    const staffUser = { id: '2', username: 'staffuser', is_staff: true };
    const { container } = renderAppWithUser(staffUser);
    // Use queryByTestId which returns null if not found
    expect(queryByTestId(container, 'debug-console')).toBeInTheDocument();
  });

  test('does not render DebugConsole for non-staff users', () => {
    const nonStaffUser = { id: '1', username: 'testuser', is_staff: false };
    const { container } = renderAppWithUser(nonStaffUser);
    expect(queryByTestId(container, 'debug-console')).not.toBeInTheDocument();
  });

  test('does not render DebugConsole when user data is loading', () => {
    const { container } = renderAppWithUser(null, true); // loading = true
    expect(queryByTestId(container, 'debug-console')).not.toBeInTheDocument();
  });

  test('does not render DebugConsole when there is an error fetching user', () => {
    const { container } = renderAppWithUser(null, false, new Error('Fetch failed')); // error state
    expect(queryByTestId(container, 'debug-console')).not.toBeInTheDocument();
  });
});
