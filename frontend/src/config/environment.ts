/**
 * environment.ts
 * 
 * Configuration settings for the frontend application.
 * This file contains environment-specific settings that can be adjusted
 * for different deployment environments (development, testing, production).
 */

// Dynamic WebSocket URL based on current location
// This automatically determines the correct WebSocket URL based on the current protocol and host
export const WS_URL = 
  (window.location.protocol === 'https:' ? 'wss://' : 'ws://') + 
  window.location.hostname +
  ':' + (window.location.port === '3000' ? '8000' : window.location.port) + 
  '/ws/game/';

// Environment configuration
export const ENV = {
  // Development environment settings
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // WebSocket settings
  // When true, the application will use a mock WebSocket implementation
  // that simulates backend responses without requiring a real backend connection.
  // With the hybrid approach, we still use the real WebSocketProvider and WebSocketManager,
  // but replace the actual WebSocket with a mock implementation.
  USE_MOCK_WS: false, // Set to false to connect to the real backend

  // Logging settings
  VERBOSE_LOGGING: true, // Enable detailed logging for debugging
};

// Export a function to check if we're in development mode
export const isDevelopment = (): boolean => ENV.NODE_ENV === 'development';
