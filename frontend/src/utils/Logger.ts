/**
 * Logger.ts
 * 
 * A utility for consistent logging across the application.
 * This logger outputs to both the browser console and can send logs to the backend via WebSocket.
 */

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

// Interface for log message structure
export interface LogMessage {
  level: LogLevel;
  component: string;
  message: string;
  data?: any;
  timestamp: string;
}

class Logger {
  private webSocketManager: any = null;
  private enabled: boolean = true;
  private terminalLogging: boolean = true; // Default to true for debugging
  
  /**
   * Set the WebSocketManager instance for sending logs to the backend
   * @param manager WebSocketManager instance
   */
  setWebSocketManager(manager: any) {
    this.webSocketManager = manager;
  }
  
  /**
   * Enable or disable logging
   * @param enabled Whether logging is enabled
   */
  setEnabled(enabled: boolean) {
    this.enabled = enabled;
  }
  
  /**
   * Enable or disable terminal logging via WebSocket
   * @param enabled Whether terminal logging is enabled
   */
  setTerminalLogging(enabled: boolean) {
    this.terminalLogging = enabled;
  }
  
  /**
   * Log a message with the specified level
   * @param level Log level
   * @param component Component name
   * @param message Log message
   * @param data Optional data to include
   */
  log(level: LogLevel, component: string, message: string, data?: any): void {
    if (!this.enabled) return;
    
    // Format the log message
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${component}] ${message}`;
    
    // Log to browser console
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(logMessage, data);
        break;
      case LogLevel.INFO:
        console.info(logMessage, data);
        break;
      case LogLevel.WARN:
        console.warn(logMessage, data);
        break;
      case LogLevel.ERROR:
        console.error(logMessage, data);
        break;
    }
    
    // Send log to backend if terminal logging is enabled
    // Note: Requires backend to handle 'log_message' type
    if (this.terminalLogging && this.webSocketManager && this.webSocketManager.isConnected()) {
      try {
        this.webSocketManager.send('log_message', {
          level,
          component,
          message,
          data: data ? JSON.stringify(data) : undefined,
          timestamp
        });
      } catch (error) {
        console.error('[Logger] Failed to send log to backend:', error);
      }
    }
  }
  
  /** Log a debug message */
  debug(component: string, message: string, data?: any): void {
    this.log(LogLevel.DEBUG, component, message, data);
  }
  
  /** Log an info message */
  info(component: string, message: string, data?: any): void {
    this.log(LogLevel.INFO, component, message, data);
  }
  
  /** Log a warning message */
  warn(component: string, message: string, data?: any): void {
    this.log(LogLevel.WARN, component, message, data);
  }
  
  /** Log an error message */
  error(component: string, message: string, data?: any): void {
    this.log(LogLevel.ERROR, component, message, data);
  }
}

// Export a singleton instance
export const logger = new Logger();
