/**
 * mockWebSocketProvider.ts
 *
 * A mock implementation of the WebSocket connection for development and testing.
 * This module provides a realistic simulation of the backend WebSocket connection
 * to enable frontend development without requiring a running Django server.
 */

import { WheelItem } from '../services/WebSocketManager';

// Flag to enable/disable mock WebSocket (can be controlled via env variable)
export const ENABLE_MOCK_WEBSOCKET = true;

// Mock data for the wheel
const MOCK_WHEEL_ITEMS: WheelItem[] = [
  {
    id: 'item-1',
    name: 'Mindfulness Meditation',
    description: 'A 10-minute guided meditation focusing on breath awareness',
    percentage: 20,
    color: '#4a90e2',
    domain: 'wellness',
    base_challenge_rating: 30,
    activity_tailored_id: 'act-1'
  },
  {
    id: 'item-2',
    name: 'Creative Writing',
    description: 'Write a short story based on a random prompt',
    percentage: 15,
    color: '#50e3c2',
    domain: 'creative',
    base_challenge_rating: 45,
    activity_tailored_id: 'act-2'
  },
  {
    id: 'item-3',
    name: 'Physical Exercise',
    description: 'A quick 5-minute workout to boost energy',
    percentage: 25,
    color: '#f5a623',
    domain: 'fitness',
    base_challenge_rating: 50,
    activity_tailored_id: 'act-3'
  },
  {
    id: 'item-4',
    name: 'Learning Challenge',
    description: 'Learn 5 new words in a foreign language',
    percentage: 20,
    color: '#bd10e0',
    domain: 'intellectual',
    base_challenge_rating: 40,
    activity_tailored_id: 'act-4'
  },
  {
    id: 'item-5',
    name: 'Social Connection',
    description: 'Reach out to a friend you haven\'t spoken to recently',
    percentage: 20,
    color: '#7ed321',
    domain: 'social',
    base_challenge_rating: 35,
    activity_tailored_id: 'act-5'
  }
];

/**
 * Creates a mock WebSocket that simulates backend behavior
 * @returns A mocked WebSocket instance
 */
export class MockWebSocket implements WebSocket {
    // Add these static constants for the WebSocket class
    static readonly CONNECTING = 0;
    static readonly OPEN = 1;
    static readonly CLOSING = 2;
    static readonly CLOSED = 3;
    
    // Add these instance properties to satisfy the interface
    readonly CONNECTING = 0;
    readonly OPEN = 1;
    readonly CLOSING = 2;
    readonly CLOSED = 3;
    
    private static instance: MockWebSocket | null = null;
    private listeners: Record<string, EventListener[]> = {
      'open': [],
      'message': [],
      'close': [],
      'error': []
    };
    private connectionState: number = WebSocket.CONNECTING;
    private _url: string;
    private messageQueue: string[] = [];
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 5;

  
  // WebSocket properties
  binaryType: BinaryType = 'blob';
  bufferedAmount: number = 0;
  extensions: string = '';
  protocol: string = '';
  readyState: number = WebSocket.CONNECTING;
  url: string = '';
  
  // Required event handlers
  onclose: ((this: WebSocket, ev: CloseEvent) => any) | null = null;
  onerror: ((this: WebSocket, ev: Event) => any) | null = null;
  onmessage: ((this: WebSocket, ev: MessageEvent<any>) => any) | null = null;
  onopen: ((this: WebSocket, ev: Event) => any) | null = null;

  // Static method to get the singleton instance
  public static getInstance(url: string): MockWebSocket {
    if (!MockWebSocket.instance) {
      MockWebSocket.instance = new MockWebSocket(url);
    }
    return MockWebSocket.instance;
  }

  constructor(url: string) {
    this._url = url;
    this.url = url;
    this.readyState = WebSocket.CONNECTING;

    // Simulate connection delay
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      this.connectionState = WebSocket.OPEN;

      // Create open event
      const openEvent = new Event('open');
      
      // Trigger open event
      this.dispatchEvent(openEvent);
      
      // Call onopen handler if present
      if (this.onopen) {
        this.onopen.call(this, openEvent);
      }

      // Send welcome message
      this.simulateMessage({
        type: 'system_message',
        content: 'Connected to the Game of Life server. Ready for your journey!'
      });

      // Process any queued messages
      this.processMessageQueue();
    }, 500);
  }

  // WebSocket methods
  addEventListener(type: string, listener: EventListener): void {
    if (!this.listeners[type]) {
      this.listeners[type] = [];
    }
    this.listeners[type].push(listener);
  }

  removeEventListener(type: string, listener: EventListener): void {
    if (this.listeners[type]) {
      this.listeners[type] = this.listeners[type].filter(l => l !== listener);
    }
  }

  dispatchEvent(event: Event): boolean {
    const type = event.type;
    if (this.listeners[type]) {
      this.listeners[type].forEach(listener => {
        listener(event);
      });
    }
    
    return !event.cancelable || !event.defaultPrevented;
  }

  close(code?: number, reason?: string): void {
    this.readyState = WebSocket.CLOSING;

    setTimeout(() => {
      this.readyState = WebSocket.CLOSED;
      this.connectionState = WebSocket.CLOSED;

      // Create close event
      const closeEvent = new CloseEvent('close', {
        code: code || 1000,
        reason: reason || 'Connection closed normally',
        wasClean: true
      });
      
      // Trigger close event
      this.dispatchEvent(closeEvent);
      
      // Call onclose handler if present
      if (this.onclose) {
        this.onclose.call(this, closeEvent);
      }
    }, 100);
  }

  send(data: string | ArrayBufferLike | Blob | ArrayBufferView): void {
    if (this.readyState !== WebSocket.OPEN) {
      this.messageQueue.push(data.toString());
      return;
    }

    this.processMessage(data.toString());
  }

  // Helper methods
  private processMessageQueue(): void {
    this.messageQueue.forEach(message => {
      this.processMessage(message);
    });
    this.messageQueue = [];
  }

  private processMessage(messageData: string): void {
    try {
      const message = JSON.parse(messageData);

      switch(message.type) {
        case 'chat_message':
          this.handleChatMessage(message);
          break;
        case 'spin_result':
          this.handleSpinResult(message);
          break;
        case 'workflow_status_request':
          this.handleWorkflowStatusRequest(message);
          break;
        default:
          console.warn('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  }

  private handleChatMessage(message: any): void {
    // Echo back the message as an acknowledgment
    this.simulateMessage({
      type: 'chat_message',
      content: message.content.message,
      is_user: true
    });

    // Set processing status
    this.simulateMessage({
      type: 'processing_status',
      status: 'processing'
    });

    // Simulate agent thinking time
    setTimeout(() => {
      // Generate response based on message content
      let response: string;

      if (message.content.message.toLowerCase().includes('activity') ||
          message.content.message.toLowerCase().includes('wheel') ||
          message.content.message.toLowerCase().includes('suggestion')) {
        response = "I've prepared some activities for you. Let's spin the wheel to see what comes up!";

        // Also send wheel data after a short delay
        setTimeout(() => {
          this.simulateMessage({
            type: 'wheel_data',
            wheel: {
              name: 'Daily Activity Wheel',
              items: MOCK_WHEEL_ITEMS
            }
          });
        }, 500);
      } else if (message.content.message.toLowerCase().includes('hello') ||
                message.content.message.toLowerCase().includes('hi')) {
        response = "Hello! I'm your personal coach. How are you feeling today?";
      } else {
        response = `I received your message: "${message.content.message}". How can I help you further with your journey?`;
      }

      // Send mock response
      this.simulateMessage({
        type: 'chat_message',
        content: response,
        is_user: false
      });

      // Set processing complete
      this.simulateMessage({
        type: 'processing_status',
        status: 'completed'
      });
    }, 1500);
  }

  private handleSpinResult(message: any): void {
    // Acknowledge the selection
    this.simulateMessage({
      type: 'chat_message',
      content: `Great choice! You selected: ${message.content.name}`,
      is_user: false
    });

    // Add more context about the activity
    if (message.content.description) {
      setTimeout(() => {
        this.simulateMessage({
          type: 'chat_message',
          content: `Here's what you'll do: ${message.content.description}. Would you like me to guide you through this activity?`,
          is_user: false
        });
      }, 1000);
    }
  }

  private handleWorkflowStatusRequest(message: any): void {
    this.simulateMessage({
      type: 'workflow_status',
      workflow_id: message.content.workflow_id,
      status: 'completed'
    });
  }

  /**
   * Simulates receiving a message from the server
   */
  private simulateMessage(data: any): void {
    if (this.readyState !== WebSocket.OPEN) return;

    const event = new MessageEvent('message', {
      data: JSON.stringify(data)
    });

    // Call listeners registered with addEventListener
    this.dispatchEvent(event);
    
    // Also call onmessage handler if present
    if (this.onmessage) {
      this.onmessage.call(this, event);
    }
  }

  /**
   * Simulates a connection error
   */
  public simulateError(message: string = 'Connection error occurred'): void {
    if (this.readyState !== WebSocket.OPEN) return;

    // Send error message
    this.simulateMessage({
      type: 'error',
      content: message
    });

    // Create and trigger error event
    const errorEvent = new ErrorEvent('error', {
      message
    });
    
    // Dispatch event to listeners
    this.dispatchEvent(errorEvent);
    
    // Call onerror handler if present
    if (this.onerror) {
      this.onerror.call(this, errorEvent);
    }
  }

  /**
   * Simulates a disconnection with automatic reconnect attempts
   */
  public simulateDisconnect(reconnect: boolean = true): void {
    if (this.readyState !== WebSocket.OPEN) return;

    this.close(1001, 'Connection lost');

    if (reconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;

      setTimeout(() => {
        this.readyState = WebSocket.CONNECTING;

        setTimeout(() => {
          this.readyState = WebSocket.OPEN;
          this.connectionState = WebSocket.OPEN;

          // Create open event
          const openEvent = new Event('open');
          
          // Trigger open event
          this.dispatchEvent(openEvent);
          
          // Call onopen handler if present
          if (this.onopen) {
            this.onopen.call(this, openEvent);
          }

          // Send reconnection message
          this.simulateMessage({
            type: 'system_message',
            content: `Reconnected to the Game of Life server (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}).`
          });

          // Process any queued messages
          this.processMessageQueue();
        }, 1000);
      }, 2000 * this.reconnectAttempts); // Exponential backoff
    }
  }
}

/**
 * Factory function to create either a real WebSocket or a mock based on configuration
 */
export function createWebSocket(url: string): WebSocket {
  if (ENABLE_MOCK_WEBSOCKET) {
    console.log('Using mock WebSocket connection');
    return MockWebSocket.getInstance(url);
  } else {
    console.log('Using real WebSocket connection to:', url);
    return new WebSocket(url);
  }
}