/**
 * WebSocketProviderSelector.tsx
 * 
 * This component determines whether to use the real WebSocket provider with a real or mock WebSocket
 * based on environment settings, making it easy to switch between development and production modes.
 * 
 * In the hybrid approach, we always use the real WebSocketProvider, but we may use a mock WebSocket
 * implementation when ENV.USE_MOCK_WS is true.
 */

import React, { FC, ReactNode } from 'react';
import { WebSocketProvider } from '../contexts/WebSocketContext';
import { ENV } from '../config/environment';

// Props interface matches the real WebSocketProvider
interface WebSocketProviderSelectorProps {
  children: ReactNode;
  url: string;
  userProfileId?: string;
}

/**
 * WebSocketProviderSelector component
 * 
 * Configures the WebSocketProvider based on environment settings.
 * With the hybrid approach, we always use the real WebSocketProvider,
 * but the WebSocketManager will use a mock WebSocket when ENV.USE_MOCK_WS is true.
 * 
 * Usage:
 * ```tsx
 * <WebSocketProviderSelector url={wsUrl} userProfileId={userId}>
 *   <App />
 * </WebSocketProviderSelector>
 * ```
 */
export const WebSocketProviderSelector: FC<WebSocketProviderSelectorProps> = (props) => {
  // Check if we should use mock WebSocket
  const useMockWs = ENV.USE_MOCK_WS;
  
  console.log('[WebSocketProviderSelector] Initializing with:', {
    useMockWs,
    url: props.url,
    userProfileId: props.userProfileId
  });
  
  console.log('[WebSocketProviderSelector] Using real WebSocketProvider with', 
    useMockWs ? 'mock WebSocket' : 'real WebSocket');
  
  // Always use the real WebSocketProvider, the WebSocketManager will handle using mock WebSocket if needed
  return <WebSocketProvider {...props} />;
};

export default WebSocketProviderSelector;
