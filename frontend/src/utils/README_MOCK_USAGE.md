# Mock WebSocket Provider

This document explains how to use the `MockWebSocketProvider` for development and testing when the backend WebSocket server is unavailable or not functioning correctly.

## Problem Overview

When developing React applications with WebSocket integrations, several challenges can arise:

1. **Backend Dependency**: Frontend development becomes blocked if the backend WebSocket server is down or not yet implemented.
2. **Inconsistent Responses**: During development, you may need predictable, consistent WebSocket responses to test specific UI scenarios.
3. **Error Testing**: It's difficult to test error handling and reconnection logic with a real backend.
4. **Development Speed**: Waiting for real backend responses slows down the development cycle.

## Solution: MockWebSocketProvider

The `MockWebSocketProvider` simulates WebSocket communication, allowing frontend development to continue independently of the backend status.

### Key Features

- **Drop-in Replacement**: The mock provider has the same interface as the real provider, allowing seamless swapping.
- **Simulated Responses**: Generates realistic responses based on message content.
- **Configurable Behavior**: Supports configuring delays, connection errors, and more.
- **Workflow Simulation**: Mimics the backend's workflow processing patterns.

## Usage Instructions

### Option 1: Temporary Replacement in App.tsx

For quick testing, modify your App.tsx to use the mock provider:

```tsx
// App.tsx
import { FC } from 'react';
// Import the mock provider instead of the real one
// import { WebSocketProvider } from './contexts/WebSocketContext';
import { WebSocketProviderMock as WebSocketProvider } from './utils/MockWebSocketProvider';
import GameContainer from './components/layout/GameContainer';

const WS_URL = 'ws://localhost:8000/ws/game/';
const USER_ID = 'demo-user-1';

const App: FC = () => {
  return (
    <WebSocketProvider url={WS_URL} userProfileId={USER_ID} simulateDelay={true}>
      <GameContainer />
    </WebSocketProvider>
  );
};

export default App;
```

### Option 2: Environment-Based Provider Selection

For a more robust approach, automatically select the appropriate provider based on environment:

```tsx
// Create a file like src/providers/WebSocketProviderSelector.tsx

import { FC, ReactNode } from 'react';
import { WebSocketProvider as RealProvider } from '../contexts/WebSocketContext';
import { WebSocketProviderMock } from '../utils/MockWebSocketProvider';

// Use the mock provider when the REACT_APP_USE_MOCK_WS is set to 'true'
const Provider = process.env.REACT_APP_USE_MOCK_WS === 'true' 
  ? WebSocketProviderMock 
  : RealProvider;

interface Props {
  children: ReactNode;
  url: string;
  userProfileId?: string;
}

export const WebSocketProviderSelector: FC<Props> = (props) => {
  return (
    <Provider {...props} />
  );
};
```

Then use this selector in your App:

```tsx
// App.tsx
import { FC } from 'react';
import { WebSocketProviderSelector as WebSocketProvider } from './providers/WebSocketProviderSelector';
import GameContainer from './components/layout/GameContainer';

const WS_URL = 'ws://localhost:8000/ws/game/';
const USER_ID = 'demo-user-1';

const App: FC = () => {
  return (
    <WebSocketProvider url={WS_URL} userProfileId={USER_ID}>
      <GameContainer />
    </WebSocketProvider>
  );
};

export default App;
```

Then, to switch between real and mock providers, set the environment variable:

```bash
# Use the mock provider
REACT_APP_USE_MOCK_WS=true npm start

# Use the real provider
REACT_APP_USE_MOCK_WS=false npm start
```

## Configuration Options

The `MockWebSocketProvider` accepts the following props:

- **children**: The components to render inside the provider
- **url**: The WebSocket URL (not used for connection, but stored for compatibility)
- **userProfileId**: The user's profile ID
- **simulateDelay** (optional, default: `true`): Whether to simulate network delays
- **simulateConnectionErrors** (optional, default: `false`): Randomly simulate connection errors
- **autoConnect** (optional, default: `true`): Whether to automatically connect on mount

## Mock Provider Implementation Details

### Message Types

The mock provider simulates all the message types defined in the WebSocket API contract:

- **System Messages**: Connection status, errors, etc.
- **Chat Messages**: Responses from the virtual agent
- **Processing Status**: Updates on workflow processing
- **Wheel Data**: Activity wheel configuration
- **Workflow Status**: Status of ongoing workflows

### Workflow Types

The provider recognizes and responds to different types of workflows:

- **wheel_generation**: Triggered by messages about activities or the wheel
- **post_spin**: After selecting an activity from the wheel
- **progress_review**: When asking about progress
- **pre_spin**: Default for other messages

### Response Generation

The mock system analyzes message content to generate contextually relevant responses:

- If a message contains "wheel" or "activity", it sends wheel data
- If the message type is "spin_result", it responds with activity guidance
- For progress-related messages, it provides progress summaries
- For other messages, it generates generic responses

## Important Notes

1. **Testing Production Code**: Always test with the real WebSocket provider before deploying to production.
2. **State Persistence**: Unlike the real provider, the mock provider doesn't persist state between sessions.
3. **Request Matching**: The mock provider tries to match responses to requests, but may not cover all edge cases.

## Troubleshooting

If you encounter issues with the mock provider:

1. **Check Console Logs**: The mock provider logs connection and message events
2. **Verify Context Usage**: Ensure components are using the WebSocket context properly
3. **Configuration**: Check that the provider is properly configured with required props
4. **Component Updates**: If components don't update with new data, verify that they're properly subscribed to context changes
