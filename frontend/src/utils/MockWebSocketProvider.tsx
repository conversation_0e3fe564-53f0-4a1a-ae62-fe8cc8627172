/**
 * MockWebSocketProvider.tsx
 *
 * This component provides a mocked version of the WebSocketProvider for development and testing.
 * It simulates WebSocket communication with the backend without requiring a real connection.
 * 
 * Use this provider in development when:
 * - The backend is not available or not working
 * - You want to test specific frontend behaviors with predictable "server" responses
 * - You're developing UI features that depend on WebSocket data
 */

import React, { createContext, useContext, useEffect, useState, type ReactNode, useCallback, useMemo } from 'react';
import {
  ChatMessageData,
  WheelData,
  ProcessingStatusData,
  ErrorData,
  SystemMessageData,
  WorkflowStatusData
} from '../services/WebSocketManager';

// Import the same types and context from the real WebSocketContext
import {
  StoredMessage,
  WebSocketProvider as RealWebSocketProvider,
  WorkflowState,
  WorkflowType,
  WebSocketContext as RealWebSocketContext,
  useWebSocket
} from '../contexts/WebSocketContext';

// Helper function to generate unique IDs
const generateId = (): string => {
  return `mock-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

// Context definition - should match the real WebSocketContext interface
interface MockWebSocketContextState {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;

  // Processing state
  isProcessing: boolean;

  // Methods
  sendChatMessage: (message: string, metadata?: Record<string, any>) => boolean;
  sendSpinResult: (activityId: string, name: string, description?: string) => boolean;
  checkWorkflowStatus: (workflowId: string) => boolean;
  reconnect: () => void;
  clearChatHistory: () => void;

  // Message histories
  chatMessages: StoredMessage[]; // History of chat messages
  systemMessages: StoredMessage[]; // History of system messages

  // Workflow state
  workflowState: WorkflowState;

  // Last received data (for specific hook subscriptions)
  lastChatMessage: ChatMessageData | null;
  lastWheelData: WheelData | null;
  lastError: ErrorData | null;
  lastWorkflowStatus: WorkflowStatusData | null;

  // UI state indicators
  isTyping: boolean; // Indicates if the assistant is "typing"

  // For compatibility with real context
  webSocketManager: any;
  lastStateUpdate: number;
}

// Maximum number of messages to keep in history
const MAX_CHAT_HISTORY = 100;

// Use the same context as the real WebSocketContext to ensure compatibility
const MockWebSocketContext = RealWebSocketContext;

// Sample data for mock responses
const MOCK_WHEEL_DATA: WheelData = {
  type: 'wheel_data',
  wheel: {
    name: "Today's Activities",
    items: [
      {
        id: 'act-1',
        name: 'Mindful Meditation',
        description: 'A 10-minute guided meditation focusing on breath and body awareness',
        percentage: 25,
        color: '#66BB6A',
        domain: 'mindfulness',
        base_challenge_rating: 30,
        activity_tailored_id: 'act-1'
      },
      {
        id: 'act-2',
        name: 'Nature Walk',
        description: 'A 20-minute walk outside, paying attention to the natural environment',
        percentage: 20,
        color: '#42A5F5',
        domain: 'physical',
        base_challenge_rating: 20,
        activity_tailored_id: 'act-2'
      },
      {
        id: 'act-3',
        name: 'Gratitude Journaling',
        description: 'Write down three things you are grateful for today',
        percentage: 20,
        color: '#7E57C2',
        domain: 'emotional',
        base_challenge_rating: 15,
        activity_tailored_id: 'act-3'
      },
      {
        id: 'act-4',
        name: 'Creative Drawing',
        description: 'Spend 15 minutes drawing without judgment',
        percentage: 15,
        color: '#FFA726',
        domain: 'creative',
        base_challenge_rating: 40,
        activity_tailored_id: 'act-4'
      },
      {
        id: 'act-5',
        name: 'Digital Detox',
        description: 'Disconnect from all digital devices for one hour',
        percentage: 20,
        color: '#EC407A',
        domain: 'lifestyle',
        base_challenge_rating: 60,
        activity_tailored_id: 'act-5'
      }
    ]
  }
};

// Provider props definition
interface MockWebSocketProviderProps {
  children: ReactNode;
  url: string;
  userProfileId?: string;
  simulateDelay?: boolean;
  simulateConnectionErrors?: boolean;
  autoConnect?: boolean;
}

/**
 * MockWebSocketProvider component
 * 
 * A drop-in replacement for the real WebSocketProvider that simulates
 * WebSocket communication for development and testing.
 */
export const MockWebSocketProvider: React.FC<MockWebSocketProviderProps> = ({
  children,
  url,
  userProfileId,
  simulateDelay = true,
  simulateConnectionErrors = false,
  autoConnect = true
}) => {
  console.log('[MockWebSocketProvider] Initializing with:', {
    url,
    userProfileId,
    simulateDelay,
    simulateConnectionErrors,
    autoConnect
  });
  // Connection state
  const [connectionState, setConnectionState] = useState<{
    isConnected: boolean;
    isConnecting: boolean;
    error: string | null;
  }>({
    isConnected: false,
    isConnecting: false,
    error: null
  });

  // Processing and UI states
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [isTyping, setIsTyping] = useState<boolean>(false);

  // Message histories
  const [chatMessages, setChatMessages] = useState<StoredMessage[]>([]);
  const [systemMessages, setSystemMessages] = useState<StoredMessage[]>([]);

  // Last message of each type
  const [lastChatMessage, setLastChatMessage] = useState<ChatMessageData | null>(null);
  const [lastWheelData, setLastWheelData] = useState<WheelData | null>(null);
  const [lastError, setLastError] = useState<ErrorData | null>(null);
  const [lastWorkflowStatus, setLastWorkflowStatus] = useState<WorkflowStatusData | null>(null);

  // Workflow state
  const [workflowState, setWorkflowState] = useState<WorkflowState>({
    currentWorkflow: null,
    status: 'unknown',
    workflowId: null,
    lastUpdated: new Date().toISOString()
  });

  // Timestamp for state updates
  const [lastStateUpdate, setLastStateUpdate] = useState<number>(Date.now());

  // Helper to add a message to history
  const addMessageToHistory = useCallback((
    messageData: any,
    setter: React.Dispatch<React.SetStateAction<StoredMessage[]>>
  ) => {
    const storedMessage: StoredMessage = {
      id: generateId(),
      timestamp: new Date().toISOString(),
      data: messageData
    };

    setter(prev => {
      const updatedMessages = [...prev, storedMessage];
      return updatedMessages.slice(-MAX_CHAT_HISTORY);
    });

    return storedMessage;
  }, []);

  // Connect to mock "backend" on mount
  useEffect(() => {
    if (autoConnect) {
      handleConnect();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Send initial wheel data and set workflow state after connection
  useEffect(() => {
    if (connectionState.isConnected) {
      console.log('[MockWebSocketProvider] Connected, sending initial wheel data and setting workflow state');
      
      // Send wheel data after a short delay to simulate backend processing
      setTimeout(() => {
        // Set wheel data
        setLastWheelData(MOCK_WHEEL_DATA);
        console.log('[MockWebSocketProvider] Sent wheel data:', MOCK_WHEEL_DATA);
        
        // Set workflow state to pre_spin to enable the wheel
        setWorkflowState({
          currentWorkflow: 'pre_spin',
          status: 'completed',
          workflowId: `mock-workflow-${Date.now()}`,
          lastUpdated: new Date().toISOString()
        });
        console.log('[MockWebSocketProvider] Set workflow state to pre_spin');
        
        // Update last state update timestamp
        setLastStateUpdate(Date.now());
      }, 1000);
    }
  }, [connectionState.isConnected]);

  // Simulate connection to backend
  const handleConnect = useCallback(() => {
    // Reset error state
    setConnectionState(prev => ({
      ...prev,
      isConnecting: true,
      error: null
    }));

    // Simulate connection delay
    setTimeout(() => {
      if (simulateConnectionErrors && Math.random() < 0.2) {
        // 20% chance of connection error
        setConnectionState({
          isConnected: false,
          isConnecting: false,
          error: 'Failed to establish WebSocket connection'
        });
        
        // Set error state
        setLastError({
          type: 'error',
          content: 'Failed to establish WebSocket connection'
        });
        
        addMessageToHistory(
          { type: 'error', content: 'Failed to establish WebSocket connection' },
          setSystemMessages
        );
      } else {
        // Successful connection
        setConnectionState({
          isConnected: true,
          isConnecting: false,
          error: null
        });
        
        // Send welcome message
        const welcomeMessage: SystemMessageData = {
          type: 'system_message',
          content: 'Connected to the Game of Life server (MOCK). Ready for your journey!'
        };
        
        addMessageToHistory(welcomeMessage, setSystemMessages);
      }
      
      setLastStateUpdate(Date.now());
    }, simulateDelay ? 500 + Math.random() * 1000 : 0);
  }, [simulateDelay, simulateConnectionErrors, addMessageToHistory]);

  // Mock processing a chat message from the user
  const processChatMessage = useCallback((
    message: string,
    metadata: Record<string, any> = {}
  ) => {
    // Create a workflow ID for this interaction
    const workflowId = `mock-workflow-${Date.now()}`;
    
    // Determine workflow type based on message content
    let workflowType: WorkflowType = 'pre_spin';
    
    if (metadata.type === 'spin_result') {
      workflowType = 'post_spin';
    } else if (message.toLowerCase().includes('wheel') || message.toLowerCase().includes('activity')) {
      workflowType = 'wheel_generation';
    } else if (message.toLowerCase().includes('progress')) {
      workflowType = 'progress_review';
    }
    
    // Update workflow state
    setWorkflowState({
      currentWorkflow: workflowType,
      status: 'initiated',
      workflowId,
      lastUpdated: new Date().toISOString()
    });
    
    // Send processing status
    const processingStatus: ProcessingStatusData = {
      type: 'processing_status',
      status: 'processing'
    };
    
    setIsProcessing(true);
    setIsTyping(true);
    
    // Simulate think time
    setTimeout(() => {
      // Generate appropriate response based on workflow type
      let responseMessage: ChatMessageData;
      
      switch (workflowType) {
        case 'wheel_generation':
          responseMessage = {
            type: 'chat_message',
            content: "I've prepared some activities that might help you today. Give the wheel a spin when you're ready!",
            is_user: false
          };
          
          // Set last chat message
          setLastChatMessage(responseMessage);
          
          // Add to chat history
          addMessageToHistory(responseMessage, setChatMessages);
          
          // Send wheel data after a short delay
          setTimeout(() => {
            setLastWheelData(MOCK_WHEEL_DATA);
            
            // Update workflow status to completed
            setWorkflowState(prev => ({
              ...prev,
              status: 'completed',
              lastUpdated: new Date().toISOString()
            }));
            
            // Send workflow status
            const workflowStatus: WorkflowStatusData = {
              type: 'workflow_status',
              workflow_id: workflowId,
              status: 'completed'
            };
            
            setLastWorkflowStatus(workflowStatus);
            
            // Send processing complete status
            const completeStatus: ProcessingStatusData = {
              type: 'processing_status',
              status: 'completed'
            };
            
            setIsProcessing(false);
            setIsTyping(false);
            
            setLastStateUpdate(Date.now());
          }, simulateDelay ? 500 : 0);
          
          break;
          
        case 'post_spin':
          // Response for after selecting an activity
          responseMessage = {
            type: 'chat_message',
            content: `Great choice! I think this activity will be really beneficial for you. Would you like some more guidance on how to approach it?`,
            is_user: false
          };
          
          setLastChatMessage(responseMessage);
          addMessageToHistory(responseMessage, setChatMessages);
          
          // Update workflow status to completed
          setWorkflowState(prev => ({
            ...prev,
            status: 'completed',
            lastUpdated: new Date().toISOString()
          }));
          
          // Send workflow status
          const workflowStatus: WorkflowStatusData = {
            type: 'workflow_status',
            workflow_id: workflowId,
            status: 'completed'
          };
          
          setLastWorkflowStatus(workflowStatus);
          
          // Send processing complete status
          setIsProcessing(false);
          setIsTyping(false);
          
          break;
          
        case 'progress_review':
          // Response for progress review
          responseMessage = {
            type: 'chat_message',
            content: `Looking at your recent activities, you've been making good progress. You've completed 5 activities in the past week, primarily in the mindfulness and creative domains. Would you like to focus more on a specific area?`,
            is_user: false
          };
          
          setLastChatMessage(responseMessage);
          addMessageToHistory(responseMessage, setChatMessages);
          
          // Update workflow status to completed
          setWorkflowState(prev => ({
            ...prev,
            status: 'completed',
            lastUpdated: new Date().toISOString()
          }));
          
          // Send workflow status
          const progressWorkflowStatus: WorkflowStatusData = {
            type: 'workflow_status',
            workflow_id: workflowId,
            status: 'completed'
          };
          
          setLastWorkflowStatus(progressWorkflowStatus);
          
          // Send processing complete status
          setIsProcessing(false);
          setIsTyping(false);
          
          break;
          
        default:
          // Generic response for other messages
          responseMessage = {
            type: 'chat_message',
            content: `I received your message: "${message}". How can I help you today? Would you like to try a new activity?`,
            is_user: false
          };
          
          setLastChatMessage(responseMessage);
          addMessageToHistory(responseMessage, setChatMessages);
          
          // Update workflow status to completed
          setWorkflowState(prev => ({
            ...prev,
            status: 'completed',
            lastUpdated: new Date().toISOString()
          }));
          
          // Send workflow status
          const genericWorkflowStatus: WorkflowStatusData = {
            type: 'workflow_status',
            workflow_id: workflowId,
            status: 'completed'
          };
          
          setLastWorkflowStatus(genericWorkflowStatus);
          
          // Send processing complete status
          setIsProcessing(false);
          setIsTyping(false);
      }
      
      setLastStateUpdate(Date.now());
    }, simulateDelay ? 1000 + Math.random() * 2000 : 0);
    
    return true;
  }, [simulateDelay, addMessageToHistory]);

  // Simulate sending a chat message
  const sendChatMessage = useCallback((message: string, metadata: Record<string, any> = {}) => {
    if (!connectionState.isConnected) {
      // Can't send if not connected
      setLastError({
        type: 'error',
        content: 'Cannot send message: WebSocket not connected'
      });
      
      addMessageToHistory(
        { type: 'error', content: 'Cannot send message: WebSocket not connected' },
        setSystemMessages
      );
      
      return false;
    }
    
    // Create a fake message for immediate display
    const userMessage: ChatMessageData = {
      type: 'chat_message',
      content: message,
      is_user: true
    };
    
    // Update last chat message
    setLastChatMessage(userMessage);
    
    // Add to chat history
    addMessageToHistory(userMessage, setChatMessages);
    
    // Process the message (simulate backend response)
    processChatMessage(message, metadata);
    
    return true;
  }, [connectionState.isConnected, addMessageToHistory, processChatMessage]);

  // Simulate sending a spin result
  const sendSpinResult = useCallback((activityId: string, name: string, description?: string) => {
    if (!connectionState.isConnected) {
      // Can't send if not connected
      setLastError({
        type: 'error',
        content: 'Cannot send spin result: WebSocket not connected'
      });
      
      addMessageToHistory(
        { type: 'error', content: 'Cannot send spin result: WebSocket not connected' },
        setSystemMessages
      );
      
      return false;
    }
    
    // Process with metadata to indicate this is a spin result
    processChatMessage(`I've selected the activity: ${name}`, {
      type: 'spin_result',
      activity_id: activityId,
      activity_name: name,
      description
    });
    
    return true;
  }, [connectionState.isConnected, processChatMessage, addMessageToHistory]);

  // Check workflow status
  const checkWorkflowStatus = useCallback((workflowId: string) => {
    if (!connectionState.isConnected) {
      // Can't send if not connected
      return false;
    }
    
    // If workflow matches current workflow, send its status
    if (workflowState.workflowId === workflowId) {
      const statusMessage: WorkflowStatusData = {
        type: 'workflow_status',
        workflow_id: workflowId,
        status: workflowState.status
      };
      
      setLastWorkflowStatus(statusMessage);
    } else {
      // Unknown workflow
      const statusMessage: WorkflowStatusData = {
        type: 'workflow_status',
        workflow_id: workflowId,
        status: 'unknown'
      };
      
      setLastWorkflowStatus(statusMessage);
    }
    
    return true;
  }, [connectionState.isConnected, workflowState]);

  // Reconnect to mock backend
  const reconnect = useCallback(() => {
    // Disconnect first
    setConnectionState(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false
    }));
    
    // Then connect again
    handleConnect();
  }, [handleConnect]);

  // Clear chat history
  const clearChatHistory = useCallback(() => {
    setChatMessages([]);
  }, []);

  // Create mock WebSocketManager for compatibility
  const mockWebSocketManager = useMemo(() => ({
    connect: handleConnect,
    disconnect: () => {
      setConnectionState(prev => ({
        ...prev,
        isConnected: false,
        isConnecting: false
      }));
    },
    sendChatMessage,
    sendSpinResult,
    checkWorkflowStatus,
    isConnected: () => connectionState.isConnected,
    getUrl: () => 'ws://mock-websocket-server.local/ws/game/',
    subscribe: () => {}, // No-op in mock
    unsubscribe: () => {}, // No-op in mock
    subscribeToConnection: () => {}, // No-op in mock
    unsubscribeFromConnection: () => {}, // No-op in mock
  }), [connectionState.isConnected, handleConnect, sendChatMessage, sendSpinResult, checkWorkflowStatus]);

  // Create context value
  const contextValue = useMemo<MockWebSocketContextState>(() => ({
    webSocketManager: mockWebSocketManager,
    isConnected: connectionState.isConnected,
    isConnecting: connectionState.isConnecting,
    connectionError: connectionState.error,
    isProcessing,
    isTyping,
    sendChatMessage,
    sendSpinResult,
    checkWorkflowStatus,
    reconnect,
    clearChatHistory,
    chatMessages,
    systemMessages,
    workflowState,
    lastChatMessage,
    lastWheelData,
    lastError,
    lastWorkflowStatus,
    lastStateUpdate
  }), [
    mockWebSocketManager,
    connectionState.isConnected,
    connectionState.isConnecting,
    connectionState.error,
    isProcessing,
    isTyping,
    sendChatMessage,
    sendSpinResult,
    checkWorkflowStatus,
    reconnect,
    clearChatHistory,
    chatMessages,
    systemMessages,
    workflowState,
    lastChatMessage,
    lastWheelData,
    lastError,
    lastWorkflowStatus,
    lastStateUpdate
  ]);

  console.log('[MockWebSocketProvider] Rendering with context value:', {
    isConnected: connectionState.isConnected,
    isConnecting: connectionState.isConnecting,
    connectionError: connectionState.error
  });
  
  return (
    <RealWebSocketContext.Provider value={contextValue}>
      {children}
    </RealWebSocketContext.Provider>
  );
};

// We don't need a separate hook since we're using the same context
// This ensures that useWebSocket from WebSocketContext.tsx works with our mock provider

/**
 * Export a modified version of the mock provider that has the same interface as the real provider
 * This allows for easy swapping between real and mock implementations
 */
export const WebSocketProviderMock: typeof RealWebSocketProvider = MockWebSocketProvider as any;
