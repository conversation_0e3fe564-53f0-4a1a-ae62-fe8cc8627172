/**
 * MockWebSocket.ts
 *
 * A mock implementation of the browser's WebSocket API.
 * This class can be used to replace the real WebSocket in development and testing.
 * It simulates WebSocket behavior without making actual network connections.
 */

import { 
  ChatMessageData, 
  WheelData, 
  ProcessingStatusData, 
  ErrorData, 
  SystemMessageData, 
  WorkflowStatusData 
} from '../services/WebSocketManager';

// Sample data for mock responses
export const MOCK_WHEEL_DATA: WheelData = {
  type: 'wheel_data',
  wheel: {
    name: "Today's Activities",
    items: [
      {
        id: 'act-1',
        name: 'Mindful Meditation',
        description: 'A 10-minute guided meditation focusing on breath and body awareness',
        percentage: 25,
        color: '#66BB6A',
        domain: 'mindfulness',
        base_challenge_rating: 30,
        activity_tailored_id: 'act-1'
      },
      {
        id: 'act-2',
        name: 'Nature Walk',
        description: 'A 20-minute walk outside, paying attention to the natural environment',
        percentage: 20,
        color: '#42A5F5',
        domain: 'physical',
        base_challenge_rating: 20,
        activity_tailored_id: 'act-2'
      },
      {
        id: 'act-3',
        name: 'Gratitude Journaling',
        description: 'Write down three things you are grateful for today',
        percentage: 20,
        color: '#7E57C2',
        domain: 'emotional',
        base_challenge_rating: 15,
        activity_tailored_id: 'act-3'
      },
      {
        id: 'act-4',
        name: 'Creative Drawing',
        description: 'Spend 15 minutes drawing without judgment',
        percentage: 15,
        color: '#FFA726',
        domain: 'creative',
        base_challenge_rating: 40,
        activity_tailored_id: 'act-4'
      },
      {
        id: 'act-5',
        name: 'Digital Detox',
        description: 'Disconnect from all digital devices for one hour',
        percentage: 20,
        color: '#EC407A',
        domain: 'lifestyle',
        base_challenge_rating: 60,
        activity_tailored_id: 'act-5'
      }
    ]
  }
};

// Event types that can be listened for
type WebSocketEventType = 'open' | 'message' | 'close' | 'error';

// Event listener type
type EventListener = (event: Event | MessageEvent | CloseEvent) => void;

/**
 * MockWebSocket class that implements the browser's WebSocket API
 */
export class MockWebSocket implements WebSocket {
  // WebSocket constants
  static readonly CONNECTING = 0;
  static readonly OPEN = 1;
  static readonly CLOSING = 2;
  static readonly CLOSED = 3;
  
  // WebSocket instance properties
  readonly CONNECTING = 0;
  readonly OPEN = 1;
  readonly CLOSING = 2;
  readonly CLOSED = 3;
  
  // WebSocket properties
  binaryType: BinaryType = 'blob';
  bufferedAmount: number = 0;
  extensions: string = '';
  protocol: string = '';
  readyState: number = WebSocket.CONNECTING;
  url: string = '';
  
  // Event handlers
  onclose: ((this: WebSocket, ev: CloseEvent) => any) | null = null;
  onerror: ((this: WebSocket, ev: Event) => any) | null = null;
  onmessage: ((this: WebSocket, ev: MessageEvent<any>) => any) | null = null;
  onopen: ((this: WebSocket, ev: Event) => any) | null = null;
  
  // Private properties
  private static instance: MockWebSocket | null = null;
  private listeners: Record<WebSocketEventType, EventListener[]> = {
    'open': [],
    'message': [],
    'close': [],
    'error': []
  };
  private messageQueue: string[] = [];
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private activeWorkflows: Map<string, { type: string, status: string }> = new Map();
  
  // Singleton pattern
  public static getInstance(url: string): MockWebSocket {
    if (!MockWebSocket.instance) {
      MockWebSocket.instance = new MockWebSocket(url);
    }
    return MockWebSocket.instance;
  }
  
  /**
   * Creates a new MockWebSocket instance
   * @param url The WebSocket URL (for compatibility with the real WebSocket)
   */
  constructor(url: string) {
    console.log('[MockWebSocket] Creating new instance with URL:', url);
    this.url = url;
    this.readyState = WebSocket.CONNECTING;
    
    // Simulate connection delay
    setTimeout(() => {
      console.log('[MockWebSocket] Connection established');
      this.readyState = WebSocket.OPEN;
      
      // Create and dispatch open event
      const openEvent = new Event('open');
      this.dispatchEvent(openEvent);
      
      // Call onopen handler if present
      if (this.onopen) {
        this.onopen.call(this, openEvent);
      }
      
      // Send welcome message
      this.simulateMessage({
        type: 'system_message',
        content: 'Connected to the Game of Life server (MOCK). Ready for your journey!'
      });
      
      // Send initial wheel data and set workflow state
      setTimeout(() => {
        console.log('[MockWebSocket] Sending initial wheel data');
        this.simulateMessage(MOCK_WHEEL_DATA);
        
        // Set workflow state to pre_spin to enable the wheel
        console.log('[MockWebSocket] Setting workflow state to pre_spin');
        const workflowId = `mock-workflow-${Date.now()}`;
        this.activeWorkflows.set(workflowId, { 
          type: 'pre_spin', 
          status: 'completed' 
        });
        
        this.simulateMessage({
          type: 'workflow_status',
          workflow_id: workflowId,
          status: 'completed',
          workflow_type: 'pre_spin'
        });
      }, 1000);
      
      // Process any queued messages
      this.processMessageQueue();
    }, 500);
  }
  
  /**
   * Add an event listener
   * @param type Event type
   * @param listener Event listener function
   */
  addEventListener(type: string, listener: EventListener): void {
    console.log(`[MockWebSocket] Adding event listener for ${type}`);
    if (this.listeners[type as WebSocketEventType]) {
      this.listeners[type as WebSocketEventType].push(listener);
    }
  }
  
  /**
   * Remove an event listener
   * @param type Event type
   * @param listener Event listener function
   */
  removeEventListener(type: string, listener: EventListener): void {
    console.log(`[MockWebSocket] Removing event listener for ${type}`);
    if (this.listeners[type as WebSocketEventType]) {
      this.listeners[type as WebSocketEventType] = this.listeners[type as WebSocketEventType].filter(
        l => l !== listener
      );
    }
  }
  
  /**
   * Dispatch an event to all listeners
   * @param event The event to dispatch
   */
  dispatchEvent(event: Event): boolean {
    console.log(`[MockWebSocket] Dispatching event: ${event.type}`);
    const type = event.type as WebSocketEventType;
    if (this.listeners[type]) {
      this.listeners[type].forEach(listener => {
        listener(event);
      });
    }
    
    return !event.cancelable || !event.defaultPrevented;
  }
  
  /**
   * Close the WebSocket connection
   * @param code Close code
   * @param reason Close reason
   */
  close(code?: number, reason?: string): void {
    console.log(`[MockWebSocket] Closing connection: ${code} ${reason}`);
    this.readyState = WebSocket.CLOSING;
    
    setTimeout(() => {
      this.readyState = WebSocket.CLOSED;
      
      // Create and dispatch close event
      const closeEvent = new CloseEvent('close', {
        code: code || 1000,
        reason: reason || 'Connection closed normally',
        wasClean: true
      });
      
      this.dispatchEvent(closeEvent);
      
      // Call onclose handler if present
      if (this.onclose) {
        this.onclose.call(this, closeEvent);
      }
    }, 100);
  }
  
  /**
   * Send a message through the WebSocket
   * @param data The data to send
   */
  send(data: string | ArrayBufferLike | Blob | ArrayBufferView): void {
    console.log('[MockWebSocket] Sending message:', data);
    
    if (this.readyState !== WebSocket.OPEN) {
      console.warn('[MockWebSocket] Cannot send message: WebSocket not open');
      this.messageQueue.push(data.toString());
      return;
    }
    
    this.processMessage(data.toString());
  }
  
  /**
   * Process a message from the client
   * @param messageData The message data
   */
  private processMessage(messageData: string): void {
    console.log('[MockWebSocket] Processing message:', messageData);
    
    try {
      const message = JSON.parse(messageData);
      
      switch (message.type) {
        case 'chat_message':
          this.handleChatMessage(message);
          break;
        case 'spin_result':
          this.handleSpinResult(message);
          break;
        case 'workflow_status_request':
          this.handleWorkflowStatusRequest(message);
          break;
        default:
          console.warn('[MockWebSocket] Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('[MockWebSocket] Error processing message:', error);
    }
  }
  
  /**
   * Process any queued messages
   */
  private processMessageQueue(): void {
    console.log(`[MockWebSocket] Processing ${this.messageQueue.length} queued messages`);
    
    this.messageQueue.forEach(message => {
      this.processMessage(message);
    });
    
    this.messageQueue = [];
  }
  
  /**
   * Handle a chat message from the client
   * @param message The chat message
   */
  private handleChatMessage(message: any): void {
    console.log('[MockWebSocket] Handling chat message:', message);
    
    // Echo back the message as an acknowledgment
    this.simulateMessage({
      type: 'chat_message',
      content: message.content.message,
      is_user: true
    });
    
    // Set processing status
    this.simulateMessage({
      type: 'processing_status',
      status: 'processing'
    });
    
    // Create a workflow ID for this interaction
    const workflowId = `mock-workflow-${Date.now()}`;
    
    // Determine workflow type based on message content
    let workflowType = 'pre_spin';
    const messageText = message.content.message.toLowerCase();
    
    if (messageText.includes('wheel') || messageText.includes('activity') || messageText.includes('suggestion')) {
      workflowType = 'wheel_generation';
    } else if (messageText.includes('progress')) {
      workflowType = 'progress_review';
    }
    
    // Store the workflow
    this.activeWorkflows.set(workflowId, { 
      type: workflowType, 
      status: 'initiated' 
    });
    
    // Send workflow status
    this.simulateMessage({
      type: 'workflow_status',
      workflow_id: workflowId,
      status: 'initiated',
      workflow_type: workflowType
    });
    
    // Simulate agent thinking time
    setTimeout(() => {
      // Generate response based on message content
      let response: string;
      
      if (workflowType === 'wheel_generation') {
        response = "I've prepared some activities for you. Let's spin the wheel to see what comes up!";
        
        // Also send wheel data after a short delay
        setTimeout(() => {
          this.simulateMessage(MOCK_WHEEL_DATA);
          
          // Update workflow status
          this.activeWorkflows.set(workflowId, { 
            type: workflowType, 
            status: 'completed' 
          });
          
          // Send workflow status
          this.simulateMessage({
            type: 'workflow_status',
            workflow_id: workflowId,
            status: 'completed',
            workflow_type: 'wheel_generation'
          });
        }, 500);
      } else if (workflowType === 'progress_review') {
        response = "Looking at your recent activities, you've been making good progress. You've completed 5 activities in the past week, primarily in the mindfulness and creative domains. Would you like to focus more on a specific area?";
        
        // Update workflow status
        this.activeWorkflows.set(workflowId, { 
          type: workflowType, 
          status: 'completed' 
        });
        
        // Send workflow status
        this.simulateMessage({
          type: 'workflow_status',
          workflow_id: workflowId,
          status: 'completed',
          workflow_type: 'pre_spin' // Add workflow type to the message
        });
      } else {
        response = `I received your message: "${message.content.message}". How can I help you further with your journey?`;
        
        // Update workflow status
        this.activeWorkflows.set(workflowId, { 
          type: 'pre_spin', 
          status: 'completed' 
        });
        
        // Send workflow status
        this.simulateMessage({
          type: 'workflow_status',
          workflow_id: workflowId,
          status: 'completed',
          workflow_type: 'post_spin'
        });
      }
      
      // Send mock response
      this.simulateMessage({
        type: 'chat_message',
        content: response,
        is_user: false
      });
      
      // Set processing complete
      this.simulateMessage({
        type: 'processing_status',
        status: 'completed'
      });
    }, 1500);
  }
  
  /**
   * Handle a spin result from the client
   * @param message The spin result message
   */
  private handleSpinResult(message: any): void {
    console.log('[MockWebSocket] Handling spin result:', message);
    
    // Generate a unique workflow ID for this spin result
    const workflowId = `mock-workflow-spin-${Date.now()}`;
    
    // First immediately change the workflow state to processing to prevent multiple spins
    this.simulateMessage({
      type: 'workflow_status',
      workflow_id: workflowId,
      status: 'processing',
      workflow_type: 'post_spin'
    });
    
    console.log(`[MockWebSocket] Workflow state set to post_spin/processing (ID: ${workflowId})`);
    
    // Store the workflow
    this.activeWorkflows.set(workflowId, { 
      type: 'post_spin', 
      status: 'processing' 
    });
    
    // Set processing status
    this.simulateMessage({
      type: 'processing_status',
      status: 'processing'
    });
    
    // Send system message acknowledgment
    this.simulateMessage({
      type: 'system_message',
      content: `Spin result received: ${message.content.name}`
    });
    
      // Immediately send a chat message about the result (no delay)
      setTimeout(() => {
        console.log(`[MockWebSocket] Sending chat message about selected activity: ${message.content.name}`);
        this.simulateMessage({
          type: 'chat_message',
          content: `I see you've selected "${message.content.name}". That's a great choice!`,
          is_user: false
        });
      }, 500);
    
    // Short delay for the second message with activity details
    setTimeout(() => {
      // Only send if there's a description
      if (message.content.description) {
        this.simulateMessage({
          type: 'chat_message',
          content: `Here's what you'll do: ${message.content.description}. Would you like me to guide you through this activity?`,
          is_user: false
        });
      }
      
      // Complete the workflow
      this.activeWorkflows.set(workflowId, { 
        type: 'post_spin', 
        status: 'completed' 
      });
      
      // Send final workflow status
      this.simulateMessage({
        type: 'workflow_status',
        workflow_id: workflowId,
        status: 'completed',
        workflow_type: 'post_spin'
      });
      
      console.log(`[MockWebSocket] Workflow state set to post_spin/completed (ID: ${workflowId})`);
      
      // Set processing complete
      this.simulateMessage({
        type: 'processing_status',
        status: 'completed'
      });
    }, 1500);
  }
  
  /**
   * Handle a workflow status request from the client
   * @param message The workflow status request message
   */
  private handleWorkflowStatusRequest(message: any): void {
    console.log('[MockWebSocket] Handling workflow status request:', message);
    
    const workflowId = message.content.workflow_id;
    
    if (this.activeWorkflows.has(workflowId)) {
      const workflow = this.activeWorkflows.get(workflowId)!;
      
      this.simulateMessage({
        type: 'workflow_status',
        workflow_id: workflowId,
        status: workflow.status
      });
    } else {
      this.simulateMessage({
        type: 'workflow_status',
        workflow_id: workflowId,
        status: 'unknown'
      });
    }
  }
  
  /**
   * Simulate receiving a message from the server
   * @param data The message data
   */
  private simulateMessage(data: any): void {
    console.log('[MockWebSocket] Simulating message:', data);
    
    if (this.readyState !== WebSocket.OPEN) {
      console.warn('[MockWebSocket] Cannot simulate message: WebSocket not open');
      return;
    }
    
    const event = new MessageEvent('message', {
      data: JSON.stringify(data)
    });
    
    // Dispatch event to listeners
    this.dispatchEvent(event);
    
    // Call onmessage handler if present
    if (this.onmessage) {
      this.onmessage.call(this, event);
    }
  }
  
  /**
   * Simulate a connection error
   * @param message Error message
   */
  public simulateError(message: string = 'Connection error occurred'): void {
    console.log('[MockWebSocket] Simulating error:', message);
    
    if (this.readyState !== WebSocket.OPEN) {
      console.warn('[MockWebSocket] Cannot simulate error: WebSocket not open');
      return;
    }
    
    // Send error message
    this.simulateMessage({
      type: 'error',
      content: message
    });
    
    // Create and dispatch error event
    const errorEvent = new ErrorEvent('error', {
      message
    });
    
    this.dispatchEvent(errorEvent);
    
    // Call onerror handler if present
    if (this.onerror) {
      this.onerror.call(this, errorEvent);
    }
  }
  
  /**
   * Simulate a disconnection with automatic reconnect attempts
   * @param reconnect Whether to attempt reconnection
   */
  public simulateDisconnect(reconnect: boolean = true): void {
    console.log('[MockWebSocket] Simulating disconnect, reconnect:', reconnect);
    
    if (this.readyState !== WebSocket.OPEN) {
      console.warn('[MockWebSocket] Cannot simulate disconnect: WebSocket not open');
      return;
    }
    
    this.close(1001, 'Connection lost');
    
    if (reconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      
      setTimeout(() => {
        console.log(`[MockWebSocket] Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.readyState = WebSocket.CONNECTING;
        
        setTimeout(() => {
          this.readyState = WebSocket.OPEN;
          
          // Create and dispatch open event
          const openEvent = new Event('open');
          this.dispatchEvent(openEvent);
          
          // Call onopen handler if present
          if (this.onopen) {
            this.onopen.call(this, openEvent);
          }
          
          // Send reconnection message
          this.simulateMessage({
            type: 'system_message',
            content: `Reconnected to the Game of Life server (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}).`
          });
          
          // Process any queued messages
          this.processMessageQueue();
        }, 1000);
      }, 2000 * this.reconnectAttempts); // Exponential backoff
    }
  }
}

/**
 * Factory function to create either a real WebSocket or a mock based on configuration
 * @param url The WebSocket URL
 * @param useMock Whether to use the mock WebSocket
 * @returns A WebSocket instance
 */
export function createWebSocket(url: string, useMock: boolean = false): WebSocket {
  if (useMock) {
    console.log('[createWebSocket] Using mock WebSocket');
    return MockWebSocket.getInstance(url);
  } else {
    console.log('[createWebSocket] Using real WebSocket');
    return new WebSocket(url);
  }
}
