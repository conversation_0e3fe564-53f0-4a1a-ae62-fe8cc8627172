/**
 * ChatMessage.tsx
 * 
 * Component for rendering an individual chat message.
 * Handles different message types (user, assistant, system).
 */

import { type FC } from 'react';
import { MessageType } from '../../hooks/useChatMessages';

interface ChatMessageProps {
  id: string;
  content: string;
  type: MessageType;
  timestamp: string;
}

/**
 * Chat message component
 */
const ChatMessage: FC<ChatMessageProps> = ({
  id,
  content,
  type,
  timestamp
}) => {
  // Format timestamp for display
  const formatTime = (isoString: string): string => {
    try {
      const date = new Date(isoString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      return '';
    }
  };
  
  // Determine message class based on type
  const getMessageClass = (): string => {
    switch (type) {
      case MessageType.User:
        return 'user';
      case MessageType.Assistant:
        return 'assistant';
      case MessageType.System:
        return 'system';
      default:
        return '';
    }
  };
  
  return (
    <div 
      id={`message-${id}`} 
      className={`message ${getMessageClass()}`}
      data-testid={`message-${type}`}
    >
      {content}
      <span className="message-time">{formatTime(timestamp)}</span>
    </div>
  );
};

export default ChatMessage;