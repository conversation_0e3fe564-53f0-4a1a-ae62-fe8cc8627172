/**
 * ChatContainer.tsx
 * 
 * Container component for the chat functionality.
 * Implements the chat interface using the useChatMessages hook.
 */

import { type FC, useRef, useState, useEffect, type ChangeEvent, type KeyboardEvent } from 'react';
import { useChatMessages, MessageType } from '../../hooks/useChatMessages';
import ChatMessage from './ChatMessage';
import TypingIndicator from './TypingIndicator';

/**
 * Chat container component
 */
const ChatContainer: FC = () => {
  const { 
    messages, 
    sendMessage, 
    isTyping,
    isConnected
  } = useChatMessages();
  
  const [inputValue, setInputValue] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Handle input changes
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };
  
  // Handle sending a message
  const handleSendMessage = () => {
    if (inputValue.trim() && isConnected) {
      // Send message via hook
      sendMessage(inputValue.trim());
      // Clear input field
      setInputValue('');
    }
  };
  
  // Handle Enter key press
  const handleKeyPress = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };
  
  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages.length]);
  
  return (
    <div className="chat-container">
      <div className="chat-messages">
        {messages.map(message => (
          <ChatMessage 
            key={message.id}
            id={message.id}
            content={message.content}
            type={message.type}
            timestamp={message.timestamp}
          />
        ))}
        
        {/* Show typing indicator when assistant is typing */}
        {isTyping && <TypingIndicator />}
        
        {/* Invisible element for scrolling to bottom */}
        <div ref={messagesEndRef} />
      </div>
      
      <div className="chat-input">
        <input
          type="text"
          placeholder="Type your message..."
          value={inputValue}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          disabled={!isConnected}
        />
        <button 
          onClick={handleSendMessage}
          disabled={!isConnected || !inputValue.trim()}
        >
          Send
        </button>
      </div>
    </div>
  );
};

export default ChatContainer;