import React, { useState, useEffect, useRef, type FC } from 'react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import './ChatInterface.css'; // We'll create this for basic styling

/**
 * ChatInterface Component
 *
 * Provides a simple interface for sending chat messages via WebSocket
 * and displaying the conversation history.
 */
const ChatInterface: FC = () => {
  const { sendChatMessage, chatMessages, isConnected, isConnecting } = useWebSocket();
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };

  const handleSendMessage = () => {
    if (inputValue.trim() && isConnected) {
      console.log('[ChatInterface] Sending message:', inputValue);
      sendChatMessage(inputValue.trim());
      setInputValue(''); // Clear input after sending
    } else if (!isConnected) {
      console.warn('[ChatInterface] Cannot send message: WebSocket not connected.');
      // Optionally, provide user feedback here
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <div className="chat-interface">
      <div className="chat-messages">
        {chatMessages.map((msg) => (
          <div
            key={msg.id}
            className={`message ${msg.data.is_user ? 'user-message' : 'system-message'}`}
          >
            <span className="message-content">{msg.data.content}</span>
            <span className="message-timestamp">{new Date(msg.timestamp).toLocaleTimeString()}</span>
          </div>
        ))}
        <div ref={messagesEndRef} /> {/* Anchor for scrolling */}
      </div>
      <div className="chat-input-area">
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder={isConnected ? "Type your message..." : (isConnecting ? "Connecting..." : "Disconnected - Cannot send")}
          disabled={!isConnected || isConnecting}
        />
        <button onClick={handleSendMessage} disabled={!isConnected || isConnecting || !inputValue.trim()}>
          Send
        </button>
      </div>
      <div className="connection-status">
        Status: {isConnected ? 'Connected' : (isConnecting ? 'Connecting...' : 'Disconnected')}
      </div>
    </div>
  );
};

export default ChatInterface;
