/**
 * TypingIndicator.tsx
 * 
 * Component for showing a typing indicator when the assistant is "typing".
 * This provides visual feedback that a response is being prepared.
 */

import { type FC } from 'react';

/**
 * Typing indicator component
 */
const TypingIndicator: FC = () => {
  return (
    <div className="typing-indicator" data-testid="typing-indicator">
      <span></span>
      <span></span>
      <span></span>
    </div>
  );
};

export default TypingIndicator;