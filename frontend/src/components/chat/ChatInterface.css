.chat-interface {
  display: flex;
  flex-direction: column;
  height: 300px; /* Example height, adjust as needed */
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
  font-family: sans-serif;
  margin: 10px; /* Add some margin */
}

.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 10px;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  gap: 8px; /* Space between messages */
}

.message {
  padding: 8px 12px;
  border-radius: 15px;
  max-width: 70%;
  word-wrap: break-word;
}

.user-message {
  background-color: #dcf8c6;
  align-self: flex-end;
  text-align: right;
}

.system-message {
  background-color: #e5e5ea;
  align-self: flex-start;
  text-align: left;
}

.message-content {
  display: block; /* Ensure content takes full width */
}

.message-timestamp {
  font-size: 0.75em;
  color: #888;
  display: block; /* Put timestamp on new line */
  margin-top: 4px;
}

.chat-input-area {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ccc;
  background-color: #fff;
}

.chat-input-area input {
  flex-grow: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-right: 5px;
}

.chat-input-area button {
  padding: 8px 15px;
  border: none;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.chat-input-area button:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

.connection-status {
  padding: 5px 10px;
  font-size: 0.8em;
  color: #555;
  background-color: #eee;
  text-align: center;
}
