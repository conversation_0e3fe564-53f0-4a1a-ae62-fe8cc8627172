import React, { useState } from 'react';
import { useDebug, DebugMessageEntry } from '../../contexts/DebugContext';
import './DebugConsole.css'; // Import CSS for styling

const DebugConsole: React.FC = () => {
  const { debugMessages, clearDebugMessages } = useDebug();
  const [isOpen, setIsOpen] = useState(false); // State to manage visibility

  const toggleOpen = () => setIsOpen(!isOpen);

  const formatTimestamp = (isoString: string): string => {
    try {
      return new Date(isoString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', fractionalSecondDigits: 3 });
    } catch (e) {
      return isoString; // Fallback if parsing fails
    }
  };

  const renderDetails = (details: Record<string, any> | undefined): React.ReactNode => {
    if (!details || Object.keys(details).length === 0) {
      return null;
    }

    const { traceback, ...otherDetails } = details; // Destructure traceback and rest

    try {
      const tracebackElement = traceback && typeof traceback === 'string' ? (
        <pre className="debug-traceback">{traceback}</pre>
      ) : null;

      const otherDetailsElement = Object.keys(otherDetails).length > 0 ? (
        <pre className="debug-details">{JSON.stringify(otherDetails, null, 2)}</pre>
      ) : null;

      // Return null if neither part has content, otherwise wrap in a fragment
      if (!tracebackElement && !otherDetailsElement) {
        return null;
      }

      return (
        <>
          {tracebackElement}
          {otherDetailsElement}
        </>
      );
    } catch (e) {
      // Fallback for any unexpected error during rendering
      return <span className="debug-details-error">[Error rendering details]</span>;
    }
  };

  return (
    <div className={`debug-console ${isOpen ? 'open' : 'closed'}`}>
      <button onClick={toggleOpen} className="debug-toggle-button">
        {isOpen ? 'Hide Debug' : 'Show Debug'} ({debugMessages.length})
      </button>
      {isOpen && (
        <div className="debug-content">
          <button onClick={clearDebugMessages} className="debug-clear-button">
            Clear Messages
          </button>
          <ul className="debug-message-list">
            {debugMessages.map((msg: DebugMessageEntry) => (
              <li key={msg.id} className={`debug-message level-${msg.level}`}>
                <span className="debug-timestamp">{formatTimestamp(msg.timestamp)}</span>
                <span className="debug-source">[{msg.source}]</span>
                <span className="debug-level">({msg.level.toUpperCase()})</span>
                <span className="debug-text">{msg.message}</span>
                {renderDetails(msg.details)}
              </li>
            ))}
            {debugMessages.length === 0 && (
              <li className="debug-no-messages">No debug messages yet.</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default DebugConsole;
