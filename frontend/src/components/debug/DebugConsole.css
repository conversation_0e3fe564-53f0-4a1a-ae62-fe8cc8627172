/* frontend/src/components/debug/DebugConsole.css */

.debug-console {
  position: fixed;
  bottom: 10px;
  right: 10px;
  max-width: 500px;
  max-height: 400px; /* Limit height */
  background-color: rgba(40, 40, 40, 0.9);
  border: 1px solid #555;
  border-radius: 5px;
  z-index: 1000;
  /* Removed overflow: hidden */
  transition: max-height 0.3s ease-in-out;
  display: flex; /* Use flex for the main container */
  flex-direction: column; /* Stack button and content */
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.8rem;
  color: #ccc;
}

.debug-console.closed {
  max-height: 40px; /* Height of the toggle button */
}

.debug-console.open {
  max-height: 400px; /* Expanded height */
}

.debug-toggle-button {
  background-color: #444;
  color: #eee;
  border: none;
  padding: 5px 10px;
  cursor: pointer;
  width: 100%;
  text-align: left;
  border-bottom: 1px solid #555;
}

.debug-toggle-button:hover {
  background-color: #555;
}

.debug-content {
  padding: 10px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box; /* Include padding in height calculation */
  overflow-y: auto; /* Make this the scrollable container */
  flex-grow: 1; /* Allow content to take available space */
  min-height: 0; /* Important for flex scrolling */
}

.debug-clear-button {
  background-color: #600;
  color: white;
  border: none;
  padding: 3px 8px;
  font-size: 0.7rem;
  cursor: pointer;
  align-self: flex-end;
  margin-bottom: 5px;
  border-radius: 3px;
}

.debug-clear-button:hover {
  background-color: #800;
}

.debug-message-list {
  list-style: none;
  padding: 0;
  margin: 0;
  /* Removed overflow-y: auto and max-height */
  /* Let the parent (.debug-content) handle scrolling */
}

.debug-message {
  margin-bottom: 5px;
  padding: 3px 5px;
  border-radius: 3px;
  white-space: pre-wrap; /* Allow wrapping */
  word-break: break-all; /* Break long words/strings */
  font-style: italic; /* Make text italic as requested */
}

.debug-timestamp {
  color: #888;
  margin-right: 5px;
}

.debug-source {
  color: #aaa;
  margin-right: 5px;
}

.debug-level {
  font-weight: bold;
  margin-right: 5px;
}

.debug-text {
  color: #ddd;
}

/* Level-specific styling */
.debug-message.level-info {
  background-color: rgba(50, 50, 80, 0.5);
}
.debug-message.level-debug {
  background-color: rgba(50, 80, 50, 0.5);
  color: #afc; /* Lighter green for debug */
}
.debug-message.level-warning {
  background-color: rgba(100, 80, 30, 0.6);
  color: #f9d71c; /* Yellowish for warning */
}
.debug-message.level-error {
  background-color: rgba(100, 40, 40, 0.7);
  color: #ff8080; /* Reddish for error */
  font-weight: bold;
}

.debug-details {
  margin-top: 3px;
  margin-left: 15px;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
  font-size: 0.75rem;
  color: #bbb;
  white-space: pre-wrap;
  word-break: break-all;
}

.debug-details-error {
  color: #f55;
  font-style: normal;
}

.debug-no-messages {
  color: #888;
  padding: 10px;
  text-align: center;
}
