/**
 * WheelSegment.tsx
 * 
 * Component representing a segment of the activity wheel.
 * This is a visual representation only - the actual segments
 * are created using CSS conic-gradient in the parent component.
 */

import React from 'react';
import { WheelItem } from '../../services/WebSocketManager';

interface WheelSegmentProps {
  item: WheelItem;
  isSelected: boolean;
}

/**
 * Wheel segment component
 */
const WheelSegment: React.FC<WheelSegmentProps> = ({
  item,
  isSelected
}) => {
  // This component doesn't render visible content directly
  // as the wheel segments are created with CSS gradient
  // However, it can be used for accessibility or for adding
  // interactive features to wheel segments in the future
  
  return (
    <div 
      className={`wheel-segment ${isSelected ? 'selected' : ''}`}
      data-id={item.id}
      data-testid={`wheel-segment-${item.id}`}
      style={{ display: 'none' }}
      aria-label={`Activity: ${item.name}, ${item.percentage}% chance`}
      title={item.description || item.name}
    />
  );
};

export default WheelSegment;