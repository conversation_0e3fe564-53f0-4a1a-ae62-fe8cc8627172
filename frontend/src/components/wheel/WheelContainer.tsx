/**
 * WheelContainer.tsx
 *
 * Container component for the activity wheel.
 * Manages wheel rendering, spinning, and result handling.
 */

import React, { useEffect, useRef } from 'react';
import { useWheel, WheelAnimationState } from '../../hooks/useWheel';
import WheelSegment from './WheelSegment';

/**
 * Wheel container component
 */
const WheelContainer: React.FC = () => {
  const wheelRef = useRef<HTMLDivElement>(null);
  const [state, actions] = useWheel();

  // Set wheel reference for DOM manipulation in the hook
  useEffect(() => {
    actions.setWheelRef(wheelRef.current);
  }, [actions, wheelRef.current]);

  // Create wheel gradient based on wheel items
  const getWheelGradient = (): string => {
    if (!state.wheel || state.wheel.items.length === 0) {
      // Default gradient if no items
      return 'conic-gradient(from 0deg, #f0f0f0, #e0e0e0)';
    }

    let cumulativePercentage = 0;
    let gradientString = 'conic-gradient(';

    // Safe access to wheel items with null check
    state.wheel.items.forEach((item, index) => {
      const startPercentage = cumulativePercentage;
      cumulativePercentage += item.percentage;

      gradientString += `${item.color} ${startPercentage}% ${cumulativePercentage}%`;

      // Add comma except for the last item
      if (index < (state.wheel?.items.length || 0) - 1) {
        gradientString += ', ';
      }
    });

    gradientString += ')';
    return gradientString;
  };

  return (
    <div className="wheel-container">
      <div 
        className={`pointer ${state.animationState === WheelAnimationState.Spinning ? 'active' : ''}`}
      ></div>
      <div
        ref={wheelRef}
        className={`wheel ${state.animationState === WheelAnimationState.Spinning ? 'spinning' : ''}`}
        style={{
          background: getWheelGradient(),
          transform: `rotate(${state.currentRotation}deg)`,
          // TypeScript-friendly way to set CSS variables
          ...{ '--rotation': `${state.currentRotation}deg` } as React.CSSProperties
        }}
      >
        {state.wheel?.items.map(item => (
          <WheelSegment
            key={item.id}
            item={item}
            isSelected={state.selectedItem?.id === item.id}
          />
        ))}
      </div>

      <button
        className="spin-button"
        onClick={actions.spin}
        disabled={
          !state.isSpinEnabled ||
          state.animationState === WheelAnimationState.Spinning ||
          !state.wheel
        }
      >
        {state.animationState === WheelAnimationState.Spinning ? 'Spinning...' : 'SPIN'}
      </button>

      {state.error && <div className="error-message">{state.error}</div>}
    </div>
  );
};

export default WheelContainer;
