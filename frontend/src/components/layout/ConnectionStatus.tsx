/**
 * ConnectionStatus.tsx
 * 
 * Component for displaying the current WebSocket connection status.
 */

import React from 'react';

interface ConnectionStatusProps {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
}

/**
 * Connection status indicator component
 */
const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  isConnecting,
  error
}) => {
  // Determine status class and message
  let statusClass = '';
  let statusMessage = '';
  
  if (isConnected) {
    statusClass = 'connected';
    statusMessage = 'Connected';
  } else if (isConnecting) {
    statusClass = 'connecting';
    statusMessage = 'Connecting...';
  } else {
    statusClass = 'disconnected';
    statusMessage = 'Disconnected';
  }
  
  return (
    <div className={`connection-status ${statusClass}`} title={error || undefined}>
      {statusMessage}
      {error && <span className="error-indicator">!</span>}
    </div>
  );
};

export default ConnectionStatus;