/**
 * GameContainer.tsx
 * 
 * Main layout component for the Game of Life application.
 * It structures the layout with the activities panel, wheel, and chat sections.
 */

import React from 'react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import UserHeader from './UserHeader';
import ActivitiesPanel from '../activities/ActivitiesPanel';
import WheelContainer from '../wheel/WheelContainer';
import ChatContainer from '../chat/ChatContainer';
import ConnectionStatus from './ConnectionStatus';

/**
 * Main game container component
 */
const GameContainer: React.FC = () => {
  console.log('[GameContainer] Rendering GameContainer component');
  
  try {
    const { isConnected, isConnecting, connectionError } = useWebSocket();
    console.log('[GameContainer] WebSocket context values:', { isConnected, isConnecting, connectionError });
    
    return (
      <div className="game-container">
        <UserHeader />
        
        <div className="main-content">
          <ActivitiesPanel />
          <WheelContainer />
          <ChatContainer />
        </div>
        
        <ConnectionStatus 
          isConnected={isConnected} 
          isConnecting={isConnecting}
          error={connectionError}
        />
      </div>
    );
  } catch (error: any) {
    console.error('[GameContainer] Error rendering GameContainer:', error);
    return <div className="error-container">Error loading game: {error.message || String(error)}</div>;
  }
};

export default GameContainer;
