/**
 * UserHeader.tsx
 * 
 * Component for displaying the user profile information in the header.
 */

import React from 'react';

// Default user name for demo purposes
const DEFAULT_USERNAME = 'Demo User';

interface UserHeaderProps {
  username?: string;
}

/**
 * Header component displaying user information
 */
const UserHeader: React.FC<UserHeaderProps> = ({ 
  username = DEFAULT_USERNAME
}) => {
  return (
    <header className="user-header">
      <h2>User: {username}</h2>
    </header>
  );
};

export default UserHeader;