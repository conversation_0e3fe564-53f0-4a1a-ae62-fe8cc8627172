/**
 * ActivityItem.tsx
 * 
 * Component for rendering an individual activity item in the activities list.
 */

import React from 'react';
import { WheelItem } from '../../services/WebSocketManager';

interface ActivityItemProps {
  item: WheelItem;
  isSelected: boolean;
}

/**
 * Activity item component
 */
const ActivityItem: React.FC<ActivityItemProps> = ({
  item,
  isSelected
}) => {
  return (
    <li 
      className={isSelected ? 'selected' : ''}
      data-id={item.id}
      title={item.description || ''}
    >
      <span 
        className="activity-color" 
        style={{ backgroundColor: item.color }}
      />
      <span className="activity-name">
        {item.name} ({item.percentage.toFixed(1)}%)
      </span>
    </li>
  );
};

export default ActivityItem;