/**
 * ActivitiesPanel.tsx
 * 
 * Component for displaying the list of activities in the wheel.
 * Shows activity names, colors, and highlights the selected activity.
 */

import React from 'react';
import { useWheel } from '../../hooks/useWheel';
import ActivityItem from './ActivityItem';

/**
 * Activities panel component
 */
const ActivitiesPanel: React.FC = () => {
  const [state] = useWheel();
  
  return (
    <div className="activities-panel">
      <h3>Activities</h3>
      
      {state.isLoading ? (
        <div className="loading-indicator">Loading activities...</div>
      ) : state.wheel && state.wheel.items.length > 0 ? (
        <ul className="activities-list">
          {state.wheel.items.map(item => (
            <ActivityItem
              key={item.id}
              item={item}
              isSelected={state.selectedItem?.id === item.id}
            />
          ))}
        </ul>
      ) : (
        <div className="empty-message">No activities available</div>
      )}
    </div>
  );
};

export default ActivitiesPanel;