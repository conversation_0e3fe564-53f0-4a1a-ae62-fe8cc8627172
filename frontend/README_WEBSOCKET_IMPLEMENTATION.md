# WebSocket Implementation in Game of Life Frontend

This document provides a comprehensive overview of the WebSocket implementation in the Game of Life frontend application, including the hybrid mocking approach.

## Overview

The Game of Life frontend uses WebSockets to communicate with the backend in real-time. The implementation includes:

1. A hybrid approach to WebSocket mocking that allows for development without a backend
2. Comprehensive logging for debugging
3. Support for various message types (chat, wheel data, workflow status, etc.)
4. Automatic reconnection handling

## Architecture

### Components

The WebSocket architecture consists of the following components:

1. **WebSocketContext** (`src/contexts/WebSocketContext.tsx`)
   - Provides a React context for WebSocket state and operations
   - Used by components to access WebSocket functionality

2. **WebSocketManager** (`src/services/WebSocketManager.ts`)
   - Manages the WebSocket connection and message handling
   - Handles reconnection, message queuing, and event routing
   - Uses either a real or mock WebSocket based on configuration

3. **MockWebSocket** (`src/utils/MockWebSocket.ts`)
   - Implements the browser's WebSocket API
   - Simulates WebSocket behavior without making actual network connections
   - Provides predefined responses for testing

4. **WebSocketProviderSelector** (`src/utils/WebSocketProviderSelector.tsx`)
   - Configures the WebSocketProvider based on environment settings
   - Always uses the real WebSocketProvider with the hybrid approach

### Data Flow

1. **Component Initialization**
   - `App` component wraps its children with `WebSocketProviderSelector`
   - `WebSocketProviderSelector` configures and renders `WebSocketProvider`
   - `WebSocketProvider` creates a `WebSocketManager` instance

2. **Connection Establishment**
   - `WebSocketManager.connect()` is called
   - Based on `ENV.USE_MOCK_WS`, either a real or mock WebSocket is created
   - Connection events are routed to subscribers

3. **Message Handling**
   - Outgoing messages are serialized and sent through the WebSocket
   - Incoming messages are parsed and routed to appropriate handlers
   - With mock WebSocket, predefined responses are generated

4. **Component Integration**
   - Components use the `useWebSocket` hook to access WebSocket functionality
   - The hook provides access to connection state, messages, and methods
   - Components are unaware of whether a real or mock WebSocket is being used

## Hybrid Mocking Approach

The hybrid mocking approach allows for development and testing without a backend connection while still using the real WebSocket implementation code paths.

### How It Works

1. The `WebSocketProviderSelector` always uses the real `WebSocketProvider`
2. The `WebSocketManager` uses either a real WebSocket or a `MockWebSocket` based on the `ENV.USE_MOCK_WS` setting
3. The `MockWebSocket` implements the browser's WebSocket API but simulates responses

### Benefits

1. **Code Reuse**: Uses the same WebSocketProvider and WebSocketManager for both real and mock modes
2. **Realistic Testing**: Tests the actual WebSocketProvider implementation
3. **Seamless Switching**: Easy to switch between real and mock modes with a single configuration change
4. **Improved Debugging**: Detailed logging throughout the system

## Message Types

The WebSocket implementation supports the following message types:

### Outgoing Messages (Client → Server)

1. **chat_message**: User sends a text message
   ```typescript
   {
     type: 'chat_message',
     content: {
       message: string,
       user_profile_id: string,
       timestamp?: string,
       metadata?: Record<string, any>
     }
   }
   ```

2. **spin_result**: User selects an activity from the wheel
   ```typescript
   {
     type: 'spin_result',
     content: {
       activity_id: string,
       name: string,
       description?: string,
       user_profile_id: string
     }
   }
   ```

3. **workflow_status_request**: Request current status of a workflow
   ```typescript
   {
     type: 'workflow_status_request',
     content: {
       workflow_id: string
     }
   }
   ```

### Incoming Messages (Server → Client)

1. **system_message**: System notifications
   ```typescript
   {
     type: 'system_message',
     content: string
   }
   ```

2. **chat_message**: Messages from agent or system
   ```typescript
   {
     type: 'chat_message',
     content: string,
     is_user: boolean
   }
   ```

3. **processing_status**: Indicates processing state
   ```typescript
   {
     type: 'processing_status',
     status: 'processing' | 'completed' | 'error'
   }
   ```

4. **wheel_data**: Activity wheel configuration
   ```typescript
   {
     type: 'wheel_data',
     wheel: {
       name: string,
       items: [
         {
           id: string,
           name: string,
           description: string,
           percentage: number,
           color: string,
           domain: string,
           base_challenge_rating: number,
           activity_tailored_id: string
         }
       ]
     }
   }
   ```

5. **error**: Error notifications
   ```typescript
   {
     type: 'error',
     content: string
   }
   ```

6. **workflow_status**: Workflow execution status
   ```typescript
   {
     type: 'workflow_status',
     workflow_id: string,
     status: 'initiated' | 'processing' | 'completed' | 'failed' | 'unknown'
   }
   ```

## Configuration

The WebSocket behavior is controlled through environment settings in `src/config/environment.ts`:

```typescript
export const ENV = {
  // Development environment settings
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // WebSocket settings
  // When true, the application will use a mock WebSocket implementation
  // that simulates backend responses without requiring a real backend connection.
  // With the hybrid approach, we still use the real WebSocketProvider and WebSocketManager,
  // but replace the actual WebSocket with a mock implementation.
  USE_MOCK_WS: true,
  
  // Logging settings
  VERBOSE_LOGGING: true, // Enable detailed logging for debugging
};
```

## Logging

The implementation includes comprehensive logging to help with debugging:

1. **Connection Events**
   - Connection attempts
   - Successful connections
   - Connection failures
   - Reconnection attempts

2. **Message Flow**
   - Outgoing messages with full payload
   - Incoming messages with full payload
   - Message processing steps
   - State updates resulting from messages

3. **Component Rendering**
   - Component mounting/unmounting
   - Props and state changes
   - Effect triggers
   - Context value updates

## Usage Examples

### Using the WebSocket in Components

```tsx
import { useWebSocket } from '../contexts/WebSocketContext';

const MyComponent = () => {
  const { 
    isConnected, 
    sendChatMessage, 
    chatMessages 
  } = useWebSocket();
  
  const handleSend = () => {
    sendChatMessage('Hello, world!');
  };
  
  return (
    <div>
      <button onClick={handleSend} disabled={!isConnected}>
        Send Message
      </button>
      <div>
        {chatMessages.map(msg => (
          <div key={msg.id}>{msg.data.content}</div>
        ))}
      </div>
    </div>
  );
};
```

### Testing with Mock WebSocket

1. Set `USE_MOCK_WS: true` in `environment.ts`
2. Run the application normally
3. The application will use the mock WebSocket implementation
4. Predefined responses will be generated for testing

### Using with Real Backend

1. Set `USE_MOCK_WS: false` in `environment.ts`
2. Ensure the backend is running and accessible
3. Run the application normally
4. The application will connect to the real backend

## Implementation Details

### MockWebSocket Class

The `MockWebSocket` class implements the browser's WebSocket API, including:

- Standard WebSocket properties (`readyState`, `binaryType`, etc.)
- Event handlers (`onopen`, `onmessage`, `onclose`, `onerror`)
- Methods (`send`, `close`, `addEventListener`, `removeEventListener`)

It also includes:

- Simulated connection delays and errors
- Automatic reconnection handling
- Message queuing for offline scenarios
- Event dispatching to listeners

### WebSocketManager

The `WebSocketManager` class manages the WebSocket connection and provides:

- Connection management (connect, disconnect, reconnect)
- Message sending and receiving
- Event subscription and routing
- Error handling and recovery
- Workflow state tracking

### WebSocketContext

The `WebSocketContext` provides React components with:

- Access to WebSocket state (connected, connecting, error)
- Methods for sending messages
- Access to message histories
- Workflow state information
- UI state indicators (typing, processing)

## Troubleshooting

### Common Issues

1. **Blank Screen / No Rendering**
   - Check browser console for errors
   - Verify that WebSocketContext is properly initialized
   - Ensure components are correctly using the useWebSocket hook

2. **No Connection**
   - Check if the WebSocket URL is correct
   - Verify that the backend is running (if using real WebSocket)
   - Check for network issues or CORS restrictions

3. **No Mock Data**
   - Verify that `USE_MOCK_WS` is set to `true`
   - Check console logs for mock WebSocket initialization
   - Ensure mock data is properly defined in MockWebSocket.ts

### Debugging Tips

1. Enable verbose logging in environment.ts
2. Check browser console for detailed logs
3. Use browser developer tools to inspect WebSocket traffic
4. Add temporary console.logs to specific components for targeted debugging

## Recent Changes

The following changes were made to implement the hybrid WebSocket mocking approach:

1. **Created MockWebSocket Class**
   - Implemented the browser's WebSocket API
   - Added simulation of backend responses
   - Included detailed logging

2. **Updated WebSocketManager**
   - Added support for using either real or mock WebSocket
   - Enhanced logging throughout
   - Improved error handling

3. **Updated WebSocketProviderSelector**
   - Modified to always use the real WebSocketProvider
   - Added configuration for using mock WebSocket

4. **Updated Environment Configuration**
   - Added detailed comments about the hybrid approach
   - Added verbose logging option

5. **Added Documentation**
   - Created WebSocketMocking.md with detailed explanation
   - Created this comprehensive README

## Future Improvements

Potential future improvements to the WebSocket implementation:

1. **Enhanced Mock Data**
   - More realistic and varied mock responses
   - Scenario-based testing support
   - Customizable mock behavior

2. **Performance Optimizations**
   - Reduce unnecessary re-renders
   - Optimize message processing
   - Improve reconnection strategy

3. **Advanced Features**
   - Message compression
   - Binary message support
   - End-to-end encryption

4. **Testing Utilities**
   - Automated WebSocket testing
   - Mock scenario recording and playback
   - Network condition simulation
