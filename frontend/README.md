# Goali Frontend

This is the frontend application for the Goali coaching system, a multi-agent life coaching application designed to help users achieve their goals.

## Architecture Overview

The frontend is built using **React** and **TypeScript**. It communicates with the backend primarily via **WebSockets** and utilizes the **React Context API** for state management across the application.

### Core Concepts

*   **WebSocket Communication:** Real-time interaction with the backend (handling chat, activity updates, wheel data, etc.) is managed through a persistent WebSocket connection.
*   **Context-Based State Management:** Application state (like user info, connection status, chat history, wheel state) is managed globally using React Context, making it accessible to components that need it without prop drilling.
*   **Component Structure:** Components are organized by feature (e.g., `chat`, `wheel`, `activities`) or layout (`layout`).
*   **Custom Hooks:** Feature-specific logic and state access are often encapsulated in custom hooks (e.g., `useWebSocket`, `useChatMessages`, `useWheel`, `useActivity`) for better reusability and separation of concerns.

### Key Components & Services

*   **`App.tsx`:** The root component. Sets up the main context providers (`WebSocketProviderSelector`, `UserProvider`, `DebugProvider`).
*   **`WebSocketProviderSelector.tsx`:** A utility component that dynamically chooses between the real `WebSocketProvider` (using `WebSocketManager`) and the `MockWebSocketProvider` (using `MockWebSocket`) based on environment configuration (`ENV.USE_MOCK_WS`). This allows for development and testing without a live backend connection.
*   **`WebSocketContext.tsx`:** Provides the `WebSocketContext`. It manages the state related to the WebSocket connection (status, errors), message histories (chat, system), workflow status, and exposes methods (`sendChatMessage`, `sendSpinResult`, etc.) and state values via the `useWebSocket` hook. It relies on `WebSocketManager` for the actual communication.
*   **`WebSocketManager.ts`:** A class responsible for the low-level WebSocket connection management. It handles connecting, disconnecting, sending/receiving formatted messages according to the API contract, reconnection logic, and routing incoming messages to subscribed handlers within the `WebSocketContext`.
*   **`UserContext.tsx`:** Manages user-specific information (like user ID, profile details). Accessed via the `useUser` hook.
*   **`DebugContext.tsx`:** Provides context for debugging tools, potentially interacting with the `WebSocketManager` for debug messages. Accessed via the `useDebug` hook (if implemented).
*   **`GameContainer.tsx`:** The main layout component rendered within `App.tsx`. It arranges the primary UI sections:
    *   `UserHeader.tsx`: Displays user information.
    *   `ActivitiesPanel.tsx`: Shows user activities.
    *   `WheelContainer.tsx`: Displays the activity selection wheel.
    *   `ChatContainer.tsx`: Manages the chat interface.
    *   `ConnectionStatus.tsx`: Shows the current WebSocket connection status.

### Directory Structure (`src/`)

```
src/
├── App.tsx                 # Root application component
├── index.tsx               # Application entry point
├── components/             # Reusable UI components, organized by feature
│   ├── activities/
│   ├── chat/
│   ├── debug/
│   ├── layout/
│   └── wheel/
├── config/                 # Configuration files (constants, environment vars)
├── contexts/               # React Context providers (WebSocket, User, Debug)
├── hooks/                  # Custom React hooks for stateful logic
├── services/               # Core services (WebSocketManager)
├── styles/                 # Global CSS styles
├── tests/                  # Test files (mirroring src structure)
├── types/                  # TypeScript type definitions (api, models)
└── utils/                  # Utility functions and helper modules (Logger, Mocking)
```

### WebSocket Flow

1.  **Initialization:** `App.tsx` renders `WebSocketProviderSelector`.
2.  **Provider Selection:** `WebSocketProviderSelector` checks `ENV.USE_MOCK_WS` and renders either `WebSocketProvider` or `MockWebSocketProvider`.
3.  **Connection:** The chosen provider instantiates `WebSocketManager` (or `MockWebSocket`) which attempts to establish a connection.
4.  **Context Update:** `WebSocketContext` subscribes to events from `WebSocketManager` (connection status, messages) and updates its internal state.
5.  **Component Consumption:** Components use the `useWebSocket` hook to access connection state, message history, workflow status, and methods like `sendChatMessage`.
6.  **Sending Messages:** Components call methods like `sendChatMessage` on the context.
7.  **Manager Action:** The context forwards the call to `WebSocketManager`, which formats the message and sends it over the WebSocket.
8.  **Receiving Messages:** `WebSocketManager` receives messages, parses them, and notifies `WebSocketContext`.
9.  **State Update & Re-render:** `WebSocketContext` updates its state, causing consuming components to re-render with the new data.

## Mobile Shell (`GoaliMobileShell/`)

A separate React Native Expo project (`frontend/GoaliMobileShell/`) exists to act as a mobile container for this web application. It primarily uses a `WebView` component to display the web frontend. See `frontend/EXPO_WEBVIEW_SHELL_SETUP.md` for setup details.

## Getting Started

### Prerequisites

*   Node.js 16+
*   npm or yarn

### Installation

1.  Navigate to the `frontend` directory: `cd frontend`
2.  Install dependencies:
    ```bash
    npm install
    ```

### Running the Development Server

```bash
npm start
```

This starts the development server, typically at `http://localhost:3000`. The application expects a running backend WebSocket server at the URL specified in `src/config/environment.ts` (`WS_URL`).

### Using the Mock WebSocket

To run the frontend without a live backend connection, set the `REACT_APP_USE_MOCK_WS` environment variable to `true` before starting:

```bash
REACT_APP_USE_MOCK_WS=true npm start
```

Or configure it in your environment/`.env` file if applicable. The mock provider simulates basic responses.

### Building for Production

```bash
npm run build
```

This creates optimized production files in the `build` directory (or as configured by your build tool/CRA).

### Running Tests

```bash
npm test                # Run all tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage report
# Note: Integration tests might require specific setup or a running backend/mock.
```

For detailed testing information, including WebSocket testing strategies and commands, see [`frontend/tests/README.md`](./tests/README.md).
