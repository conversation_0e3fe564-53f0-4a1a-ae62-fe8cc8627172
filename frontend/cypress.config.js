
const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000', // Adjust this to your frontend dev server URL
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.js',
    experimentalWebKitSupport: true,
    video: false,
    viewportWidth: 1280,
    viewportHeight: 720,
    defaultCommandTimeout: 10000, // Increased timeout for WebSocket operations
    retries: {
      runMode: 2,    // Retry failed tests in CI/run mode
      openMode: 0    // Don't retry in open mode (interactive)
    }
  },
});