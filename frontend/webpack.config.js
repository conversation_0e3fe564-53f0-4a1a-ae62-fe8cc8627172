/**
 * Webpack configuration for the Game of Life frontend
 * This configuration provides a development server with hot reloading
 * and production build capabilities.
 */

const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');



module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';

  return {
    // Entry point of the application
    entry: './src/index.tsx',

    // Output configuration
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: isProduction ? 'js/[name].[contenthash].js' : 'js/[name].js',
      publicPath: '/',
      clean: true, // Clean the output directory before emit
    },

    // Enable source maps for debugging
    devtool: isProduction ? 'source-map' : 'eval-source-map',

    // Resolve file extensions
    resolve: {
      extensions: ['.tsx', '.ts', '.js', '.jsx'],
      alias: {
        '@': path.resolve(__dirname, 'src'), // Match the alias in tsconfig.json
      },
    },

    // Module rules for processing different file types
    module: {
      rules: [
        // TypeScript and TSX files
        {
          test: /\.tsx?$/,
          use: 'ts-loader',
          exclude: [/node_modules/, /tests\//, /\.test\.tsx?$/],
        },
        // CSS files
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader'],
        },
        // Asset files (images, fonts, etc.)
        {
          test: /\.(png|svg|jpg|jpeg|gif)$/i,
          type: 'asset/resource',
          generator: {
            filename: 'assets/[hash][ext][query]',
          },
        },
      ],
    },

    // Plugins
    plugins: [
      // Generate HTML file
      new HtmlWebpackPlugin({
        template: path.resolve(__dirname, 'public/index.html'),
        favicon: path.resolve(__dirname, 'public/favicon.ico'),
        title: 'Game of Life',
        meta: {
          viewport: 'width=device-width, initial-scale=1, shrink-to-fit=no'
        }
      }),
      new webpack.DefinePlugin({
        'process.env.REACT_APP_WS_URL': JSON.stringify(process.env.REACT_APP_WS_URL || ''),
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
      })
    ],


    // Development server configuration
    devServer: {
      static: {
        directory: path.join(__dirname, 'public'),
      },
      historyApiFallback: true, // For SPA routing
      compress: true,
      port: 3000,
      hot: true,
      open: true,
    },

    // Optimization
    optimization: {
      splitChunks: {
        chunks: 'all',
      },
    },

    // Performance hints
    performance: {
      hints: isProduction ? 'warning' : false,
    },
  };
};