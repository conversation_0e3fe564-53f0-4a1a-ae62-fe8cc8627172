// .eslintrc.js
module.exports = {
    extends: [
      'eslint:recommended',
      'plugin:react/recommended',
      'plugin:@typescript-eslint/recommended',
    ],
    parser: '@typescript-eslint/parser',
    plugins: [
      'react', 
      '@typescript-eslint',
      'react-hooks-strict'
    ],
    rules: {
    // Enforce explicit typing in React hooks
    '@typescript-eslint/explicit-function-return-type': [
      'error',
      { allowExpressions: true, allowTypedFunctionExpressions: true }
    ],
    
    // Require useCallback for functions in components
    'react-hooks/exhaustive-deps': 'warn',
    
    // Enforce proper hook naming
    'naming-convention': [
      'error',
      {
        selector: 'function',
        format: ['camelCase'],
        prefix: ['use'],
        filter: {
          regex: '^use[A-Z]',
          match: true
        }
      }
    ],

    // Configure React import handling
    'react/react-in-jsx-scope': 'off', // Turn off requiring React when using JSX
    'react/jsx-uses-react': 'off'      // Turn off marking React as used when using JSX
  }
};