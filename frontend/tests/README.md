# Testing Strategy for Game of Life Frontend

This README provides an overview of the testing approach for the Game of Life frontend application, with a focus on WebSocket functionality.

## Testing Layers

Our testing strategy follows a layered approach:

1. **Unit Tests** - Test individual components and services in isolation
2. **Integration Tests** - Test interactions between components
3. **End-to-End Tests** - Test complete user flows in a real browser environment

## Directory Structure

```
frontend/tests/
├── README.md             # This file
├── unit/                 # Unit tests for isolated components
│   ├── components/       # Tests for React components
│   └── services/         # Tests for service modules (WebSocketManager, etc.)
├── integration/          # Integration tests
│   ├── MockConsumerTest.test.tsx       # Mock WebSocket server tests
│   ├── WebSocketContext.test.tsx       # WebSocket context tests
│   ├── WebSocketDjangoIntegration.test.ts  # Real Django integration tests
│   └── WebSocketIntegration.test.ts    # General WebSocket integration tests
└── end2end/              # End-to-end tests with Cypress
    ├── CYPRESS_SETUP_GUIDE.md          # Guide for setting up Cypress tests
    └── cypress/                        # Cypress test files (when created)
        ├── e2e/                        # E2E test specifications
        └── support/                    # Cypress support files
```

## Running Tests

### Unit and Integration Tests

```bash
# Run all tests
npm test

# Run only unit tests
npm run test -- -t unit

# Run only mock WebSocket tests
npm run test:mock

# Run real integration tests with Django (requires running Django server)
npm run test:integration
```

### End-to-End Tests

For end-to-end testing with Cypress, follow the instructions in [CYPRESS_SETUP_GUIDE.md](./end2end/CYPRESS_SETUP_GUIDE.md).

```bash
# Start Cypress test runner (interactive)
npm run cypress:open

# Run Cypress tests headlessly
npm run cypress:run
```

## WebSocket Testing Approaches

We use different approaches to test WebSocket functionality:

### 1. Mock Consumer Approach

Tests WebSocket client logic using a simulated WebSocket server.

- **File**: `tests/integration/MockConsumerTest.test.tsx`
- **Command**: `npm run test:mock`
- **Benefits**: Works reliably in any environment, doesn't require a backend server
- **Use When**: Testing WebSocketManager functionality in isolation

### 2. Real Django Integration

Tests integration with the actual Django WebSocket consumer.

- **File**: `tests/integration/WebSocketDjangoIntegration.test.ts`
- **Command**: `npm run test:integration`
- **Limitations**: Doesn't work in JSDOM environment, requires configuration
- **Use When**: Verifying compatibility with the Django backend

### 3. End-to-End with Cypress

Tests full user flows including WebSocket communication in a real browser.

- **Directory**: `tests/end2end/cypress/e2e/`
- **Command**: `npm run cypress:run`
- **Benefits**: Tests the entire stack including UI in a real browser
- **Use When**: Testing complete user journeys and flows

## Test Debugging

### WebSocket Tests

When WebSocket tests fail, check:

1. **Connection Issues**: Is the WebSocket URL correct? Is the server running?
2. **Environment Issues**: Are you running in JSDOM or Node? Real WebSocket connections need Node environment.
3. **Timing Issues**: Are event handlers registered before events occur?
4. **Mock Configuration**: For mock tests, is the mock properly simulating the backend?

### End-to-End Tests

Refer to [CYPRESS_SETUP_GUIDE.md](./end2end/CYPRESS_SETUP_GUIDE.md) for detailed debugging instructions for Cypress tests.

## Adding New Tests

When adding new tests:

1. **Unit Tests**: Place in `tests/unit/` directory, matching the source structure
2. **Integration Tests**: Place in `tests/integration/` with descriptive names
3. **E2E Tests**: Follow Cypress conventions in `tests/end2end/cypress/e2e/`

## Testing Best Practices

1. **Test Isolation**: Each test should run independently without relying on other tests
2. **Avoid Flakiness**: Especially for WebSocket tests, ensure robust handling of async operations
3. **Clear Assertions**: Make assertions clear and specific to what you're testing
4. **Event Timing**: For WebSocket tests, always set up subscriptions before events occur
5. **Use Appropriate Mocks**: Mock at the right level (WebSocket interface, not implementation)

## Additional Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Testing Library Documentation](https://testing-library.com/docs/)
- [Cypress Documentation](https://docs.cypress.io/)
- [WebSocket API Documentation](https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API)
