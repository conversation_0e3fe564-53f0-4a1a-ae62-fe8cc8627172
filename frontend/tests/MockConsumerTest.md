# Testing with Enhanced Mock Consumer

This guide explains how to use the `EnhancedWebSocketMock` to test your WebSocketManager without needing a running Django server.

## Why Use a Mock Consumer?

When testing WebSocket communication, there are several challenges:

1. **Environment Setup**: Running a Django server requires database setup, migrations, etc.
2. **Test Independence**: Tests shouldn't depend on external services to be reliable
3. **Speed**: Tests with real servers are slower than tests with mocks

By using a mock consumer that mimics the behavior of the real Django consumer, we can test the WebSocketManager thoroughly without these challenges.

## The Enhanced Mock Consumer

The `EnhancedWebSocketMock` in `tests/mocks/EnhancedWebSocketMock.ts` simulates the Django UserSessionConsumer by:

1. Opening a mock WebSocket server on the specified URL
2. Sending welcome messages to new connections
3. Processing incoming messages based on type
4. Sending back realistic responses according to the API contract

## Using the Mock Consumer in Tests

Here's a simple example of how to use the mock consumer:

```typescript
import { WebSocketManager } from '../../src/services/WebSocketManager';
import { createMockConsumer } from '../mocks/EnhancedWebSocketMock';

describe('WebSocketManager Integration with Mock Consumer', () => {
  const WS_URL = 'ws://localhost:8000/ws/game/';
  let mockConsumer;
  let manager;
  
  beforeEach(() => {
    // Create mock consumer first
    mockConsumer = createMockConsumer(WS_URL);
    
    // Then create WebSocketManager pointing to the mock
    manager = new WebSocketManager(WS_URL, 'test-user');
    
    // Connect to mock server
    manager.connect();
    
    // Wait for connection to establish
    return new Promise(resolve => {
      manager.subscribeToConnection(({ connected }) => {
        if (connected) resolve(true);
      });
    });
  });
  
  afterEach(() => {
    // Clean up
    mockConsumer.close();
    manager.disconnect();
  });
  
  test('should receive welcome message on connect', done => {
    manager.subscribe('system_message', message => {
      expect(message.content).toContain('Connected to the Game of Life server');
      done();
    });
  });
  
  test('should send chat message and receive mocked response', done => {
    const messages = [];
    
    manager.subscribe('chat_message', message => {
      messages.push(message);
      
      // Wait for both echo and response
      if (messages.length >= 2) {
        // First message should be echo
        expect(messages[0].is_user).toBe(true);
        expect(messages[0].content).toBe('Test message');
        
        // Second message should be response
        expect(messages[1].is_user).toBe(false);
        expect(messages[1].content).toContain('I received your message');
        
        done();
      }
    });
    
    manager.sendChatMessage('Test message');
  });
});
```

## Benefits Over Simple Mocks

The EnhancedWebSocketMock provides several advantages over simpler mocks:

1. **Realistic behavior**: It mimics the real consumer's message patterns, timing, and responses
2. **Complete API simulation**: It handles all message types defined in the API contract
3. **Error handling**: It includes error scenarios like the real consumer would
4. **Processing state**: It simulates processing states with the correct timing

## When to Use Mock vs. Real Integration

| Test Type | When to Use |
|-----------|------------|
| **Unit Tests** | For testing basic WebSocketManager functionality |
| **Mock Consumer Tests** | For testing message flow without Django setup |
| **Real Integration Tests** | For validating the actual Django consumer integration |

Best practice is to have a mix of all three: quick unit tests for basic functionality, mock consumer tests for message flow, and real integration tests for end-to-end validation.

## Running the Mock Consumer Tests

```bash
# Run the tests without needing a Django server
npm test
```

Unlike the real integration tests, these tests don't require a running Django server, making them ideal for continuous integration environments.
