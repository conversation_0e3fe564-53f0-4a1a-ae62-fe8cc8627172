# WebSocket Testing Guide

This guide explains how to test the WebSocket integration between the frontend WebSocketManager and the Django Channels backend.

## Types of Tests

### 1. Unit Tests
Test the WebSocketManager class in isolation with mocked WebSocket connections.

### 2. Integration Tests
Test the WebSocketManager with the actual Django Channels backend.

## Setup for Testing

### Install Dependencies
```bash
npm install --save-dev jest @testing-library/react @testing-library/jest-dom mock-socket jest-websocket-mock ts-jest

Configure Backend for Testing

Start the Django development server in test mode:
bashCopycd backend
python manage.py runserver --settings=config.settings.test

Verify the WebSocket endpoint is accessible at:
ws://localhost:8000/ws/game/

Running Tests
Unit Tests
bashCopynpm test
Integration Tests
bashCopy# Make sure the Django server is running first
npm run test:integration
Test Structure
WebSocketManager Tests
Tests focus on:

Connection management
Message sending & format compliance
Message receiving & handling
Reconnection logic
Message queuing

WebSocketContext Tests
Tests focus on:

Context state updates from WebSocket messages
Method pass-through to WebSocketManager
Component integration with context

End-to-End Tests
Tests focus on:

Full communication with Django backend
Message serialization/deserialization
Real message flows

Debugging Tests
To debug failing tests:

Add console.log statements in the test files
Run with verbose flag: npm test -- --verbose
Check WebSocket traffic using browser devtools when needed