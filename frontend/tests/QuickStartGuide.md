# WebSocket Testing Quick Start Guide

This guide will help you quickly set up and run WebSocket tests in the Game of Life project.

## Option 1: Testing with Mock Consumer (No Django Required)

For quick development and testing, you can use the enhanced mock consumer:

```bash
npm run test:mock
```

This approach:
- Doesn't require a Django server
- Is faster and more reliable for development
- Tests the WebSocketManager against a simulated Django consumer

## Option 2: Testing with Real Django Integration

For full end-to-end testing with the real Django backend:

### 1. Start Django Server with Test Settings

```bash
cd ../backend
python manage.py runserver --settings=config.settings.test
```

The test settings:
- Use SQLite in-memory database (no PostgreSQL needed)
- Use in-memory channel layer (no Redis needed)
- Include all required Django apps

### 2. Run Integration Tests

In a separate terminal:

```bash
cd ../frontend
npm run test:integration
```

## Understanding Test Structure

Our testing strategy has multiple layers:

1. **Unit Tests** (`npm test`):
   - Test WebSocketManager functionality in isolation
   - Use simple mocks from jest-websocket-mock

2. **Mock Consumer Tests** (`npm run test:mock`):
   - Test WebSocketManager against a simulated Django consumer
   - Verify message formatting and handling
   - No Django server required

3. **Integration Tests** (`npm run test:integration`):
   - Test with the actual Django server
   - Verify end-to-end communication
   - Requires Django running with test settings

## Troubleshooting

### Django Server Issues

If you encounter issues starting Django:

1. Check for required apps:
   ```python
   # Make sure test.py includes these
   INSTALLED_APPS = [
       'django.contrib.admin',
       'django.contrib.auth',
       'django.contrib.contenttypes',
       'django.contrib.sessions',
       'django.contrib.messages',
       'django.contrib.staticfiles',
       'channels',
       'apps.main',
       'apps.user',
       'apps.activity',
   ]
   ```

2. Verify channel layer configuration:
   ```python
   # In-memory channel layer for tests
   CHANNEL_LAYERS = {
       "default": {
           "BACKEND": "channels.layers.InMemoryChannelLayer"
       },
   }
   ```

### WebSocket Connection Issues

If tests can't connect to Django:

1. Check the WebSocket URL:
   - WebSocketManager uses: `ws://localhost:8000/ws/game/`
   - Verify this matches the URL pattern in `routing.py`

2. Look for Django connection logs in the console

## Next Steps

After getting tests running, you can:

1. Add more test cases for different message types
2. Extend the mock consumer to handle additional scenarios
3. Create UI integration tests with React Testing Library
