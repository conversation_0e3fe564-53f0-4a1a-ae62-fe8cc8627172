# Cypress End-to-End Testing for WebSockets in Game of Life

This guide provides comprehensive instructions for setting up and running end-to-end tests for WebSocket functionality in the Game of Life application using Cypress.

## What is Cypress?

Cypress is an end-to-end testing framework that runs in a real browser environment, making it perfect for testing WebSocket connections that <PERSON><PERSON><PERSON><PERSON> struggles with. Key benefits include:

1. **Real Browser Environment**: Tests run in an actual browser, not a simulated environment
2. **Time-Travel Debugging**: View exactly what happened at each step of your test
3. **Automatic Waiting**: No need for arbitrary timeouts or manual waits
4. **Network Traffic Control**: Monitor and stub network requests
5. **Screenshots and Videos**: Automatic capture of test runs for debugging

## Setting Up Cypress for WebSocket Testing

### 1. Install Cypress

If not already installed, add <PERSON><PERSON> to your project:

```bash
npm install --save-dev cypress
```

### 2. Create Configuration Files

Create the following files to configure Cypress for WebSocket testing:

#### `cypress.config.js` (in the frontend root)

```javascript
const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000', // Adjust this to your frontend dev server URL
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.js',
    experimentalWebKitSupport: true,
    video: false,
    viewportWidth: 1280,
    viewportHeight: 720,
    defaultCommandTimeout: 10000, // Increased timeout for WebSocket operations
    retries: {
      runMode: 2,    // Retry failed tests in CI/run mode
      openMode: 0    // Don't retry in open mode (interactive)
    }
  },
});
```

#### `cypress/support/e2e.js`

Create this folder structure and file:

```javascript
// cypress/support/e2e.js
// ***********************************************************
// This is a great place to put global configuration and
// behavior that modifies Cypress.
// ***********************************************************

// Custom command for logging in or setting user context
Cypress.Commands.add('setUserContext', (userId = 'test-user-1') => {
  // Store test user ID in localStorage (adjust according to your auth mechanism)
  localStorage.setItem('userProfileId', userId);
  
  // If you have a more complex auth flow, implement it here
  // For example:
  // cy.visit('/login');
  // cy.get('#username').type('testuser');
  // cy.get('#password').type('password123');
  // cy.get('#login-button').click();
});

// Listen for WebSocket events to aid debugging
Cypress.on('window:before:load', (win) => {
  // Save original WebSocket
  const originalWebSocket = win.WebSocket;
  
  // Override WebSocket constructor to add logging
  win.WebSocket = function(url, protocols) {
    const socket = new originalWebSocket(url, protocols);
    
    // Log WebSocket events
    socket.addEventListener('open', () => {
      console.log(`%c WebSocket Connected: ${url}`, 'color: green; font-weight: bold');
    });
    
    socket.addEventListener('close', (event) => {
      console.log(`%c WebSocket Closed: ${event.code}`, 'color: orange; font-weight: bold');
    });
    
    socket.addEventListener('error', (error) => {
      console.error(`%c WebSocket Error: ${error}`, 'color: red; font-weight: bold');
    });
    
    // Monitor messages
    const originalSend = socket.send;
    socket.send = function(data) {
      console.log(`%c WebSocket Sent: ${data}`, 'color: blue; font-weight: bold');
      return originalSend.apply(this, arguments);
    };
    
    socket.addEventListener('message', (event) => {
      console.log(`%c WebSocket Received: ${event.data}`, 'color: purple; font-weight: bold');
    });
    
    return socket;
  };
});
```

### 3. Create WebSocket Test Files

Create the following test files to verify WebSocket functionality:

#### `cypress/e2e/websocket/connection.cy.js`

```javascript
/**
 * Tests WebSocket connection establishment and basic functionality
 */
describe('WebSocket Connection', () => {
  beforeEach(() => {
    // Set test user context
    cy.setUserContext('cypress-test-user');
    
    // Visit the main app page
    cy.visit('/');
    
    // Wait for the app to initialize WebSocket connection
    // Adjust the selector to match your connection indicator
    cy.get('[data-testid="connection-status"]', { timeout: 5000 })
      .should('have.text', 'Connected');
  });
  
  it('should establish a WebSocket connection', () => {
    // This is verified in the beforeEach already
    // Here we can add additional assertions about the connection state
    cy.window().then((win) => {
      // Access the WebSocketContext through the window
      // Note: You might need to expose this from your React context for testing
      expect(win.wsConnectionActive).to.eq(true);
    });
  });
  
  it('should handle disconnection and reconnection', () => {
    // Simulate disconnection (you might need a debug tool or test hook in your app)
    cy.window().then((win) => {
      // Call disconnect method (adjust according to your app's API)
      if (win.debugWebSocket && win.debugWebSocket.disconnect) {
        win.debugWebSocket.disconnect();
        
        // Connection status should update
        cy.get('[data-testid="connection-status"]')
          .should('have.text', 'Disconnected');
          
        // Reconnect
        win.debugWebSocket.connect();
        
        // Status should update again
        cy.get('[data-testid="connection-status"]')
          .should('have.text', 'Connected');
      } else {
        // Skip test if debug methods aren't available
        cy.log('Debug WebSocket methods not available - skipping test');
      }
    });
  });
});
```

#### `cypress/e2e/websocket/chat-messages.cy.js`

```javascript
/**
 * Tests sending and receiving chat messages via WebSocket
 */
describe('WebSocket Chat Messages', () => {
  beforeEach(() => {
    // Set test user context
    cy.setUserContext('cypress-test-user');
    
    // Visit the chat page
    cy.visit('/chat'); // Adjust to your app's chat page path
    
    // Wait for connection to establish
    cy.get('[data-testid="connection-status"]', { timeout: 5000 })
      .should('have.text', 'Connected');
  });
  
  it('should send a chat message and receive confirmation', () => {
    const testMessage = 'Hello from Cypress test!';
    
    // Type message in the input field
    cy.get('[data-testid="message-input"]').type(testMessage);
    
    // Click send button
    cy.get('[data-testid="send-button"]').click();
    
    // Verify the message appears in the chat (user message echo)
    cy.get('[data-testid="message-list"] [data-testid="user-message"]')
      .should('contain', testMessage);
    
    // Verify processing status is shown
    cy.get('[data-testid="processing-indicator"]')
      .should('be.visible');
    
    // Verify we receive a response
    cy.get('[data-testid="message-list"] [data-testid="system-message"]', { timeout: 10000 })
      .should('exist');
    
    // Verify processing completes
    cy.get('[data-testid="processing-indicator"]')
      .should('not.exist');
  });
  
  it('should handle error responses', () => {
    // Send a message that will trigger an error (e.g., empty message)
    cy.get('[data-testid="send-button"]').click();
    
    // Verify error message appears
    cy.get('[data-testid="error-message"]')
      .should('be.visible');
  });
});
```

#### `cypress/e2e/websocket/activity-wheel.cy.js`

```javascript
/**
 * Tests the activity wheel functionality via WebSocket
 */
describe('Activity Wheel WebSocket Integration', () => {
  beforeEach(() => {
    // Set test user context
    cy.setUserContext('cypress-test-user');
    
    // Visit the wheel page
    cy.visit('/wheel'); // Adjust to your app's wheel page path
    
    // Wait for connection to establish
    cy.get('[data-testid="connection-status"]', { timeout: 5000 })
      .should('have.text', 'Connected');
  });
  
  it('should request and receive wheel data', () => {
    // Request wheel data (e.g., click a button that triggers the request)
    cy.get('[data-testid="generate-wheel-button"]').click();
    
    // Verify processing indicator shows
    cy.get('[data-testid="processing-indicator"]')
      .should('be.visible');
    
    // Verify wheel data is received and wheel is rendered
    cy.get('[data-testid="activity-wheel"]', { timeout: 15000 })
      .should('be.visible');
    
    // Verify wheel has items
    cy.get('[data-testid="wheel-item"]')
      .should('have.length.at.least', 1);
  });
  
  it('should select an activity and receive confirmation', () => {
    // First ensure wheel is loaded
    cy.get('[data-testid="generate-wheel-button"]').click();
    cy.get('[data-testid="activity-wheel"]', { timeout: 15000 })
      .should('be.visible');
    
    // Simulate spinning the wheel and selecting an activity
    // This might be tricky as wheel spinning could be animated
    // A simpler approach is to directly trigger the selection
    cy.get('[data-testid="wheel-item"]').first().click();
    
    // Alternative: use a test hook if direct selection isn't possible
    // cy.window().then((win) => {
    //   win.testSelectWheelItem(0); // Assuming you have a test helper function
    // });
    
    // Verify selection confirmation is received
    cy.get('[data-testid="activity-selected-confirmation"]')
      .should('be.visible');
    
    // Verify activity details are shown
    cy.get('[data-testid="selected-activity-details"]')
      .should('be.visible');
  });
});
```

## Testing Best Practices for WebSockets

### General Guidelines

1. **Add data-testid attributes to your components**:
   ```jsx
   <div data-testid="connection-status">Connected</div>
   <input data-testid="message-input" type="text" />
   <button data-testid="send-button">Send</button>
   ```

2. **Expose test hooks for complex interactions**:
   ```jsx
   // In your app
   if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
     window.debugWebSocket = {
       connect: () => webSocketManager.connect(),
       disconnect: () => webSocketManager.disconnect(),
       // Add other debug methods as needed
     };
   }
   ```

3. **Use appropriate timeouts**:
   - WebSocket operations might take longer than regular DOM operations
   - Use longer timeouts for WebSocket-related assertions: `{ timeout: 10000 }`

### Adapting to Your Application Structure

The examples above assume certain component structures and data-testid attributes that might not match your application. Adjust the selectors and assertions to match your actual implementation:

1. **Locate your WebSocket status indicator**:
   - Find where connection status is displayed in your UI
   - Add appropriate data-testid attributes

2. **Identify chat message components**:
   - Locate message input, send button, and message list
   - Add data-testid attributes to these elements

3. **Map wheel interaction components**:
   - Find wheel generation triggers and wheel items
   - Add data-testid attributes for testing

## Running the Tests

### Starting the Test Environment

1. **Start your backend server**:
   ```bash
   cd backend
   python manage.py runserver --settings=config.settings.test
   ```

2. **Start your frontend development server**:
   ```bash
   cd frontend
   npm start
   ```

3. **Run Cypress tests**:
   - Interactive mode: `npx cypress open`
   - Headless mode: `npx cypress run`

### Adding Scripts to package.json

Add these scripts to your package.json for easier test execution:

```json
"scripts": {
  "cypress:open": "cypress open",
  "cypress:run": "cypress run",
  "test:e2e": "cypress run"
}
```

Then you can run:
```bash
npm run cypress:open  # For interactive mode
npm run test:e2e     # For headless mode
```

## Troubleshooting

### Common Issues

1. **Tests can't find elements**:
   - Verify data-testid attributes are correctly set
   - Check if elements are visible or if they're conditionally rendered
   - Use `cy.log` and `cy.debug()` to debug test execution

2. **WebSocket connection fails**:
   - Verify backend server is running
   - Check if CORS is properly configured
   - Ensure frontend is using the correct WebSocket URL

3. **Tests timeout waiting for responses**:
   - Increase timeout values for WebSocket operations
   - Ensure backend is responding to WebSocket messages
   - Check for any errors in browser console or backend logs

### Debugging WebSockets

The e2e.js support file includes WebSocket instrumentation that logs all WebSocket activity to the browser console. This can be invaluable for debugging:

1. Open Cypress in interactive mode: `npm run cypress:open`
2. Select a test to run
3. Open the browser's Developer Tools console
4. Watch for WebSocket activity:
   - Green: connection established
   - Blue: messages sent
   - Purple: messages received
   - Orange: connection closed
   - Red: errors

## Integration with CI/CD

Add Cypress tests to your CI/CD pipeline:

```yaml
# Example GitHub Actions workflow
jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: Install dependencies
        run: npm ci
      - name: Start backend server
        run: |
          cd backend
          pip install -r requirements.txt
          python manage.py runserver --settings=config.settings.test &
      - name: Start frontend server
        run: |
          cd frontend
          npm start &
      - name: Run Cypress tests
        run: npm run test:e2e
```

## Further Resources

- [Cypress Documentation](https://docs.cypress.io/)
- [Testing WebSockets with Cypress](https://www.cypress.io/blog/2020/11/30/testing-websockets/)
- [Cypress Best Practices](https://docs.cypress.io/guides/references/best-practices)

## Next Steps

1. **Add data-testid attributes** to your components to match the test selectors
2. **Create the necessary directory structure** for Cypress tests
3. **Implement debug methods** in your WebSocketManager for testing
4. **Run the tests** and refine as needed

---

By following this guide, you'll be able to create robust end-to-end tests for WebSocket functionality in your Game of Life application. These tests will run in a real browser environment, ensuring that your WebSocket communication works as expected from the user's perspective.
