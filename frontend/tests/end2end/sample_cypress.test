// tests/end2end/WebSocketUIIntegration.test.js
describe('WebSocket UI Integration', () => {
  it('should send a chat message and receive a response', () => {
    cy.visit('/');
    
    // Login or set user context
    cy.login();
    
    // Type and send a message
    cy.get('#message-input').type('Hello, this is a test message');
    cy.get('#send-button').click();
    
    // Verify the message appears in the chat
    cy.get('.message.user').should('contain', 'Hello, this is a test message');
    
    // Wait for response from backend
    cy.get('.message.assistant').should('exist');
  });
});