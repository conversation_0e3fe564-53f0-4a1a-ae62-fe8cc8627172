// src/__tests__/contexts/WebSocketContext.test.tsx
import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { WebSocketProvider, useWebSocket } from '@/contexts/WebSocketContext';
import WS from 'jest-websocket-mock';

// Test component that uses the context
const TestComponent = () => {
  const { isConnected, lastChatMessage, sendChatMessage } = useWebSocket();
  
  return (
    <div>
      <div data-testid="connection-status">
        {isConnected ? 'Connected' : 'Disconnected'}
      </div>
      {lastChatMessage && (
        <div data-testid="last-message">
          {lastChatMessage.content}
        </div>
      )}
      <button 
        data-testid="send-button"
        onClick={() => sendChatMessage('Test message')}
      >
        Send
      </button>
    </div>
  );
};

describe('WebSocketContext', () => {
  let ws: WS;
  
  beforeEach(async () => {
    ws = new WS('ws://localhost:1234');
  });
  
  afterEach(() => {
    WS.clean();
  });
  
  test('should update context state when receiving messages', async () => {
    // Render component with context provider
    render(
      <WebSocketProvider url="ws://localhost:1234" userProfileId="test-user">
        <TestComponent />
      </WebSocketProvider>
    );
    
    // Wait for connection
    await ws.connected;
    
    // Check initial state
    expect(screen.getByTestId('connection-status')).toHaveTextContent('Connected');
    
    // Send message from server
    const mockMessage = {
      type: 'chat_message',
      content: 'Hello from server',
      is_user: false
    };
    
    act(() => {
      ws.send(JSON.stringify(mockMessage));
    });
    
    // Verify context updated component
    expect(screen.getByTestId('last-message')).toHaveTextContent('Hello from server');
  });
});