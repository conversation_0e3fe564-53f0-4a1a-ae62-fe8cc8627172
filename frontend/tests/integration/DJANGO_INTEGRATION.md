# Django WebSocket Integration Testing

This guide provides step-by-step instructions for testing WebSocket integration between the frontend and Django backend.

## Quick Setup (30-Minute Guide)

### 1. Start the Django Server

```bash
# Navigate to the backend directory
cd ../backend

# Start the Django development server
python manage.py runserver
```

The server should start successfully and display a message like:
```
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.
```

### 2. Verify WebSocket Endpoint

Before running the test, verify that the WebSocket endpoint is accessible. Your Django server should have the WebSocket endpoint defined in `apps/main/routing.py`:

```python
websocket_urlpatterns = [
    re_path(r'ws/game/$', consumers.UserSessionConsumer.as_asgi()),
]
```

### 3. Run Integration Test

In a separate terminal:

```bash
# Navigate to the frontend directory
cd ../frontend

# Run the integration test
npm run test:integration
```

The test will establish a WebSocket connection to the Django server and verify:
1. Connection establishment
2. Receiving a welcome message
3. Sending a chat message and receiving a response

## How the Integration Works

The integration test (`WebSocketDjangoIntegration.test.ts`) connects directly to your running Django server:

```typescript
// Create WebSocket manager pointing to Django endpoint
manager = new WebSocketManager('ws://localhost:8000/ws/game/test/', 'test-user');

// Connect to the actual server
manager.connect();
```

When the test runs:

1. The WebSocketManager connects to the WebSocket endpoint
2. The Django WebSocket consumer accepts the connection
3. The consumer sends a welcome message
4. The test verifies the welcome message was received
5. The test sends a chat message
6. The consumer processes the message and sends back a response
7. The test verifies the response

This confirms that the WebSocketManager correctly formats messages according to the API contract and that the Django consumer correctly interprets and responds to these messages.

## Common Issues and Troubleshooting

### Connection Refused Error

If you see an error like `WebSocket connection to 'ws://localhost:8000/ws/game/test/' failed`:

1. Verify the Django server is running
2. Check the WebSocket URL matches the route defined in `routing.py`
3. Ensure the port (8000) is correct and not blocked

### Timeout Waiting for Message

If the test times out waiting for a message:

1. Increase the test timeout (`jest.setTimeout(10000)`)
2. Check the Django consumer implementation to ensure it's sending the expected responses
3. Examine Django logs for any errors in the consumer

### Incorrectly Formatted Messages

If messages aren't being processed correctly:

1. Verify the message format matches the API contract
2. Check for any differences between test expectations and actual implementation

## Django Consumer Testing

The Django consumer can also be tested directly using Django's built-in testing utilities:

```python
# DjangoWebSocketConsumer.test.py
from channels.testing import WebsocketCommunicator
from django.test import TestCase
from config.asgi import application

class WebSocketTests(TestCase):
    async def test_websocket_connection(self):
        communicator = WebsocketCommunicator(application, "/ws/game/")
        connected, _ = await communicator.connect()
        self.assertTrue(connected)
        await communicator.disconnect()
```

Run these tests with:

```bash
cd ../backend
python manage.py test
```

This allows you to verify the backend implementation independently from the frontend.
