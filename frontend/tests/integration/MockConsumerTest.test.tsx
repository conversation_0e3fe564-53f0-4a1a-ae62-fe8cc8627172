/**
 * Integration test using the enhanced mock consumer instead of real Django
 */
import { WebSocketManager } from '../../src/services/WebSocketManager';
import { createMockConsumer } from '../mocks/EnhancedWebSocketMock';

describe('WebSocketManager Integration with Mock Consumer', () => {
  const WS_URL = 'ws://localhost:8000/ws/game/';
  let mockConsumer: any;
  let manager: WebSocketManager;

  beforeEach(() => {
    // Create mock consumer first
    mockConsumer = createMockConsumer(WS_URL);

    // Then create WebSocketManager pointing to the mock
    manager = new WebSocketManager(WS_URL, 'test-user');
    
    // Don't connect yet - we'll do that in the tests
  });

  afterEach(() => {
    // Clean up
    mockConsumer.close();
    manager.disconnect();
  });

  test('should receive welcome message on connect', done => {
    // First subscribe to the event
    manager.subscribe('system_message', message => {
      expect('content' in message ? message.content : '').toContain('Connected to the Game of Life server');
      done();
    });
    
    // Then connect after subscription is set up
    manager.connect();
  });

  test('should send chat message and receive mocked response', done => {
    const messages: any[] = [];

    // First set up the subscription
    manager.subscribe('chat_message', message => {
      messages.push(message);

      // Wait for both echo and response
      if (messages.length >= 2) {
        // First message should be echo
        expect(messages[0].is_user).toBe(true);
        expect('content' in messages[0] ? messages[0].content : '').toBe('Test message');

        // Second message should be response
        expect(messages[1].is_user).toBe(false);
        expect('content' in messages[1] ? messages[1].content : '').toContain('I received your message');

        done();
      }
    });

    // Then connect and send the message
    manager.connect();
    
    // Wait for connection to establish before sending message
    manager.subscribeToConnection(({ connected }) => {
      if (connected) {
        manager.sendChatMessage('Test message');
      }
    });
  });

  test('should handle processing status updates', done => {
    const statuses: string[] = [];

    // Subscribe to processing status
    manager.subscribe('processing_status', status => {
      if ('status' in status) {
        statuses.push(status.status);

        // We should receive processing and then completed
        if (statuses.includes('completed')) {
          expect(statuses).toContain('processing');
          expect(statuses).toContain('completed');
          done();
        }
      }
    });

    // Connect first
    manager.connect();
    
    // Wait for connection before sending the message
    manager.subscribeToConnection(({ connected }) => {
      if (connected) {
        // Send a message to trigger processing
        manager.sendChatMessage('Test message');
      }
    });
  });
});