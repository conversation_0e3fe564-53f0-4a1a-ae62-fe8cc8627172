// tests/integration/WebSocketDjangoIntegration.test.ts
import { WebSocketManager } from '../../src/services/WebSocketManager';

/**
 * IMPORTANT: Real Integration Test with Django Server
 * -----------------------------------------------
 * This test requires the Django server running with:
 * python manage.py runserver --settings=config.settings.test
 * 
 * ⚠️ NOTE: This test will NOT run correctly in the standard Jest environment (JSDOM)
 * because JSDOM cannot make real WebSocket connections to external servers.
 * 
 * For real integration testing, we need to:
 * 1. Either use the node environment (see commented config below)
 * 2. Or implement a proper end-to-end test with <PERSON>press/Playwright
 */

// Uncomment to use Node environment for the test and allow real connections
// @jest-environment node

// This test requires Django server running for full integration
// Run with: npm run test:integration
describe('WebSocket Integration with Django', () => {
  let manager: WebSocketManager;
  
  // Increase the timeout for the entire test suite
  jest.setTimeout(10000);

  beforeEach(() => {
    console.log('⚠️ Attempting to connect to real Django server at ws://localhost:8000/ws/game/');
    console.log('⚠️ If this test fails, verify the Django server is running with the test settings');
    console.log('⚠️ For reliable testing, use npm run test:mock instead, which uses a simulated backend');
    
    // Connect to actual Django test server
    manager = new WebSocketManager('ws://localhost:8000/ws/game/', 'test-user');
    
    // Setup error handling to provide more diagnostic information
    const originalOnError = WebSocket.prototype.onerror;
    WebSocket.prototype.onerror = function(event) {
      console.error('WebSocket connection error:', event);
      if (originalOnError) originalOnError.call(this, event);
    };
    
    manager.connect();

    // Wait for connection with a reasonable timeout
    return new Promise((resolve, reject) => {
      const connectionTimeout = setTimeout(() => {
        reject(new Error('Timed out waiting for WebSocket connection to establish'));
      }, 5000);
      
      manager.subscribeToConnection(({ connected }) => {
        if (connected) {
          clearTimeout(connectionTimeout);
          resolve(true);
        }
      });
    });
  });

  afterEach(() => {
    manager.disconnect();
  });

  test('should receive system welcome message', done => {
    // First subscribe to events, then connect - but in this case connection
    // already happened in beforeEach, so we're testing if we've received the
    // welcome message already
    manager.subscribe('system_message', message => {
      console.log('Received system message:', message);
      expect('content' in message ? message.content : '').toContain('Connected to the Game of Life server');
      done();
    });
  });

  test('should send chat message and receive response', done => {
    const responses: any[] = [];

    manager.subscribe('chat_message', message => {
      console.log('Received chat message:', message);
      responses.push(message);

      // After receiving echo and response
      if (responses.length >= 2) {
        expect(responses[0].is_user).toBe(true);
        expect('content' in responses[0] ? responses[0].content : '').toContain('Test integration message');

        expect(responses[1].is_user).toBe(false);

        done();
      }
    });

    // Send test message
    manager.sendChatMessage('Test integration message');
  }, 8000); // Longer timeout for backend processing
});