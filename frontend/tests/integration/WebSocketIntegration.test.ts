// tests/integration/WebSocketIntegration.test.ts
import { WebSocketManager } from '../../src/services/WebSocketManager';

// This test requires Django server running for full integration
// Run with: npm run test:integration
describe('WebSocket Integration with Django', () => {
  let manager: WebSocketManager;
  
  beforeEach(() => {
    // Connect to actual Django test server
    manager = new WebSocketManager('ws://localhost:8000/ws/game/', 'test-user');
    manager.connect();
    
    // Wait for connection - might need a more robust check
    return new Promise(resolve => {
      manager.subscribeToConnection(({ connected }) => {
        if (connected) resolve(true);
      });
    });
  });
  
  afterEach(() => {
    manager.disconnect();
  });
  
  test('should receive system welcome message', done => {
    manager.subscribe('system_message', message => {
      expect('content' in message ? message.content : '').toContain('Connected to the Game of Life server');
      done();
    });
  });
  
  test('should send chat message and receive response', done => {
    const responses: any[] = [];
    
    manager.subscribe('chat_message', message => {
      responses.push(message);
      
      // After receiving echo and response
      if (responses.length >= 2) {
        expect(responses[0].is_user).toBe(true);
        expect('content' in responses[0] ? responses[0].content : '').toContain('Test integration message');
        
        expect(responses[1].is_user).toBe(false);
        
        done();
      }
    });
    
    // Send test message
    manager.sendChatMessage('Test integration message');
  }, 5000); // Longer timeout for backend processing
});