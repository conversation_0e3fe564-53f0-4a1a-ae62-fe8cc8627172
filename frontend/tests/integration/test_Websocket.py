# tests/integration/DjangoWebSocketConsumer.test.py
from channels.testing import WebsocketCommunicator
from django.test import TestCase
from config.asgi import application

class WebSocketTests(TestCase):
    async def test_websocket_connection(self):
        communicator = <PERSON>socketCommunicator(application, "/ws/game/")
        connected, _ = await communicator.connect()
        self.assertTrue(connected)
        await communicator.disconnect()
    
    async def test_chat_message(self):
        communicator = WebsocketCommunicator(application, "/ws/game/")
        connected, _ = await communicator.connect()
        self.assertTrue(connected)
        
        # Send a chat message
        await communicator.send_json_to({
            "type": "chat_message",
            "content": {
                "message": "Test message",
                "user_profile_id": "test-user"
            }
        })
        
        # Should receive a message echo and status update
        response = await communicator.receive_json_from()
        self.assertEqual(response["type"], "chat_message")
        self.assertEqual(response["content"], "Test message")
        self.assertTrue(response["is_user"])
        
        # Clean up
        await communicator.disconnect()