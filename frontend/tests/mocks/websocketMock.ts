// tests/mocks/WebSocketMockServer.ts
import { Server } from 'mock-socket';
import { WebSocketManager } from '../../src/services/WebSocketManager';

describe('WebSocket Manager Integration', () => {
  let mockServer: Server;
  let manager: WebSocketManager;
  
  beforeEach(() => {
    // Create a mock WebSocket server
    mockServer = new Server('ws://localhost:8000/ws/test/');
    
    // Configure mock server to respond like Django consumer
    mockServer.on('connection', socket => {
      // Send welcome message
      socket.send(JSON.stringify({
        type: 'system_message',
        content: 'Connected to the Game of Life server. Ready for your journey!'
      }));
      
      // Handle messages
      socket.on('message', data => {
        const message = JSON.parse(data.toString());
        
        // Echo chat messages back with user flag
        if (message.type === 'chat_message') {
          socket.send(JSON.stringify({
            type: 'chat_message',
            content: message.content.message,
            is_user: true
          }));
          
          // Simulate assistant response
          setTimeout(() => {
            socket.send(JSON.stringify({
              type: 'chat_message',
              content: `Response to: ${message.content.message}`,
              is_user: false
            }));
          }, 100);
        }
      });
    });
    
    // Create WebSocket manager to test
    manager = new WebSocketManager('ws://localhost:8000/ws/test/');
  });
  
  afterEach(() => {
    mockServer.close();
  });
  
  test('should receive welcome message on connect', done => {
    manager.subscribe('system_message', message => {
      expect('content' in message ? message.content : '').toBe('Connected to the Game of Life server. Ready for your journey!');
      done();
    });
    
    manager.connect();
  });
  
  test('should send and receive chat messages', done => {
    const receivedMessages: any[] = [];
    
    manager.subscribe('chat_message', message => {
      receivedMessages.push(message);
      
      if (receivedMessages.length === 2) {
        // First message should be echo
        expect('content' in receivedMessages[0] ? receivedMessages[0].content : '').toBe('Hello world');
        expect(receivedMessages[0].is_user).toBe(true);

        // Second message should be response
        expect('content' in receivedMessages[1] ? receivedMessages[1].content : '').toBe('Response to: Hello world');
        expect(receivedMessages[1].is_user).toBe(false);

        done();
      }
    });
    
    manager.connect();
    
    // Give time for connection to establish
    setTimeout(() => {
      manager.sendChatMessage('Hello world');
    }, 100);
  });
});