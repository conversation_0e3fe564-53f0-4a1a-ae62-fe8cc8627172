/**
 * EnhancedWebSocketMock.ts
 * 
 * This file provides a more sophisticated mock of the Django UserSessionConsumer
 * for testing the WebSocketManager without running a real Django server.
 * 
 * It simulates the behavior of the consumer by receiving messages, processing them,
 * and sending back realistic responses according to the API contract.
 */

import { Server } from 'mock-socket';

// Types to match the Django consumer
interface ChatMessageContent {
  message: string;
  user_profile_id: string;
  timestamp?: string;
  metadata?: Record<string, any>;
}

interface SpinResultContent {
  activity_id: string;
  name: string;
  description?: string;
  user_profile_id: string;
}

interface MessageEvent {
  type: string;
  content: any;
}

export class EnhancedWebSocketMock {
  private server: Server;
  private connectionHandlers: Array<() => void> = [];
  private messageHandlers: Array<(message: MessageEvent) => void> = [];
  
  /**
   * Create a mock WebSocket server that mimics the Django consumer
   * 
   * @param url The WebSocket URL to mock
   */
  constructor(url: string) {
    this.server = new Server(url);
    
    // Set up connection handler
    this.server.on('connection', socket => {
      // Send welcome message like the real consumer does
      socket.send(JSON.stringify({
        type: 'system_message',
        content: 'Connected to the Game of Life server. Ready for your journey!'
      }));
      
      // Handle messages
      socket.on('message', data => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(socket, message);
        } catch (error) {
          console.error('Error processing message:', error);
          socket.send(JSON.stringify({
            type: 'error',
            content: 'Error processing your message'
          }));
        }
      });
      
      // Notify connection handlers
      this.connectionHandlers.forEach(handler => handler());
    });
  }
  
  /**
   * Register a handler for new connections
   */
  public onConnection(handler: () => void): void {
    this.connectionHandlers.push(handler);
  }
  
  /**
   * Register a handler for incoming messages
   */
  public onMessage(handler: (message: MessageEvent) => void): void {
    this.messageHandlers.push(handler);
  }
  
  /**
   * Close the mock server
   */
  public close(): void {
    this.server.close();
  }
  
  /**
   * Process incoming messages and respond like the Django consumer would
   */
  private handleMessage(socket: any, message: MessageEvent): void {
    // Notify message handlers
    this.messageHandlers.forEach(handler => handler(message));
    
    // Process based on message type
    switch (message.type) {
      case 'chat_message':
        this.handleChatMessage(socket, message.content);
        break;
        
      case 'spin_result':
        this.handleSpinResult(socket, message.content);
        break;
        
      case 'workflow_status_request':
        this.handleWorkflowStatusRequest(socket, message.content);
        break;
        
      default:
        socket.send(JSON.stringify({
          type: 'error',
          content: `Unknown message type: ${message.type}`
        }));
    }
  }
  
  /**
   * Handle chat messages
   */
  private handleChatMessage(socket: any, content: ChatMessageContent): void {
    // Echo back the message as an acknowledgment (like real consumer)
    socket.send(JSON.stringify({
      type: 'chat_message',
      content: content.message,
      is_user: true
    }));
    
    // Send processing status
    socket.send(JSON.stringify({
      type: 'processing_status',
      status: 'processing'
    }));
    
    // Simulate agent thinking time
    setTimeout(() => {
      // Send back a simulated response
      socket.send(JSON.stringify({
        type: 'chat_message',
        content: `I received your message: "${content.message}". How can I help you further?`,
        is_user: false
      }));
      
      // Processing complete
      socket.send(JSON.stringify({
        type: 'processing_status',
        status: 'completed'
      }));
    }, 300);
  }
  
  /**
   * Handle spin result messages
   */
  private handleSpinResult(socket: any, content: SpinResultContent): void {
    // Echo back confirmation
    socket.send(JSON.stringify({
      type: 'chat_message',
      content: `You selected the activity: ${content.name}`,
      is_user: false
    }));
    
    // If there's a description, send it too
    if (content.description) {
      setTimeout(() => {
        socket.send(JSON.stringify({
          type: 'chat_message',
          content: `About this activity: ${content.description}`,
          is_user: false
        }));
      }, 200);
    }
  }
  
  /**
   * Handle workflow status requests
   */
  private handleWorkflowStatusRequest(socket: any, content: any): void {
    socket.send(JSON.stringify({
      type: 'workflow_status',
      workflow_id: content.workflow_id,
      status: 'completed'  // Always pretend workflow is completed for simplicity
    }));
  }
}

/**
 * Helper function to create a mock consumer for testing
 */
export function createMockConsumer(url: string): EnhancedWebSocketMock {
  return new EnhancedWebSocketMock(url);
}