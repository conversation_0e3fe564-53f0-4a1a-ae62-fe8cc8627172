// tests/unit/services/WebSocketManager.test.ts
import { WebSocketManager } from '../../../src/services/WebSocketManager';
import WS from 'jest-websocket-mock';

describe('WebSocketManager', () => {
  let ws: WS;
  let manager: WebSocketManager;
  
  beforeEach(async () => {
    // Create mock WebSocket server
    ws = new WS('ws://localhost:1234');
    
    // Initialize WebSocketManager with mock server URL
    manager = new WebSocketManager('ws://localhost:1234', 'test-user');
    
    // Connect and wait for connection
    manager.connect();
    await ws.connected;
  });
  
  afterEach(() => {
    WS.clean();
  });
  
  test('should send correctly formatted chat message', async () => {
    // Set up a spy to check what gets sent
    const spy = jest.spyOn(manager['socket'] as WebSocket, 'send');
    
    // Send a chat message
    manager.sendChatMessage('Hello world');
    
    // Get the sent message
    const sentMessage = JSON.parse(spy.mock.calls[0][0] as string);
    
    // Verify message format matches API contract
    expect(sentMessage).toEqual({
      type: 'chat_message',
      content: {
        message: 'Hello world',
        user_profile_id: 'test-user',
        timestamp: expect.any(String),
        metadata: {}  // Include empty metadata object
      }
    });
  });
  
  test('should handle incoming wheel data correctly', async () => {
    // Set up message handler spy
    const wheelDataHandler = jest.fn();
    manager.subscribe('wheel_data', wheelDataHandler);
    
    // Send mock wheel data from server
    const mockWheelData = {
      type: 'wheel_data',
      wheel: {
        name: 'Test Wheel',
        items: [
          {
            id: 'item1',
            name: 'Activity 1',
            description: 'Description 1',
            percentage: 50,
            color: '#ff0000',
            domain: 'creative',
            base_challenge_rating: 30,
            activity_tailored_id: 'act1'
          }
        ]
      }
    };
    
    ws.send(JSON.stringify(mockWheelData));
    
    // Verify handler was called with correct data
    expect(wheelDataHandler).toHaveBeenCalledWith(mockWheelData);
  });
  
  test('should queue messages when disconnected', async () => {
    // Close the connection
    ws.close();
    
    // Send message while disconnected
    manager.sendChatMessage('Offline message');
    
    // Check that message was queued
    expect(manager['messageQueue'].length).toBe(1);
    expect('message' in manager['messageQueue'][0].content ? manager['messageQueue'][0].content.message : undefined).toBe('Offline message');
    
    // Create new server
    const newWs = new WS('ws://localhost:1234');
    
    // Wait for reconnection
    await newWs.connected;
    
    // Verify queued message was sent
    await expect(newWs).toReceiveMessage(expect.stringContaining('Offline message'));
  });
});