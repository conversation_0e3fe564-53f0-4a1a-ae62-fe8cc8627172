# Quick Start Guide for WebSocket Testing

This guide will help you quickly get the WebSocket tests running in the next 30 minutes to verify the connection between the WebSocketManager and Django Consumer.

## 1. Setup & Run Unit Tests (5 minutes)

Unit tests don't require the Django backend to be running, making them ideal for testing the WebSocketManager in isolation:

```bash
# Make sure you're in the frontend directory
cd /path/to/game-of-life/frontend

# Run the unit tests
npm test
```

The tests in `tests/unit/services/WebSocketManager.test.ts` verify:
- Message formatting follows the API contract
- WebSocketManager handles incoming messages correctly
- Message queuing works properly during disconnections

## 2. Setup Django Server for Integration Testing (10 minutes)

To test against the actual Django consumer:

```bash
# Navigate to the backend directory
cd ../backend

# Start Django development server
python manage.py runserver
```

Verify the server is running by checking:
- The console output shows "Starting development server at http://127.0.0.1:8000/"
- The WebSocket endpoint is accessible at `ws://localhost:8000/ws/game/`

## 3. Run Integration Tests (5 minutes)

With the Django server running:

```bash
# Go back to the frontend directory
cd ../frontend

# Run integration tests
npm run test:integration
```

These tests in `tests/integration/WebSocketIntegration.test.ts` check:
- Connection to the actual Django WebSocket endpoint
- Receiving the welcome message from Django
- Sending a chat message and getting a proper response

## 4. How It Works (Technical Explanation)

### Testing Architecture

1. **Unit Tests**: Use a mocked WebSocket server to simulate the Django backend:
   - `jest-websocket-mock` creates a mock server that behaves like Django's UserSessionConsumer
   - Tests can send/receive messages and verify format without Django running

2. **Integration Tests**: Connect to the real Django WebSocket endpoint:
   - Tests establish a real WebSocket connection
   - All messages flow through the complete system (frontend → Django → frontend)
   - Validates the entire communication pipeline

### What We're Testing

1. **Message Format Compliance**: 
   - Outgoing messages match the API contract exactly
   - Incoming messages are properly deserialized and handled

2. **Connection Management**:
   - Connection is established correctly
   - Reconnection works after disconnection
   - Message queuing functions during disconnection

3. **Event Handling**:
   - Subscribed handlers receive messages
   - Specific message types route to correct handlers

## 5. Troubleshooting Common Issues

- **Connection Failure**: Make sure Django is running and WebSocket URL is correct
- **Test Timeouts**: Increase timeout values in tests for slower responses
- **Path Issues**: Ensure import paths match your project structure

## Next Steps

- Modify the tests to cover more message types (wheel_data, workflow_status)
- Add tests for the WebSocketContext component
- Set up end-to-end tests with a full React component
