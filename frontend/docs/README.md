# Game of Life Frontend

This is the frontend application for the Game of Life coaching system, an interactive web application built with React and TypeScript that communicates with the Django Channels backend via WebSockets.

## Architecture

The frontend follows a component-based architecture using **React** and **TypeScript**. Key architectural aspects include:

-   **Component Hierarchy:** The application is structured as a tree of reusable components, starting from the root `App` component.
-   **Webpack:** Used as the module bundler to compile, bundle, and serve the application assets.
-   **Context API:** Utilized for managing global state, particularly for the WebSocket connection (`WebSocketContext`), user information (`UserContext`), and debugging (`DebugContext`).
-   **Custom Hooks:** Logic related to specific features (like the wheel and chat) is encapsulated in custom hooks (`useWheel`, `useChatMessages`) to provide a clean and reusable interface for components.
-   **WebSocket Communication:** Handled by a dedicated `WebSocketManager` service, which is exposed through the `WebSocketContext`. This manager handles connection lifecycle, message sending/receiving, and event routing. A mock WebSocket implementation is available for development and testing.

## Project Structure

The main application code resides in the `src` directory, organized as follows:

-   `src/`: Root directory for the application source code.
    -   `App.tsx`: The main application component, responsible for setting up context providers and the main layout.
    -   `index.tsx`: The application's entry point, rendering the `App` component into the DOM.
    -   `components/`: Contains reusable UI components.
        -   `activities/`: Components related to displaying activities (e.g., `ActivitiesPanel`, `ActivityItem`).
        -   `chat/`: Components for the chat interface (e.g., `ChatContainer`, `ChatMessage`, `TypingIndicator`).
        -   `debug/`: Components for debugging tools (e.g., `DebugConsole`).
        -   `layout/`: Components for the main application layout (e.g., `GameContainer`, `UserHeader`, `ConnectionStatus`).
        -   `wheel/`: Components for the interactive activity wheel (e.g., `WheelContainer`, `WheelSegment`).
    -   `config/`: Configuration files.
        -   `environment.ts`: Defines environment-specific settings (WebSocket URL, mock WebSocket flag, logging).
        -   `constants.ts`: Contains application-wide constants.
        -   `settings.ts`: Other application settings.
    -   `contexts/`: React Context providers.
        -   `DebugContext.tsx`: Provides debugging context.
        -   `UserContext.tsx`: Provides user information context.
        -   `WebSocketContext.tsx`: Provides WebSocket manager and connection state context.
    -   `hooks/`: Custom React hooks encapsulating feature logic.
        -   `useActivity.ts`: Hook for activity-related logic (likely used by `ActivityItem`).
        -   `useChatMessages.ts`: Hook for managing chat messages and interactions.
        -   `useWheel.ts`: Hook for managing wheel data, animation, and interactions.
    -   `services/`: Contains service classes.
        -   `WebSocketManager.ts`: Handles the direct WebSocket connection and message routing.
    -   `styles/`: Contains CSS files.
        -   `global.css`: Global application styles.
    -   `types/`: TypeScript type definitions.
        -   `api.ts`: Defines the structure of WebSocket messages (client-to-server and server-to-client).
        -   `models.ts`: Defines data models used in the frontend.
    -   `utils/`: Utility functions and components.
        -   `apiUtils.ts`: Utility functions for API interactions.
        -   `dateUtils.ts`: Utility functions for date manipulation.
        -   `Logger.ts`: Logging utility.
        -   `MockWebSocket.ts`: Mock WebSocket implementation and factory function.
        -   `mockWebSocketProvider.ts`: (Likely related to testing with mock WebSocket)
        -   `MockWebSocketProvider.tsx`: (Likely a component for providing mock WebSocket in tests)
        -   `README_MOCK_USAGE.md`: Documentation for mock WebSocket usage.
        -   `WebSocketProviderSelector.tsx`: Component to select between real and mock WebSocket providers.

-   `public/`: Contains static assets and the main HTML file (`index.html`).
-   `tests/`: Contains test files, organized by type (unit, integration, end-to-end).
    -   `end2end/`: End-to-end tests (using Cypress).
    -   `integration/`: Integration tests (e.g., testing WebSocket integration).
    -   `mocks/`: Mock implementations for testing (e.g., `websocketMock.ts`).
    -   `unit/`: Unit tests for individual components, hooks, and services.

## Core Concepts

### WebSocket Communication

The frontend communicates with the backend primarily through WebSockets.

-   The `WebSocketManager` class (`src/services/WebSocketManager.ts`) is responsible for establishing and managing the WebSocket connection. It handles sending messages to the backend and receiving messages, routing incoming messages to registered handlers.
-   The `WebSocketContext` (`src/contexts/WebSocketContext.tsx`) provides the `WebSocketManager` instance and the current connection state to the rest of the application via the `useWebSocket` hook. It also manages message histories and workflow state.
-   The `WebSocketProviderSelector` (`src/utils/WebSocketProviderSelector.tsx`) is used at the application root to conditionally use either a real `WebSocket` or a `MockWebSocket` based on the `ENV.USE_MOCK_WS` flag in `src/config/environment.ts`.
-   Message structures are defined in `src/types/api.ts`, ensuring type safety for WebSocket communication. These types align with the `docs/api/ApiContract.md`.

### State Management

React's Context API and custom hooks are used for state management:

-   **Contexts:**
    -   `WebSocketContext`: Manages WebSocket connection state, message histories, and workflow status.
    -   `UserContext`: Manages user-related information (used by `App.tsx` to determine staff status).
    -   `DebugContext`: Manages debugging information received from the backend.
-   **Custom Hooks:**
    -   `useWebSocket`: Provides access to the `WebSocketContext` state and methods.
    -   `useWheel`: Abstracts the logic for the activity wheel, including fetching data via WebSocket, handling spin animation, and reporting the selected activity.
    -   `useChatMessages`: Abstracts the logic for the chat interface, managing message display, sending messages via WebSocket, and handling typing indicators.

### Component Structure

The UI is composed of modular React components:

-   `App`: The root component, setting up context providers and rendering the main layout.
-   `GameContainer`: The main layout component, structuring the page into sections for activities, the wheel, and the chat.
-   Feature-specific containers (`ActivitiesPanel`, `WheelContainer`, `ChatContainer`) use custom hooks to interact with the WebSocket context and manage their respective UI logic.
-   Smaller presentational components (e.g., `ActivityItem`, `ChatMessage`, `WheelSegment`) are responsible for rendering specific pieces of the UI based on props.

## Getting Started

### Prerequisites

-   Node.js 16+
-   npm or yarn

### Installation

1.  Clone the repository.
2.  Navigate to the `frontend` directory: `cd frontend`
3.  Install dependencies: `npm install` (or `yarn install`)

### Configuration

Environment-specific settings are managed in `src/config/environment.ts`. You can adjust the `USE_MOCK_WS` flag here to switch between using the real backend WebSocket and the mock implementation.

### Running the Development Server

To start the local development server with hot reloading:

```bash
npm start
```

This will typically serve the application at `http://localhost:3000`.

### Building for Production

To create a production-ready build in the `dist/` directory:

```bash
npm run build
```

For a development build (useful for debugging the built output):

```bash
npm run build:dev
```

## Testing

The frontend uses **Jest** for unit and integration testing and **Cypress** for end-to-end testing.

-   **Unit/Integration Tests (Jest):**
    -   `npm test`: Run all Jest tests.
    -   `npm run test:watch`: Run Jest tests in watch mode (reruns tests when files change).
    -   `npm run test:coverage`: Run Jest tests and generate a coverage report.
    -   `npm run test:integration`: Run specific integration tests (e.g., `WebSocketDjangoIntegration`).
    -   `npm run test:mock`: Run tests specifically using the mock WebSocket (`MockConsumerTest.test.tsx`).
    -   The mock WebSocket (`src/utils/MockWebSocket.ts`) is crucial for testing WebSocket interactions without a running backend.

-   **End-to-End Tests (Cypress):**
    -   Cypress tests are located in the `tests/end2end/` directory.
    -   Refer to the Cypress documentation and any specific guides in the `tests/end2end/` folder for running these tests.

## Linting and Formatting

The project uses **ESLint** with configurations for recommended JavaScript, React, and TypeScript rules, as defined in `.eslintrc.js`. This helps maintain code quality and consistency.

-   Explicit function return types are enforced.
-   Warnings are provided for missing dependencies in React hooks (`react-hooks/exhaustive-deps`).
-   A naming convention for custom hooks (must start with `use`) is enforced.

Refer to the `.eslintrc.js` file for the complete set of linting rules.

## Mobile Shell Integration (GoaliMobileShell)

The `GoaliMobileShell` directory contains the code for the mobile application shell (likely using Expo). This shell is intended to embed the web frontend (this project) using a WebView, potentially adding native features as needed. Tasks related to the mobile shell are tracked in `frontend/TASK.md`.

## Further Documentation

-   [`docs/api/ApiContract.md`](../api/ApiContract.md): Detailed specification of the WebSocket message contract.
-   [`frontend/README_WEBSOCKET_IMPLEMENTATION.md`](./README_WEBSOCKET_IMPLEMENTATION.md): Specific notes or details about the WebSocket implementation.
-   [`frontend/utils/README_MOCK_USAGE.md`](./utils/README_MOCK_USAGE.md): Documentation specific to using the mock WebSocket.
-   Explore other `.md` files within the `frontend/docs/` and `frontend/tests/` directories for more specific documentation and testing details.
