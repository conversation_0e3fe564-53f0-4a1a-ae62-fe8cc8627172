# WebSocket Mocking in Game of Life Frontend

This document explains the WebSocket mocking architecture implemented in the Game of Life frontend application.

## Overview

The application uses a hybrid approach to WebSocket mocking, where:

1. We always use the real `WebSocketProvider` and `WebSocketManager` components
2. We selectively replace the browser's native `WebSocket` with a mock implementation
3. This allows for realistic testing without requiring a backend connection

## Architecture

### Components

The WebSocket architecture consists of the following components:

1. **WebSocketContext** (`src/contexts/WebSocketContext.tsx`)
   - Provides a React context for WebSocket state and operations
   - Used by components to access WebSocket functionality

2. **WebSocketManager** (`src/services/WebSocketManager.ts`)
   - Manages the WebSocket connection and message handling
   - Handles reconnection, message queuing, and event routing
   - Uses either a real or mock WebSocket based on configuration

3. **MockWebSocket** (`src/utils/MockWebSocket.ts`)
   - Implements the browser's WebSocket API
   - Simulates WebSocket behavior without making actual network connections
   - Provides predefined responses for testing

4. **WebSocketProviderSelector** (`src/utils/WebSocketProviderSelector.tsx`)
   - Configures the WebSocketProvider based on environment settings
   - Always uses the real WebSocketProvider with the hybrid approach

### Data Flow

1. **Component Initialization**
   - `App` component wraps its children with `WebSocketProviderSelector`
   - `WebSocketProviderSelector` configures and renders `WebSocketProvider`
   - `WebSocketProvider` creates a `WebSocketManager` instance

2. **Connection Establishment**
   - `WebSocketManager.connect()` is called
   - Based on `ENV.USE_MOCK_WS`, either a real or mock WebSocket is created
   - Connection events are routed to subscribers

3. **Message Handling**
   - Outgoing messages are serialized and sent through the WebSocket
   - Incoming messages are parsed and routed to appropriate handlers
   - With mock WebSocket, predefined responses are generated

4. **Component Integration**
   - Components use the `useWebSocket` hook to access WebSocket functionality
   - The hook provides access to connection state, messages, and methods
   - Components are unaware of whether a real or mock WebSocket is being used

## Mock Implementation Details

### MockWebSocket Class

The `MockWebSocket` class implements the browser's WebSocket API, including:

- Standard WebSocket properties (`readyState`, `binaryType`, etc.)
- Event handlers (`onopen`, `onmessage`, `onclose`, `onerror`)
- Methods (`send`, `close`, `addEventListener`, `removeEventListener`)

### Mock Data

The mock implementation provides predefined data for testing:

1. **Wheel Data**
   - Sample activities with names, descriptions, and properties
   - Color coding for visual representation
   - Percentage values for wheel segment sizing

2. **Workflow States**
   - Pre-spin state for initial wheel display
   - Post-spin state for activity selection
   - Other workflow states as needed

3. **Chat Messages**
   - System messages for connection events
   - Assistant responses based on user input
   - Activity-specific guidance

### Message Processing

When a message is sent through the mock WebSocket:

1. The message is parsed and its type is determined
2. Based on the message type, an appropriate handler is called
3. The handler generates a response and simulates a delay
4. The response is sent back through the mock WebSocket's event system

## Configuration

The WebSocket mocking behavior is controlled through environment settings in `src/config/environment.ts`:

```typescript
export const ENV = {
  // Other settings...
  
  // WebSocket settings
  // When true, the application will use a mock WebSocket implementation
  USE_MOCK_WS: true,
  
  // Logging settings
  VERBOSE_LOGGING: true,
};
```

## Logging

The implementation includes comprehensive logging to help with debugging:

1. **Connection Events**
   - Connection attempts
   - Successful connections
   - Connection failures
   - Reconnection attempts

2. **Message Flow**
   - Outgoing messages with full payload
   - Incoming messages with full payload
   - Message processing steps
   - State updates resulting from messages

3. **Component Rendering**
   - Component mounting/unmounting
   - Props and state changes
   - Effect triggers
   - Context value updates

## Benefits of the Hybrid Approach

1. **Code Reuse**
   - Uses the same WebSocketProvider and WebSocketManager for both real and mock modes
   - Reduces duplication and maintenance overhead

2. **Realistic Testing**
   - Tests the actual WebSocketProvider implementation
   - Ensures the same code paths are used in testing and production

3. **Seamless Switching**
   - Easy to switch between real and mock modes with a single configuration change
   - No code changes required to switch modes

4. **Improved Debugging**
   - Detailed logging throughout the system
   - Clear separation of concerns makes issues easier to isolate

## Usage Examples

### Using the WebSocket in Components

```tsx
import { useWebSocket } from '../contexts/WebSocketContext';

const MyComponent = () => {
  const { 
    isConnected, 
    sendChatMessage, 
    chatMessages 
  } = useWebSocket();
  
  const handleSend = () => {
    sendChatMessage('Hello, world!');
  };
  
  return (
    <div>
      <button onClick={handleSend} disabled={!isConnected}>
        Send Message
      </button>
      <div>
        {chatMessages.map(msg => (
          <div key={msg.id}>{msg.data.content}</div>
        ))}
      </div>
    </div>
  );
};
```

### Testing with Mock WebSocket

1. Set `USE_MOCK_WS: true` in `environment.ts`
2. Run the application normally
3. The application will use the mock WebSocket implementation
4. Predefined responses will be generated for testing

### Using with Real Backend

1. Set `USE_MOCK_WS: false` in `environment.ts`
2. Ensure the backend is running and accessible
3. Run the application normally
4. The application will connect to the real backend

## Troubleshooting

### Common Issues

1. **Blank Screen / No Rendering**
   - Check browser console for errors
   - Verify that WebSocketContext is properly initialized
   - Ensure components are correctly using the useWebSocket hook

2. **No Connection**
   - Check if the WebSocket URL is correct
   - Verify that the backend is running (if using real WebSocket)
   - Check for network issues or CORS restrictions

3. **No Mock Data**
   - Verify that `USE_MOCK_WS` is set to `true`
   - Check console logs for mock WebSocket initialization
   - Ensure mock data is properly defined in MockWebSocket.ts

### Debugging Tips

1. Enable verbose logging in environment.ts
2. Check browser console for detailed logs
3. Use browser developer tools to inspect WebSocket traffic
4. Add temporary console.logs to specific components for targeted debugging
