# Frontend WebSocket Architecture

This document provides a comprehensive overview of the WebSocket implementation in the Game of Life frontend application, including architecture, the hybrid mocking system, configuration, and usage patterns.

## 1. Overview

The Game of Life frontend uses WebSockets for real-time, bidirectional communication with the Django backend. This enables features like live chat with AI agents, dynamic activity wheel updates, and real-time status notifications.

## 2. Core Architecture Components

The WebSocket system is built around these key components:

1.  **`WebSocketContext`** (`src/contexts/WebSocketContext.tsx`):
    *   Provides a React context exposing WebSocket state (connection status, messages) and operations (sending messages) to the component tree.
    *   Components access this context via the `useWebSocket` hook.

2.  **`WebSocketManager`** (`src/services/WebSocketManager.ts`):
    *   The core service responsible for managing the WebSocket lifecycle: establishing connections, handling disconnections, and managing automatic reconnection logic (with exponential backoff).
    *   Handles sending outgoing messages (serializing them to JSON) and receiving incoming messages (parsing JSON and routing to appropriate handlers or updating context state).
    *   Implements message queuing to hold outgoing messages when the connection is temporarily unavailable.
    *   Instantiates either a real browser `WebSocket` or the `MockWebSocket` based on environment configuration, enabling the hybrid mocking approach.

3.  **`MockWebSocket`** (`src/utils/MockWebSocket.ts`):
    *   A custom class implementing the standard browser `WebSocket` API (`readyState`, `send`, `close`, `onopen`, `onmessage`, etc.).
    *   Simulates the behavior of the backend WebSocket server locally within the browser, without requiring a network connection.
    *   Generates predefined or dynamically crafted responses based on the messages it receives, mimicking the backend logic for development and testing purposes.
    *   Used by `WebSocketManager` when `ENV.USE_MOCK_WS` is true.

4.  **`WebSocketProviderSelector`** (`src/utils/WebSocketProviderSelector.tsx`):
    *   A wrapper component (though the current implementation might directly use `WebSocketProvider` and rely on `WebSocketManager`'s internal switching) that ensures the correct `WebSocketProvider` (which uses `WebSocketManager`) is configured based on environment settings. *Note: The hybrid approach primarily relies on `WebSocketManager` choosing the real or mock socket internally.*

## 3. Data Flow

1.  **Initialization:** The main `App` component wraps the application with `WebSocketProvider` (potentially via `WebSocketProviderSelector`).
2.  **Connection:** `WebSocketProvider` creates a `WebSocketManager` instance, which attempts to connect using either a real or mock `WebSocket` based on `ENV.USE_MOCK_WS`.
3.  **Component Interaction:** Components use the `useWebSocket` hook to get connection status, subscribe to incoming message types, and call methods like `sendChatMessage` or `sendSpinResult`.
4.  **Sending Messages:** `WebSocketManager` serializes the message object to JSON and sends it via the active (real or mock) WebSocket.
5.  **Receiving Messages:** When a message arrives, the `WebSocketManager` parses the JSON, identifies the `type`, and updates the relevant state in the `WebSocketContext` (e.g., adding to `chatMessages` or updating `wheelData`) or triggers specific event handlers.

## 4. Message Format

All messages exchanged via WebSocket follow a standard JSON structure:

```json
{
  "type": "message_type_string",
  "content": { ... } // Object containing message-specific data
}
```

-   **`type`**: A string identifying the kind of message (e.g., `chat_message`, `wheel_data`, `spin_result`).
-   **`content`**: An object containing the payload specific to the message type.

**The definitive source for all message types and their `content` schemas is [`../../docs/ApiContract.md`](../../docs/ApiContract.md).** Refer to that document for detailed specifications.

## 5. Hybrid Mocking System

This system allows developers to work on the frontend without a running backend, while still using the main `WebSocketManager` and `WebSocketContext` code paths.

-   **Activation:** Controlled by the `USE_MOCK_WS` flag in `src/config/environment.ts`.
-   **Mechanism:** When `USE_MOCK_WS` is `true`, `WebSocketManager` instantiates `MockWebSocket` instead of the browser's native `WebSocket`. `MockWebSocket` intercepts outgoing messages and generates simulated responses.
-   **Benefits:**
    *   **Backend Independence:** Develop and test UI features without backend delays or availability issues.
    *   **Realistic Testing:** Exercises the actual `WebSocketManager` and `WebSocketContext` logic.
    *   **Code Reuse:** Minimizes separate code paths for mocked vs. real scenarios.
    *   **Easy Switching:** Toggle between mock and real backend via the environment flag.
-   **Usage:** See `src/utils/README_MOCK_USAGE.md` for detailed instructions on how developers can leverage the mock system.

## 6. Configuration

Key configuration options are managed in `src/config/environment.ts`:

```typescript
export const ENV = {
  NODE_ENV: process.env.NODE_ENV || 'development',

  // If true, WebSocketManager uses MockWebSocket instead of a real connection.
  USE_MOCK_WS: true, // Set to false to connect to the real backend

  // Enables detailed console logging for debugging WebSocket events and data flow.
  VERBOSE_LOGGING: true,
};
```

## 7. Logging

Comprehensive logging is implemented throughout the WebSocket system, controlled by `ENV.VERBOSE_LOGGING`. When enabled, logs include:

-   Connection lifecycle events (attempts, success, failure, reconnection).
-   Outgoing message payloads.
-   Incoming message payloads.
-   Message processing steps.
-   State updates within the context.

Check the browser's developer console for these logs when debugging.

## 8. Usage in Components

Components interact with the WebSocket system via the `useWebSocket` hook provided by `WebSocketContext`.

```tsx
import React from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';

const ChatInput: React.FC = () => {
  const { isConnected, sendChatMessage } = useWebSocket();
  const [message, setMessage] = React.useState('');

  const handleSend = () => {
    if (message.trim() && isConnected) {
      sendChatMessage(message);
      setMessage('');
    }
  };

  return (
    <div>
      <input
        type="text"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        disabled={!isConnected}
      />
      <button onClick={handleSend} disabled={!isConnected || !message.trim()}>
        Send
      </button>
      {!isConnected && <span>Connecting...</span>}
    </div>
  );
};
```

## 9. Troubleshooting

-   **No Connection / Connection Refused:**
    *   If using real backend (`USE_MOCK_WS: false`): Verify the backend server is running and accessible at the correct URL (`ws://localhost:8000/ws/game/`). Check for backend errors or firewall issues.
    *   Check the WebSocket URL configured in the frontend.
    *   Look for errors in the browser console and backend logs.
-   **Messages Not Sending/Receiving:**
    *   Ensure `isConnected` is true before sending.
    *   Verify message format matches `ApiContract.md`.
    *   Check browser console logs (enable `VERBOSE_LOGGING`) to trace message flow.
    *   If using mock (`USE_MOCK_WS: true`), ensure `MockWebSocket` handles the specific message type.
-   **UI Not Updating:**
    *   Verify the component is correctly using `useWebSocket` and subscribed to the relevant context state (e.g., `chatMessages`, `wheelData`).
    *   Check for React rendering issues or errors in component logic.

## 10. Security Considerations

-   **Transport Security:** Always use `wss://` (WebSocket Secure) in production environments.
-   **Input Validation:** The backend (`UserSessionConsumer`) is responsible for validating all incoming message `content`.
-   **Authentication:** User identification (e.g., `user_profile_id`) should be securely handled and verified by the backend for relevant messages.
