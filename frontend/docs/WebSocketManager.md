# WebSocket Manager and Consumer Integration

## Architecture Overview

The WebSocket Manager provides a standardized interface for WebSocket communication between the React frontend and Django Channels backend. This document outlines the communication protocol, message formats, and integration patterns between the WebSocket Manager and the `UserSessionConsumer` backend component.


## Connection Establishment

The WebSocket connection is established through a standard WebSocket URL:

- Development: `ws://localhost:8000/ws/game/`
- Production: `wss://yourdomain.com/ws/game/`

When a connection is established:
1. The client initiates the WebSocket connection
2. The server accepts and adds the client to a user-specific group
3. The server sends a welcome message
4. Regular communication begins

## Message Format

All WebSocket messages follow a standardized JSON format:

```json
{
  "type": "message_type",
  "content": {
    // Type-specific content fields
  }
}
```

The `type` field determines how the message should be processed, while the `content` field contains type-specific data.

## Message Types and Schemas

### Server to Client Messages

#### 1. System Message

Used for system notifications and status updates.

```json
{
  "type": "system_message",
  "content": "Connected to the Game of Life server. Ready for your journey!"
}
```

#### 2. Chat Message

Messages from the agent or system to the user.

```json
{
  "type": "chat_message",
  "content": "I understand you're looking for a creative activity today.",
  "is_user": false
}
```

#### 3. Processing Status

Indicates that the system is processing a request.

```json
{
  "type": "processing_status",
  "status": "processing|completed|error"
}
```

#### 4. Wheel Data

Provides data for the activity wheel.

```json
{
  "type": "wheel_data",
  "wheel": {
    "items": [
      {
        "id": "act-123",
        "name": "Mindful Drawing",
        "description": "A 15-minute drawing exercise focusing on mindfulness",
        "percentage": 15,
        "color": "#66BB6A",
        "domain": "creative"
      },
      // Additional wheel items
    ]
  }
}
```

#### 5. Error Message

Indicates an error condition.

```json
{
  "type": "error",
  "content": "Error message details"
}
```

### Client to Server Messages

#### 1. Chat Message

Messages from the user to the system.

```json
{
  "type": "chat_message",
  "content": {
    "message": "I want to try something creative today",
    "user_profile_id": "user-123"
  }
}
```

#### 2. Spin Result

The result of a wheel spin/activity selection.

```json
{
  "type": "spin_result",
  "content": {
    "activity_id": "act-123",
    "name": "Mindful Drawing",
    "user_profile_id": "user-123"
  }
}
```

## Message Processing Flow

### Server-Side Flow (UserSessionConsumer)

1. **Receive Message**: `receive()` method processes incoming WebSocket messages
2. **Route Message**: Based on `type`, the message is routed to the appropriate handler
3. **Process**: The handler performs necessary business logic and database operations
4. **Response**: Responses are sent back to the client via the group send mechanism

### Client-Side Flow (WebSocket Manager)

1. **Send Message**: Client calls WebSocket Manager's send method with appropriate type and content
2. **Serialize**: The data is converted to a JSON string
3. **Transmit**: The JSON string is sent through the WebSocket connection
4. **Receive Response**: The server's response is received and passed to registered handlers

## Authentication

Messages that require user identification should include the `user_profile_id` field in the content object. This ID is used by the server to:

1. Associate messages with the correct user
2. Access appropriate user data from the database
3. Apply user-specific business logic

## Error Handling

### Server-Side Errors

When the server encounters an error, it sends an error message:

```json
{
  "type": "error",
  "content": "Error message details"
}
```

### Connection Errors

If the WebSocket connection is lost, the client should:
1. Attempt to reconnect with exponential backoff
2. Notify the user of connection status
3. Queue messages for transmission once connection is reestablished

## Message Handlers on the Server

The `UserSessionConsumer` defines handlers for each message type:

- `handle_chat_message`: Processes user chat messages
- `handle_spin_result`: Processes activity selections
- `handle_workflow_status_request`: Handles workflow status queries

## Channel Group Communication

The `UserSessionConsumer` adds each client to a user-specific group:

```python
self.user_ws_session_name = 'client_session_' + str(uuid.uuid4())
await self.channel_layer.group_add(
    self.user_ws_session_name,
    self.channel_name
)
```

Responses are sent to this group:

```python
await self.channel_layer.group_send(
    self.user_ws_session_name,
    {
        'type': 'chat_message',
        'user_message': message,
        'is_user': False
    }
)
```

## Implementation Notes

1. **Connection Persistence**: The WebSocket connection should be maintained for the entire user session
2. **Reconnection Logic**: Implement with exponential backoff and maximum retry limits
3. **Message Validation**: Both client and server should validate message structure
4. **Consistent Formatting**: Adhere strictly to the defined message formats
5. **Error Handling**: Properly handle all error conditions with appropriate user feedback

## Testing the Integration

To test the integration between WebSocket Manager and UserSessionConsumer:

1. Use the browser's developer tools to monitor WebSocket traffic
2. Implement logging on both client and server sides
3. Create a test harness to verify all message types are properly processed
4. Simulate connection failures to test reconnection logic

## Security Considerations

1. **Data Validation**: Always validate incoming message data
2. **Authentication**: Ensure proper user authentication before processing messages
3. **Rate Limiting**: Implement rate limiting to prevent abuse
4. **Transport Security**: Use WSS (WebSocket Secure) in production

This integration provides a robust, real-time communication channel for the Game of Life application, enabling immediate feedback and responsive interactions for users.