- atlas project id is "proj_e09ee70626754d2886dfb9553c21378b"
- when looking for contextual knowledge, always use "atlas_knowledge_list" tool with relevant filters
- when adding a new knowledge item, use "atlas_knowledge_add"
- before adding a new knowledge item, look if the knowledge already exist using relevant keywords
- if you find out an atlas knowledge item is outdated, delete it using "atlas_knowledge_delete"
- if you find out atlas knowledge items contain duplicated data, delete the items and create a new one with the most relevant data and tags