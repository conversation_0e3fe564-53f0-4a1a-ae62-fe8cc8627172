# Context Documentation

## Recent Fixes: Benchmark Management Display and Grafana Dashboard Issues

### Issue Summary
Two critical issues were identified and resolved:

1. **Benchmark Management Table Display**: Context variables (Valence, Arousal, Stress Level, Time Pressure, Token Usage, Cost) were showing as "N/A" instead of actual values
2. **Grafana Dashboard SQL Errors**: SceneVariableSet errors due to missing columns in database views

### Root Cause Analysis

#### Issue 1: Context Variables Display
**Problem**: The benchmark run object contained context variables in a flat structure at the top level, but the JavaScript extraction logic was expecting them to be nested under `mood` and `environment` objects.

**Data Structure Found**:
```json
{
  "context_variables": {
    "arousal": {
      "label": "Calm (-1.0 to 0.0)",
      "range": "-1.0-0.0", 
      "value": -0.8
    },
    "valence": {
      "label": "Negative (-1.0 to 0.0)",
      "range": "-1.0-0.0",
      "value": -0.8
    },
    "trust_level": {
      "label": "Foundation (0-39)",
      "range": "0-39",
      "value": 20
    },
    "stress_level": {
      "label": "Medium (31-70)", 
      "range": "31-70",
      "value": 45
    },
    "time_pressure": {
      "label": "Relaxed (0-30)",
      "range": "0-30", 
      "value": 10
    }
  }
}
```

**Expected Structure** (by original code):
```json
{
  "context_variables": {
    "trust_level": {...},
    "mood": {
      "valence": {...},
      "arousal": {...}
    },
    "environment": {
      "stress_level": {...},
      "time_pressure": {...}
    }
  }
}
```

#### Issue 2: Grafana Dashboard Errors
**Problem**: Database views referenced columns that didn't exist, specifically:
- `prompt_version` column was missing from `grafana_llm_performance` view
- Some dashboard queries referenced non-existent table columns

### Solutions Implemented

#### 1. Backend API Fix (`backend/apps/admin_tools/views.py`)
Updated the context variable extraction logic to handle both flat and nested structures:

```python
# Extract valence - check both nested and direct formats
valence_raw = context_vars.get('valence', 'N/A')
if valence_raw == 'N/A':
    # Try nested format
    mood = context_vars.get('mood', {})
    valence_raw = mood.get('valence', 'N/A') if isinstance(mood, dict) else 'N/A'

if isinstance(valence_raw, dict) and 'value' in valence_raw:
    valence = valence_raw['value']
else:
    valence = valence_raw
```

#### 2. Frontend JavaScript Fix (`backend/static/admin/js/benchmark_management.js`)
Updated the JavaScript extraction logic to check direct properties first, then fall back to nested structure:

```javascript
// Extract valence - check direct first, then nested
if (run.context_variables.valence !== undefined) {
    if (typeof run.context_variables.valence === 'object' && run.context_variables.valence.value !== undefined) {
        valence = parseFloat(run.context_variables.valence.value).toFixed(1);
    } else {
        valence = parseFloat(run.context_variables.valence).toFixed(1);
    }
} else if (run.context_variables.mood && run.context_variables.mood.valence !== undefined) {
    // Fallback to nested format
    // ... nested extraction logic
}
```

#### 3. Database Views Fix
Updated Grafana database views to include missing columns:

```sql
-- Added prompt_version column using agent_version as fallback
COALESCE(br.agent_version, '1.0.0') as prompt_version

-- Enhanced context variable extraction from JSON parameters
CASE
    WHEN (br.parameters->'context_variables'->'trust_level'->>'value') IS NOT NULL
    THEN (br.parameters->'context_variables'->'trust_level'->>'value')::int
    WHEN (br.parameters->'context_variables'->>'trust_level') IS NOT NULL  
    THEN (br.parameters->'context_variables'->>'trust_level')::int
    ELSE NULL
END as trust_level
```

### Key Learnings

1. **Data Structure Flexibility**: The system needs to handle multiple data structure formats for context variables due to evolution of the data model over time.

2. **Robust Extraction Logic**: Both backend and frontend should implement fallback mechanisms to handle different data formats gracefully.

3. **Database View Maintenance**: Grafana views need to be kept in sync with the actual database schema and should include proper fallbacks for missing data.

4. **Testing Strategy**: Always test with real data structures rather than assumed formats, as the actual data may differ from expectations.

### Verification Results

After implementing the fixes:

✅ **Benchmark Management API Test Results**:
- Found 26 benchmark runs
- Context variables correctly extracted:
  - Trust Level: 20 (Foundation (0-39))
  - Valence: -0.8
  - Arousal: -0.8  
  - Stress Level: 45
  - Time Pressure: 10
- Token Usage: 114+68=182
- Cost: $0.000032
- All expected fields present

✅ **Grafana Dashboard Test Results**:
- `prompt_version` column now available
- No more SQL errors in SceneVariableSet
- Dashboard queries executing successfully

### Files Modified

1. `backend/apps/admin_tools/views.py` - Enhanced context variable extraction
2. `backend/static/admin/js/benchmark_management.js` - Updated frontend extraction logic  
3. Database views via SQL commands - Added missing columns and improved JSON extraction

### Future Considerations

1. **Data Model Standardization**: Consider standardizing the context variables data structure to prevent future extraction issues.

2. **Migration Strategy**: When changing data structures, ensure backward compatibility or provide migration scripts.

3. **Automated Testing**: Add automated tests that verify context variable extraction with various data formats.

4. **Documentation**: Keep data structure documentation up-to-date to prevent similar issues.
