# PWA Implementation Plan for Goali Frontend

## 1. Technical Assessment
- **Current State:** The `frontend_enhanced` project is a React 18/TypeScript/Vite/Tailwind application featuring a split-screen UI (using `react-resizable-panels` via `@/components/ui/resizable`) and a canvas-based spinning wheel interface (`@/components/Wheel.tsx`). It currently uses mock WebSocket implementations and a `useSimulatedIntelligence` hook for initial data. The build process is managed by Vite.
- **Browser/Device Compatibility:** Target modern evergreen browsers (Chrome, Firefox, Safari, Edge) on both desktop and mobile (iOS & Android). Ensure core functionality works across these platforms, prioritizing the PWA installation experience on mobile.
- **Performance Considerations:** Mobile performance is paramount. Focus on optimizing initial load time, interaction responsiveness (especially touch gestures and animations), and efficient caching to minimize network dependency. Lighthouse scores will be a key metric. *Insight: Wheel animation was optimized using an offscreen canvas.*
- **Key PWA Features Implemented:**
    - Installability (Add to Home Screen) - *Setup complete. Insight: Initial testing failed due to missing icons; requires HTTPS for testing.*
    - Offline Capability (Core UI and cached data) - *Basic setup with offline page and navigation fallback.*
    - App Manifest (`manifest.json`) - *Generated via `vite.config.ts`. Insight: Added `purpose: 'any'` to 512 icon for robustness.*
    - Service Worker for caching and offline support - *Generated via `vite.config.ts` with `autoUpdate` and basic caching rules.*
    - Mobile-optimized UI/UX (touch targets) - *Global CSS added, resizable handle touch area increased.*
    - Update notifications - *Component created and integrated.*

## 2. Architecture Approach
- **PWA Configuration:** Utilized `vite-plugin-pwa` (installed via `npm`). Integrated into `vite.config.ts`.
- **Manifest Structure:** Defined via the `VitePWA` plugin configuration in `vite.config.ts`. Includes essential properties (`name`, `short_name`, `description`, `theme_color`, `background_color`, `display: 'standalone'`, `orientation: 'portrait'`) and icon references. *Insight: Required icons (`pwa-192x192.png`, `pwa-512x512.png`, `maskable-icon.png`, `apple-touch-icon.png`) were copied from user's Downloads folder into `/public`.*
- **Service Worker Strategy:** Employed Workbox (`generateSW` strategy via `vite-plugin-pwa`). Configured `autoUpdate` registration type. Implemented runtime caching for Google Fonts and a basic image cache rule. Added `navigateFallback: '/index.html'` to support SPA routing offline.
- **Installation Flow:**
    - Listens for the `beforeinstallprompt` event in `InstallPrompt.tsx`.
    - Prevents the default mini-infobar (`e.preventDefault()`).
    - Implemented `InstallPrompt.tsx` component (integrated into `App.tsx`) to show a custom UI.
    - Triggers the stored prompt event on user interaction. *Insight: This event only fires reliably if all PWA criteria (HTTPS, manifest with icons, SW) are met.*
- **Update Flow:** Implemented `PWAUpdater.tsx` component (integrated into `App.tsx`) using `useRegisterSW` hook from `virtual:pwa-register/react`. *Insight: Required adding `/// <reference types="vite-plugin-pwa/client" />` to `vite-env.d.ts` for TypeScript.*

## 3. User Experience Enhancements
- **Touch Optimization:** Added global CSS (`min-h-[44px] min-w-[44px]`) in `index.css`. Increased touch target area for the `ResizableHandle` in `resizable.tsx` using padding. Disabled default tap highlights. Added `-webkit-overflow-scrolling: touch`.
- **Offline Experience:** Created `Offline.tsx` page and added a route in `App.tsx`. `navigateFallback` in service worker configuration directs users to `index.html` for client-side routing to handle offline navigation.
- **Gesture Improvements:** *Lower priority tasks (pull-to-refresh, swipe navigation) not implemented.*
- **Loading and Transition Optimizations:** Optimized wheel spinning animation in `Wheel.tsx` using an offscreen canvas. Added haptic feedback (`navigator.vibrate(50)`) to the `spinWheel` function in `useWheel.tsx`. *Insight: Further optimization like skeleton loaders could be added.*
    - **Data Handling:**
        - Aligned `useSimulatedIntelligence` hook output with the `WheelItem` structure defined in `docs/api/ApiContract.md` to prepare for future backend integration.
        - **Dual-Mode Context Strategy:** Leveraged the `WebSocketProviderSelector` to dynamically provide either the real `WebSocketProvider` or the enhanced `MockWebSocketProvider`. Both providers now supply a context value conforming to the same `WebSocketContextType` interface.
        - **Refactored `useWheel` Hook:** This hook now directly consumes the context provided via `useWebSocket()`. It uses the `isConnected` flag from the context to determine its operational mode:
            - **Standalone/Mock Mode (`isConnected` is false):** Uses `useSimulatedIntelligence` for segment data, enables spinning based on UI state (`WheelState.Review`), and avoids sending results via WebSocket. This relies on the `MockWebSocketProvider` supplying the necessary state structure (e.g., default `workflowState`, mock `lastWheelData`).
            - **Connected Mode (`isConnected` is true):** Uses `lastWheelData` for segments, enables spinning based on backend `workflowState` (`pre_spin` status `completed`), and sends spin results using `sendSpinResult` from the context.
            - This approach ensures type safety and correct behavior in both offline PWA and connected scenarios without `try...catch` blocks in the consumer hook.

## 4. Implementation Strategy
- **Dependency Management:** Added `vite-plugin-pwa` as a dev dependency using `npm`. *Insight: Initial install failed due to network timeout, retry succeeded.*
- **Build Configuration Updates:** Modified `vite.config.ts` to include and configure the `VitePWA` plugin. Added necessary meta tags to `index.html`. *Insight: Corrected root `.gitignore` to remove `frontend_enhanced` entry and add `frontend_enhanced/dist/` and `frontend_enhanced/.env*`.*
- **Testing Methodology:**
    - **Local Development:** Use browser developer tools (Application tab) to inspect manifest, service worker, cache. Simulate offline mode. Use `npm run preview` to serve the build locally.
    - **Lighthouse Audits:** Run Lighthouse audits in Chrome DevTools against the preview server.
    - **Real Device Testing:** *Insight: Requires serving the preview build over HTTPS for installability checks.* Recommended methods:
        - **Localhost Tunneling:** Use `ngrok http <port>` to create a temporary HTTPS URL (Used successfully after adding icons).
        - **ADB Port Forwarding:** Use `adb reverse tcp:<port> tcp:<port>` to access `localhost:<port>` on the connected device.
    - **Update Testing:** Requires making a code change, rebuilding, and verifying the update prompt via the `PWAUpdater` component.
- **Deployment Approach:** Deploy the built static assets (from `dist/`) to a hosting provider that supports HTTPS (Netlify, Vercel, GitHub Pages, etc.).
