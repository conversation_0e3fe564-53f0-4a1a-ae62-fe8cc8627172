import React, { createContext, useContext, useRef, useState, useEffect, useMemo, useCallback } from 'react';
// Assuming types are correctly defined in api.ts or similar
import { ChatMessage, WheelData, SystemMessage, ProcessingStatus } from '../types/api';
// Assuming MockWebSocket exists and works for now, though ideally a real one would be used here
// NOTE: This file WILL BE REFACTORED to use a REAL WebSocket later.
// For now, we keep the MockWebSocket reference to fix immediate type errors caused by previous refactoring.
import { MockWebSocket } from '../services/MockWebSocket';

// Define WorkflowState structure
export type WorkflowState = {
  currentWorkflow: string | null;
  status: 'idle' | 'processing' | 'completed' | 'failed' | 'unknown' | string; // Reverted to allow string literals
  lastUpdated: number;
};

// Define Message type (can be Chat or System) - Removed as not directly used in context type
// export type Message = ChatMessage | SystemMessage;

// Define the shape of the context value - EXPORTED
export interface WebSocketContextType {
  isConnected: boolean; // Renamed from 'connected' for clarity
  connectionError: string | null;
  lastWheelData: WheelData | null; // Renamed from 'wheel'
  isProcessing: boolean; // Added isProcessing state
  workflowState: WorkflowState; // Use the object type
  // Actions
  sendChatMessage: (message: string, metadata?: { [key: string]: any }) => void; // Added metadata
  sendSpinResult: (activityTailoredId: string, name: string, description?: string) => boolean; // Corrected signature based on useWheel
  sendActivityStatus: (activityId: string, status: 'pending' | 'in-progress' | 'completed') => void;
  requestWheel: () => void;
}

// Create the context - EXPORTED
export const WebSocketContext = createContext<WebSocketContextType | null>(null);

// --- WebSocketProvider ---
// This provider should ideally connect to the REAL WebSocket backend.
// For now, it still uses the MockWebSocket for demonstration,
// but crucially, it PROVIDES the state structure expected by consumers like useWheel.
// THIS WILL BE REFACTORED LATER.
export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [systemMessages, setSystemMessages] = useState<SystemMessage[]>([]);
  const [lastWheelData, setLastWheelData] = useState<WheelData | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false); // Added state
  const [workflowState, setWorkflowState] = useState<WorkflowState>({ // Use object state
    currentWorkflow: null,
    status: 'idle', // Reverted status type
    lastUpdated: Date.now(),
  });

  const socketRef = useRef<MockWebSocket | null>(null); // Using MockWebSocket for now

  // Define requestWheel *before* useEffect uses it
  const requestWheel = useCallback(() => {
    if (!socketRef.current) return; // Guard against null ref
    console.log("[WebSocketProvider] Requesting wheel data...");
    setIsProcessing(true);
    setWorkflowState({ currentWorkflow: 'wheel_generation', status: 'processing', lastUpdated: Date.now() });
    // Simulate sending request via mock
    socketRef.current.send(JSON.stringify({ type: 'wheel_request' }));
  }, []); // No dependencies needed for mock

  // Handle incoming messages (defined before useEffect)
  const handleMessage = useCallback((message: any) => {
    // Basic type guard
    if (!message || typeof message.type !== 'string') {
        console.warn("[WebSocketProvider] Received invalid message format:", message);
        return;
    }

    setIsProcessing(false); // Assume processing ends when a message is received, adjust if needed

    switch (message.type) {
      case 'chat_message':
        // Ensure it conforms to ChatMessage structure from api.ts
        if (typeof message.content === 'string' && typeof message.is_user === 'boolean') {
            const newMsg: ChatMessage = {
                type: 'chat_message', // Add type property
                id: message.id || `msg-${Date.now()}`, // Use provided ID or generate
                content: message.content,
                is_user: message.is_user,
                sender: message.sender || (message.is_user ? 'user' : 'ai'), // Use provided sender or derive
                timestamp: message.timestamp ? new Date(message.timestamp) : new Date(), // Use provided timestamp or now
            };
            setChatMessages(prev => [...prev, newMsg]);
        } else {
             console.warn("[WebSocketProvider] Received malformed chat_message:", message);
        }
        break;
      case 'system_message':
         if (typeof message.content === 'string') {
             // Assuming SystemMessage type expects 'type', 'content', and 'level'
            const newMsg: SystemMessage = {
                type: 'system_message', // Add type property
                content: message.content,
                level: message.level || 'info', // Use provided level or default
            };
            setSystemMessages(prev => [...prev, newMsg]);
         } else {
              console.warn("[WebSocketProvider] Received malformed system_message:", message);
         }
        break;
      case 'processing_status': // Keep handling this for now if Mock sends it
        if (typeof message.status === 'string') {
            setIsProcessing(message.status === 'processing');
            // Optionally update workflowState based on this too
            setWorkflowState(prev => ({ ...prev, status: message.status, lastUpdated: Date.now() }));
        } else {
             console.warn("[WebSocketProvider] Received malformed processing_status:", message);
        }
        break;
      case 'wheel_data':
        // Basic validation for wheel_data structure
        if (message.wheel && Array.isArray(message.wheel.items)) {
            setLastWheelData(message as WheelData);
            // Assume receiving wheel data means backend is ready for pre_spin
            setWorkflowState({ currentWorkflow: 'pre_spin', status: 'completed', lastUpdated: Date.now() });
            console.log("[WebSocketProvider] Wheel data received, workflow set to pre_spin/completed.");
        } else {
            console.warn("[WebSocketProvider] Received malformed wheel_data:", message);
        }
        break;
      case 'workflow_status': // Keep handling this for now if Mock sends it
         if (typeof message.workflow_id === 'string' && typeof message.status === 'string') {
            // Update workflowState based on backend message
            setWorkflowState({ currentWorkflow: message.workflow_id, status: message.status, lastUpdated: Date.now() });
            setIsProcessing(message.status === 'processing' || message.status === 'initiated');
         } else {
              console.warn("[WebSocketProvider] Received malformed workflow_status:", message);
         }
        break;
      case 'error':
         if (typeof message.content === 'string') {
            setConnectionError(message.content); // Display backend errors
            setWorkflowState(prev => ({ ...prev, status: 'failed', lastUpdated: Date.now() }));
         } else {
              console.warn("[WebSocketProvider] Received malformed error message:", message);
         }
        break;
      default:
        console.warn(`[WebSocketProvider] Received unhandled message type: ${message.type}`);
    }
  }, []); // No dependencies needed

  // Effect to initialize and manage WebSocket connection
  useEffect(() => {
    console.log("[WebSocketProvider] Initializing MockWebSocket...");
    socketRef.current = new MockWebSocket(); // Using MockWebSocket

    socketRef.current.onopen = () => {
      console.log("[WebSocketProvider] Mock connection opened.");
      setIsConnected(true);
      setIsProcessing(false); // Reset processing on open
      setConnectionError(null);
      // Simulate initial state after connection (e.g., request wheel)
      requestWheel(); // Request initial wheel data
    };

    socketRef.current.onmessage = (event) => { // Use the memoized handler
        try {
            const data = JSON.parse(event.data);
            handleMessage(data); // Call the handler
        } catch (e) {
            console.error("[WebSocketProvider] Failed to parse message:", event.data, e);
        }
    };

    socketRef.current.onerror = (event: any) => { // Use 'any' for mock error event type
      console.error("[WebSocketProvider] Mock WebSocket error:", event.message);
      setConnectionError(event.message || "Unknown WebSocket error");
      setIsConnected(false);
      setIsProcessing(false); // Reset processing on error
    };

    socketRef.current.onclose = () => {
      console.log("[WebSocketProvider] Mock connection closed.");
      setIsConnected(false);
      setIsProcessing(false); // Reset processing on close
      // Optionally set workflow state to idle or error on close
      setWorkflowState({ currentWorkflow: null, status: 'idle', lastUpdated: Date.now() });
    };

    // Simulate connection attempt
    setTimeout(() => {
      socketRef.current?.triggerOpen(); // Use triggerOpen from MockWebSocket
    }, 500);

    return () => {
      console.log("[WebSocketProvider] Closing MockWebSocket.");
      socketRef.current?.close();
    };
  }, [requestWheel, handleMessage]); // Add dependencies

  // --- Actions ---

  const send = useCallback((message: object) => {
    if (isConnected && socketRef.current) {
      try {
        const messageString = JSON.stringify(message);
        console.log("[WebSocketProvider] Sending message (mock):", messageString);
        socketRef.current.send(messageString);
        return true;
      } catch (error) {
        console.error("[WebSocketProvider] Failed to send message (mock):", error);
        setConnectionError("Failed to send message.");
        return false;
      }
    } else {
      console.warn("[WebSocketProvider] Cannot send message, not connected (mock).");
      setConnectionError("Not connected to server.");
      return false;
    }
  }, [isConnected]); // Dependency on isConnected

  const sendChatMessage = useCallback((content: string, metadata?: { [key: string]: any }) => {
    console.log("[WebSocketProvider] Sending chat message (mock):", content, metadata);
    setIsProcessing(true); // Assume processing starts
    setWorkflowState(prev => ({ ...prev, status: 'processing', lastUpdated: Date.now() }));
    send({
      type: 'chat_message',
      content: { message: content, metadata: metadata || {} } // Include metadata
    });
  }, [send]); // Dependency on send

  const sendSpinResult = useCallback((activityTailoredId: string, name: string, description?: string): boolean => {
    console.log("[WebSocketProvider] Sending spin result (mock):", activityTailoredId, name);
    setIsProcessing(true);
    setWorkflowState({ currentWorkflow: 'post_spin', status: 'processing', lastUpdated: Date.now() });
    return send({
      type: 'spin_result',
      content: { activity_tailored_id: activityTailoredId, name, description: description || '' }
    });
  }, [send]); // Dependency on send

  const sendActivityStatus = useCallback((activityId: string, status: 'pending' | 'in-progress' | 'completed') => {
     console.log("[WebSocketProvider] Sending activity status (mock):", activityId, status);
     setIsProcessing(true); // May depend on status
     setWorkflowState(prev => ({ ...prev, status: 'processing', lastUpdated: Date.now() }));
    send({
      type: 'activity_status',
      content: { activityId, status }
    });
  }, [send]); // Dependency on send

  // requestWheel defined earlier

  // Memoize the context value
  const value = useMemo<WebSocketContextType>(() => ({
    isConnected,
    connectionError,
    chatMessages,
    systemMessages,
    lastWheelData,
    isProcessing, // Use direct state again
    workflowState,
    sendChatMessage,
    sendSpinResult,
    sendActivityStatus, // Keep this for now as Mock might use it
    requestWheel,
  }), [
    isConnected, connectionError, chatMessages, systemMessages, lastWheelData,
    isProcessing, workflowState, sendChatMessage, sendSpinResult, sendActivityStatus, requestWheel
  ]);

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};

// --- useWebSocket Hook --- EXPORTED
// (No changes needed for the hook itself)
export const useWebSocket = (): WebSocketContextType => {
  const context = useContext(WebSocketContext);
  if (context === null) { // Check for null, as that's the initial value
    // This error means the hook is used outside of a provider
    // (either WebSocketProvider or MockWebSocketProvider via the selector)
    throw new Error('useWebSocket must be used within a WebSocketProvider (or Mock via Selector)');
  }
  return context;
};
