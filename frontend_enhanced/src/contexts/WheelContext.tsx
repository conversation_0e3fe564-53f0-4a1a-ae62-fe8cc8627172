import React, { createContext, useState, useContext, useCallback, ReactNode, useMemo, useEffect } from 'react';
import { useSimulatedIntelligence } from '@/hooks/useSimulatedIntelligence'; // Import simulation hook

// Define possible states for the wheel component
export enum WheelState {
  Loading = 'loading', // Initial load or waiting for new wheel data
  Review = 'review', // Showing the wheel before spinning
  Spinning = 'spinning', // Wheel is animating
  Result = 'result', // Showing the wheel after spinning with selected item highlighted
  Error = 'error',     // An error occurred
}

// Define the shape of a wheel segment (matching Wheel.tsx and API contract)
export type WheelSegment = {
  id: string;
  name: string;
  color: string;
  description?: string;
  percentage?: number;
  domain?: string;
  base_challenge_rating?: number;
  activity_tailored_id?: string;
};

// Define the context state shape
interface WheelContextState {
  wheelState: WheelState;
  selectedSegmentId: string | null;
  segments: WheelSegment[];
  isLoading: boolean;
}

// Define the context actions shape
interface WheelContextActions {
  spinWheel: () => void;
  setWheelState: (state: WheelState) => void;
  setSelectedSegmentId: (id: string | null) => void;
  loadSegments: () => void; // Action to load/reload segments
}

// Create the context with default values
const WheelContext = createContext<(WheelContextState & WheelContextActions) | undefined>(undefined);

// Define the props for the provider
interface WheelProviderProps {
  children: ReactNode;
}

// Create the provider component
export const WheelProvider: React.FC<WheelProviderProps> = ({ children }) => {
  const [wheelState, setWheelState] = useState<WheelState>(WheelState.Review);
  const [selectedSegmentId, setSelectedSegmentIdState] = useState<string | null>(null);
  const [segments, setSegments] = useState<WheelSegment[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Use the simulation hook to get the data generator
  const { generateContextualActivities } = useSimulatedIntelligence();

  // Action to load segments (currently uses simulated data)
  const loadSegments = useCallback(() => {
    console.log('[WheelContext] Loading segments...');
    setIsLoading(true);
    // Simulate loading delay if needed, or directly set
    const simulatedItems = generateContextualActivities();
    setSegments(simulatedItems);
    setIsLoading(false);
    setWheelState(WheelState.Review); // Reset state when loading new segments
    setSelectedSegmentIdState(null);
    console.log('[WheelContext] Segments loaded:', simulatedItems);
  }, [generateContextualActivities]);

  // Action to initiate spinning
  const spinWheel = useCallback(() => {
    if (wheelState === WheelState.Review) {
      console.log('[WheelContext] Initiating spin...');
      setSelectedSegmentIdState(null); // Clear previous selection
      setWheelState(WheelState.Spinning);
      // Haptic feedback can be triggered here or in the component
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    } else {
      console.warn('[WheelContext] Cannot spin wheel when not in Review state.');
    }
  }, [wheelState]);

  // Action to set the selected segment ID (typically called by Wheel component via onSpinEnd)
  // This also transitions the state to Result
  const setSelectedSegmentId = useCallback((id: string | null) => {
    console.log(`[WheelContext] Setting selected segment ID: ${id}`);
    setSelectedSegmentIdState(id);
    setWheelState(id ? WheelState.Result : WheelState.Review); // Go to Result if ID is valid, else back to Review
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    wheelState,
    selectedSegmentId,
    segments,
    isLoading,
    spinWheel,
    setWheelState,
    setSelectedSegmentId,
    loadSegments,
  }), [wheelState, selectedSegmentId, segments, isLoading, spinWheel, setWheelState, setSelectedSegmentId, loadSegments]);

  // Load segments on initial mount
  useEffect(() => {
    loadSegments();
  }, [loadSegments]);

  return (
    <WheelContext.Provider value={contextValue}>
      {children}
    </WheelContext.Provider>
  );
};

// Create a custom hook for using the context
export const useWheelContext = (): WheelContextState & WheelContextActions => {
  const context = useContext(WheelContext);
  if (context === undefined) {
    throw new Error('useWheelContext must be used within a WheelProvider');
  }
  return context;
};
