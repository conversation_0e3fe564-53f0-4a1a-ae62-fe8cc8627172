
import React from 'react';
// Import the providers AND the hook from the main context file
import { WebSocketProvider, useWebSocket } from './WebSocketContext';
import { MockWebSocketProvider } from './MockWebSocketContext';

// Determine mode based on Vite environment variable
// Ensure you have a .env file (e.g., .env.development) with VITE_USE_MOCK_WS=true/false
const USE_MOCK_WS = import.meta.env.VITE_USE_MOCK_WS === 'true';

console.log(`[WebSocketProviderSelector] Running in ${USE_MOCK_WS ? 'Mock' : 'Real'} mode.`);

export const WebSocketProviderSelector: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return USE_MOCK_WS ? (
    <MockWebSocketProvider>{children}</MockWebSocketProvider>
  ) : (
    <WebSocketProvider>{children}</WebSocketProvider>
  );
};

// Re-export the hook so consumers import it from the selector
export { useWebSocket };
