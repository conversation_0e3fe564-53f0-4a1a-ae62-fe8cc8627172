
import React, { createContext, useContext, useState } from 'react';

interface UserProfile {
  id: string;
  name: string;
  preferences: {
    categories: string[];
    energyLevels: string[];
    durations: string[];
  };
}

interface UserContextType {
  user: UserProfile | null;
  isAuthenticated: boolean;
  updateProfile: (profile: Partial<UserProfile>) => void;
  updatePreferences: (preferences: Partial<UserProfile['preferences']>) => void;
  logout: () => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserProfile | null>({
    id: "user-123",
    name: "Demo User",
    preferences: {
      categories: ["Exercise", "Creativity", "Education", "Mindfulness"],
      energyLevels: ["Low", "Medium", "High"],
      durations: ["Short", "Medium", "Long"]
    }
  });

  const updateProfile = (profile: Partial<UserProfile>) => {
    setUser(currentUser => currentUser ? { ...currentUser, ...profile } : null);
  };

  const updatePreferences = (preferences: Partial<UserProfile['preferences']>) => {
    setUser(currentUser => 
      currentUser 
        ? { 
            ...currentUser, 
            preferences: { 
              ...currentUser.preferences, 
              ...preferences 
            } 
          } 
        : null
    );
  };

  const logout = () => {
    setUser(null);
  };

  const value: UserContextType = {
    user,
    isAuthenticated: !!user,
    updateProfile,
    updatePreferences,
    logout
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
