import React, { createContext, useState, useEffect, useMemo, useCallback } from 'react';
// Import types from the REAL context AND the api types
import { WebSocketContextType, WorkflowState } from './WebSocketContext';
import { ChatMessage, SystemMessage, WheelData } from '../types/api'; // Import specific message types

// Mock data for wheel items based on API contract
const mockWheelItems: WheelData['wheel']['items'] = [
  {
    id: "item-1",
    name: "Read",
    description: "Take 30 minutes to read a few chapters of a book you've been wanting to start.",
    percentage: 15,
    color: "#FF8C42",
    domain: "relaxation",
    base_challenge_rating: 25,
    activity_tailored_id: "tailored_1"
  },
  {
    id: "item-2",
    name: "Exercise",
    description: "A quick 20-minute jog around your neighborhood to get your heart rate up.",
    percentage: 12,
    color: "#3066BE",
    domain: "physical",
    base_challenge_rating: 45,
    activity_tailored_id: "tailored_2"
  },
  {
    id: "item-3",
    name: "<PERSON>",
    description: "Try cooking something new from that cookbook you got last year.",
    percentage: 14,
    color: "#A06CD5",
    domain: "creative",
    base_challenge_rating: 35,
    activity_tailored_id: "tailored_3"
  },
  {
    id: "item-4",
    name: "Meditate",
    description: "A 10-minute guided meditation to clear your mind and reduce stress.",
    percentage: 13,
    color: "#5D8FDD",
    domain: "mindfulness",
    base_challenge_rating: 20,
    activity_tailored_id: "tailored_4"
  },
  {
    id: "item-5",
    name: "Learn",
    description: "Spend 25 minutes learning about a topic you're curious about.",
    percentage: 12,
    color: "#204080",
    domain: "education",
    base_challenge_rating: 30,
    activity_tailored_id: "tailored_5"
  },
  {
    id: "item-6",
    name: "Create",
    description: "Express yourself through drawing or sketching for 15 minutes.",
    percentage: 11,
    color: "#6B8E23",
    domain: "creative",
    base_challenge_rating: 35,
    activity_tailored_id: "tailored_6"
  },
  {
    id: "item-7",
    name: "Socialize",
    description: "Reach out to a friend you haven't spoken to in a while.",
    percentage: 12,
    color: "#FF6B6B",
    domain: "social",
    base_challenge_rating: 30,
    activity_tailored_id: "tailored_7"
  },
  {
    id: "item-8",
    name: "Rest",
    description: "Take a quick 15-minute power nap to recharge your energy.",
    percentage: 11,
    color: "#9370DB",
    domain: "relaxation",
    base_challenge_rating: 15,
    activity_tailored_id: "tailored_8"
  },
];

// Create the context with the correct type, initially undefined
// Use the REAL context object so providers use the same context reference
import { WebSocketContext } from './WebSocketContext';

export const MockWebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State for mock values matching WebSocketContextType
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]); // Use correct type
  const [systemMessages, setSystemMessages] = useState<SystemMessage[]>([]); // Use correct type
  const [lastWheelData, setLastWheelData] = useState<WheelData | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [workflowState, setWorkflowState] = useState<WorkflowState>({
    currentWorkflow: null,
    status: 'idle',
    lastUpdated: Date.now(),
  });

  // Simulate connection and initial data load
  useEffect(() => {
    console.log("[MockWebSocketProvider] Simulating connection...");
    const connectTimeout = setTimeout(() => {
      setIsConnected(true);
      setConnectionError(null);
      console.log("[MockWebSocketProvider] Simulated connection established.");

      // Send welcome message (as ChatMessage from AI)
      const welcomeMessage: ChatMessage = {
        type: 'chat_message', // FIX: Add type
        id: `mock-chat-${Date.now()}`,
        content: "Welcome to Goali (Mock Mode)! Let's spin the wheel.",
        sender: 'ai', // FIX: Add sender
        is_user: false, // FIX: Add is_user
        timestamp: new Date(), // FIX: Use Date object
      };
      setChatMessages([welcomeMessage]);

      // Provide initial wheel data
      // FIX: Add type and ensure content matches WheelData if needed (content is any in api.ts)
      setLastWheelData({ type: 'wheel_data', content: null, wheel: { name: "Mock Activity Wheel", items: mockWheelItems } });

      // Set workflow state to allow spinning
      setWorkflowState({
        currentWorkflow: 'pre_spin',
        status: 'completed',
        lastUpdated: Date.now(),
      });
       console.log("[MockWebSocketProvider] Mock wheel data loaded and workflow set to pre_spin/completed.");

    }, 500);

    return () => clearTimeout(connectTimeout);
  }, []);

  // Mock sending chat message
  const sendChatMessage = useCallback((content: string, metadata?: { [key: string]: any }) => {
    console.log(`[MockWebSocketProvider] Received chat message: "${content}"`, metadata);
    // Create user message conforming to ChatMessage
    const userMessage: ChatMessage = {
        type: 'chat_message', // FIX: Add type
        id: `mock-chat-${Date.now()}`,
        content,
        sender: 'user', // FIX: Add sender
        is_user: true, // FIX: Add is_user
        timestamp: new Date(), // FIX: Use Date object
    };
    setChatMessages(prev => [...prev, userMessage]);
    setIsProcessing(true);
    setWorkflowState(prev => ({ ...prev, status: 'processing', lastUpdated: Date.now() }));

    // Simulate AI response
    setTimeout(() => {
      let responseContent = `Mock response to: "${content}"`;
      if (metadata?.requested_workflow === 'wheel_generation') {
        responseContent = "Okay, generating a new mock wheel!";
        // FIX: Add type and ensure content matches WheelData if needed
        setLastWheelData({ type: 'wheel_data', content: null, wheel: { name: "New Mock Wheel", items: [...mockWheelItems].reverse() } });
         setWorkflowState({ currentWorkflow: 'pre_spin', status: 'completed', lastUpdated: Date.now() });
      } else {
         setWorkflowState(prev => ({ ...prev, status: 'completed', lastUpdated: Date.now() }));
      }

      // Create AI message conforming to ChatMessage
      const aiMessage: ChatMessage = {
          type: 'chat_message', // FIX: Add type
          id: `mock-chat-${Date.now() + 1}`,
          content: responseContent,
          sender: 'ai', // FIX: Add sender
          is_user: false, // FIX: Add is_user
          timestamp: new Date(), // FIX: Use Date object
      };
      setChatMessages(prev => [...prev, aiMessage]);
      setIsProcessing(false);
    }, 800);
  }, []);

  // Mock sending spin result
  const sendSpinResult = useCallback((activityTailoredId: string, name: string, description?: string): boolean => {
    console.log(`[MockWebSocketProvider] Received spin result: ID=${activityTailoredId}, Name=${name}`);
    setIsProcessing(true);
    setWorkflowState({ currentWorkflow: 'post_spin', status: 'processing', lastUpdated: Date.now() });

    // Simulate backend processing and confirmation (as ChatMessage from AI)
    setTimeout(() => {
      const confirmMessage: ChatMessage = {
        type: 'chat_message', // FIX: Add type
        id: `mock-chat-${Date.now()}`,
        content: `Confirmed selection: ${name}!`,
        sender: 'ai', // FIX: Add sender
        is_user: false, // FIX: Add is_user
        timestamp: new Date(), // FIX: Use Date object
      };
      setChatMessages(prev => [...prev, confirmMessage]);
      setIsProcessing(false);
      setWorkflowState({ currentWorkflow: 'post_spin', status: 'completed', lastUpdated: Date.now() });
       console.log("[MockWebSocketProvider] Mock spin result processed.");
    }, 500);
    return true; // Simulate successful send
  }, []);

   // Mock sending activity status (basic implementation)
   const sendActivityStatus = useCallback((activityId: string, status: 'pending' | 'in-progress' | 'completed') => {
     console.log(`[MockWebSocketProvider] Received activity status: ${activityId} -> ${status}`);
     // Simulate AI response (as ChatMessage)
     setTimeout(() => {
        let message = '';
        switch(status) {
          case 'in-progress': message = "Mock: Great! Enjoy your activity."; break;
          case 'completed': message = "Mock: Congrats on completing! How was it?"; break;
          default: return;
        }
        // Create AI message conforming to ChatMessage
        const statusMessage: ChatMessage = {
            type: 'chat_message', // FIX: Add type
            id: `mock-chat-${Date.now()}`,
            content: message,
            sender: 'ai', // FIX: Add sender
            is_user: false, // FIX: Add is_user
            timestamp: new Date(), // FIX: Use Date object
        };
        setChatMessages(prev => [...prev, statusMessage]);
     }, 300);
   }, []);

   // Mock requesting wheel data
   const requestWheel = useCallback(() => {
     console.log("[MockWebSocketProvider] Received request for wheel data.");
     setIsProcessing(true);
     setWorkflowState({ currentWorkflow: 'wheel_generation', status: 'processing', lastUpdated: Date.now() });
     setTimeout(() => {
        // FIX: Add type and ensure content matches WheelData if needed
        setLastWheelData({ type: 'wheel_data', content: null, wheel: { name: "Refreshed Mock Wheel", items: mockWheelItems } });
        setIsProcessing(false);
        setWorkflowState({ currentWorkflow: 'pre_spin', status: 'completed', lastUpdated: Date.now() });
        console.log("[MockWebSocketProvider] Mock wheel data refreshed.");
     }, 600);
   }, []);


  // Memoize the context value matching WebSocketContextType
  const contextValue = useMemo<WebSocketContextType>(() => ({
    isConnected,
    connectionError,
    chatMessages,
    systemMessages, // Keep systemMessages state
    lastWheelData,
    isProcessing,
    workflowState,
    sendChatMessage,
    sendSpinResult,
    sendActivityStatus,
    requestWheel,
  }), [
    isConnected, connectionError, chatMessages, systemMessages, lastWheelData,
    isProcessing, workflowState, sendChatMessage, sendSpinResult, sendActivityStatus, requestWheel
  ]);

  return (
    // Provide the value using the IMPORTED context object
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

// No need to export useMockWebSocket anymore
