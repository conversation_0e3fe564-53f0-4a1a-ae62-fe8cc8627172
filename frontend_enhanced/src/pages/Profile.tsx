
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { <PERSON>r, <PERSON>, Clock, Sparkles, Settings, ChevronRight } from "lucide-react";
import MobileLayout from "@/components/MobileLayout";

const Profile = () => {
  const [notifications, setNotifications] = useState(true);

  // Sample user preferences and stats
  const preferences = {
    activityDuration: "15-30 minutes",
    preferredTime: "Evening",
    preferredCategories: ["Exercise", "Creativity", "Mindfulness"],
    avoidCategories: ["Social"]
  };
  
  const stats = {
    activitiesCompleted: 12,
    totalTime: "5h 25m",
    mostFrequent: "Reading",
    categories: {
      Exercise: 4,
      Creativity: 3,
      Mindfulness: 3,
      Education: 2
    }
  };

  return (
    <MobileLayout>
      <div className="flex flex-col h-full p-4">
        <h1 className="text-2xl font-bold mb-4">Profile</h1>
        
        <Tabs defaultValue="preferences" className="flex-1">
          <TabsList className="grid grid-cols-3">
            <TabsTrigger value="preferences">Preferences</TabsTrigger>
            <TabsTrigger value="stats">Stats</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="preferences" className="space-y-4 mt-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  Your Activity Preferences
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Typical Activity Duration</h3>
                  <p>{preferences.activityDuration}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Preferred Time of Day</h3>
                  <p>{preferences.preferredTime}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Favorite Categories</h3>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {preferences.preferredCategories.map(category => (
                      <Badge key={category} variant="secondary">{category}</Badge>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Categories to Avoid</h3>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {preferences.avoidCategories.map(category => (
                      <Badge key={category} variant="destructive">{category}</Badge>
                    ))}
                  </div>
                </div>
                
                <Button className="w-full mt-2">Edit Preferences</Button>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="stats" className="space-y-4 mt-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Sparkles className="w-5 h-5 mr-2" />
                  Your Activity Stats
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-3 rounded-md text-center">
                    <p className="text-2xl font-bold text-blue-600">{stats.activitiesCompleted}</p>
                    <p className="text-xs text-gray-600">Activities Completed</p>
                  </div>
                  
                  <div className="bg-purple-50 p-3 rounded-md text-center">
                    <p className="text-2xl font-bold text-purple-600">{stats.totalTime}</p>
                    <p className="text-xs text-gray-600">Total Time</p>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Most Frequent Activity</h3>
                  <p className="font-medium">{stats.mostFrequent}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Categories Breakdown</h3>
                  <div className="space-y-2">
                    {Object.entries(stats.categories).map(([category, count]) => (
                      <div key={category} className="flex items-center">
                        <span className="flex-1">{category}</span>
                        <div className="w-32 bg-gray-200 rounded-full h-2.5">
                          <div 
                            className="bg-blue-600 h-2.5 rounded-full"
                            style={{ width: `${(count / stats.activitiesCompleted) * 100}%` }}
                          ></div>
                        </div>
                        <span className="ml-2 text-sm font-medium">{count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="settings" className="space-y-4 mt-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Settings className="w-5 h-5 mr-2" />
                  App Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Notifications</h3>
                    <p className="text-sm text-gray-500">Receive activity reminders</p>
                  </div>
                  <Switch checked={notifications} onCheckedChange={setNotifications} />
                </div>
                
                <div className="border-t pt-4">
                  <Button variant="outline" className="w-full flex justify-between items-center">
                    <span>Account Settings</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
                
                <div>
                  <Button variant="outline" className="w-full flex justify-between items-center">
                    <span>Privacy Settings</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <div className="text-center">
              <Button variant="ghost" className="text-gray-500">
                Log Out
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MobileLayout>
  );
};

export default Profile;
