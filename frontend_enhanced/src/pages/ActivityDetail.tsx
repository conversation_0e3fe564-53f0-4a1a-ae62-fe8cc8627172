
import { useLocation, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import MobileLayout from "@/components/MobileLayout";
import SplitScreen from "@/components/SplitScreen";
import ChatInterface from "@/components/ChatInterface";
import { Clock, Battery, Tag, Check } from "lucide-react";
import { useState } from "react";
import { useActivity } from "@/hooks/useActivity";

const ActivityDetail = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const activityId = location.state?.activityId || "1";
  const [activityStatus, setActivityStatus] = useState<'pending' | 'in-progress' | 'completed'>('pending');
  const { getActivity, updateActivityStatus } = useActivity(activityId);
  
  const activity = getActivity() || {
    id: "1",
    name: "Read a book",
    description: "Take 30 minutes to read a few chapters of a book you've been wanting to start. Reading is a great way to relax, learn new things, and escape into different worlds.",
    duration: "30 min",
    energyLevel: "Low" as const,
    category: "Relaxation",
    color: "#FF8C42",
    tips: [
      "Find a quiet, comfortable spot",
      "Turn off notifications on your phone",
      "Set a timer if you tend to lose track of time",
      "Keep a notebook nearby to jot down any thoughts"
    ]
  };
  
  const initialMessages = [
    {
      id: "1",
      content: `Great choice! "${activity.name}" is a perfect activity for right now. Let me know if you have any questions about it.`,
      sender: "ai" as const,
      timestamp: new Date(),
    },
  ];

  const handleBeginActivity = () => {
    setActivityStatus('in-progress');
    updateActivityStatus('in-progress');
  };

  const handleCompleteActivity = () => {
    setActivityStatus('completed');
    updateActivityStatus('completed');
    
    setTimeout(() => {
      navigate('/home');
    }, 1500);
  };

  return (
    <MobileLayout>
      <SplitScreen
        topContent={
          <div className="flex flex-col h-full">
            <div className="mb-4 p-4 rounded-lg" style={{ backgroundColor: `${activity.color}20` }}>
              <div className="flex items-center gap-3 mb-2">
                <div 
                  className="w-6 h-6 rounded-full" 
                  style={{ backgroundColor: activity.color }}
                ></div>
                <h2 className="text-2xl font-bold">{activity.name}</h2>
              </div>
              
              <div className="flex flex-wrap gap-3 mb-4">
                <div className="flex items-center bg-white px-3 py-1 rounded-full shadow-sm">
                  <Clock className="w-4 h-4 mr-1 text-gray-600" />
                  <span className="text-sm">{activity.duration}</span>
                </div>
                
                <div className="flex items-center bg-white px-3 py-1 rounded-full shadow-sm">
                  <Battery className="w-4 h-4 mr-1 text-gray-600" />
                  <span className="text-sm">{activity.energyLevel} Energy</span>
                </div>
                
                <Badge variant="outline" className="bg-white">
                  <Tag className="w-3 h-3 mr-1" />
                  {activity.category}
                </Badge>
              </div>
              
              <p className="text-gray-800 mb-6">
                {activity.description}
              </p>
              
              <div className="mb-6">
                <h3 className="font-semibold mb-2">Tips:</h3>
                <ul className="list-none space-y-2">
                  {activity.tips && activity.tips.map((tip, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="w-4 h-4 mr-2 text-green-600 mt-1 shrink-0" />
                      <span className="text-sm">{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div className="mt-auto sticky bottom-0 bg-white p-2">
              {activityStatus === 'pending' && (
                <Button className="w-full py-6" onClick={handleBeginActivity}>
                  Begin Activity
                </Button>
              )}
              {activityStatus === 'in-progress' && (
                <Button className="w-full py-6" onClick={handleCompleteActivity}>
                  Activity Done
                </Button>
              )}
              {activityStatus === 'completed' && (
                <Button className="w-full py-6" disabled>
                  Completed!
                </Button>
              )}
            </div>
          </div>
        }
        bottomContent={
          <ChatInterface 
            initialMessages={initialMessages}
            inputPlaceholder="Need to modify this activity? Ask here..."
          />
        }
        defaultSplit={60}
      />
    </MobileLayout>
  );
};

export default ActivityDetail;
