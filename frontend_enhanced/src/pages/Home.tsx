
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import MobileLayout from "@/components/MobileLayout";
import ChatInterface from "@/components/ChatInterface";
import SplitScreen from "@/components/SplitScreen";
import Wheel from "@/components/Wheel";
import { RefreshCw, ArrowUp } from "lucide-react";
import { useWheel } from "@/hooks/useWheel";
import { useWebSocket } from "@/contexts/WebSocketContext";
import { toast } from "@/components/ui/use-toast";

const Home = () => {
  const navigate = useNavigate();
  const [hasWheel, setHasWheel] = useState(false);
  const { segments, isSpinning, selectedSegment, handleSpinEnd } = useWheel();
  
  // Get workflow status with error handling
  let workflowStatus = "idle";
  try {
    const { workflowStatus: wsStatus } = useWebSocket();
    workflowStatus = wsStatus;
  } catch (error) {
    console.error("Failed to get workflow status:", error);
    // The useWheel hook already handles the error, so we don't need to do anything else here
  }

  const initialMessages = [
    {
      id: "1",
      content: "Welcome to your Goali dashboard! What would you like to do today?",
      sender: "ai" as const,
      timestamp: new Date(),
    },
  ];

  const handleGenerateWheel = () => {
    setHasWheel(true);
    toast({
      title: "Wheel Generated",
      description: "Your activity wheel has been generated.",
    });
  };

  const handleReviewActivities = () => {
    navigate("/activity-review");
  };

  return (
    <MobileLayout>
      <SplitScreen
        topContent={
          <div className="flex flex-col items-center justify-center h-full">
            {!hasWheel ? (
              <div className="text-center">
                <h2 className="text-2xl font-bold mb-6">Ready to discover activities?</h2>
                <Button 
                  size="lg" 
                  className="py-6 px-8"
                  onClick={handleGenerateWheel}
                >
                  Generate Wheel <RefreshCw className="ml-2 h-5 w-5" />
                </Button>
              </div>
            ) : (
              <div className="w-full flex flex-col items-center">
                <Wheel 
                  segments={segments} 
                  spinning={isSpinning}
                  onSpinEnd={handleSpinEnd}
                  selectedSegment={selectedSegment}
                />
                
                <div className="flex flex-col items-center mt-6 space-y-4">
                  <Button 
                    variant="outline" 
                    className="flex items-center"
                    onClick={handleReviewActivities}
                  >
                    Review Activities <ArrowUp className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        }
        bottomContent={<ChatInterface initialMessages={initialMessages} />}
      />
    </MobileLayout>
  );
};

export default Home;
