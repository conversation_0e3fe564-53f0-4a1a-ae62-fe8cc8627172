import { useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import MobileLayout from "@/components/MobileLayout";
import Wheel from "@/components/Wheel";
import { useWheel, WheelSegment } from "@/hooks/useWheel"; // Use the refactored hook (remove WheelState import)
import { WheelState } from "@/contexts/WheelContext"; // Import WheelState directly from context
import { toast } from "sonner";
import { AlertCircle } from "lucide-react"; // For error display
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"; // For error display

const WheelSpin = () => {
  const navigate = useNavigate();
  // Use the refactored hook
  const {
    wheelState,
    segments,
    selectedSegmentId, // This now holds the activity_tailored_id
    isSpinEnabled,
    isLoading,
    error,
    spinWheel,
    setActivityResult,
    // resetWheel, // Could use this for a reset button if needed
  } = useWheel();

  // Callback for when the Wheel component finishes its animation
  const handleSpinAnimationComplete = useCallback((selectedItem: WheelSegment | null) => {
    // The Wheel component gives us the full selected item object
    // We pass this to the hook's setActivityResult action, which handles state and backend communication
    setActivityResult(selectedItem);
    if (selectedItem) {
      toast.success(`Selected: ${selectedItem.name}`);
    } else {
      toast.error("Spin finished, but no activity selected.");
    }
  }, [setActivityResult]);

  const handleContinue = () => {
    // selectedSegmentId from the hook already holds the activity_tailored_id
    console.log("Navigating to activity detail with activity_tailored_id:", selectedSegmentId);

    if (selectedSegmentId) {
      navigate("/activity-detail", {
        state: { activityId: selectedSegmentId } // Pass the ID
      });
    } else {
       console.error("Cannot navigate, selectedSegmentId is null.");
       toast.error("Could not find activity details.");
    }
  };

  return (
    <MobileLayout>
      <div className="flex flex-col items-center justify-center h-full p-6">
        <h1 className="text-2xl font-bold mb-8">Spin the Wheel!</h1>

        {error && (
           <Alert variant="destructive" className="mb-4 w-full max-w-sm">
             <AlertCircle className="h-4 w-4" />
             <AlertTitle>Error</AlertTitle>
             <AlertDescription>{error}</AlertDescription>
           </Alert>
        )}

        <div className="mb-6 relative h-[300px] w-[300px] flex items-center justify-center">
          {/* Use segments from useWheel hook */}
          {isLoading ? (
             <div className="w-full h-full bg-gray-200 rounded-full flex items-center justify-center animate-pulse">
               <p className="text-gray-600">Loading wheel...</p>
             </div>
          ) : segments && segments.length > 0 ? (
            <Wheel
              segments={segments} // Pass segments from hook
              wheelState={wheelState} // Pass state from hook
              selectedSegmentId={selectedSegmentId} // Pass selected ID from hook
              onSpinAnimationComplete={handleSpinAnimationComplete} // Pass callback
            />
          ) : (
             <div className="w-full h-full bg-gray-100 rounded-full flex items-center justify-center text-center p-4">
               <p className="text-gray-500">No activities available for the wheel.</p>
             </div>
          )}
        </div>

        <div className="flex flex-col w-full max-w-sm space-y-4">
          {/* Update conditions based on wheelState and isSpinEnabled */}
          {wheelState === WheelState.Review && (
            <Button onClick={spinWheel} disabled={!isSpinEnabled || isLoading} className="w-full py-6">
              {isLoading ? "Loading..." : isSpinEnabled ? "Spin Wheel" : "Waiting..."}
            </Button>
          )}

          {wheelState === WheelState.Spinning && (
             <Button disabled className="w-full py-6">
               Spinning...
             </Button>
          )}

          {wheelState === WheelState.Result && selectedSegmentId && (
            <Button onClick={handleContinue} variant="outline" className="w-full py-6">
              View Activity
            </Button>
          )}
          {/* Optionally add a reset button */}
          {/* {wheelState === WheelState.Result && (
            <Button onClick={resetWheel} variant="ghost" className="w-full">
              Spin Again?
            </Button>
          )} */}
        </div>
      </div>
    </MobileLayout>
  );
};

export default WheelSpin;
