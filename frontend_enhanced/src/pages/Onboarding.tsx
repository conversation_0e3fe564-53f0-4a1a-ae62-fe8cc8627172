
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import MobileLayout from "@/components/MobileLayout";
import ChatInterface from "@/components/ChatInterface";
import OnboardingProgress from "@/components/OnboardingProgress";
import SplitScreen from "@/components/SplitScreen";

const Onboarding = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  
  const steps = [
    "Introduction",
    "Personality",
    "Goals",
    "Completion",
  ];

  const initialMessages = [
    {
      id: "1",
      content: "Welcome to Goali! I'm here to help you discover new activities and break out of your comfort zone.",
      sender: "ai" as const,
      timestamp: new Date(),
    },
    {
      id: "2",
      content: "Let's start by getting to know you better. This will help me create more personalized activity suggestions.",
      sender: "ai" as const,
      timestamp: new Date(),
    },
  ];

  const handleContinue = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Onboarding complete
      navigate("/home");
    }
  };

  return (
    <MobileLayout showMenu={false}>
      <SplitScreen
        topContent={
          <div className="flex flex-col items-center justify-center h-full">
            <Card className="w-full max-w-md bg-white p-6 shadow-lg">
              <OnboardingProgress steps={steps} currentStep={currentStep} />
              
              <div className="text-center mt-4 mb-6">
                <h2 className="text-2xl font-bold text-gray-800">
                  {currentStep === 0 && "Welcome to Goali"}
                  {currentStep === 1 && "Tell us about yourself"}
                  {currentStep === 2 && "What are your goals?"}
                  {currentStep === 3 && "You're all set!"}
                </h2>
                
                <p className="mt-2 text-gray-600">
                  {currentStep === 0 && "Let's start your journey of playful discovery"}
                  {currentStep === 1 && "This helps us tailor activities to your preferences"}
                  {currentStep === 2 && "Choose what you want to achieve with Goali"}
                  {currentStep === 3 && "Your personalized wheel is ready to spin"}
                </p>
              </div>
              
              {currentStep === 3 && (
                <div className="flex justify-center mb-6">
                  <div className="w-32 h-32 bg-blue-100 rounded-full flex items-center justify-center">
                    <div className="w-24 h-24 bg-blue-500 rounded-full flex items-center justify-center text-white text-4xl">
                      🎉
                    </div>
                  </div>
                </div>
              )}
              
              <Button 
                className="w-full py-6" 
                onClick={handleContinue}
              >
                {currentStep < steps.length - 1 ? "Continue" : "Get Started"}
              </Button>
            </Card>
          </div>
        }
        bottomContent={<ChatInterface initialMessages={initialMessages} standaloneMode={true} />}
      />
    </MobileLayout>
  );
};

export default Onboarding;
