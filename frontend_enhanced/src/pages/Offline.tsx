import { Button } from "@/components/ui/button"; // Corrected import path
import { useNavigate } from "react-router-dom";

export default function Offline() {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
      <svg
        className="w-24 h-24 text-gray-400 mb-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L3 3m8.293 8.293l1.414 1.414"
        />
      </svg>
      <h1 className="text-2xl font-bold mb-2">You're offline</h1>
      <p className="mb-4 text-gray-600">
        Please check your internet connection and try again.
      </p>
      <Button onClick={() => navigate("/", { replace: true })}>
        Retry Connection
      </Button>
    </div>
  );
}
