
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import MobileLayout from "@/components/MobileLayout";
import SplitScreen from "@/components/SplitScreen";
import Wheel from "@/components/Wheel";
import ChatInterface from "@/components/ChatInterface";
import ActivityCard from "@/components/ActivityCard";
import { useActivity } from "@/hooks/useActivity";
import { useWheel } from "@/hooks/useWheel";

const ActivityReview = () => {
  const navigate = useNavigate();
  const [selectedActivity, setSelectedActivity] = useState<string | null>(null);
  const { getAllActivities } = useActivity();
  const { segments } = useWheel();

  const activities = getAllActivities();

  const initialMessages = [
    {
      id: "1",
      content: "Here are the activities I've generated for your wheel. You can review them before spinning.",
      sender: "ai" as const,
      timestamp: new Date(),
    },
    {
      id: "2",
      content: "If you'd like any changes, just let me know!",
      sender: "ai" as const,
      timestamp: new Date(),
    },
  ];

  const handleCommit = () => {
    navigate("/wheel-spin");
  };

  return (
    <MobileLayout>
      <SplitScreen
        topContent={
          <div className="flex flex-col h-full">
            <div className="mb-4 flex justify-center">
              <div className="w-40 h-40">
                <Wheel segments={segments} />
              </div>
            </div>
            
            <h2 className="text-xl font-bold mb-4 text-center">Review Activities</h2>
            
            <div className="grid grid-cols-1 gap-3 mb-4">
              {activities.map((activity) => (
                <ActivityCard
                  key={activity.id}
                  activity={activity}
                  isSelected={activity.id === selectedActivity}
                  onClick={() => setSelectedActivity(activity.id)}
                />
              ))}
            </div>
            
            <div className="flex justify-center mt-auto sticky bottom-0 bg-white p-2">
              <Button 
                className="w-full max-w-xs"
                onClick={handleCommit}
              >
                Commit
              </Button>
            </div>
          </div>
        }
        bottomContent={<ChatInterface initialMessages={initialMessages} inputPlaceholder="Suggest changes to your activities..." />}
        defaultSplit={70}
      />
    </MobileLayout>
  );
};

export default ActivityReview;
