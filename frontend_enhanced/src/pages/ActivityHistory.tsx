
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Calendar, Clock, ArrowRight } from "lucide-react";
import MobileLayout from "@/components/MobileLayout";

const ActivityHistory = () => {
  // Sample history data
  const activities = [
    {
      id: "h1",
      name: "Morning Meditation",
      date: "Today, 8:30 AM",
      duration: "15 min",
      category: "Mindfulness",
      color: "#5D8FDD",
      reflection: "Felt centered and calm afterward. Good start to the day."
    },
    {
      id: "h2",
      name: "Read Novel",
      date: "Yesterday, 2:15 PM",
      duration: "45 min",
      category: "Relaxation",
      color: "#FF8C42",
      reflection: "Enjoyed the story. Will continue tomorrow."
    },
    {
      id: "h3",
      name: "HIIT Workout",
      date: "Apr 21, 6:00 PM",
      duration: "30 min",
      category: "Exercise",
      color: "#3066BE",
      reflection: "Challenging but felt accomplished after."
    },
    {
      id: "h4",
      name: "Try New Recipe",
      date: "Apr 20, 7:30 PM",
      duration: "60 min",
      category: "Creativity",
      color: "#A06CD5",
      reflection: "Made a delicious pasta dish! Saved the recipe."
    },
    {
      id: "h5",
      name: "Language Learning",
      date: "Apr 19, 5:00 PM",
      duration: "20 min",
      category: "Education",
      color: "#204080",
      reflection: "Learned 10 new vocab words. Making progress."
    },
  ];

  return (
    <MobileLayout>
      <div className="flex flex-col h-full p-4">
        <h1 className="text-2xl font-bold mb-4">Activity History</h1>

        <div className="space-y-3">
          {activities.map((activity) => (
            <Card key={activity.id} className="p-4">
              <div className="flex items-start">
                <div 
                  className="w-4 h-4 rounded-full mt-1 mr-3" 
                  style={{ backgroundColor: activity.color }}
                ></div>
                
                <div className="flex-1">
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-semibold">{activity.name}</h3>
                    <span className="text-xs text-gray-500 flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {activity.duration}
                    </span>
                  </div>
                  
                  <div className="flex items-center text-xs text-gray-500 mb-2">
                    <Calendar className="w-3 h-3 mr-1" />
                    {activity.date}
                  </div>
                  
                  {activity.reflection && (
                    <p className="text-sm border-l-2 pl-2 border-gray-300 text-gray-600 italic">
                      "{activity.reflection}"
                    </p>
                  )}
                  
                  <div className="flex justify-end mt-2">
                    <Button variant="ghost" size="sm" className="h-7 text-blue-600">
                      Details
                      <ArrowRight className="ml-1 w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </MobileLayout>
  );
};

export default ActivityHistory;
