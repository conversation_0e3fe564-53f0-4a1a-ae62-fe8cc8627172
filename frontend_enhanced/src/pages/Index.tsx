
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON>R<PERSON>, Spark<PERSON>, Target, Clock } from "lucide-react";
import { Link } from "react-router-dom";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      {/* Header Section */}
      <header className="container mx-auto pt-8 pb-6 px-4">
        <div className="flex justify-between items-center">
          <div className="text-3xl font-bold text-blue-600">Goali</div>
          <Button variant="outline" className="rounded-full" asChild>
            <Link to="/onboarding">Get Started</Link>
          </Button>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-12 md:py-20 text-center">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          Turn Decision Paralysis into 
          <span className="text-blue-600"> Playful Discovery</span>
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-10">
          Goali helps you explore activities, break out of your comfort zone, and discover 
          new interests through beneficial randomness and AI-powered guidance.
        </p>
        <Button className="rounded-full text-lg px-8 py-6 bg-blue-600 hover:bg-blue-700" asChild>
          <Link to="/onboarding">
            Start Your Journey <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </Button>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-center mb-12">How Goali Works</h2>
        
        <div className="grid md:grid-cols-3 gap-8">
          <Card className="p-6 border-2 border-blue-100 hover:border-blue-300 transition-colors">
            <div className="bg-blue-100 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
              <Target className="text-blue-600 h-8 w-8" />
            </div>
            <h3 className="text-xl font-bold mb-2">Discover Activities</h3>
            <p className="text-gray-600">
              Generate a customized wheel of activities based on your preferences and goals.
            </p>
          </Card>

          <Card className="p-6 border-2 border-blue-100 hover:border-blue-300 transition-colors">
            <div className="bg-blue-100 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
              <Sparkles className="text-blue-600 h-8 w-8" />
            </div>
            <h3 className="text-xl font-bold mb-2">Embrace Randomness</h3>
            <p className="text-gray-600">
              Spin the wheel and let beneficial chance guide you to your next adventure.
            </p>
          </Card>

          <Card className="p-6 border-2 border-blue-100 hover:border-blue-300 transition-colors">
            <div className="bg-blue-100 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
              <Clock className="text-blue-600 h-8 w-8" />
            </div>
            <h3 className="text-xl font-bold mb-2">Track Progress</h3>
            <p className="text-gray-600">
              Record activities, reflect on experiences, and discover patterns in your journey.
            </p>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <div className="bg-blue-50 rounded-3xl p-10">
          <h2 className="text-3xl font-bold mb-4">Ready to transform how you spend your time?</h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join Goali today and start turning your free time into meaningful experiences.
          </p>
          <Button className="rounded-full text-lg px-8 py-6 bg-blue-600 hover:bg-blue-700" asChild>
            <Link to="/onboarding">
              Start Your Journey
            </Link>
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="container mx-auto px-4 py-8 border-t border-gray-200 mt-12">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="text-xl font-bold text-blue-600 mb-4 md:mb-0">Goali</div>
          <div className="text-gray-500">© 2025 Goali. All rights reserved.</div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
