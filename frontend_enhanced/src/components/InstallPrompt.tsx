import { useEffect, useState } from 'react';

export function InstallPrompt() {
  const [installPromptEvent, setInstallPromptEvent] = useState<any>(null);
  const [showPrompt, setShowPrompt] = useState(false);

  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      // Store the event for later use
      setInstallPromptEvent(e);
      // Show our custom install prompt
      setShowPrompt(true);
      console.log('beforeinstallprompt event fired, prompt stored.'); // Added for debugging
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    console.log('Event listener for beforeinstallprompt added.'); // Added for debugging

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      console.log('Event listener for beforeinstallprompt removed.'); // Added for debugging
    };
  }, []);

  const handleInstallClick = () => {
    if (!installPromptEvent) {
      console.log('Install prompt event not available.'); // Added for debugging
      return;
    }

    // Show the install prompt
    installPromptEvent.prompt();
    console.log('Install prompt shown to user.'); // Added for debugging

    // Wait for the user to respond to the prompt
    installPromptEvent.userChoice.then((choiceResult: any) => {
      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }

      // Clear the saved prompt since it can't be used again
      setInstallPromptEvent(null);
      setShowPrompt(false);
    });
  };

  if (!showPrompt) {
    // console.log('Install prompt not shown because showPrompt is false.'); // Can be noisy, uncomment if needed
    return null;
  }

  console.log('Rendering InstallPrompt component.'); // Added for debugging
  return (
    <div className="fixed bottom-4 left-4 right-4 bg-primary text-white p-4 rounded-lg shadow-lg flex justify-between items-center z-50">
      <div>
        <p className="font-medium">Install Goali</p>
        <p className="text-sm opacity-90">Add to your home screen for the best experience</p>
      </div>
      <button
        onClick={handleInstallClick}
        className="bg-white text-primary px-4 py-2 rounded-md font-medium"
      >
        Install
      </button>
    </div>
  );
}
