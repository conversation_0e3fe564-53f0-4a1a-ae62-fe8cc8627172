import { useRef, useEffect, useCallback } from "react";
import { WheelState, WheelSegment } from "@/contexts/WheelContext"; // Import context types/enum

type WheelProps = {
  segments: WheelSegment[]; // Get segments as prop
  wheelState: WheelState; // Get state as prop
  selectedSegmentId: string | null; // Get selected ID as prop
  onSpinAnimationComplete: (selectedItem: WheelSegment | null) => void; // Callback for when animation finishes
};

const Wheel = ({
  segments,
  wheelState,
  selectedSegmentId,
  onSpinAnimationComplete,
}: WheelProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const offscreenCanvasRef = useRef<HTMLCanvasElement | null>(null); // Ref for offscreen canvas
  const spinStartTimeRef = useRef<number | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const rotationAngleRef = useRef<number>(0); // Current rotation in radians
  const targetRotationRef = useRef<number>(0); // Target rotation for spin end
  const spinDuration = 3000; // 3 seconds spin duration

  // --- Drawing Logic ---

  // Function to draw the static pointer
  const drawPointer = useCallback((ctx: CanvasRenderingContext2D, centerX: number, centerY: number, radius: number) => {
    ctx.fillStyle = "#FF0000"; // Red pointer
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - radius - 15); // Point slightly above wheel
    ctx.lineTo(centerX - 10, centerY - radius + 5);
    ctx.lineTo(centerX + 10, centerY - radius + 5);
    ctx.closePath();
    ctx.fill();
    ctx.strokeStyle = "#000000"; // Black outline
    ctx.lineWidth = 1;
    ctx.stroke();
  }, []);

  // Function to draw the wheel segments onto a canvas (offscreen or main)
  const drawWheelSegments = useCallback((targetCanvas: HTMLCanvasElement, currentSegments: WheelSegment[]) => {
    const ctx = targetCanvas.getContext("2d");
    if (!ctx || currentSegments.length === 0) return;

    const width = targetCanvas.width;
    const height = targetCanvas.height;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(centerX, centerY) * 0.9;
    const segmentAngle = (2 * Math.PI) / currentSegments.length;

    ctx.clearRect(0, 0, width, height);
    ctx.font = "bold 12px sans-serif"; // Slightly smaller font

    currentSegments.forEach((segment, index) => {
      const startAngle = index * segmentAngle; // Draw segments at base rotation 0
      const endAngle = startAngle + segmentAngle;

      // Segment Arc
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();
      ctx.fillStyle = segment.color || '#CCCCCC'; // Use default color if missing
      ctx.fill();
      ctx.strokeStyle = "#FFFFFF"; // White separator lines
      ctx.lineWidth = 2;
      ctx.stroke();

      // Segment Text
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate(startAngle + segmentAngle / 2); // Rotate context for text alignment
      ctx.textAlign = "right";
      ctx.textBaseline = "middle";
      ctx.fillStyle = "#000000"; // Black text for better contrast on light colors
      // Shortened name if too long
      const displayName = segment.name.length > 12 ? segment.name.substring(0, 12) + "..." : segment.name;
      ctx.fillText(displayName, radius * 0.8, 0); // Adjust text position slightly
      ctx.restore();
    });

    // Draw center circle overlay
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.1, 0, 2 * Math.PI); // Smaller center circle
    ctx.fillStyle = "#FFFFFF";
    ctx.fill();
    ctx.strokeStyle = "#AAAAAA"; // Lighter outline
    ctx.lineWidth = 1;
    ctx.stroke();

  }, []);

  /**
   * Determines which wheel item is at a given angle (pointer position)
   *
   * The pointer is fixed at the top (0 degrees), so we need to determine
   * which segment of the wheel is under the pointer when the wheel is rotated
   * to the given angle.
   *
   * @param angle The wheel's rotation angle (in radians)
   * @param currentSegments The array of wheel segments
   * @returns The wheel item at the pointer position, or null if none found
   */
  const getItemAtAngle = useCallback((angle: number, currentSegments: WheelSegment[]): WheelSegment | null => {
    if (currentSegments.length === 0) return null;

    const pointerAngleCanvas = 3 * Math.PI / 2; // Top position in canvas coordinates (from right, counter-clockwise)
    const angleRelativeToUnrotatedWheel = (pointerAngleCanvas - angle) % (2 * Math.PI);
    const normalizedAngle = (angleRelativeToUnrotatedWheel + (2 * Math.PI)) % (2 * Math.PI); // Ensure positive

    // console.log('[Wheel] Checking pointer position at wheel rotation:', angle, 'radians');
    // console.log('[Wheel] Normalized angle for segment calculation (relative to unrotated wheel):', normalizedAngle, 'radians');

    const totalAngle = 2 * Math.PI;
    const segmentAngle = totalAngle / currentSegments.length;

    let cumulativeAngle = 0;

    for (let i = 0; i < currentSegments.length; i++) {
      const item = currentSegments[i];
      const startAngle = cumulativeAngle;
      cumulativeAngle += segmentAngle;
      const endAngle = cumulativeAngle;

      if (normalizedAngle >= startAngle && normalizedAngle < endAngle) {
        // console.log(`[Wheel] Found item at pointer position: ${item.name} (Index: ${i})`);
        return item;
      }
    }

    console.warn('[Wheel] No exact segment found at pointer position. Using fallback logic.');
    let closestItem: WheelSegment | null = null;
    let minDiff = Infinity;
    cumulativeAngle = 0;
     for (let i = 0; i < currentSegments.length; i++) {
      const item = currentSegments[i];
      const startAngle = cumulativeAngle;
      cumulativeAngle += segmentAngle;
      const diff = Math.abs(normalizedAngle - startAngle);
      if (diff < minDiff) {
        minDiff = diff;
        closestItem = item;
      }
    }
    return closestItem || currentSegments[0] || null; // Ensure a fallback
  }, []);


  // Effect to create/update offscreen canvas when segments change
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || segments.length === 0) return;

    // Create or resize offscreen canvas
    if (!offscreenCanvasRef.current) {
      offscreenCanvasRef.current = document.createElement('canvas');
    }
    if (offscreenCanvasRef.current.width !== canvas.width || offscreenCanvasRef.current.height !== canvas.height) {
        offscreenCanvasRef.current.width = canvas.width;
        offscreenCanvasRef.current.height = canvas.height;
    }

    // Draw segments onto the offscreen canvas
    drawWheelSegments(offscreenCanvasRef.current, segments);

    // Initial draw onto the main canvas if not spinning
    if (wheelState !== WheelState.Spinning) {
        const mainCtx = canvas.getContext("2d");
        if (mainCtx && offscreenCanvasRef.current) {
            mainCtx.clearRect(0, 0, canvas.width, canvas.height);
            mainCtx.drawImage(offscreenCanvasRef.current, 0, 0);
            drawPointer(mainCtx, canvas.width / 2, canvas.height / 2, Math.min(canvas.width / 2, canvas.height / 2) * 0.9);
        }
    }

  }, [segments, drawWheelSegments, drawPointer, wheelState]); // Rerun only when segments change or state changes


  // --- Animation and Drawing Logic ---
  useEffect(() => {
    const canvas = canvasRef.current;
    const offscreenCanvas = offscreenCanvasRef.current;
    if (!canvas || !offscreenCanvas || segments.length === 0) return;

    const mainCtx = canvas.getContext("2d");
    if (!mainCtx) return;

    const width = canvas.width;
    const height = canvas.height;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(centerX, centerY) * 0.9;
    const segmentAngle = (2 * Math.PI) / segments.length;


    // Stop previous animation if running
    if (animationFrameRef.current !== null) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
    spinStartTimeRef.current = null; // Reset start time

    // Helper function to draw the current state (wheel + pointer + optional highlight)
    const drawCurrentState = (currentRotation: number, highlightSelectedItem: boolean = false) => {
       // Redraw using offscreen canvas
       mainCtx.clearRect(0, 0, width, height);
       mainCtx.save();
       mainCtx.translate(centerX, centerY);
       mainCtx.rotate(currentRotation);
       mainCtx.translate(-centerX, -centerY);
       if (offscreenCanvas) { // Ensure offscreen canvas exists
           mainCtx.drawImage(offscreenCanvas, 0, 0);
       }
       mainCtx.restore();

       // Draw static pointer on top
       drawPointer(mainCtx, centerX, centerY, radius);

       // Draw highlight if in Result state and item is selected
       if (highlightSelectedItem && selectedSegmentId) {
           // Find the index of the selected segment to calculate angles
           const selectedIndex = segments.findIndex(seg => seg.activity_tailored_id === selectedSegmentId || seg.id === selectedSegmentId); // Check both IDs

           if (selectedIndex !== -1) {
               // Calculate the angle range for the selected item in the *unrotated* wheel
               const selectedStartAngle = selectedIndex * segmentAngle;
               const selectedEndAngle = selectedStartAngle + segmentAngle;

               // Calculate the angle range in the *rotated* canvas coordinates
               // The wheel is rotated by `currentRotation` clockwise.
               // A point at angle `theta` (relative to wheel's right side, counter-clockwise)
               // is at canvas angle `theta + currentRotation`.
               // We need to draw the highlight arc from `selectedStartAngle + currentRotation` to `selectedEndAngle + currentRotation`.
               const highlightStartAngle = selectedStartAngle + currentRotation;
               const highlightEndAngle = selectedEndAngle + currentRotation;


               mainCtx.save();
               // No need to translate again, already in canvas coords
               mainCtx.beginPath();
               // Draw arc for the selected segment
               mainCtx.arc(centerX, centerY, radius * 1.02, highlightStartAngle, highlightEndAngle); // Slightly larger radius
               mainCtx.lineWidth = 6; // Thicker line for highlight
               mainCtx.strokeStyle = "rgba(255, 255, 0, 0.7)"; // Yellow highlight with some transparency
               mainCtx.stroke();
               mainCtx.restore();
           }
       }
    };


    if (wheelState === WheelState.Spinning) {
      // Calculate target rotation (ensure it lands pointing up)
      const randomSpins = 4 + Math.random() * 3; // 4-7 full rotations
      const randomSegmentIndex = Math.floor(Math.random() * segments.length);
      // Calculate angle to make the middle of the selected segment point upwards (towards the pointer)
      const middleAngleOfSelectedSegment = (randomSegmentIndex + 0.5) * segmentAngle;
      let targetRotation = middleAngleOfSelectedSegment - (3 * Math.PI / 2);

      // Add full rotations to ensure minimum spins
      const currentFullRotations = Math.floor(rotationAngleRef.current / (2 * Math.PI));
      const minRotationRadians = (currentFullRotations + randomSpins) * (2 * Math.PI);

      // Ensure targetRotation is at least minRotationRadians + the calculated offset
      targetRotationRef.current = targetRotation + minRotationRadians;
      // If the calculated target is less than the minimum required, add another full rotation
      if (targetRotationRef.current < rotationAngleRef.current + randomSpins * 2 * Math.PI) {
           targetRotationRef.current += 2 * Math.PI;
      }


      const animateSpin = (timestamp: number) => {
        if (!spinStartTimeRef.current) spinStartTimeRef.current = timestamp;
        const elapsed = timestamp - spinStartTimeRef.current;
        let progress = elapsed / spinDuration;
        progress = Math.min(progress, 1); // Cap progress at 1

        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        rotationAngleRef.current = easeOutQuart * targetRotationRef.current;

        drawCurrentState(rotationAngleRef.current, false); // Don't highlight while spinning

        if (progress < 1) {
          animationFrameRef.current = requestAnimationFrame(animateSpin);
        } else {
          // Spin finished
          rotationAngleRef.current = targetRotationRef.current; // Set final rotation
          spinStartTimeRef.current = null;
          animationFrameRef.current = null;

          // Determine selected segment based on the final rotation angle
          const actualSelectedItem = getItemAtAngle(rotationAngleRef.current, segments);

          // Call the completion callback with the determined item
          onSpinAnimationComplete(actualSelectedItem);
        }
      };
      animationFrameRef.current = requestAnimationFrame(animateSpin);

    } else {
      // If not spinning (Review or Result state), draw the wheel statically
      // In Result state, highlight the selected item
      drawCurrentState(rotationAngleRef.current, wheelState === WheelState.Result);
    }

    // Cleanup function
    return () => {
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  // Update dependencies: use wheelState, selectedSegmentId, onSpinAnimationComplete
  }, [wheelState, segments, selectedSegmentId, onSpinAnimationComplete, drawWheelSegments, drawPointer, getItemAtAngle]);

  return (
    <div className="relative flex items-center justify-center">
      <canvas
        ref={canvasRef}
        width={300} // Keep fixed size for canvas drawing consistency
        height={300}
        className="max-w-full h-auto" // Allow CSS to scale visually if needed
      />
    </div>
  );
};

export default Wheel;
