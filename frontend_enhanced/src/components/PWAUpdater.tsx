import { useEffect, useState } from 'react';
import { useRegisterSW } from 'virtual:pwa-register/react';

export default function PWAUpdater() {
  const [offlineReady, setOfflineReady] = useState(false);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  const {
    needRefresh: [needRefresh, setNeedRefresh], // needRefresh state is provided but not used directly in this example UI
    updateServiceWorker
  } = useRegisterSW({
    onRegistered(r) {
      console.log('SW registered:', r);
    },
    onRegisterError(error) {
      console.log('SW registration error:', error);
    },
    onOfflineReady() {
      console.log('App ready to work offline');
      setOfflineReady(true);
    },
    onNeedRefresh() {
      console.log('New version available, need refresh');
      setUpdateAvailable(true);
    }
  });

  const close = () => {
    setOfflineReady(false);
    setUpdateAvailable(false);
    setNeedRefresh(false); // Also reset the needRefresh state if closing manually
  };

  const update = () => {
    updateServiceWorker(true);
    // The page will reload automatically after the update
  };

  // Render the UI only if offline is ready or an update is available
  if (!offlineReady && !updateAvailable) {
    return null;
  }

  return (
    <div className="fixed bottom-16 right-4 m-4 p-3 bg-white shadow-lg rounded-lg z-50 border border-gray-200">
      {offlineReady && (
        <div className="text-sm mb-2 last:mb-0">
          <span>App ready to work offline.</span>
          <button className="ml-2 text-blue-600 hover:underline font-medium" onClick={close}>
            Close
          </button>
        </div>
      )}
      {updateAvailable && (
        <div className="text-sm mb-2 last:mb-0">
          <span>New version available!</span>
          <button className="ml-2 text-green-600 hover:underline font-medium" onClick={update}>
            Update
          </button>
          <button className="ml-2 text-gray-500 hover:underline font-medium" onClick={close}>
            Dismiss
          </button>
        </div>
      )}
    </div>
  );
}
