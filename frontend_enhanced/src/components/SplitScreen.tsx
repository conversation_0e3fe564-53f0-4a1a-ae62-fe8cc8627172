
import { useState, useRef, useEffect } from "react";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";

type SplitScreenProps = {
  topContent: React.ReactNode;
  bottomContent: React.ReactNode;
  defaultSplit?: number; // percentage for top section (0-100)
};

const SplitScreen = ({
  topContent,
  bottomContent,
  defaultSplit = 60,
}: SplitScreenProps) => {
  return (
    <ResizablePanelGroup
      direction="vertical"
      className="min-h-[calc(100vh-56px)]"
    >
      <ResizablePanel defaultSize={defaultSplit} minSize={30}>
        <div className="h-full overflow-auto p-2">{topContent}</div>
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel defaultSize={100 - defaultSplit} minSize={30}>
        <div className="h-full overflow-auto p-2">{bottomContent}</div>
      </ResizablePanel>
    </ResizablePanelGroup>
  );
};

export default SplitScreen;
