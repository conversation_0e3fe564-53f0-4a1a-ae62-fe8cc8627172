
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Send, Mic } from "lucide-react";
import { useChatMessages } from "@/hooks/useChatMessages";

type Message = {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
};

type ChatInterfaceProps = {
  initialMessages?: Message[];
  inputPlaceholder?: string;
  standaloneMode?: boolean;
};

const ChatInterface = ({ 
  initialMessages = [],
  inputPlaceholder = "Type a message...",
  standaloneMode = false
}: ChatInterfaceProps) => {
  const [inputValue, setInputValue] = useState('');
  const [isAiTyping, setIsAiTyping] = useState(false);
  const [localMessages, setLocalMessages] = useState<Message[]>([]);
  
  // Only use the WebSocket context if not in standalone mode
  let contextMessages: Message[] = [];
  let sendMessage: (content: string) => void = () => {};
  
  if (!standaloneMode) {
    try {
      // Try to use the WebSocket context, but don't throw if it's not available
      const { messages, sendMessage: contextSendMessage } = useChatMessages();
      contextMessages = messages;
      sendMessage = contextSendMessage;
    } catch (error) {
      console.warn("WebSocket context not available. Using standalone mode.");
      // If WebSocket context is not available, we'll fall back to standalone mode
    }
  }

  // Combine initial messages with either context messages or local messages
  const allMessages = [...initialMessages, ...(standaloneMode ? localMessages : contextMessages)];
  
  const handleSendMessage = () => {
    if (!inputValue.trim()) return;
    
    // Create a new message object
    const newMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };
    
    // Clear input and show typing indicator
    setInputValue('');
    setIsAiTyping(true);
    
    if (standaloneMode) {
      // In standalone mode, add the message to local state
      setLocalMessages(prevMessages => [...prevMessages, newMessage]);
      
      // Simulate AI response in standalone mode
      setTimeout(() => {
        const aiResponse: Message = {
          id: Date.now().toString(),
          content: "This is a simulated response in standalone mode.",
          sender: 'ai',
          timestamp: new Date(),
        };
        setLocalMessages(prevMessages => [...prevMessages, aiResponse]);
        setIsAiTyping(false);
      }, 1500);
    } else {
      // Send the message through our hook, which uses the WebSocketContext
      sendMessage(inputValue);
      
      // Typing indicator will be cleared when the response comes in through the context
      setTimeout(() => {
        setIsAiTyping(false);
      }, 1500);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-1 pr-4">
        <div className="flex flex-col space-y-4 py-4">
          {allMessages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <Card
                className={`max-w-[80%] p-3 ${
                  message.sender === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                <p className="text-sm">{message.content}</p>
              </Card>
            </div>
          ))}
          
          {isAiTyping && (
            <div className="flex justify-start">
              <Card className="max-w-[80%] p-3 bg-gray-100">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:0.2s]" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:0.4s]" />
                </div>
              </Card>
            </div>
          )}
        </div>
      </ScrollArea>
      
      <div className="mt-4 flex space-x-2">
        <Button variant="outline" size="icon" className="shrink-0">
          <Mic className="h-5 w-5" />
        </Button>
        <Input
          placeholder={inputPlaceholder}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') handleSendMessage();
          }}
          className="flex-1"
        />
        <Button size="icon" onClick={handleSendMessage} className="shrink-0">
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default ChatInterface;
