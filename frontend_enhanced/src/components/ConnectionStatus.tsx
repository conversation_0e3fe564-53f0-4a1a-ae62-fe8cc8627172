
import { useWebSocket } from "@/contexts/WebSocketContext";
import { Badge } from "@/components/ui/badge";
import { Wifi, WifiOff } from "lucide-react";

const ConnectionStatus = () => {
  const { connected, connectionError } = useWebSocket();
  
  if (connectionError) {
    return (
      <Badge variant="destructive" className="flex items-center gap-1">
        <WifiOff className="h-3 w-3" />
        <span>Connection Error</span>
      </Badge>
    );
  }
  
  if (connected) {
    return (
      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
        <Wifi className="h-3 w-3" />
        <span>Connected</span>
      </Badge>
    );
  }
  
  return (
    <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1">
      <WifiOff className="h-3 w-3" />
      <span>Connecting...</span>
    </Badge>
  );
};

export default ConnectionStatus;
