
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Battery, ArrowRight } from "lucide-react";

export type Activity = {
  id: string;
  name: string;
  description: string;
  duration: string;
  energyLevel: 'Low' | 'Medium' | 'High';
  category: string;
  color: string;
  probability?: number;
  tips?: string[];
};

type ActivityCardProps = {
  activity: Activity;
  isSelected?: boolean;
  onClick?: () => void;
  showDetails?: boolean;
};

const ActivityCard = ({
  activity,
  isSelected = false,
  onClick,
  showDetails = false,
}: ActivityCardProps) => {
  return (
    <Card 
      className={`transition-all cursor-pointer hover:shadow-md ${
        isSelected ? 'border-2 border-blue-500 shadow-md' : ''
      }`}
      onClick={onClick}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center gap-3">
          <div 
            className="w-4 h-4 rounded-full" 
            style={{ backgroundColor: activity.color }}
          ></div>
          <CardTitle className="text-lg">{activity.name}</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-600 mb-3">
          {showDetails ? activity.description : activity.description.substring(0, 60) + (activity.description.length > 60 ? '...' : '')}
        </p>
        
        <div className="flex flex-wrap gap-2 items-center mb-2">
          <div className="flex items-center text-xs text-gray-600">
            <Clock className="w-3 h-3 mr-1" />
            {activity.duration}
          </div>
          
          <div className="flex items-center text-xs text-gray-600">
            <Battery className="w-3 h-3 mr-1" />
            {activity.energyLevel}
          </div>
          
          <Badge variant="outline" className="text-xs">
            {activity.category}
          </Badge>
          
          {activity.probability !== undefined && (
            <Badge variant="secondary" className="ml-auto">
              {activity.probability}%
            </Badge>
          )}
        </div>
        
        {showDetails && (
          <div className="flex justify-end mt-2">
            <ArrowRight className="w-4 h-4 text-blue-600" />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActivityCard;
