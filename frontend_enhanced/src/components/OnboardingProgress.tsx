
import { Check } from "lucide-react";

type OnboardingProgressProps = {
  steps: string[];
  currentStep: number;
};

const OnboardingProgress = ({ steps, currentStep }: OnboardingProgressProps) => {
  return (
    <div className="w-full py-6">
      <div className="flex items-center justify-center">
        {steps.map((step, index) => (
          <div key={index} className="flex items-center">
            {/* Step circle */}
            <div
              className={`flex items-center justify-center w-10 h-10 rounded-full ${
                index < currentStep
                  ? 'bg-blue-600 text-white'
                  : index === currentStep
                  ? 'bg-blue-200 border-2 border-blue-600 text-blue-600'
                  : 'bg-gray-200 text-gray-400'
              }`}
            >
              {index < currentStep ? (
                <Check className="w-5 h-5" />
              ) : (
                <span>{index + 1}</span>
              )}
            </div>
            
            {/* Connector line */}
            {index < steps.length - 1 && (
              <div
                className={`w-12 h-1 ${
                  index < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              ></div>
            )}
          </div>
        ))}
      </div>
      
      <div className="flex justify-center mt-2 text-sm text-gray-600">
        {steps[currentStep]}
      </div>
    </div>
  );
};

export default OnboardingProgress;
