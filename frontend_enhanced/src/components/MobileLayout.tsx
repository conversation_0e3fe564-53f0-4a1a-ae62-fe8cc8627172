
import React from 'react';
import { Link, useLocation } from "react-router-dom";
import { Home, History, User } from "lucide-react";

type MobileLayoutProps = {
  children: React.ReactNode;
  showMenu?: boolean;
};

const MobileLayout = ({ children, showMenu = true }: MobileLayoutProps) => {
  const location = useLocation();

  const menuItems = [
    { label: 'Home', icon: <Home className="h-5 w-5" />, path: '/home' },
    { label: 'History', icon: <History className="h-5 w-5" />, path: '/activity-history' },
    { label: 'Profile', icon: <User className="h-5 w-5" />, path: '/profile' },
  ];

  return (
    <div className="min-h-screen max-w-md mx-auto bg-neutral-light relative flex flex-col">
      {showMenu && (
        <header className="border-b px-4 py-3">
          <nav className="flex items-center justify-between">
            {menuItems.map((item) => (
              <Link 
                key={item.label}
                to={item.path}
                className={`flex flex-col items-center px-2 py-1 rounded-md text-sm ${
                  location.pathname === item.path 
                    ? 'text-blue-600 font-medium' 
                    : 'text-gray-600 hover:text-blue-500'
                }`}
              >
                {item.icon}
                <span className="text-xs mt-1">{item.label}</span>
              </Link>
            ))}
          </nav>
        </header>
      )}
      <main className="flex-1 flex flex-col">
        {children}
      </main>
    </div>
  );
};

export default MobileLayout;
