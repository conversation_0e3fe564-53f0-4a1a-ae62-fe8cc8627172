import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { WebSocketProviderSelector } from "./contexts/WebSocketProviderSelector";
import { UserProvider } from "./contexts/UserContext";
import Index from "./pages/Index";
import Onboarding from "./pages/Onboarding";
import Home from "./pages/Home";
import ActivityReview from "./pages/ActivityReview";
import WheelSpin from "./pages/WheelSpin";
import ActivityDetail from "./pages/ActivityDetail";
import ActivityHistory from "./pages/ActivityHistory";
import Profile from "./pages/Profile";
import Offline from "./pages/Offline"; // Import the Offline page
import NotFound from "./pages/NotFound";
import PWAUpdater from "./components/PWAUpdater"; // Import PWA Updater
import { InstallPrompt } from "./components/InstallPrompt"; // Import Install Prompt

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <WebSocketProviderSelector>
          <UserProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/onboarding" element={<Onboarding />} />
                <Route path="/home" element={<Home />} />
                <Route path="/activity-review" element={<ActivityReview />} />
                <Route path="/wheel-spin" element={<WheelSpin />} />
                <Route path="/activity-detail" element={<ActivityDetail />} />
                <Route path="/activity-history" element={<ActivityHistory />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/offline" element={<Offline />} /> {/* Add Offline route */}
                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
            {/* Render PWA components outside the router */}
            <PWAUpdater />
            <InstallPrompt />
          </UserProvider>
        </WebSocketProviderSelector>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
