import { 
  BaseMessage, 
  ChatMessage, 
  SystemMessage, 
  WheelData, 
  WheelItem 
} from '../types/api';

export class MockWebSocket {
  private handlers: Record<string, (event: any) => void> = {};
  private mockDelay = 500; // Simulate network latency

  // Mock WebSocket methods
  set onopen(handler: (event: any) => void) {
    this.handlers.open = handler;
  }

  set onmessage(handler: (event: any) => void) {
    this.handlers.message = handler;
  }

  set onerror(handler: (event: any) => void) {
    this.handlers.error = handler;
  }

  set onclose(handler: (event: any) => void) {
    this.handlers.close = handler;
  }

  // Simulate WebSocket events
  triggerOpen() {
    if (this.handlers.open) {
      this.handlers.open({ type: 'open' });
    }
  }

  triggerMessage(data: BaseMessage) {
    if (this.handlers.message) {
      this.handlers.message({ data: JSON.stringify(data) });
    }
  }

  triggerError(error: string) {
    if (this.handlers.error) {
      this.handlers.error({ type: 'error', message: error });
    }
  }

  close() {
    if (this.handlers.close) {
      this.handlers.close({ type: 'close' });
    }
  }

  // Mock send method with response simulation
  send(data: string) {
    const message = JSON.parse(data);
    
    setTimeout(() => {
      switch (message.type) {
        case 'chat_message':
          this.handleChatMessage(message);
          break;
        case 'wheel_request':
          this.handleWheelRequest();
          break;
        case 'spin_result':
          this.handleSpinResult(message);
          break;
        default:
          this.triggerError('Unknown message type');
      }
    }, this.mockDelay);
  }

  private handleChatMessage(message: any) {
    const userMessage: ChatMessage = {
      type: 'chat_message',
      content: message.content.message,
      is_user: true,
      timestamp: new Date(),
      id: Date.now().toString(),
      sender: 'user'
    };
    this.triggerMessage(userMessage);

    // Mock AI response
    setTimeout(() => {
      const aiMessage: ChatMessage = {
        type: 'chat_message',
        content: `This is a mock response to: "${message.content.message}"`,
        is_user: false,
        timestamp: new Date(),
        id: Date.now().toString(),
        sender: 'ai'
      };
      this.triggerMessage(aiMessage);
    }, 1000);
  }

  private handleWheelRequest() {
    const mockWheel: WheelData = {
      type: 'wheel_data',
      content: { status: 'success' }, // Added required content field
      wheel: {
        name: 'Activity Wheel',
        items: this.generateMockWheelItems()
      }
    };
    this.triggerMessage(mockWheel);
  }

  private handleSpinResult(message: any) {
    const systemMessage: SystemMessage = {
      type: 'system_message',
      content: `Selected activity: ${message.content.name}`,
      level: 'info'
    };
    this.triggerMessage(systemMessage);
  }

  private generateMockWheelItems(): WheelItem[] {
    return [
      {
        id: '1',
        name: 'Morning Meditation',
        description: '10-minute guided meditation',
        percentage: 25,
        color: '#66BB6A',
        domain: 'wellness',
        base_challenge_rating: 20,
        activity_tailored_id: 'act-1'
      },
      {
        id: '2',
        name: 'Quick Exercise',
        description: '15-minute workout routine',
        percentage: 20,
        color: '#42A5F5',
        domain: 'fitness',
        base_challenge_rating: 30,
        activity_tailored_id: 'act-2'
      },
      // ... Add more items as needed
    ];
  }
}
