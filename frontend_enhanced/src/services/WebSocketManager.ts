
// Mock WebSocket Manager for prototype
export class WebSocketManager {
  private ws: WebSocket | null = null;
  private url: string = "ws://localhost:8080"; // Mock URL
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectTimeout: number = 2000;
  
  // Event handlers
  public onConnected: () => void = () => {};
  public onDisconnected: () => void = () => {};
  public onError: (error: string) => void = () => {};
  public onChatMessage: (message: any) => void = () => {};
  public onSystemMessage: (message: any) => void = () => {};
  public onWorkflowUpdate: (status: string) => void = () => {};
  
  // Connection management
  public connect(): void {
    try {
      // In the prototype, we're simulating WebSocket connection
      console.log("WebSocketManager: Simulating connection");
      
      // Simulate successful connection
      setTimeout(() => {
        this.reconnectAttempts = 0;
        this.onConnected();
        this.simulateWelcomeMessage();
      }, 500);
    } catch (error) {
      this.handleConnectionError(error instanceof Error ? error.message : "Unknown error");
    }
  }
  
  public disconnect(): void {
    console.log("WebSocketManager: Disconnecting");
    this.reconnectAttempts = 0;
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.onDisconnected();
  }
  
  // Message sending methods
  public sendChatMessage(content: string): void {
    console.log(`WebSocketManager: Sending chat message: ${content}`);
    
    // Simulate AI response
    setTimeout(() => {
      const response = {
        id: Date.now().toString(),
        content: `I've understood your message: "${content}". Let me help you with that!`,
        sender: 'ai',
        timestamp: new Date(),
      };
      this.onChatMessage(response);
    }, 1500);
  }
  
  public sendSpinResult(segmentId: string): void {
    console.log(`WebSocketManager: Sending spin result: ${segmentId}`);
    
    // Simulate workflow update
    setTimeout(() => {
      this.onWorkflowUpdate('activity_selected');
    }, 500);
  }
  
  public sendActivityStatus(activityId: string, status: string): void {
    console.log(`WebSocketManager: Sending activity status update: ${activityId} -> ${status}`);
    
    // Simulate workflow update based on status
    setTimeout(() => {
      this.onWorkflowUpdate(status === 'completed' ? 'activity_completed' : 'activity_in_progress');
    }, 500);
  }
  
  // Helper methods
  private handleConnectionError(error: string): void {
    console.error(`WebSocketManager: Connection error: ${error}`);
    this.onError(error);
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`WebSocketManager: Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      setTimeout(() => this.connect(), this.reconnectTimeout);
    } else {
      console.error("WebSocketManager: Max reconnection attempts reached");
    }
  }
  
  private simulateWelcomeMessage(): void {
    const welcomeMessage = {
      id: Date.now().toString(),
      content: "Welcome to Goali! I'm here to help you discover new activities and break out of your comfort zone.",
      sender: 'ai',
      timestamp: new Date(),
    };
    
    setTimeout(() => {
      this.onChatMessage(welcomeMessage);
    }, 1000);
  }
}
