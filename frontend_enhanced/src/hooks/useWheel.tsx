import { useState, useCallback, useEffect, useMemo } from 'react';
import { useWebSocket } from '@/contexts/WebSocketProviderSelector'; // Import hook from Selector
import { useSimulatedIntelligence } from './useSimulatedIntelligence'; // For fallback data
import { WheelState } from '@/contexts/WheelContext'; // Use alias for WheelState import

// Define WheelSegment locally based on API contract for clarity within this hook
// (Could also be imported if defined centrally and exported from api types)
export interface WheelSegment {
  id: string; // Unique identifier for the wheel item itself (may differ from activity_tailored_id)
  name: string;
  description: string;
  percentage: number;
  color: string;
  domain: string;
  base_challenge_rating: number;
  activity_tailored_id: string; // Reference to the underlying ActivityTailored object
}

// Define the hook's return type
export interface UseWheelReturn {
  wheelState: WheelState;
  segments: WheelSegment[];
  selectedSegmentId: string | null; // Stores the activity_tailored_id of the selected item
  isSpinEnabled: boolean;
  isLoading: boolean; // Combined loading state
  error: string | null;
  spinWheel: () => void;
  setActivityResult: (selectedItem: WheelSegment | null) => void; // Sets result state and sends to backend
  resetWheel: () => void; // Action to reset to Review state
}

export const useWheel = (): UseWheelReturn => {
  const [wheelState, setWheelState] = useState<WheelState>(WheelState.Loading);
  const [selectedSegmentId, setSelectedSegmentId] = useState<string | null>(null);
  const [internalError, setInternalError] = useState<string | null>(null);

  // Get WebSocket context - Selector ensures a valid context (real or mock) is always provided
  // No try-catch needed here if selector guarantees a provider
  const {
    lastWheelData,
    sendSpinResult,
    isProcessing: isWsProcessing,
    workflowState,
    isConnected,
    connectionError,
  } = useWebSocket(); // Directly call the hook

  // Use simulated data hook for fallback generation logic
  const { generateContextualActivities } = useSimulatedIntelligence();

  // Determine segments: Use WebSocket data if available and connected, otherwise use simulation
  const segments = useMemo<WheelSegment[]>(() => {
    // Prioritize real data if connected and available
    if (isConnected && lastWheelData?.wheel?.items && lastWheelData.wheel.items.length > 0) {
      console.log("[useWheel] Using segments from WebSocket:", lastWheelData.wheel.items);
      return lastWheelData.wheel.items as WheelSegment[];
    }
    // Use simulation if not connected (or no data yet while connected)
    if (!isConnected) {
        console.log("[useWheel] Using simulated segments (not connected).");
        return generateContextualActivities();
    }
    // If connected but no data yet, return empty array (isLoading should be true)
    console.log("[useWheel] No segments available yet (connected but no data).");
    return [];
  }, [isConnected, lastWheelData, generateContextualActivities]);

  // Combined loading state: Loading if state is Loading OR (connected AND processing) OR (disconnected AND no segments yet)
  const isLoading = useMemo(() => {
    return wheelState === WheelState.Loading || (isConnected && isWsProcessing) || (!isConnected && segments.length === 0);
  }, [wheelState, isConnected, isWsProcessing, segments.length]);


  // Determine if spinning is enabled based on connection status and workflow state
  const isSpinEnabled = useMemo(() => {
    const uiReady = !isLoading && wheelState === WheelState.Review;
    if (!isConnected) {
        // Standalone mode: enable if UI is ready
        // console.log(`[useWheel] Standalone Mode - Spin enabled check: uiReady=${uiReady} -> ${uiReady}`);
        return uiReady;
    } else {
        // Connected mode: enable if UI is ready AND backend workflow is ready
        const backendReady = workflowState?.currentWorkflow === 'pre_spin' && workflowState?.status === 'completed';
        const enabled = uiReady && backendReady;
        // console.log(`[useWheel] Connected Mode - Spin enabled check: uiReady=${uiReady}, backendReady=${backendReady} -> ${enabled}`);
        return enabled;
    }
  }, [isConnected, isLoading, wheelState, workflowState]);

  // Update UI state based on segment availability and connection errors
  useEffect(() => {
    if (connectionError) {
      setInternalError(connectionError);
      setWheelState(WheelState.Error);
    } else if (!isLoading && segments.length > 0 && wheelState === WheelState.Loading) {
      // If loaded segments and was in Loading state, move to Review
      setWheelState(WheelState.Review);
      setInternalError(null);
    } else if (!isLoading && segments.length === 0 && wheelState !== WheelState.Error && isConnected) {
       // If connected, not loading, but no segments, stay Loading (maybe waiting for requestWheel)
       setWheelState(WheelState.Loading);
    } else if (!isLoading && segments.length === 0 && !isConnected) {
        // If not connected, not loading, but no segments (e.g., simulation failed?), show error.
        setInternalError("Failed to load simulated segments.");
        setWheelState(WheelState.Error);
    }
  }, [isLoading, segments, connectionError, wheelState, isConnected]);


  // Action to initiate spinning
  const spinWheel = useCallback(() => {
    if (isSpinEnabled) {
      console.log('[useWheel] Initiating spin...');
      setInternalError(null);
      setSelectedSegmentId(null); // Clear previous selection
      setWheelState(WheelState.Spinning);
      // Haptic feedback
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    } else {
      console.warn('[useWheel] Spin attempt failed. Conditions not met:', { isSpinEnabled, isLoading, wheelState, isConnected, workflowState });
      setInternalError("Cannot spin wheel right now.");
      // Optionally set state back to Review if it was somehow stuck
      if (wheelState === WheelState.Spinning) setWheelState(WheelState.Review);
    }
  }, [isSpinEnabled, isLoading, wheelState, isConnected, workflowState]); // Added dependencies

  // Action called by the parent component after spin animation completes
  // It sets the result state and sends the result to the backend (if connected)
  const setActivityResult = useCallback((selectedItem: WheelSegment | null) => {
    if (selectedItem) {
      console.log(`[useWheel] Setting activity result: ${selectedItem.name} (ID: ${selectedItem.activity_tailored_id})`);
      setSelectedSegmentId(selectedItem.activity_tailored_id); // Store the correct ID
      setWheelState(WheelState.Result);

      // Conditionally send result to backend via WebSocket
      if (isConnected) {
          console.log("[useWheel] Sending spin result to backend...");
          const success = sendSpinResult(
            selectedItem.activity_tailored_id,
            selectedItem.name,
            selectedItem.description
          );
          if (!success) {
            console.error("[useWheel] Failed to send spin result via WebSocket.");
            setInternalError("Failed to report spin result to server.");
            // Consider reverting state? Or just show error?
          }
      } else {
          console.log("[useWheel] Standalone mode: Spin result not sent to backend.");
      }
    } else {
      console.error("[useWheel] Spin completed but no valid item selected.");
      setInternalError("Spin finished with no selection.");
      setSelectedSegmentId(null);
      setWheelState(WheelState.Review); // Go back to review state on error
    }
  }, [isConnected, sendSpinResult]); // Added isConnected dependency

  // Action to reset the wheel state back to Review
  const resetWheel = useCallback(() => {
    console.log('[useWheel] Resetting wheel state to Review.');
    setInternalError(null);
    setSelectedSegmentId(null);
    setWheelState(WheelState.Review);
    // Optionally trigger a refresh of wheel data from backend here if needed
    // if (isConnected) { requestWheel(); } // Example
  }, []); // Removed isConnected dependency, reset is purely UI state

  return {
    wheelState,
    segments,
    selectedSegmentId,
    isSpinEnabled,
    isLoading,
    error: internalError || connectionError, // Combine internal and connection errors
    spinWheel,
    setActivityResult,
    resetWheel,
  };
};
