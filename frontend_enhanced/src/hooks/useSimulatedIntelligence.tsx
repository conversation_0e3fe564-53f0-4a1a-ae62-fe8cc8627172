import { useState, useEffect } from 'react';

// Define interface based on API Contract wheel.items
interface WheelItem {
  id: string; // Placeholder
  name: string;
  description: string;
  percentage: number;
  color: string; // Placeholder
  domain: string; // Mapped from category
  base_challenge_rating: number; // Placeholder
  activity_tailored_id: string; // Placeholder
}

// Original Activity structure generated by the hook logic
interface GeneratedActivity {
  name: string;
  description: string;
  category: string;
}

interface UserContext {
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night' | '';
  deviceType: 'mobile' | 'desktop' | '';
  hasCompletedActivities: boolean;
  possibleInterests: string[];
}

// Helper to generate placeholder colors based on domain/category
const domainColors: Record<string, string> = {
  relaxation: '#AEC6CF', // Pastel Blue
  physical: '#77DD77',   // Pastel Green
  creative: '#FDFD96',   // Pastel Yellow
  learning: '#FFB347',   // Pastel Orange
  social: '#FF6961',     // Pastel Red
  growth: '#C3B1E1',     // Pastel Purple
  mindfulness: '#B2EBF2', // Light Cyan
  productivity: '#DDDDDD', // Light Grey
  default: '#CCCCCC'      // Default Grey
};

export function useSimulatedIntelligence() {
  const [userContext, setUserContext] = useState<UserContext>({
    timeOfDay: '',
    deviceType: '',
    hasCompletedActivities: false,
    possibleInterests: ['creative', 'physical', 'learning', 'social', 'relaxation'],
  });

  useEffect(() => {
    // --- Detect Context ---
    let detectedTimeOfDay: UserContext['timeOfDay'] = 'night';
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) detectedTimeOfDay = 'morning';
    else if (hour >= 12 && hour < 17) detectedTimeOfDay = 'afternoon';
    else if (hour >= 17 && hour < 22) detectedTimeOfDay = 'evening';

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    const detectedDeviceType: UserContext['deviceType'] = isMobile ? 'mobile' : 'desktop';

    // Check localStorage safely
    let detectedHasCompleted = false;
    try {
      detectedHasCompleted = localStorage.getItem('completedActivities') !== null;
    } catch (error) {
      console.error("Could not access localStorage:", error);
    }

    setUserContext(prev => ({
      ...prev,
      timeOfDay: detectedTimeOfDay,
      deviceType: detectedDeviceType,
      hasCompletedActivities: detectedHasCompleted,
    }));

  }, []);

  // --- Generate Activities and Transform to WheelItem structure ---
  const generateContextualActivities = (): WheelItem[] => {
    const { timeOfDay, deviceType, hasCompletedActivities } = userContext;

    if (!timeOfDay) return [];

    const timeBasedActivities: Record<UserContext['timeOfDay'], GeneratedActivity[]> = {
      morning: [
        { name: 'Morning Meditation', description: 'Start your day with 10 minutes of mindfulness', category: 'relaxation' },
        { name: 'Quick Stretching', description: 'Energize your body with a 5-minute morning stretch routine', category: 'physical' },
      ],
      afternoon: [
        { name: 'Creative Writing', description: 'Spend 15 minutes writing freely about any topic', category: 'creative' },
        { name: 'Language Learning', description: 'Practice a new language for 10 minutes', category: 'learning' },
      ],
      evening: [
        { name: 'Gratitude Journaling', description: 'Reflect on 3 things you\'re grateful for today', category: 'relaxation' },
        { name: 'Evening Walk', description: 'Take a relaxing 20-minute walk before dinner', category: 'physical' },
      ],
      night: [
        { name: 'Reading Session', description: 'Read a book for 15 minutes before sleep', category: 'learning' },
        { name: 'Deep Breathing', description: '5 minutes of deep breathing to prepare for sleep', category: 'relaxation' },
      ],
      '': []
    };

    let generatedActivities: GeneratedActivity[] = [...timeBasedActivities[timeOfDay]];

    if (hasCompletedActivities) {
      generatedActivities.push({
        name: 'Building on Progress',
        description: 'Continue an activity you enjoyed previously',
        category: 'growth',
      });
    }

    if (deviceType === 'mobile') {
      generatedActivities.push({
        name: 'Phone-Free Time',
        description: 'Disconnect for 30 minutes and observe what you notice',
        category: 'mindfulness',
      });
    } else if (deviceType === 'desktop') {
      generatedActivities.push({
        name: 'Digital Cleanup',
        description: 'Organize your files or desktop for 10 minutes',
        category: 'productivity',
      });
    }

    // Transform GeneratedActivity[] to WheelItem[]
    return generatedActivities.map((activity, index) => {
      const domain = activity.category; // Map category to domain
      const placeholderId = `sim-${domain}-${index}`;
      return {
        id: placeholderId, // Placeholder ID
        name: activity.name,
        description: activity.description,
        percentage: Math.floor(Math.random() * 20) + 10, // Random percentage 10-30%
        color: domainColors[domain] || domainColors.default, // Placeholder color based on domain
        domain: domain,
        base_challenge_rating: Math.floor(Math.random() * 41) + 30, // Placeholder rating 30-70
        activity_tailored_id: placeholderId, // Placeholder tailored ID (can be same as id for now)
        // 'explanation' field is omitted as it's not in the WheelItem structure
      };
    });
  };

  // Note: The hook now returns a function that yields WheelItem[]
  return {
    userContext,
    generateContextualActivities,
  };
}
