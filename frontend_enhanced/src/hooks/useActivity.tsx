import { useCallback, useState } from 'react';
import { Activity } from '../components/ActivityCard';

// Sample activity data based on API contract wheel items
const activities: Activity[] = [
  {
    id: "1",
    name: "Read a book",
    description: "Take 30 minutes to read a few chapters of a book you've been wanting to start.",
    duration: "30 min",
    energyLevel: "Low",
    category: "Relaxation",
    color: "#FF8C42",
    probability: 15,
    tips: [
      "Find a quiet, comfortable spot",
      "Turn off notifications on your phone",
      "Set a timer if you tend to lose track of time",
      "Keep a notebook nearby to jot down any thoughts"
    ]
  },
  {
    id: "2",
    name: "Go for a run",
    description: "A quick 20-minute jog around your neighborhood to get your heart rate up.",
    duration: "20 min",
    energyLevel: "High",
    category: "Exercise",
    color: "#3066BE",
    probability: 12,
    tips: [
      "Wear appropriate running shoes",
      "Start with a warm-up",
      "Stay hydrated",
      "Cool down after your run"
    ]
  },
  {
    id: "3",
    name: "Cook a new recipe",
    description: "Try cooking something new from that cookbook you got last year.",
    duration: "45 min",
    energyLevel: "Medium",
    category: "Creativity",
    color: "#A06CD5",
    probability: 14,
    tips: [
      "Read through the recipe completely before starting",
      "Prep all ingredients before cooking",
      "Don't be afraid to adjust seasonings to your taste",
      "Clean as you go to avoid a mess at the end"
    ]
  },
  {
    id: "4",
    name: "Meditation",
    description: "A 10-minute guided meditation to clear your mind and reduce stress.",
    duration: "10 min",
    energyLevel: "Low",
    category: "Mindfulness",
    color: "#5D8FDD",
    probability: 13,
    tips: [
      "Find a quiet space where you won't be disturbed",
      "Sit in a comfortable position",
      "Focus on your breathing",
      "Return to your breath when your mind wanders"
    ]
  },
  {
    id: "5",
    name: "Learn something new",
    description: "Spend 25 minutes learning about a topic you're curious about.",
    duration: "25 min",
    energyLevel: "Medium",
    category: "Education",
    color: "#204080",
    probability: 12,
    tips: [
      "Choose a specific topic to focus on",
      "Take notes to improve retention",
      "Try to teach what you've learned to someone else",
      "Connect new information to things you already know"
    ]
  },
  {
    id: "6",
    name: "Draw or sketch",
    description: "Express yourself through drawing or sketching for 15 minutes.",
    duration: "15 min",
    energyLevel: "Medium",
    category: "Creativity",
    color: "#6B8E23",
    probability: 11,
    tips: [
      "Don't worry about perfection - just enjoy the process",
      "Try different drawing materials",
      "Consider drawing from observation rather than imagination",
      "Set a timer to stay focused for the full 15 minutes"
    ]
  },
  {
    id: "7",
    name: "Call a friend",
    description: "Reach out to a friend you haven't spoken to in a while.",
    duration: "15 min",
    energyLevel: "Medium",
    category: "Social",
    color: "#FF6B6B",
    probability: 12,
    tips: [
      "Think of a few conversation topics beforehand",
      "Listen actively to what they have to say",
      "Share something meaningful about your recent experiences",
      "Follow up on things they mentioned in previous conversations"
    ]
  },
  {
    id: "8",
    name: "Power nap",
    description: "Take a quick 15-minute power nap to recharge your energy.",
    duration: "15 min",
    energyLevel: "Low",
    category: "Rest",
    color: "#9370DB",
    probability: 11,
    tips: [
      "Set an alarm to avoid oversleeping",
      "Find a comfortable and quiet spot",
      "Consider using an eye mask or earplugs",
      "Try to relax your body gradually from head to toe"
    ]
  },
];

export const useActivity = (activityId?: string) => {
  const [activityState, setActivityState] = useState<'not_started' | 'in_progress' | 'completed'>('not_started');

  // Create a safe version that doesn't depend on WebSocket context
  const sendActivityStatus = (activityId: string, status: 'pending' | 'in-progress' | 'completed') => {
    console.log(`Sending activity status: ${activityId} - ${status}`);
    // In a real implementation, this would communicate with the backend
  };

  const updateActivity = useCallback((status: 'pending' | 'in-progress' | 'completed') => {
    if (!activityId) return;
    sendActivityStatus(activityId, status);
    
    // Fix: use 'in_progress' instead of 'in-progress' to match state type
    if (status === 'pending') {
      setActivityState('not_started');
    } else if (status === 'in-progress') {
      setActivityState('in_progress');
    } else {
      setActivityState('completed');
    }
  }, [activityId]);

  const getActivity = useCallback((id?: string) => {
    const targetId = id || activityId;
    return targetId ? activities.find(activity => activity.id === targetId) || null : null;
  }, [activityId]);

  const getAllActivities = useCallback(() => {
    return activities;
  }, []);

  return {
    activity: activityId ? getActivity() : null,
    activities: getAllActivities(),
    updateActivity,
    activityState,
    getActivity,
    getAllActivities,
    // Add this for compatibility with existing components
    updateActivityStatus: updateActivity,
    // Add a separate method that matches the expected signature in other components
    sendActivityStatus
  };
};
