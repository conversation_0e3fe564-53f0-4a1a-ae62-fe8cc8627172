
import { useCallback } from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';

export const useChatMessages = () => {
  try {
    const { chatMessages, sendChatMessage } = useWebSocket();
    
    const sendMessage = useCallback((content: string) => {
      if (content.trim()) {
        sendChatMessage(content);
      }
    }, [sendChatMessage]);
    
    return { 
      messages: chatMessages, 
      sendMessage 
    };
  } catch (error) {
    console.error("WebSocketContext not available in this component tree:", error);
    
    // Return default values when WebSocketContext is not available
    return {
      messages: [],
      sendMessage: () => console.warn("sendMessage called without WebSocketContext")
    };
  }
};
