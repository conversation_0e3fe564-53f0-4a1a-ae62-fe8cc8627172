export type MessageType = 
  | 'chat_message'
  | 'system_message'
  | 'processing_status'
  | 'wheel_data'
  | 'error'
  | 'workflow_status';

export interface BaseMessage {
  type: MessageType;
  content: any;
}

export interface ChatMessage extends BaseMessage {
  type: 'chat_message';
  content: string;
  is_user: boolean;
  timestamp: Date;
  id: string;
  sender: 'user' | 'ai';
}

export interface SystemMessage extends BaseMessage {
  type: 'system_message';
  content: string;
  level: 'info' | 'warning' | 'error';
}

export interface ProcessingStatus extends BaseMessage {
  type: 'processing_status';
  content: {
    status: 'started' | 'processing' | 'completed' | 'error';
    message?: string;
  };
}

export interface WheelData extends BaseMessage {
  type: 'wheel_data';
  content: any;
  wheel: {
    name: string;
    items: WheelItem[];
  };
}

export interface WheelItem {
  id: string;
  name: string;
  description: string;
  percentage: number;
  color: string;
  domain: string;
  base_challenge_rating: number;
  activity_tailored_id: string;
}

export interface WorkflowStatus extends BaseMessage {
  type: 'workflow_status';
  content: {
    stage: 'initial' | 'wheel_generated' | 'activity_selected' | 'activity_in_progress' | 'activity_completed';
    data?: any;
  };
}

export interface ErrorMessage extends BaseMessage {
  type: 'error';
  content: {
    code: string;
    message: string;
    details?: any;
  };
}
