# GitHub Workflows Restructuring Plan

## Current Issues

1. **Duplication**: Significant overlap between `test-backend.yml` and `test-and-coverage.yml`.
2. **Path Inconsistencies**: Container paths vs repository paths need verification.
3. **Test Organization**: Inconsistent approaches to running tests.
4. **Branch Inconsistencies**: Different workflows trigger on different branch sets.

## Proposed Workflow Structure

### 1. CI Workflow (`ci.yml`)
- **Purpose**: Main CI pipeline that runs on all PRs and pushes to main branches
- **Triggers**: Push/PR to main, master, develop
- **Jobs**:
  - `lint`: Code quality checks
  - `test-backend`: Backend tests with coverage
  - `test-frontend`: Frontend tests
  - `build`: Docker image building and validation

### 2. Deployment Workflow (`deploy.yml`)
- **Purpose**: Deploy to production/staging
- **Triggers**: Push to main/master, manual trigger
- **Jobs**:
  - `build-and-push`: Build and push Docker images
  - `deploy`: Deploy to hosting service

### 3. Benchmark Workflow (`benchmarks.yml`)
- **Purpose**: Run agent benchmarks
- **Triggers**: Manual trigger only, scheduled runs
- **Jobs**:
  - `run-benchmarks`: Run benchmark scenarios

## Path Corrections

1. **Container vs Repository Paths**:
   - Container working directory: `/usr/src/app`
   - Repository structure: 
     - Backend code in `backend/`
     - Frontend code in `frontend/`
   - In Docker Compose: `.:/usr/src/app` maps the backend directory to container

2. **Path Corrections Needed**:
   - Ensure all Docker Compose commands use correct working directory
   - Verify paths in benchmark workflow for report generation
   - Standardize environment variable setup

## Implementation Steps

1. Create new workflow files with the proposed structure
2. Verify all paths and commands
3. Test the new workflows
4. Replace the old workflows with the new ones
