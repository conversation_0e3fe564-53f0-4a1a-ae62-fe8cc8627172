name: Docker Build

on:
  push:
    branches: [ main ]
    paths:
      - 'backend/**'
      - 'frontend/**'
      - 'backend/docker-compose.yml'
      - 'backend/Dockerfile'
      - '.github/workflows/docker-build.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'backend/**'
      - 'frontend/**'
      - 'backend/docker-compose.yml'
      - 'backend/Dockerfile'
      - '.github/workflows/docker-build.yml'

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write  # This is needed to push to GHCR
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Install Docker Compose
      run: |
        # Docker Compose is now included as part of Docker CLI
        docker compose version
    
    - name: Cache Docker layers
      uses: actions/cache@v3
      with:
        path: /tmp/.buildx-cache
        key: ${{ runner.os }}-buildx-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-buildx-
    
    - name: Create environment files
      run: |
        # Create basic env files that <PERSON><PERSON> Compose expects
        echo "# Base environment variables" > backend/.env
        echo "# Development environment variables" > backend/.env.dev
        echo "# Test environment variables" > backend/.env.test
    
    - name: Build Backend Docker image
      uses: docker/build-push-action@v4
      with:
        context: ./backend
        file: ./backend/Dockerfile
        push: false
        tags: ghcr.io/elgui/goali-backend:latest
        cache-from: type=local,src=/tmp/.buildx-cache
        cache-to: type=local,dest=/tmp/.buildx-cache-new
    
    # This ugly bit is necessary if you don't want your cache to grow forever
    # till it hits GitHub's limit of 5GB.
    # Temp fix: https://github.com/docker/build-push-action/issues/252
    # https://github.com/moby/buildkit/issues/1896
    - name: Move cache
      run: |
        rm -rf /tmp/.buildx-cache
        mv /tmp/.buildx-cache-new /tmp/.buildx-cache
    
    - name: Test Docker Compose
      run: |
        # Test if docker-compose builds successfully
        cd backend && docker compose -f docker-compose.yml config  # Changed from docker-compose