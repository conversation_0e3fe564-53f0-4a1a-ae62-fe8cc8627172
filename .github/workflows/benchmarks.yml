name: Run Benchmarks

on:
  workflow_dispatch: # Allow manual trigger
  schedule:
    - cron: '0 0 * * 0' # Example: Run weekly at midnight on Sunday

jobs:
  run-benchmarks:
    name: Run Agent Benchmarks
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend # Set default directory for subsequent steps

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Compose
        # Run outside working-directory default
        working-directory: . # Reset working directory for this step
        run: docker compose --version # Simple check to ensure docker-compose is available

      - name: Build Docker images
        run: docker compose build web # Build only the 'web' service needed for the command

      - name: Run Benchmark Scenarios
        run: |
          # Construct the command arguments based on inputs (if triggered manually)
          CMD_ARGS=""
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            if [ -n "${{ github.event.inputs.tags }}" ]; then
              CMD_ARGS="$CMD_ARGS --tags ${{ github.event.inputs.tags }}"
            fi
            if [ -n "${{ github.event.inputs.scenario_ids }}" ]; then
              CMD_ARGS="$CMD_ARGS --scenarios ${{ github.event.inputs.scenario_ids }}"
            fi
            if [ -n "${{ github.event.inputs.output_file }}" ]; then
              # Ensure the directory exists within the container if specified
              OUTPUT_DIR=$(dirname "${{ github.event.inputs.output_file }}")
              docker compose exec -T web mkdir -p "$OUTPUT_DIR"
              CMD_ARGS="$CMD_ARGS --output ${{ github.event.inputs.output_file }}"
            fi
            if [ -n "${{ github.event.inputs.report_path }}" ]; then
              # Ensure the directory exists within the container if specified
              REPORT_DIR=$(dirname "${{ github.event.inputs.report_path }}")
              docker compose exec -T web mkdir -p "$REPORT_DIR"
              CMD_ARGS="$CMD_ARGS --generate-report ${{ github.event.inputs.report_path }}"
            fi
          else
            # Default behavior for scheduled runs (e.g., generate standard report)
            CMD_ARGS="--generate-report /usr/src/app/benchmark_results/scheduled_report.html"
            docker compose exec -T web mkdir -p /usr/src/app/benchmark_results/
          fi

          echo "Running benchmarks with args: $CMD_ARGS"
          # Add environment variables for database connection
          # Assuming docker-compose.yml sets up the test-db service alias
          docker compose exec -T -e DATABASE_URL=***********************************************/test_goali web python manage.py run_benchmarks $CMD_ARGS
        env:
          COMPOSE_PROJECT_NAME: goali_benchmarks_${{ github.run_id }} # Avoid conflicts

      - name: Determine Report Path
        id: report_path
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ] && [ -n "${{ github.event.inputs.report_path }}" ]; then
            echo "container_path=${{ github.event.inputs.report_path }}" >> $GITHUB_OUTPUT
            echo "host_path=./benchmark_report.html" >> $GITHUB_OUTPUT # Relative to working-directory (./backend)
          elif [ "${{ github.event_name }}" == "schedule" ]; then
            echo "container_path=/usr/src/app/benchmark_results/scheduled_report.html" >> $GITHUB_OUTPUT
            echo "host_path=./scheduled_benchmark_report.html" >> $GITHUB_OUTPUT # Relative to working-directory (./backend)
          else
            echo "container_path=" >> $GITHUB_OUTPUT # No report generated/expected
            echo "host_path=" >> $GITHUB_OUTPUT
          fi

      - name: Copy Report from Container
        if: steps.report_path.outputs.container_path != ''
        run: |
          CONTAINER_ID=$(docker compose ps -q web)
          if [ -z "$CONTAINER_ID" ]; then
            echo "Error: Could not find the 'web' container ID."
            exit 1
          fi
          HOST_REPORT_PATH="${{ steps.report_path.outputs.host_path }}"
          CONTAINER_REPORT_PATH="${{ steps.report_path.outputs.container_path }}"
          echo "Copying report from container $CONTAINER_ID:$CONTAINER_REPORT_PATH to host $HOST_REPORT_PATH"
          # Use docker cp to copy the file from the container to the host workspace
          docker cp "$CONTAINER_ID":"$CONTAINER_REPORT_PATH" "$HOST_REPORT_PATH"
          echo "Report copied."
        env:
          COMPOSE_PROJECT_NAME: goali_benchmarks_${{ github.run_id }}

      - name: Upload Benchmark Report
        if: steps.report_path.outputs.host_path != ''
        uses: actions/upload-artifact@v4
        with:
          name: benchmark-report-${{ github.run_id }}
          path: backend/${{ steps.report_path.outputs.host_path }} # Path relative to GITHUB_WORKSPACE
          if-no-files-found: error # Error if the report wasn't copied successfully
