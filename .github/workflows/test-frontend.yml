name: Frontend Tests

on:
  push:
    branches: [ main ]
    paths:
      - 'frontend/**'
      - '.github/workflows/test-frontend.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'frontend/**'
      - '.github/workflows/test-frontend.yml'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: './frontend/package-lock.json'
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run unit tests
      working-directory: ./frontend
      run: npm test
    
    # Uncomment when you have Cypress tests set up
    # - name: Run Cypress tests
    #   working-directory: ./frontend
    #   run: npm run test:e2e