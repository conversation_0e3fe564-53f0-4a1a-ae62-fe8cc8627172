# Goali Mock Server - Message Specifications

This document provides the complete specification of all WebSocket messages supported by the Goali Mock Server, including expected inputs and delivered outputs with detailed examples.

## Table of Contents

1. [Message Format Overview](#message-format-overview)
2. [Client to Server Messages](#client-to-server-messages)
3. [Server to Client Messages](#server-to-client-messages)
4. [Workflow Examples](#workflow-examples)
5. [Error Handling](#error-handling)

## Message Format Overview

All WebSocket messages follow this base structure:

```json
{
  "type": "message_type",
  "content": { /* message-specific content */ },
  // Optional additional fields
}
```

### Important Note: Backend-Determined Metadata

**Trust levels, mood, environment, and other user context variables are determined by the backend agents, not provided by the frontend.** The frontend should only send:
- User messages
- User profile ID
- Optional workflow routing hints
- Activity selection results

All psychological assessment, trust evaluation, and environmental analysis is performed by specialized backend agents (Psychological Monitoring Agent, Resource & Capacity Agent, etc.) based on user history and interaction patterns.

### Message Types

#### Client to Server
- `chat_message` - User chat input for workflows
- `spin_result` - Activity selection from wheel spin
- `workflow_status_request` - Request current workflow status

#### Server to Client
- `system_message` - System notifications
- `processing_status` - Processing state updates
- `wheel_data` - Generated wheel information
- `error` - Error notifications
- `workflow_status` - Workflow status responses
- `activity_details` - Detailed activity information
- `debug_info` - Debug information (development mode)

## Client to Server Messages

### 1. Chat Message (`chat_message`)

**Purpose**: Send user input to trigger discussion or wheel generation workflows.

**Expected Input**:
```json
{
  "type": "chat_message",
  "content": {
    "message": "string",           // Required: User's message text
    "user_profile_id": "string",   // Required: User identifier
    "timestamp": "string",         // Optional: ISO-8601 timestamp
    "metadata": {                  // Optional: Additional context
      "requested_workflow": "wheel_generation" | "pre_spin_feedback" | "activity_feedback" | "discussion" | "onboarding" | "post_spin" | "post_activity"
      // Note: All other metadata fields (trust_level, mood, environment, etc.)
      // are determined by backend agents, not provided by frontend
    }
  }
}
```

**Required Fields:**
- `message`: The user's message text
- `user_profile_id`: Unique identifier for the user

**Optional Fields:**
- `timestamp`: ISO-8601 formatted timestamp
- `metadata`: Additional context information (object)
  - `requested_workflow`: (Optional) Explicitly requests a specific backend workflow. Available workflows:
    - `"wheel_generation"`: Request a new activity wheel
    - `"pre_spin_feedback"`: Provide feedback on the currently displayed wheel before spinning
    - `"activity_feedback"`: Provide feedback on a previously completed activity
    - `"discussion"`: Intend to have a general chat
    - `"onboarding"`: User onboarding workflow
    - `"post_spin"`: Post-activity selection workflow
    - `"post_activity"`: Post-activity completion workflow

**Example - Discussion Request**:
```json
{
  "type": "chat_message",
  "content": {
    "message": "I'm feeling stressed about work and need some guidance",
    "user_profile_id": "user-123",
    "timestamp": "2025-01-27T14:30:00Z",
    "metadata": {
      "requested_workflow": "discussion"
    }
  }
}
```

**Example - Wheel Generation Request**:
```json
{
  "type": "chat_message",
  "content": {
    "message": "I'd like to create a wheel with creative and physical activities",
    "user_profile_id": "user-456",
    "timestamp": "2025-01-27T14:35:00Z",
    "metadata": {
      "requested_workflow": "wheel_generation"
    }
  }
}
```

**Example - Simple Message (No Workflow Specified)**:
```json
{
  "type": "chat_message",
  "content": {
    "message": "Hello, how are you today?",
    "user_profile_id": "user-789"
  }
}
```

### 2. Spin Result (`spin_result`)

**Purpose**: Report which activity was selected from a wheel spin.

**Expected Input**:
```json
{
  "type": "spin_result",
  "content": {
    "activity_tailored_id": "string",  // Required: Selected ActivityTailored object ID
    "name": "string",                  // Required: Activity name
    "description": "string",           // Optional: Activity description
    "user_profile_id": "string"        // Required: User identifier
  }
}
```

**Required Fields:**
- `activity_tailored_id`: Unique identifier for the selected ActivityTailored object
- `name`: Display name of the activity
- `user_profile_id`: Unique identifier for the user

**Optional Fields:**
- `description`: Activity description (if available)

**Example**:
```json
{
  "type": "spin_result",
  "content": {
    "activity_tailored_id": "act-a1b2c3d4",
    "name": "Creative Writing",
    "description": "Express yourself through writing",
    "user_profile_id": "user-123"
  }
}
```

### 3. Workflow Status Request (`workflow_status_request`)

**Purpose**: Request the current status of a workflow.

**Expected Input**:
```json
{
  "type": "workflow_status_request",
  "content": {
    "workflow_id": "string"  // Required: Workflow identifier
  }
}
```

**Example**:
```json
{
  "type": "workflow_status_request",
  "content": {
    "workflow_id": "wf-12345678"
  }
}
```

## Server to Client Messages

### 1. System Message (`system_message`)

**Purpose**: System notifications and welcome messages.

**Delivered Output**:
```json
{
  "type": "system_message",
  "content": "string"  // System message text
}
```

**Example**:
```json
{
  "type": "system_message",
  "content": "Connected to Goali Mock Server"
}
```

### 2. Chat Message (`chat_message`)

**Purpose**: AI responses and user message echoes.

**Delivered Output**:
```json
{
  "type": "chat_message",
  "content": "string",     // Message content
  "is_user": boolean      // true for user messages, false for AI responses
}
```

**Example - User Echo**:
```json
{
  "type": "chat_message",
  "content": "I'm feeling stressed about work and need some guidance",
  "is_user": true
}
```

**Example - AI Response**:
```json
{
  "type": "chat_message",
  "content": "I can sense that you're going through something important. Would you like to share more about how you're feeling?",
  "is_user": false
}
```

### 3. Processing Status (`processing_status`)

**Purpose**: Indicate current processing state.

**Delivered Output**:
```json
{
  "type": "processing_status",
  "status": "processing" | "completed" | "error"
}
```

**Examples**:
```json
{
  "type": "processing_status",
  "status": "processing"
}
```

```json
{
  "type": "processing_status",
  "status": "completed"
}
```

### 4. Wheel Data (`wheel_data`)

**Purpose**: Deliver generated wheel with activities.

**Delivered Output**:
```json
{
  "type": "wheel_data",
  "wheel": {
    "name": "string",              // Wheel name (e.g., "Daily Challenge Wheel")
    "items": [
      {
        "id": "string",                    // Unique identifier for the wheel item
        "name": "string",                  // Display name for the activity
        "description": "string",           // Activity description text
        "percentage": number,              // Probability weight for selection (0-100)
        "color": "string",                 // HEX color code for visual representation
        "domain": "string",                // Primary domain code (e.g., "creative")
        "base_challenge_rating": number,   // Difficulty level of the activity (0-100)
        "activity_tailored_id": "string"   // Reference to the underlying ActivityTailored object
      }
    ]
  }
}
```

**Example**:
```json
{
  "type": "wheel_data",
  "wheel": {
    "name": "Creative Growth Wheel",
    "items": [
      {
        "id": "item-1",
        "name": "Creative Writing",
        "description": "Express yourself through writing",
        "percentage": 33.3,
        "color": "#66BB6A",
        "domain": "creative",
        "base_challenge_rating": 60,
        "activity_tailored_id": "act-a1b2c3d4"
      },
      {
        "id": "item-2",
        "name": "Mindful Meditation",
        "description": "Focus on your breath and be present",
        "percentage": 33.3,
        "color": "#42A5F5",
        "domain": "reflective",
        "base_challenge_rating": 30,
        "activity_tailored_id": "act-e5f6g7h8"
      },
      {
        "id": "item-3",
        "name": "Nature Walk",
        "description": "Connect with the outdoors",
        "percentage": 33.4,
        "color": "#FFA726",
        "domain": "physical",
        "base_challenge_rating": 35,
        "activity_tailored_id": "act-i9j0k1l2"
      }
    ]
  }
}
```

### 5. Activity Details (`activity_details`)

**Purpose**: Provide detailed information about a selected activity.

**Delivered Output**:
```json
{
  "type": "activity_details",
  "details": {
    "id": "string",                    // Activity ID
    "name": "string",                  // Activity name
    "detailed_description": "string", // Extended description
    "preparation_steps": ["string"],   // Preparation instructions
    "tips_for_success": ["string"],    // Success tips
    "reflection_questions": ["string"] // Post-activity reflection
  }
}
```

**Example**:
```json
{
  "type": "activity_details",
  "details": {
    "id": "act-a1b2c3d4",
    "name": "Creative Writing",
    "detailed_description": "You've selected Creative Writing! This is a wonderful choice that will help you grow and explore new aspects of yourself.",
    "preparation_steps": [
      "Find a comfortable and quiet space",
      "Gather any materials you might need",
      "Set aside dedicated time without distractions",
      "Approach this with an open and curious mindset"
    ],
    "tips_for_success": [
      "Start with a positive intention",
      "Be patient with yourself",
      "Focus on the process rather than the outcome",
      "Notice how you feel before, during, and after"
    ],
    "reflection_questions": [
      "What did you discover about yourself?",
      "How did this activity make you feel?",
      "What would you do differently next time?",
      "How might this connect to other areas of your life?"
    ]
  }
}
```

### 6. Error Message (`error`)

**Purpose**: Report errors and issues.

**Delivered Output**:
```json
{
  "type": "error",
  "content": "string" | {
    "content": "string",           // Error message
    "code": "string",             // Optional error code
    "details": {}                 // Optional error details
  }
}
```

**Example - Simple Error**:
```json
{
  "type": "error",
  "content": "Invalid message format"
}
```

**Example - Detailed Error**:
```json
{
  "type": "error",
  "content": {
    "content": "Missing required field",
    "code": "VALIDATION_ERROR",
    "details": {
      "field": "user_profile_id",
      "message": "user_profile_id is required"
    }
  }
}
```

### 7. Workflow Status (`workflow_status`)

**Purpose**: Report current workflow status.

**Delivered Output**:
```json
{
  "type": "workflow_status",
  "workflow_id": "string",
  "status": "initial" | "wheel_generated" | "activity_selected" | "activity_in_progress" | "activity_completed",
  "stage": "string",             // Optional current stage
  "data": {}                     // Optional additional data
}
```

**Example**:
```json
{
  "type": "workflow_status",
  "workflow_id": "wf-12345678",
  "status": "wheel_generated",
  "stage": "complete",
  "data": {
    "activities_count": 3,
    "creation_time": "2025-01-27T14:35:15Z"
  }
}
```

## Workflow Examples

### Complete Discussion Workflow

This example shows a complete discussion workflow from start to finish.

**1. Client sends chat message**:
```json
{
  "type": "chat_message",
  "content": {
    "message": "I'm feeling overwhelmed with work and personal life. I need some guidance.",
    "user_profile_id": "user-789",
    "timestamp": "2025-01-27T15:00:00Z",
    "metadata": {
      "requested_workflow": "discussion"
    }
  }
}
```

**2. Server responses (in order)**:

a) **System welcome** (on connection):
```json
{
  "type": "system_message",
  "content": "Connected to Goali Mock Server"
}
```

b) **User message echo**:
```json
{
  "type": "chat_message",
  "content": "I'm feeling overwhelmed with work and personal life. I need some guidance.",
  "is_user": true
}
```

c) **Processing status**:
```json
{
  "type": "processing_status",
  "status": "processing"
}
```

d) **AI response**:
```json
{
  "type": "chat_message",
  "content": "I can sense that you're going through something important. Would you like to share more about how you're feeling? Sometimes talking through our emotions can help us understand them better.",
  "is_user": false
}
```

e) **Completion status**:
```json
{
  "type": "processing_status",
  "status": "completed"
}
```

### Complete Wheel Generation Workflow

This example shows a complete wheel generation workflow.

**1. Client sends wheel request**:
```json
{
  "type": "chat_message",
  "content": {
    "message": "I want to create a wheel with creative and mindfulness activities for stress relief",
    "user_profile_id": "user-456",
    "timestamp": "2025-01-27T15:10:00Z",
    "metadata": {
      "requested_workflow": "wheel_generation"
    }
  }
}
```

**2. Server responses (in order)**:

a) **User message echo**:
```json
{
  "type": "chat_message",
  "content": "I want to create a wheel with creative and mindfulness activities for stress relief",
  "is_user": true
}
```

b) **Processing status**:
```json
{
  "type": "processing_status",
  "status": "processing"
}
```

c) **AI introduction**:
```json
{
  "type": "chat_message",
  "content": "Here's your personalized Creative Growth Wheel! I've selected activities that will challenge you in just the right way while supporting your growth. These are tailored to where you are right now.",
  "is_user": false
}
```

d) **Wheel data** (see full example in Wheel Data section above)

e) **Completion status**:
```json
{
  "type": "processing_status",
  "status": "completed"
}
```

### Complete Activity Selection Workflow

This example shows what happens when a user spins the wheel and selects an activity.

**1. Client sends spin result**:
```json
{
  "type": "spin_result",
  "content": {
    "activity_tailored_id": "act-a1b2c3d4",
    "name": "Creative Writing",
    "description": "Express yourself through writing",
    "user_profile_id": "user-456"
  }
}
```

**2. Server responses (in order)**:

a) **Processing status**:
```json
{
  "type": "processing_status",
  "status": "processing"
}
```

b) **Encouragement message**:
```json
{
  "type": "chat_message",
  "content": "Wonderful choice! Creative Writing is a perfect way to explore and grow. I'm excited for you to experience this!",
  "is_user": false
}
```

c) **Activity details** (see full example in Activity Details section above)

d) **Completion status**:
```json
{
  "type": "processing_status",
  "status": "completed"
}
```

## Error Handling

### Common Error Scenarios

**1. Invalid JSON**:
```json
{
  "type": "error",
  "content": "Invalid JSON: Expecting ',' delimiter: line 3 column 5 (char 45)"
}
```

**2. Missing required fields**:
```json
{
  "type": "error",
  "content": "Error processing chat message: 'user_profile_id' is required"
}
```

**3. Unknown message type**:
```json
{
  "type": "error",
  "content": "Unknown message type: invalid_type"
}
```

**4. Server error**:
```json
{
  "type": "error",
  "content": "Server error: Workflow execution failed"
}
```

### Connection Handling

- **Connection established**: Server sends system_message with welcome
- **Connection lost**: Client should implement reconnection logic
- **Heartbeat**: Server sends ping every 20 seconds, expects pong within 10 seconds
- **Graceful shutdown**: Server closes connections with code 1000 (normal closure)

## Trust Level Adaptation (Backend Behavior)

**Note**: Trust levels are determined by the backend Psychological Monitoring Agent based on user interaction history and patterns. The mock server simulates this backend behavior for testing purposes.

### Foundation Phase (0-39)
- **Characteristics**: More supportive, encouraging, gentle challenges
- **Response style**: "I'm here to help you. [response]"
- **Activity challenge**: Reduced by 15 points
- **Wheel name**: "[Domain] Foundation Wheel"

### Expansion Phase (40-69)
- **Characteristics**: Balanced approach, moderate challenges
- **Response style**: Standard responses
- **Activity challenge**: No adjustment
- **Wheel name**: "[Domain] Growth Wheel"

### Integration Phase (70-100)
- **Characteristics**: More collaborative, philosophical, higher challenges
- **Response style**: "[response] What are your thoughts on this approach?"
- **Activity challenge**: Increased by 10 points
- **Wheel name**: "[Domain] Mastery Wheel"

## Activity Domains

The server includes four activity domains with realistic templates:

### Creative Domain
- Creative Writing, Sketch Something Beautiful, Music Exploration
- **Focus**: Self-expression, imagination, artistic exploration
- **Resources**: Art supplies, writing materials, music players

### Reflective Domain
- Mindful Meditation, Gratitude Journal, Life Reflection
- **Focus**: Self-awareness, mental well-being, introspection
- **Resources**: Quiet space, journal, minimal external needs

### Physical Domain
- Nature Walk, Gentle Stretching, Dance Break
- **Focus**: Physical health, energy, body awareness
- **Resources**: Comfortable clothing, outdoor access, movement space

### Social Domain
- Connect with a Friend, Random Act of Kindness, Community Engagement
- **Focus**: Relationships, community connection, social skills
- **Resources**: Communication tools, transportation, social opportunities

---

*This specification covers all message types and workflows supported by the Goali Mock Server v1.0*