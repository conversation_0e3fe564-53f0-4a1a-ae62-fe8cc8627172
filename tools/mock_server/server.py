"""
Mock server for frontend development.

This server simulates the backend behavior for discussion and wheel_generation
workflows using the contracts defined in the benchmarking system as the source
of truth.
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Set
import websockets

try:
    from contracts import (
        WebSocketMessage, MessageType, ChatMessage, SpinResult, ProcessingStatus,
        WheelDataMessage, ErrorMessage, WorkflowStatus, ChatMessageContent,
        SpinResultContent, ProcessingStatusType, WorkflowStatusType
    )
    from workflows import DiscussionWorkflow, WheelGenerationWorkflow
except ImportError:
    # Handle relative imports when running as script
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))

    from contracts import (
        WebSocketMessage, MessageType, ChatMessage, SpinResult, ProcessingStatus,
        WheelDataMessage, ErrorMessage, WorkflowStatus, ChatMessageContent,
        SpinResultContent, ProcessingStatusType, WorkflowStatusType
    )
    from workflows import DiscussionWorkflow, WheelGenerationWorkflow

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockServer:
    """Mock WebSocket server for frontend development."""

    def __init__(self, host: str = "localhost", port: int = 8765):
        self.host = host
        self.port = port
        self.clients: Set = set()
        self.workflows = {
            "discussion": DiscussionWorkflow(),
            "wheel_generation": WheelGenerationWorkflow()
        }

    async def register_client(self, websocket):
        """Register a new client connection."""
        self.clients.add(websocket)
        logger.info(f"Client connected. Total clients: {len(self.clients)}")

        # Send welcome message
        welcome_message = {
            "type": MessageType.SYSTEM_MESSAGE,
            "content": "Connected to Goali Mock Server"
        }
        await websocket.send(json.dumps(welcome_message))

    async def unregister_client(self, websocket):
        """Unregister a client connection."""
        self.clients.discard(websocket)
        logger.info(f"Client disconnected. Total clients: {len(self.clients)}")

    async def handle_message(self, websocket, message: str):
        """Handle incoming WebSocket message."""
        try:
            # Parse the message
            data = json.loads(message)
            message_obj = WebSocketMessage(**data)

            logger.info(f"Received message: {message_obj.type}")

            # Route message based on type
            if message_obj.type == MessageType.CHAT_MESSAGE:
                await self.handle_chat_message(websocket, message_obj)
            elif message_obj.type == MessageType.SPIN_RESULT:
                await self.handle_spin_result(websocket, message_obj)
            elif message_obj.type == MessageType.WORKFLOW_STATUS_REQUEST:
                await self.handle_workflow_status_request(websocket, message_obj)
            else:
                await self.send_error(websocket, f"Unknown message type: {message_obj.type}")

        except json.JSONDecodeError as e:
            await self.send_error(websocket, f"Invalid JSON: {str(e)}")
        except Exception as e:
            logger.error(f"Error handling message: {str(e)}", exc_info=True)
            await self.send_error(websocket, f"Server error: {str(e)}")

    async def handle_chat_message(self, websocket, message: WebSocketMessage):
        """Handle chat message from client."""
        try:
            # Parse chat message content
            content = ChatMessageContent(**message.content)

            # Echo the user message back
            echo_message = {
                "type": MessageType.CHAT_MESSAGE,
                "content": content.message,
                "is_user": True
            }
            await websocket.send(json.dumps(echo_message))

            # Send processing status
            await self.send_processing_status(websocket, ProcessingStatusType.PROCESSING)

            # Determine workflow type from metadata or default to discussion
            workflow_type = "discussion"
            if content.metadata and "requested_workflow" in content.metadata:
                workflow_type = content.metadata["requested_workflow"]
            elif "wheel" in content.message.lower() or "activity" in content.message.lower():
                workflow_type = "wheel_generation"

            # Execute workflow
            workflow = self.workflows.get(workflow_type)
            if workflow:
                result = await workflow.execute(content)
                await self.send_workflow_result(websocket, result)
            else:
                await self.send_error(websocket, f"Unknown workflow: {workflow_type}")

            # Send completion status
            await self.send_processing_status(websocket, ProcessingStatusType.COMPLETED)

        except Exception as e:
            logger.error(f"Error handling chat message: {str(e)}", exc_info=True)
            await self.send_error(websocket, f"Error processing chat message: {str(e)}")

    async def handle_spin_result(self, websocket, message: WebSocketMessage):
        """Handle spin result from client."""
        try:
            # Parse spin result content
            content = SpinResultContent(**message.content)

            # Send processing status
            await self.send_processing_status(websocket, ProcessingStatusType.PROCESSING)

            # Execute post-spin workflow
            workflow = self.workflows.get("wheel_generation")
            if workflow:
                result = await workflow.handle_spin_result(content)
                await self.send_workflow_result(websocket, result)
            else:
                await self.send_error(websocket, "Wheel generation workflow not available")

            # Send completion status
            await self.send_processing_status(websocket, ProcessingStatusType.COMPLETED)

        except Exception as e:
            logger.error(f"Error handling spin result: {str(e)}", exc_info=True)
            await self.send_error(websocket, f"Error processing spin result: {str(e)}")

    async def handle_workflow_status_request(self, websocket, message: WebSocketMessage):
        """Handle workflow status request from client."""
        try:
            workflow_id = message.content.get("workflow_id")
            if not workflow_id:
                await self.send_error(websocket, "Missing workflow_id")
                return

            # Mock workflow status response
            status_message = {
                "type": MessageType.WORKFLOW_STATUS,
                "workflow_id": workflow_id,
                "status": WorkflowStatusType.COMPLETED
            }
            await websocket.send(json.dumps(status_message))

        except Exception as e:
            logger.error(f"Error handling workflow status request: {str(e)}", exc_info=True)
            await self.send_error(websocket, f"Error processing status request: {str(e)}")

    async def send_processing_status(self, websocket, status: ProcessingStatusType):
        """Send processing status to client."""
        message = {
            "type": MessageType.PROCESSING_STATUS,
            "status": status
        }
        await websocket.send(json.dumps(message))

    async def send_error(self, websocket, error_message: str):
        """Send error message to client."""
        message = {
            "type": MessageType.ERROR,
            "content": error_message
        }
        await websocket.send(json.dumps(message))

    async def send_workflow_result(self, websocket, result: Dict[str, Any]):
        """Send workflow result to client."""
        # Send chat response if available
        if "user_response" in result:
            chat_message = {
                "type": MessageType.CHAT_MESSAGE,
                "content": result["user_response"],
                "is_user": False
            }
            await websocket.send(json.dumps(chat_message))

        # Send wheel data if available
        if "wheel" in result:
            wheel_message = {
                "type": MessageType.WHEEL_DATA,
                "wheel": result["wheel"]
            }
            await websocket.send(json.dumps(wheel_message))

        # Send activity details if available
        if "activity_details" in result:
            activity_message = {
                "type": MessageType.ACTIVITY_DETAILS,
                "details": result["activity_details"]
            }
            await websocket.send(json.dumps(activity_message))

    async def handle_client(self, websocket):
        """Handle individual client connection."""
        await self.register_client(websocket)
        try:
            async for message in websocket:
                await self.handle_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            logger.info("Client connection closed")
        except Exception as e:
            logger.error(f"Error in client handler: {str(e)}", exc_info=True)
        finally:
            await self.unregister_client(websocket)

    async def start(self):
        """Start the mock server."""
        logger.info(f"Starting mock server on {self.host}:{self.port}")

        # Start WebSocket server
        server = await websockets.serve(
            self.handle_client,
            self.host,
            self.port,
            ping_interval=20,
            ping_timeout=10
        )

        logger.info(f"Mock server running on ws://{self.host}:{self.port}")
        logger.info("Supported endpoints:")
        logger.info("  - ws://localhost:8765/ws/game/ (compatible with frontend)")

        # Keep server running
        await server.wait_closed()


async def main():
    """Main entry point."""
    server = MockServer()
    await server.start()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
