#!/usr/bin/env python3
"""
Test client for the Goali Mock Server.

This script tests the mock server functionality by sending various
message types and verifying responses.
"""

import asyncio
import json
import logging
import websockets
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockServerTestClient:
    """Test client for the mock server."""
    
    def __init__(self, uri: str = "ws://localhost:8765"):
        self.uri = uri
        self.websocket = None
        
    async def connect(self):
        """Connect to the mock server."""
        try:
            self.websocket = await websockets.connect(self.uri)
            logger.info(f"Connected to {self.uri}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
            return False
            
    async def disconnect(self):
        """Disconnect from the mock server."""
        if self.websocket:
            await self.websocket.close()
            logger.info("Disconnected from server")
            
    async def send_message(self, message: Dict[str, Any]):
        """Send a message to the server."""
        if not self.websocket:
            logger.error("Not connected to server")
            return
            
        try:
            await self.websocket.send(json.dumps(message))
            logger.info(f"Sent: {message['type']}")
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            
    async def receive_message(self, timeout: float = 10.0):
        """Receive a message from the server."""
        if not self.websocket:
            logger.error("Not connected to server")
            return None
            
        try:
            message = await asyncio.wait_for(
                self.websocket.recv(), 
                timeout=timeout
            )
            data = json.loads(message)
            logger.info(f"Received: {data.get('type', 'unknown')}")
            return data
        except asyncio.TimeoutError:
            logger.warning("Timeout waiting for message")
            return None
        except Exception as e:
            logger.error(f"Failed to receive message: {e}")
            return None
            
    async def test_discussion_workflow(self):
        """Test the discussion workflow."""
        logger.info("Testing discussion workflow...")
        
        # Send chat message
        chat_message = {
            "type": "chat_message",
            "content": {
                "message": "Hello! I'm feeling a bit stressed today and could use some guidance.",
                "user_profile_id": "test-user-123",
                "timestamp": "2025-01-27T10:00:00Z",
                "metadata": {
                    "trust_level": 60
                }
            }
        }
        
        await self.send_message(chat_message)
        
        # Receive responses
        responses = []
        for _ in range(5):  # Expect multiple responses
            response = await self.receive_message()
            if response:
                responses.append(response)
            else:
                break
                
        # Verify responses
        message_types = [r.get('type') for r in responses]
        logger.info(f"Received message types: {message_types}")
        
        # Should receive: echo, processing, chat response, completion
        expected_types = ['chat_message', 'processing_status', 'chat_message', 'processing_status']
        for expected_type in expected_types:
            if expected_type in message_types:
                logger.info(f"✓ Received expected {expected_type}")
            else:
                logger.warning(f"✗ Missing expected {expected_type}")
                
        return responses
        
    async def test_wheel_generation_workflow(self):
        """Test the wheel generation workflow."""
        logger.info("Testing wheel generation workflow...")
        
        # Send wheel generation request
        wheel_request = {
            "type": "chat_message",
            "content": {
                "message": "I'd like to create a wheel with some creative and physical activities please!",
                "user_profile_id": "test-user-123",
                "timestamp": "2025-01-27T10:05:00Z",
                "metadata": {
                    "trust_level": 75,
                    "requested_workflow": "wheel_generation"
                }
            }
        }
        
        await self.send_message(wheel_request)
        
        # Receive responses
        responses = []
        for _ in range(10):  # Expect multiple responses including wheel data
            response = await self.receive_message()
            if response:
                responses.append(response)
                # Stop if we get completion status
                if response.get('type') == 'processing_status' and response.get('status') == 'completed':
                    break
            else:
                break
                
        # Verify responses
        message_types = [r.get('type') for r in responses]
        logger.info(f"Received message types: {message_types}")
        
        # Should receive wheel data
        wheel_data_received = any(r.get('type') == 'wheel_data' for r in responses)
        if wheel_data_received:
            logger.info("✓ Received wheel data")
            # Find and examine wheel data
            for response in responses:
                if response.get('type') == 'wheel_data':
                    wheel = response.get('wheel', {})
                    activities = wheel.get('activities', [])
                    logger.info(f"✓ Wheel contains {len(activities)} activities")
                    if activities:
                        logger.info(f"✓ First activity: {activities[0].get('name', 'Unknown')}")
        else:
            logger.warning("✗ No wheel data received")
            
        return responses
        
    async def test_spin_result(self):
        """Test spin result handling."""
        logger.info("Testing spin result handling...")
        
        # Send spin result
        spin_result = {
            "type": "spin_result",
            "content": {
                "activity_tailored_id": "act-12345678",
                "name": "Creative Writing",
                "description": "Express yourself through writing",
                "user_profile_id": "test-user-123"
            }
        }
        
        await self.send_message(spin_result)
        
        # Receive responses
        responses = []
        for _ in range(5):
            response = await self.receive_message()
            if response:
                responses.append(response)
            else:
                break
                
        # Verify responses
        message_types = [r.get('type') for r in responses]
        logger.info(f"Received message types: {message_types}")
        
        # Should receive activity details
        activity_details_received = any(r.get('type') == 'activity_details' for r in responses)
        if activity_details_received:
            logger.info("✓ Received activity details")
        else:
            logger.warning("✗ No activity details received")
            
        return responses
        
    async def run_tests(self):
        """Run all tests."""
        logger.info("Starting mock server tests...")
        
        # Connect to server
        if not await self.connect():
            logger.error("Failed to connect to server")
            return False
            
        try:
            # Test discussion workflow
            await self.test_discussion_workflow()
            await asyncio.sleep(1)  # Brief pause between tests
            
            # Test wheel generation workflow
            await self.test_wheel_generation_workflow()
            await asyncio.sleep(1)  # Brief pause between tests
            
            # Test spin result
            await self.test_spin_result()
            
            logger.info("All tests completed!")
            return True
            
        except Exception as e:
            logger.error(f"Test error: {e}")
            return False
        finally:
            await self.disconnect()


async def main():
    """Main test function."""
    client = MockServerTestClient()
    success = await client.run_tests()
    
    if success:
        logger.info("✓ All tests passed!")
    else:
        logger.error("✗ Some tests failed!")
        
    return success


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        exit(1)
    except Exception as e:
        logger.error(f"Test runner error: {e}")
        exit(1)
