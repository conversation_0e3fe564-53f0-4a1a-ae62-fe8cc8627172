#!/usr/bin/env python3
"""
Entry point for the Goali Mock Server.

This script provides a convenient way to start the mock server with
different configuration options.
"""

import argparse
import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from server import MockServer


def setup_logging(level: str = "INFO"):
    """Setup logging configuration."""
    log_level = getattr(logging, level.upper(), logging.INFO)

    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('mock_server.log')
        ]
    )


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Goali Mock Server for Frontend Development"
    )

    parser.add_argument(
        "--host",
        default=os.getenv("MOCK_SERVER_HOST", "localhost"),
        help="Host to bind the server to (default: localhost, env: MOCK_SERVER_HOST)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=int(os.getenv("MOCK_SERVER_PORT", "8765")),
        help="Port to bind the server to (default: 8765, env: MOCK_SERVER_PORT)"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=os.getenv("MOCK_SERVER_LOG_LEVEL", "INFO"),
        help="Logging level (default: INFO, env: MOCK_SERVER_LOG_LEVEL)"
    )

    parser.add_argument(
        "--fast",
        action="store_true",
        help="Enable fast mode with reduced processing delays"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging (equivalent to --log-level DEBUG)"
    )

    return parser.parse_args()


async def main():
    """Main entry point."""
    args = parse_args()

    # Setup logging
    log_level = "DEBUG" if args.verbose else args.log_level
    setup_logging(log_level)

    logger = logging.getLogger(__name__)

    # Create and configure server
    server = MockServer(host=args.host, port=args.port)

    # Configure fast mode if requested
    if args.fast:
        logger.info("Fast mode enabled - reducing processing delays")
        for workflow in server.workflows.values():
            workflow.execution_delay = 0.5

    # Print startup information
    logger.info("=" * 60)
    logger.info("🚀 Starting Goali Mock Server")
    logger.info("=" * 60)
    logger.info(f"Host: {args.host}")
    logger.info(f"Port: {args.port}")
    logger.info(f"WebSocket URL: ws://{args.host}:{args.port}")
    logger.info(f"Log Level: {log_level}")
    logger.info(f"Fast Mode: {'Enabled' if args.fast else 'Disabled'}")
    logger.info("=" * 60)
    logger.info("Supported Workflows:")
    for workflow_name in server.workflows.keys():
        logger.info(f"  - {workflow_name}")
    logger.info("=" * 60)
    logger.info("Frontend Connection:")
    logger.info(f"  Update your frontend WebSocket URL to: ws://{args.host}:{args.port}")
    logger.info("=" * 60)
    logger.info("Press Ctrl+C to stop the server")
    logger.info("=" * 60)

    try:
        # Start the server
        await server.start()
    except KeyboardInterrupt:
        logger.info("\n" + "=" * 60)
        logger.info("🛑 Server stopped by user")
        logger.info("=" * 60)
    except Exception as e:
        logger.error(f"Server error: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"Failed to start server: {e}")
        sys.exit(1)
