"""
Base workflow implementation for mock server.

This provides common functionality for all workflow types.
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class BaseWorkflow(ABC):
    """Base class for mock workflow implementations."""

    def __init__(self, workflow_type: str):
        self.workflow_type = workflow_type
        self.execution_delay = 2.0  # Simulate processing time

    async def simulate_processing(self, stage: str, delay: Optional[float] = None):
        """Simulate processing time for a workflow stage."""
        actual_delay = delay or self.execution_delay
        logger.info(f"Processing {self.workflow_type} stage: {stage} (delay: {actual_delay}s)")
        await asyncio.sleep(actual_delay)

    def generate_workflow_id(self) -> str:
        """Generate a unique workflow ID."""
        return str(uuid.uuid4())

    def get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        return datetime.now().isoformat()

    def create_base_result(self, workflow_id: str, success: bool = True) -> Dict[str, Any]:
        """Create base result structure."""
        return {
            "workflow_id": workflow_id,
            "workflow_type": self.workflow_type,
            "success": success,
            "timestamp": self.get_current_timestamp(),
            "execution_time": self.execution_delay
        }

    @abstractmethod
    async def execute(self, input_data: Any) -> Dict[str, Any]:
        """Execute the workflow with given input data."""
        pass

    def extract_user_context(self, input_data: Any) -> Dict[str, Any]:
        """Extract user context from input data."""
        context = {}

        # Extract user profile ID
        if hasattr(input_data, 'user_profile_id'):
            context['user_profile_id'] = input_data.user_profile_id

        # Extract message content
        if hasattr(input_data, 'message'):
            context['message'] = input_data.message
            context['message_lower'] = input_data.message.lower()

        # Extract metadata
        if hasattr(input_data, 'metadata') and input_data.metadata:
            context['metadata'] = input_data.metadata

        return context

    def determine_trust_level(self, context: Dict[str, Any]) -> int:
        """Simulate trust level determination (normally done by backend agents).

        Note: In the real system, trust levels are determined by the Psychological
        Monitoring Agent based on user history, interaction patterns, and behavioral
        analysis. The frontend should NOT provide trust levels.
        """
        # For mock server, simulate different trust levels based on user ID
        user_id = context.get('user_profile_id', 'default')
        # Simple hash-based simulation for consistent but varied trust levels
        trust_hash = hash(user_id) % 100
        base_trust = max(20, min(80, trust_hash))  # Keep in reasonable range

        # Slight adjustment based on message content for simulation variety
        if 'message_lower' in context:
            message = context['message_lower']
            if any(word in message for word in ['help', 'please', 'thank']):
                base_trust += 5
            if any(word in message for word in ['frustrated', 'angry', 'upset']):
                base_trust -= 5

        return max(20, min(80, base_trust))

    def determine_trust_phase(self, trust_level: int) -> str:
        """Determine trust phase from trust level."""
        if trust_level < 40:
            return "foundation"
        elif trust_level < 70:
            return "expansion"
        else:
            return "integration"

    def generate_personalized_response(self, context: Dict[str, Any], base_response: str) -> str:
        """Generate a personalized response based on user context."""
        trust_level = self.determine_trust_level(context)
        trust_phase = self.determine_trust_phase(trust_level)

        # Adjust response based on trust phase
        if trust_phase == "foundation":
            # More supportive and encouraging
            if not base_response.startswith(("I'm here", "Let's", "We can")):
                base_response = f"I'm here to help you. {base_response}"
        elif trust_phase == "integration":
            # More collaborative and philosophical
            if "together" not in base_response.lower():
                base_response = f"{base_response} What are your thoughts on this approach?"

        return base_response
