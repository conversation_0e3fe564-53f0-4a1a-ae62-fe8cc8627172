"""
Wheel generation workflow implementation for mock server.

This simulates the wheel generation workflow behavior based on the actual
multi-agent implementation in the backend.
"""

import random
import uuid
from typing import Dict, Any, List
from .base import BaseWorkflow


class WheelGenerationWorkflow(BaseWorkflow):
    """Mock implementation of wheel generation workflow."""

    def __init__(self):
        super().__init__("wheel_generation")
        self.execution_delay = 3.0  # Longer for complex wheel generation

        # Activity templates for different domains
        self.activity_templates = {
            "creative": [
                {
                    "name": "Creative Writing",
                    "description": "Express yourself through writing",
                    "duration": 20,
                    "challenge_level": 60,
                    "resources_required": ["pen", "paper"],
                    "instructions": "Find a quiet place and write for 20 minutes about anything that comes to mind."
                },
                {
                    "name": "Sketch Something Beautiful",
                    "description": "Draw what inspires you",
                    "duration": 15,
                    "challenge_level": 40,
                    "resources_required": ["paper", "pencil"],
                    "instructions": "Look around and sketch something that catches your eye."
                },
                {
                    "name": "Music Exploration",
                    "description": "Discover new sounds",
                    "duration": 25,
                    "challenge_level": 50,
                    "resources_required": ["music player"],
                    "instructions": "Listen to a genre of music you've never explored before."
                }
            ],
            "reflective": [
                {
                    "name": "Mindful Meditation",
                    "description": "Focus on your breath and be present",
                    "duration": 10,
                    "challenge_level": 30,
                    "resources_required": [],
                    "instructions": "Sit comfortably and focus on your breath for 10 minutes."
                },
                {
                    "name": "Gratitude Journal",
                    "description": "Reflect on what you're grateful for",
                    "duration": 15,
                    "challenge_level": 25,
                    "resources_required": ["journal", "pen"],
                    "instructions": "Write down three things you're grateful for today and why."
                },
                {
                    "name": "Life Reflection",
                    "description": "Think about your personal growth",
                    "duration": 20,
                    "challenge_level": 45,
                    "resources_required": ["quiet space"],
                    "instructions": "Reflect on how you've grown in the past month."
                }
            ],
            "physical": [
                {
                    "name": "Nature Walk",
                    "description": "Connect with the outdoors",
                    "duration": 30,
                    "challenge_level": 35,
                    "resources_required": ["comfortable shoes"],
                    "instructions": "Take a 30-minute walk in nature, paying attention to your surroundings."
                },
                {
                    "name": "Gentle Stretching",
                    "description": "Move your body mindfully",
                    "duration": 15,
                    "challenge_level": 20,
                    "resources_required": ["yoga mat"],
                    "instructions": "Do some gentle stretches to release tension in your body."
                },
                {
                    "name": "Dance Break",
                    "description": "Move to your favorite music",
                    "duration": 10,
                    "challenge_level": 40,
                    "resources_required": ["music"],
                    "instructions": "Put on your favorite song and dance like nobody's watching."
                }
            ],
            "social": [
                {
                    "name": "Connect with a Friend",
                    "description": "Reach out to someone you care about",
                    "duration": 20,
                    "challenge_level": 35,
                    "resources_required": ["phone"],
                    "instructions": "Call or text a friend you haven't spoken to in a while."
                },
                {
                    "name": "Random Act of Kindness",
                    "description": "Brighten someone's day",
                    "duration": 15,
                    "challenge_level": 45,
                    "resources_required": [],
                    "instructions": "Do something kind for someone without expecting anything in return."
                },
                {
                    "name": "Community Engagement",
                    "description": "Connect with your local community",
                    "duration": 45,
                    "challenge_level": 55,
                    "resources_required": ["transportation"],
                    "instructions": "Attend a local community event or volunteer for a cause you care about."
                }
            ]
        }

        # Colors for wheel items
        self.colors = [
            "#66BB6A",  # Green
            "#42A5F5",  # Blue
            "#FFA726",  # Orange
            "#AB47BC",  # Purple
            "#EF5350",  # Red
            "#26C6DA",  # Cyan
            "#FFEE58",  # Yellow
            "#8D6E63"   # Brown
        ]

    async def execute(self, input_data: Any) -> Dict[str, Any]:
        """Execute wheel generation workflow."""
        workflow_id = self.generate_workflow_id()
        context = self.extract_user_context(input_data)

        # Simulate multi-stage processing
        await self.simulate_processing("orchestrator_analysis", 0.5)
        await self.simulate_processing("resource_assessment", 0.8)
        await self.simulate_processing("engagement_analysis", 0.7)
        await self.simulate_processing("psychological_assessment", 0.6)
        await self.simulate_processing("strategy_formulation", 1.0)
        await self.simulate_processing("activity_selection", 0.9)
        await self.simulate_processing("ethical_validation", 0.5)
        await self.simulate_processing("wheel_construction", 0.8)

        # Generate wheel based on user context
        wheel_data = self.generate_wheel(context)

        # Generate personalized introduction
        intro_message = self.generate_wheel_introduction(context, wheel_data)

        # Create result
        result = self.create_base_result(workflow_id)
        result.update({
            "user_response": intro_message,
            "wheel": wheel_data,
            "current_stage": "complete",
            "strategy_summary": self.generate_strategy_summary(context),
            "personalization_notes": self.generate_personalization_notes(context),
            "agent_outputs": {
                "orchestrator": {"status": "completed", "routing_decisions": 6},
                "resource": {"available_time": context.get('time_availability', '30 minutes')},
                "engagement": {"preferred_domains": self.select_domains(context)},
                "psychological": {"trust_phase": self.determine_trust_phase(self.determine_trust_level(context))},
                "strategy": {"challenge_level": "moderate", "focus": "balanced_growth"},
                "wheel_activity": {"activities_generated": len(wheel_data["items"])},
                "ethical": {"validation_passed": True, "concerns": []}
            }
        })

        return result

    async def handle_spin_result(self, spin_data: Any) -> Dict[str, Any]:
        """Handle spin result (activity selection)."""
        workflow_id = self.generate_workflow_id()

        # Simulate post-spin processing
        await self.simulate_processing("activity_preparation", 1.0)

        # Generate activity details
        activity_details = self.generate_activity_details(spin_data)

        # Generate encouragement message
        encouragement = self.generate_activity_encouragement(spin_data)

        # Create result
        result = self.create_base_result(workflow_id)
        result.update({
            "user_response": encouragement,
            "activity_details": activity_details,
            "current_stage": "activity_selected",
            "next_steps": [
                "Review the activity details",
                "Gather any required resources",
                "Set aside the recommended time",
                "Begin when you're ready"
            ]
        })

        return result

    def generate_wheel(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a wheel based on user context."""
        trust_level = self.determine_trust_level(context)
        trust_phase = self.determine_trust_phase(trust_level)

        # Select domains based on context
        selected_domains = self.select_domains(context)

        # Generate activities
        activities = []
        wheel_items = []
        value_propositions = {}

        total_percentage = 0
        for i, domain in enumerate(selected_domains):
            # Select activity from domain
            activity_template = random.choice(self.activity_templates[domain])
            activity_id = f"act-{uuid.uuid4().hex[:8]}"

            # Adjust challenge level based on trust phase
            challenge_adjustment = self.get_challenge_adjustment(trust_phase)
            adjusted_challenge = max(10, min(90, activity_template["challenge_level"] + challenge_adjustment))

            # Create activity
            activity = {
                "id": activity_id,
                "name": activity_template["name"],
                "description": activity_template["description"],
                "domain": domain,
                "duration": activity_template["duration"],
                "challenge_level": adjusted_challenge,
                "resources_required": activity_template["resources_required"],
                "estimated_completion_time": activity_template["duration"] + random.randint(0, 10),
                "resource_intensity": self.determine_resource_intensity(activity_template),
                "instructions": activity_template["instructions"]
            }
            activities.append(activity)

            # Create wheel item
            percentage = 100 // len(selected_domains)
            if i == len(selected_domains) - 1:  # Last item gets remaining percentage
                percentage = 100 - total_percentage
            total_percentage += percentage

            wheel_item = {
                "id": f"item-{i+1}",
                "activity_id": activity_id,
                "percentage": percentage,
                "position": i,
                "color": self.colors[i % len(self.colors)]
            }
            wheel_items.append(wheel_item)

            # Create value proposition
            value_propositions[activity_id] = self.generate_value_proposition(activity, trust_phase)

        # Create wheel metadata
        wheel_name = self.generate_wheel_name(trust_phase, selected_domains)

        # Convert to API contract format
        api_wheel_items = []
        for i, (item, activity) in enumerate(zip(wheel_items, activities)):
            api_item = {
                "id": item["id"],
                "name": activity["name"],
                "description": activity["description"],
                "percentage": item["percentage"],
                "color": item["color"],
                "domain": activity["domain"],
                "base_challenge_rating": activity["challenge_level"],
                "activity_tailored_id": activity["id"]
            }
            api_wheel_items.append(api_item)

        return {
            "name": wheel_name,
            "items": api_wheel_items
        }

    def select_domains(self, context: Dict[str, Any]) -> List[str]:
        """Select activity domains based on user context."""
        message = context.get('message', '').lower()

        # Default domains
        domains = ["creative", "reflective"]

        # Adjust based on message content
        if any(word in message for word in ['creative', 'art', 'write', 'draw', 'music']):
            domains = ["creative", "reflective", "social"]
        elif any(word in message for word in ['exercise', 'walk', 'move', 'physical']):
            domains = ["physical", "reflective", "creative"]
        elif any(word in message for word in ['social', 'friend', 'people', 'community']):
            domains = ["social", "creative", "reflective"]
        elif any(word in message for word in ['meditate', 'reflect', 'think', 'mindful']):
            domains = ["reflective", "creative", "physical"]
        else:
            # Balanced selection for general requests
            all_domains = list(self.activity_templates.keys())
            domains = random.sample(all_domains, min(3, len(all_domains)))

        return domains[:3]  # Limit to 3 domains for manageable wheel size

    def get_challenge_adjustment(self, trust_phase: str) -> int:
        """Get challenge level adjustment based on trust phase."""
        adjustments = {
            "foundation": -15,  # Lower challenge for building trust
            "expansion": 0,     # Moderate challenge for growth
            "integration": 10   # Higher challenge for advanced users
        }
        return adjustments.get(trust_phase, 0)

    def determine_resource_intensity(self, activity_template: Dict[str, Any]) -> str:
        """Determine resource intensity based on required resources."""
        resources = activity_template.get("resources_required", [])
        if len(resources) == 0:
            return "low"
        elif len(resources) <= 2:
            return "medium"
        else:
            return "high"

    def generate_value_proposition(self, activity: Dict[str, Any], trust_phase: str) -> Dict[str, Any]:
        """Generate value proposition for an activity."""
        domain = activity["domain"]

        value_props = {
            "creative": {
                "growth_value": "This activity supports your creative expression and imagination.",
                "connection_to_goals": "Creative activities help develop problem-solving skills and self-expression.",
                "challenge_description": f"This provides a {self.get_challenge_description(activity['challenge_level'])} level of creative challenge."
            },
            "reflective": {
                "growth_value": "This activity supports your self-awareness and mental well-being.",
                "connection_to_goals": "Reflective practices help you understand yourself better and reduce stress.",
                "challenge_description": f"This provides a {self.get_challenge_description(activity['challenge_level'])} level of introspective challenge."
            },
            "physical": {
                "growth_value": "This activity supports your physical health and energy levels.",
                "connection_to_goals": "Physical activities improve your overall well-being and mood.",
                "challenge_description": f"This provides a {self.get_challenge_description(activity['challenge_level'])} level of physical challenge."
            },
            "social": {
                "growth_value": "This activity supports your connections and sense of community.",
                "connection_to_goals": "Social activities help build relationships and improve communication skills.",
                "challenge_description": f"This provides a {self.get_challenge_description(activity['challenge_level'])} level of social challenge."
            }
        }

        return value_props.get(domain, value_props["creative"])

    def get_challenge_description(self, challenge_level: int) -> str:
        """Get challenge description based on level."""
        if challenge_level < 30:
            return "gentle"
        elif challenge_level < 60:
            return "moderate"
        else:
            return "engaging"

    def generate_wheel_name(self, trust_phase: str, domains: List[str]) -> str:
        """Generate a name for the wheel."""
        phase_names = {
            "foundation": "Foundation",
            "expansion": "Growth",
            "integration": "Mastery"
        }

        domain_focus = domains[0].title() if domains else "Balanced"
        phase_name = phase_names.get(trust_phase, "Personal")

        return f"{domain_focus} {phase_name} Wheel"

    def generate_wheel_introduction(self, context: Dict[str, Any], wheel_data: Dict[str, Any]) -> str:
        """Generate personalized introduction for the wheel."""
        trust_level = self.determine_trust_level(context)
        trust_phase = self.determine_trust_phase(trust_level)
        wheel_name = wheel_data["name"]

        intros = {
            "foundation": f"I've created a {wheel_name} just for you! These activities are designed to be gentle and supportive as we build our journey together. Each one is chosen to help you feel more confident and engaged.",
            "expansion": f"Here's your personalized {wheel_name}! I've selected activities that will challenge you in just the right way while supporting your growth. These are tailored to where you are right now.",
            "integration": f"I've crafted a {wheel_name} that reflects your readiness for deeper exploration. These activities are designed to integrate with your personal development journey and offer meaningful challenges."
        }

        base_intro = intros.get(trust_phase, intros["expansion"])
        return self.generate_personalized_response(context, base_intro)

    def generate_strategy_summary(self, context: Dict[str, Any]) -> str:
        """Generate a summary of the strategy used."""
        trust_level = self.determine_trust_level(context)
        domains = self.select_domains(context)

        return f"Strategy: Balanced approach focusing on {', '.join(domains)} activities with moderate challenge level (trust level: {trust_level})"

    def generate_personalization_notes(self, context: Dict[str, Any]) -> List[str]:
        """Generate notes about personalization applied."""
        notes = []

        message = context.get('message', '').lower()
        if 'creative' in message:
            notes.append("Emphasized creative activities based on your interest")
        if 'physical' in message or 'exercise' in message:
            notes.append("Included physical activities to match your preferences")
        if 'social' in message:
            notes.append("Added social elements to support connection")
        if 'meditate' in message or 'mindful' in message:
            notes.append("Focused on reflective practices for mindfulness")

        if not notes:
            notes.append("Balanced selection based on general wellness principles")

        trust_level = self.determine_trust_level(context)
        notes.append(f"Challenge levels adjusted for trust level {trust_level}")

        return notes

    def generate_activity_details(self, spin_data: Any) -> Dict[str, Any]:
        """Generate detailed information about the selected activity."""
        activity_name = getattr(spin_data, 'name', 'Selected Activity')
        activity_id = getattr(spin_data, 'activity_tailored_id', 'unknown')

        # Mock detailed activity information
        return {
            "id": activity_id,
            "name": activity_name,
            "detailed_description": f"You've selected {activity_name}! This is a wonderful choice that will help you grow and explore new aspects of yourself.",
            "preparation_steps": [
                "Find a comfortable and quiet space",
                "Gather any materials you might need",
                "Set aside dedicated time without distractions",
                "Approach this with an open and curious mindset"
            ],
            "tips_for_success": [
                "Start with a positive intention",
                "Be patient with yourself",
                "Focus on the process rather than the outcome",
                "Notice how you feel before, during, and after"
            ],
            "reflection_questions": [
                "What did you discover about yourself?",
                "How did this activity make you feel?",
                "What would you do differently next time?",
                "How might this connect to other areas of your life?"
            ]
        }

    def generate_activity_encouragement(self, spin_data: Any) -> str:
        """Generate encouraging message for activity selection."""
        activity_name = getattr(spin_data, 'name', 'this activity')

        encouragements = [
            f"Wonderful choice! {activity_name} is a perfect way to explore and grow. I'm excited for you to experience this!",
            f"I love that you selected {activity_name}! This activity has so much potential to bring you joy and insight.",
            f"Great selection! {activity_name} is going to be a meaningful experience. Take your time and enjoy the journey.",
            f"Perfect! {activity_name} is exactly what you need right now. Trust yourself and embrace this opportunity.",
            f"Excellent choice! {activity_name} will help you connect with yourself in a beautiful way. I'm here to support you!"
        ]

        return random.choice(encouragements)
