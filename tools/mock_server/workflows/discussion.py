"""
Discussion workflow implementation for mock server.

This simulates the discussion workflow behavior based on the actual
implementation in the backend.
"""

import random
from typing import Dict, Any, List
from .base import BaseWorkflow


class DiscussionWorkflow(BaseWorkflow):
    """Mock implementation of discussion workflow."""
    
    def __init__(self):
        super().__init__("discussion")
        self.execution_delay = 1.5  # Faster for discussion
        
        # Predefined responses for different types of input
        self.response_templates = {
            "greeting": [
                "Hello! I'm glad you're here. What's on your mind today?",
                "Hi there! How are you feeling right now?",
                "Welcome! I'm here to listen and support you. What would you like to talk about?"
            ],
            "emotional": [
                "I can sense that you're going through something important. Would you like to share more about how you're feeling?",
                "It sounds like you have some strong feelings about this. Can you tell me more?",
                "I'm here to listen. Sometimes talking through our emotions can help us understand them better."
            ],
            "goal_setting": [
                "That's a wonderful goal! What steps do you think would help you get there?",
                "I love that you're thinking about your future. What does success look like to you?",
                "Setting goals is such an important step. What's motivating you to pursue this?"
            ],
            "reflection": [
                "That's a really thoughtful observation. How does that insight feel to you?",
                "It sounds like you've learned something important about yourself. What does that mean for you going forward?",
                "Reflection is so valuable. What other patterns do you notice in your life?"
            ],
            "challenge": [
                "Challenges can be tough, but they often lead to growth. What do you think this situation is teaching you?",
                "I hear that this is difficult for you. What support do you need right now?",
                "Sometimes our biggest challenges become our greatest strengths. How might you approach this differently?"
            ],
            "general": [
                "That's interesting. Can you tell me more about that?",
                "I'm curious to hear your perspective on this. What are your thoughts?",
                "Thank you for sharing that with me. How does it feel to talk about this?"
            ]
        }
        
        # Follow-up questions to keep conversation flowing
        self.follow_up_questions = [
            "What else comes to mind when you think about this?",
            "How does this connect to other areas of your life?",
            "What would you like to explore further?",
            "Is there anything else you'd like to share about this?",
            "How are you feeling about our conversation so far?"
        ]
        
    async def execute(self, input_data: Any) -> Dict[str, Any]:
        """Execute discussion workflow."""
        workflow_id = self.generate_workflow_id()
        context = self.extract_user_context(input_data)
        
        # Simulate processing
        await self.simulate_processing("analyzing_input")
        
        # Determine response type based on message content
        response_type = self.classify_message(context.get('message', ''))
        
        # Generate response
        base_response = self.generate_response(response_type, context)
        personalized_response = self.generate_personalized_response(context, base_response)
        
        # Generate insights and recommendations
        insights = self.generate_insights(context)
        recommendations = self.generate_recommendations(context)
        
        # Create result
        result = self.create_base_result(workflow_id)
        result.update({
            "user_response": personalized_response,
            "current_stage": "conversation",
            "insights_gained": insights,
            "recommendations": recommendations,
            "follow_up_questions": random.sample(self.follow_up_questions, 2),
            "emotional_tone": self.detect_emotional_tone(context.get('message', '')),
            "conversation_summary": f"User shared thoughts about: {response_type}",
            "next_agent": None  # Discussion continues with same agent
        })
        
        return result
        
    def classify_message(self, message: str) -> str:
        """Classify the type of message to determine appropriate response."""
        message_lower = message.lower()
        
        # Check for greetings
        if any(word in message_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
            return "greeting"
            
        # Check for emotional content
        if any(word in message_lower for word in ['feel', 'emotion', 'sad', 'happy', 'angry', 'frustrated', 'excited', 'anxious']):
            return "emotional"
            
        # Check for goal-related content
        if any(word in message_lower for word in ['goal', 'want to', 'hope to', 'plan to', 'achieve', 'accomplish']):
            return "goal_setting"
            
        # Check for reflective content
        if any(word in message_lower for word in ['think', 'realize', 'understand', 'learned', 'noticed', 'pattern']):
            return "reflection"
            
        # Check for challenges
        if any(word in message_lower for word in ['difficult', 'hard', 'struggle', 'challenge', 'problem', 'stuck']):
            return "challenge"
            
        return "general"
        
    def generate_response(self, response_type: str, context: Dict[str, Any]) -> str:
        """Generate appropriate response based on message type."""
        templates = self.response_templates.get(response_type, self.response_templates["general"])
        return random.choice(templates)
        
    def generate_insights(self, context: Dict[str, Any]) -> List[str]:
        """Generate insights based on the conversation."""
        message = context.get('message', '').lower()
        insights = []
        
        if 'goal' in message or 'want' in message:
            insights.append("You seem motivated to make positive changes in your life")
            
        if any(word in message for word in ['feel', 'emotion']):
            insights.append("You're in touch with your emotions and willing to explore them")
            
        if any(word in message for word in ['difficult', 'challenge']):
            insights.append("You're facing challenges with courage and seeking support")
            
        if not insights:
            insights.append("You're taking time for self-reflection, which shows self-awareness")
            
        return insights
        
    def generate_recommendations(self, context: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on the conversation."""
        message = context.get('message', '').lower()
        recommendations = []
        
        if 'goal' in message:
            recommendations.append("Consider breaking your goal into smaller, manageable steps")
            
        if any(word in message for word in ['stress', 'anxious', 'overwhelmed']):
            recommendations.append("Try some mindfulness or breathing exercises")
            
        if 'challenge' in message or 'difficult' in message:
            recommendations.append("Remember that challenges are opportunities for growth")
            
        if not recommendations:
            recommendations.append("Continue this self-reflection practice regularly")
            
        return recommendations
        
    def detect_emotional_tone(self, message: str) -> str:
        """Detect the emotional tone of the message."""
        message_lower = message.lower()
        
        positive_words = ['happy', 'excited', 'grateful', 'good', 'great', 'wonderful', 'amazing']
        negative_words = ['sad', 'angry', 'frustrated', 'upset', 'difficult', 'hard', 'struggle']
        neutral_words = ['think', 'consider', 'maybe', 'perhaps', 'wondering']
        
        if any(word in message_lower for word in positive_words):
            return "positive"
        elif any(word in message_lower for word in negative_words):
            return "challenging"
        elif any(word in message_lower for word in neutral_words):
            return "reflective"
        else:
            return "neutral"
