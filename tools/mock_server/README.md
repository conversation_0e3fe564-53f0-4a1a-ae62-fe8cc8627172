# Goali Mock Server

A mock WebSocket server that simulates backend behavior for frontend development, using the benchmarking system as the source of truth for input-output contracts.

## Features

- **Realistic Workflow Simulation**: Simulates discussion and wheel_generation workflows with proper timing and responses
- **Pydantic-based Contracts**: Uses the same data models as the benchmarking system for consistency
- **WebSocket Compatibility**: Compatible with the existing frontend WebSocket manager
- **Personalized Responses**: Adapts responses based on user context and trust levels
- **Multi-stage Processing**: Simulates the multi-agent workflow execution with realistic delays

## Quick Start

### Option 1: Docker (Recommended)

1. **Start with Docker Compose**:
   ```bash
   cd tools/mock_server
   docker-compose up -d
   ```

2. **Monitor the Server**:
   - WebSocket: `ws://localhost:8765`
   - Monitor UI: `http://localhost:8080`

3. **View Logs**:
   ```bash
   docker-compose logs -f goali-mock-server
   ```

### Option 2: Local Development

1. **Install Dependencies**:
   ```bash
   cd tools/mock_server
   pip install -r requirements.txt
   ```

2. **Start the Server**:
   ```bash
   python3 run.py --fast
   ```

3. **Connect from Frontend**:
   The server runs on `ws://localhost:8765` and is compatible with the existing frontend WebSocket manager.

## Supported Workflows

### Discussion Workflow
- **Endpoint**: Send `chat_message` type messages
- **Features**:
  - Contextual response generation
  - Emotional tone detection
  - Personalized recommendations
  - Trust-level adaptation

### Wheel Generation Workflow
- **Endpoint**: Send `chat_message` with wheel-related keywords
- **Features**:
  - Multi-stage processing simulation
  - Activity domain selection
  - Personalized wheel creation
  - Challenge level adjustment

### Activity Selection
- **Endpoint**: Send `spin_result` type messages
- **Features**:
  - Activity detail generation
  - Encouragement messages
  - Preparation guidance

## Message Types

The server supports the following WebSocket message types:

### Client to Server
- `chat_message`: User chat messages
- `spin_result`: Activity selection results
- `workflow_status_request`: Request workflow status

### Server to Client
- `system_message`: System notifications
- `processing_status`: Processing status updates
- `wheel_data`: Generated wheel data
- `error`: Error messages
- `workflow_status`: Workflow status responses
- `activity_details`: Detailed activity information

## Configuration

### Environment Variables

The server supports configuration via environment variables:

- `MOCK_SERVER_HOST`: Server host (default: "localhost")
- `MOCK_SERVER_PORT`: Server port (default: 8765)
- `MOCK_SERVER_LOG_LEVEL`: Logging level (default: "INFO")

### Docker Configuration

```yaml
# docker-compose.yml
environment:
  - MOCK_SERVER_HOST=0.0.0.0
  - MOCK_SERVER_PORT=8765
  - MOCK_SERVER_LOG_LEVEL=DEBUG
```

### Command Line Options

```bash
python3 run.py --help
```

Available options:
- `--host`: Server host
- `--port`: Server port
- `--log-level`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `--fast`: Enable fast mode with reduced processing delays
- `--verbose`: Enable verbose logging

## Development

### Adding New Workflows

1. Create a new workflow class in `workflows/`
2. Inherit from `BaseWorkflow`
3. Implement the `execute` method
4. Add to the workflows dictionary in `server.py`

### Modifying Contracts

The contracts are defined in the `contracts/` directory:
- `base.py`: Base workflow contracts
- `discussion.py`: Discussion workflow contracts
- `wheel_generation.py`: Wheel generation contracts
- `websocket.py`: WebSocket message contracts

### Testing

The mock server is designed to work with the existing frontend without modifications. Simply:

1. Start the mock server
2. Configure your frontend to connect to `ws://localhost:8765`
3. Test your workflows

## Architecture

The mock server follows the same architectural patterns as the real backend:

- **Pydantic Models**: Ensures type safety and validation
- **Async Processing**: Simulates real-world async behavior
- **Multi-stage Workflows**: Mimics the multi-agent system
- **Personalization**: Adapts to user context and trust levels

## Compatibility

This mock server is designed to be a drop-in replacement for the real backend during frontend development. It maintains compatibility with:

- Existing WebSocket message formats
- Frontend WebSocket manager
- TypeScript API contracts
- Workflow execution patterns

## Docker Deployment

For production deployment, see the comprehensive [Docker Deployment Guide](DOCKER_DEPLOYMENT.md).

### Quick Docker Commands

```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f

# Stop and remove
docker-compose down

# Update and restart
docker-compose pull && docker-compose up -d
```

### Health Monitoring

- **Built-in health checks**: Docker container includes automatic health monitoring
- **Monitor UI**: Access `http://localhost:8080` for web-based monitoring
- **Manual health check**: `docker exec goali-mock-server python3 test_client.py`

## Message Specifications

For complete message format documentation, see [MESSAGE_SPECIFICATIONS.md](MESSAGE_SPECIFICATIONS.md).

This includes:
- All supported message types with examples
- Complete workflow examples
- Error handling scenarios
- Trust level adaptation details

## Troubleshooting

### Connection Issues
- **Docker**: Check container status with `docker ps`
- **Local**: Ensure the server is running on the correct port
- **Network**: Verify no other services are using port 8765
- **Frontend**: Verify WebSocket connection URL matches server

### Message Format Issues
- Check that messages follow the expected WebSocket contract
- Verify JSON structure matches the Pydantic models
- Review server logs for detailed error messages
- Use the monitor UI to test message formats

### Performance Issues
- **Fast mode**: Use `--fast` flag or set fast mode in Docker
- **Logging**: Adjust log level to reduce overhead
- **Resources**: Monitor Docker container resource usage
- **Delays**: Adjust processing delays in workflow implementations
