"""
WebSocket message contracts.

These models define the WebSocket message structure for communication
between frontend and backend, based on the existing API contract.
"""

from typing import Dict, Any, Optional, Union, Literal
from pydantic import BaseModel, Field
from enum import Enum

from .wheel_generation import WheelData


class MessageType(str, Enum):
    """WebSocket message types."""
    # Client to Server
    CHAT_MESSAGE = "chat_message"
    SPIN_RESULT = "spin_result"
    WORKFLOW_STATUS_REQUEST = "workflow_status_request"

    # Server to Client
    SYSTEM_MESSAGE = "system_message"
    PROCESSING_STATUS = "processing_status"
    WHEEL_DATA = "wheel_data"
    ERROR = "error"
    WORKFLOW_STATUS = "workflow_status"
    DEBUG_INFO = "debug_info"
    ACTIVITY_DETAILS = "activity_details"


class ProcessingStatusType(str, Enum):
    """Processing status values."""
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"


class WorkflowStatusType(str, Enum):
    """Workflow status values."""
    INITIAL = "initial"
    WHEEL_GENERATED = "wheel_generated"
    ACTIVITY_SELECTED = "activity_selected"
    ACTIVITY_IN_PROGRESS = "activity_in_progress"
    ACTIVITY_COMPLETED = "activity_completed"


class WebSocketMessage(BaseModel):
    """Base WebSocket message structure."""
    type: MessageType = Field(..., description="Message type")
    content: Dict[str, Any] = Field(default_factory=dict, description="Message content")

    class Config:
        extra = "allow"  # Allow additional fields like is_user, status, etc.


# Client to Server Messages

class ChatMessageContent(BaseModel):
    """Content for chat messages from client.

    Note: Trust levels, mood, environment, and other user context variables
    are determined by backend agents, not provided by the frontend.
    """
    message: str = Field(..., description="User's message text")
    user_profile_id: str = Field(..., description="User profile identifier")
    timestamp: Optional[str] = Field(None, description="ISO-8601 timestamp")
    metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional metadata. Only 'requested_workflow' should be provided by frontend."
    )


class SpinResultContent(BaseModel):
    """Content for spin result messages from client."""
    activity_tailored_id: str = Field(..., description="Selected activity ID")
    name: str = Field(..., description="Activity name")
    description: Optional[str] = Field(None, description="Activity description")
    user_profile_id: str = Field(..., description="User profile identifier")


class WorkflowStatusRequestContent(BaseModel):
    """Content for workflow status requests from client."""
    workflow_id: str = Field(..., description="Workflow identifier")


# Server to Client Messages

class SystemMessageContent(BaseModel):
    """Content for system messages to client."""
    content: str = Field(..., description="System message text")


class ChatMessageFromServerContent(BaseModel):
    """Content for chat messages from server."""
    content: str = Field(..., description="Message text")
    is_user: bool = Field(False, description="Whether message is from user")


class ProcessingStatusContent(BaseModel):
    """Content for processing status messages."""
    status: ProcessingStatusType = Field(..., description="Processing status")


class WheelDataContent(BaseModel):
    """Content for wheel data messages."""
    wheel: WheelData = Field(..., description="Wheel data")


class ErrorContent(BaseModel):
    """Content for error messages."""
    content: str = Field(..., description="Error message")
    code: Optional[str] = Field(None, description="Error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Error details")


class WorkflowStatusContent(BaseModel):
    """Content for workflow status messages."""
    workflow_id: str = Field(..., description="Workflow identifier")
    status: WorkflowStatusType = Field(..., description="Workflow status")
    stage: Optional[str] = Field(None, description="Current workflow stage")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional status data")


class DebugInfoContent(BaseModel):
    """Content for debug info messages."""
    timestamp: str = Field(..., description="ISO-8601 timestamp")
    source: str = Field(..., description="Source of debug info")
    level: Literal["info", "warning", "error", "debug"] = Field(..., description="Debug level")
    message: str = Field(..., description="Debug message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional debug details")


class ActivityDetailsContent(BaseModel):
    """Content for activity details messages."""
    details: Dict[str, Any] = Field(..., description="Activity details")


# Typed message classes

class ChatMessage(WebSocketMessage):
    """Chat message with typed content."""
    type: Literal[MessageType.CHAT_MESSAGE] = MessageType.CHAT_MESSAGE
    content: Union[ChatMessageContent, ChatMessageFromServerContent]
    is_user: Optional[bool] = None


class SpinResult(WebSocketMessage):
    """Spin result message with typed content."""
    type: Literal[MessageType.SPIN_RESULT] = MessageType.SPIN_RESULT
    content: SpinResultContent


class ProcessingStatus(WebSocketMessage):
    """Processing status message with typed content."""
    type: Literal[MessageType.PROCESSING_STATUS] = MessageType.PROCESSING_STATUS
    status: ProcessingStatusType


class ErrorMessage(WebSocketMessage):
    """Error message with typed content."""
    type: Literal[MessageType.ERROR] = MessageType.ERROR
    content: Union[str, ErrorContent]


class WheelDataMessage(WebSocketMessage):
    """Wheel data message with typed content."""
    type: Literal[MessageType.WHEEL_DATA] = MessageType.WHEEL_DATA
    wheel: WheelData


class WorkflowStatus(WebSocketMessage):
    """Workflow status message with typed content."""
    type: Literal[MessageType.WORKFLOW_STATUS] = MessageType.WORKFLOW_STATUS
    content: WorkflowStatusContent
