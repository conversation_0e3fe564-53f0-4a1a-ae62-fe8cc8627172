"""
Wheel generation workflow contracts.

These models define the input/output structure for wheel generation workflows,
based on the existing wheel generation graph and benchmarking system.
"""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from enum import Enum

from .base import BaseWorkflowInput, BaseWorkflowOutput, WorkflowType, TrustPhase


class WheelGenerationStage(str, Enum):
    """Stages in the wheel generation workflow."""
    INITIAL = "initial"
    ORCHESTRATOR = "orchestrator"
    RESOURCE_ANALYSIS = "resource_analysis"
    ENGAGEMENT_ANALYSIS = "engagement_analysis"
    PSYCHOLOGICAL_ASSESSMENT = "psychological_assessment"
    STRATEGY_FORMULATION = "strategy_formulation"
    ACTIVITY_SELECTION = "activity_selection"
    ETHICAL_VALIDATION = "ethical_validation"
    WHEEL_CONSTRUCTION = "wheel_construction"
    COMPLETE = "complete"


class WheelMetadata(BaseModel):
    """Metadata for a generated wheel."""
    name: str = Field(..., description="Name of the wheel")
    trust_phase: TrustPhase = Field(..., description="Trust phase for the wheel")
    description: Optional[str] = Field(None, description="Description of the wheel")
    timestamp: Optional[str] = Field(None, description="When the wheel was created")
    user_id: Optional[str] = Field(None, description="ID of the user the wheel is for")
    challenge_level: Optional[str] = Field(None, description="Overall challenge level")


class WheelItem(BaseModel):
    """Individual item in a wheel - matches API contract."""
    id: str = Field(..., description="Unique identifier for the wheel item")
    name: str = Field(..., description="Display name for the activity")
    description: str = Field(..., description="Activity description text")
    percentage: float = Field(..., ge=0, le=100, description="Probability weight for selection")
    color: str = Field(..., description="HEX color code for visual representation")
    domain: str = Field(..., description="Primary domain code (e.g., 'creative')")
    base_challenge_rating: int = Field(..., ge=0, le=100, description="Difficulty level of the activity")
    activity_tailored_id: str = Field(..., description="Reference to the underlying ActivityTailored object")


class WheelActivity(BaseModel):
    """Activity definition for wheel items."""
    id: str = Field(..., description="Unique identifier for the activity")
    name: str = Field(..., description="Name of the activity")
    description: str = Field(..., description="Description of the activity")
    domain: str = Field(..., description="Domain of the activity (e.g., creative, reflective)")
    duration: int = Field(..., ge=1, description="Duration in minutes")
    challenge_level: int = Field(..., ge=0, le=100, description="Challenge level (0-100)")
    resources_required: List[str] = Field(default_factory=list, description="Required resources")
    estimated_completion_time: Optional[int] = Field(
        None, ge=1, description="Estimated completion time in minutes"
    )
    resource_intensity: Optional[str] = Field(
        None, description="Resource intensity (low, medium, high)"
    )
    adaptability: Optional[Dict[str, Any]] = Field(
        None, description="Adaptability options for the activity"
    )
    instructions: Optional[str] = Field(None, description="Instructions for the activity")


class WheelData(BaseModel):
    """Complete wheel data structure - matches API contract."""
    name: str = Field(..., description="Wheel name (e.g., 'Daily Challenge Wheel')")
    items: List[WheelItem] = Field(..., description="List of wheel items")


class WheelGenerationInput(BaseWorkflowInput):
    """Input structure for wheel generation workflow."""
    current_stage: WheelGenerationStage = Field(
        WheelGenerationStage.INITIAL,
        description="Current stage of wheel generation"
    )
    resource_constraints: Optional[Dict[str, Any]] = Field(
        None, description="User's resource constraints"
    )
    activity_preferences: Optional[Dict[str, Any]] = Field(
        None, description="User's activity preferences"
    )
    previous_wheels: List[Dict[str, Any]] = Field(
        default_factory=list, description="Previously generated wheels"
    )

    def __init__(self, **data):
        # Ensure workflow_type is set correctly
        if 'metadata' in data:
            data['metadata']['workflow_type'] = WorkflowType.WHEEL_GENERATION
        super().__init__(**data)


class WheelGenerationOutput(BaseWorkflowOutput):
    """Output structure for wheel generation workflow."""
    current_stage: WheelGenerationStage = Field(
        WheelGenerationStage.COMPLETE,
        description="Current stage after processing"
    )
    wheel: Optional[WheelData] = Field(None, description="Generated wheel data")
    agent_outputs: Dict[str, Any] = Field(
        default_factory=dict, description="Outputs from individual agents"
    )
    strategy_summary: Optional[str] = Field(
        None, description="Summary of the strategy used"
    )
    personalization_notes: List[str] = Field(
        default_factory=list, description="Notes on personalization applied"
    )

    def __init__(self, **data):
        # Ensure workflow_type is set correctly
        data['workflow_type'] = WorkflowType.WHEEL_GENERATION
        super().__init__(**data)
