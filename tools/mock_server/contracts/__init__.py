"""
Workflow contracts for the mock server.

This module defines the input-output contracts for workflows based on the
benchmarking system as the source of truth. These contracts ensure that
the mock server provides realistic and consistent behavior for frontend
development and testing.
"""

from .base import *
from .discussion import *
from .wheel_generation import *
from .websocket import *

__all__ = [
    # Base contracts
    'BaseWorkflowInput',
    'BaseWorkflowOutput', 
    'ContextPacket',
    'UserProfile',
    'WorkflowMetadata',
    
    # Discussion workflow
    'DiscussionInput',
    'DiscussionOutput',
    'DiscussionStage',
    
    # Wheel generation workflow
    'WheelGenerationInput',
    'WheelGenerationOutput',
    'WheelData',
    'WheelItem',
    'WheelActivity',
    'WheelMetadata',
    
    # WebSocket messages
    'WebSocketMessage',
    'ChatMessage',
    'SpinResult',
    'ProcessingStatus',
    'ErrorMessage',
    'WheelDataMessage',
    'WorkflowStatus',
]
