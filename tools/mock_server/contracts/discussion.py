"""
Discussion workflow contracts.

These models define the input/output structure for discussion workflows,
based on the existing discussion graph implementation and benchmarking system.
"""

from typing import Dict, Any, Optional, List
from pydantic import Field
from enum import Enum

from .base import BaseWorkflowInput, BaseWorkflowOutput, WorkflowType


class DiscussionStage(str, Enum):
    """Stages in the discussion workflow."""
    INITIAL = "initial"
    CONVERSATION = "conversation"
    REFLECTION = "reflection"
    GUIDANCE = "guidance"
    TRANSITION_CHECK = "transition_check"
    WORKFLOW_COMPLETE = "workflow_complete"


class DiscussionInput(BaseWorkflowInput):
    """Input structure for discussion workflow."""
    conversation_history: List[Dict[str, Any]] = Field(
        default_factory=list, 
        description="Previous conversation messages"
    )
    current_stage: DiscussionStage = Field(
        DiscussionStage.INITIAL, 
        description="Current stage of discussion"
    )
    discussion_topic: Optional[str] = Field(
        None, 
        description="Main topic of discussion"
    )
    user_goals: Optional[List[str]] = Field(
        None, 
        description="User's stated goals or objectives"
    )
    
    def __init__(self, **data):
        # Ensure workflow_type is set correctly
        if 'metadata' in data:
            data['metadata']['workflow_type'] = WorkflowType.DISCUSSION
        super().__init__(**data)


class DiscussionOutput(BaseWorkflowOutput):
    """Output structure for discussion workflow."""
    current_stage: DiscussionStage = Field(
        DiscussionStage.CONVERSATION, 
        description="Current stage after processing"
    )
    conversation_summary: Optional[str] = Field(
        None, 
        description="Summary of the conversation so far"
    )
    insights_gained: List[str] = Field(
        default_factory=list, 
        description="Key insights from the discussion"
    )
    recommendations: List[str] = Field(
        default_factory=list, 
        description="Recommendations for the user"
    )
    transition_suggestion: Optional[str] = Field(
        None, 
        description="Suggested transition to another workflow"
    )
    follow_up_questions: List[str] = Field(
        default_factory=list, 
        description="Questions to continue the discussion"
    )
    emotional_tone: Optional[str] = Field(
        None, 
        description="Detected emotional tone of the conversation"
    )
    
    def __init__(self, **data):
        # Ensure workflow_type is set correctly
        data['workflow_type'] = WorkflowType.DISCUSSION
        super().__init__(**data)


class DiscussionContext(BaseWorkflowInput):
    """Extended context for discussion workflows."""
    previous_sessions: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Previous discussion sessions"
    )
    user_mood_history: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Historical mood data"
    )
    preferred_discussion_style: Optional[str] = Field(
        None,
        description="User's preferred discussion approach"
    )
    sensitive_topics: List[str] = Field(
        default_factory=list,
        description="Topics to handle with care"
    )
    
    class Config:
        extra = "allow"
