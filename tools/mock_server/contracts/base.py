"""
Base contracts for workflow input/output.

These models define the common structure for all workflows, extracted from
the benchmarking system and existing workflow implementations.
"""

from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class TrustPhase(str, Enum):
    """Trust phases for user interaction."""
    FOUNDATION = "foundation"  # 0-39
    EXPANSION = "expansion"    # 40-69
    INTEGRATION = "integration"  # 70-100


class WorkflowType(str, Enum):
    """Supported workflow types."""
    DISCUSSION = "discussion"
    WHEEL_GENERATION = "wheel_generation"
    ACTIVITY_FEEDBACK = "activity_feedback"
    PRE_SPIN_FEEDBACK = "pre_spin_feedback"
    POST_SPIN = "post_spin"
    ONBOARDING = "onboarding"


class UserProfile(BaseModel):
    """User profile information for workflows."""
    user_id: str = Field(..., description="Unique user identifier")
    trust_level: int = Field(50, ge=0, le=100, description="User trust level (0-100)")
    trust_phase: TrustPhase = Field(TrustPhase.EXPANSION, description="Current trust phase")
    personality_traits: Optional[Dict[str, float]] = Field(
        None, description="HEXACO personality traits (0-1 scale)"
    )
    preferences: Optional[Dict[str, Any]] = Field(
        None, description="User preferences and settings"
    )
    communication_style: Optional[str] = Field(
        None, description="Preferred communication style"
    )
    
    @property
    def computed_trust_phase(self) -> TrustPhase:
        """Compute trust phase from trust level."""
        if self.trust_level < 40:
            return TrustPhase.FOUNDATION
        elif self.trust_level < 70:
            return TrustPhase.EXPANSION
        else:
            return TrustPhase.INTEGRATION


class ContextPacket(BaseModel):
    """Context information for workflow execution."""
    user_text: Optional[str] = Field(None, description="User's input text")
    mood: Optional[str] = Field(None, description="User's current mood")
    environment: Optional[str] = Field(None, description="User's environment")
    time_availability: Optional[str] = Field(None, description="Available time")
    cognitive_focus: Optional[str] = Field(None, description="User's cognitive focus")
    trust_level: Optional[int] = Field(None, ge=0, le=100, description="Trust level")
    extraction_confidence: Optional[float] = Field(
        None, ge=0, le=1, description="Confidence in context extraction"
    )
    session_timestamp: Optional[datetime] = Field(
        None, description="Session timestamp"
    )
    user_ws_session_name: Optional[str] = Field(
        None, description="WebSocket session identifier"
    )


class WorkflowMetadata(BaseModel):
    """Metadata for workflow execution."""
    workflow_id: str = Field(..., description="Unique workflow identifier")
    workflow_type: WorkflowType = Field(..., description="Type of workflow")
    user_profile_id: str = Field(..., description="User profile identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    timestamp: datetime = Field(default_factory=datetime.now, description="Execution timestamp")
    estimated_duration: Optional[int] = Field(None, description="Estimated duration in seconds")


class BaseWorkflowInput(BaseModel):
    """Base input structure for all workflows."""
    metadata: WorkflowMetadata = Field(..., description="Workflow metadata")
    context_packet: ContextPacket = Field(..., description="Context information")
    user_profile: UserProfile = Field(..., description="User profile")
    
    class Config:
        extra = "allow"  # Allow additional fields for specific workflows


class BaseWorkflowOutput(BaseModel):
    """Base output structure for all workflows."""
    workflow_id: str = Field(..., description="Workflow identifier")
    workflow_type: WorkflowType = Field(..., description="Type of workflow")
    success: bool = Field(True, description="Whether workflow completed successfully")
    user_response: Optional[str] = Field(None, description="Response message for user")
    next_agent: Optional[str] = Field(None, description="Next agent to route to")
    stage: Optional[str] = Field(None, description="Current workflow stage")
    output_data: Dict[str, Any] = Field(default_factory=dict, description="Workflow-specific output")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered")
    execution_time: Optional[float] = Field(None, description="Execution time in seconds")
    
    class Config:
        extra = "allow"  # Allow additional fields for specific workflows
