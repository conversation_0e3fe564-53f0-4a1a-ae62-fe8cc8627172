# Goali Mock Server Docker Image
# Based on Python 3.12 with optimized dependencies

FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV MOCK_SERVER_HOST=0.0.0.0
ENV MOCK_SERVER_PORT=8765
ENV MOCK_SERVER_LOG_LEVEL=INFO

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash mockserver && \
    chown -R mockserver:mockserver /app
USER mockserver

# Expose the WebSocket port
EXPOSE 8765

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python3 -c "import asyncio, websockets; asyncio.run(websockets.connect('ws://localhost:8765'))" || exit 1

# Default command
CMD ["python3", "run.py", "--host", "0.0.0.0", "--port", "8765"]
