# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Git
.git/
.gitignore

# Documentation (keep only essential docs in container)
docs/
*.md
!README.md
!MESSAGE_SPECIFICATIONS.md

# Test files
test_*.py
*_test.py

# Development files
.env.local
.env.development
