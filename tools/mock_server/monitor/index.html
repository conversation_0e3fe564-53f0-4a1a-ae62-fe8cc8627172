<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Goali Mock Server Monitor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border-left: 4px solid #4facfe;
        }
        
        .status-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-dot.online {
            background: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }
        
        .status-dot.offline {
            background: #dc3545;
        }
        
        .connection-test {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .test-button {
            background: #4facfe;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            margin-right: 15px;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #3d8bfe;
        }
        
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
        }
        
        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
        }
        
        .info-section h3 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid #4facfe;
        }
        
        .info-item strong {
            color: #333;
            display: block;
            margin-bottom: 5px;
        }
        
        .info-item span {
            color: #666;
            font-family: monospace;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Goali Mock Server</h1>
            <p>WebSocket Server Monitor & Testing Interface</p>
        </div>
        
        <div class="content">
            <div class="status-grid">
                <div class="status-card">
                    <h3>Server Status</h3>
                    <div class="status-indicator">
                        <div class="status-dot" id="serverStatus"></div>
                        <span id="serverStatusText">Checking...</span>
                    </div>
                    <div>Last checked: <span id="lastCheck">Never</span></div>
                </div>
                
                <div class="status-card">
                    <h3>Connection Info</h3>
                    <div>WebSocket URL: <code id="wsUrl">ws://localhost:8765</code></div>
                    <div>Protocol: WebSocket</div>
                    <div>Status: <span id="connectionStatus">Disconnected</span></div>
                </div>
            </div>
            
            <div class="connection-test">
                <h3>🔧 Connection Testing</h3>
                <p>Test the WebSocket connection and server functionality:</p>
                <br>
                <button class="test-button" onclick="testConnection()">Test Connection</button>
                <button class="test-button" onclick="testDiscussion()">Test Discussion</button>
                <button class="test-button" onclick="testWheelGeneration()">Test Wheel Generation</button>
                <div id="testResult"></div>
            </div>
            
            <div class="info-section">
                <h3>📋 Server Information</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <strong>Default Port</strong>
                        <span>8765</span>
                    </div>
                    <div class="info-item">
                        <strong>Supported Workflows</strong>
                        <span>discussion, wheel_generation</span>
                    </div>
                    <div class="info-item">
                        <strong>Message Format</strong>
                        <span>JSON over WebSocket</span>
                    </div>
                    <div class="info-item">
                        <strong>Health Check</strong>
                        <span>Built-in Docker health monitoring</span>
                    </div>
                    <div class="info-item">
                        <strong>Documentation</strong>
                        <span>MESSAGE_SPECIFICATIONS.md</span>
                    </div>
                    <div class="info-item">
                        <strong>Version</strong>
                        <span>v1.0</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>Goali Mock Server Monitor | Built for Frontend Development</p>
        </div>
    </div>

    <script>
        let ws = null;
        
        function updateServerStatus(online, message) {
            const statusDot = document.getElementById('serverStatus');
            const statusText = document.getElementById('serverStatusText');
            const connectionStatus = document.getElementById('connectionStatus');
            const lastCheck = document.getElementById('lastCheck');
            
            statusDot.className = `status-dot ${online ? 'online' : 'offline'}`;
            statusText.textContent = message;
            connectionStatus.textContent = online ? 'Connected' : 'Disconnected';
            lastCheck.textContent = new Date().toLocaleTimeString();
        }
        
        function showTestResult(success, message) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.className = `test-result ${success ? 'success' : 'error'}`;
            resultDiv.textContent = message;
        }
        
        async function testConnection() {
            showTestResult(false, 'Testing connection...');
            
            try {
                const wsUrl = document.getElementById('wsUrl').textContent;
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    updateServerStatus(true, 'Server Online');
                    showTestResult(true, '✓ WebSocket connection successful');
                    ws.close();
                };
                
                ws.onerror = function() {
                    updateServerStatus(false, 'Server Offline');
                    showTestResult(false, '✗ WebSocket connection failed');
                };
                
                ws.onclose = function() {
                    updateServerStatus(false, 'Server Offline');
                };
                
            } catch (error) {
                updateServerStatus(false, 'Server Offline');
                showTestResult(false, `✗ Connection error: ${error.message}`);
            }
        }
        
        async function testDiscussion() {
            showTestResult(false, 'Testing discussion workflow...');
            
            try {
                const wsUrl = document.getElementById('wsUrl').textContent;
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    const message = {
                        type: "chat_message",
                        content: {
                            message: "Hello, this is a test message",
                            user_profile_id: "test-user-monitor",
                            metadata: { trust_level: 50 }
                        }
                    };
                    ws.send(JSON.stringify(message));
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    if (data.type === 'chat_message' && !data.is_user) {
                        showTestResult(true, `✓ Discussion workflow working: "${data.content}"`);
                        ws.close();
                    }
                };
                
                ws.onerror = function() {
                    showTestResult(false, '✗ Discussion workflow test failed');
                };
                
            } catch (error) {
                showTestResult(false, `✗ Discussion test error: ${error.message}`);
            }
        }
        
        async function testWheelGeneration() {
            showTestResult(false, 'Testing wheel generation workflow...');
            
            try {
                const wsUrl = document.getElementById('wsUrl').textContent;
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    const message = {
                        type: "chat_message",
                        content: {
                            message: "Create a wheel with creative activities",
                            user_profile_id: "test-user-monitor",
                            metadata: { 
                                trust_level: 75,
                                requested_workflow: "wheel_generation"
                            }
                        }
                    };
                    ws.send(JSON.stringify(message));
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    if (data.type === 'wheel_data') {
                        const activities = data.wheel.activities.length;
                        showTestResult(true, `✓ Wheel generation working: Created wheel with ${activities} activities`);
                        ws.close();
                    }
                };
                
                ws.onerror = function() {
                    showTestResult(false, '✗ Wheel generation workflow test failed');
                };
                
            } catch (error) {
                showTestResult(false, `✗ Wheel generation test error: ${error.message}`);
            }
        }
        
        // Auto-test connection on page load
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
        
        // Auto-refresh status every 30 seconds
        setInterval(testConnection, 30000);
    </script>
</body>
</html>
