version: '3.8'

services:
  goali-mock-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: goali-mock-server
    ports:
      - "8765:8765"
    environment:
      - MOCK_SERVER_HOST=0.0.0.0
      - MOCK_SERVER_PORT=8765
      - MOCK_SERVER_LOG_LEVEL=INFO
    volumes:
      # Optional: Mount logs directory for persistent logging
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python3", "-c", "import asyncio, websockets; asyncio.run(websockets.connect('ws://localhost:8765'))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - goali-network

  # Optional: Add a simple web interface for monitoring
  goali-mock-monitor:
    image: nginx:alpine
    container_name: goali-mock-monitor
    ports:
      - "8080:80"
    volumes:
      - ./monitor:/usr/share/nginx/html:ro
    depends_on:
      - goali-mock-server
    networks:
      - goali-network
    profiles:
      - monitoring

networks:
  goali-network:
    driver: bridge
