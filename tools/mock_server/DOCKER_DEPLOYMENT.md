# Goali Mock Server - Docker Deployment Guide

This guide provides complete instructions for deploying the Goali Mock Server as a Docker container, including all necessary setup, configuration, and operational procedures.

## Quick Start

### Prerequisites
- Docker Engine 20.10+ 
- Docker Compose 2.0+ (optional, for orchestrated deployment)
- 2GB available RAM
- Port 8765 available (or configure alternative)

### One-Command Deployment

```bash
# Clone and deploy in one step
git clone <repository-url>
cd goali/tools/mock_server
docker-compose up -d
```

The server will be available at `ws://localhost:8765`

## Deployment Options

### Option 1: Docker Compose (Recommended)

**Advantages**: Easy configuration, health checks, restart policies, optional monitoring

```bash
# Start the server
docker-compose up -d

# View logs
docker-compose logs -f goali-mock-server

# Stop the server
docker-compose down

# Update and restart
docker-compose pull && docker-compose up -d
```

### Option 2: Direct Docker Build

**Advantages**: Full control, custom configurations, minimal dependencies

```bash
# Build the image
docker build -t goali-mock-server .

# Run the container
docker run -d \
  --name goali-mock-server \
  -p 8765:8765 \
  -e MOCK_SERVER_LOG_LEVEL=INFO \
  --restart unless-stopped \
  goali-mock-server

# View logs
docker logs -f goali-mock-server
```

### Option 3: Pre-built Image (Future)

```bash
# Pull and run pre-built image
docker run -d \
  --name goali-mock-server \
  -p 8765:8765 \
  goali/mock-server:latest
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `MOCK_SERVER_HOST` | `0.0.0.0` | Server bind address |
| `MOCK_SERVER_PORT` | `8765` | Server port |
| `MOCK_SERVER_LOG_LEVEL` | `INFO` | Logging level (DEBUG, INFO, WARNING, ERROR) |

### Custom Configuration

**Using docker-compose.yml**:
```yaml
environment:
  - MOCK_SERVER_HOST=0.0.0.0
  - MOCK_SERVER_PORT=8765
  - MOCK_SERVER_LOG_LEVEL=DEBUG
```

**Using Docker run**:
```bash
docker run -d \
  -e MOCK_SERVER_HOST=0.0.0.0 \
  -e MOCK_SERVER_PORT=8765 \
  -e MOCK_SERVER_LOG_LEVEL=DEBUG \
  -p 8765:8765 \
  goali-mock-server
```

### Port Configuration

**Default setup** (port 8765):
```bash
docker run -p 8765:8765 goali-mock-server
```

**Custom port** (e.g., port 9000):
```bash
docker run -p 9000:8765 \
  -e MOCK_SERVER_PORT=8765 \
  goali-mock-server
```

**Multiple instances**:
```bash
# Instance 1 on port 8765
docker run -d --name mock-server-1 -p 8765:8765 goali-mock-server

# Instance 2 on port 8766  
docker run -d --name mock-server-2 -p 8766:8765 goali-mock-server
```

## Health Monitoring

### Built-in Health Check

The container includes automatic health monitoring:

```bash
# Check health status
docker inspect --format='{{.State.Health.Status}}' goali-mock-server

# View health check logs
docker inspect --format='{{range .State.Health.Log}}{{.Output}}{{end}}' goali-mock-server
```

### Manual Health Check

```bash
# Test WebSocket connection
docker exec goali-mock-server python3 -c "
import asyncio, websockets
async def test():
    async with websockets.connect('ws://localhost:8765') as ws:
        print('✓ Server is healthy')
asyncio.run(test())
"
```

### External Monitoring

**Using curl** (if HTTP endpoint added):
```bash
curl -f http://localhost:8765/health || exit 1
```

**Using WebSocket client**:
```bash
# Install wscat: npm install -g wscat
wscat -c ws://localhost:8765 --execute "ping"
```

## Logging

### View Logs

```bash
# Real-time logs
docker logs -f goali-mock-server

# Last 100 lines
docker logs --tail 100 goali-mock-server

# Logs since specific time
docker logs --since "2025-01-27T10:00:00" goali-mock-server
```

### Log Levels

- **DEBUG**: Detailed workflow processing information
- **INFO**: Standard operational messages (default)
- **WARNING**: Non-critical issues
- **ERROR**: Error conditions requiring attention

### Persistent Logging

**Using volume mount**:
```yaml
# docker-compose.yml
volumes:
  - ./logs:/app/logs
```

**Using Docker run**:
```bash
docker run -d \
  -v $(pwd)/logs:/app/logs \
  -p 8765:8765 \
  goali-mock-server
```

## Security Considerations

### Network Security

**Internal network only**:
```yaml
# docker-compose.yml
networks:
  internal:
    internal: true
```

**Firewall configuration**:
```bash
# Allow only specific IPs
iptables -A INPUT -p tcp --dport 8765 -s ***********/24 -j ACCEPT
iptables -A INPUT -p tcp --dport 8765 -j DROP
```

### Container Security

**Non-root user**: Container runs as `mockserver` user (UID 1000)

**Read-only filesystem**:
```bash
docker run --read-only \
  --tmpfs /tmp \
  -p 8765:8765 \
  goali-mock-server
```

**Resource limits**:
```yaml
# docker-compose.yml
deploy:
  resources:
    limits:
      memory: 512M
      cpus: '0.5'
```

## Troubleshooting

### Common Issues

**1. Port already in use**:
```bash
# Check what's using the port
lsof -i :8765

# Use different port
docker run -p 8766:8765 goali-mock-server
```

**2. Container won't start**:
```bash
# Check container logs
docker logs goali-mock-server

# Check container status
docker ps -a
```

**3. WebSocket connection fails**:
```bash
# Test from inside container
docker exec -it goali-mock-server python3 test_client.py

# Check network connectivity
docker exec -it goali-mock-server netstat -tlnp
```

**4. High memory usage**:
```bash
# Check container stats
docker stats goali-mock-server

# Set memory limit
docker run --memory=512m -p 8765:8765 goali-mock-server
```

### Debug Mode

**Enable debug logging**:
```bash
docker run -d \
  -e MOCK_SERVER_LOG_LEVEL=DEBUG \
  -p 8765:8765 \
  goali-mock-server
```

**Interactive debugging**:
```bash
# Run container interactively
docker run -it --rm \
  -p 8765:8765 \
  goali-mock-server bash

# Then manually start server
python3 run.py --log-level DEBUG
```

## Production Deployment

### Recommended Production Setup

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  goali-mock-server:
    build: .
    restart: always
    ports:
      - "8765:8765"
    environment:
      - MOCK_SERVER_LOG_LEVEL=WARNING
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### Load Balancing

**Using nginx**:
```nginx
upstream mock_servers {
    server localhost:8765;
    server localhost:8766;
    server localhost:8767;
}

server {
    listen 80;
    location / {
        proxy_pass http://mock_servers;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### Monitoring Integration

**Prometheus metrics** (future enhancement):
```yaml
# Add to docker-compose.yml
- "9090:9090"  # Metrics port
```

**Log aggregation**:
```yaml
logging:
  driver: "fluentd"
  options:
    fluentd-address: "localhost:24224"
    tag: "goali.mock-server"
```

## Maintenance

### Updates

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Backup

```bash
# Backup configuration
tar -czf goali-mock-server-config.tar.gz docker-compose.yml .env

# Backup logs (if using volume)
tar -czf goali-mock-server-logs.tar.gz logs/
```

### Cleanup

```bash
# Remove stopped containers
docker container prune

# Remove unused images
docker image prune

# Complete cleanup
docker system prune -a
```

---

**Support**: For issues or questions, refer to the main README.md or create an issue in the repository.

**Version**: Docker deployment guide for Goali Mock Server v1.0
