#!/usr/bin/env python3
"""
Comprehensive setup verification for Graphiti tools
"""
import os
import sys
import subprocess
from pathlib import Path

def check_python_environment():
    """Check Python and virtual environment setup"""
    print("🐍 Checking Python Environment...")
    
    # Check if we're in the right directory
    current_dir = Path.cwd()
    if not current_dir.name == 'graphiti' or not (current_dir.parent.name == 'tools'):
        print("   ❌ Please run this script from tools/graphiti/ directory")
        return False
    
    # Check virtual environment
    venv_path = current_dir / "analysis" / "venv"
    if not venv_path.exists():
        print("   ❌ Virtual environment not found at analysis/venv/")
        print("   📝 Run: python -m venv analysis/venv")
        return False
    
    python_exe = venv_path / "bin" / "python"
    if not python_exe.exists():
        python_exe = venv_path / "Scripts" / "python.exe"  # Windows
    
    if not python_exe.exists():
        print("   ❌ Python executable not found in virtual environment")
        return False
    
    print("   ✅ Virtual environment found")
    return True

def check_configuration():
    """Check configuration file"""
    print("\n⚙️  Checking Configuration...")
    
    env_file = Path("analysis/.env")
    if not env_file.exists():
        print("   ❌ Configuration file not found: analysis/.env")
        print("   📝 Create the file with required settings")
        return False
    
    # Read and check required variables
    required_vars = ['NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD', 'OPENAI_API_KEY', 'PROJECT_ROOT']
    missing_vars = []
    
    with open(env_file, 'r') as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=" not in content:
                missing_vars.append(var)
            elif f"{var}=your_" in content:
                missing_vars.append(f"{var} (default value)")
    
    if missing_vars:
        print(f"   ❌ Missing or default values: {', '.join(missing_vars)}")
        return False
    
    print("   ✅ Configuration file looks good")
    return True

def check_file_structure():
    """Check that all required files are present"""
    print("\n📁 Checking File Structure...")
    
    required_files = [
        "analysis/code_scanner.py",
        "analysis/graphiti_analyzer.py", 
        "analysis/code_entities.py",
        "analyze_codebase.py",
        "analyze_small_sample.py",
        "visualize_graph.py",
        "quick_test.py",
        "test_setup.py",
        "README.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"   ❌ Missing files: {', '.join(missing_files)}")
        return False
    
    print("   ✅ All required files present")
    return True

def check_dependencies():
    """Check if required Python packages are installed"""
    print("\n📦 Checking Dependencies...")
    
    venv_path = Path("analysis/venv")
    python_exe = venv_path / "bin" / "python"
    if not python_exe.exists():
        python_exe = venv_path / "Scripts" / "python.exe"  # Windows
    
    required_packages = [
        'graphiti-core',
        'python-dotenv',
        'neo4j',
        'pyvis',
        'openai'
    ]
    
    missing_packages = []
    for package in required_packages:
        module_name = package.replace("-", "_")
        if package == "python-dotenv":
            module_name = "dotenv" # Correct module name for python-dotenv

        try:
            result = subprocess.run(
                [str(python_exe), '-c', f'import {module_name}'],
                capture_output=True,
                text=True
            )
            if result.returncode != 0:
                missing_packages.append(package)
        except Exception:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"   ❌ Missing packages: {', '.join(missing_packages)}")
        print("   📝 Install with: pip install <package-name>")
        return False
    
    print("   ✅ All required packages installed")
    return True

def check_docker_neo4j():
    """Check if Neo4j is running in Docker"""
    print("\n🐳 Checking Neo4j Docker Container...")
    
    try:
        result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
        if 'neo4j' in result.stdout:
            print("   ✅ Neo4j container is running")
            return True
        else:
            print("   ⚠️  Neo4j container not found")
            print("   📝 Start with: docker run -d --name neo4j-graphiti -p 7474:7474 -p 7687:7687 -e NEO4J_AUTH=neo4j/graphiti123 neo4j:5.26")
            return False
    except FileNotFoundError:
        print("   ⚠️  Docker not found or not running")
        print("   📝 Install Docker or use Neo4j Desktop")
        return False

def main():
    """Run all verification checks"""
    print("🔍 Graphiti Setup Verification")
    print("=" * 50)
    
    checks = [
        check_python_environment,
        check_configuration,
        check_file_structure,
        check_dependencies,
        check_docker_neo4j
    ]
    
    results = []
    for check in checks:
        results.append(check())
    
    print("\n" + "=" * 50)
    print("📊 Verification Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"   ✅ All {total} checks passed! Setup is ready.")
        print("\n🚀 Next steps:")
        print("   1. Run: python quick_test.py")
        print("   2. Run: python analyze_small_sample.py")
        print("   3. Open Neo4j browser: http://localhost:7474")
        return True
    else:
        print(f"   ⚠️  {passed}/{total} checks passed. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
