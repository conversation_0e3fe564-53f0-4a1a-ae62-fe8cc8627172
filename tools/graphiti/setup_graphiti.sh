#!/bin/bash

# This script sets up and initializes the elements required by Graphit<PERSON>.
# It orchestrates the steps outlined in GRAPHITI_SETUP_README.md.

echo "🚀 Starting Graphiti Setup..."

# Navigate to the tools/graphiti directory
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
cd "$SCRIPT_DIR" || { echo "Error: Could not change to script directory."; exit 1; }

echo "Current directory: $(pwd)"

# --- 1. Neo4j Database Setup (Docker) ---
check_port_in_use() {
    PORT=$1
    PROCESS_INFO=$(lsof -i :$PORT 2>/dev/null | grep LISTEN)
    if [ -n "$PROCESS_INFO" ]; then
        echo "Port $PORT is already in use by:"
        echo "$PROCESS_INFO"
        return 0 # Port is in use
    else
        return 1 # Port is not in use
    fi
}

echo -e "\n--- 1. Setting up Neo4j Database (Docker) ---"

# Check if neo4j-graphiti container is already running
if docker ps --format '{{.Names}}' | grep -q "neo4j-graphiti"; then
    echo "Neo4j container 'neo4j-graphiti' is already running. Proceeding with setup."
    # Check if Python dependencies are already installed
    VENV_PATH="./analysis/venv"
    PYTHON_EXE="$VENV_PATH/bin/python"
    if [ ! -f "$PYTHON_EXE" ]; then
        PYTHON_EXE="$VENV_PATH/Scripts/python.exe" # Windows
    fi

    if [ -f "$PYTHON_EXE" ]; then
        echo "Checking if Python dependencies are already installed..."
        if "$PYTHON_EXE" -c "import neo4j; import openai" &> /dev/null; then
            echo "Python dependencies appear to be installed. Skipping reinstallation."
            SKIP_PYTHON_INSTALL=true
        else
            echo "Python dependencies not found or incomplete. Proceeding with installation."
            SKIP_PYTHON_INSTALL=false
        fi
    else
        echo "Virtual environment not found. Proceeding with Python dependency installation."
        SKIP_PYTHON_INSTALL=false
    fi
else
    # If container is not running, check if ports are in use by other processes
    if check_port_in_use 7474 || check_port_in_use 7687; then
        echo "❌ Required Neo4j ports (7474 or 7687) are already in use by another process (not 'neo4j-graphiti')."
        echo "Please ensure no other applications are using these ports, or run './cleanup_graphiti.sh' to stop any existing Neo4j containers."
        exit 1
    fi

    # Check if neo4j-graphiti container exists but is stopped
    if docker ps -a --format '{{.Names}}' | grep -q "neo4j-graphiti"; then
        echo "Neo4j container 'neo4j-graphiti' exists but is stopped. Starting it..."
        docker start neo4j-graphiti > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            echo "❌ Failed to start existing Neo4j container. Please check Docker."
            exit 1
        fi
        echo "Neo4j container started. Waiting a few seconds for it to initialize..."
        sleep 10 # Give Neo4j some time to start up
    else
        # No existing container, create a new one
        # Remove associated volumes to ensure a fresh start if container doesn't exist
        # This handles cases where volumes might be left over from a deleted container
        if docker volume ls -q | grep -q "neo4j_data"; then
            docker volume rm neo4j_data > /dev/null 2>&1
            echo "neo4j_data volume removed (from previous incomplete setup)."
        fi
        if docker volume ls -q | grep -q "neo4j_logs"; then
            docker volume rm neo4j_logs > /dev/null 2>&1
            echo "neo4j_logs volume removed (from previous incomplete setup)."
        fi
        echo "No existing 'neo4j-graphiti' container found. Creating and starting a new one..."
        docker run -d \
          --name neo4j-graphiti \
          -p 7474:7474 -p 7687:7687 \
          -e NEO4J_AUTH=neo4j/graphiti123 \
          -e NEO4J_PLUGINS='["apoc"]' \
          -v neo4j_data:/data \
          -v neo4j_logs:/logs \
          neo4j:5.26
        if [ $? -ne 0 ]; then
            echo "❌ Failed to start Neo4j container. Please check Docker installation and permissions."
            exit 1
        fi
        echo "Neo4j container started. Waiting a few seconds for it to initialize..."
        sleep 10 # Give Neo4j some time to start up
    fi
fi

echo "Verify Neo4j is running by opening http://localhost:7474 in your browser."
echo "Login with username: neo4j, password: graphiti123"

# --- 2. Configure Environment (OpenAI API Key) ---
echo -e "\n--- 2. Checking OpenAI API Key Configuration ---"
ENV_FILE="./analysis/.env"
if [ ! -f "$ENV_FILE" ]; then
    echo "❌ .env file not found at $ENV_FILE. Please ensure it exists."
    exit 1
fi

OPENAI_API_KEY=$(grep -E "^OPENAI_API_KEY=" "$ENV_FILE" | cut -d '=' -f2-)
if [ "$OPENAI_API_KEY" == "your_openai_api_key_here" ] || [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  OPENAI_API_KEY is not set or is still the placeholder in $ENV_FILE."
    echo "Please get your API key from https://platform.openai.com/api-keys and update $ENV_FILE."
    read -p "Press Enter to continue after updating the .env file (or Ctrl+C to abort)..."
    # Re-read the key after user prompt
    OPENAI_API_KEY=$(grep -E "^OPENAI_API_KEY=" "$ENV_FILE" | cut -d '=' -f2-)
    if [ "$OPENAI_API_KEY" == "your_openai_api_key_here" ] || [ -z "$OPENAI_API_KEY" ]; then
        echo "❌ OPENAI_API_KEY still not set. Aborting setup."
        exit 1
    fi
fi
echo "✅ OpenAI API Key appears to be set."

# --- 3. Install Dependencies and Verify Setup ---
echo -e "\n--- 3. Installing Dependencies and Verifying Setup ---"
if [ "$SKIP_PYTHON_INSTALL" != "true" ]; then
    echo "Running python3 install_dependencies.py..."
    python3 install_dependencies.py
    if [ $? -ne 0 ]; then
        echo "❌ install_dependencies.py failed. Aborting setup."
        exit 1
    fi
else
    echo "Skipping python3 install_dependencies.py as dependencies are already installed."
fi

# Determine Python executable path within the virtual environment
VENV_PATH="./analysis/venv"
PYTHON_EXE="$VENV_PATH/bin/python"
if [ ! -f "$PYTHON_EXE" ]; then
    PYTHON_EXE="$VENV_PATH/Scripts/python.exe" # Windows
fi

if [ ! -f "$PYTHON_EXE" ]; then
    echo "❌ Python executable not found in virtual environment at $VENV_PATH. Aborting setup."
    exit 1
fi

echo "Using virtual environment Python: $PYTHON_EXE"

echo "Running $PYTHON_EXE setup_verification.py..."
"$PYTHON_EXE" setup_verification.py
if [ $? -ne 0 ]; then
    echo "❌ setup_verification.py failed. Aborting setup."
    exit 1
fi

echo "Running $PYTHON_EXE quick_test.py..."
"$PYTHON_EXE" quick_test.py
if [ $? -ne 0 ]; then
    echo "❌ quick_test.py failed. Aborting setup."
    echo "Possible Neo4j authentication issue. This often happens if old Neo4j data volumes persist with different credentials."
    echo "Please ensure:"
    echo "  1. Your NEO4J_PASSWORD in analysis/.env is 'graphiti123'."
    echo "  2. If you've run Neo4j before, its data volume might have conflicting credentials."
    echo "     To ensure a completely fresh start, run './cleanup_graphiti.sh' and **explicitly choose 'y' to remove Docker volumes** when prompted (this will delete all graph data)."
    echo "     Alternatively, you can manually reset the 'neo4j' user's password to 'graphiti123' in the Neo4j browser \(http://localhost:7474\)."
    exit 1
fi
echo "✅ Dependencies installed and setup verified."

# --- 4. Analyze Sample Files ---
echo -e "\n--- 4. Analyzing Sample Files ---"
echo "Running $PYTHON_EXE analyze_small_sample.py..."
"$PYTHON_EXE" analyze_small_sample.py
if [ $? -ne 0 ]; then
    echo "❌ analyze_small_sample.py failed. Continuing, but check logs."
fi
echo "✅ Sample analysis initiated."

# --- 5. Create Visualizations ---
echo -e "\n--- 5. Creating Visualizations ---"
echo "Running $PYTHON_EXE visualize_graph.py..."
"$PYTHON_EXE" visualize_graph.py
if [ $? -ne 0 ]; then
    echo "❌ visualize_graph.py failed. Continuing, but check logs."
fi
echo "✅ Visualization creation initiated."

echo -e "\n--- Graphiti Setup Complete! ---"
echo "Graphiti is now set up and ready for codebase analysis."
echo "You can start by:"
echo "1. Running a full codebase analysis:"
echo "   cd tools/graphiti && python analyze_codebase.py"
echo "2. Exploring the interactive visualization:"
echo "   cd tools/graphiti && python visualize_graph.py (then open codebase_graph.html in your browser)"
echo "3. Viewing results in Neo4j Browser: http://localhost:7474 (Login: neo4j/graphiti123)"
echo "   - Try a basic query: MATCH (n) RETURN n LIMIT 25"
echo "For more detailed usage examples, advanced queries, and customization options, please refer to:"
echo "tools/graphiti/GRAPHITI_USAGE_GUIDE.md"
