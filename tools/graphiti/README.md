# Graphiti Codebase Analysis Tools

This directory contains tools for analyzing your codebase using Graphiti, a temporal knowledge graph framework.

## 📁 Directory Structure

```
tools/graphiti/
├── analysis/                    # Analysis environment
│   ├── venv/                   # Python virtual environment
│   ├── .env                    # Configuration file
│   ├── code_scanner.py         # Code structure extraction
│   ├── graphiti_analyzer.py    # Graphiti integration
│   └── code_entities.py        # Custom entity definitions
├── analyze_codebase.py         # Full codebase analysis
├── analyze_small_sample.py     # Sample analysis script
├── visualize_graph.py          # Interactive visualizations
├── quick_test.py               # Setup verification
├── test_setup.py               # Complete setup testing
└── README.md                   # This file
```

## 🚀 Quick Start

1. **Install dependencies**: `python install_dependencies.py`
2. **Setup Neo4j Database** (see main setup guide)
3. **Configure environment**: Edit `analysis/.env`
4. **Verify setup**: `python setup_verification.py`
5. **Test setup**: `python quick_test.py`
6. **Run analysis**: `python analyze_small_sample.py`
7. **View results**: Open Neo4j browser at http://localhost:7474

## 📋 Scripts Overview

### Core Analysis Scripts

- **`analyze_codebase.py`**: Full codebase analysis with configurable file limits
- **`analyze_small_sample.py`**: Quick analysis of key files for testing
- **`visualize_graph.py`**: Creates interactive HTML visualizations

### Setup and Testing

- **`install_dependencies.py`**: Installs required Python packages
- **`setup_verification.py`**: Comprehensive setup verification
- **`quick_test.py`**: Verifies Neo4j connection and API keys
- **`test_setup.py`**: Complete setup validation

### Analysis Modules (in `analysis/`)

- **`code_scanner.py`**: Extracts structure from Python files, docs, and configs
- **`graphiti_analyzer.py`**: Converts code structure to Graphiti episodes
- **`code_entities.py`**: Defines custom entity types for code elements

## 🔧 Configuration

Edit `analysis/.env`:

```env
# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=graphiti123

# OpenAI Configuration
OPENAI_API_KEY=your_actual_api_key_here

# Analysis Configuration
ANALYSIS_GROUP_ID=goali_codebase_v1
PROJECT_ROOT=/Users/<USER>/dev/goali
```

## 🎯 Usage Examples

### Basic Analysis
```bash
# Test the setup
python quick_test.py

# Analyze sample files
python analyze_small_sample.py

# Create visualization
python visualize_graph.py
```

### Custom Analysis
```python
# In your custom script
from analysis.code_scanner import CodebaseScanner
from analysis.graphiti_analyzer import CodebaseAnalyzer

# Initialize and analyze specific files
scanner = CodebaseScanner(project_root)
analyzer = CodebaseAnalyzer(graphiti, scanner)
await analyzer.analyze_specific_files(['path/to/file.py'])
```

## 📊 What Gets Analyzed

- **Python files**: Classes, functions, imports, docstrings
- **Documentation**: README files, markdown docs
- **Configuration**: JSON, YAML files
- **Relationships**: Import dependencies, code structure

## 🔍 Querying Results

Use Neo4j Browser (http://localhost:7474) to explore:

```cypher
// View all analyzed files
MATCH (e:Episode) RETURN e.name, e.created_at LIMIT 10

// Find Python classes
MATCH (n:Entity) WHERE n.entity_type = "CodeClass"
RETURN n.name, n.module

// Analyze dependencies
MATCH (a:Entity)-[r:IMPORTS]->(b:Entity)
RETURN a.name, b.name, r.import_type
```

## 🆘 Troubleshooting

- **Import errors**: Ensure virtual environment is activated
- **Neo4j connection**: Check Docker container or Neo4j Desktop
- **API key issues**: Verify OpenAI key in `analysis/.env`
- **Path issues**: Ensure PROJECT_ROOT points to repository root

## 📚 Documentation

- Main setup guide: `../../GRAPHITI_SETUP_README.md`
- Usage guide: `../../GRAPHITI_USAGE_GUIDE.md`
- Detailed docs: `../../graphiti/docs/`

## 🔄 Development

To extend the analysis:

1. Add new entity types in `analysis/code_entities.py`
2. Extend scanner in `analysis/code_scanner.py`
3. Update analyzer in `analysis/graphiti_analyzer.py`
4. Create custom visualization scripts
