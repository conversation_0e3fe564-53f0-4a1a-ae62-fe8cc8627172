from pydantic import BaseModel, Field

class CodeModule(BaseModel):
    """Represents a Python module or package"""
    name: str = Field(..., description="Module name")
    path: str = Field(..., description="Relative path in project")
    type: str = Field(..., description="package or module")

class CodeFunction(BaseModel):
    """Represents a function or method"""
    name: str = Field(..., description="Function name")
    module: str = Field(..., description="Parent module")
    parameters: str = Field(..., description="Parameter signature")
    returns: str = Field(default="", description="Return type annotation")
    
class CodeClass(BaseModel):
    """Represents a class definition"""
    name: str = Field(..., description="Class name")
    module: str = Field(..., description="Parent module")
    base_classes: str = Field(default="", description="Base classes")
    
class WorkflowNode(BaseModel):
    """Represents a LangGraph workflow node"""
    name: str = Field(..., description="Node identifier")
    workflow: str = Field(..., description="Parent workflow")
    node_type: str = Field(..., description="Type of node")
    
class Dependency(BaseModel):
    """Represents an import or dependency"""
    source: str = Field(..., description="Importing module")
    target: str = Field(..., description="Imported module")
    import_type: str = Field(..., description="Type of import")

# Register custom entities
CODE_ENTITY_TYPES = {
    'CodeModule': CodeModule,
    'CodeFunction': CodeFunction,
    'CodeClass': CodeClass,
    'WorkflowNode': WorkflowNode,
    'Dependency': Dependency
}
