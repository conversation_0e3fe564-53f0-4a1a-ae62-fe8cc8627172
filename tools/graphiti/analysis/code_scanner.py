import os
import ast
import json
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, List, Any

class CodebaseScanner:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.file_registry = {}
        
    def scan_python_file(self, filepath: Path) -> Dict[str, Any]:
        """Extract structure from Python file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(filepath, 'r', encoding='latin-1') as f:
                    content = f.read()
            except:
                return None
                
        try:
            tree = ast.parse(content)
            return {
                'type': 'python',
                'path': str(filepath.relative_to(self.project_root)),
                'content': content,
                'imports': self._extract_imports(tree),
                'classes': self._extract_classes(tree),
                'functions': self._extract_functions(tree),
                'docstring': ast.get_docstring(tree)
            }
        except SyntaxError:
            return None
    
    def _extract_imports(self, tree) -> List[Dict[str, str]]:
        """Extract import statements"""
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append({
                        'type': 'import',
                        'module': alias.name,
                        'alias': alias.asname
                    })
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                for alias in node.names:
                    imports.append({
                        'type': 'from_import',
                        'module': module,
                        'name': alias.name,
                        'alias': alias.asname
                    })
        return imports
    
    def _extract_classes(self, tree) -> List[Dict[str, Any]]:
        """Extract class definitions"""
        classes = []
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append({
                    'name': node.name,
                    'bases': [self._get_name(base) for base in node.bases],
                    'methods': [n.name for n in node.body if isinstance(n, ast.FunctionDef)],
                    'docstring': ast.get_docstring(node),
                    'line_number': node.lineno
                })
        return classes
    
    def _extract_functions(self, tree) -> List[Dict[str, Any]]:
        """Extract top-level function definitions (excluding methods)"""
        functions = []
        for node in tree.body: # Iterate only through top-level statements
            if isinstance(node, ast.FunctionDef):
                functions.append({
                    'name': node.name,
                    'args': [arg.arg for arg in node.args.args],
                    'docstring': ast.get_docstring(node),
                    'line_number': node.lineno
                })
        return functions
    
    def _get_name(self, node):
        """Get name from AST node"""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_name(node.value)}.{node.attr}"
        else:
            return str(node)
            
    def scan_langgraph_workflow(self, filepath: Path) -> Dict[str, Any]:
        """Extract LangGraph workflow structure"""
        # This is a placeholder for specialized LangGraph parsing
        # Look for StateGraph, nodes, edges patterns
        python_data = self.scan_python_file(filepath)
        if python_data:
            python_data['type'] = 'langgraph_workflow'
        return python_data
        
    def scan_documentation(self, filepath: Path) -> Dict[str, Any]:
        """Extract documentation structure"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(filepath, 'r', encoding='latin-1') as f:
                    content = f.read()
            except:
                return None
                
        return {
            'type': 'documentation',
            'path': str(filepath.relative_to(self.project_root)),
            'content': content,
            'format': filepath.suffix
        }
