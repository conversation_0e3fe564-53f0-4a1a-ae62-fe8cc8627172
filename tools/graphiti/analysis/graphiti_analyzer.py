from graphiti_core import <PERSON><PERSON><PERSON><PERSON>
from graphiti_core.nodes import EpisodeType
import async<PERSON>
import os
import json
from pathlib import Path
from datetime import datetime, timezone
from typing import List
from code_scanner import CodebaseScanner

class CodebaseAnalyzer:
    def __init__(self, graphiti_client: <PERSON><PERSON><PERSON><PERSON>, scanner: CodebaseScanner):
        self.graphiti = graphiti_client
        self.scanner = scanner
        self.group_id = os.getenv('ANALYSIS_GROUP_ID', 'codebase_default')

    async def analyze_file(self, filepath: Path):
        """Convert a file into a Graphiti episode"""
        file_data = None

        if filepath.suffix == '.py':
            file_data = self.scanner.scan_python_file(filepath)
        elif filepath.suffix in ['.md', '.rst', '.txt']:
            file_data = self.scanner.scan_documentation(filepath)
        elif filepath.suffix in ['.json', '.yml', '.yaml']:
            file_data = self.scanner.scan_documentation(filepath)
            if file_data:
                file_data['type'] = 'configuration'

        if file_data:
            # Create structured episode for the file
            episode_body = json.dumps(file_data, indent=2)

            try:
                await self.graphiti.add_episode(
                    name=f"Code: {filepath.name}",
                    episode_body=episode_body,
                    source=EpisodeType.json,
                    source_description=f"Codebase file: {file_data['type']}",
                    group_id=self.group_id,
                    reference_time=datetime.now(timezone.utc)
                )
                print(f"✓ Analyzed: {filepath}")
            except Exception as e:
                print(f"✗ Error analyzing {filepath}: {e}")

    async def analyze_directory(self, directory: Path, patterns=['*.py', '*.md'], max_files=50):
        """Recursively analyze a directory"""
        file_count = 0
        for pattern in patterns:
            for filepath in directory.rglob(pattern):
                # Skip hidden files and directories
                if any(part.startswith('.') for part in filepath.parts):
                    continue

                # Skip common non-source directories
                skip_dirs = {'__pycache__', 'node_modules', '.git', 'venv', 'env', 'build', 'dist'}
                if any(skip_dir in filepath.parts for skip_dir in skip_dirs):
                    continue

                print(f"Analyzing: {filepath}")
                await self.analyze_file(filepath)
                file_count += 1

                # Limit files for initial testing
                if file_count >= max_files:
                    print(f"Reached limit of {max_files} files for initial analysis")
                    break

    async def analyze_specific_files(self, file_paths: List[str]):
        """Analyze specific files by path"""
        for file_path in file_paths:
            filepath = Path(file_path)
            if filepath.exists():
                await self.analyze_file(filepath)
            else:
                print(f"File not found: {file_path}")

    async def get_analysis_summary(self):
        """Get summary of analyzed content"""
        try:
            # Search for episodes in this group
            results = await self.graphiti.search(
                query="codebase analysis files modules classes functions",
                group_ids=[self.group_id],
                num_results=20
            )

            print(f"\nAnalysis Summary for group '{self.group_id}':")
            print(f"Found {len(results)} relevant items")

            for i, result in enumerate(results[:10]):
                print(f"{i+1}. {result.fact}")

        except Exception as e:
            print(f"Error getting summary: {e}")
