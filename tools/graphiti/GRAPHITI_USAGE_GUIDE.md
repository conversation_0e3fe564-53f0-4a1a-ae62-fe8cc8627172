# Graphiti Codebase Analysis Usage Guide

This guide covers how to use the Graphiti tools for analyzing your codebase after completing the setup.

## 🎯 Quick Analysis Workflows

### 1. Full Codebase Analysis

```bash
cd tools/graphiti
python analyze_codebase.py
```

This will analyze your entire codebase and create a knowledge graph with:
- Python modules, classes, and functions
- Documentation files
- Configuration files
- Import relationships
- Code dependencies

### 2. Targeted Analysis

```bash
cd tools/graphiti
python analyze_small_sample.py
```

Analyzes a smaller subset of files for testing or focused exploration.

### 3. Interactive Exploration

```bash
cd tools/graphiti
python visualize_graph.py
```

Creates an interactive HTML visualization that you can explore in your browser.

**Note**: All scripts should be run from the `tools/graphiti/` directory to ensure proper path resolution.

## 🔍 Neo4j Query Examples

Open Neo4j Browser at http://localhost:7474 and try these queries:

### Basic Exploration

```cypher
// Count all nodes by type
MATCH (n)
RETURN labels(n) as NodeType, count(n) as Count
ORDER BY Count DESC

// View recent episodes (analyzed files)
MATCH (e:Episode)
RETURN e.name, e.created_at, e.source_description
ORDER BY e.created_at DESC
LIMIT 10
```

### Code Structure Analysis

```cypher
// Find all Python classes
MATCH (e:Entity)
WHERE e.entity_type = "CodeClass"
RETURN e.name, e.module
ORDER BY e.name

// Find functions with most parameters
MATCH (e:Entity)
WHERE e.entity_type = "CodeFunction"
RETURN e.name, e.module, e.parameters
ORDER BY length(e.parameters) DESC
LIMIT 10
```

### Dependency Analysis

```cypher
// Find import relationships
MATCH (source:Entity)-[r:IMPORTS]->(target:Entity)
RETURN source.name, target.name, r.import_type
LIMIT 20

// Find modules with most dependencies
MATCH (m:Entity)-[r:IMPORTS]->()
WHERE m.entity_type = "CodeModule"
RETURN m.name, count(r) as dependency_count
ORDER BY dependency_count DESC
LIMIT 10
```

## 📊 Analysis Patterns

### 1. Code Quality Assessment

```cypher
// Find classes without docstrings
MATCH (e:Entity)
WHERE e.entity_type = "CodeClass" AND (e.docstring IS NULL OR e.docstring = "")
RETURN e.name, e.module

// Find large functions (potential refactoring candidates)
MATCH (e:Entity)
WHERE e.entity_type = "CodeFunction"
AND e.line_count > 50
RETURN e.name, e.module, e.line_count
ORDER BY e.line_count DESC
```

### 2. Architecture Analysis

```cypher
// Find circular dependencies
MATCH (a:Entity)-[:IMPORTS]->(b:Entity)-[:IMPORTS]->(a)
RETURN a.name, b.name

// Find central modules (high connectivity)
MATCH (m:Entity)
WHERE m.entity_type = "CodeModule"
OPTIONAL MATCH (m)-[r1:IMPORTS]->()
OPTIONAL MATCH ()-[r2:IMPORTS]->(m)
RETURN m.name, count(r1) as outgoing, count(r2) as incoming,
       count(r1) + count(r2) as total_connections
ORDER BY total_connections DESC
LIMIT 10
```

## 🎨 Visualization Techniques

### 1. Module Dependency Graph

The `visualize_graph.py` script creates interactive visualizations. Customize it by editing:

```python
# Focus on specific modules
modules_of_interest = ['backend.apps.main', 'backend.apps.user']

# Adjust visualization parameters
vis.set_options("""
{
  "physics": {
    "enabled": true,
    "stabilization": {"iterations": 100}
  },
  "layout": {
    "hierarchical": {
      "enabled": true,
      "direction": "UD"
    }
  }
}
""")
```

### 2. Custom Visualizations

Create focused visualizations for specific aspects:

```python
# In your custom script
from pyvis.network import Network

# Create network for specific module
net = Network(height="600px", width="100%", bgcolor="#222222", font_color="white")

# Query for specific relationships
query = """
MATCH (source:Entity)-[r:IMPORTS]->(target:Entity)
WHERE source.module CONTAINS 'backend.apps.main'
RETURN source.name, target.name, r.import_type
"""

# Add nodes and edges based on query results
# ... visualization code ...
```

## 🔄 Continuous Analysis

### 1. File Watcher Setup

Set up automatic analysis when files change:

```python
# watch_codebase.py
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import asyncio

class CodeChangeHandler(FileSystemEventHandler):
    def on_modified(self, event):
        if event.src_path.endswith('.py'):
            # Re-analyze the changed file
            asyncio.run(analyzer.analyze_file(Path(event.src_path)))

observer = Observer()
observer.schedule(CodeChangeHandler(), path='backend/', recursive=True)
observer.start()
```

### 2. Incremental Updates

Update specific files without full re-analysis:

```python
# Update specific files
files_to_update = [
    'backend/apps/main/models.py',
    'backend/apps/user/views.py'
]

await analyzer.analyze_specific_files(files_to_update)
```

## 🎯 Advanced Use Cases

### 1. LangGraph Workflow Analysis

For analyzing LangGraph workflows specifically:

```python
# In tools/graphiti/analyze_workflows.py
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "analysis"))

from code_scanner import CodebaseScanner

workflow_files = project_root.rglob('*workflow*.py')
for filepath in workflow_files:
    workflow_data = scanner.scan_langgraph_workflow(filepath)
    # Custom analysis for workflow patterns
```

### 2. Documentation Coverage

```cypher
// Find undocumented code
MATCH (e:Entity)
WHERE e.entity_type IN ["CodeClass", "CodeFunction"]
AND (e.docstring IS NULL OR e.docstring = "")
RETURN e.entity_type, count(e) as undocumented_count

// Documentation coverage by module
MATCH (e:Entity)
WHERE e.entity_type IN ["CodeClass", "CodeFunction"]
WITH e.module as module,
     count(e) as total,
     count(CASE WHEN e.docstring IS NOT NULL AND e.docstring <> "" THEN 1 END) as documented
RETURN module, documented, total,
       round(100.0 * documented / total, 2) as coverage_percent
ORDER BY coverage_percent ASC
```

### 3. Code Evolution Tracking

```python
# Track changes over time by using different group_ids
analyzer.group_id = f"codebase_{datetime.now().strftime('%Y%m%d')}"
await analyzer.analyze_directory(project_root)

# Compare different versions
query = """
MATCH (old:Episode {group_id: 'codebase_20240101'})
MATCH (new:Episode {group_id: 'codebase_20240201'})
WHERE old.name = new.name
RETURN old.name, old.episode_body <> new.episode_body as changed
"""
```

## 🛠️ Customization

### 1. Custom Entity Types

Add new entity types in `tools/graphiti/analysis/code_entities.py`:

```python
class TestCase(BaseModel):
    """Represents a test case"""
    name: str = Field(..., description="Test case name")
    test_file: str = Field(..., description="Test file path")
    test_type: str = Field(..., description="Unit, integration, etc.")

# Register the new type
CODE_ENTITY_TYPES['TestCase'] = TestCase
```

### 2. Custom Scanners

Extend the scanner in `tools/graphiti/analysis/code_scanner.py`:

```python
def scan_test_file(self, filepath: Path) -> Dict[str, Any]:
    """Extract test cases from test files"""
    # Custom parsing logic for test files
    pass
```

## 📈 Performance Tips

1. **Batch Processing**: Analyze files in batches for large codebases
2. **Selective Analysis**: Use file patterns to focus on specific areas
3. **Index Optimization**: Ensure Neo4j indices are built for better query performance
4. **Memory Management**: Monitor Neo4j memory usage for large datasets

## 🔗 Integration with Development Workflow

1. **Pre-commit Hooks**: Run analysis on changed files before commits
2. **CI/CD Integration**: Include codebase analysis in your pipeline
3. **Code Review**: Use dependency graphs to understand change impact
4. **Refactoring**: Identify refactoring opportunities through graph analysis

For more detailed examples and advanced techniques, see the documentation in `graphiti/docs/`.
