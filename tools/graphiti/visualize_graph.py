from pyvis.network import Network
import asyncio
import sys
from pathlib import Path
from graphiti_core import Graphiti
from neo4j import GraphDatabase
import os
from dotenv import load_dotenv

# Add the analysis directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "analysis"))

load_dotenv(Path(__file__).parent / "analysis" / ".env")

class GraphVisualizer:
    def __init__(self, neo4j_driver):
        self.driver = neo4j_driver

    def create_simple_graph(self, output_file='codebase_graph.html'):
        """Create a simple visualization of the codebase graph"""
        net = Network(height='750px', width='100%', directed=True)

        # Configure physics for better layout
        net.barnes_hut(
            gravity=-80000,
            central_gravity=0.3,
            spring_length=250,
            spring_strength=0.001
        )

        with self.driver.session() as session:
            # Get all nodes and relationships
            result = session.run("""
                MATCH (n)
                OPTIONAL MATCH (n)-[r]->(m)
                RETURN n, r, m
                LIMIT 100
            """)

            nodes_added = set()

            for record in result:
                node = record['n']

                # Add source node
                node_id = str(node.element_id)
                if node_id not in nodes_added:
                    # Get node labels and properties
                    labels = list(node.labels)
                    label_text = labels[0] if labels else 'Node'

                    # Get name property if available
                    name = node.get('name', node.get('uuid', f'Node_{node.element_id}'))

                    # Color based on node type
                    color = self._get_node_color(labels)

                    net.add_node(
                        node_id,
                        label=name,
                        title=f"Type: {label_text}\nID: {node.element_id}",
                        color=color,
                        size=25
                    )
                    nodes_added.add(node_id)

                # Add relationship if exists
                if record['r'] and record['m']:
                    target = record['m']
                    target_id = str(target.element_id)

                    if target_id not in nodes_added:
                        target_labels = list(target.labels)
                        target_label_text = target_labels[0] if target_labels else 'Node'
                        target_name = target.get('name', target.get('uuid', f'Node_{target.element_id}'))
                        target_color = self._get_node_color(target_labels)

                        net.add_node(
                            target_id,
                            label=target_name,
                            title=f"Type: {target_label_text}\nID: {target.element_id}",
                            color=target_color,
                            size=20
                        )
                        nodes_added.add(target_id)

                    # Add edge
                    rel = record['r']
                    net.add_edge(
                        node_id,
                        target_id,
                        title=rel.type,
                        color='#666'
                    )

        # Save visualization
        net.show(output_file)
        print(f"📊 Visualization saved to {output_file}")
        return output_file

    def _get_node_color(self, labels):
        """Get color based on node labels"""
        if 'Episode' in labels:
            return '#4CAF50'  # Green for episodes
        elif 'Entity' in labels:
            return '#2196F3'  # Blue for entities
        elif 'Edge' in labels:
            return '#FF9800'  # Orange for edges
        else:
            return '#9E9E9E'  # Gray for others

async def visualize_codebase():
    """Create visualizations of the analyzed codebase"""
    print("🎨 Creating codebase visualizations...")

    try:
        driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'),
            auth=(os.getenv('NEO4J_USER'), os.getenv('NEO4J_PASSWORD'))
        )

        visualizer = GraphVisualizer(driver)

        # Create simple graph visualization
        output_file = visualizer.create_simple_graph()

        print(f"✅ Visualization complete!")
        print(f"📂 Open {output_file} in your browser to view the graph")

        driver.close()

    except Exception as e:
        print(f"❌ Error creating visualization: {e}")
        import traceback
        traceback.print_exc() # Print full traceback for debugging

if __name__ == "__main__":
    asyncio.run(visualize_codebase())
