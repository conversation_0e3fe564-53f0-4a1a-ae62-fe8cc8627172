#!/bin/bash

# This script cleans up the elements created by Graphiti setup.
# It stops and removes the Neo4j Docker container and offers to remove data.

echo "🧹 Starting Graphiti Cleanup..."

# Navigate to the tools/graphiti directory
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
cd "$SCRIPT_DIR" || { echo "Error: Could not change to script directory."; exit 1; }

echo "Current directory: $(pwd)"

# --- 1. Stop and Remove Neo4j Docker Container ---
echo -e "\n--- 1. Stopping and Removing Neo4j Docker Container ---"
if docker ps -a --format '{{.Names}}' | grep -q "neo4j-graphiti"; then
    echo "Stopping Neo4j container 'neo4j-graphiti'..."
    docker stop neo4j-graphiti
    if [ $? -ne 0 ]; then
        echo "⚠️  Failed to stop Neo4j container. It might not be running."
    fi
    echo "Removing Neo4j container 'neo4j-graphiti'..."
    docker rm neo4j-graphiti
    if [ $? -ne 0 ]; then
        echo "❌ Failed to remove Neo4j container. Please check Docker."
    else
        echo "✅ Neo4j container removed."
    fi
else
    echo "Neo4j container 'neo4j-graphiti' not found. Nothing to stop or remove."
fi

# --- 2. Remove Docker Volumes ---
echo -e "\n--- 2. Removing Neo4j Docker Volumes (Forceful) ---"
echo "Forcefully removing neo4j_data and neo4j_logs volumes to ensure a clean start."
echo "⚠️ WARNING: This will delete ALL Graphiti data stored in Neo4j."

docker volume rm neo4j_data > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "⚠️  Failed to remove neo4j_data volume. It might not exist or be in use, or permissions issue."
else
    echo "✅ neo4j_data volume removed."
fi

docker volume rm neo4j_logs > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "⚠️  Failed to remove neo4j_logs volume. It might not exist or be in use, or permissions issue."
else
    echo "✅ neo4j_logs volume removed."
fi

echo -e "\n--- 2.1. Pruning all unused Docker volumes (Optional, but recommended for deep cleanup) ---"
read -p "Do you want to remove all unused Docker volumes? This is a more aggressive cleanup. (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Running 'docker system prune --volumes -f'..."
    docker system prune --volumes -f
    if [ $? -ne 0 ]; then
        echo "❌ Failed to prune Docker volumes."
    else
        echo "✅ Unused Docker volumes pruned."
    fi
else
    echo "Skipping pruning of all unused Docker volumes."
fi

# --- 3. Remove Python Virtual Environment ---
echo -e "\n--- 3. Removing Python Virtual Environment (Optional) ---"
VENV_PATH="./analysis/venv"
if [ -d "$VENV_PATH" ]; then
    read -p "Do you want to remove the Python virtual environment at $VENV_PATH? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Removing virtual environment..."
        rm -rf "$VENV_PATH"
        if [ $? -ne 0 ]; then
            echo "❌ Failed to remove virtual environment."
        else
            echo "✅ Virtual environment removed."
        fi
    else
        echo "Skipping virtual environment removal."
    fi
else
    echo "Virtual environment not found at $VENV_PATH. Nothing to remove."
fi

# --- 4. Remove Generated Visualization Files ---
echo -e "\n--- 4. Removing Generated Visualization Files (Optional) ---"
VIS_FILE="codebase_graph.html"
if [ -f "$VIS_FILE" ]; then
    read -p "Do you want to remove the generated visualization file ($VIS_FILE)? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Removing $VIS_FILE..."
        rm "$VIS_FILE"
        if [ $? -ne 0 ]; then
            echo "❌ Failed to remove $VIS_FILE."
        else
            echo "✅ $VIS_FILE removed."
        fi
    else
        echo "Skipping visualization file removal."
    fi
else
    echo "Visualization file ($VIS_FILE) not found. Nothing to remove."
fi

echo -e "\n--- Graphiti Cleanup Complete! ---"
echo "You can now re-run ./setup_graphiti.sh to set up Graphiti again."
