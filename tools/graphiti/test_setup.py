import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from graphiti_core import Graphiti
from neo4j import GraphDatabase

# Add the analysis directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "analysis"))

load_dotenv(Path(__file__).parent / "analysis" / ".env")

async def test_setup():
    """Test the Graphiti setup"""
    print("🧪 Testing Graphiti setup...")

    # Test 1: Check environment variables
    print("\n1. Checking environment variables...")
    required_vars = ['NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD', 'OPENAI_API_KEY', 'PROJECT_ROOT']

    for var in required_vars:
        value = os.getenv(var)
        if value and value != 'your_openai_api_key_here':
            print(f"   ✓ {var}: Set")
        else:
            print(f"   ❌ {var}: Not set or default value")

    # Test 2: Neo4j connection
    print("\n2. Testing Neo4j connection...")
    try:
        driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'),
            auth=(os.getenv('NEO4J_USER'), os.getenv('NEO4J_PASSWORD'))
        )

        with driver.session() as session:
            result = session.run("RETURN 'Hello Neo4j!' as message")
            record = result.single()
            print(f"   ✓ Neo4j connection successful: {record['message']}")

        driver.close()

    except Exception as e:
        print(f"   ❌ Neo4j connection failed: {e}")
        return False

    # Test 3: Graphiti initialization
    print("\n3. Testing Graphiti initialization...")
    try:
        graphiti = Graphiti(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'),
            password=os.getenv('NEO4J_PASSWORD')
        )
        print("   ✓ Graphiti client created successfully")

        # Test building indices
        await graphiti.build_indices_and_constraints()
        print("   ✓ Indices and constraints built")

        await graphiti.close()

    except Exception as e:
        print(f"   ❌ Graphiti initialization failed: {e}")
        return False

    # Test 4: Project root exists
    print("\n4. Checking project root...")
    project_root = os.getenv('PROJECT_ROOT')
    if project_root and os.path.exists(project_root):
        print(f"   ✓ Project root exists: {project_root}")
    else:
        print(f"   ❌ Project root not found: {project_root}")
        return False

    print("\n✅ All tests passed! Setup is ready.")
    return True

if __name__ == "__main__":
    asyncio.run(test_setup())
