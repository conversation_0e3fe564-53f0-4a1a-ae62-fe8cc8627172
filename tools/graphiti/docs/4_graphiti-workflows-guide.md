# Practical Workflows and Best Practices

## Part 1: Daily Development Workflows

### 1.1 Morning Codebase Review Workflow

Create `daily_workflows.py`:

```python
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List

class DailyCodebaseReview:
    def __init__(self, graphiti_client, visualizer):
        self.graphiti = graphiti_client
        self.visualizer = visualizer
        
    async def morning_review(self) -> Dict:
        """Generate morning codebase health report"""
        report = {
            'date': datetime.now().isoformat(),
            'changes_since_yesterday': [],
            'complexity_hotspots': [],
            'broken_patterns': [],
            'new_dependencies': []
        }
        
        # Find recent changes
        yesterday = datetime.now() - timedelta(days=1)
        recent_episodes = await self.graphiti.retrieve_episodes(
            group_ids=[os.getenv('ANALYSIS_GROUP_ID')],
            last_n=100,
            reference_time=datetime.now(timezone.utc)
        )
        
        # Filter for yesterday's changes
        report['changes_since_yesterday'] = [
            ep for ep in recent_episodes 
            if ep.created_at > yesterday
        ]
        
        # Identify complexity hotspots
        complexity_query = """
        MATCH (m:CodeModule)-[r]-(connected)
        WITH m, COUNT(DISTINCT connected) as connections
        WHERE connections > 10
        RETURN m.name as module, connections
        ORDER BY connections DESC
        LIMIT 10
        """
        
        # Check for broken patterns
        pattern_violations = await self.check_pattern_violations()
        report['broken_patterns'] = pattern_violations
        
        # Visualize changes
        self.visualizer.create_change_impact_graph(
            report['changes_since_yesterday'],
            'morning_review.html'
        )
        
        return report
    
    async def check_pattern_violations(self) -> List[Dict]:
        """Check for violations of established patterns"""
        violations = []
        
        # Example: Check for circular dependencies
        circular_deps = await self.graphiti.search(
            query="circular dependency import cycle",
            num_results=20
        )
        
        for dep in circular_deps:
            violations.append({
                'type': 'circular_dependency',
                'source': dep.source_node_name,
                'target': dep.target_node_name,
                'severity': 'high'
            })
        
        return violations
```

### 1.2 Feature Development Workflow

Create `feature_workflow.py`:

```python
class FeatureDevelopmentWorkflow:
    def __init__(self, graphiti_client, analyzer):
        self.graphiti = graphiti_client
        self.analyzer = analyzer
        self.feature_context = {}
        
    async def start_new_feature(self, feature_name: str, related_modules: List[str]):
        """Initialize context for new feature development"""
        self.feature_context = {
            'name': feature_name,
            'start_time': datetime.now(),
            'related_modules': related_modules,
            'impact_analysis': {},
            'dependencies': []
        }
        
        # Analyze impact area
        for module in related_modules:
            impact = await self.analyze_module_impact(module)
            self.feature_context['impact_analysis'][module] = impact
        
        # Create feature tracking node
        await self.graphiti.add_episode(
            name=f"Feature: {feature_name}",
            episode_body=json.dumps({
                'type': 'feature_start',
                'name': feature_name,
                'modules': related_modules,
                'impact_analysis': self.feature_context['impact_analysis']
            }),
            source=EpisodeType.json,
            source_description="Feature development tracking",
            group_id=f"feature_{feature_name}"
        )
        
        # Generate feature workspace
        await self.create_feature_workspace()
        
    async def analyze_module_impact(self, module_name: str) -> Dict:
        """Analyze impact of changes to a module"""
        # Find all dependent modules
        dependents = await self.graphiti.search(
            query=f"depends on imports from {module_name}",
            num_results=50
        )
        
        # Find all tests related to module
        tests = await self.graphiti.search(
            query=f"test {module_name} unit integration",
            num_results=20
        )
        
        return {
            'dependent_modules': [d.source_node_name for d in dependents],
            'affected_tests': [t.source_node_name for t in tests],
            'risk_level': 'high' if len(dependents) > 10 else 'medium'
        }
    
    async def create_feature_workspace(self):
        """Create dedicated workspace for feature"""
        # Generate feature-specific visualizations
        self.visualizer.create_feature_impact_graph(
            self.feature_context,
            f"feature_{self.feature_context['name']}_impact.html"
        )
        
        # Create VSCode workspace file
        workspace_config = {
            "folders": [
                {"path": module} for module in self.feature_context['related_modules']
            ],
            "settings": {
                "graphiti.activeFeature": self.feature_context['name'],
                "graphiti.showFeatureContext": True
            }
        }
        
        with open(f"{self.feature_context['name']}.code-workspace", 'w') as f:
            json.dump(workspace_config, f, indent=2)
```

### 1.3 Code Review Workflow

Create `code_review_workflow.py`:

```python
class CodeReviewWorkflow:
    def __init__(self, graphiti_client):
        self.graphiti = graphiti_client
        
    async def prepare_review_context(self, changed_files: List[str]) -> Dict:
        """Prepare context for code review"""
        review_context = {
            'changed_files': changed_files,
            'affected_concepts': [],
            'dependency_changes': [],
            'pattern_analysis': [],
            'test_coverage': []
        }
        
        for file in changed_files:
            # Find affected concepts
            concepts = await self.graphiti.search(
                query=f"file {file} concept pattern domain",
                num_results=10
            )
            
            review_context['affected_concepts'].extend([
                {
                    'file': file,
                    'concept': c.target_node_name,
                    'impact': c.fact
                }
                for c in concepts
            ])
            
            # Check for dependency changes
            deps = await self.analyze_dependency_changes(file)
            review_context['dependency_changes'].extend(deps)
        
        # Generate review checklist
        checklist = self.generate_review_checklist(review_context)
        
        return {
            'context': review_context,
            'checklist': checklist,
            'visualization': await self.create_review_visualization(review_context)
        }
    
    def generate_review_checklist(self, context: Dict) -> List[Dict]:
        """Generate automated review checklist"""
        checklist = []
        
        # Check for high-risk changes
        if any(d['risk'] == 'high' for d in context['dependency_changes']):
            checklist.append({
                'item': 'High-risk dependency changes detected',
                'severity': 'critical',
                'action': 'Review impact on dependent modules'
            })
        
        # Check for pattern violations
        if context['pattern_analysis']:
            checklist.append({
                'item': 'Design pattern considerations',
                'severity': 'medium',
                'action': 'Verify pattern implementation consistency'
            })
        
        return checklist
```

## Part 2: LangGraph Workflow Visualization

### 2.1 LangGraph Specific Analysis

Create `langgraph_analyzer.py`:

```python
import re
from typing import Dict, List, Tuple

class LangGraphAnalyzer:
    def __init__(self, graphiti_client):
        self.graphiti = graphiti_client
        
    async def analyze_langgraph_file(self, file_path: str, content: str) -> Dict:
        """Specialized analysis for LangGraph workflows"""
        workflow_data = {
            'type': 'langgraph_workflow',
            'nodes': [],
            'edges': [],
            'state_definition': None,
            'conditional_edges': []
        }
        
        # Extract StateGraph definition
        state_match = re.search(r'StateGraph\[(.*?)\]', content)
        if state_match:
            workflow_data['state_definition'] = state_match.group(1)
        
        # Extract nodes
        node_pattern = r'\.add_node\(["\'](\w+)["\'],\s*(\w+)\)'
        for match in re.finditer(node_pattern, content):
            workflow_data['nodes'].append({
                'id': match.group(1),
                'handler': match.group(2),
                'type': self._infer_node_type(match.group(2), content)
            })
        
        # Extract edges
        edge_pattern = r'\.add_edge\(["\'](\w+)["\'],\s*["\'](\w+)["\']\)'
        for match in re.finditer(edge_pattern, content):
            workflow_data['edges'].append({
                'from': match.group(1),
                'to': match.group(2),
                'type': 'direct'
            })
        
        # Extract conditional edges
        cond_pattern = r'\.add_conditional_edges\(["\'](\w+)["\'],\s*(\w+),\s*{([^}]+)}'
        for match in re.finditer(cond_pattern, content):
            conditions = self._parse_conditions(match.group(3))
            workflow_data['conditional_edges'].append({
                'from': match.group(1),
                'condition_func': match.group(2),
                'branches': conditions
            })
        
        # Create workflow episode
        await self.graphiti.add_episode(
            name=f"LangGraph: {file_path}",
            episode_body=json.dumps(workflow_data),
            source=EpisodeType.json,
            source_description="LangGraph workflow definition",
            group_id=os.getenv('ANALYSIS_GROUP_ID')
        )
        
        return workflow_data
    
    def _infer_node_type(self, handler_name: str, content: str) -> str:
        """Infer node type from handler name and implementation"""
        if 'tool' in handler_name.lower():
            return 'tool_node'
        elif 'llm' in handler_name.lower() or 'agent' in handler_name.lower():
            return 'agent_node'
        elif 'condition' in handler_name.lower():
            return 'condition_node'
        else:
            return 'action_node'
    
    async def create_workflow_visualization(self, workflow_data: Dict, output_file: str):
        """Create interactive LangGraph workflow visualization"""
        net = Network(height='750px', width='100%', directed=True)
        net.barnes_hut(spring_length=200)
        
        # Add nodes with workflow-specific styling
        node_styles = {
            'tool_node': {'color': '#4CAF50', 'shape': 'box'},
            'agent_node': {'color': '#2196F3', 'shape': 'ellipse'},
            'condition_node': {'color': '#FF9800', 'shape': 'diamond'},
            'action_node': {'color': '#9C27B0', 'shape': 'dot'}
        }
        
        for node in workflow_data['nodes']:
            style = node_styles.get(node['type'], {'color': '#666', 'shape': 'dot'})
            net.add_node(
                node['id'],
                label=node['id'],
                title=f"Handler: {node['handler']}",
                **style,
                size=30
            )
        
        # Add edges
        for edge in workflow_data['edges']:
            net.add_edge(
                edge['from'],
                edge['to'],
                color='#666',
                width=2
            )
        
        # Add conditional edges with labels
        for cond_edge in workflow_data['conditional_edges']:
            for condition, target in cond_edge['branches'].items():
                net.add_edge(
                    cond_edge['from'],
                    target,
                    label=condition,
                    color='#FF5722',
                    dashes=True,
                    width=2
                )
        
        net.show(output_file)
```

### 2.2 Workflow Testing Visualization

Create `workflow_test_visualizer.py`:

```python
class WorkflowTestVisualizer:
    def __init__(self, graphiti_client):
        self.graphiti = graphiti_client
        
    async def visualize_test_coverage(self, workflow_name: str):
        """Visualize test coverage for workflow paths"""
        # Find workflow nodes
        workflow_nodes = await self.graphiti.search(
            query=f"workflow {workflow_name} node",
            num_results=50
        )
        
        # Find test cases
        test_cases = await self.graphiti.search(
            query=f"test {workflow_name} path coverage",
            num_results=50
        )
        
        # Create coverage visualization
        coverage_data = self.analyze_path_coverage(workflow_nodes, test_cases)
        
        # Generate interactive visualization
        self.create_coverage_heatmap(coverage_data, f"{workflow_name}_coverage.html")
```

## Part 3: Best Practices and Tips

### 3.1 Performance Optimization

```python
class GraphitiPerformanceOptimizer:
    def __init__(self, graphiti_client):
        self.graphiti = graphiti_client
        
    async def optimize_large_codebase_analysis(self, project_root: Path):
        """Optimize analysis for large codebases"""
        # 1. Batch processing
        batch_size = 50
        files = list(project_root.rglob('*.py'))
        
        for i in range(0, len(files), batch_size):
            batch = files[i:i+batch_size]
            await self.process_batch(batch)
            
            # Add delay to prevent overwhelming the system
            await asyncio.sleep(1)
        
    async def process_batch(self, files: List[Path]):
        """Process files in batch"""
        episodes = []
        
        for file in files:
            # Read and prepare episode data
            content = file.read_text(encoding='utf-8')
            episode_data = {
                'file': str(file),
                'content': content,
                'metadata': self.extract_metadata(content)
            }
            episodes.append(episode_data)
        
        # Bulk add episodes
        for ep in episodes:
            await self.graphiti.add_episode(
                name=f"Batch: {ep['file']}",
                episode_body=json.dumps(ep),
                source=EpisodeType.json,
                group_id=os.getenv('ANALYSIS_GROUP_ID')
            )
```

### 3.2 Maintenance Scripts

Create `maintenance_scripts.py`:

```python
class GraphitiMaintenance:
    def __init__(self, graphiti_client):
        self.graphiti = graphiti_client
        
    async def cleanup_outdated_nodes(self, days_old: int = 30):
        """Remove nodes older than specified days"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        # Query for old nodes
        old_episodes = await self.graphiti.retrieve_episodes(
            group_ids=[os.getenv('ANALYSIS_GROUP_ID')],
            last_n=1000
        )
        
        for episode in old_episodes:
            if episode.created_at < cutoff_date:
                await episode.delete(self.graphiti.driver)
    
    async def consolidate_concepts(self):
        """Merge similar concepts"""
        # Find similar concepts
        concepts = await self.graphiti.search(
            query="concept pattern domain",
            num_results=100
        )
        
        # Group by similarity
        similar_groups = self.group_similar_concepts(concepts)
        
        # Merge similar concepts
        for group in similar_groups:
            await self.merge_concept_group(group)
```

### 3.3 Integration Best Practices

```markdown
## Best Practices Summary

### 1. Structuring Your Analysis
- **Group by Feature**: Use separate group_ids for different features
- **Time-based Snapshots**: Create periodic snapshots of your codebase state
- **Incremental Updates**: Only re-analyze changed files

### 2. Query Optimization
- **Use Specific Queries**: More specific queries return better results
- **Center Node Strategy**: Always use center_node_uuid for contextual searches
- **Limit Results**: Start with small result sets and expand as needed

### 3. Visualization Guidelines
- **Multiple Views**: Create different visualizations for different audiences
- **Interactive First**: Static images should be secondary to interactive graphs
- **Performance**: Limit initial node display to 100-200 for smooth interaction

### 4. Workflow Integration
- **CI/CD Integration**: Add Graphiti analysis to your build pipeline
- **Pre-commit Hooks**: Analyze changes before committing
- **Review Automation**: Generate review contexts automatically

### 5. Maintenance Schedule
- **Daily**: Update changed files, check for pattern violations
- **Weekly**: Consolidate concepts, update documentation nodes
- **Monthly**: Full re-analysis, cleanup old nodes

### 6. VSCode Integration Tips
- **Custom Tasks**: Create tasks.json entries for common Graphiti operations
- **Snippets**: Add code snippets for common graph queries
- **Extensions**: Consider building a custom extension for deeper integration
```

## Part 4: Troubleshooting Guide

Create `troubleshooting.md`:

```markdown
## Common Issues and Solutions

### 1. Performance Issues
**Problem**: Slow graph queries
**Solution**: 
- Add indices to frequently queried properties
- Use more specific search queries
- Implement caching for repeated queries

### 2. Memory Usage
**Problem**: High memory consumption during analysis
**Solution**:
- Process files in smaller batches
- Clear driver session regularly
- Use streaming for large file analysis

### 3. Visualization Rendering
**Problem**: Graph visualization is slow or unresponsive
**Solution**:
- Limit initial node count
- Use clustering for large graphs
- Enable physics simulation selectively

### 4. Entity Extraction
**Problem**: Missing or incorrect entities
**Solution**:
- Customize entity types for your domain
- Improve docstring quality
- Add explicit type annotations
```

This comprehensive guide provides you with practical workflows, specialized tools for LangGraph visualization, performance optimization strategies, and best practices for maintaining and using your Graphiti-powered codebase analysis system effectively.