# Setting Up Graphiti for Codebase Analysis

## Step 1: Environment Preparation

### 1.1 Create a Dedicated Analysis Environment

```bash
# Create a new directory for your analysis
mkdir codebase-analysis
cd codebase-analysis

# Create a Python virtual environment
python -m venv venv

# Activate it (Windows)
.\venv\Scripts\activate
```

### 1.2 Install Graphiti and Dependencies

```bash
# Install Graphiti with visualization extras
pip install graphiti-core
pip install jupyter notebook  # For interactive exploration
pip install pyvis  # For graph visualization
pip install matplotlib seaborn  # For analytics
```

## Step 2: Neo4j Configuration

### 2.1 Start Neo4j Desktop
1. Open Neo4j Desktop
2. Create a new project called "Codebase Analysis"
3. Add a local DBMS with:
   - Name: "CodeGraph"
   - Password: Set a memorable password
   - Version: 5.26+

### 2.2 Configure Connection

Create a `.env` file in your analysis directory:

```env
# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password_here

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Analysis Configuration
ANALYSIS_GROUP_ID=my_codebase_v1
PROJECT_ROOT=C:/path/to/your/project
```

## Step 3: Create the Analysis Framework

### 3.1 Code Scanner Module

Create `code_scanner.py`:

```python
import os
import ast
import json
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, List, Any

class CodebaseScanner:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.file_registry = {}
        
    def scan_python_file(self, filepath: Path) -> Dict[str, Any]:
        """Extract structure from Python file"""
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            
        try:
            tree = ast.parse(content)
            return {
                'type': 'python',
                'path': str(filepath.relative_to(self.project_root)),
                'content': content,
                'imports': self._extract_imports(tree),
                'classes': self._extract_classes(tree),
                'functions': self._extract_functions(tree),
                'docstring': ast.get_docstring(tree)
            }
        except SyntaxError:
            return None
            
    def scan_langgraph_workflow(self, filepath: Path) -> Dict[str, Any]:
        """Extract LangGraph workflow structure"""
        # Specialized parsing for LangGraph files
        # Look for StateGraph, nodes, edges patterns
        pass
        
    def scan_documentation(self, filepath: Path) -> Dict[str, Any]:
        """Extract documentation structure"""
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            
        return {
            'type': 'documentation',
            'path': str(filepath.relative_to(self.project_root)),
            'content': content,
            'format': filepath.suffix
        }
```

### 3.2 Graphiti Integration Module

Create `graphiti_analyzer.py`:

```python
from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType
import asyncio
from code_scanner import CodebaseScanner

class CodebaseAnalyzer:
    def __init__(self, graphiti_client: Graphiti, scanner: CodebaseScanner):
        self.graphiti = graphiti_client
        self.scanner = scanner
        self.group_id = os.getenv('ANALYSIS_GROUP_ID', 'codebase_default')
        
    async def analyze_file(self, filepath: Path):
        """Convert a file into a Graphiti episode"""
        file_data = None
        
        if filepath.suffix == '.py':
            file_data = self.scanner.scan_python_file(filepath)
        elif filepath.suffix in ['.md', '.rst', '.txt']:
            file_data = self.scanner.scan_documentation(filepath)
            
        if file_data:
            # Create structured episode for the file
            episode_body = json.dumps(file_data)
            
            await self.graphiti.add_episode(
                name=f"Code: {filepath.name}",
                episode_body=episode_body,
                source=EpisodeType.json,
                source_description=f"Codebase file: {file_data['type']}",
                group_id=self.group_id,
                reference_time=datetime.now(timezone.utc)
            )
            
    async def analyze_directory(self, directory: Path, patterns=['*.py', '*.md']):
        """Recursively analyze a directory"""
        for pattern in patterns:
            for filepath in directory.rglob(pattern):
                print(f"Analyzing: {filepath}")
                await self.analyze_file(filepath)
```

## Step 4: Custom Entity Types for Code

Create `code_entities.py`:

```python
from pydantic import BaseModel, Field

class CodeModule(BaseModel):
    """Represents a Python module or package"""
    name: str = Field(..., description="Module name")
    path: str = Field(..., description="Relative path in project")
    type: str = Field(..., description="package or module")

class CodeFunction(BaseModel):
    """Represents a function or method"""
    name: str = Field(..., description="Function name")
    module: str = Field(..., description="Parent module")
    parameters: str = Field(..., description="Parameter signature")
    returns: str = Field(default="", description="Return type annotation")
    
class CodeClass(BaseModel):
    """Represents a class definition"""
    name: str = Field(..., description="Class name")
    module: str = Field(..., description="Parent module")
    base_classes: str = Field(default="", description="Base classes")
    
class WorkflowNode(BaseModel):
    """Represents a LangGraph workflow node"""
    name: str = Field(..., description="Node identifier")
    workflow: str = Field(..., description="Parent workflow")
    node_type: str = Field(..., description="Type of node")
    
class Dependency(BaseModel):
    """Represents an import or dependency"""
    source: str = Field(..., description="Importing module")
    target: str = Field(..., description="Imported module")
    import_type: str = Field(..., description="Type of import")

# Register custom entities
CODE_ENTITY_TYPES = {
    'CodeModule': CodeModule,
    'CodeFunction': CodeFunction,
    'CodeClass': CodeClass,
    'WorkflowNode': WorkflowNode,
    'Dependency': Dependency
}
```

## Step 5: Initial Analysis Script

Create `analyze_codebase.py`:

```python
import asyncio
import os
from pathlib import Path
from dotenv import load_dotenv
from graphiti_core import Graphiti
from code_scanner import CodebaseScanner
from graphiti_analyzer import CodebaseAnalyzer
from code_entities import CODE_ENTITY_TYPES

load_dotenv()

async def main():
    # Initialize connections
    graphiti = Graphiti(
        uri=os.getenv('NEO4J_URI'),
        user=os.getenv('NEO4J_USER'),
        password=os.getenv('NEO4J_PASSWORD')
    )
    
    # Build indices
    await graphiti.build_indices_and_constraints()
    
    # Initialize scanner and analyzer
    project_root = Path(os.getenv('PROJECT_ROOT'))
    scanner = CodebaseScanner(project_root)
    analyzer = CodebaseAnalyzer(graphiti, scanner)
    
    # Start analysis
    print(f"Analyzing codebase at: {project_root}")
    
    # Analyze Python files
    await analyzer.analyze_directory(
        project_root, 
        patterns=['*.py', '*.md', '*.yml', '*.json']
    )
    
    print("Analysis complete!")
    
    # Close connection
    await graphiti.close()

if __name__ == "__main__":
    asyncio.run(main())
```

## Step 6: Verify Setup

Run a test analysis on a small directory first:

```bash
# Set a test directory in .env
PROJECT_ROOT=C:/path/to/test/folder

# Run the analysis
python analyze_codebase.py
```

Check Neo4j Browser (http://localhost:7474) to see if nodes and relationships were created.

## Next Steps

Once setup is complete and you've verified it works on a test directory, you're ready to:
1. Analyze your full codebase
2. Create custom visualizations
3. Build interactive exploration tools
4. Set up continuous analysis