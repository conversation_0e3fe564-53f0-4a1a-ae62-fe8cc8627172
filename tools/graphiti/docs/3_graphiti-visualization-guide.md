# Creating Interactive Visualizations with Graphiti

## Part 1: Basic Graph Visualization

### 1.1 Neo4j Browser Queries

Start with Neo4<PERSON> Browser for immediate visual feedback:

```cypher
// View all module nodes
MATCH (m:CodeModule)
RETURN m LIMIT 50

// Show import relationships
MATCH (m1:CodeModule)-[r:IMPORTS]->(m2:CodeModule)
RETURN m1, r, m2 LIMIT 100

// Find all classes in a specific module
MATCH (m:CodeModule {name: 'main.py'})-[:CONTAINS]->(c:CodeClass)
RETURN m, c

// Trace workflow paths
MATCH path = (start:WorkflowNode)-[:TRANSITIONS_TO*]->(end:WorkflowNode)
WHERE start.workflow = 'discussion_graph'
RETURN path
```

### 1.2 PyVis Interactive Visualizations

Create `visualize_graph.py`:

```python
from pyvis.network import Network
import asyncio
from graphiti_core import Graphiti
from neo4j import GraphDatabase
import os
from dotenv import load_dotenv

load_dotenv()

class GraphVisualizer:
    def __init__(self, neo4j_driver):
        self.driver = neo4j_driver
        
    def create_module_dependency_graph(self, output_file='module_deps.html'):
        """Create an interactive module dependency visualization"""
        net = Network(height='750px', width='100%', directed=True)
        
        # Configure physics for better layout
        net.barnes_hut(
            gravity=-80000,
            central_gravity=0.3,
            spring_length=250,
            spring_strength=0.001
        )
        
        with self.driver.session() as session:
            # Get modules and their relationships
            result = session.run("""
                MATCH (m:CodeModule)
                OPTIONAL MATCH (m)-[r:IMPORTS|DEPENDS_ON]->(m2:CodeModule)
                RETURN m, r, m2
            """)
            
            nodes_added = set()
            
            for record in result:
                module = record['m']
                
                # Add source node
                if module['name'] not in nodes_added:
                    net.add_node(
                        module['name'],
                        label=module['name'],
                        title=f"Path: {module.get('path', 'Unknown')}",
                        color='#4CAF50' if 'main' in module['name'] else '#2196F3',
                        size=25
                    )
                    nodes_added.add(module['name'])
                
                # Add relationship if exists
                if record['r'] and record['m2']:
                    target = record['m2']
                    if target['name'] not in nodes_added:
                        net.add_node(
                            target['name'],
                            label=target['name'],
                            title=f"Path: {target.get('path', 'Unknown')}",
                            color='#FF9800',
                            size=20
                        )
                        nodes_added.add(target['name'])
                    
                    net.add_edge(
                        module['name'],
                        target['name'],
                        title=record['r'].type,
                        color='#666'
                    )
        
        # Save visualization
        net.show(output_file)
        print(f"Visualization saved to {output_file}")
        
    def create_class_hierarchy_graph(self, module_name=None, output_file='class_hierarchy.html'):
        """Visualize class inheritance and composition"""
        net = Network(height='750px', width='100%', directed=True)
        
        query = """
            MATCH (c:CodeClass)
            OPTIONAL MATCH (c)-[r:INHERITS_FROM|CONTAINS|USES]->(c2:CodeClass)
        """
        
        if module_name:
            query += f" WHERE c.module = '{module_name}'"
            
        query += " RETURN c, r, c2"
        
        with self.driver.session() as session:
            result = session.run(query)
            
            nodes_added = set()
            
            for record in result:
                class_node = record['c']
                
                # Add class node with shape based on type
                if class_node['name'] not in nodes_added:
                    net.add_node(
                        class_node['name'],
                        label=class_node['name'],
                        title=f"Module: {class_node.get('module', 'Unknown')}",
                        shape='box' if 'Base' in class_node['name'] else 'ellipse',
                        color='#9C27B0',
                        size=30
                    )
                    nodes_added.add(class_node['name'])
                
                # Add relationships
                if record['r'] and record['c2']:
                    target_class = record['c2']
                    if target_class['name'] not in nodes_added:
                        net.add_node(
                            target_class['name'],
                            label=target_class['name'],
                            shape='box',
                            color='#E91E63',
                            size=25
                        )
                        nodes_added.add(target_class['name'])
                    
                    edge_color = {
                        'INHERITS_FROM': '#FF5722',
                        'CONTAINS': '#4CAF50',
                        'USES': '#2196F3'
                    }.get(record['r'].type, '#666')
                    
                    net.add_edge(
                        class_node['name'],
                        target_class['name'],
                        title=record['r'].type,
                        color=edge_color,
                        width=3 if record['r'].type == 'INHERITS_FROM' else 1
                    )
        
        net.show(output_file)
        
    def create_langgraph_workflow_viz(self, workflow_name, output_file='workflow.html'):
        """Visualize LangGraph workflow"""
        net = Network(height='750px', width='100%', directed=True)
        net.set_edge_smooth('dynamic')
        
        with self.driver.session() as session:
            # Get workflow nodes and transitions
            result = session.run("""
                MATCH (n:WorkflowNode {workflow: $workflow})
                OPTIONAL MATCH (n)-[t:TRANSITIONS_TO]->(n2:WorkflowNode)
                RETURN n, t, n2
            """, workflow=workflow_name)
            
            nodes_added = set()
            
            for record in result:
                node = record['n']
                
                # Style based on node type
                node_colors = {
                    'start': '#4CAF50',
                    'end': '#F44336',
                    'decision': '#FF9800',
                    'action': '#2196F3',
                    'default': '#9E9E9E'
                }
                
                if node['name'] not in nodes_added:
                    net.add_node(
                        node['name'],
                        label=node['name'],
                        title=f"Type: {node.get('node_type', 'Unknown')}",
                        color=node_colors.get(node.get('node_type', 'default'), '#9E9E9E'),
                        shape='diamond' if 'decision' in node.get('node_type', '') else 'dot',
                        size=40
                    )
                    nodes_added.add(node['name'])
                
                # Add transitions
                if record['t'] and record['n2']:
                    target_node = record['n2']
                    if target_node['name'] not in nodes_added:
                        net.add_node(
                            target_node['name'],
                            label=target_node['name'],
                            color=node_colors.get(target_node.get('node_type', 'default'), '#9E9E9E'),
                            shape='diamond' if 'decision' in target_node.get('node_type', '') else 'dot',
                            size=40
                        )
                        nodes_added.add(target_node['name'])
                    
                    net.add_edge(
                        node['name'],
                        target_node['name'],
                        title=record['t'].get('condition', 'default'),
                        arrows='to',
                        physics=True
                    )
        
        net.show(output_file)

# Usage example
async def visualize_codebase():
    driver = GraphDatabase.driver(
        os.getenv('NEO4J_URI'),
        auth=(os.getenv('NEO4J_USER'), os.getenv('NEO4J_PASSWORD'))
    )
    
    visualizer = GraphVisualizer(driver)
    
    # Create different visualizations
    visualizer.create_module_dependency_graph()
    visualizer.create_class_hierarchy_graph()
    visualizer.create_langgraph_workflow_viz('discussion_graph')
    
    driver.close()

if __name__ == "__main__":
    asyncio.run(visualize_codebase())
```

## Part 2: Interactive Jupyter Notebooks

### 2.1 Create Analysis Notebook

Create `codebase_explorer.ipynb`:

```python
# Cell 1: Setup
import os
import asyncio
from dotenv import load_dotenv
from graphiti_core import Graphiti
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from IPython.display import IFrame, display, HTML

load_dotenv()

# Initialize Graphiti
graphiti = Graphiti(
    uri=os.getenv('NEO4J_URI'),
    user=os.getenv('NEO4J_USER'),
    password=os.getenv('NEO4J_PASSWORD')
)

# Cell 2: Search and Explore Functions
async def search_code_concepts(query, limit=10):
    """Search for code concepts in the graph"""
    results = await graphiti.search(
        query=query,
        group_ids=[os.getenv('ANALYSIS_GROUP_ID')],
        num_results=limit
    )
    
    df = pd.DataFrame([
        {
            'Fact': r.fact,
            'Source': r.source_node_name,
            'Target': r.target_node_name,
            'Created': r.created_at
        }
        for r in results
    ])
    
    return df

async def get_module_metrics():
    """Get metrics about modules"""
    # Use Graphiti's search to find module relationships
    module_search = await graphiti._search(
        query="module import dependency",
        config=NODE_HYBRID_SEARCH_RRF
    )
    
    # Process results into metrics
    return module_search

# Cell 3: Visualization Functions
def create_complexity_heatmap(module_data):
    """Create a heatmap of module complexity"""
    # Convert module data to matrix form
    # Show coupling between modules
    plt.figure(figsize=(12, 8))
    sns.heatmap(module_data, cmap='YlOrRd', annot=True)
    plt.title('Module Coupling Heatmap')
    plt.show()

def show_interactive_graph(html_file):
    """Display PyVis graph in notebook"""
    display(IFrame(src=html_file, width=1000, height=600))

# Cell 4: Analysis Queries
# Find most connected modules
most_connected_query = """
MATCH (m:CodeModule)-[r]-(other)
RETURN m.name as Module, 
       COUNT(DISTINCT other) as Connections,
       COLLECT(DISTINCT type(r)) as RelationshipTypes
ORDER BY Connections DESC
LIMIT 10
"""

# Find circular dependencies
circular_deps_query = """
MATCH path = (m1:CodeModule)-[:IMPORTS*2..5]->(m1)
RETURN path LIMIT 10
"""

# Find unused code
unused_code_query = """
MATCH (f:CodeFunction)
WHERE NOT (f)<-[:CALLS]-()
RETURN f.name as UnusedFunction, f.module as Module
"""
```

### 2.2 VSCode Integration Script

Create `vscode_integration.py`:

```python
import subprocess
import webbrowser
from pathlib import Path

class VSCodeNavigator:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        
    def open_in_vscode(self, file_path, line_number=None):
        """Open a file in VSCode at specific line"""
        full_path = self.project_root / file_path
        
        if line_number:
            # Open at specific line
            subprocess.run([
                'code', 
                '--goto', 
                f'{full_path}:{line_number}'
            ])
        else:
            subprocess.run(['code', str(full_path)])
            
    def open_graph_and_code(self, graph_html, code_file):
        """Open graph visualization and corresponding code side by side"""
        # Open graph in browser
        webbrowser.open(f'file://{Path(graph_html).absolute()}')
        
        # Open code in VSCode
        self.open_in_vscode(code_file)
```

## Part 3: Dynamic Visualization Dashboard

Create `dashboard.py`:

```python
from flask import Flask, render_template, jsonify
import asyncio
from graphiti_analyzer import CodebaseAnalyzer

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('graph_explorer.html')

@app.route('/api/search/<query>')
async def search(query):
    """API endpoint for searching the graph"""
    results = await graphiti.search(query)
    return jsonify([r.dict() for r in results])

@app.route('/api/visualize/<scope>')
def visualize(scope):
    """Generate visualization for specific scope"""
    # Generate PyVis graph based on scope
    # Return path to HTML file
    pass

# HTML template with D3.js for custom visualizations
graph_explorer_template = """
<!DOCTYPE html>
<html>
<head>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        .node { cursor: pointer; }
        .link { stroke: #999; stroke-opacity: 0.6; }
    </style>
</head>
<body>
    <div id="graph"></div>
    <script>
        // D3.js force-directed graph
        // Fetch data from API and render
    </script>
</body>
</html>
"""

if __name__ == '__main__':
    app.run(debug=True)
```

## Part 4: Continuous Visualization Updates

Create `watch_and_update.py`:

```python
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import asyncio

class CodeChangeHandler(FileSystemEventHandler):
    def __init__(self, analyzer):
        self.analyzer = analyzer
        
    def on_modified(self, event):
        if event.src_path.endswith('.py'):
            # Re-analyze the modified file
            asyncio.run(self.analyzer.analyze_file(Path(event.src_path)))
            
            # Trigger visualization update
            self.update_visualizations()
            
    def update_visualizations(self):
        # Regenerate affected visualizations
        pass

# Set up file watcher
observer = Observer()
observer.schedule(CodeChangeHandler(analyzer), project_root, recursive=True)
observer.start()
```

## Next Steps

With these visualizations in place, you can:
1. Create custom views for specific subsystems
2. Build interactive exploration tools
3. Generate architecture documentation
4. Track code evolution over time