import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the analysis directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "analysis"))

from graphiti_core import Graphiti
from code_scanner import CodebaseScanner
from graphiti_analyzer import CodebaseAnalyzer
from code_entities import CODE_ENTITY_TYPES

load_dotenv(Path(__file__).parent / "analysis" / ".env")

async def main():
    # Check if OpenAI API key is set
    openai_key = os.getenv('OPENAI_API_KEY')
    if not openai_key or openai_key == 'your_openai_api_key_here':
        print("❌ Please set your OPENAI_API_KEY in the .env file")
        print("You can get an API key from: https://platform.openai.com/api-keys")
        return

    print("🚀 Starting Graphiti codebase analysis...")

    # Initialize connections
    try:
        graphiti = Graphiti(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'),
            password=os.getenv('NEO4J_PASSWORD')
        )
        print("✓ Connected to Graphiti")
    except Exception as e:
        print(f"❌ Failed to connect to Graphiti: {e}")
        print("Make sure Neo4j is running and credentials are correct")
        return

    try:
        # Build indices
        print("🔧 Building indices and constraints...")
        await graphiti.build_indices_and_constraints()
        print("✓ Indices built successfully")
    except Exception as e:
        print(f"⚠️  Warning: Could not build indices: {e}")

    # Initialize scanner and analyzer
    project_root = Path(os.getenv('PROJECT_ROOT'))
    if not project_root.exists():
        print(f"❌ Project root does not exist: {project_root}")
        return

    scanner = CodebaseScanner(project_root)
    analyzer = CodebaseAnalyzer(graphiti, scanner)

    # Start analysis
    print(f"📁 Analyzing codebase at: {project_root}")

    # For initial testing, let's analyze a small subset
    print("🔍 Starting with a small subset for testing...")

    # Analyze Python files and documentation
    await analyzer.analyze_directory(
        project_root,
        patterns=['*.py', '*.md'],
        max_files=20  # Limit for initial test
    )

    print("📊 Getting analysis summary...")
    await analyzer.get_analysis_summary()

    print("✅ Analysis complete!")
    print("\n📋 Next steps:")
    print("1. Check Neo4j Browser at http://localhost:7474")
    print("2. Run visualization scripts")
    print("3. Increase max_files limit for full analysis")

    # Close connection
    await graphiti.close()

if __name__ == "__main__":
    asyncio.run(main())
