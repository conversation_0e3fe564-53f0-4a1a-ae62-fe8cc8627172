#!/usr/bin/env python3
"""
Install required dependencies for Graphiti tools
"""
import subprocess
import sys
from pathlib import Path

def install_dependencies():
    """Install required Python packages in the virtual environment"""
    print("📦 Installing Graphiti Dependencies...")
    
    # Check if we're in the right directory
    current_dir = Path.cwd()
    if not current_dir.name == 'graphiti' or not (current_dir.parent.name == 'tools'):
        print("❌ Please run this script from tools/graphiti/ directory")
        return False
    
    # Check virtual environment
    venv_path = current_dir / "analysis" / "venv"
    if not venv_path.exists():
        print("🔧 Creating virtual environment...")
        try:
            subprocess.run([sys.executable, '-m', 'venv', str(venv_path)], check=True)
            print("✅ Virtual environment created")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create virtual environment: {e}")
            return False
    
    # Determine Python executable path
    python_exe = venv_path / "bin" / "python"
    if not python_exe.exists():
        python_exe = venv_path / "Scripts" / "python.exe"  # Windows
    
    if not python_exe.exists():
        print("❌ Python executable not found in virtual environment")
        return False
    
    # Required packages
    packages = [
        'graphiti-core',
        'python-dotenv',
        'neo4j',
        'pyvis',
        'openai',
        'jupyter',
        'notebook',
        'matplotlib',
        'seaborn'
    ]
    
    # Explicitly uninstall pyvis to ensure a clean reinstallation
    print("Attempting to uninstall pyvis for a clean reinstallation...")
    try:
        subprocess.run([str(python_exe), '-m', 'pip', 'uninstall', '-y', 'pyvis'], check=False)
        print("✅ pyvis uninstalled (if present).")
    except Exception as e:
        print(f"⚠️ Could not uninstall pyvis: {e}")
        # Continue even if uninstall fails, as it might not be installed

    print(f"📥 Installing {len(packages)} packages...")
    
    # Install packages
    for package in packages:
        print(f"   Installing {package}...")
        try:
            subprocess.run([
                str(python_exe), '-m', 'pip', 'install', package
            ], check=True) # Removed --force-reinstall to prevent unnecessary reinstallation
            print(f"   ✅ {package} installed")
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Failed to install {package}: {e}")
            return False
    
    print("\n✅ All dependencies installed successfully!")
    print("\n📋 Next steps:")
    print("   1. Configure analysis/.env with your API keys")
    print("   2. Start Neo4j (Docker or Desktop)")
    print("   3. Run: python setup_verification.py")
    
    return True

if __name__ == "__main__":
    success = install_dependencies()
    sys.exit(0 if success else 1)
