# Graphiti Codebase Analysis Setup

This setup allows you to analyze your codebase using Graphiti, a temporal knowledge graph framework.

## 🚀 Quick Start

### 1. Neo4j Database Setup

**Option A: Using Docker (Recommended)**
```bash
# Start Neo4j in Docker
docker run -d \
  --name neo4j-graphiti \
  -p 7474:7474 -p 7687:7687 \
  -e NEO4J_AUTH=neo4j/graphiti123 \
  -e NEO4J_PLUGINS='["apoc"]' \
  -v neo4j_data:/data \
  -v neo4j_logs:/logs \
  neo4j:5.26
```

**Option B: Using Neo4j Desktop**
- Download and install Neo4j Desktop
- Create a new project called "Codebase Analysis"
- Add a local DBMS with password: `graphiti123`

**Verify Neo4j is running:**
- Open Neo4j browser at: http://localhost:7474
- Login with username: `neo4j`, password: `graphiti123`

### 2. Configure Environment

**Set OpenAI API Key:**
- Get your API key from: https://platform.openai.com/api-keys
- Edit `tools/graphiti/analysis/.env` and replace `your_openai_api_key_here` with your actual key

### 3. Install Dependencies and Verify Setup

```bash
cd tools/graphiti

# Install required Python packages
python install_dependencies.py

# Comprehensive setup verification
python setup_verification.py

# Quick connection test
python quick_test.py
```

This will install dependencies and verify that everything is configured correctly.

### 4. Analyze Sample Files

```bash
cd tools/graphiti
python analyze_small_sample.py
```

This will analyze a few key files from your codebase to test the system.

### 5. View Results

**In Neo4j Browser (http://localhost:7474):**
```cypher
// View all nodes
MATCH (n) RETURN n LIMIT 25

// View episodes (your code files)
MATCH (n:Episode) RETURN n.name, n.created_at LIMIT 10

// View entities extracted from code
MATCH (n:Entity) RETURN n.name, n.entity_type LIMIT 10
```

### 6. Create Visualizations

```bash
cd tools/graphiti
python visualize_graph.py
```

This will create an interactive HTML visualization of your codebase graph.

## 📁 Directory Structure

```
tools/graphiti/
├── analysis/                    # Analysis environment
│   ├── venv/                   # Python virtual environment
│   ├── .env                    # Configuration file
│   ├── code_scanner.py         # Extracts structure from code files
│   ├── graphiti_analyzer.py    # Converts code to Graphiti episodes
│   └── code_entities.py        # Custom entity types for code
├── analyze_codebase.py         # Full codebase analysis script
├── analyze_small_sample.py     # Sample analysis script
├── visualize_graph.py          # Creates interactive visualizations
├── install_dependencies.py     # Dependency installation
├── setup_verification.py       # Comprehensive setup verification
├── quick_test.py               # Quick setup verification
├── test_setup.py               # Complete setup testing
└── README.md                   # Tool documentation
```

## 🔧 Configuration

Edit `tools/graphiti/analysis/.env`:

```env
# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=graphiti123

# OpenAI Configuration
OPENAI_API_KEY=your_actual_api_key_here

# Analysis Configuration
ANALYSIS_GROUP_ID=goali_codebase_v1
PROJECT_ROOT=/Users/<USER>/dev/goali
```

## 📊 What Gets Analyzed

- **Python files**: Classes, functions, imports, docstrings
- **Documentation**: README files, markdown docs
- **Configuration**: JSON, YAML files
- **Structure**: File relationships, dependencies

## 🎯 Next Steps

1. **Expand Analysis**: Increase `max_files` in scripts to analyze more files
2. **Custom Queries**: Write Cypher queries to explore your codebase
3. **Visualizations**: Create custom visualizations for specific aspects
4. **Continuous Analysis**: Set up file watchers for real-time updates

## 🆘 Troubleshooting

**Neo4j Connection Issues:**
- **Docker**: Check if container is running: `docker ps | grep neo4j-graphiti`
- **Desktop**: Ensure Neo4j Desktop database is started
- Verify password is set correctly (`graphiti123`)
- Check ports 7474 and 7687 are accessible
- **Docker restart**: `docker restart neo4j-graphiti`

**OpenAI API Issues:**
- Verify API key is valid and has credits
- Check rate limits if getting errors
- Ensure `.env` file is in the correct location

**Python Issues:**
- Activate virtual environment: `source tools/graphiti/analysis/venv/bin/activate`
- Install missing packages: `pip install <package-name>`
- Check Python path and imports

**Docker Neo4j Setup Issues:**
- **Permission errors**: Add `-u $(id -u):$(id -g)` to docker run command
- **Data persistence**: Volumes are created automatically, check with `docker volume ls`
- **Memory issues**: Add `-e NEO4J_dbms_memory_heap_initial__size=512m` for low-memory systems

## 📚 Documentation

See the `graphiti/docs/` folder for detailed guides on:
- Advanced visualization techniques
- Custom entity types
- Workflow analysis
- Navigation and exploration

For usage examples and workflows, see `GRAPHITI_USAGE_GUIDE.md`.

## 🧹 Clean Implementation

All Graphiti-related files are now organized in the `tools/graphiti/` directory:

- **Main scripts**: Located in `tools/graphiti/` for easy access
- **Analysis modules**: Located in `tools/graphiti/analysis/` with virtual environment
- **Documentation**: Comprehensive guides in repository root and `graphiti/docs/`
- **Configuration**: Centralized in `tools/graphiti/analysis/.env`

This organization follows the user preference for tool-specific files in dedicated directories rather than cluttering the repository root.
