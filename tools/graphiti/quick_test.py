#!/usr/bin/env python3
"""
Quick test script for Graphiti setup
"""
import os
import sys
from pathlib import Path

# Add the analysis directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "analysis"))

import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv(Path(__file__).parent / "analysis" / ".env")

async def quick_test():
    print("🚀 Quick Graphiti Setup Test")
    print("=" * 40)

    # Check environment
    print("\n📋 Environment Check:")
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key and openai_key != 'your_openai_api_key_here':
        print("   ✅ OpenAI API Key: Set")
    else:
        print("   ❌ OpenAI API Key: Please set in analysis/.env")
        print("   📝 Get your key from: https://platform.openai.com/api-keys")
        return False

    # Test Neo4j connection
    print("\n🔗 Neo4j Connection Test:")
    try:
        from neo4j import GraphDatabase

        driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'),
            auth=(os.getenv('NEO4J_USER'), os.getenv('NEO4J_PASSWORD'))
        )

        with driver.session() as session:
            result = session.run("RETURN 'Connection successful!' as message")
            record = result.single()
            print(f"   ✅ {record['message']}")

        driver.close()

    except Exception as e:
        print(f"   ❌ Connection failed: {e}")
        print("   📝 Please set Neo4j password to 'graphiti123' in the browser")
        return False

    # Test Graphiti
    print("\n🧠 Graphiti Test:")
    try:
        from graphiti_core import Graphiti

        graphiti = Graphiti(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'),
            password=os.getenv('NEO4J_PASSWORD')
        )

        await graphiti.build_indices_and_constraints()
        print("   ✅ Graphiti initialized successfully")

        await graphiti.close()

    except Exception as e:
        print(f"   ❌ Graphiti failed: {e}")
        return False

    print("\n🎉 All tests passed! Ready to analyze your codebase.")
    print("\n📋 Next steps:")
    print("   1. Run: python analyze_small_sample.py")
    print("   2. Check results in Neo4j browser")
    print("   3. Run: python visualize_graph.py")

    return True

if __name__ == "__main__":
    asyncio.run(quick_test())
