#!/usr/bin/env python3
"""
Analyze a small sample of the codebase to test Graphiti setup
"""
import os
import sys
from pathlib import Path

# Add the analysis directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "analysis"))

import asyncio
from dotenv import load_dotenv
from graphiti_core import Graphiti
from code_scanner import CodebaseScanner
from graphiti_analyzer import CodebaseAnalyzer

# Load environment variables
load_dotenv(Path(__file__).parent / "analysis" / ".env")

async def analyze_sample():
    print("🔍 Analyzing Small Codebase Sample")
    print("=" * 40)

    # Check prerequisites
    openai_key = os.getenv('OPENAI_API_KEY')
    if not openai_key or openai_key == 'your_openai_api_key_here':
        print("❌ Please set OPENAI_API_KEY in analysis/.env first")
        return

    # Initialize Graphiti
    try:
        graphiti = Graphiti(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'),
            password=os.getenv('NEO4J_PASSWORD')
        )
        print("✅ Connected to Graphiti")
    except Exception as e:
        print(f"❌ Failed to connect: {e}")
        return

    # Build indices
    try:
        await graphiti.build_indices_and_constraints()
        print("✅ Indices built")
    except Exception as e:
        print(f"⚠️  Warning: {e}")

    # Initialize scanner and analyzer
    project_root = Path(os.getenv('PROJECT_ROOT'))
    scanner = CodebaseScanner(project_root)
    analyzer = CodebaseAnalyzer(graphiti, scanner)

    print(f"\n📁 Project root: {project_root}")

    # Analyze specific interesting files
    sample_files = [
        "README.md",
        "backend/manage.py",
        "backend/config/settings/__init__.py",
        "backend/apps/main/models.py",
        "frontend/src/App.tsx",
    ]

    print(f"\n🔍 Analyzing {len(sample_files)} sample files...")

    analyzed_count = 0
    for file_path in sample_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"   📄 {file_path}")
            await analyzer.analyze_file(full_path)
            analyzed_count += 1
        else:
            print(f"   ⚠️  {file_path} (not found)")

    print(f"\n✅ Analyzed {analyzed_count} files")

    # Get summary
    print("\n📊 Analysis Summary:")
    await analyzer.get_analysis_summary()

    # Close connection
    await graphiti.close()

    print(f"\n🎉 Sample analysis complete!")
    print(f"📋 Next steps:")
    print(f"   1. Open Neo4j browser: http://localhost:7474")
    print(f"   2. Run: MATCH (n) RETURN n LIMIT 25")
    print(f"   3. Run: python visualize_graph.py")

if __name__ == "__main__":
    asyncio.run(analyze_sample())
