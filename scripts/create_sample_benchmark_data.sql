-- Create sample benchmark data for Grafana dashboards
-- This script creates realistic sample data to demonstrate the dashboard functionality

-- Use existing agents and LLM configs, create scenarios if needed
INSERT INTO main_benchmarkscenario (name, version, description, agent_role, is_active, metadata, created_at, updated_at)
VALUES
    ('Wheel Generation - Basic', 1, 'Basic wheel generation scenario', 'mentor', true, '{"workflow_type": "wheel_generation", "difficulty": "basic"}', NOW(), NOW()),
    ('Wheel Generation - Advanced', 1, 'Advanced wheel generation with complex constraints', 'mentor', true, '{"workflow_type": "wheel_generation", "difficulty": "advanced"}', NOW(), NOW()),
    ('Resource Analysis', 1, 'Resource analysis and planning scenario', 'resource', true, '{"workflow_type": "resource_planning", "difficulty": "medium"}', NOW(), NOW()),
    ('Psychological Assessment', 1, 'Psychological assessment and guidance session', 'psychological', true, '{"workflow_type": "assessment", "difficulty": "medium"}', NOW(), NOW())
ON CONFLICT (name, version) DO NOTHING;

-- Create sample benchmark runs with realistic data
-- Get scenario IDs first
DO $$
DECLARE
    scenario_basic_id INTEGER;
    scenario_advanced_id INTEGER;
    scenario_resource_id INTEGER;
    scenario_psych_id INTEGER;
BEGIN
    -- Get scenario IDs
    SELECT id INTO scenario_basic_id FROM main_benchmarkscenario WHERE name = 'Wheel Generation - Basic' LIMIT 1;
    SELECT id INTO scenario_advanced_id FROM main_benchmarkscenario WHERE name = 'Wheel Generation - Advanced' LIMIT 1;
    SELECT id INTO scenario_resource_id FROM main_benchmarkscenario WHERE name = 'Resource Analysis' LIMIT 1;
    SELECT id INTO scenario_psych_id FROM main_benchmarkscenario WHERE name = 'Psychological Assessment' LIMIT 1;

    -- If scenarios don't exist, create them with known IDs
    IF scenario_basic_id IS NULL THEN
        INSERT INTO main_benchmarkscenario (name, version, description, agent_role, is_active, metadata, created_at, updated_at)
        VALUES ('Wheel Generation - Basic', 1, 'Basic wheel generation scenario', 'mentor', true, '{"workflow_type": "wheel_generation", "difficulty": "basic"}', NOW(), NOW())
        RETURNING id INTO scenario_basic_id;
    END IF;

    IF scenario_advanced_id IS NULL THEN
        INSERT INTO main_benchmarkscenario (name, version, description, agent_role, is_active, metadata, created_at, updated_at)
        VALUES ('Wheel Generation - Advanced', 1, 'Advanced wheel generation with complex constraints', 'mentor', true, '{"workflow_type": "wheel_generation", "difficulty": "advanced"}', NOW(), NOW())
        RETURNING id INTO scenario_advanced_id;
    END IF;

    IF scenario_resource_id IS NULL THEN
        INSERT INTO main_benchmarkscenario (name, version, description, agent_role, is_active, metadata, created_at, updated_at)
        VALUES ('Resource Analysis', 1, 'Resource analysis and planning scenario', 'resource', true, '{"workflow_type": "resource_planning", "difficulty": "medium"}', NOW(), NOW())
        RETURNING id INTO scenario_resource_id;
    END IF;

    IF scenario_psych_id IS NULL THEN
        INSERT INTO main_benchmarkscenario (name, version, description, agent_role, is_active, metadata, created_at, updated_at)
        VALUES ('Psychological Assessment', 1, 'Psychological assessment and guidance session', 'psychological', true, '{"workflow_type": "assessment", "difficulty": "medium"}', NOW(), NOW())
        RETURNING id INTO scenario_psych_id;
    END IF;

    -- Create sample benchmark runs with realistic data
    INSERT INTO main_benchmarkrun (
        agent_definition_id, scenario_id, llm_config_id, agent_version, execution_date,
        parameters, evaluator_llm_model, runs_count, mean_duration, median_duration,
        min_duration, max_duration, std_dev, success_rate, llm_calls, tool_calls,
        tool_breakdown, memory_operations, semantic_score, semantic_evaluation_details,
        semantic_evaluations, raw_results, total_input_tokens, total_output_tokens, estimated_cost
    )
    VALUES
        -- GPT-4o-mini runs (recent)
        (1, scenario_basic_id, 1, '1.0', NOW() - INTERVAL '1 hour',
         '{"context_variables": {"trust_level": 7, "mood": {"valence": 0.6, "arousal": 0.4}, "environment": {"stress_level": 3, "time_pressure": 2}}}',
         'gpt-4o-mini', 5, 2500.0, 2400.0, 1800.0, 3200.0, 450.0, 0.85, 12, 8,
         '{"get_user_profile": 2, "generate_wheel": 3, "validate_activities": 3}', 4, 0.82,
         '{"criteria": ["relevance", "creativity", "feasibility"], "scores": [0.8, 0.85, 0.8]}',
         '{"overall_score": 0.82, "detailed_scores": {"relevance": 0.8, "creativity": 0.85, "feasibility": 0.8}}',
         '{"runs": [{"duration": 2400, "success": true}, {"duration": 2600, "success": true}]}',
         1200, 800, 0.024),

    (1, 1, 1, '1.0', NOW() - INTERVAL '2 hours',
     '{"context_variables": {"trust_level": 8, "mood": {"valence": 0.7, "arousal": 0.5}, "environment": {"stress_level": 2, "time_pressure": 1}}}',
     'gpt-4o-mini', 5, 2200.0, 2100.0, 1600.0, 2800.0, 380.0, 0.90, 10, 7,
     '{"get_user_profile": 2, "generate_wheel": 2, "validate_activities": 3}', 3, 0.88,
     '{"criteria": ["relevance", "creativity", "feasibility"], "scores": [0.9, 0.85, 0.9]}',
     '{"overall_score": 0.88, "detailed_scores": {"relevance": 0.9, "creativity": 0.85, "feasibility": 0.9}}',
     '{"runs": [{"duration": 2100, "success": true}, {"duration": 2300, "success": true}]}',
     1100, 750, 0.022),

    -- GPT-4o runs (higher cost, better performance)
    (1, 2, 2, '1.0', NOW() - INTERVAL '3 hours',
     '{"context_variables": {"trust_level": 6, "mood": {"valence": 0.5, "arousal": 0.6}, "environment": {"stress_level": 4, "time_pressure": 3}}}',
     'gpt-4o', 3, 1800.0, 1750.0, 1500.0, 2100.0, 250.0, 0.95, 8, 6,
     '{"get_user_profile": 1, "generate_wheel": 2, "validate_activities": 3}', 2, 0.92,
     '{"criteria": ["relevance", "creativity", "feasibility"], "scores": [0.95, 0.9, 0.9]}',
     '{"overall_score": 0.92, "detailed_scores": {"relevance": 0.95, "creativity": 0.9, "feasibility": 0.9}}',
     '{"runs": [{"duration": 1750, "success": true}, {"duration": 1850, "success": true}]}',
     2000, 1200, 0.096),

    -- Claude-3-sonnet runs
    (2, 3, 3, '1.0', NOW() - INTERVAL '4 hours',
     '{"context_variables": {"trust_level": 7, "mood": {"valence": 0.6, "arousal": 0.3}, "environment": {"stress_level": 2, "time_pressure": 2}}}',
     'claude-3-sonnet', 4, 2800.0, 2700.0, 2200.0, 3400.0, 480.0, 0.88, 15, 10,
     '{"analyze_situation": 3, "create_strategy": 4, "validate_plan": 3}', 5, 0.85,
     '{"criteria": ["strategic_depth", "feasibility", "innovation"], "scores": [0.9, 0.8, 0.85]}',
     '{"overall_score": 0.85, "detailed_scores": {"strategic_depth": 0.9, "feasibility": 0.8, "innovation": 0.85}}',
     '{"runs": [{"duration": 2700, "success": true}, {"duration": 2900, "success": true}]}',
     1800, 1100, 0.057),

    -- Mistral runs (cost-effective)
    (3, 4, 4, '1.0', NOW() - INTERVAL '5 hours',
     '{"context_variables": {"trust_level": 8, "mood": {"valence": 0.8, "arousal": 0.4}, "environment": {"stress_level": 1, "time_pressure": 1}}}',
     'mistral-small-latest', 5, 3200.0, 3100.0, 2800.0, 3600.0, 320.0, 0.82, 18, 12,
     '{"assess_goals": 4, "provide_guidance": 6, "track_progress": 2}', 6, 0.80,
     '{"criteria": ["empathy", "actionability", "clarity"], "scores": [0.85, 0.75, 0.8]}',
     '{"overall_score": 0.80, "detailed_scores": {"empathy": 0.85, "actionability": 0.75, "clarity": 0.8}}',
     '{"runs": [{"duration": 3100, "success": true}, {"duration": 3300, "success": false}]}',
     2200, 1400, 0.036),

    -- More historical data (yesterday)
    (1, 1, 1, '1.0', NOW() - INTERVAL '1 day',
     '{"context_variables": {"trust_level": 6, "mood": {"valence": 0.4, "arousal": 0.7}, "environment": {"stress_level": 5, "time_pressure": 4}}}',
     'gpt-4o-mini', 5, 2800.0, 2750.0, 2200.0, 3400.0, 520.0, 0.75, 14, 9,
     '{"get_user_profile": 3, "generate_wheel": 3, "validate_activities": 3}', 5, 0.72,
     '{"criteria": ["relevance", "creativity", "feasibility"], "scores": [0.7, 0.75, 0.7]}',
     '{"overall_score": 0.72, "detailed_scores": {"relevance": 0.7, "creativity": 0.75, "feasibility": 0.7}}',
     '{"runs": [{"duration": 2750, "success": true}, {"duration": 2850, "success": false}]}',
     1300, 900, 0.026),

    (1, 2, 2, '1.0', NOW() - INTERVAL '1 day' - INTERVAL '2 hours',
     '{"context_variables": {"trust_level": 9, "mood": {"valence": 0.8, "arousal": 0.3}, "environment": {"stress_level": 1, "time_pressure": 1}}}',
     'gpt-4o', 3, 1600.0, 1550.0, 1400.0, 1800.0, 180.0, 0.98, 7, 5,
     '{"get_user_profile": 1, "generate_wheel": 2, "validate_activities": 2}', 2, 0.95,
     '{"criteria": ["relevance", "creativity", "feasibility"], "scores": [0.98, 0.92, 0.95]}',
     '{"overall_score": 0.95, "detailed_scores": {"relevance": 0.98, "creativity": 0.92, "feasibility": 0.95}}',
     '{"runs": [{"duration": 1550, "success": true}, {"duration": 1650, "success": true}]}',
     1900, 1150, 0.092),

    -- Week-old data for trends
    (2, 3, 3, '1.0', NOW() - INTERVAL '7 days',
     '{"context_variables": {"trust_level": 5, "mood": {"valence": 0.3, "arousal": 0.8}, "environment": {"stress_level": 6, "time_pressure": 5}}}',
     'claude-3-sonnet', 4, 3200.0, 3150.0, 2800.0, 3600.0, 380.0, 0.70, 20, 15,
     '{"analyze_situation": 5, "create_strategy": 5, "validate_plan": 5}', 8, 0.68,
     '{"criteria": ["strategic_depth", "feasibility", "innovation"], "scores": [0.7, 0.65, 0.7]}',
     '{"overall_score": 0.68, "detailed_scores": {"strategic_depth": 0.7, "feasibility": 0.65, "innovation": 0.7}}',
     '{"runs": [{"duration": 3150, "success": true}, {"duration": 3250, "success": false}]}',
     2100, 1300, 0.063),

    (3, 4, 4, '1.0', NOW() - INTERVAL '7 days' - INTERVAL '3 hours',
     '{"context_variables": {"trust_level": 7, "mood": {"valence": 0.6, "arousal": 0.5}, "environment": {"stress_level": 3, "time_pressure": 2}}}',
     'mistral-small-latest', 5, 2900.0, 2850.0, 2500.0, 3300.0, 290.0, 0.85, 16, 11,
     '{"assess_goals": 3, "provide_guidance": 5, "track_progress": 3}', 5, 0.83,
     '{"criteria": ["empathy", "actionability", "clarity"], "scores": [0.85, 0.8, 0.85]}',
     '{"overall_score": 0.83, "detailed_scores": {"empathy": 0.85, "actionability": 0.8, "clarity": 0.85}}',
     '{"runs": [{"duration": 2850, "success": true}, {"duration": 2950, "success": true}]}',
     2000, 1200, 0.032);

-- Update sequence values to avoid conflicts
SELECT setval('main_genericagent_id_seq', (SELECT MAX(id) FROM main_genericagent));
SELECT setval('main_llmconfig_id_seq', (SELECT MAX(id) FROM main_llmconfig));
SELECT setval('main_benchmarkscenario_id_seq', (SELECT MAX(id) FROM main_benchmarkscenario));
