-- Create minimal sample benchmark data for Grafana dashboards
-- This script creates just enough data to demonstrate the dashboard functionality

-- Create a simple scenario if it doesn't exist
INSERT INTO main_benchmarkscenario (name, version, description, agent_role, input_data, is_active, is_latest, metadata, created_at, updated_at)
VALUES 
    ('Demo Scenario', 1, 'Demo scenario for Grafana dashboards', 'mentor', '{"user_id": "demo-user", "context": "demo"}', true, true, '{"workflow_type": "demo", "difficulty": "basic"}', NOW(), NOW())
ON CONFLICT (name, version) DO NOTHING;

-- Create sample benchmark runs with all required fields
INSERT INTO main_benchmarkrun (
    agent_definition_id, scenario_id, llm_config_id, agent_version, execution_date, 
    parameters, evaluator_llm_model, runs_count, mean_duration, median_duration, 
    min_duration, max_duration, std_dev, success_rate, llm_calls, tool_calls, 
    tool_breakdown, memory_operations, semantic_score, semantic_evaluation_details, 
    semantic_evaluations, raw_results, total_input_tokens, total_output_tokens, estimated_cost,
    stage_performance_details
)
VALUES 
    -- Recent GPT-4o-mini run
    (1, (SELECT id FROM main_benchmarkscenario WHERE name = 'Demo Scenario' LIMIT 1), 1, '1.0', NOW() - INTERVAL '1 hour', 
     '{"context_variables": {"trust_level": 7, "mood": {"valence": 0.6, "arousal": 0.4}, "environment": {"stress_level": 3, "time_pressure": 2}}}',
     'gpt-4o-mini', 5, 2500.0, 2400.0, 1800.0, 3200.0, 450.0, 0.85, 12, 8, 
     '{"get_user_profile": 2, "generate_wheel": 3, "validate_activities": 3}', 4, 0.82,
     '{"criteria": ["relevance", "creativity", "feasibility"], "scores": [0.8, 0.85, 0.8]}',
     '{"overall_score": 0.82, "detailed_scores": {"relevance": 0.8, "creativity": 0.85, "feasibility": 0.8}}',
     '{"runs": [{"duration": 2400, "success": true}, {"duration": 2600, "success": true}]}',
     1200, 800, 0.024, '{"stages": [{"name": "initialization", "duration": 200}, {"name": "processing", "duration": 2000}, {"name": "validation", "duration": 300}]}'),
     
    -- Another GPT-4o-mini run
    (1, (SELECT id FROM main_benchmarkscenario WHERE name = 'Demo Scenario' LIMIT 1), 1, '1.0', NOW() - INTERVAL '2 hours',
     '{"context_variables": {"trust_level": 8, "mood": {"valence": 0.7, "arousal": 0.5}, "environment": {"stress_level": 2, "time_pressure": 1}}}',
     'gpt-4o-mini', 5, 2200.0, 2100.0, 1600.0, 2800.0, 380.0, 0.90, 10, 7,
     '{"get_user_profile": 2, "generate_wheel": 2, "validate_activities": 3}', 3, 0.88,
     '{"criteria": ["relevance", "creativity", "feasibility"], "scores": [0.9, 0.85, 0.9]}',
     '{"overall_score": 0.88, "detailed_scores": {"relevance": 0.9, "creativity": 0.85, "feasibility": 0.9}}',
     '{"runs": [{"duration": 2100, "success": true}, {"duration": 2300, "success": true}]}',
     1100, 750, 0.022, '{"stages": [{"name": "initialization", "duration": 180}, {"name": "processing", "duration": 1800}, {"name": "validation", "duration": 220}]}'),

    -- GPT-4o run (higher cost, better performance)
    (1, (SELECT id FROM main_benchmarkscenario WHERE name = 'Demo Scenario' LIMIT 1), 2, '1.0', NOW() - INTERVAL '3 hours',
     '{"context_variables": {"trust_level": 6, "mood": {"valence": 0.5, "arousal": 0.6}, "environment": {"stress_level": 4, "time_pressure": 3}}}',
     'gpt-4o', 3, 1800.0, 1750.0, 1500.0, 2100.0, 250.0, 0.95, 8, 6,
     '{"get_user_profile": 1, "generate_wheel": 2, "validate_activities": 3}', 2, 0.92,
     '{"criteria": ["relevance", "creativity", "feasibility"], "scores": [0.95, 0.9, 0.9]}',
     '{"overall_score": 0.92, "detailed_scores": {"relevance": 0.95, "creativity": 0.9, "feasibility": 0.9}}',
     '{"runs": [{"duration": 1750, "success": true}, {"duration": 1850, "success": true}]}',
     2000, 1200, 0.096, '{"stages": [{"name": "initialization", "duration": 150}, {"name": "processing", "duration": 1500}, {"name": "validation", "duration": 200}]}'),

    -- Claude-3-sonnet run
    (2, (SELECT id FROM main_benchmarkscenario WHERE name = 'Demo Scenario' LIMIT 1), 3, '1.0', NOW() - INTERVAL '4 hours',
     '{"context_variables": {"trust_level": 7, "mood": {"valence": 0.6, "arousal": 0.3}, "environment": {"stress_level": 2, "time_pressure": 2}}}',
     'claude-3-sonnet', 4, 2800.0, 2700.0, 2200.0, 3400.0, 480.0, 0.88, 15, 10,
     '{"analyze_situation": 3, "create_strategy": 4, "validate_plan": 3}', 5, 0.85,
     '{"criteria": ["strategic_depth", "feasibility", "innovation"], "scores": [0.9, 0.8, 0.85]}',
     '{"overall_score": 0.85, "detailed_scores": {"strategic_depth": 0.9, "feasibility": 0.8, "innovation": 0.85}}',
     '{"runs": [{"duration": 2700, "success": true}, {"duration": 2900, "success": true}]}',
     1800, 1100, 0.057, '{"stages": [{"name": "initialization", "duration": 250}, {"name": "processing", "duration": 2200}, {"name": "validation", "duration": 350}]}'),

    -- Mistral run (cost-effective)
    (5, (SELECT id FROM main_benchmarkscenario WHERE name = 'Demo Scenario' LIMIT 1), 4, '1.0', NOW() - INTERVAL '5 hours',
     '{"context_variables": {"trust_level": 8, "mood": {"valence": 0.8, "arousal": 0.4}, "environment": {"stress_level": 1, "time_pressure": 1}}}',
     'mistral-small-latest', 5, 3200.0, 3100.0, 2800.0, 3600.0, 320.0, 0.82, 18, 12,
     '{"assess_goals": 4, "provide_guidance": 6, "track_progress": 2}', 6, 0.80,
     '{"criteria": ["empathy", "actionability", "clarity"], "scores": [0.85, 0.75, 0.8]}',
     '{"overall_score": 0.80, "detailed_scores": {"empathy": 0.85, "actionability": 0.75, "clarity": 0.8}}',
     '{"runs": [{"duration": 3100, "success": true}, {"duration": 3300, "success": false}]}',
     2200, 1400, 0.036, '{"stages": [{"name": "initialization", "duration": 300}, {"name": "processing", "duration": 2600}, {"name": "validation", "duration": 400}]}'),

    -- Historical data (yesterday)
    (1, (SELECT id FROM main_benchmarkscenario WHERE name = 'Demo Scenario' LIMIT 1), 1, '1.0', NOW() - INTERVAL '1 day',
     '{"context_variables": {"trust_level": 6, "mood": {"valence": 0.4, "arousal": 0.7}, "environment": {"stress_level": 5, "time_pressure": 4}}}',
     'gpt-4o-mini', 5, 2800.0, 2750.0, 2200.0, 3400.0, 520.0, 0.75, 14, 9,
     '{"get_user_profile": 3, "generate_wheel": 3, "validate_activities": 3}', 5, 0.72,
     '{"criteria": ["relevance", "creativity", "feasibility"], "scores": [0.7, 0.75, 0.7]}',
     '{"overall_score": 0.72, "detailed_scores": {"relevance": 0.7, "creativity": 0.75, "feasibility": 0.7}}',
     '{"runs": [{"duration": 2750, "success": true}, {"duration": 2850, "success": false}]}',
     1300, 900, 0.026, '{"stages": [{"name": "initialization", "duration": 250}, {"name": "processing", "duration": 2300}, {"name": "validation", "duration": 300}]}'),

    -- Week-old data for trends
    (2, (SELECT id FROM main_benchmarkscenario WHERE name = 'Demo Scenario' LIMIT 1), 3, '1.0', NOW() - INTERVAL '7 days',
     '{"context_variables": {"trust_level": 5, "mood": {"valence": 0.3, "arousal": 0.8}, "environment": {"stress_level": 6, "time_pressure": 5}}}',
     'claude-3-sonnet', 4, 3200.0, 3150.0, 2800.0, 3600.0, 380.0, 0.70, 20, 15,
     '{"analyze_situation": 5, "create_strategy": 5, "validate_plan": 5}', 8, 0.68,
     '{"criteria": ["strategic_depth", "feasibility", "innovation"], "scores": [0.7, 0.65, 0.7]}',
     '{"overall_score": 0.68, "detailed_scores": {"strategic_depth": 0.7, "feasibility": 0.65, "innovation": 0.7}}',
     '{"runs": [{"duration": 3150, "success": true}, {"duration": 3250, "success": false}]}',
     2100, 1300, 0.063, '{"stages": [{"name": "initialization", "duration": 300}, {"name": "processing", "duration": 2650}, {"name": "validation", "duration": 350}]}');
