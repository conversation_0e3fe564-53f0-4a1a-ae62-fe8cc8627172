#!/bin/bash

# Grafana Integration Setup Script
# This script sets up the complete Grafana integration for benchmark visualization

set -e

echo "🚀 Setting up Grafana Integration for Benchmark Visualization"
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "backend/docker-compose.yml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Step 1: Create necessary directories
print_status "Creating Grafana and Prometheus directory structure..."
mkdir -p monitoring/grafana/provisioning/datasources
mkdir -p monitoring/grafana/provisioning/dashboards
mkdir -p monitoring/grafana/dashboards/benchmark-analytics
mkdir -p monitoring/grafana/dashboards/llm-performance
mkdir -p monitoring/grafana/dashboards/prompt-engineering
mkdir -p monitoring/grafana/dashboards/contextual-evaluation
mkdir -p monitoring/prometheus

# Create provisioning files
cat <<EOF > monitoring/grafana/provisioning/datasources/datasources.yaml
apiVersion: 1

datasources:
  - name: PostgreSQL
    type: postgres
    url: db:5432
    database: mydb
    user: postgres
    password: postgres
    isDefault: true
    editable: true
    jsonData:
      sslmode: 'disable' # Use 'require' or 'verify-full' for production
      maxOpenConns: 0
      maxIdleConns: 0
      connMaxLifetime: 14400
    version: 1
    readOnly: false
EOF
# Create dashboard provisioning configuration
cat <<EOF > monitoring/grafana/provisioning/dashboards/dashboards.yaml
apiVersion: 1

providers:
  - name: 'Benchmark Analytics'
    orgId: 1
    folder: 'Benchmark Analytics'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/benchmark-analytics

  - name: 'LLM Performance'
    orgId: 1
    folder: 'LLM Performance'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/llm-performance

  - name: 'Prompt Engineering'
    orgId: 1
    folder: 'Prompt Engineering'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/prompt-engineering

  - name: 'Contextual Evaluation'
    orgId: 1
    folder: 'Contextual Evaluation'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/contextual-evaluation
EOF

touch monitoring/grafana/provisioning/notifiers.yaml
touch monitoring/grafana/provisioning/plugins.yaml

print_success "Directory structure created"

# Step 1.5: Create default prometheus.yml if it doesn't exist
if [ ! -f "monitoring/prometheus/prometheus.yml" ]; then
    print_status "Creating default prometheus.yml..."
    cat <<EOF > monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
EOF
    print_success "Default prometheus.yml created"
else
    print_status "prometheus.yml already exists, skipping creation"
fi

# Step 2: Check if services are running
print_status "Checking Docker services..."
cd backend

# Ensure a clean Docker environment by stopping and removing existing services and volumes
print_status "Cleaning up existing Docker services and volumes..."
docker-compose down --volumes --remove-orphans || true # Use || true to prevent script from exiting if no services are running
print_success "Docker environment cleaned up."

# Explicitly remove Grafana data volume to ensure a clean state
print_status "Removing Grafana data volume to ensure clean state..."
docker volume rm backend_grafana_data || true # Use || true to prevent script from exiting if volume doesn't exist
print_success "Grafana data volume removed (if it existed)."

if ! docker-compose ps | grep -q "Up"; then
    print_warning "Docker services not running. Starting required services..."
    docker-compose up -d db redis

    # Wait for database to be ready
    print_status "Waiting for database to be ready..."
    sleep 10

    # Check database health
    for i in {1..30}; do
        if docker-compose exec -T db pg_isready -U postgres > /dev/null 2>&1; then
            print_success "Database is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "Database failed to start within 30 seconds"
            exit 1
        fi
        sleep 1
    done
fi

# Step 3: Apply database migrations
print_status "Applying database migrations for analytics views..."
if docker-compose run --rm web python manage.py migrate; then
    print_success "Database migrations applied successfully"
else
    print_error "Failed to apply database migrations"
    exit 1
fi

# Step 4: Start Grafana and Prometheus
print_status "Starting Grafana and Prometheus services..."
if docker-compose up -d grafana prometheus; then
    print_success "Grafana and Prometheus services started"
else
    print_error "Failed to start Grafana services"
    exit 1
fi

# Step 5: Wait for Grafana to be ready
print_status "Waiting for Grafana to be ready..."
for i in {1..60}; do
    if curl -s http://localhost:3000/api/health > /dev/null 2>&1; then
        print_success "Grafana is ready"
        break
    fi
    if [ $i -eq 60 ]; then
        print_error "Grafana failed to start within 60 seconds"
        exit 1
    fi
    sleep 1
done

# Step 6: Verify data source connection
print_status "Verifying PostgreSQL data source connection..."
sleep 5  # Give Grafana time to load provisioning

# Step 7: Check if sample data exists and create if needed
print_status "Checking for benchmark data..."
BENCHMARK_COUNT=$(docker-compose exec -T db psql -U postgres -d mydb -t -c "SELECT COUNT(*) FROM main_benchmarkrun;" 2>/dev/null | tr -d ' \n' || echo "0")

if [ "$BENCHMARK_COUNT" -eq "0" ]; then
    print_warning "No benchmark data found. Creating sample data for dashboard demonstration..."

    # Copy sample data script to container
    docker cp ../scripts/create_minimal_sample_data.sql backend-db-1:/tmp/minimal_sample_data.sql

    # Execute sample data script
    if docker-compose exec -T db psql -U postgres -d mydb -f /tmp/minimal_sample_data.sql > /dev/null 2>&1; then
        print_success "Sample benchmark data created successfully"

        # Verify data was created
        NEW_COUNT=$(docker-compose exec -T db psql -U postgres -d mydb -t -c "SELECT COUNT(*) FROM main_benchmarkrun;" 2>/dev/null | tr -d ' \n' || echo "0")
        print_success "Created $NEW_COUNT sample benchmark runs"

        # Check views have data
        VIEW_COUNT=$(docker-compose exec -T db psql -U postgres -d mydb -t -c "SELECT COUNT(*) FROM grafana_llm_performance;" 2>/dev/null | tr -d ' \n' || echo "0")
        print_success "Grafana views populated with $VIEW_COUNT records"
    else
        print_error "Failed to create sample data"
        print_status "You can manually run benchmarks using: docker-compose run --rm web python manage.py run_benchmarks"
    fi
else
    print_success "Found $BENCHMARK_COUNT benchmark runs in database"

    # Check if views have data
    VIEW_COUNT=$(docker-compose exec -T db psql -U postgres -d mydb -t -c "SELECT COUNT(*) FROM grafana_llm_performance;" 2>/dev/null | tr -d ' \n' || echo "0")
    if [ "$VIEW_COUNT" -gt "0" ]; then
        print_success "Grafana views contain $VIEW_COUNT records"
    else
        print_warning "Grafana views are empty - data may not be compatible with view structure"
    fi
fi

# Step 8: Display access information
echo ""
echo "🎉 Grafana Integration Setup Complete!"
echo "======================================"
echo ""
echo "📊 Access Information:"
echo "  Grafana URL: http://localhost:3000"
echo "  Username: admin"
echo "  Password: admin"
echo ""
echo "📈 Available Dashboards:"
echo "  • LLM Performance Overview"
echo "  • Contextual Evaluation Insights"
echo "  • Cost Analytics Dashboard"
echo "  • Prompt Effectiveness Dashboard"
echo ""
echo "🔧 Services Running:"
echo "  • Grafana: http://localhost:3000"
echo "  • Prometheus: http://localhost:9090"
echo "  • PostgreSQL: localhost:5432"
echo ""
echo "📚 Documentation:"
echo "  • Integration Guide: docs/backend/GRAFANA_INTEGRATION.md"
echo "  • Contextual Evaluation: docs/backend/CONTEXTUAL_EVALUATION_SYSTEM.md"
echo ""

# Step 9: Optional - Open Grafana in browser
if command -v xdg-open > /dev/null 2>&1; then
    read -p "Would you like to open Grafana in your browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        xdg-open http://localhost:3000
    fi
elif command -v open > /dev/null 2>&1; then
    read -p "Would you like to open Grafana in your browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open http://localhost:3000
    fi
fi

print_success "Setup completed successfully!"

# Return to original directory
cd ..

echo ""
echo "🚀 Next Steps:"
echo "1. Access Grafana at http://localhost:3000"
echo "2. Explore the pre-built dashboards"
echo "3. Run benchmarks to see live data"
echo "4. Customize dashboards for your specific needs"
echo ""
echo "Happy analyzing! 📊✨"
