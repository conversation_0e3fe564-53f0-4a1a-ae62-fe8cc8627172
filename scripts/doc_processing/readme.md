# Documentation Processing Tools

## Overview

This directory contains a suite of Python scripts designed to inventory and analyze markdown documentation files within the repository. The primary goals are to:
1.  **Inventory Documents**: Scan all markdown files, extract metadata (including frontmatter and git history), and create a comprehensive index.
2.  **Analyze Documents**: Perform various analyses on the indexed documents to identify duplicates, assess staleness, check structure, analyze links, understand audience distribution, and more.

The system is designed to be modular, with core functionalities and specific analyzers separated for better maintainability and extensibility.
- For more details on the architecture and refactoring, see `knowledge.md`.
- For future plans and enhancements, see `roadmap.md`.

## Prerequisites

- Python 3.x
- Git (must be installed and in the system PATH for `doc_inventory.py` to fetch commit history)
- The following Python packages (consider creating a `requirements.txt` for this directory):
    - `PyYAML` (dependency for `frontmatter`)
    - `frontmatter`
    - `numpy`
    - `matplotlib` (optional, for visualizations)
    - `networkx` (optional, for visualizations)
    - `scikit-learn` (optional, for some analysis and visualization features)
    - `textstat` (optional, for readability analysis)
    - `openai` (optional, for OpenAI embeddings)
    - `tiktoken` (optional, for OpenAI token counting)
    - `sentence-transformers` (optional, for local sentence embeddings)

You can typically install these using pip:
```bash
pip install PyYAML frontmatter numpy matplotlib networkx scikit-learn textstat openai tiktoken sentence-transformers
```

## Scripts

### 1. `doc_inventory.py`

-   **Purpose**: Scans the repository for markdown files (excluding specified directories), extracts metadata, titles, section structures, TODOs, obsolescence markers, internal links, and detailed git commit history for each file. It also attempts to infer audience and check for code synchronization status.
-   **Usage**:
    ```bash
    python scripts/doc_processing/doc_inventory.py
    ```
    This script should be run from the root of the repository.
-   **Output**: Creates a `doc_index.json` file in the root of the repository, containing an array of metadata objects for each processed markdown file.

### 2. `inventory_analyzer.py`

-   **Purpose**: Takes the `doc_index.json` (or another specified inventory file) as input and performs a comprehensive analysis. This includes finding exact duplicates, similar content, conceptual similarities, document clusters, structural issues, staleness, link issues, audience distribution, maintenance patterns, documentation location patterns, metadata completeness, document types, and adherence to documentation standards. It can generate reports in Markdown or JSON format and create visualizations.
-   **Usage**:
    ```bash
    python scripts/doc_processing/inventory_analyzer.py --inventory <path_to_doc_index.json> [options]
    ```
    This script should also be run from the root of the repository.
-   **Key Options**:
    -   `--inventory <file>`: Path to the document inventory JSON file (default: `doc_index.json`).
    -   `--output <basename>`: Base name for the output report file (default: `analysis_report`).
    -   `--format <md|json>`: Output report format (default: `md`).
    -   `--visualize`: Generate visualizations (e.g., document graph, audience chart).
    -   `--visual-dir <directory>`: Directory to save visualizations (default: `./visualizations` in the current working directory).
    -   `--stale-days <days>`: Threshold in days for considering a document stale (default: 180).
    -   `--sync-days <days>`: Threshold in days for considering a document out-of-sync with code (default: 90).
    -   `--workers <num>`: Number of worker processes/threads for parallel tasks.
    -   `--exclude-dirs <dir1> <dir2> ...`: List of directories to exclude from analysis.
    -   `--use-default-excludes`: Use a predefined list of common directories to exclude.
    -   `--skip-metadata`, `--skip-location`, `--skip-doc-types`, `--skip-standards`: Flags to skip specific analysis modules.
-   **Output**:
    -   A report file (e.g., `analysis_report.md` or `analysis_report.json`) in the root of the repository.
    -   If `--visualize` is used, visualizations are saved in the specified `--visual-dir` (default: `visualizations/` in the root).

## Workflow

1.  **Generate Document Inventory**:
    Run `doc_inventory.py` to scan all markdown files and create `doc_index.json`.
    ```bash
    python scripts/doc_processing/doc_inventory.py
    ```
2.  **Analyze Inventory**:
    Run `inventory_analyzer.py` using the `doc_index.json` generated in the previous step.
    ```bash
    # Example: Generate Markdown report and visualizations
    python scripts/doc_processing/inventory_analyzer.py --inventory doc_index.json --output analysis_report --format md --visualize

    # Example: Generate JSON report, excluding 'frontend' and using default excludes
    python scripts/doc_processing/inventory_analyzer.py --inventory doc_index.json --output analysis_report_data --format json --exclude-dirs frontend --use-default-excludes
    ```

## Convenience Bash Scripts

To simplify the execution of these tools, the following bash scripts are provided within this directory:

-   `run_inventory.sh`: A simple script to execute `doc_inventory.py`.
-   `run_analysis.sh`: Executes `inventory_analyzer.py` with common default options (Markdown report, visualizations).
-   `run_full_pipeline.sh`: Executes both `run_inventory.sh` and then `run_analysis.sh` sequentially.

These scripts are intended to be run from the root of the repository, e.g., `./scripts/doc_processing/run_inventory.sh`.
