#!/bin/bash

# <PERSON>ript to run the full document processing pipeline:
# 1. Run document inventory
# 2. Run document analysis
# This script should be executed from the root of the repository,
# or it will navigate to the root assuming it's in scripts/doc_processing/

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT_DIR="$( cd "$SCRIPT_DIR/../.." && pwd )" # Navigate two levels up to the repo root

echo "Changing to repository root: $REPO_ROOT_DIR"
cd "$REPO_ROOT_DIR" || exit 1

echo "------------------------------------"
echo "Step 1: Running Document Inventory"
echo "------------------------------------"
# Assuming run_inventory.sh is in the same directory as this script
"$SCRIPT_DIR/run_inventory.sh"

# Check if doc_index.json was created
if [ ! -f "doc_index.json" ]; then
    echo "Error: doc_index.json not found after running inventory script. Aborting."
    exit 1
fi

echo ""
echo "------------------------------------"
echo "Step 2: Running Document Analysis"
echo "------------------------------------"
# Assuming run_analysis.sh is in the same directory as this script
"$SCRIPT_DIR/run_analysis.sh"

echo ""
echo "------------------------------------"
echo "Full documentation pipeline finished."
echo "------------------------------------"
echo "Check for doc_index.json, analysis_report.md, and the 'visualizations' directory in the repository root."
