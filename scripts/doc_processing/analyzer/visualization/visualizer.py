"""
Handles visualization generation for the documentation analyzer.
"""
import logging
import os
import numpy as np
from typing import List, Dict, Any

# Assuming necessary constants and classes are available or passed
# Import constants that are directly used.
from ..core.constants import READABILITY_THRESHOLDS

# Optional imports - gracefully handle if not available
try:
    import matplotlib.pyplot as plt
    import networkx as nx
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.cluster import DBSCAN
    HAS_VISUALIZATION_LIBS = True # Renamed from HAS_VISUALIZATION to avoid conflict
except ImportError:
    HAS_VISUALIZATION_LIBS = False

logger = logging.getLogger(__name__)

def create_visualizations(
    docs_data: List[Dict[str, Any]],
    analysis_results: Dict[str, Any],
    doc_store: Any,
    output_dir: str = "./visualizations"
) -> bool:
    """
    Create visualizations of the documentation structure and relationships.

    Args:
        docs_data: A list of document dictionaries.
        analysis_results: Dictionary containing results from various analyses.
        doc_store: An instance of CachedDocumentStore.
        output_dir: Directory to save the visualizations.

    Returns:
        True if visualizations were created, False otherwise.
    """
    if not HAS_VISUALIZATION_LIBS:
        logger.warning("Visualizations not available: Missing matplotlib, networkx or scikit-learn")
        return False

    logger.info("Generating visualizations...")

    # Create output directory if needed
    os.makedirs(output_dir, exist_ok=True)

    # 1. Create document relationship graph
    _create_document_graph(docs_data, analysis_results, output_dir)

    # 2. Create audience distribution chart
    _create_audience_chart(analysis_results, output_dir)

    # 3. Create staleness heatmap
    _create_staleness_heatmap(docs_data, output_dir)

    # 4. Create cluster visualization (if available)
    if "document_clusters" in analysis_results:
        _create_cluster_visualization(analysis_results, output_dir)

    return True

def _create_document_graph(docs_data: List[Dict[str, Any]], analysis_results: Dict[str, Any], output_dir: str):
    """Create a graph visualization of document relationships."""
    logger.info("Creating document relationship graph...")

    # Create a new graph
    G = nx.DiGraph()

    # Add nodes (documents)
    for doc_item in docs_data:
        path = doc_item["path"]
        title = doc_item.get("title", os.path.basename(path))
        audience = doc_item.get("audience", "unknown")

        # Add node with attributes
        G.add_node(path, title=title, audience=audience)

    # Add edges (links between documents)
    # Use the links analysis results if available, otherwise iterate through docs_data
    links_results = analysis_results.get("links", {})
    if links_results and links_results.get("outgoing_links"):
        outgoing_links = links_results["outgoing_links"]
        for source_path, targets in outgoing_links.items():
            for target in targets:
                 # Check if both source and target nodes exist in the graph before adding edge
                 if source_path in G and target in G:
                    G.add_edge(source_path, target)
                 elif target not in G:
                     logger.debug(f"Skipping edge from {source_path} to {target}: target node not found.")
                 elif source_path not in G:
                      logger.debug(f"Skipping edge from {source_path} to {target}: source node not found.")

    else:
        # Fallback if links analysis results are not available
        logger.warning("Links analysis results not available for graph visualization. Using raw internal_links.")
        # Build a mapping of paths to docs for quick lookup
        path_to_doc = {doc_item["path"]: doc_item for doc_item in docs_data}
        for doc_item in docs_data:
            source_path = doc_item["path"]
            for link in doc_item.get("internal_links", []):
                 # Simple normalization (may need refinement)
                 target_path = link.replace('/', os.sep).replace('\\', os.sep)
                 if source_path in G and target_path in G:
                     G.add_edge(source_path, target_path)
                 elif target_path not in G:
                     logger.debug(f"Skipping edge from {source_path} to {target_path}: target node not found (fallback).")
                 elif source_path not in G:
                      logger.debug(f"Skipping edge from {source_path} to {target_path}: source node not found (fallback).")


    # Create figure
    plt.figure(figsize=(12, 10))

    # Set node colors based on audience
    audience_colors = {
        "human": "skyblue",
        "ai": "lightgreen",
        "unknown": "lightgray"
    }

    node_colors = [audience_colors.get(G.nodes[n].get("audience", "unknown"), "lightgray") for n in G.nodes] # Use get with default

    # Create positions (layout)
    pos = nx.spring_layout(G, k=0.3)

    # Draw the graph
    nx.draw_networkx_nodes(G, pos, node_color=node_colors, alpha=0.8, node_size=100)
    nx.draw_networkx_edges(G, pos, edge_color="gray", alpha=0.5, arrows=True, arrowsize=10)

    # Draw labels for important nodes only (hubs and authorities)
    important_nodes = set()

    # Add hubs (nodes with many outgoing edges) - check if G.out_degree is not empty
    if G.out_degree:
        for node, out_degree in sorted(G.out_degree, key=lambda x: x[1], reverse=True)[:10]:
            if out_degree > 1:
                important_nodes.add(node)

    # Add authorities (nodes with many incoming edges) - check if G.in_degree is not empty
    if G.in_degree:
        for node, in_degree in sorted(G.in_degree, key=lambda x: x[1], reverse=True)[:10]:
            if in_degree > 1:
                important_nodes.add(node)

    # Create a subset of labels for important nodes
    labels = {node: G.nodes[node].get("title", os.path.basename(node)) for node in important_nodes} # Use get with default
    nx.draw_networkx_labels(G, pos, labels=labels, font_size=8)

    # Add legend
    legend_elements = [
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=color, label=audience, markersize=10)
        for audience, color in audience_colors.items()
    ]
    plt.legend(handles=legend_elements, title="Audience")

    # Set title and remove axes
    plt.title("Document Relationship Graph")
    plt.axis("off")

    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "document_graph.png"), dpi=300)
    plt.close()

def _create_audience_chart(analysis_results: Dict[str, Any], output_dir: str):
    """Create a chart showing audience distribution."""
    logger.info("Creating audience distribution chart...")

    # Get audience data
    audience_counts = analysis_results.get("audience", {}).get("audience_counts", {})

    if not audience_counts:
        logger.warning("No audience data available for chart.")
        return

    # Create figure
    plt.figure(figsize=(10, 6))

    # Create pie chart
    labels = list(audience_counts.keys())
    sizes = list(audience_counts.values())
    colors = ['skyblue', 'lightgreen', 'lightgray', 'gold', 'lightcoral'] # Example colors

    # Ensure colors list is long enough or cycle colors
    num_colors = len(labels)
    if num_colors > len(colors):
        # Cycle through existing colors or generate more
        cycled_colors = [colors[i % len(colors)] for i in range(num_colors)]
    else:
        cycled_colors = colors[:num_colors]


    plt.pie(sizes, labels=labels, colors=cycled_colors, autopct='%1.1f%%', startangle=90)
    plt.axis('equal')
    plt.title('Documentation by Audience')

    # Save figure
    plt.savefig(os.path.join(output_dir, "audience_distribution.png"), dpi=300)
    plt.close()

    # Create directory-specific audience charts
    directory_audience = analysis_results.get("audience", {}).get("directory_audience", {})

    if directory_audience:
        # Filter to directories with multiple documents (using doc_count from dir_stats if available)
        dir_stats = analysis_results.get("doc_location", {}).get("dir_stats", {})
        significant_dirs = {dir_name: counts for dir_name, counts in directory_audience.items()
                         if dir_stats.get(dir_name, {}).get("doc_count", 0) >= 5} # Use doc_count from dir_stats

        if significant_dirs:
            # Determine number of subplots
            n_dirs = len(significant_dirs)
            n_cols = min(3, n_dirs)
            n_rows = (n_dirs + n_cols - 1) // n_cols

            plt.figure(figsize=(12, n_rows * 4)) # Adjust figure size based on number of rows

            # Create subplots
            for i, (dir_name, counts) in enumerate(significant_dirs.items(), 1):
                plt.subplot(n_rows, n_cols, i)

                labels = list(counts.keys())
                sizes = list(counts.values())

                # Ensure colors list is long enough or cycle colors for subplots
                num_colors_subplot = len(labels)
                if num_colors_subplot > len(colors):
                    cycled_colors_subplot = [colors[j % len(colors)] for j in range(num_colors_subplot)]
                else:
                    cycled_colors_subplot = colors[:num_colors_subplot]

                plt.pie(sizes, labels=labels, colors=cycled_colors_subplot, autopct='%1.1f%%', startangle=90)
                plt.axis('equal')
                plt.title(dir_name, fontsize=10)

            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, "directory_audience.png"), dpi=300)
            plt.close()

def _create_staleness_heatmap(docs_data: List[Dict[str, Any]], output_dir: str):
    """Create a heatmap showing document staleness."""
    logger.info("Creating document staleness heatmap...")

    # Collect staleness data by directory
    dir_staleness = defaultdict(list)

    for doc_item in docs_data:
        path = doc_item["path"]
        dir_name = os.path.dirname(path)
        days_stale = doc_item.get("git_history", {}).get("time_since_last_update", 0)

        if days_stale is not None: # Ensure days_stale is not None
            dir_staleness[dir_name].append(days_stale)

    # Filter to directories with multiple documents
    significant_dirs = {dir_name: staleness for dir_name, staleness in dir_staleness.items()
                     if len(staleness) >= 3}

    if not significant_dirs:
        logger.warning("No significant directories with staleness data for heatmap.")
        return

    # Calculate average staleness by directory
    avg_staleness = {dir_name: sum(days)/len(days) for dir_name, days in significant_dirs.items() if len(days) > 0} # Avoid division by zero

    # Sort directories by average staleness
    sorted_dirs = sorted(avg_staleness.items(), key=lambda x: x[1], reverse=True)

    # Create figure
    plt.figure(figsize=(12, 8))

    # Create bar chart
    labels = [dir_name for dir_name, _ in sorted_dirs]
    values = [staleness for _, staleness in sorted_dirs]

    # Shorten directory names for display
    display_labels = [dir_name[-20:] if len(dir_name) > 20 else dir_name for dir_name in labels]

    # Create colormap based on staleness
    # Ensure values is not empty before calculating max
    if values:
        colors = plt.cm.YlOrRd(np.array(values) / max(values))
    else:
        colors = 'gray' # Default color if no values


    # Create bar chart
    bars = plt.barh(display_labels, values, color=colors)

    # Add value labels
    for bar, value in zip(bars, values):
        plt.text(bar.get_width() + 5, bar.get_y() + bar.get_height()/2,
                f"{int(value)} days", va='center')

    # Set labels and title
    plt.xlabel('Average Days Since Last Update')
    plt.title('Documentation Staleness by Directory')

    # Invert y-axis to have most stale at top
    plt.gca().invert_yaxis()

    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "staleness_heatmap.png"), dpi=300)
    plt.close()

def _create_cluster_visualization(analysis_results: Dict[str, Any], output_dir: str):
    """Create a visualization of document clusters."""
    logger.info("Creating document cluster visualization...")

    # Get cluster data
    clusters = analysis_results.get("document_clusters", {}).get("clusters", [])

    if not clusters:
        logger.warning("No document cluster data available for visualization.")
        return

    # Create figure
    plt.figure(figsize=(12, 8))

    # Create scatter plot
    x = []
    y = []
    colors = []
    labels = []

    # Generate a color map
    # Ensure there are clusters before getting cmap
    if clusters:
        cmap = plt.cm.get_cmap('tab10', len(clusters))
    else:
        logger.warning("No clusters to generate colormap.")
        return


    for i, cluster in enumerate(clusters):
        # For each document in the cluster
        for doc in cluster["documents"]:
            # For visualization purposes, use random positions (these will be adjusted by layout algorithm)
            x.append(np.random.random())
            y.append(np.random.random())
            colors.append(cmap(i))
            labels.append(os.path.basename(doc["path"]))

    # Create scatter plot
    plt.scatter(x, y, c=colors, alpha=0.7)

    # Add labels for a sample of points
    if len(x) > 20:
        # Show only some labels to avoid overcrowding
        sample_indices = np.random.choice(range(len(x)), size=min(20, len(x)), replace=False) # Ensure sample size is not larger than data size
        for i in sample_indices:
            plt.annotate(labels[i], (x[i], y[i]), fontsize=8)
    else:
        # Show all labels if there are few points
        for i in range(len(x)):
            plt.annotate(labels[i], (x[i], y[i]), fontsize=8)

    # Set title
    plt.title(f"Document Clusters ({len(clusters)} clusters)")

    # Remove axes
    plt.axis("off")

    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "document_clusters.png"), dpi=300)
    plt.close()
