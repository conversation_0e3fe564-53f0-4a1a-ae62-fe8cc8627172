"""
Caching mechanisms for the documentation analyzer.
"""

import os
import re
import hashlib
import datetime
import logging
from typing import Dict, List, Optional
import numpy as np

# Local imports
from .dataclasses import DocumentSummary
from .constants import DOC_TYPE_PATTERNS, EXPECTED_METADATA_FIELDS

# Optional imports - gracefully handle if not available
try:
    # Local embedding models (sentence-transformers is much faster than OpenAI API)
    from sentence_transformers import SentenceTransformer
    HAS_LOCAL_EMBEDDINGS = True
except ImportError:
    HAS_LOCAL_EMBEDDINGS = False

# Configure logging
logger = logging.getLogger('analyzer.core.cache') # More specific logger name

class CachedDocumentStore:
    """Efficiently caches document content and computed values"""

    def __init__(self):
        self.content_cache: Dict[str, str] = {}  # path -> content
        self.embedding_cache: Dict[str, np.ndarray] = {}  # path -> embedding
        self.summary_cache: Dict[str, DocumentSummary] = {}  # path -> DocumentSummary
        self.embedding_model: Optional[SentenceTransformer] = None # Type hint for clarity

    def get_content(self, path: str) -> str:
        """Get document content, using cache if available"""
        if path not in self.content_cache:
            try:
                with open(path, 'r', encoding='utf-8', errors='replace') as f:
                    self.content_cache[path] = f.read()
            except Exception as e:
                logger.warning(f"Error reading {path}: {e}")
                self.content_cache[path] = ""
        return self.content_cache[path]

    def get_embedding(self, path: str) -> Optional[np.ndarray]:
        """Get document embedding, computing if needed"""
        if path not in self.embedding_cache and self.embedding_model is not None:
            content = self.get_content(path)
            if content:
                try:
                    self.embedding_cache[path] = self.embedding_model.encode(content)
                except Exception as e:
                    logger.warning(f"Error generating embedding for {path}: {e}")
                    # Return None or a zero vector if preferred for error handling
                    self.embedding_cache[path] = np.zeros(self.embedding_model.get_sentence_embedding_dimension() if hasattr(self.embedding_model, 'get_sentence_embedding_dimension') else 384) # Default size
            else:
                # Return None or zero vector for empty content
                self.embedding_cache[path] = np.zeros(self.embedding_model.get_sentence_embedding_dimension() if hasattr(self.embedding_model, 'get_sentence_embedding_dimension') else 384)
        
        return self.embedding_cache.get(path)

    def get_summary(self, path: str, doc_info: Dict) -> DocumentSummary:
        """Get document summary, computing if needed"""
        if path not in self.summary_cache:
            content = self.get_content(path)
            headings = re.findall(r'^(#{2,6}) (.+)$', content, re.MULTILINE)
            heading_texts = [h[1] for h in headings]
            content_digest = hashlib.md5(content.encode('utf-8')).hexdigest()
            word_count = len(content.split())

            try:
                file_size = os.path.getsize(path)
            except Exception:
                file_size = 0

            update_time = None
            if doc_info.get('git_history') and doc_info['git_history'].get('last_modified_date'):
                try:
                    update_time_str = doc_info['git_history']['last_modified_date']
                    # Attempt to parse with common formats, be more robust
                    for fmt in ('%a %b %d %H:%M:%S %Y %z', '%Y-%m-%dT%H:%M:%S%z', '%Y-%m-%d %H:%M:%S%z'):
                        try:
                            update_time = datetime.datetime.strptime(update_time_str, fmt)
                            break
                        except (ValueError, TypeError):
                            continue
                    if update_time is None and update_time_str: # Log if still None after trying formats
                        logger.debug(f"Could not parse update_time '{update_time_str}' for {path}")
                except Exception as e: # Catch any other unexpected errors during parsing
                    logger.debug(f"Error parsing update_time for {path}: {e}")


            metadata_fields = {}
            description = ""
            tags = []
            author = ""
            created_date = None
            version = ""

            frontmatter_match = re.search(r'^---\s*\n(.*?)\n---\s*\n', content, re.DOTALL)
            if frontmatter_match:
                frontmatter = frontmatter_match.group(1)
                for line in frontmatter.split('\n'):
                    if ':' in line:
                        key_val = line.split(':', 1)
                        key = key_val[0].strip().lower()
                        value = key_val[1].strip() if len(key_val) > 1 else ""
                        metadata_fields[key] = value

                        if key == 'description':
                            description = value
                        elif key == 'tags' or key == 'keywords':
                            tags = [tag.strip() for tag in value.split(',') if tag.strip()]
                        elif key == 'author':
                            author = value
                        elif key == 'date' or key == 'created_date':
                            try:
                                # Be more flexible with date parsing
                                for fmt in ('%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%dT%H:%M:%S%z'):
                                    try:
                                        created_date = datetime.datetime.strptime(value, fmt)
                                        break
                                    except ValueError:
                                        continue
                                if created_date is None and value:
                                     logger.debug(f"Could not parse created_date '{value}' for {path} with known formats.")
                            except Exception as e:
                                logger.debug(f"Error parsing created_date for {path}: {e}")
                        elif key == 'version':
                            version = value
            
            metadata_score = 0.0
            if metadata_fields and EXPECTED_METADATA_FIELDS: # Ensure EXPECTED_METADATA_FIELDS is not empty
                present_fields = set(metadata_fields.keys())
                expected_fields_set = set(EXPECTED_METADATA_FIELDS)
                metadata_score = len(present_fields.intersection(expected_fields_set)) / len(expected_fields_set) if expected_fields_set else 0.0

            doc_type = self._classify_document_type(content, path, heading_texts)

            self.summary_cache[path] = DocumentSummary(
                path=path,
                title=doc_info.get('title', os.path.basename(path)),
                headings=heading_texts,
                content_digest=content_digest,
                word_count=word_count,
                size=file_size,
                audience=doc_info.get('audience', 'unknown'),
                update_time=update_time,
                doc_type=doc_type,
                metadata_fields=metadata_fields,
                metadata_score=metadata_score,
                description=description,
                tags=tags,
                author=author,
                created_date=created_date,
                version=version
            )
        return self.summary_cache[path] # Ensure it always returns, even if from cache

    def _classify_document_type(self, content: str, path: str, headings: List[str]) -> str:
        """Classify document type based on content, path, and headings"""
        path_lower = path.lower()
        filename = os.path.basename(path_lower)

        if filename in ["readme.md", "index.md", "index.html", "index.rst"]:
            return "index"

        for doc_type, patterns in DOC_TYPE_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, path_lower):
                    return doc_type
        
        if headings:
            first_heading = headings[0].lower()
            for doc_type, patterns in DOC_TYPE_PATTERNS.items():
                for pattern in patterns:
                    if re.search(pattern, first_heading):
                        return doc_type

        content_sample = content[:1000].lower()
        for doc_type, patterns in DOC_TYPE_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, content_sample):
                    return doc_type
        return "unknown"
