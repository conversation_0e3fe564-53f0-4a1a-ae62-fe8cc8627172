"""
Dataclasses used by the documentation analyzer.
"""

import os
import datetime
from dataclasses import dataclass, field
from typing import List, Dict, Optional

@dataclass
class DocumentSummary:
    """Compact summary of document features for fast comparison"""
    path: str
    title: str
    headings: List[str]
    content_digest: str     # Hash of content for quick identity checks
    word_count: int
    size: int               # File size in bytes
    audience: str
    update_time: Optional[datetime.datetime] = None
    doc_type: str = "unknown"  # Document type (tutorial, reference, guide, etc.)
    metadata_fields: Dict[str, str] = field(default_factory=dict)  # All metadata fields found in the document
    metadata_score: float = 0.0  # Score for metadata completeness (0-1)
    description: str = ""  # Document description
    tags: List[str] = field(default_factory=list)  # Document tags
    author: str = ""  # Document author
    created_date: Optional[datetime.datetime] = None  # Document creation date
    version: str = ""  # Document version

    # __post_init__ was removed as default_factory handles the initialization of metadata_fields and tags

    @property
    def basename(self) -> str:
        return os.path.basename(self.path)

    @property
    def dirname(self) -> str:
        return os.path.dirname(self.path)

    @property
    def location_depth(self) -> int:
        """Return the depth of the document in the directory structure"""
        return len(self.path.split(os.sep))

    @property
    def is_index(self) -> bool:
        """Check if this is an index document (README, index.md, etc.)"""
        basename = self.basename.lower()
        return basename in ["readme.md", "index.md", "index.html", "index.rst"]
