"""
Global constants for the documentation analyzer.
"""

import multiprocessing

# Constants
MAX_WORKERS = max(4, multiprocessing.cpu_count())
MIN_DOC_BYTES = 100  # Ignore tiny docs for some comparisons
DEFAULT_EMBEDDING_MODEL = "all-MiniLM-L6-v2"  # Small, fast, good quality
SIMILAR_DOCS_THRESHOLD = 0.75  # Threshold for document similarity (0-1)
CONCEPTUAL_SIMILARITY_THRESHOLD = 0.8  # Threshold for semantic similarity
# Common directories to exclude from analysis
DEFAULT_EXCLUDE_DIRS = [
    'frontend',
    '.pytest_cache',
    r'backend\.pytest_cache',  # Raw string for regex-like pattern
    r'backend\staticfiles',   # Raw string for regex-like pattern
    '.VSCodeCounter',
    'node_modules',  # Common exclusion
    'myenv',         # Python virtual environment
    '__pycache__',
    '.git'
]


READABILITY_THRESHOLDS = {
    'excellent': 70,  # Very easy to read
    'good': 60,       # Easy to read
    'acceptable': 50, # Fairly easy to read
    'difficult': 30,  # Difficult to read
    'very_difficult': 0  # Very difficult to read
}

# Documentation type patterns (for classification)
DOC_TYPE_PATTERNS = {
    'tutorial': [r'tutorial', r'getting started', r'quickstart', r'step by step', r'how to'],
    'reference': [r'reference', r'api', r'specification', r'schema', r'parameters'],
    'guide': [r'guide', r'best practices', r'guidelines', r'recommendations'],
    'concept': [r'concept', r'overview', r'introduction', r'understanding'],
    'example': [r'example', r'sample', r'demo', r'showcase'],
    'troubleshooting': [r'troubleshoot', r'debug', r'problem', r'issue', r'error', r'faq']
}

# Common metadata fields that should be present
EXPECTED_METADATA_FIELDS = [
    'title',
    'description',
    'author',
    'created_date',
    'updated_date',
    'version',
    'audience',
    'tags'
]

# Documentation organization patterns
DOC_ORG_PATTERNS = {
    'by_audience': r'docs/[^/]+/(developer|user|admin|api)/',
    'by_version': r'docs/v\d+/',
    'by_topic': r'docs/(getting-started|concepts|reference|guides|tutorials)/',
    'by_language': r'docs/(python|javascript|java|csharp|go)/'
}
