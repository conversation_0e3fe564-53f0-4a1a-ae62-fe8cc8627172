"""
Analyzes document structure issues.
"""
import time
import logging
import os
from collections import defaultdict, Counter
from typing import List, Dict, Any

# Assuming necessary constants and classes are available or passed
# Import constants that are directly used.
from ..core.constants import MIN_DOC_BYTES

logger = logging.getLogger(__name__)

def analyze_structure(
    docs_data: List[Dict[str, Any]],
    doc_store: Any
) -> Dict[str, Any]:
    """
    Analyze document structure issues.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Analyzing structure...")
    start_time = time.time()

    # Initialize categories
    categories = {
        "messy_docs": [],
        "no_audience": [],
        "todos": [],
        "obsolete": [],
        "thin_docs": [],
        "no_headings": [],
        "inconsistent_naming": []
    }

    # Collect directory structure
    dir_structure = defaultdict(list)

    for doc_item in docs_data:
        path = doc_item["path"]
        dir_name = os.path.dirname(path)
        dir_structure[dir_name].append(doc_item)

        # Check for docs in the "messy" directory
        if '/docs/messy/' in path:
            categories["messy_docs"].append({
                "path": path,
                "title": doc_item.get("title", os.path.basename(path))
            })

        # Check for missing audience
        if doc_item.get("audience") == "unknown":
            categories["no_audience"].append({
                "path": path,
                "title": doc_item.get("title", os.path.basename(path))
            })

        # Check for TODOs and obsolete markers
        if doc_item.get("has_todo"):
            categories["todos"].append({
                "path": path,
                "title": doc_item.get("title", os.path.basename(path))
            })

        if doc_item.get("has_obsolete"):
            categories["obsolete"].append({
                "path": path,
                "title": doc_item.get("title", os.path.basename(path))
            })

        # Check for very small docs (potentially stubs or placeholders)
        # Need to get summary to check size and headings
        summary = doc_store.get_summary(path, doc_item)
        if summary.size < MIN_DOC_BYTES: # MIN_DOC_BYTES imported from core.constants
            categories["thin_docs"].append({
                "path": path,
                "title": summary.title,
                "size": summary.size
            })

        # Check for docs with no section headings
        if not summary.headings and summary.size > MIN_DOC_BYTES:
            categories["no_headings"].append({
                "path": path,
                "title": summary.title
            })

    # Check for inconsistent naming in directories
    for dir_name, dir_docs_data in dir_structure.items(): # Renamed docs to dir_docs_data
        # Skip if only one file
        if len(dir_docs_data) <= 1:
            continue

        # Check naming patterns
        filenames = [os.path.basename(doc_item["path"]) for doc_item in dir_docs_data] # Renamed doc to doc_item

        # Look for inconsistent casing
        has_upper = any(f[0].isupper() for f in filenames if f)
        has_lower = any(f[0].islower() for f in filenames if f)

        if has_upper and has_lower:
            # Found inconsistent casing
            categories["inconsistent_naming"].append({
                "directory": dir_name,
                "files": filenames,
                "issue": "mixed_casing"
            })

        # Look for inconsistent word separation (_- vs snake_case vs camelCase vs kebab-case)
        has_snake = any("_" in f for f in filenames)
        has_kebab = any("-" in f for f in filenames)
        # Simple check for camelCase: starts lower, has an upper char
        has_camel = any(f and f[0].islower() and any(c.isupper() for c in f[1:]) for f in filenames)


        if sum([has_snake, has_kebab, has_camel]) > 1:
            # Found inconsistent word separation
            categories["inconsistent_naming"].append({
                "directory": dir_name,
                "files": filenames,
                "issue": "mixed_word_separation"
            })

    duration = time.time() - start_time
    logger.info(f"Completed structure analysis in {duration:.2f}s")

    return {
        "categories": categories,
        "analysis_time": duration
    }
