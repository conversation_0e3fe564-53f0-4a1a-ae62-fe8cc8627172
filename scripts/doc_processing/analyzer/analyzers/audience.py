"""
Analyzes audience distribution and patterns.
"""
import time
import logging
import os
from collections import Counter, defaultdict
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

def analyze_audience(
    docs_data: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Analyze audience distribution.

    Args:
        docs_data: A list of document dictionaries.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Analyzing audience distribution...")
    start_time = time.time()

    audience_counts = Counter()
    directory_audience = defaultdict(Counter)
    audience_patterns = defaultdict(list)

    for doc_item in docs_data:
        audience = doc_item.get("audience", "unknown")
        audience_counts[audience] += 1

        # Track audience by directory
        path_parts = doc_item["path"].split(os.sep) # Use os.sep for cross-platform compatibility
        if len(path_parts) >= 1: # Check if path_parts is not empty
            # Use the first part of the path as the top-level directory
            top_dir = path_parts[0] if path_parts[0] else os.sep # Handle root directory case
            directory_audience[top_dir][audience] += 1

        # Group documents by audience for pattern analysis
        audience_patterns[audience].append(doc_item)

    # Analyze patterns in audience groups
    audience_insights = {}
    for audience, docs_list in audience_patterns.items(): # Renamed docs to docs_list
        # Skip unknown audience
        if audience == "unknown":
            continue

        # Calculate metrics
        # Ensure docs_list is not empty before calculating averages
        if docs_list:
            avg_size = sum(os.path.getsize(doc_item["path"]) for doc_item in docs_list if os.path.exists(doc_item["path"])) / len(docs_list) # Check if file exists before getting size
            # Assuming section_structure is a list in the doc_item
            avg_sections = sum(len(doc_item.get("section_structure", [])) for doc_item in docs_list) / len(docs_list)

            # Find common directories
            dirs = Counter(os.path.dirname(doc_item["path"]) for doc_item in docs_list)
            top_dirs = dirs.most_common(3)

            audience_insights[audience] = {
                "document_count": len(docs_list),
                "avg_size_bytes": avg_size,
                "avg_sections": avg_sections,
                "top_directories": top_dirs
            }
        else:
             # Handle case where docs_list is empty for an audience (shouldn't happen with current logic, but for safety)
             audience_insights[audience] = {
                "document_count": 0,
                "avg_size_bytes": 0,
                "avg_sections": 0,
                "top_directories": []
            }


    duration = time.time() - start_time
    logger.info(f"Analyzed audience distribution in {duration:.2f}s")

    return {
        "audience_counts": dict(audience_counts),
        "directory_audience": {dir_name: dict(counts) for dir_name, counts in directory_audience.items()},
        "audience_insights": audience_insights,
        "analysis_time": duration
    }
