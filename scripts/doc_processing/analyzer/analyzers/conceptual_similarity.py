"""
Analyzes documents to find conceptually similar content using embeddings.
"""
import time
import logging
import os
import multiprocessing
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import numpy as np

# Assuming necessary constants and classes are available or passed
# Import constants that are directly used.
from ..core.constants import MIN_DOC_BYTES, CONCEPTUAL_SIMILARITY_THRESHOLD

# Optional imports - gracefully handle if not available
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

logger = logging.getLogger(__name__)

def analyze_conceptual_similarity(
    docs_data: List[Dict[str, Any]],
    doc_store: Any,
    analysis_results: Dict[str, Any],
    options: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Find conceptually similar documents using embeddings.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.
        analysis_results: Dictionary containing results from other analyses (e.g., similar_content).
        options: Dictionary of analyzer options, including 'workers' and 'openai_api_key'.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Analyzing conceptual similarity...")
    start_time = time.time()

    conceptually_similar = []

    # Skip if no embedding capability is set up in the doc_store and OpenAI is not configured
    if doc_store.embedding_model is None and not (HAS_OPENAI and 'openai_api_key' in options):
        logger.warning("Skipping conceptual similarity analysis: no embedding model available or OpenAI not configured")
        return {
            "similar_pairs": [],
            "count": 0,
            "analysis_time": 0
        }

    # Filter docs to analyze (to avoid too many comparisons)
    # Focus on documents with:
    # 1. Substantial content (larger than MIN_DOC_BYTES)
    # 2. No exact or similar duplicates already found

    # Get IDs of docs already detected as similar
    already_similar = set()
    for pair in analysis_results.get("similar_content", {}).get("similar_pairs", []):
        already_similar.add(pair["doc1"]["path"])
        already_similar.add(pair["doc2"]["path"])

    # Add exact duplicates
    for group in analysis_results.get("exact_duplicates", {}).get("duplicate_groups", []):
        for doc in group:
            already_similar.add(doc["path"])

    # Filter docs for embedding
    docs_to_embed = []
    for doc_item in docs_data:
        path = doc_item["path"]

        # Skip if already identified as duplicate/similar
        if path in already_similar:
            continue

        # Skip tiny documents
        if os.path.exists(path) and os.path.getsize(path) < MIN_DOC_BYTES:
            continue
        
        # Also skip if file doesn't exist
        if not os.path.exists(path):
             logger.debug(f"Skipping missing file for embedding: {path}")
             continue


        docs_to_embed.append(doc_item)

    # If using local embedding model
    if doc_store.embedding_model is not None:
        logger.info(f"Using local embeddings for {len(docs_to_embed)} documents")

        # Generate embeddings in parallel
        num_workers = options.get('workers', max(4, multiprocessing.cpu_count()))
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            # Use a list comprehension to trigger execution and collect results (embeddings are cached internally)
            [doc_store.get_embedding(doc["path"]) for doc in docs_to_embed]


        # Compare embeddings to find similar documents
        similar_pairs = []
        for i, doc1_data in enumerate(docs_to_embed):
            doc1_path = doc1_data["path"]
            emb1 = doc_store.get_embedding(doc1_path)

            # Skip if embedding failed or was not generated
            if emb1 is None or not isinstance(emb1, np.ndarray) or emb1.size == 0:
                continue

            for j, doc2_data in enumerate(docs_to_embed[i+1:], i+1):
                doc2_path = doc2_data["path"]
                # Skip if comparing docs from completely different areas (example logic)
                if (('/docs/frontend/' in doc1_path and '/docs/backend/' in doc2_path) or
                    ('/docs/frontend/' in doc2_path and '/docs/backend/' in doc1_path)):
                    continue

                emb2 = doc_store.get_embedding(doc2_path)

                # Skip if embedding failed or was not generated
                if emb2 is None or not isinstance(emb2, np.ndarray) or emb2.size == 0:
                    continue

                # Calculate cosine similarity
                # Add a small epsilon to the denominator to prevent division by zero
                norm1 = np.linalg.norm(emb1)
                norm2 = np.linalg.norm(emb2)
                if norm1 > 1e-6 and norm2 > 1e-6: # Check if norms are not close to zero
                    similarity = np.dot(emb1, emb2) / (norm1 * norm2)
                else:
                    similarity = 0.0 # Treat as no similarity if norms are zero

                if similarity >= CONCEPTUAL_SIMILARITY_THRESHOLD:
                    similar_pairs.append({
                        "doc1": {
                            "path": doc1_path,
                            "title": doc1_data.get("title", os.path.basename(doc1_path))
                        },
                        "doc2": {
                            "path": doc2_path,
                            "title": doc2_data.get("title", os.path.basename(doc2_path))
                        },
                        "similarity": float(similarity),
                        "type": "conceptual"
                    })

            conceptually_similar = similar_pairs

    # If using OpenAI for embeddings (fallback)
    elif HAS_OPENAI and 'openai_api_key' in options:
        logger.info(f"Using OpenAI for embeddings for {len(docs_to_embed)} documents")
        # This part would need to be implemented to handle OpenAI API calls,
        # potentially with rate limiting and cost considerations.
        # For now, this section is a placeholder.
        logger.warning("OpenAI embedding analysis is not yet fully implemented.")
        pass # Placeholder for OpenAI implementation

    # Sort by similarity score
    conceptually_similar.sort(key=lambda x: x["similarity"], reverse=True)

    duration = time.time() - start_time
    logger.info(f"Found {len(conceptually_similar)} conceptually similar documents in {duration:.2f}s")

    return {
        "similar_pairs": conceptually_similar,
        "count": len(conceptually_similar),
        "analysis_time": duration
    }
