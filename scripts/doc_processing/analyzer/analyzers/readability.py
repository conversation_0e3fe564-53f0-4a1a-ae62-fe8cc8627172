"""
Analyzes document readability.
"""
import time
import logging
import os
from collections import defaultdict
from typing import List, Dict, Any

# Assuming necessary constants and classes are available or passed
# Import constants that are directly used.
from ..core.constants import MIN_DOC_BYTES, READABILITY_THRESHOLDS

# Optional imports - gracefully handle if not available
try:
    from textstat import flesch_reading_ease, text_standard
    HAS_READABILITY = True
except ImportError:
    HAS_READABILITY = False

logger = logging.getLogger(__name__)

def analyze_readability(
    docs_data: List[Dict[str, Any]],
    doc_store: Any
) -> Dict[str, Any]:
    """
    Analyze document readability if textstat is available.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.

    Returns:
        A dictionary containing the analysis results.
    """
    if not HAS_READABILITY:
        logger.warning("Skipping document readability analysis: Missing textstat")
        return {
            "readability_scores": [],
            "analysis_time": 0
        }

    logger.info("Analyzing document readability...")
    start_time = time.time()

    readability_scores = []

    for doc_item in docs_data:
        path = doc_item["path"]
        content = doc_store.get_content(path)

        # Skip tiny or empty documents
        if not content or len(content) < MIN_DOC_BYTES: # MIN_DOC_BYTES imported
            continue

        try:
            # Calculate readability scores
            flesch_score = flesch_reading_ease(content)
            grade_level = text_standard(content, float_output=True)

            # Determine readability level based on thresholds
            # READABILITY_THRESHOLDS imported
            if flesch_score >= READABILITY_THRESHOLDS["excellent"]:
                readability_level = "excellent"
            elif flesch_score >= READABILITY_THRESHOLDS["good"]:
                readability_level = "good"
            elif flesch_score >= READABILITY_THRESHOLDS["acceptable"]:
                readability_level = "acceptable"
            elif flesch_score >= READABILITY_THRESHOLDS["difficult"]:
                readability_level = "difficult"
            else:
                readability_level = "very_difficult"

            readability_scores.append({
                "path": path,
                "title": doc_item.get("title", os.path.basename(path)),
                "audience": doc_item.get("audience", "unknown"),
                "flesch_score": flesch_score,
                "grade_level": grade_level,
                "readability_level": readability_level
            })
        except Exception as e:
            logger.warning(f"Error calculating readability for {path}: {e}")

    # Sort by readability (Flesch score)
    readability_scores.sort(key=lambda x: x.get("flesch_score", -1), reverse=True) # Use get with default

    # Group scores by audience
    audience_readability = defaultdict(list)
    for score in readability_scores:
        audience_readability[score["audience"]].append(score["flesch_score"])

    # Calculate average readability by audience
    audience_avg_readability = {}
    for audience, scores in audience_readability.items():
        if scores: # Ensure scores list is not empty
            audience_avg_readability[audience] = sum(scores) / len(scores)
        else:
            audience_avg_readability[audience] = 0.0 # Handle empty scores list


    duration = time.time() - start_time
    logger.info(f"Analyzed readability for {len(readability_scores)} documents in {duration:.2f}s")

    return {
        "readability_scores": readability_scores,
        "audience_avg_readability": audience_avg_readability,
        "analysis_time": duration
    }
