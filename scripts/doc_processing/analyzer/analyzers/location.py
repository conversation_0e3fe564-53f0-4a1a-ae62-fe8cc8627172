"""
Analyzes documentation location patterns and organization.
"""
import time
import logging
import os
import re
from collections import defaultdict, Counter
from typing import List, Dict, Any

# Assuming necessary constants and classes are available or passed
# Import constants that are directly used.
from ..core.constants import DOC_ORG_PATTERNS, DOC_TYPE_PATTERNS

logger = logging.getLogger(__name__)

def _build_directory_hierarchy(docs_data: List[Dict[str, Any]]):
    """
    Build a hierarchical representation of the documentation directory structure.
    Helper for analyze_doc_location.
    """
    hierarchy = {}

    # Get all unique directories
    all_dirs = set()
    for doc_item in docs_data:
        path = doc_item["path"]
        dir_path = os.path.dirname(path)

        # Add all parent directories
        parts = dir_path.split(os.sep)
        current = ""
        for part in parts:
            if part:
                current = os.path.join(current, part) if current else part
                all_dirs.add(current)

    # Build hierarchy
    for dir_path in sorted(list(all_dirs)): # Sort for consistent output
        parts = dir_path.split(os.sep)

        # Navigate to the right spot in the hierarchy
        current = hierarchy
        for i, part in enumerate(parts[:-1]):
            if part:
                if part not in current:
                    current[part] = {}
                current = current[part]

        # Add the final directory
        if parts and parts[-1]:
            if parts[-1] not in current:
                current[parts[-1]] = {}

    return hierarchy


def analyze_doc_location(
    docs_data: List[Dict[str, Any]],
    doc_store: Any
) -> Dict[str, Any]:
    """
    Analyze documentation location patterns and organization.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Analyzing documentation location patterns...")
    start_time = time.time()

    # Analyze directory structure
    dir_structure = defaultdict(list)
    dir_depths = defaultdict(int)
    dir_doc_types = defaultdict(Counter)
    dir_audiences = defaultdict(Counter)

    # Track organization patterns
    org_patterns = {pattern: [] for pattern in DOC_ORG_PATTERNS} # DOC_ORG_PATTERNS imported

    # Track index documents
    index_docs = []

    # Track documentation gaps
    missing_indexes = set()

    # Analyze each document
    for doc_item in docs_data:
        path = doc_item["path"]
        summary = doc_store.get_summary(path, doc_item)

        # Add to directory structure
        dir_name = os.path.dirname(path)
        dir_structure[dir_name].append(summary)

        # Track directory depth
        depth = summary.location_depth
        dir_depths[dir_name] = max(dir_depths[dir_name], depth)

        # Track document types in directory
        dir_doc_types[dir_name][summary.doc_type] += 1

        # Track audiences in directory
        dir_audiences[dir_name][summary.audience] += 1

        # Check for index documents
        if summary.is_index:
            index_docs.append({
                "path": path,
                "directory": dir_name,
                "title": summary.title,
                "word_count": summary.word_count
            })

        # Check for organization patterns
        for pattern_name, pattern in DOC_ORG_PATTERNS.items():
            if re.search(pattern, path):
                org_patterns[pattern_name].append(path)

    # Find directories without index documents
    for dir_name, summaries_list in dir_structure.items(): # Renamed docs to summaries_list
        if len(summaries_list) >= 3:  # Only consider directories with at least 3 docs
            has_index = any(summary.is_index for summary in summaries_list) # Use summary object
            if not has_index:
                missing_indexes.add(dir_name)

    # Analyze directory hierarchy
    dir_hierarchy = _build_directory_hierarchy(docs_data) # Pass docs_data to helper

    # Calculate directory statistics
    dir_stats = {}
    for dir_name, summaries_list in dir_structure.items(): # Renamed docs to summaries_list
        if len(summaries_list) >= 2:  # Only include directories with multiple docs
            # Calculate average metadata score for the directory
            avg_metadata_score = sum(summary.metadata_score for summary in summaries_list) / len(summaries_list)

            # Calculate document type diversity
            doc_types = Counter(summary.doc_type for summary in summaries_list)
            type_diversity = len(doc_types) / max(1, len(DOC_TYPE_PATTERNS) + 1)  # DOC_TYPE_PATTERNS imported, +1 for "index" type

            # Calculate audience consistency
            audiences = Counter(summary.audience for summary in summaries_list)
            primary_audience = audiences.most_common(1)[0][0] if audiences else "unknown"
            audience_consistency = audiences.get(primary_audience, 0) / len(summaries_list)

            dir_stats[dir_name] = {
                "doc_count": len(summaries_list),
                "avg_metadata_score": avg_metadata_score,
                "doc_types": dict(doc_types),
                "type_diversity": type_diversity,
                "primary_audience": primary_audience,
                "audience_consistency": audience_consistency,
                "has_index": dir_name not in missing_indexes
            }

    # Identify documentation organization patterns
    detected_patterns = {}
    for pattern_name, paths in org_patterns.items():
        if paths:
            detected_patterns[pattern_name] = {
                "count": len(paths),
                "examples": paths
            }

    duration = time.time() - start_time
    logger.info(f"Analyzed documentation location patterns in {duration:.2f}s")

    return {
        "dir_stats": dir_stats,
        "dir_hierarchy": dir_hierarchy,
        "index_docs": index_docs,
        "missing_indexes": list(missing_indexes),
        "org_patterns": detected_patterns,
        "analysis_time": duration
    }
