"""
Analyzes documents to find exact duplicates.
"""
import time
import logging
from collections import defaultdict
from typing import List, Dict, Tuple, Any

# Assuming DocumentSummary and CachedDocumentStore will be passed or imported if needed
# For now, this module will rely on the main analyzer to pass the necessary data structures.

logger = logging.getLogger(__name__)

def analyze_exact_duplicates(docs: List[Dict[str, Any]], doc_store: Any) -> Dict[str, Any]:
    """
    Find exact duplicate documents using fast content hashing.

    Args:
        docs: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Finding exact duplicates...")
    start_time = time.time()

    # Group documents by content digest
    digest_groups = defaultdict(list)

    for doc_data in docs: # Renamed doc to doc_data to avoid conflict with potential doc object
        path = doc_data['path']
        # Assuming get_summary now correctly fetches or computes summary
        summary = doc_store.get_summary(path, doc_data)
        digest_groups[summary.content_digest].append((doc_data, summary))

    # Filter groups to only those with multiple documents
    duplicate_groups = {
        digest: doc_items for digest, doc_items in digest_groups.items() # Renamed docs to doc_items
        if len(doc_items) > 1
    }

    # Format results
    results = []
    for digest, doc_group in duplicate_groups.items():
        group = []
        for doc_data, summary in doc_group: # Renamed doc to doc_data
            group.append({
                "path": doc_data["path"],
                "title": summary.title,
                "audience": summary.audience,
                "word_count": summary.word_count
            })
        results.append(group)

    duration = time.time() - start_time
    logger.info(f"Found {len(results)} duplicate groups in {duration:.2f}s")

    return {
        "duplicate_groups": results,
        "count": len(results),
        "analysis_time": duration
    }
