"""
Analyzes documents to find similar (but not identical) content.
"""
import time
import logging
import re
import random
import multiprocessing
from collections import defaultdict
from typing import List, Dict, Any, Optional
from concurrent.futures import ProcessPoolExecutor

# Assuming DocumentSummary, CachedDocumentStore, MIN_DOC_BYTES, SIMILAR_DOCS_THRESHOLD
# will be passed or imported if this module needs them directly.
# For now, this module will rely on the main analyzer to pass the necessary data.
# Import constants that are directly used.
from ..core.constants import MIN_DOC_BYTES, SIMILAR_DOCS_THRESHOLD

logger = logging.getLogger(__name__)

# Helper function, was previously a static method
def _compare_docs_for_similarity(doc1_summary: Any, doc2_summary: Any, doc_store: Any) -> Optional[Dict[str, Any]]:
    """
    Compare two document summaries for similarity based on multiple factors.
    Helper for analyze_similar_content.
    """
    # Skip if documents are exactly the same
    if doc1_summary.content_digest == doc2_summary.content_digest:
        return None

    # Skip if documents are in completely different areas (example logic)
    if (('/docs/frontend/' in doc1_summary.path and '/docs/backend/' in doc2_summary.path) or
        ('/docs/frontend/' in doc2_summary.path and '/docs/backend/' in doc1_summary.path)):
        return None

    # 1. Title similarity
    title_similarity = 0
    if doc1_summary.title and doc2_summary.title:
        title_words1 = set(doc1_summary.title.lower().split())
        title_words2 = set(doc2_summary.title.lower().split())
        if title_words1 and title_words2: # Check if sets are not empty
            union_size = len(title_words1.union(title_words2))
            if union_size > 0: # Avoid division by zero
                title_similarity = len(title_words1.intersection(title_words2)) / union_size

    # 2. Heading similarity
    heading_similarity = 0
    if doc1_summary.headings and doc2_summary.headings:
        headings1 = set(h.lower() for h in doc1_summary.headings)
        headings2 = set(h.lower() for h in doc2_summary.headings)
        if headings1 and headings2: # Check if sets are not empty
            union_size = len(headings1.union(headings2))
            if union_size > 0: # Avoid division by zero
                heading_similarity = len(headings1.intersection(headings2)) / union_size
    
    # 3. Basic content similarity using tokens
    content1 = doc_store.get_content(doc1_summary.path)
    content2 = doc_store.get_content(doc2_summary.path)

    words1 = set(re.findall(r'\b[a-zA-Z0-9_]+\b', content1.lower()))
    words2 = set(re.findall(r'\b[a-zA-Z0-9_]+\b', content2.lower()))

    token_similarity = 0
    if words1 and words2:
        important_words1 = {w for w in words1 if len(w) > 4}
        important_words2 = {w for w in words2 if len(w) > 4}
        if important_words1 and important_words2: # Check if sets are not empty
            union_size = len(important_words1.union(important_words2))
            if union_size > 0: # Avoid division by zero
                token_similarity = len(important_words1.intersection(important_words2)) / union_size

    weighted_similarity = (
        title_similarity * 0.3 +
        heading_similarity * 0.3 +
        token_similarity * 0.4
    )

    if weighted_similarity >= SIMILAR_DOCS_THRESHOLD:
        return {
            "doc1": {"path": doc1_summary.path, "title": doc1_summary.title},
            "doc2": {"path": doc2_summary.path, "title": doc2_summary.title},
            "similarity": weighted_similarity,
            "title_similarity": title_similarity,
            "heading_similarity": heading_similarity,
            "token_similarity": token_similarity
        }
    return None

def analyze_similar_content(
    docs_data: List[Dict[str, Any]], 
    doc_store: Any, 
    options: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Find similar but not identical documents.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.
        options: Dictionary of analyzer options, including 'workers'.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Finding similar content...")
    start_time = time.time()

    summaries = []
    for doc_item in docs_data:
        path = doc_item['path']
        summary = doc_store.get_summary(path, doc_item)
        if summary.size < MIN_DOC_BYTES: # MIN_DOC_BYTES imported from core.constants
            continue
        summaries.append(summary)

    dir_groups = defaultdict(list)
    for summary in summaries:
        dir_groups[summary.dirname].append(summary)

    results = []
    # Use max_workers from options, defaulting to a reasonable number if not specified
    num_workers = options.get('workers', max(4, multiprocessing.cpu_count()))

    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        futures = []
        for dir_path, dir_summaries in dir_groups.items():
            if len(dir_summaries) > 1:
                pairs = [(s1, s2) for i, s1 in enumerate(dir_summaries) for s2 in dir_summaries[i+1:]]
                for pair in pairs:
                    futures.append(executor.submit(_compare_docs_for_similarity, pair[0], pair[1], doc_store))
        
        all_summaries_for_seeding = [s for group in dir_groups.values() for s in group]
        sample_size = min(50, len(all_summaries_for_seeding))

        if sample_size > 10 and len(all_summaries_for_seeding) > 0: # Ensure all_summaries_for_seeding is not empty
            seeds = random.sample(all_summaries_for_seeding, sample_size)
            for seed in seeds:
                other_dirs = [d for d in dir_groups.keys() if d != seed.dirname]
                if other_dirs:
                    comparison_docs_sample = []
                    # Limit comparison to a few other directories to manage complexity
                    for other_dir in random.sample(other_dirs, min(3, len(other_dirs))): 
                        other_dir_docs = dir_groups[other_dir]
                        if other_dir_docs:
                            samples = random.sample(other_dir_docs, min(5, len(other_dir_docs)))
                            comparison_docs_sample.extend(samples)
                    
                    for other_doc_summary in comparison_docs_sample:
                        # Ensure we don't compare a doc with itself if it ended up in seeds and comparison_docs_sample
                        if seed.path != other_doc_summary.path:
                             futures.append(executor.submit(_compare_docs_for_similarity, seed, other_doc_summary, doc_store))

        for future in futures:
            result = future.result()
            if result:
                results.append(result)

    results.sort(key=lambda x: x["similarity"], reverse=True)
    duration = time.time() - start_time
    logger.info(f"Found {len(results)} similar documents in {duration:.2f}s")

    return {
        "similar_pairs": results,
        "count": len(results),
        "analysis_time": duration
    }
