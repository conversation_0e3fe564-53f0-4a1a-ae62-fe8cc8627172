"""
Analyzes documents to find clusters of similar content using TF-IDF and clustering.
"""
import time
import logging
import multiprocessing
from collections import defaultdict
from typing import List, Dict, Any

# Assuming necessary constants and classes are available or passed
# Import constants that are directly used.
from ..core.constants import MIN_DOC_BYTES

# Optional imports - gracefully handle if not available
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.cluster import DBSCAN
    HAS_SKLEARN = True # Renamed from HAS_VISUALIZATION as this is the dependency check
except ImportError:
    HAS_SKLEARN = False

logger = logging.getLogger(__name__)

def analyze_document_clusters(
    docs_data: List[Dict[str, Any]],
    doc_store: Any,
    options: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Group similar documents using TF-IDF and clustering.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.
        options: Dictionary of analyzer options (not directly used here, but for consistency).

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Clustering documents...")
    start_time = time.time()

    # Skip if necessary libraries are not available
    if not HAS_SKLEARN:
        logger.warning("Skipping document clustering analysis: Missing scikit-learn")
        return {
            "clusters": [],
            "count": 0,
            "analysis_time": 0
        }

    # Prepare documents
    doc_contents = []
    doc_objects = []

    for doc_item in docs_data:
        path = doc_item["path"]
        content = doc_store.get_content(path)
        # Ensure content is not empty or too small
        if not content or len(content) < MIN_DOC_BYTES:
            continue

        doc_contents.append(content)
        doc_objects.append(doc_item)

    # Skip if too few documents to cluster
    if len(doc_contents) < 5:
        logger.info(f"Skipping clustering: Only {len(doc_contents)} documents available (need at least 5)")
        return {
            "clusters": [],
            "count": 0,
            "analysis_time": 0
        }

    # Create TF-IDF matrix
    vectorizer = TfidfVectorizer(
        max_features=5000,
        stop_words='english',
        ngram_range=(1, 2)
    )

    try:
        tfidf_matrix = vectorizer.fit_transform(doc_contents)
    except ValueError as e:
        logger.warning(f"Could not create TF-IDF matrix: {e}. Skipping clustering.")
        return {
            "clusters": [],
            "count": 0,
            "analysis_time": 0
        }


    # Calculate similarity matrix
    similarity_matrix = cosine_similarity(tfidf_matrix)

    # Use DBSCAN for clustering
    distance_matrix = 1 - similarity_matrix
    # Adjust eps and min_samples based on dataset characteristics if needed
    clustering = DBSCAN(
        eps=0.3,  # Maximum distance between samples in a cluster
        min_samples=2,  # Minimum samples in a neighborhood to form a core point
        metric='precomputed'
    ).fit(distance_matrix)

    # Get cluster labels
    labels = clustering.labels_

    # Group documents by cluster
    clusters = defaultdict(list)
    for i, label in enumerate(labels):
        # Skip noise points (label -1)
        if label != -1:
            doc_item = doc_objects[i]
            clusters[label].append({
                "path": doc_item["path"],
                "title": doc_item.get("title", os.path.basename(doc_item["path"]))
            })

    # Format clusters
    cluster_list = [
        {"id": label, "documents": docs}
        for label, docs in clusters.items()
    ]

    # Sort by size
    cluster_list.sort(key=lambda x: len(x["documents"]), reverse=True)

    duration = time.time() - start_time
    logger.info(f"Found {len(cluster_list)} document clusters in {duration:.2f}s")

    return {
        "clusters": cluster_list,
        "count": len(cluster_list),
        "analysis_time": duration
    }
