"""
Analyzes document type distribution and gaps.
"""
import time
import logging
import os
from collections import Counter, defaultdict
from typing import List, Dict, Any

# Assuming necessary constants and classes are available or passed
# Import constants that are directly used.
from ..core.constants import DOC_TYPE_PATTERNS

logger = logging.getLogger(__name__)

def analyze_doc_types(
    docs_data: List[Dict[str, Any]],
    doc_store: Any
) -> Dict[str, Any]:
    """
    Analyze document type distribution and gaps.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Analyzing document types...")
    start_time = time.time()

    # Track document types
    doc_type_counts = Counter()
    doc_type_examples = defaultdict(list)
    doc_type_by_dir = defaultdict(Counter)

    # Analyze each document
    for doc_item in docs_data:
        path = doc_item["path"]
        summary = doc_store.get_summary(path, doc_item)
        dir_name = os.path.dirname(path)

        # Count document types
        doc_type_counts[summary.doc_type] += 1

        # Track examples of each type
        if len(doc_type_examples[summary.doc_type]) < 5:  # Limit to 5 examples per type
            doc_type_examples[summary.doc_type].append({
                "path": path,
                "title": summary.title,
                "audience": summary.audience
            })

        # Track document types by directory
        doc_type_by_dir[dir_name][summary.doc_type] += 1

    # Identify directories with missing document types
    dir_type_gaps = {}
    for dir_name, type_counts in doc_type_by_dir.items():
        # Only consider directories with at least 5 documents
        if sum(type_counts.values()) >= 5:
            # Check for missing important document types
            missing_types = []
            for doc_type in ['tutorial', 'reference', 'guide', 'concept']: # Hardcoded types for now
                if doc_type not in type_counts:
                    missing_types.append(doc_type)

            if missing_types:
                dir_type_gaps[dir_name] = missing_types

    # Calculate document type diversity
    # DOC_TYPE_PATTERNS imported from core.constants
    type_diversity = len(doc_type_counts) / (len(DOC_TYPE_PATTERNS) + 1)  # +1 for "index" type

    duration = time.time() - start_time
    logger.info(f"Analyzed document types in {duration:.2f}s")

    return {
        "type_counts": dict(doc_type_counts),
        "type_examples": dict(doc_type_examples),
        "type_by_dir": {dir_name: dict(counts) for dir_name, counts in doc_type_by_dir.items()},
        "dir_type_gaps": dir_type_gaps,
        "type_diversity": type_diversity,
        "analysis_time": duration
    }
