"""
Analyzes internal linking patterns between documentation.
"""
import time
import logging
import os
from collections import defaultdict
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

def analyze_links(
    docs_data: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Analyze internal linking patterns between documentation.

    Args:
        docs_data: A list of document dictionaries.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Analyzing links...")
    start_time = time.time()

    # Track incoming and outgoing links
    incoming_links = defaultdict(list)
    outgoing_links = defaultdict(list)
    orphaned_docs = []

    # Build a mapping of paths to docs for quick lookup
    path_to_doc = {doc_item["path"]: doc_item for doc_item in docs_data}

    # Analyze links
    for doc_item in docs_data:
        source_path = doc_item["path"]
        for link in doc_item.get("internal_links", []):
            # Normalize the link path
            # This logic should ideally be more robust to handle various link formats
            # For now, keeping the existing logic:
            if link.startswith("./"):
                # Relative link from the current directory
                link_dir = os.path.dirname(source_path)
                target_path = os.path.normpath(os.path.join(link_dir, link))
            elif link.startswith("../"):
                # Relative link going up directories
                link_dir = os.path.dirname(source_path)
                target_path = os.path.normpath(os.path.join(link_dir, link))
            else:
                # Assume absolute from repo root or same directory if no slashes
                if "/" not in link and "\\" not in link: # Check for both slash types
                    # Same directory
                    link_dir = os.path.dirname(source_path)
                    target_path = os.path.join(link_dir, link)
                else:
                    # Assume absolute from repo root (or relative path with slashes)
                    # This might need refinement based on actual link formats
                    target_path = link.replace('/', os.sep).replace('\\', os.sep) # Normalize slashes


            # Record the link relationships
            outgoing_links[source_path].append(target_path)
            incoming_links[target_path].append(source_path)

    # Identify orphaned documents (no incoming links)
    for doc_item in docs_data:
        if doc_item["path"] not in incoming_links:
            orphaned_docs.append({
                "path": doc_item["path"],
                "title": doc_item.get("title", os.path.basename(doc_item["path"]))
            })

    # Identify hubs (docs with many outgoing links)
    hubs = []
    for path, links in outgoing_links.items():
        if len(links) >= 3:  # Only include significant hubs
            doc_item = path_to_doc.get(path, {"path": path}) # Use get with default
            hubs.append({
                "path": path,
                "title": doc_item.get("title", os.path.basename(path)),
                "links_count": len(links),
                "links": links
            })

    # Sort hubs by link count
    hubs.sort(key=lambda x: x["links_count"], reverse=True)

    # Identify authorities (docs with many incoming links)
    authorities = []
    for path, links in incoming_links.items():
        if len(links) >= 2:  # Only include significant authorities
            doc_item = path_to_doc.get(path, {"path": path}) # Use get with default
            authorities.append({
                "path": path,
                "title": doc_item.get("title", os.path.basename(path)),
                "links_count": len(links),
                "sources": links
            })

    # Sort authorities by link count
    authorities.sort(key=lambda x: x["links_count"], reverse=True)

    # Identify broken links
    broken_links = []
    for source_path, targets in outgoing_links.items():
        for target in targets:
            # Check if the target path exists in the original list of documents
            if target not in path_to_doc:
                source_doc_item = path_to_doc.get(source_path, {"path": source_path}) # Use get with default
                broken_links.append({
                    "source": {
                        "path": source_path,
                        "title": source_doc_item.get("title", os.path.basename(source_path))
                    },
                    "target": target
                })

    duration = time.time() - start_time
    logger.info(f"Analyzed links in {duration:.2f}s")

    return {
        "orphaned_docs": orphaned_docs,
        "hubs": hubs,
        "authorities": authorities,
        "broken_links": broken_links,
        "analysis_time": duration
    }
