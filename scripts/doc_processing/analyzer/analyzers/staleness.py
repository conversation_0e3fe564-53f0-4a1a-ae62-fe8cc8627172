"""
Analyzes document staleness and sync issues.
"""
import time
import logging
import os
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

def analyze_staleness(
    docs_data: List[Dict[str, Any]],
    options: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Analyze document staleness and sync issues.

    Args:
        docs_data: A list of document dictionaries.
        options: Dictionary of analyzer options, including 'stale_days' and 'sync_days'.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Analyzing staleness...")
    start_time = time.time()

    stale_threshold = options.get("stale_days", 180)  # Default: 6 months
    sync_threshold = options.get("sync_days", 90)     # Default: 3 months

    stale_docs = []
    sync_issues = []

    for doc_item in docs_data:
        # Check for staleness based on last update
        time_since_update = doc_item.get("git_history", {}).get("time_since_last_update")
        # Safely compare, handling None and non-numeric values
        if isinstance(time_since_update, (int, float)) and time_since_update > stale_threshold:
            stale_docs.append({
                "path": doc_item["path"],
                "title": doc_item.get("title", os.path.basename(doc_item["path"])),
                "days_stale": time_since_update,
                "author": doc_item.get("git_history", {}).get("unique_authors", [])[-1] if doc_item.get("git_history", {}).get("unique_authors", []) else None
            })

        # Check for sync issues with code
        code_sync = doc_item.get("code_sync", {})
        days_behind = code_sync.get("days_behind")
        # Safely compare, handling None and non-numeric values
        if isinstance(days_behind, (int, float)) and days_behind > sync_threshold:
            sync_issues.append({
                "path": doc_item["path"],
                "title": doc_item.get("title", os.path.basename(doc_item["path"])),
                "days_behind": days_behind,
                "related_code": code_sync.get("related_code_files", []),
                "sync_status": code_sync.get("sync_status", "unknown")
            })

    # Sort by staleness/sync issues
    stale_docs.sort(key=lambda x: x.get("days_stale", 0), reverse=True) # Use get with default for safety
    sync_issues.sort(key=lambda x: x.get("days_behind", 0), reverse=True) # Use get with default for safety

    duration = time.time() - start_time
    logger.info(f"Found {len(stale_docs)} stale docs and {len(sync_issues)} sync issues in {duration:.2f}s")

    return {
        "stale_docs": stale_docs,
        "sync_issues": sync_issues,
        "stale_threshold": stale_threshold,
        "sync_threshold": sync_threshold,
        "analysis_time": duration
    }
