"""
Analyzes document maintenance patterns.
"""
import time
import logging
import os
import datetime
from collections import defaultdict, Counter
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

def analyze_maintenance(
    docs_data: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Analyze document maintenance patterns.

    Args:
        docs_data: A list of document dictionaries.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Analyzing maintenance patterns...")
    start_time = time.time()

    # Sort docs by update frequency
    docs_by_updates = []
    for doc_item in docs_data:
        git_history = doc_item.get("git_history", {})
        commit_count = git_history.get("commit_count", 0)
        last_modified = git_history.get("last_modified_date")

        docs_by_updates.append({
            "path": doc_item["path"],
            "title": doc_item.get("title", os.path.basename(doc_item["path"])),
            "commit_count": commit_count,
            "last_modified": last_modified
        })

    # Sort by commit count
    most_updated = sorted(docs_by_updates, key=lambda x: x.get("commit_count", 0), reverse=True) # Use get with default
    least_updated = sorted([d for d in docs_by_updates if d.get("commit_count", 0) > 0], key=lambda x: x.get("commit_count", 0)) # Use get with default

    # Identify multi-author docs
    multi_author_docs = []
    for doc_item in docs_data:
        authors = doc_item.get("git_history", {}).get("unique_authors", [])
        if len(authors) > 1:
            multi_author_docs.append({
                "path": doc_item["path"],
                "title": doc_item.get("title", os.path.basename(doc_item["path"])),
                "author_count": len(authors),
                "authors": authors
            })

    # Sort by author count
    multi_author_docs.sort(key=lambda x: x.get("author_count", 0), reverse=True) # Use get with default

    # Identify top document authors
    author_doc_counts = Counter()
    for doc_item in docs_data:
        for author in doc_item.get("git_history", {}).get("unique_authors", []):
            author_doc_counts[author] += 1

    top_authors = [
        {"author": author, "doc_count": count}
        for author, count in author_doc_counts.most_common(10)
    ]

    # Analyze update frequency over time
    update_frequency = defaultdict(list)

    for doc_item in docs_data:
        commits = doc_item.get("git_history", {}).get("commits", [])
        if len(commits) >= 2:
            # Extract dates
            try:
                dates = []
                for commit in commits:
                    date_str = commit.get("date")
                    if date_str:
                        # Attempt to parse with common formats, be more robust
                        parsed_date = None
                        for fmt in ("%a %b %d %H:%M:%S %Y %z", "%Y-%m-%dT%H:%M:%S%z", "%Y-%m-%d %H:%M:%S%z"):
                            try:
                                parsed_date = datetime.datetime.strptime(date_str, fmt)
                                break
                            except (ValueError, TypeError):
                                continue
                        if parsed_date:
                            dates.append(parsed_date)
                        elif date_str:
                             logger.debug(f"Could not parse commit date '{date_str}' for {doc_item['path']}")

                # Sort dates
                dates.sort()

                # Calculate intervals between updates
                intervals = [(dates[i+1] - dates[i]).days for i in range(len(dates)-1)]

                # Store for analysis
                update_frequency[doc_item["path"]] = intervals
            except Exception as e: # Catch any other unexpected errors during parsing or processing
                logger.debug(f"Error processing commit dates for {doc_item['path']}: {e}")


    # Calculate average update interval for each document
    avg_update_intervals = []
    for path, intervals in update_frequency.items():
        if intervals:
            avg_interval = sum(intervals) / len(intervals)
            doc_item = next((d for d in docs_data if d["path"] == path), None)
            if doc_item:
                avg_update_intervals.append({
                    "path": path,
                    "title": doc_item.get("title", os.path.basename(path)),
                    "avg_days_between_updates": avg_interval,
                    "update_count": len(intervals) + 1  # +1 for the first commit
                })

    # Sort by average interval
    avg_update_intervals.sort(key=lambda x: x.get("avg_days_between_updates", float('inf'))) # Use get with default, sort inf last

    duration = time.time() - start_time
    logger.info(f"Analyzed maintenance patterns in {duration:.2f}s")

    return {
        "most_updated": most_updated[:20],
        "least_updated": least_updated[:20],
        "multi_author_docs": multi_author_docs,
        "top_authors": top_authors,
        "fastest_updated": avg_update_intervals[:20],
        "analysis_time": duration
    }
