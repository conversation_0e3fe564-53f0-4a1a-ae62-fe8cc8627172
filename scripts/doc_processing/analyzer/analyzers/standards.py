"""
Analyzes documentation against standards and best practices.
"""
import time
import logging
import re
from collections import defaultdict
from typing import List, Dict, Any

# Assuming necessary constants and classes are available or passed
# Import constants that are directly used.
from ..core.constants import MIN_DOC_BYTES, EXPECTED_METADATA_FIELDS

logger = logging.getLogger(__name__)

def analyze_doc_standards(
    docs_data: List[Dict[str, Any]],
    doc_store: Any
) -> Dict[str, Any]:
    """
    Analyze documentation against standards and best practices.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Analyzing documentation standards...")
    start_time = time.time()

    # Define standards checks
    standards = {
        "has_title": 0,
        "has_description": 0,
        "has_headings": 0,
        "has_examples": 0,
        "has_links": 0,
        "has_toc": 0,
        "proper_heading_hierarchy": 0
    }

    # Track documents failing standards
    failing_standards = defaultdict(list)

    # Analyze each document
    for doc_item in docs_data:
        path = doc_item["path"]
        summary = doc_store.get_summary(path, doc_item)
        content = doc_store.get_content(path)

        # Skip tiny documents
        if summary.size < MIN_DOC_BYTES: # MIN_DOC_BYTES imported
            continue

        # Check for title
        has_title = bool(summary.title and summary.title != os.path.basename(path))
        if has_title:
            standards["has_title"] += 1
        else:
            failing_standards["has_title"].append(path)

        # Check for description
        has_description = bool(summary.description)
        if has_description:
            standards["has_description"] += 1
        else:
            failing_standards["has_description"].append(path)

        # Check for headings
        has_headings = len(summary.headings) >= 2
        if has_headings:
            standards["has_headings"] += 1
        else:
            failing_standards["has_headings"].append(path)

        # Check for examples (code blocks)
        has_examples = bool(re.search(r'```', content))
        if has_examples:
            standards["has_examples"] += 1
        else:
            failing_standards["has_examples"].append(path)

        # Check for links
        has_links = bool(re.search(r'\[.+?\]\(.+?\)', content))
        if has_links:
            standards["has_links"] += 1
        else:
            failing_standards["has_links"].append(path)

        # Check for table of contents
        has_toc = bool(re.search(r'(table of contents|toc|contents)', content, re.IGNORECASE))
        if has_toc:
            standards["has_toc"] += 1
        else:
            failing_standards["has_toc"].append(path)

        # Check for proper heading hierarchy
        proper_hierarchy = True
        if summary.headings:
            # Extract heading levels
            heading_levels = [len(h[0]) for h in re.findall(r'^(#{1,6}) (.+)$', content, re.MULTILINE)]

            # Check if levels are sequential (no skipping, e.g., h1 -> h3)
            if heading_levels:
                for i in range(1, len(heading_levels)):
                    if heading_levels[i] > heading_levels[i-1] + 1:
                        proper_hierarchy = False
                        break
        else:
            proper_hierarchy = False

        if proper_hierarchy:
            standards["proper_heading_hierarchy"] += 1
        else:
            failing_standards["proper_heading_hierarchy"].append(path)

    # Calculate compliance percentages
    total_docs = len(docs_data)
    compliance = {}
    if total_docs > 0: # Avoid division by zero
        for standard, count in standards.items():
            compliance[standard] = count / total_docs * 100
    else:
        # Handle case where there are no documents
        compliance = {standard: 0.0 for standard in standards}


    # Identify best and worst standards
    sorted_compliance = sorted(compliance.items(), key=lambda x: x[1])
    worst_standards = sorted_compliance[:3]
    best_standards = sorted_compliance[-3:]

    duration = time.time() - start_time
    logger.info(f"Analyzed documentation standards in {duration:.2f}s")

    return {
        "compliance": compliance,
        "worst_standards": worst_standards,
        "best_standards": best_standards,
        "failing_docs": {standard: paths for standard, paths in failing_standards.items()},
        "analysis_time": duration
    }
