"""
Analyzes document metadata completeness and patterns.
"""
import time
import logging
import os
from collections import defaultdict
from typing import List, Dict, Any

# Assuming necessary constants and classes are available or passed
# Import constants that are directly used.
from ..core.constants import EXPECTED_METADATA_FIELDS

logger = logging.getLogger(__name__)

def analyze_metadata(
    docs_data: List[Dict[str, Any]],
    doc_store: Any
) -> Dict[str, Any]:
    """
    Analyze document metadata completeness and patterns.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info("Analyzing document metadata...")
    start_time = time.time()

    # Track metadata statistics
    metadata_stats = {
        "complete": [],
        "partial": [],
        "missing": [],
        "field_presence": defaultdict(int),
        "avg_score": 0.0
    }

    # Track metadata by document type
    metadata_by_type = defaultdict(list)

    # Analyze each document
    total_score = 0.0
    for doc_item in docs_data:
        path = doc_item["path"]
        summary = doc_store.get_summary(path, doc_item)

        # Add to appropriate category based on metadata score
        if summary.metadata_score >= 0.75:
            metadata_stats["complete"].append({
                "path": path,
                "title": summary.title,
                "score": summary.metadata_score,
                "fields": list(summary.metadata_fields.keys())
            })
        elif summary.metadata_score >= 0.3:
            metadata_stats["partial"].append({
                "path": path,
                "title": summary.title,
                "score": summary.metadata_score,
                "fields": list(summary.metadata_fields.keys()),
                "missing_fields": [field for field in EXPECTED_METADATA_FIELDS # EXPECTED_METADATA_FIELDS imported
                                  if field not in summary.metadata_fields]
            })
        else:
            metadata_stats["missing"].append({
                "path": path,
                "title": summary.title,
                "score": summary.metadata_score,
                "fields": list(summary.metadata_fields.keys()) if summary.metadata_fields else []
            })

        # Track field presence
        for field in summary.metadata_fields:
            metadata_stats["field_presence"][field] += 1

        # Track by document type
        metadata_by_type[summary.doc_type].append(summary.metadata_score)

        # Add to total score
        total_score += summary.metadata_score

    # Calculate average metadata score
    if docs_data: # Ensure docs_data is not empty
        metadata_stats["avg_score"] = total_score / len(docs_data)

    # Calculate average score by document type
    avg_by_type = {}
    for doc_type, scores in metadata_by_type.items():
        if scores: # Ensure scores list is not empty
            avg_by_type[doc_type] = sum(scores) / len(scores)
        else:
            avg_by_type[doc_type] = 0.0 # Handle empty scores list

    # Calculate field presence percentages
    field_presence_pct = {}
    if docs_data: # Ensure docs_data is not empty
        total_docs = len(docs_data)
        for field, count in metadata_stats["field_presence"].items():
            field_presence_pct[field] = count / total_docs * 100
    else:
        # Handle case where docs_data is empty
        field_presence_pct = {field: 0.0 for field in EXPECTED_METADATA_FIELDS}


    duration = time.time() - start_time
    logger.info(f"Analyzed document metadata in {duration:.2f}s")

    return {
        "complete_count": len(metadata_stats["complete"]),
        "partial_count": len(metadata_stats["partial"]),
        "missing_count": len(metadata_stats["missing"]),
        "complete_docs": metadata_stats["complete"],
        "partial_docs": metadata_stats["partial"],
        "missing_docs": metadata_stats["missing"],
        "field_presence": field_presence_pct,
        "avg_score": metadata_stats["avg_score"],
        "avg_by_type": avg_by_type,
        "analysis_time": duration
    }
