#!/usr/bin/env python3
import os
import argparse

try:
    import pathspec
except ImportError:
    print("Please install the 'pathspec' library (pip install pathspec)")
    exit(1)

def load_ignore_spec(folder_path):
    """
    Loads .gitignore patterns from the specified folder (if present)
    and returns a compiled PathSpec object.
    """
    gitignore_path = os.path.join(folder_path, '.gitignore')
    if os.path.exists(gitignore_path):
        with open(gitignore_path, 'r', encoding="utf-8") as f:
            patterns = f.read().splitlines()
        return pathspec.PathSpec.from_lines('gitwildmatch', patterns)
    return None

def generate_markdown(folder_path, output_file):
    """
    Recursively traverses folder_path and writes its structure to output_file in Markdown format,
    while ignoring files and directories based on .gitignore patterns (if available).
    """
    ignore_spec = load_ignore_spec(folder_path)

    markdown_lines = []
    markdown_lines.append(f"# Folder Structure for `{folder_path}`\n")

    for root, dirs, files in os.walk(folder_path):
        # Compute the relative path from the root folder for matching.
        rel_root = os.path.relpath(root, folder_path)
        if rel_root == ".":
            rel_root = ""
        
        # Filter out directories and files that match the ignore patterns.
        if ignore_spec:
            dirs[:] = [
                d for d in dirs
                if not ignore_spec.match_file(os.path.join(rel_root, d).replace(os.sep, '/'))
            ]
            files = [
                f for f in files
                if not ignore_spec.match_file(os.path.join(rel_root, f).replace(os.sep, '/'))
            ]

        # Determine the current level of indentation.
        level = rel_root.count(os.sep) if rel_root else 0
        indent = "    " * level
        
        # Display the directory name (bold), using the folder's basename for the root.
        if root == folder_path:
            folder_name = os.path.basename(os.path.abspath(folder_path))
            markdown_lines.append(f"- **{folder_name}/**")
        else:
            folder_name = os.path.basename(root)
            markdown_lines.append(f"{indent}- **{folder_name}/**")
        
        # List files in the current directory.
        file_indent = "    " * (level + 1)
        for file in sorted(files):
            markdown_lines.append(f"{file_indent}- {file}")

    # Write the Markdown content to the specified output file.
    with open(output_file, "w", encoding="utf-8") as f:
        f.write("\n".join(markdown_lines))

    print(f"Markdown file '{output_file}' created successfully.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Generate a Markdown file showing the folder and file structure of a directory, ignoring files/folders as specified by a .gitignore file."
    )
    parser.add_argument("-f", "--folder", default=".", help="Path to the folder to scan")
    parser.add_argument(
        "-o", "--output", default="folder_structure.md",
        help="Name of the output Markdown file (default: folder_structure.md)"
    )
    args = parser.parse_args()

    if not os.path.isdir(args.folder):
        print(f"Error: The folder '{args.folder}' does not exist or is not a directory.")
    else:
        generate_markdown(args.folder, args.output)
