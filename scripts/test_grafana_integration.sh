#!/bin/bash

# Grafana Integration Test Runner
# This script runs comprehensive tests for the Grafana integration

set -e

echo "🧪 Running Grafana Integration Tests"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "backend/docker-compose.yml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

cd backend

# Test categories
CATEGORIES=(
    "test_grafana_integration"
    "test_grafana_setup" 
    "test_grafana_migration"
)

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
FAILED_TEST_DETAILS=()

print_status "Starting Grafana integration test suite..."

# Ensure test database is ready
print_status "Preparing test environment..."
if ! docker-compose run --rm web-test python manage.py migrate --verbosity=0; then
    print_error "Failed to prepare test database"
    exit 1
fi

# Run each test category
for category in "${CATEGORIES[@]}"; do
    print_status "Running tests for: $category"
    
    # Run the tests and capture output
    if docker-compose run --rm web-test pytest "apps/main/tests/${category}.py" -v --tb=short > "/tmp/${category}_output.txt" 2>&1; then
        # Parse test results
        test_count=$(grep -c "PASSED\|FAILED\|ERROR" "/tmp/${category}_output.txt" || echo "0")
        passed_count=$(grep -c "PASSED" "/tmp/${category}_output.txt" || echo "0")
        failed_count=$(grep -c "FAILED\|ERROR" "/tmp/${category}_output.txt" || echo "0")
        
        TOTAL_TESTS=$((TOTAL_TESTS + test_count))
        PASSED_TESTS=$((PASSED_TESTS + passed_count))
        FAILED_TESTS=$((FAILED_TESTS + failed_count))
        
        if [ $failed_count -eq 0 ]; then
            print_success "$category: $passed_count/$test_count tests passed"
        else
            print_warning "$category: $passed_count/$test_count tests passed, $failed_count failed"
            FAILED_TEST_DETAILS+=("$category: $failed_count failed tests")
        fi
    else
        print_error "Failed to run tests for $category"
        FAILED_TEST_DETAILS+=("$category: Test execution failed")
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    echo ""
done

# Run specific integration tests
print_status "Running database view integration tests..."
if docker-compose run --rm web-test pytest "apps/main/tests/test_grafana_integration.py::TestGrafanaAnalyticsViews" -v; then
    print_success "Database view tests passed"
else
    print_error "Database view tests failed"
    FAILED_TEST_DETAILS+=("Database view integration tests failed")
fi

print_status "Running query performance tests..."
if docker-compose run --rm web-test pytest "apps/main/tests/test_grafana_integration.py::TestGrafanaViewQueries" -v; then
    print_success "Query performance tests passed"
else
    print_error "Query performance tests failed"
    FAILED_TEST_DETAILS+=("Query performance tests failed")
fi

print_status "Running edge case tests..."
if docker-compose run --rm web-test pytest "apps/main/tests/test_grafana_integration.py::TestGrafanaViewEdgeCases" -v; then
    print_success "Edge case tests passed"
else
    print_error "Edge case tests failed"
    FAILED_TEST_DETAILS+=("Edge case tests failed")
fi

# Test configuration files
print_status "Validating configuration files..."

CONFIG_TESTS_PASSED=0
CONFIG_TESTS_TOTAL=5

# Test 1: Check if Grafana provisioning files exist
if [ -f "../monitoring/grafana/provisioning/datasources/postgres.yml" ] && 
   [ -f "../monitoring/grafana/provisioning/dashboards/dashboard-provider.yml" ]; then
    print_success "Grafana provisioning files exist"
    CONFIG_TESTS_PASSED=$((CONFIG_TESTS_PASSED + 1))
else
    print_error "Grafana provisioning files missing"
fi

# Test 2: Check if dashboard files exist
DASHBOARD_COUNT=$(find "../monitoring/grafana/dashboards" -name "*.json" | wc -l)
if [ $DASHBOARD_COUNT -ge 5 ]; then
    print_success "Dashboard files exist ($DASHBOARD_COUNT found)"
    CONFIG_TESTS_PASSED=$((CONFIG_TESTS_PASSED + 1))
else
    print_error "Insufficient dashboard files ($DASHBOARD_COUNT found, expected >= 5)"
fi

# Test 3: Validate JSON syntax in dashboards
JSON_VALID=true
for dashboard in ../monitoring/grafana/dashboards/*/*.json; do
    if [ -f "$dashboard" ]; then
        if ! python -m json.tool "$dashboard" > /dev/null 2>&1; then
            print_error "Invalid JSON in $dashboard"
            JSON_VALID=false
        fi
    fi
done

if [ "$JSON_VALID" = true ]; then
    print_success "All dashboard JSON files are valid"
    CONFIG_TESTS_PASSED=$((CONFIG_TESTS_PASSED + 1))
else
    print_error "Some dashboard JSON files are invalid"
fi

# Test 4: Check docker-compose configuration
if grep -q "grafana:" docker-compose.yml && grep -q "prometheus:" docker-compose.yml; then
    print_success "Docker Compose configuration includes Grafana services"
    CONFIG_TESTS_PASSED=$((CONFIG_TESTS_PASSED + 1))
else
    print_error "Docker Compose configuration missing Grafana services"
fi

# Test 5: Check setup script
if [ -x "../scripts/setup_grafana.sh" ]; then
    print_success "Setup script exists and is executable"
    CONFIG_TESTS_PASSED=$((CONFIG_TESTS_PASSED + 1))
else
    print_error "Setup script missing or not executable"
fi

# Test database views exist (if database is available)
print_status "Testing database view creation..."
if docker-compose run --rm web-test python -c "
from django.db import connection
with connection.cursor() as cursor:
    cursor.execute(\"SELECT COUNT(*) FROM information_schema.views WHERE table_name LIKE 'grafana_%'\")
    count = cursor.fetchone()[0]
    print(f'Found {count} Grafana views')
    assert count >= 4, f'Expected at least 4 views, found {count}'
print('Database views test passed')
"; then
    print_success "Database views exist and are accessible"
else
    print_error "Database views test failed"
    FAILED_TEST_DETAILS+=("Database views not properly created")
fi

# Summary
echo ""
echo "📊 Test Summary"
echo "==============="
echo "Total Tests Run: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"
echo "Configuration Tests: $CONFIG_TESTS_PASSED/$CONFIG_TESTS_TOTAL"

if [ $FAILED_TESTS -eq 0 ] && [ $CONFIG_TESTS_PASSED -eq $CONFIG_TESTS_TOTAL ]; then
    print_success "All Grafana integration tests passed! 🎉"
    echo ""
    echo "✅ Database views created successfully"
    echo "✅ Configuration files validated"
    echo "✅ Dashboard JSON files are valid"
    echo "✅ Docker Compose integration working"
    echo "✅ Setup script ready for deployment"
    echo ""
    echo "🚀 Grafana integration is ready for production!"
    exit 0
else
    print_error "Some tests failed:"
    for detail in "${FAILED_TEST_DETAILS[@]}"; do
        echo "  ❌ $detail"
    done
    
    if [ $CONFIG_TESTS_PASSED -ne $CONFIG_TESTS_TOTAL ]; then
        echo "  ❌ Configuration validation: $CONFIG_TESTS_PASSED/$CONFIG_TESTS_TOTAL passed"
    fi
    
    echo ""
    echo "🔧 Please fix the failing tests before deploying Grafana integration."
    exit 1
fi
