import os

def merge_sections(sections_directory, output_file, sections_order):
    """
    Merges multiple content files (e.g., .md, .txt) into a single output file.

    :param sections_directory: The path to the folder containing the content sections.
    :param output_file: The path (including filename) for the merged output.
    :param sections_order: A list of filenames (in desired merge order).
    """
    merged_lines = []

    for filename in sections_order:
        file_path = os.path.join(sections_directory, filename)
        if not os.path.isfile(file_path):
            print(f"Warning: {file_path} does not exist. Skipping.")
            continue

        with open(file_path, "r", encoding="utf-8") as f:
            content = f.readlines()

        merged_lines.extend(content)

    # Write the merged content to the output file (overwrites each run)
    with open(output_file, "w", encoding="utf-8") as out:
        out.writelines(merged_lines)

    print(f"Merged content saved to: {output_file}")


if __name__ == "__main__":
    # Prompt user for MVP or Full description
    description_type = input("Which type of description do you want? (MVP / Full): ").strip().lower()

    # The directory containing all your content sections
    sections_dir = "./docs/global"  # Adjust if needed

    # Decide output filename based on user choice
    if description_type == "mvp":
        output_file = os.path.join(sections_dir, "description_MVP_generated.md")
    else:
        output_file = os.path.join(sections_dir, "description_FULL_generated.md")

    # Define the base order for normal sections (sections 8 and 9 are omitted)
    normal_sections = [
        "1_introduction.md",
        "2_ethical_framework.md",
        "3_user_experience.md",
        "4_system_strategy.md",
        "5_technical_architecture.md",
        "6_implementation_roadmap.md",
        "7_success_framework.md"
    ]

    final_order = []

    if description_type == "mvp":
        # For MVP, only include the normal sections (skip any FUTURE files)
        final_order = normal_sections.copy()
    else:
        # For Full, include normal sections and insert their FUTURE file (if it exists) immediately after.
        for normal_file in normal_sections:
            final_order.append(normal_file)
            # Build FUTURE filename by inserting (FUTURE) before the file extension
            future_file = normal_file.replace(".md", "(FUTURE).md")
            future_path = os.path.join(sections_dir, future_file)
            if os.path.isfile(future_path):
                final_order.append(future_file)

    # Merge the sections as determined by final_order
    merge_sections(sections_dir, output_file, sections_order=final_order)
