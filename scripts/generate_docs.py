import os
from pathlib import Path
import pkgutil
import sys
import django

import importlib



# Get the current working directory as a Path object
current_dir = Path.cwd()

# Specify the subfolder relative to the current directory
# Replace 'your_subfolder' with the name of the subfolder you want to include
subfolder = current_dir / 'backend'

# Add the subfolder to sys.path so Python will search there for modules
sys.path.insert(0, str(subfolder))
# Set the settings module to your project's settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.base")

django.setup()
from apps.main.agents.tools.tools_registration import get_tool_registry_info

def import_all_modules_from_package(package_name: str):
    """
    Recursively imports all modules in the given package.
    This forces the execution of module-level code (e.g. decorators) so that all tool
    functions register themselves in the global registry.
    """
    package = importlib.import_module(package_name)
    if hasattr(package, '__path__'):
        for finder, name, ispkg in pkgutil.walk_packages(package.__path__, package.__name__ + "."):
            importlib.import_module(name)
    else:
        # The package is a single module
        importlib.import_module(package_name)

def generate_documentation(output_file: str):
    """
    Imports tool modules, collects documentation from registered tool functions,
    and writes a Markdown file.
    """
    # List (or packages) where your decorated tool functions are defined.
    # Update these paths with your actual module/package names.
    modules_to_import = [
        "apps.main.agents.tools.tools", 
        "apps.main.agents.tools.extra_tools",
        "apps.main.agents.tools.dispatcher_tools",
        "apps.main.agents.tools.user_state_analysis_tool",
    ]
    
    # Import all modules so that registration occurs.
    for module_name in modules_to_import:
        import_all_modules_from_package(module_name)
    
    # Get the tool info from the registry (including docstrings and signatures)
    tools_info = get_tool_registry_info()
    
    # Prepare Markdown documentation using a simple template.
    md_lines = []
    md_lines.append("# Tools Documentation")
    md_lines.append("")
    
    tool_entry_template = (
        "## {tool_code}: {name}\n\n"
        "{description}\n\n"
        "**Function Path:** `{path}`\n\n"
        "**Input Schema:**\n```json\n{input_schema}\n```\n\n"
        "**Output Schema:**\n```json\n{output_schema}\n```\n\n"
        "---\n"
    )
    
    for tool_code, info in tools_info.items():
        formatted_desc = info.get("description", "No documentation available.").replace("\n", "\n\n")
        entry = tool_entry_template.format(
            tool_code=tool_code,
            name=info.get("name", "No name"),
            description=formatted_desc,
            path=info.get("path", "Unknown"),
            input_schema=info.get("input_schema", {}),
            output_schema=info.get("output_schema", {})
        )
        md_lines.append(entry)
    
    # Write the documentation to a Markdown file
    with open(f"./backend/docs/ai_distill/{output_file}", "w", encoding="utf-8") as f:
        f.write("\n".join(md_lines))
    
    print(f"Documentation generated in {output_file}")

if __name__ == "__main__":
    generate_documentation("tools_gen.md")