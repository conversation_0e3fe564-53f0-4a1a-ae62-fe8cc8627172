#!/bin/bash
# Run tests inside the Docker container and update coverage.json

set -e

# Change to the project root directory
cd "$(dirname "$0")/.."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo "Docker is not running. Please start Docker first."
  exit 1
fi

# Make sure docker compose is available
if ! command -v docker > /dev/null 2>&1; then
  echo "Docker is not installed. Please install it first."
  exit 1
fi

# Check if the web container is running
if ! docker compose -f ./backend/docker-compose.yml ps | grep -q "web.*Up"; then
  echo "Starting Docker containers..."
  docker compose -f ./backend/docker-compose.yml up -d
else
  echo "Docker containers are already running."
fi

echo "Running tests using the web-test service..."
# Use docker compose run to start the web-test service, run its command, and remove it
# The command (migrations + pytest) is defined in docker-compose.yml for web-test
docker compose -f ./backend/docker-compose.yml run --rm web-test

# Check if the coverage.json file exists
if [ -f "./backend/coverage.json" ]; then
  echo "Coverage data generated successfully!"
  echo "View the coverage dashboard at http://localhost:8000/coverage/"
else
  echo "Warning: coverage.json was not generated. Check for errors in the test output."
fi