#!/bin/bash

# Comprehensive Monitoring System Test Script
# Tests Grafana, Prometheus, and database connectivity

set -e

echo "🔍 Comprehensive Monitoring System Test"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "backend/docker-compose.yml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

cd backend

# Test 1: Check if all services are running
print_status "Testing service availability..."

services=("db" "grafana" "prometheus" "redis")
for service in "${services[@]}"; do
    if docker-compose ps | grep -q "${service}.*Up"; then
        print_success "$service service is running"
    else
        print_error "$service service is not running"
        exit 1
    fi
done

# Test 2: Check database connectivity and views
print_status "Testing database connectivity and views..."

# Check database connection
if docker-compose exec -T db pg_isready -U postgres > /dev/null 2>&1; then
    print_success "Database connection OK"
else
    print_error "Database connection failed"
    exit 1
fi

# Check if Grafana views exist
view_count=$(docker-compose exec -T db psql -U postgres -d mydb -t -c "SELECT COUNT(*) FROM information_schema.views WHERE table_name LIKE 'grafana_%';" 2>/dev/null | tr -d ' \n' || echo "0")
if [ "$view_count" -eq "4" ]; then
    print_success "All 4 Grafana analytics views exist"
else
    print_error "Expected 4 Grafana views, found $view_count"
    exit 1
fi

# Check if views have data
data_count=$(docker-compose exec -T db psql -U postgres -d mydb -t -c "SELECT COUNT(*) FROM grafana_llm_performance;" 2>/dev/null | tr -d ' \n' || echo "0")
if [ "$data_count" -gt "0" ]; then
    print_success "Grafana views contain $data_count records"
else
    print_warning "Grafana views are empty - consider running benchmarks to populate data"
fi

# Test 3: Check Grafana connectivity and datasources
print_status "Testing Grafana connectivity..."

# Wait for Grafana to be ready
for i in {1..30}; do
    if curl -s http://localhost:3000/api/health > /dev/null 2>&1; then
        print_success "Grafana is accessible"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Grafana is not accessible after 30 seconds"
        exit 1
    fi
    sleep 1
done

# Check datasource
datasource_status=$(docker-compose exec -T grafana curl -s -u admin:admin http://localhost:3000/api/datasources/uid/postgres-benchmarks/health 2>/dev/null | grep -o '"status":"[^"]*"' | cut -d'"' -f4 || echo "FAILED")
if [ "$datasource_status" = "OK" ]; then
    print_success "PostgreSQL datasource connection OK"
else
    print_error "PostgreSQL datasource connection failed: $datasource_status"
    exit 1
fi

# Check dashboards
dashboard_count=$(docker-compose exec -T grafana curl -s -u admin:admin http://localhost:3000/api/search?type=dash-db 2>/dev/null | grep -o '"uid"' | wc -l || echo "0")
if [ "$dashboard_count" -ge "4" ]; then
    print_success "Found $dashboard_count dashboards"
else
    print_error "Expected at least 4 dashboards, found $dashboard_count"
    exit 1
fi

# Test 4: Check Prometheus connectivity
print_status "Testing Prometheus connectivity..."

if curl -s http://localhost:9090/api/v1/status/config > /dev/null 2>&1; then
    print_success "Prometheus is accessible"
else
    print_warning "Prometheus is not accessible - this is optional for benchmark monitoring"
fi

# Test 5: Test sample queries
print_status "Testing sample dashboard queries..."

# Test LLM performance query
llm_query_result=$(docker-compose exec -T db psql -U postgres -d mydb -t -c "SELECT COUNT(*) FROM grafana_llm_performance WHERE execution_date >= NOW() - INTERVAL '7 days';" 2>/dev/null | tr -d ' \n' || echo "0")
print_success "LLM performance query returned $llm_query_result records (last 7 days)"

# Test contextual evaluation query
contextual_query_result=$(docker-compose exec -T db psql -U postgres -d mydb -t -c "SELECT COUNT(DISTINCT trust_phase) FROM grafana_contextual_evaluation;" 2>/dev/null | tr -d ' \n' || echo "0")
print_success "Contextual evaluation query found $contextual_query_result distinct trust phases"

# Test cost analytics query
cost_query_result=$(docker-compose exec -T db psql -U postgres -d mydb -t -c "SELECT COUNT(*) FROM grafana_cost_analytics;" 2>/dev/null | tr -d ' \n' || echo "0")
print_success "Cost analytics query returned $cost_query_result records"

echo ""
echo "🎉 Monitoring System Test Complete!"
echo "==================================="
echo ""
echo "📊 Test Results Summary:"
echo "  ✅ All services running"
echo "  ✅ Database connectivity OK"
echo "  ✅ Grafana analytics views present ($view_count/4)"
echo "  ✅ Grafana accessible and configured"
echo "  ✅ PostgreSQL datasource working"
echo "  ✅ Dashboards provisioned ($dashboard_count found)"
echo "  ✅ Sample queries working"
echo ""
echo "🔗 Access URLs:"
echo "  • Grafana: http://localhost:3000 (admin/admin)"
echo "  • Prometheus: http://localhost:9090"
echo ""
echo "📈 Available Dashboards:"
echo "  • LLM Performance Overview"
echo "  • Contextual Evaluation Insights"
echo "  • Cost Analytics Dashboard"
echo "  • Prompt Effectiveness Dashboard"
echo "  • Advanced Benchmark Analytics"
echo ""

if [ "$data_count" -eq "0" ]; then
    echo "💡 Next Steps:"
    echo "  Run benchmarks to populate dashboards with data:"
    echo "  docker-compose run --rm web python manage.py run_benchmarks"
    echo ""
fi

print_success "Monitoring system is fully operational! 🚀"

cd ..
