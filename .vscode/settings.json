{
    // backend/.vscode/settings.json
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "python.testing.nosetestsEnabled": false,

    // Use a custom test runner command to run tests inside Docker Compose
    "python.testing.pytestPath": "pytest",
    "python.testing.pytestArgs": [
        "--import-mode=importlib",
        "backend/apps/main/tests"
    ],

    // Rely on the interpreter selected in the VS Code status bar
    // "python.defaultInterpreterPath": "{workspaceFolder}/.venv/Scripts/python.exe", 

    "files.eol": "\n",
    
    // Environment variables for test explorer
    "python.envFile": "${workspaceFolder}/backend/.env.test",
    
    // Auto-discover Python tests
    "python.testing.autoTestDiscoverOnSaveEnabled": true
}
