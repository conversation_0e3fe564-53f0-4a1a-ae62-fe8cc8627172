{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "start-django-wait-option",
      "type": "shell",
      // Set WAIT_FOR_DEBUGGER env var directly for the docker compose command (rely on finding file in cwd)
      "command": "cd \"${workspaceFolder}/backend\" && WAIT_FOR_DEBUGGER=${input:waitForDebugger} docker compose up --build --force-recreate -d web",
      "windows": {
        // Run commands sequentially without explicit script block to ensure task waits
        "command": "powershell -Command \"Set-Location -Path '${workspaceFolder}\\backend'; $env:WAIT_FOR_DEBUGGER='${input:waitForDebugger}'; docker compose -f '${workspaceFolder}\\backend\\docker-compose.yml' up --force-recreate -d web\""
      },
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "restart-django-wait-option",
      "type": "shell",
      // Set WAIT_FOR_DEBUGGER env var directly for the docker compose command (rely on finding file in cwd)
      "command": "cd \"${workspaceFolder}/backend\" && WAIT_FOR_DEBUGGER=${input:waitForDebugger} docker compose restart web",
      "windows": {
        // Run commands sequentially without explicit script block to ensure task waits
        "command": "powershell -Command \"Set-Location -Path '${workspaceFolder}\\backend'; $env:WAIT_FOR_DEBUGGER='${input:waitForDebugger}'; docker compose -f '${workspaceFolder}\\backend\\docker-compose.yml' restart web\""
      },
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "start-everything-wait-option",
      "type": "shell",
      // Set WAIT_FOR_DEBUGGER env var directly for the docker compose command (rely on finding file in cwd)
      "command": "cd \"${workspaceFolder}/backend\" && WAIT_FOR_DEBUGGER=${input:waitForDebugger} docker compose up -d",
      "windows": {
        // Run commands sequentially without explicit script block to ensure task waits
        "command": "powershell -Command \"Set-Location -Path '${workspaceFolder}\\backend'; $env:WAIT_FOR_DEBUGGER='${input:waitForDebugger}'; docker compose -f '${workspaceFolder}\\backend\\docker-compose.yml' up -d\"" // Note: Fixed potential missing quote at end from previous state
      },
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "run-management-command-debug",
      "type": "shell",
      // Specify config file for exec
      "command": "cd \"${workspaceFolder}/backend\" && docker compose -f docker-compose.yml exec web python -m debugpy --wait-for-client --listen 0.0.0.0:5679 manage.py ${input:commandAndArgs}",
      "windows": {
        // Run commands sequentially without explicit script block
        "command": "powershell -Command \"Set-Location -Path '${workspaceFolder}\\backend'; docker compose -f '${workspaceFolder}\\backend\\docker-compose.yml' exec web python -m debugpy --wait-for-client --listen 0.0.0.0:5679 manage.py ${input:commandAndArgs}\""
      },
      "presentation": {
        "reveal": "always",
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "start-test-debug-container",
      "type": "shell",
      // Specify config file for up
      "command": "cd \"${workspaceFolder}/backend\" && docker compose -f docker-compose.yml up -d debug-tests",
      "windows": {
        // Run commands sequentially without explicit script block
        "command": "powershell -Command \"Set-Location -Path '${workspaceFolder}\\backend'; docker compose -f '${workspaceFolder}\\backend\\docker-compose.yml' up -d debug-tests\""
      },
      "presentation": {
        "reveal": "never", // Don't need to see this panel
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "run-test-debug-container-with-args",
      "type": "shell",
      // Use docker compose run to pass env var easily and manage container lifecycle, specify config file
      "command": "cd \"${workspaceFolder}/backend\" && docker compose -f docker-compose.yml run --rm --service-ports -e PYTEST_ARGS=\"${input:pytestArgs}\" debug-tests",
      "windows": {
        // Run commands sequentially without explicit script block
        "command": "powershell -Command \"Set-Location -Path '${workspaceFolder}\\backend'; docker compose -f '${workspaceFolder}\\backend\\docker-compose.yml' run --rm --service-ports -e PYTEST_ARGS='${input:pytestArgs}' debug-tests\""
      },
      "presentation": {
        "reveal": "always", // Show panel for this task
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
        "label": "Run Docker Tests within dev container",
        "type": "shell",
        "command": "docker-compose exec -T -w /usr/src/app/backend web pytest --import-mode=importlib backend/apps/main/tests",
        "group": {
            "kind": "test",
            "isDefault": true
        },
        "presentation": {
            "reveal": "always",
            "panel": "new"
        },
        "problemMatcher": []
    }
  ],
  "inputs": [
    {
      "id": "waitForDebugger",
      "type": "pickString",
      "description": "Wait for debugger to attach?",
      "options": [
        {
          "label": "Yes",
          "value": "true"
        },
        {
          "label": "No",
          "value": "false"
        }
      ],
      "default": "false"
    },
    {
      "id": "commandAndArgs",
      "type": "promptString",
      "description": "Enter the management command and arguments (e.g., 'check --deploy' or 'seed_db_phiphi')",
      "default": "check"
    },
    {
      "id": "pytestArgs",
      "type": "promptString",
      "description": "Enter pytest arguments (e.g., '-k test_specific', 'apps/main/tests/test_file.py', or leave empty for all)",
      "default": ""
    }
  ]
}
