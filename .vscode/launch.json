{
  "version": "0.2.0",
  "inputs": [
    {
      "id": "waitForDebugger",
      "type": "pickString",
      "description": "Wait for debugger to attach?",
      "options": ["yes", "no"],
      "default": "no"
    },
    {
      "id": "commandAndArgs",
      "type": "promptString",
      "description": "Enter the management command and arguments (e.g., 'check --deploy' or 'seed_db_phiphi')",
      "default": "check"
    }
  ],
  "configurations": [
    // --- Development Debugging ---
    {
      "name": "Django: Debug",
      "type": "python",
      "request": "attach",
      "connect": {
        "host": "localhost",
        "port": 5678
      },
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}/backend",
          "remoteRoot": "/usr/src/app"
        }
      ],
      "django": true,
      "justMyCode": false,
      "presentation": {
        "hidden": false,
        "group": "1. Django Dev",
        "order": 1
      },
      "preLaunchTask": "start-django-wait-option" // Uses waitForDebugger input
    },
    {
      "name": "Django: Debug Admin (Direct)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/backend/manage.py",
      "args": [
        "runserver",
        "--noreload",
        "--nothreading"
      ],
      "django": true,
      "justMyCode": false,
      "presentation": {
        "hidden": false,
        "group": "1. Django Dev",
        "order": 3
      },
      "envFile": "${workspaceFolder}/backend/.env.dev"
    },
    {
      "name": "Django: Restart",
      "type": "python",
      "request": "attach",
      "connect": {
        "host": "localhost",
        "port": 5678
      },
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}/backend",
          "remoteRoot": "/usr/src/app"
        }
      ],
      "django": true,
      "justMyCode": false,
      "preLaunchTask": "restart-django-wait-option", // Uses waitForDebugger input
      "presentation": {
        "hidden": false,
        "group": "1. Django Dev",
        "order": 2
      }
    },
    // Removed redundant "Django: Debug (attach only)"
    // --- Management Command Debugging ---
    {
      "name": "Django: Management Command",
      "type": "python",
      "request": "attach",
      "connect": {
        "host": "localhost",
        "port": 5679 // Debug port exposed by the 'run-management-command-debug' task
      },
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}/backend", // Map backend folder
          "remoteRoot": "/usr/src/app"
        }
      ],
      "django": true,
      "justMyCode": false,
      "timeout": 30000, // Increased timeout
      "showReturnValue": true,
      "preLaunchTask": "run-management-command-debug", // Runs the command with debugpy
      "presentation": {
        "hidden": false,
        "group": "2. Django Tools",
        "order": 1
      }
    },
    // --- Celery Debugging ---
    {
      "name": "Django: Celery Worker",
      "type": "python",
      "request": "attach",
      "connect": {
        "host": "localhost",
        "port": 5680 // Port exposed by celery service in docker-compose
      },
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}/backend", // Map backend folder
          "remoteRoot": "/usr/src/app"
        }
      ],
      "justMyCode": false,
      "django": true,
      "preLaunchTask": "docker-compose up -d celery", // Ensure celery container is running
      "presentation": {
        "hidden": false,
        "group": "3. Background Tasks",
        "order": 1
      }
    },
    // --- Test Debugging ---
    {
      "name": "Django: Debug Test",
      "type": "python",
      "request": "attach",
  "connect": {
    "host": "localhost",
    "port": 5681 // Port exposed by debug-tests service in docker-compose.yml
  },
  "pathMappings": [
        {
          "localRoot": "${workspaceFolder}/backend", // Map backend folder
          "remoteRoot": "/usr/src/app"
        }
  ],
  "django": true,
  "justMyCode": false,
  "preLaunchTask": "start-test-debug-container", // Task to start the debug-tests container
  "presentation": {
    "hidden": false,
    "group": "4. Testing",
        "order": 1
      }
    },
    // --- Test Debugging with Selection ---
    {
      "name": "Django: Debug Test (Select)",
      "type": "python",
      "request": "attach",
      "connect": {
        "host": "localhost",
        "port": 5681 // Port exposed by debug-tests service in docker-compose.yml
      },
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}/backend", // Map backend folder
          "remoteRoot": "/usr/src/app"
        }
      ],
      "django": true,
      "justMyCode": false,
      "preLaunchTask": "run-test-debug-container-with-args", // Task that prompts for args and runs the container
      "presentation": {
        "hidden": false,
        "group": "4. Testing",
        "order": 2 // Place it after the default debug test
      }
    }
    // Removed "the whole thing in Docker" and "Python: Debug Tests"
  ]
}
