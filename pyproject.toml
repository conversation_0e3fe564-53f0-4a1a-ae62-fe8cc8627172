[tool.pytest.ini_options]
# Django settings
DJANGO_SETTINGS_MODULE = "config.settings.test"

# Python path configuration
pythonpath = [
    "./backend",
]

# Test discovery paths
testpaths = [
    "./backend/apps", 
]

# Additional command-line options
addopts = [
    "--import-mode=importlib",
    "-v",
    "--cov=backend/apps",
    "--cov-report=term-missing",
    "--cov-report=html:backend/coverage_html",
    "--cov-report=lcov:backend/coverage.lcov",
    "--cov-report=json:backend/coverage.json",
    # Added to create the database based on the django_db_setup fixture
    "--reuse-db",
]

# Test file discovery patterns
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"

# Markers for categorizing tests
markers = [
    "asyncio: mark a test as an asyncio coroutine",
    "django: mark a test as a Django test",
    "workflow: mark a test as part of a workflow",
    "llm: mark a test as related to LLMs",
    "component: mark a test as a component test",
    "tool: mark a test as a tool test",
    "test_type: mark a test with a specific type",
]

# Asyncio configuration (using pytest-asyncio)
asyncio_mode = "strict"

# Logging configuration
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
log_format = "%(asctime)s %(levelname)s %(message)s"

# Filter out noisy warnings
filterwarnings = [
    "ignore::DeprecationWarning",
]

# Ensure all tests marked with django_db run within an atomic transaction
django_atomic_tests = true
