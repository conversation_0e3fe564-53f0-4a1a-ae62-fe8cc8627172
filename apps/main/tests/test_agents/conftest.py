import os
import django
import pytest

# Set up Django before importing models
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')
django.setup()

# Now we can import Django models
from django.apps import apps
from apps.main.models import GenericAgent, LLMConfig

@pytest.fixture(scope="session", autouse=True)
def setup_django():
    """Set up Django for tests."""
    # Django is already set up above, but we include this fixture
    # to ensure it's explicitly documented and can be referenced
    pass

@pytest.fixture(scope="session")
def django_db_setup():
    """Set up the test database."""
    # This is handled by pytest-django, but we include it for clarity
    pass
