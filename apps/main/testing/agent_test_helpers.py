"""
Helper functions for testing agents with consistent output structure.
"""
import logging
from typing import Any, Dict, Optional, Callable, Awaitable, Type
from unittest.mock import AsyncMock

logger = logging.getLogger(__name__)

def ensure_agent_output_structure(output_data: Dict[str, Any], agent_role: str) -> Dict[str, Any]:
    """
    Ensure agent output has required fields based on the agent role.
    
    Args:
        output_data: The output data from the agent
        agent_role: The role of the agent (e.g., 'mentor', 'psy', 'ethical', etc.)
        
    Returns:
        Dict[str, Any]: The output data with required fields added if missing
    """
    if output_data is None:
        output_data = {}
    
    # Add common fields if missing
    if 'context_packet' not in output_data:
        output_data['context_packet'] = {}
    
    if 'user_response' not in output_data:
        output_data['user_response'] = "I'm here to help you on your journey."
    
    # Add role-specific fields if missing
    if 'next_agent' not in output_data:
        if agent_role == 'mentor':
            output_data['next_agent'] = 'mentor'
        elif agent_role == 'psy':
            output_data['next_agent'] = 'strategy'
        elif agent_role == 'ethical':
            output_data['next_agent'] = 'resource'
        elif agent_role == 'resource':
            output_data['next_agent'] = 'strategy'
        elif agent_role == 'strategy':
            output_data['next_agent'] = 'engagement'
        elif agent_role == 'error_handler':
            output_data['next_agent'] = 'mentor'
        elif agent_role == 'orchestrator':
            output_data['next_agent'] = 'mentor'
        else:
            output_data['next_agent'] = 'mentor'  # Default to mentor
    
    # Add psychological assessment if missing for psy agent
    if agent_role == 'psy' and 'psychological_assessment' not in output_data:
        output_data['psychological_assessment'] = {
            'hexaco_traits': {},
            'communication_preferences': {},
            'trust_level': 50
        }
    
    # Add challenge_calibration if missing for psy agent
    if agent_role == 'psy' and 'challenge_calibration' not in output_data:
        output_data['challenge_calibration'] = {
            'current_level': 'moderate',
            'reasoning': 'Default calibration level'
        }
    
    # Add growth_opportunities if missing for psy agent
    if agent_role == 'psy' and 'growth_opportunities' not in output_data:
        output_data['growth_opportunities'] = [
            {
                'area': 'self-awareness',
                'description': 'Default growth opportunity'
            }
        ]
    
    # Add ethical validation if missing for ethical agent
    if agent_role == 'ethical' and 'ethical_validation' not in output_data:
        output_data['ethical_validation'] = {
            'is_appropriate': True,
            'reasoning': 'No ethical concerns detected.'
        }
    
    # Add resource context if missing for resource agent
    if agent_role == 'resource' and 'resource_context' not in output_data:
        output_data['resource_context'] = {
            'relevant_resources': [],
            'knowledge_gaps': []
        }
    
    # Add strategy framework if missing for strategy agent
    if agent_role == 'strategy' and 'strategy_framework' not in output_data:
        output_data['strategy_framework'] = {
            'approach': 'balanced',
            'focus_areas': [],
            'constraint_boundaries': {},
            'growth_alignment': {}
        }
    
    # Add error handling result if missing for error handler agent
    if agent_role == 'error_handler' and 'error_handling_result' not in output_data:
        output_data['error_handling_result'] = {
            'error_handled': True,
            'recovery_action': 'restart'
        }
    
    # Add routing decision if missing for orchestrator agent
    if agent_role == 'orchestrator' and 'routing_decision' not in output_data:
        output_data['routing_decision'] = {
            'selected_agent': 'mentor',
            'reasoning': 'Initial routing to mentor agent.'
        }
    
    return output_data

def patch_agent_process_method(agent_class: Type) -> Callable:
    """
    Patch an agent class's process method to ensure consistent output structure.
    
    Args:
        agent_class: The agent class to patch
        
    Returns:
        Callable: The original process method for later restoration
    """
    original_process = agent_class.process
    
    async def patched_process(self, state):
        try:
            result = await original_process(self, state)
            # Get agent role from class name
            agent_role = self.__class__.__name__.lower().replace('agent', '')
            # Ensure output structure
            if isinstance(result, dict):
                result = ensure_agent_output_structure(result, agent_role)
            return result
        except Exception as e:
            logger.error(f"Error in patched process method: {e}")
            raise
    
    agent_class.process = patched_process
    return original_process

def create_mock_profiler():
    """
    Create a mock profiler for testing.
    
    Returns:
        AsyncMock: A mock profiler with async record_usage method
    """
    mock_profiler = AsyncMock()
    mock_profiler.record_usage = AsyncMock()
    return mock_profiler
